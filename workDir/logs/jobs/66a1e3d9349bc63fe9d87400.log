[INFO ] 2024-07-25 14:00:29.438 - [Dame<PERSON>二次校验 - Co<PERSON>] - Task initialization... 
[INFO ] 2024-07-25 14:00:29.439 - [Dameng二次校验 - Copy] - Start task milestones: 66a1e3d9349bc63fe9d87400(<PERSON><PERSON>二次校验 - Copy) 
[INFO ] 2024-07-25 14:00:29.643 - [Dame<PERSON>二次校验 - Co<PERSON>] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-25 14:00:29.823 - [Dameng二次校验 - Copy] - The engine receives Dameng二次校验 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 14:00:29.823 - [Dameng二次校验 - Copy][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:00:29.823 - [Dame<PERSON>二次校验 - Co<PERSON>][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:00:29.823 - [Dameng二次校验 - Copy][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 14:00:29.823 - [Dameng二次校验 - Copy][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 14:00:30.504 - [Dameng二次校验 - Copy][DamengSource] - Source node "DamengSource" read batch size: 100 
[INFO ] 2024-07-25 14:00:30.505 - [Dameng二次校验 - Copy][DamengSource] - Source node "DamengSource" event queue capacity: 200 
[INFO ] 2024-07-25 14:00:30.505 - [Dameng二次校验 - Copy][DamengSource] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 14:00:30.521 - [Dameng二次校验 - Copy][DamengSource] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":40631916,"pendingScn":40631916,"timestamp":null} 
[INFO ] 2024-07-25 14:00:30.521 - [Dameng二次校验 - Copy][DamengSource] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-25 14:00:30.572 - [Dameng二次校验 - Copy][DamengSource] - Initial sync started 
[INFO ] 2024-07-25 14:00:30.572 - [Dameng二次校验 - Copy][DamengSource] - Starting batch read, table name: bmsql_history_test, offset: null 
[INFO ] 2024-07-25 14:00:30.654 - [Dameng二次校验 - Copy][DamengSource] - Table bmsql_history_test is going to be initial synced 
[INFO ] 2024-07-25 14:00:30.654 - [Dameng二次校验 - Copy][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-25 14:00:30.654 - [Dameng二次校验 - Copy][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 14:00:30.860 - [Dameng二次校验 - Copy][DamengSource] - Query table 'bmsql_history_test' counts: 11262 
[INFO ] 2024-07-25 14:00:31.492 - [Dameng二次校验 - Copy][DamengSource] - Table [bmsql_history_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:00:31.492 - [Dameng二次校验 - Copy][DamengSource] - Starting batch read, table name: bmsql_district_test, offset: null 
[INFO ] 2024-07-25 14:00:31.508 - [Dameng二次校验 - Copy][DamengSource] - Table bmsql_district_test is going to be initial synced 
[INFO ] 2024-07-25 14:00:31.508 - [Dameng二次校验 - Copy][DamengSource] - Table [bmsql_district_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:00:31.513 - [Dameng二次校验 - Copy][DamengSource] - Query table 'bmsql_district_test' counts: 10 
[INFO ] 2024-07-25 14:00:31.513 - [Dameng二次校验 - Copy][DamengSource] - Starting batch read, table name: bmsql_new_order_test, offset: null 
[INFO ] 2024-07-25 14:00:31.527 - [Dameng二次校验 - Copy][DamengSource] - Table bmsql_new_order_test is going to be initial synced 
[INFO ] 2024-07-25 14:00:31.528 - [Dameng二次校验 - Copy][DamengSource] - Query table 'bmsql_new_order_test' counts: 2408 
[INFO ] 2024-07-25 14:00:31.582 - [Dameng二次校验 - Copy][DamengSource] - Table [bmsql_new_order_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:00:31.582 - [Dameng二次校验 - Copy][DamengSource] - Starting batch read, table name: bmsql_item_test, offset: null 
[INFO ] 2024-07-25 14:00:31.583 - [Dameng二次校验 - Copy][DamengSource] - Table bmsql_item_test is going to be initial synced 
[INFO ] 2024-07-25 14:00:31.787 - [Dameng二次校验 - Copy][DamengSource] - Query table 'bmsql_item_test' counts: 10000 
[INFO ] 2024-07-25 14:00:31.924 - [Dameng二次校验 - Copy][DamengSource] - Table [bmsql_item_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:00:31.924 - [Dameng二次校验 - Copy][DamengSource] - Starting batch read, table name: bmsql_config_test, offset: null 
[INFO ] 2024-07-25 14:00:31.925 - [Dameng二次校验 - Copy][DamengSource] - Table bmsql_config_test is going to be initial synced 
[INFO ] 2024-07-25 14:00:31.951 - [Dameng二次校验 - Copy][DamengSource] - Table [bmsql_config_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:00:31.953 - [Dameng二次校验 - Copy][DamengSource] - Query table 'bmsql_config_test' counts: 4 
[INFO ] 2024-07-25 14:00:31.953 - [Dameng二次校验 - Copy][DamengSource] - Starting batch read, table name: bmsql_customer_test, offset: null 
[INFO ] 2024-07-25 14:00:31.954 - [Dameng二次校验 - Copy][DamengSource] - Table bmsql_customer_test is going to be initial synced 
[INFO ] 2024-07-25 14:00:32.159 - [Dameng二次校验 - Copy][DamengSource] - Query table 'bmsql_customer_test' counts: 10000 
[INFO ] 2024-07-25 14:00:32.770 - [Dameng二次校验 - Copy][DamengSource] - Table [bmsql_customer_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:00:32.770 - [Dameng二次校验 - Copy][DamengSource] - Starting batch read, table name: bmsql_order_line_test, offset: null 
[INFO ] 2024-07-25 14:00:32.806 - [Dameng二次校验 - Copy][DamengSource] - Table bmsql_order_line_test is going to be initial synced 
[INFO ] 2024-07-25 14:00:32.806 - [Dameng二次校验 - Copy][DamengSource] - Query table 'bmsql_order_line_test' counts: 105317 
[INFO ] 2024-07-25 14:00:38.581 - [Dameng二次校验 - Copy][DamengSource] - Table [bmsql_order_line_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:00:38.582 - [Dameng二次校验 - Copy][DamengSource] - Starting batch read, table name: bmsql_warehouse_test, offset: null 
[INFO ] 2024-07-25 14:00:38.582 - [Dameng二次校验 - Copy][DamengSource] - Table bmsql_warehouse_test is going to be initial synced 
[INFO ] 2024-07-25 14:00:38.599 - [Dameng二次校验 - Copy][DamengSource] - Query table 'bmsql_warehouse_test' counts: 1 
[INFO ] 2024-07-25 14:00:38.599 - [Dameng二次校验 - Copy][DamengSource] - Table [bmsql_warehouse_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:00:38.599 - [Dameng二次校验 - Copy][DamengSource] - Starting batch read, table name: bmsql_oorder_test, offset: null 
[INFO ] 2024-07-25 14:00:38.599 - [Dameng二次校验 - Copy][DamengSource] - Table bmsql_oorder_test is going to be initial synced 
[INFO ] 2024-07-25 14:00:38.801 - [Dameng二次校验 - Copy][DamengSource] - Query table 'bmsql_oorder_test' counts: 11728 
[INFO ] 2024-07-25 14:00:39.097 - [Dameng二次校验 - Copy][DamengSource] - Table [bmsql_oorder_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:00:39.099 - [Dameng二次校验 - Copy][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:00:39.099 - [Dameng二次校验 - Copy][DamengSource] - Incremental sync starting... 
[INFO ] 2024-07-25 14:00:39.099 - [Dameng二次校验 - Copy][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:00:39.304 - [Dameng二次校验 - Copy][DamengSource] - Starting stream read, table list: [bmsql_history_test, bmsql_district_test, bmsql_new_order_test, bmsql_item_test, bmsql_config_test, bmsql_customer_test, bmsql_order_line_test, bmsql_warehouse_test, bmsql_oorder_test], offset: {"sortString":null,"offsetValue":null,"lastScn":40631916,"pendingScn":40631916,"timestamp":null} 
[INFO ] 2024-07-25 14:00:39.447 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:00:41.866 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:00:44.413 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:00:46.819 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:00:49.440 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:00:51.746 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:00:54.393 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:00:54.630 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:00:57.341 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:00:59.628 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:02.265 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:04.609 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:05.220 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:09.931 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:12.222 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:14.673 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:17.140 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:17.573 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:20.055 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:20.522 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:22.957 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:25.392 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:25.917 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:30.371 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:32.907 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:35.438 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:38.070 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:42.687 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:45.065 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:47.642 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:50.469 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:52.946 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:57.485 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:01:59.764 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:02.414 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:06.843 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:09.468 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:11.918 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:14.195 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:18.715 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:21.358 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:23.648 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:26.125 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:30.639 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:31.247 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:33.581 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:36.030 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:38.478 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:41.125 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:45.455 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:48.115 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:50.499 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:52.935 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:57.555 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:02:59.890 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:02.538 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:04.801 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:09.299 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:11.949 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:14.258 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:18.885 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:21.221 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:23.870 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:26.349 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:28.997 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:33.369 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:35.851 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:38.490 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:40.843 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:45.499 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:47.941 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:50.275 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:54.937 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:57.290 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:03:59.927 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:02.394 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:06.677 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:09.330 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:11.683 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:14.335 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:18.654 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:21.300 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:23.600 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:26.038 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:30.568 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:33.010 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:35.654 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:38.125 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:42.439 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:44.903 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:47.552 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:49.818 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:54.466 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:56.792 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:04:59.276 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:03.771 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:06.416 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:08.741 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:11.392 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:15.731 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:18.381 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:20.689 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:23.342 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:27.748 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:30.390 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:32.672 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:35.309 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:39.668 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:42.324 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:44.588 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:47.227 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:51.581 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:54.228 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:56.551 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:05:58.997 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:06:03.683 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:06:05.982 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:06:08.609 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:06:10.898 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:06:15.390 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:06:17.844 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:06:20.482 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:06:25.027 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:06:27.256 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:06:29.902 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:06:32.195 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:06:36.777 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:06:39.179 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:06:41.832 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:06:44.096 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:06:48.572 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:06:51.223 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:06:53.522 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:06:56.170 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:00.467 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:03.111 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:05.631 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:08.076 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:12.436 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:15.079 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:17.518 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:21.852 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:24.489 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:26.781 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:29.258 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:33.904 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:36.219 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:38.872 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:41.246 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:45.722 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:48.092 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:50.737 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:53.012 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:07:57.483 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:00.038 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:02.400 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:04.865 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:09.549 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:11.816 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:14.457 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:18.780 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:21.232 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:23.861 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:26.221 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:30.810 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:33.242 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:35.901 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:38.239 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:42.792 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:45.466 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:48.530 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:48.939 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:51.297 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:53.858 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:08:56.511 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:00.932 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:03.527 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:05.887 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:08.327 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:12.945 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:15.389 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:17.735 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:20.383 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:24.941 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:27.238 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:29.878 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:32.151 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:36.652 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:39.302 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:41.671 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:44.251 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:48.815 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:51.152 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:53.599 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:09:56.073 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:00.694 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:03.034 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:05.675 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:08.026 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:12.729 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:15.000 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:17.646 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:19.935 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:22.452 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:23.062 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:27.600 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:29.886 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:32.315 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:36.844 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:39.470 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:41.773 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:44.395 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:48.772 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:51.412 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:53.721 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:10:56.352 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:00.833 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:03.179 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:05.764 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:08.173 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:12.762 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:15.155 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:17.669 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:20.118 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:24.874 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:27.213 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:29.772 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:32.117 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:36.796 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:39.092 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:41.739 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:44.018 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:48.669 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:51.269 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:53.552 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:11:56.000 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:00.534 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:03.136 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:05.462 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:08.109 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:12.564 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:14.897 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:17.332 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:21.989 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:24.324 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:26.769 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:29.256 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:33.941 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:36.260 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:38.714 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:41.351 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:45.708 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:48.356 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:48.610 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:51.253 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:53.565 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:12:56.214 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:00.814 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:03.249 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:05.692 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:08.009 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:12.562 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:15.210 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:17.551 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:20.207 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:24.805 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:27.165 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:29.782 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:32.169 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:36.812 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:39.242 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:41.732 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:44.251 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:48.919 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:51.297 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:53.773 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:13:56.418 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:01.031 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:03.473 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:05.741 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:08.226 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:12.815 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:15.244 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:17.888 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:20.185 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:24.699 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:27.243 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:29.871 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:32.174 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:36.692 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:39.343 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:41.578 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:44.219 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:48.583 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:51.115 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:53.498 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:14:56.144 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:00.504 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:03.066 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:05.460 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:08.111 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:12.489 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:14.918 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:17.578 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:21.892 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:24.528 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:26.828 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:29.291 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:33.893 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:36.536 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:38.834 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:41.479 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:45.878 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:48.643 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:50.954 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:53.607 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:15:55.969 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:00.603 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:02.983 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:05.644 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:07.973 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:12.535 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:15.066 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:17.713 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:20.061 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:24.659 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:27.181 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:29.830 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:32.194 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:36.675 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:39.137 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:41.601 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:44.241 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:48.667 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:51.319 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:51.723 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:54.163 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:56.557 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:16:59.126 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:03.524 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:06.123 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:08.538 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:11.005 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:15.521 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:18.017 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:20.651 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:22.953 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:27.542 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:29.924 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:32.488 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:34.916 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:39.703 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:42.561 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:45.051 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:47.702 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:50.034 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:54.564 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:57.215 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:17:59.708 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:02.277 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:06.744 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:09.257 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:11.730 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:14.372 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:18.729 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:21.376 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:23.786 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:26.173 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:28.769 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:29.114 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:33.877 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:36.510 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:38.833 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:41.463 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:45.849 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:48.448 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:50.783 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:53.417 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:18:57.986 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:00.632 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:03.136 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:05.785 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:08.152 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:12.762 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:15.191 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:17.688 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:20.536 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:24.885 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:27.353 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:29.865 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:32.390 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:36.981 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:39.372 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:41.909 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:44.644 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:47.219 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:51.698 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:54.270 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:56.846 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:19:59.273 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:03.821 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:06.381 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:08.793 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:11.336 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:15.936 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:18.544 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:21.070 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:23.601 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:26.157 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:30.753 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:33.239 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:35.727 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:38.260 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:42.808 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:45.334 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:47.846 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:50.380 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:55.138 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:57.428 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:20:59.931 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:21:02.488 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:21:05.030 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:21:09.631 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:21:12.011 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:21:14.590 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:21:16.973 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:21:21.637 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:21:24.047 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:21:26.634 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:21:29.042 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:21:33.685 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:21:36.081 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:21:38.689 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:21:41.078 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:21:45.759 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:21:48.201 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:21:50.853 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:21:53.217 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:21:57.815 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:22:00.387 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:22:03.039 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:22:05.485 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:22:09.841 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:22:12.358 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:22:14.796 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:22:17.451 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:22:21.779 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:22:24.335 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:22:26.750 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:22:29.303 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:22:33.876 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:22:36.368 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:22:39.006 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:22:41.354 - [Dameng二次校验 - Copy][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:22:43.543 - [Dameng二次校验 - Copy][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] running status set to false 
[INFO ] 2024-07-25 14:22:43.547 - [Dameng二次校验 - Copy][DamengSource] - Log Miner is shutting down... 
[WARN ] 2024-07-25 14:22:43.581 - [Dameng二次校验 - Copy][DamengSource] - Stop PDK connector node failed: Unknown exception occur when operate table: unknown | Associate id: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 14:22:43.587 - [Dameng二次校验 - Copy][DamengSource] - PDK connector node released: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 14:22:43.587 - [Dameng二次校验 - Copy][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] schema data cleaned 
[INFO ] 2024-07-25 14:22:43.588 - [Dameng二次校验 - Copy][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] monitor closed 
[INFO ] 2024-07-25 14:22:43.588 - [Dameng二次校验 - Copy][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] close complete, cost 44 ms 
[INFO ] 2024-07-25 14:22:43.588 - [Dameng二次校验 - Copy][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] running status set to false 
[INFO ] 2024-07-25 14:22:43.603 - [Dameng二次校验 - Copy][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 14:22:43.603 - [Dameng二次校验 - Copy][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 14:22:43.603 - [Dameng二次校验 - Copy][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] schema data cleaned 
[INFO ] 2024-07-25 14:22:43.603 - [Dameng二次校验 - Copy][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] monitor closed 
[INFO ] 2024-07-25 14:22:43.805 - [Dameng二次校验 - Copy][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] close complete, cost 19 ms 
[INFO ] 2024-07-25 14:22:44.637 - [Dameng二次校验 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 14:22:44.756 - [Dameng二次校验 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2c391845 
[INFO ] 2024-07-25 14:22:44.756 - [Dameng二次校验 - Copy] - Stop task milestones: 66a1e3d9349bc63fe9d87400(Dameng二次校验 - Copy)  
[INFO ] 2024-07-25 14:22:44.769 - [Dameng二次校验 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-07-25 14:22:44.769 - [Dameng二次校验 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 14:22:44.837 - [Dameng二次校验 - Copy] - Remove memory task client succeed, task: Dameng二次校验 - Copy[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 14:22:44.837 - [Dameng二次校验 - Copy] - Destroy memory task client cache succeed, task: Dameng二次校验 - Copy[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 14:22:45.618 - [Dameng二次校验 - Copy][DamengSource] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-25 14:22:45.621 - [Dameng二次校验 - Copy][DamengSource] - java.lang.RuntimeException: java.lang.RuntimeException: check log file error <-- Error Message -->
java.lang.RuntimeException: java.lang.RuntimeException: check log file error

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: check log file error
	io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.checkLogNameWithScn(SingleDamengLogMiner.java:103)
	io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:49)
	io.tapdata.connector.dameng.cdc.DamengCdcRunner.startCdcRunner(DamengCdcRunner.java:38)
	io.tapdata.connector.dameng.DamengConnector.streamRead(DamengConnector.java:392)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.RuntimeException: check log file error
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: check log file error
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:76)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.startCdcRunner(DamengCdcRunner.java:38)
	at io.tapdata.connector.dameng.DamengConnector.streamRead(DamengConnector.java:392)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.RuntimeException: check log file error
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.checkLogNameWithScn(SingleDamengLogMiner.java:103)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:49)
	... 20 more

[INFO ] 2024-07-25 14:26:12.397 - [TestSame1] - Task initialization... 
[INFO ] 2024-07-25 14:26:12.474 - [TestSame1] - Start task milestones: 66a1e3d9349bc63fe9d87400(TestSame1) 
[INFO ] 2024-07-25 14:26:12.673 - [TestSame1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-25 14:26:12.709 - [TestSame1] - The engine receives TestSame1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 14:26:12.765 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:26:12.765 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:26:12.766 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 14:26:12.766 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 14:26:13.771 - [TestSame1][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-25 14:26:13.775 - [TestSame1][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 14:26:13.799 - [TestSame1][Mysql3307] - Table "test.bmsql_history_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:26:13.800 - [TestSame1][Mysql3307] - The table bmsql_history_test has already exist. 
[INFO ] 2024-07-25 14:26:13.937 - [TestSame1][DamengSource] - Source node "DamengSource" read batch size: 100 
[INFO ] 2024-07-25 14:26:13.938 - [TestSame1][DamengSource] - Source node "DamengSource" event queue capacity: 200 
[INFO ] 2024-07-25 14:26:13.938 - [TestSame1][DamengSource] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 14:26:13.963 - [TestSame1][DamengSource] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":40632547,"pendingScn":40632547,"timestamp":null} 
[INFO ] 2024-07-25 14:26:13.964 - [TestSame1][DamengSource] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-25 14:26:13.969 - [TestSame1][Mysql3307] - Table "test.bmsql_district_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:26:13.971 - [TestSame1][Mysql3307] - The table bmsql_district_test has already exist. 
[INFO ] 2024-07-25 14:26:14.012 - [TestSame1][Mysql3307] - Table "test.bmsql_new_order_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:26:14.013 - [TestSame1][Mysql3307] - The table bmsql_new_order_test has already exist. 
[INFO ] 2024-07-25 14:26:14.061 - [TestSame1][DamengSource] - Initial sync started 
[INFO ] 2024-07-25 14:26:14.061 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_history_test, offset: null 
[INFO ] 2024-07-25 14:26:14.088 - [TestSame1][DamengSource] - Table bmsql_history_test is going to be initial synced 
[INFO ] 2024-07-25 14:26:14.088 - [TestSame1][Mysql3307] - Table "test.bmsql_item_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:26:14.088 - [TestSame1][Mysql3307] - The table bmsql_item_test has already exist. 
[INFO ] 2024-07-25 14:26:14.130 - [TestSame1][Mysql3307] - Table "test.bmsql_config_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:26:14.133 - [TestSame1][Mysql3307] - The table bmsql_config_test has already exist. 
[INFO ] 2024-07-25 14:26:14.163 - [TestSame1][Mysql3307] - Table "test.bmsql_customer_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:26:14.163 - [TestSame1][Mysql3307] - The table bmsql_customer_test has already exist. 
[INFO ] 2024-07-25 14:26:14.207 - [TestSame1][Mysql3307] - Table "test.bmsql_order_line_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:26:14.207 - [TestSame1][Mysql3307] - The table bmsql_order_line_test has already exist. 
[INFO ] 2024-07-25 14:26:14.245 - [TestSame1][DamengSource] - Query table 'bmsql_history_test' counts: 11262 
[INFO ] 2024-07-25 14:26:14.252 - [TestSame1][Mysql3307] - Table "test.bmsql_warehouse_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:26:14.252 - [TestSame1][Mysql3307] - The table bmsql_warehouse_test has already exist. 
[INFO ] 2024-07-25 14:26:14.282 - [TestSame1][Mysql3307] - Table "test.bmsql_oorder_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:26:14.331 - [TestSame1][Mysql3307] - The table bmsql_oorder_test has already exist. 
[INFO ] 2024-07-25 14:26:14.953 - [TestSame1][DamengSource] - Table [bmsql_history_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:26:14.953 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_district_test, offset: null 
[INFO ] 2024-07-25 14:26:14.954 - [TestSame1][DamengSource] - Table bmsql_district_test is going to be initial synced 
[INFO ] 2024-07-25 14:26:14.991 - [TestSame1][DamengSource] - Query table 'bmsql_district_test' counts: 10 
[INFO ] 2024-07-25 14:26:14.998 - [TestSame1][DamengSource] - Table [bmsql_district_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:26:14.998 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_new_order_test, offset: null 
[INFO ] 2024-07-25 14:26:14.999 - [TestSame1][DamengSource] - Table bmsql_new_order_test is going to be initial synced 
[INFO ] 2024-07-25 14:26:15.018 - [TestSame1][DamengSource] - Query table 'bmsql_new_order_test' counts: 2408 
[INFO ] 2024-07-25 14:26:15.122 - [TestSame1][DamengSource] - Table [bmsql_new_order_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:26:15.123 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_item_test, offset: null 
[INFO ] 2024-07-25 14:26:15.123 - [TestSame1][DamengSource] - Table bmsql_item_test is going to be initial synced 
[INFO ] 2024-07-25 14:26:15.161 - [TestSame1][DamengSource] - Query table 'bmsql_item_test' counts: 10000 
[INFO ] 2024-07-25 14:26:15.725 - [TestSame1][DamengSource] - Table [bmsql_item_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:26:15.725 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_config_test, offset: null 
[INFO ] 2024-07-25 14:26:15.725 - [TestSame1][DamengSource] - Table bmsql_config_test is going to be initial synced 
[INFO ] 2024-07-25 14:26:15.763 - [TestSame1][DamengSource] - Table [bmsql_config_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:26:15.774 - [TestSame1][DamengSource] - Query table 'bmsql_config_test' counts: 4 
[INFO ] 2024-07-25 14:26:15.774 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_customer_test, offset: null 
[INFO ] 2024-07-25 14:26:15.774 - [TestSame1][DamengSource] - Table bmsql_customer_test is going to be initial synced 
[INFO ] 2024-07-25 14:26:15.801 - [TestSame1][DamengSource] - Query table 'bmsql_customer_test' counts: 10000 
[INFO ] 2024-07-25 14:26:17.225 - [TestSame1][DamengSource] - Table [bmsql_customer_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:26:17.225 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_order_line_test, offset: null 
[INFO ] 2024-07-25 14:26:17.225 - [TestSame1][DamengSource] - Table bmsql_order_line_test is going to be initial synced 
[INFO ] 2024-07-25 14:26:17.258 - [TestSame1][DamengSource] - Query table 'bmsql_order_line_test' counts: 105317 
[INFO ] 2024-07-25 14:26:26.062 - [TestSame1][DamengSource] - Table [bmsql_order_line_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:26:26.062 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_warehouse_test, offset: null 
[INFO ] 2024-07-25 14:26:26.063 - [TestSame1][DamengSource] - Table bmsql_warehouse_test is going to be initial synced 
[INFO ] 2024-07-25 14:26:26.105 - [TestSame1][DamengSource] - Table [bmsql_warehouse_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:26:26.116 - [TestSame1][DamengSource] - Query table 'bmsql_warehouse_test' counts: 1 
[INFO ] 2024-07-25 14:26:26.116 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_oorder_test, offset: null 
[INFO ] 2024-07-25 14:26:26.116 - [TestSame1][DamengSource] - Table bmsql_oorder_test is going to be initial synced 
[INFO ] 2024-07-25 14:26:26.182 - [TestSame1][DamengSource] - Query table 'bmsql_oorder_test' counts: 11728 
[INFO ] 2024-07-25 14:26:26.942 - [TestSame1][DamengSource] - Table [bmsql_oorder_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:26:26.942 - [TestSame1][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:26:26.942 - [TestSame1][DamengSource] - Incremental sync starting... 
[INFO ] 2024-07-25 14:26:26.943 - [TestSame1][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:26:26.944 - [TestSame1][DamengSource] - Starting stream read, table list: [bmsql_history_test, bmsql_district_test, bmsql_new_order_test, bmsql_item_test, bmsql_config_test, bmsql_customer_test, bmsql_order_line_test, bmsql_warehouse_test, bmsql_oorder_test], offset: {"sortString":null,"offsetValue":null,"lastScn":40632547,"pendingScn":40632547,"timestamp":null} 
[INFO ] 2024-07-25 14:26:27.308 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:27.781 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:30.388 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:33.106 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:35.778 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:36.155 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:36.654 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:37.142 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:37.593 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:38.022 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:38.541 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:41.215 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:45.884 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:48.384 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:48.996 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:51.281 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:53.855 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:26:53.857 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] running status set to false 
[INFO ] 2024-07-25 14:26:53.857 - [TestSame1][DamengSource] - Log Miner is shutting down... 
[INFO ] 2024-07-25 14:26:53.876 - [TestSame1][DamengSource] - Incremental sync completed 
[WARN ] 2024-07-25 14:26:53.876 - [TestSame1][DamengSource] - Stop PDK connector node failed: Unknown exception occur when operate table: unknown | Associate id: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 14:26:53.876 - [TestSame1][DamengSource] - PDK connector node released: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 14:26:53.876 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] schema data cleaned 
[INFO ] 2024-07-25 14:26:53.878 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] monitor closed 
[INFO ] 2024-07-25 14:26:53.878 - [TestSame1][DamengSource] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[INFO ] 2024-07-25 14:26:53.879 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] close complete, cost 24 ms 
[INFO ] 2024-07-25 14:26:53.879 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] running status set to false 
[ERROR] 2024-07-25 14:26:53.903 - [TestSame1][DamengSource] - java.lang.RuntimeException: java.lang.RuntimeException: check log file error <-- Error Message -->
java.lang.RuntimeException: java.lang.RuntimeException: check log file error

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: check log file error
	io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.checkLogNameWithScn(SingleDamengLogMiner.java:103)
	io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:49)
	io.tapdata.connector.dameng.cdc.DamengCdcRunner.startCdcRunner(DamengCdcRunner.java:38)
	io.tapdata.connector.dameng.DamengConnector.streamRead(DamengConnector.java:392)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.RuntimeException: check log file error
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: check log file error
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:76)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.startCdcRunner(DamengCdcRunner.java:38)
	at io.tapdata.connector.dameng.DamengConnector.streamRead(DamengConnector.java:392)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.RuntimeException: check log file error
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.checkLogNameWithScn(SingleDamengLogMiner.java:103)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:49)
	... 20 more

[INFO ] 2024-07-25 14:26:53.903 - [TestSame1][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 14:26:53.903 - [TestSame1][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 14:26:53.903 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] schema data cleaned 
[INFO ] 2024-07-25 14:26:53.903 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] monitor closed 
[INFO ] 2024-07-25 14:26:53.903 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] close complete, cost 24 ms 
[INFO ] 2024-07-25 14:26:55.304 - [TestSame1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 14:26:55.306 - [TestSame1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1073c866 
[INFO ] 2024-07-25 14:26:55.426 - [TestSame1] - Stop task milestones: 66a1e3d9349bc63fe9d87400(TestSame1)  
[INFO ] 2024-07-25 14:26:55.444 - [TestSame1] - Stopped task aspect(s) 
[INFO ] 2024-07-25 14:26:55.444 - [TestSame1] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 14:26:55.473 - [TestSame1] - Remove memory task client succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 14:26:55.476 - [TestSame1] - Destroy memory task client cache succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 14:26:58.125 - [TestSame1] - Task initialization... 
[INFO ] 2024-07-25 14:26:58.125 - [TestSame1] - Start task milestones: 66a1e3d9349bc63fe9d87400(TestSame1) 
[INFO ] 2024-07-25 14:26:58.272 - [TestSame1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-25 14:26:58.351 - [TestSame1] - The engine receives TestSame1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 14:26:58.351 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:26:58.351 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:26:58.351 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 14:26:58.351 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] preload schema finished, cost 1 ms 
[INFO ] 2024-07-25 14:26:58.567 - [TestSame1][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-25 14:26:58.567 - [TestSame1][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 14:26:58.589 - [TestSame1][DamengSource] - Source node "DamengSource" read batch size: 100 
[INFO ] 2024-07-25 14:26:58.590 - [TestSame1][DamengSource] - Source node "DamengSource" event queue capacity: 200 
[INFO ] 2024-07-25 14:26:58.591 - [TestSame1][DamengSource] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-25 14:26:58.591 - [TestSame1][DamengSource] - batch offset found: {"bmsql_history_test":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"bmsql_new_order_test":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"bmsql_district_test":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"bmsql_item_test":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"bmsql_config_test":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"bmsql_order_line_test":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"bmsql_customer_test":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"bmsql_warehouse_test":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"bmsql_oorder_test":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":40632547,"pendingScn":40632547,"timestamp":null} 
[INFO ] 2024-07-25 14:26:58.594 - [TestSame1][DamengSource] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-25 14:26:58.639 - [TestSame1][DamengSource] - Incremental sync starting... 
[INFO ] 2024-07-25 14:26:58.639 - [TestSame1][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:26:58.845 - [TestSame1][DamengSource] - Starting stream read, table list: [bmsql_history_test, bmsql_district_test, bmsql_new_order_test, bmsql_item_test, bmsql_config_test, bmsql_customer_test, bmsql_order_line_test, bmsql_warehouse_test, bmsql_oorder_test], offset: {"sortString":null,"offsetValue":null,"lastScn":40632547,"pendingScn":40632547,"timestamp":null} 
[INFO ] 2024-07-25 14:26:59.158 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:03.711 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] running status set to false 
[INFO ] 2024-07-25 14:27:03.713 - [TestSame1][DamengSource] - Log Miner is shutting down... 
[WARN ] 2024-07-25 14:27:03.736 - [TestSame1][DamengSource] - Stop PDK connector node failed: Unknown exception occur when operate table: unknown | Associate id: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 14:27:03.736 - [TestSame1][DamengSource] - PDK connector node released: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 14:27:03.736 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] schema data cleaned 
[INFO ] 2024-07-25 14:27:03.737 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] monitor closed 
[INFO ] 2024-07-25 14:27:03.740 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] close complete, cost 27 ms 
[INFO ] 2024-07-25 14:27:03.741 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] running status set to false 
[INFO ] 2024-07-25 14:27:03.751 - [TestSame1][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 14:27:03.751 - [TestSame1][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 14:27:03.751 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] schema data cleaned 
[INFO ] 2024-07-25 14:27:03.751 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] monitor closed 
[INFO ] 2024-07-25 14:27:03.774 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] close complete, cost 14 ms 
[INFO ] 2024-07-25 14:27:03.774 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:03.974 - [TestSame1][DamengSource] - Incremental sync completed 
[INFO ] 2024-07-25 14:27:05.504 - [TestSame1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 14:27:05.505 - [TestSame1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@cd0be5d 
[INFO ] 2024-07-25 14:27:05.621 - [TestSame1] - Stop task milestones: 66a1e3d9349bc63fe9d87400(TestSame1)  
[INFO ] 2024-07-25 14:27:05.646 - [TestSame1] - Stopped task aspect(s) 
[INFO ] 2024-07-25 14:27:05.647 - [TestSame1] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 14:27:05.664 - [TestSame1] - Remove memory task client succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 14:27:05.667 - [TestSame1] - Destroy memory task client cache succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 14:27:18.325 - [TestSame1] - Task initialization... 
[INFO ] 2024-07-25 14:27:18.492 - [TestSame1] - Start task milestones: 66a1e3d9349bc63fe9d87400(TestSame1) 
[INFO ] 2024-07-25 14:27:18.492 - [TestSame1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-25 14:27:18.571 - [TestSame1] - The engine receives TestSame1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 14:27:18.572 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:27:18.572 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:27:18.572 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 14:27:18.572 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 14:27:19.291 - [TestSame1][DamengSource] - Source node "DamengSource" read batch size: 100 
[INFO ] 2024-07-25 14:27:19.291 - [TestSame1][DamengSource] - Source node "DamengSource" event queue capacity: 200 
[INFO ] 2024-07-25 14:27:19.291 - [TestSame1][DamengSource] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 14:27:19.331 - [TestSame1][DamengSource] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":40632603,"pendingScn":40632603,"timestamp":null} 
[INFO ] 2024-07-25 14:27:19.331 - [TestSame1][DamengSource] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-25 14:27:19.386 - [TestSame1][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-25 14:27:19.386 - [TestSame1][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 14:27:19.407 - [TestSame1][DamengSource] - Initial sync started 
[INFO ] 2024-07-25 14:27:19.409 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_history_test, offset: null 
[INFO ] 2024-07-25 14:27:19.409 - [TestSame1][DamengSource] - Table bmsql_history_test is going to be initial synced 
[INFO ] 2024-07-25 14:27:19.516 - [TestSame1][Mysql3307] - Table "test.bmsql_history_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:27:19.516 - [TestSame1][Mysql3307] - The table bmsql_history_test has already exist. 
[INFO ] 2024-07-25 14:27:19.516 - [TestSame1][Mysql3307] - Table "test.bmsql_district_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:27:19.516 - [TestSame1][Mysql3307] - The table bmsql_district_test has already exist. 
[INFO ] 2024-07-25 14:27:19.555 - [TestSame1][DamengSource] - Query table 'bmsql_history_test' counts: 11262 
[INFO ] 2024-07-25 14:27:19.555 - [TestSame1][Mysql3307] - Table "test.bmsql_new_order_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:27:19.556 - [TestSame1][Mysql3307] - The table bmsql_new_order_test has already exist. 
[INFO ] 2024-07-25 14:27:19.687 - [TestSame1][Mysql3307] - Table "test.bmsql_item_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:27:19.687 - [TestSame1][Mysql3307] - The table bmsql_item_test has already exist. 
[INFO ] 2024-07-25 14:27:19.687 - [TestSame1][Mysql3307] - Table "test.bmsql_config_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:27:19.687 - [TestSame1][Mysql3307] - The table bmsql_config_test has already exist. 
[INFO ] 2024-07-25 14:27:19.741 - [TestSame1][Mysql3307] - Table "test.bmsql_customer_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:27:19.741 - [TestSame1][Mysql3307] - The table bmsql_customer_test has already exist. 
[INFO ] 2024-07-25 14:27:19.866 - [TestSame1][Mysql3307] - Table "test.bmsql_order_line_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:27:19.867 - [TestSame1][Mysql3307] - The table bmsql_order_line_test has already exist. 
[INFO ] 2024-07-25 14:27:19.963 - [TestSame1][Mysql3307] - Table "test.bmsql_warehouse_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:27:19.963 - [TestSame1][Mysql3307] - The table bmsql_warehouse_test has already exist. 
[INFO ] 2024-07-25 14:27:20.003 - [TestSame1][Mysql3307] - Table "test.bmsql_oorder_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:27:20.003 - [TestSame1][Mysql3307] - The table bmsql_oorder_test has already exist. 
[INFO ] 2024-07-25 14:27:20.631 - [TestSame1][DamengSource] - Table [bmsql_history_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:27:20.631 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_district_test, offset: null 
[INFO ] 2024-07-25 14:27:20.657 - [TestSame1][DamengSource] - Table bmsql_district_test is going to be initial synced 
[INFO ] 2024-07-25 14:27:20.657 - [TestSame1][DamengSource] - Table [bmsql_district_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:27:20.666 - [TestSame1][DamengSource] - Query table 'bmsql_district_test' counts: 10 
[INFO ] 2024-07-25 14:27:20.669 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_new_order_test, offset: null 
[INFO ] 2024-07-25 14:27:20.669 - [TestSame1][DamengSource] - Table bmsql_new_order_test is going to be initial synced 
[INFO ] 2024-07-25 14:27:20.745 - [TestSame1][DamengSource] - Query table 'bmsql_new_order_test' counts: 2408 
[INFO ] 2024-07-25 14:27:20.745 - [TestSame1][DamengSource] - Table [bmsql_new_order_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:27:20.745 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_item_test, offset: null 
[INFO ] 2024-07-25 14:27:20.745 - [TestSame1][DamengSource] - Table bmsql_item_test is going to be initial synced 
[INFO ] 2024-07-25 14:27:20.950 - [TestSame1][DamengSource] - Query table 'bmsql_item_test' counts: 10000 
[INFO ] 2024-07-25 14:27:21.262 - [TestSame1][DamengSource] - Table [bmsql_item_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:27:21.263 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_config_test, offset: null 
[INFO ] 2024-07-25 14:27:21.263 - [TestSame1][DamengSource] - Table bmsql_config_test is going to be initial synced 
[INFO ] 2024-07-25 14:27:21.270 - [TestSame1][DamengSource] - Table [bmsql_config_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:27:21.271 - [TestSame1][DamengSource] - Query table 'bmsql_config_test' counts: 4 
[INFO ] 2024-07-25 14:27:21.271 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_customer_test, offset: null 
[INFO ] 2024-07-25 14:27:21.282 - [TestSame1][DamengSource] - Table bmsql_customer_test is going to be initial synced 
[INFO ] 2024-07-25 14:27:21.282 - [TestSame1][DamengSource] - Query table 'bmsql_customer_test' counts: 10000 
[INFO ] 2024-07-25 14:27:22.419 - [TestSame1][DamengSource] - Table [bmsql_customer_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:27:22.419 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_order_line_test, offset: null 
[INFO ] 2024-07-25 14:27:22.419 - [TestSame1][DamengSource] - Table bmsql_order_line_test is going to be initial synced 
[INFO ] 2024-07-25 14:27:22.466 - [TestSame1][DamengSource] - Query table 'bmsql_order_line_test' counts: 105317 
[INFO ] 2024-07-25 14:27:29.687 - [TestSame1][DamengSource] - Table [bmsql_order_line_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:27:29.688 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_warehouse_test, offset: null 
[INFO ] 2024-07-25 14:27:29.688 - [TestSame1][DamengSource] - Table bmsql_warehouse_test is going to be initial synced 
[INFO ] 2024-07-25 14:27:29.737 - [TestSame1][DamengSource] - Query table 'bmsql_warehouse_test' counts: 1 
[INFO ] 2024-07-25 14:27:29.749 - [TestSame1][DamengSource] - Table [bmsql_warehouse_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:27:29.751 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_oorder_test, offset: null 
[INFO ] 2024-07-25 14:27:29.752 - [TestSame1][DamengSource] - Table bmsql_oorder_test is going to be initial synced 
[INFO ] 2024-07-25 14:27:29.957 - [TestSame1][DamengSource] - Query table 'bmsql_oorder_test' counts: 11728 
[INFO ] 2024-07-25 14:27:30.420 - [TestSame1][DamengSource] - Table [bmsql_oorder_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:27:30.420 - [TestSame1][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:27:30.420 - [TestSame1][DamengSource] - Incremental sync starting... 
[INFO ] 2024-07-25 14:27:30.420 - [TestSame1][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:27:30.423 - [TestSame1][DamengSource] - Starting stream read, table list: [bmsql_history_test, bmsql_district_test, bmsql_new_order_test, bmsql_item_test, bmsql_config_test, bmsql_customer_test, bmsql_order_line_test, bmsql_warehouse_test, bmsql_oorder_test], offset: {"sortString":null,"offsetValue":null,"lastScn":40632603,"pendingScn":40632603,"timestamp":null} 
[INFO ] 2024-07-25 14:27:30.695 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:33.222 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:35.868 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:38.402 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:42.963 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:45.607 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:48.021 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:50.594 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:53.012 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:27:57.637 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:00.136 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:02.672 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:05.268 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:09.884 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:12.424 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:14.928 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:17.389 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:19.905 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:20.334 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:24.947 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:27.428 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:29.944 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:32.429 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:37.034 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:39.640 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:42.192 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:44.650 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:47.220 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:49.794 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:50.252 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:54.803 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:57.341 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:28:59.832 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:02.334 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:06.897 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:09.532 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:12.009 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:14.526 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:19.185 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:21.663 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:24.212 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:26.814 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:29.378 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:34.083 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:36.852 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:39.325 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:41.958 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:44.479 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:48.843 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:51.324 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:54.186 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:56.797 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:29:59.251 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:03.694 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:06.371 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:08.716 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:11.308 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:15.971 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:18.385 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:21.017 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:23.541 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:27.930 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:30.534 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:32.973 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:35.546 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:38.125 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:42.872 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:45.529 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:47.817 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:50.449 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:54.916 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:30:57.516 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:00.166 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:02.635 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:07.031 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:09.638 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:11.991 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:14.464 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:17.091 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:21.624 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:24.078 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:26.543 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:31.216 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:33.668 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:36.011 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:38.620 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:43.020 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:45.602 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:47.955 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:50.588 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:54.927 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:57.451 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:31:59.976 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:02.603 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:07.190 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:09.581 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:12.214 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:14.579 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:17.123 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:21.877 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:24.349 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:26.661 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:29.286 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:33.928 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:36.432 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:38.773 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:41.419 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:45.860 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:48.431 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:50.780 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:53.434 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:32:57.799 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:00.442 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:02.782 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:05.422 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:09.923 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:12.435 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:15.086 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:17.424 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:22.109 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:24.413 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:26.883 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:29.516 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:33.926 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:36.471 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:38.957 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:41.514 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:44.146 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:48.851 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:51.194 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:53.842 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:33:56.365 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:00.971 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:03.327 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:05.896 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:08.236 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:12.772 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:15.289 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:17.783 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:20.217 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:24.772 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:27.341 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:29.755 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:32.374 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:36.799 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:39.379 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:41.798 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:44.434 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:48.862 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:51.397 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:53.970 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:34:56.379 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:35:00.186 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] running status set to false 
[INFO ] 2024-07-25 14:35:00.187 - [TestSame1][DamengSource] - Log Miner is shutting down... 
[WARN ] 2024-07-25 14:35:00.211 - [TestSame1][DamengSource] - Stop PDK connector node failed: Unknown exception occur when operate table: unknown | Associate id: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 14:35:00.211 - [TestSame1][DamengSource] - PDK connector node released: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 14:35:00.211 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] schema data cleaned 
[INFO ] 2024-07-25 14:35:00.212 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] monitor closed 
[INFO ] 2024-07-25 14:35:00.214 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] close complete, cost 30 ms 
[INFO ] 2024-07-25 14:35:00.214 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] running status set to false 
[INFO ] 2024-07-25 14:35:00.230 - [TestSame1][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 14:35:00.230 - [TestSame1][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 14:35:00.230 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] schema data cleaned 
[INFO ] 2024-07-25 14:35:00.230 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] monitor closed 
[INFO ] 2024-07-25 14:35:00.246 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] close complete, cost 16 ms 
[INFO ] 2024-07-25 14:35:00.246 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:35:00.653 - [TestSame1][DamengSource] - Incremental sync completed 
[INFO ] 2024-07-25 14:35:01.112 - [TestSame1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 14:35:01.112 - [TestSame1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@21840204 
[INFO ] 2024-07-25 14:35:01.244 - [TestSame1] - Stop task milestones: 66a1e3d9349bc63fe9d87400(TestSame1)  
[INFO ] 2024-07-25 14:35:01.245 - [TestSame1] - Stopped task aspect(s) 
[INFO ] 2024-07-25 14:35:01.245 - [TestSame1] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 14:35:01.281 - [TestSame1] - Remove memory task client succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 14:35:01.284 - [TestSame1] - Destroy memory task client cache succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 14:35:32.112 - [TestSame1] - Task initialization... 
[INFO ] 2024-07-25 14:35:32.112 - [TestSame1] - Start task milestones: 66a1e3d9349bc63fe9d87400(TestSame1) 
[INFO ] 2024-07-25 14:35:32.294 - [TestSame1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-25 14:35:32.294 - [TestSame1] - The engine receives TestSame1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 14:35:32.340 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:35:32.340 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:35:32.340 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] preload schema finished, cost 1 ms 
[INFO ] 2024-07-25 14:35:32.340 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 14:35:33.020 - [TestSame1][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-25 14:35:33.021 - [TestSame1][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 14:35:33.039 - [TestSame1][Mysql3307] - Table "test.bmsql_history_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:35:33.040 - [TestSame1][Mysql3307] - The table bmsql_history_test has already exist. 
[INFO ] 2024-07-25 14:35:33.114 - [TestSame1][DamengSource] - Source node "DamengSource" read batch size: 100 
[INFO ] 2024-07-25 14:35:33.115 - [TestSame1][DamengSource] - Source node "DamengSource" event queue capacity: 200 
[INFO ] 2024-07-25 14:35:33.115 - [TestSame1][DamengSource] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 14:35:33.124 - [TestSame1][Mysql3307] - Table "test.bmsql_district_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:35:33.124 - [TestSame1][Mysql3307] - The table bmsql_district_test has already exist. 
[INFO ] 2024-07-25 14:35:33.146 - [TestSame1][DamengSource] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":40632797,"pendingScn":40632797,"timestamp":null} 
[INFO ] 2024-07-25 14:35:33.149 - [TestSame1][DamengSource] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-25 14:35:33.169 - [TestSame1][Mysql3307] - Table "test.bmsql_new_order_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:35:33.169 - [TestSame1][Mysql3307] - The table bmsql_new_order_test has already exist. 
[INFO ] 2024-07-25 14:35:33.199 - [TestSame1][DamengSource] - Initial sync started 
[INFO ] 2024-07-25 14:35:33.199 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_history_test, offset: null 
[INFO ] 2024-07-25 14:35:33.200 - [TestSame1][DamengSource] - Table bmsql_history_test is going to be initial synced 
[INFO ] 2024-07-25 14:35:33.200 - [TestSame1][Mysql3307] - Table "test.bmsql_item_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:35:33.200 - [TestSame1][Mysql3307] - The table bmsql_item_test has already exist. 
[INFO ] 2024-07-25 14:35:33.253 - [TestSame1][Mysql3307] - Table "test.bmsql_config_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:35:33.254 - [TestSame1][Mysql3307] - The table bmsql_config_test has already exist. 
[INFO ] 2024-07-25 14:35:33.322 - [TestSame1][Mysql3307] - Table "test.bmsql_customer_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:35:33.326 - [TestSame1][Mysql3307] - The table bmsql_customer_test has already exist. 
[INFO ] 2024-07-25 14:35:33.380 - [TestSame1][Mysql3307] - Table "test.bmsql_order_line_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:35:33.380 - [TestSame1][Mysql3307] - The table bmsql_order_line_test has already exist. 
[INFO ] 2024-07-25 14:35:33.451 - [TestSame1][DamengSource] - Query table 'bmsql_history_test' counts: 11262 
[INFO ] 2024-07-25 14:35:33.451 - [TestSame1][Mysql3307] - Table "test.bmsql_warehouse_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:35:33.451 - [TestSame1][Mysql3307] - The table bmsql_warehouse_test has already exist. 
[INFO ] 2024-07-25 14:35:33.501 - [TestSame1][Mysql3307] - Table "test.bmsql_oorder_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:35:33.502 - [TestSame1][Mysql3307] - The table bmsql_oorder_test has already exist. 
[INFO ] 2024-07-25 14:35:34.033 - [TestSame1][DamengSource] - Table [bmsql_history_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:35:34.045 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_district_test, offset: null 
[INFO ] 2024-07-25 14:35:34.045 - [TestSame1][DamengSource] - Table bmsql_district_test is going to be initial synced 
[INFO ] 2024-07-25 14:35:34.070 - [TestSame1][DamengSource] - Query table 'bmsql_district_test' counts: 10 
[INFO ] 2024-07-25 14:35:34.101 - [TestSame1][DamengSource] - Table [bmsql_district_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:35:34.101 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_new_order_test, offset: null 
[INFO ] 2024-07-25 14:35:34.125 - [TestSame1][DamengSource] - Table bmsql_new_order_test is going to be initial synced 
[INFO ] 2024-07-25 14:35:34.125 - [TestSame1][DamengSource] - Query table 'bmsql_new_order_test' counts: 2408 
[INFO ] 2024-07-25 14:35:34.194 - [TestSame1][DamengSource] - Table [bmsql_new_order_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:35:34.194 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_item_test, offset: null 
[INFO ] 2024-07-25 14:35:34.194 - [TestSame1][DamengSource] - Table bmsql_item_test is going to be initial synced 
[INFO ] 2024-07-25 14:35:34.214 - [TestSame1][DamengSource] - Query table 'bmsql_item_test' counts: 10000 
[INFO ] 2024-07-25 14:35:34.651 - [TestSame1][DamengSource] - Table [bmsql_item_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:35:34.651 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_config_test, offset: null 
[INFO ] 2024-07-25 14:35:34.651 - [TestSame1][DamengSource] - Table bmsql_config_test is going to be initial synced 
[INFO ] 2024-07-25 14:35:34.666 - [TestSame1][DamengSource] - Table [bmsql_config_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:35:34.666 - [TestSame1][DamengSource] - Query table 'bmsql_config_test' counts: 4 
[INFO ] 2024-07-25 14:35:34.667 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_customer_test, offset: null 
[INFO ] 2024-07-25 14:35:34.667 - [TestSame1][DamengSource] - Table bmsql_customer_test is going to be initial synced 
[INFO ] 2024-07-25 14:35:34.868 - [TestSame1][DamengSource] - Query table 'bmsql_customer_test' counts: 10000 
[INFO ] 2024-07-25 14:35:35.623 - [TestSame1][DamengSource] - Table [bmsql_customer_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:35:35.623 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_order_line_test, offset: null 
[INFO ] 2024-07-25 14:35:35.660 - [TestSame1][DamengSource] - Table bmsql_order_line_test is going to be initial synced 
[INFO ] 2024-07-25 14:35:35.660 - [TestSame1][DamengSource] - Query table 'bmsql_order_line_test' counts: 105317 
[INFO ] 2024-07-25 14:35:42.481 - [TestSame1][DamengSource] - Table [bmsql_order_line_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:35:42.481 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_warehouse_test, offset: null 
[INFO ] 2024-07-25 14:35:42.482 - [TestSame1][DamengSource] - Table bmsql_warehouse_test is going to be initial synced 
[INFO ] 2024-07-25 14:35:42.510 - [TestSame1][DamengSource] - Query table 'bmsql_warehouse_test' counts: 1 
[INFO ] 2024-07-25 14:35:42.524 - [TestSame1][DamengSource] - Table [bmsql_warehouse_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:35:42.524 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_oorder_test, offset: null 
[INFO ] 2024-07-25 14:35:42.524 - [TestSame1][DamengSource] - Table bmsql_oorder_test is going to be initial synced 
[INFO ] 2024-07-25 14:35:42.728 - [TestSame1][DamengSource] - Query table 'bmsql_oorder_test' counts: 11728 
[INFO ] 2024-07-25 14:35:43.124 - [TestSame1][DamengSource] - Table [bmsql_oorder_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:35:43.124 - [TestSame1][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:35:43.124 - [TestSame1][DamengSource] - Incremental sync starting... 
[INFO ] 2024-07-25 14:35:43.125 - [TestSame1][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:35:43.126 - [TestSame1][DamengSource] - Starting stream read, table list: [bmsql_history_test, bmsql_district_test, bmsql_new_order_test, bmsql_item_test, bmsql_config_test, bmsql_customer_test, bmsql_order_line_test, bmsql_warehouse_test, bmsql_oorder_test], offset: {"sortString":null,"offsetValue":null,"lastScn":40632797,"pendingScn":40632797,"timestamp":null} 
[INFO ] 2024-07-25 14:35:43.564 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:35:46.194 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:35:48.844 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:35:51.277 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:35:51.889 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:35:52.182 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:35:54.814 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:35:57.178 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:35:59.818 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:02.187 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:06.847 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:07.173 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:09.814 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:10.162 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:10.774 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:13.154 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:15.804 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:18.238 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:20.881 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:23.218 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:28.014 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:30.437 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:32.848 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:35.488 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:40.079 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:40.440 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:42.928 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:45.590 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:45.871 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:46.238 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:48.884 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:51.413 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:53.884 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:54.111 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:56.590 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:59.057 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:36:59.480 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:02.104 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:02.359 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:07.000 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:09.364 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:09.971 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:12.469 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:14.885 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:17.387 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:17.994 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:18.210 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:20.898 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:23.444 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:28.000 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:30.393 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:32.945 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:35.369 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:39.969 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:42.608 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:44.915 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:47.557 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:51.982 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:54.559 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:57.084 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:37:59.656 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:04.092 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:06.637 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:09.197 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:11.822 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:14.213 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:18.980 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:21.405 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:23.877 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:26.511 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:30.960 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:33.553 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:36.181 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:38.718 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:41.146 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:45.754 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:48.553 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:51.070 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:53.579 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:38:58.019 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:00.673 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:03.249 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:05.793 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:08.440 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:13.024 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:15.666 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:17.949 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:20.588 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:23.140 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:28.528 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:31.113 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:33.678 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:35.998 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:38.986 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:41.547 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:46.173 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:48.744 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:51.217 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:53.956 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:39:56.310 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:00.990 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:03.537 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:06.135 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:08.679 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:11.310 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:15.849 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:18.366 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:20.889 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:23.478 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:28.113 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:30.681 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:33.267 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:33.878 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:36.718 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:39.090 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:41.732 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:44.161 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:49.120 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:51.766 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:52.177 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:54.605 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:54.886 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:56.132 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] running status set to false 
[INFO ] 2024-07-25 14:40:56.132 - [TestSame1][DamengSource] - Log Miner is shutting down... 
[WARN ] 2024-07-25 14:40:56.153 - [TestSame1][DamengSource] - Stop PDK connector node failed: Unknown exception occur when operate table: unknown | Associate id: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 14:40:56.153 - [TestSame1][DamengSource] - PDK connector node released: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 14:40:56.153 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] schema data cleaned 
[INFO ] 2024-07-25 14:40:56.155 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] monitor closed 
[INFO ] 2024-07-25 14:40:56.155 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] close complete, cost 27 ms 
[INFO ] 2024-07-25 14:40:56.169 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] running status set to false 
[INFO ] 2024-07-25 14:40:56.169 - [TestSame1][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 14:40:56.169 - [TestSame1][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 14:40:56.169 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] schema data cleaned 
[INFO ] 2024-07-25 14:40:56.169 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] monitor closed 
[INFO ] 2024-07-25 14:40:56.189 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] close complete, cost 14 ms 
[INFO ] 2024-07-25 14:40:56.189 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:40:56.582 - [TestSame1][DamengSource] - Incremental sync completed 
[INFO ] 2024-07-25 14:40:56.889 - [TestSame1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 14:40:56.890 - [TestSame1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4170cf8b 
[INFO ] 2024-07-25 14:40:57.001 - [TestSame1] - Stop task milestones: 66a1e3d9349bc63fe9d87400(TestSame1)  
[INFO ] 2024-07-25 14:40:57.012 - [TestSame1] - Stopped task aspect(s) 
[INFO ] 2024-07-25 14:40:57.012 - [TestSame1] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 14:40:57.028 - [TestSame1] - Remove memory task client succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 14:40:57.029 - [TestSame1] - Destroy memory task client cache succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 14:41:18.081 - [TestSame1] - Task initialization... 
[INFO ] 2024-07-25 14:41:18.081 - [TestSame1] - Start task milestones: 66a1e3d9349bc63fe9d87400(TestSame1) 
[INFO ] 2024-07-25 14:41:18.283 - [TestSame1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-25 14:41:18.283 - [TestSame1] - The engine receives TestSame1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 14:41:18.332 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:41:18.332 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:41:18.332 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 14:41:18.332 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 14:41:18.973 - [TestSame1][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-25 14:41:18.974 - [TestSame1][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 14:41:19.003 - [TestSame1][Mysql3307] - Table "test.bmsql_history_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:41:19.003 - [TestSame1][Mysql3307] - The table bmsql_history_test has already exist. 
[INFO ] 2024-07-25 14:41:19.079 - [TestSame1][Mysql3307] - Table "test.bmsql_district_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:41:19.079 - [TestSame1][Mysql3307] - The table bmsql_district_test has already exist. 
[INFO ] 2024-07-25 14:41:19.120 - [TestSame1][Mysql3307] - Table "test.bmsql_new_order_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:41:19.120 - [TestSame1][Mysql3307] - The table bmsql_new_order_test has already exist. 
[INFO ] 2024-07-25 14:41:19.149 - [TestSame1][DamengSource] - Source node "DamengSource" read batch size: 100 
[INFO ] 2024-07-25 14:41:19.149 - [TestSame1][DamengSource] - Source node "DamengSource" event queue capacity: 200 
[INFO ] 2024-07-25 14:41:19.149 - [TestSame1][DamengSource] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 14:41:19.153 - [TestSame1][Mysql3307] - Table "test.bmsql_item_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:41:19.163 - [TestSame1][Mysql3307] - The table bmsql_item_test has already exist. 
[INFO ] 2024-07-25 14:41:19.174 - [TestSame1][DamengSource] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":40632991,"pendingScn":40632991,"timestamp":null} 
[INFO ] 2024-07-25 14:41:19.174 - [TestSame1][DamengSource] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-25 14:41:19.197 - [TestSame1][Mysql3307] - Table "test.bmsql_config_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:41:19.197 - [TestSame1][Mysql3307] - The table bmsql_config_test has already exist. 
[INFO ] 2024-07-25 14:41:19.223 - [TestSame1][DamengSource] - Initial sync started 
[INFO ] 2024-07-25 14:41:19.223 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_history_test, offset: null 
[INFO ] 2024-07-25 14:41:19.244 - [TestSame1][DamengSource] - Table bmsql_history_test is going to be initial synced 
[INFO ] 2024-07-25 14:41:19.244 - [TestSame1][Mysql3307] - Table "test.bmsql_customer_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:41:19.244 - [TestSame1][Mysql3307] - The table bmsql_customer_test has already exist. 
[INFO ] 2024-07-25 14:41:19.446 - [TestSame1][DamengSource] - Query table 'bmsql_history_test' counts: 11262 
[INFO ] 2024-07-25 14:41:19.494 - [TestSame1][Mysql3307] - Table "test.bmsql_order_line_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:41:19.495 - [TestSame1][Mysql3307] - The table bmsql_order_line_test has already exist. 
[INFO ] 2024-07-25 14:41:19.556 - [TestSame1][Mysql3307] - Table "test.bmsql_warehouse_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:41:19.556 - [TestSame1][Mysql3307] - The table bmsql_warehouse_test has already exist. 
[INFO ] 2024-07-25 14:41:19.594 - [TestSame1][Mysql3307] - Table "test.bmsql_oorder_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:41:19.594 - [TestSame1][Mysql3307] - The table bmsql_oorder_test has already exist. 
[INFO ] 2024-07-25 14:41:20.065 - [TestSame1][DamengSource] - Table [bmsql_history_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:41:20.066 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_district_test, offset: null 
[INFO ] 2024-07-25 14:41:20.066 - [TestSame1][DamengSource] - Table bmsql_district_test is going to be initial synced 
[INFO ] 2024-07-25 14:41:20.131 - [TestSame1][DamengSource] - Query table 'bmsql_district_test' counts: 10 
[INFO ] 2024-07-25 14:41:20.131 - [TestSame1][DamengSource] - Table [bmsql_district_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:41:20.131 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_new_order_test, offset: null 
[INFO ] 2024-07-25 14:41:20.131 - [TestSame1][DamengSource] - Table bmsql_new_order_test is going to be initial synced 
[INFO ] 2024-07-25 14:41:20.223 - [TestSame1][DamengSource] - Query table 'bmsql_new_order_test' counts: 2408 
[INFO ] 2024-07-25 14:41:20.223 - [TestSame1][DamengSource] - Table [bmsql_new_order_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:41:20.223 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_item_test, offset: null 
[INFO ] 2024-07-25 14:41:20.223 - [TestSame1][DamengSource] - Table bmsql_item_test is going to be initial synced 
[INFO ] 2024-07-25 14:41:20.249 - [TestSame1][DamengSource] - Query table 'bmsql_item_test' counts: 10000 
[INFO ] 2024-07-25 14:41:20.722 - [TestSame1][DamengSource] - Table [bmsql_item_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:41:20.722 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_config_test, offset: null 
[INFO ] 2024-07-25 14:41:20.731 - [TestSame1][DamengSource] - Table bmsql_config_test is going to be initial synced 
[INFO ] 2024-07-25 14:41:20.731 - [TestSame1][DamengSource] - Table [bmsql_config_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:41:20.731 - [TestSame1][DamengSource] - Query table 'bmsql_config_test' counts: 4 
[INFO ] 2024-07-25 14:41:20.731 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_customer_test, offset: null 
[INFO ] 2024-07-25 14:41:20.745 - [TestSame1][DamengSource] - Table bmsql_customer_test is going to be initial synced 
[INFO ] 2024-07-25 14:41:20.745 - [TestSame1][DamengSource] - Query table 'bmsql_customer_test' counts: 10000 
[INFO ] 2024-07-25 14:41:21.661 - [TestSame1][DamengSource] - Table [bmsql_customer_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:41:21.661 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_order_line_test, offset: null 
[INFO ] 2024-07-25 14:41:21.661 - [TestSame1][DamengSource] - Table bmsql_order_line_test is going to be initial synced 
[INFO ] 2024-07-25 14:41:21.866 - [TestSame1][DamengSource] - Query table 'bmsql_order_line_test' counts: 105317 
[INFO ] 2024-07-25 14:41:27.424 - [TestSame1][DamengSource] - Table [bmsql_order_line_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:41:27.424 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_warehouse_test, offset: null 
[INFO ] 2024-07-25 14:41:27.440 - [TestSame1][DamengSource] - Table bmsql_warehouse_test is going to be initial synced 
[INFO ] 2024-07-25 14:41:27.440 - [TestSame1][DamengSource] - Table [bmsql_warehouse_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:41:27.447 - [TestSame1][DamengSource] - Query table 'bmsql_warehouse_test' counts: 1 
[INFO ] 2024-07-25 14:41:27.447 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_oorder_test, offset: null 
[INFO ] 2024-07-25 14:41:27.471 - [TestSame1][DamengSource] - Table bmsql_oorder_test is going to be initial synced 
[INFO ] 2024-07-25 14:41:27.471 - [TestSame1][DamengSource] - Query table 'bmsql_oorder_test' counts: 11728 
[INFO ] 2024-07-25 14:41:27.937 - [TestSame1][DamengSource] - Table [bmsql_oorder_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:41:27.939 - [TestSame1][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:41:27.939 - [TestSame1][DamengSource] - Incremental sync starting... 
[INFO ] 2024-07-25 14:41:27.939 - [TestSame1][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:41:27.939 - [TestSame1][DamengSource] - Starting stream read, table list: [bmsql_history_test, bmsql_district_test, bmsql_new_order_test, bmsql_item_test, bmsql_config_test, bmsql_customer_test, bmsql_order_line_test, bmsql_warehouse_test, bmsql_oorder_test], offset: {"sortString":null,"offsetValue":null,"lastScn":40632991,"pendingScn":40632991,"timestamp":null} 
[INFO ] 2024-07-25 14:41:28.344 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:30.831 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:33.483 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:35.782 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:38.417 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:43.067 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:45.830 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:48.322 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:51.047 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:53.434 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:41:58.149 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:00.745 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:03.541 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:06.139 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:08.775 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:13.106 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:15.747 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:18.150 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:20.816 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:21.342 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] running status set to false 
[INFO ] 2024-07-25 14:42:21.343 - [TestSame1][DamengSource] - Log Miner is shutting down... 
[WARN ] 2024-07-25 14:42:21.351 - [TestSame1][DamengSource] - Stop PDK connector node failed: Unknown exception occur when operate table: unknown | Associate id: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 14:42:21.351 - [TestSame1][DamengSource] - PDK connector node released: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 14:42:21.351 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] schema data cleaned 
[INFO ] 2024-07-25 14:42:21.352 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] monitor closed 
[INFO ] 2024-07-25 14:42:21.352 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] close complete, cost 11 ms 
[INFO ] 2024-07-25 14:42:21.352 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] running status set to false 
[INFO ] 2024-07-25 14:42:21.385 - [TestSame1][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 14:42:21.385 - [TestSame1][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 14:42:21.386 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] schema data cleaned 
[INFO ] 2024-07-25 14:42:21.386 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] monitor closed 
[INFO ] 2024-07-25 14:42:21.591 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] close complete, cost 33 ms 
[INFO ] 2024-07-25 14:42:22.091 - [TestSame1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 14:42:22.091 - [TestSame1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7af7ee4e 
[INFO ] 2024-07-25 14:42:22.212 - [TestSame1] - Stop task milestones: 66a1e3d9349bc63fe9d87400(TestSame1)  
[INFO ] 2024-07-25 14:42:22.212 - [TestSame1] - Stopped task aspect(s) 
[INFO ] 2024-07-25 14:42:22.212 - [TestSame1] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 14:42:22.233 - [TestSame1] - Remove memory task client succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 14:42:22.233 - [TestSame1] - Destroy memory task client cache succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 14:42:23.381 - [TestSame1][DamengSource] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-25 14:42:23.587 - [TestSame1][DamengSource] - java.lang.RuntimeException: java.lang.RuntimeException: check log file error <-- Error Message -->
java.lang.RuntimeException: java.lang.RuntimeException: check log file error

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: check log file error
	io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.checkLogNameWithScn(SingleDamengLogMiner.java:103)
	io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:49)
	io.tapdata.connector.dameng.cdc.DamengCdcRunner.startCdcRunner(DamengCdcRunner.java:38)
	io.tapdata.connector.dameng.DamengConnector.streamRead(DamengConnector.java:392)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.RuntimeException: check log file error
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: check log file error
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:76)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.startCdcRunner(DamengCdcRunner.java:38)
	at io.tapdata.connector.dameng.DamengConnector.streamRead(DamengConnector.java:392)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.RuntimeException: check log file error
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.checkLogNameWithScn(SingleDamengLogMiner.java:103)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:49)
	... 20 more

[INFO ] 2024-07-25 14:42:38.538 - [TestSame1] - Task initialization... 
[INFO ] 2024-07-25 14:42:38.539 - [TestSame1] - Start task milestones: 66a1e3d9349bc63fe9d87400(TestSame1) 
[INFO ] 2024-07-25 14:42:38.728 - [TestSame1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-25 14:42:38.728 - [TestSame1] - The engine receives TestSame1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 14:42:38.770 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:42:38.771 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:42:38.771 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 14:42:38.771 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 14:42:39.424 - [TestSame1][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-25 14:42:39.424 - [TestSame1][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 14:42:39.445 - [TestSame1][Mysql3307] - Table "test.bmsql_history_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:42:39.533 - [TestSame1][Mysql3307] - The table bmsql_history_test has already exist. 
[INFO ] 2024-07-25 14:42:39.533 - [TestSame1][Mysql3307] - Table "test.bmsql_district_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:42:39.533 - [TestSame1][Mysql3307] - The table bmsql_district_test has already exist. 
[INFO ] 2024-07-25 14:42:39.569 - [TestSame1][DamengSource] - Source node "DamengSource" read batch size: 100 
[INFO ] 2024-07-25 14:42:39.569 - [TestSame1][DamengSource] - Source node "DamengSource" event queue capacity: 200 
[INFO ] 2024-07-25 14:42:39.569 - [TestSame1][DamengSource] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 14:42:39.573 - [TestSame1][Mysql3307] - Table "test.bmsql_new_order_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:42:39.573 - [TestSame1][Mysql3307] - The table bmsql_new_order_test has already exist. 
[INFO ] 2024-07-25 14:42:39.592 - [TestSame1][DamengSource] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":40633028,"pendingScn":40633028,"timestamp":null} 
[INFO ] 2024-07-25 14:42:39.595 - [TestSame1][DamengSource] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-25 14:42:39.613 - [TestSame1][Mysql3307] - Table "test.bmsql_item_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:42:39.613 - [TestSame1][Mysql3307] - The table bmsql_item_test has already exist. 
[INFO ] 2024-07-25 14:42:39.643 - [TestSame1][DamengSource] - Initial sync started 
[INFO ] 2024-07-25 14:42:39.643 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_history_test, offset: null 
[INFO ] 2024-07-25 14:42:39.643 - [TestSame1][DamengSource] - Table bmsql_history_test is going to be initial synced 
[INFO ] 2024-07-25 14:42:39.702 - [TestSame1][Mysql3307] - Table "test.bmsql_config_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:42:39.702 - [TestSame1][Mysql3307] - The table bmsql_config_test has already exist. 
[INFO ] 2024-07-25 14:42:39.702 - [TestSame1][Mysql3307] - Table "test.bmsql_customer_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:42:39.702 - [TestSame1][Mysql3307] - The table bmsql_customer_test has already exist. 
[INFO ] 2024-07-25 14:42:39.751 - [TestSame1][Mysql3307] - Table "test.bmsql_order_line_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:42:39.751 - [TestSame1][Mysql3307] - The table bmsql_order_line_test has already exist. 
[INFO ] 2024-07-25 14:42:39.814 - [TestSame1][DamengSource] - Query table 'bmsql_history_test' counts: 11262 
[INFO ] 2024-07-25 14:42:40.116 - [TestSame1][Mysql3307] - Table "test.bmsql_warehouse_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:42:40.116 - [TestSame1][Mysql3307] - The table bmsql_warehouse_test has already exist. 
[INFO ] 2024-07-25 14:42:40.153 - [TestSame1][Mysql3307] - Table "test.bmsql_oorder_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:42:40.153 - [TestSame1][Mysql3307] - The table bmsql_oorder_test has already exist. 
[INFO ] 2024-07-25 14:42:40.637 - [TestSame1][DamengSource] - Table [bmsql_history_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:42:40.637 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_district_test, offset: null 
[INFO ] 2024-07-25 14:42:40.637 - [TestSame1][DamengSource] - Table bmsql_district_test is going to be initial synced 
[INFO ] 2024-07-25 14:42:40.684 - [TestSame1][DamengSource] - Table [bmsql_district_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:42:40.684 - [TestSame1][DamengSource] - Query table 'bmsql_district_test' counts: 10 
[INFO ] 2024-07-25 14:42:40.684 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_new_order_test, offset: null 
[INFO ] 2024-07-25 14:42:40.684 - [TestSame1][DamengSource] - Table bmsql_new_order_test is going to be initial synced 
[INFO ] 2024-07-25 14:42:40.775 - [TestSame1][DamengSource] - Query table 'bmsql_new_order_test' counts: 2408 
[INFO ] 2024-07-25 14:42:40.775 - [TestSame1][DamengSource] - Table [bmsql_new_order_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:42:40.775 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_item_test, offset: null 
[INFO ] 2024-07-25 14:42:40.775 - [TestSame1][DamengSource] - Table bmsql_item_test is going to be initial synced 
[INFO ] 2024-07-25 14:42:40.978 - [TestSame1][DamengSource] - Query table 'bmsql_item_test' counts: 10000 
[INFO ] 2024-07-25 14:42:41.204 - [TestSame1][DamengSource] - Table [bmsql_item_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:42:41.204 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_config_test, offset: null 
[INFO ] 2024-07-25 14:42:41.204 - [TestSame1][DamengSource] - Table bmsql_config_test is going to be initial synced 
[INFO ] 2024-07-25 14:42:41.217 - [TestSame1][DamengSource] - Table [bmsql_config_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:42:41.217 - [TestSame1][DamengSource] - Query table 'bmsql_config_test' counts: 4 
[INFO ] 2024-07-25 14:42:41.217 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_customer_test, offset: null 
[INFO ] 2024-07-25 14:42:41.217 - [TestSame1][DamengSource] - Table bmsql_customer_test is going to be initial synced 
[INFO ] 2024-07-25 14:42:41.430 - [TestSame1][DamengSource] - Query table 'bmsql_customer_test' counts: 10000 
[INFO ] 2024-07-25 14:42:42.352 - [TestSame1][DamengSource] - Table [bmsql_customer_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:42:42.352 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_order_line_test, offset: null 
[INFO ] 2024-07-25 14:42:42.352 - [TestSame1][DamengSource] - Table bmsql_order_line_test is going to be initial synced 
[INFO ] 2024-07-25 14:42:42.373 - [TestSame1][DamengSource] - Query table 'bmsql_order_line_test' counts: 105317 
[INFO ] 2024-07-25 14:42:51.508 - [TestSame1][DamengSource] - Table [bmsql_order_line_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:42:51.508 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_warehouse_test, offset: null 
[INFO ] 2024-07-25 14:42:51.508 - [TestSame1][DamengSource] - Table bmsql_warehouse_test is going to be initial synced 
[INFO ] 2024-07-25 14:42:51.531 - [TestSame1][DamengSource] - Query table 'bmsql_warehouse_test' counts: 1 
[INFO ] 2024-07-25 14:42:51.531 - [TestSame1][DamengSource] - Table [bmsql_warehouse_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:42:51.532 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_oorder_test, offset: null 
[INFO ] 2024-07-25 14:42:51.532 - [TestSame1][DamengSource] - Table bmsql_oorder_test is going to be initial synced 
[INFO ] 2024-07-25 14:42:51.737 - [TestSame1][DamengSource] - Query table 'bmsql_oorder_test' counts: 11728 
[INFO ] 2024-07-25 14:42:52.175 - [TestSame1][DamengSource] - Table [bmsql_oorder_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:42:52.175 - [TestSame1][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:42:52.175 - [TestSame1][DamengSource] - Incremental sync starting... 
[INFO ] 2024-07-25 14:42:52.175 - [TestSame1][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:42:52.175 - [TestSame1][DamengSource] - Starting stream read, table list: [bmsql_history_test, bmsql_district_test, bmsql_new_order_test, bmsql_item_test, bmsql_config_test, bmsql_customer_test, bmsql_order_line_test, bmsql_warehouse_test, bmsql_oorder_test], offset: {"sortString":null,"offsetValue":null,"lastScn":40633028,"pendingScn":40633028,"timestamp":null} 
[INFO ] 2024-07-25 14:42:52.423 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:55.084 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:42:57.591 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:00.445 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:03.086 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:05.572 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:08.419 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:13.049 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:15.419 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:18.251 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:20.688 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:23.303 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:27.935 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:30.574 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:31.134 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:31.749 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:32.360 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:43:34.396 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] running status set to false 
[INFO ] 2024-07-25 14:43:34.397 - [TestSame1][DamengSource] - Log Miner is shutting down... 
[WARN ] 2024-07-25 14:43:34.405 - [TestSame1][DamengSource] - Stop PDK connector node failed: Unknown exception occur when operate table: unknown | Associate id: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 14:43:34.405 - [TestSame1][DamengSource] - PDK connector node released: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 14:43:34.405 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] schema data cleaned 
[INFO ] 2024-07-25 14:43:34.405 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] monitor closed 
[INFO ] 2024-07-25 14:43:34.407 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] close complete, cost 12 ms 
[INFO ] 2024-07-25 14:43:34.407 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] running status set to false 
[INFO ] 2024-07-25 14:43:34.437 - [TestSame1][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 14:43:34.437 - [TestSame1][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 14:43:34.437 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] schema data cleaned 
[INFO ] 2024-07-25 14:43:34.437 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] monitor closed 
[INFO ] 2024-07-25 14:43:34.642 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] close complete, cost 30 ms 
[INFO ] 2024-07-25 14:43:36.448 - [TestSame1][DamengSource] - Incremental sync completed 
[INFO ] 2024-07-25 14:43:36.448 - [TestSame1][DamengSource] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-25 14:43:36.654 - [TestSame1][DamengSource] - java.lang.RuntimeException: java.lang.RuntimeException: check log file error <-- Error Message -->
java.lang.RuntimeException: java.lang.RuntimeException: check log file error

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: check log file error
	io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.checkLogNameWithScn(SingleDamengLogMiner.java:103)
	io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:49)
	io.tapdata.connector.dameng.cdc.DamengCdcRunner.startCdcRunner(DamengCdcRunner.java:38)
	io.tapdata.connector.dameng.DamengConnector.streamRead(DamengConnector.java:392)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.RuntimeException: check log file error
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: check log file error
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:76)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.startCdcRunner(DamengCdcRunner.java:38)
	at io.tapdata.connector.dameng.DamengConnector.streamRead(DamengConnector.java:392)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.RuntimeException: check log file error
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.checkLogNameWithScn(SingleDamengLogMiner.java:103)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:49)
	... 20 more

[INFO ] 2024-07-25 14:43:37.311 - [TestSame1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 14:43:37.311 - [TestSame1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@de5cccc 
[INFO ] 2024-07-25 14:43:37.420 - [TestSame1] - Stop task milestones: 66a1e3d9349bc63fe9d87400(TestSame1)  
[INFO ] 2024-07-25 14:43:37.433 - [TestSame1] - Stopped task aspect(s) 
[INFO ] 2024-07-25 14:43:37.434 - [TestSame1] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 14:43:37.457 - [TestSame1] - Remove memory task client succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 14:43:37.457 - [TestSame1] - Destroy memory task client cache succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 14:43:50.663 - [TestSame1] - Task initialization... 
[INFO ] 2024-07-25 14:43:50.664 - [TestSame1] - Start task milestones: 66a1e3d9349bc63fe9d87400(TestSame1) 
[INFO ] 2024-07-25 14:43:50.942 - [TestSame1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-25 14:43:50.942 - [TestSame1] - The engine receives TestSame1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 14:43:50.987 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:43:50.987 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] start preload schema,table counts: 9 
[INFO ] 2024-07-25 14:43:50.987 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] preload schema finished, cost 1 ms 
[INFO ] 2024-07-25 14:43:50.987 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 14:43:51.662 - [TestSame1][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-25 14:43:51.662 - [TestSame1][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 14:43:51.703 - [TestSame1][Mysql3307] - Table "test.bmsql_history_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:43:51.705 - [TestSame1][Mysql3307] - The table bmsql_history_test has already exist. 
[INFO ] 2024-07-25 14:43:51.778 - [TestSame1][Mysql3307] - Table "test.bmsql_district_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:43:51.778 - [TestSame1][Mysql3307] - The table bmsql_district_test has already exist. 
[INFO ] 2024-07-25 14:43:51.792 - [TestSame1][DamengSource] - Source node "DamengSource" read batch size: 100 
[INFO ] 2024-07-25 14:43:51.792 - [TestSame1][DamengSource] - Source node "DamengSource" event queue capacity: 200 
[INFO ] 2024-07-25 14:43:51.792 - [TestSame1][DamengSource] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 14:43:51.814 - [TestSame1][Mysql3307] - Table "test.bmsql_new_order_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:43:51.814 - [TestSame1][Mysql3307] - The table bmsql_new_order_test has already exist. 
[INFO ] 2024-07-25 14:43:51.820 - [TestSame1][DamengSource] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":40633129,"pendingScn":40633129,"timestamp":null} 
[INFO ] 2024-07-25 14:43:51.820 - [TestSame1][DamengSource] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-25 14:43:51.847 - [TestSame1][Mysql3307] - Table "test.bmsql_item_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:43:51.847 - [TestSame1][Mysql3307] - The table bmsql_item_test has already exist. 
[INFO ] 2024-07-25 14:43:51.866 - [TestSame1][DamengSource] - Initial sync started 
[INFO ] 2024-07-25 14:43:51.883 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_history_test, offset: null 
[INFO ] 2024-07-25 14:43:51.884 - [TestSame1][DamengSource] - Table bmsql_history_test is going to be initial synced 
[INFO ] 2024-07-25 14:43:51.914 - [TestSame1][Mysql3307] - Table "test.bmsql_config_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:43:51.914 - [TestSame1][Mysql3307] - The table bmsql_config_test has already exist. 
[INFO ] 2024-07-25 14:43:51.967 - [TestSame1][Mysql3307] - Table "test.bmsql_customer_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:43:51.967 - [TestSame1][Mysql3307] - The table bmsql_customer_test has already exist. 
[INFO ] 2024-07-25 14:43:52.012 - [TestSame1][Mysql3307] - Table "test.bmsql_order_line_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:43:52.012 - [TestSame1][Mysql3307] - The table bmsql_order_line_test has already exist. 
[INFO ] 2024-07-25 14:43:52.216 - [TestSame1][DamengSource] - Query table 'bmsql_history_test' counts: 11262 
[INFO ] 2024-07-25 14:43:52.552 - [TestSame1][Mysql3307] - Table "test.bmsql_warehouse_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:43:52.552 - [TestSame1][Mysql3307] - The table bmsql_warehouse_test has already exist. 
[INFO ] 2024-07-25 14:43:52.589 - [TestSame1][Mysql3307] - Table "test.bmsql_oorder_test" exists, skip auto create table 
[INFO ] 2024-07-25 14:43:52.590 - [TestSame1][Mysql3307] - The table bmsql_oorder_test has already exist. 
[INFO ] 2024-07-25 14:43:53.121 - [TestSame1][DamengSource] - Table [bmsql_history_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:43:53.121 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_district_test, offset: null 
[INFO ] 2024-07-25 14:43:53.121 - [TestSame1][DamengSource] - Table bmsql_district_test is going to be initial synced 
[INFO ] 2024-07-25 14:43:53.154 - [TestSame1][DamengSource] - Query table 'bmsql_district_test' counts: 10 
[INFO ] 2024-07-25 14:43:53.154 - [TestSame1][DamengSource] - Table [bmsql_district_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:43:53.154 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_new_order_test, offset: null 
[INFO ] 2024-07-25 14:43:53.155 - [TestSame1][DamengSource] - Table bmsql_new_order_test is going to be initial synced 
[INFO ] 2024-07-25 14:43:53.325 - [TestSame1][DamengSource] - Query table 'bmsql_new_order_test' counts: 2408 
[INFO ] 2024-07-25 14:43:53.325 - [TestSame1][DamengSource] - Table [bmsql_new_order_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:43:53.325 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_item_test, offset: null 
[INFO ] 2024-07-25 14:43:53.325 - [TestSame1][DamengSource] - Table bmsql_item_test is going to be initial synced 
[INFO ] 2024-07-25 14:43:53.370 - [TestSame1][DamengSource] - Query table 'bmsql_item_test' counts: 10000 
[INFO ] 2024-07-25 14:43:53.971 - [TestSame1][DamengSource] - Table [bmsql_item_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:43:53.971 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_config_test, offset: null 
[INFO ] 2024-07-25 14:43:53.971 - [TestSame1][DamengSource] - Table bmsql_config_test is going to be initial synced 
[INFO ] 2024-07-25 14:43:54.001 - [TestSame1][DamengSource] - Query table 'bmsql_config_test' counts: 4 
[INFO ] 2024-07-25 14:43:54.001 - [TestSame1][DamengSource] - Table [bmsql_config_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:43:54.001 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_customer_test, offset: null 
[INFO ] 2024-07-25 14:43:54.001 - [TestSame1][DamengSource] - Table bmsql_customer_test is going to be initial synced 
[INFO ] 2024-07-25 14:43:54.206 - [TestSame1][DamengSource] - Query table 'bmsql_customer_test' counts: 10000 
[INFO ] 2024-07-25 14:43:54.970 - [TestSame1][DamengSource] - Table [bmsql_customer_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:43:54.970 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_order_line_test, offset: null 
[INFO ] 2024-07-25 14:43:54.970 - [TestSame1][DamengSource] - Table bmsql_order_line_test is going to be initial synced 
[INFO ] 2024-07-25 14:43:55.170 - [TestSame1][DamengSource] - Query table 'bmsql_order_line_test' counts: 105317 
[INFO ] 2024-07-25 14:44:00.667 - [TestSame1][DamengSource] - Table [bmsql_order_line_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:44:00.667 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_warehouse_test, offset: null 
[INFO ] 2024-07-25 14:44:00.667 - [TestSame1][DamengSource] - Table bmsql_warehouse_test is going to be initial synced 
[INFO ] 2024-07-25 14:44:00.692 - [TestSame1][DamengSource] - Query table 'bmsql_warehouse_test' counts: 1 
[INFO ] 2024-07-25 14:44:00.703 - [TestSame1][DamengSource] - Table [bmsql_warehouse_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:44:00.703 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_oorder_test, offset: null 
[INFO ] 2024-07-25 14:44:00.722 - [TestSame1][DamengSource] - Table bmsql_oorder_test is going to be initial synced 
[INFO ] 2024-07-25 14:44:00.723 - [TestSame1][DamengSource] - Query table 'bmsql_oorder_test' counts: 11728 
[INFO ] 2024-07-25 14:44:01.470 - [TestSame1][DamengSource] - Table [bmsql_oorder_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 14:44:01.470 - [TestSame1][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:44:01.470 - [TestSame1][DamengSource] - Incremental sync starting... 
[INFO ] 2024-07-25 14:44:01.470 - [TestSame1][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 14:44:01.673 - [TestSame1][DamengSource] - Starting stream read, table list: [bmsql_history_test, bmsql_district_test, bmsql_new_order_test, bmsql_item_test, bmsql_config_test, bmsql_customer_test, bmsql_order_line_test, bmsql_warehouse_test, bmsql_oorder_test], offset: {"sortString":null,"offsetValue":null,"lastScn":40633129,"pendingScn":40633129,"timestamp":null} 
[INFO ] 2024-07-25 14:44:01.879 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:02.493 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:07.018 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:09.606 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:12.372 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:14.998 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:17.606 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:22.226 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:24.838 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:27.233 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:29.819 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:32.429 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:37.087 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:39.681 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:42.283 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:44.912 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:47.574 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:50.207 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:54.913 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:44:57.754 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:00.409 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:02.958 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:05.556 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:08.391 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:12.916 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:15.555 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:18.199 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:21.050 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:24.291 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:27.046 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:30.097 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:33.138 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:35.849 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:38.873 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:41.376 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:46.398 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:49.028 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:51.734 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:54.529 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:55.338 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:45:58.123 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:00.929 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:03.764 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:06.318 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:09.352 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:12.101 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:12.911 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:15.707 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:18.701 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:21.175 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:24.021 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:26.748 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:29.603 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:32.312 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:35.224 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:40.005 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:40.403 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:43.215 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:45.862 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:48.310 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:50.914 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:53.484 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:56.180 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:56.786 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:46:59.360 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:02.120 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:02.391 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:07.299 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:09.940 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:12.541 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:15.129 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:15.738 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:16.332 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:16.890 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:17.703 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:18.312 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:20.857 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:23.668 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:26.286 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:29.081 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:29.691 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:32.273 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:37.345 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:39.979 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:42.740 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:45.788 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:48.593 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:51.303 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:54.010 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:56.652 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:47:59.412 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:04.245 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:06.809 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:09.666 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:12.161 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:15.004 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:17.644 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:22.150 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:24.750 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:27.591 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:30.333 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:32.849 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:35.479 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:40.347 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:42.789 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:45.297 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:47.940 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:50.593 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:55.167 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:48:57.857 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:00.434 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:03.168 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:06.010 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:08.649 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:13.166 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:15.750 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:18.433 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:21.176 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:23.838 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:26.601 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:31.265 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:33.910 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:36.563 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:39.455 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:41.943 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:44.360 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:49.323 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:52.014 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:54.550 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:49:57.404 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:00.353 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:03.054 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:05.823 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:08.509 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:11.311 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:16.014 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:18.462 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:21.142 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:23.608 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:28.475 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:31.075 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:33.741 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:36.379 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:39.358 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:42.120 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:44.945 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:47.459 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:52.414 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:54.987 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:50:57.474 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:00.023 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:02.735 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:05.390 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:10.141 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:12.712 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:15.373 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:18.076 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:20.740 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:23.413 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:28.323 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:30.990 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:33.657 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:36.363 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:39.231 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:41.777 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:44.363 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:49.069 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:51.707 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:54.588 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:57.160 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:51:59.804 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:02.392 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:07.276 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:09.887 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:12.209 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:14.895 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:19.426 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:21.717 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:24.377 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:26.858 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:31.244 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:33.898 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:34.167 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:36.901 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:40.374 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:43.296 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:46.378 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:49.409 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:52.704 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:53.458 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:52:56.504 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:01.383 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:04.441 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:07.329 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:10.180 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:13.047 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:16.316 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:19.244 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:22.152 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:24.935 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:27.738 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:30.786 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:33.655 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:36.534 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:39.815 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:42.898 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:45.890 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:48.854 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:52.091 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:52.753 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:53.366 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:58.464 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:53:59.700 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:00.491 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:01.613 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:02.420 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:03.646 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:04.462 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:05.345 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:06.158 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:06.987 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:09.561 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:12.324 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:15.709 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:19.099 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:22.478 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:25.289 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:28.099 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:31.195 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:34.357 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:37.131 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:40.450 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:43.554 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:46.381 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:49.290 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:52.627 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:53.440 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:56.606 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:54:59.643 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:02.830 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:05.982 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:09.162 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:12.363 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:15.466 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:18.289 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:21.476 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:24.141 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:26.702 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:29.354 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:29.982 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:30.331 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:33.022 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:33.382 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:36.058 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:38.489 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:43.241 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:45.640 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:48.330 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:50.940 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:53.392 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:55:58.159 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:00.837 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:03.490 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:04.108 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:06.785 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:09.483 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:12.174 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:14.569 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:19.303 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:21.859 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:24.398 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:27.097 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:29.651 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:32.253 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:32.884 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:35.572 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:40.002 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:42.670 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:45.245 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:47.691 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:50.256 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:50.870 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:55.428 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:57.810 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:58.416 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:59.244 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:56:59.574 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:00.089 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:02.671 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:03.194 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:05.755 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:08.396 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:08.791 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:09.400 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:11.875 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:14.471 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:19.114 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:21.801 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:24.537 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:26.929 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:29.586 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:34.263 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:36.603 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:39.303 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:41.713 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:46.455 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:48.853 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:51.740 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:54.435 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:57:57.189 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:00.077 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:02.674 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:05.490 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:10.482 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:13.093 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:15.675 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:18.313 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:20.767 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:23.356 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:28.254 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:30.772 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:34.054 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:36.435 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:37.063 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:39.734 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:42.232 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:44.883 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:47.574 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:52.111 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:54.673 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:58:57.338 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:00.117 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:02.731 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:07.386 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:10.069 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:12.491 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:15.181 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:17.590 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:22.289 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:25.253 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:28.244 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:30.931 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:33.379 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:36.052 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:39.111 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:41.719 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:44.584 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:49.163 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:52.016 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:54.437 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:57.042 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 14:59:59.709 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:04.307 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:06.986 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:09.501 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:12.171 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:14.610 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:19.232 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:21.797 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:24.469 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:26.916 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:29.506 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:34.271 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:36.752 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:39.360 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:42.044 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:44.514 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:49.274 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:51.869 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:54.254 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:56.795 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:00:59.490 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:04.030 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:06.690 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:09.346 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:11.746 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:16.467 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:18.901 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:21.533 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:24.213 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:26.643 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:31.401 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:33.789 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:36.389 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:39.233 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:41.694 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:46.333 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:48.905 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:51.551 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:54.190 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:01:56.548 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:01.339 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:03.722 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:06.553 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:09.187 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:11.587 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:16.306 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:18.934 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:19.754 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:22.216 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:24.778 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:27.327 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:29.947 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:32.649 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:37.208 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:39.849 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:42.185 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:45.191 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:47.845 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:48.245 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:51.043 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:53.437 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:02:58.088 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:00.709 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:03.331 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:05.830 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:10.347 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:12.891 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:15.522 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:18.086 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:20.726 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:25.323 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:27.971 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:30.520 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:32.972 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:35.532 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:40.053 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:42.705 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:45.093 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:47.913 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:50.681 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:55.130 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:03:57.661 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:00.314 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:02.960 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:07.474 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:10.032 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:12.424 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:14.986 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:17.758 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:22.420 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:24.861 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:27.437 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:30.075 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:32.704 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:37.262 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:39.628 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:42.226 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:44.837 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:47.383 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:51.999 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:54.552 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:57.154 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:04:59.701 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:04.460 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:06.841 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:09.400 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:12.045 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:14.697 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:19.230 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:21.871 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:24.397 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:26.998 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:29.545 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:34.221 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:36.864 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:39.410 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:41.952 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:46.398 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:49.040 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:51.607 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:54.243 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:05:56.817 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:01.398 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:03.903 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:06.422 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:08.847 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:11.374 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:16.008 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:18.776 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:21.414 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:23.882 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:26.649 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:31.336 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:33.985 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:36.379 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:38.930 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:41.554 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:44.076 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:44.683 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:47.330 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:47.942 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:50.478 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:53.303 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:53.856 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:06:56.703 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:07:01.231 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:07:03.984 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:07:06.335 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:07:20.006 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:11:59.082 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:31.804 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:32.413 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:33.021 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:33.628 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:36.262 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:39.109 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:41.692 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:46.308 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:48.878 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:51.514 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:54.149 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:12:56.563 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:01.337 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:03.920 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:06.507 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:09.115 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:11.662 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:16.269 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:18.827 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:21.389 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:24.224 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:26.887 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:31.413 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:34.059 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:36.463 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:39.064 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:41.707 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:46.230 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:48.865 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:51.504 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:54.141 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:13:56.570 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:01.363 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:03.951 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:06.316 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:08.893 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:11.527 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:16.193 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:18.805 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:21.524 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:24.132 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:26.773 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:31.274 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:34.036 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:36.417 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:39.241 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:41.682 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:46.498 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:49.282 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:52.051 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:52.892 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:54.156 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:57.038 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:14:59.503 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:04.376 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:07.068 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:09.965 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:12.808 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:15.239 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:17.817 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:22.493 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:25.295 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:27.741 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:30.541 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:33.100 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:35.647 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:40.331 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:42.984 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:45.363 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:47.960 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:50.555 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:55.407 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:15:58.070 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:00.552 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:03.200 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:05.801 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:10.496 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:13.149 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:15.816 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:18.269 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:20.921 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:21.410 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:24.251 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:26.905 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:31.527 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:32.138 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:32.571 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:37.241 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:40.229 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:42.673 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:45.253 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:47.890 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 15:16:47.896 - [TestSame1][DamengSource] - Incremental sync completed 
[INFO ] 2024-07-25 15:16:47.896 - [TestSame1][DamengSource] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-25 15:16:47.908 - [TestSame1][DamengSource] - java.lang.RuntimeException: dm.jdbc.driver.DMException: 网络通信异常 <-- Error Message -->
java.lang.RuntimeException: dm.jdbc.driver.DMException: 网络通信异常

<-- Simple Stack Trace -->
Caused by: java.io.EOFException: null
	dm.jdbc.b.a.c.a(ByteArrayNode.java:132)
	dm.jdbc.b.a.a.a(Buffer.java:365)
	dm.jdbc.b.a.e(DBAccess.java:315)
	dm.jdbc.b.a.a(DBAccess.java:225)
	dm.jdbc.b.a.a(DBAccess.java:701)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: dm.jdbc.driver.DMException: 网络通信异常
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: dm.jdbc.driver.DMException: 网络通信异常
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:76)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.startCdcRunner(DamengCdcRunner.java:38)
	at io.tapdata.connector.dameng.DamengConnector.streamRead(DamengConnector.java:392)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: dm.jdbc.driver.DMException: 网络通信异常
	at dm.jdbc.driver.DBError.throwException(DBError.java:759)
	at dm.jdbc.b.a.a(DBAccess.java:235)
	at dm.jdbc.b.a.a(DBAccess.java:701)
	at dm.jdbc.driver.DmdbStatement.executeInner(DmdbStatement.java:723)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:189)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:181)
	at dm.jdbc.driver.DmdbStatement.execute(DmdbStatement.java:1230)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:51)
	... 20 more
Caused by: java.io.EOFException
	at dm.jdbc.b.a.c.a(ByteArrayNode.java:132)
	at dm.jdbc.b.a.a.a(Buffer.java:365)
	at dm.jdbc.b.a.e(DBAccess.java:315)
	at dm.jdbc.b.a.a(DBAccess.java:225)
	... 28 more

[INFO ] 2024-07-25 15:16:47.916 - [TestSame1][DamengSource] - Job suspend in error handle 
[INFO ] 2024-07-25 15:16:48.315 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] running status set to false 
[INFO ] 2024-07-25 15:16:48.318 - [TestSame1][DamengSource] - Log Miner is shutting down... 
[WARN ] 2024-07-25 15:16:48.318 - [TestSame1][DamengSource] - Stop PDK connector node failed: Unknown exception occur when operate table: unknown | Associate id: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 15:16:48.319 - [TestSame1][DamengSource] - PDK connector node released: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 15:16:48.319 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] schema data cleaned 
[INFO ] 2024-07-25 15:16:48.321 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] monitor closed 
[INFO ] 2024-07-25 15:16:48.321 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] close complete, cost 8 ms 
[INFO ] 2024-07-25 15:16:48.342 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] running status set to false 
[INFO ] 2024-07-25 15:16:48.342 - [TestSame1][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 15:16:48.342 - [TestSame1][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 15:16:48.342 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] schema data cleaned 
[INFO ] 2024-07-25 15:16:48.343 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] monitor closed 
[INFO ] 2024-07-25 15:16:48.343 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] close complete, cost 21 ms 
[INFO ] 2024-07-25 15:16:51.887 - [TestSame1] - Task [TestSame1] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-25 15:16:51.892 - [TestSame1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 15:16:51.892 - [TestSame1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@606adbef 
[INFO ] 2024-07-25 15:16:52.002 - [TestSame1] - Stop task milestones: 66a1e3d9349bc63fe9d87400(TestSame1)  
[INFO ] 2024-07-25 15:16:52.006 - [TestSame1] - Stopped task aspect(s) 
[INFO ] 2024-07-25 15:16:52.006 - [TestSame1] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 15:16:52.018 - [TestSame1] - Remove memory task client succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 15:16:52.021 - [TestSame1] - Destroy memory task client cache succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 16:13:32.302 - [TestSame1] - Task initialization... 
[INFO ] 2024-07-25 16:13:32.303 - [TestSame1] - Start task milestones: 66a1e3d9349bc63fe9d87400(TestSame1) 
[INFO ] 2024-07-25 16:13:32.496 - [TestSame1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-25 16:13:32.496 - [TestSame1] - The engine receives TestSame1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 16:13:32.545 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] start preload schema,table counts: 9 
[INFO ] 2024-07-25 16:13:32.545 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] start preload schema,table counts: 9 
[INFO ] 2024-07-25 16:13:32.545 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] preload schema finished, cost 1 ms 
[INFO ] 2024-07-25 16:13:32.545 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] preload schema finished, cost 1 ms 
[INFO ] 2024-07-25 16:13:33.363 - [TestSame1][DamengSource] - Source node "DamengSource" read batch size: 100 
[INFO ] 2024-07-25 16:13:33.363 - [TestSame1][DamengSource] - Source node "DamengSource" event queue capacity: 200 
[INFO ] 2024-07-25 16:13:33.364 - [TestSame1][DamengSource] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 16:13:33.375 - [TestSame1][DamengSource] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":40635553,"pendingScn":40635553,"timestamp":null} 
[INFO ] 2024-07-25 16:13:33.375 - [TestSame1][DamengSource] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-25 16:13:33.444 - [TestSame1][DamengSource] - Initial sync started 
[INFO ] 2024-07-25 16:13:33.446 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_history_test, offset: null 
[INFO ] 2024-07-25 16:13:33.446 - [TestSame1][DamengSource] - Table bmsql_history_test is going to be initial synced 
[INFO ] 2024-07-25 16:13:33.528 - [TestSame1][DamengSource] - Query table 'bmsql_history_test' counts: 11262 
[INFO ] 2024-07-25 16:13:33.537 - [TestSame1][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-25 16:13:33.537 - [TestSame1][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 16:13:33.556 - [TestSame1][Mysql3307] - Table "test.bmsql_history_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:13:33.685 - [TestSame1][Mysql3307] - The table bmsql_history_test has already exist. 
[INFO ] 2024-07-25 16:13:33.685 - [TestSame1][Mysql3307] - Table "test.bmsql_district_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:13:33.685 - [TestSame1][Mysql3307] - The table bmsql_district_test has already exist. 
[INFO ] 2024-07-25 16:13:33.758 - [TestSame1][Mysql3307] - Table "test.bmsql_new_order_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:13:33.758 - [TestSame1][Mysql3307] - The table bmsql_new_order_test has already exist. 
[INFO ] 2024-07-25 16:13:33.809 - [TestSame1][Mysql3307] - Table "test.bmsql_item_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:13:33.809 - [TestSame1][Mysql3307] - The table bmsql_item_test has already exist. 
[INFO ] 2024-07-25 16:13:33.883 - [TestSame1][Mysql3307] - Table "test.bmsql_config_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:13:33.883 - [TestSame1][Mysql3307] - The table bmsql_config_test has already exist. 
[INFO ] 2024-07-25 16:13:33.950 - [TestSame1][Mysql3307] - Table "test.bmsql_customer_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:13:33.950 - [TestSame1][Mysql3307] - The table bmsql_customer_test has already exist. 
[INFO ] 2024-07-25 16:13:34.000 - [TestSame1][Mysql3307] - Table "test.bmsql_order_line_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:13:34.000 - [TestSame1][Mysql3307] - The table bmsql_order_line_test has already exist. 
[INFO ] 2024-07-25 16:13:34.067 - [TestSame1][Mysql3307] - Table "test.bmsql_warehouse_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:13:34.068 - [TestSame1][Mysql3307] - The table bmsql_warehouse_test has already exist. 
[INFO ] 2024-07-25 16:13:34.105 - [TestSame1][Mysql3307] - Table "test.bmsql_oorder_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:13:34.105 - [TestSame1][Mysql3307] - The table bmsql_oorder_test has already exist. 
[INFO ] 2024-07-25 16:13:34.548 - [TestSame1][DamengSource] - Table [bmsql_history_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:13:34.549 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_district_test, offset: null 
[INFO ] 2024-07-25 16:13:34.567 - [TestSame1][DamengSource] - Table bmsql_district_test is going to be initial synced 
[INFO ] 2024-07-25 16:13:34.567 - [TestSame1][DamengSource] - Table [bmsql_district_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:13:34.574 - [TestSame1][DamengSource] - Query table 'bmsql_district_test' counts: 10 
[INFO ] 2024-07-25 16:13:34.574 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_new_order_test, offset: null 
[INFO ] 2024-07-25 16:13:34.590 - [TestSame1][DamengSource] - Table bmsql_new_order_test is going to be initial synced 
[INFO ] 2024-07-25 16:13:34.590 - [TestSame1][DamengSource] - Query table 'bmsql_new_order_test' counts: 2408 
[INFO ] 2024-07-25 16:13:34.660 - [TestSame1][DamengSource] - Table [bmsql_new_order_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:13:34.662 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_item_test, offset: null 
[INFO ] 2024-07-25 16:13:34.662 - [TestSame1][DamengSource] - Table bmsql_item_test is going to be initial synced 
[INFO ] 2024-07-25 16:13:34.867 - [TestSame1][DamengSource] - Query table 'bmsql_item_test' counts: 10000 
[INFO ] 2024-07-25 16:13:34.996 - [TestSame1][DamengSource] - Table [bmsql_item_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:13:34.996 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_config_test, offset: null 
[INFO ] 2024-07-25 16:13:34.997 - [TestSame1][DamengSource] - Table bmsql_config_test is going to be initial synced 
[INFO ] 2024-07-25 16:13:35.007 - [TestSame1][DamengSource] - Table [bmsql_config_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:13:35.007 - [TestSame1][DamengSource] - Query table 'bmsql_config_test' counts: 4 
[INFO ] 2024-07-25 16:13:35.007 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_customer_test, offset: null 
[INFO ] 2024-07-25 16:13:35.008 - [TestSame1][DamengSource] - Table bmsql_customer_test is going to be initial synced 
[INFO ] 2024-07-25 16:13:35.209 - [TestSame1][DamengSource] - Query table 'bmsql_customer_test' counts: 10000 
[INFO ] 2024-07-25 16:13:35.809 - [TestSame1][DamengSource] - Table [bmsql_customer_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:13:35.809 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_order_line_test, offset: null 
[INFO ] 2024-07-25 16:13:35.828 - [TestSame1][DamengSource] - Table bmsql_order_line_test is going to be initial synced 
[INFO ] 2024-07-25 16:13:35.828 - [TestSame1][DamengSource] - Query table 'bmsql_order_line_test' counts: 105317 
[INFO ] 2024-07-25 16:13:41.351 - [TestSame1][DamengSource] - Table [bmsql_order_line_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:13:41.359 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_warehouse_test, offset: null 
[INFO ] 2024-07-25 16:13:41.365 - [TestSame1][DamengSource] - Table bmsql_warehouse_test is going to be initial synced 
[INFO ] 2024-07-25 16:13:41.374 - [TestSame1][DamengSource] - Query table 'bmsql_warehouse_test' counts: 1 
[INFO ] 2024-07-25 16:13:41.384 - [TestSame1][DamengSource] - Table [bmsql_warehouse_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:13:41.384 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_oorder_test, offset: null 
[INFO ] 2024-07-25 16:13:41.401 - [TestSame1][DamengSource] - Table bmsql_oorder_test is going to be initial synced 
[INFO ] 2024-07-25 16:13:41.402 - [TestSame1][DamengSource] - Query table 'bmsql_oorder_test' counts: 11728 
[INFO ] 2024-07-25 16:13:41.912 - [TestSame1][DamengSource] - Table [bmsql_oorder_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:13:41.912 - [TestSame1][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 16:13:41.912 - [TestSame1][DamengSource] - Incremental sync starting... 
[INFO ] 2024-07-25 16:13:41.912 - [TestSame1][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 16:13:41.913 - [TestSame1][DamengSource] - Starting stream read, table list: [bmsql_history_test, bmsql_district_test, bmsql_new_order_test, bmsql_item_test, bmsql_config_test, bmsql_customer_test, bmsql_order_line_test, bmsql_warehouse_test, bmsql_oorder_test], offset: {"sortString":null,"offsetValue":null,"lastScn":40635553,"pendingScn":40635553,"timestamp":null} 
[INFO ] 2024-07-25 16:13:42.155 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:13:46.834 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:13:49.107 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:13:51.549 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:13:56.107 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:13:58.554 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:01.196 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:03.559 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:08.032 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:10.679 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:13.124 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:15.379 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:19.859 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:22.347 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:24.983 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:27.327 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:32.008 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:34.641 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:34.873 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:37.506 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:39.832 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:40.233 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:42.870 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:45.252 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:47.687 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:48.299 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:52.623 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:54.252 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] running status set to false 
[INFO ] 2024-07-25 16:14:54.253 - [TestSame1][DamengSource] - Log Miner is shutting down... 
[WARN ] 2024-07-25 16:14:54.264 - [TestSame1][DamengSource] - Stop PDK connector node failed: Unknown exception occur when operate table: unknown | Associate id: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 16:14:54.264 - [TestSame1][DamengSource] - PDK connector node released: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 16:14:54.264 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] schema data cleaned 
[INFO ] 2024-07-25 16:14:54.265 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] monitor closed 
[INFO ] 2024-07-25 16:14:54.266 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] close complete, cost 14 ms 
[INFO ] 2024-07-25 16:14:54.266 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] running status set to false 
[INFO ] 2024-07-25 16:14:54.282 - [TestSame1][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 16:14:54.282 - [TestSame1][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 16:14:54.282 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] schema data cleaned 
[INFO ] 2024-07-25 16:14:54.282 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] monitor closed 
[INFO ] 2024-07-25 16:14:54.295 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] close complete, cost 16 ms 
[INFO ] 2024-07-25 16:14:54.295 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:14:54.900 - [TestSame1][DamengSource] - Incremental sync completed 
[INFO ] 2024-07-25 16:14:57.686 - [TestSame1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 16:14:57.686 - [TestSame1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3866fcb4 
[INFO ] 2024-07-25 16:14:57.803 - [TestSame1] - Stop task milestones: 66a1e3d9349bc63fe9d87400(TestSame1)  
[INFO ] 2024-07-25 16:14:57.851 - [TestSame1] - Stopped task aspect(s) 
[INFO ] 2024-07-25 16:14:57.851 - [TestSame1] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 16:14:57.851 - [TestSame1] - Remove memory task client succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 16:14:57.851 - [TestSame1] - Destroy memory task client cache succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 16:15:02.253 - [TestSame1] - Task initialization... 
[INFO ] 2024-07-25 16:15:02.253 - [TestSame1] - Start task milestones: 66a1e3d9349bc63fe9d87400(TestSame1) 
[INFO ] 2024-07-25 16:15:02.408 - [TestSame1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-25 16:15:02.549 - [TestSame1] - The engine receives TestSame1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 16:15:02.549 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] start preload schema,table counts: 9 
[INFO ] 2024-07-25 16:15:02.549 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] start preload schema,table counts: 9 
[INFO ] 2024-07-25 16:15:02.549 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 16:15:02.549 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 16:15:02.773 - [TestSame1][DamengSource] - Source node "DamengSource" read batch size: 100 
[INFO ] 2024-07-25 16:15:02.773 - [TestSame1][DamengSource] - Source node "DamengSource" event queue capacity: 200 
[INFO ] 2024-07-25 16:15:02.774 - [TestSame1][DamengSource] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-25 16:15:02.775 - [TestSame1][DamengSource] - batch offset found: {"bmsql_history_test":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"bmsql_new_order_test":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"bmsql_district_test":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"bmsql_item_test":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"bmsql_config_test":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"bmsql_order_line_test":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"bmsql_customer_test":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"bmsql_warehouse_test":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"bmsql_oorder_test":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":40635553,"pendingScn":40635553,"timestamp":null} 
[INFO ] 2024-07-25 16:15:02.775 - [TestSame1][DamengSource] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-25 16:15:02.828 - [TestSame1][DamengSource] - Incremental sync starting... 
[INFO ] 2024-07-25 16:15:02.828 - [TestSame1][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 16:15:02.849 - [TestSame1][DamengSource] - Starting stream read, table list: [bmsql_history_test, bmsql_district_test, bmsql_new_order_test, bmsql_item_test, bmsql_config_test, bmsql_customer_test, bmsql_order_line_test, bmsql_warehouse_test, bmsql_oorder_test], offset: {"sortString":null,"offsetValue":null,"lastScn":40635553,"pendingScn":40635553,"timestamp":null} 
[INFO ] 2024-07-25 16:15:02.849 - [TestSame1][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-25 16:15:02.849 - [TestSame1][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 16:15:03.257 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:07.724 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:08.335 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:10.686 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:13.300 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:15.822 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:16.296 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:16.696 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:17.156 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:17.574 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:17.995 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:18.403 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:19.010 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:19.213 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:19.617 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:20.022 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:20.642 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:20.935 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:21.549 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:21.957 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:24.571 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:27.168 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:27.676 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:28.234 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:28.637 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:31.382 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:33.820 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:36.170 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:40.801 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:43.098 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:45.709 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:48.115 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:50.609 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:51.084 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:51.544 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:56.073 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:15:58.463 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:01.035 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:03.455 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:07.910 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:08.298 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:10.811 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:13.205 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:15.645 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:18.115 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:22.650 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:25.093 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:27.500 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:27.778 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] running status set to false 
[INFO ] 2024-07-25 16:16:27.778 - [TestSame1][DamengSource] - Log Miner is shutting down... 
[INFO ] 2024-07-25 16:16:27.785 - [TestSame1][DamengSource] - Incremental sync completed 
[INFO ] 2024-07-25 16:16:27.785 - [TestSame1][DamengSource] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-25 16:16:27.799 - [TestSame1][DamengSource] - java.lang.RuntimeException: java.lang.RuntimeException: check log file error <-- Error Message -->
java.lang.RuntimeException: java.lang.RuntimeException: check log file error

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: check log file error
	io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.checkLogNameWithScn(SingleDamengLogMiner.java:103)
	io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:49)
	io.tapdata.connector.dameng.cdc.DamengCdcRunner.startCdcRunner(DamengCdcRunner.java:38)
	io.tapdata.connector.dameng.DamengConnector.streamRead(DamengConnector.java:392)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.RuntimeException: check log file error
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: check log file error
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:76)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.startCdcRunner(DamengCdcRunner.java:38)
	at io.tapdata.connector.dameng.DamengConnector.streamRead(DamengConnector.java:392)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.RuntimeException: check log file error
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.checkLogNameWithScn(SingleDamengLogMiner.java:103)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:49)
	... 20 more

[WARN ] 2024-07-25 16:16:27.799 - [TestSame1][DamengSource] - Stop PDK connector node failed: Unknown exception occur when operate table: unknown | Associate id: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 16:16:27.800 - [TestSame1][DamengSource] - PDK connector node released: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 16:16:27.800 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] schema data cleaned 
[INFO ] 2024-07-25 16:16:27.803 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] monitor closed 
[INFO ] 2024-07-25 16:16:27.807 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] close complete, cost 26 ms 
[INFO ] 2024-07-25 16:16:27.808 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] running status set to false 
[INFO ] 2024-07-25 16:16:27.831 - [TestSame1][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 16:16:27.831 - [TestSame1][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 16:16:27.832 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] schema data cleaned 
[INFO ] 2024-07-25 16:16:27.832 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] monitor closed 
[INFO ] 2024-07-25 16:16:27.921 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] close complete, cost 29 ms 
[INFO ] 2024-07-25 16:16:27.922 - [TestSame1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 16:16:28.016 - [TestSame1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@62cae47e 
[INFO ] 2024-07-25 16:16:28.054 - [TestSame1] - Stop task milestones: 66a1e3d9349bc63fe9d87400(TestSame1)  
[INFO ] 2024-07-25 16:16:28.055 - [TestSame1] - Stopped task aspect(s) 
[INFO ] 2024-07-25 16:16:28.055 - [TestSame1] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 16:16:28.084 - [TestSame1] - Remove memory task client succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 16:16:28.086 - [TestSame1] - Destroy memory task client cache succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 16:16:46.173 - [TestSame1] - Task initialization... 
[INFO ] 2024-07-25 16:16:46.350 - [TestSame1] - Start task milestones: 66a1e3d9349bc63fe9d87400(TestSame1) 
[INFO ] 2024-07-25 16:16:46.350 - [TestSame1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-25 16:16:46.425 - [TestSame1] - The engine receives TestSame1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 16:16:46.425 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] start preload schema,table counts: 9 
[INFO ] 2024-07-25 16:16:46.425 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] start preload schema,table counts: 9 
[INFO ] 2024-07-25 16:16:46.425 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 16:16:46.626 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 16:16:47.176 - [TestSame1][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-25 16:16:47.176 - [TestSame1][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 16:16:47.208 - [TestSame1][Mysql3307] - Table "test.bmsql_history_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:16:47.208 - [TestSame1][Mysql3307] - The table bmsql_history_test has already exist. 
[INFO ] 2024-07-25 16:16:47.215 - [TestSame1][DamengSource] - Source node "DamengSource" read batch size: 100 
[INFO ] 2024-07-25 16:16:47.215 - [TestSame1][DamengSource] - Source node "DamengSource" event queue capacity: 200 
[INFO ] 2024-07-25 16:16:47.215 - [TestSame1][DamengSource] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 16:16:47.225 - [TestSame1][DamengSource] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":40635672,"pendingScn":40635672,"timestamp":null} 
[INFO ] 2024-07-25 16:16:47.225 - [TestSame1][DamengSource] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-25 16:16:47.277 - [TestSame1][DamengSource] - Initial sync started 
[INFO ] 2024-07-25 16:16:47.277 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_history_test, offset: null 
[INFO ] 2024-07-25 16:16:47.294 - [TestSame1][DamengSource] - Table bmsql_history_test is going to be initial synced 
[INFO ] 2024-07-25 16:16:47.294 - [TestSame1][Mysql3307] - Table "test.bmsql_district_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:16:47.294 - [TestSame1][Mysql3307] - The table bmsql_district_test has already exist. 
[INFO ] 2024-07-25 16:16:47.343 - [TestSame1][DamengSource] - Query table 'bmsql_history_test' counts: 11262 
[INFO ] 2024-07-25 16:16:47.343 - [TestSame1][Mysql3307] - Table "test.bmsql_new_order_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:16:47.343 - [TestSame1][Mysql3307] - The table bmsql_new_order_test has already exist. 
[INFO ] 2024-07-25 16:16:47.386 - [TestSame1][Mysql3307] - Table "test.bmsql_item_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:16:47.386 - [TestSame1][Mysql3307] - The table bmsql_item_test has already exist. 
[INFO ] 2024-07-25 16:16:47.420 - [TestSame1][Mysql3307] - Table "test.bmsql_config_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:16:47.420 - [TestSame1][Mysql3307] - The table bmsql_config_test has already exist. 
[INFO ] 2024-07-25 16:16:47.454 - [TestSame1][Mysql3307] - Table "test.bmsql_customer_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:16:47.454 - [TestSame1][Mysql3307] - The table bmsql_customer_test has already exist. 
[INFO ] 2024-07-25 16:16:47.492 - [TestSame1][Mysql3307] - Table "test.bmsql_order_line_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:16:47.492 - [TestSame1][Mysql3307] - The table bmsql_order_line_test has already exist. 
[INFO ] 2024-07-25 16:16:47.534 - [TestSame1][Mysql3307] - Table "test.bmsql_warehouse_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:16:47.534 - [TestSame1][Mysql3307] - The table bmsql_warehouse_test has already exist. 
[INFO ] 2024-07-25 16:16:47.568 - [TestSame1][Mysql3307] - Table "test.bmsql_oorder_test" exists, skip auto create table 
[INFO ] 2024-07-25 16:16:47.568 - [TestSame1][Mysql3307] - The table bmsql_oorder_test has already exist. 
[INFO ] 2024-07-25 16:16:47.968 - [TestSame1][DamengSource] - Table [bmsql_history_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:16:47.968 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_district_test, offset: null 
[INFO ] 2024-07-25 16:16:47.968 - [TestSame1][DamengSource] - Table bmsql_district_test is going to be initial synced 
[INFO ] 2024-07-25 16:16:47.979 - [TestSame1][DamengSource] - Table [bmsql_district_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:16:47.979 - [TestSame1][DamengSource] - Query table 'bmsql_district_test' counts: 10 
[INFO ] 2024-07-25 16:16:47.979 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_new_order_test, offset: null 
[INFO ] 2024-07-25 16:16:47.979 - [TestSame1][DamengSource] - Table bmsql_new_order_test is going to be initial synced 
[INFO ] 2024-07-25 16:16:48.042 - [TestSame1][DamengSource] - Query table 'bmsql_new_order_test' counts: 2408 
[INFO ] 2024-07-25 16:16:48.043 - [TestSame1][DamengSource] - Table [bmsql_new_order_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:16:48.043 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_item_test, offset: null 
[INFO ] 2024-07-25 16:16:48.043 - [TestSame1][DamengSource] - Table bmsql_item_test is going to be initial synced 
[INFO ] 2024-07-25 16:16:48.247 - [TestSame1][DamengSource] - Query table 'bmsql_item_test' counts: 10000 
[INFO ] 2024-07-25 16:16:48.419 - [TestSame1][DamengSource] - Table [bmsql_item_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:16:48.419 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_config_test, offset: null 
[INFO ] 2024-07-25 16:16:48.419 - [TestSame1][DamengSource] - Table bmsql_config_test is going to be initial synced 
[INFO ] 2024-07-25 16:16:48.428 - [TestSame1][DamengSource] - Table [bmsql_config_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:16:48.428 - [TestSame1][DamengSource] - Query table 'bmsql_config_test' counts: 4 
[INFO ] 2024-07-25 16:16:48.428 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_customer_test, offset: null 
[INFO ] 2024-07-25 16:16:48.428 - [TestSame1][DamengSource] - Table bmsql_customer_test is going to be initial synced 
[INFO ] 2024-07-25 16:16:48.633 - [TestSame1][DamengSource] - Query table 'bmsql_customer_test' counts: 10000 
[INFO ] 2024-07-25 16:16:49.174 - [TestSame1][DamengSource] - Table [bmsql_customer_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:16:49.174 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_order_line_test, offset: null 
[INFO ] 2024-07-25 16:16:49.196 - [TestSame1][DamengSource] - Table bmsql_order_line_test is going to be initial synced 
[INFO ] 2024-07-25 16:16:49.196 - [TestSame1][DamengSource] - Query table 'bmsql_order_line_test' counts: 105317 
[INFO ] 2024-07-25 16:16:54.033 - [TestSame1][DamengSource] - Table [bmsql_order_line_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:16:54.033 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_warehouse_test, offset: null 
[INFO ] 2024-07-25 16:16:54.044 - [TestSame1][DamengSource] - Table bmsql_warehouse_test is going to be initial synced 
[INFO ] 2024-07-25 16:16:54.045 - [TestSame1][DamengSource] - Query table 'bmsql_warehouse_test' counts: 1 
[INFO ] 2024-07-25 16:16:54.058 - [TestSame1][DamengSource] - Table [bmsql_warehouse_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:16:54.058 - [TestSame1][DamengSource] - Starting batch read, table name: bmsql_oorder_test, offset: null 
[INFO ] 2024-07-25 16:16:54.077 - [TestSame1][DamengSource] - Table bmsql_oorder_test is going to be initial synced 
[INFO ] 2024-07-25 16:16:54.077 - [TestSame1][DamengSource] - Query table 'bmsql_oorder_test' counts: 11728 
[INFO ] 2024-07-25 16:16:54.537 - [TestSame1][DamengSource] - Table [bmsql_oorder_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 16:16:54.538 - [TestSame1][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 16:16:54.538 - [TestSame1][DamengSource] - Incremental sync starting... 
[INFO ] 2024-07-25 16:16:54.538 - [TestSame1][DamengSource] - Initial sync completed 
[INFO ] 2024-07-25 16:16:54.709 - [TestSame1][DamengSource] - Starting stream read, table list: [bmsql_history_test, bmsql_district_test, bmsql_new_order_test, bmsql_item_test, bmsql_config_test, bmsql_customer_test, bmsql_order_line_test, bmsql_warehouse_test, bmsql_oorder_test], offset: {"sortString":null,"offsetValue":null,"lastScn":40635672,"pendingScn":40635672,"timestamp":null} 
[INFO ] 2024-07-25 16:16:54.709 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:16:57.146 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:01.636 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:04.157 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:06.787 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:09.187 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:13.761 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:16.396 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:18.693 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:21.326 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:25.776 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:28.212 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:30.476 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:34.950 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:37.580 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:40.013 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:42.446 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:46.905 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:49.206 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:51.845 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:54.144 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:17:58.806 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:01.229 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:03.666 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:07.977 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:10.423 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:13.070 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:15.497 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:17.740 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:18.192 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:20.635 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:21.450 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:23.741 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:24.348 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:28.652 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:31.284 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:31.694 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:34.126 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:36.453 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:39.091 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:39.494 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:43.755 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:46.398 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:46.583 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:46.981 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:47.590 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:50.026 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:52.447 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:54.760 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:18:57.208 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:01.888 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:04.327 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:06.767 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:09.095 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:13.574 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:16.032 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:18.678 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:23.155 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:25.593 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:28.039 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:30.488 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:34.754 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:37.196 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:39.623 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:44.124 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:46.575 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:49.209 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:51.636 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:56.081 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:19:58.343 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:20:00.772 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:20:03.218 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:20:07.662 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:20:10.147 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:20:12.579 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:20:17.021 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:20:19.663 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:20:21.909 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:20:24.344 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:20:28.819 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:20:31.271 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:20:33.697 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:20:36.330 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:20:40.651 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:20:43.293 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:20:45.735 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:20:52.302 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:20:52.709 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:05.165 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:13.798 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:16.848 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:17.137 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:19.791 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:22.220 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:22.625 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:25.051 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:25.455 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:27.888 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:30.211 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:32.851 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:33.092 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:33.697 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:38.167 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:40.447 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:41.050 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:43.281 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:43.681 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:46.311 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:48.571 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:53.038 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:55.508 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:21:57.938 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:00.581 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:05.048 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:07.484 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:09.923 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:12.356 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:16.687 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:19.150 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:21.807 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:24.239 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:28.673 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:31.329 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:33.572 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:38.249 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:40.517 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:42.943 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:45.382 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:49.865 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:52.291 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:54.724 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:22:57.156 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:01.830 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:04.471 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:06.882 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:09.531 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:12.128 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:16.604 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:19.257 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:21.559 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:23.984 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:24.589 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:28.907 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:31.339 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:33.973 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:36.375 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:41.056 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:41.282 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:43.743 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:46.382 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:48.637 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:51.256 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:53.576 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:53.979 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:54.585 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:54.809 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:55.219 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:23:57.841 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:24:11.676 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:24:21.248 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:24:26.645 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:24:29.282 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:24:31.559 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:24:33.985 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:24:43.126 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:25:42.949 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:25:44.926 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:25:45.179 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:25:59.117 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:25:59.138 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:25:59.539 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:26:23.624 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:26:25.231 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:26:27.887 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:26:38.659 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:26:38.861 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:26:40.329 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:26:40.731 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:26:43.011 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:27:02.936 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:27:17.850 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:27:20.298 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:27:22.746 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:27:25.185 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:27:27.404 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:27:31.869 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:27:34.264 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:27:36.733 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:27:39.146 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:27:43.633 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:27:46.268 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:27:48.709 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:27:53.058 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:27:55.495 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:27:57.917 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:00.354 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:04.826 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:07.249 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:09.687 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:14.167 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:16.795 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:19.031 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:21.499 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:25.955 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:28.371 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:30.789 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:33.199 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:37.636 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:40.072 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:46.162 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:46.564 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:48.998 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:49.608 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:50.015 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:52.253 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:54.745 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:55.141 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:28:57.566 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:00.202 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:00.488 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:03.112 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:03.339 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:07.986 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:10.251 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:12.689 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:17.323 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:19.752 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:22.006 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:24.476 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:29.127 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:31.567 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:31.782 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:34.212 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:36.841 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:41.322 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:43.543 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:45.965 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:48.414 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:52.872 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:55.532 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:29:57.771 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:00.210 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:04.697 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:07.328 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:09.589 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:12.224 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:16.882 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:19.185 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:21.830 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:26.148 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:28.576 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:31.003 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:33.423 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:38.096 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:40.369 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:42.996 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:45.477 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:49.940 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:52.367 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:54.817 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:30:57.436 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:31:01.795 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:31:04.212 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:31:06.641 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:31:09.269 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:31:30.396 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:31:34.195 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:31:56.265 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:32:01.134 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:32:01.543 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:32:15.294 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:32:15.579 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:32:19.140 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:32:21.778 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:32:25.525 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:32:27.610 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:32:27.985 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:32:29.284 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:32:31.600 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:32:48.759 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:32:58.373 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:32:58.983 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:32:59.511 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:33:00.120 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:33:00.730 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:33:01.111 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:33:03.722 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:33:06.321 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:33:06.881 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:33:09.517 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:33:14.168 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:33:16.557 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:34:54.644 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:09.104 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:11.686 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:14.242 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:16.872 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:19.515 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:21.881 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:22.404 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:25.026 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:27.665 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:28.053 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:30.632 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:31.244 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:31.456 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:34.091 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:36.536 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:38.983 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:41.413 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:41.787 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:42.296 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:42.870 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:50.538 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:52.317 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:55.573 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:36:57.891 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:37:00.571 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:37:02.746 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:37:03.357 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:38:43.404 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:38:49.699 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:38:57.587 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:39:13.183 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:39:17.414 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:39:20.976 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:40:05.848 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:40:21.618 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:40:22.337 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:40:48.981 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:40:49.587 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:01.582 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:01.905 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:04.429 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:07.014 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:09.591 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:10.151 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:10.759 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:13.391 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:15.781 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:18.333 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:23.386 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:26.021 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:28.643 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:31.053 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:31.601 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:32.212 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:32.825 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:35.210 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:37.769 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:38.376 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:41.012 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:43.649 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:46.091 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:48.710 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:53.371 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:55.732 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:41:58.269 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:00.817 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:03.446 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:08.094 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:10.467 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:13.041 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:15.673 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:20.349 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:22.764 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:25.315 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:27.954 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:30.588 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:35.448 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:37.892 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:40.523 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:43.371 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:45.771 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:48.317 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:50.950 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:51.556 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:56.034 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:58.876 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:59.207 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:42:59.716 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:00.320 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:00.927 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:03.345 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:12.869 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:13.213 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:13.697 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:16.317 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:18.968 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:19.291 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:19.825 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:20.435 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:22.991 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:25.514 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:28.089 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:30.733 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:35.196 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:37.797 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:40.360 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:43.178 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:45.569 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:46.088 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:48.714 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:53.375 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:53.998 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:56.439 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:43:59.003 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:01.636 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:04.281 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:06.667 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:11.242 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:13.762 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:16.600 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:19.078 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:21.714 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:26.383 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:28.758 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:31.307 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:33.947 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:36.597 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:41.016 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:43.570 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:46.403 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:48.771 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:53.448 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:55.849 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:44:58.505 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:01.152 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:03.594 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:08.268 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:10.707 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:13.252 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:15.890 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:18.538 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:23.001 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:25.650 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:28.049 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:30.897 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:35.323 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:37.969 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:40.411 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:42.948 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:45.583 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:50.265 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:52.682 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:55.247 - [TestSame1][DamengSource] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-07-25_13-48-25.log 
[INFO ] 2024-07-25 16:45:56.829 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] running status set to false 
[INFO ] 2024-07-25 16:45:56.829 - [TestSame1][DamengSource] - Log Miner is shutting down... 
[WARN ] 2024-07-25 16:45:56.839 - [TestSame1][DamengSource] - Stop PDK connector node failed: Unknown exception occur when operate table: unknown | Associate id: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 16:45:56.839 - [TestSame1][DamengSource] - PDK connector node released: HazelcastSourcePdkDataNode-43bd2b27-a791-4ba7-b73c-f7705c7380e0 
[INFO ] 2024-07-25 16:45:56.839 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] schema data cleaned 
[INFO ] 2024-07-25 16:45:56.840 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] monitor closed 
[INFO ] 2024-07-25 16:45:56.845 - [TestSame1][DamengSource] - Node DamengSource[43bd2b27-a791-4ba7-b73c-f7705c7380e0] close complete, cost 19 ms 
[INFO ] 2024-07-25 16:45:56.845 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] running status set to false 
[INFO ] 2024-07-25 16:45:56.868 - [TestSame1][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 16:45:56.868 - [TestSame1][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-945148df-6ff1-4e87-8c7c-894a3f48737d 
[INFO ] 2024-07-25 16:45:56.868 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] schema data cleaned 
[INFO ] 2024-07-25 16:45:56.868 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] monitor closed 
[INFO ] 2024-07-25 16:45:56.869 - [TestSame1][Mysql3307] - Node Mysql3307[945148df-6ff1-4e87-8c7c-894a3f48737d] close complete, cost 25 ms 
[INFO ] 2024-07-25 16:45:58.864 - [TestSame1][DamengSource] - Incremental sync completed 
[INFO ] 2024-07-25 16:45:58.864 - [TestSame1][DamengSource] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-25 16:45:59.066 - [TestSame1][DamengSource] - java.lang.RuntimeException: java.lang.RuntimeException: check log file error <-- Error Message -->
java.lang.RuntimeException: java.lang.RuntimeException: check log file error

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: check log file error
	io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.checkLogNameWithScn(SingleDamengLogMiner.java:103)
	io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:49)
	io.tapdata.connector.dameng.cdc.DamengCdcRunner.startCdcRunner(DamengCdcRunner.java:38)
	io.tapdata.connector.dameng.DamengConnector.streamRead(DamengConnector.java:392)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.RuntimeException: check log file error
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: check log file error
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:76)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.startCdcRunner(DamengCdcRunner.java:38)
	at io.tapdata.connector.dameng.DamengConnector.streamRead(DamengConnector.java:392)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.RuntimeException: check log file error
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.checkLogNameWithScn(SingleDamengLogMiner.java:103)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:49)
	... 20 more

[INFO ] 2024-07-25 16:46:01.621 - [TestSame1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 16:46:01.745 - [TestSame1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2810e983 
[INFO ] 2024-07-25 16:46:01.746 - [TestSame1] - Stop task milestones: 66a1e3d9349bc63fe9d87400(TestSame1)  
[INFO ] 2024-07-25 16:46:01.764 - [TestSame1] - Stopped task aspect(s) 
[INFO ] 2024-07-25 16:46:01.764 - [TestSame1] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 16:46:01.783 - [TestSame1] - Remove memory task client succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
[INFO ] 2024-07-25 16:46:01.786 - [TestSame1] - Destroy memory task client cache succeed, task: TestSame1[66a1e3d9349bc63fe9d87400] 
