[INFO ] 2024-07-04 14:25:52.047 - [来自SouceMysql的共享挖掘任务] - Start task milestones: 66841ae5976c4665eeaed7b2(来自SouceMysql的共享挖掘任务) 
[INFO ] 2024-07-04 14:25:53.475 - [来自SouceMysql的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-04 14:25:53.517 - [来自SouceMysql的共享挖掘任务] - The engine receives 来自SouceMysql的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-04 14:25:54.401 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-04 14:25:54.422 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Node SouceMysql[1c297bb9243248bf82d9e94203e1eaf3] start preload schema,table counts: 3 
[INFO ] 2024-07-04 14:25:54.424 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-04 14:25:54.429 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Node SouceMysql[1c297bb9243248bf82d9e94203e1eaf3] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 14:25:54.600 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6685289966ab5ede8ab9bd4f, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=667413fd7b5e1f6c3b139e78_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1199739013, shareCdcTaskId=66841ae5976c4665eeaed7b2, connectionId=667413fd7b5e1f6c3b139e78) 
[INFO ] 2024-07-04 14:25:54.616 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66851c5b66ab5ede8ab76a3c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=667413fd7b5e1f6c3b139e78__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_985897921, shareCdcTaskId=66841ae5976c4665eeaed7b2, connectionId=667413fd7b5e1f6c3b139e78) 
[INFO ] 2024-07-04 14:25:55.154 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SouceMysql的共享挖掘任务_CUSTOMER', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_1199739013', head seq: 0, tail seq: 0 
[INFO ] 2024-07-04 14:25:55.155 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SouceMysql的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_985897921', head seq: 0, tail seq: 6154 
[INFO ] 2024-07-04 14:25:55.259 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66841ae566ab5ede8aa3b331, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=667413fd7b5e1f6c3b139e78_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-7751783, shareCdcTaskId=66841ae5976c4665eeaed7b2, connectionId=667413fd7b5e1f6c3b139e78) 
[INFO ] 2024-07-04 14:25:55.268 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SouceMysql的共享挖掘任务_POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-7751783', head seq: 0, tail seq: 14 
[INFO ] 2024-07-04 14:25:55.284 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-04 14:25:55.948 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Source node "SouceMysql" read batch size: 2000 
[INFO ] 2024-07-04 14:25:55.951 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Source node "SouceMysql" event queue capacity: 4000 
[INFO ] 2024-07-04 14:25:55.960 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-04 14:25:55.985 - [来自SouceMysql的共享挖掘任务][SouceMysql] - batch offset found: {},stream offset found: {"name":"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f","offset":{"{\"server\":\"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f\"}":"{\"ts_sec\":1720006650,\"file\":\"binlog.000032\",\"pos\":19992886,\"row\":1,\"server_id\":1,\"event\":2}"}} 
[INFO ] 2024-07-04 14:25:55.986 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-04 14:25:56.117 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Starting stream read, table list: [CUSTOMER, _tapdata_heartbeat_table, POLICY], offset: {"name":"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f","offset":{"{\"server\":\"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f\"}":"{\"ts_sec\":1720006650,\"file\":\"binlog.000032\",\"pos\":19992886,\"row\":1,\"server_id\":1,\"event\":2}"}} 
[INFO ] 2024-07-04 14:25:56.117 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Starting mysql cdc, server name: 56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f 
[INFO ] 2024-07-04 14:25:56.157 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1694780836
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f
  database.port: 3306
  threadName: Debezium-Mysql-Connector-56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f
  database.hostname: localhost
  database.password: ********
  name: 56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f
  pdk.offset.string: {"name":"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f","offset":{"{\"server\":\"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f\"}":"{\"ts_sec\":1720006650,\"file\":\"binlog.000032\",\"pos\":19992886,\"row\":1,\"server_id\":1,\"event\":2}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test2.CUSTOMER,test2._tapdata_heartbeat_table,test2.POLICY
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test2
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-04 14:25:57.079 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Connector Mysql incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table, POLICY], data change syncing 
[INFO ] 2024-07-04 14:39:00.761 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Node SouceMysql[1c297bb9243248bf82d9e94203e1eaf3] running status set to false 
[INFO ] 2024-07-04 14:39:00.893 - [来自SouceMysql的共享挖掘任务][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-04 14:39:00.896 - [来自SouceMysql的共享挖掘任务][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-1c297bb9243248bf82d9e94203e1eaf3 
[INFO ] 2024-07-04 14:39:00.896 - [来自SouceMysql的共享挖掘任务][SouceMysql] - PDK connector node released: HazelcastSourcePdkShareCDCNode-1c297bb9243248bf82d9e94203e1eaf3 
[INFO ] 2024-07-04 14:39:00.897 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Node SouceMysql[1c297bb9243248bf82d9e94203e1eaf3] schema data cleaned 
[INFO ] 2024-07-04 14:39:00.897 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Node SouceMysql[1c297bb9243248bf82d9e94203e1eaf3] monitor closed 
[INFO ] 2024-07-04 14:39:00.898 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Node SouceMysql[1c297bb9243248bf82d9e94203e1eaf3] close complete, cost 160 ms 
[INFO ] 2024-07-04 14:39:00.898 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[a3bda18cf014436c8dc83da1d6d64726] running status set to false 
[WARN ] 2024-07-04 14:39:06.348 - [来自SouceMysql的共享挖掘任务] - Save milestone failed: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/update": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[WARN ] 2024-07-04 14:39:06.754 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Save to snapshot failed, collection: Task/syncProgress/66841ae5976c4665eeaed7b2, object: {1c297bb9243248bf82d9e94203e1eaf3,a3bda18cf014436c8dc83da1d6d64726=SyncProgress{eventSerialNo=487, syncStage='CDC', batchOffset='{}', streamOffset='MysqlStreamOffset{name='56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f', offset={{"server":"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f"}={"ts_sec":1720075072,"file":"binlog.000032","pos":20254150,"server_id":1}}}'}}, errors: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/syncProgress/66841ae5976c4665eeaed7b2": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). 
[INFO ] 2024-07-04 14:40:06.961 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-04 14:40:06.961 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-04 14:40:06.961 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[a3bda18cf014436c8dc83da1d6d64726] schema data cleaned 
[INFO ] 2024-07-04 14:40:06.961 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[a3bda18cf014436c8dc83da1d6d64726] monitor closed 
[INFO ] 2024-07-04 14:40:06.962 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[a3bda18cf014436c8dc83da1d6d64726] close complete, cost 66063 ms 
[INFO ] 2024-07-04 14:40:09.742 - [来自SouceMysql的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-04 14:40:09.744 - [来自SouceMysql的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5738cfb1 
[INFO ] 2024-07-04 14:40:09.744 - [来自SouceMysql的共享挖掘任务] - Stop task milestones: 66841ae5976c4665eeaed7b2(来自SouceMysql的共享挖掘任务)  
[INFO ] 2024-07-04 14:40:09.856 - [来自SouceMysql的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-04 14:40:09.856 - [来自SouceMysql的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-04 14:40:09.857 - [来自SouceMysql的共享挖掘任务] - Remove memory task client succeed, task: 来自SouceMysql的共享挖掘任务[66841ae5976c4665eeaed7b2] 
[INFO ] 2024-07-04 14:40:10.059 - [来自SouceMysql的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自SouceMysql的共享挖掘任务[66841ae5976c4665eeaed7b2] 
[INFO ] 2024-07-04 14:55:16.776 - [来自SouceMysql的共享挖掘任务] - Start task milestones: 66841ae5976c4665eeaed7b2(来自SouceMysql的共享挖掘任务) 
[INFO ] 2024-07-04 14:55:16.961 - [来自SouceMysql的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-04 14:55:17.143 - [来自SouceMysql的共享挖掘任务] - The engine receives 来自SouceMysql的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-04 14:55:17.144 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Node SouceMysql[1c297bb9243248bf82d9e94203e1eaf3] start preload schema,table counts: 3 
[INFO ] 2024-07-04 14:55:17.144 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Node SouceMysql[1c297bb9243248bf82d9e94203e1eaf3] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 14:55:17.189 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-04 14:55:17.191 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-04 14:55:17.218 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6685289966ab5ede8ab9bd4f, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=667413fd7b5e1f6c3b139e78_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1199739013, shareCdcTaskId=66841ae5976c4665eeaed7b2, connectionId=667413fd7b5e1f6c3b139e78) 
[INFO ] 2024-07-04 14:55:17.218 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66841ae566ab5ede8aa3b331, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=667413fd7b5e1f6c3b139e78_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-7751783, shareCdcTaskId=66841ae5976c4665eeaed7b2, connectionId=667413fd7b5e1f6c3b139e78) 
[INFO ] 2024-07-04 14:55:17.363 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SouceMysql的共享挖掘任务_CUSTOMER', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_1199739013', head seq: 0, tail seq: 0 
[INFO ] 2024-07-04 14:55:17.364 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SouceMysql的共享挖掘任务_POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-7751783', head seq: 0, tail seq: 713 
[INFO ] 2024-07-04 14:55:17.384 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66851c5b66ab5ede8ab76a3c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=667413fd7b5e1f6c3b139e78__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_985897921, shareCdcTaskId=66841ae5976c4665eeaed7b2, connectionId=667413fd7b5e1f6c3b139e78) 
[INFO ] 2024-07-04 14:55:17.385 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SouceMysql的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_985897921', head seq: 0, tail seq: 6596 
[INFO ] 2024-07-04 14:55:17.566 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-04 14:55:17.566 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Source node "SouceMysql" read batch size: 2000 
[INFO ] 2024-07-04 14:55:17.566 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Source node "SouceMysql" event queue capacity: 4000 
[INFO ] 2024-07-04 14:55:17.571 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-04 14:55:17.571 - [来自SouceMysql的共享挖掘任务][SouceMysql] - batch offset found: {},stream offset found: {"name":"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f","offset":{"{\"server\":\"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f\"}":"{\"ts_sec\":1720075072,\"file\":\"binlog.000032\",\"pos\":20254150,\"server_id\":1}"}} 
[INFO ] 2024-07-04 14:55:17.727 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-04 14:55:17.727 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Starting stream read, table list: [CUSTOMER, _tapdata_heartbeat_table, POLICY], offset: {"name":"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f","offset":{"{\"server\":\"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f\"}":"{\"ts_sec\":1720075072,\"file\":\"binlog.000032\",\"pos\":20254150,\"server_id\":1}"}} 
[INFO ] 2024-07-04 14:55:17.750 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Starting mysql cdc, server name: 56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f 
[INFO ] 2024-07-04 14:55:17.750 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 769101940
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f
  database.port: 3306
  threadName: Debezium-Mysql-Connector-56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f
  database.hostname: localhost
  database.password: ********
  name: 56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f
  pdk.offset.string: {"name":"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f","offset":{"{\"server\":\"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f\"}":"{\"ts_sec\":1720075072,\"file\":\"binlog.000032\",\"pos\":20254150,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test2.CUSTOMER,test2._tapdata_heartbeat_table,test2.POLICY
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test2
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-04 14:55:17.955 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Connector Mysql incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table, POLICY], data change syncing 
[INFO ] 2024-07-04 15:02:12.806 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Node SouceMysql[1c297bb9243248bf82d9e94203e1eaf3] running status set to false 
[INFO ] 2024-07-04 15:46:29.852 - [来自SouceMysql的共享挖掘任务] - Start task milestones: 66841ae5976c4665eeaed7b2(来自SouceMysql的共享挖掘任务) 
[INFO ] 2024-07-04 15:46:30.845 - [来自SouceMysql的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-04 15:46:30.846 - [来自SouceMysql的共享挖掘任务] - The engine receives 来自SouceMysql的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-04 15:46:31.217 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Node SouceMysql[1c297bb9243248bf82d9e94203e1eaf3] start preload schema,table counts: 3 
[INFO ] 2024-07-04 15:46:31.218 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Node SouceMysql[1c297bb9243248bf82d9e94203e1eaf3] preload schema finished, cost 1 ms 
[INFO ] 2024-07-04 15:46:31.219 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-04 15:46:31.348 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-04 15:46:31.357 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66841ae566ab5ede8aa3b331, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=667413fd7b5e1f6c3b139e78_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-7751783, shareCdcTaskId=66841ae5976c4665eeaed7b2, connectionId=667413fd7b5e1f6c3b139e78) 
[INFO ] 2024-07-04 15:46:31.359 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6685289966ab5ede8ab9bd4f, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=667413fd7b5e1f6c3b139e78_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1199739013, shareCdcTaskId=66841ae5976c4665eeaed7b2, connectionId=667413fd7b5e1f6c3b139e78) 
[INFO ] 2024-07-04 15:46:31.632 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SouceMysql的共享挖掘任务_CUSTOMER', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_1199739013', head seq: 0, tail seq: 0 
[INFO ] 2024-07-04 15:46:31.761 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SouceMysql的共享挖掘任务_POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-7751783', head seq: 0, tail seq: 713 
[INFO ] 2024-07-04 15:46:31.762 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66851c5b66ab5ede8ab76a3c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=667413fd7b5e1f6c3b139e78__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_985897921, shareCdcTaskId=66841ae5976c4665eeaed7b2, connectionId=667413fd7b5e1f6c3b139e78) 
[INFO ] 2024-07-04 15:46:31.860 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SouceMysql的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_985897921', head seq: 0, tail seq: 6596 
[INFO ] 2024-07-04 15:46:31.860 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-04 15:46:32.722 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Source node "SouceMysql" read batch size: 2000 
[INFO ] 2024-07-04 15:46:32.723 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Source node "SouceMysql" event queue capacity: 4000 
[INFO ] 2024-07-04 15:46:32.758 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-04 15:46:32.763 - [来自SouceMysql的共享挖掘任务][SouceMysql] - batch offset found: {},stream offset found: {"name":"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f","offset":{"{\"server\":\"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f\"}":"{\"file\":\"binlog.000032\",\"pos\":20254150,\"server_id\":1}"}} 
[INFO ] 2024-07-04 15:46:32.763 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-04 15:46:32.864 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Starting stream read, table list: [CUSTOMER, _tapdata_heartbeat_table, POLICY], offset: {"name":"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f","offset":{"{\"server\":\"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f\"}":"{\"file\":\"binlog.000032\",\"pos\":20254150,\"server_id\":1}"}} 
[INFO ] 2024-07-04 15:46:32.953 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Starting mysql cdc, server name: 56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f 
[INFO ] 2024-07-04 15:46:33.061 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 198731152
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f
  database.port: 3306
  threadName: Debezium-Mysql-Connector-56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f
  database.hostname: localhost
  database.password: ********
  name: 56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f
  pdk.offset.string: {"name":"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f","offset":{"{\"server\":\"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f\"}":"{\"file\":\"binlog.000032\",\"pos\":20254150,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test2.CUSTOMER,test2._tapdata_heartbeat_table,test2.POLICY
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test2
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-04 15:46:33.981 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Connector Mysql incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table, POLICY], data change syncing 
[INFO ] 2024-07-04 16:00:57.304 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Node SouceMysql[1c297bb9243248bf82d9e94203e1eaf3] running status set to false 
[INFO ] 2024-07-04 16:00:57.434 - [来自SouceMysql的共享挖掘任务][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-04 16:00:57.436 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-07-04 16:00:57.453 - [来自SouceMysql的共享挖掘任务][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-1c297bb9243248bf82d9e94203e1eaf3 
[INFO ] 2024-07-04 16:00:57.454 - [来自SouceMysql的共享挖掘任务][SouceMysql] - PDK connector node released: HazelcastSourcePdkShareCDCNode-1c297bb9243248bf82d9e94203e1eaf3 
[INFO ] 2024-07-04 16:00:57.454 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Node SouceMysql[1c297bb9243248bf82d9e94203e1eaf3] schema data cleaned 
[INFO ] 2024-07-04 16:00:57.457 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Node SouceMysql[1c297bb9243248bf82d9e94203e1eaf3] monitor closed 
[INFO ] 2024-07-04 16:00:57.457 - [来自SouceMysql的共享挖掘任务][SouceMysql] - Node SouceMysql[1c297bb9243248bf82d9e94203e1eaf3] close complete, cost 182 ms 
[INFO ] 2024-07-04 16:00:57.466 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[a3bda18cf014436c8dc83da1d6d64726] running status set to false 
[INFO ] 2024-07-04 16:00:57.466 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-04 16:00:57.466 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-04 16:00:57.466 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[a3bda18cf014436c8dc83da1d6d64726] schema data cleaned 
[INFO ] 2024-07-04 16:00:57.467 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[a3bda18cf014436c8dc83da1d6d64726] monitor closed 
[INFO ] 2024-07-04 16:00:57.467 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[a3bda18cf014436c8dc83da1d6d64726] close complete, cost 9 ms 
[INFO ] 2024-07-04 16:00:59.407 - [来自SouceMysql的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-04 16:00:59.517 - [来自SouceMysql的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@90a053d 
[INFO ] 2024-07-04 16:00:59.518 - [来自SouceMysql的共享挖掘任务] - Stop task milestones: 66841ae5976c4665eeaed7b2(来自SouceMysql的共享挖掘任务)  
[INFO ] 2024-07-04 16:00:59.525 - [来自SouceMysql的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-04 16:00:59.525 - [来自SouceMysql的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-04 16:00:59.546 - [来自SouceMysql的共享挖掘任务] - Remove memory task client succeed, task: 来自SouceMysql的共享挖掘任务[66841ae5976c4665eeaed7b2] 
[INFO ] 2024-07-04 16:00:59.548 - [来自SouceMysql的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自SouceMysql的共享挖掘任务[66841ae5976c4665eeaed7b2] 
[INFO ] 2024-07-04 16:46:31.683 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SouceMysql的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_985897921', head seq: 0, tail seq: 6596 
[INFO ] 2024-07-04 16:46:31.688 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SouceMysql的共享挖掘任务_POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-7751783', head seq: 0, tail seq: 713 
[INFO ] 2024-07-04 16:46:31.688 - [来自SouceMysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SouceMysql的共享挖掘任务_CUSTOMER', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_1199739013', head seq: 0, tail seq: 0 
