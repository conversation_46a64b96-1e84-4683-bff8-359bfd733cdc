[INFO ] 2024-07-26 19:40:18.083 - [Heartbeat-source] - Start task milestones: 66a38b21183a6022d03ba9ff(Heartbeat-source) 
[INFO ] 2024-07-26 19:40:21.953 - [Heartbeat-source] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:40:22.037 - [Heartbeat-source] - The engine receives Heartbeat-source task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 19:40:22.510 - [Heartbeat-source][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4f8eded9-a393-48c1-8b89-033e51362511] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:40:22.516 - [Heartbeat-source][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c701e53f-a301-4029-b1d2-5f04e224ca9a] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:40:22.556 - [Heartbeat-source][_tapdata_heartbeat_table] - Exception skipping - The current exception does not match the skip exception strategy, message: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a38b13183a6022d03ba9f8_66a38b21183a6022d03ba9ff 
[INFO ] 2024-07-26 19:40:22.618 - [Heartbeat-source][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4f8eded9-a393-48c1-8b89-033e51362511] preload schema finished, cost 100 ms 
[ERROR] 2024-07-26 19:40:22.620 - [Heartbeat-source][_tapdata_heartbeat_table] - java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a38b13183a6022d03ba9f8_66a38b21183a6022d03ba9ff <-- Error Message -->
java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a38b13183a6022d03ba9f8_66a38b21183a6022d03ba9ff

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a38b13183a6022d03ba9f8_66a38b21183a6022d03ba9ff
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:423)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:210)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a38b13183a6022d03ba9f8_66a38b21183a6022d03ba9ff
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:568)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:222)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a38b13183a6022d03ba9f8_66a38b21183a6022d03ba9ff
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:210)
	at io.tapdata.schema.TapTableMap.preLoadSchema(TapTableMap.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:214)
	... 12 more
Caused by: java.lang.RuntimeException: Table name "_tapdata_heartbeat_table" not exists, qualified name: T_dummy_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_66a38b13183a6022d03ba9f8_66a38b21183a6022d03ba9ff
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:423)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:461)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 15 more

[INFO ] 2024-07-26 19:40:22.657 - [Heartbeat-source][_tapdata_heartbeat_table] - Job suspend in error handle 
[INFO ] 2024-07-26 19:40:22.663 - [Heartbeat-source][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c701e53f-a301-4029-b1d2-5f04e224ca9a] running status set to false 
[INFO ] 2024-07-26 19:40:22.667 - [Heartbeat-source][_tapdata_heartbeat_table] - PDK connector node stopped: null 
[INFO ] 2024-07-26 19:40:22.676 - [Heartbeat-source][_tapdata_heartbeat_table] - PDK connector node released: null 
[INFO ] 2024-07-26 19:40:22.676 - [Heartbeat-source][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c701e53f-a301-4029-b1d2-5f04e224ca9a] schema data cleaned 
[INFO ] 2024-07-26 19:40:22.676 - [Heartbeat-source][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c701e53f-a301-4029-b1d2-5f04e224ca9a] monitor closed 
[INFO ] 2024-07-26 19:40:22.676 - [Heartbeat-source][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[c701e53f-a301-4029-b1d2-5f04e224ca9a] close complete, cost 11 ms 
[INFO ] 2024-07-26 19:40:22.677 - [Heartbeat-source][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4f8eded9-a393-48c1-8b89-033e51362511] running status set to false 
[INFO ] 2024-07-26 19:40:22.679 - [Heartbeat-source][_tapdata_heartbeat_table] - PDK connector node stopped: null 
[INFO ] 2024-07-26 19:40:22.683 - [Heartbeat-source][_tapdata_heartbeat_table] - PDK connector node released: null 
[INFO ] 2024-07-26 19:40:22.683 - [Heartbeat-source][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4f8eded9-a393-48c1-8b89-033e51362511] schema data cleaned 
[INFO ] 2024-07-26 19:40:22.683 - [Heartbeat-source][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4f8eded9-a393-48c1-8b89-033e51362511] monitor closed 
[INFO ] 2024-07-26 19:40:22.683 - [Heartbeat-source][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[4f8eded9-a393-48c1-8b89-033e51362511] close complete, cost 8 ms 
[INFO ] 2024-07-26 19:40:22.981 - [Heartbeat-source][_tapdata_heartbeat_table] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_4f8eded9-a393-48c1-8b89-033e51362511 
[ERROR] 2024-07-26 19:40:22.982 - [Heartbeat-source][_tapdata_heartbeat_table] - Map name: PdkStateMap_4f8eded9-a393-48c1-8b89-033e51362511 <-- Error Message -->
Map name: PdkStateMap_4f8eded9-a393-48c1-8b89-033e51362511

<-- Simple Stack Trace -->
Caused by: java.lang.InterruptedException: null
	java.util.concurrent.locks.AbstractQueuedSynchronizer.acquireSharedInterruptibly(AbstractQueuedSynchronizer.java:1306)
	java.util.concurrent.Semaphore.acquire(Semaphore.java:312)
	com.mongodb.internal.connection.ConcurrentPool.acquirePermit(ConcurrentPool.java:199)
	com.mongodb.internal.connection.ConcurrentPool.get(ConcurrentPool.java:140)
	com.mongodb.internal.connection.ConcurrentPool.get(ConcurrentPool.java:123)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_4f8eded9-a393-48c1-8b89-033e51362511
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:177)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoInterruptedException: Interrupted acquiring a permit to retrieve an item from the pool 
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 13 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoInterruptedException: Interrupted acquiring a permit to retrieve an item from the pool 
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 18 more
Caused by: com.mongodb.MongoInterruptedException: Interrupted acquiring a permit to retrieve an item from the pool 
	at com.mongodb.internal.connection.ConcurrentPool.acquirePermit(ConcurrentPool.java:203)
	at com.mongodb.internal.connection.ConcurrentPool.get(ConcurrentPool.java:140)
	at com.mongodb.internal.connection.ConcurrentPool.get(ConcurrentPool.java:123)
	at com.mongodb.internal.session.ServerSessionPool.get(ServerSessionPool.java:80)
	at com.mongodb.internal.session.BaseClientSessionImpl.<init>(BaseClientSessionImpl.java:40)
	at com.mongodb.client.internal.ClientSessionImpl.<init>(ClientSessionImpl.java:48)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:110)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:249)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:190)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 19 more
Caused by: java.lang.InterruptedException
	at java.util.concurrent.locks.AbstractQueuedSynchronizer.acquireSharedInterruptibly(AbstractQueuedSynchronizer.java:1306)
	at java.util.concurrent.Semaphore.acquire(Semaphore.java:312)
	at com.mongodb.internal.connection.ConcurrentPool.acquirePermit(ConcurrentPool.java:199)
	... 36 more

[INFO ] 2024-07-26 19:40:26.146 - [Heartbeat-source] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 19:40:26.150 - [Heartbeat-source] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@60e6787c 
[INFO ] 2024-07-26 19:40:26.284 - [Heartbeat-source] - Stop task milestones: 66a38b21183a6022d03ba9ff(Heartbeat-source)  
[INFO ] 2024-07-26 19:40:26.300 - [Heartbeat-source] - Stopped task aspect(s) 
[INFO ] 2024-07-26 19:40:26.300 - [Heartbeat-source] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 19:40:26.330 - [Heartbeat-source] - Remove memory task client succeed, task: Heartbeat-source[66a38b21183a6022d03ba9ff] 
[INFO ] 2024-07-26 19:40:26.331 - [Heartbeat-source] - Destroy memory task client cache succeed, task: Heartbeat-source[66a38b21183a6022d03ba9ff] 
