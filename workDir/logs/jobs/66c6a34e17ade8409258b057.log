[INFO ] 2024-08-22 11:20:53.237 - [任务 12] - Start task milestones: 66c6a34e17ade8409258b057(任务 12) 
[INFO ] 2024-08-22 11:20:53.239 - [任务 12] - Task initialization... 
[INFO ] 2024-08-22 11:20:53.287 - [任务 12] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-08-22 11:20:53.415 - [任务 12] - The engine receives 任务 12 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-22 11:20:53.415 - [任务 12][DB2TEST - Copy] - Node DB2TEST - Copy[0d794267-cd65-491d-8048-11104b168d90] start preload schema,table counts: 1 
[INFO ] 2024-08-22 11:20:53.416 - [任务 12][TestDummy] - Node TestDummy[bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a] start preload schema,table counts: 1 
[INFO ] 2024-08-22 11:20:53.465 - [任务 12][DB2TEST - Copy] - Node DB2TEST - Copy[0d794267-cd65-491d-8048-11104b168d90] preload schema finished, cost 41 ms 
[INFO ] 2024-08-22 11:20:53.466 - [任务 12][TestDummy] - Node TestDummy[bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a] preload schema finished, cost 40 ms 
[INFO ] 2024-08-22 11:20:54.319 - [任务 12][TestDummy] - Node(TestDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-22 11:20:54.322 - [任务 12][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-22 11:20:54.562 - [任务 12][DB2TEST - Copy] - Source node "DB2TEST - Copy" read batch size: 500 
[INFO ] 2024-08-22 11:20:54.562 - [任务 12][DB2TEST - Copy] - Source node "DB2TEST - Copy" event queue capacity: 1000 
[INFO ] 2024-08-22 11:20:54.566 - [任务 12][DB2TEST - Copy] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-22 11:20:54.769 - [任务 12][DB2TEST - Copy] - Table [CUSTOMERS] not open CDC 
[INFO ] 2024-08-22 11:20:54.777 - [任务 12][DB2TEST - Copy] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724296854754} 
[INFO ] 2024-08-22 11:20:54.777 - [任务 12][DB2TEST - Copy] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-22 11:20:54.870 - [任务 12][DB2TEST - Copy] - Initial sync started 
[INFO ] 2024-08-22 11:20:54.870 - [任务 12][DB2TEST - Copy] - Starting batch read, table name: CUSTOMERS, offset: null 
[INFO ] 2024-08-22 11:20:54.946 - [任务 12][DB2TEST - Copy] - Table CUSTOMERS is going to be initial synced 
[INFO ] 2024-08-22 11:20:54.948 - [任务 12][DB2TEST - Copy] - Query table 'CUSTOMERS' counts: 1 
[INFO ] 2024-08-22 11:20:54.978 - [任务 12][DB2TEST - Copy] - Table [CUSTOMERS] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 11:20:54.978 - [任务 12][DB2TEST - Copy] - Initial sync completed 
[INFO ] 2024-08-22 11:20:54.980 - [任务 12][DB2TEST - Copy] - Incremental sync starting... 
[INFO ] 2024-08-22 11:20:54.980 - [任务 12][DB2TEST - Copy] - Initial sync completed 
[INFO ] 2024-08-22 11:20:55.190 - [任务 12][DB2TEST - Copy] - Starting stream read, table list: [CUSTOMERS], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724296854754} 
[WARN ] 2024-08-22 11:20:58.052 - [任务 12][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 1 
[WARN ] 2024-08-22 11:21:00.874 - [任务 12][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 2 
[WARN ] 2024-08-22 11:21:03.429 - [任务 12][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 3 
[WARN ] 2024-08-22 11:21:06.028 - [任务 12][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 4 
[WARN ] 2024-08-22 11:21:08.646 - [任务 12][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 5 
[WARN ] 2024-08-22 11:21:11.897 - [任务 12][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 6 
[WARN ] 2024-08-22 11:21:14.537 - [任务 12][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 7 
[WARN ] 2024-08-22 11:21:17.111 - [任务 12][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 8 
[WARN ] 2024-08-22 11:21:20.167 - [任务 12][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 9 
[INFO ] 2024-08-22 11:21:22.800 - [任务 12][DB2TEST - Copy] - Incremental sync completed 
[ERROR] 2024-08-22 11:21:22.844 - [任务 12][DB2TEST - Copy] - java.lang.RuntimeException: Exception occurs in Grpc Log Miner service <-- Error Message -->
java.lang.RuntimeException: Exception occurs in Grpc Log Miner service

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Exception occurs in Grpc Log Miner service
	io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner.startMiner(Db2GrpcLogMiner.java:256)
	io.tapdata.connector.db2.cdc.Db2CdcRunner.startCdcRunner(Db2CdcRunner.java:35)
	io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:280)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Exception occurs in Grpc Log Miner service
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Exception occurs in Grpc Log Miner service
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner.startMiner(Db2GrpcLogMiner.java:256)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.startCdcRunner(Db2CdcRunner.java:35)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:280)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more

[INFO ] 2024-08-22 11:21:22.844 - [任务 12][DB2TEST - Copy] - Job suspend in error handle 
[INFO ] 2024-08-22 11:21:22.954 - [任务 12][DB2TEST - Copy] - Node DB2TEST - Copy[0d794267-cd65-491d-8048-11104b168d90] running status set to false 
[INFO ] 2024-08-22 11:21:22.955 - [任务 12][DB2TEST - Copy] - Log Miner is shutting down... 
[INFO ] 2024-08-22 11:21:23.267 - [任务 12][DB2TEST - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode-0d794267-cd65-491d-8048-11104b168d90 
[INFO ] 2024-08-22 11:21:23.268 - [任务 12][DB2TEST - Copy] - PDK connector node released: HazelcastSourcePdkDataNode-0d794267-cd65-491d-8048-11104b168d90 
[INFO ] 2024-08-22 11:21:23.274 - [任务 12][DB2TEST - Copy] - Node DB2TEST - Copy[0d794267-cd65-491d-8048-11104b168d90] schema data cleaned 
[INFO ] 2024-08-22 11:21:23.275 - [任务 12][DB2TEST - Copy] - Node DB2TEST - Copy[0d794267-cd65-491d-8048-11104b168d90] monitor closed 
[INFO ] 2024-08-22 11:21:23.292 - [任务 12][DB2TEST - Copy] - Node DB2TEST - Copy[0d794267-cd65-491d-8048-11104b168d90] close complete, cost 331 ms 
[INFO ] 2024-08-22 11:21:23.292 - [任务 12][TestDummy] - Node TestDummy[bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a] running status set to false 
[INFO ] 2024-08-22 11:21:23.328 - [任务 12][TestDummy] - Stop connector 
[INFO ] 2024-08-22 11:21:23.330 - [任务 12][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a 
[INFO ] 2024-08-22 11:21:23.332 - [任务 12][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a 
[INFO ] 2024-08-22 11:21:23.332 - [任务 12][TestDummy] - Node TestDummy[bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a] schema data cleaned 
[INFO ] 2024-08-22 11:21:23.332 - [任务 12][TestDummy] - Node TestDummy[bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a] monitor closed 
[INFO ] 2024-08-22 11:21:23.547 - [任务 12][TestDummy] - Node TestDummy[bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a] close complete, cost 41 ms 
[INFO ] 2024-08-22 11:21:27.555 - [任务 12] - Task [任务 12] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-08-22 11:21:27.611 - [任务 12] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-22 11:21:27.651 - [任务 12] - Stop task milestones: 66c6a34e17ade8409258b057(任务 12)  
[INFO ] 2024-08-22 11:21:27.652 - [任务 12] - Stopped task aspect(s) 
[INFO ] 2024-08-22 11:21:27.705 - [任务 12] - Snapshot order controller have been removed 
[INFO ] 2024-08-22 11:21:27.707 - [任务 12] - Remove memory task client succeed, task: 任务 12[66c6a34e17ade8409258b057] 
[INFO ] 2024-08-22 11:21:27.708 - [任务 12] - Destroy memory task client cache succeed, task: 任务 12[66c6a34e17ade8409258b057] 
[INFO ] 2024-08-22 11:23:23.328 - [任务 12] - Start task milestones: 66c6a34e17ade8409258b057(任务 12) 
[INFO ] 2024-08-22 11:23:23.391 - [任务 12] - Task initialization... 
[INFO ] 2024-08-22 11:23:23.393 - [任务 12] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-22 11:23:23.551 - [任务 12] - The engine receives 任务 12 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-22 11:23:23.551 - [任务 12][TestDummy] - Node TestDummy[bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a] start preload schema,table counts: 1 
[INFO ] 2024-08-22 11:23:23.617 - [任务 12][TestDummy] - Node TestDummy[bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a] preload schema finished, cost 21 ms 
[INFO ] 2024-08-22 11:23:23.617 - [任务 12][DB2TEST - Copy] - Node DB2TEST - Copy[0d794267-cd65-491d-8048-11104b168d90] start preload schema,table counts: 1 
[INFO ] 2024-08-22 11:23:23.819 - [任务 12][DB2TEST - Copy] - Node DB2TEST - Copy[0d794267-cd65-491d-8048-11104b168d90] preload schema finished, cost 18 ms 
[INFO ] 2024-08-22 11:23:24.344 - [任务 12][TestDummy] - Node(TestDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-22 11:23:24.348 - [任务 12][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-22 11:23:24.565 - [任务 12][DB2TEST - Copy] - Source node "DB2TEST - Copy" read batch size: 500 
[INFO ] 2024-08-22 11:23:24.567 - [任务 12][DB2TEST - Copy] - Source node "DB2TEST - Copy" event queue capacity: 1000 
[INFO ] 2024-08-22 11:23:24.567 - [任务 12][DB2TEST - Copy] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-22 11:23:24.876 - [任务 12][DB2TEST - Copy] - Table [CUSTOMERS] not open CDC 
[INFO ] 2024-08-22 11:23:24.881 - [任务 12][DB2TEST - Copy] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724297004873} 
[INFO ] 2024-08-22 11:23:24.881 - [任务 12][DB2TEST - Copy] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-22 11:23:24.998 - [任务 12][DB2TEST - Copy] - Initial sync started 
[INFO ] 2024-08-22 11:23:25.008 - [任务 12][DB2TEST - Copy] - Starting batch read, table name: CUSTOMERS, offset: null 
[INFO ] 2024-08-22 11:23:25.009 - [任务 12][DB2TEST - Copy] - Table CUSTOMERS is going to be initial synced 
[INFO ] 2024-08-22 11:23:25.052 - [任务 12][DB2TEST - Copy] - Table [CUSTOMERS] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 11:23:25.061 - [任务 12][DB2TEST - Copy] - Query table 'CUSTOMERS' counts: 1 
[INFO ] 2024-08-22 11:23:25.061 - [任务 12][DB2TEST - Copy] - Initial sync completed 
[INFO ] 2024-08-22 11:23:25.062 - [任务 12][DB2TEST - Copy] - Incremental sync starting... 
[INFO ] 2024-08-22 11:23:25.062 - [任务 12][DB2TEST - Copy] - Initial sync completed 
[INFO ] 2024-08-22 11:23:25.266 - [任务 12][DB2TEST - Copy] - Starting stream read, table list: [CUSTOMERS], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724297004873} 
[WARN ] 2024-08-22 11:23:54.141 - [任务 12][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 1 
[WARN ] 2024-08-22 11:23:56.842 - [任务 12][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 2 
[WARN ] 2024-08-22 11:24:04.876 - [任务 12][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 3 
[WARN ] 2024-08-22 11:24:21.795 - [任务 12][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 4 
[WARN ] 2024-08-22 11:25:25.413 - [任务 12][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 5 
[WARN ] 2024-08-22 11:25:28.151 - [任务 12][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 6 
[WARN ] 2024-08-22 11:25:30.800 - [任务 12][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 7 
[WARN ] 2024-08-22 11:25:33.399 - [任务 12][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 8 
[WARN ] 2024-08-22 11:25:36.441 - [任务 12][DB2TEST - Copy] - Retry to start Grpc Log Miner service, retry times: 9 
[INFO ] 2024-08-22 11:25:38.793 - [任务 12][DB2TEST - Copy] - Incremental sync completed 
[ERROR] 2024-08-22 11:25:38.799 - [任务 12][DB2TEST - Copy] - java.lang.RuntimeException: Exception occurs in Grpc Log Miner service <-- Error Message -->
java.lang.RuntimeException: Exception occurs in Grpc Log Miner service

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Exception occurs in Grpc Log Miner service
	io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner.startMiner(Db2GrpcLogMiner.java:256)
	io.tapdata.connector.db2.cdc.Db2CdcRunner.startCdcRunner(Db2CdcRunner.java:35)
	io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:280)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Exception occurs in Grpc Log Miner service
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Exception occurs in Grpc Log Miner service
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner.startMiner(Db2GrpcLogMiner.java:256)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.startCdcRunner(Db2CdcRunner.java:35)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:280)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more

[INFO ] 2024-08-22 11:25:39.007 - [任务 12][DB2TEST - Copy] - Job suspend in error handle 
[INFO ] 2024-08-22 11:25:39.264 - [任务 12][DB2TEST - Copy] - Node DB2TEST - Copy[0d794267-cd65-491d-8048-11104b168d90] running status set to false 
[INFO ] 2024-08-22 11:25:39.264 - [任务 12][DB2TEST - Copy] - Log Miner is shutting down... 
[INFO ] 2024-08-22 11:25:39.311 - [任务 12][DB2TEST - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode-0d794267-cd65-491d-8048-11104b168d90 
[INFO ] 2024-08-22 11:25:39.311 - [任务 12][DB2TEST - Copy] - PDK connector node released: HazelcastSourcePdkDataNode-0d794267-cd65-491d-8048-11104b168d90 
[INFO ] 2024-08-22 11:25:39.311 - [任务 12][DB2TEST - Copy] - Node DB2TEST - Copy[0d794267-cd65-491d-8048-11104b168d90] schema data cleaned 
[INFO ] 2024-08-22 11:25:39.312 - [任务 12][DB2TEST - Copy] - Node DB2TEST - Copy[0d794267-cd65-491d-8048-11104b168d90] monitor closed 
[INFO ] 2024-08-22 11:25:39.320 - [任务 12][DB2TEST - Copy] - Node DB2TEST - Copy[0d794267-cd65-491d-8048-11104b168d90] close complete, cost 55 ms 
[INFO ] 2024-08-22 11:25:39.320 - [任务 12][TestDummy] - Node TestDummy[bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a] running status set to false 
[INFO ] 2024-08-22 11:25:39.338 - [任务 12][TestDummy] - Stop connector 
[INFO ] 2024-08-22 11:25:39.345 - [任务 12][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a 
[INFO ] 2024-08-22 11:25:39.345 - [任务 12][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a 
[INFO ] 2024-08-22 11:25:39.345 - [任务 12][TestDummy] - Node TestDummy[bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a] schema data cleaned 
[INFO ] 2024-08-22 11:25:39.345 - [任务 12][TestDummy] - Node TestDummy[bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a] monitor closed 
[INFO ] 2024-08-22 11:25:39.347 - [任务 12][TestDummy] - Node TestDummy[bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a] close complete, cost 32 ms 
[INFO ] 2024-08-22 11:25:40.247 - [任务 12] - Task [任务 12] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-08-22 11:25:40.251 - [任务 12] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-22 11:25:40.271 - [任务 12] - Stop task milestones: 66c6a34e17ade8409258b057(任务 12)  
[INFO ] 2024-08-22 11:25:40.272 - [任务 12] - Stopped task aspect(s) 
[INFO ] 2024-08-22 11:25:40.272 - [任务 12] - Snapshot order controller have been removed 
[INFO ] 2024-08-22 11:25:40.285 - [任务 12] - Remove memory task client succeed, task: 任务 12[66c6a34e17ade8409258b057] 
[INFO ] 2024-08-22 11:25:40.288 - [任务 12] - Destroy memory task client cache succeed, task: 任务 12[66c6a34e17ade8409258b057] 
[INFO ] 2024-08-22 12:01:18.454 - [任务 12] - Start task milestones: 66c6a34e17ade8409258b057(任务 12) 
[INFO ] 2024-08-22 12:01:18.455 - [任务 12] - Task initialization... 
[INFO ] 2024-08-22 12:01:18.564 - [任务 12] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-22 12:01:18.711 - [任务 12] - The engine receives 任务 12 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-22 12:01:18.714 - [任务 12][DB2TEST - Copy] - Node DB2TEST - Copy[0d794267-cd65-491d-8048-11104b168d90] start preload schema,table counts: 1 
[INFO ] 2024-08-22 12:01:18.714 - [任务 12][TestDummy] - Node TestDummy[bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a] start preload schema,table counts: 1 
[INFO ] 2024-08-22 12:01:18.731 - [任务 12][DB2TEST - Copy] - Node DB2TEST - Copy[0d794267-cd65-491d-8048-11104b168d90] preload schema finished, cost 17 ms 
[INFO ] 2024-08-22 12:01:18.731 - [任务 12][TestDummy] - Node TestDummy[bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a] preload schema finished, cost 17 ms 
[INFO ] 2024-08-22 12:01:19.563 - [任务 12][DB2TEST - Copy] - Source node "DB2TEST - Copy" read batch size: 500 
[INFO ] 2024-08-22 12:01:19.563 - [任务 12][DB2TEST - Copy] - Source node "DB2TEST - Copy" event queue capacity: 1000 
[INFO ] 2024-08-22 12:01:19.563 - [任务 12][DB2TEST - Copy] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-22 12:01:19.612 - [任务 12][TestDummy] - Node(TestDummy) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-22 12:01:19.678 - [任务 12][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-08-22 12:01:19.679 - [任务 12][DB2TEST - Copy] - Table [CUSTOMERS] not open CDC 
[INFO ] 2024-08-22 12:01:19.679 - [任务 12][DB2TEST - Copy] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724299279678} 
[INFO ] 2024-08-22 12:01:19.679 - [任务 12][DB2TEST - Copy] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-22 12:01:19.719 - [任务 12][DB2TEST - Copy] - Initial sync started 
[INFO ] 2024-08-22 12:01:19.732 - [任务 12][DB2TEST - Copy] - Starting batch read, table name: CUSTOMERS, offset: null 
[INFO ] 2024-08-22 12:01:19.733 - [任务 12][DB2TEST - Copy] - Table CUSTOMERS is going to be initial synced 
[INFO ] 2024-08-22 12:01:19.769 - [任务 12][DB2TEST - Copy] - Table [CUSTOMERS] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 12:01:19.769 - [任务 12][DB2TEST - Copy] - Query table 'CUSTOMERS' counts: 1 
[INFO ] 2024-08-22 12:01:19.770 - [任务 12][DB2TEST - Copy] - Initial sync completed 
[INFO ] 2024-08-22 12:01:19.770 - [任务 12][DB2TEST - Copy] - Incremental sync starting... 
[INFO ] 2024-08-22 12:01:19.770 - [任务 12][DB2TEST - Copy] - Initial sync completed 
[INFO ] 2024-08-22 12:01:19.970 - [任务 12][DB2TEST - Copy] - Starting stream read, table list: [CUSTOMERS], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1724299279678} 
[INFO ] 2024-08-22 12:09:34.725 - [任务 12] - Stop task milestones: 66c6a34e17ade8409258b057(任务 12)  
[INFO ] 2024-08-22 12:09:34.878 - [任务 12][DB2TEST - Copy] - Node DB2TEST - Copy[0d794267-cd65-491d-8048-11104b168d90] running status set to false 
[INFO ] 2024-08-22 12:09:34.879 - [任务 12][DB2TEST - Copy] - Log Miner is shutting down... 
[ERROR] 2024-08-22 12:09:34.904 - [任务 12][DB2TEST - Copy] - io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted <-- Full Stack Trace -->
java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:567)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:223)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	... 6 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.grpc.Status.asRuntimeException(Status.java:535)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:649)
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner.startMiner(Db2GrpcLogMiner.java:175)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.startCdcRunner(Db2CdcRunner.java:35)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:280)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.InterruptedException
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.throwIfInterrupted(ClientCalls.java:750)
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.waitAndDrain(ClientCalls.java:733)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.waitForNext(ClientCalls.java:623)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:644)
	... 21 more

[INFO ] 2024-08-22 12:09:36.846 - [任务 12][DB2TEST - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode-0d794267-cd65-491d-8048-11104b168d90 
[INFO ] 2024-08-22 12:09:36.846 - [任务 12][DB2TEST - Copy] - PDK connector node released: HazelcastSourcePdkDataNode-0d794267-cd65-491d-8048-11104b168d90 
[INFO ] 2024-08-22 12:09:36.849 - [任务 12][DB2TEST - Copy] - Node DB2TEST - Copy[0d794267-cd65-491d-8048-11104b168d90] schema data cleaned 
[INFO ] 2024-08-22 12:09:36.849 - [任务 12][DB2TEST - Copy] - Node DB2TEST - Copy[0d794267-cd65-491d-8048-11104b168d90] monitor closed 
[INFO ] 2024-08-22 12:09:36.855 - [任务 12][DB2TEST - Copy] - Node DB2TEST - Copy[0d794267-cd65-491d-8048-11104b168d90] close complete, cost 1982 ms 
[INFO ] 2024-08-22 12:09:36.855 - [任务 12][TestDummy] - Node TestDummy[bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a] running status set to false 
[INFO ] 2024-08-22 12:09:36.870 - [任务 12][TestDummy] - Stop connector 
[INFO ] 2024-08-22 12:09:36.884 - [任务 12][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a 
[INFO ] 2024-08-22 12:09:36.885 - [任务 12][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a 
[INFO ] 2024-08-22 12:09:36.885 - [任务 12][TestDummy] - Node TestDummy[bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a] schema data cleaned 
[INFO ] 2024-08-22 12:09:36.885 - [任务 12][TestDummy] - Node TestDummy[bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a] monitor closed 
[INFO ] 2024-08-22 12:09:36.885 - [任务 12][TestDummy] - Node TestDummy[bb31e07b-1ad5-4f7d-a2e5-d2d8e4cd828a] close complete, cost 20 ms 
[INFO ] 2024-08-22 12:09:38.990 - [任务 12] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-22 12:09:38.991 - [任务 12] - Stopped task aspect(s) 
[INFO ] 2024-08-22 12:09:38.991 - [任务 12] - Snapshot order controller have been removed 
[INFO ] 2024-08-22 12:09:39.008 - [任务 12] - Remove memory task client succeed, task: 任务 12[66c6a34e17ade8409258b057] 
[INFO ] 2024-08-22 12:09:39.010 - [任务 12] - Destroy memory task client cache succeed, task: 任务 12[66c6a34e17ade8409258b057] 
