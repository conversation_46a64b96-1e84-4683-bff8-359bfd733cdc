[INFO ] 2024-03-29 18:20:03.684 - [suppliers_import_import(100)][Suppliers] - Node Suppliers[30de60c0-85ae-4119-a399-39991fd9338a] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:20:03.684 - [suppliers_import_import(100)][b403908f-7e08-4032-a91c-6a0f837155a2] - Node b403908f-7e08-4032-a91c-6a0f837155a2[b403908f-7e08-4032-a91c-6a0f837155a2] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:20:03.684 - [suppliers_import_import(100)][Suppliers] - Node Suppliers[893260db-662e-4838-b36e-a51ec88ebac0] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:20:03.684 - [suppliers_import_import(100)][Suppliers] - Node Suppliers[893260db-662e-4838-b36e-a51ec88ebac0] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:20:03.684 - [suppliers_import_import(100)][b403908f-7e08-4032-a91c-6a0f837155a2] - Node b403908f-7e08-4032-a91c-6a0f837155a2[b403908f-7e08-4032-a91c-6a0f837155a2] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:20:03.684 - [suppliers_import_import(100)][Suppliers] - Node Suppliers[30de60c0-85ae-4119-a399-39991fd9338a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:20:03.756 - [suppliers_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:20:03.784 - [suppliers_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7aa3306c error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7aa3306c error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7aa3306c error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:20:04.803 - [suppliers_import_import(100)][Suppliers] - Node Suppliers[30de60c0-85ae-4119-a399-39991fd9338a] running status set to false 
[INFO ] 2024-03-29 18:20:04.865 - [suppliers_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-30de60c0-85ae-4119-a399-39991fd9338a 
[INFO ] 2024-03-29 18:20:04.865 - [suppliers_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-30de60c0-85ae-4119-a399-39991fd9338a 
[INFO ] 2024-03-29 18:20:04.866 - [suppliers_import_import(100)][Suppliers] - Node Suppliers[30de60c0-85ae-4119-a399-39991fd9338a] schema data cleaned 
[INFO ] 2024-03-29 18:20:04.866 - [suppliers_import_import(100)][Suppliers] - Node Suppliers[30de60c0-85ae-4119-a399-39991fd9338a] monitor closed 
[INFO ] 2024-03-29 18:20:04.866 - [suppliers_import_import(100)][Suppliers] - Node Suppliers[30de60c0-85ae-4119-a399-39991fd9338a] close complete, cost 77 ms 
[INFO ] 2024-03-29 18:20:06.442 - [suppliers_import_import(100)][Suppliers] - Node Suppliers[893260db-662e-4838-b36e-a51ec88ebac0] running status set to false 
[INFO ] 2024-03-29 18:20:06.443 - [suppliers_import_import(100)][b403908f-7e08-4032-a91c-6a0f837155a2] - Node b403908f-7e08-4032-a91c-6a0f837155a2[b403908f-7e08-4032-a91c-6a0f837155a2] running status set to false 
[INFO ] 2024-03-29 18:20:06.443 - [suppliers_import_import(100)][Suppliers] - Node Suppliers[893260db-662e-4838-b36e-a51ec88ebac0] schema data cleaned 
[INFO ] 2024-03-29 18:20:06.443 - [suppliers_import_import(100)][Suppliers] - Node Suppliers[893260db-662e-4838-b36e-a51ec88ebac0] monitor closed 
[INFO ] 2024-03-29 18:20:06.450 - [suppliers_import_import(100)][b403908f-7e08-4032-a91c-6a0f837155a2] - Node b403908f-7e08-4032-a91c-6a0f837155a2[b403908f-7e08-4032-a91c-6a0f837155a2] schema data cleaned 
[INFO ] 2024-03-29 18:20:06.453 - [suppliers_import_import(100)][b403908f-7e08-4032-a91c-6a0f837155a2] - Node b403908f-7e08-4032-a91c-6a0f837155a2[b403908f-7e08-4032-a91c-6a0f837155a2] monitor closed 
[INFO ] 2024-03-29 18:20:06.454 - [suppliers_import_import(100)][Suppliers] - Node Suppliers[893260db-662e-4838-b36e-a51ec88ebac0] close complete, cost 17 ms 
[INFO ] 2024-03-29 18:20:06.454 - [suppliers_import_import(100)][b403908f-7e08-4032-a91c-6a0f837155a2] - Node b403908f-7e08-4032-a91c-6a0f837155a2[b403908f-7e08-4032-a91c-6a0f837155a2] close complete, cost 14 ms 
[INFO ] 2024-03-29 18:20:06.460 - [suppliers_import_import(100)] - load tapTable task 660695cc39a4e7320f0e79a0-b403908f-7e08-4032-a91c-6a0f837155a2 complete, cost 2888ms 
