[INFO ] 2024-07-04 14:28:23.888 - [Task 4(100)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] start preload schema,table counts: 1 
[INFO ] 2024-07-04 14:28:23.899 - [Task 4(100)][38572837-d4ad-4fd8-ba55-0d5c9c41c404] - Node 38572837-d4ad-4fd8-ba55-0d5c9c41c404[38572837-d4ad-4fd8-ba55-0d5c9c41c404] start preload schema,table counts: 0 
[INFO ] 2024-07-04 14:28:23.899 - [Task 4(100)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] start preload schema,table counts: 1 
[INFO ] 2024-07-04 14:28:23.899 - [Task 4(100)][38572837-d4ad-4fd8-ba55-0d5c9c41c404] - Node 38572837-d4ad-4fd8-ba55-0d5c9c41c404[38572837-d4ad-4fd8-ba55-0d5c9c41c404] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 14:28:23.899 - [Task 4(100)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 14:28:23.899 - [Task 4(100)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 14:28:24.602 - [Task 4(100)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] running status set to false 
[INFO ] 2024-07-04 14:28:24.602 - [Task 4(100)][POLICY] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-32a9e251-e83a-4311-915f-f151503bb049 
[INFO ] 2024-07-04 14:28:24.602 - [Task 4(100)][POLICY] - PDK connector node released: HazelcastSampleSourcePdkDataNode-32a9e251-e83a-4311-915f-f151503bb049 
[INFO ] 2024-07-04 14:28:24.602 - [Task 4(100)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] schema data cleaned 
[INFO ] 2024-07-04 14:28:24.602 - [Task 4(100)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] monitor closed 
[INFO ] 2024-07-04 14:28:24.718 - [Task 4(100)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] close complete, cost 11 ms 
[INFO ] 2024-07-04 14:28:24.720 - [Task 4(100)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] running status set to false 
[INFO ] 2024-07-04 14:28:24.720 - [Task 4(100)][38572837-d4ad-4fd8-ba55-0d5c9c41c404] - Node 38572837-d4ad-4fd8-ba55-0d5c9c41c404[38572837-d4ad-4fd8-ba55-0d5c9c41c404] running status set to false 
[INFO ] 2024-07-04 14:28:24.720 - [Task 4(100)][38572837-d4ad-4fd8-ba55-0d5c9c41c404] - Node 38572837-d4ad-4fd8-ba55-0d5c9c41c404[38572837-d4ad-4fd8-ba55-0d5c9c41c404] schema data cleaned 
[INFO ] 2024-07-04 14:28:24.720 - [Task 4(100)][38572837-d4ad-4fd8-ba55-0d5c9c41c404] - Node 38572837-d4ad-4fd8-ba55-0d5c9c41c404[38572837-d4ad-4fd8-ba55-0d5c9c41c404] monitor closed 
[INFO ] 2024-07-04 14:28:24.725 - [Task 4(100)][38572837-d4ad-4fd8-ba55-0d5c9c41c404] - Node 38572837-d4ad-4fd8-ba55-0d5c9c41c404[38572837-d4ad-4fd8-ba55-0d5c9c41c404] close complete, cost 1 ms 
[INFO ] 2024-07-04 14:28:24.729 - [Task 4(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-aa5f02ac-327a-4460-9428-3b5d4ff3299b 
[INFO ] 2024-07-04 14:28:24.730 - [Task 4(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-aa5f02ac-327a-4460-9428-3b5d4ff3299b 
[INFO ] 2024-07-04 14:28:24.730 - [Task 4(100)][增强JS] - [ScriptExecutorsManager-6682d405494d8d2a5c957f37-bca58ceb-88e7-4364-bfef-bde381562d81-6674feb868ca1e3afc2a0d99] schema data cleaned 
[INFO ] 2024-07-04 14:28:24.736 - [Task 4(100)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] schema data cleaned 
[INFO ] 2024-07-04 14:28:24.736 - [Task 4(100)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] monitor closed 
[INFO ] 2024-07-04 14:28:24.741 - [Task 4(100)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] close complete, cost 27 ms 
[INFO ] 2024-07-04 14:28:24.743 - [Task 4(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-04 14:28:24.743 - [Task 4(100)] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2fd885bf 
[INFO ] 2024-07-04 14:28:24.878 - [Task 4(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-04 15:00:06.975 - [Task 4(100)][3f591d88-d010-45e0-b87c-7cf9e78556b1] - Node 3f591d88-d010-45e0-b87c-7cf9e78556b1[3f591d88-d010-45e0-b87c-7cf9e78556b1] start preload schema,table counts: 0 
[INFO ] 2024-07-04 15:00:06.975 - [Task 4(100)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] start preload schema,table counts: 1 
[INFO ] 2024-07-04 15:00:06.975 - [Task 4(100)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] start preload schema,table counts: 1 
[INFO ] 2024-07-04 15:00:06.975 - [Task 4(100)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 15:00:06.976 - [Task 4(100)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 15:00:07.180 - [Task 4(100)][3f591d88-d010-45e0-b87c-7cf9e78556b1] - Node 3f591d88-d010-45e0-b87c-7cf9e78556b1[3f591d88-d010-45e0-b87c-7cf9e78556b1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 15:00:07.210 - [Task 4(100)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] running status set to false 
[INFO ] 2024-07-04 15:00:07.211 - [Task 4(100)][POLICY] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-32a9e251-e83a-4311-915f-f151503bb049 
[INFO ] 2024-07-04 15:00:07.211 - [Task 4(100)][POLICY] - PDK connector node released: HazelcastSampleSourcePdkDataNode-32a9e251-e83a-4311-915f-f151503bb049 
[INFO ] 2024-07-04 15:00:07.211 - [Task 4(100)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] schema data cleaned 
[INFO ] 2024-07-04 15:00:07.211 - [Task 4(100)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] monitor closed 
[INFO ] 2024-07-04 15:00:07.213 - [Task 4(100)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] close complete, cost 11 ms 
[INFO ] 2024-07-04 15:00:07.244 - [Task 4(100)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] running status set to false 
[INFO ] 2024-07-04 15:00:07.250 - [Task 4(100)][3f591d88-d010-45e0-b87c-7cf9e78556b1] - Node 3f591d88-d010-45e0-b87c-7cf9e78556b1[3f591d88-d010-45e0-b87c-7cf9e78556b1] running status set to false 
[INFO ] 2024-07-04 15:00:07.251 - [Task 4(100)][3f591d88-d010-45e0-b87c-7cf9e78556b1] - Node 3f591d88-d010-45e0-b87c-7cf9e78556b1[3f591d88-d010-45e0-b87c-7cf9e78556b1] schema data cleaned 
[INFO ] 2024-07-04 15:00:07.251 - [Task 4(100)][3f591d88-d010-45e0-b87c-7cf9e78556b1] - Node 3f591d88-d010-45e0-b87c-7cf9e78556b1[3f591d88-d010-45e0-b87c-7cf9e78556b1] monitor closed 
[INFO ] 2024-07-04 15:00:07.255 - [Task 4(100)][3f591d88-d010-45e0-b87c-7cf9e78556b1] - Node 3f591d88-d010-45e0-b87c-7cf9e78556b1[3f591d88-d010-45e0-b87c-7cf9e78556b1] close complete, cost 8 ms 
[INFO ] 2024-07-04 15:00:07.255 - [Task 4(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-e77e1c30-3e1f-48c6-aedf-608fd98a79a0 
[INFO ] 2024-07-04 15:00:07.256 - [Task 4(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-e77e1c30-3e1f-48c6-aedf-608fd98a79a0 
[INFO ] 2024-07-04 15:00:07.256 - [Task 4(100)][增强JS] - [ScriptExecutorsManager-6682d405494d8d2a5c957f37-bca58ceb-88e7-4364-bfef-bde381562d81-6674feb868ca1e3afc2a0d99] schema data cleaned 
[INFO ] 2024-07-04 15:00:07.258 - [Task 4(100)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] schema data cleaned 
[INFO ] 2024-07-04 15:00:07.258 - [Task 4(100)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] monitor closed 
[INFO ] 2024-07-04 15:00:07.259 - [Task 4(100)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] close complete, cost 17 ms 
[INFO ] 2024-07-04 15:00:07.260 - [Task 4(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-04 15:00:07.260 - [Task 4(100)] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3ca2dc1f 
[INFO ] 2024-07-04 15:00:07.348 - [Task 4(100)] - Stopped task aspect(s) 
[INFO ] 2024-07-04 15:46:31.111 - [Task 4(100)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] start preload schema,table counts: 1 
[INFO ] 2024-07-04 15:46:31.111 - [Task 4(100)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] start preload schema,table counts: 1 
[INFO ] 2024-07-04 15:46:31.112 - [Task 4(100)][ac37d82d-56ff-466f-b673-13021d1f641e] - Node ac37d82d-56ff-466f-b673-13021d1f641e[ac37d82d-56ff-466f-b673-13021d1f641e] start preload schema,table counts: 0 
[INFO ] 2024-07-04 15:46:31.112 - [Task 4(100)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 15:46:31.113 - [Task 4(100)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 15:46:31.113 - [Task 4(100)][ac37d82d-56ff-466f-b673-13021d1f641e] - Node ac37d82d-56ff-466f-b673-13021d1f641e[ac37d82d-56ff-466f-b673-13021d1f641e] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 15:46:32.560 - [Task 4(100)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] running status set to false 
[INFO ] 2024-07-04 15:46:32.563 - [Task 4(100)][POLICY] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-32a9e251-e83a-4311-915f-f151503bb049 
[INFO ] 2024-07-04 15:46:32.576 - [Task 4(100)][POLICY] - PDK connector node released: HazelcastSampleSourcePdkDataNode-32a9e251-e83a-4311-915f-f151503bb049 
[INFO ] 2024-07-04 15:46:32.577 - [Task 4(100)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] schema data cleaned 
[INFO ] 2024-07-04 15:46:32.577 - [Task 4(100)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] monitor closed 
[INFO ] 2024-07-04 15:46:32.588 - [Task 4(100)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] close complete, cost 69 ms 
[INFO ] 2024-07-04 15:46:32.620 - [Task 4(100)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] running status set to false 
[INFO ] 2024-07-04 15:46:32.622 - [Task 4(100)][ac37d82d-56ff-466f-b673-13021d1f641e] - Node ac37d82d-56ff-466f-b673-13021d1f641e[ac37d82d-56ff-466f-b673-13021d1f641e] running status set to false 
[INFO ] 2024-07-04 15:46:32.631 - [Task 4(100)][ac37d82d-56ff-466f-b673-13021d1f641e] - Node ac37d82d-56ff-466f-b673-13021d1f641e[ac37d82d-56ff-466f-b673-13021d1f641e] schema data cleaned 
[INFO ] 2024-07-04 15:46:32.632 - [Task 4(100)][ac37d82d-56ff-466f-b673-13021d1f641e] - Node ac37d82d-56ff-466f-b673-13021d1f641e[ac37d82d-56ff-466f-b673-13021d1f641e] monitor closed 
[INFO ] 2024-07-04 15:46:32.632 - [Task 4(100)][ac37d82d-56ff-466f-b673-13021d1f641e] - Node ac37d82d-56ff-466f-b673-13021d1f641e[ac37d82d-56ff-466f-b673-13021d1f641e] close complete, cost 8 ms 
[INFO ] 2024-07-04 15:46:32.657 - [Task 4(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-a58ff235-b6d0-4849-8b3b-c1e6ac35c5a1 
[INFO ] 2024-07-04 15:46:32.658 - [Task 4(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-a58ff235-b6d0-4849-8b3b-c1e6ac35c5a1 
[INFO ] 2024-07-04 15:46:32.664 - [Task 4(100)][增强JS] - [ScriptExecutorsManager-6682d405494d8d2a5c957f37-bca58ceb-88e7-4364-bfef-bde381562d81-6674feb868ca1e3afc2a0d99] schema data cleaned 
[INFO ] 2024-07-04 15:46:32.670 - [Task 4(100)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] schema data cleaned 
[INFO ] 2024-07-04 15:46:32.670 - [Task 4(100)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] monitor closed 
[INFO ] 2024-07-04 15:46:32.671 - [Task 4(100)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] close complete, cost 59 ms 
[INFO ] 2024-07-04 15:46:32.688 - [Task 4(100)] - Closed task monitor(s)
null 
[INFO ] 2024-07-04 15:46:32.696 - [Task 4(100)] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7bfbe519 
[INFO ] 2024-07-04 15:46:32.696 - [Task 4(100)] - Stopped task aspect(s) 
