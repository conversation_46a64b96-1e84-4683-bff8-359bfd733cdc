[INFO ] 2024-03-29 14:52:14.255 - [orders_import_import_import(100)][5670d707-f73f-482e-8430-48f2f1e97f86] - Node 5670d707-f73f-482e-8430-48f2f1e97f86[5670d707-f73f-482e-8430-48f2f1e97f86] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:52:14.259 - [orders_import_import_import(100)][Order Details] - Node Order Details[fff7bb5e-a5b4-4b73-98b1-b64c3447bbf9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:52:14.260 - [orders_import_import_import(100)][Order Details] - Node Order Details[617240ac-360c-4a04-a2ec-364fc33bdc2a] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:52:14.260 - [orders_import_import_import(100)][5670d707-f73f-482e-8430-48f2f1e97f86] - Node 5670d707-f73f-482e-8430-48f2f1e97f86[5670d707-f73f-482e-8430-48f2f1e97f86] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:52:14.260 - [orders_import_import_import(100)][Order Details] - Node Order Details[617240ac-360c-4a04-a2ec-364fc33bdc2a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:52:14.260 - [orders_import_import_import(100)][Order Details] - Node Order Details[fff7bb5e-a5b4-4b73-98b1-b64c3447bbf9] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:56:27.480 - [orders_import_import_import(100)][Order Details] - io.tapdata.exception.ManagementException: Failed to call rest api, msg I/O error on GET request for "http://127.0.0.1:3000/api/Javascript_functions/all": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). <-- Error Message -->
io.tapdata.exception.ManagementException: Failed to call rest api, msg I/O error on GET request for "http://127.0.0.1:3000/api/Javascript_functions/all": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out).

<-- Simple Stack Trace -->
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out)
	org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:159)
	org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373)
	org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:381)
	org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237)
	org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185)
	...
Caused by: java.net.ConnectException: Operation timed out (Connection timed out)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
io.tapdata.exception.ManagementException: Failed to call rest api, msg I/O error on GET request for "http://127.0.0.1:3000/api/Javascript_functions/all": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out).
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:557)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:211)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: Failed to call rest api, msg I/O error on GET request for "http://127.0.0.1:3000/api/Javascript_functions/all": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out).
	at com.tapdata.mongo.HttpClientMongoOperator.find(HttpClientMongoOperator.java:289)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:119)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	... 12 more
Caused by: io.tapdata.exception.ManagementException: Failed to call rest api, msg I/O error on GET request for "http://127.0.0.1:3000/api/Javascript_functions/all": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out).
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:790)
	at com.tapdata.mongo.RestTemplateOperator.getBatch(RestTemplateOperator.java:381)
	at com.tapdata.mongo.RestTemplateOperator.getBatch(RestTemplateOperator.java:377)
	at com.tapdata.mongo.HttpClientMongoOperator.find(HttpClientMongoOperator.java:287)
	... 14 more
Caused by: org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://127.0.0.1:3000/api/Javascript_functions/all": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:743)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:704)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:621)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getBatch$10(RestTemplateOperator.java:394)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	... 17 more
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:159)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:381)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:111)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:89)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:104)
	at com.tapdata.tm.sdk.interceptor.VersionHeaderInterceptor.intercept(VersionHeaderInterceptor.java:26)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:92)
	at com.tapdata.interceptor.LoggingInterceptor.intercept(LoggingInterceptor.java:33)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:92)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:729)
	... 21 more
Caused by: java.net.ConnectException: Operation timed out (Connection timed out)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 41 more

[ERROR] 2024-03-29 14:56:27.481 - [orders_import_import_import(100)][Order Details] - start source runner failed: Failed to call rest api, msg I/O error on GET request for "http://127.0.0.1:3000/api/ExternalStorage": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out). <-- Error Message -->
start source runner failed: Failed to call rest api, msg I/O error on GET request for "http://127.0.0.1:3000/api/ExternalStorage": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out).

<-- Simple Stack Trace -->
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out)
	org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:159)
	org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373)
	org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:381)
	org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237)
	org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185)
	...
Caused by: java.net.ConnectException: Operation timed out (Connection timed out)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
io.tapdata.exception.ManagementException: Failed to call rest api, msg I/O error on GET request for "http://127.0.0.1:3000/api/ExternalStorage": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out).
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.ManagementException: Failed to call rest api, msg I/O error on GET request for "http://127.0.0.1:3000/api/ExternalStorage": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out).
	at com.tapdata.mongo.HttpClientMongoOperator.find(HttpClientMongoOperator.java:289)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.getTapdataOrDefaultExternalStorage(ExternalStorageUtil.java:564)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:73)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more
Caused by: io.tapdata.exception.ManagementException: Failed to call rest api, msg I/O error on GET request for "http://127.0.0.1:3000/api/ExternalStorage": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out).
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:790)
	at com.tapdata.mongo.RestTemplateOperator.getBatch(RestTemplateOperator.java:381)
	at com.tapdata.mongo.RestTemplateOperator.getBatch(RestTemplateOperator.java:377)
	at com.tapdata.mongo.HttpClientMongoOperator.find(HttpClientMongoOperator.java:287)
	... 19 more
Caused by: org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://127.0.0.1:3000/api/ExternalStorage": Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:743)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:704)
	at org.springframework.web.client.RestTemplate.exchange(RestTemplate.java:621)
	at com.tapdata.mongo.RestTemplateOperator.lambda$getBatch$10(RestTemplateOperator.java:394)
	at com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	... 22 more
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:3000 [/127.0.0.1] failed: Operation timed out (Connection timed out)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:159)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:373)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:381)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:237)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:185)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:111)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:89)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:104)
	at com.tapdata.tm.sdk.interceptor.VersionHeaderInterceptor.intercept(VersionHeaderInterceptor.java:26)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:92)
	at com.tapdata.interceptor.LoggingInterceptor.intercept(LoggingInterceptor.java:33)
	at org.springframework.http.client.InterceptingClientHttpRequest$InterceptingRequestExecution.execute(InterceptingClientHttpRequest.java:92)
	at org.springframework.http.client.InterceptingClientHttpRequest.executeInternal(InterceptingClientHttpRequest.java:76)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:729)
	... 26 more
Caused by: java.net.ConnectException: Operation timed out (Connection timed out)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 46 more

[INFO ] 2024-03-29 14:56:30.013 - [orders_import_import_import(100)][Order Details] - Node Order Details[fff7bb5e-a5b4-4b73-98b1-b64c3447bbf9] running status set to false 
[INFO ] 2024-03-29 14:56:30.014 - [orders_import_import_import(100)][Order Details] - Node Order Details[617240ac-360c-4a04-a2ec-364fc33bdc2a] running status set to false 
[INFO ] 2024-03-29 14:56:30.014 - [orders_import_import_import(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:56:30.014 - [orders_import_import_import(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:56:30.014 - [orders_import_import_import(100)][Order Details] - Node Order Details[617240ac-360c-4a04-a2ec-364fc33bdc2a] schema data cleaned 
[INFO ] 2024-03-29 14:56:30.014 - [orders_import_import_import(100)][Order Details] - Node Order Details[fff7bb5e-a5b4-4b73-98b1-b64c3447bbf9] schema data cleaned 
[INFO ] 2024-03-29 14:56:30.015 - [orders_import_import_import(100)][5670d707-f73f-482e-8430-48f2f1e97f86] - Node 5670d707-f73f-482e-8430-48f2f1e97f86[5670d707-f73f-482e-8430-48f2f1e97f86] running status set to false 
[INFO ] 2024-03-29 14:56:30.023 - [orders_import_import_import(100)][Order Details] - Node Order Details[617240ac-360c-4a04-a2ec-364fc33bdc2a] monitor closed 
[INFO ] 2024-03-29 14:56:30.025 - [orders_import_import_import(100)][5670d707-f73f-482e-8430-48f2f1e97f86] - Node 5670d707-f73f-482e-8430-48f2f1e97f86[5670d707-f73f-482e-8430-48f2f1e97f86] schema data cleaned 
[INFO ] 2024-03-29 14:56:30.025 - [orders_import_import_import(100)][Order Details] - Node Order Details[fff7bb5e-a5b4-4b73-98b1-b64c3447bbf9] monitor closed 
[INFO ] 2024-03-29 14:56:30.025 - [orders_import_import_import(100)][Order Details] - Node Order Details[617240ac-360c-4a04-a2ec-364fc33bdc2a] close complete, cost 12 ms 
[INFO ] 2024-03-29 14:56:30.025 - [orders_import_import_import(100)][5670d707-f73f-482e-8430-48f2f1e97f86] - Node 5670d707-f73f-482e-8430-48f2f1e97f86[5670d707-f73f-482e-8430-48f2f1e97f86] monitor closed 
[INFO ] 2024-03-29 14:56:30.026 - [orders_import_import_import(100)][5670d707-f73f-482e-8430-48f2f1e97f86] - Node 5670d707-f73f-482e-8430-48f2f1e97f86[5670d707-f73f-482e-8430-48f2f1e97f86] close complete, cost 11 ms 
[INFO ] 2024-03-29 14:56:30.026 - [orders_import_import_import(100)][Order Details] - Node Order Details[fff7bb5e-a5b4-4b73-98b1-b64c3447bbf9] close complete, cost 15 ms 
[INFO ] 2024-03-29 14:56:30.029 - [orders_import_import_import(100)] - load tapTable task 6606650673eec71f077e9e34-5670d707-f73f-482e-8430-48f2f1e97f86 complete, cost 262269ms 
