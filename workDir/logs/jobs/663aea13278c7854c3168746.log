[INFO ] 2024-05-08 11:05:13.680 - [任务 2] - Start task milestones: 663aea13278c7854c3168746(任务 2) 
[INFO ] 2024-05-08 11:05:13.680 - [任务 2] - Task initialization... 
[INFO ] 2024-05-08 11:05:13.799 - [任务 2] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-05-08 11:05:13.800 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-08 11:05:13.863 - [任务 2][TestMysql] - Node TestMysql[978d6d55-656c-49c8-b799-23b558ea1d3e] start preload schema,table counts: 1 
[INFO ] 2024-05-08 11:05:13.864 - [任务 2][MongoSource] - Node MongoSource[bf4abeea-6c04-46e7-aec8-ba9caf6d3ccf] start preload schema,table counts: 1 
[INFO ] 2024-05-08 11:05:13.890 - [任务 2][MongoSource] - Node MongoSource[bf4abeea-6c04-46e7-aec8-ba9caf6d3ccf] preload schema finished, cost 22 ms 
[INFO ] 2024-05-08 11:05:13.891 - [任务 2][TestMysql] - Node TestMysql[978d6d55-656c-49c8-b799-23b558ea1d3e] preload schema finished, cost 28 ms 
[INFO ] 2024-05-08 11:05:14.774 - [任务 2][TestMysql] - Node(TestMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-05-08 11:05:14.786 - [任务 2][TestMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-08 11:05:14.799 - [任务 2][MongoSource] - Source node "MongoSource" read batch size: 100 
[INFO ] 2024-05-08 11:05:14.803 - [任务 2][MongoSource] - Source node "MongoSource" event queue capacity: 200 
[INFO ] 2024-05-08 11:05:14.804 - [任务 2][MongoSource] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-08 11:05:14.963 - [任务 2][MongoSource] - batch offset found: {},stream offset found: {"cdcOffset":1715137514,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-05-08 11:05:15.028 - [任务 2][MongoSource] - Initial sync started 
[INFO ] 2024-05-08 11:05:15.035 - [任务 2][MongoSource] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-05-08 11:05:15.035 - [任务 2][MongoSource] - Table CLAIM is going to be initial synced 
[INFO ] 2024-05-08 11:05:18.823 - [任务 2][MongoSource] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-05-08 11:05:18.991 - [任务 2][MongoSource] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-08 11:05:18.991 - [任务 2][MongoSource] - Initial sync completed 
[INFO ] 2024-05-08 11:05:18.991 - [任务 2][MongoSource] - Incremental sync starting... 
[INFO ] 2024-05-08 11:05:18.991 - [任务 2][MongoSource] - Initial sync completed 
[INFO ] 2024-05-08 11:05:19.000 - [任务 2][MongoSource] - Starting stream read, table list: [CLAIM], offset: {"cdcOffset":1715137514,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-05-08 11:05:19.206 - [任务 2][MongoSource] - Connector MongoDB incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-05-08 11:37:39.488 - [任务 2] - Stop task milestones: 663aea13278c7854c3168746(任务 2)  
[INFO ] 2024-05-08 11:37:39.811 - [任务 2][MongoSource] - Node MongoSource[bf4abeea-6c04-46e7-aec8-ba9caf6d3ccf] running status set to false 
[INFO ] 2024-05-08 11:37:39.889 - [任务 2][MongoSource] - PDK connector node stopped: HazelcastSourcePdkDataNode-bf4abeea-6c04-46e7-aec8-ba9caf6d3ccf 
[INFO ] 2024-05-08 11:37:39.890 - [任务 2][MongoSource] - PDK connector node released: HazelcastSourcePdkDataNode-bf4abeea-6c04-46e7-aec8-ba9caf6d3ccf 
[INFO ] 2024-05-08 11:37:39.890 - [任务 2][MongoSource] - Node MongoSource[bf4abeea-6c04-46e7-aec8-ba9caf6d3ccf] schema data cleaned 
[INFO ] 2024-05-08 11:37:39.891 - [任务 2][MongoSource] - Node MongoSource[bf4abeea-6c04-46e7-aec8-ba9caf6d3ccf] monitor closed 
[INFO ] 2024-05-08 11:37:39.891 - [任务 2][MongoSource] - Node MongoSource[bf4abeea-6c04-46e7-aec8-ba9caf6d3ccf] close complete, cost 96 ms 
[INFO ] 2024-05-08 11:37:39.929 - [任务 2][TestMysql] - Node TestMysql[978d6d55-656c-49c8-b799-23b558ea1d3e] running status set to false 
[INFO ] 2024-05-08 11:37:39.934 - [任务 2][TestMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-978d6d55-656c-49c8-b799-23b558ea1d3e 
[INFO ] 2024-05-08 11:37:39.935 - [任务 2][TestMysql] - PDK connector node released: HazelcastTargetPdkDataNode-978d6d55-656c-49c8-b799-23b558ea1d3e 
[INFO ] 2024-05-08 11:37:39.935 - [任务 2][TestMysql] - Node TestMysql[978d6d55-656c-49c8-b799-23b558ea1d3e] schema data cleaned 
[INFO ] 2024-05-08 11:37:39.935 - [任务 2][TestMysql] - Node TestMysql[978d6d55-656c-49c8-b799-23b558ea1d3e] monitor closed 
[INFO ] 2024-05-08 11:37:39.935 - [任务 2][TestMysql] - Node TestMysql[978d6d55-656c-49c8-b799-23b558ea1d3e] close complete, cost 38 ms 
[INFO ] 2024-05-08 11:37:40.923 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-08 11:37:40.924 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-05-08 11:37:40.924 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-05-08 11:37:40.967 - [任务 2] - Remove memory task client succeed, task: 任务 2[663aea13278c7854c3168746] 
[INFO ] 2024-05-08 11:37:40.968 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[663aea13278c7854c3168746] 
