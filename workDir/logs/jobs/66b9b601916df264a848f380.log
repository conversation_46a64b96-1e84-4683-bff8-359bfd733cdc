[INFO ] 2024-08-13 17:28:00.774 - [任务 1] - Task initialization... 
[INFO ] 2024-08-13 17:28:00.791 - [任务 1] - Start task milestones: 66b9b601916df264a848f380(任务 1) 
[INFO ] 2024-08-13 17:28:00.996 - [任务 1] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-08-13 17:28:01.204 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-13 17:28:01.524 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] start preload schema,table counts: 3 
[INFO ] 2024-08-13 17:28:01.725 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] start preload schema,table counts: 3 
[INFO ] 2024-08-13 17:28:01.877 - [任务 1][Mysql] - Node <PERSON>sql[d42e316f-5648-4c9e-a121-085e42d4ff56] preload schema finished, cost 313 ms 
[INFO ] 2024-08-13 17:28:01.880 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] preload schema finished, cost 352 ms 
[INFO ] 2024-08-13 17:28:02.586 - [任务 1][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-13 17:28:02.590 - [任务 1][Pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-13 17:28:02.904 - [任务 1][Mysql] - Source node "Mysql" read batch size: 500 
[INFO ] 2024-08-13 17:28:02.905 - [任务 1][Mysql] - Source node "Mysql" event queue capacity: 1000 
[INFO ] 2024-08-13 17:28:02.908 - [任务 1][Mysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-13 17:28:02.925 - [任务 1][Mysql] - batch offset found: {},stream offset found: {"name":"67b2396a-93e8-4554-ad22-a81fa573ecff","offset":{"{\"server\":\"67b2396a-93e8-4554-ad22-a81fa573ecff\"}":"{\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":1}"}} 
[INFO ] 2024-08-13 17:28:03.129 - [任务 1][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-13 17:28:03.150 - [任务 1][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-13 17:28:03.157 - [任务 1][Mysql] - Initial sync completed 
[INFO ] 2024-08-13 17:28:03.157 - [任务 1][Mysql] - Starting stream read, table list: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], offset: {"name":"67b2396a-93e8-4554-ad22-a81fa573ecff","offset":{"{\"server\":\"67b2396a-93e8-4554-ad22-a81fa573ecff\"}":"{\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":1}"}} 
[INFO ] 2024-08-13 17:28:03.218 - [任务 1][Mysql] - Starting mysql cdc, server name: 67b2396a-93e8-4554-ad22-a81fa573ecff 
[INFO ] 2024-08-13 17:28:03.218 - [任务 1][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1685058869
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 67b2396a-93e8-4554-ad22-a81fa573ecff
  database.port: 3306
  threadName: Debezium-Mysql-Connector-67b2396a-93e8-4554-ad22-a81fa573ecff
  database.hostname: localhost
  database.password: ********
  name: 67b2396a-93e8-4554-ad22-a81fa573ecff
  pdk.offset.string: {"name":"67b2396a-93e8-4554-ad22-a81fa573ecff","offset":{"{\"server\":\"67b2396a-93e8-4554-ad22-a81fa573ecff\"}":"{\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-13 17:28:04.024 - [任务 1][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-13 17:30:11.563 - [任务 1] - Stop task milestones: 66b9b601916df264a848f380(任务 1)  
[INFO ] 2024-08-13 17:30:12.098 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] running status set to false 
[INFO ] 2024-08-13 17:30:12.100 - [任务 1][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-13 17:30:12.100 - [任务 1][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-13 17:30:12.157 - [任务 1][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-d42e316f-5648-4c9e-a121-085e42d4ff56 
[INFO ] 2024-08-13 17:30:12.158 - [任务 1][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-d42e316f-5648-4c9e-a121-085e42d4ff56 
[INFO ] 2024-08-13 17:30:12.160 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] schema data cleaned 
[INFO ] 2024-08-13 17:30:12.161 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] monitor closed 
[INFO ] 2024-08-13 17:30:12.168 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] close complete, cost 114 ms 
[INFO ] 2024-08-13 17:30:12.168 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] running status set to false 
[INFO ] 2024-08-13 17:30:12.239 - [任务 1][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode-21430775-aa43-4102-8cf4-1c4160d42aad 
[INFO ] 2024-08-13 17:30:12.239 - [任务 1][Pg] - PDK connector node released: HazelcastTargetPdkDataNode-21430775-aa43-4102-8cf4-1c4160d42aad 
[INFO ] 2024-08-13 17:30:12.239 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] schema data cleaned 
[INFO ] 2024-08-13 17:30:12.240 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] monitor closed 
[INFO ] 2024-08-13 17:30:12.240 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] close complete, cost 74 ms 
[INFO ] 2024-08-13 17:30:17.171 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-13 17:30:17.172 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-13 17:30:17.284 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-13 17:30:17.284 - [任务 1] - Remove memory task client succeed, task: 任务 1[66b9b601916df264a848f380] 
[INFO ] 2024-08-13 17:30:17.490 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66b9b601916df264a848f380] 
[INFO ] 2024-08-13 17:59:41.670 - [任务 1] - Task initialization... 
[INFO ] 2024-08-13 17:59:41.672 - [任务 1] - Start task milestones: 66b9b601916df264a848f380(任务 1) 
[INFO ] 2024-08-13 17:59:42.289 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-13 17:59:42.474 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-13 17:59:42.475 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] start preload schema,table counts: 3 
[INFO ] 2024-08-13 17:59:42.475 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] start preload schema,table counts: 3 
[INFO ] 2024-08-13 17:59:42.577 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] preload schema finished, cost 102 ms 
[INFO ] 2024-08-13 17:59:42.578 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] preload schema finished, cost 102 ms 
[INFO ] 2024-08-13 17:59:43.367 - [任务 1][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-13 17:59:43.371 - [任务 1][Pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-13 17:59:43.566 - [任务 1][Pg] - The table BMSQL_DISTRICT has already exist. 
[INFO ] 2024-08-13 17:59:43.566 - [任务 1][Mysql] - Source node "Mysql" read batch size: 500 
[INFO ] 2024-08-13 17:59:43.566 - [任务 1][Mysql] - Source node "Mysql" event queue capacity: 1000 
[INFO ] 2024-08-13 17:59:43.567 - [任务 1][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-13 17:59:43.586 - [任务 1][Mysql] - batch offset found: {},stream offset found: {"filename":"binlog.000034","position":308235516,"gtidSet":""} 
[INFO ] 2024-08-13 17:59:43.657 - [任务 1][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-13 17:59:43.658 - [任务 1][Pg] - The table BMSQL_CUSTOMER has already exist. 
[INFO ] 2024-08-13 17:59:43.788 - [任务 1][Mysql] - Initial sync started 
[INFO ] 2024-08-13 17:59:43.792 - [任务 1][Mysql] - Starting batch read, table name: BMSQL_DISTRICT, offset: null 
[INFO ] 2024-08-13 17:59:43.871 - [任务 1][Mysql] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-13 17:59:43.875 - [任务 1][Mysql] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-13 17:59:43.886 - [任务 1][Mysql] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-13 17:59:43.886 - [任务 1][Mysql] - Starting batch read, table name: BMSQL_CUSTOMER, offset: null 
[INFO ] 2024-08-13 17:59:43.892 - [任务 1][Mysql] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-08-13 17:59:43.892 - [任务 1][Mysql] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-13 17:59:43.893 - [任务 1][Mysql] - Query table 'BMSQL_CUSTOMER' counts: 0 
[INFO ] 2024-08-13 17:59:43.893 - [任务 1][Mysql] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-08-13 17:59:43.901 - [任务 1][Mysql] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-13 17:59:43.901 - [任务 1][Mysql] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-13 17:59:43.901 - [任务 1][Mysql] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-13 17:59:43.901 - [任务 1][Mysql] - Initial sync completed 
[INFO ] 2024-08-13 17:59:43.903 - [任务 1][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-13 17:59:43.905 - [任务 1][Mysql] - Initial sync completed 
[INFO ] 2024-08-13 17:59:43.920 - [任务 1][Mysql] - Starting stream read, table list: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], offset: {"filename":"binlog.000034","position":308235516,"gtidSet":""} 
[INFO ] 2024-08-13 17:59:43.920 - [任务 1][Pg] - The table BMSQL_CONFIG has already exist. 
[INFO ] 2024-08-13 17:59:43.969 - [任务 1][Mysql] - Starting mysql cdc, server name: 520c2174-6165-4026-936b-f82efbe2b65c 
[INFO ] 2024-08-13 17:59:43.972 - [任务 1][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 927032103
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 520c2174-6165-4026-936b-f82efbe2b65c
  database.port: 3306
  threadName: Debezium-Mysql-Connector-520c2174-6165-4026-936b-f82efbe2b65c
  database.hostname: localhost
  database.password: ********
  name: 520c2174-6165-4026-936b-f82efbe2b65c
  pdk.offset.string: {"name":"520c2174-6165-4026-936b-f82efbe2b65c","offset":{"{\"server\":\"520c2174-6165-4026-936b-f82efbe2b65c\"}":"{\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-13 17:59:44.180 - [任务 1][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-13 18:05:01.530 - [任务 1] - Stop task milestones: 66b9b601916df264a848f380(任务 1)  
[INFO ] 2024-08-13 18:05:01.531 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] running status set to false 
[INFO ] 2024-08-13 18:05:01.653 - [任务 1][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-13 18:05:01.653 - [任务 1][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-13 18:05:01.661 - [任务 1][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-d42e316f-5648-4c9e-a121-085e42d4ff56 
[INFO ] 2024-08-13 18:05:01.662 - [任务 1][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-d42e316f-5648-4c9e-a121-085e42d4ff56 
[INFO ] 2024-08-13 18:05:01.662 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] schema data cleaned 
[INFO ] 2024-08-13 18:05:01.663 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] monitor closed 
[INFO ] 2024-08-13 18:05:01.665 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] close complete, cost 137 ms 
[INFO ] 2024-08-13 18:05:01.696 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] running status set to false 
[INFO ] 2024-08-13 18:05:01.696 - [任务 1][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode-21430775-aa43-4102-8cf4-1c4160d42aad 
[INFO ] 2024-08-13 18:05:01.696 - [任务 1][Pg] - PDK connector node released: HazelcastTargetPdkDataNode-21430775-aa43-4102-8cf4-1c4160d42aad 
[INFO ] 2024-08-13 18:05:01.697 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] schema data cleaned 
[INFO ] 2024-08-13 18:05:01.697 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] monitor closed 
[INFO ] 2024-08-13 18:05:01.697 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] close complete, cost 33 ms 
[INFO ] 2024-08-13 18:05:02.319 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-13 18:05:02.320 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-13 18:05:02.320 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-13 18:05:02.388 - [任务 1] - Remove memory task client succeed, task: 任务 1[66b9b601916df264a848f380] 
[INFO ] 2024-08-13 18:05:02.388 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66b9b601916df264a848f380] 
[INFO ] 2024-08-13 18:08:19.699 - [任务 1] - Task initialization... 
[INFO ] 2024-08-13 18:08:19.700 - [任务 1] - Start task milestones: 66b9b601916df264a848f380(任务 1) 
[INFO ] 2024-08-13 18:08:19.720 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-13 18:08:19.835 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-13 18:08:19.835 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] start preload schema,table counts: 3 
[INFO ] 2024-08-13 18:08:19.895 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] start preload schema,table counts: 3 
[INFO ] 2024-08-13 18:08:19.896 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] preload schema finished, cost 60 ms 
[INFO ] 2024-08-13 18:08:20.104 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] preload schema finished, cost 58 ms 
[INFO ] 2024-08-13 18:08:20.797 - [任务 1][Mysql] - Source node "Mysql" read batch size: 500 
[INFO ] 2024-08-13 18:08:20.797 - [任务 1][Mysql] - Source node "Mysql" event queue capacity: 1000 
[INFO ] 2024-08-13 18:08:20.797 - [任务 1][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-13 18:08:20.799 - [任务 1][Mysql] - batch offset found: {},stream offset found: {"filename":"binlog.000034","position":308235516,"gtidSet":""} 
[INFO ] 2024-08-13 18:08:20.867 - [任务 1][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-13 18:08:20.867 - [任务 1][Mysql] - Initial sync started 
[INFO ] 2024-08-13 18:08:20.867 - [任务 1][Mysql] - Starting batch read, table name: BMSQL_DISTRICT, offset: null 
[INFO ] 2024-08-13 18:08:20.875 - [任务 1][Mysql] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-13 18:08:20.917 - [任务 1][Mysql] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-13 18:08:20.918 - [任务 1][Mysql] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-13 18:08:20.918 - [任务 1][Mysql] - Starting batch read, table name: BMSQL_CUSTOMER, offset: null 
[INFO ] 2024-08-13 18:08:20.919 - [任务 1][Mysql] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-08-13 18:08:20.922 - [任务 1][Mysql] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-13 18:08:20.922 - [任务 1][Mysql] - Query table 'BMSQL_CUSTOMER' counts: 0 
[INFO ] 2024-08-13 18:08:20.924 - [任务 1][Mysql] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-08-13 18:08:20.924 - [任务 1][Mysql] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-13 18:08:20.927 - [任务 1][Mysql] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-13 18:08:20.927 - [任务 1][Mysql] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-13 18:08:20.928 - [任务 1][Mysql] - Initial sync completed 
[INFO ] 2024-08-13 18:08:20.928 - [任务 1][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-13 18:08:20.936 - [任务 1][Mysql] - Initial sync completed 
[INFO ] 2024-08-13 18:08:20.938 - [任务 1][Mysql] - Starting stream read, table list: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], offset: {"filename":"binlog.000034","position":308235516,"gtidSet":""} 
[INFO ] 2024-08-13 18:08:20.972 - [任务 1][Mysql] - Starting mysql cdc, server name: 21e61ad5-b754-4496-9ebd-0a8ae3639262 
[INFO ] 2024-08-13 18:08:20.973 - [任务 1][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1750019724
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 21e61ad5-b754-4496-9ebd-0a8ae3639262
  database.port: 3306
  threadName: Debezium-Mysql-Connector-21e61ad5-b754-4496-9ebd-0a8ae3639262
  database.hostname: localhost
  database.password: ********
  name: 21e61ad5-b754-4496-9ebd-0a8ae3639262
  pdk.offset.string: {"name":"21e61ad5-b754-4496-9ebd-0a8ae3639262","offset":{"{\"server\":\"21e61ad5-b754-4496-9ebd-0a8ae3639262\"}":"{\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-13 18:08:20.975 - [任务 1][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-13 18:08:20.975 - [任务 1][Pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-13 18:08:23.459 - [任务 1][Pg] - The table BMSQL_DISTRICT has already exist. 
[INFO ] 2024-08-13 18:08:23.459 - [任务 1][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-13 18:08:23.577 - [任务 1][Pg] - The table BMSQL_CUSTOMER has already exist. 
[INFO ] 2024-08-13 18:08:23.579 - [任务 1][Pg] - The table BMSQL_CONFIG has already exist. 
[INFO ] 2024-08-13 18:11:44.749 - [任务 1] - Stop task milestones: 66b9b601916df264a848f380(任务 1)  
[INFO ] 2024-08-13 18:11:44.750 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] running status set to false 
[INFO ] 2024-08-13 18:11:44.772 - [任务 1][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-13 18:11:44.773 - [任务 1][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-13 18:11:44.784 - [任务 1][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-d42e316f-5648-4c9e-a121-085e42d4ff56 
[INFO ] 2024-08-13 18:11:44.785 - [任务 1][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-d42e316f-5648-4c9e-a121-085e42d4ff56 
[INFO ] 2024-08-13 18:11:44.785 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] schema data cleaned 
[INFO ] 2024-08-13 18:11:44.791 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] monitor closed 
[INFO ] 2024-08-13 18:11:44.793 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] close complete, cost 41 ms 
[INFO ] 2024-08-13 18:11:44.793 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] running status set to false 
[INFO ] 2024-08-13 18:11:47.789 - [任务 1][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode-21430775-aa43-4102-8cf4-1c4160d42aad 
[INFO ] 2024-08-13 18:11:47.789 - [任务 1][Pg] - PDK connector node released: HazelcastTargetPdkDataNode-21430775-aa43-4102-8cf4-1c4160d42aad 
[INFO ] 2024-08-13 18:11:47.790 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] schema data cleaned 
[INFO ] 2024-08-13 18:11:47.792 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] monitor closed 
[INFO ] 2024-08-13 18:11:47.797 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] close complete, cost 3000 ms 
[INFO ] 2024-08-13 18:11:50.711 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-13 18:11:50.711 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-13 18:11:50.712 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-13 18:11:50.740 - [任务 1] - Remove memory task client succeed, task: 任务 1[66b9b601916df264a848f380] 
[INFO ] 2024-08-13 18:11:50.741 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66b9b601916df264a848f380] 
[INFO ] 2024-08-13 18:12:22.294 - [任务 1] - Task initialization... 
[INFO ] 2024-08-13 18:12:22.295 - [任务 1] - Start task milestones: 66b9b601916df264a848f380(任务 1) 
[INFO ] 2024-08-13 18:12:22.505 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-13 18:12:22.511 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-13 18:12:22.656 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] start preload schema,table counts: 3 
[INFO ] 2024-08-13 18:12:22.656 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] start preload schema,table counts: 3 
[INFO ] 2024-08-13 18:12:22.767 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] preload schema finished, cost 107 ms 
[INFO ] 2024-08-13 18:12:22.768 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] preload schema finished, cost 110 ms 
[INFO ] 2024-08-13 18:12:23.586 - [任务 1][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-13 18:12:23.589 - [任务 1][Pg] - Write batch size: 100, max wait ms per batch: 5000 
[INFO ] 2024-08-13 18:12:25.659 - [任务 1][Pg] - The table BMSQL_DISTRICT has already exist. 
[INFO ] 2024-08-13 18:12:25.659 - [任务 1][Mysql] - Source node "Mysql" read batch size: 500 
[INFO ] 2024-08-13 18:12:25.659 - [任务 1][Mysql] - Source node "Mysql" event queue capacity: 1000 
[INFO ] 2024-08-13 18:12:25.659 - [任务 1][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-13 18:12:25.676 - [任务 1][Mysql] - batch offset found: {},stream offset found: {"filename":"binlog.000034","position":308235516,"gtidSet":""} 
[INFO ] 2024-08-13 18:12:25.708 - [任务 1][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-13 18:12:25.708 - [任务 1][Pg] - The table BMSQL_CUSTOMER has already exist. 
[INFO ] 2024-08-13 18:12:25.737 - [任务 1][Mysql] - Initial sync started 
[INFO ] 2024-08-13 18:12:25.737 - [任务 1][Mysql] - Starting batch read, table name: BMSQL_DISTRICT, offset: null 
[INFO ] 2024-08-13 18:12:25.789 - [任务 1][Mysql] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-13 18:12:25.789 - [任务 1][Mysql] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-13 18:12:25.790 - [任务 1][Mysql] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-13 18:12:25.790 - [任务 1][Mysql] - Starting batch read, table name: BMSQL_CUSTOMER, offset: null 
[INFO ] 2024-08-13 18:12:25.792 - [任务 1][Mysql] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-08-13 18:12:25.792 - [任务 1][Pg] - The table BMSQL_CONFIG has already exist. 
[INFO ] 2024-08-13 18:12:25.794 - [任务 1][Mysql] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-13 18:12:25.794 - [任务 1][Mysql] - Query table 'BMSQL_CUSTOMER' counts: 0 
[INFO ] 2024-08-13 18:12:25.795 - [任务 1][Mysql] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-08-13 18:12:25.795 - [任务 1][Mysql] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-13 18:12:25.805 - [任务 1][Mysql] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-13 18:12:25.805 - [任务 1][Mysql] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-13 18:12:25.806 - [任务 1][Mysql] - Initial sync completed 
[INFO ] 2024-08-13 18:12:25.806 - [任务 1][Mysql] - Incremental sync starting... 
[INFO ] 2024-08-13 18:12:25.806 - [任务 1][Mysql] - Initial sync completed 
[INFO ] 2024-08-13 18:12:25.809 - [任务 1][Mysql] - Starting stream read, table list: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], offset: {"filename":"binlog.000034","position":308235516,"gtidSet":""} 
[INFO ] 2024-08-13 18:12:26.781 - [任务 1][Mysql] - Starting mysql cdc, server name: 8d462209-bbf2-46da-9a54-45d30c16bc36 
[INFO ] 2024-08-13 18:12:26.853 - [任务 1][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1738518699
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 8d462209-bbf2-46da-9a54-45d30c16bc36
  database.port: 3306
  threadName: Debezium-Mysql-Connector-8d462209-bbf2-46da-9a54-45d30c16bc36
  database.hostname: localhost
  database.password: ********
  name: 8d462209-bbf2-46da-9a54-45d30c16bc36
  pdk.offset.string: {"name":"8d462209-bbf2-46da-9a54-45d30c16bc36","offset":{"{\"server\":\"8d462209-bbf2-46da-9a54-45d30c16bc36\"}":"{\"file\":\"binlog.000034\",\"pos\":308235516,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-13 18:12:26.853 - [任务 1][Mysql] - Connector Mysql incremental start succeed, tables: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], data change syncing 
[WARN ] 2024-08-13 18:14:50.136 - [任务 1][Pg] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.net.ConnectException: Connection refused (Connection refused)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-13 18:16:56.894 - [任务 1] - Stop task milestones: 66b9b601916df264a848f380(任务 1)  
[INFO ] 2024-08-13 18:16:57.013 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] running status set to false 
[INFO ] 2024-08-13 18:16:57.013 - [任务 1][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-13 18:16:57.019 - [任务 1][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-08-13 18:16:57.019 - [任务 1][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-d42e316f-5648-4c9e-a121-085e42d4ff56 
[INFO ] 2024-08-13 18:16:57.019 - [任务 1][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-d42e316f-5648-4c9e-a121-085e42d4ff56 
[INFO ] 2024-08-13 18:16:57.020 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] schema data cleaned 
[INFO ] 2024-08-13 18:16:57.020 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] monitor closed 
[INFO ] 2024-08-13 18:16:57.020 - [任务 1][Mysql] - Node Mysql[d42e316f-5648-4c9e-a121-085e42d4ff56] close complete, cost 121 ms 
[INFO ] 2024-08-13 18:16:57.025 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] running status set to false 
[WARN ] 2024-08-13 18:16:57.026 - [任务 1][Pg] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: java.lang.InterruptedException: sleep interrupted
	java.lang.Thread.sleep(Native Method)
	io.tapdata.connector.postgres.PostgresConnector.writeRecord(PostgresConnector.java:352)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:828)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-13 18:16:57.034 - [任务 1][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode-21430775-aa43-4102-8cf4-1c4160d42aad 
[INFO ] 2024-08-13 18:16:57.037 - [任务 1][Pg] - PDK connector node released: HazelcastTargetPdkDataNode-21430775-aa43-4102-8cf4-1c4160d42aad 
[INFO ] 2024-08-13 18:16:57.039 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] schema data cleaned 
[INFO ] 2024-08-13 18:16:57.039 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] monitor closed 
[INFO ] 2024-08-13 18:16:57.244 - [任务 1][Pg] - Node Pg[21430775-aa43-4102-8cf4-1c4160d42aad] close complete, cost 17 ms 
[INFO ] 2024-08-13 18:16:57.692 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-13 18:16:57.693 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-13 18:16:57.693 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-13 18:16:57.715 - [任务 1] - Remove memory task client succeed, task: 任务 1[66b9b601916df264a848f380] 
[INFO ] 2024-08-13 18:16:57.920 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66b9b601916df264a848f380] 
[ERROR] 2024-08-13 18:17:57.123 - [任务 1][Pg] - Unknown exception occur when operate table: unknown <-- Error Message -->
Unknown exception occur when operate table: unknown

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:785)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	...

<-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$55(HazelcastTargetPdkDataNode.java:780)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:774)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$24(HazelcastTargetPdkDataNode.java:483)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:483)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:670)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:554)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:510)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:469)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:482)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:528)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$54(HazelcastTargetPdkDataNode.java:785)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 25 more

