[TRACE] 2025-06-27 01:57:28.143 - [PG~Sybase中文字] - Task initialization... 
[TRACE] 2025-06-27 01:57:28.149 - [PG~Sybase中文字] - Start task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字) 
[INFO ] 2025-06-27 01:57:28.307 - [PG~Sybase中文字] - Loading table structure completed 
[TRACE] 2025-06-27 01:57:28.307 - [PG~Sybase中文字] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-27 01:57:28.354 - [PG~Sybase中文字] - The engine receives PG~Sybase中文字 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-27 01:57:28.354 - [PG~Sybase中文字] - Task started 
[TRACE] 2025-06-27 01:57:28.568 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] start preload schema,table counts: 1 
[TRACE] 2025-06-27 01:57:28.580 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] start preload schema,table counts: 1 
[TRACE] 2025-06-27 01:57:28.581 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] start preload schema,table counts: 1 
[TRACE] 2025-06-27 01:57:28.581 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] preload schema finished, cost 1 ms 
[TRACE] 2025-06-27 01:57:28.581 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 01:57:28.581 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] preload schema finished, cost 0 ms 
[INFO ] 2025-06-27 01:57:28.610 - [PG~Sybase中文字][PGMaster] - Enable partition table support for source database 
[TRACE] 2025-06-27 01:57:28.610 - [PG~Sybase中文字][表编辑] - Node table_rename_processor(表编辑: 1373b647-c5ed-4163-bc67-6d952f713ac1) enable batch process 
[INFO ] 2025-06-27 01:57:29.263 - [PG~Sybase中文字][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-27 01:57:29.267 - [PG~Sybase中文字][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-27 01:57:29.267 - [PG~Sybase中文字][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-27 01:57:29.334 - [PG~Sybase中文字][Sybase] - Apply table structure to target database 
[WARN ] 2025-06-27 01:57:29.336 - [PG~Sybase中文字][Sybase] - Table bwk_bwk_ccc_big5_20160905_bak not exists, skip drop 
[INFO ] 2025-06-27 01:57:29.536 - [PG~Sybase中文字][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-27 01:57:29.537 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-27 01:57:29.537 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-27 01:57:29.537 - [PG~Sybase中文字][PGMaster] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-27 01:57:29.605 - [PG~Sybase中文字][PGMaster] - Starting batch read from 1 tables 
[TRACE] 2025-06-27 01:57:29.605 - [PG~Sybase中文字][PGMaster] - Initial sync started 
[INFO ] 2025-06-27 01:57:29.605 - [PG~Sybase中文字][PGMaster] - Starting batch read from table: bwk_ccc_big5_20160905 
[TRACE] 2025-06-27 01:57:29.615 - [PG~Sybase中文字][PGMaster] - Table bwk_ccc_big5_20160905 is going to be initial synced 
[TRACE] 2025-06-27 01:57:29.615 - [PG~Sybase中文字][PGMaster] - Query snapshot row size completed: PGMaster(2fdf666e-dab5-4fc5-8fe7-10517ca46358) 
[INFO ] 2025-06-27 01:57:29.617 - [PG~Sybase中文字][PGMaster] - Table bwk_ccc_big5_20160905 has been completed batch read 
[TRACE] 2025-06-27 01:57:29.617 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[TRACE] 2025-06-27 01:57:29.622 - [PG~Sybase中文字][PGMaster] - Skip table [bwk_ccc_big5_20160905] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-06-27 01:57:29.622 - [PG~Sybase中文字][PGMaster] - Query snapshot row size completed: PGMaster(2fdf666e-dab5-4fc5-8fe7-10517ca46358) 
[TRACE] 2025-06-27 01:57:29.627 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[INFO ] 2025-06-27 01:57:29.627 - [PG~Sybase中文字][PGMaster] - Batch read completed. 
[INFO ] 2025-06-27 01:57:29.838 - [PG~Sybase中文字][PGMaster] - Task run completed 
[TRACE] 2025-06-27 01:57:31.074 - [PG~Sybase中文字][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: bwk_bwk_ccc_big5_20160905_bak 
[ERROR] 2025-06-27 01:57:31.075 - [PG~Sybase中文字][Sybase] - Execute PDK method: TARGET_WRITE_RECORD, tableName: bwk_bwk_ccc_big5_20160905_bak <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: bwk_bwk_ccc_big5_20160905_bak

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: Cannot invoke "Object.toString()" because the return value of "java.util.Map$Entry.getValue()" is null
	io.tapdata.sybase.SybaseConnectorV2.lambda$convertBig2CP850$45(SybaseConnectorV2.java:676)
	java.base/java.util.LinkedHashMap$LinkedEntrySet.forEach(LinkedHashMap.java:708)
	io.tapdata.sybase.SybaseConnectorV2.convertBig2CP850(SybaseConnectorV2.java:672)
	io.tapdata.sybase.SybaseConnectorV2.convertWriteDataToCP850(SybaseConnectorV2.java:655)
	io.tapdata.sybase.SybaseConnectorV2.writeRecord(SybaseConnectorV2.java:644)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: bwk_bwk_ccc_big5_20160905_bak
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1127)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1064)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$34(HazelcastTargetPdkDataNode.java:719)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:719)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:974)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:888)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:829)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:789)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:675)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$25(HazelcastTargetPdkBaseNode.java:761)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:813)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:760)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NullPointerException: Cannot invoke "Object.toString()" because the return value of "java.util.Map$Entry.getValue()" is null
	at io.tapdata.sybase.SybaseConnectorV2.lambda$convertBig2CP850$45(SybaseConnectorV2.java:676)
	at java.base/java.util.LinkedHashMap$LinkedEntrySet.forEach(LinkedHashMap.java:708)
	at io.tapdata.sybase.SybaseConnectorV2.convertBig2CP850(SybaseConnectorV2.java:672)
	at io.tapdata.sybase.SybaseConnectorV2.convertWriteDataToCP850(SybaseConnectorV2.java:655)
	at io.tapdata.sybase.SybaseConnectorV2.writeRecord(SybaseConnectorV2.java:644)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1113)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1109)
	... 26 more

[TRACE] 2025-06-27 01:57:31.075 - [PG~Sybase中文字][Sybase] - Job suspend in error handle 
[TRACE] 2025-06-27 01:57:31.089 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] running status set to false 
[TRACE] 2025-06-27 01:57:31.089 - [PG~Sybase中文字][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1750960649393 
[TRACE] 2025-06-27 01:57:31.089 - [PG~Sybase中文字][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1750960649393 
[TRACE] 2025-06-27 01:57:31.090 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] schema data cleaned 
[TRACE] 2025-06-27 01:57:31.090 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] monitor closed 
[TRACE] 2025-06-27 01:57:31.091 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] close complete, cost 5 ms 
[TRACE] 2025-06-27 01:57:31.091 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] running status set to false 
[TRACE] 2025-06-27 01:57:31.093 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] schema data cleaned 
[TRACE] 2025-06-27 01:57:31.094 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] monitor closed 
[TRACE] 2025-06-27 01:57:31.095 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] close complete, cost 3 ms 
[TRACE] 2025-06-27 01:57:31.095 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] running status set to false 
[TRACE] 2025-06-27 01:57:31.112 - [PG~Sybase中文字][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_c166b09d-683f-43e8-921b-2d384ed0fc4e_1750960649196 
[TRACE] 2025-06-27 01:57:31.112 - [PG~Sybase中文字][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_c166b09d-683f-43e8-921b-2d384ed0fc4e_1750960649196 
[TRACE] 2025-06-27 01:57:31.112 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] schema data cleaned 
[TRACE] 2025-06-27 01:57:31.113 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] monitor closed 
[TRACE] 2025-06-27 01:57:31.314 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] close complete, cost 18 ms 
[INFO ] 2025-06-27 01:57:33.863 - [PG~Sybase中文字] - Task [PG~Sybase中文字] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-06-27 01:57:38.881 - [PG~Sybase中文字] - Task [PG~Sybase中文字] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-06-27 01:57:39.085 - [PG~Sybase中文字] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-27 01:57:39.896 - [PG~Sybase中文字] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@144496db 
[TRACE] 2025-06-27 01:57:39.905 - [PG~Sybase中文字] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@24afcd2d 
[TRACE] 2025-06-27 01:57:39.905 - [PG~Sybase中文字] - Stop task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字)  
[TRACE] 2025-06-27 01:57:40.048 - [PG~Sybase中文字] - Stopped task aspect(s) 
[TRACE] 2025-06-27 01:57:40.048 - [PG~Sybase中文字] - Snapshot order controller have been removed 
[INFO ] 2025-06-27 01:57:40.049 - [PG~Sybase中文字] - Task stopped. 
[TRACE] 2025-06-27 01:57:40.101 - [PG~Sybase中文字] - Remove memory task client succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 01:57:40.103 - [PG~Sybase中文字] - Destroy memory task client cache succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 01:58:26.150 - [PG~Sybase中文字] - Task initialization... 
[TRACE] 2025-06-27 01:58:26.150 - [PG~Sybase中文字] - Start task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字) 
[INFO ] 2025-06-27 01:58:26.284 - [PG~Sybase中文字] - Loading table structure completed 
[TRACE] 2025-06-27 01:58:26.285 - [PG~Sybase中文字] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-27 01:58:26.322 - [PG~Sybase中文字] - The engine receives PG~Sybase中文字 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-27 01:58:26.323 - [PG~Sybase中文字] - Task started 
[TRACE] 2025-06-27 01:58:26.348 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] start preload schema,table counts: 1 
[TRACE] 2025-06-27 01:58:26.349 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] start preload schema,table counts: 1 
[TRACE] 2025-06-27 01:58:26.349 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] preload schema finished, cost 1 ms 
[TRACE] 2025-06-27 01:58:26.349 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 01:58:26.349 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] start preload schema,table counts: 1 
[INFO ] 2025-06-27 01:58:26.349 - [PG~Sybase中文字][PGMaster] - Enable partition table support for source database 
[TRACE] 2025-06-27 01:58:26.349 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 01:58:26.555 - [PG~Sybase中文字][表编辑] - Node table_rename_processor(表编辑: 1373b647-c5ed-4163-bc67-6d952f713ac1) enable batch process 
[INFO ] 2025-06-27 01:58:27.092 - [PG~Sybase中文字][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-27 01:58:27.095 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-27 01:58:27.095 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-27 01:58:27.095 - [PG~Sybase中文字][PGMaster] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-27 01:58:27.145 - [PG~Sybase中文字][PGMaster] - Starting batch read from 1 tables 
[TRACE] 2025-06-27 01:58:27.145 - [PG~Sybase中文字][PGMaster] - Initial sync started 
[INFO ] 2025-06-27 01:58:27.146 - [PG~Sybase中文字][PGMaster] - Starting batch read from table: bwk_ccc_big5_20160905 
[TRACE] 2025-06-27 01:58:27.146 - [PG~Sybase中文字][PGMaster] - Table bwk_ccc_big5_20160905 is going to be initial synced 
[TRACE] 2025-06-27 01:58:27.148 - [PG~Sybase中文字][PGMaster] - Query snapshot row size completed: PGMaster(2fdf666e-dab5-4fc5-8fe7-10517ca46358) 
[INFO ] 2025-06-27 01:58:27.148 - [PG~Sybase中文字][PGMaster] - Table bwk_ccc_big5_20160905 has been completed batch read 
[TRACE] 2025-06-27 01:58:27.149 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[INFO ] 2025-06-27 01:58:27.149 - [PG~Sybase中文字][PGMaster] - Batch read completed. 
[INFO ] 2025-06-27 01:58:29.588 - [PG~Sybase中文字][PGMaster] - Task run completed 
[INFO ] 2025-06-27 01:58:52.939 - [PG~Sybase中文字][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-27 01:58:52.940 - [PG~Sybase中文字][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-27 01:58:52.955 - [PG~Sybase中文字][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-27 01:58:52.956 - [PG~Sybase中文字][Sybase] - Apply table structure to target database 
[TRACE] 2025-06-27 02:01:01.442 - [PG~Sybase中文字][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: bwk_bwk_ccc_big5_20160905_bak 
[ERROR] 2025-06-27 02:01:01.453 - [PG~Sybase中文字][Sybase] - Execute PDK method: TARGET_WRITE_RECORD, tableName: bwk_bwk_ccc_big5_20160905_bak <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: bwk_bwk_ccc_big5_20160905_bak

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: Cannot invoke "Object.toString()" because the return value of "java.util.Map$Entry.getValue()" is null
	io.tapdata.sybase.SybaseConnectorV2.lambda$convertBig2CP850$45(SybaseConnectorV2.java:676)
	java.base/java.util.LinkedHashMap$LinkedEntrySet.forEach(LinkedHashMap.java:708)
	io.tapdata.sybase.SybaseConnectorV2.convertBig2CP850(SybaseConnectorV2.java:672)
	io.tapdata.sybase.SybaseConnectorV2.convertWriteDataToCP850(SybaseConnectorV2.java:655)
	io.tapdata.sybase.SybaseConnectorV2.writeRecord(SybaseConnectorV2.java:644)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: bwk_bwk_ccc_big5_20160905_bak
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1127)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1064)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$34(HazelcastTargetPdkDataNode.java:719)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:719)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:974)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:888)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:829)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:789)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:675)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$25(HazelcastTargetPdkBaseNode.java:761)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:813)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:760)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NullPointerException: Cannot invoke "Object.toString()" because the return value of "java.util.Map$Entry.getValue()" is null
	at io.tapdata.sybase.SybaseConnectorV2.lambda$convertBig2CP850$45(SybaseConnectorV2.java:676)
	at java.base/java.util.LinkedHashMap$LinkedEntrySet.forEach(LinkedHashMap.java:708)
	at io.tapdata.sybase.SybaseConnectorV2.convertBig2CP850(SybaseConnectorV2.java:672)
	at io.tapdata.sybase.SybaseConnectorV2.convertWriteDataToCP850(SybaseConnectorV2.java:655)
	at io.tapdata.sybase.SybaseConnectorV2.writeRecord(SybaseConnectorV2.java:644)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1113)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1109)
	... 26 more

[TRACE] 2025-06-27 02:01:01.454 - [PG~Sybase中文字][Sybase] - Job suspend in error handle 
[TRACE] 2025-06-27 02:01:01.471 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] running status set to false 
[TRACE] 2025-06-27 02:01:01.478 - [PG~Sybase中文字][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1750960706931 
[TRACE] 2025-06-27 02:01:01.478 - [PG~Sybase中文字][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1750960706931 
[TRACE] 2025-06-27 02:01:01.478 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] schema data cleaned 
[TRACE] 2025-06-27 02:01:01.478 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] monitor closed 
[TRACE] 2025-06-27 02:01:01.478 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] close complete, cost 14 ms 
[TRACE] 2025-06-27 02:01:01.479 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] running status set to false 
[TRACE] 2025-06-27 02:01:01.479 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] schema data cleaned 
[TRACE] 2025-06-27 02:01:01.479 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] monitor closed 
[TRACE] 2025-06-27 02:01:01.479 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] close complete, cost 3 ms 
[TRACE] 2025-06-27 02:01:01.479 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] running status set to false 
[TRACE] 2025-06-27 02:01:02.051 - [PG~Sybase中文字][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_c166b09d-683f-43e8-921b-2d384ed0fc4e_1750960707136 
[TRACE] 2025-06-27 02:01:02.051 - [PG~Sybase中文字][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_c166b09d-683f-43e8-921b-2d384ed0fc4e_1750960707136 
[TRACE] 2025-06-27 02:01:02.052 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] schema data cleaned 
[TRACE] 2025-06-27 02:01:02.052 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] monitor closed 
[TRACE] 2025-06-27 02:01:02.254 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] close complete, cost 573 ms 
[INFO ] 2025-06-27 02:01:06.310 - [PG~Sybase中文字] - Task [PG~Sybase中文字] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-06-27 02:01:11.318 - [PG~Sybase中文字] - Task [PG~Sybase中文字] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-06-27 02:01:11.521 - [PG~Sybase中文字] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-27 02:01:12.322 - [PG~Sybase中文字] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@1e70803c 
[TRACE] 2025-06-27 02:01:12.329 - [PG~Sybase中文字] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2fa419ce 
[TRACE] 2025-06-27 02:01:12.329 - [PG~Sybase中文字] - Stop task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字)  
[TRACE] 2025-06-27 02:01:12.460 - [PG~Sybase中文字] - Stopped task aspect(s) 
[TRACE] 2025-06-27 02:01:12.460 - [PG~Sybase中文字] - Snapshot order controller have been removed 
[INFO ] 2025-06-27 02:01:12.460 - [PG~Sybase中文字] - Task stopped. 
[TRACE] 2025-06-27 02:01:12.489 - [PG~Sybase中文字] - Remove memory task client succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 02:01:12.490 - [PG~Sybase中文字] - Destroy memory task client cache succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 02:01:18.353 - [PG~Sybase中文字] - Task initialization... 
[TRACE] 2025-06-27 02:01:18.354 - [PG~Sybase中文字] - Start task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字) 
[INFO ] 2025-06-27 02:01:18.526 - [PG~Sybase中文字] - Loading table structure completed 
[TRACE] 2025-06-27 02:01:18.527 - [PG~Sybase中文字] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-27 02:01:18.580 - [PG~Sybase中文字] - The engine receives PG~Sybase中文字 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-27 02:01:18.580 - [PG~Sybase中文字] - Task started 
[TRACE] 2025-06-27 02:01:18.616 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] start preload schema,table counts: 1 
[TRACE] 2025-06-27 02:01:18.616 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] start preload schema,table counts: 1 
[TRACE] 2025-06-27 02:01:18.616 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] start preload schema,table counts: 1 
[TRACE] 2025-06-27 02:01:18.616 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 02:01:18.617 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 02:01:18.617 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 02:01:18.617 - [PG~Sybase中文字][表编辑] - Node table_rename_processor(表编辑: 1373b647-c5ed-4163-bc67-6d952f713ac1) enable batch process 
[INFO ] 2025-06-27 02:01:18.618 - [PG~Sybase中文字][PGMaster] - Enable partition table support for source database 
[INFO ] 2025-06-27 02:01:21.086 - [PG~Sybase中文字][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-27 02:01:21.087 - [PG~Sybase中文字][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-27 02:01:21.087 - [PG~Sybase中文字][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-27 02:01:21.289 - [PG~Sybase中文字][Sybase] - Apply table structure to target database 
[INFO ] 2025-06-27 02:01:21.307 - [PG~Sybase中文字][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-27 02:01:21.307 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-27 02:01:21.307 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-27 02:01:21.307 - [PG~Sybase中文字][PGMaster] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-27 02:01:21.358 - [PG~Sybase中文字][PGMaster] - Starting batch read from 1 tables 
[TRACE] 2025-06-27 02:01:21.358 - [PG~Sybase中文字][PGMaster] - Initial sync started 
[INFO ] 2025-06-27 02:01:21.358 - [PG~Sybase中文字][PGMaster] - Starting batch read from table: bwk_ccc_big5_20160905 
[TRACE] 2025-06-27 02:01:21.358 - [PG~Sybase中文字][PGMaster] - Table bwk_ccc_big5_20160905 is going to be initial synced 
[TRACE] 2025-06-27 02:01:21.362 - [PG~Sybase中文字][PGMaster] - Query snapshot row size completed: PGMaster(2fdf666e-dab5-4fc5-8fe7-10517ca46358) 
[INFO ] 2025-06-27 02:01:21.362 - [PG~Sybase中文字][PGMaster] - Table bwk_ccc_big5_20160905 has been completed batch read 
[TRACE] 2025-06-27 02:01:21.362 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[INFO ] 2025-06-27 02:01:21.362 - [PG~Sybase中文字][PGMaster] - Batch read completed. 
[INFO ] 2025-06-27 02:01:21.362 - [PG~Sybase中文字][PGMaster] - Task run completed 
[TRACE] 2025-06-27 02:03:09.147 - [PG~Sybase中文字][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: bwk_bwk_ccc_big5_20160905_bak 
[ERROR] 2025-06-27 02:03:09.150 - [PG~Sybase中文字][Sybase] - Execute PDK method: TARGET_WRITE_RECORD, tableName: bwk_bwk_ccc_big5_20160905_bak <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: bwk_bwk_ccc_big5_20160905_bak

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: Cannot invoke "Object.toString()" because the return value of "java.util.Map$Entry.getValue()" is null
	io.tapdata.sybase.SybaseConnectorV2.lambda$convertBig2CP850$45(SybaseConnectorV2.java:676)
	java.base/java.util.LinkedHashMap$LinkedEntrySet.forEach(LinkedHashMap.java:708)
	io.tapdata.sybase.SybaseConnectorV2.convertBig2CP850(SybaseConnectorV2.java:672)
	io.tapdata.sybase.SybaseConnectorV2.convertWriteDataToCP850(SybaseConnectorV2.java:655)
	io.tapdata.sybase.SybaseConnectorV2.writeRecord(SybaseConnectorV2.java:644)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: bwk_bwk_ccc_big5_20160905_bak
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1127)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$72(HazelcastTargetPdkDataNode.java:1064)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$34(HazelcastTargetPdkDataNode.java:719)
	at java.base/java.util.HashMap.forEach(HashMap.java:1421)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:719)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:974)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:888)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:829)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:789)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:675)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$25(HazelcastTargetPdkBaseNode.java:761)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:813)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:760)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NullPointerException: Cannot invoke "Object.toString()" because the return value of "java.util.Map$Entry.getValue()" is null
	at io.tapdata.sybase.SybaseConnectorV2.lambda$convertBig2CP850$45(SybaseConnectorV2.java:676)
	at java.base/java.util.LinkedHashMap$LinkedEntrySet.forEach(LinkedHashMap.java:708)
	at io.tapdata.sybase.SybaseConnectorV2.convertBig2CP850(SybaseConnectorV2.java:672)
	at io.tapdata.sybase.SybaseConnectorV2.convertWriteDataToCP850(SybaseConnectorV2.java:655)
	at io.tapdata.sybase.SybaseConnectorV2.writeRecord(SybaseConnectorV2.java:644)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1113)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$71(HazelcastTargetPdkDataNode.java:1109)
	... 26 more

[TRACE] 2025-06-27 02:03:09.150 - [PG~Sybase中文字][Sybase] - Job suspend in error handle 
[TRACE] 2025-06-27 02:03:09.220 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] running status set to false 
[TRACE] 2025-06-27 02:03:09.222 - [PG~Sybase中文字][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1750960881160 
[TRACE] 2025-06-27 02:03:09.222 - [PG~Sybase中文字][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1750960881160 
[TRACE] 2025-06-27 02:03:09.222 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] schema data cleaned 
[TRACE] 2025-06-27 02:03:09.222 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] monitor closed 
[TRACE] 2025-06-27 02:03:09.222 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] close complete, cost 4 ms 
[TRACE] 2025-06-27 02:03:09.222 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] running status set to false 
[TRACE] 2025-06-27 02:03:09.222 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] schema data cleaned 
[TRACE] 2025-06-27 02:03:09.222 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] monitor closed 
[TRACE] 2025-06-27 02:03:09.223 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] close complete, cost 1 ms 
[TRACE] 2025-06-27 02:03:09.223 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] running status set to false 
[TRACE] 2025-06-27 02:03:09.269 - [PG~Sybase中文字][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_c166b09d-683f-43e8-921b-2d384ed0fc4e_1750960879142 
[TRACE] 2025-06-27 02:03:09.269 - [PG~Sybase中文字][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_c166b09d-683f-43e8-921b-2d384ed0fc4e_1750960879142 
[TRACE] 2025-06-27 02:03:09.269 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] schema data cleaned 
[TRACE] 2025-06-27 02:03:09.270 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] monitor closed 
[TRACE] 2025-06-27 02:03:09.270 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] close complete, cost 46 ms 
[INFO ] 2025-06-27 02:03:14.328 - [PG~Sybase中文字] - Task [PG~Sybase中文字] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-06-27 02:03:19.147 - [PG~Sybase中文字] - Task [PG~Sybase中文字] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-06-27 02:03:19.148 - [PG~Sybase中文字] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-27 02:03:20.154 - [PG~Sybase中文字] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@61ebe07e 
[TRACE] 2025-06-27 02:03:20.154 - [PG~Sybase中文字] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@192bde99 
[TRACE] 2025-06-27 02:03:20.269 - [PG~Sybase中文字] - Stop task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字)  
[TRACE] 2025-06-27 02:03:20.269 - [PG~Sybase中文字] - Stopped task aspect(s) 
[TRACE] 2025-06-27 02:03:20.269 - [PG~Sybase中文字] - Snapshot order controller have been removed 
[INFO ] 2025-06-27 02:03:20.269 - [PG~Sybase中文字] - Task stopped. 
[TRACE] 2025-06-27 02:03:20.291 - [PG~Sybase中文字] - Remove memory task client succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 02:03:20.291 - [PG~Sybase中文字] - Destroy memory task client cache succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 02:05:56.837 - [PG~Sybase中文字] - Task initialization... 
[TRACE] 2025-06-27 02:05:56.838 - [PG~Sybase中文字] - Start task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字) 
[INFO ] 2025-06-27 02:05:57.008 - [PG~Sybase中文字] - Loading table structure completed 
[TRACE] 2025-06-27 02:05:57.008 - [PG~Sybase中文字] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-27 02:05:57.062 - [PG~Sybase中文字] - The engine receives PG~Sybase中文字 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-27 02:05:57.062 - [PG~Sybase中文字] - Task started 
[TRACE] 2025-06-27 02:05:57.080 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] start preload schema,table counts: 1 
[TRACE] 2025-06-27 02:05:57.080 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] start preload schema,table counts: 1 
[TRACE] 2025-06-27 02:05:57.082 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 02:05:57.082 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] preload schema finished, cost 0 ms 
[INFO ] 2025-06-27 02:05:57.082 - [PG~Sybase中文字][PGMaster] - Enable partition table support for source database 
[TRACE] 2025-06-27 02:05:57.082 - [PG~Sybase中文字][表编辑] - Node table_rename_processor(表编辑: 1373b647-c5ed-4163-bc67-6d952f713ac1) enable batch process 
[TRACE] 2025-06-27 02:05:57.082 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] start preload schema,table counts: 1 
[TRACE] 2025-06-27 02:05:57.082 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] preload schema finished, cost 0 ms 
[INFO ] 2025-06-27 02:05:57.770 - [PG~Sybase中文字][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-27 02:05:57.772 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-27 02:05:57.772 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-27 02:05:57.772 - [PG~Sybase中文字][PGMaster] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-27 02:05:57.823 - [PG~Sybase中文字][PGMaster] - Starting batch read from 1 tables 
[TRACE] 2025-06-27 02:05:57.824 - [PG~Sybase中文字][PGMaster] - Initial sync started 
[INFO ] 2025-06-27 02:05:57.824 - [PG~Sybase中文字][PGMaster] - Starting batch read from table: bwk_ccc_big5_20160905 
[TRACE] 2025-06-27 02:05:57.824 - [PG~Sybase中文字][PGMaster] - Table bwk_ccc_big5_20160905 is going to be initial synced 
[TRACE] 2025-06-27 02:05:57.831 - [PG~Sybase中文字][PGMaster] - Query snapshot row size completed: PGMaster(2fdf666e-dab5-4fc5-8fe7-10517ca46358) 
[INFO ] 2025-06-27 02:05:57.831 - [PG~Sybase中文字][PGMaster] - Table bwk_ccc_big5_20160905 has been completed batch read 
[TRACE] 2025-06-27 02:05:57.831 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[INFO ] 2025-06-27 02:05:57.831 - [PG~Sybase中文字][PGMaster] - Batch read completed. 
[INFO ] 2025-06-27 02:05:57.831 - [PG~Sybase中文字][PGMaster] - Task run completed 
[INFO ] 2025-06-27 02:05:57.844 - [PG~Sybase中文字][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-27 02:05:57.848 - [PG~Sybase中文字][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-27 02:05:57.848 - [PG~Sybase中文字][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-27 02:05:58.049 - [PG~Sybase中文字][Sybase] - Apply table structure to target database 
[TRACE] 2025-06-27 02:05:58.580 - [PG~Sybase中文字][Sybase] - Process after table "bwk_bwk_ccc_big5_20160905_bak" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-27 02:05:58.580 - [PG~Sybase中文字][Sybase] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-27 02:05:58.642 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] running status set to false 
[TRACE] 2025-06-27 02:05:58.646 - [PG~Sybase中文字][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1750961157612 
[TRACE] 2025-06-27 02:05:58.650 - [PG~Sybase中文字][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1750961157612 
[TRACE] 2025-06-27 02:05:58.650 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] running status set to false 
[TRACE] 2025-06-27 02:05:58.650 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] schema data cleaned 
[TRACE] 2025-06-27 02:05:58.650 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] schema data cleaned 
[TRACE] 2025-06-27 02:05:58.650 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] monitor closed 
[TRACE] 2025-06-27 02:05:58.650 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] monitor closed 
[TRACE] 2025-06-27 02:05:58.653 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] close complete, cost 7 ms 
[TRACE] 2025-06-27 02:05:58.653 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] close complete, cost 1 ms 
[TRACE] 2025-06-27 02:05:58.693 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] running status set to false 
[TRACE] 2025-06-27 02:05:58.693 - [PG~Sybase中文字][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_c166b09d-683f-43e8-921b-2d384ed0fc4e_1750961157783 
[TRACE] 2025-06-27 02:05:58.694 - [PG~Sybase中文字][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_c166b09d-683f-43e8-921b-2d384ed0fc4e_1750961157783 
[TRACE] 2025-06-27 02:05:58.694 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] schema data cleaned 
[TRACE] 2025-06-27 02:05:58.694 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] monitor closed 
[TRACE] 2025-06-27 02:05:58.694 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] close complete, cost 44 ms 
[TRACE] 2025-06-27 02:06:10.443 - [PG~Sybase中文字] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-27 02:06:11.450 - [PG~Sybase中文字] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@6d2e882d 
[TRACE] 2025-06-27 02:06:11.459 - [PG~Sybase中文字] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@506d1940 
[TRACE] 2025-06-27 02:06:11.464 - [PG~Sybase中文字] - Stop task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字)  
[TRACE] 2025-06-27 02:06:11.601 - [PG~Sybase中文字] - Stopped task aspect(s) 
[TRACE] 2025-06-27 02:06:11.601 - [PG~Sybase中文字] - Snapshot order controller have been removed 
[INFO ] 2025-06-27 02:06:11.602 - [PG~Sybase中文字] - Task stopped. 
[TRACE] 2025-06-27 02:06:11.666 - [PG~Sybase中文字] - Remove memory task client succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 02:06:11.666 - [PG~Sybase中文字] - Destroy memory task client cache succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 03:12:26.286 - [PG~Sybase中文字] - Task initialization... 
[TRACE] 2025-06-27 03:12:26.401 - [PG~Sybase中文字] - Start task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字) 
[INFO ] 2025-06-27 03:12:26.401 - [PG~Sybase中文字] - Loading table structure completed 
[TRACE] 2025-06-27 03:12:26.527 - [PG~Sybase中文字] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-27 03:12:26.705 - [PG~Sybase中文字] - The engine receives PG~Sybase中文字 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-27 03:12:26.706 - [PG~Sybase中文字] - Task started 
[TRACE] 2025-06-27 03:12:26.769 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] start preload schema,table counts: 1 
[TRACE] 2025-06-27 03:12:26.770 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] start preload schema,table counts: 1 
[TRACE] 2025-06-27 03:12:26.770 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] start preload schema,table counts: 1 
[TRACE] 2025-06-27 03:12:26.770 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 03:12:26.772 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] preload schema finished, cost 1 ms 
[TRACE] 2025-06-27 03:12:26.772 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] preload schema finished, cost 1 ms 
[TRACE] 2025-06-27 03:12:26.772 - [PG~Sybase中文字][表编辑] - Node table_rename_processor(表编辑: 1373b647-c5ed-4163-bc67-6d952f713ac1) enable batch process 
[INFO ] 2025-06-27 03:12:26.772 - [PG~Sybase中文字][PGMaster] - Enable partition table support for source database 
[INFO ] 2025-06-27 03:12:27.680 - [PG~Sybase中文字][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-27 03:12:27.680 - [PG~Sybase中文字][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-27 03:12:27.681 - [PG~Sybase中文字][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-27 03:12:27.706 - [PG~Sybase中文字][Sybase] - Apply table structure to target database 
[INFO ] 2025-06-27 03:12:27.931 - [PG~Sybase中文字][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-27 03:12:27.933 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-27 03:12:27.933 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-27 03:12:27.933 - [PG~Sybase中文字][PGMaster] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-27 03:12:28.074 - [PG~Sybase中文字][PGMaster] - Starting batch read from 1 tables 
[TRACE] 2025-06-27 03:12:28.077 - [PG~Sybase中文字][PGMaster] - Initial sync started 
[INFO ] 2025-06-27 03:12:28.077 - [PG~Sybase中文字][PGMaster] - Starting batch read from table: ccc_big5_20160905 
[TRACE] 2025-06-27 03:12:28.077 - [PG~Sybase中文字][PGMaster] - Table ccc_big5_20160905 is going to be initial synced 
[TRACE] 2025-06-27 03:12:28.114 - [PG~Sybase中文字][PGMaster] - Query snapshot row size completed: PGMaster(2fdf666e-dab5-4fc5-8fe7-10517ca46358) 
[INFO ] 2025-06-27 03:12:28.114 - [PG~Sybase中文字][PGMaster] - Table ccc_big5_20160905 has been completed batch read 
[TRACE] 2025-06-27 03:12:28.114 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[INFO ] 2025-06-27 03:12:28.114 - [PG~Sybase中文字][PGMaster] - Batch read completed. 
[INFO ] 2025-06-27 03:12:28.114 - [PG~Sybase中文字][PGMaster] - Task run completed 
[TRACE] 2025-06-27 03:12:29.495 - [PG~Sybase中文字][Sybase] - Process after table "bwk_ccc_big5_20160905" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-27 03:12:29.500 - [PG~Sybase中文字][Sybase] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-27 03:12:29.524 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] running status set to false 
[TRACE] 2025-06-27 03:12:29.524 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] running status set to false 
[TRACE] 2025-06-27 03:12:29.525 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] schema data cleaned 
[TRACE] 2025-06-27 03:12:29.525 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] monitor closed 
[TRACE] 2025-06-27 03:12:29.529 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] running status set to false 
[TRACE] 2025-06-27 03:12:29.530 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] close complete, cost 5 ms 
[TRACE] 2025-06-27 03:12:29.534 - [PG~Sybase中文字][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1750965147676 
[TRACE] 2025-06-27 03:12:29.534 - [PG~Sybase中文字][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1750965147676 
[TRACE] 2025-06-27 03:12:29.534 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] schema data cleaned 
[TRACE] 2025-06-27 03:12:29.534 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] monitor closed 
[TRACE] 2025-06-27 03:12:29.561 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] close complete, cost 20 ms 
[TRACE] 2025-06-27 03:12:29.562 - [PG~Sybase中文字][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_c166b09d-683f-43e8-921b-2d384ed0fc4e_1750965147492 
[TRACE] 2025-06-27 03:12:29.562 - [PG~Sybase中文字][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_c166b09d-683f-43e8-921b-2d384ed0fc4e_1750965147492 
[TRACE] 2025-06-27 03:12:29.562 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] schema data cleaned 
[TRACE] 2025-06-27 03:12:29.564 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] monitor closed 
[TRACE] 2025-06-27 03:12:29.564 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] close complete, cost 35 ms 
[TRACE] 2025-06-27 03:12:37.695 - [PG~Sybase中文字] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-27 03:12:38.603 - [PG~Sybase中文字] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@52a0704b 
[TRACE] 2025-06-27 03:12:38.604 - [PG~Sybase中文字] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7470247 
[TRACE] 2025-06-27 03:12:38.763 - [PG~Sybase中文字] - Stop task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字)  
[TRACE] 2025-06-27 03:12:38.763 - [PG~Sybase中文字] - Stopped task aspect(s) 
[TRACE] 2025-06-27 03:12:38.768 - [PG~Sybase中文字] - Snapshot order controller have been removed 
[INFO ] 2025-06-27 03:12:38.769 - [PG~Sybase中文字] - Task stopped. 
[TRACE] 2025-06-27 03:12:38.806 - [PG~Sybase中文字] - Remove memory task client succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 03:12:38.806 - [PG~Sybase中文字] - Destroy memory task client cache succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 03:15:44.611 - [PG~Sybase中文字] - Task initialization... 
[TRACE] 2025-06-27 03:15:44.754 - [PG~Sybase中文字] - Start task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字) 
[INFO ] 2025-06-27 03:15:44.754 - [PG~Sybase中文字] - Loading table structure completed 
[TRACE] 2025-06-27 03:15:45.341 - [PG~Sybase中文字] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-27 03:15:45.672 - [PG~Sybase中文字] - The engine receives PG~Sybase中文字 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-27 03:15:45.852 - [PG~Sybase中文字] - Task started 
[TRACE] 2025-06-27 03:15:45.852 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] start preload schema,table counts: 1 
[TRACE] 2025-06-27 03:15:45.853 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] start preload schema,table counts: 1 
[TRACE] 2025-06-27 03:15:45.853 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] start preload schema,table counts: 1 
[TRACE] 2025-06-27 03:15:45.853 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 03:15:45.853 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 03:15:45.853 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 03:15:45.853 - [PG~Sybase中文字][表编辑] - Node table_rename_processor(表编辑: 1373b647-c5ed-4163-bc67-6d952f713ac1) enable batch process 
[INFO ] 2025-06-27 03:15:46.072 - [PG~Sybase中文字][PGMaster] - Enable partition table support for source database 
[INFO ] 2025-06-27 03:15:46.806 - [PG~Sybase中文字][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-27 03:15:46.809 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-27 03:15:46.809 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-27 03:15:46.809 - [PG~Sybase中文字][PGMaster] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-06-27 03:15:47.003 - [PG~Sybase中文字][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-27 03:15:47.004 - [PG~Sybase中文字][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-27 03:15:47.004 - [PG~Sybase中文字][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-27 03:15:47.211 - [PG~Sybase中文字][Sybase] - Apply table structure to target database 
[INFO ] 2025-06-27 03:15:47.326 - [PG~Sybase中文字][PGMaster] - Starting batch read from 1 tables 
[TRACE] 2025-06-27 03:15:47.327 - [PG~Sybase中文字][PGMaster] - Initial sync started 
[INFO ] 2025-06-27 03:15:47.327 - [PG~Sybase中文字][PGMaster] - Starting batch read from table: ccc_big5_20160905 
[TRACE] 2025-06-27 03:15:47.328 - [PG~Sybase中文字][PGMaster] - Table ccc_big5_20160905 is going to be initial synced 
[TRACE] 2025-06-27 03:15:47.338 - [PG~Sybase中文字][PGMaster] - Query snapshot row size completed: PGMaster(2fdf666e-dab5-4fc5-8fe7-10517ca46358) 
[INFO ] 2025-06-27 03:15:47.350 - [PG~Sybase中文字][PGMaster] - Table ccc_big5_20160905 has been completed batch read 
[TRACE] 2025-06-27 03:15:47.350 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[INFO ] 2025-06-27 03:15:47.351 - [PG~Sybase中文字][PGMaster] - Batch read completed. 
[INFO ] 2025-06-27 03:15:47.351 - [PG~Sybase中文字][PGMaster] - Task run completed 
[TRACE] 2025-06-27 03:15:48.404 - [PG~Sybase中文字][Sybase] - Process after table "bwk_ccc_big5_20160905" initial sync finished, cost: 1 ms 
[INFO ] 2025-06-27 03:15:48.407 - [PG~Sybase中文字][Sybase] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-27 03:15:48.534 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] running status set to false 
[TRACE] 2025-06-27 03:15:48.543 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] running status set to false 
[TRACE] 2025-06-27 03:15:48.544 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] running status set to false 
[TRACE] 2025-06-27 03:15:48.607 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] schema data cleaned 
[TRACE] 2025-06-27 03:15:48.607 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] monitor closed 
[TRACE] 2025-06-27 03:15:48.607 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] close complete, cost 63 ms 
[TRACE] 2025-06-27 03:15:48.607 - [PG~Sybase中文字][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1750965346480 
[TRACE] 2025-06-27 03:15:48.607 - [PG~Sybase中文字][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1750965346480 
[TRACE] 2025-06-27 03:15:48.608 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] schema data cleaned 
[TRACE] 2025-06-27 03:15:48.608 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] monitor closed 
[TRACE] 2025-06-27 03:15:48.870 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] close complete, cost 343 ms 
[TRACE] 2025-06-27 03:15:48.904 - [PG~Sybase中文字][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_c166b09d-683f-43e8-921b-2d384ed0fc4e_1750965346785 
[TRACE] 2025-06-27 03:15:48.905 - [PG~Sybase中文字][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_c166b09d-683f-43e8-921b-2d384ed0fc4e_1750965346785 
[TRACE] 2025-06-27 03:15:48.905 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] schema data cleaned 
[TRACE] 2025-06-27 03:15:48.908 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] monitor closed 
[TRACE] 2025-06-27 03:15:48.908 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] close complete, cost 394 ms 
[TRACE] 2025-06-27 03:15:55.089 - [PG~Sybase中文字] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-27 03:15:55.976 - [PG~Sybase中文字] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@5793bdc3 
[TRACE] 2025-06-27 03:15:55.976 - [PG~Sybase中文字] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@114cabfa 
[TRACE] 2025-06-27 03:15:55.987 - [PG~Sybase中文字] - Stop task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字)  
[TRACE] 2025-06-27 03:15:56.122 - [PG~Sybase中文字] - Stopped task aspect(s) 
[TRACE] 2025-06-27 03:15:56.124 - [PG~Sybase中文字] - Snapshot order controller have been removed 
[INFO ] 2025-06-27 03:15:56.125 - [PG~Sybase中文字] - Task stopped. 
[TRACE] 2025-06-27 03:15:56.165 - [PG~Sybase中文字] - Remove memory task client succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 03:15:56.165 - [PG~Sybase中文字] - Destroy memory task client cache succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 03:16:18.490 - [PG~Sybase中文字] - Task initialization... 
[TRACE] 2025-06-27 03:16:18.530 - [PG~Sybase中文字] - Start task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字) 
[INFO ] 2025-06-27 03:16:19.120 - [PG~Sybase中文字] - Loading table structure completed 
[TRACE] 2025-06-27 03:16:19.457 - [PG~Sybase中文字] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-27 03:16:19.460 - [PG~Sybase中文字] - The engine receives PG~Sybase中文字 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-27 03:16:19.589 - [PG~Sybase中文字] - Task started 
[TRACE] 2025-06-27 03:16:19.741 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] start preload schema,table counts: 1 
[TRACE] 2025-06-27 03:16:19.747 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] start preload schema,table counts: 1 
[TRACE] 2025-06-27 03:16:19.761 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] start preload schema,table counts: 1 
[TRACE] 2025-06-27 03:16:19.777 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 03:16:19.780 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] preload schema finished, cost 0 ms 
[INFO ] 2025-06-27 03:16:19.781 - [PG~Sybase中文字][PGMaster] - Enable partition table support for source database 
[TRACE] 2025-06-27 03:16:19.791 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 03:16:19.993 - [PG~Sybase中文字][表编辑] - Node table_rename_processor(表编辑: 1373b647-c5ed-4163-bc67-6d952f713ac1) enable batch process 
[INFO ] 2025-06-27 03:16:20.678 - [PG~Sybase中文字][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-27 03:16:20.693 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-27 03:16:20.703 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-27 03:16:20.706 - [PG~Sybase中文字][PGMaster] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-27 03:16:20.767 - [PG~Sybase中文字][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-27 03:16:20.811 - [PG~Sybase中文字][PGMaster] - new logical replication slot created, slotName:tapdata_cdc_419f0a3c_b9f1_49ae_b9a9_6df80cf77a03 
[INFO ] 2025-06-27 03:16:20.812 - [PG~Sybase中文字][PGMaster] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-27 03:16:20.813 - [PG~Sybase中文字][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-27 03:16:20.816 - [PG~Sybase中文字][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-27 03:16:20.817 - [PG~Sybase中文字][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-27 03:16:21.018 - [PG~Sybase中文字][Sybase] - Apply table structure to target database 
[INFO ] 2025-06-27 03:16:21.208 - [PG~Sybase中文字][PGMaster] - Starting batch read from 1 tables 
[TRACE] 2025-06-27 03:16:21.209 - [PG~Sybase中文字][PGMaster] - Initial sync started 
[INFO ] 2025-06-27 03:16:21.210 - [PG~Sybase中文字][PGMaster] - Starting batch read from table: ccc_big5_20160905 
[TRACE] 2025-06-27 03:16:21.210 - [PG~Sybase中文字][PGMaster] - Table ccc_big5_20160905 is going to be initial synced 
[TRACE] 2025-06-27 03:16:21.221 - [PG~Sybase中文字][PGMaster] - Query snapshot row size completed: PGMaster(2fdf666e-dab5-4fc5-8fe7-10517ca46358) 
[INFO ] 2025-06-27 03:16:21.226 - [PG~Sybase中文字][PGMaster] - Table ccc_big5_20160905 has been completed batch read 
[TRACE] 2025-06-27 03:16:21.230 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[INFO ] 2025-06-27 03:16:21.231 - [PG~Sybase中文字][PGMaster] - Batch read completed. 
[TRACE] 2025-06-27 03:16:21.232 - [PG~Sybase中文字][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-27 03:16:21.247 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[TRACE] 2025-06-27 03:16:21.250 - [PG~Sybase中文字][PGMaster] - Starting stream read, table list: [ccc_big5_20160905], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-27 03:16:21.251 - [PG~Sybase中文字][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-27 03:16:21.280 - [PG~Sybase中文字][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-27 03:16:21.281 - [PG~Sybase中文字][PGMaster] - Using an existing logical replication slot, slotName:tapdata_cdc_419f0a3c_b9f1_49ae_b9a9_6df80cf77a03 
[TRACE] 2025-06-27 03:16:21.885 - [PG~Sybase中文字][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [ccc_big5_20160905], data change syncing 
[TRACE] 2025-06-27 03:16:22.163 - [PG~Sybase中文字][Sybase] - Process after table "bwk_ccc_big5_20160905" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-27 03:16:22.164 - [PG~Sybase中文字][Sybase] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-27 03:19:19.871 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] running status set to false 
[TRACE] 2025-06-27 10:25:32.133 - [PG~Sybase中文字] - Task initialization... 
[TRACE] 2025-06-27 10:25:32.134 - [PG~Sybase中文字] - Start task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字) 
[INFO ] 2025-06-27 10:25:32.308 - [PG~Sybase中文字] - Loading table structure completed 
[TRACE] 2025-06-27 10:25:32.308 - [PG~Sybase中文字] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-27 10:25:32.367 - [PG~Sybase中文字] - The engine receives PG~Sybase中文字 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-27 10:25:32.367 - [PG~Sybase中文字] - Task started 
[TRACE] 2025-06-27 10:25:32.423 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] start preload schema,table counts: 1 
[TRACE] 2025-06-27 10:25:32.423 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 10:25:32.423 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] start preload schema,table counts: 1 
[TRACE] 2025-06-27 10:25:32.424 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] start preload schema,table counts: 1 
[TRACE] 2025-06-27 10:25:32.424 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 10:25:32.424 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] preload schema finished, cost 0 ms 
[INFO ] 2025-06-27 10:25:32.424 - [PG~Sybase中文字][PGMaster] - Enable partition table support for source database 
[TRACE] 2025-06-27 10:25:32.626 - [PG~Sybase中文字][表编辑] - Node table_rename_processor(表编辑: 1373b647-c5ed-4163-bc67-6d952f713ac1) enable batch process 
[INFO ] 2025-06-27 10:26:01.630 - [PG~Sybase中文字][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-27 10:26:01.630 - [PG~Sybase中文字][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-27 10:26:01.630 - [PG~Sybase中文字][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-27 10:26:01.677 - [PG~Sybase中文字][Sybase] - Apply table structure to target database 
[INFO ] 2025-06-27 10:26:01.819 - [PG~Sybase中文字][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-27 10:26:01.822 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-27 10:26:01.822 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-27 10:26:01.823 - [PG~Sybase中文字][PGMaster] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-27 10:26:01.878 - [PG~Sybase中文字][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-27 10:26:01.878 - [PG~Sybase中文字][PGMaster] - new logical replication slot created, slotName:tapdata_cdc_966ed08c_ae3c_466a_a8d0_2b7a3e32f5ff 
[INFO ] 2025-06-27 10:26:01.948 - [PG~Sybase中文字][PGMaster] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-27 10:26:01.948 - [PG~Sybase中文字][PGMaster] - Starting batch read from 1 tables 
[TRACE] 2025-06-27 10:26:01.956 - [PG~Sybase中文字][PGMaster] - Initial sync started 
[INFO ] 2025-06-27 10:26:01.956 - [PG~Sybase中文字][PGMaster] - Starting batch read from table: ccc_big5_20160905 
[TRACE] 2025-06-27 10:26:01.956 - [PG~Sybase中文字][PGMaster] - Table ccc_big5_20160905 is going to be initial synced 
[TRACE] 2025-06-27 10:26:01.962 - [PG~Sybase中文字][PGMaster] - Query snapshot row size completed: PGMaster(2fdf666e-dab5-4fc5-8fe7-10517ca46358) 
[INFO ] 2025-06-27 10:26:01.962 - [PG~Sybase中文字][PGMaster] - Table ccc_big5_20160905 has been completed batch read 
[TRACE] 2025-06-27 10:26:01.962 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[INFO ] 2025-06-27 10:26:01.962 - [PG~Sybase中文字][PGMaster] - Batch read completed. 
[TRACE] 2025-06-27 10:26:01.962 - [PG~Sybase中文字][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-27 10:26:01.962 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[TRACE] 2025-06-27 10:26:01.963 - [PG~Sybase中文字][PGMaster] - Starting stream read, table list: [ccc_big5_20160905], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-27 10:26:01.963 - [PG~Sybase中文字][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-27 10:26:01.968 - [PG~Sybase中文字][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-27 10:26:01.968 - [PG~Sybase中文字][PGMaster] - Using an existing logical replication slot, slotName:tapdata_cdc_966ed08c_ae3c_466a_a8d0_2b7a3e32f5ff 
[WARN ] 2025-06-27 10:26:02.169 - [PG~Sybase中文字][Sybase] - Create index failed Column name '_no_pk_hash' does not exist in target table.
, please execute it manually [create unique index  IDX_bwk_ccc_big5_20160ff11aeff  on  s1.dbo.bwk_ccc_big5_20160905 ( _no_pk_hash  asc)] 
[TRACE] 2025-06-27 10:26:02.370 - [PG~Sybase中文字][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [ccc_big5_20160905], data change syncing 
[TRACE] 2025-06-27 10:26:02.888 - [PG~Sybase中文字][Sybase] - Process after table "bwk_ccc_big5_20160905" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-27 10:26:02.889 - [PG~Sybase中文字][Sybase] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-27 10:26:34.784 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] running status set to false 
[TRACE] 2025-06-27 10:26:35.154 - [PG~Sybase中文字][PGMaster] - Incremental sync completed 
[TRACE] 2025-06-27 10:26:35.155 - [PG~Sybase中文字][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1750991161647 
[TRACE] 2025-06-27 10:26:35.156 - [PG~Sybase中文字][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1750991161647 
[TRACE] 2025-06-27 10:26:35.156 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] schema data cleaned 
[TRACE] 2025-06-27 10:26:35.161 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] monitor closed 
[TRACE] 2025-06-27 10:26:35.161 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] close complete, cost 376 ms 
[TRACE] 2025-06-27 10:26:35.163 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] running status set to false 
[TRACE] 2025-06-27 10:26:35.163 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] schema data cleaned 
[TRACE] 2025-06-27 10:26:35.163 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] monitor closed 
[TRACE] 2025-06-27 10:26:35.164 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] close complete, cost 1 ms 
[TRACE] 2025-06-27 10:26:35.164 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] running status set to false 
[TRACE] 2025-06-27 10:26:35.216 - [PG~Sybase中文字][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_c166b09d-683f-43e8-921b-2d384ed0fc4e_1750991132951 
[TRACE] 2025-06-27 10:26:35.216 - [PG~Sybase中文字][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_c166b09d-683f-43e8-921b-2d384ed0fc4e_1750991132951 
[TRACE] 2025-06-27 10:26:35.216 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] schema data cleaned 
[TRACE] 2025-06-27 10:26:35.216 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] monitor closed 
[TRACE] 2025-06-27 10:26:35.418 - [PG~Sybase中文字][Sybase] - Node Sybase[c166b09d-683f-43e8-921b-2d384ed0fc4e] close complete, cost 52 ms 
[TRACE] 2025-06-27 10:26:41.932 - [PG~Sybase中文字] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-27 10:26:42.775 - [PG~Sybase中文字] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@29af9889 
[TRACE] 2025-06-27 10:26:42.777 - [PG~Sybase中文字] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4e5f72c0 
[TRACE] 2025-06-27 10:26:42.929 - [PG~Sybase中文字] - Stop task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字)  
[TRACE] 2025-06-27 10:26:42.930 - [PG~Sybase中文字] - Stopped task aspect(s) 
[TRACE] 2025-06-27 10:26:42.938 - [PG~Sybase中文字] - Snapshot order controller have been removed 
[INFO ] 2025-06-27 10:26:42.938 - [PG~Sybase中文字] - Task stopped. 
[TRACE] 2025-06-27 10:26:43.005 - [PG~Sybase中文字] - Remove memory task client succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 10:26:43.005 - [PG~Sybase中文字] - Destroy memory task client cache succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 10:29:12.475 - [PG~Sybase中文字] - Task initialization... 
[TRACE] 2025-06-27 10:29:12.605 - [PG~Sybase中文字] - Start task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字) 
[INFO ] 2025-06-27 10:29:12.605 - [PG~Sybase中文字] - Loading table structure completed 
[TRACE] 2025-06-27 10:29:12.689 - [PG~Sybase中文字] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-27 10:29:12.689 - [PG~Sybase中文字] - The engine receives PG~Sybase中文字 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-27 10:29:12.726 - [PG~Sybase中文字] - Task started 
[TRACE] 2025-06-27 10:29:12.726 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] start preload schema,table counts: 1 
[TRACE] 2025-06-27 10:29:12.726 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] start preload schema,table counts: 1 
[TRACE] 2025-06-27 10:29:12.726 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 10:29:12.726 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 10:29:12.726 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] start preload schema,table counts: 1 
[TRACE] 2025-06-27 10:29:12.726 - [PG~Sybase中文字][表编辑] - Node table_rename_processor(表编辑: 1373b647-c5ed-4163-bc67-6d952f713ac1) enable batch process 
[TRACE] 2025-06-27 10:29:12.726 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] preload schema finished, cost 0 ms 
[INFO ] 2025-06-27 10:29:12.927 - [PG~Sybase中文字][PGMaster] - Enable partition table support for source database 
[INFO ] 2025-06-27 10:29:13.356 - [PG~Sybase中文字][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-27 10:29:13.356 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-27 10:29:13.357 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-27 10:29:13.357 - [PG~Sybase中文字][PGMaster] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-27 10:29:19.239 - [PG~Sybase中文字][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-27 10:29:19.251 - [PG~Sybase中文字][PGMaster] - new logical replication slot created, slotName:tapdata_cdc_568c2047_2b3e_406f_b82f_7ca318cc8755 
[INFO ] 2025-06-27 10:31:27.761 - [PG~Sybase中文字][PGMaster] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-27 10:31:27.772 - [PG~Sybase中文字][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-27 10:31:27.780 - [PG~Sybase中文字][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-27 10:31:27.780 - [PG~Sybase中文字][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-27 10:31:27.827 - [PG~Sybase中文字][Sybase] - Apply table structure to target database 
[INFO ] 2025-06-27 10:31:27.830 - [PG~Sybase中文字][PGMaster] - Starting batch read from 1 tables 
[TRACE] 2025-06-27 10:31:27.835 - [PG~Sybase中文字][PGMaster] - Initial sync started 
[INFO ] 2025-06-27 10:31:27.835 - [PG~Sybase中文字][PGMaster] - Starting batch read from table: ccc_big5_20160905 
[TRACE] 2025-06-27 10:31:27.835 - [PG~Sybase中文字][PGMaster] - Table ccc_big5_20160905 is going to be initial synced 
[TRACE] 2025-06-27 10:31:27.838 - [PG~Sybase中文字][PGMaster] - Query snapshot row size completed: PGMaster(2fdf666e-dab5-4fc5-8fe7-10517ca46358) 
[INFO ] 2025-06-27 10:31:27.839 - [PG~Sybase中文字][PGMaster] - Table ccc_big5_20160905 has been completed batch read 
[TRACE] 2025-06-27 10:31:27.839 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[INFO ] 2025-06-27 10:31:27.839 - [PG~Sybase中文字][PGMaster] - Batch read completed. 
[TRACE] 2025-06-27 10:31:27.839 - [PG~Sybase中文字][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-27 10:31:27.840 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[TRACE] 2025-06-27 10:31:27.840 - [PG~Sybase中文字][PGMaster] - Starting stream read, table list: [ccc_big5_20160905], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-27 10:31:27.840 - [PG~Sybase中文字][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-27 10:31:27.842 - [PG~Sybase中文字][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-27 10:31:27.842 - [PG~Sybase中文字][PGMaster] - Using an existing logical replication slot, slotName:tapdata_cdc_568c2047_2b3e_406f_b82f_7ca318cc8755 
[TRACE] 2025-06-27 10:31:28.043 - [PG~Sybase中文字][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [ccc_big5_20160905], data change syncing 
[TRACE] 2025-06-27 10:31:28.968 - [PG~Sybase中文字][Sybase] - Process after table "bwk_ccc_big5_20160905" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-27 10:31:28.968 - [PG~Sybase中文字][Sybase] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-27 10:43:39.666 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] running status set to false 
[TRACE] 2025-06-27 10:43:39.692 - [PG~Sybase中文字][PGMaster] - Incremental sync completed 
[TRACE] 2025-06-27 10:43:39.693 - [PG~Sybase中文字][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1750991353192 
[TRACE] 2025-06-27 10:43:39.693 - [PG~Sybase中文字][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1750991353192 
[TRACE] 2025-06-27 10:43:39.693 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] schema data cleaned 
[TRACE] 2025-06-27 10:43:39.693 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] monitor closed 
[TRACE] 2025-06-27 10:43:39.694 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] close complete, cost 28 ms 
[TRACE] 2025-06-27 10:43:39.694 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] running status set to false 
[TRACE] 2025-06-27 10:43:39.694 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] schema data cleaned 
[TRACE] 2025-06-27 10:43:39.694 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] monitor closed 
[TRACE] 2025-06-27 10:43:39.695 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] close complete, cost 0 ms 
[TRACE] 2025-06-27 10:43:39.695 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] running status set to false 
[TRACE] 2025-06-27 10:43:39.730 - [PG~Sybase中文字][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_09b9c60e-c4e2-4400-8f8e-edef6e119b25_1750991353382 
[TRACE] 2025-06-27 10:43:39.730 - [PG~Sybase中文字][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_09b9c60e-c4e2-4400-8f8e-edef6e119b25_1750991353382 
[TRACE] 2025-06-27 10:43:39.730 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] schema data cleaned 
[TRACE] 2025-06-27 10:43:39.730 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] monitor closed 
[TRACE] 2025-06-27 10:43:39.931 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] close complete, cost 35 ms 
[TRACE] 2025-06-27 10:43:48.520 - [PG~Sybase中文字] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-27 10:43:49.525 - [PG~Sybase中文字] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@582f4052 
[TRACE] 2025-06-27 10:43:49.528 - [PG~Sybase中文字] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@923b55f 
[TRACE] 2025-06-27 10:43:49.528 - [PG~Sybase中文字] - Stop task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字)  
[TRACE] 2025-06-27 10:43:49.650 - [PG~Sybase中文字] - Stopped task aspect(s) 
[TRACE] 2025-06-27 10:43:49.650 - [PG~Sybase中文字] - Snapshot order controller have been removed 
[INFO ] 2025-06-27 10:43:49.678 - [PG~Sybase中文字] - Task stopped. 
[TRACE] 2025-06-27 10:43:49.680 - [PG~Sybase中文字] - Remove memory task client succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 10:43:49.680 - [PG~Sybase中文字] - Destroy memory task client cache succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 14:16:33.585 - [PG~Sybase中文字] - Task initialization... 
[TRACE] 2025-06-27 14:16:33.585 - [PG~Sybase中文字] - Start task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字) 
[INFO ] 2025-06-27 14:16:33.726 - [PG~Sybase中文字] - Loading table structure completed 
[TRACE] 2025-06-27 14:16:33.726 - [PG~Sybase中文字] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-27 14:16:33.763 - [PG~Sybase中文字] - The engine receives PG~Sybase中文字 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-27 14:16:33.763 - [PG~Sybase中文字] - Task started 
[TRACE] 2025-06-27 14:16:33.781 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] start preload schema,table counts: 1 
[TRACE] 2025-06-27 14:16:33.781 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] start preload schema,table counts: 1 
[TRACE] 2025-06-27 14:16:33.781 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 14:16:33.781 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] preload schema finished, cost 0 ms 
[INFO ] 2025-06-27 14:16:33.781 - [PG~Sybase中文字][PGMaster] - Enable partition table support for source database 
[TRACE] 2025-06-27 14:16:33.781 - [PG~Sybase中文字][表编辑] - Node table_rename_processor(表编辑: 1373b647-c5ed-4163-bc67-6d952f713ac1) enable batch process 
[TRACE] 2025-06-27 14:16:33.827 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] start preload schema,table counts: 1 
[TRACE] 2025-06-27 14:16:33.829 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] preload schema finished, cost 0 ms 
[INFO ] 2025-06-27 14:16:34.396 - [PG~Sybase中文字][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-27 14:16:34.396 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-27 14:16:34.396 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-27 14:16:34.396 - [PG~Sybase中文字][PGMaster] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-27 14:16:34.475 - [PG~Sybase中文字][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-27 14:16:34.475 - [PG~Sybase中文字][PGMaster] - new logical replication slot created, slotName:tapdata_cdc_ef2c2347_8136_48a8_ba6c_cd29365a7cd9 
[INFO ] 2025-06-27 14:16:34.488 - [PG~Sybase中文字][PGMaster] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[ERROR] 2025-06-27 14:16:34.489 - [PG~Sybase中文字][Sybase] - Sink connector(Sybase) initialization error: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null <-- Full Stack Trace -->
java.lang.RuntimeException: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:612)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:205)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	... 9 more
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:112)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-06-27 14:16:34.492 - [PG~Sybase中文字][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null 
[ERROR] 2025-06-27 14:16:34.506 - [PG~Sybase中文字][Sybase] - Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null <-- Error Message -->
Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:112)
	io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:612)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:205)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:112)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-06-27 14:16:34.506 - [PG~Sybase中文字][Sybase] - Job suspend in error handle 
[INFO ] 2025-06-27 14:16:34.526 - [PG~Sybase中文字][PGMaster] - Starting batch read from 1 tables 
[TRACE] 2025-06-27 14:16:34.526 - [PG~Sybase中文字][PGMaster] - Initial sync started 
[INFO ] 2025-06-27 14:16:34.526 - [PG~Sybase中文字][PGMaster] - Starting batch read from table: ccc_big5_20160905 
[TRACE] 2025-06-27 14:16:34.526 - [PG~Sybase中文字][PGMaster] - Table ccc_big5_20160905 is going to be initial synced 
[TRACE] 2025-06-27 14:16:34.532 - [PG~Sybase中文字][PGMaster] - Query snapshot row size completed: PGMaster(2fdf666e-dab5-4fc5-8fe7-10517ca46358) 
[TRACE] 2025-06-27 14:16:34.532 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[INFO ] 2025-06-27 14:16:34.532 - [PG~Sybase中文字][PGMaster] - Batch read completed. 
[TRACE] 2025-06-27 14:16:34.532 - [PG~Sybase中文字][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-27 14:16:34.532 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[TRACE] 2025-06-27 14:16:34.532 - [PG~Sybase中文字][PGMaster] - Starting stream read, table list: [ccc_big5_20160905], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-27 14:16:34.532 - [PG~Sybase中文字][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-27 14:16:34.534 - [PG~Sybase中文字][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-27 14:16:34.534 - [PG~Sybase中文字][PGMaster] - Using an existing logical replication slot, slotName:tapdata_cdc_ef2c2347_8136_48a8_ba6c_cd29365a7cd9 
[TRACE] 2025-06-27 14:16:34.627 - [PG~Sybase中文字][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [ccc_big5_20160905], data change syncing 
[TRACE] 2025-06-27 14:16:34.627 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] running status set to false 
[TRACE] 2025-06-27 14:16:34.664 - [PG~Sybase中文字][PGMaster] - Incremental sync completed 
[TRACE] 2025-06-27 14:16:34.665 - [PG~Sybase中文字][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1751004994237 
[TRACE] 2025-06-27 14:16:34.665 - [PG~Sybase中文字][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1751004994237 
[TRACE] 2025-06-27 14:16:34.665 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] schema data cleaned 
[TRACE] 2025-06-27 14:16:34.665 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] monitor closed 
[TRACE] 2025-06-27 14:16:34.665 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] close complete, cost 38 ms 
[TRACE] 2025-06-27 14:16:34.665 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] running status set to false 
[TRACE] 2025-06-27 14:16:34.665 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] schema data cleaned 
[TRACE] 2025-06-27 14:16:34.665 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] monitor closed 
[TRACE] 2025-06-27 14:16:34.665 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] close complete, cost 0 ms 
[TRACE] 2025-06-27 14:16:34.666 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] running status set to false 
[TRACE] 2025-06-27 14:16:34.682 - [PG~Sybase中文字][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_09b9c60e-c4e2-4400-8f8e-edef6e119b25_1751004994418 
[TRACE] 2025-06-27 14:16:34.683 - [PG~Sybase中文字][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_09b9c60e-c4e2-4400-8f8e-edef6e119b25_1751004994418 
[TRACE] 2025-06-27 14:16:34.683 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] schema data cleaned 
[TRACE] 2025-06-27 14:16:34.683 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] monitor closed 
[TRACE] 2025-06-27 14:16:34.683 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] close complete, cost 17 ms 
[INFO ] 2025-06-27 14:16:34.884 - [PG~Sybase中文字] - Task [PG~Sybase中文字] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-06-27 14:16:39.778 - [PG~Sybase中文字] - Task [PG~Sybase中文字] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-06-27 14:16:39.778 - [PG~Sybase中文字] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-27 14:16:40.783 - [PG~Sybase中文字] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@71134f6f 
[TRACE] 2025-06-27 14:16:40.784 - [PG~Sybase中文字] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4ec704a2 
[TRACE] 2025-06-27 14:16:40.928 - [PG~Sybase中文字] - Stop task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字)  
[TRACE] 2025-06-27 14:16:40.929 - [PG~Sybase中文字] - Stopped task aspect(s) 
[TRACE] 2025-06-27 14:16:40.929 - [PG~Sybase中文字] - Snapshot order controller have been removed 
[INFO ] 2025-06-27 14:16:40.929 - [PG~Sybase中文字] - Task stopped. 
[TRACE] 2025-06-27 14:16:40.968 - [PG~Sybase中文字] - Remove memory task client succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 14:16:40.968 - [PG~Sybase中文字] - Destroy memory task client cache succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 14:17:44.553 - [PG~Sybase中文字] - Task initialization... 
[TRACE] 2025-06-27 14:17:44.650 - [PG~Sybase中文字] - Start task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字) 
[INFO ] 2025-06-27 14:17:44.650 - [PG~Sybase中文字] - Loading table structure completed 
[TRACE] 2025-06-27 14:17:44.729 - [PG~Sybase中文字] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-27 14:17:44.729 - [PG~Sybase中文字] - The engine receives PG~Sybase中文字 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-27 14:17:44.753 - [PG~Sybase中文字] - Task started 
[TRACE] 2025-06-27 14:17:44.753 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] start preload schema,table counts: 1 
[TRACE] 2025-06-27 14:17:44.753 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] start preload schema,table counts: 1 
[TRACE] 2025-06-27 14:17:44.753 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 14:17:44.754 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] start preload schema,table counts: 1 
[TRACE] 2025-06-27 14:17:44.754 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] preload schema finished, cost 0 ms 
[INFO ] 2025-06-27 14:17:44.754 - [PG~Sybase中文字][PGMaster] - Enable partition table support for source database 
[TRACE] 2025-06-27 14:17:44.754 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 14:17:44.959 - [PG~Sybase中文字][表编辑] - Node table_rename_processor(表编辑: 1373b647-c5ed-4163-bc67-6d952f713ac1) enable batch process 
[INFO ] 2025-06-27 14:17:45.369 - [PG~Sybase中文字][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-27 14:17:45.369 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-27 14:17:45.369 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-27 14:17:45.369 - [PG~Sybase中文字][PGMaster] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-27 14:17:45.431 - [PG~Sybase中文字][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-27 14:17:45.431 - [PG~Sybase中文字][PGMaster] - new logical replication slot created, slotName:tapdata_cdc_7cd8fb95_e637_48aa_bfb5_88f7752a78b2 
[INFO ] 2025-06-27 14:17:54.041 - [PG~Sybase中文字][PGMaster] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-27 14:18:31.973 - [PG~Sybase中文字][PGMaster] - Starting batch read from 1 tables 
[ERROR] 2025-06-27 14:18:31.979 - [PG~Sybase中文字][Sybase] - Sink connector(Sybase) initialization error: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null <-- Full Stack Trace -->
java.lang.RuntimeException: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:612)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:205)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	... 9 more
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:112)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-06-27 14:18:31.987 - [PG~Sybase中文字][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null 
[TRACE] 2025-06-27 14:18:31.988 - [PG~Sybase中文字][PGMaster] - Initial sync started 
[INFO ] 2025-06-27 14:18:31.988 - [PG~Sybase中文字][PGMaster] - Starting batch read from table: ccc_big5_20160905 
[TRACE] 2025-06-27 14:18:31.988 - [PG~Sybase中文字][PGMaster] - Table ccc_big5_20160905 is going to be initial synced 
[ERROR] 2025-06-27 14:18:31.989 - [PG~Sybase中文字][Sybase] - Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null <-- Error Message -->
Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:112)
	io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:612)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:205)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:112)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-06-27 14:18:31.992 - [PG~Sybase中文字][Sybase] - Job suspend in error handle 
[TRACE] 2025-06-27 14:18:31.992 - [PG~Sybase中文字][PGMaster] - Query snapshot row size completed: PGMaster(2fdf666e-dab5-4fc5-8fe7-10517ca46358) 
[TRACE] 2025-06-27 14:18:31.992 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[INFO ] 2025-06-27 14:18:31.993 - [PG~Sybase中文字][PGMaster] - Batch read completed. 
[TRACE] 2025-06-27 14:18:31.993 - [PG~Sybase中文字][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-27 14:18:31.993 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[TRACE] 2025-06-27 14:18:31.993 - [PG~Sybase中文字][PGMaster] - Starting stream read, table list: [ccc_big5_20160905], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-27 14:18:31.994 - [PG~Sybase中文字][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-27 14:18:31.994 - [PG~Sybase中文字][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-27 14:18:32.012 - [PG~Sybase中文字][PGMaster] - Using an existing logical replication slot, slotName:tapdata_cdc_7cd8fb95_e637_48aa_bfb5_88f7752a78b2 
[TRACE] 2025-06-27 14:18:32.073 - [PG~Sybase中文字][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [ccc_big5_20160905], data change syncing 
[TRACE] 2025-06-27 14:18:32.073 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] running status set to false 
[TRACE] 2025-06-27 14:18:32.133 - [PG~Sybase中文字][PGMaster] - Incremental sync completed 
[TRACE] 2025-06-27 14:18:32.133 - [PG~Sybase中文字][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1751005065213 
[TRACE] 2025-06-27 14:18:32.133 - [PG~Sybase中文字][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1751005065213 
[TRACE] 2025-06-27 14:18:32.133 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] schema data cleaned 
[TRACE] 2025-06-27 14:18:32.133 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] monitor closed 
[TRACE] 2025-06-27 14:18:32.134 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] close complete, cost 60 ms 
[TRACE] 2025-06-27 14:18:32.134 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] running status set to false 
[TRACE] 2025-06-27 14:18:32.134 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] schema data cleaned 
[TRACE] 2025-06-27 14:18:32.134 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] monitor closed 
[TRACE] 2025-06-27 14:18:32.134 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] close complete, cost 0 ms 
[TRACE] 2025-06-27 14:18:32.134 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] running status set to false 
[TRACE] 2025-06-27 14:18:32.152 - [PG~Sybase中文字][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_09b9c60e-c4e2-4400-8f8e-edef6e119b25_1751005065392 
[TRACE] 2025-06-27 14:18:32.152 - [PG~Sybase中文字][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_09b9c60e-c4e2-4400-8f8e-edef6e119b25_1751005065392 
[TRACE] 2025-06-27 14:18:32.152 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] schema data cleaned 
[TRACE] 2025-06-27 14:18:32.152 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] monitor closed 
[TRACE] 2025-06-27 14:18:32.355 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] close complete, cost 17 ms 
[INFO ] 2025-06-27 14:18:37.142 - [PG~Sybase中文字] - Task [PG~Sybase中文字] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-06-27 14:18:41.989 - [PG~Sybase中文字] - Task [PG~Sybase中文字] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-06-27 14:18:42.194 - [PG~Sybase中文字] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-27 14:18:42.995 - [PG~Sybase中文字] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@73528b09 
[TRACE] 2025-06-27 14:18:42.998 - [PG~Sybase中文字] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@b00c549 
[TRACE] 2025-06-27 14:18:42.998 - [PG~Sybase中文字] - Stop task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字)  
[TRACE] 2025-06-27 14:18:43.112 - [PG~Sybase中文字] - Stopped task aspect(s) 
[TRACE] 2025-06-27 14:18:43.112 - [PG~Sybase中文字] - Snapshot order controller have been removed 
[INFO ] 2025-06-27 14:18:43.112 - [PG~Sybase中文字] - Task stopped. 
[TRACE] 2025-06-27 14:18:43.130 - [PG~Sybase中文字] - Remove memory task client succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 14:18:43.132 - [PG~Sybase中文字] - Destroy memory task client cache succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 14:25:57.339 - [PG~Sybase中文字] - Task initialization... 
[TRACE] 2025-06-27 14:25:57.341 - [PG~Sybase中文字] - Start task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字) 
[INFO ] 2025-06-27 14:25:57.543 - [PG~Sybase中文字] - Loading table structure completed 
[TRACE] 2025-06-27 14:25:57.663 - [PG~Sybase中文字] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-27 14:25:57.666 - [PG~Sybase中文字] - The engine receives PG~Sybase中文字 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-27 14:25:57.690 - [PG~Sybase中文字] - Task started 
[TRACE] 2025-06-27 14:25:57.690 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] start preload schema,table counts: 1 
[TRACE] 2025-06-27 14:25:57.690 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] start preload schema,table counts: 1 
[TRACE] 2025-06-27 14:25:57.690 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 14:25:57.690 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 14:25:57.690 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] start preload schema,table counts: 1 
[TRACE] 2025-06-27 14:25:57.690 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] preload schema finished, cost 0 ms 
[INFO ] 2025-06-27 14:25:57.691 - [PG~Sybase中文字][PGMaster] - Enable partition table support for source database 
[TRACE] 2025-06-27 14:25:57.891 - [PG~Sybase中文字][表编辑] - Node table_rename_processor(表编辑: 1373b647-c5ed-4163-bc67-6d952f713ac1) enable batch process 
[ERROR] 2025-06-27 14:25:58.400 - [PG~Sybase中文字][Sybase] - Sink connector(Sybase) initialization error: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null <-- Full Stack Trace -->
java.lang.RuntimeException: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:612)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:205)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	... 9 more
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:113)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-06-27 14:25:58.400 - [PG~Sybase中文字][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null 
[ERROR] 2025-06-27 14:25:58.412 - [PG~Sybase中文字][Sybase] - Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null <-- Error Message -->
Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:113)
	io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:612)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:205)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:113)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-06-27 14:25:58.415 - [PG~Sybase中文字][Sybase] - Job suspend in error handle 
[INFO ] 2025-06-27 14:25:58.573 - [PG~Sybase中文字][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-27 14:25:58.573 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-27 14:25:58.573 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-27 14:25:58.573 - [PG~Sybase中文字][PGMaster] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-27 14:25:58.648 - [PG~Sybase中文字][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-27 14:25:58.656 - [PG~Sybase中文字][PGMaster] - new logical replication slot created, slotName:tapdata_cdc_bbc7cac9_1c35_425e_a024_53e99aa77978 
[INFO ] 2025-06-27 14:25:58.726 - [PG~Sybase中文字][PGMaster] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-27 14:25:58.726 - [PG~Sybase中文字][PGMaster] - Starting batch read from 1 tables 
[TRACE] 2025-06-27 14:25:58.733 - [PG~Sybase中文字][PGMaster] - Initial sync started 
[INFO ] 2025-06-27 14:25:58.733 - [PG~Sybase中文字][PGMaster] - Starting batch read from table: ccc_big5_20160905 
[TRACE] 2025-06-27 14:25:58.733 - [PG~Sybase中文字][PGMaster] - Table ccc_big5_20160905 is going to be initial synced 
[TRACE] 2025-06-27 14:25:58.740 - [PG~Sybase中文字][PGMaster] - Query snapshot row size completed: PGMaster(2fdf666e-dab5-4fc5-8fe7-10517ca46358) 
[TRACE] 2025-06-27 14:25:58.740 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[INFO ] 2025-06-27 14:25:58.741 - [PG~Sybase中文字][PGMaster] - Batch read completed. 
[TRACE] 2025-06-27 14:25:58.741 - [PG~Sybase中文字][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-27 14:25:58.741 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[TRACE] 2025-06-27 14:25:58.741 - [PG~Sybase中文字][PGMaster] - Starting stream read, table list: [ccc_big5_20160905], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-27 14:25:58.742 - [PG~Sybase中文字][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-27 14:25:58.744 - [PG~Sybase中文字][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-27 14:25:58.744 - [PG~Sybase中文字][PGMaster] - Using an existing logical replication slot, slotName:tapdata_cdc_bbc7cac9_1c35_425e_a024_53e99aa77978 
[TRACE] 2025-06-27 14:25:58.830 - [PG~Sybase中文字][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [ccc_big5_20160905], data change syncing 
[TRACE] 2025-06-27 14:25:58.830 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] running status set to false 
[TRACE] 2025-06-27 14:25:58.871 - [PG~Sybase中文字][PGMaster] - Incremental sync completed 
[TRACE] 2025-06-27 14:25:58.871 - [PG~Sybase中文字][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1751005558422 
[TRACE] 2025-06-27 14:25:58.871 - [PG~Sybase中文字][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1751005558422 
[TRACE] 2025-06-27 14:25:58.871 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] schema data cleaned 
[TRACE] 2025-06-27 14:25:58.871 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] monitor closed 
[TRACE] 2025-06-27 14:25:58.871 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] close complete, cost 41 ms 
[TRACE] 2025-06-27 14:25:58.871 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] running status set to false 
[TRACE] 2025-06-27 14:25:58.872 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] schema data cleaned 
[TRACE] 2025-06-27 14:25:58.872 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] monitor closed 
[TRACE] 2025-06-27 14:25:58.872 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] close complete, cost 0 ms 
[TRACE] 2025-06-27 14:25:58.872 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] running status set to false 
[TRACE] 2025-06-27 14:25:58.891 - [PG~Sybase中文字][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_09b9c60e-c4e2-4400-8f8e-edef6e119b25_1751005558319 
[TRACE] 2025-06-27 14:25:58.891 - [PG~Sybase中文字][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_09b9c60e-c4e2-4400-8f8e-edef6e119b25_1751005558319 
[TRACE] 2025-06-27 14:25:58.891 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] schema data cleaned 
[TRACE] 2025-06-27 14:25:58.891 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] monitor closed 
[TRACE] 2025-06-27 14:25:59.094 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] close complete, cost 19 ms 
[INFO ] 2025-06-27 14:25:59.705 - [PG~Sybase中文字] - Task [PG~Sybase中文字] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-06-27 14:26:04.661 - [PG~Sybase中文字] - Task [PG~Sybase中文字] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-06-27 14:26:04.771 - [PG~Sybase中文字] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-27 14:26:05.669 - [PG~Sybase中文字] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@4a863add 
[TRACE] 2025-06-27 14:26:05.673 - [PG~Sybase中文字] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1b987eba 
[TRACE] 2025-06-27 14:26:05.674 - [PG~Sybase中文字] - Stop task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字)  
[TRACE] 2025-06-27 14:26:05.807 - [PG~Sybase中文字] - Stopped task aspect(s) 
[TRACE] 2025-06-27 14:26:05.807 - [PG~Sybase中文字] - Snapshot order controller have been removed 
[INFO ] 2025-06-27 14:26:05.807 - [PG~Sybase中文字] - Task stopped. 
[TRACE] 2025-06-27 14:26:05.828 - [PG~Sybase中文字] - Remove memory task client succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 14:26:05.829 - [PG~Sybase中文字] - Destroy memory task client cache succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 14:26:21.537 - [PG~Sybase中文字] - Task initialization... 
[TRACE] 2025-06-27 14:26:21.641 - [PG~Sybase中文字] - Start task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字) 
[INFO ] 2025-06-27 14:26:21.641 - [PG~Sybase中文字] - Loading table structure completed 
[TRACE] 2025-06-27 14:26:21.819 - [PG~Sybase中文字] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-27 14:26:21.819 - [PG~Sybase中文字] - The engine receives PG~Sybase中文字 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-27 14:26:21.849 - [PG~Sybase中文字] - Task started 
[TRACE] 2025-06-27 14:26:21.849 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] start preload schema,table counts: 1 
[TRACE] 2025-06-27 14:26:21.849 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] start preload schema,table counts: 1 
[TRACE] 2025-06-27 14:26:21.849 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 14:26:21.849 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 14:26:21.849 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] start preload schema,table counts: 1 
[INFO ] 2025-06-27 14:26:21.849 - [PG~Sybase中文字][PGMaster] - Enable partition table support for source database 
[TRACE] 2025-06-27 14:26:21.849 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 14:26:22.050 - [PG~Sybase中文字][表编辑] - Node table_rename_processor(表编辑: 1373b647-c5ed-4163-bc67-6d952f713ac1) enable batch process 
[ERROR] 2025-06-27 14:27:32.692 - [PG~Sybase中文字][Sybase] - Sink connector(Sybase) initialization error: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null <-- Full Stack Trace -->
java.lang.RuntimeException: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:612)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:205)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	... 9 more
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:113)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-06-27 14:27:32.697 - [PG~Sybase中文字][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null 
[ERROR] 2025-06-27 14:27:32.754 - [PG~Sybase中文字][Sybase] - Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null <-- Error Message -->
Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:113)
	io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:612)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:205)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:113)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-06-27 14:27:32.754 - [PG~Sybase中文字][Sybase] - Job suspend in error handle 
[INFO ] 2025-06-27 14:27:32.960 - [PG~Sybase中文字][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-27 14:27:32.960 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-27 14:27:32.960 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-27 14:27:32.960 - [PG~Sybase中文字][PGMaster] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-27 14:27:33.031 - [PG~Sybase中文字][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-27 14:27:33.031 - [PG~Sybase中文字][PGMaster] - new logical replication slot created, slotName:tapdata_cdc_451cbb80_aabe_40bc_9fc3_5b63d93dabb6 
[INFO ] 2025-06-27 14:27:33.183 - [PG~Sybase中文字][PGMaster] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-27 14:27:33.184 - [PG~Sybase中文字][PGMaster] - Starting batch read from 1 tables 
[TRACE] 2025-06-27 14:27:33.189 - [PG~Sybase中文字][PGMaster] - Initial sync started 
[INFO ] 2025-06-27 14:27:33.189 - [PG~Sybase中文字][PGMaster] - Starting batch read from table: ccc_big5_20160905 
[TRACE] 2025-06-27 14:27:33.189 - [PG~Sybase中文字][PGMaster] - Table ccc_big5_20160905 is going to be initial synced 
[TRACE] 2025-06-27 14:27:33.192 - [PG~Sybase中文字][PGMaster] - Query snapshot row size completed: PGMaster(2fdf666e-dab5-4fc5-8fe7-10517ca46358) 
[TRACE] 2025-06-27 14:27:33.192 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[INFO ] 2025-06-27 14:27:33.192 - [PG~Sybase中文字][PGMaster] - Batch read completed. 
[TRACE] 2025-06-27 14:27:33.192 - [PG~Sybase中文字][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-27 14:27:33.192 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[TRACE] 2025-06-27 14:27:33.192 - [PG~Sybase中文字][PGMaster] - Starting stream read, table list: [ccc_big5_20160905], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-27 14:27:33.192 - [PG~Sybase中文字][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-27 14:27:33.195 - [PG~Sybase中文字][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-27 14:27:33.195 - [PG~Sybase中文字][PGMaster] - Using an existing logical replication slot, slotName:tapdata_cdc_451cbb80_aabe_40bc_9fc3_5b63d93dabb6 
[TRACE] 2025-06-27 14:27:33.285 - [PG~Sybase中文字][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [ccc_big5_20160905], data change syncing 
[TRACE] 2025-06-27 14:27:33.285 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] running status set to false 
[TRACE] 2025-06-27 14:27:33.320 - [PG~Sybase中文字][PGMaster] - Incremental sync completed 
[TRACE] 2025-06-27 14:27:33.320 - [PG~Sybase中文字][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1751005652772 
[TRACE] 2025-06-27 14:27:33.320 - [PG~Sybase中文字][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1751005652772 
[TRACE] 2025-06-27 14:27:33.320 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] schema data cleaned 
[TRACE] 2025-06-27 14:27:33.320 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] monitor closed 
[TRACE] 2025-06-27 14:27:33.321 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] close complete, cost 35 ms 
[TRACE] 2025-06-27 14:27:33.321 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] running status set to false 
[TRACE] 2025-06-27 14:27:33.321 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] schema data cleaned 
[TRACE] 2025-06-27 14:27:33.322 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] monitor closed 
[TRACE] 2025-06-27 14:27:33.322 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] close complete, cost 0 ms 
[TRACE] 2025-06-27 14:27:33.322 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] running status set to false 
[TRACE] 2025-06-27 14:27:33.340 - [PG~Sybase中文字][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_09b9c60e-c4e2-4400-8f8e-edef6e119b25_1751005582416 
[TRACE] 2025-06-27 14:27:33.340 - [PG~Sybase中文字][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_09b9c60e-c4e2-4400-8f8e-edef6e119b25_1751005582416 
[TRACE] 2025-06-27 14:27:33.340 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] schema data cleaned 
[TRACE] 2025-06-27 14:27:33.340 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] monitor closed 
[TRACE] 2025-06-27 14:27:33.340 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] close complete, cost 18 ms 
[INFO ] 2025-06-27 14:27:37.807 - [PG~Sybase中文字] - Task [PG~Sybase中文字] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-06-27 14:27:42.696 - [PG~Sybase中文字] - Task [PG~Sybase中文字] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-06-27 14:27:42.696 - [PG~Sybase中文字] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-27 14:27:43.704 - [PG~Sybase中文字] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@634e7e0d 
[TRACE] 2025-06-27 14:27:43.705 - [PG~Sybase中文字] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@717a0787 
[TRACE] 2025-06-27 14:27:43.839 - [PG~Sybase中文字] - Stop task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字)  
[TRACE] 2025-06-27 14:27:43.840 - [PG~Sybase中文字] - Stopped task aspect(s) 
[TRACE] 2025-06-27 14:27:43.840 - [PG~Sybase中文字] - Snapshot order controller have been removed 
[INFO ] 2025-06-27 14:27:43.840 - [PG~Sybase中文字] - Task stopped. 
[TRACE] 2025-06-27 14:27:43.870 - [PG~Sybase中文字] - Remove memory task client succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 14:27:43.875 - [PG~Sybase中文字] - Destroy memory task client cache succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 14:29:50.672 - [PG~Sybase中文字] - Task initialization... 
[TRACE] 2025-06-27 14:29:50.672 - [PG~Sybase中文字] - Start task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字) 
[INFO ] 2025-06-27 14:29:50.809 - [PG~Sybase中文字] - Loading table structure completed 
[TRACE] 2025-06-27 14:29:50.809 - [PG~Sybase中文字] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-27 14:29:50.852 - [PG~Sybase中文字] - The engine receives PG~Sybase中文字 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-27 14:29:50.852 - [PG~Sybase中文字] - Task started 
[TRACE] 2025-06-27 14:29:50.877 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] start preload schema,table counts: 1 
[TRACE] 2025-06-27 14:29:50.877 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 14:29:50.877 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] start preload schema,table counts: 1 
[TRACE] 2025-06-27 14:29:50.877 - [PG~Sybase中文字][表编辑] - Node table_rename_processor(表编辑: 1373b647-c5ed-4163-bc67-6d952f713ac1) enable batch process 
[TRACE] 2025-06-27 14:29:50.877 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 14:29:50.878 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] start preload schema,table counts: 1 
[TRACE] 2025-06-27 14:29:50.878 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] preload schema finished, cost 0 ms 
[INFO ] 2025-06-27 14:29:50.878 - [PG~Sybase中文字][PGMaster] - Enable partition table support for source database 
[ERROR] 2025-06-27 14:29:51.608 - [PG~Sybase中文字][Sybase] - Sink connector(Sybase) initialization error: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null <-- Full Stack Trace -->
java.lang.RuntimeException: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:612)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:205)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	... 9 more
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:120)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-06-27 14:29:51.611 - [PG~Sybase中文字][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null 
[ERROR] 2025-06-27 14:29:51.612 - [PG~Sybase中文字][Sybase] - Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null <-- Error Message -->
Unknown PDK exception occur, java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:120)
	io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:612)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:205)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Boolean.booleanValue()" because the return value of "io.tapdata.sybase.extend.SybaseConfig.getAutoEncode()" is null
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:120)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-06-27 14:29:51.616 - [PG~Sybase中文字][Sybase] - Job suspend in error handle 
[INFO ] 2025-06-27 14:29:51.741 - [PG~Sybase中文字][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-27 14:29:51.741 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-27 14:29:51.741 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-27 14:29:51.741 - [PG~Sybase中文字][PGMaster] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-27 14:29:51.811 - [PG~Sybase中文字][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-27 14:29:51.812 - [PG~Sybase中文字][PGMaster] - new logical replication slot created, slotName:tapdata_cdc_9875c98b_4c07_4394_823b_fbd1b0cb2ed3 
[INFO ] 2025-06-27 14:29:51.883 - [PG~Sybase中文字][PGMaster] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-27 14:29:51.883 - [PG~Sybase中文字][PGMaster] - Starting batch read from 1 tables 
[TRACE] 2025-06-27 14:29:51.901 - [PG~Sybase中文字][PGMaster] - Initial sync started 
[INFO ] 2025-06-27 14:29:51.901 - [PG~Sybase中文字][PGMaster] - Starting batch read from table: ccc_big5_20160905 
[TRACE] 2025-06-27 14:29:51.901 - [PG~Sybase中文字][PGMaster] - Table ccc_big5_20160905 is going to be initial synced 
[TRACE] 2025-06-27 14:29:51.905 - [PG~Sybase中文字][PGMaster] - Query snapshot row size completed: PGMaster(2fdf666e-dab5-4fc5-8fe7-10517ca46358) 
[TRACE] 2025-06-27 14:29:51.906 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[INFO ] 2025-06-27 14:29:51.906 - [PG~Sybase中文字][PGMaster] - Batch read completed. 
[TRACE] 2025-06-27 14:29:51.906 - [PG~Sybase中文字][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-27 14:29:51.906 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[TRACE] 2025-06-27 14:29:51.906 - [PG~Sybase中文字][PGMaster] - Starting stream read, table list: [ccc_big5_20160905], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-27 14:29:51.906 - [PG~Sybase中文字][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-27 14:29:51.909 - [PG~Sybase中文字][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-27 14:29:51.909 - [PG~Sybase中文字][PGMaster] - Using an existing logical replication slot, slotName:tapdata_cdc_9875c98b_4c07_4394_823b_fbd1b0cb2ed3 
[TRACE] 2025-06-27 14:29:51.988 - [PG~Sybase中文字][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [ccc_big5_20160905], data change syncing 
[TRACE] 2025-06-27 14:29:51.988 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] running status set to false 
[TRACE] 2025-06-27 14:29:52.055 - [PG~Sybase中文字][PGMaster] - Incremental sync completed 
[TRACE] 2025-06-27 14:29:52.055 - [PG~Sybase中文字][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1751005791588 
[TRACE] 2025-06-27 14:29:52.055 - [PG~Sybase中文字][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1751005791588 
[TRACE] 2025-06-27 14:29:52.055 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] schema data cleaned 
[TRACE] 2025-06-27 14:29:52.056 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] monitor closed 
[TRACE] 2025-06-27 14:29:52.056 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] close complete, cost 68 ms 
[TRACE] 2025-06-27 14:29:52.056 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] running status set to false 
[TRACE] 2025-06-27 14:29:52.056 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] schema data cleaned 
[TRACE] 2025-06-27 14:29:52.056 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] monitor closed 
[TRACE] 2025-06-27 14:29:52.056 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] close complete, cost 0 ms 
[TRACE] 2025-06-27 14:29:52.056 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] running status set to false 
[TRACE] 2025-06-27 14:29:52.080 - [PG~Sybase中文字][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_09b9c60e-c4e2-4400-8f8e-edef6e119b25_1751005791541 
[TRACE] 2025-06-27 14:29:52.080 - [PG~Sybase中文字][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_09b9c60e-c4e2-4400-8f8e-edef6e119b25_1751005791541 
[TRACE] 2025-06-27 14:29:52.080 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] schema data cleaned 
[TRACE] 2025-06-27 14:29:52.080 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] monitor closed 
[TRACE] 2025-06-27 14:29:52.080 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] close complete, cost 24 ms 
[INFO ] 2025-06-27 14:29:55.191 - [PG~Sybase中文字] - Task [PG~Sybase中文字] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2025-06-27 14:30:00.201 - [PG~Sybase中文字] - Task [PG~Sybase中文字] cannot retry, reason: Max retry duration set to 0 
[TRACE] 2025-06-27 14:30:00.202 - [PG~Sybase中文字] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-27 14:30:01.211 - [PG~Sybase中文字] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@670b2cdf 
[TRACE] 2025-06-27 14:30:01.213 - [PG~Sybase中文字] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@36b8e516 
[TRACE] 2025-06-27 14:30:01.213 - [PG~Sybase中文字] - Stop task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字)  
[TRACE] 2025-06-27 14:30:01.359 - [PG~Sybase中文字] - Stopped task aspect(s) 
[TRACE] 2025-06-27 14:30:01.359 - [PG~Sybase中文字] - Snapshot order controller have been removed 
[INFO ] 2025-06-27 14:30:01.359 - [PG~Sybase中文字] - Task stopped. 
[TRACE] 2025-06-27 14:30:01.385 - [PG~Sybase中文字] - Remove memory task client succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 14:30:01.387 - [PG~Sybase中文字] - Destroy memory task client cache succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 14:32:28.956 - [PG~Sybase中文字] - Task initialization... 
[TRACE] 2025-06-27 14:32:28.959 - [PG~Sybase中文字] - Start task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字) 
[INFO ] 2025-06-27 14:32:29.125 - [PG~Sybase中文字] - Loading table structure completed 
[TRACE] 2025-06-27 14:32:29.125 - [PG~Sybase中文字] - Node performs snapshot read asynchronously 
[TRACE] 2025-06-27 14:32:29.167 - [PG~Sybase中文字] - The engine receives PG~Sybase中文字 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-06-27 14:32:29.167 - [PG~Sybase中文字] - Task started 
[TRACE] 2025-06-27 14:32:29.191 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] start preload schema,table counts: 1 
[TRACE] 2025-06-27 14:32:29.191 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 14:32:29.191 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] start preload schema,table counts: 1 
[INFO ] 2025-06-27 14:32:29.191 - [PG~Sybase中文字][PGMaster] - Enable partition table support for source database 
[TRACE] 2025-06-27 14:32:29.191 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] start preload schema,table counts: 1 
[TRACE] 2025-06-27 14:32:29.191 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 14:32:29.191 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] preload schema finished, cost 0 ms 
[TRACE] 2025-06-27 14:32:29.392 - [PG~Sybase中文字][表编辑] - Node table_rename_processor(表编辑: 1373b647-c5ed-4163-bc67-6d952f713ac1) enable batch process 
[INFO ] 2025-06-27 14:32:29.921 - [PG~Sybase中文字][PGMaster] - Source connector(PGMaster) initialization completed 
[TRACE] 2025-06-27 14:32:29.921 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" read batch size: 100 
[TRACE] 2025-06-27 14:32:29.921 - [PG~Sybase中文字][PGMaster] - Source node "PGMaster" event queue capacity: 200 
[TRACE] 2025-06-27 14:32:29.921 - [PG~Sybase中文字][PGMaster] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-06-27 14:32:29.994 - [PG~Sybase中文字][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-27 14:32:30.010 - [PG~Sybase中文字][PGMaster] - new logical replication slot created, slotName:tapdata_cdc_7623801c_ce10_4b39_bba4_41ebaffb5e16 
[INFO ] 2025-06-27 14:32:30.013 - [PG~Sybase中文字][PGMaster] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-27 14:32:30.037 - [PG~Sybase中文字][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-06-27 14:32:30.040 - [PG~Sybase中文字][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-06-27 14:32:30.040 - [PG~Sybase中文字][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-06-27 14:32:30.102 - [PG~Sybase中文字][Sybase] - Apply table structure to target database 
[INFO ] 2025-06-27 14:32:30.102 - [PG~Sybase中文字][PGMaster] - Starting batch read from 1 tables 
[TRACE] 2025-06-27 14:32:30.125 - [PG~Sybase中文字][PGMaster] - Initial sync started 
[INFO ] 2025-06-27 14:32:30.126 - [PG~Sybase中文字][PGMaster] - Starting batch read from table: ccc_big5_20160905 
[TRACE] 2025-06-27 14:32:30.126 - [PG~Sybase中文字][PGMaster] - Table ccc_big5_20160905 is going to be initial synced 
[TRACE] 2025-06-27 14:32:30.128 - [PG~Sybase中文字][PGMaster] - Query snapshot row size completed: PGMaster(2fdf666e-dab5-4fc5-8fe7-10517ca46358) 
[INFO ] 2025-06-27 14:32:30.128 - [PG~Sybase中文字][PGMaster] - Table ccc_big5_20160905 has been completed batch read 
[TRACE] 2025-06-27 14:32:30.128 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[INFO ] 2025-06-27 14:32:30.128 - [PG~Sybase中文字][PGMaster] - Batch read completed. 
[TRACE] 2025-06-27 14:32:30.128 - [PG~Sybase中文字][PGMaster] - Incremental sync starting... 
[TRACE] 2025-06-27 14:32:30.128 - [PG~Sybase中文字][PGMaster] - Initial sync completed 
[TRACE] 2025-06-27 14:32:30.129 - [PG~Sybase中文字][PGMaster] - Starting stream read, table list: [ccc_big5_20160905], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-06-27 14:32:30.130 - [PG~Sybase中文字][PGMaster] - Starting incremental sync using database log parser 
[WARN ] 2025-06-27 14:32:30.130 - [PG~Sybase中文字][PGMaster] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-06-27 14:32:30.193 - [PG~Sybase中文字][PGMaster] - Using an existing logical replication slot, slotName:tapdata_cdc_7623801c_ce10_4b39_bba4_41ebaffb5e16 
[TRACE] 2025-06-27 14:32:30.194 - [PG~Sybase中文字][PGMaster] - Connector PostgreSQL incremental start succeed, tables: [ccc_big5_20160905], data change syncing 
[TRACE] 2025-06-27 14:32:31.827 - [PG~Sybase中文字][Sybase] - Process after table "bwk_ccc_big5_20160905" initial sync finished, cost: 0 ms 
[INFO ] 2025-06-27 14:32:31.827 - [PG~Sybase中文字][Sybase] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-06-27 14:52:06.510 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] running status set to false 
[TRACE] 2025-06-27 14:52:06.591 - [PG~Sybase中文字][PGMaster] - Incremental sync completed 
[TRACE] 2025-06-27 14:52:06.592 - [PG~Sybase中文字][PGMaster] - PDK connector node stopped: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1751005949752 
[TRACE] 2025-06-27 14:52:06.592 - [PG~Sybase中文字][PGMaster] - PDK connector node released: HazelcastSourcePdkDataNode_2fdf666e-dab5-4fc5-8fe7-10517ca46358_1751005949752 
[TRACE] 2025-06-27 14:52:06.592 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] schema data cleaned 
[TRACE] 2025-06-27 14:52:06.592 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] monitor closed 
[TRACE] 2025-06-27 14:52:06.599 - [PG~Sybase中文字][PGMaster] - Node PGMaster[2fdf666e-dab5-4fc5-8fe7-10517ca46358] close complete, cost 107 ms 
[TRACE] 2025-06-27 14:52:06.599 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] running status set to false 
[TRACE] 2025-06-27 14:52:06.599 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] schema data cleaned 
[TRACE] 2025-06-27 14:52:06.599 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] monitor closed 
[TRACE] 2025-06-27 14:52:06.600 - [PG~Sybase中文字][表编辑] - Node 表编辑[1373b647-c5ed-4163-bc67-6d952f713ac1] close complete, cost 1 ms 
[TRACE] 2025-06-27 14:52:06.600 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] running status set to false 
[TRACE] 2025-06-27 14:52:06.722 - [PG~Sybase中文字][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_09b9c60e-c4e2-4400-8f8e-edef6e119b25_1751005949970 
[TRACE] 2025-06-27 14:52:06.726 - [PG~Sybase中文字][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_09b9c60e-c4e2-4400-8f8e-edef6e119b25_1751005949970 
[TRACE] 2025-06-27 14:52:06.726 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] schema data cleaned 
[TRACE] 2025-06-27 14:52:06.726 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] monitor closed 
[TRACE] 2025-06-27 14:52:06.732 - [PG~Sybase中文字][Sybase] - Node Sybase[09b9c60e-c4e2-4400-8f8e-edef6e119b25] close complete, cost 129 ms 
[TRACE] 2025-06-27 14:53:01.767 - [PG~Sybase中文字] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-06-27 14:53:02.777 - [PG~Sybase中文字] - Closed TaskInspect instance
  com.tapdata.taskinspect.TaskInspect@3ee8615e 
[TRACE] 2025-06-27 14:53:02.778 - [PG~Sybase中文字] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@16c201c6 
[TRACE] 2025-06-27 14:53:02.966 - [PG~Sybase中文字] - Stop task milestones: 685ac0e1cf039e48079f30ec(PG~Sybase中文字)  
[TRACE] 2025-06-27 14:53:02.967 - [PG~Sybase中文字] - Stopped task aspect(s) 
[TRACE] 2025-06-27 14:53:02.971 - [PG~Sybase中文字] - Snapshot order controller have been removed 
[INFO ] 2025-06-27 14:53:02.971 - [PG~Sybase中文字] - Task stopped. 
[TRACE] 2025-06-27 14:53:03.037 - [PG~Sybase中文字] - Remove memory task client succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
[TRACE] 2025-06-27 14:53:03.038 - [PG~Sybase中文字] - Destroy memory task client cache succeed, task: PG~Sybase中文字[685ac0e1cf039e48079f30ec] 
