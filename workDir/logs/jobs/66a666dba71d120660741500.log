[INFO ] 2024-07-28 23:45:18.021 - [任务 1] - Start task milestones: 66a666dba71d120660741500(任务 1) 
[INFO ] 2024-07-28 23:45:18.041 - [任务 1] - Task initialization... 
[INFO ] 2024-07-28 23:45:18.992 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 23:45:19.009 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-28 23:45:19.502 - [任务 1][TestPO] - Node TestPO[4930baf9-58e6-46cc-bbb8-fc5cf9066da7] start preload schema,table counts: 1 
[INFO ] 2024-07-28 23:45:19.503 - [任务 1][TESTPO] - Node TESTPO[79162208-72e4-4115-8f94-1bc8e87df9d7] start preload schema,table counts: 1 
[INFO ] 2024-07-28 23:45:19.507 - [任务 1][TestPO] - Node TestPO[4930baf9-58e6-46cc-bbb8-fc5cf9066da7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 23:45:19.509 - [任务 1][TESTPO] - Node TESTPO[79162208-72e4-4115-8f94-1bc8e87df9d7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 23:45:20.600 - [任务 1][TESTPO] - Source node "TESTPO" read batch size: 100 
[INFO ] 2024-07-28 23:45:20.601 - [任务 1][TESTPO] - Source node "TESTPO" event queue capacity: 200 
[INFO ] 2024-07-28 23:45:20.601 - [任务 1][TESTPO] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-28 23:45:20.686 - [任务 1][TestPO] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-28 23:45:20.757 - [任务 1][TESTPO] - batch offset found: {},stream offset found: {"cdcOffset":1722181520,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 23:45:20.759 - [任务 1][TestPO] - Table "test.TestPO" exists, skip auto create table 
[INFO ] 2024-07-28 23:45:20.759 - [任务 1][TestPO] - The table TestPO has already exist. 
[INFO ] 2024-07-28 23:45:20.830 - [任务 1][TESTPO] - Initial sync started 
[INFO ] 2024-07-28 23:45:20.849 - [任务 1][TESTPO] - Starting batch read, table name: TESTPO, offset: null 
[INFO ] 2024-07-28 23:45:20.849 - [任务 1][TESTPO] - Table TESTPO is going to be initial synced 
[INFO ] 2024-07-28 23:45:20.929 - [任务 1][TESTPO] - Table [TESTPO] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-28 23:45:20.932 - [任务 1][TESTPO] - Query table 'TESTPO' counts: 600 
[INFO ] 2024-07-28 23:45:20.932 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 23:45:20.932 - [任务 1][TESTPO] - Incremental sync starting... 
[INFO ] 2024-07-28 23:45:20.932 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 23:45:20.935 - [任务 1][TESTPO] - Starting stream read, table list: [TESTPO, _tapdata_heartbeat_table], offset: {"cdcOffset":1722181520,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 23:45:21.140 - [任务 1][TESTPO] - Connector MongoDB incremental start succeed, tables: [TESTPO, _tapdata_heartbeat_table], data change syncing 
[WARN ] 2024-07-28 23:59:02.346 - [任务 1] - Save milestone failed: RestException{uri='http://localhost:3000/api/Task/update?access_token=e14a1c81cf164e29b7816b116721f5a9de3318ccbce34db79760c63b6c5ebc8d&where=%7B%20%22_id%22%20%3A%20%7B%20%22%24oid%22%20%3A%20%2266a666dba71d120660741500%22%20%7D%20%7D', method='POST', param=Document{{$set=Document{{attrs.milestone={TASK=io.tapdata.milestone.entity.MilestoneEntity@4cb1ac72, CDC=io.tapdata.milestone.entity.MilestoneEntity@5554134d, SNAPSHOT=io.tapdata.milestone.entity.MilestoneEntity@3a79c9fe, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@3dfa4898, DEDUCTION=io.tapdata.milestone.entity.MilestoneEntity@7c69a169, DATA_NODE_INIT=io.tapdata.milestone.entity.MilestoneEntity@9d21bd2}, attrs.nodeMilestones={4930baf9-58e6-46cc-bbb8-fc5cf9066da7={CDC_WRITE=io.tapdata.milestone.entity.MilestoneEntity@7d90152f, NODE=io.tapdata.milestone.entity.MilestoneEntity@12c12c8d, TABLE_INIT=io.tapdata.milestone.entity.MilestoneEntity@2392b555, SNAPSHOT_WRITE=io.tapdata.milestone.entity.MilestoneEntity@3a07267e}, 79162208-72e4-4115-8f94-1bc8e87df9d7={OPEN_CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@47c3757c, NODE=io.tapdata.milestone.entity.MilestoneEntity@5cfd8c4d, SNAPSHOT_READ=io.tapdata.milestone.entity.MilestoneEntity@6af3a29a, CDC_READ=io.tapdata.milestone.entity.MilestoneEntity@69c44d6c}}, syncStatus=CDC}}}}, code='IllegalArgument', data=null, reqId=fa45a168-c2ff-4e4c-a83b-930315123fd4}: 无效参数: task not found 
[INFO ] 2024-07-28 23:59:02.378 - [任务 1][TESTPO] - Node TESTPO[79162208-72e4-4115-8f94-1bc8e87df9d7] running status set to false 
[INFO ] 2024-07-28 23:59:02.379 - [任务 1][TESTPO] - PDK connector node stopped: HazelcastSourcePdkDataNode-79162208-72e4-4115-8f94-1bc8e87df9d7 
[INFO ] 2024-07-28 23:59:02.382 - [任务 1][TESTPO] - PDK connector node released: HazelcastSourcePdkDataNode-79162208-72e4-4115-8f94-1bc8e87df9d7 
[INFO ] 2024-07-28 23:59:02.382 - [任务 1][TESTPO] - Node TESTPO[79162208-72e4-4115-8f94-1bc8e87df9d7] schema data cleaned 
[INFO ] 2024-07-28 23:59:02.398 - [任务 1][TESTPO] - Node TESTPO[79162208-72e4-4115-8f94-1bc8e87df9d7] monitor closed 
[INFO ] 2024-07-28 23:59:02.399 - [任务 1][TESTPO] - Node TESTPO[79162208-72e4-4115-8f94-1bc8e87df9d7] close complete, cost 37 ms 
[INFO ] 2024-07-28 23:59:02.399 - [任务 1][TestPO] - Node TestPO[4930baf9-58e6-46cc-bbb8-fc5cf9066da7] running status set to false 
[INFO ] 2024-07-28 23:59:02.441 - [任务 1][TestPO] - PDK connector node stopped: HazelcastTargetPdkDataNode-4930baf9-58e6-46cc-bbb8-fc5cf9066da7 
[INFO ] 2024-07-28 23:59:02.441 - [任务 1][TestPO] - PDK connector node released: HazelcastTargetPdkDataNode-4930baf9-58e6-46cc-bbb8-fc5cf9066da7 
[INFO ] 2024-07-28 23:59:02.442 - [任务 1][TestPO] - Node TestPO[4930baf9-58e6-46cc-bbb8-fc5cf9066da7] schema data cleaned 
[INFO ] 2024-07-28 23:59:02.442 - [任务 1][TestPO] - Node TestPO[4930baf9-58e6-46cc-bbb8-fc5cf9066da7] monitor closed 
[INFO ] 2024-07-28 23:59:02.651 - [任务 1][TestPO] - Node TestPO[4930baf9-58e6-46cc-bbb8-fc5cf9066da7] close complete, cost 45 ms 
[INFO ] 2024-07-28 23:59:04.376 - [任务 1][TESTPO] - Incremental sync completed 
