[INFO ] 2024-11-17 14:28:25.086 - [任务 291] - Task initialization... 
[INFO ] 2024-11-17 14:28:25.088 - [任务 291] - Start task milestones: 67398ce01939741a30f90655(任务 291) 
[INFO ] 2024-11-17 14:28:25.425 - [任务 291] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-17 14:28:25.426 - [任务 291] - The engine receives 任务 291 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-17 14:28:25.477 - [任务 291][PG] - Node PG[224796cc-783d-42b6-97fb-24714e651438] start preload schema,table counts: 4 
[INFO ] 2024-11-17 14:28:25.477 - [任务 291][SqlServer] - Node SqlServer[bef9a994-aab1-4e44-8817-8fc193a4ec82] start preload schema,table counts: 4 
[INFO ] 2024-11-17 14:28:25.477 - [任务 291][PG] - Node PG[224796cc-783d-42b6-97fb-24714e651438] preload schema finished, cost 0 ms 
[INFO ] 2024-11-17 14:28:25.477 - [任务 291][SqlServer] - Node SqlServer[bef9a994-aab1-4e44-8817-8fc193a4ec82] preload schema finished, cost 0 ms 
[INFO ] 2024-11-17 14:28:26.780 - [任务 291][SqlServer] - Node(SqlServer) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-17 14:28:26.784 - [任务 291][SqlServer] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-17 14:28:26.797 - [任务 291][PG] - Source node "PG" read batch size: 100 
[INFO ] 2024-11-17 14:28:26.831 - [任务 291][PG] - Source node "PG" event queue capacity: 200 
[INFO ] 2024-11-17 14:28:26.831 - [任务 291][PG] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-17 14:28:27.142 - [任务 291][SqlServer] - Will create master partition table [wim_num_new] to target, init sub partition list: [wim_num_new_0_100] 
[WARN ] 2024-11-17 14:28:27.885 - [任务 291][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-11-17 14:28:27.885 - [任务 291][PG] - new logical replication slot created, slotName:tapdata_cdc_f31a1212_2e1b_4c7a_a919_dfe3b1d39245 
[INFO ] 2024-11-17 14:28:27.913 - [任务 291][PG] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2024-11-17 14:28:27.928 - [任务 291][PG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-17 14:28:27.931 - [任务 291] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-11-17 14:28:28.035 - [任务 291][PG] - Initial sync started 
[INFO ] 2024-11-17 14:28:28.040 - [任务 291][PG] - Starting batch read, table name: wim_num_new 
[INFO ] 2024-11-17 14:28:28.040 - [任务 291][PG] - Table wim_num_new is going to be initial synced 
[INFO ] 2024-11-17 14:28:28.194 - [任务 291][SqlServer] - Will create master partition table [wim_parition] to target, init sub partition list: [wim_parition_202410, wim_parition_202411, wim_parition_202412, wim_parition_202401, wim_parition_202403] 
[INFO ] 2024-11-17 14:28:28.194 - [任务 291][PG] - Table [wim_num_new] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-17 14:28:28.194 - [任务 291][PG] - Starting batch read, table name: wim_parition 
[INFO ] 2024-11-17 14:28:28.375 - [任务 291][PG] - Table wim_parition is going to be initial synced 
[INFO ] 2024-11-17 14:28:28.375 - [任务 291][PG] - Table [wim_parition] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-17 14:28:28.375 - [任务 291][PG] - Starting batch read, table name: wim_num 
[INFO ] 2024-11-17 14:28:28.376 - [任务 291][PG] - Table wim_num is going to be initial synced 
[INFO ] 2024-11-17 14:28:28.517 - [任务 291][PG] - Table [wim_num] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-17 14:28:28.518 - [任务 291][PG] - Starting batch read, table name: wim_parition_da 
[INFO ] 2024-11-17 14:28:28.518 - [任务 291][PG] - Table wim_parition_da is going to be initial synced 
[INFO ] 2024-11-17 14:28:28.541 - [任务 291][PG] - Query snapshot row size completed: PG(224796cc-783d-42b6-97fb-24714e651438) 
[INFO ] 2024-11-17 14:28:28.614 - [任务 291][PG] - Table [wim_parition_da] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-17 14:28:28.620 - [任务 291][PG] - Initial sync completed 
[INFO ] 2024-11-17 14:28:28.620 - [任务 291][PG] - Incremental sync starting... 
[INFO ] 2024-11-17 14:28:28.620 - [任务 291][PG] - Initial sync completed 
[INFO ] 2024-11-17 14:28:28.620 - [任务 291][PG] - Starting stream read, table list: [wim_num_new, wim_parition, wim_num, wim_parition_da], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[WARN ] 2024-11-17 14:28:28.712 - [任务 291][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-11-17 14:28:28.714 - [任务 291][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_f31a1212_2e1b_4c7a_a919_dfe3b1d39245 
[INFO ] 2024-11-17 14:28:29.326 - [任务 291][SqlServer] - Will create master partition table [wim_num] to target, init sub partition list: [wim_num600_700, wim_num800_900, wim_num900_1000, wim_num1000_1100, wim_num300_400, wim_num100_200, wim_num200_300, wim_num1100_1200, wim_num1200_1300, wim_num1300_1400, wim_num0_100, wim_num400_500, wim_num500_600] 
[INFO ] 2024-11-17 14:28:29.793 - [任务 291][PG] - Connector PostgreSQL incremental start succeed, tables: [wim_num_new, wim_parition, wim_num, wim_parition_da, wim_num_new_0_100, wim_parition_202410, wim_parition_202411, wim_parition_202412, wim_parition_202401, wim_parition_202403, wim_num600_700, wim_num800_900, wim_num900_1000, wim_num1000_1100, wim_num300_400, wim_num100_200, wim_num200_300, wim_num1100_1200, wim_num1200_1300, wim_num1300_1400, wim_num0_100, wim_num400_500, wim_num500_600, wim_parition_da_202410, wim_parition_da_202407, wim_parition_da_202411, wim_parition_da_202409, wim_parition_da_202406, wim_parition_da_202405, wim_parition_da_202412, wim_parition_da_202408], data change syncing 
[INFO ] 2024-11-17 14:28:30.180 - [任务 291][SqlServer] - Will create master partition table [wim_parition_da] to target, init sub partition list: [wim_parition_da_202410, wim_parition_da_202407, wim_parition_da_202411, wim_parition_da_202409, wim_parition_da_202406, wim_parition_da_202405, wim_parition_da_202412, wim_parition_da_202408] 
[INFO ] 2024-11-17 14:31:28.094 - [任务 291][PG] - Found new table(s): [wim_num_new_200_300, wim_num_new] 
[INFO ] 2024-11-17 14:31:28.515 - [任务 291][PG] - Load new table(s) schema finished, loaded schema count: 1 
[WARN ] 2024-11-17 14:31:28.516 - [任务 291][PG] - It is expected to load 2 new table models, and 1 table models no longer exist and will be ignored. The table name(s) that does not exist: [wim_num_new] 
[INFO ] 2024-11-17 14:31:28.546 - [任务 291][PG] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@3e1d4c80: {"partitionMasterTableId":"wim_num_new","table":{"comment":"","id":"wim_num_new_200_300","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"num"}],"name":"wim_num_new_200_300_pkey","primary":true,"unique":true}],"lastUpdate":1731825088516,"maxPKPos":2,"maxPos":3,"name":"wim_num_new_200_300","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"dataType":"character varying(100)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"character varying","tapType":{"bytes":100,"defaultValue":10485760,"type":10},"virtual":false},"num":{"autoInc":false,"dataType":"integer","name":"num","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"num"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"num":{"fieldAsc":true,"name":"num"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (num)","partitionFields":[{"autoInc":false,"dataType":"integer","name":"num","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM (0) TO (100)","tableName":"wim_num_new_0_100","tapPartitionTypes":[{"rangeFrom":{"originValue":"0","value":"0","valueType":"NORMAL"},"rangeTo":{"originValue":"100","value":"100","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (200) TO (300)","tableName":"wim_num_new_200_300","tapPartitionTypes":[{"rangeFrom":{"originValue":"200","value":"200","valueType":"NORMAL"},"rangeTo":{"originValue":"300","value":"300","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"wim_num_new"},"tableId":"wim_num_new_200_300","type":206} 
[INFO ] 2024-11-17 14:31:28.546 - [任务 291][PG] - Sync sub table's [wim_num_new_200_300] create table ddl, will add update master table [wim_num_new] metadata 
[INFO ] 2024-11-17 14:31:28.621 - [任务 291][PG] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_wim_num_new_200_300_673762ee83338516ea176572_67398ce01939741a30f90655 
[INFO ] 2024-11-17 14:31:28.621 - [任务 291][PG] - Create new table schema transform finished: TapTable id wim_num_new_200_300 name wim_num_new_200_300 storageEngine null charset null number of fields 3 
[INFO ] 2024-11-17 14:31:28.822 - [任务 291][PG] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-11-17 14:31:28.927 - [任务 291][PG] - Incremental sync completed 
[INFO ] 2024-11-17 14:31:28.927 - [任务 291][SqlServer] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@3e1d4c80: {"partitionMasterTableId":"wim_num_new","table":{"comment":"","id":"wim_num_new_200_300","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"num"}],"name":"wim_num_new_200_300_pkey","primary":true,"unique":true}],"lastUpdate":1731825088516,"maxPKPos":2,"maxPos":3,"name":"wim_num_new_200_300","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"dataType":"character varying(100)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"character varying","tapType":{"bytes":100,"defaultValue":10485760,"type":10},"virtual":false},"num":{"autoInc":false,"dataType":"integer","name":"num","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"num"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"num":{"fieldAsc":true,"name":"num"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (num)","partitionFields":[{"autoInc":false,"dataType":"integer","name":"num","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM (0) TO (100)","tableName":"wim_num_new_0_100","tapPartitionTypes":[{"rangeFrom":{"originValue":"0","value":"0","valueType":"NORMAL"},"rangeTo":{"originValue":"100","value":"100","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (200) TO (300)","tableName":"wim_num_new_200_300","tapPartitionTypes":[{"rangeFrom":{"originValue":"200","value":"200","valueType":"NORMAL"},"rangeTo":{"originValue":"300","value":"300","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"wim_num_new"},"tableId":"wim_num_new_200_300","type":206}). Wait for all previous events to be processed 
[INFO ] 2024-11-17 14:31:29.535 - [任务 291][SqlServer] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@3e1d4c80: {"partitionMasterTableId":"wim_num_new","table":{"comment":"","id":"wim_num_new_200_300","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"num"}],"name":"wim_num_new_200_300_pkey","primary":true,"unique":true}],"lastUpdate":1731825088516,"maxPKPos":2,"maxPos":3,"name":"wim_num_new_200_300","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"dataType":"character varying(100)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"character varying","tapType":{"bytes":100,"defaultValue":10485760,"type":10},"virtual":false},"num":{"autoInc":false,"dataType":"integer","name":"num","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"num"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"num":{"fieldAsc":true,"name":"num"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (num)","partitionFields":[{"autoInc":false,"dataType":"integer","name":"num","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM (0) TO (100)","tableName":"wim_num_new_0_100","tapPartitionTypes":[{"rangeFrom":{"originValue":"0","value":"0","valueType":"NORMAL"},"rangeTo":{"originValue":"100","value":"100","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (200) TO (300)","tableName":"wim_num_new_200_300","tapPartitionTypes":[{"rangeFrom":{"originValue":"200","value":"200","valueType":"NORMAL"},"rangeTo":{"originValue":"300","value":"300","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"wim_num_new"},"tableId":"wim_num_new_200_300","type":206}) 
[INFO ] 2024-11-17 14:31:29.642 - [任务 291][PG] - Starting batch read, table name: wim_num_new_200_300 
[INFO ] 2024-11-17 14:31:29.720 - [任务 291][PG] - Table wim_num_new_200_300 is going to be initial synced 
[INFO ] 2024-11-17 14:31:29.721 - [任务 291][PG] - Query snapshot row size completed: PG(224796cc-783d-42b6-97fb-24714e651438) 
[INFO ] 2024-11-17 14:31:29.811 - [任务 291][PG] - Table [wim_num_new_200_300] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-17 14:31:29.811 - [任务 291][PG] - Initial sync completed 
[INFO ] 2024-11-17 14:31:29.812 - [任务 291][PG] - Incremental sync starting... 
[INFO ] 2024-11-17 14:31:29.812 - [任务 291][PG] - Initial sync completed 
[INFO ] 2024-11-17 14:31:29.826 - [任务 291][PG] - Starting stream read, table list: [wim_num_new, wim_parition, wim_num, wim_num_new_200_300, wim_parition_da], offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":621059688,\"lsn_commit\":621059688,\"lsn\":621059688,\"ts_usec\":1731824936519269}"} 
[WARN ] 2024-11-17 14:31:29.827 - [任务 291][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-11-17 14:31:29.919 - [任务 291][SqlServer] - Will create sub partition table [wim_num_new_200_300] to target, master table is: wim_num_new 
[INFO ] 2024-11-17 14:31:29.921 - [任务 291][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_f31a1212_2e1b_4c7a_a919_dfe3b1d39245 
[INFO ] 2024-11-17 14:31:30.528 - [任务 291][SqlServer] - The table wim_num_new_200_300 has already exist. 
[INFO ] 2024-11-17 14:31:31.132 - [任务 291][PG] - Connector PostgreSQL incremental start succeed, tables: [wim_num_new, wim_parition, wim_num, wim_num_new_200_300, wim_parition_da, wim_num_new_0_100, wim_parition_202410, wim_parition_202411, wim_parition_202412, wim_parition_202401, wim_parition_202403, wim_num600_700, wim_num800_900, wim_num900_1000, wim_num1000_1100, wim_num300_400, wim_num100_200, wim_num200_300, wim_num1100_1200, wim_num1200_1300, wim_num1300_1400, wim_num0_100, wim_num400_500, wim_num500_600, wim_parition_da_202410, wim_parition_da_202407, wim_parition_da_202411, wim_parition_da_202409, wim_parition_da_202406, wim_parition_da_202405, wim_parition_da_202412, wim_parition_da_202408], data change syncing 
[INFO ] 2024-11-17 14:36:28.082 - [任务 291][PG] - Found new table(s): [wim_parition_da_202404, wim_parition_da] 
[INFO ] 2024-11-17 14:36:28.492 - [任务 291][PG] - Load new table(s) schema finished, loaded schema count: 1 
[WARN ] 2024-11-17 14:36:28.495 - [任务 291][PG] - It is expected to load 2 new table models, and 1 table models no longer exist and will be ignored. The table name(s) that does not exist: [wim_parition_da] 
[INFO ] 2024-11-17 14:36:28.525 - [任务 291][PG] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@3f326eb: {"partitionMasterTableId":"wim_parition_da","table":{"comment":"","id":"wim_parition_da_202404","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"createdat"}],"name":"wim_parition_da_202404_pkey","primary":true,"unique":true}],"lastUpdate":1731825388495,"maxPKPos":2,"maxPos":3,"name":"wim_parition_da_202404","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"dataType":"character varying(100)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"character varying","tapType":{"bytes":100,"defaultValue":10485760,"type":10},"virtual":false},"createdat":{"autoInc":false,"dataType":"timestamp without time zone","name":"createdat","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"timestamp without time zone","tapType":{"defaultFraction":6,"fraction":6,"max":"9999-12-31T23:59:59Z","min":"1000-01-01T00:00:00Z","type":1,"withTimeZone":false},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"createdat"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"createdat":{"fieldAsc":true,"name":"createdat"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (createdat)","partitionFields":[{"autoInc":false,"dataType":"timestamp without time zone","name":"createdat","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-08-01 00:00:00') TO ('2024-08-31 00:00:00')","tableName":"wim_parition_da_202408","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-08-01 00:00:00'","value":"'2024-08-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-08-31 00:00:00'","value":"'2024-08-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-10-01 00:00:00') TO ('2024-10-31 00:00:00')","tableName":"wim_parition_da_202410","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-10-01 00:00:00'","value":"'2024-10-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-10-31 00:00:00'","value":"'2024-10-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-07-01 00:00:00') TO ('2024-07-31 00:00:00')","tableName":"wim_parition_da_202407","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-07-01 00:00:00'","value":"'2024-07-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-07-31 00:00:00'","value":"'2024-07-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-11-01 00:00:00') TO ('2024-11-30 00:00:00')","tableName":"wim_parition_da_202411","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-11-01 00:00:00'","value":"'2024-11-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-11-30 00:00:00'","value":"'2024-11-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-09-01 00:00:00') TO ('2024-09-30 00:00:00')","tableName":"wim_parition_da_202409","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-09-01 00:00:00'","value":"'2024-09-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-09-30 00:00:00'","value":"'2024-09-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-06-01 00:00:00') TO ('2024-06-30 00:00:00')","tableName":"wim_parition_da_202406","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-06-01 00:00:00'","value":"'2024-06-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-06-30 00:00:00'","value":"'2024-06-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-05-01 00:00:00') TO ('2024-05-30 00:00:00')","tableName":"wim_parition_da_202405","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-05-01 00:00:00'","value":"'2024-05-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-05-30 00:00:00'","value":"'2024-05-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-04-01 00:00:00') TO ('2024-04-30 00:00:00')","tableName":"wim_parition_da_202404","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-04-01 00:00:00'","value":"'2024-04-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-04-30 00:00:00'","value":"'2024-04-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-12-01 00:00:00') TO ('2024-12-30 00:00:00')","tableName":"wim_parition_da_202412","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-12-01 00:00:00'","value":"'2024-12-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-30 00:00:00'","value":"'2024-12-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"wim_parition_da"},"tableId":"wim_parition_da_202404","type":206} 
[INFO ] 2024-11-17 14:36:28.527 - [任务 291][PG] - Sync sub table's [wim_parition_da_202404] create table ddl, will add update master table [wim_parition_da] metadata 
[INFO ] 2024-11-17 14:36:28.593 - [任务 291][PG] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_wim_parition_da_202404_673762ee83338516ea176572_67398ce01939741a30f90655 
[INFO ] 2024-11-17 14:36:28.593 - [任务 291][PG] - Create new table schema transform finished: TapTable id wim_parition_da_202404 name wim_parition_da_202404 storageEngine null charset null number of fields 3 
[INFO ] 2024-11-17 14:36:28.717 - [任务 291][PG] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-11-17 14:36:28.718 - [任务 291][SqlServer] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@3f326eb: {"partitionMasterTableId":"wim_parition_da","table":{"comment":"","id":"wim_parition_da_202404","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"createdat"}],"name":"wim_parition_da_202404_pkey","primary":true,"unique":true}],"lastUpdate":1731825388495,"maxPKPos":2,"maxPos":3,"name":"wim_parition_da_202404","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"dataType":"character varying(100)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"character varying","tapType":{"bytes":100,"defaultValue":10485760,"type":10},"virtual":false},"createdat":{"autoInc":false,"dataType":"timestamp(6) without time zone","name":"createdat","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"timestamp without time zone","tapType":{"defaultFraction":6,"fraction":6,"max":"9999-12-31T23:59:59Z","min":"1000-01-01T00:00:00Z","type":1,"withTimeZone":false},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"createdat"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"createdat":{"fieldAsc":true,"name":"createdat"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (createdat)","partitionFields":[{"autoInc":false,"dataType":"timestamp without time zone","name":"createdat","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"tapType":{"defaultFraction":6,"fraction":6,"max":"9999-12-31T23:59:59Z","min":"1000-01-01T00:00:00Z","type":1,"withTimeZone":false},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-08-01 00:00:00') TO ('2024-08-31 00:00:00')","tableName":"wim_parition_da_202408","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-08-01 00:00:00'","value":"'2024-08-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-08-31 00:00:00'","value":"'2024-08-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-10-01 00:00:00') TO ('2024-10-31 00:00:00')","tableName":"wim_parition_da_202410","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-10-01 00:00:00'","value":"'2024-10-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-10-31 00:00:00'","value":"'2024-10-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-07-01 00:00:00') TO ('2024-07-31 00:00:00')","tableName":"wim_parition_da_202407","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-07-01 00:00:00'","value":"'2024-07-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-07-31 00:00:00'","value":"'2024-07-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-11-01 00:00:00') TO ('2024-11-30 00:00:00')","tableName":"wim_parition_da_202411","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-11-01 00:00:00'","value":"'2024-11-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-11-30 00:00:00'","value":"'2024-11-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-09-01 00:00:00') TO ('2024-09-30 00:00:00')","tableName":"wim_parition_da_202409","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-09-01 00:00:00'","value":"'2024-09-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-09-30 00:00:00'","value":"'2024-09-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-06-01 00:00:00') TO ('2024-06-30 00:00:00')","tableName":"wim_parition_da_202406","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-06-01 00:00:00'","value":"'2024-06-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-06-30 00:00:00'","value":"'2024-06-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-05-01 00:00:00') TO ('2024-05-30 00:00:00')","tableName":"wim_parition_da_202405","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-05-01 00:00:00'","value":"'2024-05-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-05-30 00:00:00'","value":"'2024-05-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-04-01 00:00:00') TO ('2024-04-30 00:00:00')","tableName":"wim_parition_da_202404","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-04-01 00:00:00'","value":"'2024-04-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-04-30 00:00:00'","value":"'2024-04-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-12-01 00:00:00') TO ('2024-12-30 00:00:00')","tableName":"wim_parition_da_202412","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-12-01 00:00:00'","value":"'2024-12-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-30 00:00:00'","value":"'2024-12-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"wim_parition_da"},"tableId":"wim_parition_da_202404","type":206}). Wait for all previous events to be processed 
[INFO ] 2024-11-17 14:36:28.919 - [任务 291][SqlServer] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@3f326eb: {"partitionMasterTableId":"wim_parition_da","table":{"comment":"","id":"wim_parition_da_202404","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"createdat"}],"name":"wim_parition_da_202404_pkey","primary":true,"unique":true}],"lastUpdate":1731825388495,"maxPKPos":2,"maxPos":3,"name":"wim_parition_da_202404","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"dataType":"character varying(100)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"character varying","tapType":{"bytes":100,"defaultValue":10485760,"type":10},"virtual":false},"createdat":{"autoInc":false,"dataType":"timestamp(6) without time zone","name":"createdat","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"timestamp without time zone","tapType":{"defaultFraction":6,"fraction":6,"max":"9999-12-31T23:59:59Z","min":"1000-01-01T00:00:00Z","type":1,"withTimeZone":false},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"createdat"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"createdat":{"fieldAsc":true,"name":"createdat"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (createdat)","partitionFields":[{"autoInc":false,"dataType":"timestamp without time zone","name":"createdat","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"tapType":{"defaultFraction":7,"fraction":6,"max":"9999-12-31T23:59:59Z","min":"0001-01-01T00:00:00Z","type":1},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-08-01 00:00:00') TO ('2024-08-31 00:00:00')","tableName":"wim_parition_da_202408","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-08-01 00:00:00'","value":"'2024-08-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-08-31 00:00:00'","value":"'2024-08-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-10-01 00:00:00') TO ('2024-10-31 00:00:00')","tableName":"wim_parition_da_202410","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-10-01 00:00:00'","value":"'2024-10-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-10-31 00:00:00'","value":"'2024-10-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-07-01 00:00:00') TO ('2024-07-31 00:00:00')","tableName":"wim_parition_da_202407","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-07-01 00:00:00'","value":"'2024-07-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-07-31 00:00:00'","value":"'2024-07-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-11-01 00:00:00') TO ('2024-11-30 00:00:00')","tableName":"wim_parition_da_202411","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-11-01 00:00:00'","value":"'2024-11-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-11-30 00:00:00'","value":"'2024-11-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-09-01 00:00:00') TO ('2024-09-30 00:00:00')","tableName":"wim_parition_da_202409","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-09-01 00:00:00'","value":"'2024-09-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-09-30 00:00:00'","value":"'2024-09-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-06-01 00:00:00') TO ('2024-06-30 00:00:00')","tableName":"wim_parition_da_202406","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-06-01 00:00:00'","value":"'2024-06-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-06-30 00:00:00'","value":"'2024-06-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-05-01 00:00:00') TO ('2024-05-30 00:00:00')","tableName":"wim_parition_da_202405","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-05-01 00:00:00'","value":"'2024-05-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-05-30 00:00:00'","value":"'2024-05-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-04-01 00:00:00') TO ('2024-04-30 00:00:00')","tableName":"wim_parition_da_202404","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-04-01 00:00:00'","value":"'2024-04-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-04-30 00:00:00'","value":"'2024-04-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-12-01 00:00:00') TO ('2024-12-30 00:00:00')","tableName":"wim_parition_da_202412","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-12-01 00:00:00'","value":"'2024-12-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-30 00:00:00'","value":"'2024-12-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"wim_parition_da"},"tableId":"wim_parition_da_202404","type":206}) 
[INFO ] 2024-11-17 14:36:29.122 - [任务 291][PG] - Incremental sync completed 
[INFO ] 2024-11-17 14:36:29.284 - [任务 291][SqlServer] - Will create sub partition table [wim_parition_da_202404] to target, master table is: wim_parition_da 
[INFO ] 2024-11-17 14:36:29.728 - [任务 291][PG] - Starting batch read, table name: wim_parition_da_202404 
[INFO ] 2024-11-17 14:36:29.728 - [任务 291][PG] - Table wim_parition_da_202404 is going to be initial synced 
[INFO ] 2024-11-17 14:36:29.892 - [任务 291][SqlServer] - The table wim_parition_da_202404 has already exist. 
[INFO ] 2024-11-17 14:36:29.892 - [任务 291][PG] - Query snapshot row size completed: PG(224796cc-783d-42b6-97fb-24714e651438) 
[INFO ] 2024-11-17 14:36:29.892 - [任务 291][PG] - Table [wim_parition_da_202404] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-17 14:36:29.892 - [任务 291][PG] - Initial sync completed 
[INFO ] 2024-11-17 14:36:29.893 - [任务 291][PG] - Incremental sync starting... 
[INFO ] 2024-11-17 14:36:29.893 - [任务 291][PG] - Initial sync completed 
[INFO ] 2024-11-17 14:36:29.894 - [任务 291][PG] - Starting stream read, table list: [wim_num_new, wim_parition, wim_parition_da_202404, wim_num, wim_num_new_200_300, wim_parition_da], offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":621319944,\"lsn_commit\":621319944,\"lsn\":621319944,\"ts_usec\":1731825130269155}"} 
[WARN ] 2024-11-17 14:36:29.984 - [任务 291][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-11-17 14:36:29.985 - [任务 291][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_f31a1212_2e1b_4c7a_a919_dfe3b1d39245 
[INFO ] 2024-11-17 14:36:31.203 - [任务 291][PG] - Connector PostgreSQL incremental start succeed, tables: [wim_num_new, wim_parition, wim_parition_da_202404, wim_num, wim_num_new_200_300, wim_parition_da, wim_num_new_0_100, wim_parition_202410, wim_parition_202411, wim_parition_202412, wim_parition_202401, wim_parition_202403, wim_num600_700, wim_num800_900, wim_num900_1000, wim_num1000_1100, wim_num300_400, wim_num100_200, wim_num200_300, wim_num1100_1200, wim_num1200_1300, wim_num1300_1400, wim_num0_100, wim_num400_500, wim_num500_600, wim_parition_da_202410, wim_parition_da_202407, wim_parition_da_202411, wim_parition_da_202409, wim_parition_da_202406, wim_parition_da_202405, wim_parition_da_202412, wim_parition_da_202408], data change syncing 
[INFO ] 2024-11-17 14:44:03.414 - [任务 291][PG] - Node PG[224796cc-783d-42b6-97fb-24714e651438] running status set to false 
[INFO ] 2024-11-17 14:44:03.531 - [任务 291][PG] - Incremental sync completed 
[INFO ] 2024-11-17 14:44:03.544 - [任务 291][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_224796cc-783d-42b6-97fb-24714e651438_1731825388978 
[INFO ] 2024-11-17 14:44:03.545 - [任务 291][PG] - PDK connector node released: HazelcastSourcePdkDataNode_224796cc-783d-42b6-97fb-24714e651438_1731825388978 
[INFO ] 2024-11-17 14:44:03.545 - [任务 291][PG] - Node PG[224796cc-783d-42b6-97fb-24714e651438] schema data cleaned 
[INFO ] 2024-11-17 14:44:03.547 - [任务 291][PG] - Node PG[224796cc-783d-42b6-97fb-24714e651438] monitor closed 
[INFO ] 2024-11-17 14:44:03.547 - [任务 291][PG] - Node PG[224796cc-783d-42b6-97fb-24714e651438] close complete, cost 290 ms 
[INFO ] 2024-11-17 14:44:03.581 - [任务 291][SqlServer] - Node SqlServer[bef9a994-aab1-4e44-8817-8fc193a4ec82] running status set to false 
[INFO ] 2024-11-17 14:44:03.581 - [任务 291][SqlServer] - PDK connector node stopped: HazelcastTargetPdkDataNode_bef9a994-aab1-4e44-8817-8fc193a4ec82_1731824906188 
[INFO ] 2024-11-17 14:44:03.581 - [任务 291][SqlServer] - PDK connector node released: HazelcastTargetPdkDataNode_bef9a994-aab1-4e44-8817-8fc193a4ec82_1731824906188 
[INFO ] 2024-11-17 14:44:03.581 - [任务 291][SqlServer] - Node SqlServer[bef9a994-aab1-4e44-8817-8fc193a4ec82] schema data cleaned 
[INFO ] 2024-11-17 14:44:03.582 - [任务 291][SqlServer] - Node SqlServer[bef9a994-aab1-4e44-8817-8fc193a4ec82] monitor closed 
[INFO ] 2024-11-17 14:44:03.788 - [任务 291][SqlServer] - Node SqlServer[bef9a994-aab1-4e44-8817-8fc193a4ec82] close complete, cost 37 ms 
[INFO ] 2024-11-17 14:44:07.087 - [任务 291] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-17 14:44:07.087 - [任务 291] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7d7d3f2 
[INFO ] 2024-11-17 14:44:07.093 - [任务 291] - Stop task milestones: 67398ce01939741a30f90655(任务 291)  
[INFO ] 2024-11-17 14:44:07.216 - [任务 291] - Stopped task aspect(s) 
[INFO ] 2024-11-17 14:44:07.216 - [任务 291] - Snapshot order controller have been removed 
[INFO ] 2024-11-17 14:44:07.244 - [任务 291] - Remove memory task client succeed, task: 任务 291[67398ce01939741a30f90655] 
[INFO ] 2024-11-17 14:44:07.244 - [任务 291] - Destroy memory task client cache succeed, task: 任务 291[67398ce01939741a30f90655] 
[INFO ] 2024-11-17 14:45:23.775 - [任务 291] - Task initialization... 
[INFO ] 2024-11-17 14:45:23.776 - [任务 291] - Start task milestones: 67398ce01939741a30f90655(任务 291) 
[INFO ] 2024-11-17 14:45:23.932 - [任务 291] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-17 14:45:24.021 - [任务 291] - The engine receives 任务 291 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-17 14:45:24.021 - [任务 291][SqlServer] - Node SqlServer[bef9a994-aab1-4e44-8817-8fc193a4ec82] start preload schema,table counts: 4 
[INFO ] 2024-11-17 14:45:24.021 - [任务 291][PG] - Node PG[224796cc-783d-42b6-97fb-24714e651438] start preload schema,table counts: 4 
[INFO ] 2024-11-17 14:45:24.021 - [任务 291][PG] - Node PG[224796cc-783d-42b6-97fb-24714e651438] preload schema finished, cost 0 ms 
[INFO ] 2024-11-17 14:45:24.021 - [任务 291][SqlServer] - Node SqlServer[bef9a994-aab1-4e44-8817-8fc193a4ec82] preload schema finished, cost 0 ms 
[INFO ] 2024-11-17 14:45:25.358 - [任务 291][SqlServer] - Node(SqlServer) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-17 14:45:25.358 - [任务 291][SqlServer] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-17 14:45:25.376 - [任务 291][PG] - Source node "PG" read batch size: 100 
[INFO ] 2024-11-17 14:45:25.376 - [任务 291][PG] - Source node "PG" event queue capacity: 200 
[INFO ] 2024-11-17 14:45:25.376 - [任务 291][PG] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-17 14:45:25.737 - [任务 291][SqlServer] - Will create master partition table [wim_num_new] to target, init sub partition list: [wim_num_new_0_100, wim_num_new_200_300] 
[INFO ] 2024-11-17 14:45:26.188 - [任务 291][SqlServer] - Exception skipping - The current exception does not match the skip exception strategy, message: Table model: TapTable id wim_num_new name wim_num_new storageEngine null charset null number of fields 3io.tapdata.entity.event.ddl.table.TapCreateTableEvent@199be7d4: {"partitionMasterTableId":"wim_num_new","table":{"ancestorsName":"wim_num_new","id":"wim_num_new","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"num"}],"name":"wim_num_new_pkey","primary":true,"unique":true}],"lastUpdate":1731824393959,"maxPKPos":2,"maxPos":3,"name":"wim_num_new","nameFieldMap":{"id":{"autoInc":false,"autoincrement":"NO","columnPosition":1,"createSource":"auto","dataType":"int","dataTypeTemp":"int","deleted":false,"fieldName":"id","id":"67398b0999d16ea391ec34e7","isNullable":false,"name":"id","nullable":false,"originalDataType":"integer","originalFieldName":"id","partitionKey":false,"pos":1,"previousDataType":"integer","previousFieldName":"id","primaryKey":true,"primaryKeyPos":1,"primaryKeyPosition":1,"pureDataType":"integer","source":"auto","sourceDbType":"SQL Server","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"unique":false,"virtual":false},"name":{"autoInc":false,"autoincrement":"NO","columnPosition":2,"createSource":"auto","dataType":"nvarchar(100)","dataTypeTemp":"nvarchar(100)","deleted":false,"fieldName":"name","id":"67398b0999d16ea391ec34e8","isNullable":true,"name":"name","nullable":true,"originalDataType":"character varying(100)","originalFieldName":"name","partitionKey":false,"pos":2,"previousDataType":"character varying(100)","previousFieldName":"name","primaryKey":false,"primaryKeyPos":0,"primaryKeyPosition":0,"pureDataType":"character varying","source":"auto","sourceDbType":"SQL Server","tapType":{"byteRatio":2,"bytes":100,"defaultValue":1,"type":10},"unique":false,"virtual":false},"num":{"autoInc":false,"autoincrement":"NO","columnPosition":3,"createSource":"auto","dataType":"int","dataTypeTemp":"int","deleted":false,"fieldName":"num","id":"67398b0999d16ea391ec34e9","isNullable":false,"name":"num","nullable":false,"originalDataType":"integer","originalFieldName":"num","partitionKey":false,"pos":3,"previousDataType":"integer","previousFieldName":"num","primaryKey":true,"primaryKeyPos":2,"primaryKeyPosition":2,"pureDataType":"integer","source":"auto","sourceDbType":"SQL Server","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"unique":false,"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"num"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"num":{"fieldAsc":true,"name":"num"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (num)","partitionFields":[{"autoInc":false,"dataType":"integer","name":"num","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM (0) TO (100)","tableName":"wim_num_new_0_100","tapPartitionTypes":[{"rangeFrom":{"originValue":"0","value":"0","valueType":"NORMAL"},"rangeTo":{"originValue":"100","value":"100","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (200) TO (300)","tableName":"wim_num_new_200_300","tapPartitionTypes":[{"rangeFrom":{"originValue":"200","value":"200","valueType":"NORMAL"},"rangeTo":{"originValue":"300","value":"300","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"wim_num_new"},"tableId":"wim_num_new","time":1731825925739,"type":206}
 
[ERROR] 2024-11-17 14:45:26.193 - [任务 291][SqlServer] - Table model: TapTable id wim_num_new name wim_num_new storageEngine null charset null number of fields 3io.tapdata.entity.event.ddl.table.TapCreateTableEvent@199be7d4: {"partitionMasterTableId":"wim_num_new","table":{"ancestorsName":"wim_num_new","id":"wim_num_new","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"num"}],"name":"wim_num_new_pkey","primary":true,"unique":true}],"lastUpdate":1731824393959,"maxPKPos":2,"maxPos":3,"name":"wim_num_new","nameFieldMap":{"id":{"autoInc":false,"autoincrement":"NO","columnPosition":1,"createSource":"auto","dataType":"int","dataTypeTemp":"int","deleted":false,"fieldName":"id","id":"67398b0999d16ea391ec34e7","isNullable":false,"name":"id","nullable":false,"originalDataType":"integer","originalFieldName":"id","partitionKey":false,"pos":1,"previousDataType":"integer","previousFieldName":"id","primaryKey":true,"primaryKeyPos":1,"primaryKeyPosition":1,"pureDataType":"integer","source":"auto","sourceDbType":"SQL Server","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"unique":false,"virtual":false},"name":{"autoInc":false,"autoincrement":"NO","columnPosition":2,"createSource":"auto","dataType":"nvarchar(100)","dataTypeTemp":"nvarchar(100)","deleted":false,"fieldName":"name","id":"67398b0999d16ea391ec34e8","isNullable":true,"name":"name","nullable":true,"originalDataType":"character varying(100)","originalFieldName":"name","partitionKey":false,"pos":2,"previousDataType":"character varying(100)","previousFieldName":"name","primaryKey":false,"primaryKeyPos":0,"primaryKeyPosition":0,"pureDataType":"character varying","source":"auto","sourceDbType":"SQL Server","tapType":{"byteRatio":2,"bytes":100,"defaultValue":1,"type":10},"unique":false,"virtual":false},"num":{"autoInc":false,"autoincrement":"NO","columnPosition":3,"createSource":"auto","dataType":"int","dataTypeTemp":"int","deleted":false,"fieldName":"num","id":"67398b0999d16ea391ec34e9","isNullable":false,"name":"num","nullable":false,"originalDataType":"integer","originalFieldName":"num","partitionKey":false,"pos":3,"previousDataType":"integer","previousFieldName":"num","primaryKey":true,"primaryKeyPos":2,"primaryKeyPosition":2,"pureDataType":"integer","source":"auto","sourceDbType":"SQL Server","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"unique":false,"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"num"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"num":{"fieldAsc":true,"name":"num"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (num)","partitionFields":[{"autoInc":false,"dataType":"integer","name":"num","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM (0) TO (100)","tableName":"wim_num_new_0_100","tapPartitionTypes":[{"rangeFrom":{"originValue":"0","value":"0","valueType":"NORMAL"},"rangeTo":{"originValue":"100","value":"100","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (200) TO (300)","tableName":"wim_num_new_200_300","tapPartitionTypes":[{"rangeFrom":{"originValue":"200","value":"200","valueType":"NORMAL"},"rangeTo":{"originValue":"300","value":"300","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"wim_num_new"},"tableId":"wim_num_new","time":1731825925739,"type":206}
 <-- Error Message -->
Table model: TapTable id wim_num_new name wim_num_new storageEngine null charset null number of fields 3io.tapdata.entity.event.ddl.table.TapCreateTableEvent@199be7d4: {"partitionMasterTableId":"wim_num_new","table":{"ancestorsName":"wim_num_new","id":"wim_num_new","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"num"}],"name":"wim_num_new_pkey","primary":true,"unique":true}],"lastUpdate":1731824393959,"maxPKPos":2,"maxPos":3,"name":"wim_num_new","nameFieldMap":{"id":{"autoInc":false,"autoincrement":"NO","columnPosition":1,"createSource":"auto","dataType":"int","dataTypeTemp":"int","deleted":false,"fieldName":"id","id":"67398b0999d16ea391ec34e7","isNullable":false,"name":"id","nullable":false,"originalDataType":"integer","originalFieldName":"id","partitionKey":false,"pos":1,"previousDataType":"integer","previousFieldName":"id","primaryKey":true,"primaryKeyPos":1,"primaryKeyPosition":1,"pureDataType":"integer","source":"auto","sourceDbType":"SQL Server","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"unique":false,"virtual":false},"name":{"autoInc":false,"autoincrement":"NO","columnPosition":2,"createSource":"auto","dataType":"nvarchar(100)","dataTypeTemp":"nvarchar(100)","deleted":false,"fieldName":"name","id":"67398b0999d16ea391ec34e8","isNullable":true,"name":"name","nullable":true,"originalDataType":"character varying(100)","originalFieldName":"name","partitionKey":false,"pos":2,"previousDataType":"character varying(100)","previousFieldName":"name","primaryKey":false,"primaryKeyPos":0,"primaryKeyPosition":0,"pureDataType":"character varying","source":"auto","sourceDbType":"SQL Server","tapType":{"byteRatio":2,"bytes":100,"defaultValue":1,"type":10},"unique":false,"virtual":false},"num":{"autoInc":false,"autoincrement":"NO","columnPosition":3,"createSource":"auto","dataType":"int","dataTypeTemp":"int","deleted":false,"fieldName":"num","id":"67398b0999d16ea391ec34e9","isNullable":false,"name":"num","nullable":false,"originalDataType":"integer","originalFieldName":"num","partitionKey":false,"pos":3,"previousDataType":"integer","previousFieldName":"num","primaryKey":true,"primaryKeyPos":2,"primaryKeyPosition":2,"pureDataType":"integer","source":"auto","sourceDbType":"SQL Server","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"unique":false,"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"num"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"num":{"fieldAsc":true,"name":"num"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (num)","partitionFields":[{"autoInc":false,"dataType":"integer","name":"num","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM (0) TO (100)","tableName":"wim_num_new_0_100","tapPartitionTypes":[{"rangeFrom":{"originValue":"0","value":"0","valueType":"NORMAL"},"rangeTo":{"originValue":"100","value":"100","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (200) TO (300)","tableName":"wim_num_new_200_300","tapPartitionTypes":[{"rangeFrom":{"originValue":"200","value":"200","valueType":"NORMAL"},"rangeTo":{"originValue":"300","value":"300","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"wim_num_new"},"tableId":"wim_num_new","time":1731825925739,"type":206}


<-- Simple Stack Trace -->
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: There is already an object named 'partition_function_wim_num_new' in the database.
	com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:265)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1662)
	com.microsoft.sqlserver.jdbc.SQLServerStatement.doExecuteStatement(SQLServerStatement.java:898)
	com.microsoft.sqlserver.jdbc.SQLServerStatement$StmtExecCmd.doExecute(SQLServerStatement.java:793)
	com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	...

<-- Full Stack Trace -->
Table model: TapTable id wim_num_new name wim_num_new storageEngine null charset null number of fields 3
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createTable(HazelcastTargetPdkBaseNode.java:460)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:226)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$3(HazelcastTargetPdkDataNode.java:174)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:160)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:112)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:238)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: code: 0 | message: Execute create table sql failed: There is already an object named 'partition_function_wim_num_new' in the database.
 Sql: CREATE PARTITION FUNCTION partition_function_wim_num_new (int) AS RANGE RIGHT FOR VALUES (0,200) 
 - CREATE PARTITION SCHEME partition_schema_wim_num_new     AS PARTITION partition_function_wim_num_new      ALL TO ('PRIMARY') 
 - IF OBJECT_ID('[TAPDATA].[dbo].[wim_num_new]', 'U') IS NULL 
create table [TAPDATA].[dbo].[wim_num_new](
  [id] int not null,
  [name] nvarchar(100),
  [num] int not null,
primary key nonclustered ([id],[num])
) ON partition_schema_wim_num_new (num)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$createPartitionTable$11(HazelcastTargetPdkBaseNode.java:334)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doCreateTable(HazelcastTargetPdkBaseNode.java:320)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPartitionTable(HazelcastTargetPdkBaseNode.java:330)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createTable(HazelcastTargetPdkBaseNode.java:422)
	... 19 more
Caused by: code: 0 | message: Execute create table sql failed: There is already an object named 'partition_function_wim_num_new' in the database.
 Sql: CREATE PARTITION FUNCTION partition_function_wim_num_new (int) AS RANGE RIGHT FOR VALUES (0,200) 
 - CREATE PARTITION SCHEME partition_schema_wim_num_new     AS PARTITION partition_function_wim_num_new      ALL TO ('PRIMARY') 
 - IF OBJECT_ID('[TAPDATA].[dbo].[wim_num_new]', 'U') IS NULL 
create table [TAPDATA].[dbo].[wim_num_new](
  [id] int not null,
  [name] nvarchar(100),
  [num] int not null,
primary key nonclustered ([id],[num])
) ON partition_schema_wim_num_new (num)
	at io.tapdata.connector.mssql.MssqlConnector.createPartitionTable(MssqlConnector.java:252)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$9(HazelcastTargetPdkBaseNode.java:341)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$10(HazelcastTargetPdkBaseNode.java:340)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 24 more
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: There is already an object named 'partition_function_wim_num_new' in the database.
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:265)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1662)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.doExecuteStatement(SQLServerStatement.java:898)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement$StmtExecCmd.doExecute(SQLServerStatement.java:793)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3488)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:262)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:237)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.execute(SQLServerStatement.java:766)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.connector.mssql.MssqlConnector.createPartitionTable(MssqlConnector.java:250)
	... 33 more

[INFO ] 2024-11-17 14:45:26.398 - [任务 291][SqlServer] - Job suspend in error handle 
[WARN ] 2024-11-17 14:45:26.603 - [任务 291][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-11-17 14:45:26.737 - [任务 291][PG] - new logical replication slot created, slotName:tapdata_cdc_5bb88e13_a8ca_42b0_8d2e_ed956b7ccde1 
[INFO ] 2024-11-17 14:45:26.754 - [任务 291][PG] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2024-11-17 14:45:26.754 - [任务 291][PG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-17 14:45:26.754 - [任务 291] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-11-17 14:45:26.916 - [任务 291][PG] - Initial sync started 
[INFO ] 2024-11-17 14:45:26.916 - [任务 291][PG] - Starting batch read, table name: wim_num_new 
[INFO ] 2024-11-17 14:45:26.917 - [任务 291][PG] - Table wim_num_new is going to be initial synced 
[INFO ] 2024-11-17 14:45:26.997 - [任务 291][PG] - Node PG[224796cc-783d-42b6-97fb-24714e651438] running status set to false 
[INFO ] 2024-11-17 14:45:27.023 - [任务 291][PG] - Initial sync completed 
[INFO ] 2024-11-17 14:45:27.023 - [任务 291][PG] - Query snapshot row size completed: PG(224796cc-783d-42b6-97fb-24714e651438) 
[ERROR] 2024-11-17 14:45:27.229 - [任务 291][PG] - java.lang.RuntimeException: java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.handleThrowable(HazelcastSourcePdkDataNode.java:440)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:409)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:485)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:493)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 13 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:146)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutHashSplit(CommonDbConnector.java:576)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:570)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:572)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 19 more
Caused by: java.lang.InterruptedException
	at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:944)
	at com.zaxxer.hikari.util.ConcurrentBag.borrow(ConcurrentBag.java:151)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:180)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:128)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 24 more

[INFO ] 2024-11-17 14:45:27.328 - [任务 291] - Task [任务 291] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-17 14:45:27.341 - [任务 291][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_224796cc-783d-42b6-97fb-24714e651438_1731825924614 
[INFO ] 2024-11-17 14:45:27.342 - [任务 291][PG] - PDK connector node released: HazelcastSourcePdkDataNode_224796cc-783d-42b6-97fb-24714e651438_1731825924614 
[INFO ] 2024-11-17 14:45:27.342 - [任务 291][PG] - Node PG[224796cc-783d-42b6-97fb-24714e651438] schema data cleaned 
[INFO ] 2024-11-17 14:45:27.342 - [任务 291][PG] - Node PG[224796cc-783d-42b6-97fb-24714e651438] monitor closed 
[INFO ] 2024-11-17 14:45:27.350 - [任务 291][PG] - Node PG[224796cc-783d-42b6-97fb-24714e651438] close complete, cost 356 ms 
[INFO ] 2024-11-17 14:45:27.350 - [任务 291][SqlServer] - Node SqlServer[bef9a994-aab1-4e44-8817-8fc193a4ec82] running status set to false 
[INFO ] 2024-11-17 14:45:27.363 - [任务 291][SqlServer] - PDK connector node stopped: HazelcastTargetPdkDataNode_bef9a994-aab1-4e44-8817-8fc193a4ec82_1731825924710 
[INFO ] 2024-11-17 14:45:27.363 - [任务 291][SqlServer] - PDK connector node released: HazelcastTargetPdkDataNode_bef9a994-aab1-4e44-8817-8fc193a4ec82_1731825924710 
[INFO ] 2024-11-17 14:45:27.364 - [任务 291][SqlServer] - Node SqlServer[bef9a994-aab1-4e44-8817-8fc193a4ec82] schema data cleaned 
[INFO ] 2024-11-17 14:45:27.364 - [任务 291][SqlServer] - Node SqlServer[bef9a994-aab1-4e44-8817-8fc193a4ec82] monitor closed 
[INFO ] 2024-11-17 14:45:27.565 - [任务 291][SqlServer] - Node SqlServer[bef9a994-aab1-4e44-8817-8fc193a4ec82] close complete, cost 14 ms 
[INFO ] 2024-11-17 14:45:32.336 - [任务 291] - Task [任务 291] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-17 14:45:32.349 - [任务 291] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-17 14:45:32.349 - [任务 291] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@52185f3f 
[INFO ] 2024-11-17 14:45:32.366 - [任务 291] - Stop task milestones: 67398ce01939741a30f90655(任务 291)  
[INFO ] 2024-11-17 14:45:32.523 - [任务 291] - Stopped task aspect(s) 
[INFO ] 2024-11-17 14:45:32.523 - [任务 291] - Snapshot order controller have been removed 
[INFO ] 2024-11-17 14:45:32.540 - [任务 291] - Remove memory task client succeed, task: 任务 291[67398ce01939741a30f90655] 
[INFO ] 2024-11-17 14:45:32.547 - [任务 291] - Destroy memory task client cache succeed, task: 任务 291[67398ce01939741a30f90655] 
[INFO ] 2024-11-17 14:50:29.548 - [任务 291] - Task initialization... 
[INFO ] 2024-11-17 14:50:29.549 - [任务 291] - Start task milestones: 67398ce01939741a30f90655(任务 291) 
[INFO ] 2024-11-17 14:50:29.698 - [任务 291] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-17 14:50:29.788 - [任务 291] - The engine receives 任务 291 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-17 14:50:29.789 - [任务 291][SqlServer] - Node SqlServer[bef9a994-aab1-4e44-8817-8fc193a4ec82] start preload schema,table counts: 4 
[INFO ] 2024-11-17 14:50:29.789 - [任务 291][PG] - Node PG[224796cc-783d-42b6-97fb-24714e651438] start preload schema,table counts: 4 
[INFO ] 2024-11-17 14:50:29.789 - [任务 291][SqlServer] - Node SqlServer[bef9a994-aab1-4e44-8817-8fc193a4ec82] preload schema finished, cost 0 ms 
[INFO ] 2024-11-17 14:50:29.991 - [任务 291][PG] - Node PG[224796cc-783d-42b6-97fb-24714e651438] preload schema finished, cost 0 ms 
[INFO ] 2024-11-17 14:50:30.965 - [任务 291][SqlServer] - Node(SqlServer) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-17 14:50:31.171 - [任务 291][SqlServer] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-17 14:50:31.245 - [任务 291][PG] - Source node "PG" read batch size: 100 
[INFO ] 2024-11-17 14:50:31.245 - [任务 291][PG] - Source node "PG" event queue capacity: 200 
[INFO ] 2024-11-17 14:50:31.245 - [任务 291][PG] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-17 14:50:31.311 - [任务 291][SqlServer] - Will create master partition table [wim_num_new] to target, init sub partition list: [wim_num_new_0_100, wim_num_new_200_300] 
[INFO ] 2024-11-17 14:50:32.271 - [任务 291][SqlServer] - Will create master partition table [wim_parition] to target, init sub partition list: [wim_parition_202410, wim_parition_202411, wim_parition_202412, wim_parition_202401, wim_parition_202403] 
[WARN ] 2024-11-17 14:50:32.271 - [任务 291][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-11-17 14:50:32.377 - [任务 291][PG] - new logical replication slot created, slotName:tapdata_cdc_56b49585_fd43_45af_b89a_81d307b23d05 
[INFO ] 2024-11-17 14:50:32.398 - [任务 291][PG] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2024-11-17 14:50:32.398 - [任务 291][PG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-17 14:50:32.398 - [任务 291] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-11-17 14:50:32.516 - [任务 291][PG] - Initial sync started 
[INFO ] 2024-11-17 14:50:32.516 - [任务 291][PG] - Starting batch read, table name: wim_num_new 
[INFO ] 2024-11-17 14:50:32.714 - [任务 291][PG] - Table wim_num_new is going to be initial synced 
[INFO ] 2024-11-17 14:50:32.716 - [任务 291][PG] - Table [wim_num_new] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-17 14:50:32.716 - [任务 291][PG] - Starting batch read, table name: wim_parition 
[INFO ] 2024-11-17 14:50:32.716 - [任务 291][PG] - Table wim_parition is going to be initial synced 
[INFO ] 2024-11-17 14:50:32.907 - [任务 291][PG] - Table [wim_parition] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-17 14:50:32.909 - [任务 291][PG] - Starting batch read, table name: wim_num 
[INFO ] 2024-11-17 14:50:32.909 - [任务 291][PG] - Table wim_num is going to be initial synced 
[INFO ] 2024-11-17 14:50:33.019 - [任务 291][PG] - Table [wim_num] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-17 14:50:33.022 - [任务 291][PG] - Starting batch read, table name: wim_parition_da 
[INFO ] 2024-11-17 14:50:33.022 - [任务 291][PG] - Table wim_parition_da is going to be initial synced 
[INFO ] 2024-11-17 14:50:33.106 - [任务 291][PG] - Query snapshot row size completed: PG(224796cc-783d-42b6-97fb-24714e651438) 
[INFO ] 2024-11-17 14:50:33.106 - [任务 291][PG] - Table [wim_parition_da] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-17 14:50:33.106 - [任务 291][PG] - Initial sync completed 
[INFO ] 2024-11-17 14:50:33.106 - [任务 291][PG] - Incremental sync starting... 
[INFO ] 2024-11-17 14:50:33.107 - [任务 291][PG] - Initial sync completed 
[INFO ] 2024-11-17 14:50:33.107 - [任务 291][PG] - Starting stream read, table list: [wim_num_new, wim_parition, wim_num, wim_parition_da], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[WARN ] 2024-11-17 14:50:33.140 - [任务 291][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-11-17 14:50:33.141 - [任务 291][SqlServer] - Will create master partition table [wim_num] to target, init sub partition list: [wim_num600_700, wim_num800_900, wim_num900_1000, wim_num1000_1100, wim_num300_400, wim_num100_200, wim_num200_300, wim_num1100_1200, wim_num1200_1300, wim_num1300_1400, wim_num0_100, wim_num400_500, wim_num500_600] 
[INFO ] 2024-11-17 14:50:33.346 - [任务 291][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_56b49585_fd43_45af_b89a_81d307b23d05 
[INFO ] 2024-11-17 14:50:34.162 - [任务 291][SqlServer] - Will create master partition table [wim_parition_da] to target, init sub partition list: [wim_parition_da_202410, wim_parition_da_202407, wim_parition_da_202411, wim_parition_da_202412, wim_parition_da_202409, wim_parition_da_202405, wim_parition_da_202404, wim_parition_da_202406, wim_parition_da_202408] 
[INFO ] 2024-11-17 14:50:34.328 - [任务 291][PG] - Connector PostgreSQL incremental start succeed, tables: [wim_num_new, wim_parition, wim_num, wim_parition_da, wim_num_new_0_100, wim_num_new_200_300, wim_parition_202410, wim_parition_202411, wim_parition_202412, wim_parition_202401, wim_parition_202403, wim_num600_700, wim_num800_900, wim_num900_1000, wim_num1000_1100, wim_num300_400, wim_num100_200, wim_num200_300, wim_num1100_1200, wim_num1200_1300, wim_num1300_1400, wim_num0_100, wim_num400_500, wim_num500_600, wim_parition_da_202410, wim_parition_da_202407, wim_parition_da_202411, wim_parition_da_202412, wim_parition_da_202409, wim_parition_da_202405, wim_parition_da_202404, wim_parition_da_202406, wim_parition_da_202408], data change syncing 
[INFO ] 2024-11-17 14:57:32.718 - [任务 291][PG] - Found new table(s): [wim_parition_da_202403, wim_parition_da] 
[INFO ] 2024-11-17 14:57:33.097 - [任务 291][PG] - Load new table(s) schema finished, loaded schema count: 1 
[WARN ] 2024-11-17 14:57:33.100 - [任务 291][PG] - It is expected to load 2 new table models, and 1 table models no longer exist and will be ignored. The table name(s) that does not exist: [wim_parition_da] 
[INFO ] 2024-11-17 14:57:33.114 - [任务 291][PG] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@685e410f: {"partitionMasterTableId":"wim_parition_da","table":{"comment":"","id":"wim_parition_da_202403","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"createdat"}],"name":"wim_parition_da_202403_pkey","primary":true,"unique":true}],"lastUpdate":1731826653099,"maxPKPos":2,"maxPos":3,"name":"wim_parition_da_202403","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"dataType":"character varying(100)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"character varying","tapType":{"bytes":100,"defaultValue":10485760,"type":10},"virtual":false},"createdat":{"autoInc":false,"dataType":"timestamp without time zone","name":"createdat","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"timestamp without time zone","tapType":{"defaultFraction":6,"fraction":6,"max":"9999-12-31T23:59:59Z","min":"1000-01-01T00:00:00Z","type":1,"withTimeZone":false},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"createdat"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"createdat":{"fieldAsc":true,"name":"createdat"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (createdat)","partitionFields":[{"autoInc":false,"dataType":"timestamp without time zone","name":"createdat","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-08-01 00:00:00') TO ('2024-08-31 00:00:00')","tableName":"wim_parition_da_202408","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-08-01 00:00:00'","value":"'2024-08-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-08-31 00:00:00'","value":"'2024-08-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-10-01 00:00:00') TO ('2024-10-31 00:00:00')","tableName":"wim_parition_da_202410","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-10-01 00:00:00'","value":"'2024-10-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-10-31 00:00:00'","value":"'2024-10-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-07-01 00:00:00') TO ('2024-07-31 00:00:00')","tableName":"wim_parition_da_202407","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-07-01 00:00:00'","value":"'2024-07-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-07-31 00:00:00'","value":"'2024-07-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-11-01 00:00:00') TO ('2024-11-30 00:00:00')","tableName":"wim_parition_da_202411","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-11-01 00:00:00'","value":"'2024-11-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-11-30 00:00:00'","value":"'2024-11-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-09-01 00:00:00') TO ('2024-09-30 00:00:00')","tableName":"wim_parition_da_202409","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-09-01 00:00:00'","value":"'2024-09-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-09-30 00:00:00'","value":"'2024-09-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-06-01 00:00:00') TO ('2024-06-30 00:00:00')","tableName":"wim_parition_da_202406","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-06-01 00:00:00'","value":"'2024-06-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-06-30 00:00:00'","value":"'2024-06-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-05-01 00:00:00') TO ('2024-05-30 00:00:00')","tableName":"wim_parition_da_202405","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-05-01 00:00:00'","value":"'2024-05-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-05-30 00:00:00'","value":"'2024-05-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-04-01 00:00:00') TO ('2024-04-30 00:00:00')","tableName":"wim_parition_da_202404","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-04-01 00:00:00'","value":"'2024-04-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-04-30 00:00:00'","value":"'2024-04-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-03-01 00:00:00') TO ('2024-03-30 00:00:00')","tableName":"wim_parition_da_202403","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-03-01 00:00:00'","value":"'2024-03-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-03-30 00:00:00'","value":"'2024-03-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-12-01 00:00:00') TO ('2024-12-30 00:00:00')","tableName":"wim_parition_da_202412","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-12-01 00:00:00'","value":"'2024-12-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-30 00:00:00'","value":"'2024-12-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"wim_parition_da"},"tableId":"wim_parition_da_202403","type":206} 
[INFO ] 2024-11-17 14:57:33.114 - [任务 291][PG] - Sync sub table's [wim_parition_da_202403] create table ddl, will add update master table [wim_parition_da] metadata 
[INFO ] 2024-11-17 14:57:33.218 - [任务 291][PG] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_wim_parition_da_202403_673762ee83338516ea176572_67398ce01939741a30f90655 
[INFO ] 2024-11-17 14:57:33.218 - [任务 291][PG] - Create new table schema transform finished: TapTable id wim_parition_da_202403 name wim_parition_da_202403 storageEngine null charset null number of fields 3 
[INFO ] 2024-11-17 14:57:33.426 - [任务 291][PG] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-11-17 14:57:33.724 - [任务 291][SqlServer] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@685e410f: {"partitionMasterTableId":"wim_parition_da","table":{"comment":"","id":"wim_parition_da_202403","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"createdat"}],"name":"wim_parition_da_202403_pkey","primary":true,"unique":true}],"lastUpdate":1731826653099,"maxPKPos":2,"maxPos":3,"name":"wim_parition_da_202403","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"dataType":"character varying(100)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"character varying","tapType":{"bytes":100,"defaultValue":10485760,"type":10},"virtual":false},"createdat":{"autoInc":false,"dataType":"timestamp(6) without time zone","name":"createdat","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"timestamp without time zone","tapType":{"defaultFraction":6,"fraction":6,"max":"9999-12-31T23:59:59Z","min":"1000-01-01T00:00:00Z","type":1,"withTimeZone":false},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"createdat"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"createdat":{"fieldAsc":true,"name":"createdat"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (createdat)","partitionFields":[{"autoInc":false,"dataType":"timestamp without time zone","name":"createdat","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"tapType":{"defaultFraction":6,"fraction":6,"max":"9999-12-31T23:59:59Z","min":"1000-01-01T00:00:00Z","type":1,"withTimeZone":false},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-08-01 00:00:00') TO ('2024-08-31 00:00:00')","tableName":"wim_parition_da_202408","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-08-01 00:00:00'","value":"'2024-08-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-08-31 00:00:00'","value":"'2024-08-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-10-01 00:00:00') TO ('2024-10-31 00:00:00')","tableName":"wim_parition_da_202410","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-10-01 00:00:00'","value":"'2024-10-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-10-31 00:00:00'","value":"'2024-10-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-07-01 00:00:00') TO ('2024-07-31 00:00:00')","tableName":"wim_parition_da_202407","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-07-01 00:00:00'","value":"'2024-07-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-07-31 00:00:00'","value":"'2024-07-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-11-01 00:00:00') TO ('2024-11-30 00:00:00')","tableName":"wim_parition_da_202411","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-11-01 00:00:00'","value":"'2024-11-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-11-30 00:00:00'","value":"'2024-11-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-09-01 00:00:00') TO ('2024-09-30 00:00:00')","tableName":"wim_parition_da_202409","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-09-01 00:00:00'","value":"'2024-09-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-09-30 00:00:00'","value":"'2024-09-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-06-01 00:00:00') TO ('2024-06-30 00:00:00')","tableName":"wim_parition_da_202406","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-06-01 00:00:00'","value":"'2024-06-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-06-30 00:00:00'","value":"'2024-06-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-05-01 00:00:00') TO ('2024-05-30 00:00:00')","tableName":"wim_parition_da_202405","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-05-01 00:00:00'","value":"'2024-05-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-05-30 00:00:00'","value":"'2024-05-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-04-01 00:00:00') TO ('2024-04-30 00:00:00')","tableName":"wim_parition_da_202404","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-04-01 00:00:00'","value":"'2024-04-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-04-30 00:00:00'","value":"'2024-04-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-03-01 00:00:00') TO ('2024-03-30 00:00:00')","tableName":"wim_parition_da_202403","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-03-01 00:00:00'","value":"'2024-03-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-03-30 00:00:00'","value":"'2024-03-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-12-01 00:00:00') TO ('2024-12-30 00:00:00')","tableName":"wim_parition_da_202412","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-12-01 00:00:00'","value":"'2024-12-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-30 00:00:00'","value":"'2024-12-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"wim_parition_da"},"tableId":"wim_parition_da_202403","type":206}). Wait for all previous events to be processed 
[INFO ] 2024-11-17 14:57:33.725 - [任务 291][PG] - Incremental sync completed 
[INFO ] 2024-11-17 14:57:34.128 - [任务 291][SqlServer] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@685e410f: {"partitionMasterTableId":"wim_parition_da","table":{"comment":"","id":"wim_parition_da_202403","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"createdat"}],"name":"wim_parition_da_202403_pkey","primary":true,"unique":true}],"lastUpdate":1731826653099,"maxPKPos":2,"maxPos":3,"name":"wim_parition_da_202403","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"dataType":"character varying(100)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"character varying","tapType":{"bytes":100,"defaultValue":10485760,"type":10},"virtual":false},"createdat":{"autoInc":false,"dataType":"timestamp(6) without time zone","name":"createdat","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"timestamp without time zone","tapType":{"defaultFraction":6,"fraction":6,"max":"9999-12-31T23:59:59Z","min":"1000-01-01T00:00:00Z","type":1,"withTimeZone":false},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"createdat"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"createdat":{"fieldAsc":true,"name":"createdat"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (createdat)","partitionFields":[{"autoInc":false,"dataType":"timestamp without time zone","name":"createdat","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"tapType":{"defaultFraction":7,"fraction":6,"max":"9999-12-31T23:59:59Z","min":"0001-01-01T00:00:00Z","type":1},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-08-01 00:00:00') TO ('2024-08-31 00:00:00')","tableName":"wim_parition_da_202408","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-08-01 00:00:00'","value":"'2024-08-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-08-31 00:00:00'","value":"'2024-08-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-10-01 00:00:00') TO ('2024-10-31 00:00:00')","tableName":"wim_parition_da_202410","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-10-01 00:00:00'","value":"'2024-10-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-10-31 00:00:00'","value":"'2024-10-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-07-01 00:00:00') TO ('2024-07-31 00:00:00')","tableName":"wim_parition_da_202407","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-07-01 00:00:00'","value":"'2024-07-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-07-31 00:00:00'","value":"'2024-07-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-11-01 00:00:00') TO ('2024-11-30 00:00:00')","tableName":"wim_parition_da_202411","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-11-01 00:00:00'","value":"'2024-11-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-11-30 00:00:00'","value":"'2024-11-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-09-01 00:00:00') TO ('2024-09-30 00:00:00')","tableName":"wim_parition_da_202409","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-09-01 00:00:00'","value":"'2024-09-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-09-30 00:00:00'","value":"'2024-09-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-06-01 00:00:00') TO ('2024-06-30 00:00:00')","tableName":"wim_parition_da_202406","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-06-01 00:00:00'","value":"'2024-06-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-06-30 00:00:00'","value":"'2024-06-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-05-01 00:00:00') TO ('2024-05-30 00:00:00')","tableName":"wim_parition_da_202405","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-05-01 00:00:00'","value":"'2024-05-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-05-30 00:00:00'","value":"'2024-05-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-04-01 00:00:00') TO ('2024-04-30 00:00:00')","tableName":"wim_parition_da_202404","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-04-01 00:00:00'","value":"'2024-04-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-04-30 00:00:00'","value":"'2024-04-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-03-01 00:00:00') TO ('2024-03-30 00:00:00')","tableName":"wim_parition_da_202403","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-03-01 00:00:00'","value":"'2024-03-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-03-30 00:00:00'","value":"'2024-03-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-12-01 00:00:00') TO ('2024-12-30 00:00:00')","tableName":"wim_parition_da_202412","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-12-01 00:00:00'","value":"'2024-12-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-30 00:00:00'","value":"'2024-12-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"wim_parition_da"},"tableId":"wim_parition_da_202403","type":206}) 
[INFO ] 2024-11-17 14:57:34.555 - [任务 291][PG] - Starting batch read, table name: wim_parition_da_202403 
[INFO ] 2024-11-17 14:57:34.555 - [任务 291][SqlServer] - Will create sub partition table [wim_parition_da_202403] to target, master table is: wim_parition_da 
[INFO ] 2024-11-17 14:57:34.628 - [任务 291][PG] - Table wim_parition_da_202403 is going to be initial synced 
[INFO ] 2024-11-17 14:57:34.628 - [任务 291][PG] - Query snapshot row size completed: PG(224796cc-783d-42b6-97fb-24714e651438) 
[INFO ] 2024-11-17 14:57:34.723 - [任务 291][PG] - Table [wim_parition_da_202403] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-17 14:57:34.723 - [任务 291][PG] - Initial sync completed 
[INFO ] 2024-11-17 14:57:34.724 - [任务 291][PG] - Incremental sync starting... 
[INFO ] 2024-11-17 14:57:34.724 - [任务 291][PG] - Initial sync completed 
[INFO ] 2024-11-17 14:57:34.731 - [任务 291][PG] - Starting stream read, table list: [wim_num_new, wim_parition, wim_num, wim_parition_da, wim_parition_da_202403], offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":621769048,\"lsn_commit\":621769048,\"lsn\":621769048,\"ts_usec\":1731826593646492}"} 
[WARN ] 2024-11-17 14:57:34.732 - [任务 291][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-11-17 14:57:34.934 - [任务 291][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_56b49585_fd43_45af_b89a_81d307b23d05 
[INFO ] 2024-11-17 14:57:35.082 - [任务 291][SqlServer] - The table wim_parition_da_202403 has already exist. 
[INFO ] 2024-11-17 14:57:36.097 - [任务 291][PG] - Connector PostgreSQL incremental start succeed, tables: [wim_num_new, wim_parition, wim_num, wim_parition_da, wim_parition_da_202403, wim_num_new_0_100, wim_num_new_200_300, wim_parition_202410, wim_parition_202411, wim_parition_202412, wim_parition_202401, wim_parition_202403, wim_num600_700, wim_num800_900, wim_num900_1000, wim_num1000_1100, wim_num300_400, wim_num100_200, wim_num200_300, wim_num1100_1200, wim_num1200_1300, wim_num1300_1400, wim_num0_100, wim_num400_500, wim_num500_600, wim_parition_da_202410, wim_parition_da_202407, wim_parition_da_202411, wim_parition_da_202412, wim_parition_da_202409, wim_parition_da_202405, wim_parition_da_202404, wim_parition_da_202406, wim_parition_da_202408], data change syncing 
[INFO ] 2024-11-17 15:01:32.570 - [任务 291][PG] - Found new table(s): [wim_num1400_1500, wim_num] 
[INFO ] 2024-11-17 15:01:32.966 - [任务 291][PG] - Load new table(s) schema finished, loaded schema count: 1 
[WARN ] 2024-11-17 15:01:32.970 - [任务 291][PG] - It is expected to load 2 new table models, and 1 table models no longer exist and will be ignored. The table name(s) that does not exist: [wim_num] 
[INFO ] 2024-11-17 15:01:32.991 - [任务 291][PG] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@5012b53b: {"partitionMasterTableId":"wim_num","table":{"comment":"","id":"wim_num1400_1500","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"num"}],"name":"wim_num1400_1500_pkey","primary":true,"unique":true}],"lastUpdate":1731826892969,"maxPKPos":2,"maxPos":3,"name":"wim_num1400_1500","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"dataType":"character varying(100)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"character varying","tapType":{"bytes":100,"defaultValue":10485760,"type":10},"virtual":false},"num":{"autoInc":false,"dataType":"integer","name":"num","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"num"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"num":{"fieldAsc":true,"name":"num"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (num)","partitionFields":[{"autoInc":false,"dataType":"integer","name":"num","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM (400) TO (500)","tableName":"wim_num400_500","tapPartitionTypes":[{"rangeFrom":{"originValue":"400","value":"400","valueType":"NORMAL"},"rangeTo":{"originValue":"500","value":"500","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (500) TO (600)","tableName":"wim_num500_600","tapPartitionTypes":[{"rangeFrom":{"originValue":"500","value":"500","valueType":"NORMAL"},"rangeTo":{"originValue":"600","value":"600","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (600) TO (700)","tableName":"wim_num600_700","tapPartitionTypes":[{"rangeFrom":{"originValue":"600","value":"600","valueType":"NORMAL"},"rangeTo":{"originValue":"700","value":"700","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (800) TO (900)","tableName":"wim_num800_900","tapPartitionTypes":[{"rangeFrom":{"originValue":"800","value":"800","valueType":"NORMAL"},"rangeTo":{"originValue":"900","value":"900","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (900) TO (1000)","tableName":"wim_num900_1000","tapPartitionTypes":[{"rangeFrom":{"originValue":"900","value":"900","valueType":"NORMAL"},"rangeTo":{"originValue":"1000","value":"1000","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (1000) TO (1100)","tableName":"wim_num1000_1100","tapPartitionTypes":[{"rangeFrom":{"originValue":"1000","value":"1000","valueType":"NORMAL"},"rangeTo":{"originValue":"1100","value":"1100","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (300) TO (400)","tableName":"wim_num300_400","tapPartitionTypes":[{"rangeFrom":{"originValue":"300","value":"300","valueType":"NORMAL"},"rangeTo":{"originValue":"400","value":"400","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (100) TO (200)","tableName":"wim_num100_200","tapPartitionTypes":[{"rangeFrom":{"originValue":"100","value":"100","valueType":"NORMAL"},"rangeTo":{"originValue":"200","value":"200","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (200) TO (300)","tableName":"wim_num200_300","tapPartitionTypes":[{"rangeFrom":{"originValue":"200","value":"200","valueType":"NORMAL"},"rangeTo":{"originValue":"300","value":"300","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (1100) TO (1200)","tableName":"wim_num1100_1200","tapPartitionTypes":[{"rangeFrom":{"originValue":"1100","value":"1100","valueType":"NORMAL"},"rangeTo":{"originValue":"1200","value":"1200","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (1200) TO (1300)","tableName":"wim_num1200_1300","tapPartitionTypes":[{"rangeFrom":{"originValue":"1200","value":"1200","valueType":"NORMAL"},"rangeTo":{"originValue":"1300","value":"1300","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (1300) TO (1400)","tableName":"wim_num1300_1400","tapPartitionTypes":[{"rangeFrom":{"originValue":"1300","value":"1300","valueType":"NORMAL"},"rangeTo":{"originValue":"1400","value":"1400","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (1400) TO (1500)","tableName":"wim_num1400_1500","tapPartitionTypes":[{"rangeFrom":{"originValue":"1400","value":"1400","valueType":"NORMAL"},"rangeTo":{"originValue":"1500","value":"1500","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (0) TO (100)","tableName":"wim_num0_100","tapPartitionTypes":[{"rangeFrom":{"originValue":"0","value":"0","valueType":"NORMAL"},"rangeTo":{"originValue":"100","value":"100","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"wim_num"},"tableId":"wim_num1400_1500","type":206} 
[INFO ] 2024-11-17 15:01:32.991 - [任务 291][PG] - Sync sub table's [wim_num1400_1500] create table ddl, will add update master table [wim_num] metadata 
[INFO ] 2024-11-17 15:01:33.070 - [任务 291][PG] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_wim_num1400_1500_673762ee83338516ea176572_67398ce01939741a30f90655 
[INFO ] 2024-11-17 15:01:33.071 - [任务 291][PG] - Create new table schema transform finished: TapTable id wim_num1400_1500 name wim_num1400_1500 storageEngine null charset null number of fields 3 
[INFO ] 2024-11-17 15:01:33.071 - [任务 291][PG] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-11-17 15:01:33.571 - [任务 291][PG] - Incremental sync completed 
[INFO ] 2024-11-17 15:01:33.573 - [任务 291][SqlServer] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@5012b53b: {"partitionMasterTableId":"wim_num","table":{"comment":"","id":"wim_num1400_1500","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"num"}],"name":"wim_num1400_1500_pkey","primary":true,"unique":true}],"lastUpdate":1731826892969,"maxPKPos":2,"maxPos":3,"name":"wim_num1400_1500","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"dataType":"character varying(100)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"character varying","tapType":{"bytes":100,"defaultValue":10485760,"type":10},"virtual":false},"num":{"autoInc":false,"dataType":"integer","name":"num","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"num"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"num":{"fieldAsc":true,"name":"num"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (num)","partitionFields":[{"autoInc":false,"dataType":"integer","name":"num","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM (400) TO (500)","tableName":"wim_num400_500","tapPartitionTypes":[{"rangeFrom":{"originValue":"400","value":"400","valueType":"NORMAL"},"rangeTo":{"originValue":"500","value":"500","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (500) TO (600)","tableName":"wim_num500_600","tapPartitionTypes":[{"rangeFrom":{"originValue":"500","value":"500","valueType":"NORMAL"},"rangeTo":{"originValue":"600","value":"600","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (600) TO (700)","tableName":"wim_num600_700","tapPartitionTypes":[{"rangeFrom":{"originValue":"600","value":"600","valueType":"NORMAL"},"rangeTo":{"originValue":"700","value":"700","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (800) TO (900)","tableName":"wim_num800_900","tapPartitionTypes":[{"rangeFrom":{"originValue":"800","value":"800","valueType":"NORMAL"},"rangeTo":{"originValue":"900","value":"900","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (900) TO (1000)","tableName":"wim_num900_1000","tapPartitionTypes":[{"rangeFrom":{"originValue":"900","value":"900","valueType":"NORMAL"},"rangeTo":{"originValue":"1000","value":"1000","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (1000) TO (1100)","tableName":"wim_num1000_1100","tapPartitionTypes":[{"rangeFrom":{"originValue":"1000","value":"1000","valueType":"NORMAL"},"rangeTo":{"originValue":"1100","value":"1100","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (300) TO (400)","tableName":"wim_num300_400","tapPartitionTypes":[{"rangeFrom":{"originValue":"300","value":"300","valueType":"NORMAL"},"rangeTo":{"originValue":"400","value":"400","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (100) TO (200)","tableName":"wim_num100_200","tapPartitionTypes":[{"rangeFrom":{"originValue":"100","value":"100","valueType":"NORMAL"},"rangeTo":{"originValue":"200","value":"200","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (200) TO (300)","tableName":"wim_num200_300","tapPartitionTypes":[{"rangeFrom":{"originValue":"200","value":"200","valueType":"NORMAL"},"rangeTo":{"originValue":"300","value":"300","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (1100) TO (1200)","tableName":"wim_num1100_1200","tapPartitionTypes":[{"rangeFrom":{"originValue":"1100","value":"1100","valueType":"NORMAL"},"rangeTo":{"originValue":"1200","value":"1200","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (1200) TO (1300)","tableName":"wim_num1200_1300","tapPartitionTypes":[{"rangeFrom":{"originValue":"1200","value":"1200","valueType":"NORMAL"},"rangeTo":{"originValue":"1300","value":"1300","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (1300) TO (1400)","tableName":"wim_num1300_1400","tapPartitionTypes":[{"rangeFrom":{"originValue":"1300","value":"1300","valueType":"NORMAL"},"rangeTo":{"originValue":"1400","value":"1400","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (1400) TO (1500)","tableName":"wim_num1400_1500","tapPartitionTypes":[{"rangeFrom":{"originValue":"1400","value":"1400","valueType":"NORMAL"},"rangeTo":{"originValue":"1500","value":"1500","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (0) TO (100)","tableName":"wim_num0_100","tapPartitionTypes":[{"rangeFrom":{"originValue":"0","value":"0","valueType":"NORMAL"},"rangeTo":{"originValue":"100","value":"100","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"wim_num"},"tableId":"wim_num1400_1500","type":206}). Wait for all previous events to be processed 
[INFO ] 2024-11-17 15:01:34.182 - [任务 291][SqlServer] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@5012b53b: {"partitionMasterTableId":"wim_num","table":{"comment":"","id":"wim_num1400_1500","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"num"}],"name":"wim_num1400_1500_pkey","primary":true,"unique":true}],"lastUpdate":1731826892969,"maxPKPos":2,"maxPos":3,"name":"wim_num1400_1500","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"dataType":"character varying(100)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"character varying","tapType":{"bytes":100,"defaultValue":10485760,"type":10},"virtual":false},"num":{"autoInc":false,"dataType":"integer","name":"num","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"num"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"num":{"fieldAsc":true,"name":"num"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (num)","partitionFields":[{"autoInc":false,"dataType":"integer","name":"num","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM (400) TO (500)","tableName":"wim_num400_500","tapPartitionTypes":[{"rangeFrom":{"originValue":"400","value":"400","valueType":"NORMAL"},"rangeTo":{"originValue":"500","value":"500","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (500) TO (600)","tableName":"wim_num500_600","tapPartitionTypes":[{"rangeFrom":{"originValue":"500","value":"500","valueType":"NORMAL"},"rangeTo":{"originValue":"600","value":"600","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (600) TO (700)","tableName":"wim_num600_700","tapPartitionTypes":[{"rangeFrom":{"originValue":"600","value":"600","valueType":"NORMAL"},"rangeTo":{"originValue":"700","value":"700","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (800) TO (900)","tableName":"wim_num800_900","tapPartitionTypes":[{"rangeFrom":{"originValue":"800","value":"800","valueType":"NORMAL"},"rangeTo":{"originValue":"900","value":"900","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (900) TO (1000)","tableName":"wim_num900_1000","tapPartitionTypes":[{"rangeFrom":{"originValue":"900","value":"900","valueType":"NORMAL"},"rangeTo":{"originValue":"1000","value":"1000","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (1000) TO (1100)","tableName":"wim_num1000_1100","tapPartitionTypes":[{"rangeFrom":{"originValue":"1000","value":"1000","valueType":"NORMAL"},"rangeTo":{"originValue":"1100","value":"1100","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (300) TO (400)","tableName":"wim_num300_400","tapPartitionTypes":[{"rangeFrom":{"originValue":"300","value":"300","valueType":"NORMAL"},"rangeTo":{"originValue":"400","value":"400","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (100) TO (200)","tableName":"wim_num100_200","tapPartitionTypes":[{"rangeFrom":{"originValue":"100","value":"100","valueType":"NORMAL"},"rangeTo":{"originValue":"200","value":"200","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (200) TO (300)","tableName":"wim_num200_300","tapPartitionTypes":[{"rangeFrom":{"originValue":"200","value":"200","valueType":"NORMAL"},"rangeTo":{"originValue":"300","value":"300","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (1100) TO (1200)","tableName":"wim_num1100_1200","tapPartitionTypes":[{"rangeFrom":{"originValue":"1100","value":"1100","valueType":"NORMAL"},"rangeTo":{"originValue":"1200","value":"1200","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (1200) TO (1300)","tableName":"wim_num1200_1300","tapPartitionTypes":[{"rangeFrom":{"originValue":"1200","value":"1200","valueType":"NORMAL"},"rangeTo":{"originValue":"1300","value":"1300","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (1300) TO (1400)","tableName":"wim_num1300_1400","tapPartitionTypes":[{"rangeFrom":{"originValue":"1300","value":"1300","valueType":"NORMAL"},"rangeTo":{"originValue":"1400","value":"1400","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (1400) TO (1500)","tableName":"wim_num1400_1500","tapPartitionTypes":[{"rangeFrom":{"originValue":"1400","value":"1400","valueType":"NORMAL"},"rangeTo":{"originValue":"1500","value":"1500","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM (0) TO (100)","tableName":"wim_num0_100","tapPartitionTypes":[{"rangeFrom":{"originValue":"0","value":"0","valueType":"NORMAL"},"rangeTo":{"originValue":"100","value":"100","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"wim_num"},"tableId":"wim_num1400_1500","type":206}) 
[INFO ] 2024-11-17 15:01:34.430 - [任务 291][PG] - Starting batch read, table name: wim_num1400_1500 
[INFO ] 2024-11-17 15:01:34.430 - [任务 291][PG] - Table wim_num1400_1500 is going to be initial synced 
[INFO ] 2024-11-17 15:01:34.515 - [任务 291][PG] - Query snapshot row size completed: PG(224796cc-783d-42b6-97fb-24714e651438) 
[INFO ] 2024-11-17 15:01:34.610 - [任务 291][SqlServer] - Will create sub partition table [wim_num1400_1500] to target, master table is: wim_num 
[INFO ] 2024-11-17 15:01:34.610 - [任务 291][PG] - Table [wim_num1400_1500] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-17 15:01:34.612 - [任务 291][PG] - Initial sync completed 
[INFO ] 2024-11-17 15:01:34.612 - [任务 291][PG] - Incremental sync starting... 
[INFO ] 2024-11-17 15:01:34.612 - [任务 291][PG] - Initial sync completed 
[INFO ] 2024-11-17 15:01:34.613 - [任务 291][PG] - Starting stream read, table list: [wim_num_new, wim_parition, wim_parition_da_202403, wim_num1400_1500, wim_num, wim_parition_da], offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":621929512,\"lsn_commit\":621929512,\"lsn\":621929512,\"ts_usec\":1731826751467195}"} 
[WARN ] 2024-11-17 15:01:34.614 - [任务 291][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-11-17 15:01:34.814 - [任务 291][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_56b49585_fd43_45af_b89a_81d307b23d05 
[INFO ] 2024-11-17 15:01:35.218 - [任务 291][SqlServer] - The table wim_num1400_1500 has already exist. 
[INFO ] 2024-11-17 15:01:35.802 - [任务 291][PG] - Connector PostgreSQL incremental start succeed, tables: [wim_num_new, wim_parition, wim_parition_da_202403, wim_num1400_1500, wim_num, wim_parition_da, wim_num_new_0_100, wim_num_new_200_300, wim_parition_202410, wim_parition_202411, wim_parition_202412, wim_parition_202401, wim_parition_202403, wim_num600_700, wim_num800_900, wim_num900_1000, wim_num1000_1100, wim_num300_400, wim_num100_200, wim_num200_300, wim_num1100_1200, wim_num1200_1300, wim_num1300_1400, wim_num0_100, wim_num400_500, wim_num500_600, wim_parition_da_202410, wim_parition_da_202407, wim_parition_da_202411, wim_parition_da_202412, wim_parition_da_202409, wim_parition_da_202405, wim_parition_da_202404, wim_parition_da_202406, wim_parition_da_202408], data change syncing 
[INFO ] 2024-11-17 15:12:32.610 - [任务 291][PG] - Found new table(s): [wim_parition_da_202402, wim_parition_da] 
[INFO ] 2024-11-17 15:12:33.519 - [任务 291][PG] - Load new table(s) schema finished, loaded schema count: 1 
[WARN ] 2024-11-17 15:12:33.524 - [任务 291][PG] - It is expected to load 2 new table models, and 1 table models no longer exist and will be ignored. The table name(s) that does not exist: [wim_parition_da] 
[INFO ] 2024-11-17 15:12:33.572 - [任务 291][PG] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@54754092: {"partitionMasterTableId":"wim_parition_da","table":{"comment":"","id":"wim_parition_da_202402","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"createdat"}],"name":"wim_parition_da_202402_pkey","primary":true,"unique":true}],"lastUpdate":1731827553522,"maxPKPos":2,"maxPos":3,"name":"wim_parition_da_202402","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"dataType":"character varying(100)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"character varying","tapType":{"bytes":100,"defaultValue":10485760,"type":10},"virtual":false},"createdat":{"autoInc":false,"dataType":"timestamp without time zone","name":"createdat","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"timestamp without time zone","tapType":{"defaultFraction":6,"fraction":6,"max":"9999-12-31T23:59:59Z","min":"1000-01-01T00:00:00Z","type":1,"withTimeZone":false},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"createdat"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"createdat":{"fieldAsc":true,"name":"createdat"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (createdat)","partitionFields":[{"autoInc":false,"dataType":"timestamp without time zone","name":"createdat","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-08-01 00:00:00') TO ('2024-08-31 00:00:00')","tableName":"wim_parition_da_202408","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-08-01 00:00:00'","value":"'2024-08-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-08-31 00:00:00'","value":"'2024-08-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-10-01 00:00:00') TO ('2024-10-31 00:00:00')","tableName":"wim_parition_da_202410","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-10-01 00:00:00'","value":"'2024-10-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-10-31 00:00:00'","value":"'2024-10-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-07-01 00:00:00') TO ('2024-07-31 00:00:00')","tableName":"wim_parition_da_202407","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-07-01 00:00:00'","value":"'2024-07-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-07-31 00:00:00'","value":"'2024-07-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-11-01 00:00:00') TO ('2024-11-30 00:00:00')","tableName":"wim_parition_da_202411","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-11-01 00:00:00'","value":"'2024-11-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-11-30 00:00:00'","value":"'2024-11-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-12-01 00:00:00') TO ('2024-12-30 00:00:00')","tableName":"wim_parition_da_202412","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-12-01 00:00:00'","value":"'2024-12-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-30 00:00:00'","value":"'2024-12-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-09-01 00:00:00') TO ('2024-09-30 00:00:00')","tableName":"wim_parition_da_202409","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-09-01 00:00:00'","value":"'2024-09-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-09-30 00:00:00'","value":"'2024-09-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-05-01 00:00:00') TO ('2024-05-30 00:00:00')","tableName":"wim_parition_da_202405","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-05-01 00:00:00'","value":"'2024-05-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-05-30 00:00:00'","value":"'2024-05-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-04-01 00:00:00') TO ('2024-04-30 00:00:00')","tableName":"wim_parition_da_202404","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-04-01 00:00:00'","value":"'2024-04-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-04-30 00:00:00'","value":"'2024-04-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-03-01 00:00:00') TO ('2024-03-30 00:00:00')","tableName":"wim_parition_da_202403","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-03-01 00:00:00'","value":"'2024-03-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-03-30 00:00:00'","value":"'2024-03-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-02-01 00:00:00') TO ('2024-02-28 00:00:00')","tableName":"wim_parition_da_202402","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-02-01 00:00:00'","value":"'2024-02-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-02-28 00:00:00'","value":"'2024-02-28 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-06-01 00:00:00') TO ('2024-06-30 00:00:00')","tableName":"wim_parition_da_202406","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-06-01 00:00:00'","value":"'2024-06-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-06-30 00:00:00'","value":"'2024-06-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"wim_parition_da"},"tableId":"wim_parition_da_202402","type":206} 
[INFO ] 2024-11-17 15:12:33.574 - [任务 291][PG] - Sync sub table's [wim_parition_da_202402] create table ddl, will add update master table [wim_parition_da] metadata 
[INFO ] 2024-11-17 15:12:33.664 - [任务 291][PG] - Create new table in memory, qualified name: T_postgres_io_tapdata_1_0-SNAPSHOT_wim_parition_da_202402_673762ee83338516ea176572_67398ce01939741a30f90655 
[INFO ] 2024-11-17 15:12:33.664 - [任务 291][PG] - Create new table schema transform finished: TapTable id wim_parition_da_202402 name wim_parition_da_202402 storageEngine null charset null number of fields 3 
[INFO ] 2024-11-17 15:12:33.781 - [任务 291][PG] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-11-17 15:12:33.781 - [任务 291][PG] - Incremental sync completed 
[INFO ] 2024-11-17 15:12:34.237 - [任务 291][SqlServer] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@54754092: {"partitionMasterTableId":"wim_parition_da","table":{"comment":"","id":"wim_parition_da_202402","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"createdat"}],"name":"wim_parition_da_202402_pkey","primary":true,"unique":true}],"lastUpdate":1731827553522,"maxPKPos":2,"maxPos":3,"name":"wim_parition_da_202402","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"dataType":"character varying(100)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"character varying","tapType":{"bytes":100,"defaultValue":10485760,"type":10},"virtual":false},"createdat":{"autoInc":false,"dataType":"timestamp(6) without time zone","name":"createdat","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"timestamp without time zone","tapType":{"defaultFraction":6,"fraction":6,"max":"9999-12-31T23:59:59Z","min":"1000-01-01T00:00:00Z","type":1,"withTimeZone":false},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"createdat"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"createdat":{"fieldAsc":true,"name":"createdat"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (createdat)","partitionFields":[{"autoInc":false,"dataType":"timestamp without time zone","name":"createdat","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"tapType":{"defaultFraction":6,"fraction":6,"max":"9999-12-31T23:59:59Z","min":"1000-01-01T00:00:00Z","type":1,"withTimeZone":false},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-08-01 00:00:00') TO ('2024-08-31 00:00:00')","tableName":"wim_parition_da_202408","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-08-01 00:00:00'","value":"'2024-08-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-08-31 00:00:00'","value":"'2024-08-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-10-01 00:00:00') TO ('2024-10-31 00:00:00')","tableName":"wim_parition_da_202410","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-10-01 00:00:00'","value":"'2024-10-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-10-31 00:00:00'","value":"'2024-10-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-07-01 00:00:00') TO ('2024-07-31 00:00:00')","tableName":"wim_parition_da_202407","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-07-01 00:00:00'","value":"'2024-07-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-07-31 00:00:00'","value":"'2024-07-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-11-01 00:00:00') TO ('2024-11-30 00:00:00')","tableName":"wim_parition_da_202411","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-11-01 00:00:00'","value":"'2024-11-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-11-30 00:00:00'","value":"'2024-11-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-12-01 00:00:00') TO ('2024-12-30 00:00:00')","tableName":"wim_parition_da_202412","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-12-01 00:00:00'","value":"'2024-12-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-30 00:00:00'","value":"'2024-12-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-09-01 00:00:00') TO ('2024-09-30 00:00:00')","tableName":"wim_parition_da_202409","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-09-01 00:00:00'","value":"'2024-09-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-09-30 00:00:00'","value":"'2024-09-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-05-01 00:00:00') TO ('2024-05-30 00:00:00')","tableName":"wim_parition_da_202405","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-05-01 00:00:00'","value":"'2024-05-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-05-30 00:00:00'","value":"'2024-05-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-04-01 00:00:00') TO ('2024-04-30 00:00:00')","tableName":"wim_parition_da_202404","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-04-01 00:00:00'","value":"'2024-04-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-04-30 00:00:00'","value":"'2024-04-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-03-01 00:00:00') TO ('2024-03-30 00:00:00')","tableName":"wim_parition_da_202403","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-03-01 00:00:00'","value":"'2024-03-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-03-30 00:00:00'","value":"'2024-03-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-02-01 00:00:00') TO ('2024-02-28 00:00:00')","tableName":"wim_parition_da_202402","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-02-01 00:00:00'","value":"'2024-02-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-02-28 00:00:00'","value":"'2024-02-28 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-06-01 00:00:00') TO ('2024-06-30 00:00:00')","tableName":"wim_parition_da_202406","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-06-01 00:00:00'","value":"'2024-06-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-06-30 00:00:00'","value":"'2024-06-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"wim_parition_da"},"tableId":"wim_parition_da_202402","type":206}). Wait for all previous events to be processed 
[INFO ] 2024-11-17 15:12:34.237 - [任务 291][SqlServer] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@54754092: {"partitionMasterTableId":"wim_parition_da","table":{"comment":"","id":"wim_parition_da_202402","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"createdat"}],"name":"wim_parition_da_202402_pkey","primary":true,"unique":true}],"lastUpdate":1731827553522,"maxPKPos":2,"maxPos":3,"name":"wim_parition_da_202402","nameFieldMap":{"id":{"autoInc":false,"dataType":"integer","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"pureDataType":"integer","tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"dataType":"character varying(100)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"pureDataType":"character varying","tapType":{"bytes":100,"defaultValue":10485760,"type":10},"virtual":false},"createdat":{"autoInc":false,"dataType":"timestamp(6) without time zone","name":"createdat","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"pureDataType":"timestamp without time zone","tapType":{"defaultFraction":6,"fraction":6,"max":"9999-12-31T23:59:59Z","min":"1000-01-01T00:00:00Z","type":1,"withTimeZone":false},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"},{"fieldAsc":true,"name":"createdat"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"},"createdat":{"fieldAsc":true,"name":"createdat"}},"unique":true},"partitionInfo":{"invalidType":false,"originPartitionStageSQL":"RANGE (createdat)","partitionFields":[{"autoInc":false,"dataType":"timestamp without time zone","name":"createdat","nullable":false,"partitionKey":false,"pos":3,"primaryKey":true,"primaryKeyPos":2,"tapType":{"defaultFraction":7,"fraction":6,"max":"9999-12-31T23:59:59Z","min":"0001-01-01T00:00:00Z","type":1},"virtual":false}],"subPartitionTableInfo":[{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-08-01 00:00:00') TO ('2024-08-31 00:00:00')","tableName":"wim_parition_da_202408","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-08-01 00:00:00'","value":"'2024-08-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-08-31 00:00:00'","value":"'2024-08-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-10-01 00:00:00') TO ('2024-10-31 00:00:00')","tableName":"wim_parition_da_202410","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-10-01 00:00:00'","value":"'2024-10-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-10-31 00:00:00'","value":"'2024-10-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-07-01 00:00:00') TO ('2024-07-31 00:00:00')","tableName":"wim_parition_da_202407","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-07-01 00:00:00'","value":"'2024-07-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-07-31 00:00:00'","value":"'2024-07-31 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-11-01 00:00:00') TO ('2024-11-30 00:00:00')","tableName":"wim_parition_da_202411","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-11-01 00:00:00'","value":"'2024-11-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-11-30 00:00:00'","value":"'2024-11-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-12-01 00:00:00') TO ('2024-12-30 00:00:00')","tableName":"wim_parition_da_202412","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-12-01 00:00:00'","value":"'2024-12-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-12-30 00:00:00'","value":"'2024-12-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-09-01 00:00:00') TO ('2024-09-30 00:00:00')","tableName":"wim_parition_da_202409","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-09-01 00:00:00'","value":"'2024-09-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-09-30 00:00:00'","value":"'2024-09-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-05-01 00:00:00') TO ('2024-05-30 00:00:00')","tableName":"wim_parition_da_202405","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-05-01 00:00:00'","value":"'2024-05-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-05-30 00:00:00'","value":"'2024-05-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-04-01 00:00:00') TO ('2024-04-30 00:00:00')","tableName":"wim_parition_da_202404","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-04-01 00:00:00'","value":"'2024-04-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-04-30 00:00:00'","value":"'2024-04-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-03-01 00:00:00') TO ('2024-03-30 00:00:00')","tableName":"wim_parition_da_202403","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-03-01 00:00:00'","value":"'2024-03-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-03-30 00:00:00'","value":"'2024-03-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-02-01 00:00:00') TO ('2024-02-28 00:00:00')","tableName":"wim_parition_da_202402","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-02-01 00:00:00'","value":"'2024-02-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-02-28 00:00:00'","value":"'2024-02-28 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]},{"originPartitionBoundSQL":"FOR VALUES FROM ('2024-06-01 00:00:00') TO ('2024-06-30 00:00:00')","tableName":"wim_parition_da_202406","tapPartitionTypes":[{"rangeFrom":{"originValue":"'2024-06-01 00:00:00'","value":"'2024-06-01 00:00:00'","valueType":"NORMAL"},"rangeTo":{"originValue":"'2024-06-30 00:00:00'","value":"'2024-06-30 00:00:00'","valueType":"NORMAL"},"type":"RANGE"}]}],"type":"RANGE"},"partitionMasterTableId":"wim_parition_da"},"tableId":"wim_parition_da_202402","type":206}) 
[INFO ] 2024-11-17 15:12:34.550 - [任务 291][PG] - Starting batch read, table name: wim_parition_da_202402 
[INFO ] 2024-11-17 15:12:34.550 - [任务 291][PG] - Table wim_parition_da_202402 is going to be initial synced 
[INFO ] 2024-11-17 15:12:34.734 - [任务 291][PG] - Query snapshot row size completed: PG(224796cc-783d-42b6-97fb-24714e651438) 
[INFO ] 2024-11-17 15:12:34.734 - [任务 291][SqlServer] - Will create sub partition table [wim_parition_da_202402] to target, master table is: wim_parition_da 
[INFO ] 2024-11-17 15:12:34.780 - [任务 291][PG] - Table [wim_parition_da_202402] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-17 15:12:34.780 - [任务 291][PG] - Initial sync completed 
[INFO ] 2024-11-17 15:12:34.780 - [任务 291][PG] - Incremental sync starting... 
[INFO ] 2024-11-17 15:12:34.780 - [任务 291][PG] - Initial sync completed 
[INFO ] 2024-11-17 15:12:34.781 - [任务 291][PG] - Starting stream read, table list: [wim_num_new, wim_parition, wim_parition_da_202402, wim_parition_da_202403, wim_num1400_1500, wim_num, wim_parition_da], offset: {"sortString":null,"offsetValue":null,"sourceOffset":"{\"lsn_proc\":622104256,\"lsn_commit\":622104256,\"lsn\":622104256,\"ts_usec\":1731827452071042}"} 
[WARN ] 2024-11-17 15:12:34.782 - [任务 291][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-11-17 15:12:34.983 - [任务 291][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_56b49585_fd43_45af_b89a_81d307b23d05 
[INFO ] 2024-11-17 15:12:35.760 - [任务 291][SqlServer] - The table wim_parition_da_202402 has already exist. 
[INFO ] 2024-11-17 15:12:36.739 - [任务 291][PG] - Connector PostgreSQL incremental start succeed, tables: [wim_num_new, wim_parition, wim_parition_da_202402, wim_parition_da_202403, wim_num1400_1500, wim_num, wim_parition_da, wim_num_new_0_100, wim_num_new_200_300, wim_parition_202410, wim_parition_202411, wim_parition_202412, wim_parition_202401, wim_parition_202403, wim_num600_700, wim_num800_900, wim_num900_1000, wim_num1000_1100, wim_num300_400, wim_num100_200, wim_num200_300, wim_num1100_1200, wim_num1200_1300, wim_num1300_1400, wim_num0_100, wim_num400_500, wim_num500_600, wim_parition_da_202410, wim_parition_da_202407, wim_parition_da_202411, wim_parition_da_202412, wim_parition_da_202409, wim_parition_da_202405, wim_parition_da_202404, wim_parition_da_202406, wim_parition_da_202408], data change syncing 
[INFO ] 2024-11-17 15:27:28.790 - [任务 291][PG] - Node PG[224796cc-783d-42b6-97fb-24714e651438] running status set to false 
[INFO ] 2024-11-17 15:27:28.791 - [任务 291][PG] - Incremental sync completed 
[INFO ] 2024-11-17 15:27:28.795 - [任务 291][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_224796cc-783d-42b6-97fb-24714e651438_1731827553833 
[INFO ] 2024-11-17 15:27:28.796 - [任务 291][PG] - PDK connector node released: HazelcastSourcePdkDataNode_224796cc-783d-42b6-97fb-24714e651438_1731827553833 
[INFO ] 2024-11-17 15:27:28.796 - [任务 291][PG] - Node PG[224796cc-783d-42b6-97fb-24714e651438] schema data cleaned 
[INFO ] 2024-11-17 15:27:28.796 - [任务 291][PG] - Node PG[224796cc-783d-42b6-97fb-24714e651438] monitor closed 
[INFO ] 2024-11-17 15:27:28.799 - [任务 291][PG] - Node PG[224796cc-783d-42b6-97fb-24714e651438] close complete, cost 34 ms 
[INFO ] 2024-11-17 15:27:28.799 - [任务 291][SqlServer] - Node SqlServer[bef9a994-aab1-4e44-8817-8fc193a4ec82] running status set to false 
[INFO ] 2024-11-17 15:27:28.816 - [任务 291][SqlServer] - PDK connector node stopped: HazelcastTargetPdkDataNode_bef9a994-aab1-4e44-8817-8fc193a4ec82_1731826230357 
[INFO ] 2024-11-17 15:27:28.816 - [任务 291][SqlServer] - PDK connector node released: HazelcastTargetPdkDataNode_bef9a994-aab1-4e44-8817-8fc193a4ec82_1731826230357 
[INFO ] 2024-11-17 15:27:28.817 - [任务 291][SqlServer] - Node SqlServer[bef9a994-aab1-4e44-8817-8fc193a4ec82] schema data cleaned 
[INFO ] 2024-11-17 15:27:28.817 - [任务 291][SqlServer] - Node SqlServer[bef9a994-aab1-4e44-8817-8fc193a4ec82] monitor closed 
[INFO ] 2024-11-17 15:27:29.020 - [任务 291][SqlServer] - Node SqlServer[bef9a994-aab1-4e44-8817-8fc193a4ec82] close complete, cost 18 ms 
[INFO ] 2024-11-17 15:27:29.792 - [任务 291] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-17 15:27:29.792 - [任务 291] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5c3cd371 
[INFO ] 2024-11-17 15:27:29.932 - [任务 291] - Stop task milestones: 67398ce01939741a30f90655(任务 291)  
[INFO ] 2024-11-17 15:27:29.932 - [任务 291] - Stopped task aspect(s) 
[INFO ] 2024-11-17 15:27:29.932 - [任务 291] - Snapshot order controller have been removed 
[INFO ] 2024-11-17 15:27:29.954 - [任务 291] - Remove memory task client succeed, task: 任务 291[67398ce01939741a30f90655] 
[INFO ] 2024-11-17 15:27:29.958 - [任务 291] - Destroy memory task client cache succeed, task: 任务 291[67398ce01939741a30f90655] 
