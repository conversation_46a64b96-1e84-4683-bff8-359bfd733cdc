[INFO ] 2024-07-24 16:50:59.451 - [任务 26] - load tapTable task 66a0c057f604e81d788d0875-9799375f-7f83-40bd-9d9c-d27ededa655f complete, cost 1113ms 
[INFO ] 2024-07-24 16:50:59.731 - [任务 26] - load tapTable task 66a0c057f604e81d788d0875-92e11d9f-2c42-48b5-8c8f-a43a05b1477b complete, cost 496ms 
[INFO ] 2024-07-24 16:51:01.817 - [任务 26] - load tapTable task 66a0c057f604e81d788d0875-227ae6fc-0213-483c-bceb-a83eec46db48 complete, cost 3469ms 
[INFO ] 2024-07-24 16:51:23.941 - [任务 26] - load tapTable task 66a0c057f604e81d788d0875-06641070-9b81-4bbb-bda1-b87290f76a03 complete, cost 417ms 
[INFO ] 2024-07-24 16:51:28.188 - [任务 26] - load tapTable task 66a0c057f604e81d788d0875-05d94bc7-70e2-4b19-ab3e-d8d37b9a8005 complete, cost 336ms 
[INFO ] 2024-07-24 16:51:31.182 - [任务 26] - Task initialization... 
[INFO ] 2024-07-24 16:51:31.185 - [任务 26] - Start task milestones: 66a0c057f604e81d788d0876(任务 26) 
[INFO ] 2024-07-24 16:51:31.699 - [任务 26] - load tapTable task 66a0c057f604e81d788d0875-b1fd381b-ce1c-4e82-8d77-f5dee6e749d7 complete, cost 418ms 
[INFO ] 2024-07-24 16:51:31.761 - [任务 26] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 16:51:31.891 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 16:51:31.892 - [任务 26][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:51:31.892 - [任务 26][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:51:31.892 - [任务 26][Modules] - Node Modules[5bdba8b0-39a4-4381-9890-6284e6ab896d] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:51:31.892 - [任务 26][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:51:31.892 - [任务 26][Modules] - Node Modules[5bdba8b0-39a4-4381-9890-6284e6ab896d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:51:31.892 - [任务 26][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:51:32.466 - [任务 26][Modules] - Source node "Modules" read batch size: 100 
[INFO ] 2024-07-24 16:51:32.468 - [任务 26][Modules] - Source node "Modules" event queue capacity: 200 
[INFO ] 2024-07-24 16:51:32.471 - [任务 26][Modules] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-24 16:51:32.667 - [任务 26][Modules] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-24 16:51:32.667 - [任务 26][Modules] - batch offset found: {},stream offset found: {"cdcOffset":1721811092,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-24 16:51:32.734 - [任务 26][Modules] - Initial sync started 
[INFO ] 2024-07-24 16:51:32.735 - [任务 26][Modules] - Starting batch read, table name: Modules, offset: null 
[INFO ] 2024-07-24 16:51:32.735 - [任务 26][Modules] - Table Modules is going to be initial synced 
[INFO ] 2024-07-24 16:51:32.896 - [任务 26][Modules] - Query table 'Modules' counts: 19 
[INFO ] 2024-07-24 16:51:32.896 - [任务 26][Modules] - Table [Modules] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 16:51:32.903 - [任务 26][Modules] - Initial sync completed 
[INFO ] 2024-07-24 16:51:32.904 - [任务 26][Modules] - Incremental sync starting... 
[INFO ] 2024-07-24 16:51:32.904 - [任务 26][Modules] - Initial sync completed 
[INFO ] 2024-07-24 16:51:32.928 - [任务 26][Modules] - Starting stream read, table list: [Modules], offset: {"cdcOffset":1721811092,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-24 16:51:32.928 - [任务 26][Modules] - Connector MongoDB incremental start succeed, tables: [Modules], data change syncing 
[INFO ] 2024-07-24 16:58:15.149 - [任务 26][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] running status set to false 
[INFO ] 2024-07-24 16:58:15.150 - [任务 26][Modules] - PDK connector node stopped: HazelcastSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0 
[INFO ] 2024-07-24 16:58:15.150 - [任务 26][Modules] - PDK connector node released: HazelcastSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0 
[INFO ] 2024-07-24 16:58:15.150 - [任务 26][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] schema data cleaned 
[INFO ] 2024-07-24 16:58:15.150 - [任务 26][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] monitor closed 
[INFO ] 2024-07-24 16:58:15.151 - [任务 26][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] close complete, cost 33 ms 
[INFO ] 2024-07-24 16:58:15.157 - [任务 26][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] running status set to false 
[INFO ] 2024-07-24 16:58:15.157 - [任务 26][增强JS] - PDK connector node stopped: ScriptExecutor-MiddleMongo184-3f2bbd02-9189-419c-ae61-421d3194c57f 
[INFO ] 2024-07-24 16:58:15.157 - [任务 26][增强JS] - PDK connector node released: ScriptExecutor-MiddleMongo184-3f2bbd02-9189-419c-ae61-421d3194c57f 
[INFO ] 2024-07-24 16:58:15.157 - [任务 26][增强JS] - [ScriptExecutorsManager-66a0c057f604e81d788d0876-c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c-66a0bbeaf604e81d788d05a8] schema data cleaned 
[INFO ] 2024-07-24 16:58:15.162 - [任务 26][增强JS] - PDK connector node stopped: ScriptExecutor-Mongo-a804b2a7-078d-441e-96a3-14dc3f6c3d28 
[INFO ] 2024-07-24 16:58:15.162 - [任务 26][增强JS] - PDK connector node released: ScriptExecutor-Mongo-a804b2a7-078d-441e-96a3-14dc3f6c3d28 
[INFO ] 2024-07-24 16:58:15.162 - [任务 26][增强JS] - [ScriptExecutorsManager-66a0c057f604e81d788d0876-c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c-6695b8966d76494ed53f3874] schema data cleaned 
[INFO ] 2024-07-24 16:58:15.191 - [任务 26][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] schema data cleaned 
[INFO ] 2024-07-24 16:58:15.193 - [任务 26][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] monitor closed 
[INFO ] 2024-07-24 16:58:15.198 - [任务 26][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] close complete, cost 45 ms 
[INFO ] 2024-07-24 16:58:15.198 - [任务 26][Modules] - Node Modules[5bdba8b0-39a4-4381-9890-6284e6ab896d] running status set to false 
[INFO ] 2024-07-24 16:58:15.204 - [任务 26][Modules] - PDK connector node stopped: HazelcastTargetPdkDataNode-5bdba8b0-39a4-4381-9890-6284e6ab896d 
[INFO ] 2024-07-24 16:58:15.204 - [任务 26][Modules] - PDK connector node released: HazelcastTargetPdkDataNode-5bdba8b0-39a4-4381-9890-6284e6ab896d 
[INFO ] 2024-07-24 16:58:15.205 - [任务 26][Modules] - Node Modules[5bdba8b0-39a4-4381-9890-6284e6ab896d] schema data cleaned 
[INFO ] 2024-07-24 16:58:15.205 - [任务 26][Modules] - Node Modules[5bdba8b0-39a4-4381-9890-6284e6ab896d] monitor closed 
[INFO ] 2024-07-24 16:58:15.410 - [任务 26][Modules] - Node Modules[5bdba8b0-39a4-4381-9890-6284e6ab896d] close complete, cost 7 ms 
[INFO ] 2024-07-24 16:58:17.235 - [任务 26][Modules] - Incremental sync completed 
[INFO ] 2024-07-24 16:58:17.903 - [任务 26] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 16:58:17.904 - [任务 26] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@23fbf5c4 
[INFO ] 2024-07-24 16:58:18.035 - [任务 26] - Stop task milestones: 66a0c057f604e81d788d0876(任务 26)  
[INFO ] 2024-07-24 16:58:18.043 - [任务 26] - Stopped task aspect(s) 
[INFO ] 2024-07-24 16:58:18.043 - [任务 26] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 16:58:18.063 - [任务 26] - Remove memory task client succeed, task: 任务 26[66a0c057f604e81d788d0876] 
[INFO ] 2024-07-24 16:58:18.066 - [任务 26] - Destroy memory task client cache succeed, task: 任务 26[66a0c057f604e81d788d0876] 
[INFO ] 2024-07-24 16:58:25.414 - [任务 26] - Task initialization... 
[INFO ] 2024-07-24 16:58:25.586 - [任务 26] - Start task milestones: 66a0c057f604e81d788d0876(任务 26) 
[INFO ] 2024-07-24 16:58:26.145 - [任务 26] - load tapTable task 66a0c057f604e81d788d0875-af19dfe5-9b2c-4a76-9028-d7f0c5eb8c9b complete, cost 610ms 
[INFO ] 2024-07-24 16:58:26.294 - [任务 26] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 16:58:26.294 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 16:58:26.367 - [任务 26][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:58:26.368 - [任务 26][Modules] - Node Modules[5bdba8b0-39a4-4381-9890-6284e6ab896d] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:58:26.368 - [任务 26][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] start preload schema,table counts: 1 
[INFO ] 2024-07-24 16:58:26.368 - [任务 26][Modules] - Node Modules[5bdba8b0-39a4-4381-9890-6284e6ab896d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:58:26.368 - [任务 26][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:58:26.368 - [任务 26][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:58:26.693 - [任务 26][Modules] - Source node "Modules" read batch size: 100 
[INFO ] 2024-07-24 16:58:26.693 - [任务 26][Modules] - Source node "Modules" event queue capacity: 200 
[INFO ] 2024-07-24 16:58:26.810 - [任务 26][Modules] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-24 16:58:26.810 - [任务 26][Modules] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-24 16:58:26.971 - [任务 26][Modules] - batch offset found: {},stream offset found: {"cdcOffset":1721811506,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-24 16:58:27.069 - [任务 26][Modules] - Initial sync started 
[INFO ] 2024-07-24 16:58:27.069 - [任务 26][Modules] - Starting batch read, table name: Modules, offset: null 
[INFO ] 2024-07-24 16:58:27.070 - [任务 26][Modules] - Table Modules is going to be initial synced 
[INFO ] 2024-07-24 16:58:27.252 - [任务 26][Modules] - Query table 'Modules' counts: 19 
[INFO ] 2024-07-24 16:58:27.252 - [任务 26][Modules] - Table [Modules] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 16:58:27.261 - [任务 26][Modules] - Initial sync completed 
[INFO ] 2024-07-24 16:58:27.261 - [任务 26][Modules] - Incremental sync starting... 
[INFO ] 2024-07-24 16:58:27.261 - [任务 26][Modules] - Initial sync completed 
[INFO ] 2024-07-24 16:58:27.287 - [任务 26][Modules] - Starting stream read, table list: [Modules], offset: {"cdcOffset":1721811506,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-24 16:58:27.288 - [任务 26][Modules] - Connector MongoDB incremental start succeed, tables: [Modules], data change syncing 
[INFO ] 2024-07-24 16:58:43.566 - [任务 26][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] running status set to false 
[INFO ] 2024-07-24 16:58:43.587 - [任务 26][Modules] - PDK connector node stopped: HazelcastSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0 
[INFO ] 2024-07-24 16:58:43.588 - [任务 26][Modules] - PDK connector node released: HazelcastSourcePdkDataNode-5312c60e-d637-42cb-9114-ebc0840588b0 
[INFO ] 2024-07-24 16:58:43.588 - [任务 26][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] schema data cleaned 
[INFO ] 2024-07-24 16:58:43.588 - [任务 26][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] monitor closed 
[INFO ] 2024-07-24 16:58:43.589 - [任务 26][Modules] - Node Modules[5312c60e-d637-42cb-9114-ebc0840588b0] close complete, cost 29 ms 
[INFO ] 2024-07-24 16:58:43.589 - [任务 26][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] running status set to false 
[INFO ] 2024-07-24 16:58:43.590 - [任务 26][增强JS] - PDK connector node stopped: ScriptExecutor-MiddleMongo184-c267adf8-30dd-4d1f-8d67-b802ea755bd7 
[INFO ] 2024-07-24 16:58:43.590 - [任务 26][增强JS] - PDK connector node released: ScriptExecutor-MiddleMongo184-c267adf8-30dd-4d1f-8d67-b802ea755bd7 
[INFO ] 2024-07-24 16:58:43.590 - [任务 26][增强JS] - [ScriptExecutorsManager-66a0c057f604e81d788d0876-c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c-66a0bbeaf604e81d788d05a8] schema data cleaned 
[INFO ] 2024-07-24 16:58:43.592 - [任务 26][增强JS] - PDK connector node stopped: ScriptExecutor-Mongo-59008839-8e8e-48be-92ec-cf1716cd6fcc 
[INFO ] 2024-07-24 16:58:43.592 - [任务 26][增强JS] - PDK connector node released: ScriptExecutor-Mongo-59008839-8e8e-48be-92ec-cf1716cd6fcc 
[INFO ] 2024-07-24 16:58:43.622 - [任务 26][增强JS] - [ScriptExecutorsManager-66a0c057f604e81d788d0876-c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c-6695b8966d76494ed53f3874] schema data cleaned 
[INFO ] 2024-07-24 16:58:43.624 - [任务 26][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] schema data cleaned 
[INFO ] 2024-07-24 16:58:43.624 - [任务 26][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] monitor closed 
[INFO ] 2024-07-24 16:58:43.625 - [任务 26][增强JS] - Node 增强JS[c00f4a17-7d55-40b7-9d0d-f87aa59bcb3c] close complete, cost 35 ms 
[INFO ] 2024-07-24 16:58:43.625 - [任务 26][Modules] - Node Modules[5bdba8b0-39a4-4381-9890-6284e6ab896d] running status set to false 
[INFO ] 2024-07-24 16:58:43.627 - [任务 26][Modules] - PDK connector node stopped: HazelcastTargetPdkDataNode-5bdba8b0-39a4-4381-9890-6284e6ab896d 
[INFO ] 2024-07-24 16:58:43.628 - [任务 26][Modules] - PDK connector node released: HazelcastTargetPdkDataNode-5bdba8b0-39a4-4381-9890-6284e6ab896d 
[INFO ] 2024-07-24 16:58:43.628 - [任务 26][Modules] - Node Modules[5bdba8b0-39a4-4381-9890-6284e6ab896d] schema data cleaned 
[INFO ] 2024-07-24 16:58:43.628 - [任务 26][Modules] - Node Modules[5bdba8b0-39a4-4381-9890-6284e6ab896d] monitor closed 
[INFO ] 2024-07-24 16:58:43.628 - [任务 26][Modules] - Node Modules[5bdba8b0-39a4-4381-9890-6284e6ab896d] close complete, cost 3 ms 
[INFO ] 2024-07-24 16:58:44.645 - [任务 26][Modules] - Incremental sync completed 
[INFO ] 2024-07-24 16:58:48.139 - [任务 26] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 16:58:48.263 - [任务 26] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@26ae582d 
[INFO ] 2024-07-24 16:58:48.263 - [任务 26] - Stop task milestones: 66a0c057f604e81d788d0876(任务 26)  
[INFO ] 2024-07-24 16:58:48.271 - [任务 26] - Stopped task aspect(s) 
[INFO ] 2024-07-24 16:58:48.271 - [任务 26] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 16:58:48.287 - [任务 26] - Remove memory task client succeed, task: 任务 26[66a0c057f604e81d788d0876] 
[INFO ] 2024-07-24 16:58:48.290 - [任务 26] - Destroy memory task client cache succeed, task: 任务 26[66a0c057f604e81d788d0876] 
