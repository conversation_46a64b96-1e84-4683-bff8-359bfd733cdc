[INFO ] 2024-06-27 23:44:47.771 - [任务 5] - Task initialization... 
[INFO ] 2024-06-27 23:44:47.975 - [任务 5] - Start task milestones: 667d8854ff9e756e4afacaa1(任务 5) 
[INFO ] 2024-06-27 23:44:48.730 - [任务 5] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-06-27 23:44:48.730 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-27 23:44:49.303 - [任务 5][Homedepot订单] - Node Homedepot订单[d646a3eb-c658-4639-8f47-b832201f7707] start preload schema,table counts: 1 
[INFO ] 2024-06-27 23:44:49.306 - [任务 5][HomedepotTest] - Node HomedepotTest[f66edfcc-cc0f-4d34-8d6b-7803f26dd3af] start preload schema,table counts: 1 
[INFO ] 2024-06-27 23:44:49.307 - [任务 5][Homedepot订单] - Node Homedepot订单[d646a3eb-c658-4639-8f47-b832201f7707] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 23:44:49.307 - [任务 5][HomedepotTest] - Node HomedepotTest[f66edfcc-cc0f-4d34-8d6b-7803f26dd3af] preload schema finished, cost 0 ms 
[INFO ] 2024-06-27 23:44:50.815 - [任务 5][HomedepotTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-27 23:44:50.966 - [任务 5][Homedepot订单] - Source node "Homedepot订单" read batch size: 100 
[INFO ] 2024-06-27 23:44:50.980 - [任务 5][Homedepot订单] - Source node "Homedepot订单" event queue capacity: 200 
[INFO ] 2024-06-27 23:44:50.981 - [任务 5][Homedepot订单] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-27 23:44:51.035 - [任务 5][Homedepot订单] - batch offset found: {},stream offset found: {"filename":"binlog.000002","position":1389390,"gtidSet":""} 
[INFO ] 2024-06-27 23:44:51.103 - [任务 5][Homedepot订单] - Initial sync started 
[INFO ] 2024-06-27 23:44:51.107 - [任务 5][Homedepot订单] - Starting batch read, table name: Homedepot订单, offset: null 
[INFO ] 2024-06-27 23:44:51.182 - [任务 5][Homedepot订单] - Table Homedepot订单 is going to be initial synced 
[INFO ] 2024-06-27 23:44:51.182 - [任务 5][Homedepot订单] - Query table 'Homedepot订单' counts: 1 
[INFO ] 2024-06-27 23:44:51.205 - [任务 5][Homedepot订单] - Table [Homedepot订单] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-27 23:44:51.205 - [任务 5][Homedepot订单] - Initial sync completed 
[INFO ] 2024-06-27 23:44:51.206 - [任务 5][Homedepot订单] - Incremental sync starting... 
[INFO ] 2024-06-27 23:44:51.207 - [任务 5][Homedepot订单] - Initial sync completed 
[INFO ] 2024-06-27 23:44:51.214 - [任务 5][Homedepot订单] - Starting stream read, table list: [Homedepot订单], offset: {"filename":"binlog.000002","position":1389390,"gtidSet":""} 
[INFO ] 2024-06-27 23:44:51.423 - [任务 5][Homedepot订单] - Starting mysql cdc, server name: cd180a8a-0fd1-47c9-86b0-f7747dd5960b 
[INFO ] 2024-06-27 23:44:51.631 - [任务 5][Homedepot订单] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 669489717
  time.precision.mode: adaptive_time_microseconds
  database.server.name: cd180a8a-0fd1-47c9-86b0-f7747dd5960b
  database.port: 3306
  threadName: Debezium-Mysql-Connector-cd180a8a-0fd1-47c9-86b0-f7747dd5960b
  database.hostname: ***********
  database.password: ********
  name: cd180a8a-0fd1-47c9-86b0-f7747dd5960b
  pdk.offset.string: {"name":"cd180a8a-0fd1-47c9-86b0-f7747dd5960b","offset":{"{\"server\":\"cd180a8a-0fd1-47c9-86b0-f7747dd5960b\"}":"{\"file\":\"binlog.000002\",\"pos\":1389390,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: 库存.Homedepot订单
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: 库存
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-27 23:44:51.880 - [任务 5][Homedepot订单] - Connector Mysql incremental start succeed, tables: [Homedepot订单], data change syncing 
[INFO ] 2024-06-27 23:49:07.369 - [任务 5] - Stop task milestones: 667d8854ff9e756e4afacaa1(任务 5)  
[INFO ] 2024-06-27 23:49:07.543 - [任务 5][Homedepot订单] - Node Homedepot订单[d646a3eb-c658-4639-8f47-b832201f7707] running status set to false 
[INFO ] 2024-06-27 23:49:07.543 - [任务 5][Homedepot订单] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-06-27 23:49:07.544 - [任务 5][Homedepot订单] - Mysql binlog reader stopped 
[INFO ] 2024-06-27 23:49:07.589 - [任务 5][Homedepot订单] - PDK connector node stopped: HazelcastSourcePdkDataNode-d646a3eb-c658-4639-8f47-b832201f7707 
[INFO ] 2024-06-27 23:49:07.591 - [任务 5][Homedepot订单] - PDK connector node released: HazelcastSourcePdkDataNode-d646a3eb-c658-4639-8f47-b832201f7707 
[INFO ] 2024-06-27 23:49:07.594 - [任务 5][Homedepot订单] - Node Homedepot订单[d646a3eb-c658-4639-8f47-b832201f7707] schema data cleaned 
[INFO ] 2024-06-27 23:49:07.596 - [任务 5][Homedepot订单] - Node Homedepot订单[d646a3eb-c658-4639-8f47-b832201f7707] monitor closed 
[INFO ] 2024-06-27 23:49:07.601 - [任务 5][Homedepot订单] - Node Homedepot订单[d646a3eb-c658-4639-8f47-b832201f7707] close complete, cost 192 ms 
[INFO ] 2024-06-27 23:49:07.602 - [任务 5][HomedepotTest] - Node HomedepotTest[f66edfcc-cc0f-4d34-8d6b-7803f26dd3af] running status set to false 
[INFO ] 2024-06-27 23:49:07.665 - [任务 5][HomedepotTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-f66edfcc-cc0f-4d34-8d6b-7803f26dd3af 
[INFO ] 2024-06-27 23:49:07.665 - [任务 5][HomedepotTest] - PDK connector node released: HazelcastTargetPdkDataNode-f66edfcc-cc0f-4d34-8d6b-7803f26dd3af 
[INFO ] 2024-06-27 23:49:07.666 - [任务 5][HomedepotTest] - Node HomedepotTest[f66edfcc-cc0f-4d34-8d6b-7803f26dd3af] schema data cleaned 
[INFO ] 2024-06-27 23:49:07.666 - [任务 5][HomedepotTest] - Node HomedepotTest[f66edfcc-cc0f-4d34-8d6b-7803f26dd3af] monitor closed 
[INFO ] 2024-06-27 23:49:07.666 - [任务 5][HomedepotTest] - Node HomedepotTest[f66edfcc-cc0f-4d34-8d6b-7803f26dd3af] close complete, cost 64 ms 
[INFO ] 2024-06-27 23:49:07.843 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-27 23:49:07.843 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-06-27 23:49:07.845 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-06-27 23:49:07.907 - [任务 5] - Remove memory task client succeed, task: 任务 5[667d8854ff9e756e4afacaa1] 
[INFO ] 2024-06-27 23:49:07.907 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[667d8854ff9e756e4afacaa1] 
