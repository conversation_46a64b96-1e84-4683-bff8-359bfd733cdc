[INFO ] 2024-07-15 11:43:55.551 - [任务 1] - Task initialization... 
[INFO ] 2024-07-15 11:43:55.555 - [任务 1] - Start task milestones: 66949a381df4b966216a4a4b(任务 1) 
[INFO ] 2024-07-15 11:43:55.784 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 11:43:55.816 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 11:43:55.845 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] start preload schema,table counts: 1 
[INFO ] 2024-07-15 11:43:55.845 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] start preload schema,table counts: 1 
[INFO ] 2024-07-15 11:43:55.845 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 11:43:55.845 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 11:43:56.997 - [任务 1][POLICY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 11:43:57.155 - [任务 1][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-15 11:43:57.155 - [任务 1][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-15 11:43:57.155 - [任务 1][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 11:43:57.162 - [任务 1][POLICY] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":111411645,"gtidSet":""} 
[INFO ] 2024-07-15 11:43:57.209 - [任务 1][POLICY] - Initial sync started 
[INFO ] 2024-07-15 11:43:57.209 - [任务 1][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-15 11:43:57.246 - [任务 1][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-15 11:43:57.246 - [任务 1][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-15 11:43:57.338 - [任务 1][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-15 11:43:57.341 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 11:43:57.341 - [任务 1][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-15 11:43:57.341 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 11:43:57.380 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-15 11:43:57.380 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection TestMysql enable share cdc: true 
[INFO ] 2024-07-15 11:43:57.380 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 1 enable share cdc: true 
[INFO ] 2024-07-15 11:43:57.412 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自TestMysql的共享挖掘任务 
[INFO ] 2024-07-15 11:43:57.438 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-15 11:43:57.447 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66949afb66ab5ede8a84b30b, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669496f21df4b966216a489c_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1382432995, shareCdcTaskId=66949afb1df4b966216a4a93, connectionId=669496f21df4b966216a489c) 
[INFO ] 2024-07-15 11:43:57.476 - [任务 1][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TestMysql的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1382432995', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 11:43:57.477 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-15 11:43:57.477 - [任务 1][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-15 11:43:57.477 - [任务 1][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-15 11:43:57.477 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-15 11:43:57.477 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-15 11:43:57.493 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66949afb66ab5ede8a84b30b, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669496f21df4b966216a489c_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1382432995, shareCdcTaskId=66949afb1df4b966216a4a93, connectionId=669496f21df4b966216a489c) 
[INFO ] 2024-07-15 11:43:57.494 - [任务 1][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自TestMysql的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1382432995', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 11:43:57.494 - [任务 1][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自TestMysql的共享挖掘任务_POLICY_任务 1, external storage name: ExternalStorage_SHARE_CDC_-1382432995 
[INFO ] 2024-07-15 11:43:57.498 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-15 11:43:57.504 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-15T03:43:57.154Z): 1 
[INFO ] 2024-07-15 11:43:57.505 - [任务 1][POLICY] - Connector Mysql incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-15 11:43:57.505 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 1 
[INFO ] 2024-07-15 11:43:57.714 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=1} 
[INFO ] 2024-07-15 11:58:42.929 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] running status set to false 
[INFO ] 2024-07-15 11:58:42.930 - [任务 1][POLICY] - Incremental sync completed 
[INFO ] 2024-07-15 11:58:42.942 - [任务 1][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-8638ce24-36bd-41c4-b1bb-5f42173bd2cb 
[INFO ] 2024-07-15 11:58:42.943 - [任务 1][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-8638ce24-36bd-41c4-b1bb-5f42173bd2cb 
[INFO ] 2024-07-15 11:58:42.943 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] schema data cleaned 
[INFO ] 2024-07-15 11:58:42.943 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] monitor closed 
[INFO ] 2024-07-15 11:58:42.945 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] close complete, cost 50 ms 
[INFO ] 2024-07-15 11:58:42.974 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] running status set to false 
[INFO ] 2024-07-15 11:58:42.974 - [任务 1][POLICY] - PDK connector node stopped: HazelcastTargetPdkDataNode-7b038694-38df-48a9-9f6b-e13d855629cc 
[INFO ] 2024-07-15 11:58:42.974 - [任务 1][POLICY] - PDK connector node released: HazelcastTargetPdkDataNode-7b038694-38df-48a9-9f6b-e13d855629cc 
[INFO ] 2024-07-15 11:58:43.063 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] schema data cleaned 
[INFO ] 2024-07-15 11:58:43.073 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] monitor closed 
[INFO ] 2024-07-15 11:58:43.073 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] close complete, cost 126 ms 
[INFO ] 2024-07-15 11:58:45.447 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 11:58:45.447 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@d77a89b 
[INFO ] 2024-07-15 11:58:45.569 - [任务 1] - Stop task milestones: 66949a381df4b966216a4a4b(任务 1)  
[INFO ] 2024-07-15 11:58:45.579 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-15 11:58:45.579 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 11:58:45.607 - [任务 1] - Remove memory task client succeed, task: 任务 1[66949a381df4b966216a4a4b] 
[INFO ] 2024-07-15 11:58:45.609 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66949a381df4b966216a4a4b] 
[INFO ] 2024-07-15 11:58:49.895 - [任务 1] - Task initialization... 
[INFO ] 2024-07-15 11:58:50.107 - [任务 1] - Start task milestones: 66949a381df4b966216a4a4b(任务 1) 
[INFO ] 2024-07-15 11:58:50.159 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 11:58:50.160 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 11:58:50.218 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] start preload schema,table counts: 1 
[INFO ] 2024-07-15 11:58:50.218 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] start preload schema,table counts: 1 
[INFO ] 2024-07-15 11:58:50.219 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 11:58:50.219 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 11:58:51.031 - [任务 1][POLICY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 11:58:51.234 - [任务 1][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-15 11:58:51.234 - [任务 1][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-15 11:58:51.234 - [任务 1][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 11:58:51.238 - [任务 1][POLICY] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":111411645,"gtidSet":""} 
[INFO ] 2024-07-15 11:58:51.374 - [任务 1][POLICY] - Initial sync started 
[INFO ] 2024-07-15 11:58:51.374 - [任务 1][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-15 11:58:51.382 - [任务 1][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-15 11:58:51.502 - [任务 1][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-15 11:58:51.502 - [任务 1][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-15 11:58:51.503 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 11:58:51.503 - [任务 1][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-15 11:58:51.556 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 11:58:51.556 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-15 11:58:51.557 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection TestMysql enable share cdc: true 
[INFO ] 2024-07-15 11:58:51.557 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 1 enable share cdc: true 
[INFO ] 2024-07-15 11:58:51.598 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自TestMysql的共享挖掘任务 
[INFO ] 2024-07-15 11:58:51.598 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-15 11:58:51.619 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66949afb66ab5ede8a84b30b, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669496f21df4b966216a489c_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1382432995, shareCdcTaskId=66949afb1df4b966216a4a93, connectionId=669496f21df4b966216a489c) 
[INFO ] 2024-07-15 11:58:51.620 - [任务 1][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TestMysql的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1382432995', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 11:58:51.640 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-15 11:58:51.640 - [任务 1][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-15 11:58:51.647 - [任务 1][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-15 11:58:51.647 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-15 11:58:51.656 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-15 11:58:51.656 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66949afb66ab5ede8a84b30b, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669496f21df4b966216a489c_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1382432995, shareCdcTaskId=66949afb1df4b966216a4a93, connectionId=669496f21df4b966216a489c) 
[INFO ] 2024-07-15 11:58:51.673 - [任务 1][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自TestMysql的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1382432995', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 11:58:51.686 - [任务 1][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自TestMysql的共享挖掘任务_POLICY_任务 1, external storage name: ExternalStorage_SHARE_CDC_-1382432995 
[INFO ] 2024-07-15 11:58:51.687 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-15 11:58:51.688 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-15T03:58:51.235Z): 1 
[INFO ] 2024-07-15 11:58:51.688 - [任务 1][POLICY] - Connector Mysql incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-15 11:58:51.688 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 1 
[INFO ] 2024-07-15 11:58:51.688 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=1} 
[INFO ] 2024-07-15 12:00:24.324 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] running status set to false 
[INFO ] 2024-07-15 12:00:24.324 - [任务 1][POLICY] - Incremental sync completed 
[INFO ] 2024-07-15 12:00:24.332 - [任务 1][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-8638ce24-36bd-41c4-b1bb-5f42173bd2cb 
[INFO ] 2024-07-15 12:00:24.332 - [任务 1][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-8638ce24-36bd-41c4-b1bb-5f42173bd2cb 
[INFO ] 2024-07-15 12:00:24.332 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] schema data cleaned 
[INFO ] 2024-07-15 12:00:24.332 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] monitor closed 
[INFO ] 2024-07-15 12:00:24.335 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] close complete, cost 34 ms 
[INFO ] 2024-07-15 12:00:24.335 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] running status set to false 
[INFO ] 2024-07-15 12:00:24.352 - [任务 1][POLICY] - PDK connector node stopped: HazelcastTargetPdkDataNode-7b038694-38df-48a9-9f6b-e13d855629cc 
[INFO ] 2024-07-15 12:00:24.353 - [任务 1][POLICY] - PDK connector node released: HazelcastTargetPdkDataNode-7b038694-38df-48a9-9f6b-e13d855629cc 
[INFO ] 2024-07-15 12:00:24.353 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] schema data cleaned 
[INFO ] 2024-07-15 12:00:24.353 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] monitor closed 
[INFO ] 2024-07-15 12:00:24.555 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] close complete, cost 19 ms 
[INFO ] 2024-07-15 12:00:25.698 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 12:00:25.698 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@41b6bd59 
[INFO ] 2024-07-15 12:00:25.817 - [任务 1] - Stop task milestones: 66949a381df4b966216a4a4b(任务 1)  
[INFO ] 2024-07-15 12:00:25.825 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-15 12:00:25.826 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 12:00:25.848 - [任务 1] - Remove memory task client succeed, task: 任务 1[66949a381df4b966216a4a4b] 
[INFO ] 2024-07-15 12:00:25.848 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66949a381df4b966216a4a4b] 
[INFO ] 2024-07-15 12:01:01.115 - [任务 1] - Task initialization... 
[INFO ] 2024-07-15 12:01:01.117 - [任务 1] - Start task milestones: 66949a381df4b966216a4a4b(任务 1) 
[INFO ] 2024-07-15 12:01:01.286 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 12:01:01.286 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 12:01:01.344 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] start preload schema,table counts: 1 
[INFO ] 2024-07-15 12:01:01.344 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] start preload schema,table counts: 1 
[INFO ] 2024-07-15 12:01:01.344 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 12:01:01.345 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 12:01:02.090 - [任务 1][POLICY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 12:01:02.364 - [任务 1][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-15 12:01:02.364 - [任务 1][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-15 12:01:02.364 - [任务 1][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 12:01:02.366 - [任务 1][POLICY] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":111411645,"gtidSet":""} 
[INFO ] 2024-07-15 12:01:02.454 - [任务 1][POLICY] - Initial sync started 
[INFO ] 2024-07-15 12:01:02.468 - [任务 1][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-15 12:01:02.478 - [任务 1][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-15 12:01:02.580 - [任务 1][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-15 12:01:02.611 - [任务 1][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-15 12:01:02.611 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 12:01:02.611 - [任务 1][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-15 12:01:02.612 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 12:01:02.651 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-15 12:01:02.651 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection TestMysql enable share cdc: true 
[INFO ] 2024-07-15 12:01:02.710 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 1 enable share cdc: true 
[INFO ] 2024-07-15 12:01:02.710 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自TestMysql的共享挖掘任务 
[INFO ] 2024-07-15 12:01:02.724 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-15 12:01:02.724 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66949afb66ab5ede8a84b30b, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669496f21df4b966216a489c_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1382432995, shareCdcTaskId=66949afb1df4b966216a4a93, connectionId=669496f21df4b966216a489c) 
[INFO ] 2024-07-15 12:01:02.747 - [任务 1][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TestMysql的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1382432995', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 12:01:02.747 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-15 12:01:02.747 - [任务 1][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-15 12:01:02.747 - [任务 1][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-15 12:01:02.747 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-15 12:01:02.754 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-15 12:01:02.754 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66949afb66ab5ede8a84b30b, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669496f21df4b966216a489c_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1382432995, shareCdcTaskId=66949afb1df4b966216a4a93, connectionId=669496f21df4b966216a489c) 
[INFO ] 2024-07-15 12:01:02.756 - [任务 1][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自TestMysql的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1382432995', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 12:01:02.756 - [任务 1][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自TestMysql的共享挖掘任务_POLICY_任务 1, external storage name: ExternalStorage_SHARE_CDC_-1382432995 
[INFO ] 2024-07-15 12:01:02.760 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-15 12:01:02.760 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-15T04:01:02.362Z): 1 
[INFO ] 2024-07-15 12:01:02.761 - [任务 1][POLICY] - Connector Mysql incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-15 12:01:02.761 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 1 
[INFO ] 2024-07-15 12:01:02.963 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=1} 
[INFO ] 2024-07-15 12:02:10.850 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] running status set to false 
[INFO ] 2024-07-15 12:02:10.875 - [任务 1][POLICY] - Incremental sync completed 
[INFO ] 2024-07-15 12:02:10.875 - [任务 1][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-8638ce24-36bd-41c4-b1bb-5f42173bd2cb 
[INFO ] 2024-07-15 12:02:10.876 - [任务 1][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-8638ce24-36bd-41c4-b1bb-5f42173bd2cb 
[INFO ] 2024-07-15 12:02:10.876 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] schema data cleaned 
[INFO ] 2024-07-15 12:02:10.876 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] monitor closed 
[INFO ] 2024-07-15 12:02:10.879 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] close complete, cost 39 ms 
[INFO ] 2024-07-15 12:02:10.879 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] running status set to false 
[INFO ] 2024-07-15 12:02:10.905 - [任务 1][POLICY] - PDK connector node stopped: HazelcastTargetPdkDataNode-7b038694-38df-48a9-9f6b-e13d855629cc 
[INFO ] 2024-07-15 12:02:10.905 - [任务 1][POLICY] - PDK connector node released: HazelcastTargetPdkDataNode-7b038694-38df-48a9-9f6b-e13d855629cc 
[INFO ] 2024-07-15 12:02:10.906 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] schema data cleaned 
[INFO ] 2024-07-15 12:02:10.906 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] monitor closed 
[INFO ] 2024-07-15 12:02:10.924 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] close complete, cost 27 ms 
[INFO ] 2024-07-15 12:02:10.924 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 12:02:10.924 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7e70d4c6 
[INFO ] 2024-07-15 12:02:11.061 - [任务 1] - Stop task milestones: 66949a381df4b966216a4a4b(任务 1)  
[INFO ] 2024-07-15 12:02:11.069 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-15 12:02:11.069 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 12:02:11.088 - [任务 1] - Remove memory task client succeed, task: 任务 1[66949a381df4b966216a4a4b] 
[INFO ] 2024-07-15 12:02:11.090 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66949a381df4b966216a4a4b] 
[INFO ] 2024-07-15 12:02:32.326 - [任务 1] - Task initialization... 
[INFO ] 2024-07-15 12:02:32.349 - [任务 1] - Start task milestones: 66949a381df4b966216a4a4b(任务 1) 
[INFO ] 2024-07-15 12:02:32.419 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 12:02:32.457 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 12:02:32.516 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] start preload schema,table counts: 1 
[INFO ] 2024-07-15 12:02:32.516 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] start preload schema,table counts: 1 
[INFO ] 2024-07-15 12:02:32.516 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 12:02:32.516 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] preload schema finished, cost 1 ms 
[INFO ] 2024-07-15 12:02:33.552 - [任务 1][POLICY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 12:02:33.552 - [任务 1][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-15 12:02:33.553 - [任务 1][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-15 12:02:33.553 - [任务 1][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 12:02:33.613 - [任务 1][POLICY] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":111411645,"gtidSet":""} 
[INFO ] 2024-07-15 12:02:33.613 - [任务 1][POLICY] - Initial sync started 
[INFO ] 2024-07-15 12:02:33.614 - [任务 1][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-15 12:02:33.614 - [任务 1][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-15 12:02:33.723 - [任务 1][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-15 12:02:33.724 - [任务 1][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-15 12:02:33.725 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 12:02:33.725 - [任务 1][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-15 12:02:33.725 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 12:02:33.796 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-15 12:02:33.796 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection TestMysql enable share cdc: true 
[INFO ] 2024-07-15 12:02:33.796 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 1 enable share cdc: true 
[INFO ] 2024-07-15 12:02:33.842 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自TestMysql的共享挖掘任务 
[INFO ] 2024-07-15 12:02:33.843 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-15 12:02:33.868 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66949afb66ab5ede8a84b30b, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669496f21df4b966216a489c_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1382432995, shareCdcTaskId=66949afb1df4b966216a4a93, connectionId=669496f21df4b966216a489c) 
[INFO ] 2024-07-15 12:02:33.868 - [任务 1][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TestMysql的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1382432995', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 12:02:33.879 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-15 12:02:33.879 - [任务 1][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-15 12:02:33.879 - [任务 1][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-15 12:02:33.879 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-15 12:02:33.887 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-15 12:02:33.887 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66949afb66ab5ede8a84b30b, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669496f21df4b966216a489c_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1382432995, shareCdcTaskId=66949afb1df4b966216a4a93, connectionId=669496f21df4b966216a489c) 
[INFO ] 2024-07-15 12:02:33.890 - [任务 1][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自TestMysql的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1382432995', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 12:02:33.890 - [任务 1][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自TestMysql的共享挖掘任务_POLICY_任务 1, external storage name: ExternalStorage_SHARE_CDC_-1382432995 
[INFO ] 2024-07-15 12:02:33.895 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-15 12:02:33.896 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-15T04:02:33.553Z): 1 
[INFO ] 2024-07-15 12:02:33.898 - [任务 1][POLICY] - Connector Mysql incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-15 12:02:33.898 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 1 
[INFO ] 2024-07-15 12:02:34.104 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=1} 
[INFO ] 2024-07-15 12:02:45.076 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] running status set to false 
[INFO ] 2024-07-15 12:02:45.117 - [任务 1][POLICY] - Incremental sync completed 
[INFO ] 2024-07-15 12:02:45.117 - [任务 1][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-8638ce24-36bd-41c4-b1bb-5f42173bd2cb 
[INFO ] 2024-07-15 12:02:45.117 - [任务 1][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-8638ce24-36bd-41c4-b1bb-5f42173bd2cb 
[INFO ] 2024-07-15 12:02:45.117 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] schema data cleaned 
[INFO ] 2024-07-15 12:02:45.119 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] monitor closed 
[INFO ] 2024-07-15 12:02:45.123 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] close complete, cost 67 ms 
[INFO ] 2024-07-15 12:02:45.123 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] running status set to false 
[INFO ] 2024-07-15 12:02:45.160 - [任务 1][POLICY] - PDK connector node stopped: HazelcastTargetPdkDataNode-7b038694-38df-48a9-9f6b-e13d855629cc 
[INFO ] 2024-07-15 12:02:45.161 - [任务 1][POLICY] - PDK connector node released: HazelcastTargetPdkDataNode-7b038694-38df-48a9-9f6b-e13d855629cc 
[INFO ] 2024-07-15 12:02:45.161 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] schema data cleaned 
[INFO ] 2024-07-15 12:02:45.161 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] monitor closed 
[INFO ] 2024-07-15 12:02:45.162 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] close complete, cost 39 ms 
[INFO ] 2024-07-15 12:02:46.152 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 12:02:46.153 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@216584c2 
[INFO ] 2024-07-15 12:02:46.278 - [任务 1] - Stop task milestones: 66949a381df4b966216a4a4b(任务 1)  
[INFO ] 2024-07-15 12:02:46.286 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-15 12:02:46.286 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 12:02:46.310 - [任务 1] - Remove memory task client succeed, task: 任务 1[66949a381df4b966216a4a4b] 
[INFO ] 2024-07-15 12:02:46.310 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66949a381df4b966216a4a4b] 
[INFO ] 2024-07-15 12:02:50.532 - [任务 1] - Task initialization... 
[INFO ] 2024-07-15 12:02:50.577 - [任务 1] - Start task milestones: 66949a381df4b966216a4a4b(任务 1) 
[INFO ] 2024-07-15 12:02:50.672 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 12:02:50.732 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 12:02:50.732 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] start preload schema,table counts: 1 
[INFO ] 2024-07-15 12:02:50.732 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] start preload schema,table counts: 1 
[INFO ] 2024-07-15 12:02:50.733 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 12:02:50.733 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 12:02:51.547 - [任务 1][POLICY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 12:02:51.810 - [任务 1][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-15 12:02:51.810 - [任务 1][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-15 12:02:51.811 - [任务 1][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 12:02:51.813 - [任务 1][POLICY] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":111411645,"gtidSet":""} 
[INFO ] 2024-07-15 12:02:51.870 - [任务 1][POLICY] - Initial sync started 
[INFO ] 2024-07-15 12:02:51.870 - [任务 1][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-15 12:02:51.916 - [任务 1][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-15 12:02:51.916 - [任务 1][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-15 12:02:51.967 - [任务 1][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-15 12:02:51.968 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 12:02:51.968 - [任务 1][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-15 12:02:51.968 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 12:02:52.021 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-15 12:02:52.021 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection TestMysql enable share cdc: true 
[INFO ] 2024-07-15 12:02:52.021 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 1 enable share cdc: true 
[INFO ] 2024-07-15 12:02:52.033 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自TestMysql的共享挖掘任务 
[INFO ] 2024-07-15 12:02:52.047 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav390?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-15 12:02:52.047 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66949afb66ab5ede8a84b30b, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669496f21df4b966216a489c_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1382432995, shareCdcTaskId=66949afb1df4b966216a4a93, connectionId=669496f21df4b966216a489c) 
[INFO ] 2024-07-15 12:02:52.072 - [任务 1][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TestMysql的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1382432995', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 12:02:52.077 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-15 12:02:52.077 - [任务 1][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-15 12:02:52.077 - [任务 1][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-15 12:02:52.077 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-15 12:02:52.077 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-15 12:02:52.085 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66949afb66ab5ede8a84b30b, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669496f21df4b966216a489c_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1382432995, shareCdcTaskId=66949afb1df4b966216a4a93, connectionId=669496f21df4b966216a489c) 
[INFO ] 2024-07-15 12:02:52.085 - [任务 1][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自TestMysql的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav390.ExternalStorage_SHARE_CDC_-1382432995', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 12:02:52.086 - [任务 1][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自TestMysql的共享挖掘任务_POLICY_任务 1, external storage name: ExternalStorage_SHARE_CDC_-1382432995 
[INFO ] 2024-07-15 12:02:52.086 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-15 12:02:52.094 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-15T04:02:51.811Z): 1 
[INFO ] 2024-07-15 12:02:52.094 - [任务 1][POLICY] - Connector Mysql incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-15 12:02:52.097 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 1 
[INFO ] 2024-07-15 12:02:52.097 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=1} 
[INFO ] 2024-07-15 12:03:28.254 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] running status set to false 
[INFO ] 2024-07-15 12:03:28.255 - [任务 1][POLICY] - Incremental sync completed 
[INFO ] 2024-07-15 12:03:28.267 - [任务 1][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-8638ce24-36bd-41c4-b1bb-5f42173bd2cb 
[INFO ] 2024-07-15 12:03:28.267 - [任务 1][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-8638ce24-36bd-41c4-b1bb-5f42173bd2cb 
[INFO ] 2024-07-15 12:03:28.267 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] schema data cleaned 
[INFO ] 2024-07-15 12:03:28.268 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] monitor closed 
[INFO ] 2024-07-15 12:03:28.269 - [任务 1][POLICY] - Node POLICY[8638ce24-36bd-41c4-b1bb-5f42173bd2cb] close complete, cost 49 ms 
[INFO ] 2024-07-15 12:03:28.269 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] running status set to false 
[INFO ] 2024-07-15 12:03:28.280 - [任务 1][POLICY] - PDK connector node stopped: HazelcastTargetPdkDataNode-7b038694-38df-48a9-9f6b-e13d855629cc 
[INFO ] 2024-07-15 12:03:28.280 - [任务 1][POLICY] - PDK connector node released: HazelcastTargetPdkDataNode-7b038694-38df-48a9-9f6b-e13d855629cc 
[INFO ] 2024-07-15 12:03:28.280 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] schema data cleaned 
[INFO ] 2024-07-15 12:03:28.281 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] monitor closed 
[INFO ] 2024-07-15 12:03:28.486 - [任务 1][POLICY] - Node POLICY[7b038694-38df-48a9-9f6b-e13d855629cc] close complete, cost 12 ms 
[INFO ] 2024-07-15 12:03:31.381 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 12:03:31.381 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3f075d0b 
[INFO ] 2024-07-15 12:03:31.510 - [任务 1] - Stop task milestones: 66949a381df4b966216a4a4b(任务 1)  
[INFO ] 2024-07-15 12:03:31.528 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-15 12:03:31.528 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 12:03:31.547 - [任务 1] - Remove memory task client succeed, task: 任务 1[66949a381df4b966216a4a4b] 
[INFO ] 2024-07-15 12:03:31.549 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66949a381df4b966216a4a4b] 
