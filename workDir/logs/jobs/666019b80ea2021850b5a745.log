[INFO ] 2024-06-05 15:55:01.887 - [任务 2] - Task initialization... 
[INFO ] 2024-06-05 15:55:01.888 - [任务 2] - Start task milestones: 666019b80ea2021850b5a745(任务 2) 
[INFO ] 2024-06-05 15:55:01.897 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-05 15:55:01.999 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-05 15:55:01.999 - [任务 2][aaaa] - Node aaaa[679aa619-b19e-4d08-909d-e573b40d5ab6] start preload schema,table counts: 1 
[INFO ] 2024-06-05 15:55:01.999 - [任务 2][TESTSTRINTABLE] - Node TESTSTRINTABLE[d360adb9-223e-4568-aa3a-6c6987707f89] start preload schema,table counts: 1 
[INFO ] 2024-06-05 15:55:02.018 - [任务 2][TESTSTRINTABLE] - Node TESTSTRINTABLE[d360adb9-223e-4568-aa3a-6c6987707f89] preload schema finished, cost 19 ms 
[INFO ] 2024-06-05 15:55:02.018 - [任务 2][aaaa] - Node aaaa[679aa619-b19e-4d08-909d-e573b40d5ab6] preload schema finished, cost 19 ms 
[INFO ] 2024-06-05 15:55:03.160 - [任务 2][TESTSTRINTABLE] - Source node "TESTSTRINTABLE" read batch size: 100 
[INFO ] 2024-06-05 15:55:03.160 - [任务 2][TESTSTRINTABLE] - Source node "TESTSTRINTABLE" event queue capacity: 200 
[INFO ] 2024-06-05 15:55:03.160 - [任务 2][TESTSTRINTABLE] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-05 15:55:03.258 - [任务 2][aaaa] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-06-05 15:55:03.460 - [任务 2][TESTSTRINTABLE] - Found pending transaction, please check if there are any earlier transactions to prevent data loss, the earliest startScn: 53833553 
[INFO ] 2024-06-05 15:55:03.642 - [任务 2][TESTSTRINTABLE] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53833552,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-05 15:55:03.642 - [任务 2][TESTSTRINTABLE] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-05 15:55:03.710 - [任务 2][TESTSTRINTABLE] - Initial sync started 
[INFO ] 2024-06-05 15:55:03.711 - [任务 2][TESTSTRINTABLE] - Starting batch read, table name: TESTSTRINTABLE, offset: null 
[INFO ] 2024-06-05 15:55:03.744 - [任务 2][TESTSTRINTABLE] - Table TESTSTRINTABLE is going to be initial synced 
[INFO ] 2024-06-05 15:55:03.744 - [任务 2][TESTSTRINTABLE] - Table [TESTSTRINTABLE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-05 15:55:03.744 - [任务 2][TESTSTRINTABLE] - Query table 'TESTSTRINTABLE' counts: 0 
[INFO ] 2024-06-05 15:55:03.745 - [任务 2][TESTSTRINTABLE] - Initial sync completed 
[INFO ] 2024-06-05 15:55:03.745 - [任务 2][TESTSTRINTABLE] - Incremental sync starting... 
[INFO ] 2024-06-05 15:55:03.745 - [任务 2][TESTSTRINTABLE] - Initial sync completed 
[INFO ] 2024-06-05 15:55:03.784 - [任务 2][TESTSTRINTABLE] - Starting stream read, table list: [TESTSTRINTABLE], offset: {"sortString":null,"offsetValue":null,"lastScn":53833552,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-05 15:55:03.985 - [任务 2][TESTSTRINTABLE] - total start mining scn: 53833552 
[INFO ] 2024-06-05 15:55:05.212 - [任务 2][TESTSTRINTABLE] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-06-05 15:58:08.060 - [任务 2] - Stop task milestones: 666019b80ea2021850b5a745(任务 2)  
[INFO ] 2024-06-05 15:58:08.154 - [任务 2][TESTSTRINTABLE] - Node TESTSTRINTABLE[d360adb9-223e-4568-aa3a-6c6987707f89] running status set to false 
[INFO ] 2024-06-05 15:58:08.154 - [任务 2][TESTSTRINTABLE] - Log Miner is shutting down... 
[INFO ] 2024-06-05 15:58:08.154 - [任务 2][TESTSTRINTABLE] - Log Miner has been closed! 
[ERROR] 2024-06-05 15:58:08.171 - [任务 2][TESTSTRINTABLE] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:413)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-06-05 15:58:08.214 - [任务 2][TESTSTRINTABLE] - PDK connector node stopped: HazelcastSourcePdkDataNode-d360adb9-223e-4568-aa3a-6c6987707f89 
[INFO ] 2024-06-05 15:58:08.214 - [任务 2][TESTSTRINTABLE] - PDK connector node released: HazelcastSourcePdkDataNode-d360adb9-223e-4568-aa3a-6c6987707f89 
[INFO ] 2024-06-05 15:58:08.214 - [任务 2][TESTSTRINTABLE] - Node TESTSTRINTABLE[d360adb9-223e-4568-aa3a-6c6987707f89] schema data cleaned 
[INFO ] 2024-06-05 15:58:08.214 - [任务 2][TESTSTRINTABLE] - Node TESTSTRINTABLE[d360adb9-223e-4568-aa3a-6c6987707f89] monitor closed 
[INFO ] 2024-06-05 15:58:08.214 - [任务 2][TESTSTRINTABLE] - Node TESTSTRINTABLE[d360adb9-223e-4568-aa3a-6c6987707f89] close complete, cost 78 ms 
[INFO ] 2024-06-05 15:58:08.245 - [任务 2][aaaa] - Node aaaa[679aa619-b19e-4d08-909d-e573b40d5ab6] running status set to false 
[INFO ] 2024-06-05 15:58:08.245 - [任务 2][aaaa] - PDK connector node stopped: HazelcastTargetPdkDataNode-679aa619-b19e-4d08-909d-e573b40d5ab6 
[INFO ] 2024-06-05 15:58:08.245 - [任务 2][aaaa] - PDK connector node released: HazelcastTargetPdkDataNode-679aa619-b19e-4d08-909d-e573b40d5ab6 
[INFO ] 2024-06-05 15:58:08.245 - [任务 2][aaaa] - Node aaaa[679aa619-b19e-4d08-909d-e573b40d5ab6] schema data cleaned 
[INFO ] 2024-06-05 15:58:08.246 - [任务 2][aaaa] - Node aaaa[679aa619-b19e-4d08-909d-e573b40d5ab6] monitor closed 
[INFO ] 2024-06-05 15:58:08.246 - [任务 2][aaaa] - Node aaaa[679aa619-b19e-4d08-909d-e573b40d5ab6] close complete, cost 30 ms 
[INFO ] 2024-06-05 15:58:12.659 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-05 15:58:12.659 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-06-05 15:58:12.660 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-06-05 15:58:12.687 - [任务 2] - Remove memory task client succeed, task: 任务 2[666019b80ea2021850b5a745] 
[INFO ] 2024-06-05 15:58:12.688 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[666019b80ea2021850b5a745] 
