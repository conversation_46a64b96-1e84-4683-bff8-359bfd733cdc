[INFO ] 2024-03-27 16:14:01.332 - [任务 25] - Task initialization... 
[INFO ] 2024-03-27 16:14:01.333 - [任务 25] - Start task milestones: 6603d4cb8b5bca60f72df65e(任务 25) 
[INFO ] 2024-03-27 16:14:01.334 - [任务 25] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-03-27 16:14:01.334 - [任务 25] - The engine receives 任务 25 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 16:14:01.334 - [任务 25][字段改名] - Node 字段改名[9496be92-ffa6-416b-a1f2-3487c3818c73] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:14:01.334 - [任务 25][test3] - Node test3[eeabfb68-9a53-4d62-b6c1-f7cb8bf5788a] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:14:01.334 - [任务 25][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] start preload schema,table counts: 1 
[INFO ] 2024-03-27 16:14:01.352 - [任务 25][test3] - Node test3[eeabfb68-9a53-4d62-b6c1-f7cb8bf5788a] preload schema finished, cost 27 ms 
[INFO ] 2024-03-27 16:14:01.352 - [任务 25][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] preload schema finished, cost 23 ms 
[INFO ] 2024-03-27 16:14:01.352 - [任务 25][字段改名] - Node 字段改名[9496be92-ffa6-416b-a1f2-3487c3818c73] preload schema finished, cost 28 ms 
[INFO ] 2024-03-27 16:14:01.737 - [任务 25][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 16:14:01.737 - [任务 25][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 16:14:01.737 - [任务 25][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-27 16:14:01.738 - [任务 25][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":145162303,"gtidSet":""} 
[INFO ] 2024-03-27 16:14:01.738 - [任务 25][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-03-27 16:14:01.916 - [任务 25][test3] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 16:14:07.101 - [任务 25][CLAIM] - Initial sync started 
[INFO ] 2024-03-27 16:14:07.109 - [任务 25][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-27 16:14:07.121 - [任务 25][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-27 16:14:07.182 - [任务 25][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-27 16:14:07.212 - [任务 25][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 16:14:07.219 - [任务 25][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-27 16:14:07.219 - [任务 25][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 16:14:07.220 - [任务 25][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":145162303,"gtidSet":""} 
[INFO ] 2024-03-27 16:14:07.275 - [任务 25][CLAIM] - Starting mysql cdc, server name: d4b5e53c-5c18-4ff7-a92e-0b631f2fc871 
[INFO ] 2024-03-27 16:14:07.278 - [任务 25][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 730618500
  time.precision.mode: adaptive_time_microseconds
  database.server.name: d4b5e53c-5c18-4ff7-a92e-0b631f2fc871
  database.port: 3306
  threadName: Debezium-Mysql-Connector-d4b5e53c-5c18-4ff7-a92e-0b631f2fc871
  database.hostname: 127.0.0.1
  database.password: ********
  name: d4b5e53c-5c18-4ff7-a92e-0b631f2fc871
  pdk.offset.string: {"name":"d4b5e53c-5c18-4ff7-a92e-0b631f2fc871","offset":{"{\"server\":\"d4b5e53c-5c18-4ff7-a92e-0b631f2fc871\"}":"{\"file\":\"binlog.000020\",\"pos\":145162303,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 16:14:07.345 - [任务 25][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 16:15:06.371 - [任务 25][CLAIM] - Read DDL: alter table CLAIM add column name varchar(50), about to be packaged as some event(s) 
[INFO ] 2024-03-27 16:15:06.375 - [任务 25][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='d4b5e53c-5c18-4ff7-a92e-0b631f2fc871', offset={{"server":"d4b5e53c-5c18-4ff7-a92e-0b631f2fc871"}={"ts_sec":1711527306,"file":"binlog.000020","pos":145162560,"server_id":1}}} 
[INFO ] 2024-03-27 16:15:06.397 - [任务 25][CLAIM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@70518229: {"newFields":[{"autoInc":false,"dataType":"varchar(50)","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false}],"referenceTime":1711527306111,"tableId":"CLAIM","time":1711527306339,"type":209} 
[INFO ] 2024-03-27 16:15:06.408 - [任务 25][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_6603d4cb8b5bca60f72df65e 
[INFO ] 2024-03-27 16:15:06.510 - [任务 25][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 16:17:06.401 - [任务 25][CLAIM] - Read DDL: alter table CLAIM rename column `name` to `name1`, about to be packaged as some event(s) 
[INFO ] 2024-03-27 16:17:06.425 - [任务 25][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldNameEvent
  - Offset: MysqlStreamOffset{name='d4b5e53c-5c18-4ff7-a92e-0b631f2fc871', offset={{"server":"d4b5e53c-5c18-4ff7-a92e-0b631f2fc871"}={"ts_sec":1711527414,"file":"binlog.000020","pos":145162821,"server_id":1}}} 
[INFO ] 2024-03-27 16:17:06.429 - [任务 25][CLAIM] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='CLAIM', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@7f5945c5} 
[INFO ] 2024-03-27 16:17:59.673 - [任务 25][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_6603d4cb8b5bca60f72df65e 
[INFO ] 2024-03-27 16:21:14.763 - [任务 25][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-03-27 16:21:14.764 - [任务 25][CLAIM] - Incremental sync completed 
[ERROR] 2024-03-27 16:21:14.767 - [任务 25][CLAIM] - java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms <-- Error Message -->
java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms

<-- Simple Stack Trace -->
Caused by: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
	com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:986)
	com.github.shyiko.mysql.binlog.BinaryLogClient$SpawnKeepAliveThread.run(BinaryLogClient.java:907)
	java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	java.util.concurrent.FutureTask.run(FutureTask.java)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
	at io.tapdata.common.exception.AbstractExceptionCollector.collectCdcConfigInvalid(AbstractExceptionCollector.java:63)
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:142)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:333)
	at io.tapdata.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:458)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
	at com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:986)
	at com.github.shyiko.mysql.binlog.BinaryLogClient$SpawnKeepAliveThread.run(BinaryLogClient.java:907)
	... 6 more

[INFO ] 2024-03-27 16:21:14.767 - [任务 25][CLAIM] - Job suspend in error handle 
[INFO ] 2024-03-27 16:21:14.768 - [任务 25][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] running status set to false 
[INFO ] 2024-03-27 16:21:14.768 - [任务 25][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:21:14.768 - [任务 25][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-3844f6fe-10b2-436e-a2d3-dd79e0627476 
[INFO ] 2024-03-27 16:21:14.768 - [任务 25][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] schema data cleaned 
[INFO ] 2024-03-27 16:21:14.768 - [任务 25][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] monitor closed 
[INFO ] 2024-03-27 16:21:14.768 - [任务 25][CLAIM] - Node CLAIM[3844f6fe-10b2-436e-a2d3-dd79e0627476] close complete, cost 21 ms 
[INFO ] 2024-03-27 16:21:14.769 - [任务 25][字段改名] - Node 字段改名[9496be92-ffa6-416b-a1f2-3487c3818c73] running status set to false 
[INFO ] 2024-03-27 16:21:14.889 - [任务 25][字段改名] - Node 字段改名[9496be92-ffa6-416b-a1f2-3487c3818c73] schema data cleaned 
[INFO ] 2024-03-27 16:21:14.893 - [任务 25][字段改名] - Node 字段改名[9496be92-ffa6-416b-a1f2-3487c3818c73] monitor closed 
[INFO ] 2024-03-27 16:21:14.893 - [任务 25][字段改名] - Node 字段改名[9496be92-ffa6-416b-a1f2-3487c3818c73] close complete, cost 126 ms 
[INFO ] 2024-03-27 16:21:14.893 - [任务 25][test3] - Node test3[eeabfb68-9a53-4d62-b6c1-f7cb8bf5788a] running status set to false 
[INFO ] 2024-03-27 16:21:14.919 - [任务 25][test3] - PDK connector node stopped: HazelcastTargetPdkDataNode-eeabfb68-9a53-4d62-b6c1-f7cb8bf5788a 
[INFO ] 2024-03-27 16:21:14.921 - [任务 25][test3] - PDK connector node released: HazelcastTargetPdkDataNode-eeabfb68-9a53-4d62-b6c1-f7cb8bf5788a 
[INFO ] 2024-03-27 16:21:14.921 - [任务 25][test3] - Node test3[eeabfb68-9a53-4d62-b6c1-f7cb8bf5788a] schema data cleaned 
[INFO ] 2024-03-27 16:21:14.921 - [任务 25][test3] - Node test3[eeabfb68-9a53-4d62-b6c1-f7cb8bf5788a] monitor closed 
[INFO ] 2024-03-27 16:21:14.922 - [任务 25][test3] - Node test3[eeabfb68-9a53-4d62-b6c1-f7cb8bf5788a] close complete, cost 30 ms 
[INFO ] 2024-03-27 16:21:19.057 - [任务 25] - Task [任务 25] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-27 16:21:19.082 - [任务 25] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 16:21:19.095 - [任务 25] - Stop task milestones: 6603d4cb8b5bca60f72df65e(任务 25)  
[INFO ] 2024-03-27 16:21:19.113 - [任务 25] - Stopped task aspect(s) 
[INFO ] 2024-03-27 16:21:19.114 - [任务 25] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 16:21:19.137 - [任务 25] - Remove memory task client succeed, task: 任务 25[6603d4cb8b5bca60f72df65e] 
[INFO ] 2024-03-27 16:21:19.140 - [任务 25] - Destroy memory task client cache succeed, task: 任务 25[6603d4cb8b5bca60f72df65e] 
