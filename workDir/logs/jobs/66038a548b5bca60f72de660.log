[INFO ] 2024-03-27 10:54:34.906 - [任务 18] - Start task milestones: 66038a548b5bca60f72de660(任务 18) 
[INFO ] 2024-03-27 10:54:34.909 - [任务 18] - Task initialization... 
[INFO ] 2024-03-27 10:54:34.929 - [任务 18] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 10:54:34.983 - [任务 18] - The engine receives 任务 18 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 10:54:35.034 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:54:35.035 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:54:35.056 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] preload schema finished, cost 22 ms 
[INFO ] 2024-03-27 10:54:35.058 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] preload schema finished, cost 23 ms 
[INFO ] 2024-03-27 10:54:35.740 - [任务 18][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 10:54:35.740 - [任务 18][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 10:54:35.740 - [任务 18][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-27 10:54:35.741 - [任务 18][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":144639512,"gtidSet":""} 
[INFO ] 2024-03-27 10:54:35.782 - [任务 18][CLAIM] - Initial sync started 
[INFO ] 2024-03-27 10:54:35.782 - [任务 18][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-27 10:54:35.788 - [任务 18][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-27 10:54:35.825 - [任务 18][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-27 10:54:35.942 - [任务 18][test8] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 10:54:36.143 - [任务 18][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 10:54:36.148 - [任务 18][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-27 10:54:36.148 - [任务 18][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 10:54:36.152 - [任务 18][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":144639512,"gtidSet":""} 
[INFO ] 2024-03-27 10:54:36.198 - [任务 18][CLAIM] - Starting mysql cdc, server name: 27e8485f-8855-4ea5-9eae-4f63d14ee0bd 
[INFO ] 2024-03-27 10:54:36.201 - [任务 18][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 373129439
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 27e8485f-8855-4ea5-9eae-4f63d14ee0bd
  database.port: 3306
  threadName: Debezium-Mysql-Connector-27e8485f-8855-4ea5-9eae-4f63d14ee0bd
  database.hostname: 127.0.0.1
  database.password: ********
  name: 27e8485f-8855-4ea5-9eae-4f63d14ee0bd
  pdk.offset.string: {"name":"27e8485f-8855-4ea5-9eae-4f63d14ee0bd","offset":{"{\"server\":\"27e8485f-8855-4ea5-9eae-4f63d14ee0bd\"}":"{\"file\":\"binlog.000020\",\"pos\":144639512,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 10:54:36.245 - [任务 18][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 11:10:20.314 - [任务 18] - Start task milestones: 66038a548b5bca60f72de660(任务 18) 
[INFO ] 2024-03-27 11:10:20.315 - [任务 18] - Task initialization... 
[INFO ] 2024-03-27 11:10:20.316 - [任务 18] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 11:10:20.316 - [任务 18] - The engine receives 任务 18 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 11:10:20.316 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:10:20.316 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:10:20.353 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] preload schema finished, cost 46 ms 
[INFO ] 2024-03-27 11:10:20.356 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] preload schema finished, cost 46 ms 
[INFO ] 2024-03-27 11:10:20.642 - [任务 18][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 11:10:20.642 - [任务 18][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 11:10:20.656 - [任务 18][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-27 11:10:20.663 - [任务 18][CLAIM] - batch offset found: {"CLAIM":{}},stream offset found: {"name":"27e8485f-8855-4ea5-9eae-4f63d14ee0bd","offset":{"{\"server\":\"27e8485f-8855-4ea5-9eae-4f63d14ee0bd\"}":"{\"ts_sec\":1711508076,\"file\":\"binlog.000020\",\"pos\":144640816,\"server_id\":1}"}} 
[INFO ] 2024-03-27 11:10:20.720 - [任务 18][test8] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 11:10:20.725 - [任务 18][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-27 11:10:20.728 - [任务 18][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 11:10:20.730 - [任务 18][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"name":"27e8485f-8855-4ea5-9eae-4f63d14ee0bd","offset":{"{\"server\":\"27e8485f-8855-4ea5-9eae-4f63d14ee0bd\"}":"{\"ts_sec\":1711508076,\"file\":\"binlog.000020\",\"pos\":144640816,\"server_id\":1}"}} 
[INFO ] 2024-03-27 11:10:20.758 - [任务 18][CLAIM] - Starting mysql cdc, server name: 27e8485f-8855-4ea5-9eae-4f63d14ee0bd 
[INFO ] 2024-03-27 11:10:20.808 - [任务 18][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1829049724
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 27e8485f-8855-4ea5-9eae-4f63d14ee0bd
  database.port: 3306
  threadName: Debezium-Mysql-Connector-27e8485f-8855-4ea5-9eae-4f63d14ee0bd
  database.hostname: 127.0.0.1
  database.password: ********
  name: 27e8485f-8855-4ea5-9eae-4f63d14ee0bd
  pdk.offset.string: {"name":"27e8485f-8855-4ea5-9eae-4f63d14ee0bd","offset":{"{\"server\":\"27e8485f-8855-4ea5-9eae-4f63d14ee0bd\"}":"{\"ts_sec\":1711508076,\"file\":\"binlog.000020\",\"pos\":144640816,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 11:10:21.678 - [任务 18][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 11:10:22.192 - [任务 18][CLAIM] - Read DDL: alter table CLAIM rename column `name1` to `name`, about to be packaged as some event(s) 
[INFO ] 2024-03-27 11:10:22.196 - [任务 18][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldNameEvent
  - Offset: MysqlStreamOffset{name='27e8485f-8855-4ea5-9eae-4f63d14ee0bd', offset={{"server":"27e8485f-8855-4ea5-9eae-4f63d14ee0bd"}={"ts_sec":1711509021,"file":"binlog.000020","pos":144728629,"server_id":1}}} 
[INFO ] 2024-03-27 11:10:22.200 - [任务 18][CLAIM] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='CLAIM', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@3d8451a4} 
[WARN ] 2024-03-27 11:10:22.210 - [任务 18][CLAIM] - DDL events are filtered
 - Event: TapAlterFieldNameEvent{tableId='CLAIM', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@3d8451a4}
 - Filter: {} 
[INFO ] 2024-03-27 11:21:15.333 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] running status set to false 
[INFO ] 2024-03-27 11:21:15.336 - [任务 18] - Stop task milestones: 66038a548b5bca60f72de660(任务 18)  
[INFO ] 2024-03-27 11:21:15.337 - [任务 18][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-27 11:21:15.337 - [任务 18][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-f8140253-8afa-48eb-9301-41e313fd6c5d 
[INFO ] 2024-03-27 11:21:15.337 - [任务 18][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-f8140253-8afa-48eb-9301-41e313fd6c5d 
[INFO ] 2024-03-27 11:21:15.338 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] schema data cleaned 
[INFO ] 2024-03-27 11:21:15.338 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] monitor closed 
[INFO ] 2024-03-27 11:21:15.338 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] close complete, cost 154 ms 
[INFO ] 2024-03-27 11:21:15.338 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] running status set to false 
[INFO ] 2024-03-27 11:21:15.338 - [任务 18][test8] - PDK connector node stopped: HazelcastTargetPdkDataNode-e696817e-bab0-4420-a7d2-4d6e478892fa 
[INFO ] 2024-03-27 11:21:15.339 - [任务 18][test8] - PDK connector node released: HazelcastTargetPdkDataNode-e696817e-bab0-4420-a7d2-4d6e478892fa 
[INFO ] 2024-03-27 11:21:15.339 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] schema data cleaned 
[INFO ] 2024-03-27 11:21:15.339 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] monitor closed 
[INFO ] 2024-03-27 11:21:15.339 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] close complete, cost 32 ms 
[INFO ] 2024-03-27 11:21:15.339 - [任务 18] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 11:21:15.339 - [任务 18] - Stopped task aspect(s) 
[INFO ] 2024-03-27 11:21:15.339 - [任务 18] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 11:21:15.372 - [任务 18] - Remove memory task client succeed, task: 任务 18[66038a548b5bca60f72de660] 
[INFO ] 2024-03-27 11:21:15.372 - [任务 18] - Destroy memory task client cache succeed, task: 任务 18[66038a548b5bca60f72de660] 
[INFO ] 2024-03-27 11:21:16.836 - [任务 18] - Start task milestones: 66038a548b5bca60f72de660(任务 18) 
[INFO ] 2024-03-27 11:21:16.837 - [任务 18] - Task initialization... 
[INFO ] 2024-03-27 11:21:16.849 - [任务 18] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 11:21:16.929 - [任务 18] - The engine receives 任务 18 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 11:21:16.987 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:21:16.987 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:21:17.016 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] preload schema finished, cost 29 ms 
[INFO ] 2024-03-27 11:21:17.017 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] preload schema finished, cost 28 ms 
[INFO ] 2024-03-27 11:21:17.266 - [任务 18][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 11:21:17.266 - [任务 18][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 11:21:17.266 - [任务 18][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-27 11:21:17.268 - [任务 18][CLAIM] - batch offset found: {},stream offset found: {"name":"27e8485f-8855-4ea5-9eae-4f63d14ee0bd","offset":{"{\"server\":\"27e8485f-8855-4ea5-9eae-4f63d14ee0bd\"}":"{\"ts_sec\":1711509021,\"file\":\"binlog.000020\",\"pos\":144728629,\"server_id\":1}"}} 
[INFO ] 2024-03-27 11:21:17.310 - [任务 18][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-27 11:21:17.310 - [任务 18][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 11:21:17.311 - [任务 18][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"name":"27e8485f-8855-4ea5-9eae-4f63d14ee0bd","offset":{"{\"server\":\"27e8485f-8855-4ea5-9eae-4f63d14ee0bd\"}":"{\"ts_sec\":1711509021,\"file\":\"binlog.000020\",\"pos\":144728629,\"server_id\":1}"}} 
[INFO ] 2024-03-27 11:21:17.318 - [任务 18][CLAIM] - Starting mysql cdc, server name: 27e8485f-8855-4ea5-9eae-4f63d14ee0bd 
[INFO ] 2024-03-27 11:21:17.323 - [任务 18][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 991934219
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 27e8485f-8855-4ea5-9eae-4f63d14ee0bd
  database.port: 3306
  threadName: Debezium-Mysql-Connector-27e8485f-8855-4ea5-9eae-4f63d14ee0bd
  database.hostname: 127.0.0.1
  database.password: ********
  name: 27e8485f-8855-4ea5-9eae-4f63d14ee0bd
  pdk.offset.string: {"name":"27e8485f-8855-4ea5-9eae-4f63d14ee0bd","offset":{"{\"server\":\"27e8485f-8855-4ea5-9eae-4f63d14ee0bd\"}":"{\"ts_sec\":1711509021,\"file\":\"binlog.000020\",\"pos\":144728629,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 11:21:17.351 - [任务 18][test8] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 11:21:17.407 - [任务 18][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 11:21:50.928 - [任务 18][CLAIM] - Read DDL: alter table CLAIM rename column `name` to `name1`, about to be packaged as some event(s) 
[INFO ] 2024-03-27 11:21:50.932 - [任务 18][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldNameEvent
  - Offset: MysqlStreamOffset{name='27e8485f-8855-4ea5-9eae-4f63d14ee0bd', offset={{"server":"27e8485f-8855-4ea5-9eae-4f63d14ee0bd"}={"ts_sec":1711509710,"file":"binlog.000020","pos":144728890,"server_id":1}}} 
[INFO ] 2024-03-27 11:21:50.932 - [任务 18][CLAIM] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='CLAIM', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@5fa308d6} 
[WARN ] 2024-03-27 11:21:50.932 - [任务 18][CLAIM] - DDL events are filtered
 - Event: TapAlterFieldNameEvent{tableId='CLAIM', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@5fa308d6}
 - Filter: {} 
[INFO ] 2024-03-27 11:22:27.990 - [任务 18] - Stop task milestones: 66038a548b5bca60f72de660(任务 18)  
[INFO ] 2024-03-27 11:22:28.026 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] running status set to false 
[INFO ] 2024-03-27 11:22:28.081 - [任务 18][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-27 11:22:28.085 - [任务 18][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-03-27 11:22:28.094 - [任务 18][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-f8140253-8afa-48eb-9301-41e313fd6c5d 
[INFO ] 2024-03-27 11:22:28.095 - [任务 18][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-f8140253-8afa-48eb-9301-41e313fd6c5d 
[INFO ] 2024-03-27 11:22:28.095 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] schema data cleaned 
[INFO ] 2024-03-27 11:22:28.095 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] monitor closed 
[INFO ] 2024-03-27 11:22:28.096 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] close complete, cost 82 ms 
[INFO ] 2024-03-27 11:22:28.097 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] running status set to false 
[INFO ] 2024-03-27 11:22:28.134 - [任务 18][test8] - PDK connector node stopped: HazelcastTargetPdkDataNode-e696817e-bab0-4420-a7d2-4d6e478892fa 
[INFO ] 2024-03-27 11:22:28.145 - [任务 18][test8] - PDK connector node released: HazelcastTargetPdkDataNode-e696817e-bab0-4420-a7d2-4d6e478892fa 
[INFO ] 2024-03-27 11:22:28.152 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] schema data cleaned 
[INFO ] 2024-03-27 11:22:28.156 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] monitor closed 
[INFO ] 2024-03-27 11:22:28.157 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] close complete, cost 36 ms 
[INFO ] 2024-03-27 11:22:30.450 - [任务 18] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 11:22:30.452 - [任务 18] - Stopped task aspect(s) 
[INFO ] 2024-03-27 11:22:30.453 - [任务 18] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 11:22:30.472 - [任务 18] - Remove memory task client succeed, task: 任务 18[66038a548b5bca60f72de660] 
[INFO ] 2024-03-27 11:22:30.475 - [任务 18] - Destroy memory task client cache succeed, task: 任务 18[66038a548b5bca60f72de660] 
[INFO ] 2024-03-27 11:22:38.032 - [任务 18] - Start task milestones: 66038a548b5bca60f72de660(任务 18) 
[INFO ] 2024-03-27 11:22:38.039 - [任务 18] - Task initialization... 
[INFO ] 2024-03-27 11:22:38.059 - [任务 18] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 11:22:38.301 - [任务 18] - The engine receives 任务 18 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 11:22:38.378 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:22:38.379 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] start preload schema,table counts: 1 
[INFO ] 2024-03-27 11:22:38.399 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] preload schema finished, cost 19 ms 
[INFO ] 2024-03-27 11:22:38.399 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] preload schema finished, cost 20 ms 
[INFO ] 2024-03-27 11:22:38.664 - [任务 18][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 11:22:38.665 - [任务 18][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 11:22:38.665 - [任务 18][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-27 11:22:38.667 - [任务 18][CLAIM] - batch offset found: {},stream offset found: "_tap_zip_\u001F\b\u0000\u0000\u0000\u0000\u0000\u0000\u0000-Ë£0\fE÷©Ê¿\u0018\u0012¦*^\b\u001CÀ\f\u000Eáa{\u0007$áiÒ3$áñõíé\u0016RJW:W\u0015\u001CÍ¿!\u0002Ñ\u00050ý*\u0016\u0003qfvEµ/é«FÏwÑ«lÇÑ¹°¾\u0018¢¯Û=cÖ¯e6Ç\u0017×ëy¦}n7BU°¨¹/;\u0007:­+\u0002ÐM`:F]d©:ã\u0005¹´u~><3\u0006_£¨ýS¬\u0000@¼Þz\u0004p\b¶÷¤vqúÑGZ]âÇ¾tOàa²\u0013qBJ?®ö4N\u001EÔIkzFÈìÞ#I[Ú\u0005qÔØlKí\u0006Lû\u000Fg¢ö\u0014åÙô<;ö\u001FÕVH\\}t±-q\u0004\u0013XQ\u0005¿\u000BçP£¨w\u001DX,º2]sç0\n·¶OÅø?ìõSÉ¿d^\u0000æëâ)\u0017.\u0018iÂ,TOö\u0014WhÐ>Ä¦øz®Ã,­Å\u0019!õ·¯\u0002\u001FÈËSsc'8\u000Fcsqé­ÔÓ´÷®ÇÆ`\u0004Ì¾åÉ<{Õo¸úâ\u001Dü\u0016¦\u0000r\u001AXd$ù*\u0017ò+O®dO[b(ÕË»åÙ©\t[ûpíæ\u001B¶Xø\u0006\u0002\u0000\u0000" 
[INFO ] 2024-03-27 11:22:38.727 - [任务 18][test8] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 11:22:38.727 - [任务 18][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-27 11:22:38.727 - [任务 18][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 11:22:38.736 - [任务 18][CLAIM] - Starting stream read, table list: [CLAIM], offset: "_tap_zip_\u001F\b\u0000\u0000\u0000\u0000\u0000\u0000\u0000-Ë£0\fE÷©Ê¿\u0018\u0012¦*^\b\u001CÀ\f\u000Eáa{\u0007$áiÒ3$áñõíé\u0016RJW:W\u0015\u001CÍ¿!\u0002Ñ\u00050ý*\u0016\u0003qfvEµ/é«FÏwÑ«lÇÑ¹°¾\u0018¢¯Û=cÖ¯e6Ç\u0017×ëy¦}n7BU°¨¹/;\u0007:­+\u0002ÐM`:F]d©:ã\u0005¹´u~><3\u0006_£¨ýS¬\u0000@¼Þz\u0004p\b¶÷¤vqúÑGZ]âÇ¾tOàa²\u0013qBJ?®ö4N\u001EÔIkzFÈìÞ#I[Ú\u0005qÔØlKí\u0006Lû\u000Fg¢ö\u0014åÙô<;ö\u001FÕVH\\}t±-q\u0004\u0013XQ\u0005¿\u000BçP£¨w\u001DX,º2]sç0\n·¶OÅø?ìõSÉ¿d^\u0000æëâ)\u0017.\u0018iÂ,TOö\u0014WhÐ>Ä¦øz®Ã,­Å\u0019!õ·¯\u0002\u001FÈËSsc'8\u000Fcsqé­ÔÓ´÷®ÇÆ`\u0004Ì¾åÉ<{Õo¸úâ\u001Dü\u0016¦\u0000r\u001AXd$ù*\u0017ò+O®dO[b(ÕË»åÙ©\t[ûpíæ\u001B¶Xø\u0006\u0002\u0000\u0000" 
[INFO ] 2024-03-27 11:22:38.744 - [任务 18][CLAIM] - Starting mysql cdc, server name: 27e8485f-8855-4ea5-9eae-4f63d14ee0bd 
[INFO ] 2024-03-27 11:22:38.747 - [任务 18][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 202813928
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 27e8485f-8855-4ea5-9eae-4f63d14ee0bd
  database.port: 3306
  threadName: Debezium-Mysql-Connector-27e8485f-8855-4ea5-9eae-4f63d14ee0bd
  database.hostname: 127.0.0.1
  database.password: ********
  name: 27e8485f-8855-4ea5-9eae-4f63d14ee0bd
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 11:22:38.786 - [任务 18][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-03-27 11:22:38.793 - [任务 18][CLAIM] - Incremental sync completed 
[ERROR] 2024-03-27 11:22:42.259 - [任务 18][CLAIM] - java.lang.RuntimeException: io.debezium.DebeziumException: Could not find existing binlog information while attempting schema only recovery snapshot <-- Error Message -->
java.lang.RuntimeException: io.debezium.DebeziumException: Could not find existing binlog information while attempting schema only recovery snapshot

<-- Simple Stack Trace -->
Caused by: io.debezium.DebeziumException: Could not find existing binlog information while attempting schema only recovery snapshot
	io.debezium.connector.mysql.MySqlConnectorTask.validateAndLoadDatabaseHistory(MySqlConnectorTask.java:300)
	io.debezium.connector.mysql.MySqlConnectorTask.start(MySqlConnectorTask.java:110)
	io.debezium.connector.common.BaseSourceTask.start(BaseSourceTask.java:130)
	io.debezium.embedded.EmbeddedEngine.run(EmbeddedEngine.java:759)
	io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:328)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: io.debezium.DebeziumException: Could not find existing binlog information while attempting schema only recovery snapshot
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: io.debezium.DebeziumException: Could not find existing binlog information while attempting schema only recovery snapshot
	at io.tapdata.common.exception.AbstractExceptionCollector.collectCdcConfigInvalid(AbstractExceptionCollector.java:63)
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:142)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:333)
	at io.tapdata.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:458)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: io.debezium.DebeziumException: Could not find existing binlog information while attempting schema only recovery snapshot
	at io.debezium.connector.mysql.MySqlConnectorTask.validateAndLoadDatabaseHistory(MySqlConnectorTask.java:300)
	at io.debezium.connector.mysql.MySqlConnectorTask.start(MySqlConnectorTask.java:110)
	at io.debezium.connector.common.BaseSourceTask.start(BaseSourceTask.java:130)
	at io.debezium.embedded.EmbeddedEngine.run(EmbeddedEngine.java:759)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:328)
	... 19 more

[INFO ] 2024-03-27 11:22:42.274 - [任务 18][CLAIM] - Job suspend in error handle 
[INFO ] 2024-03-27 11:22:42.689 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] running status set to false 
[INFO ] 2024-03-27 11:22:42.718 - [任务 18][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-f8140253-8afa-48eb-9301-41e313fd6c5d 
[INFO ] 2024-03-27 11:22:42.720 - [任务 18][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-f8140253-8afa-48eb-9301-41e313fd6c5d 
[INFO ] 2024-03-27 11:22:42.726 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] schema data cleaned 
[INFO ] 2024-03-27 11:22:42.726 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] monitor closed 
[INFO ] 2024-03-27 11:22:42.734 - [任务 18][CLAIM] - Node CLAIM[f8140253-8afa-48eb-9301-41e313fd6c5d] close complete, cost 58 ms 
[INFO ] 2024-03-27 11:22:42.735 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] running status set to false 
[INFO ] 2024-03-27 11:22:42.749 - [任务 18][test8] - PDK connector node stopped: HazelcastTargetPdkDataNode-e696817e-bab0-4420-a7d2-4d6e478892fa 
[INFO ] 2024-03-27 11:22:42.749 - [任务 18][test8] - PDK connector node released: HazelcastTargetPdkDataNode-e696817e-bab0-4420-a7d2-4d6e478892fa 
[INFO ] 2024-03-27 11:22:42.749 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] schema data cleaned 
[INFO ] 2024-03-27 11:22:42.749 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] monitor closed 
[INFO ] 2024-03-27 11:22:42.749 - [任务 18][test8] - Node test8[e696817e-bab0-4420-a7d2-4d6e478892fa] close complete, cost 19 ms 
[INFO ] 2024-03-27 11:22:47.263 - [任务 18] - Task [任务 18] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-27 11:22:47.303 - [任务 18] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 11:22:47.334 - [任务 18] - Stop task milestones: 66038a548b5bca60f72de660(任务 18)  
[INFO ] 2024-03-27 11:22:47.354 - [任务 18] - Stopped task aspect(s) 
[INFO ] 2024-03-27 11:22:47.362 - [任务 18] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 11:22:47.379 - [任务 18] - Remove memory task client succeed, task: 任务 18[66038a548b5bca60f72de660] 
[INFO ] 2024-03-27 11:22:47.387 - [任务 18] - Destroy memory task client cache succeed, task: 任务 18[66038a548b5bca60f72de660] 
