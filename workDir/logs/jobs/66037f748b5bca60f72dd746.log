[INFO ] 2024-03-27 10:28:18.553 - [任务 16] - Start task milestones: 66037f748b5bca60f72dd746(任务 16) 
[INFO ] 2024-03-27 10:28:18.553 - [任务 16] - Task initialization... 
[INFO ] 2024-03-27 10:28:18.561 - [任务 16] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 10:28:18.629 - [任务 16] - The engine receives 任务 16 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 10:28:18.686 - [任务 16][test4] - Node test4[97d54fa4-ff14-45f7-98d9-926505004474] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:28:18.691 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:28:18.691 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:28:18.733 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] preload schema finished, cost 48 ms 
[INFO ] 2024-03-27 10:28:18.734 - [任务 16][test4] - Node test4[97d54fa4-ff14-45f7-98d9-926505004474] preload schema finished, cost 48 ms 
[INFO ] 2024-03-27 10:28:18.734 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] preload schema finished, cost 48 ms 
[INFO ] 2024-03-27 10:28:19.325 - [任务 16][test4] - Source node "test4" read batch size: 100 
[INFO ] 2024-03-27 10:28:19.327 - [任务 16][test4] - Source node "test4" event queue capacity: 200 
[INFO ] 2024-03-27 10:28:19.327 - [任务 16][test4] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-27 10:28:19.327 - [任务 16][test4] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-27 10:28:19.465 - [任务 16][test4] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-27 10:28:19.466 - [任务 16][test4] - Initial sync started 
[INFO ] 2024-03-27 10:28:19.466 - [任务 16][test4] - Starting batch read, table name: test4, offset: null 
[INFO ] 2024-03-27 10:28:19.468 - [任务 16][test4] - Table test4 is going to be initial synced 
[INFO ] 2024-03-27 10:28:19.478 - [任务 16][CLAIMBACK] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 10:28:26.315 - [任务 16][test4] - Initial sync completed 
[INFO ] 2024-03-27 10:28:26.317 - [任务 16][test4] - Incremental sync starting... 
[INFO ] 2024-03-27 10:28:26.318 - [任务 16][test4] - Initial sync completed 
[INFO ] 2024-03-27 10:28:26.319 - [任务 16][test4] - Starting stream read, table list: [test4], offset: [] 
[INFO ] 2024-03-27 10:30:12.592 - [任务 16] - Stop task milestones: 66037f748b5bca60f72dd746(任务 16)  
[INFO ] 2024-03-27 10:30:12.758 - [任务 16][test4] - Node test4[97d54fa4-ff14-45f7-98d9-926505004474] running status set to false 
[ERROR] 2024-03-27 10:30:21.708 - [任务 16][test4] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-27 10:30:21.715 - [任务 16][test4] - PDK connector node stopped: HazelcastSourcePdkDataNode-97d54fa4-ff14-45f7-98d9-926505004474 
[INFO ] 2024-03-27 10:30:21.715 - [任务 16][test4] - PDK connector node released: HazelcastSourcePdkDataNode-97d54fa4-ff14-45f7-98d9-926505004474 
[INFO ] 2024-03-27 10:30:21.715 - [任务 16][test4] - Node test4[97d54fa4-ff14-45f7-98d9-926505004474] schema data cleaned 
[INFO ] 2024-03-27 10:30:21.715 - [任务 16][test4] - Node test4[97d54fa4-ff14-45f7-98d9-926505004474] monitor closed 
[INFO ] 2024-03-27 10:30:21.715 - [任务 16][test4] - Node test4[97d54fa4-ff14-45f7-98d9-926505004474] close complete, cost 8956 ms 
[INFO ] 2024-03-27 10:30:21.715 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] running status set to false 
[INFO ] 2024-03-27 10:30:21.739 - [任务 16][增强JS] - PDK connector node stopped: ScriptExecutor-Test-5e95c354-6cb3-44f7-b337-5abf174966e1 
[INFO ] 2024-03-27 10:30:21.747 - [任务 16][增强JS] - PDK connector node released: ScriptExecutor-Test-5e95c354-6cb3-44f7-b337-5abf174966e1 
[INFO ] 2024-03-27 10:30:21.748 - [任务 16][增强JS] - [ScriptExecutorsManager-66037f748b5bca60f72dd746-f09dc8ae-fc5a-4997-b587-6661d20f42f2-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 10:30:21.750 - [任务 16][增强JS] - PDK connector node stopped: ScriptExecutor-testMysql-4739445c-56f3-492b-8635-7467e3e591d6 
[INFO ] 2024-03-27 10:30:21.750 - [任务 16][增强JS] - PDK connector node released: ScriptExecutor-testMysql-4739445c-56f3-492b-8635-7467e3e591d6 
[INFO ] 2024-03-27 10:30:21.750 - [任务 16][增强JS] - [ScriptExecutorsManager-66037f748b5bca60f72dd746-f09dc8ae-fc5a-4997-b587-6661d20f42f2-65fd534767def503a78ea02d] schema data cleaned 
[INFO ] 2024-03-27 10:30:21.762 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] schema data cleaned 
[INFO ] 2024-03-27 10:30:21.762 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] monitor closed 
[INFO ] 2024-03-27 10:30:21.762 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] close complete, cost 48 ms 
[INFO ] 2024-03-27 10:30:21.762 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] running status set to false 
[INFO ] 2024-03-27 10:30:21.780 - [任务 16][CLAIMBACK] - PDK connector node stopped: HazelcastTargetPdkDataNode-c496a6d2-b36a-4de3-8435-38942846a5c2 
[INFO ] 2024-03-27 10:30:21.781 - [任务 16][CLAIMBACK] - PDK connector node released: HazelcastTargetPdkDataNode-c496a6d2-b36a-4de3-8435-38942846a5c2 
[INFO ] 2024-03-27 10:30:21.781 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] schema data cleaned 
[INFO ] 2024-03-27 10:30:21.781 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] monitor closed 
[INFO ] 2024-03-27 10:30:21.781 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] close complete, cost 17 ms 
[INFO ] 2024-03-27 10:30:26.742 - [任务 16] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 10:30:26.743 - [任务 16] - Stopped task aspect(s) 
[INFO ] 2024-03-27 10:30:26.744 - [任务 16] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 10:30:26.782 - [任务 16] - Remove memory task client succeed, task: 任务 16[66037f748b5bca60f72dd746] 
[INFO ] 2024-03-27 10:30:26.783 - [任务 16] - Destroy memory task client cache succeed, task: 任务 16[66037f748b5bca60f72dd746] 
[INFO ] 2024-03-27 10:30:49.791 - [任务 16] - Start task milestones: 66037f748b5bca60f72dd746(任务 16) 
[INFO ] 2024-03-27 10:30:49.794 - [任务 16] - Task initialization... 
[INFO ] 2024-03-27 10:30:49.816 - [任务 16] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 10:30:49.911 - [任务 16] - The engine receives 任务 16 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 10:30:49.999 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:30:50.003 - [任务 16][test4] - Node test6[97d54fa4-ff14-45f7-98d9-926505004474] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:30:50.003 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:30:50.035 - [任务 16][test4] - Node test6[97d54fa4-ff14-45f7-98d9-926505004474] preload schema finished, cost 34 ms 
[INFO ] 2024-03-27 10:30:50.039 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] preload schema finished, cost 35 ms 
[INFO ] 2024-03-27 10:30:50.039 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] preload schema finished, cost 34 ms 
[INFO ] 2024-03-27 10:30:50.343 - [任务 16][CLAIMBACK] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 10:30:50.345 - [任务 16][CLAIMBACK] - Sync progress not exists, will run task as first time 
[INFO ] 2024-03-27 10:30:50.400 - [任务 16][test4] - Source node "test6" read batch size: 100 
[INFO ] 2024-03-27 10:30:50.400 - [任务 16][test4] - Source node "test6" event queue capacity: 200 
[INFO ] 2024-03-27 10:30:50.400 - [任务 16][test4] - Sync progress not exists, will run task as first time 
[INFO ] 2024-03-27 10:30:50.400 - [任务 16][test4] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-27 10:30:50.401 - [任务 16][test4] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-27 10:30:50.466 - [任务 16][test4] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-27 10:30:50.466 - [任务 16][test4] - Initial sync started 
[INFO ] 2024-03-27 10:30:50.466 - [任务 16][test4] - Starting batch read, table name: test6, offset: null 
[INFO ] 2024-03-27 10:30:50.466 - [任务 16][test4] - Table test6 is going to be initial synced 
[INFO ] 2024-03-27 10:30:57.022 - [任务 16][test4] - Initial sync completed 
[INFO ] 2024-03-27 10:30:57.024 - [任务 16][test4] - Incremental sync starting... 
[INFO ] 2024-03-27 10:30:57.024 - [任务 16][test4] - Initial sync completed 
[INFO ] 2024-03-27 10:30:57.025 - [任务 16][test4] - Starting stream read, table list: [test6], offset: [] 
[INFO ] 2024-03-27 10:31:27.882 - [任务 16] - Stop task milestones: 66037f748b5bca60f72dd746(任务 16)  
[INFO ] 2024-03-27 10:31:28.063 - [任务 16][test4] - Node test6[97d54fa4-ff14-45f7-98d9-926505004474] running status set to false 
[ERROR] 2024-03-27 10:31:30.637 - [任务 16][test4] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-27 10:31:30.650 - [任务 16][test4] - PDK connector node stopped: HazelcastSourcePdkDataNode-97d54fa4-ff14-45f7-98d9-926505004474 
[INFO ] 2024-03-27 10:31:30.651 - [任务 16][test4] - PDK connector node released: HazelcastSourcePdkDataNode-97d54fa4-ff14-45f7-98d9-926505004474 
[INFO ] 2024-03-27 10:31:30.651 - [任务 16][test4] - Node test6[97d54fa4-ff14-45f7-98d9-926505004474] schema data cleaned 
[INFO ] 2024-03-27 10:31:30.651 - [任务 16][test4] - Node test6[97d54fa4-ff14-45f7-98d9-926505004474] monitor closed 
[INFO ] 2024-03-27 10:31:30.657 - [任务 16][test4] - Node test6[97d54fa4-ff14-45f7-98d9-926505004474] close complete, cost 2588 ms 
[INFO ] 2024-03-27 10:31:30.658 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] running status set to false 
[INFO ] 2024-03-27 10:31:30.667 - [任务 16][增强JS] - PDK connector node stopped: ScriptExecutor-Test-d9922b4c-a43f-44ce-96bf-35578a1a2614 
[INFO ] 2024-03-27 10:31:30.671 - [任务 16][增强JS] - PDK connector node released: ScriptExecutor-Test-d9922b4c-a43f-44ce-96bf-35578a1a2614 
[INFO ] 2024-03-27 10:31:30.672 - [任务 16][增强JS] - [ScriptExecutorsManager-66037f748b5bca60f72dd746-f09dc8ae-fc5a-4997-b587-6661d20f42f2-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 10:31:30.694 - [任务 16][增强JS] - PDK connector node stopped: ScriptExecutor-testMysql-eefc78c0-d1fb-40ec-b25a-2b091496651a 
[INFO ] 2024-03-27 10:31:30.694 - [任务 16][增强JS] - PDK connector node released: ScriptExecutor-testMysql-eefc78c0-d1fb-40ec-b25a-2b091496651a 
[INFO ] 2024-03-27 10:31:30.695 - [任务 16][增强JS] - [ScriptExecutorsManager-66037f748b5bca60f72dd746-f09dc8ae-fc5a-4997-b587-6661d20f42f2-65fd534767def503a78ea02d] schema data cleaned 
[INFO ] 2024-03-27 10:31:30.705 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] schema data cleaned 
[INFO ] 2024-03-27 10:31:30.705 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] monitor closed 
[INFO ] 2024-03-27 10:31:30.706 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] close complete, cost 54 ms 
[INFO ] 2024-03-27 10:31:30.706 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] running status set to false 
[INFO ] 2024-03-27 10:31:30.720 - [任务 16][CLAIMBACK] - PDK connector node stopped: HazelcastTargetPdkDataNode-c496a6d2-b36a-4de3-8435-38942846a5c2 
[INFO ] 2024-03-27 10:31:30.721 - [任务 16][CLAIMBACK] - PDK connector node released: HazelcastTargetPdkDataNode-c496a6d2-b36a-4de3-8435-38942846a5c2 
[INFO ] 2024-03-27 10:31:30.721 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] schema data cleaned 
[INFO ] 2024-03-27 10:31:30.721 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] monitor closed 
[INFO ] 2024-03-27 10:31:30.721 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] close complete, cost 15 ms 
[INFO ] 2024-03-27 10:31:31.861 - [任务 16] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 10:31:31.862 - [任务 16] - Stopped task aspect(s) 
[INFO ] 2024-03-27 10:31:31.862 - [任务 16] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 10:31:31.882 - [任务 16] - Remove memory task client succeed, task: 任务 16[66037f748b5bca60f72dd746] 
[INFO ] 2024-03-27 10:31:31.885 - [任务 16] - Destroy memory task client cache succeed, task: 任务 16[66037f748b5bca60f72dd746] 
[INFO ] 2024-03-27 10:32:00.385 - [任务 16] - Start task milestones: 66037f748b5bca60f72dd746(任务 16) 
[INFO ] 2024-03-27 10:32:00.389 - [任务 16] - Task initialization... 
[INFO ] 2024-03-27 10:32:00.407 - [任务 16] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 10:32:00.473 - [任务 16] - The engine receives 任务 16 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 10:32:00.539 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:32:00.540 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:32:00.540 - [任务 16][test4] - Node test6[97d54fa4-ff14-45f7-98d9-926505004474] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:32:00.555 - [任务 16][test4] - Node test6[97d54fa4-ff14-45f7-98d9-926505004474] preload schema finished, cost 14 ms 
[INFO ] 2024-03-27 10:32:00.556 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] preload schema finished, cost 16 ms 
[INFO ] 2024-03-27 10:32:00.556 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] preload schema finished, cost 14 ms 
[INFO ] 2024-03-27 10:32:00.834 - [任务 16][CLAIMBACK] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 10:32:00.835 - [任务 16][CLAIMBACK] - Sync progress not exists, will run task as first time 
[INFO ] 2024-03-27 10:32:00.901 - [任务 16][test4] - Source node "test6" read batch size: 100 
[INFO ] 2024-03-27 10:32:00.903 - [任务 16][test4] - Source node "test6" event queue capacity: 200 
[INFO ] 2024-03-27 10:32:00.903 - [任务 16][test4] - Sync progress not exists, will run task as first time 
[INFO ] 2024-03-27 10:32:00.903 - [任务 16][test4] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-27 10:32:00.903 - [任务 16][test4] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-27 10:32:00.979 - [任务 16][test4] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-27 10:32:00.980 - [任务 16][test4] - Initial sync started 
[INFO ] 2024-03-27 10:32:00.980 - [任务 16][test4] - Starting batch read, table name: test6, offset: null 
[INFO ] 2024-03-27 10:32:00.980 - [任务 16][test4] - Table test6 is going to be initial synced 
[INFO ] 2024-03-27 10:32:07.521 - [任务 16][test4] - Initial sync completed 
[INFO ] 2024-03-27 10:32:07.527 - [任务 16][test4] - Incremental sync starting... 
[INFO ] 2024-03-27 10:32:07.527 - [任务 16][test4] - Initial sync completed 
[INFO ] 2024-03-27 10:32:07.527 - [任务 16][test4] - Starting stream read, table list: [test6], offset: [] 
[INFO ] 2024-03-27 10:32:59.293 - [任务 16] - Stop task milestones: 66037f748b5bca60f72dd746(任务 16)  
[INFO ] 2024-03-27 10:32:59.466 - [任务 16][test4] - Node test6[97d54fa4-ff14-45f7-98d9-926505004474] running status set to false 
[ERROR] 2024-03-27 10:32:59.479 - [任务 16][test4] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-27 10:32:59.489 - [任务 16][test4] - PDK connector node stopped: HazelcastSourcePdkDataNode-97d54fa4-ff14-45f7-98d9-926505004474 
[INFO ] 2024-03-27 10:32:59.490 - [任务 16][test4] - PDK connector node released: HazelcastSourcePdkDataNode-97d54fa4-ff14-45f7-98d9-926505004474 
[INFO ] 2024-03-27 10:32:59.490 - [任务 16][test4] - Node test6[97d54fa4-ff14-45f7-98d9-926505004474] schema data cleaned 
[INFO ] 2024-03-27 10:32:59.490 - [任务 16][test4] - Node test6[97d54fa4-ff14-45f7-98d9-926505004474] monitor closed 
[INFO ] 2024-03-27 10:32:59.491 - [任务 16][test4] - Node test6[97d54fa4-ff14-45f7-98d9-926505004474] close complete, cost 34 ms 
[INFO ] 2024-03-27 10:32:59.491 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] running status set to false 
[INFO ] 2024-03-27 10:32:59.505 - [任务 16][增强JS] - PDK connector node stopped: ScriptExecutor-Test-a52ba35d-ffe0-4884-aaee-78184666e91a 
[INFO ] 2024-03-27 10:32:59.508 - [任务 16][增强JS] - PDK connector node released: ScriptExecutor-Test-a52ba35d-ffe0-4884-aaee-78184666e91a 
[INFO ] 2024-03-27 10:32:59.510 - [任务 16][增强JS] - [ScriptExecutorsManager-66037f748b5bca60f72dd746-f09dc8ae-fc5a-4997-b587-6661d20f42f2-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 10:32:59.514 - [任务 16][增强JS] - PDK connector node stopped: ScriptExecutor-testMysql-5b5fdac9-b593-4854-a360-5d992e3756cc 
[INFO ] 2024-03-27 10:32:59.516 - [任务 16][增强JS] - PDK connector node released: ScriptExecutor-testMysql-5b5fdac9-b593-4854-a360-5d992e3756cc 
[INFO ] 2024-03-27 10:32:59.516 - [任务 16][增强JS] - [ScriptExecutorsManager-66037f748b5bca60f72dd746-f09dc8ae-fc5a-4997-b587-6661d20f42f2-65fd534767def503a78ea02d] schema data cleaned 
[INFO ] 2024-03-27 10:32:59.526 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] schema data cleaned 
[INFO ] 2024-03-27 10:32:59.530 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] monitor closed 
[INFO ] 2024-03-27 10:32:59.530 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] close complete, cost 35 ms 
[INFO ] 2024-03-27 10:32:59.530 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] running status set to false 
[INFO ] 2024-03-27 10:32:59.533 - [任务 16][CLAIMBACK] - PDK connector node stopped: HazelcastTargetPdkDataNode-c496a6d2-b36a-4de3-8435-38942846a5c2 
[INFO ] 2024-03-27 10:32:59.533 - [任务 16][CLAIMBACK] - PDK connector node released: HazelcastTargetPdkDataNode-c496a6d2-b36a-4de3-8435-38942846a5c2 
[INFO ] 2024-03-27 10:32:59.533 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] schema data cleaned 
[INFO ] 2024-03-27 10:32:59.533 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] monitor closed 
[INFO ] 2024-03-27 10:32:59.533 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] close complete, cost 6 ms 
[INFO ] 2024-03-27 10:33:01.989 - [任务 16] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 10:33:01.991 - [任务 16] - Stopped task aspect(s) 
[INFO ] 2024-03-27 10:33:01.991 - [任务 16] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 10:33:02.006 - [任务 16] - Remove memory task client succeed, task: 任务 16[66037f748b5bca60f72dd746] 
[INFO ] 2024-03-27 10:33:02.006 - [任务 16] - Destroy memory task client cache succeed, task: 任务 16[66037f748b5bca60f72dd746] 
[INFO ] 2024-03-27 10:34:03.545 - [任务 16] - Start task milestones: 66037f748b5bca60f72dd746(任务 16) 
[INFO ] 2024-03-27 10:34:03.549 - [任务 16] - Task initialization... 
[INFO ] 2024-03-27 10:34:03.577 - [任务 16] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 10:34:03.696 - [任务 16] - The engine receives 任务 16 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 10:34:03.760 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:34:03.770 - [任务 16][test4] - Node test6[97d54fa4-ff14-45f7-98d9-926505004474] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:34:03.770 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:34:03.804 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] preload schema finished, cost 42 ms 
[INFO ] 2024-03-27 10:34:03.806 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] preload schema finished, cost 43 ms 
[INFO ] 2024-03-27 10:34:03.806 - [任务 16][test4] - Node test6[97d54fa4-ff14-45f7-98d9-926505004474] preload schema finished, cost 43 ms 
[INFO ] 2024-03-27 10:34:04.143 - [任务 16][test4] - Source node "test6" read batch size: 100 
[INFO ] 2024-03-27 10:34:04.144 - [任务 16][test4] - Source node "test6" event queue capacity: 200 
[INFO ] 2024-03-27 10:34:04.147 - [任务 16][test4] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-27 10:34:04.148 - [任务 16][test4] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-27 10:34:04.182 - [任务 16][test4] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-27 10:34:04.183 - [任务 16][test4] - Initial sync started 
[INFO ] 2024-03-27 10:34:04.183 - [任务 16][test4] - Starting batch read, table name: test6, offset: null 
[INFO ] 2024-03-27 10:34:04.183 - [任务 16][test4] - Table test6 is going to be initial synced 
[INFO ] 2024-03-27 10:34:04.516 - [任务 16][CLAIMBACK] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 10:34:10.862 - [任务 16][test4] - Initial sync completed 
[INFO ] 2024-03-27 10:34:10.882 - [任务 16][test4] - Incremental sync starting... 
[INFO ] 2024-03-27 10:34:10.885 - [任务 16][test4] - Initial sync completed 
[INFO ] 2024-03-27 10:34:10.888 - [任务 16][test4] - Starting stream read, table list: [test6], offset: [] 
[INFO ] 2024-03-27 10:35:57.675 - [任务 16] - Stop task milestones: 66037f748b5bca60f72dd746(任务 16)  
[INFO ] 2024-03-27 10:35:57.782 - [任务 16][test4] - Node test6[97d54fa4-ff14-45f7-98d9-926505004474] running status set to false 
[ERROR] 2024-03-27 10:35:57.816 - [任务 16][test4] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:316)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:583)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:314)
	... 18 more

[INFO ] 2024-03-27 10:35:57.822 - [任务 16][test4] - PDK connector node stopped: HazelcastSourcePdkDataNode-97d54fa4-ff14-45f7-98d9-926505004474 
[INFO ] 2024-03-27 10:35:57.823 - [任务 16][test4] - PDK connector node released: HazelcastSourcePdkDataNode-97d54fa4-ff14-45f7-98d9-926505004474 
[INFO ] 2024-03-27 10:35:57.823 - [任务 16][test4] - Node test6[97d54fa4-ff14-45f7-98d9-926505004474] schema data cleaned 
[INFO ] 2024-03-27 10:35:57.823 - [任务 16][test4] - Node test6[97d54fa4-ff14-45f7-98d9-926505004474] monitor closed 
[INFO ] 2024-03-27 10:35:57.824 - [任务 16][test4] - Node test6[97d54fa4-ff14-45f7-98d9-926505004474] close complete, cost 44 ms 
[INFO ] 2024-03-27 10:35:57.824 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] running status set to false 
[INFO ] 2024-03-27 10:35:57.836 - [任务 16][增强JS] - PDK connector node stopped: ScriptExecutor-Test-62114647-7d42-459b-b257-be6165720743 
[INFO ] 2024-03-27 10:35:57.836 - [任务 16][增强JS] - PDK connector node released: ScriptExecutor-Test-62114647-7d42-459b-b257-be6165720743 
[INFO ] 2024-03-27 10:35:57.841 - [任务 16][增强JS] - [ScriptExecutorsManager-66037f748b5bca60f72dd746-f09dc8ae-fc5a-4997-b587-6661d20f42f2-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 10:35:57.849 - [任务 16][增强JS] - PDK connector node stopped: ScriptExecutor-testMysql-f54d6e3b-c083-406c-98a2-ff22352fa807 
[INFO ] 2024-03-27 10:35:57.849 - [任务 16][增强JS] - PDK connector node released: ScriptExecutor-testMysql-f54d6e3b-c083-406c-98a2-ff22352fa807 
[INFO ] 2024-03-27 10:35:57.849 - [任务 16][增强JS] - [ScriptExecutorsManager-66037f748b5bca60f72dd746-f09dc8ae-fc5a-4997-b587-6661d20f42f2-65fd534767def503a78ea02d] schema data cleaned 
[INFO ] 2024-03-27 10:35:57.859 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] schema data cleaned 
[INFO ] 2024-03-27 10:35:57.860 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] monitor closed 
[INFO ] 2024-03-27 10:35:57.861 - [任务 16][增强JS] - Node 增强JS[f09dc8ae-fc5a-4997-b587-6661d20f42f2] close complete, cost 43 ms 
[INFO ] 2024-03-27 10:35:57.861 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] running status set to false 
[INFO ] 2024-03-27 10:35:57.870 - [任务 16][CLAIMBACK] - PDK connector node stopped: HazelcastTargetPdkDataNode-c496a6d2-b36a-4de3-8435-38942846a5c2 
[INFO ] 2024-03-27 10:35:57.870 - [任务 16][CLAIMBACK] - PDK connector node released: HazelcastTargetPdkDataNode-c496a6d2-b36a-4de3-8435-38942846a5c2 
[INFO ] 2024-03-27 10:35:57.870 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] schema data cleaned 
[INFO ] 2024-03-27 10:35:57.870 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] monitor closed 
[INFO ] 2024-03-27 10:35:57.871 - [任务 16][CLAIMBACK] - Node CLAIMBACK[c496a6d2-b36a-4de3-8435-38942846a5c2] close complete, cost 8 ms 
[INFO ] 2024-03-27 10:36:02.243 - [任务 16] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 10:36:02.244 - [任务 16] - Stopped task aspect(s) 
[INFO ] 2024-03-27 10:36:02.245 - [任务 16] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 10:36:02.261 - [任务 16] - Remove memory task client succeed, task: 任务 16[66037f748b5bca60f72dd746] 
[INFO ] 2024-03-27 10:36:02.265 - [任务 16] - Destroy memory task client cache succeed, task: 任务 16[66037f748b5bca60f72dd746] 
