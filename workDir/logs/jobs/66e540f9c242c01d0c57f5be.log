[INFO ] 2024-09-22 17:07:05.825 - [测试时区] - Task initialization... 
[INFO ] 2024-09-22 17:07:06.071 - [测试时区] - Start task milestones: 66e540f9c242c01d0c57f5be(测试时区) 
[INFO ] 2024-09-22 17:07:08.071 - [测试时区] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-22 17:07:08.437 - [测试时区] - The engine receives 测试时区 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-22 17:07:09.527 - [测试时区][mysql3307] - Node mysql3307[3649b1e3-fd31-45ae-a716-10959e6f7b2f] start preload schema,table counts: 1 
[INFO ] 2024-09-22 17:07:09.530 - [测试时区][localmaster] - Node localmaster[ce5b89f4-1534-411d-a2e9-52ba4714f092] start preload schema,table counts: 1 
[INFO ] 2024-09-22 17:07:09.531 - [测试时区][mysql3307] - Node mysql3307[3649b1e3-fd31-45ae-a716-10959e6f7b2f] preload schema finished, cost 0 ms 
[INFO ] 2024-09-22 17:07:09.531 - [测试时区][localmaster] - Node localmaster[ce5b89f4-1534-411d-a2e9-52ba4714f092] preload schema finished, cost 0 ms 
[WARN ] 2024-09-22 17:07:11.106 - [测试时区][localmaster] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1726996030996} and {hostPort=localhost:43306, time=1726967230994} 
[INFO ] 2024-09-22 17:07:11.166 - [测试时区][localmaster] - Source node "localmaster" read batch size: 100 
[INFO ] 2024-09-22 17:07:11.166 - [测试时区][localmaster] - Source node "localmaster" event queue capacity: 200 
[INFO ] 2024-09-22 17:07:11.166 - [测试时区][localmaster] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-22 17:07:11.169 - [测试时区][localmaster] - batch offset found: {},stream offset found: {"name":"1171da59-0d75-45c9-8e78-9530326649ed","offset":{"{\"server\":\"1171da59-0d75-45c9-8e78-9530326649ed\"}":"{\"ts_sec\":1726719145,\"file\":\"mysql-bin.000023\",\"pos\":9097,\"server_id\":1}"}} 
[INFO ] 2024-09-22 17:07:11.172 - [测试时区][localmaster] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-22 17:07:11.233 - [测试时区][localmaster] - Incremental sync starting... 
[INFO ] 2024-09-22 17:07:11.238 - [测试时区][localmaster] - Initial sync completed 
[INFO ] 2024-09-22 17:07:11.259 - [测试时区][localmaster] - Starting stream read, table list: [testDateTime], offset: {"name":"1171da59-0d75-45c9-8e78-9530326649ed","offset":{"{\"server\":\"1171da59-0d75-45c9-8e78-9530326649ed\"}":"{\"ts_sec\":1726719145,\"file\":\"mysql-bin.000023\",\"pos\":9097,\"server_id\":1}"}} 
[INFO ] 2024-09-22 17:07:15.762 - [测试时区][localmaster] - Starting mysql cdc, server name: 1171da59-0d75-45c9-8e78-9530326649ed 
[INFO ] 2024-09-22 17:07:15.798 - [测试时区][localmaster] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 713893464
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 1171da59-0d75-45c9-8e78-9530326649ed
  database.port: 33306
  threadName: Debezium-Mysql-Connector-1171da59-0d75-45c9-8e78-9530326649ed
  enable.time.adjuster: false
  database.hostname: localhost
  database.password: ********
  name: 1171da59-0d75-45c9-8e78-9530326649ed
  pdk.offset.string: {"name":"1171da59-0d75-45c9-8e78-9530326649ed","offset":{"{\"server\":\"1171da59-0d75-45c9-8e78-9530326649ed\"}":"{\"file\":\"mysql-bin.000023\",\"pos\":8663,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testDateTime
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-22 17:07:16.013 - [测试时区][mysql3307] - Node(mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-22 17:07:16.013 - [测试时区][mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-22 17:07:16.625 - [测试时区][localmaster] - Connector Mysql incremental start succeed, tables: [testDateTime], data change syncing 
[INFO ] 2024-09-22 17:07:17.089 - [测试时区][localmaster] - last event is io.tapdata.entity.event.dml.TapInsertRecordEvent@6fe8e712: {"after":{"dateCol":-62135596800,"id":32},"containsIllegalDate":false,"referenceTime":1726718873000,"tableId":"testDateTime","time":1726996037087,"type":300} 
[INFO ] 2024-09-22 17:07:17.103 - [测试时区][localmaster] - last event is io.tapdata.entity.event.dml.TapInsertRecordEvent@32f4f1fb: {"after":{"dateCol":-62135596800,"id":33},"containsIllegalDate":false,"referenceTime":1726719145000,"tableId":"testDateTime","time":1726996037087,"type":300} 
[INFO ] 2024-09-22 17:07:26.589 - [测试时区][localmaster] - last event is io.tapdata.entity.event.control.HeartbeatEvent@40a940d6: {"time":1726996046588,"type":501} 
[INFO ] 2024-09-22 17:08:15.087 - [测试时区][localmaster] - last event is io.tapdata.entity.event.control.HeartbeatEvent@383a64cd: {"time":1726996095087,"type":501} 
[INFO ] 2024-09-22 17:08:50.723 - [测试时区][localmaster] - Node localmaster[ce5b89f4-1534-411d-a2e9-52ba4714f092] running status set to false 
[INFO ] 2024-09-22 17:08:50.823 - [测试时区][localmaster] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-22 17:08:50.838 - [测试时区][localmaster] - Mysql binlog reader stopped 
[INFO ] 2024-09-22 17:08:50.905 - [测试时区][localmaster] - PDK connector node stopped: HazelcastSourcePdkDataNode-ce5b89f4-1534-411d-a2e9-52ba4714f092 
[INFO ] 2024-09-22 17:08:50.910 - [测试时区][localmaster] - PDK connector node released: HazelcastSourcePdkDataNode-ce5b89f4-1534-411d-a2e9-52ba4714f092 
[INFO ] 2024-09-22 17:08:50.915 - [测试时区][localmaster] - Node localmaster[ce5b89f4-1534-411d-a2e9-52ba4714f092] schema data cleaned 
[INFO ] 2024-09-22 17:08:50.927 - [测试时区][localmaster] - Node localmaster[ce5b89f4-1534-411d-a2e9-52ba4714f092] monitor closed 
[INFO ] 2024-09-22 17:08:50.928 - [测试时区][localmaster] - Node localmaster[ce5b89f4-1534-411d-a2e9-52ba4714f092] close complete, cost 205 ms 
[INFO ] 2024-09-22 17:08:50.930 - [测试时区][mysql3307] - Node mysql3307[3649b1e3-fd31-45ae-a716-10959e6f7b2f] running status set to false 
[INFO ] 2024-09-22 17:08:51.101 - [测试时区][mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-3649b1e3-fd31-45ae-a716-10959e6f7b2f 
[INFO ] 2024-09-22 17:08:51.102 - [测试时区][mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-3649b1e3-fd31-45ae-a716-10959e6f7b2f 
[INFO ] 2024-09-22 17:08:51.115 - [测试时区][mysql3307] - Node mysql3307[3649b1e3-fd31-45ae-a716-10959e6f7b2f] schema data cleaned 
[INFO ] 2024-09-22 17:08:51.118 - [测试时区][mysql3307] - Node mysql3307[3649b1e3-fd31-45ae-a716-10959e6f7b2f] monitor closed 
[INFO ] 2024-09-22 17:08:51.171 - [测试时区][mysql3307] - Node mysql3307[3649b1e3-fd31-45ae-a716-10959e6f7b2f] close complete, cost 190 ms 
[INFO ] 2024-09-22 17:08:51.193 - [测试时区] - Stop task milestones: 66e540f9c242c01d0c57f5be(测试时区)  
[INFO ] 2024-09-22 17:08:55.070 - [测试时区] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-22 17:08:55.070 - [测试时区] - Stopped task aspect(s) 
[INFO ] 2024-09-22 17:08:55.070 - [测试时区] - Snapshot order controller have been removed 
[INFO ] 2024-09-22 17:08:55.080 - [测试时区] - Remove memory task client succeed, task: 测试时区[66e540f9c242c01d0c57f5be] 
[INFO ] 2024-09-22 17:08:55.080 - [测试时区] - Destroy memory task client cache succeed, task: 测试时区[66e540f9c242c01d0c57f5be] 
