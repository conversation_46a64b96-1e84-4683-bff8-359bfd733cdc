[INFO ] 2024-04-05 19:04:35.739 - [任务 53] - Start task milestones: 660fb16d7083fc5a86bf7653(任务 53) 
[INFO ] 2024-04-05 19:04:35.740 - [任务 53] - Task initialization... 
[INFO ] 2024-04-05 19:04:35.789 - [任务 53] - <PERSON><PERSON> performs snapshot read asynchronously 
[ERROR] 2024-04-05 19:04:35.820 - [任务 53] - Node [id 99e483ac-59e5-4b36-8e45-ca5da41d00cb, name Test] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id 99e483ac-59e5-4b36-8e45-ca5da41d00cb, name Test] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:303)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:125)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:340)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.run(SingleLockWithKey.java:57)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-04-05 19:04:35.823 - [任务 53] - Stop task milestones: 660fb16d7083fc5a86bf7653(任务 53)  
