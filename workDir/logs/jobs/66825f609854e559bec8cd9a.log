[INFO ] 2024-07-01 15:49:36.183 - [任务 3] - Start task milestones: 66825f609854e559bec8cd9a(任务 3) 
[INFO ] 2024-07-01 15:49:36.186 - [任务 3] - Task initialization... 
[INFO ] 2024-07-01 15:49:36.511 - [任务 3] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-01 15:49:36.514 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-01 15:49:36.586 - [任务 3][Mongo] - <PERSON><PERSON>go[9bbb6b75-98be-4f6d-88df-bbde31fb9ea7] start preload schema,table counts: 6 
[INFO ] 2024-07-01 15:49:36.587 - [任务 3][表编辑] - Node 表编辑[992e3aca-f8fa-4e47-b473-a9f9d89d8059] start preload schema,table counts: 6 
[INFO ] 2024-07-01 15:49:36.587 - [任务 3][<PERSON><PERSON>] - <PERSON><PERSON>[9bbb6b75-98be-4f6d-88df-bbde31fb9ea7] preload schema finished, cost 1 ms 
[INFO ] 2024-07-01 15:49:36.587 - [任务 3][表编辑] - Node 表编辑[992e3aca-f8fa-4e47-b473-a9f9d89d8059] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 15:49:36.629 - [任务 3][Mysql] - Node Mysql[0c60e2d9-9a73-4409-bf35-b40e37e9977c] start preload schema,table counts: 6 
[INFO ] 2024-07-01 15:49:36.630 - [任务 3][Mysql] - Node Mysql[0c60e2d9-9a73-4409-bf35-b40e37e9977c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 15:49:37.374 - [任务 3][Mongo] - Source node "Mongo" read batch size: 100 
[INFO ] 2024-07-01 15:49:37.377 - [任务 3][Mongo] - Source node "Mongo" event queue capacity: 200 
[INFO ] 2024-07-01 15:49:37.377 - [任务 3][Mongo] - Sync progress not exists, will run task as first time 
[INFO ] 2024-07-01 15:49:37.377 - [任务 3][Mongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-01 15:49:37.501 - [任务 3][Mongo] - batch offset found: {},stream offset found: {"cdcOffset":1719820177,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 15:49:37.503 - [任务 3] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-07-01 15:49:37.613 - [任务 3][Mysql] - Node(Mysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-01 15:49:37.621 - [任务 3][Mongo] - Initial sync started 
[INFO ] 2024-07-01 15:49:37.621 - [任务 3][Mysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-01 15:49:37.621 - [任务 3][Mongo] - Starting batch read, table name: TEST2, offset: null 
[INFO ] 2024-07-01 15:49:37.622 - [任务 3][Mysql] - Sync progress not exists, will run task as first time 
[INFO ] 2024-07-01 15:49:37.836 - [任务 3][Mongo] - Table TEST2 is going to be initial synced 
[INFO ] 2024-07-01 15:49:37.836 - [任务 3][Mongo] - Query table 'TEST2' counts: 1076 
[INFO ] 2024-07-01 15:49:38.118 - [任务 3][Mongo] - Table [TEST2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:49:38.121 - [任务 3][Mongo] - Starting batch read, table name: TEST3, offset: null 
[INFO ] 2024-07-01 15:49:38.121 - [任务 3][Mongo] - Table TEST3 is going to be initial synced 
[INFO ] 2024-07-01 15:49:38.188 - [任务 3][Mongo] - Query table 'TEST3' counts: 1053 
[INFO ] 2024-07-01 15:49:38.193 - [任务 3][Mongo] - Table [TEST3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:49:38.193 - [任务 3][Mongo] - Starting batch read, table name: TEST4, offset: null 
[INFO ] 2024-07-01 15:49:38.193 - [任务 3][Mongo] - Table TEST4 is going to be initial synced 
[INFO ] 2024-07-01 15:49:38.302 - [任务 3][Mongo] - Query table 'TEST4' counts: 1053 
[INFO ] 2024-07-01 15:49:38.305 - [任务 3][Mongo] - Table [TEST4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:49:38.305 - [任务 3][Mongo] - Starting batch read, table name: TEST5, offset: null 
[INFO ] 2024-07-01 15:49:38.311 - [任务 3][Mongo] - Table TEST5 is going to be initial synced 
[INFO ] 2024-07-01 15:49:38.311 - [任务 3][Mongo] - Query table 'TEST5' counts: 0 
[INFO ] 2024-07-01 15:49:38.313 - [任务 3][Mongo] - Table [TEST5] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:49:38.314 - [任务 3][Mongo] - Starting batch read, table name: TEST6, offset: null 
[INFO ] 2024-07-01 15:49:38.314 - [任务 3][Mongo] - Table TEST6 is going to be initial synced 
[INFO ] 2024-07-01 15:49:38.320 - [任务 3][Mongo] - Query table 'TEST6' counts: 1 
[INFO ] 2024-07-01 15:49:38.320 - [任务 3][Mongo] - Table [TEST6] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:49:38.320 - [任务 3][Mongo] - Starting batch read, table name: TEST_CLAIM1, offset: null 
[INFO ] 2024-07-01 15:49:38.320 - [任务 3][Mongo] - Table TEST_CLAIM1 is going to be initial synced 
[INFO ] 2024-07-01 15:49:38.385 - [任务 3][Mongo] - Query table 'TEST_CLAIM1' counts: 1053 
[INFO ] 2024-07-01 15:49:38.385 - [任务 3][Mongo] - Table [TEST_CLAIM1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:49:38.385 - [任务 3][Mongo] - Initial sync completed 
[INFO ] 2024-07-01 15:49:38.385 - [任务 3][Mongo] - Incremental sync starting... 
[INFO ] 2024-07-01 15:49:38.397 - [任务 3][Mongo] - Initial sync completed 
[INFO ] 2024-07-01 15:49:38.398 - [任务 3][Mongo] - Starting stream read, table list: [TEST2, TEST3, TEST4, TEST5, TEST6, TEST_CLAIM1], offset: {"cdcOffset":1719820177,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 15:49:38.603 - [任务 3][Mongo] - Connector MongoDB incremental start succeed, tables: [TEST2, TEST3, TEST4, TEST5, TEST6, TEST_CLAIM1], data change syncing 
[INFO ] 2024-07-01 15:51:37.578 - [任务 3][Mongo] - Found new table(s): [TEST7] 
[INFO ] 2024-07-01 15:51:37.614 - [任务 3][Mongo] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-07-01 15:51:37.636 - [任务 3][Mongo] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@63cc1a2a: {"table":{"defaultPrimaryKeys":["_id"],"id":"TEST7","indexList":[{"indexFields":[{}],"name":"__t__{\"v\": 2, \"key\": {\"_id\": 1}, \"name\": \"_id_\", \"ns\": \"DevSource.TEST7\"}"}],"maxPKPos":1,"maxPos":1,"name":"TEST7","nameFieldMap":{"_id":{"autoInc":false,"dataType":"OBJECT_ID","name":"_id","nullable":true,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bytes":24,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"_id"}],"indexMap":{"_id":{"fieldAsc":true,"name":"_id"}},"unique":true},"tableAttr":{"size":0,"ns":"DevSource.TEST7","capped":false,"storageSize":4096,"shard":{}}},"tableId":"TEST7","type":206} 
[INFO ] 2024-07-01 15:51:37.637 - [任务 3][Mongo] - Create new table in memory, qualified name: T_mongodb_io_tapdata_1_0-SNAPSHOT_TEST7_66825885c94d2f5d40a10e83_66825f609854e559bec8cd9a 
[INFO ] 2024-07-01 15:51:37.763 - [任务 3][Mongo] - Create new table schema transform finished: TapTable id TEST7 name TEST7 storageEngine null charset null number of fields 1 
[INFO ] 2024-07-01 15:51:37.778 - [任务 3][Mongo] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-07-01 15:51:37.895 - [任务 3][Mongo] - Starting batch read, table name: TEST7, offset: null 
[INFO ] 2024-07-01 15:51:37.895 - [任务 3][Mongo] - Table TEST7 is going to be initial synced 
[INFO ] 2024-07-01 15:51:37.940 - [任务 3][Mongo] - Table [TEST7] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:51:37.950 - [任务 3][Mongo] - Query table 'TEST7' counts: 0 
[INFO ] 2024-07-01 15:51:37.950 - [任务 3][Mongo] - Initial sync completed 
[INFO ] 2024-07-01 15:51:37.951 - [任务 3][Mongo] - Incremental sync starting... 
[INFO ] 2024-07-01 15:51:37.951 - [任务 3][Mongo] - Initial sync completed 
[INFO ] 2024-07-01 15:51:37.993 - [任务 3][Mongo] - Starting stream read, table list: [TEST2, TEST3, TEST4, TEST5, TEST6, TEST7, TEST_CLAIM1], offset: {"cdcOffset":1719820177,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 15:51:37.994 - [任务 3][Mongo] - Connector MongoDB incremental start succeed, tables: [TEST2, TEST3, TEST4, TEST5, TEST6, TEST7, TEST_CLAIM1], data change syncing 
[INFO ] 2024-07-01 15:51:38.400 - [任务 3][Mongo] - Incremental sync completed 
[INFO ] 2024-07-01 15:53:31.078 - [任务 3] - Stop task milestones: 66825f609854e559bec8cd9a(任务 3)  
[INFO ] 2024-07-01 15:53:31.251 - [任务 3][Mongo] - Node Mongo[9bbb6b75-98be-4f6d-88df-bbde31fb9ea7] running status set to false 
[INFO ] 2024-07-01 15:53:31.251 - [任务 3][Mongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-9bbb6b75-98be-4f6d-88df-bbde31fb9ea7 
[INFO ] 2024-07-01 15:53:31.251 - [任务 3][Mongo] - PDK connector node released: HazelcastSourcePdkDataNode-9bbb6b75-98be-4f6d-88df-bbde31fb9ea7 
[INFO ] 2024-07-01 15:53:31.251 - [任务 3][Mongo] - Node Mongo[9bbb6b75-98be-4f6d-88df-bbde31fb9ea7] schema data cleaned 
[INFO ] 2024-07-01 15:53:31.253 - [任务 3][Mongo] - Node Mongo[9bbb6b75-98be-4f6d-88df-bbde31fb9ea7] monitor closed 
[INFO ] 2024-07-01 15:53:31.253 - [任务 3][Mongo] - Node Mongo[9bbb6b75-98be-4f6d-88df-bbde31fb9ea7] close complete, cost 17 ms 
[INFO ] 2024-07-01 15:53:31.261 - [任务 3][表编辑] - Node 表编辑[992e3aca-f8fa-4e47-b473-a9f9d89d8059] running status set to false 
[INFO ] 2024-07-01 15:53:31.261 - [任务 3][表编辑] - Node 表编辑[992e3aca-f8fa-4e47-b473-a9f9d89d8059] schema data cleaned 
[INFO ] 2024-07-01 15:53:31.261 - [任务 3][表编辑] - Node 表编辑[992e3aca-f8fa-4e47-b473-a9f9d89d8059] monitor closed 
[INFO ] 2024-07-01 15:53:31.262 - [任务 3][表编辑] - Node 表编辑[992e3aca-f8fa-4e47-b473-a9f9d89d8059] close complete, cost 9 ms 
[INFO ] 2024-07-01 15:53:31.294 - [任务 3][Mysql] - Node Mysql[0c60e2d9-9a73-4409-bf35-b40e37e9977c] running status set to false 
[INFO ] 2024-07-01 15:53:31.294 - [任务 3][Mysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-0c60e2d9-9a73-4409-bf35-b40e37e9977c 
[INFO ] 2024-07-01 15:53:31.294 - [任务 3][Mysql] - PDK connector node released: HazelcastTargetPdkDataNode-0c60e2d9-9a73-4409-bf35-b40e37e9977c 
[INFO ] 2024-07-01 15:53:31.295 - [任务 3][Mysql] - Node Mysql[0c60e2d9-9a73-4409-bf35-b40e37e9977c] schema data cleaned 
[INFO ] 2024-07-01 15:53:31.295 - [任务 3][Mysql] - Node Mysql[0c60e2d9-9a73-4409-bf35-b40e37e9977c] monitor closed 
[INFO ] 2024-07-01 15:53:31.295 - [任务 3][Mysql] - Node Mysql[0c60e2d9-9a73-4409-bf35-b40e37e9977c] close complete, cost 33 ms 
[INFO ] 2024-07-01 15:53:34.444 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-01 15:53:34.445 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-07-01 15:53:34.462 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-07-01 15:53:34.553 - [任务 3] - Remove memory task client succeed, task: 任务 3[66825f609854e559bec8cd9a] 
[INFO ] 2024-07-01 15:53:34.553 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[66825f609854e559bec8cd9a] 
[INFO ] 2024-07-01 15:54:35.638 - [任务 3] - Start task milestones: 66825f609854e559bec8cd9a(任务 3) 
[INFO ] 2024-07-01 15:54:35.638 - [任务 3] - Task initialization... 
[INFO ] 2024-07-01 15:54:35.842 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-01 15:54:35.913 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-01 15:54:35.914 - [任务 3][Mongo] - Node Mongo[9bbb6b75-98be-4f6d-88df-bbde31fb9ea7] start preload schema,table counts: 7 
[INFO ] 2024-07-01 15:54:35.915 - [任务 3][Mysql] - Node Mysql[0c60e2d9-9a73-4409-bf35-b40e37e9977c] start preload schema,table counts: 7 
[INFO ] 2024-07-01 15:54:35.915 - [任务 3][表编辑] - Node 表编辑[992e3aca-f8fa-4e47-b473-a9f9d89d8059] start preload schema,table counts: 7 
[INFO ] 2024-07-01 15:54:35.915 - [任务 3][Mongo] - Node Mongo[9bbb6b75-98be-4f6d-88df-bbde31fb9ea7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 15:54:35.915 - [任务 3][Mysql] - Node Mysql[0c60e2d9-9a73-4409-bf35-b40e37e9977c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 15:54:35.915 - [任务 3][表编辑] - Node 表编辑[992e3aca-f8fa-4e47-b473-a9f9d89d8059] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 15:54:36.673 - [任务 3][Mysql] - Node(Mysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-01 15:54:36.674 - [任务 3][Mysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-01 15:54:36.793 - [任务 3][Mongo] - Source node "Mongo" read batch size: 100 
[INFO ] 2024-07-01 15:54:36.796 - [任务 3][Mongo] - Source node "Mongo" event queue capacity: 200 
[INFO ] 2024-07-01 15:54:36.796 - [任务 3][Mongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-01 15:54:36.912 - [任务 3][Mongo] - batch offset found: {},stream offset found: {"cdcOffset":1719820476,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 15:54:36.913 - [任务 3] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-07-01 15:54:36.991 - [任务 3][Mongo] - Initial sync started 
[INFO ] 2024-07-01 15:54:37.002 - [任务 3][Mongo] - Starting batch read, table name: TEST2, offset: null 
[INFO ] 2024-07-01 15:54:37.002 - [任务 3][Mongo] - Table TEST2 is going to be initial synced 
[INFO ] 2024-07-01 15:54:37.126 - [任务 3][Mongo] - Query table 'TEST2' counts: 1076 
[INFO ] 2024-07-01 15:54:37.126 - [任务 3][Mongo] - Table [TEST2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:54:37.126 - [任务 3][Mongo] - Starting batch read, table name: TEST3, offset: null 
[INFO ] 2024-07-01 15:54:37.126 - [任务 3][Mongo] - Table TEST3 is going to be initial synced 
[INFO ] 2024-07-01 15:54:37.203 - [任务 3][Mongo] - Query table 'TEST3' counts: 1053 
[INFO ] 2024-07-01 15:54:37.203 - [任务 3][Mongo] - Table [TEST3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:54:37.203 - [任务 3][Mongo] - Starting batch read, table name: TEST4, offset: null 
[INFO ] 2024-07-01 15:54:37.205 - [任务 3][Mongo] - Table TEST4 is going to be initial synced 
[INFO ] 2024-07-01 15:54:37.205 - [任务 3][Mongo] - Query table 'TEST4' counts: 1053 
[INFO ] 2024-07-01 15:54:37.281 - [任务 3][Mongo] - Table [TEST4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:54:37.281 - [任务 3][Mongo] - Starting batch read, table name: TEST5, offset: null 
[INFO ] 2024-07-01 15:54:37.289 - [任务 3][Mongo] - Table TEST5 is going to be initial synced 
[INFO ] 2024-07-01 15:54:37.289 - [任务 3][Mongo] - Query table 'TEST5' counts: 0 
[INFO ] 2024-07-01 15:54:37.290 - [任务 3][Mongo] - Table [TEST5] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:54:37.290 - [任务 3][Mongo] - Starting batch read, table name: TEST6, offset: null 
[INFO ] 2024-07-01 15:54:37.290 - [任务 3][Mongo] - Table TEST6 is going to be initial synced 
[INFO ] 2024-07-01 15:54:37.291 - [任务 3][Mongo] - Query table 'TEST6' counts: 1 
[INFO ] 2024-07-01 15:54:37.291 - [任务 3][Mongo] - Table [TEST6] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:54:37.291 - [任务 3][Mongo] - Starting batch read, table name: TEST7, offset: null 
[INFO ] 2024-07-01 15:54:37.291 - [任务 3][Mongo] - Table TEST7 is going to be initial synced 
[INFO ] 2024-07-01 15:54:37.291 - [任务 3][Mongo] - Table [TEST7] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:54:37.293 - [任务 3][Mongo] - Query table 'TEST7' counts: 1 
[INFO ] 2024-07-01 15:54:37.293 - [任务 3][Mongo] - Starting batch read, table name: TEST_CLAIM1, offset: null 
[INFO ] 2024-07-01 15:54:37.293 - [任务 3][Mongo] - Table TEST_CLAIM1 is going to be initial synced 
[INFO ] 2024-07-01 15:54:37.363 - [任务 3][Mongo] - Query table 'TEST_CLAIM1' counts: 1053 
[INFO ] 2024-07-01 15:54:37.363 - [任务 3][Mongo] - Table [TEST_CLAIM1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 15:54:37.363 - [任务 3][Mongo] - Initial sync completed 
[INFO ] 2024-07-01 15:54:37.365 - [任务 3][Mongo] - Incremental sync starting... 
[INFO ] 2024-07-01 15:54:37.366 - [任务 3][Mongo] - Initial sync completed 
[INFO ] 2024-07-01 15:54:37.366 - [任务 3][Mongo] - Starting stream read, table list: [TEST2, TEST3, TEST4, TEST5, TEST6, TEST7, TEST_CLAIM1], offset: {"cdcOffset":1719820476,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 15:54:37.571 - [任务 3][Mongo] - Connector MongoDB incremental start succeed, tables: [TEST2, TEST3, TEST4, TEST5, TEST6, TEST7, TEST_CLAIM1], data change syncing 
[INFO ] 2024-07-01 15:59:57.176 - [任务 3][Mongo] - Node Mongo[9bbb6b75-98be-4f6d-88df-bbde31fb9ea7] running status set to false 
