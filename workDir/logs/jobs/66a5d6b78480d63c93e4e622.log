[INFO ] 2024-07-28 13:30:59.646 - [任务 1] - Task initialization... 
[INFO ] 2024-07-28 13:30:59.653 - [任务 1] - Start task milestones: 66a5d6b78480d63c93e4e622(任务 1) 
[INFO ] 2024-07-28 13:31:00.585 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 13:31:00.684 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-28 13:31:01.034 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] start preload schema,table counts: 1 
[INFO ] 2024-07-28 13:31:01.034 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] preload schema finished, cost 1 ms 
[INFO ] 2024-07-28 13:31:01.052 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] start preload schema,table counts: 1 
[INFO ] 2024-07-28 13:31:01.054 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 13:31:02.069 - [任务 1][TestPO] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-28 13:31:02.132 - [任务 1][TESTPO] - Source node "TESTPO" read batch size: 100 
[INFO ] 2024-07-28 13:31:02.133 - [任务 1][TESTPO] - Source node "TESTPO" event queue capacity: 200 
[INFO ] 2024-07-28 13:31:02.133 - [任务 1][TESTPO] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-28 13:31:02.258 - [任务 1][TESTPO] - batch offset found: {},stream offset found: {"cdcOffset":1722144662,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 13:31:02.316 - [任务 1][TESTPO] - Initial sync started 
[INFO ] 2024-07-28 13:31:02.317 - [任务 1][TESTPO] - Starting batch read, table name: TESTPO, offset: null 
[INFO ] 2024-07-28 13:31:02.348 - [任务 1][TESTPO] - Table TESTPO is going to be initial synced 
[INFO ] 2024-07-28 13:31:02.348 - [任务 1][TESTPO] - Query table 'TESTPO' counts: 600 
[INFO ] 2024-07-28 13:31:02.360 - [任务 1][TESTPO] - Table [TESTPO] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-28 13:31:02.366 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 13:31:02.380 - [任务 1][TESTPO] - Incremental sync starting... 
[INFO ] 2024-07-28 13:31:02.384 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 13:31:02.384 - [任务 1][TESTPO] - Starting stream read, table list: [TESTPO, _tapdata_heartbeat_table], offset: {"cdcOffset":1722144662,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 13:31:02.432 - [任务 1][TESTPO] - Connector MongoDB incremental start succeed, tables: [TESTPO, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-28 13:31:24.877 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] running status set to false 
[INFO ] 2024-07-28 13:31:24.891 - [任务 1][TESTPO] - PDK connector node stopped: HazelcastSourcePdkDataNode-7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8 
[INFO ] 2024-07-28 13:31:24.891 - [任务 1][TESTPO] - PDK connector node released: HazelcastSourcePdkDataNode-7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8 
[INFO ] 2024-07-28 13:31:24.892 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] schema data cleaned 
[INFO ] 2024-07-28 13:31:24.893 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] monitor closed 
[INFO ] 2024-07-28 13:31:24.901 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] close complete, cost 32 ms 
[INFO ] 2024-07-28 13:31:24.901 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] running status set to false 
[INFO ] 2024-07-28 13:31:24.918 - [任务 1][TestPO] - PDK connector node stopped: HazelcastTargetPdkDataNode-c7a3e27b-deda-4ffe-818c-2dc9e0788871 
[INFO ] 2024-07-28 13:31:24.920 - [任务 1][TestPO] - PDK connector node released: HazelcastTargetPdkDataNode-c7a3e27b-deda-4ffe-818c-2dc9e0788871 
[INFO ] 2024-07-28 13:31:24.920 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] schema data cleaned 
[INFO ] 2024-07-28 13:31:24.921 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] monitor closed 
[INFO ] 2024-07-28 13:31:24.921 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] close complete, cost 21 ms 
[INFO ] 2024-07-28 13:31:25.599 - [任务 1][TESTPO] - Incremental sync completed 
[INFO ] 2024-07-28 13:31:29.412 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 13:31:29.417 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@55fc3067 
[INFO ] 2024-07-28 13:31:29.421 - [任务 1] - Stop task milestones: 66a5d6b78480d63c93e4e622(任务 1)  
[INFO ] 2024-07-28 13:31:29.572 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-28 13:31:29.572 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-28 13:31:29.626 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a5d6b78480d63c93e4e622] 
[INFO ] 2024-07-28 13:31:29.627 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a5d6b78480d63c93e4e622] 
[INFO ] 2024-07-28 13:33:19.941 - [任务 1] - Task initialization... 
[INFO ] 2024-07-28 13:33:20.058 - [任务 1] - Start task milestones: 66a5d6b78480d63c93e4e622(任务 1) 
[INFO ] 2024-07-28 13:33:20.180 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 13:33:20.192 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-28 13:33:20.291 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] start preload schema,table counts: 1 
[INFO ] 2024-07-28 13:33:20.291 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] start preload schema,table counts: 1 
[INFO ] 2024-07-28 13:33:20.293 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 13:33:20.294 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 13:33:20.642 - [任务 1][TESTPO] - Source node "TESTPO" read batch size: 100 
[INFO ] 2024-07-28 13:33:20.644 - [任务 1][TESTPO] - Source node "TESTPO" event queue capacity: 200 
[INFO ] 2024-07-28 13:33:20.663 - [任务 1][TESTPO] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-28 13:33:20.683 - [任务 1][TESTPO] - batch offset found: {"TESTPO":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1722144662,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 13:33:20.684 - [任务 1][TestPO] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-28 13:33:20.750 - [任务 1][TESTPO] - Initial sync started 
[INFO ] 2024-07-28 13:33:20.753 - [任务 1][TESTPO] - Starting batch read, table name: TESTPO, offset: {} 
[INFO ] 2024-07-28 13:33:20.810 - [任务 1][TESTPO] - Table TESTPO is going to be initial synced 
[INFO ] 2024-07-28 13:33:20.810 - [任务 1][TESTPO] - Table [TESTPO] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-28 13:33:20.810 - [任务 1][TESTPO] - Query table 'TESTPO' counts: 600 
[INFO ] 2024-07-28 13:33:20.814 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 13:33:20.815 - [任务 1][TESTPO] - Incremental sync starting... 
[INFO ] 2024-07-28 13:33:20.867 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 13:33:20.867 - [任务 1][TESTPO] - Starting stream read, table list: [TESTPO, _tapdata_heartbeat_table], offset: {"cdcOffset":1722144662,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 13:33:21.072 - [任务 1][TESTPO] - Connector MongoDB incremental start succeed, tables: [TESTPO, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-28 13:35:31.611 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] running status set to false 
[INFO ] 2024-07-28 13:35:31.640 - [任务 1][TESTPO] - PDK connector node stopped: HazelcastSourcePdkDataNode-7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8 
[INFO ] 2024-07-28 13:35:31.641 - [任务 1][TESTPO] - PDK connector node released: HazelcastSourcePdkDataNode-7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8 
[INFO ] 2024-07-28 13:35:31.641 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] schema data cleaned 
[INFO ] 2024-07-28 13:35:31.641 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] monitor closed 
[INFO ] 2024-07-28 13:35:31.645 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] close complete, cost 62 ms 
[INFO ] 2024-07-28 13:35:31.645 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] running status set to false 
[INFO ] 2024-07-28 13:35:31.675 - [任务 1][TestPO] - PDK connector node stopped: HazelcastTargetPdkDataNode-c7a3e27b-deda-4ffe-818c-2dc9e0788871 
[INFO ] 2024-07-28 13:35:31.675 - [任务 1][TestPO] - PDK connector node released: HazelcastTargetPdkDataNode-c7a3e27b-deda-4ffe-818c-2dc9e0788871 
[INFO ] 2024-07-28 13:35:31.675 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] schema data cleaned 
[INFO ] 2024-07-28 13:35:31.675 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] monitor closed 
[INFO ] 2024-07-28 13:35:31.676 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] close complete, cost 32 ms 
[INFO ] 2024-07-28 13:35:32.549 - [任务 1][TESTPO] - Incremental sync completed 
[INFO ] 2024-07-28 13:35:34.832 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 13:35:34.833 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@18e9fa98 
[INFO ] 2024-07-28 13:35:34.975 - [任务 1] - Stop task milestones: 66a5d6b78480d63c93e4e622(任务 1)  
[INFO ] 2024-07-28 13:35:34.976 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-28 13:35:34.976 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-28 13:35:35.030 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a5d6b78480d63c93e4e622] 
[INFO ] 2024-07-28 13:35:35.232 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a5d6b78480d63c93e4e622] 
[INFO ] 2024-07-28 13:35:43.957 - [任务 1] - Task initialization... 
[INFO ] 2024-07-28 13:35:43.957 - [任务 1] - Start task milestones: 66a5d6b78480d63c93e4e622(任务 1) 
[INFO ] 2024-07-28 13:35:44.100 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 13:35:44.156 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-28 13:35:44.156 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] start preload schema,table counts: 1 
[INFO ] 2024-07-28 13:35:44.156 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] start preload schema,table counts: 1 
[INFO ] 2024-07-28 13:35:44.156 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] preload schema finished, cost 1 ms 
[INFO ] 2024-07-28 13:35:44.156 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 13:35:44.422 - [任务 1][TESTPO] - Source node "TESTPO" read batch size: 100 
[INFO ] 2024-07-28 13:35:44.423 - [任务 1][TESTPO] - Source node "TESTPO" event queue capacity: 200 
[INFO ] 2024-07-28 13:35:44.424 - [任务 1][TESTPO] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-28 13:35:44.491 - [任务 1][TESTPO] - batch offset found: {"TESTPO":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1722144662,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 13:35:44.491 - [任务 1][TestPO] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-28 13:35:44.498 - [任务 1][TESTPO] - Initial sync started 
[INFO ] 2024-07-28 13:35:44.498 - [任务 1][TESTPO] - Starting batch read, table name: TESTPO, offset: {} 
[INFO ] 2024-07-28 13:35:44.603 - [任务 1][TESTPO] - Table TESTPO is going to be initial synced 
[INFO ] 2024-07-28 13:35:44.603 - [任务 1][TESTPO] - Query table 'TESTPO' counts: 600 
[INFO ] 2024-07-28 13:35:44.604 - [任务 1][TESTPO] - Table [TESTPO] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-28 13:35:44.610 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 13:35:44.613 - [任务 1][TESTPO] - Incremental sync starting... 
[INFO ] 2024-07-28 13:35:44.613 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 13:35:44.613 - [任务 1][TESTPO] - Starting stream read, table list: [TESTPO, _tapdata_heartbeat_table], offset: {"cdcOffset":1722144662,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 13:35:44.651 - [任务 1][TESTPO] - Connector MongoDB incremental start succeed, tables: [TESTPO, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-28 13:36:00.451 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] running status set to false 
[INFO ] 2024-07-28 13:36:00.451 - [任务 1][TESTPO] - PDK connector node stopped: HazelcastSourcePdkDataNode-7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8 
[INFO ] 2024-07-28 13:36:00.451 - [任务 1][TESTPO] - PDK connector node released: HazelcastSourcePdkDataNode-7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8 
[INFO ] 2024-07-28 13:36:00.451 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] schema data cleaned 
[INFO ] 2024-07-28 13:36:00.452 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] monitor closed 
[INFO ] 2024-07-28 13:36:00.457 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] close complete, cost 43 ms 
[INFO ] 2024-07-28 13:36:00.457 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] running status set to false 
[INFO ] 2024-07-28 13:36:00.490 - [任务 1][TestPO] - PDK connector node stopped: HazelcastTargetPdkDataNode-c7a3e27b-deda-4ffe-818c-2dc9e0788871 
[INFO ] 2024-07-28 13:36:00.490 - [任务 1][TestPO] - PDK connector node released: HazelcastTargetPdkDataNode-c7a3e27b-deda-4ffe-818c-2dc9e0788871 
[INFO ] 2024-07-28 13:36:00.491 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] schema data cleaned 
[INFO ] 2024-07-28 13:36:00.492 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] monitor closed 
[INFO ] 2024-07-28 13:36:00.494 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] close complete, cost 37 ms 
[INFO ] 2024-07-28 13:36:02.439 - [任务 1][TESTPO] - Incremental sync completed 
[INFO ] 2024-07-28 13:36:05.110 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 13:36:05.119 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@40ce5b7c 
[INFO ] 2024-07-28 13:36:05.121 - [任务 1] - Stop task milestones: 66a5d6b78480d63c93e4e622(任务 1)  
[INFO ] 2024-07-28 13:36:05.244 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-28 13:36:05.244 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-28 13:36:05.272 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a5d6b78480d63c93e4e622] 
[INFO ] 2024-07-28 13:36:05.272 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a5d6b78480d63c93e4e622] 
[INFO ] 2024-07-28 13:36:19.808 - [任务 1] - Task initialization... 
[INFO ] 2024-07-28 13:36:19.809 - [任务 1] - Start task milestones: 66a5d6b78480d63c93e4e622(任务 1) 
[INFO ] 2024-07-28 13:36:19.888 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 13:36:19.945 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-28 13:36:19.970 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] start preload schema,table counts: 1 
[INFO ] 2024-07-28 13:36:19.970 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] start preload schema,table counts: 1 
[INFO ] 2024-07-28 13:36:19.972 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 13:36:19.972 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 13:36:20.268 - [任务 1][TESTPO] - Source node "TESTPO" read batch size: 100 
[INFO ] 2024-07-28 13:36:20.287 - [任务 1][TESTPO] - Source node "TESTPO" event queue capacity: 200 
[INFO ] 2024-07-28 13:36:20.287 - [任务 1][TESTPO] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-28 13:36:20.313 - [任务 1][TESTPO] - batch offset found: {"TESTPO":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1722144662,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 13:36:20.313 - [任务 1][TestPO] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-28 13:36:20.340 - [任务 1][TESTPO] - Incremental sync starting... 
[INFO ] 2024-07-28 13:36:20.340 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 13:36:20.415 - [任务 1][TESTPO] - Starting stream read, table list: [TESTPO, _tapdata_heartbeat_table], offset: {"cdcOffset":1722144662,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 13:36:20.416 - [任务 1][TESTPO] - Connector MongoDB incremental start succeed, tables: [TESTPO, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-28 13:36:30.587 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] running status set to false 
[INFO ] 2024-07-28 13:36:30.587 - [任务 1][TESTPO] - PDK connector node stopped: HazelcastSourcePdkDataNode-7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8 
[INFO ] 2024-07-28 13:36:30.588 - [任务 1][TESTPO] - PDK connector node released: HazelcastSourcePdkDataNode-7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8 
[INFO ] 2024-07-28 13:36:30.588 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] schema data cleaned 
[INFO ] 2024-07-28 13:36:30.590 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] monitor closed 
[INFO ] 2024-07-28 13:36:30.595 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] close complete, cost 30 ms 
[INFO ] 2024-07-28 13:36:30.596 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] running status set to false 
[INFO ] 2024-07-28 13:36:30.601 - [任务 1][TestPO] - PDK connector node stopped: HazelcastTargetPdkDataNode-c7a3e27b-deda-4ffe-818c-2dc9e0788871 
[INFO ] 2024-07-28 13:36:30.602 - [任务 1][TestPO] - PDK connector node released: HazelcastTargetPdkDataNode-c7a3e27b-deda-4ffe-818c-2dc9e0788871 
[INFO ] 2024-07-28 13:36:30.602 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] schema data cleaned 
[INFO ] 2024-07-28 13:36:30.606 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] monitor closed 
[INFO ] 2024-07-28 13:36:30.615 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] close complete, cost 13 ms 
[INFO ] 2024-07-28 13:36:31.620 - [任务 1][TESTPO] - Incremental sync completed 
[INFO ] 2024-07-28 13:36:35.360 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 13:36:35.372 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@f16fcf0 
[INFO ] 2024-07-28 13:36:35.373 - [任务 1] - Stop task milestones: 66a5d6b78480d63c93e4e622(任务 1)  
[INFO ] 2024-07-28 13:36:35.489 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-28 13:36:35.491 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-28 13:36:35.519 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a5d6b78480d63c93e4e622] 
[INFO ] 2024-07-28 13:36:35.720 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a5d6b78480d63c93e4e622] 
[INFO ] 2024-07-28 13:36:45.421 - [任务 1] - Task initialization... 
[INFO ] 2024-07-28 13:36:45.422 - [任务 1] - Start task milestones: 66a5d6b78480d63c93e4e622(任务 1) 
[INFO ] 2024-07-28 13:36:45.502 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 13:36:45.570 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-28 13:36:45.586 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] start preload schema,table counts: 1 
[INFO ] 2024-07-28 13:36:45.586 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] start preload schema,table counts: 1 
[INFO ] 2024-07-28 13:36:45.587 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 13:36:45.587 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 13:36:45.832 - [任务 1][TESTPO] - Source node "TESTPO" read batch size: 100 
[INFO ] 2024-07-28 13:36:45.833 - [任务 1][TESTPO] - Source node "TESTPO" event queue capacity: 200 
[INFO ] 2024-07-28 13:36:45.838 - [任务 1][TESTPO] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-28 13:36:45.838 - [任务 1][TESTPO] - batch offset found: {"TESTPO":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1722144662,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 13:36:45.907 - [任务 1][TESTPO] - Incremental sync starting... 
[INFO ] 2024-07-28 13:36:45.908 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 13:36:45.912 - [任务 1][TESTPO] - Starting stream read, table list: [TESTPO, _tapdata_heartbeat_table], offset: {"cdcOffset":1722144662,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 13:36:45.912 - [任务 1][TestPO] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-28 13:36:46.114 - [任务 1][TESTPO] - Connector MongoDB incremental start succeed, tables: [TESTPO, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-28 13:39:00.926 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] running status set to false 
[INFO ] 2024-07-28 13:39:00.962 - [任务 1][TESTPO] - PDK connector node stopped: HazelcastSourcePdkDataNode-7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8 
[INFO ] 2024-07-28 13:39:00.963 - [任务 1][TESTPO] - PDK connector node released: HazelcastSourcePdkDataNode-7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8 
[INFO ] 2024-07-28 13:39:00.963 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] schema data cleaned 
[INFO ] 2024-07-28 13:39:00.967 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] monitor closed 
[INFO ] 2024-07-28 13:39:00.967 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] close complete, cost 62 ms 
[INFO ] 2024-07-28 13:39:00.982 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] running status set to false 
[INFO ] 2024-07-28 13:39:00.983 - [任务 1][TestPO] - PDK connector node stopped: HazelcastTargetPdkDataNode-c7a3e27b-deda-4ffe-818c-2dc9e0788871 
[INFO ] 2024-07-28 13:39:00.983 - [任务 1][TestPO] - PDK connector node released: HazelcastTargetPdkDataNode-c7a3e27b-deda-4ffe-818c-2dc9e0788871 
[INFO ] 2024-07-28 13:39:00.983 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] schema data cleaned 
[INFO ] 2024-07-28 13:39:00.984 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] monitor closed 
[INFO ] 2024-07-28 13:39:01.186 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] close complete, cost 17 ms 
[INFO ] 2024-07-28 13:39:01.796 - [任务 1][TESTPO] - Incremental sync completed 
[INFO ] 2024-07-28 13:39:05.683 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 13:39:05.685 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2b2d90b9 
[INFO ] 2024-07-28 13:39:05.699 - [任务 1] - Stop task milestones: 66a5d6b78480d63c93e4e622(任务 1)  
[INFO ] 2024-07-28 13:39:05.817 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-28 13:39:05.818 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-28 13:39:05.844 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a5d6b78480d63c93e4e622] 
[INFO ] 2024-07-28 13:39:05.845 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a5d6b78480d63c93e4e622] 
[INFO ] 2024-07-28 13:39:08.968 - [任务 1] - Task initialization... 
[INFO ] 2024-07-28 13:39:08.968 - [任务 1] - Start task milestones: 66a5d6b78480d63c93e4e622(任务 1) 
[INFO ] 2024-07-28 13:39:09.098 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 13:39:09.098 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-28 13:39:09.133 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] start preload schema,table counts: 1 
[INFO ] 2024-07-28 13:39:09.133 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] start preload schema,table counts: 1 
[INFO ] 2024-07-28 13:39:09.137 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 13:39:09.137 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 13:39:09.387 - [任务 1][TESTPO] - Source node "TESTPO" read batch size: 100 
[INFO ] 2024-07-28 13:39:09.389 - [任务 1][TESTPO] - Source node "TESTPO" event queue capacity: 200 
[INFO ] 2024-07-28 13:39:09.390 - [任务 1][TESTPO] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-28 13:39:09.470 - [任务 1][TESTPO] - batch offset found: {"TESTPO":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1722144662,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 13:39:09.471 - [任务 1][TestPO] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-28 13:39:09.471 - [任务 1][TESTPO] - Incremental sync starting... 
[INFO ] 2024-07-28 13:39:09.471 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 13:39:09.591 - [任务 1][TESTPO] - Starting stream read, table list: [TESTPO, _tapdata_heartbeat_table], offset: {"cdcOffset":1722144662,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 13:39:09.591 - [任务 1][TESTPO] - Connector MongoDB incremental start succeed, tables: [TESTPO, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-28 13:39:19.784 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] running status set to false 
[INFO ] 2024-07-28 13:39:19.785 - [任务 1][TESTPO] - PDK connector node stopped: HazelcastSourcePdkDataNode-7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8 
[INFO ] 2024-07-28 13:39:19.786 - [任务 1][TESTPO] - PDK connector node released: HazelcastSourcePdkDataNode-7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8 
[INFO ] 2024-07-28 13:39:19.787 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] schema data cleaned 
[INFO ] 2024-07-28 13:39:19.791 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] monitor closed 
[INFO ] 2024-07-28 13:39:19.792 - [任务 1][TESTPO] - Node TESTPO[7ae1132d-9083-4a3a-ab94-bc9c5a81f2b8] close complete, cost 54 ms 
[INFO ] 2024-07-28 13:39:19.792 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] running status set to false 
[INFO ] 2024-07-28 13:39:19.811 - [任务 1][TestPO] - PDK connector node stopped: HazelcastTargetPdkDataNode-c7a3e27b-deda-4ffe-818c-2dc9e0788871 
[INFO ] 2024-07-28 13:39:19.812 - [任务 1][TestPO] - PDK connector node released: HazelcastTargetPdkDataNode-c7a3e27b-deda-4ffe-818c-2dc9e0788871 
[INFO ] 2024-07-28 13:39:19.813 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] schema data cleaned 
[INFO ] 2024-07-28 13:39:19.813 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] monitor closed 
[INFO ] 2024-07-28 13:39:20.015 - [任务 1][TestPO] - Node TestPO[c7a3e27b-deda-4ffe-818c-2dc9e0788871] close complete, cost 23 ms 
[INFO ] 2024-07-28 13:39:20.704 - [任务 1][TESTPO] - Incremental sync completed 
[INFO ] 2024-07-28 13:39:20.903 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 13:39:20.904 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@42b42de2 
[INFO ] 2024-07-28 13:39:20.913 - [任务 1] - Stop task milestones: 66a5d6b78480d63c93e4e622(任务 1)  
[INFO ] 2024-07-28 13:39:21.054 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-28 13:39:21.054 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-28 13:39:21.101 - [任务 1] - Remove memory task client succeed, task: 任务 1[66a5d6b78480d63c93e4e622] 
[INFO ] 2024-07-28 13:39:21.101 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66a5d6b78480d63c93e4e622] 
