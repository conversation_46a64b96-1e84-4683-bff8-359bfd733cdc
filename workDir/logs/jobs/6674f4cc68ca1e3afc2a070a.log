[INFO ] 2024-06-21 11:55:42.310 - [任务 5(101)] - 6674f4cc68ca1e3afc2a070a task start 
[INFO ] 2024-06-21 11:55:42.310 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:55:42.310 - [任务 5(101)][df2821bf-aa17-4dc8-8468-477da114adf5] - Node df2821bf-aa17-4dc8-8468-477da114adf5[df2821bf-aa17-4dc8-8468-477da114adf5] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:55:42.310 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:55:42.310 - [任务 5(101)][df2821bf-aa17-4dc8-8468-477da114adf5] - Node df2821bf-aa17-4dc8-8468-477da114adf5[df2821bf-aa17-4dc8-8468-477da114adf5] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:55:42.401 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 92 ms 
[INFO ] 2024-06-21 11:55:42.402 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 92 ms 
[INFO ] 2024-06-21 11:55:42.652 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:55:42.652 - [任务 5(101)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:55:42.653 - [任务 5(101)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:55:42.653 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:55:42.653 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:55:42.655 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 17 ms 
[INFO ] 2024-06-21 11:55:42.861 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:55:42.872 - [任务 5(101)][df2821bf-aa17-4dc8-8468-477da114adf5] - Node df2821bf-aa17-4dc8-8468-477da114adf5[df2821bf-aa17-4dc8-8468-477da114adf5] running status set to false 
[INFO ] 2024-06-21 11:55:42.872 - [任务 5(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-7074de25-ac4a-4c2f-b2c6-e101f728e4a7 
[INFO ] 2024-06-21 11:55:42.873 - [任务 5(101)][df2821bf-aa17-4dc8-8468-477da114adf5] - Node df2821bf-aa17-4dc8-8468-477da114adf5[df2821bf-aa17-4dc8-8468-477da114adf5] schema data cleaned 
[INFO ] 2024-06-21 11:55:42.873 - [任务 5(101)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-7074de25-ac4a-4c2f-b2c6-e101f728e4a7 
[INFO ] 2024-06-21 11:55:42.873 - [任务 5(101)][df2821bf-aa17-4dc8-8468-477da114adf5] - Node df2821bf-aa17-4dc8-8468-477da114adf5[df2821bf-aa17-4dc8-8468-477da114adf5] monitor closed 
[INFO ] 2024-06-21 11:55:42.873 - [任务 5(101)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070a-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:55:42.875 - [任务 5(101)][df2821bf-aa17-4dc8-8468-477da114adf5] - Node df2821bf-aa17-4dc8-8468-477da114adf5[df2821bf-aa17-4dc8-8468-477da114adf5] close complete, cost 36 ms 
[INFO ] 2024-06-21 11:55:42.875 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:55:42.875 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:55:42.875 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 42 ms 
[INFO ] 2024-06-21 11:55:42.903 - [任务 5(101)] - test run task 6674f4cc68ca1e3afc2a070a complete, cost 697ms 
[INFO ] 2024-06-21 11:55:53.850 - [任务 5(101)] - 6674f4cc68ca1e3afc2a070a task start 
[INFO ] 2024-06-21 11:55:53.957 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:55:53.958 - [任务 5(101)][fe236eab-ea36-446b-81eb-5ce0f4078c76] - Node fe236eab-ea36-446b-81eb-5ce0f4078c76[fe236eab-ea36-446b-81eb-5ce0f4078c76] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:55:53.958 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:55:53.958 - [任务 5(101)][fe236eab-ea36-446b-81eb-5ce0f4078c76] - Node fe236eab-ea36-446b-81eb-5ce0f4078c76[fe236eab-ea36-446b-81eb-5ce0f4078c76] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:55:53.971 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 16 ms 
[INFO ] 2024-06-21 11:55:54.178 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 20 ms 
[INFO ] 2024-06-21 11:55:54.814 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:55:54.815 - [任务 5(101)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:55:54.817 - [任务 5(101)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:55:54.817 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:55:54.818 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:55:54.819 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 40 ms 
[INFO ] 2024-06-21 11:55:54.825 - [任务 5(101)][增强JS] - 1718883457000 
[INFO ] 2024-06-21 11:55:54.825 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:55:54.851 - [任务 5(101)][fe236eab-ea36-446b-81eb-5ce0f4078c76] - Node fe236eab-ea36-446b-81eb-5ce0f4078c76[fe236eab-ea36-446b-81eb-5ce0f4078c76] running status set to false 
[INFO ] 2024-06-21 11:55:54.852 - [任务 5(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-5770b079-8a0f-47f1-9c39-2eb35d06093d 
[INFO ] 2024-06-21 11:55:54.853 - [任务 5(101)][fe236eab-ea36-446b-81eb-5ce0f4078c76] - Node fe236eab-ea36-446b-81eb-5ce0f4078c76[fe236eab-ea36-446b-81eb-5ce0f4078c76] schema data cleaned 
[INFO ] 2024-06-21 11:55:54.853 - [任务 5(101)][fe236eab-ea36-446b-81eb-5ce0f4078c76] - Node fe236eab-ea36-446b-81eb-5ce0f4078c76[fe236eab-ea36-446b-81eb-5ce0f4078c76] monitor closed 
[INFO ] 2024-06-21 11:55:54.854 - [任务 5(101)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-5770b079-8a0f-47f1-9c39-2eb35d06093d 
[INFO ] 2024-06-21 11:55:54.854 - [任务 5(101)][fe236eab-ea36-446b-81eb-5ce0f4078c76] - Node fe236eab-ea36-446b-81eb-5ce0f4078c76[fe236eab-ea36-446b-81eb-5ce0f4078c76] close complete, cost 21 ms 
[INFO ] 2024-06-21 11:55:54.854 - [任务 5(101)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070a-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:55:54.861 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:55:54.861 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:55:54.873 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 37 ms 
[INFO ] 2024-06-21 11:55:54.873 - [任务 5(101)] - test run task 6674f4cc68ca1e3afc2a070a complete, cost 1025ms 
[INFO ] 2024-06-21 11:56:09.666 - [任务 5(101)] - 6674f4cc68ca1e3afc2a070a task start 
[INFO ] 2024-06-21 11:56:09.787 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:56:09.788 - [任务 5(101)][4e7ae152-dca8-4090-8431-d251c9b39d6d] - Node 4e7ae152-dca8-4090-8431-d251c9b39d6d[4e7ae152-dca8-4090-8431-d251c9b39d6d] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:56:09.788 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:56:09.789 - [任务 5(101)][4e7ae152-dca8-4090-8431-d251c9b39d6d] - Node 4e7ae152-dca8-4090-8431-d251c9b39d6d[4e7ae152-dca8-4090-8431-d251c9b39d6d] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:56:09.813 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 20 ms 
[INFO ] 2024-06-21 11:56:09.814 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 26 ms 
[INFO ] 2024-06-21 11:56:10.638 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:56:10.643 - [任务 5(101)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:56:10.643 - [任务 5(101)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:56:10.649 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:56:10.650 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:56:10.650 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 51 ms 
[INFO ] 2024-06-21 11:56:10.650 - [任务 5(101)][增强JS] - 1718883457000 
[INFO ] 2024-06-21 11:56:10.654 - [任务 5(101)][增强JS] - 1.718883457E9 
[INFO ] 2024-06-21 11:56:10.654 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:56:10.657 - [任务 5(101)][4e7ae152-dca8-4090-8431-d251c9b39d6d] - Node 4e7ae152-dca8-4090-8431-d251c9b39d6d[4e7ae152-dca8-4090-8431-d251c9b39d6d] running status set to false 
[INFO ] 2024-06-21 11:56:10.667 - [任务 5(101)][4e7ae152-dca8-4090-8431-d251c9b39d6d] - Node 4e7ae152-dca8-4090-8431-d251c9b39d6d[4e7ae152-dca8-4090-8431-d251c9b39d6d] schema data cleaned 
[INFO ] 2024-06-21 11:56:10.681 - [任务 5(101)][4e7ae152-dca8-4090-8431-d251c9b39d6d] - Node 4e7ae152-dca8-4090-8431-d251c9b39d6d[4e7ae152-dca8-4090-8431-d251c9b39d6d] monitor closed 
[INFO ] 2024-06-21 11:56:10.686 - [任务 5(101)][4e7ae152-dca8-4090-8431-d251c9b39d6d] - Node 4e7ae152-dca8-4090-8431-d251c9b39d6d[4e7ae152-dca8-4090-8431-d251c9b39d6d] close complete, cost 27 ms 
[INFO ] 2024-06-21 11:56:10.686 - [任务 5(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-4ef5fb6e-77f0-4d54-ba53-0ab9bdecf8da 
[INFO ] 2024-06-21 11:56:10.687 - [任务 5(101)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-4ef5fb6e-77f0-4d54-ba53-0ab9bdecf8da 
[INFO ] 2024-06-21 11:56:10.687 - [任务 5(101)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070a-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:56:10.690 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:56:10.690 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:56:10.690 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 36 ms 
[INFO ] 2024-06-21 11:56:10.701 - [任务 5(101)] - test run task 6674f4cc68ca1e3afc2a070a complete, cost 1073ms 
[INFO ] 2024-06-21 11:59:53.245 - [任务 5(101)] - 6674f4cc68ca1e3afc2a070a task start 
[INFO ] 2024-06-21 11:59:53.315 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:59:53.315 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:59:53.315 - [任务 5(101)][29505c93-1b8c-424a-87cb-181ab3f006c0] - Node 29505c93-1b8c-424a-87cb-181ab3f006c0[29505c93-1b8c-424a-87cb-181ab3f006c0] start preload schema,table counts: 0 
[INFO ] 2024-06-21 11:59:53.315 - [任务 5(101)][29505c93-1b8c-424a-87cb-181ab3f006c0] - Node 29505c93-1b8c-424a-87cb-181ab3f006c0[29505c93-1b8c-424a-87cb-181ab3f006c0] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:59:53.340 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] preload schema finished, cost 22 ms 
[INFO ] 2024-06-21 11:59:53.340 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 22 ms 
[INFO ] 2024-06-21 11:59:53.606 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 11:59:53.614 - [任务 5(101)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:59:53.614 - [任务 5(101)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 11:59:53.615 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 11:59:53.615 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 11:59:53.615 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 26 ms 
[INFO ] 2024-06-21 11:59:53.778 - [任务 5(101)][增强JS] - 1718883457000 
[INFO ] 2024-06-21 11:59:53.778 - [任务 5(101)][增强JS] - 1.718883457E9 
[INFO ] 2024-06-21 11:59:53.782 - [任务 5(101)][增强JS] - 1.718883457E9 
[INFO ] 2024-06-21 11:59:53.782 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] running status set to false 
[INFO ] 2024-06-21 11:59:53.782 - [任务 5(101)][29505c93-1b8c-424a-87cb-181ab3f006c0] - Node 29505c93-1b8c-424a-87cb-181ab3f006c0[29505c93-1b8c-424a-87cb-181ab3f006c0] running status set to false 
[INFO ] 2024-06-21 11:59:53.782 - [任务 5(101)][29505c93-1b8c-424a-87cb-181ab3f006c0] - Node 29505c93-1b8c-424a-87cb-181ab3f006c0[29505c93-1b8c-424a-87cb-181ab3f006c0] schema data cleaned 
[INFO ] 2024-06-21 11:59:53.782 - [任务 5(101)][29505c93-1b8c-424a-87cb-181ab3f006c0] - Node 29505c93-1b8c-424a-87cb-181ab3f006c0[29505c93-1b8c-424a-87cb-181ab3f006c0] monitor closed 
[INFO ] 2024-06-21 11:59:53.792 - [任务 5(101)][29505c93-1b8c-424a-87cb-181ab3f006c0] - Node 29505c93-1b8c-424a-87cb-181ab3f006c0[29505c93-1b8c-424a-87cb-181ab3f006c0] close complete, cost 1 ms 
[INFO ] 2024-06-21 11:59:53.803 - [任务 5(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SouceMysql-753ff34e-a750-4535-893f-510f8e597750 
[INFO ] 2024-06-21 11:59:53.804 - [任务 5(101)][增强JS] - PDK connector node released: ScriptExecutor-SouceMysql-753ff34e-a750-4535-893f-510f8e597750 
[INFO ] 2024-06-21 11:59:53.804 - [任务 5(101)][增强JS] - [ScriptExecutorsManager-6674f4cc68ca1e3afc2a070a-ca6c57ae-6983-401b-9f57-bea907d063be-667413fd7b5e1f6c3b139e78] schema data cleaned 
[INFO ] 2024-06-21 11:59:53.808 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] schema data cleaned 
[INFO ] 2024-06-21 11:59:53.808 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] monitor closed 
[INFO ] 2024-06-21 11:59:53.820 - [任务 5(101)][增强JS] - Node 增强JS[ca6c57ae-6983-401b-9f57-bea907d063be] close complete, cost 27 ms 
[INFO ] 2024-06-21 11:59:53.821 - [任务 5(101)] - test run task 6674f4cc68ca1e3afc2a070a complete, cost 612ms 
[INFO ] 2024-06-21 12:00:24.830 - [任务 5(101)] - 6674f4cc68ca1e3afc2a070a task start 
[INFO ] 2024-06-21 12:00:24.871 - [任务 5(101)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] start preload schema,table counts: 1 
[INFO ] 2024-06-21 12:00:24.872 - [任务 5(101)][24758d5e-03f4-4695-80ea-a07fc59d05bb] - Node 24758d5e-03f4-4695-80ea-a07fc59d05bb[24758d5e-03f4-4695-80ea-a07fc59d05bb] start preload schema,table counts: 0 
[INFO ] 2024-06-21 12:00:24.872 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 12:00:24.872 - [任务 5(101)][24758d5e-03f4-4695-80ea-a07fc59d05bb] - Node 24758d5e-03f4-4695-80ea-a07fc59d05bb[24758d5e-03f4-4695-80ea-a07fc59d05bb] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 12:00:24.890 - [任务 5(101)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] preload schema finished, cost 18 ms 
[INFO ] 2024-06-21 12:00:24.890 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 18 ms 
[INFO ] 2024-06-21 12:00:25.323 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 12:00:25.326 - [任务 5(101)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 12:00:25.326 - [任务 5(101)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 12:00:25.326 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 12:00:25.326 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 12:00:25.327 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 26 ms 
[INFO ] 2024-06-21 12:00:25.349 - [任务 5(101)][标准JS] - 1718883457000 
[INFO ] 2024-06-21 12:00:25.350 - [任务 5(101)][标准JS] - 1.718883457E9 
[INFO ] 2024-06-21 12:00:25.350 - [任务 5(101)][标准JS] - 1.718883457E9 
[INFO ] 2024-06-21 12:00:25.356 - [任务 5(101)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] running status set to false 
[INFO ] 2024-06-21 12:00:25.356 - [任务 5(101)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] schema data cleaned 
[INFO ] 2024-06-21 12:00:25.356 - [任务 5(101)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] monitor closed 
[INFO ] 2024-06-21 12:00:25.357 - [任务 5(101)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] close complete, cost 2 ms 
[INFO ] 2024-06-21 12:00:25.357 - [任务 5(101)][24758d5e-03f4-4695-80ea-a07fc59d05bb] - Node 24758d5e-03f4-4695-80ea-a07fc59d05bb[24758d5e-03f4-4695-80ea-a07fc59d05bb] running status set to false 
[INFO ] 2024-06-21 12:00:25.357 - [任务 5(101)][24758d5e-03f4-4695-80ea-a07fc59d05bb] - Node 24758d5e-03f4-4695-80ea-a07fc59d05bb[24758d5e-03f4-4695-80ea-a07fc59d05bb] schema data cleaned 
[INFO ] 2024-06-21 12:00:25.357 - [任务 5(101)][24758d5e-03f4-4695-80ea-a07fc59d05bb] - Node 24758d5e-03f4-4695-80ea-a07fc59d05bb[24758d5e-03f4-4695-80ea-a07fc59d05bb] monitor closed 
[INFO ] 2024-06-21 12:00:25.357 - [任务 5(101)][24758d5e-03f4-4695-80ea-a07fc59d05bb] - Node 24758d5e-03f4-4695-80ea-a07fc59d05bb[24758d5e-03f4-4695-80ea-a07fc59d05bb] close complete, cost 0 ms 
[INFO ] 2024-06-21 12:00:25.378 - [任务 5(101)] - test run task 6674f4cc68ca1e3afc2a070a complete, cost 558ms 
[INFO ] 2024-06-21 12:03:48.242 - [任务 5(101)] - 6674f4cc68ca1e3afc2a070a task start 
[INFO ] 2024-06-21 12:03:48.366 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] start preload schema,table counts: 1 
[INFO ] 2024-06-21 12:03:48.366 - [任务 5(101)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] start preload schema,table counts: 1 
[INFO ] 2024-06-21 12:03:48.368 - [任务 5(101)][5ab91430-f5bc-4375-91f6-91d1fd2d1742] - Node 5ab91430-f5bc-4375-91f6-91d1fd2d1742[5ab91430-f5bc-4375-91f6-91d1fd2d1742] start preload schema,table counts: 0 
[INFO ] 2024-06-21 12:03:48.368 - [任务 5(101)][5ab91430-f5bc-4375-91f6-91d1fd2d1742] - Node 5ab91430-f5bc-4375-91f6-91d1fd2d1742[5ab91430-f5bc-4375-91f6-91d1fd2d1742] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 12:03:48.420 - [任务 5(101)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] preload schema finished, cost 52 ms 
[INFO ] 2024-06-21 12:03:48.420 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] preload schema finished, cost 52 ms 
[INFO ] 2024-06-21 12:03:48.707 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] running status set to false 
[INFO ] 2024-06-21 12:03:48.738 - [任务 5(101)][testReference] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 12:03:48.739 - [任务 5(101)][testReference] - PDK connector node released: HazelcastSampleSourcePdkDataNode-d892a224-f6b4-4569-ba61-f565a7b87b37 
[INFO ] 2024-06-21 12:03:48.742 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] schema data cleaned 
[INFO ] 2024-06-21 12:03:48.742 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] monitor closed 
[INFO ] 2024-06-21 12:03:48.758 - [任务 5(101)][testReference] - Node testReference[d892a224-f6b4-4569-ba61-f565a7b87b37] close complete, cost 36 ms 
[INFO ] 2024-06-21 12:03:48.758 - [任务 5(101)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] running status set to false 
[INFO ] 2024-06-21 12:03:48.759 - [任务 5(101)][5ab91430-f5bc-4375-91f6-91d1fd2d1742] - Node 5ab91430-f5bc-4375-91f6-91d1fd2d1742[5ab91430-f5bc-4375-91f6-91d1fd2d1742] running status set to false 
[INFO ] 2024-06-21 12:03:48.759 - [任务 5(101)][5ab91430-f5bc-4375-91f6-91d1fd2d1742] - Node 5ab91430-f5bc-4375-91f6-91d1fd2d1742[5ab91430-f5bc-4375-91f6-91d1fd2d1742] schema data cleaned 
[INFO ] 2024-06-21 12:03:48.763 - [任务 5(101)][5ab91430-f5bc-4375-91f6-91d1fd2d1742] - Node 5ab91430-f5bc-4375-91f6-91d1fd2d1742[5ab91430-f5bc-4375-91f6-91d1fd2d1742] monitor closed 
[INFO ] 2024-06-21 12:03:48.763 - [任务 5(101)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] schema data cleaned 
[INFO ] 2024-06-21 12:03:48.763 - [任务 5(101)][5ab91430-f5bc-4375-91f6-91d1fd2d1742] - Node 5ab91430-f5bc-4375-91f6-91d1fd2d1742[5ab91430-f5bc-4375-91f6-91d1fd2d1742] close complete, cost 4 ms 
[INFO ] 2024-06-21 12:03:48.763 - [任务 5(101)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] monitor closed 
[INFO ] 2024-06-21 12:03:48.763 - [任务 5(101)][标准JS] - Node 标准JS[1b7c4988-58fd-4187-a067-82f2f091e751] close complete, cost 7 ms 
[INFO ] 2024-06-21 12:03:48.966 - [任务 5(101)] - test run task 6674f4cc68ca1e3afc2a070a complete, cost 598ms 
