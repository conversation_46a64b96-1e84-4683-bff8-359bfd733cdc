[TRACE] 2025-02-20 22:03:38.760 - [任务 25] - Task initialization... 
[TRACE] 2025-02-20 22:03:38.910 - [任务 25] - Start task milestones: 67b73607875cdc50c184f3b1(任务 25) 
[INFO ] 2025-02-20 22:03:38.910 - [任务 25] - Loading table structure completed 
[TRACE] 2025-02-20 22:03:38.980 - [任务 25] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-02-20 22:03:38.980 - [任务 25] - The engine receives 任务 25 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-20 22:03:39.041 - [任务 25] - Task started 
[TRACE] 2025-02-20 22:03:39.041 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] start preload schema,table counts: 2 
[TRACE] 2025-02-20 22:03:39.041 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] start preload schema,table counts: 2 
[TRACE] 2025-02-20 22:03:39.041 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] start preload schema,table counts: 2 
[TRACE] 2025-02-20 22:03:39.042 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:03:39.042 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:03:39.042 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] preload schema finished, cost 0 ms 
[INFO ] 2025-02-20 22:03:39.042 - [任务 25][Pg] - Enable partition table support for source database 
[TRACE] 2025-02-20 22:03:39.042 - [任务 25][表编辑] - Node table_rename_processor(表编辑: 3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c) enable batch process 
[ERROR] 2025-02-20 22:03:39.822 - [任务 25][Sybase] - Sink connector(Sybase) initialization error: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Full Stack Trace -->
java.lang.RuntimeException: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	... 9 more
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:03:39.823 - [任务 25][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer 
[ERROR] 2025-02-20 22:03:39.835 - [任务 25][Sybase] - Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Error Message -->
Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer

<-- Simple Stack Trace -->
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	...

<-- Full Stack Trace -->
java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:03:39.835 - [任务 25][Sybase] - Job suspend in error handle 
[INFO ] 2025-02-20 22:03:40.191 - [任务 25][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-02-20 22:03:40.196 - [任务 25][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-02-20 22:03:40.196 - [任务 25][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-02-20 22:03:40.196 - [任务 25][Pg] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-20 22:03:40.796 - [任务 25][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-20 22:03:40.799 - [任务 25][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-20 22:03:40.886 - [任务 25][Pg] - Starting batch read from 2 tables 
[TRACE] 2025-02-20 22:03:40.886 - [任务 25][Pg] - Initial sync started 
[INFO ] 2025-02-20 22:03:40.889 - [任务 25][Pg] - Starting batch read from table: td_null_col 
[TRACE] 2025-02-20 22:03:40.889 - [任务 25][Pg] - Table td_null_col is going to be initial synced 
[TRACE] 2025-02-20 22:03:40.981 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] running status set to false 
[TRACE] 2025-02-20 22:03:40.987 - [任务 25][Pg] - Query snapshot row size completed: Pg(07c5b86c-af18-4cb6-845c-d800783f9b32) 
[TRACE] 2025-02-20 22:03:41.024 - [任务 25][Pg] - Initial sync completed 
[INFO ] 2025-02-20 22:03:41.024 - [任务 25][Pg] - Batch read completed. 
[TRACE] 2025-02-20 22:03:41.024 - [任务 25][Pg] - Incremental sync starting... 
[TRACE] 2025-02-20 22:03:41.024 - [任务 25][Pg] - Incremental sync completed 
[TRACE] 2025-02-20 22:03:41.165 - [任务 25][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_07c5b86c-af18-4cb6-845c-d800783f9b32_1740060219625 
[TRACE] 2025-02-20 22:03:41.165 - [任务 25][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_07c5b86c-af18-4cb6-845c-d800783f9b32_1740060219625 
[TRACE] 2025-02-20 22:03:41.172 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] schema data cleaned 
[TRACE] 2025-02-20 22:03:41.173 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] monitor closed 
[TRACE] 2025-02-20 22:03:41.181 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] close complete, cost 206 ms 
[TRACE] 2025-02-20 22:03:41.182 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] running status set to false 
[TRACE] 2025-02-20 22:03:41.182 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] schema data cleaned 
[TRACE] 2025-02-20 22:03:41.182 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] monitor closed 
[TRACE] 2025-02-20 22:03:41.187 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] close complete, cost 6 ms 
[TRACE] 2025-02-20 22:03:41.192 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] running status set to false 
[TRACE] 2025-02-20 22:03:41.306 - [任务 25][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_52faca30-d179-493d-b833-8e9026653bdb_1740060219739 
[TRACE] 2025-02-20 22:03:41.306 - [任务 25][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_52faca30-d179-493d-b833-8e9026653bdb_1740060219739 
[TRACE] 2025-02-20 22:03:41.307 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] schema data cleaned 
[TRACE] 2025-02-20 22:03:41.308 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] monitor closed 
[TRACE] 2025-02-20 22:03:41.308 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] close complete, cost 121 ms 
[INFO ] 2025-02-20 22:03:44.615 - [任务 25] - Task [任务 25] cannot retry, reason: Task retry service not start 
[TRACE] 2025-02-20 22:03:44.616 - [任务 25] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-20 22:03:44.616 - [任务 25] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1e2de25e 
[TRACE] 2025-02-20 22:03:44.731 - [任务 25] - Stop task milestones: 67b73607875cdc50c184f3b1(任务 25)  
[TRACE] 2025-02-20 22:03:44.747 - [任务 25] - Stopped task aspect(s) 
[TRACE] 2025-02-20 22:03:44.747 - [任务 25] - Snapshot order controller have been removed 
[INFO ] 2025-02-20 22:03:44.747 - [任务 25] - Task stopped. 
[TRACE] 2025-02-20 22:03:44.772 - [任务 25] - Remove memory task client succeed, task: 任务 25[67b73607875cdc50c184f3b1] 
[TRACE] 2025-02-20 22:03:44.775 - [任务 25] - Destroy memory task client cache succeed, task: 任务 25[67b73607875cdc50c184f3b1] 
[TRACE] 2025-02-20 22:04:17.273 - [任务 25] - Task initialization... 
[TRACE] 2025-02-20 22:04:17.274 - [任务 25] - Start task milestones: 67b73607875cdc50c184f3b1(任务 25) 
[INFO ] 2025-02-20 22:04:17.372 - [任务 25] - Loading table structure completed 
[TRACE] 2025-02-20 22:04:17.372 - [任务 25] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-20 22:04:17.412 - [任务 25] - The engine receives 任务 25 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-20 22:04:17.412 - [任务 25] - Task started 
[TRACE] 2025-02-20 22:04:17.440 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:04:17.445 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:04:17.445 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:04:17.445 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:04:17.445 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:04:17.445 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:04:17.445 - [任务 25][表编辑] - Node table_rename_processor(表编辑: 3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c) enable batch process 
[ERROR] 2025-02-20 22:04:18.107 - [任务 25][Sybase] - Sink connector(Sybase) initialization error: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Full Stack Trace -->
java.lang.RuntimeException: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	... 9 more
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:04:18.113 - [任务 25][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer 
[ERROR] 2025-02-20 22:04:18.117 - [任务 25][Sybase] - Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Error Message -->
Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer

<-- Simple Stack Trace -->
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	...

<-- Full Stack Trace -->
java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:04:18.122 - [任务 25][Sybase] - Job suspend in error handle 
[INFO ] 2025-02-20 22:04:18.654 - [任务 25][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-02-20 22:04:18.655 - [任务 25][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-02-20 22:04:18.655 - [任务 25][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-02-20 22:04:18.655 - [任务 25][Pg] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-20 22:04:19.230 - [任务 25][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-20 22:04:19.232 - [任务 25][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-20 22:04:19.321 - [任务 25][Pg] - Starting batch read from 1 tables 
[TRACE] 2025-02-20 22:04:19.344 - [任务 25][Pg] - Initial sync started 
[INFO ] 2025-02-20 22:04:19.345 - [任务 25][Pg] - Starting batch read from table: td_null_col 
[TRACE] 2025-02-20 22:04:19.345 - [任务 25][Pg] - Table td_null_col is going to be initial synced 
[TRACE] 2025-02-20 22:04:19.429 - [任务 25][Pg] - Query snapshot row size completed: Pg(07c5b86c-af18-4cb6-845c-d800783f9b32) 
[TRACE] 2025-02-20 22:04:19.437 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] running status set to false 
[TRACE] 2025-02-20 22:04:19.470 - [任务 25][Pg] - Initial sync completed 
[INFO ] 2025-02-20 22:04:19.470 - [任务 25][Pg] - Batch read completed. 
[TRACE] 2025-02-20 22:04:19.470 - [任务 25][Pg] - Incremental sync starting... 
[TRACE] 2025-02-20 22:04:19.470 - [任务 25][Pg] - Incremental sync completed 
[TRACE] 2025-02-20 22:04:19.633 - [任务 25][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_07c5b86c-af18-4cb6-845c-d800783f9b32_1740060258129 
[TRACE] 2025-02-20 22:04:19.633 - [任务 25][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_07c5b86c-af18-4cb6-845c-d800783f9b32_1740060258129 
[TRACE] 2025-02-20 22:04:19.634 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] schema data cleaned 
[TRACE] 2025-02-20 22:04:19.636 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] monitor closed 
[TRACE] 2025-02-20 22:04:19.638 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] close complete, cost 209 ms 
[TRACE] 2025-02-20 22:04:19.643 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] running status set to false 
[TRACE] 2025-02-20 22:04:19.643 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] schema data cleaned 
[TRACE] 2025-02-20 22:04:19.643 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] monitor closed 
[TRACE] 2025-02-20 22:04:19.643 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] close complete, cost 1 ms 
[TRACE] 2025-02-20 22:04:19.643 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] running status set to false 
[TRACE] 2025-02-20 22:04:19.760 - [任务 25][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_52faca30-d179-493d-b833-8e9026653bdb_1740060258010 
[TRACE] 2025-02-20 22:04:19.760 - [任务 25][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_52faca30-d179-493d-b833-8e9026653bdb_1740060258010 
[TRACE] 2025-02-20 22:04:19.760 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] schema data cleaned 
[TRACE] 2025-02-20 22:04:19.760 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] monitor closed 
[TRACE] 2025-02-20 22:04:19.805 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] close complete, cost 121 ms 
[INFO ] 2025-02-20 22:04:19.805 - [任务 25] - Task [任务 25] cannot retry, reason: Task retry service not start 
[TRACE] 2025-02-20 22:04:19.811 - [任务 25] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-20 22:04:19.811 - [任务 25] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3c0cade3 
[TRACE] 2025-02-20 22:04:19.940 - [任务 25] - Stop task milestones: 67b73607875cdc50c184f3b1(任务 25)  
[TRACE] 2025-02-20 22:04:19.942 - [任务 25] - Stopped task aspect(s) 
[TRACE] 2025-02-20 22:04:19.942 - [任务 25] - Snapshot order controller have been removed 
[INFO ] 2025-02-20 22:04:19.942 - [任务 25] - Task stopped. 
[TRACE] 2025-02-20 22:04:19.960 - [任务 25] - Remove memory task client succeed, task: 任务 25[67b73607875cdc50c184f3b1] 
[TRACE] 2025-02-20 22:04:19.962 - [任务 25] - Destroy memory task client cache succeed, task: 任务 25[67b73607875cdc50c184f3b1] 
[TRACE] 2025-02-20 22:04:56.874 - [任务 25] - Task initialization... 
[TRACE] 2025-02-20 22:04:56.955 - [任务 25] - Start task milestones: 67b73607875cdc50c184f3b1(任务 25) 
[INFO ] 2025-02-20 22:04:56.955 - [任务 25] - Loading table structure completed 
[TRACE] 2025-02-20 22:04:57.031 - [任务 25] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-20 22:04:57.031 - [任务 25] - The engine receives 任务 25 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-20 22:04:57.067 - [任务 25] - Task started 
[TRACE] 2025-02-20 22:04:57.067 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:04:57.067 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:04:57.067 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:04:57.067 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] preload schema finished, cost 1 ms 
[TRACE] 2025-02-20 22:04:57.068 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:04:57.068 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:04:57.068 - [任务 25][表编辑] - Node table_rename_processor(表编辑: 3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c) enable batch process 
[ERROR] 2025-02-20 22:04:57.759 - [任务 25][Sybase] - Sink connector(Sybase) initialization error: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Full Stack Trace -->
java.lang.RuntimeException: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	... 9 more
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:04:57.764 - [任务 25][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer 
[ERROR] 2025-02-20 22:04:57.773 - [任务 25][Sybase] - Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Error Message -->
Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer

<-- Simple Stack Trace -->
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	...

<-- Full Stack Trace -->
java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:04:57.980 - [任务 25][Sybase] - Job suspend in error handle 
[INFO ] 2025-02-20 22:04:58.294 - [任务 25][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-02-20 22:04:58.295 - [任务 25][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-02-20 22:04:58.295 - [任务 25][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-02-20 22:04:58.295 - [任务 25][Pg] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-20 22:04:58.920 - [任务 25][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-20 22:04:58.921 - [任务 25][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-20 22:04:59.019 - [任务 25][Pg] - Starting batch read from 1 tables 
[TRACE] 2025-02-20 22:04:59.038 - [任务 25][Pg] - Initial sync started 
[INFO ] 2025-02-20 22:04:59.039 - [任务 25][Pg] - Starting batch read from table: td_null_col 
[TRACE] 2025-02-20 22:04:59.118 - [任务 25][Pg] - Table td_null_col is going to be initial synced 
[TRACE] 2025-02-20 22:04:59.118 - [任务 25][Pg] - Query snapshot row size completed: Pg(07c5b86c-af18-4cb6-845c-d800783f9b32) 
[TRACE] 2025-02-20 22:04:59.166 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] running status set to false 
[TRACE] 2025-02-20 22:04:59.166 - [任务 25][Pg] - Initial sync completed 
[INFO ] 2025-02-20 22:04:59.166 - [任务 25][Pg] - Batch read completed. 
[TRACE] 2025-02-20 22:04:59.166 - [任务 25][Pg] - Incremental sync starting... 
[TRACE] 2025-02-20 22:04:59.310 - [任务 25][Pg] - Incremental sync completed 
[TRACE] 2025-02-20 22:04:59.312 - [任务 25][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_07c5b86c-af18-4cb6-845c-d800783f9b32_1740060297760 
[TRACE] 2025-02-20 22:04:59.312 - [任务 25][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_07c5b86c-af18-4cb6-845c-d800783f9b32_1740060297760 
[TRACE] 2025-02-20 22:04:59.312 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] schema data cleaned 
[TRACE] 2025-02-20 22:04:59.312 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] monitor closed 
[TRACE] 2025-02-20 22:04:59.321 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] close complete, cost 196 ms 
[TRACE] 2025-02-20 22:04:59.321 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] running status set to false 
[TRACE] 2025-02-20 22:04:59.321 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] schema data cleaned 
[TRACE] 2025-02-20 22:04:59.321 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] monitor closed 
[TRACE] 2025-02-20 22:04:59.321 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] close complete, cost 1 ms 
[TRACE] 2025-02-20 22:04:59.322 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] running status set to false 
[TRACE] 2025-02-20 22:04:59.437 - [任务 25][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_52faca30-d179-493d-b833-8e9026653bdb_1740060297652 
[TRACE] 2025-02-20 22:04:59.437 - [任务 25][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_52faca30-d179-493d-b833-8e9026653bdb_1740060297652 
[TRACE] 2025-02-20 22:04:59.437 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] schema data cleaned 
[TRACE] 2025-02-20 22:04:59.437 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] monitor closed 
[TRACE] 2025-02-20 22:04:59.447 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] close complete, cost 116 ms 
[INFO ] 2025-02-20 22:05:00.053 - [任务 25] - Task [任务 25] cannot retry, reason: Task retry service not start 
[TRACE] 2025-02-20 22:05:00.059 - [任务 25] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-20 22:05:00.059 - [任务 25] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@62dca5e7 
[TRACE] 2025-02-20 22:05:00.234 - [任务 25] - Stop task milestones: 67b73607875cdc50c184f3b1(任务 25)  
[TRACE] 2025-02-20 22:05:00.235 - [任务 25] - Stopped task aspect(s) 
[TRACE] 2025-02-20 22:05:00.235 - [任务 25] - Snapshot order controller have been removed 
[INFO ] 2025-02-20 22:05:00.236 - [任务 25] - Task stopped. 
[TRACE] 2025-02-20 22:05:00.255 - [任务 25] - Remove memory task client succeed, task: 任务 25[67b73607875cdc50c184f3b1] 
[TRACE] 2025-02-20 22:05:00.258 - [任务 25] - Destroy memory task client cache succeed, task: 任务 25[67b73607875cdc50c184f3b1] 
[TRACE] 2025-02-20 22:06:04.874 - [任务 25] - Task initialization... 
[TRACE] 2025-02-20 22:06:04.971 - [任务 25] - Start task milestones: 67b73607875cdc50c184f3b1(任务 25) 
[INFO ] 2025-02-20 22:06:04.971 - [任务 25] - Loading table structure completed 
[TRACE] 2025-02-20 22:06:05.068 - [任务 25] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-20 22:06:05.068 - [任务 25] - The engine receives 任务 25 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-20 22:06:05.113 - [任务 25] - Task started 
[TRACE] 2025-02-20 22:06:05.119 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:06:05.119 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:06:05.119 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:06:05.119 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:06:05.119 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:06:05.119 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:06:05.119 - [任务 25][表编辑] - Node table_rename_processor(表编辑: 3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c) enable batch process 
[ERROR] 2025-02-20 22:06:44.803 - [任务 25][Sybase] - Sink connector(Sybase) initialization error: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Full Stack Trace -->
java.lang.RuntimeException: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	... 9 more
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:06:44.805 - [任务 25][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer 
[ERROR] 2025-02-20 22:06:44.815 - [任务 25][Sybase] - Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Error Message -->
Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer

<-- Simple Stack Trace -->
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	...

<-- Full Stack Trace -->
java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:06:45.020 - [任务 25][Sybase] - Job suspend in error handle 
[INFO ] 2025-02-20 22:06:45.060 - [任务 25][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-02-20 22:06:45.060 - [任务 25][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-02-20 22:06:45.060 - [任务 25][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-02-20 22:06:45.264 - [任务 25][Pg] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-20 22:06:45.785 - [任务 25][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-20 22:06:45.787 - [任务 25][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-20 22:06:45.888 - [任务 25][Pg] - Batch read completed. 
[TRACE] 2025-02-20 22:06:45.888 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] running status set to false 
[TRACE] 2025-02-20 22:06:45.908 - [任务 25][Pg] - Incremental sync starting... 
[TRACE] 2025-02-20 22:06:45.908 - [任务 25][Pg] - Incremental sync completed 
[TRACE] 2025-02-20 22:06:45.918 - [任务 25][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_07c5b86c-af18-4cb6-845c-d800783f9b32_1740060365731 
[TRACE] 2025-02-20 22:06:45.918 - [任务 25][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_07c5b86c-af18-4cb6-845c-d800783f9b32_1740060365731 
[TRACE] 2025-02-20 22:06:45.918 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] schema data cleaned 
[TRACE] 2025-02-20 22:06:45.923 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] monitor closed 
[TRACE] 2025-02-20 22:06:45.923 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] close complete, cost 37 ms 
[TRACE] 2025-02-20 22:06:45.923 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] running status set to false 
[TRACE] 2025-02-20 22:06:45.923 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] schema data cleaned 
[TRACE] 2025-02-20 22:06:45.924 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] monitor closed 
[TRACE] 2025-02-20 22:06:45.924 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] close complete, cost 1 ms 
[TRACE] 2025-02-20 22:06:45.924 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] running status set to false 
[TRACE] 2025-02-20 22:06:46.044 - [任务 25][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_52faca30-d179-493d-b833-8e9026653bdb_1740060365879 
[TRACE] 2025-02-20 22:06:46.046 - [任务 25][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_52faca30-d179-493d-b833-8e9026653bdb_1740060365879 
[TRACE] 2025-02-20 22:06:46.047 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] schema data cleaned 
[TRACE] 2025-02-20 22:06:46.047 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] monitor closed 
[TRACE] 2025-02-20 22:06:46.048 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] close complete, cost 122 ms 
[TRACE] 2025-02-20 22:06:49.797 - [任务 25] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-20 22:06:49.916 - [任务 25] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4e6c632b 
[TRACE] 2025-02-20 22:06:49.916 - [任务 25] - Stop task milestones: 67b73607875cdc50c184f3b1(任务 25)  
[TRACE] 2025-02-20 22:06:49.924 - [任务 25] - Stopped task aspect(s) 
[TRACE] 2025-02-20 22:06:49.924 - [任务 25] - Snapshot order controller have been removed 
[INFO ] 2025-02-20 22:06:49.939 - [任务 25] - Task stopped. 
[TRACE] 2025-02-20 22:06:49.942 - [任务 25] - Remove memory task client succeed, task: 任务 25[67b73607875cdc50c184f3b1] 
[TRACE] 2025-02-20 22:06:49.942 - [任务 25] - Destroy memory task client cache succeed, task: 任务 25[67b73607875cdc50c184f3b1] 
[TRACE] 2025-02-20 22:06:59.629 - [任务 25] - Task initialization... 
[TRACE] 2025-02-20 22:06:59.630 - [任务 25] - Start task milestones: 67b73607875cdc50c184f3b1(任务 25) 
[INFO ] 2025-02-20 22:06:59.734 - [任务 25] - Loading table structure completed 
[TRACE] 2025-02-20 22:06:59.734 - [任务 25] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-20 22:06:59.784 - [任务 25] - The engine receives 任务 25 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-20 22:06:59.784 - [任务 25] - Task started 
[TRACE] 2025-02-20 22:06:59.806 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:06:59.806 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:06:59.806 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:06:59.812 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] preload schema finished, cost 1 ms 
[TRACE] 2025-02-20 22:06:59.812 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:06:59.812 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:06:59.812 - [任务 25][表编辑] - Node table_rename_processor(表编辑: 3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c) enable batch process 
[ERROR] 2025-02-20 22:08:49.810 - [任务 25][Sybase] - Sink connector(Sybase) initialization error: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Full Stack Trace -->
java.lang.RuntimeException: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	... 9 more
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:08:49.818 - [任务 25][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer 
[ERROR] 2025-02-20 22:08:49.858 - [任务 25][Sybase] - Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Error Message -->
Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer

<-- Simple Stack Trace -->
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	...

<-- Full Stack Trace -->
java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:08:50.072 - [任务 25][Sybase] - Job suspend in error handle 
[INFO ] 2025-02-20 22:08:50.566 - [任务 25][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-02-20 22:08:50.567 - [任务 25][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-02-20 22:08:50.567 - [任务 25][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-02-20 22:08:50.567 - [任务 25][Pg] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-20 22:08:51.394 - [任务 25][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-20 22:08:51.394 - [任务 25][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-20 22:08:51.525 - [任务 25][Pg] - Starting batch read from 1 tables 
[TRACE] 2025-02-20 22:08:51.525 - [任务 25][Pg] - Initial sync started 
[INFO ] 2025-02-20 22:08:51.529 - [任务 25][Pg] - Starting batch read from table: td_null_col 
[TRACE] 2025-02-20 22:08:51.530 - [任务 25][Pg] - Table td_null_col is going to be initial synced 
[TRACE] 2025-02-20 22:08:51.624 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] running status set to false 
[INFO ] 2025-02-20 22:08:51.631 - [任务 25][Pg] - Retry operation null failed, total cost 14:08:51.613000 
[TRACE] 2025-02-20 22:08:51.646 - [任务 25][Pg] - Initial sync completed 
[TRACE] 2025-02-20 22:08:51.647 - [任务 25][Pg] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.InterruptedException 
[TRACE] 2025-02-20 22:08:51.659 - [任务 25][Pg] - Query snapshot row size completed: Pg(07c5b86c-af18-4cb6-845c-d800783f9b32) 
[ERROR] 2025-02-20 22:08:51.661 - [任务 25][Pg] - java.lang.RuntimeException: java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.handleThrowable(HazelcastSourcePdkDataNode.java:445)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:414)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:356)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:269)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:490)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:412)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:219)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:134)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:498)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 13 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	... 17 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:146)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutHashSplit(CommonDbConnector.java:697)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:691)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:577)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 21 more
Caused by: java.lang.InterruptedException
	at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:944)
	at com.zaxxer.hikari.util.ConcurrentBag.borrow(ConcurrentBag.java:151)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:180)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:128)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 26 more

[TRACE] 2025-02-20 22:08:51.943 - [任务 25][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_07c5b86c-af18-4cb6-845c-d800783f9b32_1740060529845 
[TRACE] 2025-02-20 22:08:51.944 - [任务 25][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_07c5b86c-af18-4cb6-845c-d800783f9b32_1740060529845 
[TRACE] 2025-02-20 22:08:51.944 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] schema data cleaned 
[TRACE] 2025-02-20 22:08:51.944 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] monitor closed 
[TRACE] 2025-02-20 22:08:51.954 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] close complete, cost 343 ms 
[TRACE] 2025-02-20 22:08:51.954 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] running status set to false 
[TRACE] 2025-02-20 22:08:51.954 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] schema data cleaned 
[TRACE] 2025-02-20 22:08:51.954 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] monitor closed 
[TRACE] 2025-02-20 22:08:51.955 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] close complete, cost 2 ms 
[TRACE] 2025-02-20 22:08:52.093 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] running status set to false 
[TRACE] 2025-02-20 22:08:52.093 - [任务 25][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_52faca30-d179-493d-b833-8e9026653bdb_1740060420388 
[TRACE] 2025-02-20 22:08:52.093 - [任务 25][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_52faca30-d179-493d-b833-8e9026653bdb_1740060420388 
[TRACE] 2025-02-20 22:08:52.093 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] schema data cleaned 
[TRACE] 2025-02-20 22:08:52.093 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] monitor closed 
[TRACE] 2025-02-20 22:08:52.096 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] close complete, cost 140 ms 
[INFO ] 2025-02-20 22:08:54.684 - [任务 25] - Task [任务 25] cannot retry, reason: Task retry service not start 
[TRACE] 2025-02-20 22:08:54.705 - [任务 25] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-20 22:08:54.706 - [任务 25] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@59b64452 
[TRACE] 2025-02-20 22:08:54.852 - [任务 25] - Stop task milestones: 67b73607875cdc50c184f3b1(任务 25)  
[TRACE] 2025-02-20 22:08:54.852 - [任务 25] - Stopped task aspect(s) 
[TRACE] 2025-02-20 22:08:54.852 - [任务 25] - Snapshot order controller have been removed 
[INFO ] 2025-02-20 22:08:54.852 - [任务 25] - Task stopped. 
[TRACE] 2025-02-20 22:08:54.865 - [任务 25] - Remove memory task client succeed, task: 任务 25[67b73607875cdc50c184f3b1] 
[TRACE] 2025-02-20 22:08:54.868 - [任务 25] - Destroy memory task client cache succeed, task: 任务 25[67b73607875cdc50c184f3b1] 
[TRACE] 2025-02-20 22:10:09.082 - [任务 25] - Task initialization... 
[TRACE] 2025-02-20 22:10:09.083 - [任务 25] - Start task milestones: 67b73607875cdc50c184f3b1(任务 25) 
[INFO ] 2025-02-20 22:10:09.231 - [任务 25] - Loading table structure completed 
[TRACE] 2025-02-20 22:10:09.231 - [任务 25] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-20 22:10:09.292 - [任务 25] - The engine receives 任务 25 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-20 22:10:09.292 - [任务 25] - Task started 
[TRACE] 2025-02-20 22:10:09.319 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:10:09.319 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:10:09.319 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:10:09.319 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:10:09.320 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:10:09.320 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:10:09.525 - [任务 25][表编辑] - Node table_rename_processor(表编辑: 3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c) enable batch process 
[ERROR] 2025-02-20 22:10:10.142 - [任务 25][Sybase] - Sink connector(Sybase) initialization error: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Full Stack Trace -->
java.lang.RuntimeException: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	... 9 more
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:10:10.145 - [任务 25][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer 
[ERROR] 2025-02-20 22:10:10.152 - [任务 25][Sybase] - Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Error Message -->
Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer

<-- Simple Stack Trace -->
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	...

<-- Full Stack Trace -->
java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:10:10.159 - [任务 25][Sybase] - Job suspend in error handle 
[INFO ] 2025-02-20 22:10:10.551 - [任务 25][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-02-20 22:10:10.551 - [任务 25][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-02-20 22:10:10.551 - [任务 25][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-02-20 22:10:10.551 - [任务 25][Pg] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-20 22:10:11.285 - [任务 25][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-20 22:10:11.287 - [任务 25][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-20 22:10:11.359 - [任务 25][Pg] - Batch read completed. 
[TRACE] 2025-02-20 22:10:11.370 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] running status set to false 
[TRACE] 2025-02-20 22:10:11.375 - [任务 25][Pg] - Incremental sync starting... 
[TRACE] 2025-02-20 22:10:11.376 - [任务 25][Pg] - Incremental sync completed 
[TRACE] 2025-02-20 22:10:11.385 - [任务 25][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_07c5b86c-af18-4cb6-845c-d800783f9b32_1740060609916 
[TRACE] 2025-02-20 22:10:11.387 - [任务 25][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_07c5b86c-af18-4cb6-845c-d800783f9b32_1740060609916 
[TRACE] 2025-02-20 22:10:11.387 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] schema data cleaned 
[TRACE] 2025-02-20 22:10:11.392 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] monitor closed 
[TRACE] 2025-02-20 22:10:11.392 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] close complete, cost 36 ms 
[TRACE] 2025-02-20 22:10:11.393 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] running status set to false 
[TRACE] 2025-02-20 22:10:11.393 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] schema data cleaned 
[TRACE] 2025-02-20 22:10:11.393 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] monitor closed 
[TRACE] 2025-02-20 22:10:11.393 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] close complete, cost 1 ms 
[TRACE] 2025-02-20 22:10:11.396 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] running status set to false 
[TRACE] 2025-02-20 22:10:11.516 - [任务 25][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_52faca30-d179-493d-b833-8e9026653bdb_1740060610063 
[TRACE] 2025-02-20 22:10:11.516 - [任务 25][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_52faca30-d179-493d-b833-8e9026653bdb_1740060610063 
[TRACE] 2025-02-20 22:10:11.516 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] schema data cleaned 
[TRACE] 2025-02-20 22:10:11.516 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] monitor closed 
[TRACE] 2025-02-20 22:10:11.516 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] close complete, cost 122 ms 
[TRACE] 2025-02-20 22:10:14.940 - [任务 25] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-20 22:10:14.940 - [任务 25] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7008c412 
[TRACE] 2025-02-20 22:10:15.072 - [任务 25] - Stop task milestones: 67b73607875cdc50c184f3b1(任务 25)  
[TRACE] 2025-02-20 22:10:15.089 - [任务 25] - Stopped task aspect(s) 
[TRACE] 2025-02-20 22:10:15.089 - [任务 25] - Snapshot order controller have been removed 
[INFO ] 2025-02-20 22:10:15.090 - [任务 25] - Task stopped. 
[TRACE] 2025-02-20 22:10:15.111 - [任务 25] - Remove memory task client succeed, task: 任务 25[67b73607875cdc50c184f3b1] 
[TRACE] 2025-02-20 22:10:15.112 - [任务 25] - Destroy memory task client cache succeed, task: 任务 25[67b73607875cdc50c184f3b1] 
[TRACE] 2025-02-20 22:10:24.249 - [任务 25] - Task initialization... 
[TRACE] 2025-02-20 22:10:24.452 - [任务 25] - Start task milestones: 67b73607875cdc50c184f3b1(任务 25) 
[INFO ] 2025-02-20 22:10:24.508 - [任务 25] - Loading table structure completed 
[TRACE] 2025-02-20 22:10:24.508 - [任务 25] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-20 22:10:24.556 - [任务 25] - The engine receives 任务 25 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-20 22:10:24.556 - [任务 25] - Task started 
[TRACE] 2025-02-20 22:10:24.582 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:10:24.582 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:10:24.582 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:10:24.582 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:10:24.582 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:10:24.582 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:10:24.582 - [任务 25][表编辑] - Node table_rename_processor(表编辑: 3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c) enable batch process 
[ERROR] 2025-02-20 22:10:25.314 - [任务 25][Sybase] - Sink connector(Sybase) initialization error: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Full Stack Trace -->
java.lang.RuntimeException: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	... 9 more
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:10:25.318 - [任务 25][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer 
[ERROR] 2025-02-20 22:10:25.321 - [任务 25][Sybase] - Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Error Message -->
Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer

<-- Simple Stack Trace -->
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	...

<-- Full Stack Trace -->
java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:10:25.329 - [任务 25][Sybase] - Job suspend in error handle 
[INFO ] 2025-02-20 22:10:25.932 - [任务 25][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-02-20 22:10:25.933 - [任务 25][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-02-20 22:10:25.933 - [任务 25][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-02-20 22:10:25.933 - [任务 25][Pg] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-20 22:10:26.536 - [任务 25][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-20 22:10:26.537 - [任务 25][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-20 22:10:26.636 - [任务 25][Pg] - Batch read completed. 
[TRACE] 2025-02-20 22:10:26.644 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] running status set to false 
[TRACE] 2025-02-20 22:10:26.654 - [任务 25][Pg] - Incremental sync starting... 
[TRACE] 2025-02-20 22:10:26.657 - [任务 25][Pg] - Incremental sync completed 
[TRACE] 2025-02-20 22:10:26.658 - [任务 25][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_07c5b86c-af18-4cb6-845c-d800783f9b32_1740060625342 
[TRACE] 2025-02-20 22:10:26.658 - [任务 25][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_07c5b86c-af18-4cb6-845c-d800783f9b32_1740060625342 
[TRACE] 2025-02-20 22:10:26.658 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] schema data cleaned 
[TRACE] 2025-02-20 22:10:26.659 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] monitor closed 
[TRACE] 2025-02-20 22:10:26.660 - [任务 25][Pg] - Node Pg[07c5b86c-af18-4cb6-845c-d800783f9b32] close complete, cost 36 ms 
[TRACE] 2025-02-20 22:10:26.661 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] running status set to false 
[TRACE] 2025-02-20 22:10:26.661 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] schema data cleaned 
[TRACE] 2025-02-20 22:10:26.661 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] monitor closed 
[TRACE] 2025-02-20 22:10:26.661 - [任务 25][表编辑] - Node 表编辑[3b3fadc0-01a6-4636-b4f8-107a8d3b2a2c] close complete, cost 0 ms 
[TRACE] 2025-02-20 22:10:26.661 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] running status set to false 
[TRACE] 2025-02-20 22:10:26.777 - [任务 25][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_52faca30-d179-493d-b833-8e9026653bdb_1740060625190 
[TRACE] 2025-02-20 22:10:26.778 - [任务 25][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_52faca30-d179-493d-b833-8e9026653bdb_1740060625190 
[TRACE] 2025-02-20 22:10:26.778 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] schema data cleaned 
[TRACE] 2025-02-20 22:10:26.778 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] monitor closed 
[TRACE] 2025-02-20 22:10:26.985 - [任务 25][Sybase] - Node Sybase[52faca30-d179-493d-b833-8e9026653bdb] close complete, cost 116 ms 
[TRACE] 2025-02-20 22:10:30.179 - [任务 25] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-20 22:10:30.181 - [任务 25] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@346d7432 
[TRACE] 2025-02-20 22:10:30.300 - [任务 25] - Stop task milestones: 67b73607875cdc50c184f3b1(任务 25)  
[TRACE] 2025-02-20 22:10:30.317 - [任务 25] - Stopped task aspect(s) 
[TRACE] 2025-02-20 22:10:30.317 - [任务 25] - Snapshot order controller have been removed 
[INFO ] 2025-02-20 22:10:30.340 - [任务 25] - Task stopped. 
[TRACE] 2025-02-20 22:10:30.342 - [任务 25] - Remove memory task client succeed, task: 任务 25[67b73607875cdc50c184f3b1] 
[TRACE] 2025-02-20 22:10:30.342 - [任务 25] - Destroy memory task client cache succeed, task: 任务 25[67b73607875cdc50c184f3b1] 
