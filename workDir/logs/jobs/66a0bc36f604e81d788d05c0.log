[INFO ] 2024-07-24 16:34:52.584 - [任务 25] - Task initialization... 
[INFO ] 2024-07-24 16:34:52.588 - [任务 25] - Start task milestones: 66a0bc36f604e81d788d05c0(任务 25) 
[INFO ] 2024-07-24 16:34:53.079 - [任务 25] - No<PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-24 16:34:53.080 - [任务 25] - The engine receives 任务 25 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 16:34:53.169 - [任务 25][MiddleMongo184] - Node MiddleMongo184[3a1e3dbe-8e69-4780-b40d-c3b056748578] start preload schema,table counts: 2 
[INFO ] 2024-07-24 16:34:53.170 - [任务 25][Mongo] - Node <PERSON>[5bcdc7cc-4daf-4431-a1a0-d44c0b7c1fc5] start preload schema,table counts: 2 
[INFO ] 2024-07-24 16:34:53.170 - [任务 25][MiddleMongo184] - Node MiddleMongo184[3a1e3dbe-8e69-4780-b40d-c3b056748578] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:34:53.375 - [任务 25][Mongo] - Node Mongo[5bcdc7cc-4daf-4431-a1a0-d44c0b7c1fc5] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 16:34:53.834 - [任务 25][MiddleMongo184] - Source node "MiddleMongo184" read batch size: 100 
[INFO ] 2024-07-24 16:34:53.834 - [任务 25][MiddleMongo184] - Source node "MiddleMongo184" event queue capacity: 200 
[INFO ] 2024-07-24 16:34:53.834 - [任务 25][MiddleMongo184] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-24 16:34:54.064 - [任务 25][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-24 16:34:54.064 - [任务 25][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-24 16:34:54.072 - [任务 25][MiddleMongo184] - batch offset found: {},stream offset found: {"cdcOffset":1721810094,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-24 16:34:54.202 - [任务 25][MiddleMongo184] - Initial sync started 
[INFO ] 2024-07-24 16:34:54.203 - [任务 25][MiddleMongo184] - Starting batch read, table name: Modules, offset: null 
[INFO ] 2024-07-24 16:34:54.203 - [任务 25][MiddleMongo184] - Table Modules is going to be initial synced 
[INFO ] 2024-07-24 16:34:54.364 - [任务 25][MiddleMongo184] - Query table 'Modules' counts: 19 
[INFO ] 2024-07-24 16:34:54.364 - [任务 25][MiddleMongo184] - Table [Modules] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 16:34:54.366 - [任务 25][MiddleMongo184] - Starting batch read, table name: ApiCall, offset: null 
[INFO ] 2024-07-24 16:34:54.366 - [任务 25][MiddleMongo184] - Table ApiCall is going to be initial synced 
[INFO ] 2024-07-24 16:34:54.544 - [任务 25][MiddleMongo184] - Query table 'ApiCall' counts: 868 
[INFO ] 2024-07-24 16:34:54.544 - [任务 25][MiddleMongo184] - Table [ApiCall] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 16:34:54.562 - [任务 25][MiddleMongo184] - Initial sync completed 
[INFO ] 2024-07-24 16:34:54.587 - [任务 25][MiddleMongo184] - Incremental sync starting... 
[INFO ] 2024-07-24 16:34:54.589 - [任务 25][MiddleMongo184] - Initial sync completed 
[INFO ] 2024-07-24 16:34:54.594 - [任务 25][MiddleMongo184] - Starting stream read, table list: [Modules, ApiCall], offset: {"cdcOffset":1721810094,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-24 16:34:54.805 - [任务 25][MiddleMongo184] - Connector MongoDB incremental start succeed, tables: [Modules, ApiCall], data change syncing 
[INFO ] 2024-07-24 16:41:51.437 - [任务 25][MiddleMongo184] - Node MiddleMongo184[3a1e3dbe-8e69-4780-b40d-c3b056748578] running status set to false 
[INFO ] 2024-07-24 16:41:51.453 - [任务 25][MiddleMongo184] - Incremental sync completed 
[INFO ] 2024-07-24 16:41:51.453 - [任务 25][MiddleMongo184] - PDK connector node stopped: HazelcastSourcePdkDataNode-3a1e3dbe-8e69-4780-b40d-c3b056748578 
[INFO ] 2024-07-24 16:41:51.453 - [任务 25][MiddleMongo184] - PDK connector node released: HazelcastSourcePdkDataNode-3a1e3dbe-8e69-4780-b40d-c3b056748578 
[INFO ] 2024-07-24 16:41:51.453 - [任务 25][MiddleMongo184] - Node MiddleMongo184[3a1e3dbe-8e69-4780-b40d-c3b056748578] schema data cleaned 
[INFO ] 2024-07-24 16:41:51.455 - [任务 25][MiddleMongo184] - Node MiddleMongo184[3a1e3dbe-8e69-4780-b40d-c3b056748578] monitor closed 
[INFO ] 2024-07-24 16:41:51.455 - [任务 25][MiddleMongo184] - Node MiddleMongo184[3a1e3dbe-8e69-4780-b40d-c3b056748578] close complete, cost 37 ms 
[INFO ] 2024-07-24 16:41:51.466 - [任务 25][Mongo] - Node Mongo[5bcdc7cc-4daf-4431-a1a0-d44c0b7c1fc5] running status set to false 
[INFO ] 2024-07-24 16:41:51.466 - [任务 25][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-5bcdc7cc-4daf-4431-a1a0-d44c0b7c1fc5 
[INFO ] 2024-07-24 16:41:51.468 - [任务 25][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-5bcdc7cc-4daf-4431-a1a0-d44c0b7c1fc5 
[INFO ] 2024-07-24 16:41:51.473 - [任务 25][Mongo] - Node Mongo[5bcdc7cc-4daf-4431-a1a0-d44c0b7c1fc5] schema data cleaned 
[INFO ] 2024-07-24 16:41:51.474 - [任务 25][Mongo] - Node Mongo[5bcdc7cc-4daf-4431-a1a0-d44c0b7c1fc5] monitor closed 
[INFO ] 2024-07-24 16:41:51.474 - [任务 25][Mongo] - Node Mongo[5bcdc7cc-4daf-4431-a1a0-d44c0b7c1fc5] close complete, cost 13 ms 
[INFO ] 2024-07-24 16:41:56.014 - [任务 25] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 16:41:56.014 - [任务 25] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4be1ed3d 
[INFO ] 2024-07-24 16:41:56.152 - [任务 25] - Stop task milestones: 66a0bc36f604e81d788d05c0(任务 25)  
[INFO ] 2024-07-24 16:41:56.153 - [任务 25] - Stopped task aspect(s) 
[INFO ] 2024-07-24 16:41:56.186 - [任务 25] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 16:41:56.186 - [任务 25] - Remove memory task client succeed, task: 任务 25[66a0bc36f604e81d788d05c0] 
[INFO ] 2024-07-24 16:41:56.390 - [任务 25] - Destroy memory task client cache succeed, task: 任务 25[66a0bc36f604e81d788d05c0] 
