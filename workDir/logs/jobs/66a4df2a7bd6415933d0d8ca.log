[INFO ] 2024-07-28 11:36:49.334 - [任务 3][BMSQL_ITEM] - Node BMSQL_ITEM[f8afef5d-9eb9-4c85-95b5-9307bc2ca563] running status set to false 
[INFO ] 2024-07-28 11:36:49.372 - [任务 3][BMSQL_ITEM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-28 11:36:49.372 - [任务 3][BMSQL_ITEM] - Mysql binlog reader stopped 
[INFO ] 2024-07-28 11:36:49.373 - [任务 3][BMSQL_ITEM] - Incremental sync completed 
[INFO ] 2024-07-28 11:36:49.402 - [任务 3][BMSQL_ITEM] - PDK connector node stopped: HazelcastSourcePdkDataNode-f8afef5d-9eb9-4c85-95b5-9307bc2ca563 
[INFO ] 2024-07-28 11:36:49.403 - [任务 3][BMSQL_ITEM] - PDK connector node released: HazelcastSourcePdkDataNode-f8afef5d-9eb9-4c85-95b5-9307bc2ca563 
[INFO ] 2024-07-28 11:36:49.403 - [任务 3][BMSQL_ITEM] - Node BMSQL_ITEM[f8afef5d-9eb9-4c85-95b5-9307bc2ca563] schema data cleaned 
[INFO ] 2024-07-28 11:36:49.403 - [任务 3][BMSQL_ITEM] - Node BMSQL_ITEM[f8afef5d-9eb9-4c85-95b5-9307bc2ca563] monitor closed 
[INFO ] 2024-07-28 11:36:49.404 - [任务 3][BMSQL_ITEM] - Node BMSQL_ITEM[f8afef5d-9eb9-4c85-95b5-9307bc2ca563] close complete, cost 101 ms 
[INFO ] 2024-07-28 11:36:49.404 - [任务 3][TestItem2] - Node TestItem2[2ae3c452-721c-4d7f-aacf-bee995d4e872] running status set to false 
[INFO ] 2024-07-28 11:36:49.417 - [任务 3][TestItem2] - PDK connector node stopped: HazelcastTargetPdkDataNode-2ae3c452-721c-4d7f-aacf-bee995d4e872 
[INFO ] 2024-07-28 11:36:49.417 - [任务 3][TestItem2] - PDK connector node released: HazelcastTargetPdkDataNode-2ae3c452-721c-4d7f-aacf-bee995d4e872 
[INFO ] 2024-07-28 11:36:49.420 - [任务 3][TestItem2] - Node TestItem2[2ae3c452-721c-4d7f-aacf-bee995d4e872] schema data cleaned 
[INFO ] 2024-07-28 11:36:49.420 - [任务 3][TestItem2] - Node TestItem2[2ae3c452-721c-4d7f-aacf-bee995d4e872] monitor closed 
[INFO ] 2024-07-28 11:36:49.622 - [任务 3][TestItem2] - Node TestItem2[2ae3c452-721c-4d7f-aacf-bee995d4e872] close complete, cost 16 ms 
[INFO ] 2024-07-28 11:36:58.501 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 11:36:58.501 - [任务 3] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@20376092 
[INFO ] 2024-07-28 11:36:58.625 - [任务 3] - Stop task milestones: 66a4df2a7bd6415933d0d8ca(任务 3)  
[INFO ] 2024-07-28 11:36:58.660 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-07-28 11:36:58.660 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-07-28 11:36:58.688 - [任务 3] - Remove memory task client succeed, task: 任务 3[66a4df2a7bd6415933d0d8ca] 
[INFO ] 2024-07-28 11:36:58.688 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[66a4df2a7bd6415933d0d8ca] 
