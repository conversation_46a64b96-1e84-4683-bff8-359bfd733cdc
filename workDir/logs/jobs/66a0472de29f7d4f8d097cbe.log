[INFO ] 2024-07-24 08:14:20.295 - [任务 21] - Task initialization... 
[INFO ] 2024-07-24 08:14:20.500 - [任务 21] - Start task milestones: 66a0472de29f7d4f8d097cbe(任务 21) 
[INFO ] 2024-07-24 08:14:20.534 - [任务 21] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 08:14:20.662 - [任务 21] - The engine receives 任务 21 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 08:14:20.662 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] start preload schema,table counts: 1 
[INFO ] 2024-07-24 08:14:20.662 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] start preload schema,table counts: 1 
[INFO ] 2024-07-24 08:14:20.662 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] preload schema finished, cost 1 ms 
[INFO ] 2024-07-24 08:14:20.663 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] preload schema finished, cost 1 ms 
[INFO ] 2024-07-24 08:14:21.529 - [任务 21][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-24 08:14:21.530 - [任务 21][dummy_test] - Source node "dummy_test" read batch size: 100 
[INFO ] 2024-07-24 08:14:21.530 - [任务 21][dummy_test] - Source node "dummy_test" event queue capacity: 200 
[INFO ] 2024-07-24 08:14:21.530 - [任务 21][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-24 08:14:21.530 - [任务 21][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721780061529,"lastTimes":1721780061529,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-24 08:14:21.599 - [任务 21][dummy_test] - Initial sync started 
[INFO ] 2024-07-24 08:14:21.601 - [任务 21][dummy_test] - Starting batch read, table name: dummy_test 
[INFO ] 2024-07-24 08:14:21.601 - [任务 21][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-07-24 08:14:21.610 - [任务 21][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-07-24 08:14:21.610 - [任务 21][dummy_test] - Query table 'dummy_test' counts: 50000000 
[WARN ] 2024-07-24 10:20:50.057 - [任务 21][TestDummy] - SQL Server 返回的响应不完整。连接已关闭。
 - Error record: io.tapdata.entity.event.dml.TapInsertRecordEvent@461ab41f: {"after":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"c2712f52-4ccc-47d6-a53b-b14c7200927b","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721781891092,"tableId":"dummy_test","time":1721781891092,"type":300}
 - Stack trace: com.microsoft.sqlserver.jdbc.SQLServerException: SQL Server 返回的响应不完整。连接已关闭。
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3411)
	at com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:6623)
	at com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:7802)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatementBatch(SQLServerPreparedStatement.java:2816)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtBatchExecCmd.doExecute(SQLServerPreparedStatement.java:2694)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3488)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:262)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:237)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2092)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.WriteRecorder.executeBatch(WriteRecorder.java:81)
	at io.tapdata.common.RecordWriter.write(RecordWriter.java:78)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:322)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$56(HazelcastTargetPdkDataNode.java:869)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:53)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:865)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$58(HazelcastTargetPdkDataNode.java:810)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:504)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:504)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:747)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:684)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:633)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$12(HazelcastTargetPdkBaseNode.java:602)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:518)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13(HazelcastTargetPdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:618)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:573)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 
[WARN ] 2024-07-24 10:20:50.058 - [任务 21][TestDummy] - SQL Server 返回的响应不完整。连接已关闭。
 - Error record: io.tapdata.entity.event.dml.TapInsertRecordEvent@50637c66: {"after":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"fee76fb8-bb96-4d6a-ac0b-6445d9d4b684","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721781891092,"tableId":"dummy_test","time":1721781891092,"type":300}
 - Stack trace: com.microsoft.sqlserver.jdbc.SQLServerException: SQL Server 返回的响应不完整。连接已关闭。
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3411)
	at com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:6623)
	at com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:7802)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatementBatch(SQLServerPreparedStatement.java:2816)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtBatchExecCmd.doExecute(SQLServerPreparedStatement.java:2694)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3488)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:262)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:237)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2092)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.WriteRecorder.executeBatch(WriteRecorder.java:81)
	at io.tapdata.common.RecordWriter.write(RecordWriter.java:78)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:322)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$56(HazelcastTargetPdkDataNode.java:869)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:53)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:865)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$58(HazelcastTargetPdkDataNode.java:810)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:504)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:504)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:747)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:684)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:633)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$12(HazelcastTargetPdkBaseNode.java:602)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:518)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13(HazelcastTargetPdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:618)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:573)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 
[WARN ] 2024-07-24 10:20:50.059 - [任务 21][TestDummy] - The remaining 97 write error record will not be printed on the interface, please go to tapdata-agent.log to check. 
[WARN ] 2024-07-24 10:20:50.060 - [任务 21][TestDummy] - SQL Server 返回的响应不完整。连接已关闭。
 - Error record: io.tapdata.entity.event.dml.TapInsertRecordEvent@35e866e2: {"after":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"533d41b2-432f-47a0-9180-ecd97a44300c","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721781891092,"tableId":"dummy_test","time":1721781891092,"type":300}
 - Stack trace: com.microsoft.sqlserver.jdbc.SQLServerException: SQL Server 返回的响应不完整。连接已关闭。
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3411)
	at com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:6623)
	at com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:7802)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatementBatch(SQLServerPreparedStatement.java:2816)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtBatchExecCmd.doExecute(SQLServerPreparedStatement.java:2694)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3488)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:262)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:237)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2092)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.WriteRecorder.executeBatch(WriteRecorder.java:81)
	at io.tapdata.common.RecordWriter.write(RecordWriter.java:78)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:322)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$56(HazelcastTargetPdkDataNode.java:869)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:53)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:865)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$58(HazelcastTargetPdkDataNode.java:810)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:504)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:504)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:747)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:684)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:633)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$12(HazelcastTargetPdkBaseNode.java:602)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:518)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13(HazelcastTargetPdkBaseNode.java:574)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:618)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:573)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 
[INFO ] 2024-07-24 10:23:16.877 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] running status set to false 
[INFO ] 2024-07-24 10:23:16.883 - [任务 21][dummy_test] - Stop connector 
[INFO ] 2024-07-24 10:23:16.894 - [任务 21][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-07-24 10:23:16.897 - [任务 21][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 10:23:16.900 - [任务 21][dummy_test] - Initial sync completed 
[INFO ] 2024-07-24 10:23:16.900 - [任务 21][dummy_test] - Incremental sync starting... 
[INFO ] 2024-07-24 10:23:16.900 - [任务 21][dummy_test] - Incremental sync completed 
[INFO ] 2024-07-24 10:23:16.905 - [任务 21][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb 
[INFO ] 2024-07-24 10:23:16.906 - [任务 21][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb 
[INFO ] 2024-07-24 10:23:16.906 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] schema data cleaned 
[INFO ] 2024-07-24 10:23:16.909 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] monitor closed 
[INFO ] 2024-07-24 10:23:16.910 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] close complete, cost 52 ms 
[INFO ] 2024-07-24 10:23:17.116 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] running status set to false 
[INFO ] 2024-07-24 10:23:39.240 - [任务 21][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-b133d4eb-3caf-453c-96ee-145b01436237 
[INFO ] 2024-07-24 10:23:39.240 - [任务 21][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-b133d4eb-3caf-453c-96ee-145b01436237 
[INFO ] 2024-07-24 10:23:39.241 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] schema data cleaned 
[INFO ] 2024-07-24 10:23:39.241 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] monitor closed 
[INFO ] 2024-07-24 10:23:39.449 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] close complete, cost 22332 ms 
[INFO ] 2024-07-24 10:23:43.897 - [任务 21] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 10:23:43.900 - [任务 21] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@501aa47c 
[INFO ] 2024-07-24 10:23:44.030 - [任务 21] - Stop task milestones: 66a0472de29f7d4f8d097cbe(任务 21)  
[INFO ] 2024-07-24 10:23:44.030 - [任务 21] - Stopped task aspect(s) 
[INFO ] 2024-07-24 10:23:44.030 - [任务 21] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 10:23:44.045 - [任务 21] - Remove memory task client succeed, task: 任务 21[66a0472de29f7d4f8d097cbe] 
[INFO ] 2024-07-24 10:23:44.045 - [任务 21] - Destroy memory task client cache succeed, task: 任务 21[66a0472de29f7d4f8d097cbe] 
[INFO ] 2024-07-24 10:28:46.100 - [任务 21] - Task initialization... 
[INFO ] 2024-07-24 10:28:46.124 - [任务 21] - Start task milestones: 66a0472de29f7d4f8d097cbe(任务 21) 
[INFO ] 2024-07-24 10:28:46.266 - [任务 21] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 10:28:46.268 - [任务 21] - The engine receives 任务 21 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 10:28:46.352 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] start preload schema,table counts: 1 
[INFO ] 2024-07-24 10:28:46.354 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] start preload schema,table counts: 1 
[INFO ] 2024-07-24 10:28:46.380 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 10:28:46.380 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 10:28:47.325 - [任务 21][dummy_test] - Source node "dummy_test" read batch size: 100 
[INFO ] 2024-07-24 10:28:47.325 - [任务 21][dummy_test] - Source node "dummy_test" event queue capacity: 200 
[INFO ] 2024-07-24 10:28:47.325 - [任务 21][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-24 10:28:47.326 - [任务 21][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721788127321,"lastTimes":1721788127321,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-24 10:28:47.388 - [任务 21][dummy_test] - Initial sync started 
[INFO ] 2024-07-24 10:28:47.388 - [任务 21][dummy_test] - Starting batch read, table name: dummy_test 
[INFO ] 2024-07-24 10:28:47.390 - [任务 21][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-07-24 10:28:47.401 - [任务 21][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-07-24 10:28:47.401 - [任务 21][dummy_test] - Query table 'dummy_test' counts: 50000000 
[INFO ] 2024-07-24 10:28:47.804 - [任务 21][TestDummy] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-24 10:29:07.306 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] running status set to false 
[INFO ] 2024-07-24 10:29:07.319 - [任务 21][dummy_test] - Stop connector 
[INFO ] 2024-07-24 10:29:07.319 - [任务 21][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-07-24 10:29:07.322 - [任务 21][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 10:29:07.322 - [任务 21][dummy_test] - Initial sync completed 
[INFO ] 2024-07-24 10:29:07.322 - [任务 21][dummy_test] - Incremental sync starting... 
[INFO ] 2024-07-24 10:29:07.322 - [任务 21][dummy_test] - Incremental sync completed 
[INFO ] 2024-07-24 10:29:07.327 - [任务 21][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb 
[INFO ] 2024-07-24 10:29:07.327 - [任务 21][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb 
[INFO ] 2024-07-24 10:29:07.327 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] schema data cleaned 
[INFO ] 2024-07-24 10:29:07.328 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] monitor closed 
[INFO ] 2024-07-24 10:29:07.328 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] close complete, cost 28 ms 
[INFO ] 2024-07-24 10:29:07.328 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] running status set to false 
[INFO ] 2024-07-24 10:29:07.339 - [任务 21][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-b133d4eb-3caf-453c-96ee-145b01436237 
[INFO ] 2024-07-24 10:29:07.340 - [任务 21][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-b133d4eb-3caf-453c-96ee-145b01436237 
[INFO ] 2024-07-24 10:29:07.340 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] schema data cleaned 
[INFO ] 2024-07-24 10:29:07.340 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] monitor closed 
[INFO ] 2024-07-24 10:29:07.340 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] close complete, cost 11 ms 
[INFO ] 2024-07-24 10:29:09.294 - [任务 21] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 10:29:09.420 - [任务 21] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5a7057ac 
[INFO ] 2024-07-24 10:29:09.422 - [任务 21] - Stop task milestones: 66a0472de29f7d4f8d097cbe(任务 21)  
[INFO ] 2024-07-24 10:29:09.430 - [任务 21] - Stopped task aspect(s) 
[INFO ] 2024-07-24 10:29:09.430 - [任务 21] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 10:29:09.451 - [任务 21] - Remove memory task client succeed, task: 任务 21[66a0472de29f7d4f8d097cbe] 
[INFO ] 2024-07-24 10:29:09.454 - [任务 21] - Destroy memory task client cache succeed, task: 任务 21[66a0472de29f7d4f8d097cbe] 
[INFO ] 2024-07-24 10:29:31.544 - [任务 21] - Task initialization... 
[INFO ] 2024-07-24 10:29:31.746 - [任务 21] - Start task milestones: 66a0472de29f7d4f8d097cbe(任务 21) 
[INFO ] 2024-07-24 10:29:31.746 - [任务 21] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 10:29:31.778 - [任务 21] - The engine receives 任务 21 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 10:29:31.821 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] start preload schema,table counts: 1 
[INFO ] 2024-07-24 10:29:31.821 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] start preload schema,table counts: 1 
[INFO ] 2024-07-24 10:29:31.821 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 10:29:31.821 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] preload schema finished, cost 1 ms 
[INFO ] 2024-07-24 10:29:32.579 - [任务 21][dummy_test] - Source node "dummy_test" read batch size: 100 
[INFO ] 2024-07-24 10:29:32.581 - [任务 21][dummy_test] - Source node "dummy_test" event queue capacity: 200 
[INFO ] 2024-07-24 10:29:32.590 - [任务 21][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-24 10:29:32.594 - [任务 21][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721788172579,"lastTimes":1721788172579,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-24 10:29:32.620 - [任务 21][dummy_test] - Initial sync started 
[INFO ] 2024-07-24 10:29:32.622 - [任务 21][dummy_test] - Starting batch read, table name: dummy_test 
[INFO ] 2024-07-24 10:29:32.623 - [任务 21][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-07-24 10:29:32.626 - [任务 21][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-07-24 10:29:32.827 - [任务 21][dummy_test] - Query table 'dummy_test' counts: 50000000 
[INFO ] 2024-07-24 10:29:33.436 - [任务 21][TestDummy] - Write batch size: 5000, max wait ms per batch: 500 
[INFO ] 2024-07-24 10:30:02.564 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] running status set to false 
[INFO ] 2024-07-24 10:30:02.565 - [任务 21][dummy_test] - Stop connector 
[INFO ] 2024-07-24 10:30:02.570 - [任务 21][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-07-24 10:30:02.571 - [任务 21][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 10:30:02.575 - [任务 21][dummy_test] - Initial sync completed 
[INFO ] 2024-07-24 10:30:02.575 - [任务 21][dummy_test] - Incremental sync starting... 
[INFO ] 2024-07-24 10:30:02.575 - [任务 21][dummy_test] - Incremental sync completed 
[INFO ] 2024-07-24 10:30:02.581 - [任务 21][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb 
[INFO ] 2024-07-24 10:30:02.581 - [任务 21][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb 
[INFO ] 2024-07-24 10:30:02.581 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] schema data cleaned 
[INFO ] 2024-07-24 10:30:02.582 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] monitor closed 
[INFO ] 2024-07-24 10:30:02.583 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] close complete, cost 35 ms 
[INFO ] 2024-07-24 10:30:02.583 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] running status set to false 
[INFO ] 2024-07-24 10:30:02.612 - [任务 21][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-b133d4eb-3caf-453c-96ee-145b01436237 
[INFO ] 2024-07-24 10:30:02.612 - [任务 21][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-b133d4eb-3caf-453c-96ee-145b01436237 
[INFO ] 2024-07-24 10:30:02.613 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] schema data cleaned 
[INFO ] 2024-07-24 10:30:02.613 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] monitor closed 
[INFO ] 2024-07-24 10:30:02.642 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] close complete, cost 36 ms 
[WARN ] 2024-07-24 10:30:02.648 - [任务 21][TestDummy] - Socket closed
 - Error record: io.tapdata.entity.event.dml.TapInsertRecordEvent@11070105: {"after":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"57960b23-21cc-4a19-a077-84daead406d6","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721788190690,"tableId":"dummy_test","time":1721788190690,"type":300}
 - Stack trace: com.microsoft.sqlserver.jdbc.SQLServerException: Socket closed
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2066)
	at com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:6616)
	at com.microsoft.sqlserver.jdbc.TDSReader.nextPacket(IOBuffer.java:6566)
	at com.microsoft.sqlserver.jdbc.TDSReader.ensurePayload(IOBuffer.java:6539)
	at com.microsoft.sqlserver.jdbc.TDSReader.readBytes(IOBuffer.java:6850)
	at com.microsoft.sqlserver.jdbc.TDSReader.readWrappedBytes(IOBuffer.java:6871)
	at com.microsoft.sqlserver.jdbc.TDSReader.readShort(IOBuffer.java:6788)
	at com.microsoft.sqlserver.jdbc.StreamDone.setFromTDS(StreamDone.java:155)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement$1NextResult.onDone(SQLServerStatement.java:1472)
	at com.microsoft.sqlserver.jdbc.TDSParser.parse(tdsparser.java:96)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1658)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatementBatch(SQLServerPreparedStatement.java:2830)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtBatchExecCmd.doExecute(SQLServerPreparedStatement.java:2694)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3488)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:262)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:237)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2092)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.WriteRecorder.executeBatch(WriteRecorder.java:81)
	at io.tapdata.common.RecordWriter.write(RecordWriter.java:78)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:322)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$56(HazelcastTargetPdkDataNode.java:869)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:53)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:865)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$58(HazelcastTargetPdkDataNode.java:810)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:504)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:504)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:747)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:684)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:123)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Socket closed
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2058)
	... 48 more
 
[WARN ] 2024-07-24 10:30:02.653 - [任务 21][TestDummy] - Socket closed
 - Error record: io.tapdata.entity.event.dml.TapInsertRecordEvent@5accdac8: {"after":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"bacbb10c-e468-4f8a-8a76-c6d6e217cd45","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721788190686,"tableId":"dummy_test","time":1721788190686,"type":300}
 - Stack trace: com.microsoft.sqlserver.jdbc.SQLServerException: Socket closed
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2066)
	at com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:6616)
	at com.microsoft.sqlserver.jdbc.TDSReader.nextPacket(IOBuffer.java:6566)
	at com.microsoft.sqlserver.jdbc.TDSReader.ensurePayload(IOBuffer.java:6539)
	at com.microsoft.sqlserver.jdbc.TDSReader.readBytes(IOBuffer.java:6850)
	at com.microsoft.sqlserver.jdbc.TDSReader.readWrappedBytes(IOBuffer.java:6871)
	at com.microsoft.sqlserver.jdbc.TDSReader.readShort(IOBuffer.java:6788)
	at com.microsoft.sqlserver.jdbc.StreamDone.setFromTDS(StreamDone.java:155)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement$1NextResult.onDone(SQLServerStatement.java:1472)
	at com.microsoft.sqlserver.jdbc.TDSParser.parse(tdsparser.java:96)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1658)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatementBatch(SQLServerPreparedStatement.java:2830)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtBatchExecCmd.doExecute(SQLServerPreparedStatement.java:2694)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3488)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:262)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:237)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2092)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.WriteRecorder.executeBatch(WriteRecorder.java:81)
	at io.tapdata.common.RecordWriter.write(RecordWriter.java:78)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:322)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$56(HazelcastTargetPdkDataNode.java:869)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:53)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:865)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$58(HazelcastTargetPdkDataNode.java:810)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:504)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:504)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:747)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:684)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:123)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Socket closed
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2058)
	... 48 more
 
[WARN ] 2024-07-24 10:30:02.653 - [任务 21][TestDummy] - The remaining 995 write error record will not be printed on the interface, please go to tapdata-agent.log to check. 
[WARN ] 2024-07-24 10:30:02.653 - [任务 21][TestDummy] - Socket closed
 - Error record: io.tapdata.entity.event.dml.TapInsertRecordEvent@140e8406: {"after":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"4dfdfa67-5e12-4c36-81a2-db89c04bb29f","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721788192114,"tableId":"dummy_test","time":1721788192114,"type":300}
 - Stack trace: com.microsoft.sqlserver.jdbc.SQLServerException: Socket closed
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2066)
	at com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:6616)
	at com.microsoft.sqlserver.jdbc.TDSReader.nextPacket(IOBuffer.java:6566)
	at com.microsoft.sqlserver.jdbc.TDSReader.ensurePayload(IOBuffer.java:6539)
	at com.microsoft.sqlserver.jdbc.TDSReader.readBytes(IOBuffer.java:6850)
	at com.microsoft.sqlserver.jdbc.TDSReader.readWrappedBytes(IOBuffer.java:6871)
	at com.microsoft.sqlserver.jdbc.TDSReader.readShort(IOBuffer.java:6788)
	at com.microsoft.sqlserver.jdbc.StreamDone.setFromTDS(StreamDone.java:155)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement$1NextResult.onDone(SQLServerStatement.java:1472)
	at com.microsoft.sqlserver.jdbc.TDSParser.parse(tdsparser.java:96)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1658)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatementBatch(SQLServerPreparedStatement.java:2830)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtBatchExecCmd.doExecute(SQLServerPreparedStatement.java:2694)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3488)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:262)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:237)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2092)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.WriteRecorder.executeBatch(WriteRecorder.java:81)
	at io.tapdata.common.RecordWriter.write(RecordWriter.java:78)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:322)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$56(HazelcastTargetPdkDataNode.java:869)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:53)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:865)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$58(HazelcastTargetPdkDataNode.java:810)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:504)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:504)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:747)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:684)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:123)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Socket closed
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2058)
	... 48 more
 
[INFO ] 2024-07-24 10:30:02.832 - [任务 21][TestDummy] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: TestDummy
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@3127dfb0: {"after":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"9d6f1fc1-8db6-4d9f-bfe9-d9cf103abfd9","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721788192121,"tableId":"dummy_test","time":1721788192121,"type":300}, nodeIds=[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb], sourceTime=1721788172579, sourceSerialNo=null} 
[ERROR] 2024-07-24 10:30:02.834 - [任务 21][TestDummy] - target write record(s) failed <-- Full Stack Trace -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: TestDummy
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:172)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:123)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: TestDummy
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:692)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:166)
	... 7 more
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: TestDummy
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:504)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:504)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:747)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:684)
	... 9 more
Caused by: Execute PDK method: TARGET_WRITE_RECORD, tableName: TestDummy
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:878)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$58(HazelcastTargetPdkDataNode.java:810)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 16 more
Caused by: java.net.SocketException: Socket closed
	at io.tapdata.connector.mssql.exception.MssqlExceptionCollector.collectTerminateByServer(MssqlExceptionCollector.java:27)
	at io.tapdata.common.RecordWriter.write(RecordWriter.java:88)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:322)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$56(HazelcastTargetPdkDataNode.java:869)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:53)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:865)
	... 23 more
Caused by: java.net.SocketException: Socket closed
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2058)
	at com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:6616)
	at com.microsoft.sqlserver.jdbc.TDSReader.nextPacket(IOBuffer.java:6566)
	at com.microsoft.sqlserver.jdbc.TDSReader.ensurePayload(IOBuffer.java:6539)
	at com.microsoft.sqlserver.jdbc.TDSReader.readBytes(IOBuffer.java:6850)
	at com.microsoft.sqlserver.jdbc.TDSReader.readWrappedBytes(IOBuffer.java:6871)
	at com.microsoft.sqlserver.jdbc.TDSReader.readShort(IOBuffer.java:6788)
	at com.microsoft.sqlserver.jdbc.StreamDone.setFromTDS(StreamDone.java:155)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement$1NextResult.onDone(SQLServerStatement.java:1472)
	at com.microsoft.sqlserver.jdbc.TDSParser.parse(tdsparser.java:96)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1658)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatementBatch(SQLServerPreparedStatement.java:2830)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtBatchExecCmd.doExecute(SQLServerPreparedStatement.java:2694)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3488)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:262)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:237)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2092)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.WriteRecorder.executeBatch(WriteRecorder.java:81)
	at io.tapdata.common.RecordWriter.write(RecordWriter.java:78)
	... 27 more

[INFO ] 2024-07-24 10:30:04.550 - [任务 21] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 10:30:04.551 - [任务 21] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@13b1d32c 
[INFO ] 2024-07-24 10:30:04.683 - [任务 21] - Stop task milestones: 66a0472de29f7d4f8d097cbe(任务 21)  
[INFO ] 2024-07-24 10:30:04.703 - [任务 21] - Stopped task aspect(s) 
[INFO ] 2024-07-24 10:30:04.703 - [任务 21] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 10:30:04.722 - [任务 21] - Remove memory task client succeed, task: 任务 21[66a0472de29f7d4f8d097cbe] 
[INFO ] 2024-07-24 10:30:04.724 - [任务 21] - Destroy memory task client cache succeed, task: 任务 21[66a0472de29f7d4f8d097cbe] 
[INFO ] 2024-07-24 10:30:20.004 - [任务 21] - Task initialization... 
[INFO ] 2024-07-24 10:30:20.214 - [任务 21] - Start task milestones: 66a0472de29f7d4f8d097cbe(任务 21) 
[INFO ] 2024-07-24 10:30:20.251 - [任务 21] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 10:30:20.251 - [任务 21] - The engine receives 任务 21 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 10:30:20.297 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] start preload schema,table counts: 1 
[INFO ] 2024-07-24 10:30:20.297 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] start preload schema,table counts: 1 
[INFO ] 2024-07-24 10:30:20.297 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 10:30:20.297 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 10:30:20.943 - [任务 21][dummy_test] - Source node "dummy_test" read batch size: 100 
[INFO ] 2024-07-24 10:30:20.943 - [任务 21][dummy_test] - Source node "dummy_test" event queue capacity: 200 
[INFO ] 2024-07-24 10:30:20.943 - [任务 21][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-24 10:30:20.945 - [任务 21][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721788220942,"lastTimes":1721788220942,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-24 10:30:20.991 - [任务 21][dummy_test] - Initial sync started 
[INFO ] 2024-07-24 10:30:20.998 - [任务 21][dummy_test] - Starting batch read, table name: dummy_test 
[INFO ] 2024-07-24 10:30:20.998 - [任务 21][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-07-24 10:30:20.998 - [任务 21][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-07-24 10:30:21.201 - [任务 21][dummy_test] - Query table 'dummy_test' counts: 50000000 
[INFO ] 2024-07-24 10:30:21.362 - [任务 21][TestDummy] - Write batch size: 15000, max wait ms per batch: 500 
[INFO ] 2024-07-24 10:30:54.317 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] running status set to false 
[INFO ] 2024-07-24 10:30:54.329 - [任务 21][dummy_test] - Stop connector 
[INFO ] 2024-07-24 10:30:54.329 - [任务 21][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-07-24 10:30:54.329 - [任务 21][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 10:30:54.332 - [任务 21][dummy_test] - Initial sync completed 
[INFO ] 2024-07-24 10:30:54.332 - [任务 21][dummy_test] - Incremental sync starting... 
[INFO ] 2024-07-24 10:30:54.332 - [任务 21][dummy_test] - Incremental sync completed 
[INFO ] 2024-07-24 10:30:54.346 - [任务 21][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb 
[INFO ] 2024-07-24 10:30:54.346 - [任务 21][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb 
[INFO ] 2024-07-24 10:30:54.346 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] schema data cleaned 
[INFO ] 2024-07-24 10:30:54.346 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] monitor closed 
[INFO ] 2024-07-24 10:30:54.349 - [任务 21][dummy_test] - Node dummy_test[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb] close complete, cost 38 ms 
[INFO ] 2024-07-24 10:30:54.350 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] running status set to false 
[WARN ] 2024-07-24 10:30:54.445 - [任务 21][TestDummy] - Socket closed
 - Error record: io.tapdata.entity.event.dml.TapInsertRecordEvent@433b4338: {"after":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"90b3cef2-8417-4a25-bc60-8a21ec061801","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721788230678,"tableId":"dummy_test","time":1721788230678,"type":300}
 - Stack trace: com.microsoft.sqlserver.jdbc.SQLServerException: Socket closed
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2066)
	at com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:6616)
	at com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:7802)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatementBatch(SQLServerPreparedStatement.java:2816)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtBatchExecCmd.doExecute(SQLServerPreparedStatement.java:2694)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3488)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:262)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:237)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2092)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.WriteRecorder.executeBatch(WriteRecorder.java:81)
	at io.tapdata.common.RecordWriter.write(RecordWriter.java:78)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:322)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$56(HazelcastTargetPdkDataNode.java:869)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:53)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:865)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$58(HazelcastTargetPdkDataNode.java:810)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:504)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:504)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:747)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:684)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:123)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Socket closed
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2058)
	... 40 more
 
[INFO ] 2024-07-24 10:30:54.447 - [任务 21][TestDummy] - PDK connector node stopped: HazelcastTargetPdkDataNode-b133d4eb-3caf-453c-96ee-145b01436237 
[INFO ] 2024-07-24 10:30:54.447 - [任务 21][TestDummy] - PDK connector node released: HazelcastTargetPdkDataNode-b133d4eb-3caf-453c-96ee-145b01436237 
[INFO ] 2024-07-24 10:30:54.447 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] schema data cleaned 
[INFO ] 2024-07-24 10:30:54.447 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] monitor closed 
[INFO ] 2024-07-24 10:30:54.457 - [任务 21][TestDummy] - Node TestDummy[b133d4eb-3caf-453c-96ee-145b01436237] close complete, cost 98 ms 
[WARN ] 2024-07-24 10:30:54.458 - [任务 21][TestDummy] - Socket closed
 - Error record: io.tapdata.entity.event.dml.TapInsertRecordEvent@7f80df81: {"after":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"440dd0b9-863e-4ede-bdbe-94b541e1147b","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721788230671,"tableId":"dummy_test","time":1721788230671,"type":300}
 - Stack trace: com.microsoft.sqlserver.jdbc.SQLServerException: Socket closed
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2066)
	at com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:6616)
	at com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:7802)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatementBatch(SQLServerPreparedStatement.java:2816)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtBatchExecCmd.doExecute(SQLServerPreparedStatement.java:2694)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3488)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:262)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:237)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2092)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.WriteRecorder.executeBatch(WriteRecorder.java:81)
	at io.tapdata.common.WriteRecorder.addAndCheckCommit(WriteRecorder.java:97)
	at io.tapdata.common.RecordWriter.write(RecordWriter.java:63)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:322)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$56(HazelcastTargetPdkDataNode.java:869)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:53)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:865)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$58(HazelcastTargetPdkDataNode.java:810)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:504)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:504)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:747)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:684)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:123)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Socket closed
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2058)
	... 41 more
 
[WARN ] 2024-07-24 10:30:54.458 - [任务 21][TestDummy] - The remaining 997 write error record will not be printed on the interface, please go to tapdata-agent.log to check. 
[WARN ] 2024-07-24 10:30:54.458 - [任务 21][TestDummy] - The remaining 870 write error record will not be printed on the interface, please go to tapdata-agent.log to check. 
[WARN ] 2024-07-24 10:30:54.459 - [任务 21][TestDummy] - The remaining 997 write error record will not be printed on the interface, please go to tapdata-agent.log to check. 
[WARN ] 2024-07-24 10:30:54.459 - [任务 21][TestDummy] - Socket closed
 - Error record: io.tapdata.entity.event.dml.TapInsertRecordEvent@5cdbd4d9: {"after":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"0654c541-1ce9-47e5-8681-fdfb6120cb8c","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721788228059,"tableId":"dummy_test","time":1721788228059,"type":300}
 - Stack trace: com.microsoft.sqlserver.jdbc.SQLServerException: Socket closed
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.terminate(SQLServerConnection.java:3422)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2066)
	at com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:6616)
	at com.microsoft.sqlserver.jdbc.TDSReader.nextPacket(IOBuffer.java:6566)
	at com.microsoft.sqlserver.jdbc.TDSReader.ensurePayload(IOBuffer.java:6539)
	at com.microsoft.sqlserver.jdbc.TDSReader.readBytes(IOBuffer.java:6850)
	at com.microsoft.sqlserver.jdbc.TDSReader.readWrappedBytes(IOBuffer.java:6871)
	at com.microsoft.sqlserver.jdbc.TDSReader.readLong(IOBuffer.java:6844)
	at com.microsoft.sqlserver.jdbc.StreamDone.setFromTDS(StreamDone.java:156)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement$1NextResult.onDone(SQLServerStatement.java:1472)
	at com.microsoft.sqlserver.jdbc.TDSParser.parse(tdsparser.java:96)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1658)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatementBatch(SQLServerPreparedStatement.java:2830)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtBatchExecCmd.doExecute(SQLServerPreparedStatement.java:2694)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3488)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:262)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:237)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2092)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.WriteRecorder.executeBatch(WriteRecorder.java:81)
	at io.tapdata.common.RecordWriter.write(RecordWriter.java:78)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:322)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$56(HazelcastTargetPdkDataNode.java:869)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:53)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:865)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$58(HazelcastTargetPdkDataNode.java:810)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:504)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:504)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:747)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:684)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:123)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Socket closed
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2058)
	... 48 more
 
[INFO ] 2024-07-24 10:30:54.848 - [任务 21] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 10:30:54.850 - [任务 21] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@35df448c 
[INFO ] 2024-07-24 10:30:54.920 - [任务 21][TestDummy] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: TestDummy
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@272baace: {"after":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"f046260e-b6ea-45c9-8a9e-7dfbbaba8481","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721788230678,"tableId":"dummy_test","time":1721788230678,"type":300}, nodeIds=[e5e1fa1a-0500-4ed9-83ee-2a6e6e2a14bb], sourceTime=1721788220942, sourceSerialNo=null} 
[ERROR] 2024-07-24 10:30:54.973 - [任务 21][TestDummy] - target write record(s) failed <-- Full Stack Trace -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: TestDummy
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:172)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:123)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: TestDummy
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:692)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:166)
	... 7 more
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: TestDummy
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:504)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:504)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:747)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:684)
	... 9 more
Caused by: Execute PDK method: TARGET_WRITE_RECORD, tableName: TestDummy
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:878)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$58(HazelcastTargetPdkDataNode.java:810)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 16 more
Caused by: java.net.SocketException: Socket closed
	at io.tapdata.connector.mssql.exception.MssqlExceptionCollector.collectTerminateByServer(MssqlExceptionCollector.java:27)
	at io.tapdata.common.RecordWriter.write(RecordWriter.java:88)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:322)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$56(HazelcastTargetPdkDataNode.java:869)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:53)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:865)
	... 23 more
Caused by: java.net.SocketException: Socket closed
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2058)
	at com.microsoft.sqlserver.jdbc.TDSReader.readPacket(IOBuffer.java:6616)
	at com.microsoft.sqlserver.jdbc.TDSCommand.startResponse(IOBuffer.java:7802)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatementBatch(SQLServerPreparedStatement.java:2816)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtBatchExecCmd.doExecute(SQLServerPreparedStatement.java:2694)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7417)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:3488)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:262)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:237)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeBatch(SQLServerPreparedStatement.java:2092)
	at com.zaxxer.hikari.pool.ProxyStatement.executeBatch(ProxyStatement.java:127)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeBatch(HikariProxyPreparedStatement.java)
	at io.tapdata.common.WriteRecorder.executeBatch(WriteRecorder.java:81)
	at io.tapdata.common.RecordWriter.write(RecordWriter.java:78)
	... 27 more

[INFO ] 2024-07-24 10:30:54.973 - [任务 21] - Stop task milestones: 66a0472de29f7d4f8d097cbe(任务 21)  
[INFO ] 2024-07-24 10:30:54.985 - [任务 21] - Stopped task aspect(s) 
[INFO ] 2024-07-24 10:30:54.987 - [任务 21] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 10:30:55.004 - [任务 21] - Remove memory task client succeed, task: 任务 21[66a0472de29f7d4f8d097cbe] 
[INFO ] 2024-07-24 10:30:55.007 - [任务 21] - Destroy memory task client cache succeed, task: 任务 21[66a0472de29f7d4f8d097cbe] 
