[INFO ] 2024-07-29 04:57:49.607 - [TestCKtuple] - Task initialization... 
[INFO ] 2024-07-29 04:57:49.610 - [TestCKtuple] - Start task milestones: 66a6b09d66c385580759c17d(TestCKtuple) 
[INFO ] 2024-07-29 04:57:49.766 - [TestCKtuple] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-29 04:57:49.767 - [TestCKtuple] - The engine receives TestCKtuple task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-29 04:57:49.851 - [TestCKtuple][TTUPLE] - Node TTUPLE[87e86a01-dc90-4e63-8a87-1e414199f3ce] start preload schema,table counts: 1 
[INFO ] 2024-07-29 04:57:49.851 - [TestCKtuple][my_table_tuple] - Node my_table_tuple[be3dbac4-c13f-4380-b3a8-e9e31093c0d7] start preload schema,table counts: 1 
[INFO ] 2024-07-29 04:57:49.852 - [TestCKtuple][TTUPLE] - Node TTUPLE[87e86a01-dc90-4e63-8a87-1e414199f3ce] preload schema finished, cost 1 ms 
[INFO ] 2024-07-29 04:57:49.852 - [TestCKtuple][my_table_tuple] - Node my_table_tuple[be3dbac4-c13f-4380-b3a8-e9e31093c0d7] preload schema finished, cost 1 ms 
[INFO ] 2024-07-29 04:57:50.726 - [TestCKtuple][TTUPLE] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-29 04:57:50.726 - [TestCKtuple][my_table_tuple] - Source node "my_table_tuple" read batch size: 100 
[INFO ] 2024-07-29 04:57:50.726 - [TestCKtuple][my_table_tuple] - Source node "my_table_tuple" event queue capacity: 200 
[INFO ] 2024-07-29 04:57:50.726 - [TestCKtuple][my_table_tuple] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-07-29 04:57:50.727 - [TestCKtuple][my_table_tuple] - Pdk connector does not support timestamp to stream offset function, will stop task after snapshot: clickhouse-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-07-29 04:57:50.727 - [TestCKtuple][my_table_tuple] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-29 04:57:50.727 - [TestCKtuple][my_table_tuple] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-29 04:57:50.802 - [TestCKtuple][my_table_tuple] - Initial sync started 
[INFO ] 2024-07-29 04:57:50.803 - [TestCKtuple][my_table_tuple] - Starting batch read, table name: my_table_tuple, offset: null 
[INFO ] 2024-07-29 04:57:50.826 - [TestCKtuple][my_table_tuple] - Table my_table_tuple is going to be initial synced 
[INFO ] 2024-07-29 04:57:50.827 - [TestCKtuple][my_table_tuple] - Table [my_table_tuple] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-29 04:57:50.834 - [TestCKtuple][my_table_tuple] - Query table 'my_table_tuple' counts: 4 
[INFO ] 2024-07-29 04:57:50.837 - [TestCKtuple][my_table_tuple] - Initial sync completed 
[INFO ] 2024-07-29 04:57:50.837 - [TestCKtuple][my_table_tuple] - Incremental sync starting... 
[INFO ] 2024-07-29 04:57:50.837 - [TestCKtuple][my_table_tuple] - Initial sync completed 
[INFO ] 2024-07-29 04:57:50.847 - [TestCKtuple][my_table_tuple] - Incremental sync completed 
[INFO ] 2024-07-29 04:57:50.848 - [TestCKtuple][my_table_tuple] - Exception skipping - The current exception does not match the skip exception strategy, message: Starting stream read failed, errors: start point offset is null 
[ERROR] 2024-07-29 04:57:50.870 - [TestCKtuple][my_table_tuple] - Starting stream read failed, errors: start point offset is null <-- Error Message -->
Starting stream read failed, errors: start point offset is null

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.NodeException: Starting stream read failed, errors: start point offset is null
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.enterCDCStage(HazelcastSourcePdkDataNode.java:648)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:596)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	...

<-- Full Stack Trace -->
io.tapdata.exception.NodeException: Starting stream read failed, errors: start point offset is null
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:223)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Starting stream read failed, errors: start point offset is null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.enterCDCStage(HazelcastSourcePdkDataNode.java:648)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:596)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	... 6 more

[INFO ] 2024-07-29 04:57:50.871 - [TestCKtuple][my_table_tuple] - Job suspend in error handle 
[INFO ] 2024-07-29 04:57:51.351 - [TestCKtuple][my_table_tuple] - Node my_table_tuple[be3dbac4-c13f-4380-b3a8-e9e31093c0d7] running status set to false 
[INFO ] 2024-07-29 04:57:51.352 - [TestCKtuple][my_table_tuple] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-29 04:57:51.371 - [TestCKtuple][my_table_tuple] - Clickhouse Optimize Table end 
[INFO ] 2024-07-29 04:57:51.412 - [TestCKtuple][my_table_tuple] - PDK connector node stopped: HazelcastSourcePdkDataNode-be3dbac4-c13f-4380-b3a8-e9e31093c0d7 
[INFO ] 2024-07-29 04:57:51.412 - [TestCKtuple][my_table_tuple] - PDK connector node released: HazelcastSourcePdkDataNode-be3dbac4-c13f-4380-b3a8-e9e31093c0d7 
[INFO ] 2024-07-29 04:57:51.412 - [TestCKtuple][my_table_tuple] - Node my_table_tuple[be3dbac4-c13f-4380-b3a8-e9e31093c0d7] schema data cleaned 
[INFO ] 2024-07-29 04:57:51.413 - [TestCKtuple][my_table_tuple] - Node my_table_tuple[be3dbac4-c13f-4380-b3a8-e9e31093c0d7] monitor closed 
[INFO ] 2024-07-29 04:57:51.415 - [TestCKtuple][my_table_tuple] - Node my_table_tuple[be3dbac4-c13f-4380-b3a8-e9e31093c0d7] close complete, cost 76 ms 
[INFO ] 2024-07-29 04:57:51.415 - [TestCKtuple][TTUPLE] - Node TTUPLE[87e86a01-dc90-4e63-8a87-1e414199f3ce] running status set to false 
[INFO ] 2024-07-29 04:57:51.433 - [TestCKtuple][TTUPLE] - PDK connector node stopped: HazelcastTargetPdkDataNode-87e86a01-dc90-4e63-8a87-1e414199f3ce 
[INFO ] 2024-07-29 04:57:51.433 - [TestCKtuple][TTUPLE] - PDK connector node released: HazelcastTargetPdkDataNode-87e86a01-dc90-4e63-8a87-1e414199f3ce 
[INFO ] 2024-07-29 04:57:51.433 - [TestCKtuple][TTUPLE] - Node TTUPLE[87e86a01-dc90-4e63-8a87-1e414199f3ce] schema data cleaned 
[INFO ] 2024-07-29 04:57:51.433 - [TestCKtuple][TTUPLE] - Node TTUPLE[87e86a01-dc90-4e63-8a87-1e414199f3ce] monitor closed 
[INFO ] 2024-07-29 04:57:51.634 - [TestCKtuple][TTUPLE] - Node TTUPLE[87e86a01-dc90-4e63-8a87-1e414199f3ce] close complete, cost 17 ms 
[INFO ] 2024-07-29 04:57:54.169 - [TestCKtuple] - Task [TestCKtuple] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-29 04:57:54.169 - [TestCKtuple] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-29 04:57:54.169 - [TestCKtuple] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7c89c42a 
[INFO ] 2024-07-29 04:57:54.285 - [TestCKtuple] - Stop task milestones: 66a6b09d66c385580759c17d(TestCKtuple)  
[INFO ] 2024-07-29 04:57:54.300 - [TestCKtuple] - Stopped task aspect(s) 
[INFO ] 2024-07-29 04:57:54.303 - [TestCKtuple] - Snapshot order controller have been removed 
[INFO ] 2024-07-29 04:57:54.327 - [TestCKtuple] - Remove memory task client succeed, task: TestCKtuple[66a6b09d66c385580759c17d] 
[INFO ] 2024-07-29 04:57:54.327 - [TestCKtuple] - Destroy memory task client cache succeed, task: TestCKtuple[66a6b09d66c385580759c17d] 
