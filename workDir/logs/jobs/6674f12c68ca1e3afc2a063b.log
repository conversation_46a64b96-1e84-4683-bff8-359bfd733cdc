[INFO ] 2024-06-21 11:30:06.128 - [任务 3] - Task initialization... 
[INFO ] 2024-06-21 11:30:06.167 - [任务 3] - Start task milestones: 6674f12c68ca1e3afc2a063b(任务 3) 
[INFO ] 2024-06-21 11:30:06.356 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-21 11:30:06.550 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-21 11:30:06.551 - [任务 3][ParentTable] - Node ParentTable[21121354-a47f-4123-987d-ad51988cea18] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:30:06.557 - [任务 3][ParentTable] - Node ParentTable[21121354-a47f-4123-987d-ad51988cea18] preload schema finished, cost 1 ms 
[INFO ] 2024-06-21 11:30:06.557 - [任务 3][copyParentTable] - Node copyParentTable[3722238e-6a9b-47b1-97c3-fadd68541daa] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:30:06.557 - [任务 3][copyParentTable] - Node copyParentTable[3722238e-6a9b-47b1-97c3-fadd68541daa] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:30:07.678 - [任务 3][ParentTable] - Source node "ParentTable" read batch size: 100 
[INFO ] 2024-06-21 11:30:07.679 - [任务 3][ParentTable] - Source node "ParentTable" event queue capacity: 200 
[INFO ] 2024-06-21 11:30:07.681 - [任务 3][ParentTable] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-21 11:30:07.755 - [任务 3][copyParentTable] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-21 11:30:07.755 - [任务 3][ParentTable] - batch offset found: {},stream offset found: {"currentStartLSN":"000036A10000A8280001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-06-21 11:30:07.833 - [任务 3][ParentTable] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-21 11:30:07.833 - [任务 3][ParentTable] - Initial sync started 
[INFO ] 2024-06-21 11:30:07.856 - [任务 3][ParentTable] - Starting batch read, table name: ParentTable, offset: null 
[INFO ] 2024-06-21 11:30:07.857 - [任务 3][ParentTable] - Table ParentTable is going to be initial synced 
[INFO ] 2024-06-21 11:30:08.009 - [任务 3][ParentTable] - Table [ParentTable] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-21 11:30:08.011 - [任务 3][ParentTable] - Query table 'ParentTable' counts: 3 
[INFO ] 2024-06-21 11:30:08.011 - [任务 3][ParentTable] - Initial sync completed 
[INFO ] 2024-06-21 11:30:08.011 - [任务 3][ParentTable] - Incremental sync starting... 
[INFO ] 2024-06-21 11:30:08.011 - [任务 3][ParentTable] - Initial sync completed 
[INFO ] 2024-06-21 11:30:08.019 - [任务 3][ParentTable] - Starting stream read, table list: [ParentTable], offset: {"currentStartLSN":"000036A10000A8280001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-06-21 11:30:08.441 - [任务 3][ParentTable] - opened cdc tables: [Category_test, Category, a_test, test001_dummy_test, dummy612, Product, TEST_LENGTH, table_name, IdentityServerIdentityResourceProperties, orderitem, Category1, testTable, Supplier, SupplierInfo, TEST_DDL_001, reimbursement_approval, _tapdata_heartbeat_table, my_table, MyTable, t_9_1_3, t_9_1_1, testdate, test8, test9, Category1_test, test7, orders] 
[INFO ] 2024-06-21 11:30:08.443 - [任务 3][ParentTable] - building CT table for table ParentTable 
[INFO ] 2024-06-21 11:30:08.837 - [任务 3][ParentTable] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-06-21 11:30:08.837 - [任务 3][ParentTable] - Connector SQL Server incremental start succeed, tables: [ParentTable], data change syncing 
[INFO ] 2024-06-21 14:19:19.687 - [任务 3] - Start task milestones: 6674f12c68ca1e3afc2a063b(任务 3) 
[INFO ] 2024-06-21 14:19:19.689 - [任务 3] - Task initialization... 
[INFO ] 2024-06-21 14:19:20.565 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-21 14:19:20.990 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-21 14:19:21.481 - [任务 3][copyParentTable] - Node copyParentTable[3722238e-6a9b-47b1-97c3-fadd68541daa] start preload schema,table counts: 1 
[INFO ] 2024-06-21 14:19:21.487 - [任务 3][copyParentTable] - Node copyParentTable[3722238e-6a9b-47b1-97c3-fadd68541daa] preload schema finished, cost 3 ms 
[INFO ] 2024-06-21 14:19:21.504 - [任务 3][ParentTable] - Node ParentTable[21121354-a47f-4123-987d-ad51988cea18] start preload schema,table counts: 1 
[INFO ] 2024-06-21 14:19:21.504 - [任务 3][ParentTable] - Node ParentTable[21121354-a47f-4123-987d-ad51988cea18] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 14:19:22.843 - [任务 3][ParentTable] - Source node "ParentTable" read batch size: 100 
[INFO ] 2024-06-21 14:19:22.858 - [任务 3][copyParentTable] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-21 14:19:22.859 - [任务 3][ParentTable] - Source node "ParentTable" event queue capacity: 200 
[INFO ] 2024-06-21 14:19:22.879 - [任务 3][ParentTable] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-21 14:19:22.898 - [任务 3][ParentTable] - batch offset found: {"ParentTable":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"currentStartLSN":"000036A10000A8280001","tablesOffset":{"ParentTable":"000036A10000A8280001"},"ddlOffset":"AAA2oQAAqCgAAQ=="} 
[INFO ] 2024-06-21 14:19:22.912 - [任务 3][ParentTable] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-21 14:19:22.973 - [任务 3][ParentTable] - Incremental sync starting... 
[INFO ] 2024-06-21 14:19:22.974 - [任务 3][ParentTable] - Initial sync completed 
[INFO ] 2024-06-21 14:19:22.980 - [任务 3][ParentTable] - Starting stream read, table list: [ParentTable], offset: {"currentStartLSN":"000036A10000A8280001","tablesOffset":{"ParentTable":"000036A10000A8280001"},"ddlOffset":"AAA2oQAAqCgAAQ=="} 
[INFO ] 2024-06-21 14:19:23.323 - [任务 3][ParentTable] - opened cdc tables: [Category_test, Category, ChildTable, a_test, test001_dummy_test, dummy612, Product, TEST_LENGTH, table_name, IdentityServerIdentityResourceProperties, orderitem, Category1, testTable, Supplier, SupplierInfo, TEST_DDL_001, reimbursement_approval, _tapdata_heartbeat_table, my_table, MyTable, t_9_1_3, ParentTable, t_9_1_1, testdate, test8, test9, Category1_test, test7, orders] 
[INFO ] 2024-06-21 14:19:23.455 - [任务 3][ParentTable] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-06-21 14:19:23.455 - [任务 3][ParentTable] - Connector SQL Server incremental start succeed, tables: [ParentTable], data change syncing 
[INFO ] 2024-06-21 15:03:26.656 - [任务 3] - Start task milestones: 6674f12c68ca1e3afc2a063b(任务 3) 
[INFO ] 2024-06-21 15:03:26.657 - [任务 3] - Task initialization... 
[INFO ] 2024-06-21 15:03:27.486 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-21 15:03:27.560 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-21 15:03:28.213 - [任务 3][ParentTable] - Node ParentTable[21121354-a47f-4123-987d-ad51988cea18] start preload schema,table counts: 1 
[INFO ] 2024-06-21 15:03:28.216 - [任务 3][copyParentTable] - Node copyParentTable[3722238e-6a9b-47b1-97c3-fadd68541daa] start preload schema,table counts: 1 
[INFO ] 2024-06-21 15:03:28.217 - [任务 3][ParentTable] - Node ParentTable[21121354-a47f-4123-987d-ad51988cea18] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 15:03:28.217 - [任务 3][copyParentTable] - Node copyParentTable[3722238e-6a9b-47b1-97c3-fadd68541daa] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 15:03:29.072 - [任务 3][copyParentTable] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-21 15:03:29.073 - [任务 3][ParentTable] - Source node "ParentTable" read batch size: 100 
[INFO ] 2024-06-21 15:03:29.088 - [任务 3][ParentTable] - Source node "ParentTable" event queue capacity: 200 
[INFO ] 2024-06-21 15:03:29.118 - [任务 3][ParentTable] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-21 15:03:29.141 - [任务 3][ParentTable] - batch offset found: {"ParentTable":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"currentStartLSN":"000036A10000A8280001","tablesOffset":{"ParentTable":"000036A10000A8280001"},"ddlOffset":"AAA2oQAAqCgAAQ=="} 
[INFO ] 2024-06-21 15:03:29.142 - [任务 3][ParentTable] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-21 15:03:29.236 - [任务 3][ParentTable] - Incremental sync starting... 
[INFO ] 2024-06-21 15:03:29.237 - [任务 3][ParentTable] - Initial sync completed 
[INFO ] 2024-06-21 15:03:29.246 - [任务 3][ParentTable] - Starting stream read, table list: [ParentTable], offset: {"currentStartLSN":"000036A10000A8280001","tablesOffset":{"ParentTable":"000036A10000A8280001"},"ddlOffset":"AAA2oQAAqCgAAQ=="} 
[INFO ] 2024-06-21 15:03:29.937 - [任务 3][ParentTable] - opened cdc tables: [Category_test, Category, ChildTable, a_test, test001_dummy_test, dummy612, Product, TEST_LENGTH, table_name, IdentityServerIdentityResourceProperties, orderitem, Category1, testTable, Supplier, SupplierInfo, TEST_DDL_001, reimbursement_approval, _tapdata_heartbeat_table, my_table, MyTable, t_9_1_3, ParentTable, t_9_1_1, testdate, test8, test9, Category1_test, test7, orders] 
[INFO ] 2024-06-21 15:03:30.089 - [任务 3][ParentTable] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-06-21 15:03:30.093 - [任务 3][ParentTable] - Connector SQL Server incremental start succeed, tables: [ParentTable], data change syncing 
[INFO ] 2024-06-21 17:09:49.426 - [任务 3] - Stop task milestones: 6674f12c68ca1e3afc2a063b(任务 3)  
[INFO ] 2024-06-21 17:09:49.633 - [任务 3][ParentTable] - Node ParentTable[21121354-a47f-4123-987d-ad51988cea18] running status set to false 
[INFO ] 2024-06-21 17:09:52.539 - [任务 3][ParentTable] - PDK connector node stopped: HazelcastSourcePdkDataNode-21121354-a47f-4123-987d-ad51988cea18 
[INFO ] 2024-06-21 17:09:52.542 - [任务 3][ParentTable] - PDK connector node released: HazelcastSourcePdkDataNode-21121354-a47f-4123-987d-ad51988cea18 
[INFO ] 2024-06-21 17:09:52.543 - [任务 3][ParentTable] - Node ParentTable[21121354-a47f-4123-987d-ad51988cea18] schema data cleaned 
[INFO ] 2024-06-21 17:09:52.543 - [任务 3][ParentTable] - Node ParentTable[21121354-a47f-4123-987d-ad51988cea18] monitor closed 
[INFO ] 2024-06-21 17:09:52.545 - [任务 3][ParentTable] - Node ParentTable[21121354-a47f-4123-987d-ad51988cea18] close complete, cost 3036 ms 
[INFO ] 2024-06-21 17:09:52.545 - [任务 3][copyParentTable] - Node copyParentTable[3722238e-6a9b-47b1-97c3-fadd68541daa] running status set to false 
[INFO ] 2024-06-21 17:09:52.552 - [任务 3][copyParentTable] - PDK connector node stopped: HazelcastTargetPdkDataNode-3722238e-6a9b-47b1-97c3-fadd68541daa 
[INFO ] 2024-06-21 17:09:52.553 - [任务 3][copyParentTable] - PDK connector node released: HazelcastTargetPdkDataNode-3722238e-6a9b-47b1-97c3-fadd68541daa 
[INFO ] 2024-06-21 17:09:52.554 - [任务 3][copyParentTable] - Node copyParentTable[3722238e-6a9b-47b1-97c3-fadd68541daa] schema data cleaned 
[INFO ] 2024-06-21 17:09:52.554 - [任务 3][copyParentTable] - Node copyParentTable[3722238e-6a9b-47b1-97c3-fadd68541daa] monitor closed 
[INFO ] 2024-06-21 17:09:52.762 - [任务 3][copyParentTable] - Node copyParentTable[3722238e-6a9b-47b1-97c3-fadd68541daa] close complete, cost 10 ms 
[INFO ] 2024-06-21 17:09:53.841 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-21 17:09:53.850 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-06-21 17:09:53.850 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-06-21 17:09:53.890 - [任务 3] - Remove memory task client succeed, task: 任务 3[6674f12c68ca1e3afc2a063b] 
[INFO ] 2024-06-21 17:09:53.890 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[6674f12c68ca1e3afc2a063b] 
