[INFO ] 2024-03-29 18:29:44.681 - [products_import_import_import_import_import_import] - Start task milestones: 660697bd57c8b774d7a9425c(products_import_import_import_import_import_import) 
[INFO ] 2024-03-29 18:29:44.682 - [products_import_import_import_import_import_import] - Task initialization... 
[INFO ] 2024-03-29 18:29:44.848 - [products_import_import_import_import_import_import] - Node performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-03-29 18:29:44.850 - [products_import_import_import_import_import_import] - The engine receives products_import_import_import_import_import_import task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 18:29:44.970 - [products_import_import_import_import_import_import][products] - Node products[e23b91f8-f4d0-4c70-8754-a02f8d9b2b71] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:29:44.974 - [products_import_import_import_import_import_import][merge] - Node merge[d877758a-6aae-464e-9e69-006883fd6680] start preload schema,table counts: 3 
[INFO ] 2024-03-29 18:29:44.974 - [products_import_import_import_import_import_import][Products] - Node Products[39acf1f7-ff8b-436f-9cd7-2ba27da834c4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:29:44.974 - [products_import_import_import_import_import_import][Rename Categories] - Node Rename Categories[0068e240-cda9-42b8-8727-9244d5475f43] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:29:44.974 - [products_import_import_import_import_import_import][Categories] - Node Categories[b38b81f6-ffd6-4c73-ac9e-10c6bbc92459] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:29:44.974 - [products_import_import_import_import_import_import][Rename Products] - Node Rename Products[37ebfc62-f9c2-4992-a038-158f73276a6d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:29:45.045 - [products_import_import_import_import_import_import][products] - Node products[e23b91f8-f4d0-4c70-8754-a02f8d9b2b71] preload schema finished, cost 62 ms 
[INFO ] 2024-03-29 18:29:45.046 - [products_import_import_import_import_import_import][Rename Products] - Node Rename Products[37ebfc62-f9c2-4992-a038-158f73276a6d] preload schema finished, cost 54 ms 
[INFO ] 2024-03-29 18:29:45.046 - [products_import_import_import_import_import_import][Products] - Node Products[39acf1f7-ff8b-436f-9cd7-2ba27da834c4] preload schema finished, cost 57 ms 
[INFO ] 2024-03-29 18:29:45.046 - [products_import_import_import_import_import_import][Categories] - Node Categories[b38b81f6-ffd6-4c73-ac9e-10c6bbc92459] preload schema finished, cost 54 ms 
[INFO ] 2024-03-29 18:29:45.046 - [products_import_import_import_import_import_import][Rename Categories] - Node Rename Categories[0068e240-cda9-42b8-8727-9244d5475f43] preload schema finished, cost 54 ms 
[INFO ] 2024-03-29 18:29:45.079 - [products_import_import_import_import_import_import][merge] - Node merge[d877758a-6aae-464e-9e69-006883fd6680] preload schema finished, cost 115 ms 
[INFO ] 2024-03-29 18:29:45.082 - [products_import_import_import_import_import_import][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 18:29:45.082 - [products_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  Products(37ebfc62-f9c2-4992-a038-158f73276a6d)
    ->Categories(0068e240-cda9-42b8-8727-9244d5475f43)
} 
[INFO ] 2024-03-29 18:29:45.374 - [products_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Categories_0068e240-cda9-42b8-8727-9244d5475f43__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:29:45.376 - [products_import_import_import_import_import_import][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 18:29:45.376 - [products_import_import_import_import_import_import][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 18:29:46.190 - [products_import_import_import_import_import_import][products] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 18:29:46.190 - [products_import_import_import_import_import_import][Products] - Source node "Products" read batch size: 500 
[INFO ] 2024-03-29 18:29:46.190 - [products_import_import_import_import_import_import][Products] - Source node "Products" event queue capacity: 1000 
[INFO ] 2024-03-29 18:29:46.190 - [products_import_import_import_import_import_import][Products] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:29:46.254 - [products_import_import_import_import_import_import][Products] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:29:46.254 - [products_import_import_import_import_import_import][Products] - Initial sync started 
[INFO ] 2024-03-29 18:29:46.254 - [products_import_import_import_import_import_import][Products] - Starting batch read, table name: Products, offset: null 
[INFO ] 2024-03-29 18:29:46.261 - [products_import_import_import_import_import_import][Products] - Table Products is going to be initial synced 
[INFO ] 2024-03-29 18:29:46.304 - [products_import_import_import_import_import_import][Products] - Query table 'Products' counts: 1 
[INFO ] 2024-03-29 18:29:46.305 - [products_import_import_import_import_import_import][Products] - Initial sync completed 
[INFO ] 2024-03-29 18:29:46.382 - [products_import_import_import_import_import_import][Categories] - Source node "Categories" read batch size: 500 
[INFO ] 2024-03-29 18:29:46.382 - [products_import_import_import_import_import_import][Categories] - Source node "Categories" event queue capacity: 1000 
[INFO ] 2024-03-29 18:29:46.382 - [products_import_import_import_import_import_import][Categories] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:29:46.458 - [products_import_import_import_import_import_import][Categories] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:29:46.459 - [products_import_import_import_import_import_import] - Node[Categories] is waiting for running 
[INFO ] 2024-03-29 18:29:47.564 - [products_import_import_import_import_import_import] - Node[Products] finish, notify next layer to run 
[INFO ] 2024-03-29 18:29:47.568 - [products_import_import_import_import_import_import][Categories] - Initial sync started 
[INFO ] 2024-03-29 18:29:47.568 - [products_import_import_import_import_import_import] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 18:29:47.569 - [products_import_import_import_import_import_import][Categories] - Starting batch read, table name: Categories, offset: null 
[INFO ] 2024-03-29 18:29:47.660 - [products_import_import_import_import_import_import][Categories] - Table Categories is going to be initial synced 
[INFO ] 2024-03-29 18:29:47.660 - [products_import_import_import_import_import_import][Categories] - Query table 'Categories' counts: 1 
[INFO ] 2024-03-29 18:29:47.864 - [products_import_import_import_import_import_import][Categories] - Initial sync completed 
[INFO ] 2024-03-29 18:29:48.313 - [products_import_import_import_import_import_import][Products] - Incremental sync starting... 
[INFO ] 2024-03-29 18:29:48.314 - [products_import_import_import_import_import_import][Products] - Initial sync completed 
[INFO ] 2024-03-29 18:29:48.314 - [products_import_import_import_import_import_import][Products] - Starting stream read, table list: [Products], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:29:48.344 - [products_import_import_import_import_import_import][Products] - Starting mysql cdc, server name: 819c21b7-0bc9-4091-8a1f-f8ed6e1669c1 
[INFO ] 2024-03-29 18:29:48.348 - [products_import_import_import_import_import_import][Products] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 212238757
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 819c21b7-0bc9-4091-8a1f-f8ed6e1669c1
  database.port: 3307
  threadName: Debezium-Mysql-Connector-819c21b7-0bc9-4091-8a1f-f8ed6e1669c1
  database.hostname: 127.0.0.1
  database.password: ********
  name: 819c21b7-0bc9-4091-8a1f-f8ed6e1669c1
  pdk.offset.string: {"name":"819c21b7-0bc9-4091-8a1f-f8ed6e1669c1","offset":{"{\"server\":\"819c21b7-0bc9-4091-8a1f-f8ed6e1669c1\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Products
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:29:48.552 - [products_import_import_import_import_import_import][Products] - Connector Mysql incremental start succeed, tables: [Products], data change syncing 
[INFO ] 2024-03-29 18:29:48.667 - [products_import_import_import_import_import_import][Categories] - Incremental sync starting... 
[INFO ] 2024-03-29 18:29:48.667 - [products_import_import_import_import_import_import][Categories] - Initial sync completed 
[INFO ] 2024-03-29 18:29:48.691 - [products_import_import_import_import_import_import][Categories] - Starting stream read, table list: [Categories], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:29:48.695 - [products_import_import_import_import_import_import][Categories] - Starting mysql cdc, server name: faa6d20e-4270-4feb-9a8c-7096fe460b1e 
[INFO ] 2024-03-29 18:29:48.695 - [products_import_import_import_import_import_import][Categories] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 209191790
  time.precision.mode: adaptive_time_microseconds
  database.server.name: faa6d20e-4270-4feb-9a8c-7096fe460b1e
  database.port: 3307
  threadName: Debezium-Mysql-Connector-faa6d20e-4270-4feb-9a8c-7096fe460b1e
  database.hostname: 127.0.0.1
  database.password: ********
  name: faa6d20e-4270-4feb-9a8c-7096fe460b1e
  pdk.offset.string: {"name":"faa6d20e-4270-4feb-9a8c-7096fe460b1e","offset":{"{\"server\":\"faa6d20e-4270-4feb-9a8c-7096fe460b1e\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Categories
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:29:48.900 - [products_import_import_import_import_import_import][Categories] - Connector Mysql incremental start succeed, tables: [Categories], data change syncing 
[INFO ] 2024-03-29 18:36:29.689 - [products_import_import_import_import_import_import] - Stop task milestones: 660697bd57c8b774d7a9425c(products_import_import_import_import_import_import)  
[INFO ] 2024-03-29 18:36:29.691 - [products_import_import_import_import_import_import][Products] - Node Products[39acf1f7-ff8b-436f-9cd7-2ba27da834c4] running status set to false 
[INFO ] 2024-03-29 18:36:29.771 - [products_import_import_import_import_import_import][Products] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:36:29.771 - [products_import_import_import_import_import_import][Products] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:36:29.774 - [products_import_import_import_import_import_import][Products] - PDK connector node stopped: HazelcastSourcePdkDataNode-39acf1f7-ff8b-436f-9cd7-2ba27da834c4 
[INFO ] 2024-03-29 18:36:29.774 - [products_import_import_import_import_import_import][Products] - PDK connector node released: HazelcastSourcePdkDataNode-39acf1f7-ff8b-436f-9cd7-2ba27da834c4 
[INFO ] 2024-03-29 18:36:29.774 - [products_import_import_import_import_import_import][Products] - Node Products[39acf1f7-ff8b-436f-9cd7-2ba27da834c4] schema data cleaned 
[INFO ] 2024-03-29 18:36:29.776 - [products_import_import_import_import_import_import][Products] - Node Products[39acf1f7-ff8b-436f-9cd7-2ba27da834c4] monitor closed 
[INFO ] 2024-03-29 18:36:29.776 - [products_import_import_import_import_import_import][Products] - Node Products[39acf1f7-ff8b-436f-9cd7-2ba27da834c4] close complete, cost 90 ms 
[INFO ] 2024-03-29 18:36:29.778 - [products_import_import_import_import_import_import][Rename Products] - Node Rename Products[37ebfc62-f9c2-4992-a038-158f73276a6d] running status set to false 
[INFO ] 2024-03-29 18:36:29.915 - [products_import_import_import_import_import_import][Rename Products] - Node Rename Products[37ebfc62-f9c2-4992-a038-158f73276a6d] schema data cleaned 
[INFO ] 2024-03-29 18:36:29.915 - [products_import_import_import_import_import_import][Rename Products] - Node Rename Products[37ebfc62-f9c2-4992-a038-158f73276a6d] monitor closed 
[INFO ] 2024-03-29 18:36:29.916 - [products_import_import_import_import_import_import][Rename Products] - Node Rename Products[37ebfc62-f9c2-4992-a038-158f73276a6d] close complete, cost 138 ms 
[INFO ] 2024-03-29 18:36:29.917 - [products_import_import_import_import_import_import][Categories] - Node Categories[b38b81f6-ffd6-4c73-ac9e-10c6bbc92459] running status set to false 
[INFO ] 2024-03-29 18:36:30.011 - [products_import_import_import_import_import_import][Categories] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:36:30.013 - [products_import_import_import_import_import_import][Categories] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:36:30.013 - [products_import_import_import_import_import_import][Categories] - PDK connector node stopped: HazelcastSourcePdkDataNode-b38b81f6-ffd6-4c73-ac9e-10c6bbc92459 
[INFO ] 2024-03-29 18:36:30.013 - [products_import_import_import_import_import_import][Categories] - PDK connector node released: HazelcastSourcePdkDataNode-b38b81f6-ffd6-4c73-ac9e-10c6bbc92459 
[INFO ] 2024-03-29 18:36:30.013 - [products_import_import_import_import_import_import][Categories] - Node Categories[b38b81f6-ffd6-4c73-ac9e-10c6bbc92459] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.013 - [products_import_import_import_import_import_import][Categories] - Node Categories[b38b81f6-ffd6-4c73-ac9e-10c6bbc92459] monitor closed 
[INFO ] 2024-03-29 18:36:30.013 - [products_import_import_import_import_import_import][Categories] - Node Categories[b38b81f6-ffd6-4c73-ac9e-10c6bbc92459] close complete, cost 96 ms 
[INFO ] 2024-03-29 18:36:30.013 - [products_import_import_import_import_import_import][Rename Categories] - Node Rename Categories[0068e240-cda9-42b8-8727-9244d5475f43] running status set to false 
[INFO ] 2024-03-29 18:36:30.101 - [products_import_import_import_import_import_import][Rename Categories] - Node Rename Categories[0068e240-cda9-42b8-8727-9244d5475f43] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.102 - [products_import_import_import_import_import_import][Rename Categories] - Node Rename Categories[0068e240-cda9-42b8-8727-9244d5475f43] monitor closed 
[INFO ] 2024-03-29 18:36:30.102 - [products_import_import_import_import_import_import][Rename Categories] - Node Rename Categories[0068e240-cda9-42b8-8727-9244d5475f43] close complete, cost 88 ms 
[INFO ] 2024-03-29 18:36:30.102 - [products_import_import_import_import_import_import][merge] - Node merge[d877758a-6aae-464e-9e69-006883fd6680] running status set to false 
[INFO ] 2024-03-29 18:36:30.102 - [products_import_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Categories_0068e240-cda9-42b8-8727-9244d5475f43__TPORIG 
[INFO ] 2024-03-29 18:36:30.113 - [products_import_import_import_import_import_import][merge] - Node merge[d877758a-6aae-464e-9e69-006883fd6680] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.113 - [products_import_import_import_import_import_import][merge] - Node merge[d877758a-6aae-464e-9e69-006883fd6680] monitor closed 
[INFO ] 2024-03-29 18:36:30.113 - [products_import_import_import_import_import_import][merge] - Node merge[d877758a-6aae-464e-9e69-006883fd6680] close complete, cost 10 ms 
[INFO ] 2024-03-29 18:36:30.113 - [products_import_import_import_import_import_import][products] - Node products[e23b91f8-f4d0-4c70-8754-a02f8d9b2b71] running status set to false 
[INFO ] 2024-03-29 18:36:30.170 - [products_import_import_import_import_import_import][products] - PDK connector node stopped: HazelcastTargetPdkDataNode-e23b91f8-f4d0-4c70-8754-a02f8d9b2b71 
[INFO ] 2024-03-29 18:36:30.170 - [products_import_import_import_import_import_import][products] - PDK connector node released: HazelcastTargetPdkDataNode-e23b91f8-f4d0-4c70-8754-a02f8d9b2b71 
[INFO ] 2024-03-29 18:36:30.171 - [products_import_import_import_import_import_import][products] - Node products[e23b91f8-f4d0-4c70-8754-a02f8d9b2b71] schema data cleaned 
[INFO ] 2024-03-29 18:36:30.171 - [products_import_import_import_import_import_import][products] - Node products[e23b91f8-f4d0-4c70-8754-a02f8d9b2b71] monitor closed 
[INFO ] 2024-03-29 18:36:30.172 - [products_import_import_import_import_import_import][products] - Node products[e23b91f8-f4d0-4c70-8754-a02f8d9b2b71] close complete, cost 58 ms 
[INFO ] 2024-03-29 18:36:33.840 - [products_import_import_import_import_import_import] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-29 18:36:33.840 - [products_import_import_import_import_import_import] - Stopped task aspect(s) 
[INFO ] 2024-03-29 18:36:33.840 - [products_import_import_import_import_import_import] - Snapshot order controller have been removed 
[INFO ] 2024-03-29 18:36:33.858 - [products_import_import_import_import_import_import] - Remove memory task client succeed, task: products_import_import_import_import_import_import[660697bd57c8b774d7a9425c] 
[INFO ] 2024-03-29 18:36:33.862 - [products_import_import_import_import_import_import] - Destroy memory task client cache succeed, task: products_import_import_import_import_import_import[660697bd57c8b774d7a9425c] 
