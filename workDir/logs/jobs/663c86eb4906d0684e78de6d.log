[INFO ] 2024-05-11 18:23:59.557 - [任务 4] - Start task milestones: 663c86eb4906d0684e78de6d(任务 4) 
[INFO ] 2024-05-11 18:23:59.575 - [任务 4] - Task initialization... 
[INFO ] 2024-05-11 18:23:59.576 - [任务 4] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-11 18:23:59.663 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-11 18:23:59.994 - [任务 4][TEST2] - Node TEST2[4a30701c-81bb-4e2b-8422-b805a86713f3] start preload schema,table counts: 1 
[INFO ] 2024-05-11 18:23:59.994 - [任务 4][TEST2] - Node TEST2[2be22209-36c0-4ed2-94c8-a770dd910794] start preload schema,table counts: 1 
[INFO ] 2024-05-11 18:24:00.188 - [任务 4][TEST2] - Node TEST2[4a30701c-81bb-4e2b-8422-b805a86713f3] preload schema finished, cost 177 ms 
[INFO ] 2024-05-11 18:24:00.194 - [任务 4][TEST2] - Node TEST2[2be22209-36c0-4ed2-94c8-a770dd910794] preload schema finished, cost 177 ms 
[INFO ] 2024-05-11 18:24:01.195 - [任务 4][TEST2] - Source node "TEST2" read batch size: 100 
[INFO ] 2024-05-11 18:24:01.196 - [任务 4][TEST2] - Source node "TEST2" event queue capacity: 200 
[INFO ] 2024-05-11 18:24:01.197 - [任务 4][TEST2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-11 18:24:01.363 - [任务 4][TEST2] - batch offset found: {},stream offset found: {"cdcOffset":1715423041,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-05-11 18:24:01.535 - [任务 4][TEST2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-11 18:24:01.619 - [任务 4][TEST2] - Initial sync started 
[INFO ] 2024-05-11 18:24:01.636 - [任务 4][TEST2] - Starting batch read, table name: TEST2, offset: null 
[INFO ] 2024-05-11 18:24:01.636 - [任务 4][TEST2] - Table TEST2 is going to be initial synced 
[INFO ] 2024-05-11 18:24:01.847 - [任务 4][TEST2] - Query table 'TEST2' counts: 1076 
[INFO ] 2024-05-11 18:24:01.995 - [任务 4][TEST2] - Table [TEST2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-11 18:24:02.003 - [任务 4][TEST2] - Initial sync completed 
[INFO ] 2024-05-11 18:24:02.007 - [任务 4][TEST2] - Incremental sync starting... 
[INFO ] 2024-05-11 18:24:02.009 - [任务 4][TEST2] - Initial sync completed 
[INFO ] 2024-05-11 18:24:02.009 - [任务 4][TEST2] - Starting stream read, table list: [TEST2], offset: {"cdcOffset":1715423041,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-05-11 18:24:02.042 - [任务 4][TEST2] - Connector MongoDB incremental start succeed, tables: [TEST2], data change syncing 
[WARN ] 2024-05-11 18:24:44.118 - [任务 4][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: Time Out
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-11 18:25:45.533 - [任务 4][TEST2] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-05-11 18:26:06.431 - [任务 4][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: Time Out
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-11 18:27:07.640 - [任务 4][TEST2] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-05-11 18:30:06.758 - [任务 4][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: Time Out
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-11 18:31:07.920 - [任务 4][TEST2] - [Auto Retry] Method (source_stream_read) retry succeed 
