[WARN ] 2024-07-04 14:25:51.941 - [任务 49][SouceMysql] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-72013c5b-c37a-401f-8b4c-feb3c2607133 
[INFO ] 2024-07-04 14:25:52.025 - [任务 49] - Start task milestones: 6685287cf5458d4b99316229(任务 49) 
[INFO ] 2024-07-04 14:25:52.046 - [任务 49] - Task initialization... 
[INFO ] 2024-07-04 14:25:53.539 - [任务 49] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-04 14:25:53.609 - [任务 49] - The engine receives 任务 49 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-04 14:25:54.400 - [任务 49][TargetMongo] - Node TargetMongo[51ed4299-ca7a-4af7-91a2-ffa261025b0c] start preload schema,table counts: 1 
[INFO ] 2024-07-04 14:25:54.400 - [任务 49][SouceMysql] - Node SouceMysql[72013c5b-c37a-401f-8b4c-feb3c2607133] start preload schema,table counts: 1 
[INFO ] 2024-07-04 14:25:54.407 - [任务 49][TargetMongo] - Node TargetMongo[51ed4299-ca7a-4af7-91a2-ffa261025b0c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 14:25:54.407 - [任务 49][SouceMysql] - Node SouceMysql[72013c5b-c37a-401f-8b4c-feb3c2607133] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 14:25:55.890 - [任务 49][TargetMongo] - Node(TargetMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-04 14:25:55.898 - [任务 49][TargetMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-04 14:25:55.951 - [任务 49][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-07-04 14:25:55.953 - [任务 49][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-07-04 14:25:55.960 - [任务 49][SouceMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-04 14:25:55.982 - [任务 49][SouceMysql] - batch offset found: {"CUSTOMER":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"sequenceMap":{"CUSTOMER":0,"_tapdata_heartbeat_table":6151},"streamOffset":{"name":"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f","offset":{"{\"server\":\"56bedfe2-0ecc-48a4-bbd0-10f20a40fb1f\"}":"{\"ts_sec\":1720006649,\"file\":\"binlog.000032\",\"pos\":19992509,\"row\":1,\"server_id\":1,\"event\":2}"}}} 
[INFO ] 2024-07-04 14:25:56.065 - [任务 49][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-07-04 14:25:56.066 - [任务 49][SouceMysql] - Initial sync completed 
[INFO ] 2024-07-04 14:25:56.132 - [任务 49][SouceMysql] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-04 14:25:56.132 - [任务 49][SouceMysql] - [Share CDC Task HZ Reader] - Step 1 - Check connection SouceMysql enable share cdc: true 
[INFO ] 2024-07-04 14:25:56.132 - [任务 49][SouceMysql] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 49 enable share cdc: true 
[INFO ] 2024-07-04 14:25:56.157 - [任务 49][SouceMysql] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SouceMysql的共享挖掘任务 
[INFO ] 2024-07-04 14:25:56.175 - [任务 49][SouceMysql] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-04 14:25:56.196 - [任务 49][SouceMysql] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6685289966ab5ede8ab9bd4f, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=667413fd7b5e1f6c3b139e78_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1199739013, shareCdcTaskId=66841ae5976c4665eeaed7b2, connectionId=667413fd7b5e1f6c3b139e78) 
[INFO ] 2024-07-04 14:25:56.199 - [任务 49][SouceMysql] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SouceMysql的共享挖掘任务_CUSTOMER_任务 49', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_1199739013', head seq: 0, tail seq: 0 
[INFO ] 2024-07-04 14:25:56.200 - [任务 49][SouceMysql] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-04 14:25:56.208 - [任务 49][SouceMysql] - Init share cdc reader completed 
[INFO ] 2024-07-04 14:25:56.212 - [任务 49][SouceMysql] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-04 14:25:56.223 - [任务 49][SouceMysql] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-04 14:25:56.224 - [任务 49][SouceMysql] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-04 14:25:56.231 - [任务 49][SouceMysql] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6685289966ab5ede8ab9bd4f, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=667413fd7b5e1f6c3b139e78_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_1199739013, shareCdcTaskId=66841ae5976c4665eeaed7b2, connectionId=667413fd7b5e1f6c3b139e78) 
[INFO ] 2024-07-04 14:25:56.234 - [任务 49][SouceMysql] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SouceMysql的共享挖掘任务_CUSTOMER_任务 49', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_1199739013', head seq: 0, tail seq: 0 
[INFO ] 2024-07-04 14:25:56.234 - [任务 49][SouceMysql] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SouceMysql的共享挖掘任务_CUSTOMER_任务 49, external storage name: ExternalStorage_SHARE_CDC_1199739013 
[INFO ] 2024-07-04 14:25:56.236 - [任务 49][SouceMysql] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CUSTOMER] 
[INFO ] 2024-07-04 14:25:56.242 - [任务 49][SouceMysql] - Connector Mysql incremental start succeed, tables: [CUSTOMER], data change syncing 
[INFO ] 2024-07-04 14:25:56.242 - [任务 49][SouceMysql] - [Share CDC Task HZ Reader] - Starting read 'CUSTOMER' log, sequence: 0 
[INFO ] 2024-07-04 14:25:56.243 - [任务 49][SouceMysql] - [Share CDC Task HZ Reader] - Find by CUSTOMER filter: {sequence=0} 
[INFO ] 2024-07-04 14:27:54.568 - [任务 49][SouceMysql] - Node SouceMysql[72013c5b-c37a-401f-8b4c-feb3c2607133] running status set to false 
[INFO ] 2024-07-04 14:27:54.595 - [任务 49][SouceMysql] - Incremental sync completed 
[INFO ] 2024-07-04 14:27:54.595 - [任务 49][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-72013c5b-c37a-401f-8b4c-feb3c2607133 
[INFO ] 2024-07-04 14:27:54.598 - [任务 49][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-72013c5b-c37a-401f-8b4c-feb3c2607133 
[INFO ] 2024-07-04 14:27:54.598 - [任务 49][SouceMysql] - Node SouceMysql[72013c5b-c37a-401f-8b4c-feb3c2607133] schema data cleaned 
[INFO ] 2024-07-04 14:27:54.604 - [任务 49][SouceMysql] - Node SouceMysql[72013c5b-c37a-401f-8b4c-feb3c2607133] monitor closed 
[INFO ] 2024-07-04 14:27:54.605 - [任务 49][SouceMysql] - Node SouceMysql[72013c5b-c37a-401f-8b4c-feb3c2607133] close complete, cost 45 ms 
[INFO ] 2024-07-04 14:27:54.641 - [任务 49][TargetMongo] - Node TargetMongo[51ed4299-ca7a-4af7-91a2-ffa261025b0c] running status set to false 
[INFO ] 2024-07-04 14:27:54.641 - [任务 49][TargetMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-51ed4299-ca7a-4af7-91a2-ffa261025b0c 
[INFO ] 2024-07-04 14:27:54.641 - [任务 49][TargetMongo] - PDK connector node released: HazelcastTargetPdkDataNode-51ed4299-ca7a-4af7-91a2-ffa261025b0c 
[INFO ] 2024-07-04 14:27:54.641 - [任务 49][TargetMongo] - Node TargetMongo[51ed4299-ca7a-4af7-91a2-ffa261025b0c] schema data cleaned 
[INFO ] 2024-07-04 14:27:54.642 - [任务 49][TargetMongo] - Node TargetMongo[51ed4299-ca7a-4af7-91a2-ffa261025b0c] monitor closed 
[INFO ] 2024-07-04 14:27:54.848 - [任务 49][TargetMongo] - Node TargetMongo[51ed4299-ca7a-4af7-91a2-ffa261025b0c] close complete, cost 38 ms 
[INFO ] 2024-07-04 14:27:56.348 - [任务 49] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-04 14:27:56.348 - [任务 49] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@364fc6d8 
[INFO ] 2024-07-04 14:27:56.510 - [任务 49] - Stop task milestones: 6685287cf5458d4b99316229(任务 49)  
[INFO ] 2024-07-04 14:27:56.510 - [任务 49] - Stopped task aspect(s) 
[INFO ] 2024-07-04 14:27:56.515 - [任务 49] - Snapshot order controller have been removed 
[INFO ] 2024-07-04 14:27:56.597 - [任务 49] - Remove memory task client succeed, task: 任务 49[6685287cf5458d4b99316229] 
[INFO ] 2024-07-04 14:27:56.612 - [任务 49] - Destroy memory task client cache succeed, task: 任务 49[6685287cf5458d4b99316229] 
