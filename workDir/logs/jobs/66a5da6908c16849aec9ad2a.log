[INFO ] 2024-07-28 13:47:41.703 - [任务 1] - Task initialization... 
[INFO ] 2024-07-28 13:47:41.917 - [任务 1] - Start task milestones: 66a5da6908c16849aec9ad2a(任务 1) 
[INFO ] 2024-07-28 13:47:42.718 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 13:47:42.850 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-28 13:47:43.729 - [任务 1][TESTPO] - Node TESTPO[490ed992-cf27-4bc4-af5c-0a4bb8fadec2] start preload schema,table counts: 1 
[INFO ] 2024-07-28 13:47:43.731 - [任务 1][TestPO] - Node TestPO[8c260b3f-1644-42c3-8df4-7dca2cec3135] start preload schema,table counts: 1 
[INFO ] 2024-07-28 13:47:43.731 - [任务 1][TestPO] - Node TestPO[8c260b3f-1644-42c3-8df4-7dca2cec3135] preload schema finished, cost 0 ms 
[INFO ] 2024-07-28 13:47:43.731 - [任务 1][TESTPO] - Node TESTPO[490ed992-cf27-4bc4-af5c-0a4bb8fadec2] preload schema finished, cost 3 ms 
[INFO ] 2024-07-28 13:47:44.577 - [任务 1][TestPO] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-28 13:47:44.643 - [任务 1][TestPO] - Table "test.TestPO" exists, skip auto create table 
[INFO ] 2024-07-28 13:47:44.643 - [任务 1][TESTPO] - Source node "TESTPO" read batch size: 100 
[INFO ] 2024-07-28 13:47:44.643 - [任务 1][TESTPO] - Source node "TESTPO" event queue capacity: 200 
[INFO ] 2024-07-28 13:47:44.643 - [任务 1][TESTPO] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-28 13:47:44.646 - [任务 1][TestPO] - The table TestPO has already exist. 
[INFO ] 2024-07-28 13:47:44.811 - [任务 1][TESTPO] - batch offset found: {},stream offset found: {"cdcOffset":1722145664,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 13:47:44.877 - [任务 1][TESTPO] - Initial sync started 
[INFO ] 2024-07-28 13:47:44.892 - [任务 1][TESTPO] - Starting batch read, table name: TESTPO, offset: null 
[INFO ] 2024-07-28 13:47:44.896 - [任务 1][TESTPO] - Table TESTPO is going to be initial synced 
[INFO ] 2024-07-28 13:47:44.985 - [任务 1][TESTPO] - Table [TESTPO] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-28 13:47:44.986 - [任务 1][TESTPO] - Query table 'TESTPO' counts: 600 
[INFO ] 2024-07-28 13:47:44.986 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 13:47:44.988 - [任务 1][TESTPO] - Incremental sync starting... 
[INFO ] 2024-07-28 13:47:44.988 - [任务 1][TESTPO] - Initial sync completed 
[INFO ] 2024-07-28 13:47:44.988 - [任务 1][TESTPO] - Starting stream read, table list: [TESTPO, _tapdata_heartbeat_table], offset: {"cdcOffset":1722145664,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-28 13:47:45.035 - [任务 1][TESTPO] - Connector MongoDB incremental start succeed, tables: [TESTPO, _tapdata_heartbeat_table], data change syncing 
