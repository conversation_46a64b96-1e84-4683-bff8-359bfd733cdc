[INFO ] 2024-03-26 18:24:04.757 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] start preload schema,table counts: 1 
[INFO ] 2024-03-26 18:24:04.758 - [任务 12(100)][63cb8e8e-d07c-4659-8b1e-d417ce5a93a0] - Node 63cb8e8e-d07c-4659-8b1e-d417ce5a93a0[63cb8e8e-d07c-4659-8b1e-d417ce5a93a0] start preload schema,table counts: 0 
[INFO ] 2024-03-26 18:24:04.759 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] start preload schema,table counts: 1 
[INFO ] 2024-03-26 18:24:04.759 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] preload schema finished, cost 1 ms 
[INFO ] 2024-03-26 18:24:04.759 - [任务 12(100)][63cb8e8e-d07c-4659-8b1e-d417ce5a93a0] - Node 63cb8e8e-d07c-4659-8b1e-d417ce5a93a0[63cb8e8e-d07c-4659-8b1e-d417ce5a93a0] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 18:24:04.760 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 18:24:05.849 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] running status set to false 
[INFO ] 2024-03-26 18:24:05.849 - [任务 12(100)][test1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 18:24:05.856 - [任务 12(100)][test1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 18:24:05.856 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] schema data cleaned 
[INFO ] 2024-03-26 18:24:05.858 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] monitor closed 
[INFO ] 2024-03-26 18:24:05.858 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] close complete, cost 30 ms 
[INFO ] 2024-03-26 18:24:05.871 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] running status set to false 
[INFO ] 2024-03-26 18:24:05.871 - [任务 12(100)][63cb8e8e-d07c-4659-8b1e-d417ce5a93a0] - Node 63cb8e8e-d07c-4659-8b1e-d417ce5a93a0[63cb8e8e-d07c-4659-8b1e-d417ce5a93a0] running status set to false 
[INFO ] 2024-03-26 18:24:05.871 - [任务 12(100)][63cb8e8e-d07c-4659-8b1e-d417ce5a93a0] - Node 63cb8e8e-d07c-4659-8b1e-d417ce5a93a0[63cb8e8e-d07c-4659-8b1e-d417ce5a93a0] schema data cleaned 
[INFO ] 2024-03-26 18:24:05.871 - [任务 12(100)][63cb8e8e-d07c-4659-8b1e-d417ce5a93a0] - Node 63cb8e8e-d07c-4659-8b1e-d417ce5a93a0[63cb8e8e-d07c-4659-8b1e-d417ce5a93a0] monitor closed 
[INFO ] 2024-03-26 18:24:05.875 - [任务 12(100)][63cb8e8e-d07c-4659-8b1e-d417ce5a93a0] - Node 63cb8e8e-d07c-4659-8b1e-d417ce5a93a0[63cb8e8e-d07c-4659-8b1e-d417ce5a93a0] close complete, cost 4 ms 
[INFO ] 2024-03-26 18:24:05.883 - [任务 12(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-f061d228-7b90-421c-8080-1d2d44319144 
[INFO ] 2024-03-26 18:24:05.884 - [任务 12(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-f061d228-7b90-421c-8080-1d2d44319144 
[INFO ] 2024-03-26 18:24:05.884 - [任务 12(100)][增强JS] - [ScriptExecutorsManager-6602a23855700b348d5e938a-574d0bf8-db5b-41d1-90c7-80208621cf74-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-26 18:24:05.886 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] schema data cleaned 
[INFO ] 2024-03-26 18:24:05.886 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] monitor closed 
[INFO ] 2024-03-26 18:24:05.892 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] close complete, cost 18 ms 
[INFO ] 2024-03-26 18:24:05.892 - [任务 12(100)] - load tapTable task 6602a23855700b348d5e938a-63cb8e8e-d07c-4659-8b1e-d417ce5a93a0 complete, cost 1235ms 
[INFO ] 2024-03-26 18:24:06.466 - [任务 12(100)][3d49ee6a-c9ab-4e44-8ee1-29353ff68122] - Node 3d49ee6a-c9ab-4e44-8ee1-29353ff68122[3d49ee6a-c9ab-4e44-8ee1-29353ff68122] start preload schema,table counts: 0 
[INFO ] 2024-03-26 18:24:06.466 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] start preload schema,table counts: 1 
[INFO ] 2024-03-26 18:24:06.467 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] start preload schema,table counts: 1 
[INFO ] 2024-03-26 18:24:06.467 - [任务 12(100)][3d49ee6a-c9ab-4e44-8ee1-29353ff68122] - Node 3d49ee6a-c9ab-4e44-8ee1-29353ff68122[3d49ee6a-c9ab-4e44-8ee1-29353ff68122] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 18:24:06.467 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 18:24:06.468 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 18:24:06.771 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] running status set to false 
[INFO ] 2024-03-26 18:24:06.784 - [任务 12(100)][test1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 18:24:06.784 - [任务 12(100)][test1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 18:24:06.787 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] schema data cleaned 
[INFO ] 2024-03-26 18:24:06.787 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] monitor closed 
[INFO ] 2024-03-26 18:24:06.788 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] close complete, cost 20 ms 
[INFO ] 2024-03-26 18:24:06.999 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] running status set to false 
[INFO ] 2024-03-26 18:24:07.007 - [任务 12(100)][3d49ee6a-c9ab-4e44-8ee1-29353ff68122] - Node 3d49ee6a-c9ab-4e44-8ee1-29353ff68122[3d49ee6a-c9ab-4e44-8ee1-29353ff68122] running status set to false 
[INFO ] 2024-03-26 18:24:07.007 - [任务 12(100)][3d49ee6a-c9ab-4e44-8ee1-29353ff68122] - Node 3d49ee6a-c9ab-4e44-8ee1-29353ff68122[3d49ee6a-c9ab-4e44-8ee1-29353ff68122] schema data cleaned 
[INFO ] 2024-03-26 18:24:07.007 - [任务 12(100)][3d49ee6a-c9ab-4e44-8ee1-29353ff68122] - Node 3d49ee6a-c9ab-4e44-8ee1-29353ff68122[3d49ee6a-c9ab-4e44-8ee1-29353ff68122] monitor closed 
[INFO ] 2024-03-26 18:24:07.010 - [任务 12(100)][3d49ee6a-c9ab-4e44-8ee1-29353ff68122] - Node 3d49ee6a-c9ab-4e44-8ee1-29353ff68122[3d49ee6a-c9ab-4e44-8ee1-29353ff68122] close complete, cost 5 ms 
[INFO ] 2024-03-26 18:24:07.011 - [任务 12(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-19819e07-4161-4a47-9fd1-3081d18d9fbe 
[INFO ] 2024-03-26 18:24:07.011 - [任务 12(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-19819e07-4161-4a47-9fd1-3081d18d9fbe 
[INFO ] 2024-03-26 18:24:07.011 - [任务 12(100)][增强JS] - [ScriptExecutorsManager-6602a23855700b348d5e938a-574d0bf8-db5b-41d1-90c7-80208621cf74-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-26 18:24:07.013 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] schema data cleaned 
[INFO ] 2024-03-26 18:24:07.013 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] monitor closed 
[INFO ] 2024-03-26 18:24:07.015 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] close complete, cost 17 ms 
[INFO ] 2024-03-26 18:24:07.015 - [任务 12(100)] - load tapTable task 6602a23855700b348d5e938a-3d49ee6a-c9ab-4e44-8ee1-29353ff68122 complete, cost 647ms 
[INFO ] 2024-03-26 18:58:33.311 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] start preload schema,table counts: 1 
[INFO ] 2024-03-26 18:58:33.311 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] start preload schema,table counts: 1 
[INFO ] 2024-03-26 18:58:33.311 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 18:58:33.313 - [任务 12(100)][12e2b55a-7831-4c92-831f-c84c0b36313b] - Node 12e2b55a-7831-4c92-831f-c84c0b36313b[12e2b55a-7831-4c92-831f-c84c0b36313b] start preload schema,table counts: 0 
[INFO ] 2024-03-26 18:58:33.313 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 18:58:33.313 - [任务 12(100)][12e2b55a-7831-4c92-831f-c84c0b36313b] - Node 12e2b55a-7831-4c92-831f-c84c0b36313b[12e2b55a-7831-4c92-831f-c84c0b36313b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 18:58:33.598 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] running status set to false 
[INFO ] 2024-03-26 18:58:33.620 - [任务 12(100)][test1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 18:58:33.620 - [任务 12(100)][test1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 18:58:33.621 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] schema data cleaned 
[INFO ] 2024-03-26 18:58:33.621 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] monitor closed 
[INFO ] 2024-03-26 18:58:33.622 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] close complete, cost 27 ms 
[INFO ] 2024-03-26 18:58:33.830 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] running status set to false 
[INFO ] 2024-03-26 18:58:33.831 - [任务 12(100)][12e2b55a-7831-4c92-831f-c84c0b36313b] - Node 12e2b55a-7831-4c92-831f-c84c0b36313b[12e2b55a-7831-4c92-831f-c84c0b36313b] running status set to false 
[INFO ] 2024-03-26 18:58:33.831 - [任务 12(100)][12e2b55a-7831-4c92-831f-c84c0b36313b] - Node 12e2b55a-7831-4c92-831f-c84c0b36313b[12e2b55a-7831-4c92-831f-c84c0b36313b] schema data cleaned 
[INFO ] 2024-03-26 18:58:33.831 - [任务 12(100)][12e2b55a-7831-4c92-831f-c84c0b36313b] - Node 12e2b55a-7831-4c92-831f-c84c0b36313b[12e2b55a-7831-4c92-831f-c84c0b36313b] monitor closed 
[INFO ] 2024-03-26 18:58:33.832 - [任务 12(100)][12e2b55a-7831-4c92-831f-c84c0b36313b] - Node 12e2b55a-7831-4c92-831f-c84c0b36313b[12e2b55a-7831-4c92-831f-c84c0b36313b] close complete, cost 6 ms 
[INFO ] 2024-03-26 18:58:33.840 - [任务 12(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-d6ebf0c3-8d0d-4f8c-98e4-3351859d510f 
[INFO ] 2024-03-26 18:58:33.840 - [任务 12(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-d6ebf0c3-8d0d-4f8c-98e4-3351859d510f 
[INFO ] 2024-03-26 18:58:33.840 - [任务 12(100)][增强JS] - [ScriptExecutorsManager-6602a23855700b348d5e938a-574d0bf8-db5b-41d1-90c7-80208621cf74-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-26 18:58:33.842 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] schema data cleaned 
[INFO ] 2024-03-26 18:58:33.842 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] monitor closed 
[INFO ] 2024-03-26 18:58:33.844 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] close complete, cost 19 ms 
[INFO ] 2024-03-26 18:58:33.844 - [任务 12(100)] - load tapTable task 6602a23855700b348d5e938a-12e2b55a-7831-4c92-831f-c84c0b36313b complete, cost 637ms 
[INFO ] 2024-03-26 19:03:06.637 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] start preload schema,table counts: 1 
[INFO ] 2024-03-26 19:03:06.637 - [任务 12(100)][c9ac59b0-b06e-404c-af18-67756db0bf10] - Node c9ac59b0-b06e-404c-af18-67756db0bf10[c9ac59b0-b06e-404c-af18-67756db0bf10] start preload schema,table counts: 0 
[INFO ] 2024-03-26 19:03:06.637 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] start preload schema,table counts: 1 
[INFO ] 2024-03-26 19:03:06.637 - [任务 12(100)][c9ac59b0-b06e-404c-af18-67756db0bf10] - Node c9ac59b0-b06e-404c-af18-67756db0bf10[c9ac59b0-b06e-404c-af18-67756db0bf10] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:03:06.637 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:03:06.637 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:03:06.637 - [任务 12(100)][1962c131-6f5f-4c2d-b997-b2c8186cec08] - Node 1962c131-6f5f-4c2d-b997-b2c8186cec08[1962c131-6f5f-4c2d-b997-b2c8186cec08] start preload schema,table counts: 0 
[INFO ] 2024-03-26 19:03:06.637 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] start preload schema,table counts: 1 
[INFO ] 2024-03-26 19:03:06.637 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] start preload schema,table counts: 1 
[INFO ] 2024-03-26 19:03:06.637 - [任务 12(100)][1962c131-6f5f-4c2d-b997-b2c8186cec08] - Node 1962c131-6f5f-4c2d-b997-b2c8186cec08[1962c131-6f5f-4c2d-b997-b2c8186cec08] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:03:06.637 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:03:06.637 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:03:06.638 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] running status set to false 
[INFO ] 2024-03-26 19:03:06.648 - [任务 12(100)][test1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 19:03:06.648 - [任务 12(100)][test1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 19:03:06.648 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] schema data cleaned 
[INFO ] 2024-03-26 19:03:06.649 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] monitor closed 
[INFO ] 2024-03-26 19:03:06.851 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] close complete, cost 34 ms 
[INFO ] 2024-03-26 19:03:07.081 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] running status set to false 
[INFO ] 2024-03-26 19:03:07.083 - [任务 12(100)][c9ac59b0-b06e-404c-af18-67756db0bf10] - Node c9ac59b0-b06e-404c-af18-67756db0bf10[c9ac59b0-b06e-404c-af18-67756db0bf10] running status set to false 
[INFO ] 2024-03-26 19:03:07.083 - [任务 12(100)][c9ac59b0-b06e-404c-af18-67756db0bf10] - Node c9ac59b0-b06e-404c-af18-67756db0bf10[c9ac59b0-b06e-404c-af18-67756db0bf10] schema data cleaned 
[INFO ] 2024-03-26 19:03:07.085 - [任务 12(100)][c9ac59b0-b06e-404c-af18-67756db0bf10] - Node c9ac59b0-b06e-404c-af18-67756db0bf10[c9ac59b0-b06e-404c-af18-67756db0bf10] monitor closed 
[INFO ] 2024-03-26 19:03:07.085 - [任务 12(100)][c9ac59b0-b06e-404c-af18-67756db0bf10] - Node c9ac59b0-b06e-404c-af18-67756db0bf10[c9ac59b0-b06e-404c-af18-67756db0bf10] close complete, cost 5 ms 
[INFO ] 2024-03-26 19:03:07.086 - [任务 12(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-78a07128-1fbf-46b5-878e-6c3d7e91edd3 
[INFO ] 2024-03-26 19:03:07.086 - [任务 12(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-78a07128-1fbf-46b5-878e-6c3d7e91edd3 
[INFO ] 2024-03-26 19:03:07.088 - [任务 12(100)][增强JS] - [ScriptExecutorsManager-6602a23855700b348d5e938a-574d0bf8-db5b-41d1-90c7-80208621cf74-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-26 19:03:07.090 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] schema data cleaned 
[INFO ] 2024-03-26 19:03:07.090 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] monitor closed 
[INFO ] 2024-03-26 19:03:07.090 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] close complete, cost 13 ms 
[INFO ] 2024-03-26 19:03:07.282 - [任务 12(100)] - load tapTable task 6602a23855700b348d5e938a-c9ac59b0-b06e-404c-af18-67756db0bf10 complete, cost 903ms 
[INFO ] 2024-03-26 19:03:07.285 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] running status set to false 
[INFO ] 2024-03-26 19:03:07.294 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] running status set to false 
[INFO ] 2024-03-26 19:03:07.295 - [任务 12(100)][1962c131-6f5f-4c2d-b997-b2c8186cec08] - Node 1962c131-6f5f-4c2d-b997-b2c8186cec08[1962c131-6f5f-4c2d-b997-b2c8186cec08] running status set to false 
[INFO ] 2024-03-26 19:03:07.295 - [任务 12(100)][1962c131-6f5f-4c2d-b997-b2c8186cec08] - Node 1962c131-6f5f-4c2d-b997-b2c8186cec08[1962c131-6f5f-4c2d-b997-b2c8186cec08] schema data cleaned 
[INFO ] 2024-03-26 19:03:07.296 - [任务 12(100)][1962c131-6f5f-4c2d-b997-b2c8186cec08] - Node 1962c131-6f5f-4c2d-b997-b2c8186cec08[1962c131-6f5f-4c2d-b997-b2c8186cec08] monitor closed 
[INFO ] 2024-03-26 19:03:07.296 - [任务 12(100)][1962c131-6f5f-4c2d-b997-b2c8186cec08] - Node 1962c131-6f5f-4c2d-b997-b2c8186cec08[1962c131-6f5f-4c2d-b997-b2c8186cec08] close complete, cost 1 ms 
[INFO ] 2024-03-26 19:03:07.298 - [任务 12(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-392ba796-ce82-4730-b441-c06bef771c64 
[INFO ] 2024-03-26 19:03:07.298 - [任务 12(100)][test1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 19:03:07.298 - [任务 12(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-392ba796-ce82-4730-b441-c06bef771c64 
[INFO ] 2024-03-26 19:03:07.298 - [任务 12(100)][test1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 19:03:07.299 - [任务 12(100)][增强JS] - [ScriptExecutorsManager-6602a23855700b348d5e938a-574d0bf8-db5b-41d1-90c7-80208621cf74-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-26 19:03:07.299 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] schema data cleaned 
[INFO ] 2024-03-26 19:03:07.300 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] monitor closed 
[INFO ] 2024-03-26 19:03:07.300 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] close complete, cost 27 ms 
[INFO ] 2024-03-26 19:03:07.300 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] schema data cleaned 
[INFO ] 2024-03-26 19:03:07.300 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] monitor closed 
[INFO ] 2024-03-26 19:03:07.302 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] close complete, cost 9 ms 
[INFO ] 2024-03-26 19:03:07.302 - [任务 12(100)] - load tapTable task 6602a23855700b348d5e938a-1962c131-6f5f-4c2d-b997-b2c8186cec08 complete, cost 1038ms 
[INFO ] 2024-03-26 19:03:22.010 - [任务 12(100)][5f714936-dbab-4fb8-8350-9096590c04d1] - Node 5f714936-dbab-4fb8-8350-9096590c04d1[5f714936-dbab-4fb8-8350-9096590c04d1] start preload schema,table counts: 0 
[INFO ] 2024-03-26 19:03:22.010 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] start preload schema,table counts: 1 
[INFO ] 2024-03-26 19:03:22.010 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] start preload schema,table counts: 1 
[INFO ] 2024-03-26 19:03:22.010 - [任务 12(100)][5f714936-dbab-4fb8-8350-9096590c04d1] - Node 5f714936-dbab-4fb8-8350-9096590c04d1[5f714936-dbab-4fb8-8350-9096590c04d1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:03:22.012 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:03:22.213 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:03:22.283 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] running status set to false 
[INFO ] 2024-03-26 19:03:22.283 - [任务 12(100)][test1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 19:03:22.283 - [任务 12(100)][test1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 19:03:22.283 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] schema data cleaned 
[INFO ] 2024-03-26 19:03:22.283 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] monitor closed 
[INFO ] 2024-03-26 19:03:22.283 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] close complete, cost 11 ms 
[INFO ] 2024-03-26 19:03:22.494 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] running status set to false 
[INFO ] 2024-03-26 19:03:22.494 - [任务 12(100)][5f714936-dbab-4fb8-8350-9096590c04d1] - Node 5f714936-dbab-4fb8-8350-9096590c04d1[5f714936-dbab-4fb8-8350-9096590c04d1] running status set to false 
[INFO ] 2024-03-26 19:03:22.494 - [任务 12(100)][5f714936-dbab-4fb8-8350-9096590c04d1] - Node 5f714936-dbab-4fb8-8350-9096590c04d1[5f714936-dbab-4fb8-8350-9096590c04d1] schema data cleaned 
[INFO ] 2024-03-26 19:03:22.494 - [任务 12(100)][5f714936-dbab-4fb8-8350-9096590c04d1] - Node 5f714936-dbab-4fb8-8350-9096590c04d1[5f714936-dbab-4fb8-8350-9096590c04d1] monitor closed 
[INFO ] 2024-03-26 19:03:22.494 - [任务 12(100)][5f714936-dbab-4fb8-8350-9096590c04d1] - Node 5f714936-dbab-4fb8-8350-9096590c04d1[5f714936-dbab-4fb8-8350-9096590c04d1] close complete, cost 2 ms 
[INFO ] 2024-03-26 19:03:22.497 - [任务 12(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-41928ffc-d518-4993-bf2a-8d6eac2adcf4 
[INFO ] 2024-03-26 19:03:22.497 - [任务 12(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-41928ffc-d518-4993-bf2a-8d6eac2adcf4 
[INFO ] 2024-03-26 19:03:22.498 - [任务 12(100)][增强JS] - [ScriptExecutorsManager-6602a23855700b348d5e938a-574d0bf8-db5b-41d1-90c7-80208621cf74-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-26 19:03:22.498 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] schema data cleaned 
[INFO ] 2024-03-26 19:03:22.498 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] monitor closed 
[INFO ] 2024-03-26 19:03:22.498 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] close complete, cost 7 ms 
[INFO ] 2024-03-26 19:03:22.698 - [任务 12(100)] - load tapTable task 6602a23855700b348d5e938a-5f714936-dbab-4fb8-8350-9096590c04d1 complete, cost 553ms 
[INFO ] 2024-03-26 19:03:52.178 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] start preload schema,table counts: 1 
[INFO ] 2024-03-26 19:03:52.181 - [任务 12(100)][356b65ae-4f3f-483e-9d4f-418af247d936] - Node 356b65ae-4f3f-483e-9d4f-418af247d936[356b65ae-4f3f-483e-9d4f-418af247d936] start preload schema,table counts: 0 
[INFO ] 2024-03-26 19:03:52.181 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] start preload schema,table counts: 1 
[INFO ] 2024-03-26 19:03:52.181 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:03:52.181 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:03:52.181 - [任务 12(100)][356b65ae-4f3f-483e-9d4f-418af247d936] - Node 356b65ae-4f3f-483e-9d4f-418af247d936[356b65ae-4f3f-483e-9d4f-418af247d936] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:03:52.523 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] running status set to false 
[INFO ] 2024-03-26 19:03:52.523 - [任务 12(100)][test1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 19:03:52.524 - [任务 12(100)][test1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 19:03:52.524 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] schema data cleaned 
[INFO ] 2024-03-26 19:03:52.524 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] monitor closed 
[INFO ] 2024-03-26 19:03:52.524 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] close complete, cost 27 ms 
[INFO ] 2024-03-26 19:03:52.727 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] running status set to false 
[INFO ] 2024-03-26 19:03:52.732 - [任务 12(100)][356b65ae-4f3f-483e-9d4f-418af247d936] - Node 356b65ae-4f3f-483e-9d4f-418af247d936[356b65ae-4f3f-483e-9d4f-418af247d936] running status set to false 
[INFO ] 2024-03-26 19:03:52.732 - [任务 12(100)][356b65ae-4f3f-483e-9d4f-418af247d936] - Node 356b65ae-4f3f-483e-9d4f-418af247d936[356b65ae-4f3f-483e-9d4f-418af247d936] schema data cleaned 
[INFO ] 2024-03-26 19:03:52.732 - [任务 12(100)][356b65ae-4f3f-483e-9d4f-418af247d936] - Node 356b65ae-4f3f-483e-9d4f-418af247d936[356b65ae-4f3f-483e-9d4f-418af247d936] monitor closed 
[INFO ] 2024-03-26 19:03:52.732 - [任务 12(100)][356b65ae-4f3f-483e-9d4f-418af247d936] - Node 356b65ae-4f3f-483e-9d4f-418af247d936[356b65ae-4f3f-483e-9d4f-418af247d936] close complete, cost 3 ms 
[INFO ] 2024-03-26 19:03:52.732 - [任务 12(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-7d20dca4-8ea9-441e-aa66-40871f4bff9b 
[INFO ] 2024-03-26 19:03:52.732 - [任务 12(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-7d20dca4-8ea9-441e-aa66-40871f4bff9b 
[INFO ] 2024-03-26 19:03:52.733 - [任务 12(100)][增强JS] - [ScriptExecutorsManager-6602a23855700b348d5e938a-574d0bf8-db5b-41d1-90c7-80208621cf74-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-26 19:03:52.734 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] schema data cleaned 
[INFO ] 2024-03-26 19:03:52.734 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] monitor closed 
[INFO ] 2024-03-26 19:03:52.736 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] close complete, cost 8 ms 
[INFO ] 2024-03-26 19:03:52.939 - [任务 12(100)] - load tapTable task 6602a23855700b348d5e938a-356b65ae-4f3f-483e-9d4f-418af247d936 complete, cost 656ms 
[INFO ] 2024-03-26 19:12:17.052 - [任务 12(100)][2138289f-b31b-4df3-8b86-1dda31a25d41] - Node 2138289f-b31b-4df3-8b86-1dda31a25d41[2138289f-b31b-4df3-8b86-1dda31a25d41] start preload schema,table counts: 0 
[INFO ] 2024-03-26 19:12:17.052 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] start preload schema,table counts: 1 
[INFO ] 2024-03-26 19:12:17.052 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] start preload schema,table counts: 1 
[INFO ] 2024-03-26 19:12:17.052 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:12:17.052 - [任务 12(100)][2138289f-b31b-4df3-8b86-1dda31a25d41] - Node 2138289f-b31b-4df3-8b86-1dda31a25d41[2138289f-b31b-4df3-8b86-1dda31a25d41] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:12:17.052 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] preload schema finished, cost 1 ms 
[INFO ] 2024-03-26 19:12:17.052 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] start preload schema,table counts: 1 
[INFO ] 2024-03-26 19:12:17.052 - [任务 12(100)][81e8d2b4-89dd-4559-a7b9-c4ec926b3d37] - Node 81e8d2b4-89dd-4559-a7b9-c4ec926b3d37[81e8d2b4-89dd-4559-a7b9-c4ec926b3d37] start preload schema,table counts: 0 
[INFO ] 2024-03-26 19:12:17.052 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] start preload schema,table counts: 1 
[INFO ] 2024-03-26 19:12:17.052 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:12:17.052 - [任务 12(100)][81e8d2b4-89dd-4559-a7b9-c4ec926b3d37] - Node 81e8d2b4-89dd-4559-a7b9-c4ec926b3d37[81e8d2b4-89dd-4559-a7b9-c4ec926b3d37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:12:17.053 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:12:17.053 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] running status set to false 
[INFO ] 2024-03-26 19:12:17.053 - [任务 12(100)][test1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 19:12:17.053 - [任务 12(100)][test1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 19:12:17.053 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] schema data cleaned 
[INFO ] 2024-03-26 19:12:17.053 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] monitor closed 
[INFO ] 2024-03-26 19:12:17.053 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] close complete, cost 13 ms 
[INFO ] 2024-03-26 19:12:17.491 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] running status set to false 
[INFO ] 2024-03-26 19:12:17.491 - [任务 12(100)][test1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 19:12:17.491 - [任务 12(100)][test1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 19:12:17.491 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] schema data cleaned 
[INFO ] 2024-03-26 19:12:17.496 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] monitor closed 
[INFO ] 2024-03-26 19:12:17.496 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] close complete, cost 19 ms 
[INFO ] 2024-03-26 19:12:17.497 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] running status set to false 
[INFO ] 2024-03-26 19:12:17.497 - [任务 12(100)][81e8d2b4-89dd-4559-a7b9-c4ec926b3d37] - Node 81e8d2b4-89dd-4559-a7b9-c4ec926b3d37[81e8d2b4-89dd-4559-a7b9-c4ec926b3d37] running status set to false 
[INFO ] 2024-03-26 19:12:17.497 - [任务 12(100)][81e8d2b4-89dd-4559-a7b9-c4ec926b3d37] - Node 81e8d2b4-89dd-4559-a7b9-c4ec926b3d37[81e8d2b4-89dd-4559-a7b9-c4ec926b3d37] schema data cleaned 
[INFO ] 2024-03-26 19:12:17.497 - [任务 12(100)][81e8d2b4-89dd-4559-a7b9-c4ec926b3d37] - Node 81e8d2b4-89dd-4559-a7b9-c4ec926b3d37[81e8d2b4-89dd-4559-a7b9-c4ec926b3d37] monitor closed 
[INFO ] 2024-03-26 19:12:17.503 - [任务 12(100)][81e8d2b4-89dd-4559-a7b9-c4ec926b3d37] - Node 81e8d2b4-89dd-4559-a7b9-c4ec926b3d37[81e8d2b4-89dd-4559-a7b9-c4ec926b3d37] close complete, cost 1 ms 
[INFO ] 2024-03-26 19:12:17.503 - [任务 12(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-df7eb4a9-7c19-46ab-b7a2-d9734f99873b 
[INFO ] 2024-03-26 19:12:17.503 - [任务 12(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-df7eb4a9-7c19-46ab-b7a2-d9734f99873b 
[INFO ] 2024-03-26 19:12:17.503 - [任务 12(100)][增强JS] - [ScriptExecutorsManager-6602a23855700b348d5e938a-574d0bf8-db5b-41d1-90c7-80208621cf74-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-26 19:12:17.505 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] schema data cleaned 
[INFO ] 2024-03-26 19:12:17.505 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] monitor closed 
[INFO ] 2024-03-26 19:12:17.507 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] close complete, cost 10 ms 
[INFO ] 2024-03-26 19:12:17.508 - [任务 12(100)] - load tapTable task 6602a23855700b348d5e938a-81e8d2b4-89dd-4559-a7b9-c4ec926b3d37 complete, cost 702ms 
[INFO ] 2024-03-26 19:12:17.752 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] running status set to false 
[INFO ] 2024-03-26 19:12:17.752 - [任务 12(100)][2138289f-b31b-4df3-8b86-1dda31a25d41] - Node 2138289f-b31b-4df3-8b86-1dda31a25d41[2138289f-b31b-4df3-8b86-1dda31a25d41] running status set to false 
[INFO ] 2024-03-26 19:12:17.752 - [任务 12(100)][2138289f-b31b-4df3-8b86-1dda31a25d41] - Node 2138289f-b31b-4df3-8b86-1dda31a25d41[2138289f-b31b-4df3-8b86-1dda31a25d41] schema data cleaned 
[INFO ] 2024-03-26 19:12:17.752 - [任务 12(100)][2138289f-b31b-4df3-8b86-1dda31a25d41] - Node 2138289f-b31b-4df3-8b86-1dda31a25d41[2138289f-b31b-4df3-8b86-1dda31a25d41] monitor closed 
[INFO ] 2024-03-26 19:12:17.752 - [任务 12(100)][2138289f-b31b-4df3-8b86-1dda31a25d41] - Node 2138289f-b31b-4df3-8b86-1dda31a25d41[2138289f-b31b-4df3-8b86-1dda31a25d41] close complete, cost 4 ms 
[INFO ] 2024-03-26 19:12:17.756 - [任务 12(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-61774b96-a90f-4aa9-ba4a-89db601e1cc1 
[INFO ] 2024-03-26 19:12:17.756 - [任务 12(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-61774b96-a90f-4aa9-ba4a-89db601e1cc1 
[INFO ] 2024-03-26 19:12:17.756 - [任务 12(100)][增强JS] - [ScriptExecutorsManager-6602a23855700b348d5e938a-574d0bf8-db5b-41d1-90c7-80208621cf74-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-26 19:12:17.757 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] schema data cleaned 
[INFO ] 2024-03-26 19:12:17.757 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] monitor closed 
[INFO ] 2024-03-26 19:12:17.757 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] close complete, cost 12 ms 
[INFO ] 2024-03-26 19:12:17.963 - [任务 12(100)] - load tapTable task 6602a23855700b348d5e938a-2138289f-b31b-4df3-8b86-1dda31a25d41 complete, cost 1070ms 
[INFO ] 2024-03-26 19:12:18.440 - [任务 12(100)][4cb5f9c1-6eb9-4f06-aeef-683a601a35ef] - Node 4cb5f9c1-6eb9-4f06-aeef-683a601a35ef[4cb5f9c1-6eb9-4f06-aeef-683a601a35ef] start preload schema,table counts: 0 
[INFO ] 2024-03-26 19:12:18.440 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] start preload schema,table counts: 1 
[INFO ] 2024-03-26 19:12:18.440 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] start preload schema,table counts: 1 
[INFO ] 2024-03-26 19:12:18.440 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:12:18.440 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:12:18.440 - [任务 12(100)][4cb5f9c1-6eb9-4f06-aeef-683a601a35ef] - Node 4cb5f9c1-6eb9-4f06-aeef-683a601a35ef[4cb5f9c1-6eb9-4f06-aeef-683a601a35ef] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:12:18.739 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] running status set to false 
[INFO ] 2024-03-26 19:12:18.739 - [任务 12(100)][test1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 19:12:18.739 - [任务 12(100)][test1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 19:12:18.739 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] schema data cleaned 
[INFO ] 2024-03-26 19:12:18.743 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] monitor closed 
[INFO ] 2024-03-26 19:12:18.743 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] close complete, cost 13 ms 
[INFO ] 2024-03-26 19:12:18.963 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] running status set to false 
[INFO ] 2024-03-26 19:12:18.963 - [任务 12(100)][4cb5f9c1-6eb9-4f06-aeef-683a601a35ef] - Node 4cb5f9c1-6eb9-4f06-aeef-683a601a35ef[4cb5f9c1-6eb9-4f06-aeef-683a601a35ef] running status set to false 
[INFO ] 2024-03-26 19:12:18.963 - [任务 12(100)][4cb5f9c1-6eb9-4f06-aeef-683a601a35ef] - Node 4cb5f9c1-6eb9-4f06-aeef-683a601a35ef[4cb5f9c1-6eb9-4f06-aeef-683a601a35ef] schema data cleaned 
[INFO ] 2024-03-26 19:12:18.963 - [任务 12(100)][4cb5f9c1-6eb9-4f06-aeef-683a601a35ef] - Node 4cb5f9c1-6eb9-4f06-aeef-683a601a35ef[4cb5f9c1-6eb9-4f06-aeef-683a601a35ef] monitor closed 
[INFO ] 2024-03-26 19:12:18.970 - [任务 12(100)][4cb5f9c1-6eb9-4f06-aeef-683a601a35ef] - Node 4cb5f9c1-6eb9-4f06-aeef-683a601a35ef[4cb5f9c1-6eb9-4f06-aeef-683a601a35ef] close complete, cost 1 ms 
[INFO ] 2024-03-26 19:12:18.970 - [任务 12(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-a005b2a1-27ab-4dc1-b767-52452147a5fd 
[INFO ] 2024-03-26 19:12:18.971 - [任务 12(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-a005b2a1-27ab-4dc1-b767-52452147a5fd 
[INFO ] 2024-03-26 19:12:18.971 - [任务 12(100)][增强JS] - [ScriptExecutorsManager-6602a23855700b348d5e938a-574d0bf8-db5b-41d1-90c7-80208621cf74-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-26 19:12:18.973 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] schema data cleaned 
[INFO ] 2024-03-26 19:12:18.973 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] monitor closed 
[INFO ] 2024-03-26 19:12:18.975 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] close complete, cost 13 ms 
[INFO ] 2024-03-26 19:12:18.975 - [任务 12(100)] - load tapTable task 6602a23855700b348d5e938a-4cb5f9c1-6eb9-4f06-aeef-683a601a35ef complete, cost 593ms 
[INFO ] 2024-03-26 19:12:36.134 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] start preload schema,table counts: 1 
[INFO ] 2024-03-26 19:12:36.134 - [任务 12(100)][3275df5e-180e-49a5-bbf1-92398de91645] - Node 3275df5e-180e-49a5-bbf1-92398de91645[3275df5e-180e-49a5-bbf1-92398de91645] start preload schema,table counts: 0 
[INFO ] 2024-03-26 19:12:36.134 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] start preload schema,table counts: 1 
[INFO ] 2024-03-26 19:12:36.136 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:12:36.136 - [任务 12(100)][3275df5e-180e-49a5-bbf1-92398de91645] - Node 3275df5e-180e-49a5-bbf1-92398de91645[3275df5e-180e-49a5-bbf1-92398de91645] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:12:36.136 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-26 19:12:36.423 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] running status set to false 
[INFO ] 2024-03-26 19:12:36.433 - [任务 12(100)][test1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 19:12:36.433 - [任务 12(100)][test1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-98871eab-c56c-4a79-bc78-7c7db1abf9d4 
[INFO ] 2024-03-26 19:12:36.434 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] schema data cleaned 
[INFO ] 2024-03-26 19:12:36.434 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] monitor closed 
[INFO ] 2024-03-26 19:12:36.636 - [任务 12(100)][test1] - Node test1[98871eab-c56c-4a79-bc78-7c7db1abf9d4] close complete, cost 16 ms 
[INFO ] 2024-03-26 19:12:36.644 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] running status set to false 
[INFO ] 2024-03-26 19:12:36.644 - [任务 12(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-35969aaf-2712-41c6-bf3e-6e7c713e6d1a 
[INFO ] 2024-03-26 19:12:36.644 - [任务 12(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-35969aaf-2712-41c6-bf3e-6e7c713e6d1a 
[INFO ] 2024-03-26 19:12:36.644 - [任务 12(100)][增强JS] - [ScriptExecutorsManager-6602a23855700b348d5e938a-574d0bf8-db5b-41d1-90c7-80208621cf74-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-26 19:12:36.644 - [任务 12(100)][3275df5e-180e-49a5-bbf1-92398de91645] - Node 3275df5e-180e-49a5-bbf1-92398de91645[3275df5e-180e-49a5-bbf1-92398de91645] running status set to false 
[INFO ] 2024-03-26 19:12:36.644 - [任务 12(100)][3275df5e-180e-49a5-bbf1-92398de91645] - Node 3275df5e-180e-49a5-bbf1-92398de91645[3275df5e-180e-49a5-bbf1-92398de91645] schema data cleaned 
[INFO ] 2024-03-26 19:12:36.644 - [任务 12(100)][3275df5e-180e-49a5-bbf1-92398de91645] - Node 3275df5e-180e-49a5-bbf1-92398de91645[3275df5e-180e-49a5-bbf1-92398de91645] monitor closed 
[INFO ] 2024-03-26 19:12:36.645 - [任务 12(100)][3275df5e-180e-49a5-bbf1-92398de91645] - Node 3275df5e-180e-49a5-bbf1-92398de91645[3275df5e-180e-49a5-bbf1-92398de91645] close complete, cost 0 ms 
[INFO ] 2024-03-26 19:12:36.645 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] schema data cleaned 
[INFO ] 2024-03-26 19:12:36.645 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] monitor closed 
[INFO ] 2024-03-26 19:12:36.645 - [任务 12(100)][增强JS] - Node 增强JS[574d0bf8-db5b-41d1-90c7-80208621cf74] close complete, cost 5 ms 
[INFO ] 2024-03-26 19:12:36.846 - [任务 12(100)] - load tapTable task 6602a23855700b348d5e938a-3275df5e-180e-49a5-bbf1-92398de91645 complete, cost 601ms 
