[INFO ] 2024-07-23 04:40:09.068 - [任务 11] - Task initialization... 
[INFO ] 2024-07-23 04:40:09.069 - [任务 11] - Start task milestones: 669ebfc32f0fe71c3fdc1a38(任务 11) 
[INFO ] 2024-07-23 04:40:09.284 - [任务 11] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-23 04:40:09.364 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-23 04:40:09.364 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] start preload schema,table counts: 1 
[INFO ] 2024-07-23 04:40:09.364 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] start preload schema,table counts: 1 
[INFO ] 2024-07-23 04:40:09.364 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 04:40:09.365 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 04:40:09.997 - [任务 11][dummy_test] - Source node "dummy_test" read batch size: 100 
[INFO ] 2024-07-23 04:40:09.999 - [任务 11][dummy_test] - Source node "dummy_test" event queue capacity: 200 
[INFO ] 2024-07-23 04:40:09.999 - [任务 11][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-23 04:40:09.999 - [任务 11][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721680809995,"lastTimes":1721680809995,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-23 04:40:10.046 - [任务 11][dummy_test] - Initial sync started 
[INFO ] 2024-07-23 04:40:10.055 - [任务 11][dummy_test] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-07-23 04:40:10.058 - [任务 11][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-07-23 04:40:10.058 - [任务 11][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-07-23 04:40:10.202 - [任务 11][dummy_test] - Query table 'dummy_test' counts: 1000000 
[INFO ] 2024-07-23 04:40:10.202 - [任务 11][Test1] - Write batch size: 1500, max wait ms per batch: 500 
[INFO ] 2024-07-23 04:40:30.579 - [任务 11][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-07-23 04:40:30.583 - [任务 11][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-23 04:40:30.583 - [任务 11][dummy_test] - Initial sync completed 
[INFO ] 2024-07-23 04:40:30.583 - [任务 11][dummy_test] - Incremental sync starting... 
[INFO ] 2024-07-23 04:40:30.583 - [任务 11][dummy_test] - Initial sync completed 
[INFO ] 2024-07-23 04:40:30.594 - [任务 11][dummy_test] - Starting stream read, table list: [dummy_test], offset: {"syncStage":null,"beginTimes":1721680809995,"lastTimes":1721680809995,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-23 04:40:30.594 - [任务 11][dummy_test] - Start [dummy_test] stream read 
[INFO ] 2024-07-23 04:40:30.707 - [任务 11][dummy_test] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[WARN ] 2024-07-23 04:40:30.707 - [任务 11][Test1] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: java.lang.Throwable: Code: 420, e.displayText() = DB::Exception: Cannot UPDATE key column `id` (version 21.8.15.7)

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeUpdate(ClickHouseStatementImpl.java:215)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-23 04:40:45.722 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] running status set to false 
[INFO ] 2024-07-23 04:40:45.724 - [任务 11][dummy_test] - Compile [dummy_test] batch read 
[INFO ] 2024-07-23 04:40:45.724 - [任务 11][dummy_test] - Stop connector 
[INFO ] 2024-07-23 04:40:45.729 - [任务 11][dummy_test] - Incremental sync completed 
[INFO ] 2024-07-23 04:40:45.736 - [任务 11][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-9f916799-2ed1-4fec-8a53-d61180b5f44a 
[INFO ] 2024-07-23 04:40:45.736 - [任务 11][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-9f916799-2ed1-4fec-8a53-d61180b5f44a 
[INFO ] 2024-07-23 04:40:45.736 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] schema data cleaned 
[INFO ] 2024-07-23 04:40:45.738 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] monitor closed 
[INFO ] 2024-07-23 04:40:45.738 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] close complete, cost 27 ms 
[INFO ] 2024-07-23 04:40:45.739 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] running status set to false 
[INFO ] 2024-07-23 04:40:45.739 - [任务 11][Test1] - Clickhouse Optimize Table start, tables: ["Test1"] 
[INFO ] 2024-07-23 04:40:45.942 - [任务 11][Test1] - Clickhouse Optimize Table start, tables: ["Test1"] 
[INFO ] 2024-07-23 04:40:48.559 - [任务 11][Test1] - Clickhouse Optimize Table end 
[WARN ] 2024-07-23 04:40:48.560 - [任务 11][Test1] - Clickhouse Optimize Table failed 
[INFO ] 2024-07-23 04:40:48.562 - [任务 11][Test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-07fb72e8-b163-4a73-853d-38814e9ca021 
[INFO ] 2024-07-23 04:40:48.562 - [任务 11][Test1] - PDK connector node released: HazelcastTargetPdkDataNode-07fb72e8-b163-4a73-853d-38814e9ca021 
[INFO ] 2024-07-23 04:40:48.562 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] schema data cleaned 
[INFO ] 2024-07-23 04:40:48.563 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] monitor closed 
[INFO ] 2024-07-23 04:40:48.591 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] close complete, cost 2824 ms 
[INFO ] 2024-07-23 04:40:48.592 - [任务 11][Test1] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-76) has been closed.
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapDeleteRecordEvent@2c3dabe6: {"before":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"3ae22ffa-b5ab-4c57-b4a4-3cb290b5f804","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721680830609,"tableId":"dummy_test","time":1721680830609,"type":301}, nodeIds=[9f916799-2ed1-4fec-8a53-d61180b5f44a], sourceTime=1721680830609, sourceSerialNo=null} 
[ERROR] 2024-07-23 04:40:48.797 - [任务 11][Test1] - java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-76) has been closed.
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapDeleteRecordEvent@2c3dabe6: {"before":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"3ae22ffa-b5ab-4c57-b4a4-3cb290b5f804","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721680830609,"tableId":"dummy_test","time":1721680830609,"type":301}, nodeIds=[9f916799-2ed1-4fec-8a53-d61180b5f44a], sourceTime=1721680830609, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-76) has been closed.
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapDeleteRecordEvent@2c3dabe6: {"before":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"3ae22ffa-b5ab-4c57-b4a4-3cb290b5f804","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721680830609,"tableId":"dummy_test","time":1721680830609,"type":301}, nodeIds=[9f916799-2ed1-4fec-8a53-d61180b5f44a], sourceTime=1721680830609, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-76) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:47)
	io.tapdata.connector.clickhouse.dml.ClickhouseRecordWriter.<init>(ClickhouseRecordWriter.java:13)
	io.tapdata.connector.clickhouse.ClickhouseConnector.writeRecord(ClickhouseConnector.java:310)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-76) has been closed.
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:632)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:601)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:582)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:494)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:500)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:546)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-76) has been closed.
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:798)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:498)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:498)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:624)
	... 12 more
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-76) has been closed.
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:47)
	at io.tapdata.connector.clickhouse.dml.ClickhouseRecordWriter.<init>(ClickhouseRecordWriter.java:13)
	at io.tapdata.connector.clickhouse.ClickhouseConnector.writeRecord(ClickhouseConnector.java:310)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:852)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 19 more
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-76) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 32 more

[INFO ] 2024-07-23 04:40:52.680 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-23 04:40:52.681 - [任务 11] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1c5fa3c7 
[INFO ] 2024-07-23 04:40:52.791 - [任务 11] - Stop task milestones: 669ebfc32f0fe71c3fdc1a38(任务 11)  
[INFO ] 2024-07-23 04:40:52.805 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-07-23 04:40:52.805 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-07-23 04:40:52.824 - [任务 11] - Remove memory task client succeed, task: 任务 11[669ebfc32f0fe71c3fdc1a38] 
[INFO ] 2024-07-23 04:40:52.829 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[669ebfc32f0fe71c3fdc1a38] 
[INFO ] 2024-07-23 04:42:38.734 - [任务 11] - Task initialization... 
[INFO ] 2024-07-23 04:42:38.889 - [任务 11] - Start task milestones: 669ebfc32f0fe71c3fdc1a38(任务 11) 
[INFO ] 2024-07-23 04:42:38.890 - [任务 11] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-23 04:42:38.984 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-23 04:42:38.984 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] start preload schema,table counts: 1 
[INFO ] 2024-07-23 04:42:38.984 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] start preload schema,table counts: 1 
[INFO ] 2024-07-23 04:42:38.984 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 04:42:38.985 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] preload schema finished, cost 1 ms 
[INFO ] 2024-07-23 04:42:39.639 - [任务 11][dummy_test] - Source node "dummy_test" read batch size: 100 
[INFO ] 2024-07-23 04:42:39.639 - [任务 11][dummy_test] - Source node "dummy_test" event queue capacity: 200 
[INFO ] 2024-07-23 04:42:39.639 - [任务 11][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-23 04:42:39.640 - [任务 11][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721680959639,"lastTimes":1721680959639,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-23 04:42:39.695 - [任务 11][Test1] - Write batch size: 10000, max wait ms per batch: 500 
[INFO ] 2024-07-23 04:42:39.696 - [任务 11][dummy_test] - Initial sync started 
[INFO ] 2024-07-23 04:42:39.696 - [任务 11][dummy_test] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-07-23 04:42:39.704 - [任务 11][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-07-23 04:42:39.704 - [任务 11][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-07-23 04:42:39.905 - [任务 11][dummy_test] - Query table 'dummy_test' counts: 10000000 
[INFO ] 2024-07-23 04:43:55.268 - [任务 11][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-07-23 04:43:55.269 - [任务 11][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-23 04:43:55.269 - [任务 11][dummy_test] - Initial sync completed 
[INFO ] 2024-07-23 04:43:55.269 - [任务 11][dummy_test] - Incremental sync starting... 
[INFO ] 2024-07-23 04:43:55.269 - [任务 11][dummy_test] - Initial sync completed 
[INFO ] 2024-07-23 04:43:55.286 - [任务 11][dummy_test] - Starting stream read, table list: [dummy_test], offset: {"syncStage":null,"beginTimes":1721680959639,"lastTimes":1721680959639,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-23 04:43:55.286 - [任务 11][dummy_test] - Start [dummy_test] stream read 
[INFO ] 2024-07-23 04:43:55.287 - [任务 11][dummy_test] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[WARN ] 2024-07-23 04:44:07.871 - [任务 11][Test1] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: java.lang.Throwable: Code: 420, e.displayText() = DB::Exception: Cannot UPDATE key column `id` (version 21.8.15.7)

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeUpdate(ClickHouseStatementImpl.java:215)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-23 04:44:21.541 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] running status set to false 
[INFO ] 2024-07-23 04:44:21.544 - [任务 11][dummy_test] - Stop connector 
[INFO ] 2024-07-23 04:44:21.545 - [任务 11][dummy_test] - Compile [dummy_test] batch read 
[INFO ] 2024-07-23 04:44:21.547 - [任务 11][dummy_test] - Incremental sync completed 
[INFO ] 2024-07-23 04:44:21.560 - [任务 11][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-9f916799-2ed1-4fec-8a53-d61180b5f44a 
[INFO ] 2024-07-23 04:44:21.561 - [任务 11][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-9f916799-2ed1-4fec-8a53-d61180b5f44a 
[INFO ] 2024-07-23 04:44:21.562 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] schema data cleaned 
[INFO ] 2024-07-23 04:44:21.562 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] monitor closed 
[INFO ] 2024-07-23 04:44:21.564 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] close complete, cost 27 ms 
[INFO ] 2024-07-23 04:44:21.564 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] running status set to false 
[INFO ] 2024-07-23 04:44:21.581 - [任务 11][Test1] - Clickhouse Optimize Table start, tables: ["Test1"] 
[INFO ] 2024-07-23 04:44:21.581 - [任务 11][Test1] - Clickhouse Optimize Table start, tables: ["Test1"] 
[INFO ] 2024-07-23 04:44:48.357 - [任务 11][Test1] - Clickhouse Optimize Table end 
[WARN ] 2024-07-23 04:44:48.390 - [任务 11][Test1] - Clickhouse Optimize Table failed 
[INFO ] 2024-07-23 04:44:48.399 - [任务 11][Test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-07fb72e8-b163-4a73-853d-38814e9ca021 
[INFO ] 2024-07-23 04:44:48.400 - [任务 11][Test1] - PDK connector node released: HazelcastTargetPdkDataNode-07fb72e8-b163-4a73-853d-38814e9ca021 
[INFO ] 2024-07-23 04:44:48.406 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] schema data cleaned 
[INFO ] 2024-07-23 04:44:48.406 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] monitor closed 
[INFO ] 2024-07-23 04:44:48.406 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] close complete, cost 26835 ms 
[INFO ] 2024-07-23 04:44:48.427 - [任务 11][Test1] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-77) has been closed.
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@2851b10f: {"after":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"13285542-b363-4925-905e-537cd6faa013","name2":"name2","name1":"name1"},"before":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"13285542-b363-4925-905e-537cd6faa013","name2":"name2","name1":"name1"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721681035412,"tableId":"dummy_test","time":1721681035412,"type":302}, nodeIds=[9f916799-2ed1-4fec-8a53-d61180b5f44a], sourceTime=1721681035412, sourceSerialNo=null} 
[ERROR] 2024-07-23 04:44:48.430 - [任务 11][Test1] - java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-77) has been closed.
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@2851b10f: {"after":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"13285542-b363-4925-905e-537cd6faa013","name2":"name2","name1":"name1"},"before":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"13285542-b363-4925-905e-537cd6faa013","name2":"name2","name1":"name1"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721681035412,"tableId":"dummy_test","time":1721681035412,"type":302}, nodeIds=[9f916799-2ed1-4fec-8a53-d61180b5f44a], sourceTime=1721681035412, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-77) has been closed.
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@2851b10f: {"after":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"13285542-b363-4925-905e-537cd6faa013","name2":"name2","name1":"name1"},"before":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"13285542-b363-4925-905e-537cd6faa013","name2":"name2","name1":"name1"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721681035412,"tableId":"dummy_test","time":1721681035412,"type":302}, nodeIds=[9f916799-2ed1-4fec-8a53-d61180b5f44a], sourceTime=1721681035412, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-77) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:47)
	io.tapdata.connector.clickhouse.dml.ClickhouseRecordWriter.<init>(ClickhouseRecordWriter.java:13)
	io.tapdata.connector.clickhouse.ClickhouseConnector.writeRecord(ClickhouseConnector.java:310)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-77) has been closed.
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:632)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:601)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:582)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:494)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:500)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:546)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-77) has been closed.
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:798)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:498)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:498)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:624)
	... 12 more
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-77) has been closed.
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:47)
	at io.tapdata.connector.clickhouse.dml.ClickhouseRecordWriter.<init>(ClickhouseRecordWriter.java:13)
	at io.tapdata.connector.clickhouse.ClickhouseConnector.writeRecord(ClickhouseConnector.java:310)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:852)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 19 more
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-77) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 32 more

[INFO ] 2024-07-23 04:44:53.089 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-23 04:44:53.090 - [任务 11] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@41c6fa02 
[INFO ] 2024-07-23 04:44:53.237 - [任务 11] - Stop task milestones: 669ebfc32f0fe71c3fdc1a38(任务 11)  
[INFO ] 2024-07-23 04:44:53.237 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-07-23 04:44:53.237 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-07-23 04:44:53.261 - [任务 11] - Remove memory task client succeed, task: 任务 11[669ebfc32f0fe71c3fdc1a38] 
[INFO ] 2024-07-23 04:44:53.263 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[669ebfc32f0fe71c3fdc1a38] 
[INFO ] 2024-07-23 04:45:23.877 - [任务 11] - Task initialization... 
[INFO ] 2024-07-23 04:45:24.029 - [任务 11] - Start task milestones: 669ebfc32f0fe71c3fdc1a38(任务 11) 
[INFO ] 2024-07-23 04:45:24.030 - [任务 11] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-23 04:45:24.121 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-23 04:45:24.254 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] start preload schema,table counts: 1 
[INFO ] 2024-07-23 04:45:24.255 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] start preload schema,table counts: 1 
[INFO ] 2024-07-23 04:45:24.255 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 04:45:24.255 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 04:45:24.995 - [任务 11][dummy_test] - Source node "dummy_test" read batch size: 100 
[INFO ] 2024-07-23 04:45:24.996 - [任务 11][Test1] - Write batch size: 10000, max wait ms per batch: 500 
[INFO ] 2024-07-23 04:45:24.996 - [任务 11][dummy_test] - Source node "dummy_test" event queue capacity: 200 
[INFO ] 2024-07-23 04:45:24.996 - [任务 11][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-23 04:45:24.998 - [任务 11][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721681124996,"lastTimes":1721681124996,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-23 04:45:25.076 - [任务 11][dummy_test] - Initial sync started 
[INFO ] 2024-07-23 04:45:25.084 - [任务 11][dummy_test] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-07-23 04:45:25.084 - [任务 11][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-07-23 04:45:25.090 - [任务 11][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-07-23 04:45:25.294 - [任务 11][dummy_test] - Query table 'dummy_test' counts: 10000000 
[INFO ] 2024-07-23 04:46:42.383 - [任务 11][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-07-23 04:46:42.383 - [任务 11][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-23 04:46:42.383 - [任务 11][dummy_test] - Initial sync completed 
[INFO ] 2024-07-23 04:46:42.384 - [任务 11][dummy_test] - Incremental sync starting... 
[INFO ] 2024-07-23 04:46:42.384 - [任务 11][dummy_test] - Initial sync completed 
[INFO ] 2024-07-23 04:46:42.398 - [任务 11][dummy_test] - Starting stream read, table list: [dummy_test], offset: {"syncStage":null,"beginTimes":1721681124996,"lastTimes":1721681124996,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-23 04:46:42.398 - [任务 11][dummy_test] - Start [dummy_test] stream read 
[INFO ] 2024-07-23 04:46:42.398 - [任务 11][dummy_test] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[INFO ] 2024-07-23 04:46:52.895 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] running status set to false 
[INFO ] 2024-07-23 04:46:52.895 - [任务 11][dummy_test] - Stop connector 
[INFO ] 2024-07-23 04:46:52.897 - [任务 11][dummy_test] - Compile [dummy_test] batch read 
[INFO ] 2024-07-23 04:46:52.902 - [任务 11][dummy_test] - Incremental sync completed 
[INFO ] 2024-07-23 04:46:52.902 - [任务 11][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-9f916799-2ed1-4fec-8a53-d61180b5f44a 
[INFO ] 2024-07-23 04:46:52.903 - [任务 11][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-9f916799-2ed1-4fec-8a53-d61180b5f44a 
[INFO ] 2024-07-23 04:46:52.903 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] schema data cleaned 
[INFO ] 2024-07-23 04:46:52.904 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] monitor closed 
[INFO ] 2024-07-23 04:46:52.905 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] close complete, cost 32 ms 
[INFO ] 2024-07-23 04:46:52.905 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] running status set to false 
[INFO ] 2024-07-23 04:46:52.916 - [任务 11][Test1] - Clickhouse Optimize Table start, tables: ["Test1"] 
[INFO ] 2024-07-23 04:47:19.709 - [任务 11][Test1] - Clickhouse Optimize Table end 
[INFO ] 2024-07-23 04:47:19.715 - [任务 11][Test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-07fb72e8-b163-4a73-853d-38814e9ca021 
[INFO ] 2024-07-23 04:47:19.715 - [任务 11][Test1] - PDK connector node released: HazelcastTargetPdkDataNode-07fb72e8-b163-4a73-853d-38814e9ca021 
[INFO ] 2024-07-23 04:47:19.715 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] schema data cleaned 
[INFO ] 2024-07-23 04:47:19.715 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] monitor closed 
[INFO ] 2024-07-23 04:47:19.716 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] close complete, cost 26811 ms 
[INFO ] 2024-07-23 04:47:23.431 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-23 04:47:23.432 - [任务 11] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4b9d65ae 
[INFO ] 2024-07-23 04:47:23.566 - [任务 11] - Stop task milestones: 669ebfc32f0fe71c3fdc1a38(任务 11)  
[INFO ] 2024-07-23 04:47:23.566 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-07-23 04:47:23.566 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-07-23 04:47:23.587 - [任务 11] - Remove memory task client succeed, task: 任务 11[669ebfc32f0fe71c3fdc1a38] 
[INFO ] 2024-07-23 04:47:23.589 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[669ebfc32f0fe71c3fdc1a38] 
[INFO ] 2024-07-23 04:48:07.718 - [任务 11] - Task initialization... 
[INFO ] 2024-07-23 04:48:07.887 - [任务 11] - Start task milestones: 669ebfc32f0fe71c3fdc1a38(任务 11) 
[INFO ] 2024-07-23 04:48:07.888 - [任务 11] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-23 04:48:07.945 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-23 04:48:08.097 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] start preload schema,table counts: 1 
[INFO ] 2024-07-23 04:48:08.097 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] start preload schema,table counts: 1 
[INFO ] 2024-07-23 04:48:08.097 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 04:48:08.097 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 04:48:08.741 - [任务 11][dummy_test] - Source node "dummy_test" read batch size: 10000 
[INFO ] 2024-07-23 04:48:08.741 - [任务 11][dummy_test] - Source node "dummy_test" event queue capacity: 20000 
[INFO ] 2024-07-23 04:48:08.741 - [任务 11][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-23 04:48:08.741 - [任务 11][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721681288739,"lastTimes":1721681288739,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-23 04:48:08.786 - [任务 11][dummy_test] - Initial sync started 
[INFO ] 2024-07-23 04:48:08.791 - [任务 11][dummy_test] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-07-23 04:48:08.791 - [任务 11][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-07-23 04:48:08.795 - [任务 11][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-07-23 04:48:08.798 - [任务 11][dummy_test] - Query table 'dummy_test' counts: 10000000 
[INFO ] 2024-07-23 04:48:09.000 - [任务 11][Test1] - Write batch size: 20000, max wait ms per batch: 500 
[INFO ] 2024-07-23 04:49:15.657 - [任务 11][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-07-23 04:49:15.658 - [任务 11][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-23 04:49:15.659 - [任务 11][dummy_test] - Initial sync completed 
[INFO ] 2024-07-23 04:49:15.667 - [任务 11][dummy_test] - Incremental sync starting... 
[INFO ] 2024-07-23 04:49:15.668 - [任务 11][dummy_test] - Initial sync completed 
[INFO ] 2024-07-23 04:49:15.670 - [任务 11][dummy_test] - Starting stream read, table list: [dummy_test], offset: {"syncStage":null,"beginTimes":1721681288739,"lastTimes":1721681288739,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-23 04:49:15.679 - [任务 11][dummy_test] - Start [dummy_test] stream read 
[INFO ] 2024-07-23 04:49:15.679 - [任务 11][dummy_test] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[WARN ] 2024-07-23 04:49:28.673 - [任务 11][Test1] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: java.lang.Throwable: Code: 420, e.displayText() = DB::Exception: Cannot UPDATE key column `id` (version 21.8.15.7)

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeUpdate(ClickHouseStatementImpl.java:215)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-23 04:50:04.577 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] running status set to false 
[INFO ] 2024-07-23 04:50:04.577 - [任务 11][dummy_test] - Stop connector 
[INFO ] 2024-07-23 04:50:04.580 - [任务 11][dummy_test] - Compile [dummy_test] batch read 
[INFO ] 2024-07-23 04:50:04.580 - [任务 11][dummy_test] - Incremental sync completed 
[INFO ] 2024-07-23 04:50:04.637 - [任务 11][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-9f916799-2ed1-4fec-8a53-d61180b5f44a 
[INFO ] 2024-07-23 04:50:04.637 - [任务 11][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-9f916799-2ed1-4fec-8a53-d61180b5f44a 
[INFO ] 2024-07-23 04:50:04.638 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] schema data cleaned 
[INFO ] 2024-07-23 04:50:04.638 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] monitor closed 
[INFO ] 2024-07-23 04:50:04.639 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] close complete, cost 72 ms 
[INFO ] 2024-07-23 04:50:04.639 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] running status set to false 
[INFO ] 2024-07-23 04:50:04.646 - [任务 11][Test1] - Clickhouse Optimize Table start, tables: ["Test1"] 
[INFO ] 2024-07-23 04:50:04.646 - [任务 11][Test1] - Clickhouse Optimize Table start, tables: ["Test1"] 
[INFO ] 2024-07-23 04:50:31.829 - [任务 11][Test1] - Clickhouse Optimize Table end 
[WARN ] 2024-07-23 04:50:31.829 - [任务 11][Test1] - Clickhouse Optimize Table failed 
[INFO ] 2024-07-23 04:50:31.836 - [任务 11][Test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-07fb72e8-b163-4a73-853d-38814e9ca021 
[INFO ] 2024-07-23 04:50:31.836 - [任务 11][Test1] - PDK connector node released: HazelcastTargetPdkDataNode-07fb72e8-b163-4a73-853d-38814e9ca021 
[INFO ] 2024-07-23 04:50:31.837 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] schema data cleaned 
[INFO ] 2024-07-23 04:50:31.837 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] monitor closed 
[INFO ] 2024-07-23 04:50:31.855 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] close complete, cost 27198 ms 
[INFO ] 2024-07-23 04:50:31.856 - [任务 11][Test1] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-79) has been closed.
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapDeleteRecordEvent@7d46e41a: {"before":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"2ee83651-0eb8-45e8-97ed-f6c5d959f308","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721681355881,"tableId":"dummy_test","time":1721681355881,"type":301}, nodeIds=[9f916799-2ed1-4fec-8a53-d61180b5f44a], sourceTime=1721681355881, sourceSerialNo=null} 
[ERROR] 2024-07-23 04:50:32.059 - [任务 11][Test1] - java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-79) has been closed.
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapDeleteRecordEvent@7d46e41a: {"before":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"2ee83651-0eb8-45e8-97ed-f6c5d959f308","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721681355881,"tableId":"dummy_test","time":1721681355881,"type":301}, nodeIds=[9f916799-2ed1-4fec-8a53-d61180b5f44a], sourceTime=1721681355881, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-79) has been closed.
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapDeleteRecordEvent@7d46e41a: {"before":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"2ee83651-0eb8-45e8-97ed-f6c5d959f308","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721681355881,"tableId":"dummy_test","time":1721681355881,"type":301}, nodeIds=[9f916799-2ed1-4fec-8a53-d61180b5f44a], sourceTime=1721681355881, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-79) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:47)
	io.tapdata.connector.clickhouse.dml.ClickhouseRecordWriter.<init>(ClickhouseRecordWriter.java:13)
	io.tapdata.connector.clickhouse.ClickhouseConnector.writeRecord(ClickhouseConnector.java:310)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-79) has been closed.
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:632)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:601)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:582)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:494)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:500)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:546)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-79) has been closed.
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:798)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:498)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:498)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:624)
	... 12 more
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-79) has been closed.
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:47)
	at io.tapdata.connector.clickhouse.dml.ClickhouseRecordWriter.<init>(ClickhouseRecordWriter.java:13)
	at io.tapdata.connector.clickhouse.ClickhouseConnector.writeRecord(ClickhouseConnector.java:310)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:852)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 19 more
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-79) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 32 more

[INFO ] 2024-07-23 04:50:33.893 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-23 04:50:33.893 - [任务 11] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@9d10344 
[INFO ] 2024-07-23 04:50:34.036 - [任务 11] - Stop task milestones: 669ebfc32f0fe71c3fdc1a38(任务 11)  
[INFO ] 2024-07-23 04:50:34.037 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-07-23 04:50:34.037 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-07-23 04:50:34.059 - [任务 11] - Remove memory task client succeed, task: 任务 11[669ebfc32f0fe71c3fdc1a38] 
[INFO ] 2024-07-23 04:50:34.059 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[669ebfc32f0fe71c3fdc1a38] 
[INFO ] 2024-07-23 05:21:49.628 - [任务 11] - Task initialization... 
[INFO ] 2024-07-23 05:21:49.652 - [任务 11] - Start task milestones: 669ebfc32f0fe71c3fdc1a38(任务 11) 
[INFO ] 2024-07-23 05:21:49.823 - [任务 11] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-23 05:21:49.823 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-23 05:21:49.895 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] start preload schema,table counts: 1 
[INFO ] 2024-07-23 05:21:49.895 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] start preload schema,table counts: 1 
[INFO ] 2024-07-23 05:21:49.896 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] preload schema finished, cost 1 ms 
[INFO ] 2024-07-23 05:21:49.897 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] preload schema finished, cost 1 ms 
[INFO ] 2024-07-23 05:21:50.596 - [任务 11][dummy_test] - Source node "dummy_test" read batch size: 10000 
[INFO ] 2024-07-23 05:21:50.596 - [任务 11][dummy_test] - Source node "dummy_test" event queue capacity: 20000 
[INFO ] 2024-07-23 05:21:50.596 - [任务 11][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-23 05:21:50.597 - [任务 11][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721683310596,"lastTimes":1721683310596,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-23 05:21:50.655 - [任务 11][Test1] - Write batch size: 20000, max wait ms per batch: 500 
[INFO ] 2024-07-23 05:21:50.655 - [任务 11][dummy_test] - Initial sync started 
[INFO ] 2024-07-23 05:21:50.655 - [任务 11][dummy_test] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-07-23 05:21:50.663 - [任务 11][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-07-23 05:21:50.663 - [任务 11][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-07-23 05:21:50.865 - [任务 11][dummy_test] - Query table 'dummy_test' counts: 10000000 
[INFO ] 2024-07-23 05:22:53.448 - [任务 11][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-07-23 05:22:53.448 - [任务 11][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-23 05:22:53.448 - [任务 11][dummy_test] - Initial sync completed 
[INFO ] 2024-07-23 05:22:53.450 - [任务 11][dummy_test] - Incremental sync starting... 
[INFO ] 2024-07-23 05:22:53.450 - [任务 11][dummy_test] - Initial sync completed 
[INFO ] 2024-07-23 05:22:53.461 - [任务 11][dummy_test] - Starting stream read, table list: [dummy_test], offset: {"syncStage":null,"beginTimes":1721683310596,"lastTimes":1721683310596,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-23 05:22:53.464 - [任务 11][dummy_test] - Start [dummy_test] stream read 
[INFO ] 2024-07-23 05:22:53.464 - [任务 11][dummy_test] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[WARN ] 2024-07-23 05:23:06.883 - [任务 11][Test1] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: java.lang.Throwable: Code: 420, e.displayText() = DB::Exception: Cannot UPDATE key column `id` (version 21.8.15.7)

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeUpdate(ClickHouseStatementImpl.java:215)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-23 05:23:34.287 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] running status set to false 
[INFO ] 2024-07-23 05:23:34.287 - [任务 11][dummy_test] - Stop connector 
[INFO ] 2024-07-23 05:23:34.289 - [任务 11][dummy_test] - Compile [dummy_test] batch read 
[INFO ] 2024-07-23 05:23:34.289 - [任务 11][dummy_test] - Incremental sync completed 
[INFO ] 2024-07-23 05:23:34.293 - [任务 11][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-9f916799-2ed1-4fec-8a53-d61180b5f44a 
[INFO ] 2024-07-23 05:23:34.293 - [任务 11][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-9f916799-2ed1-4fec-8a53-d61180b5f44a 
[INFO ] 2024-07-23 05:23:34.293 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] schema data cleaned 
[INFO ] 2024-07-23 05:23:34.294 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] monitor closed 
[INFO ] 2024-07-23 05:23:34.294 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] close complete, cost 11 ms 
[INFO ] 2024-07-23 05:23:34.298 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] running status set to false 
[INFO ] 2024-07-23 05:23:34.298 - [任务 11][Test1] - Clickhouse Optimize Table start, tables: ["Test1"] 
[INFO ] 2024-07-23 05:23:34.502 - [任务 11][Test1] - Clickhouse Optimize Table start, tables: ["Test1"] 
[INFO ] 2024-07-23 05:24:01.057 - [任务 11][Test1] - Clickhouse Optimize Table end 
[WARN ] 2024-07-23 05:24:01.078 - [任务 11][Test1] - Clickhouse Optimize Table failed 
[INFO ] 2024-07-23 05:24:01.078 - [任务 11][Test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-07fb72e8-b163-4a73-853d-38814e9ca021 
[INFO ] 2024-07-23 05:24:01.078 - [任务 11][Test1] - PDK connector node released: HazelcastTargetPdkDataNode-07fb72e8-b163-4a73-853d-38814e9ca021 
[INFO ] 2024-07-23 05:24:01.085 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] schema data cleaned 
[INFO ] 2024-07-23 05:24:01.085 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] monitor closed 
[INFO ] 2024-07-23 05:24:01.086 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] close complete, cost 26784 ms 
[INFO ] 2024-07-23 05:24:01.105 - [任务 11][Test1] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-80) has been closed.
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapDeleteRecordEvent@6a8c7f51: {"before":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"c3681c47-cea4-40c7-aad9-2313d94910e9","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721683373660,"tableId":"dummy_test","time":1721683373660,"type":301}, nodeIds=[9f916799-2ed1-4fec-8a53-d61180b5f44a], sourceTime=1721683373660, sourceSerialNo=null} 
[ERROR] 2024-07-23 05:24:01.105 - [任务 11][Test1] - java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-80) has been closed.
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapDeleteRecordEvent@6a8c7f51: {"before":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"c3681c47-cea4-40c7-aad9-2313d94910e9","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721683373660,"tableId":"dummy_test","time":1721683373660,"type":301}, nodeIds=[9f916799-2ed1-4fec-8a53-d61180b5f44a], sourceTime=1721683373660, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-80) has been closed.
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapDeleteRecordEvent@6a8c7f51: {"before":{"name6":"name6","name5":"name5","name4":"name4","name3":"name3","name9":"name9","name8":"name8","name7":"name7","id":"c3681c47-cea4-40c7-aad9-2313d94910e9","name2":"name2","name1":"name1"},"containsIllegalDate":false,"referenceTime":1721683373660,"tableId":"dummy_test","time":1721683373660,"type":301}, nodeIds=[9f916799-2ed1-4fec-8a53-d61180b5f44a], sourceTime=1721683373660, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-80) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:47)
	io.tapdata.connector.clickhouse.dml.ClickhouseRecordWriter.<init>(ClickhouseRecordWriter.java:13)
	io.tapdata.connector.clickhouse.ClickhouseConnector.writeRecord(ClickhouseConnector.java:310)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-80) has been closed.
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:632)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:601)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:582)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:494)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:500)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:546)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-80) has been closed.
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:798)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:498)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:498)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:624)
	... 12 more
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-80) has been closed.
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:47)
	at io.tapdata.connector.clickhouse.dml.ClickhouseRecordWriter.<init>(ClickhouseRecordWriter.java:13)
	at io.tapdata.connector.clickhouse.ClickhouseConnector.writeRecord(ClickhouseConnector.java:310)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:852)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 19 more
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-80) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 32 more

[INFO ] 2024-07-23 05:24:05.461 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-23 05:24:05.461 - [任务 11] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@56f4e472 
[INFO ] 2024-07-23 05:24:05.594 - [任务 11] - Stop task milestones: 669ebfc32f0fe71c3fdc1a38(任务 11)  
[INFO ] 2024-07-23 05:24:05.594 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-07-23 05:24:05.594 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-07-23 05:24:05.616 - [任务 11] - Remove memory task client succeed, task: 任务 11[669ebfc32f0fe71c3fdc1a38] 
[INFO ] 2024-07-23 05:24:05.617 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[669ebfc32f0fe71c3fdc1a38] 
[INFO ] 2024-07-23 05:25:12.507 - [任务 11] - Task initialization... 
[INFO ] 2024-07-23 05:25:12.619 - [任务 11] - Start task milestones: 669ebfc32f0fe71c3fdc1a38(任务 11) 
[INFO ] 2024-07-23 05:25:12.619 - [任务 11] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-23 05:25:12.685 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-23 05:25:12.686 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] start preload schema,table counts: 1 
[INFO ] 2024-07-23 05:25:12.686 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] start preload schema,table counts: 1 
[INFO ] 2024-07-23 05:25:12.686 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 05:25:12.686 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] preload schema finished, cost 1 ms 
[INFO ] 2024-07-23 05:25:13.414 - [任务 11][dummy_test] - Source node "dummy_test" read batch size: 10000 
[INFO ] 2024-07-23 05:25:13.415 - [任务 11][Test1] - Write batch size: 20000, max wait ms per batch: 500 
[INFO ] 2024-07-23 05:25:13.415 - [任务 11][dummy_test] - Source node "dummy_test" event queue capacity: 20000 
[INFO ] 2024-07-23 05:25:13.416 - [任务 11][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-23 05:25:13.419 - [任务 11][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721683513416,"lastTimes":1721683513416,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-23 05:25:13.467 - [任务 11][dummy_test] - Initial sync started 
[INFO ] 2024-07-23 05:25:13.467 - [任务 11][dummy_test] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-07-23 05:25:13.472 - [任务 11][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-07-23 05:25:13.481 - [任务 11][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-07-23 05:25:13.481 - [任务 11][dummy_test] - Query table 'dummy_test' counts: 10000000 
[INFO ] 2024-07-23 05:26:20.482 - [任务 11][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-07-23 05:26:20.483 - [任务 11][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-23 05:26:20.483 - [任务 11][dummy_test] - Initial sync completed 
[INFO ] 2024-07-23 05:26:20.483 - [任务 11][dummy_test] - Incremental sync starting... 
[INFO ] 2024-07-23 05:26:20.483 - [任务 11][dummy_test] - Initial sync completed 
[INFO ] 2024-07-23 05:26:20.503 - [任务 11][dummy_test] - Starting stream read, table list: [dummy_test], offset: {"syncStage":null,"beginTimes":1721683513416,"lastTimes":1721683513416,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-23 05:26:20.503 - [任务 11][dummy_test] - Start [dummy_test] stream read 
[INFO ] 2024-07-23 05:26:20.708 - [任务 11][dummy_test] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[WARN ] 2024-07-23 05:26:33.808 - [任务 11][Test1] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: java.lang.Throwable: Code: 420, e.displayText() = DB::Exception: Cannot UPDATE key column `id` (version 21.8.15.7)

	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:53)
	ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:28)
	ru.yandex.clickhouse.ClickHouseStatementImpl.checkForErrorAndThrow(ClickHouseStatementImpl.java:875)
	ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:616)
	ru.yandex.clickhouse.ClickHouseStatementImpl.executeUpdate(ClickHouseStatementImpl.java:215)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-23 05:27:33.825 - [任务 11][Test1] - Clickhouse Optimize Table start, tables: ["Test1"] 
[INFO ] 2024-07-23 05:27:57.592 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] running status set to false 
[INFO ] 2024-07-23 05:27:57.592 - [任务 11][dummy_test] - Compile [dummy_test] batch read 
[INFO ] 2024-07-23 05:27:57.593 - [任务 11][dummy_test] - Incremental sync completed 
[INFO ] 2024-07-23 05:27:57.593 - [任务 11][dummy_test] - Stop connector 
[INFO ] 2024-07-23 05:27:57.597 - [任务 11][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-9f916799-2ed1-4fec-8a53-d61180b5f44a 
[INFO ] 2024-07-23 05:27:57.600 - [任务 11][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-9f916799-2ed1-4fec-8a53-d61180b5f44a 
[INFO ] 2024-07-23 05:27:57.600 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] schema data cleaned 
[INFO ] 2024-07-23 05:27:57.600 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] monitor closed 
[INFO ] 2024-07-23 05:27:57.600 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] close complete, cost 8 ms 
[INFO ] 2024-07-23 05:27:57.600 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] running status set to false 
[INFO ] 2024-07-23 05:27:57.805 - [任务 11][Test1] - Clickhouse Optimize Table start, tables: ["Test1"] 
[INFO ] 2024-07-23 05:28:02.255 - [任务 11][Test1] - Clickhouse Optimize Table end 
[WARN ] 2024-07-23 05:28:02.284 - [任务 11][Test1] - Clickhouse Optimize Table failed 
[INFO ] 2024-07-23 05:28:02.297 - [任务 11][Test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-07fb72e8-b163-4a73-853d-38814e9ca021 
[INFO ] 2024-07-23 05:28:02.297 - [任务 11][Test1] - PDK connector node released: HazelcastTargetPdkDataNode-07fb72e8-b163-4a73-853d-38814e9ca021 
[INFO ] 2024-07-23 05:28:02.297 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] schema data cleaned 
[INFO ] 2024-07-23 05:28:02.298 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] monitor closed 
[INFO ] 2024-07-23 05:28:02.325 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] close complete, cost 4699 ms 
[INFO ] 2024-07-23 05:28:02.327 - [任务 11][Test1] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record 
[ERROR] 2024-07-23 05:28:02.534 - [任务 11][Test1] - java.lang.RuntimeException: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record <-- Full Stack Trace -->
java.lang.RuntimeException: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:556)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:632)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:601)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:582)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:531)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:494)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:500)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:546)
	... 6 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:798)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:498)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:498)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:624)
	... 12 more
Caused by: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:210)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:129)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 19 more
Caused by: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	... 23 more
Caused by: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:809)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 27 more

[INFO ] 2024-07-23 05:28:05.998 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-23 05:28:06.143 - [任务 11] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@723da8e3 
[INFO ] 2024-07-23 05:28:06.144 - [任务 11] - Stop task milestones: 669ebfc32f0fe71c3fdc1a38(任务 11)  
[INFO ] 2024-07-23 05:28:06.157 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-07-23 05:28:06.157 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-07-23 05:28:06.187 - [任务 11] - Remove memory task client succeed, task: 任务 11[669ebfc32f0fe71c3fdc1a38] 
[INFO ] 2024-07-23 05:28:06.187 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[669ebfc32f0fe71c3fdc1a38] 
[INFO ] 2024-07-23 05:28:35.825 - [任务 11] - Task initialization... 
[INFO ] 2024-07-23 05:28:35.968 - [任务 11] - Start task milestones: 669ebfc32f0fe71c3fdc1a38(任务 11) 
[INFO ] 2024-07-23 05:28:35.968 - [任务 11] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-23 05:28:36.024 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-23 05:28:36.025 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] start preload schema,table counts: 1 
[INFO ] 2024-07-23 05:28:36.025 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] start preload schema,table counts: 1 
[INFO ] 2024-07-23 05:28:36.028 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 05:28:36.028 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 05:28:36.631 - [任务 11][Test1] - Write batch size: 20000, max wait ms per batch: 500 
[INFO ] 2024-07-23 05:28:36.645 - [任务 11][dummy_test] - Source node "dummy_test" read batch size: 10000 
[INFO ] 2024-07-23 05:28:36.646 - [任务 11][dummy_test] - Source node "dummy_test" event queue capacity: 20000 
[INFO ] 2024-07-23 05:28:36.646 - [任务 11][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-23 05:28:36.646 - [任务 11][dummy_test] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-23 05:28:36.683 - [任务 11][dummy_test] - Initial sync started 
[INFO ] 2024-07-23 05:28:36.683 - [任务 11][dummy_test] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-07-23 05:28:36.692 - [任务 11][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-07-23 05:28:36.692 - [任务 11][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-07-23 05:28:36.897 - [任务 11][dummy_test] - Query table 'dummy_test' counts: 10000000 
[INFO ] 2024-07-23 05:29:47.720 - [任务 11][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-07-23 05:29:47.720 - [任务 11][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-23 05:29:47.720 - [任务 11][dummy_test] - Initial sync completed 
[INFO ] 2024-07-23 05:29:55.046 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] running status set to false 
[INFO ] 2024-07-23 05:29:55.059 - [任务 11][dummy_test] - Stop connector 
[INFO ] 2024-07-23 05:29:55.059 - [任务 11][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-9f916799-2ed1-4fec-8a53-d61180b5f44a 
[INFO ] 2024-07-23 05:29:55.060 - [任务 11][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-9f916799-2ed1-4fec-8a53-d61180b5f44a 
[INFO ] 2024-07-23 05:29:55.060 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] running status set to false 
[INFO ] 2024-07-23 05:29:55.060 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] schema data cleaned 
[INFO ] 2024-07-23 05:29:55.062 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] monitor closed 
[INFO ] 2024-07-23 05:29:55.062 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] close complete, cost 26 ms 
[INFO ] 2024-07-23 05:29:55.266 - [任务 11][Test1] - Clickhouse Optimize Table start, tables: ["Test1"] 
[INFO ] 2024-07-23 05:30:21.776 - [任务 11][Test1] - Clickhouse Optimize Table end 
[INFO ] 2024-07-23 05:30:21.776 - [任务 11][Test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-07fb72e8-b163-4a73-853d-38814e9ca021 
[INFO ] 2024-07-23 05:30:21.777 - [任务 11][Test1] - PDK connector node released: HazelcastTargetPdkDataNode-07fb72e8-b163-4a73-853d-38814e9ca021 
[INFO ] 2024-07-23 05:30:21.777 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] schema data cleaned 
[INFO ] 2024-07-23 05:30:21.778 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] monitor closed 
[INFO ] 2024-07-23 05:30:21.778 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] close complete, cost 26717 ms 
[INFO ] 2024-07-23 05:30:26.338 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-23 05:30:26.446 - [任务 11] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@35b7ec5 
[INFO ] 2024-07-23 05:30:26.446 - [任务 11] - Stop task milestones: 669ebfc32f0fe71c3fdc1a38(任务 11)  
[INFO ] 2024-07-23 05:30:26.458 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-07-23 05:30:26.458 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-07-23 05:30:26.494 - [任务 11] - Remove memory task client succeed, task: 任务 11[669ebfc32f0fe71c3fdc1a38] 
[INFO ] 2024-07-23 05:30:26.497 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[669ebfc32f0fe71c3fdc1a38] 
[INFO ] 2024-07-23 05:34:44.129 - [任务 11] - Task initialization... 
[INFO ] 2024-07-23 05:34:44.264 - [任务 11] - Start task milestones: 669ebfc32f0fe71c3fdc1a38(任务 11) 
[INFO ] 2024-07-23 05:34:44.264 - [任务 11] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-23 05:34:44.355 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-23 05:34:44.356 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] start preload schema,table counts: 1 
[INFO ] 2024-07-23 05:34:44.356 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] start preload schema,table counts: 1 
[INFO ] 2024-07-23 05:34:44.356 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 05:34:44.559 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] preload schema finished, cost 1 ms 
[INFO ] 2024-07-23 05:34:45.119 - [任务 11][dummy_test] - Source node "dummy_test" read batch size: 10000 
[INFO ] 2024-07-23 05:34:45.119 - [任务 11][dummy_test] - Source node "dummy_test" event queue capacity: 20000 
[INFO ] 2024-07-23 05:34:45.119 - [任务 11][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-23 05:34:45.120 - [任务 11][dummy_test] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-23 05:34:45.174 - [任务 11][Test1] - Write batch size: 20000, max wait ms per batch: 500 
[INFO ] 2024-07-23 05:34:45.174 - [任务 11][dummy_test] - Initial sync started 
[INFO ] 2024-07-23 05:34:45.174 - [任务 11][dummy_test] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-07-23 05:34:45.185 - [任务 11][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-07-23 05:34:45.185 - [任务 11][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-07-23 05:34:45.387 - [任务 11][dummy_test] - Query table 'dummy_test' counts: 10000000 
[INFO ] 2024-07-23 05:35:50.909 - [任务 11][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-07-23 05:35:50.910 - [任务 11][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-23 05:35:50.910 - [任务 11][dummy_test] - Initial sync completed 
[INFO ] 2024-07-23 05:35:58.031 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] running status set to false 
[INFO ] 2024-07-23 05:35:58.031 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] running status set to false 
[INFO ] 2024-07-23 05:35:58.032 - [任务 11][dummy_test] - Stop connector 
[INFO ] 2024-07-23 05:35:58.037 - [任务 11][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-9f916799-2ed1-4fec-8a53-d61180b5f44a 
[INFO ] 2024-07-23 05:35:58.037 - [任务 11][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-9f916799-2ed1-4fec-8a53-d61180b5f44a 
[INFO ] 2024-07-23 05:35:58.038 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] schema data cleaned 
[INFO ] 2024-07-23 05:35:58.038 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] monitor closed 
[INFO ] 2024-07-23 05:35:58.039 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] close complete, cost 11 ms 
[INFO ] 2024-07-23 05:35:58.039 - [任务 11][Test1] - Clickhouse Optimize Table start, tables: ["Test1"] 
[INFO ] 2024-07-23 05:36:27.467 - [任务 11][Test1] - Clickhouse Optimize Table end 
[INFO ] 2024-07-23 05:36:27.499 - [任务 11][Test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-07fb72e8-b163-4a73-853d-38814e9ca021 
[INFO ] 2024-07-23 05:36:27.499 - [任务 11][Test1] - PDK connector node released: HazelcastTargetPdkDataNode-07fb72e8-b163-4a73-853d-38814e9ca021 
[INFO ] 2024-07-23 05:36:27.499 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] schema data cleaned 
[INFO ] 2024-07-23 05:36:27.499 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] monitor closed 
[INFO ] 2024-07-23 05:36:27.502 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] close complete, cost 29472 ms 
[INFO ] 2024-07-23 05:36:31.825 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-23 05:36:31.826 - [任务 11] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1db3b037 
[INFO ] 2024-07-23 05:36:31.939 - [任务 11] - Stop task milestones: 669ebfc32f0fe71c3fdc1a38(任务 11)  
[INFO ] 2024-07-23 05:36:31.949 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-07-23 05:36:31.949 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-07-23 05:36:31.967 - [任务 11] - Remove memory task client succeed, task: 任务 11[669ebfc32f0fe71c3fdc1a38] 
[INFO ] 2024-07-23 05:36:31.968 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[669ebfc32f0fe71c3fdc1a38] 
[INFO ] 2024-07-23 05:39:49.975 - [任务 11] - Task initialization... 
[INFO ] 2024-07-23 05:39:50.082 - [任务 11] - Start task milestones: 669ebfc32f0fe71c3fdc1a38(任务 11) 
[INFO ] 2024-07-23 05:39:50.082 - [任务 11] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-23 05:39:50.157 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-23 05:39:50.157 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] start preload schema,table counts: 1 
[INFO ] 2024-07-23 05:39:50.157 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] start preload schema,table counts: 1 
[INFO ] 2024-07-23 05:39:50.157 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 05:39:50.157 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 05:39:50.744 - [任务 11][dummy_test] - Source node "dummy_test" read batch size: 10000 
[INFO ] 2024-07-23 05:39:50.745 - [任务 11][dummy_test] - Source node "dummy_test" event queue capacity: 20000 
[INFO ] 2024-07-23 05:39:50.745 - [任务 11][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-23 05:39:50.745 - [任务 11][dummy_test] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-23 05:39:50.819 - [任务 11][dummy_test] - Initial sync started 
[INFO ] 2024-07-23 05:39:50.826 - [任务 11][dummy_test] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-07-23 05:39:50.829 - [任务 11][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-07-23 05:39:50.829 - [任务 11][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-07-23 05:39:50.988 - [任务 11][dummy_test] - Query table 'dummy_test' counts: 50000000 
[INFO ] 2024-07-23 05:39:50.990 - [任务 11][Test1] - Write batch size: 20000, max wait ms per batch: 500 
[INFO ] 2024-07-23 05:46:09.529 - [任务 11][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-07-23 05:46:09.530 - [任务 11][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-23 05:46:09.531 - [任务 11][dummy_test] - Initial sync completed 
[INFO ] 2024-07-23 05:46:17.851 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] running status set to false 
[INFO ] 2024-07-23 05:46:17.856 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] running status set to false 
[INFO ] 2024-07-23 05:46:17.856 - [任务 11][dummy_test] - Stop connector 
[INFO ] 2024-07-23 05:46:17.864 - [任务 11][Test1] - Clickhouse Optimize Table start, tables: ["Test1"] 
[INFO ] 2024-07-23 05:46:17.866 - [任务 11][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-9f916799-2ed1-4fec-8a53-d61180b5f44a 
[INFO ] 2024-07-23 05:46:17.866 - [任务 11][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-9f916799-2ed1-4fec-8a53-d61180b5f44a 
[INFO ] 2024-07-23 05:46:17.866 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] schema data cleaned 
[INFO ] 2024-07-23 05:46:17.866 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] monitor closed 
[INFO ] 2024-07-23 05:46:18.075 - [任务 11][dummy_test] - Node dummy_test[9f916799-2ed1-4fec-8a53-d61180b5f44a] close complete, cost 22 ms 
[WARN ] 2024-07-23 05:46:47.905 - [任务 11][Test1] - Clickhouse Optimize Table failed 
[INFO ] 2024-07-23 05:46:47.906 - [任务 11][Test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-07fb72e8-b163-4a73-853d-38814e9ca021 
[INFO ] 2024-07-23 05:46:47.906 - [任务 11][Test1] - PDK connector node released: HazelcastTargetPdkDataNode-07fb72e8-b163-4a73-853d-38814e9ca021 
[INFO ] 2024-07-23 05:46:47.906 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] schema data cleaned 
[INFO ] 2024-07-23 05:46:47.906 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] monitor closed 
[INFO ] 2024-07-23 05:46:48.113 - [任务 11][Test1] - Node Test1[07fb72e8-b163-4a73-853d-38814e9ca021] close complete, cost 30055 ms 
[INFO ] 2024-07-23 05:46:52.782 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-23 05:46:52.782 - [任务 11] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@41898109 
[INFO ] 2024-07-23 05:46:52.924 - [任务 11] - Stop task milestones: 669ebfc32f0fe71c3fdc1a38(任务 11)  
[INFO ] 2024-07-23 05:46:52.924 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-07-23 05:46:52.924 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-07-23 05:46:52.939 - [任务 11] - Remove memory task client succeed, task: 任务 11[669ebfc32f0fe71c3fdc1a38] 
[INFO ] 2024-07-23 05:46:52.942 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[669ebfc32f0fe71c3fdc1a38] 
