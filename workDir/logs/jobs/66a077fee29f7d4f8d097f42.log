[INFO ] 2024-07-26 15:47:27.915 - [Heartbeat-LoginUserTest] - Start task milestones: 66a077fee29f7d4f8d097f42(Heartbeat-LoginUserTest) 
[INFO ] 2024-07-26 15:47:28.101 - [Heartbeat-LoginUserTest] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-26 15:47:28.102 - [Heartbeat-LoginUserTest] - The engine receives Heartbeat-LoginUserTest task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 15:47:28.164 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[8797ffa3-ba31-4a64-be43-f40cb174c480] start preload schema,table counts: 1 
[INFO ] 2024-07-26 15:47:28.164 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[8797ffa3-ba31-4a64-be43-f40cb174c480] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 15:47:28.183 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d676180c-1592-4529-9cde-bcafd75ce2cf] start preload schema,table counts: 1 
[INFO ] 2024-07-26 15:47:28.183 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d676180c-1592-4529-9cde-bcafd75ce2cf] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 15:47:28.323 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-26 15:47:28.326 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-26 15:47:28.326 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-26 15:47:28.326 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - batch offset found: {"_tapdata_heartbeat_table":{"batch_read_connector_offset":{"syncStage":"Initial","beginTimes":1721793185229,"lastTimes":1721793185237,"lastTN":1,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1,"updateTotals":0,"deleteTotals":0}}},"batch_read_connector_status":"RUNNING"}},stream offset found: {"syncStage":"Incremental","beginTimes":1721793185078,"lastTimes":1721793989818,"lastTN":741,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":740,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-26 15:47:28.326 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-26 15:47:28.383 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721793185078,"lastTimes":1721793989818,"lastTN":741,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":740,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-26 15:47:28.384 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-26 15:47:28.491 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 15:47:28.491 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-26 18:10:39.968 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d676180c-1592-4529-9cde-bcafd75ce2cf] running status set to false 
[INFO ] 2024-07-26 18:10:39.969 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-26 18:10:39.972 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-d676180c-1592-4529-9cde-bcafd75ce2cf 
[INFO ] 2024-07-26 18:10:39.973 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-d676180c-1592-4529-9cde-bcafd75ce2cf 
[INFO ] 2024-07-26 18:10:39.973 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d676180c-1592-4529-9cde-bcafd75ce2cf] schema data cleaned 
[INFO ] 2024-07-26 18:10:39.974 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d676180c-1592-4529-9cde-bcafd75ce2cf] monitor closed 
[INFO ] 2024-07-26 18:10:39.974 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d676180c-1592-4529-9cde-bcafd75ce2cf] close complete, cost 11 ms 
[INFO ] 2024-07-26 18:10:40.014 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[8797ffa3-ba31-4a64-be43-f40cb174c480] running status set to false 
[INFO ] 2024-07-26 18:10:40.018 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-8797ffa3-ba31-4a64-be43-f40cb174c480 
[INFO ] 2024-07-26 18:10:40.018 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-8797ffa3-ba31-4a64-be43-f40cb174c480 
[INFO ] 2024-07-26 18:10:40.019 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[8797ffa3-ba31-4a64-be43-f40cb174c480] schema data cleaned 
[INFO ] 2024-07-26 18:10:40.019 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[8797ffa3-ba31-4a64-be43-f40cb174c480] monitor closed 
[INFO ] 2024-07-26 18:10:40.019 - [Heartbeat-LoginUserTest][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[8797ffa3-ba31-4a64-be43-f40cb174c480] close complete, cost 41 ms 
[INFO ] 2024-07-26 18:10:42.034 - [Heartbeat-LoginUserTest] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 18:10:42.034 - [Heartbeat-LoginUserTest] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7ba612e8 
[INFO ] 2024-07-26 18:10:42.034 - [Heartbeat-LoginUserTest] - Stop task milestones: 66a077fee29f7d4f8d097f42(Heartbeat-LoginUserTest)  
[INFO ] 2024-07-26 18:10:42.156 - [Heartbeat-LoginUserTest] - Stopped task aspect(s) 
[INFO ] 2024-07-26 18:10:42.157 - [Heartbeat-LoginUserTest] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 18:10:42.184 - [Heartbeat-LoginUserTest] - Remove memory task client succeed, task: Heartbeat-LoginUserTest[66a077fee29f7d4f8d097f42] 
[INFO ] 2024-07-26 18:10:42.184 - [Heartbeat-LoginUserTest] - Destroy memory task client cache succeed, task: Heartbeat-LoginUserTest[66a077fee29f7d4f8d097f42] 
