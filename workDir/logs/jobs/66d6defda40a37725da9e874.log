[INFO ] 2024-09-03 18:04:03.784 - [任务 9] - Task initialization... 
[INFO ] 2024-09-03 18:04:03.789 - [任务 9] - Start task milestones: 66d6defda40a37725da9e874(任务 9) 
[INFO ] 2024-09-03 18:04:03.996 - [任务 9] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-09-03 18:04:04.069 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-03 18:04:04.069 - [任务 9][Mssql] - Node <PERSON>sql[c713df77-c5fa-4320-b904-14156641deaf] start preload schema,table counts: 1 
[INFO ] 2024-09-03 18:04:04.069 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] start preload schema,table counts: 1 
[INFO ] 2024-09-03 18:04:04.069 - [任务 9][Mssql] - Node <PERSON>[c713df77-c5fa-4320-b904-14156641deaf] preload schema finished, cost 1 ms 
[INFO ] 2024-09-03 18:04:04.069 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] preload schema finished, cost 1 ms 
[INFO ] 2024-09-03 18:04:04.833 - [任务 9][TargetMssql] - Node(TargetMssql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-03 18:04:04.833 - [任务 9][TargetMssql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-03 18:04:04.833 - [任务 9][Mssql] - Source node "Mssql" read batch size: 100 
[INFO ] 2024-09-03 18:04:04.833 - [任务 9][Mssql] - Source node "Mssql" event queue capacity: 200 
[INFO ] 2024-09-03 18:04:04.833 - [任务 9][Mssql] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-09-03 18:04:04.904 - [任务 9][TargetMssql] - Target connector does not support query index and will no longer synchronize indexes 
[INFO ] 2024-09-03 18:04:05.112 - [任务 9][Mssql] - building CT table for table hq_wz_base 
[INFO ] 2024-09-03 18:04:13.471 - [任务 9][Mssql] - batch offset found: {},stream offset found: {"currentStartLSN":null,"tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-09-03 18:04:13.472 - [任务 9][Mssql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-03 18:04:13.512 - [任务 9][Mssql] - Initial sync started 
[INFO ] 2024-09-03 18:04:13.513 - [任务 9][Mssql] - Starting batch read, table name: hq_wz_base 
[INFO ] 2024-09-03 18:04:13.559 - [任务 9][Mssql] - Table hq_wz_base is going to be initial synced 
[INFO ] 2024-09-03 18:04:13.559 - [任务 9][Mssql] - Query table 'hq_wz_base' counts: 0 
[INFO ] 2024-09-03 18:04:13.592 - [任务 9][Mssql] - Table [hq_wz_base] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-03 18:04:13.592 - [任务 9][Mssql] - Initial sync completed 
[INFO ] 2024-09-03 18:04:13.593 - [任务 9][Mssql] - Incremental sync starting... 
[INFO ] 2024-09-03 18:04:13.593 - [任务 9][Mssql] - Initial sync completed 
[INFO ] 2024-09-03 18:04:13.710 - [任务 9][Mssql] - Starting stream read, table list: [hq_wz_base], offset: {"currentStartLSN":null,"tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-09-03 18:04:13.711 - [任务 9][Mssql] - opened cdc tables: [hq_wz_base] 
[INFO ] 2024-09-03 18:04:13.888 - [任务 9][Mssql] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-09-03 18:04:13.888 - [任务 9][Mssql] - Connector SQL Server incremental start succeed, tables: [hq_wz_base], data change syncing 
[WARN ] 2024-09-03 18:04:14.505 - [任务 9][TargetMssql] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[c713df77-c5fa-4320-b904-14156641deaf], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-09-03 18:05:51.264 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] running status set to false 
[INFO ] 2024-09-03 18:05:51.877 - [任务 9][Mssql] - Incremental sync completed 
[INFO ] 2024-09-03 18:05:54.289 - [任务 9][Mssql] - PDK connector node stopped: HazelcastSourcePdkDataNode-c713df77-c5fa-4320-b904-14156641deaf 
[INFO ] 2024-09-03 18:05:54.289 - [任务 9][Mssql] - PDK connector node released: HazelcastSourcePdkDataNode-c713df77-c5fa-4320-b904-14156641deaf 
[INFO ] 2024-09-03 18:05:54.289 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] schema data cleaned 
[INFO ] 2024-09-03 18:05:54.290 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] monitor closed 
[INFO ] 2024-09-03 18:05:54.290 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] close complete, cost 3057 ms 
[INFO ] 2024-09-03 18:05:54.290 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] running status set to false 
[INFO ] 2024-09-03 18:05:54.295 - [任务 9][TargetMssql] - PDK connector node stopped: HazelcastTargetPdkDataNode-f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f 
[INFO ] 2024-09-03 18:05:54.295 - [任务 9][TargetMssql] - PDK connector node released: HazelcastTargetPdkDataNode-f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f 
[INFO ] 2024-09-03 18:05:54.295 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] schema data cleaned 
[INFO ] 2024-09-03 18:05:54.295 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] monitor closed 
[INFO ] 2024-09-03 18:05:54.501 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] close complete, cost 5 ms 
[INFO ] 2024-09-03 18:05:57.458 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-03 18:05:57.573 - [任务 9] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2a3f9093 
[INFO ] 2024-09-03 18:05:57.574 - [任务 9] - Stop task milestones: 66d6defda40a37725da9e874(任务 9)  
[INFO ] 2024-09-03 18:05:57.583 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-09-03 18:05:57.583 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-09-03 18:05:57.601 - [任务 9] - Remove memory task client succeed, task: 任务 9[66d6defda40a37725da9e874] 
[INFO ] 2024-09-03 18:05:57.601 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[66d6defda40a37725da9e874] 
[INFO ] 2024-09-03 18:06:14.399 - [任务 9] - Task initialization... 
[INFO ] 2024-09-03 18:06:14.402 - [任务 9] - Start task milestones: 66d6defda40a37725da9e874(任务 9) 
[INFO ] 2024-09-03 18:06:14.702 - [任务 9] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-03 18:06:14.702 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-03 18:06:14.789 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] start preload schema,table counts: 1 
[INFO ] 2024-09-03 18:06:14.789 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] start preload schema,table counts: 1 
[INFO ] 2024-09-03 18:06:14.790 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] preload schema finished, cost 0 ms 
[INFO ] 2024-09-03 18:06:14.790 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] preload schema finished, cost 0 ms 
[INFO ] 2024-09-03 18:06:15.455 - [任务 9][Mssql] - Source node "Mssql" read batch size: 100 
[INFO ] 2024-09-03 18:06:15.456 - [任务 9][Mssql] - Source node "Mssql" event queue capacity: 200 
[INFO ] 2024-09-03 18:06:15.456 - [任务 9][Mssql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-03 18:06:15.573 - [任务 9][TargetMssql] - Node(TargetMssql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-03 18:06:15.573 - [任务 9][TargetMssql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-03 18:06:15.588 - [任务 9][Mssql] - batch offset found: {},stream offset found: {"currentStartLSN":"00000026000008CA0001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-09-03 18:06:15.588 - [任务 9][Mssql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-03 18:06:15.637 - [任务 9][TargetMssql] - The table hq_wz_base has already exist. 
[INFO ] 2024-09-03 18:06:15.637 - [任务 9][Mssql] - Initial sync started 
[INFO ] 2024-09-03 18:06:15.637 - [任务 9][Mssql] - Starting batch read, table name: hq_wz_base 
[INFO ] 2024-09-03 18:06:15.638 - [任务 9][Mssql] - Table hq_wz_base is going to be initial synced 
[WARN ] 2024-09-03 18:06:15.676 - [任务 9][TargetMssql] - Table: hq_wz_base already exists and will no longer synchronize indexes 
[INFO ] 2024-09-03 18:06:15.676 - [任务 9][Mssql] - Table [hq_wz_base] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-03 18:06:15.730 - [任务 9][Mssql] - Query table 'hq_wz_base' counts: 0 
[INFO ] 2024-09-03 18:06:15.732 - [任务 9][Mssql] - Initial sync completed 
[INFO ] 2024-09-03 18:06:15.732 - [任务 9][Mssql] - Incremental sync starting... 
[INFO ] 2024-09-03 18:06:15.732 - [任务 9][Mssql] - Initial sync completed 
[INFO ] 2024-09-03 18:06:15.732 - [任务 9][Mssql] - Starting stream read, table list: [hq_wz_base], offset: {"currentStartLSN":"00000026000008CA0001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-09-03 18:06:15.939 - [任务 9][Mssql] - opened cdc tables: [hq_wz_base] 
[INFO ] 2024-09-03 18:06:15.955 - [任务 9][Mssql] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-09-03 18:06:15.960 - [任务 9][Mssql] - Connector SQL Server incremental start succeed, tables: [hq_wz_base], data change syncing 
[WARN ] 2024-09-03 18:06:17.170 - [任务 9][TargetMssql] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[c713df77-c5fa-4320-b904-14156641deaf], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-09-03 18:06:31.428 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] running status set to false 
[INFO ] 2024-09-03 18:06:31.635 - [任务 9][Mssql] - Incremental sync completed 
[INFO ] 2024-09-03 18:06:34.474 - [任务 9][Mssql] - PDK connector node stopped: HazelcastSourcePdkDataNode-c713df77-c5fa-4320-b904-14156641deaf 
[INFO ] 2024-09-03 18:06:34.475 - [任务 9][Mssql] - PDK connector node released: HazelcastSourcePdkDataNode-c713df77-c5fa-4320-b904-14156641deaf 
[INFO ] 2024-09-03 18:06:34.475 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] schema data cleaned 
[INFO ] 2024-09-03 18:06:34.475 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] monitor closed 
[INFO ] 2024-09-03 18:06:34.479 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] close complete, cost 3072 ms 
[INFO ] 2024-09-03 18:06:34.484 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] running status set to false 
[INFO ] 2024-09-03 18:06:34.487 - [任务 9][TargetMssql] - PDK connector node stopped: HazelcastTargetPdkDataNode-f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f 
[INFO ] 2024-09-03 18:06:34.487 - [任务 9][TargetMssql] - PDK connector node released: HazelcastTargetPdkDataNode-f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f 
[INFO ] 2024-09-03 18:06:34.487 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] schema data cleaned 
[INFO ] 2024-09-03 18:06:34.487 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] monitor closed 
[INFO ] 2024-09-03 18:06:34.692 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] close complete, cost 11 ms 
[INFO ] 2024-09-03 18:06:37.653 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-03 18:06:37.654 - [任务 9] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@73404db2 
[INFO ] 2024-09-03 18:06:37.792 - [任务 9] - Stop task milestones: 66d6defda40a37725da9e874(任务 9)  
[INFO ] 2024-09-03 18:06:37.792 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-09-03 18:06:37.792 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-09-03 18:06:37.810 - [任务 9] - Remove memory task client succeed, task: 任务 9[66d6defda40a37725da9e874] 
[INFO ] 2024-09-03 18:06:37.810 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[66d6defda40a37725da9e874] 
[INFO ] 2024-09-03 18:07:02.986 - [任务 9] - Task initialization... 
[INFO ] 2024-09-03 18:07:03.131 - [任务 9] - Start task milestones: 66d6defda40a37725da9e874(任务 9) 
[INFO ] 2024-09-03 18:07:03.131 - [任务 9] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-03 18:07:03.266 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-03 18:07:03.266 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] start preload schema,table counts: 1 
[INFO ] 2024-09-03 18:07:03.266 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] start preload schema,table counts: 1 
[INFO ] 2024-09-03 18:07:03.267 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] preload schema finished, cost 1 ms 
[INFO ] 2024-09-03 18:07:03.267 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] preload schema finished, cost 1 ms 
[INFO ] 2024-09-03 18:07:03.940 - [任务 9][TargetMssql] - Node(TargetMssql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-03 18:07:03.940 - [任务 9][TargetMssql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-03 18:07:04.011 - [任务 9][Mssql] - Source node "Mssql" read batch size: 100 
[INFO ] 2024-09-03 18:07:04.011 - [任务 9][Mssql] - Source node "Mssql" event queue capacity: 200 
[INFO ] 2024-09-03 18:07:04.011 - [任务 9][Mssql] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-09-03 18:08:05.266 - [任务 9][TargetMssql] - Target connector does not support query index and will no longer synchronize indexes 
[INFO ] 2024-09-03 18:08:05.453 - [任务 9][Mssql] - batch offset found: {},stream offset found: {"currentStartLSN":"00000026000008CA0001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-09-03 18:08:05.519 - [任务 9][Mssql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-03 18:08:05.520 - [任务 9][Mssql] - Initial sync started 
[INFO ] 2024-09-03 18:08:05.520 - [任务 9][Mssql] - Starting batch read, table name: hq_wz_base 
[INFO ] 2024-09-03 18:08:05.521 - [任务 9][Mssql] - Table hq_wz_base is going to be initial synced 
[INFO ] 2024-09-03 18:08:05.606 - [任务 9][Mssql] - Table [hq_wz_base] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-03 18:08:05.606 - [任务 9][Mssql] - Query table 'hq_wz_base' counts: 0 
[INFO ] 2024-09-03 18:08:05.606 - [任务 9][Mssql] - Initial sync completed 
[INFO ] 2024-09-03 18:08:05.607 - [任务 9][Mssql] - Incremental sync starting... 
[INFO ] 2024-09-03 18:08:05.607 - [任务 9][Mssql] - Initial sync completed 
[INFO ] 2024-09-03 18:08:05.699 - [任务 9][Mssql] - Starting stream read, table list: [hq_wz_base], offset: {"currentStartLSN":"00000026000008CA0001","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-09-03 18:08:05.699 - [任务 9][Mssql] - opened cdc tables: [hq_wz_base] 
[INFO ] 2024-09-03 18:08:05.822 - [任务 9][Mssql] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-09-03 18:08:06.023 - [任务 9][Mssql] - Connector SQL Server incremental start succeed, tables: [hq_wz_base], data change syncing 
[WARN ] 2024-09-03 18:08:06.834 - [任务 9][TargetMssql] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[c713df77-c5fa-4320-b904-14156641deaf], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-09-03 18:36:34.114 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] running status set to false 
[INFO ] 2024-09-03 18:36:34.649 - [任务 9][Mssql] - Incremental sync completed 
[INFO ] 2024-09-03 18:36:37.136 - [任务 9][Mssql] - PDK connector node stopped: HazelcastSourcePdkDataNode-c713df77-c5fa-4320-b904-14156641deaf 
[INFO ] 2024-09-03 18:36:37.136 - [任务 9][Mssql] - PDK connector node released: HazelcastSourcePdkDataNode-c713df77-c5fa-4320-b904-14156641deaf 
[INFO ] 2024-09-03 18:36:37.137 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] schema data cleaned 
[INFO ] 2024-09-03 18:36:37.137 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] monitor closed 
[INFO ] 2024-09-03 18:36:37.138 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] close complete, cost 3038 ms 
[INFO ] 2024-09-03 18:36:37.139 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] running status set to false 
[INFO ] 2024-09-03 18:36:37.142 - [任务 9][TargetMssql] - PDK connector node stopped: HazelcastTargetPdkDataNode-f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f 
[INFO ] 2024-09-03 18:36:37.142 - [任务 9][TargetMssql] - PDK connector node released: HazelcastTargetPdkDataNode-f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f 
[INFO ] 2024-09-03 18:36:37.144 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] schema data cleaned 
[INFO ] 2024-09-03 18:36:37.144 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] monitor closed 
[INFO ] 2024-09-03 18:36:37.349 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] close complete, cost 5 ms 
[INFO ] 2024-09-03 18:36:41.921 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-03 18:36:41.922 - [任务 9] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@47705ef9 
[INFO ] 2024-09-03 18:36:42.052 - [任务 9] - Stop task milestones: 66d6defda40a37725da9e874(任务 9)  
[INFO ] 2024-09-03 18:36:42.052 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-09-03 18:36:42.052 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-09-03 18:36:42.069 - [任务 9] - Remove memory task client succeed, task: 任务 9[66d6defda40a37725da9e874] 
[INFO ] 2024-09-03 18:36:42.072 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[66d6defda40a37725da9e874] 
[INFO ] 2024-09-03 18:40:04.778 - [任务 9] - Task initialization... 
[INFO ] 2024-09-03 18:40:04.944 - [任务 9] - Start task milestones: 66d6defda40a37725da9e874(任务 9) 
[INFO ] 2024-09-03 18:40:04.944 - [任务 9] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-03 18:40:05.050 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-03 18:40:05.050 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] start preload schema,table counts: 1 
[INFO ] 2024-09-03 18:40:05.050 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] start preload schema,table counts: 1 
[INFO ] 2024-09-03 18:40:05.050 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] preload schema finished, cost 0 ms 
[INFO ] 2024-09-03 18:40:05.050 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] preload schema finished, cost 0 ms 
[INFO ] 2024-09-03 18:40:05.791 - [任务 9][Mssql] - Source node "Mssql" read batch size: 100 
[INFO ] 2024-09-03 18:40:05.791 - [任务 9][Mssql] - Source node "Mssql" event queue capacity: 200 
[INFO ] 2024-09-03 18:40:05.791 - [任务 9][Mssql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-03 18:40:05.873 - [任务 9][TargetMssql] - Node(TargetMssql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-03 18:40:05.873 - [任务 9][TargetMssql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-03 18:40:05.873 - [任务 9][Mssql] - batch offset found: {},stream offset found: {"currentStartLSN":"0000002600000A930003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-09-03 18:40:05.873 - [任务 9][Mssql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-03 18:40:09.794 - [任务 9][Mssql] - Initial sync started 
[INFO ] 2024-09-03 18:40:09.795 - [任务 9][Mssql] - Starting batch read, table name: hq_wz_base 
[INFO ] 2024-09-03 18:40:09.795 - [任务 9][Mssql] - Table hq_wz_base is going to be initial synced 
[INFO ] 2024-09-03 18:40:09.860 - [任务 9][Mssql] - Table [hq_wz_base] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-03 18:40:32.954 - [任务 9][Mssql] - Query table 'hq_wz_base' counts: 0 
[INFO ] 2024-09-03 18:40:32.958 - [任务 9][Mssql] - Initial sync completed 
[INFO ] 2024-09-03 18:40:32.958 - [任务 9][Mssql] - Incremental sync starting... 
[INFO ] 2024-09-03 18:40:32.958 - [任务 9][Mssql] - Initial sync completed 
[INFO ] 2024-09-03 18:40:33.121 - [任务 9][Mssql] - Starting stream read, table list: [hq_wz_base], offset: {"currentStartLSN":"0000002600000A930003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-09-03 18:40:33.121 - [任务 9][Mssql] - opened cdc tables: [hq_wz_base] 
[INFO ] 2024-09-03 18:40:33.233 - [任务 9][Mssql] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-09-03 18:40:33.233 - [任务 9][Mssql] - Connector SQL Server incremental start succeed, tables: [hq_wz_base], data change syncing 
[WARN ] 2024-09-03 18:40:33.436 - [任务 9][TargetMssql] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[c713df77-c5fa-4320-b904-14156641deaf], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-09-03 18:41:34.547 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] running status set to false 
[INFO ] 2024-09-03 18:41:35.158 - [任务 9][Mssql] - Incremental sync completed 
[INFO ] 2024-09-03 18:41:37.511 - [任务 9][Mssql] - PDK connector node stopped: HazelcastSourcePdkDataNode-c713df77-c5fa-4320-b904-14156641deaf 
[INFO ] 2024-09-03 18:41:37.511 - [任务 9][Mssql] - PDK connector node released: HazelcastSourcePdkDataNode-c713df77-c5fa-4320-b904-14156641deaf 
[INFO ] 2024-09-03 18:41:37.511 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] schema data cleaned 
[INFO ] 2024-09-03 18:41:37.511 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] monitor closed 
[INFO ] 2024-09-03 18:41:37.517 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] close complete, cost 3043 ms 
[INFO ] 2024-09-03 18:41:37.518 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] running status set to false 
[INFO ] 2024-09-03 18:41:37.520 - [任务 9][TargetMssql] - PDK connector node stopped: HazelcastTargetPdkDataNode-f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f 
[INFO ] 2024-09-03 18:41:37.520 - [任务 9][TargetMssql] - PDK connector node released: HazelcastTargetPdkDataNode-f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f 
[INFO ] 2024-09-03 18:41:37.520 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] schema data cleaned 
[INFO ] 2024-09-03 18:41:37.520 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] monitor closed 
[INFO ] 2024-09-03 18:41:37.520 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] close complete, cost 7 ms 
[INFO ] 2024-09-03 18:41:38.061 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-03 18:41:38.062 - [任务 9] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3fc6df3b 
[INFO ] 2024-09-03 18:41:38.181 - [任务 9] - Stop task milestones: 66d6defda40a37725da9e874(任务 9)  
[INFO ] 2024-09-03 18:41:38.190 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-09-03 18:41:38.190 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-09-03 18:41:38.209 - [任务 9] - Remove memory task client succeed, task: 任务 9[66d6defda40a37725da9e874] 
[INFO ] 2024-09-03 18:41:38.209 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[66d6defda40a37725da9e874] 
[INFO ] 2024-09-03 18:57:20.567 - [任务 9] - Task initialization... 
[INFO ] 2024-09-03 18:57:20.568 - [任务 9] - Start task milestones: 66d6defda40a37725da9e874(任务 9) 
[INFO ] 2024-09-03 18:57:20.732 - [任务 9] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-03 18:57:20.732 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-03 18:57:20.782 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] start preload schema,table counts: 1 
[INFO ] 2024-09-03 18:57:20.782 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] start preload schema,table counts: 1 
[INFO ] 2024-09-03 18:57:20.782 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] preload schema finished, cost 0 ms 
[INFO ] 2024-09-03 18:57:20.782 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] preload schema finished, cost 1 ms 
[INFO ] 2024-09-03 18:57:21.412 - [任务 9][TargetMssql] - Node(TargetMssql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-03 18:57:21.412 - [任务 9][TargetMssql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-03 18:57:21.498 - [任务 9][Mssql] - Source node "Mssql" read batch size: 100 
[INFO ] 2024-09-03 18:57:21.498 - [任务 9][Mssql] - Source node "Mssql" event queue capacity: 200 
[INFO ] 2024-09-03 18:57:21.498 - [任务 9][Mssql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-03 18:57:21.561 - [任务 9][Mssql] - batch offset found: {},stream offset found: {"currentStartLSN":"0000002600000B4D0003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-09-03 18:57:21.561 - [任务 9][Mssql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-03 18:57:21.662 - [任务 9] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-09-03 18:57:21.662 - [任务 9][Mssql] - Initial sync started 
[INFO ] 2024-09-03 18:57:21.662 - [任务 9][Mssql] - Starting batch read, table name: hq_wz_base 
[INFO ] 2024-09-03 18:57:21.748 - [任务 9][Mssql] - Table hq_wz_base is going to be initial synced 
[INFO ] 2024-09-03 18:57:21.748 - [任务 9][Mssql] - Table [hq_wz_base] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-03 18:57:21.771 - [任务 9][Mssql] - Query table 'hq_wz_base' counts: 0 
[INFO ] 2024-09-03 18:57:21.771 - [任务 9][Mssql] - Initial sync completed 
[INFO ] 2024-09-03 18:57:21.771 - [任务 9][Mssql] - Incremental sync starting... 
[INFO ] 2024-09-03 18:57:21.771 - [任务 9][Mssql] - Initial sync completed 
[INFO ] 2024-09-03 18:57:21.856 - [任务 9][Mssql] - Starting stream read, table list: [hq_wz_base], offset: {"currentStartLSN":"0000002600000B4D0003","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-09-03 18:57:21.856 - [任务 9][Mssql] - opened cdc tables: [hq_wz_base] 
[INFO ] 2024-09-03 18:57:21.984 - [任务 9][Mssql] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-09-03 18:57:22.187 - [任务 9][Mssql] - Connector SQL Server incremental start succeed, tables: [hq_wz_base], data change syncing 
[WARN ] 2024-09-03 18:57:22.423 - [任务 9][TargetMssql] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[c713df77-c5fa-4320-b904-14156641deaf], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-09-03 18:57:42.038 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] running status set to false 
[INFO ] 2024-09-03 18:57:42.039 - [任务 9][Mssql] - Incremental sync completed 
[INFO ] 2024-09-03 18:57:45.067 - [任务 9][Mssql] - PDK connector node stopped: HazelcastSourcePdkDataNode-c713df77-c5fa-4320-b904-14156641deaf 
[INFO ] 2024-09-03 18:57:45.067 - [任务 9][Mssql] - PDK connector node released: HazelcastSourcePdkDataNode-c713df77-c5fa-4320-b904-14156641deaf 
[INFO ] 2024-09-03 18:57:45.067 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] schema data cleaned 
[INFO ] 2024-09-03 18:57:45.068 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] monitor closed 
[INFO ] 2024-09-03 18:57:45.071 - [任务 9][Mssql] - Node Mssql[c713df77-c5fa-4320-b904-14156641deaf] close complete, cost 3051 ms 
[INFO ] 2024-09-03 18:57:45.071 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] running status set to false 
[INFO ] 2024-09-03 18:57:45.078 - [任务 9][TargetMssql] - PDK connector node stopped: HazelcastTargetPdkDataNode-f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f 
[INFO ] 2024-09-03 18:57:45.078 - [任务 9][TargetMssql] - PDK connector node released: HazelcastTargetPdkDataNode-f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f 
[INFO ] 2024-09-03 18:57:45.078 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] schema data cleaned 
[INFO ] 2024-09-03 18:57:45.078 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] monitor closed 
[INFO ] 2024-09-03 18:57:45.285 - [任务 9][TargetMssql] - Node TargetMssql[f5a546f0-3fa6-4d54-b455-6a5ca74f7f5f] close complete, cost 8 ms 
[INFO ] 2024-09-03 18:57:48.982 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-03 18:57:49.094 - [任务 9] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5b9d596f 
[INFO ] 2024-09-03 18:57:49.094 - [任务 9] - Stop task milestones: 66d6defda40a37725da9e874(任务 9)  
[INFO ] 2024-09-03 18:57:49.107 - [任务 9] - Stopped task aspect(s) 
[INFO ] 2024-09-03 18:57:49.107 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2024-09-03 18:57:49.152 - [任务 9] - Remove memory task client succeed, task: 任务 9[66d6defda40a37725da9e874] 
[INFO ] 2024-09-03 18:57:49.154 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[66d6defda40a37725da9e874] 
