[INFO ] 2024-06-24 13:20:25.440 - [任务 17] - Task initialization... 
[INFO ] 2024-06-24 13:20:25.652 - [任务 17] - Start task milestones: 667901f7e2fb9a28343e5956(任务 17) 
[INFO ] 2024-06-24 13:20:25.814 - [任务 17] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-06-24 13:20:25.816 - [任务 17] - The engine receives 任务 17 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-24 13:20:25.895 - [任务 17][SouceMysql] - Node SouceMysql[8514cf5e-fe69-4da1-bb8d-34b648752916] start preload schema,table counts: 1 
[INFO ] 2024-06-24 13:20:25.909 - [任务 17][表编辑] - Node 表编辑[5319c2eb-35e0-431c-9ae4-cd7604905957] start preload schema,table counts: 1 
[INFO ] 2024-06-24 13:20:25.910 - [任务 17][SourceMongo] - Node SourceMongo[85b64ff9-ccfc-4ce5-b159-f3919f4c005f] start preload schema,table counts: 1 
[INFO ] 2024-06-24 13:20:25.910 - [任务 17][SourceMongo] - Node SourceMongo[85b64ff9-ccfc-4ce5-b159-f3919f4c005f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 13:20:25.911 - [任务 17][表编辑] - Node 表编辑[5319c2eb-35e0-431c-9ae4-cd7604905957] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 13:20:25.911 - [任务 17][SouceMysql] - Node SouceMysql[8514cf5e-fe69-4da1-bb8d-34b648752916] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 13:20:26.558 - [任务 17][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-06-24 13:20:26.560 - [任务 17][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-06-24 13:20:26.560 - [任务 17][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-24 13:20:26.768 - [任务 17][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719206426,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-24 13:20:26.784 - [任务 17][SourceMongo] - Initial sync started 
[INFO ] 2024-06-24 13:20:26.785 - [任务 17][SourceMongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-24 13:20:26.826 - [任务 17][SourceMongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-24 13:20:26.826 - [任务 17][SourceMongo] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-06-24 13:20:26.938 - [任务 17][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-24 13:20:26.947 - [任务 17][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-24 13:20:30.675 - [任务 17][SourceMongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-24 13:20:30.678 - [任务 17][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-24 13:20:30.678 - [任务 17][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-06-24 13:20:30.678 - [任务 17][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-24 13:20:30.680 - [任务 17][SourceMongo] - Starting stream read, table list: [CLAIM], offset: {"cdcOffset":1719206426,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-24 13:20:30.883 - [任务 17][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-24 16:35:33.153 - [任务 17][SourceMongo] - Node SourceMongo[85b64ff9-ccfc-4ce5-b159-f3919f4c005f] running status set to false 
[INFO ] 2024-06-24 16:35:33.607 - [任务 17] - Task initialization... 
[INFO ] 2024-06-24 16:35:33.608 - [任务 17] - Start task milestones: 667901f7e2fb9a28343e5956(任务 17) 
[INFO ] 2024-06-24 16:35:34.628 - [任务 17] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-24 16:35:34.729 - [任务 17] - The engine receives 任务 17 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-24 16:35:35.374 - [任务 17][表编辑] - Node 表编辑[5319c2eb-35e0-431c-9ae4-cd7604905957] start preload schema,table counts: 1 
[INFO ] 2024-06-24 16:35:35.375 - [任务 17][表编辑] - Node 表编辑[5319c2eb-35e0-431c-9ae4-cd7604905957] preload schema finished, cost 1 ms 
[INFO ] 2024-06-24 16:35:35.423 - [任务 17][SouceMysql] - Node SouceMysql[8514cf5e-fe69-4da1-bb8d-34b648752916] start preload schema,table counts: 1 
[INFO ] 2024-06-24 16:35:35.425 - [任务 17][SourceMongo] - Node SourceMongo[85b64ff9-ccfc-4ce5-b159-f3919f4c005f] start preload schema,table counts: 1 
[INFO ] 2024-06-24 16:35:35.427 - [任务 17][SouceMysql] - Node SouceMysql[8514cf5e-fe69-4da1-bb8d-34b648752916] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 16:35:35.430 - [任务 17][SourceMongo] - Node SourceMongo[85b64ff9-ccfc-4ce5-b159-f3919f4c005f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 16:35:35.917 - [任务 17][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-06-24 16:35:35.918 - [任务 17][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-06-24 16:35:35.924 - [任务 17][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-24 16:35:35.956 - [任务 17][SourceMongo] - batch offset found: {"CLAIM":{"batch_read_connector_offset":{"sortKey":"_id","value":"657baffa62a1bacd962c870f","objectId":null},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1719206426,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-24 16:35:36.019 - [任务 17][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-06-24 16:35:36.019 - [任务 17][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-24 16:35:36.029 - [任务 17][SourceMongo] - Starting stream read, table list: [CLAIM], offset: {"cdcOffset":1719206426,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-24 16:35:36.093 - [任务 17][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-24 16:35:36.123 - [任务 17][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-24 16:35:36.288 - [任务 17][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-24 16:58:06.809 - [任务 17] - Task initialization... 
[INFO ] 2024-06-24 16:58:06.966 - [任务 17] - Start task milestones: 667901f7e2fb9a28343e5956(任务 17) 
[INFO ] 2024-06-24 16:58:08.146 - [任务 17] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-24 16:58:08.147 - [任务 17] - The engine receives 任务 17 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-24 16:58:08.969 - [任务 17][SourceMongo] - Node SourceMongo[85b64ff9-ccfc-4ce5-b159-f3919f4c005f] start preload schema,table counts: 1 
[INFO ] 2024-06-24 16:58:08.993 - [任务 17][SouceMysql] - Node SouceMysql[8514cf5e-fe69-4da1-bb8d-34b648752916] start preload schema,table counts: 1 
[INFO ] 2024-06-24 16:58:09.016 - [任务 17][SourceMongo] - Node SourceMongo[85b64ff9-ccfc-4ce5-b159-f3919f4c005f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 16:58:09.029 - [任务 17][SouceMysql] - Node SouceMysql[8514cf5e-fe69-4da1-bb8d-34b648752916] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 16:58:09.034 - [任务 17][表编辑] - Node 表编辑[5319c2eb-35e0-431c-9ae4-cd7604905957] start preload schema,table counts: 1 
[INFO ] 2024-06-24 16:58:09.034 - [任务 17][表编辑] - Node 表编辑[5319c2eb-35e0-431c-9ae4-cd7604905957] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 16:58:09.415 - [任务 17][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-06-24 16:58:09.415 - [任务 17][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-06-24 16:58:09.421 - [任务 17][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-24 16:58:09.532 - [任务 17][SourceMongo] - batch offset found: {"CLAIM":{"batch_read_connector_offset":{"sortKey":"_id","value":"657baffa62a1bacd962c870f","objectId":null},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1719206426,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-24 16:58:09.562 - [任务 17][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-06-24 16:58:09.562 - [任务 17][SourceMongo] - Initial sync completed 
[INFO ] 2024-06-24 16:58:09.597 - [任务 17][SourceMongo] - Starting stream read, table list: [CLAIM], offset: {"cdcOffset":1719206426,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-24 16:58:09.862 - [任务 17][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-24 16:58:09.903 - [任务 17][SourceMongo] - Incremental sync completed 
[INFO ] 2024-06-24 16:58:09.903 - [任务 17][SourceMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: Increment start point exceeds the log time window of mongodb, start point: 1719206426 
[INFO ] 2024-06-24 16:58:09.964 - [任务 17][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[ERROR] 2024-06-24 16:58:09.965 - [任务 17][SourceMongo] - com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1719219489, "i": 75}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1719219489, "i": 75}}, "signature": {"hash": {"$binary": {"base64": "mgwS9ornbnVn/QO9cp4+7ONJs34=", "subType": "00"}}, "keyId": 7329804974198620162}}} <-- Error Message -->
com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1719219489, "i": 75}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1719219489, "i": 75}}, "signature": {"hash": {"$binary": {"base64": "mgwS9ornbnVn/QO9cp4+7ONJs34=", "subType": "00"}}, "keyId": 7329804974198620162}}}

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1719219489, "i": 75}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1719219489, "i": 75}}, "signature": {"hash": {"$binary": {"base64": "mgwS9ornbnVn/QO9cp4+7ONJs34=", "subType": "00"}}, "keyId": 7329804974198620162}}}
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:114)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:643)
	...

<-- Full Stack Trace -->
com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1719219489, "i": 75}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1719219489, "i": 75}}, "signature": {"hash": {"$binary": {"base64": "mgwS9ornbnVn/QO9cp4+7ONJs34=", "subType": "00"}}, "keyId": 7329804974198620162}}}
	at io.tapdata.mongodb.reader.MongodbV4StreamReader.read(MongodbV4StreamReader.java:265)
	at io.tapdata.mongodb.MongodbConnector.doStreamRead(MongodbConnector.java:1542)
	at io.tapdata.mongodb.MongodbConnector.streamRead(MongodbConnector.java:1536)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1719219489, "i": 75}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1719219489, "i": 75}}, "signature": {"hash": {"$binary": {"base64": "mgwS9ornbnVn/QO9cp4+7ONJs34=", "subType": "00"}}, "keyId": 7329804974198620162}}}
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:114)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:643)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:73)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:204)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:122)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:87)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:76)
	at com.mongodb.internal.connection.DefaultServer$OperationCountTrackingConnection.command(DefaultServer.java:288)
	at com.mongodb.internal.operation.CommandOperationHelper.createReadCommandAndExecute(CommandOperationHelper.java:239)
	at com.mongodb.internal.operation.CommandOperationHelper.lambda$executeRetryableRead$4(CommandOperationHelper.java:220)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$0(OperationHelper.java:358)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$1(OperationHelper.java:357)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.withSourceAndConnection(OperationHelper.java:356)
	at com.mongodb.internal.operation.CommandOperationHelper.lambda$executeRetryableRead$5(CommandOperationHelper.java:218)
	at com.mongodb.internal.async.function.RetryingSyncSupplier.get(RetryingSyncSupplier.java:67)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:223)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:204)
	at com.mongodb.internal.operation.AggregateOperationImpl.execute(AggregateOperationImpl.java:191)
	at com.mongodb.internal.operation.ChangeStreamOperation.lambda$execute$0(ChangeStreamOperation.java:187)
	at com.mongodb.internal.operation.OperationHelper.withReadConnectionSource(OperationHelper.java:321)
	at com.mongodb.internal.operation.ChangeStreamOperation.execute(ChangeStreamOperation.java:185)
	at com.mongodb.internal.operation.ChangeStreamOperation.execute(ChangeStreamOperation.java:55)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:185)
	at com.mongodb.client.internal.ChangeStreamIterableImpl.execute(ChangeStreamIterableImpl.java:212)
	at com.mongodb.client.internal.ChangeStreamIterableImpl.cursor(ChangeStreamIterableImpl.java:187)
	at io.tapdata.mongodb.reader.MongodbV4StreamReader.read(MongodbV4StreamReader.java:111)
	... 22 more

[INFO ] 2024-06-24 16:58:09.965 - [任务 17][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-24 16:58:10.017 - [任务 17][SourceMongo] - Job suspend in error handle 
[INFO ] 2024-06-24 16:58:10.334 - [任务 17][SourceMongo] - Node SourceMongo[85b64ff9-ccfc-4ce5-b159-f3919f4c005f] running status set to false 
[INFO ] 2024-06-24 16:58:10.335 - [任务 17][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-85b64ff9-ccfc-4ce5-b159-f3919f4c005f 
[INFO ] 2024-06-24 16:58:10.336 - [任务 17][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-85b64ff9-ccfc-4ce5-b159-f3919f4c005f 
[INFO ] 2024-06-24 16:58:10.336 - [任务 17][SourceMongo] - Node SourceMongo[85b64ff9-ccfc-4ce5-b159-f3919f4c005f] schema data cleaned 
[INFO ] 2024-06-24 16:58:10.347 - [任务 17][SourceMongo] - Node SourceMongo[85b64ff9-ccfc-4ce5-b159-f3919f4c005f] monitor closed 
[INFO ] 2024-06-24 16:58:10.347 - [任务 17][SourceMongo] - Node SourceMongo[85b64ff9-ccfc-4ce5-b159-f3919f4c005f] close complete, cost 51 ms 
[INFO ] 2024-06-24 16:58:10.351 - [任务 17][表编辑] - Node 表编辑[5319c2eb-35e0-431c-9ae4-cd7604905957] running status set to false 
[INFO ] 2024-06-24 16:58:10.351 - [任务 17][表编辑] - Node 表编辑[5319c2eb-35e0-431c-9ae4-cd7604905957] schema data cleaned 
[INFO ] 2024-06-24 16:58:10.352 - [任务 17][表编辑] - Node 表编辑[5319c2eb-35e0-431c-9ae4-cd7604905957] monitor closed 
[INFO ] 2024-06-24 16:58:10.352 - [任务 17][表编辑] - Node 表编辑[5319c2eb-35e0-431c-9ae4-cd7604905957] close complete, cost 4 ms 
[INFO ] 2024-06-24 16:58:10.417 - [任务 17][SouceMysql] - Node SouceMysql[8514cf5e-fe69-4da1-bb8d-34b648752916] running status set to false 
[INFO ] 2024-06-24 16:58:10.420 - [任务 17][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-8514cf5e-fe69-4da1-bb8d-34b648752916 
[INFO ] 2024-06-24 16:58:10.421 - [任务 17][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-8514cf5e-fe69-4da1-bb8d-34b648752916 
[INFO ] 2024-06-24 16:58:10.421 - [任务 17][SouceMysql] - Node SouceMysql[8514cf5e-fe69-4da1-bb8d-34b648752916] schema data cleaned 
[INFO ] 2024-06-24 16:58:10.421 - [任务 17][SouceMysql] - Node SouceMysql[8514cf5e-fe69-4da1-bb8d-34b648752916] monitor closed 
[INFO ] 2024-06-24 16:58:10.636 - [任务 17][SouceMysql] - Node SouceMysql[8514cf5e-fe69-4da1-bb8d-34b648752916] close complete, cost 68 ms 
[INFO ] 2024-06-24 16:58:11.929 - [任务 17] - Task [任务 17] cannot retry, reason: Task retry service not start 
[INFO ] 2024-06-24 16:58:11.954 - [任务 17] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-24 16:58:11.955 - [任务 17] - Stop task milestones: 667901f7e2fb9a28343e5956(任务 17)  
[INFO ] 2024-06-24 16:58:11.975 - [任务 17] - Stopped task aspect(s) 
[INFO ] 2024-06-24 16:58:11.976 - [任务 17] - Snapshot order controller have been removed 
[INFO ] 2024-06-24 16:58:12.004 - [任务 17] - Remove memory task client succeed, task: 任务 17[667901f7e2fb9a28343e5956] 
[INFO ] 2024-06-24 16:58:12.004 - [任务 17] - Destroy memory task client cache succeed, task: 任务 17[667901f7e2fb9a28343e5956] 
