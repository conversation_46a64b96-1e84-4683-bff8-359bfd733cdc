[INFO ] 2024-05-31 15:47:53.769 - [任务 4 - Co<PERSON>(100)][8101474f-50da-4d52-95a9-797ee5a72bde] - Node 8101474f-50da-4d52-95a9-797ee5a72bde[8101474f-50da-4d52-95a9-797ee5a72bde] start preload schema,table counts: 0 
[INFO ] 2024-05-31 15:47:53.769 - [任务 4 - Copy(100)][products] - Node products[88558a73-4c45-495f-a6f5-5bcd9f4d1d45] start preload schema,table counts: 1 
[INFO ] 2024-05-31 15:47:53.770 - [任务 4 - Copy(100)][增强JS] - Node 增强JS[8296f71d-6f2c-4dd4-bd4f-8271374b44e0] start preload schema,table counts: 1 
[INFO ] 2024-05-31 15:47:53.770 - [任务 4 - Copy(100)][products] - Node products[88558a73-4c45-495f-a6f5-5bcd9f4d1d45] preload schema finished, cost 0 ms 
[INFO ] 2024-05-31 15:47:53.770 - [任务 4 - Copy(100)][增强JS] - Node 增强JS[8296f71d-6f2c-4dd4-bd4f-8271374b44e0] preload schema finished, cost 0 ms 
[INFO ] 2024-05-31 15:47:53.770 - [任务 4 - Copy(100)][8101474f-50da-4d52-95a9-797ee5a72bde] - Node 8101474f-50da-4d52-95a9-797ee5a72bde[8101474f-50da-4d52-95a9-797ee5a72bde] preload schema finished, cost 1 ms 
[INFO ] 2024-05-31 15:47:54.861 - [任务 4 - Copy(100)][products] - Node products[88558a73-4c45-495f-a6f5-5bcd9f4d1d45] running status set to false 
[INFO ] 2024-05-31 15:47:54.872 - [任务 4 - Copy(100)][products] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-88558a73-4c45-495f-a6f5-5bcd9f4d1d45 
[INFO ] 2024-05-31 15:47:54.872 - [任务 4 - Copy(100)][products] - PDK connector node released: HazelcastSampleSourcePdkDataNode-88558a73-4c45-495f-a6f5-5bcd9f4d1d45 
[INFO ] 2024-05-31 15:47:54.872 - [任务 4 - Copy(100)][products] - Node products[88558a73-4c45-495f-a6f5-5bcd9f4d1d45] schema data cleaned 
[INFO ] 2024-05-31 15:47:54.873 - [任务 4 - Copy(100)][products] - Node products[88558a73-4c45-495f-a6f5-5bcd9f4d1d45] monitor closed 
[INFO ] 2024-05-31 15:47:54.932 - [任务 4 - Copy(100)][products] - Node products[88558a73-4c45-495f-a6f5-5bcd9f4d1d45] close complete, cost 20 ms 
[INFO ] 2024-05-31 15:47:54.932 - [任务 4 - Copy(100)][增强JS] - {id=1, name=product1, category=category1, price=10.00, inventory=100} 
[INFO ] 2024-05-31 15:47:54.955 - [任务 4 - Copy(100)][增强JS] - Node 增强JS[8296f71d-6f2c-4dd4-bd4f-8271374b44e0] running status set to false 
[INFO ] 2024-05-31 15:47:54.955 - [任务 4 - Copy(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourcePg-f4d18724-d215-4f1f-8141-ddb012dbb57c 
[INFO ] 2024-05-31 15:47:54.957 - [任务 4 - Copy(100)][增强JS] - PDK connector node released: ScriptExecutor-SourcePg-f4d18724-d215-4f1f-8141-ddb012dbb57c 
[INFO ] 2024-05-31 15:47:54.957 - [任务 4 - Copy(100)][增强JS] - [ScriptExecutorsManager-665980a901ddf7118846fa5c-8296f71d-6f2c-4dd4-bd4f-8271374b44e0-66594042f6745e7b93674806] schema data cleaned 
[INFO ] 2024-05-31 15:47:54.961 - [任务 4 - Copy(100)][增强JS] - Node 增强JS[8296f71d-6f2c-4dd4-bd4f-8271374b44e0] schema data cleaned 
[INFO ] 2024-05-31 15:47:54.961 - [任务 4 - Copy(100)][增强JS] - Node 增强JS[8296f71d-6f2c-4dd4-bd4f-8271374b44e0] monitor closed 
[INFO ] 2024-05-31 15:47:54.961 - [任务 4 - Copy(100)][增强JS] - Node 增强JS[8296f71d-6f2c-4dd4-bd4f-8271374b44e0] close complete, cost 26 ms 
[INFO ] 2024-05-31 15:47:54.982 - [任务 4 - Copy(100)][8101474f-50da-4d52-95a9-797ee5a72bde] - Node 8101474f-50da-4d52-95a9-797ee5a72bde[8101474f-50da-4d52-95a9-797ee5a72bde] running status set to false 
[INFO ] 2024-05-31 15:47:54.982 - [任务 4 - Copy(100)][8101474f-50da-4d52-95a9-797ee5a72bde] - Node 8101474f-50da-4d52-95a9-797ee5a72bde[8101474f-50da-4d52-95a9-797ee5a72bde] schema data cleaned 
[INFO ] 2024-05-31 15:47:54.982 - [任务 4 - Copy(100)][8101474f-50da-4d52-95a9-797ee5a72bde] - Node 8101474f-50da-4d52-95a9-797ee5a72bde[8101474f-50da-4d52-95a9-797ee5a72bde] monitor closed 
[INFO ] 2024-05-31 15:47:54.982 - [任务 4 - Copy(100)][8101474f-50da-4d52-95a9-797ee5a72bde] - Node 8101474f-50da-4d52-95a9-797ee5a72bde[8101474f-50da-4d52-95a9-797ee5a72bde] close complete, cost 1 ms 
[INFO ] 2024-05-31 15:47:55.190 - [任务 4 - Copy(100)] - load tapTable task 665980a901ddf7118846fa5c-8101474f-50da-4d52-95a9-797ee5a72bde complete, cost 1328ms 
