[INFO ] 2024-09-27 16:06:37.768 - [任务 5] - Start task milestones: 66f6677483aeee2f9d5811a1(任务 5) 
[INFO ] 2024-09-27 16:06:37.871 - [任务 5] - Task initialization... 
[INFO ] 2024-09-27 16:06:37.871 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 16:06:37.997 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 16:06:37.998 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:06:37.998 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] preload schema finished, cost 1 ms 
[INFO ] 2024-09-27 16:06:38.182 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:06:38.183 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 16:06:38.183 - [任务 5][SourceMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_968d62dd-1c2b-4e93-878a-1ac0cd70d32e 
[ERROR] 2024-09-27 16:06:38.252 - [任务 5][SourceMongo] - Map name: PdkStateMap_968d62dd-1c2b-4e93-878a-1ac0cd70d32e <-- Error Message -->
Map name: PdkStateMap_968d62dd-1c2b-4e93-878a-1ac0cd70d32e

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1727424398, "i" : 1 } }, "signature" : { "hash" : { "$binary" : "PeU695q5VsMQ4O+6+vsPdc2T2CE=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1727424398, "i" : 1 } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_968d62dd-1c2b-4e93-878a-1ac0cd70d32e
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:200)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:452)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:185)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 13 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 18 more
Caused by: com.mongodb.MongoSecurityException: Exception authenticating MongoCredential{mechanism=SCRAM-SHA-256, userName='wim', source='admin', password=<hidden>, mechanismProperties={}}
	at com.mongodb.internal.connection.SaslAuthenticator.wrapException(SaslAuthenticator.java:173)
	at com.mongodb.internal.connection.SaslAuthenticator.access$300(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:70)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.SaslAuthenticator.doAsSubject(SaslAuthenticator.java:179)
	at com.mongodb.internal.connection.SaslAuthenticator.authenticate(SaslAuthenticator.java:47)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.authenticateAll(InternalStreamConnectionInitializer.java:151)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:64)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.open(UsageTrackingInternalConnection.java:50)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.open(DefaultConnectionPool.java:390)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:106)
	at com.mongodb.internal.connection.DefaultConnectionPool.get(DefaultConnectionPool.java:92)
	at com.mongodb.internal.connection.DefaultServer.getConnection(DefaultServer.java:85)
	at com.mongodb.binding.ClusterBinding$ClusterBindingConnectionSource.getConnection(ClusterBinding.java:115)
	at com.mongodb.client.internal.ClientSessionBinding$SessionBindingConnectionSource.getConnection(ClientSessionBinding.java:111)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:451)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:287)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 19 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 18 (AuthenticationFailed): 'Authentication failed.' on server localhost:27017. The full response is { "ok" : 0.0, "errmsg" : "Authentication failed.", "code" : 18, "codeName" : "AuthenticationFailed", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1727424398, "i" : 1 } }, "signature" : { "hash" : { "$binary" : "PeU695q5VsMQ4O+6+vsPdc2T2CE=", "$type" : "00" }, "keyId" : { "$numberLong" : "7376103549123428362" } } }, "operationTime" : { "$timestamp" : { "t" : 1727424398, "i" : 1 } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.SaslAuthenticator.sendSaslContinue(SaslAuthenticator.java:134)
	at com.mongodb.internal.connection.SaslAuthenticator.access$200(SaslAuthenticator.java:40)
	at com.mongodb.internal.connection.SaslAuthenticator$1.run(SaslAuthenticator.java:67)
	... 46 more

[INFO ] 2024-09-27 16:06:38.466 - [任务 5][SourceMongo] - Job suspend in error handle 
[INFO ] 2024-09-27 16:06:38.730 - [任务 5][local3307] - Source node "local3307" read batch size: 100 
[INFO ] 2024-09-27 16:06:38.731 - [任务 5][local3307] - Source node "local3307" event queue capacity: 200 
[INFO ] 2024-09-27 16:06:38.731 - [任务 5][local3307] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-27 16:06:38.744 - [任务 5][local3307] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":282674752,"gtidSet":""} 
[INFO ] 2024-09-27 16:06:38.808 - [任务 5][local3307] - Initial sync started 
[INFO ] 2024-09-27 16:06:38.808 - [任务 5][local3307] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-09-27 16:06:38.865 - [任务 5][local3307] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-09-27 16:06:38.865 - [任务 5][local3307] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-27 16:06:38.879 - [任务 5][local3307] - Query table 'BMSQL_ITEM' counts: 0 
[INFO ] 2024-09-27 16:06:38.879 - [任务 5][local3307] - Initial sync completed 
[INFO ] 2024-09-27 16:06:38.879 - [任务 5][local3307] - Incremental sync starting... 
[INFO ] 2024-09-27 16:06:38.879 - [任务 5][local3307] - Initial sync completed 
[INFO ] 2024-09-27 16:06:38.880 - [任务 5][local3307] - Starting stream read, table list: [BMSQL_ITEM], offset: {"filename":"binlog.000032","position":282674752,"gtidSet":""} 
[INFO ] 2024-09-27 16:06:38.922 - [任务 5][local3307] - Starting mysql cdc, server name: 24c20f48-76f3-421a-aed6-5dd91c851e12 
[INFO ] 2024-09-27 16:06:38.923 - [任务 5][local3307] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"24c20f48-76f3-421a-aed6-5dd91c851e12","offset":{"{\"server\":\"24c20f48-76f3-421a-aed6-5dd91c851e12\"}":"{\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 506087909
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 24c20f48-76f3-421a-aed6-5dd91c851e12
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-24c20f48-76f3-421a-aed6-5dd91c851e12
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 24c20f48-76f3-421a-aed6-5dd91c851e12
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-27 16:06:39.127 - [任务 5][local3307] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-09-27 16:06:39.318 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] running status set to false 
[INFO ] 2024-09-27 16:06:39.432 - [任务 5][local3307] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-27 16:06:39.467 - [任务 5][local3307] - Mysql binlog reader stopped 
[INFO ] 2024-09-27 16:06:39.469 - [任务 5][local3307] - PDK connector node stopped: HazelcastSourcePdkDataNode-fea1a74b-c372-4d40-b7e0-98671c76f997 
[INFO ] 2024-09-27 16:06:39.470 - [任务 5][local3307] - PDK connector node released: HazelcastSourcePdkDataNode-fea1a74b-c372-4d40-b7e0-98671c76f997 
[INFO ] 2024-09-27 16:06:39.472 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] schema data cleaned 
[INFO ] 2024-09-27 16:06:39.473 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] monitor closed 
[INFO ] 2024-09-27 16:06:39.473 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] close complete, cost 158 ms 
[INFO ] 2024-09-27 16:06:39.473 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] running status set to false 
[INFO ] 2024-09-27 16:06:39.473 - [任务 5][SourceMongo] - PDK connector node stopped: null 
[INFO ] 2024-09-27 16:06:39.473 - [任务 5][SourceMongo] - PDK connector node released: null 
[INFO ] 2024-09-27 16:06:39.473 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] schema data cleaned 
[INFO ] 2024-09-27 16:06:39.473 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] monitor closed 
[INFO ] 2024-09-27 16:06:39.629 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] close complete, cost 0 ms 
[INFO ] 2024-09-27 16:06:42.780 - [任务 5] - Task [任务 5] cannot retry, reason: Task retry service not start 
[INFO ] 2024-09-27 16:06:42.780 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-27 16:06:42.780 - [任务 5] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5cd5fbde 
[INFO ] 2024-09-27 16:06:42.917 - [任务 5] - Stop task milestones: 66f6677483aeee2f9d5811a1(任务 5)  
[INFO ] 2024-09-27 16:06:42.918 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-09-27 16:06:42.918 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-09-27 16:06:42.951 - [任务 5] - Remove memory task client succeed, task: 任务 5[66f6677483aeee2f9d5811a1] 
[INFO ] 2024-09-27 16:06:42.953 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[66f6677483aeee2f9d5811a1] 
[INFO ] 2024-09-27 16:08:56.565 - [任务 5] - Start task milestones: 66f6677483aeee2f9d5811a1(任务 5) 
[INFO ] 2024-09-27 16:08:56.566 - [任务 5] - Task initialization... 
[INFO ] 2024-09-27 16:08:56.688 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 16:08:56.810 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 16:08:56.813 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:08:56.813 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:08:56.813 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] preload schema finished, cost 1 ms 
[INFO ] 2024-09-27 16:08:56.813 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 16:09:02.090 - [任务 5][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-27 16:09:02.092 - [任务 5][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-27 16:09:02.327 - [任务 5][local3307] - Source node "local3307" read batch size: 100 
[INFO ] 2024-09-27 16:09:02.328 - [任务 5][local3307] - Source node "local3307" event queue capacity: 200 
[INFO ] 2024-09-27 16:09:02.328 - [任务 5][local3307] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-27 16:09:02.333 - [任务 5][local3307] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":282674752,"gtidSet":""} 
[INFO ] 2024-09-27 16:09:02.388 - [任务 5][local3307] - Initial sync started 
[INFO ] 2024-09-27 16:09:02.388 - [任务 5][local3307] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-09-27 16:09:02.418 - [任务 5][local3307] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-09-27 16:09:02.418 - [任务 5][local3307] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-27 16:09:02.427 - [任务 5][local3307] - Query table 'BMSQL_ITEM' counts: 0 
[INFO ] 2024-09-27 16:09:02.427 - [任务 5][local3307] - Initial sync completed 
[INFO ] 2024-09-27 16:09:02.427 - [任务 5][local3307] - Incremental sync starting... 
[INFO ] 2024-09-27 16:09:02.428 - [任务 5][local3307] - Initial sync completed 
[INFO ] 2024-09-27 16:09:02.455 - [任务 5][local3307] - Starting stream read, table list: [BMSQL_ITEM], offset: {"filename":"binlog.000032","position":282674752,"gtidSet":""} 
[INFO ] 2024-09-27 16:09:02.455 - [任务 5][local3307] - Starting mysql cdc, server name: a7d540e9-376e-4831-b809-1bf518b6bbf0 
[INFO ] 2024-09-27 16:09:02.507 - [任务 5][local3307] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"a7d540e9-376e-4831-b809-1bf518b6bbf0","offset":{"{\"server\":\"a7d540e9-376e-4831-b809-1bf518b6bbf0\"}":"{\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1214908090
  time.precision.mode: adaptive_time_microseconds
  database.server.name: a7d540e9-376e-4831-b809-1bf518b6bbf0
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-a7d540e9-376e-4831-b809-1bf518b6bbf0
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: a7d540e9-376e-4831-b809-1bf518b6bbf0
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-27 16:09:02.508 - [任务 5][local3307] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-09-27 16:09:03.116 - [任务 5][local3307] - last event is io.tapdata.entity.event.control.HeartbeatEvent@225b319c: {"time":1727424543003,"type":501} 
[WARN ] 2024-09-27 16:09:03.665 - [任务 5][SourceMongo] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[fea1a74b-c372-4d40-b7e0-98671c76f997], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-09-27 16:09:51.000 - [任务 5][local3307] - last event is io.tapdata.entity.event.control.HeartbeatEvent@5dbd26f8: {"time":1727424590999,"type":501} 
[INFO ] 2024-09-27 16:10:38.998 - [任务 5][local3307] - last event is io.tapdata.entity.event.control.HeartbeatEvent@48741eec: {"time":1727424638998,"type":501} 
[INFO ] 2024-09-27 16:11:08.987 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] running status set to false 
[INFO ] 2024-09-27 16:11:09.253 - [任务 5][local3307] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-27 16:11:09.254 - [任务 5][local3307] - Mysql binlog reader stopped 
[INFO ] 2024-09-27 16:11:09.372 - [任务 5][local3307] - Incremental sync completed 
[WARN ] 2024-09-27 16:23:22.417 - [任务 5][local3307] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-fea1a74b-c372-4d40-b7e0-98671c76f997 
[INFO ] 2024-09-27 16:23:22.468 - [任务 5] - Task initialization... 
[INFO ] 2024-09-27 16:23:22.653 - [任务 5] - Start task milestones: 66f6677483aeee2f9d5811a1(任务 5) 
[INFO ] 2024-09-27 16:23:24.059 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 16:23:24.221 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 16:23:25.015 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:23:25.017 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 16:23:25.020 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:23:25.020 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 16:23:26.703 - [任务 5][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-27 16:23:26.913 - [任务 5][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-27 16:23:28.687 - [任务 5][local3307] - Source node "local3307" read batch size: 100 
[INFO ] 2024-09-27 16:23:28.688 - [任务 5][local3307] - Source node "local3307" event queue capacity: 200 
[INFO ] 2024-09-27 16:23:28.688 - [任务 5][local3307] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-27 16:23:28.696 - [任务 5][local3307] - batch offset found: {"BMSQL_ITEM":{"batch_read_connector_status":"OVER"}},stream offset found: {"name":"a7d540e9-376e-4831-b809-1bf518b6bbf0","offset":{"{\"server\":\"a7d540e9-376e-4831-b809-1bf518b6bbf0\"}":"{\"ts_sec\":1727424542,\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":1}"}} 
[INFO ] 2024-09-27 16:23:28.770 - [任务 5][local3307] - Incremental sync starting... 
[INFO ] 2024-09-27 16:23:28.771 - [任务 5][local3307] - Initial sync completed 
[INFO ] 2024-09-27 16:23:28.772 - [任务 5][local3307] - Starting stream read, table list: [BMSQL_ITEM], offset: {"name":"a7d540e9-376e-4831-b809-1bf518b6bbf0","offset":{"{\"server\":\"a7d540e9-376e-4831-b809-1bf518b6bbf0\"}":"{\"ts_sec\":1727424542,\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":1}"}} 
[INFO ] 2024-09-27 16:23:28.835 - [任务 5][local3307] - Starting mysql cdc, server name: a7d540e9-376e-4831-b809-1bf518b6bbf0 
[INFO ] 2024-09-27 16:23:28.835 - [任务 5][local3307] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"a7d540e9-376e-4831-b809-1bf518b6bbf0","offset":{"{\"server\":\"a7d540e9-376e-4831-b809-1bf518b6bbf0\"}":"{\"ts_sec\":1727424542,\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 222404414
  time.precision.mode: adaptive_time_microseconds
  database.server.name: a7d540e9-376e-4831-b809-1bf518b6bbf0
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-a7d540e9-376e-4831-b809-1bf518b6bbf0
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: a7d540e9-376e-4831-b809-1bf518b6bbf0
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-27 16:23:29.028 - [任务 5][local3307] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-09-27 16:25:12.171 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] running status set to false 
[INFO ] 2024-09-27 16:25:12.306 - [任务 5][local3307] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-27 16:25:12.311 - [任务 5][local3307] - Mysql binlog reader stopped 
[INFO ] 2024-09-27 16:25:12.312 - [任务 5][local3307] - Incremental sync completed 
[INFO ] 2024-09-27 16:25:12.327 - [任务 5][local3307] - PDK connector node stopped: HazelcastSourcePdkDataNode-fea1a74b-c372-4d40-b7e0-98671c76f997 
[INFO ] 2024-09-27 16:25:12.327 - [任务 5][local3307] - PDK connector node released: HazelcastSourcePdkDataNode-fea1a74b-c372-4d40-b7e0-98671c76f997 
[INFO ] 2024-09-27 16:25:12.328 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] schema data cleaned 
[INFO ] 2024-09-27 16:25:12.328 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] monitor closed 
[INFO ] 2024-09-27 16:25:12.331 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] close complete, cost 169 ms 
[INFO ] 2024-09-27 16:25:12.332 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] running status set to false 
[INFO ] 2024-09-27 16:25:12.358 - [任务 5][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-968d62dd-1c2b-4e93-878a-1ac0cd70d32e 
[INFO ] 2024-09-27 16:25:12.358 - [任务 5][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-968d62dd-1c2b-4e93-878a-1ac0cd70d32e 
[INFO ] 2024-09-27 16:25:12.358 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] schema data cleaned 
[INFO ] 2024-09-27 16:25:12.358 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] monitor closed 
[INFO ] 2024-09-27 16:25:12.566 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] close complete, cost 28 ms 
[INFO ] 2024-09-27 16:25:13.527 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-27 16:25:13.640 - [任务 5] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@509bdd95 
[INFO ] 2024-09-27 16:25:13.640 - [任务 5] - Stop task milestones: 66f6677483aeee2f9d5811a1(任务 5)  
[INFO ] 2024-09-27 16:25:13.659 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-09-27 16:25:13.660 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-09-27 16:25:13.681 - [任务 5] - Remove memory task client succeed, task: 任务 5[66f6677483aeee2f9d5811a1] 
[INFO ] 2024-09-27 16:25:13.684 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[66f6677483aeee2f9d5811a1] 
[INFO ] 2024-09-27 16:26:18.923 - [任务 5] - Task initialization... 
[INFO ] 2024-09-27 16:26:18.960 - [任务 5] - Start task milestones: 66f6677483aeee2f9d5811a1(任务 5) 
[INFO ] 2024-09-27 16:26:19.027 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 16:26:19.077 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 16:26:19.224 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:26:19.224 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:26:19.224 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 16:26:19.224 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 16:26:19.941 - [任务 5][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-27 16:26:19.941 - [任务 5][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-27 16:26:21.109 - [任务 5][local3307] - Source node "local3307" read batch size: 100 
[INFO ] 2024-09-27 16:26:21.109 - [任务 5][local3307] - Source node "local3307" event queue capacity: 200 
[INFO ] 2024-09-27 16:26:21.120 - [任务 5][local3307] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-27 16:26:21.123 - [任务 5][local3307] - batch offset found: {},stream offset found: {"name":"a7d540e9-376e-4831-b809-1bf518b6bbf0","offset":{"{\"server\":\"a7d540e9-376e-4831-b809-1bf518b6bbf0\"}":"{\"ts_sec\":1727425409,\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":1}"}} 
[INFO ] 2024-09-27 16:26:21.180 - [任务 5][local3307] - Incremental sync starting... 
[INFO ] 2024-09-27 16:26:21.180 - [任务 5][local3307] - Initial sync completed 
[INFO ] 2024-09-27 16:26:21.180 - [任务 5][local3307] - Starting stream read, table list: [BMSQL_ITEM], offset: {"name":"a7d540e9-376e-4831-b809-1bf518b6bbf0","offset":{"{\"server\":\"a7d540e9-376e-4831-b809-1bf518b6bbf0\"}":"{\"ts_sec\":1727425409,\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":1}"}} 
[INFO ] 2024-09-27 16:26:21.207 - [任务 5][local3307] - Starting mysql cdc, server name: a7d540e9-376e-4831-b809-1bf518b6bbf0 
[INFO ] 2024-09-27 16:26:21.207 - [任务 5][local3307] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"a7d540e9-376e-4831-b809-1bf518b6bbf0","offset":{"{\"server\":\"a7d540e9-376e-4831-b809-1bf518b6bbf0\"}":"{\"ts_sec\":1727425409,\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 755380862
  time.precision.mode: adaptive_time_microseconds
  database.server.name: a7d540e9-376e-4831-b809-1bf518b6bbf0
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-a7d540e9-376e-4831-b809-1bf518b6bbf0
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: a7d540e9-376e-4831-b809-1bf518b6bbf0
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-27 16:26:21.357 - [任务 5][local3307] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-09-27 17:13:33.035 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] running status set to false 
[INFO ] 2024-09-27 17:13:33.281 - [任务 5][local3307] - PDK connector node stopped: HazelcastSourcePdkDataNode-fea1a74b-c372-4d40-b7e0-98671c76f997 
[INFO ] 2024-09-27 17:13:33.282 - [任务 5][local3307] - PDK connector node released: HazelcastSourcePdkDataNode-fea1a74b-c372-4d40-b7e0-98671c76f997 
[INFO ] 2024-09-27 17:13:33.282 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] schema data cleaned 
[INFO ] 2024-09-27 17:13:33.282 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] monitor closed 
[INFO ] 2024-09-27 17:13:33.284 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] close complete, cost 262 ms 
[INFO ] 2024-09-27 17:13:33.285 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] running status set to false 
[INFO ] 2024-09-27 17:13:33.310 - [任务 5][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-968d62dd-1c2b-4e93-878a-1ac0cd70d32e 
[INFO ] 2024-09-27 17:13:33.310 - [任务 5][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-968d62dd-1c2b-4e93-878a-1ac0cd70d32e 
[INFO ] 2024-09-27 17:13:33.310 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] schema data cleaned 
[INFO ] 2024-09-27 17:13:33.311 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] monitor closed 
[INFO ] 2024-09-27 17:13:33.311 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] close complete, cost 26 ms 
[INFO ] 2024-09-27 17:13:37.382 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-27 17:13:37.382 - [任务 5] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@52ec4130 
[INFO ] 2024-09-27 17:13:37.499 - [任务 5] - Stop task milestones: 66f6677483aeee2f9d5811a1(任务 5)  
[INFO ] 2024-09-27 17:13:37.499 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-09-27 17:13:37.499 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-09-27 17:13:37.517 - [任务 5] - Remove memory task client succeed, task: 任务 5[66f6677483aeee2f9d5811a1] 
[INFO ] 2024-09-27 17:13:37.520 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[66f6677483aeee2f9d5811a1] 
[INFO ] 2024-09-27 17:16:08.713 - [任务 5] - Task initialization... 
[INFO ] 2024-09-27 17:16:08.757 - [任务 5] - Start task milestones: 66f6677483aeee2f9d5811a1(任务 5) 
[INFO ] 2024-09-27 17:16:09.037 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 17:16:09.173 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 17:16:09.371 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] start preload schema,table counts: 1 
[INFO ] 2024-09-27 17:16:09.371 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] start preload schema,table counts: 1 
[INFO ] 2024-09-27 17:16:09.371 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 17:16:09.371 - [任务 5][SourceMongo] - Node SourceMongo[968d62dd-1c2b-4e93-878a-1ac0cd70d32e] preload schema finished, cost 2 ms 
[INFO ] 2024-09-27 17:16:09.938 - [任务 5][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-27 17:16:10.087 - [任务 5][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-27 17:16:11.594 - [任务 5][local3307] - Source node "local3307" read batch size: 100 
[INFO ] 2024-09-27 17:16:11.595 - [任务 5][local3307] - Source node "local3307" event queue capacity: 200 
[INFO ] 2024-09-27 17:16:11.595 - [任务 5][local3307] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-27 17:16:11.597 - [任务 5][local3307] - batch offset found: {},stream offset found: {"name":"a7d540e9-376e-4831-b809-1bf518b6bbf0","offset":{"{\"server\":\"a7d540e9-376e-4831-b809-1bf518b6bbf0\"}":"{\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":1}"}} 
[INFO ] 2024-09-27 17:16:11.704 - [任务 5][local3307] - Incremental sync starting... 
[INFO ] 2024-09-27 17:16:11.705 - [任务 5][local3307] - Initial sync completed 
[INFO ] 2024-09-27 17:16:11.728 - [任务 5][local3307] - Starting stream read, table list: [BMSQL_ITEM], offset: {"name":"a7d540e9-376e-4831-b809-1bf518b6bbf0","offset":{"{\"server\":\"a7d540e9-376e-4831-b809-1bf518b6bbf0\"}":"{\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":1}"}} 
[INFO ] 2024-09-27 17:16:11.729 - [任务 5][local3307] - Starting mysql cdc, server name: a7d540e9-376e-4831-b809-1bf518b6bbf0 
[INFO ] 2024-09-27 17:16:11.823 - [任务 5][local3307] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"a7d540e9-376e-4831-b809-1bf518b6bbf0","offset":{"{\"server\":\"a7d540e9-376e-4831-b809-1bf518b6bbf0\"}":"{\"file\":\"binlog.000032\",\"pos\":282674752,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 904043138
  time.precision.mode: adaptive_time_microseconds
  database.server.name: a7d540e9-376e-4831-b809-1bf518b6bbf0
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3307
  threadName: Debezium-Mysql-Connector-a7d540e9-376e-4831-b809-1bf518b6bbf0
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: a7d540e9-376e-4831-b809-1bf518b6bbf0
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-27 17:16:12.843 - [任务 5][local3307] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-09-27 18:53:28.742 - [任务 5][local3307] - Node local3307[fea1a74b-c372-4d40-b7e0-98671c76f997] running status set to false 
