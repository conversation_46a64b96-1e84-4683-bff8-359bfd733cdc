[INFO ] 2024-07-25 12:15:30.029 - [任务 28] - Task initialization... 
[INFO ] 2024-07-25 12:15:30.233 - [任务 28] - Start task milestones: 66a1d128349bc63fe9d86528(任务 28) 
[INFO ] 2024-07-25 12:15:30.260 - [任务 28] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-25 12:15:30.338 - [任务 28] - The engine receives 任务 28 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 12:15:30.339 - [任务 28][Mysql] - Node Mysql[2b3934e1-1c4b-4b77-9b8f-e1fc6d3da7eb] start preload schema,table counts: 2 
[INFO ] 2024-07-25 12:15:30.340 - [任务 28][Mongo] - No<PERSON>[28b76ecc-1c72-4dd1-92af-5814fb8db641] start preload schema,table counts: 2 
[INFO ] 2024-07-25 12:15:30.340 - [任务 28][Mysql] - Node <PERSON>sql[2b3934e1-1c4b-4b77-9b8f-e1fc6d3da7eb] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 12:15:35.133 - [任务 28][Mongo] - Node Mongo[28b76ecc-1c72-4dd1-92af-5814fb8db641] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 12:15:35.890 - [任务 28][Mysql] - Node(Mysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-25 12:15:35.892 - [任务 28][Mongo] - Source node "Mongo" read batch size: 100 
[INFO ] 2024-07-25 12:15:35.899 - [任务 28][Mysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 12:15:35.902 - [任务 28][Mongo] - Source node "Mongo" event queue capacity: 200 
[INFO ] 2024-07-25 12:15:36.108 - [任务 28][Mongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 12:15:36.317 - [任务 28][Mongo] - batch offset found: {},stream offset found: {"cdcOffset":1721880935,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-25 12:15:36.317 - [任务 28][Mongo] - Initial sync started 
[INFO ] 2024-07-25 12:15:36.320 - [任务 28][Mongo] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-25 12:15:36.320 - [任务 28][Mongo] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-25 12:15:36.411 - [任务 28][Mongo] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 12:15:36.445 - [任务 28][Mongo] - Query table 'CUSTOMER' counts: 676 
[INFO ] 2024-07-25 12:15:36.447 - [任务 28][Mongo] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-25 12:15:36.477 - [任务 28][Mongo] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-25 12:15:36.485 - [任务 28][Mongo] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-25 12:15:36.506 - [任务 28][Mongo] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 12:15:36.506 - [任务 28][Mongo] - Initial sync completed 
[INFO ] 2024-07-25 12:15:36.507 - [任务 28][Mongo] - Incremental sync starting... 
[INFO ] 2024-07-25 12:15:36.507 - [任务 28][Mongo] - Initial sync completed 
[INFO ] 2024-07-25 12:15:36.585 - [任务 28][Mongo] - Starting stream read, table list: [CUSTOMER, POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1721880935,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-25 12:15:36.585 - [任务 28][Mongo] - Connector MongoDB incremental start succeed, tables: [CUSTOMER, POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-25 13:07:36.515 - [任务 28][Mongo] - Node Mongo[28b76ecc-1c72-4dd1-92af-5814fb8db641] running status set to false 
[INFO ] 2024-07-25 13:07:36.515 - [任务 28][Mongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-28b76ecc-1c72-4dd1-92af-5814fb8db641 
[INFO ] 2024-07-25 13:07:36.516 - [任务 28][Mongo] - PDK connector node released: HazelcastSourcePdkDataNode-28b76ecc-1c72-4dd1-92af-5814fb8db641 
[INFO ] 2024-07-25 13:07:36.516 - [任务 28][Mongo] - Node Mongo[28b76ecc-1c72-4dd1-92af-5814fb8db641] schema data cleaned 
[INFO ] 2024-07-25 13:07:36.522 - [任务 28][Mongo] - Node Mongo[28b76ecc-1c72-4dd1-92af-5814fb8db641] monitor closed 
[INFO ] 2024-07-25 13:07:36.522 - [任务 28][Mongo] - Node Mongo[28b76ecc-1c72-4dd1-92af-5814fb8db641] close complete, cost 52 ms 
[INFO ] 2024-07-25 13:07:36.546 - [任务 28][Mysql] - Node Mysql[2b3934e1-1c4b-4b77-9b8f-e1fc6d3da7eb] running status set to false 
[INFO ] 2024-07-25 13:07:36.546 - [任务 28][Mysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-2b3934e1-1c4b-4b77-9b8f-e1fc6d3da7eb 
[INFO ] 2024-07-25 13:07:36.546 - [任务 28][Mysql] - PDK connector node released: HazelcastTargetPdkDataNode-2b3934e1-1c4b-4b77-9b8f-e1fc6d3da7eb 
[INFO ] 2024-07-25 13:07:36.547 - [任务 28][Mysql] - Node Mysql[2b3934e1-1c4b-4b77-9b8f-e1fc6d3da7eb] schema data cleaned 
[INFO ] 2024-07-25 13:07:36.548 - [任务 28][Mysql] - Node Mysql[2b3934e1-1c4b-4b77-9b8f-e1fc6d3da7eb] monitor closed 
[INFO ] 2024-07-25 13:07:36.548 - [任务 28][Mysql] - Node Mysql[2b3934e1-1c4b-4b77-9b8f-e1fc6d3da7eb] close complete, cost 25 ms 
[INFO ] 2024-07-25 13:07:36.982 - [任务 28][Mongo] - Incremental sync completed 
[INFO ] 2024-07-25 13:07:39.598 - [任务 28] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 13:07:39.598 - [任务 28] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@b44606a 
[INFO ] 2024-07-25 13:07:39.719 - [任务 28] - Stop task milestones: 66a1d128349bc63fe9d86528(任务 28)  
[INFO ] 2024-07-25 13:07:39.719 - [任务 28] - Stopped task aspect(s) 
[INFO ] 2024-07-25 13:07:39.777 - [任务 28] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 13:07:39.778 - [任务 28] - Remove memory task client succeed, task: 任务 28[66a1d128349bc63fe9d86528] 
[INFO ] 2024-07-25 13:07:39.778 - [任务 28] - Destroy memory task client cache succeed, task: 任务 28[66a1d128349bc63fe9d86528] 
