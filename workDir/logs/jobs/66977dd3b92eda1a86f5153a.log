[INFO ] 2024-07-17 16:21:24.752 - [任务 3] - Start task milestones: 66977dd3b92eda1a86f5153a(任务 3) 
[INFO ] 2024-07-17 16:21:24.752 - [任务 3] - Task initialization... 
[INFO ] 2024-07-17 16:21:25.023 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-17 16:21:25.225 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-17 16:21:25.257 - [任务 3][CUSTOMER] - Node CUSTOMER[a7966278-5ff7-41e2-8127-a37bc1a1f894] start preload schema,table counts: 1 
[INFO ] 2024-07-17 16:21:25.257 - [任务 3][CUSTOMER] - Node CUSTOMER[d19ae17d-54bc-45ce-8be6-66a8463a7004] start preload schema,table counts: 1 
[INFO ] 2024-07-17 16:21:25.261 - [任务 3][CUSTOMER] - Node CUSTOMER[a7966278-5ff7-41e2-8127-a37bc1a1f894] preload schema finished, cost 1 ms 
[INFO ] 2024-07-17 16:21:25.261 - [任务 3][CUSTOMER] - Node CUSTOMER[d19ae17d-54bc-45ce-8be6-66a8463a7004] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 16:21:25.867 - [任务 3][CUSTOMER] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-17 16:21:25.934 - [任务 3][CUSTOMER] - Source node "CUSTOMER" read batch size: 500 
[INFO ] 2024-07-17 16:21:25.934 - [任务 3][CUSTOMER] - Source node "CUSTOMER" event queue capacity: 1000 
[INFO ] 2024-07-17 16:21:25.934 - [任务 3][CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-17 16:21:26.119 - [任务 3][CUSTOMER] - batch offset found: {},stream offset found: {"cdcOffset":1721204485,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 16:21:26.287 - [任务 3][CUSTOMER] - Initial sync started 
[INFO ] 2024-07-17 16:21:26.298 - [任务 3][CUSTOMER] - Starting batch read, table name: CUSTOMER, offset: null 
[INFO ] 2024-07-17 16:21:26.298 - [任务 3][CUSTOMER] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-07-17 16:21:26.372 - [任务 3][CUSTOMER] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-17 16:21:26.372 - [任务 3][CUSTOMER] - Query table 'CUSTOMER' counts: 675 
[INFO ] 2024-07-17 16:21:26.372 - [任务 3][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-17 16:21:26.372 - [任务 3][CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-07-17 16:21:26.373 - [任务 3][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-17 16:21:26.373 - [任务 3][CUSTOMER] - Starting stream read, table list: [CUSTOMER], offset: {"cdcOffset":1721204485,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 16:21:26.574 - [任务 3][CUSTOMER] - Connector MongoDB incremental start succeed, tables: [CUSTOMER], data change syncing 
[INFO ] 2024-07-17 16:21:48.884 - [任务 3][CUSTOMER] - Node CUSTOMER[d19ae17d-54bc-45ce-8be6-66a8463a7004] running status set to false 
[INFO ] 2024-07-17 16:21:48.885 - [任务 3][CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode-d19ae17d-54bc-45ce-8be6-66a8463a7004 
[INFO ] 2024-07-17 16:21:48.885 - [任务 3][CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode-d19ae17d-54bc-45ce-8be6-66a8463a7004 
[INFO ] 2024-07-17 16:21:48.886 - [任务 3][CUSTOMER] - Node CUSTOMER[d19ae17d-54bc-45ce-8be6-66a8463a7004] schema data cleaned 
[INFO ] 2024-07-17 16:21:48.889 - [任务 3][CUSTOMER] - Node CUSTOMER[d19ae17d-54bc-45ce-8be6-66a8463a7004] monitor closed 
[INFO ] 2024-07-17 16:21:48.889 - [任务 3][CUSTOMER] - Node CUSTOMER[d19ae17d-54bc-45ce-8be6-66a8463a7004] close complete, cost 25 ms 
[INFO ] 2024-07-17 16:21:48.914 - [任务 3][CUSTOMER] - Node CUSTOMER[a7966278-5ff7-41e2-8127-a37bc1a1f894] running status set to false 
[INFO ] 2024-07-17 16:21:48.915 - [任务 3][CUSTOMER] - PDK connector node stopped: HazelcastTargetPdkDataNode-a7966278-5ff7-41e2-8127-a37bc1a1f894 
[INFO ] 2024-07-17 16:21:48.915 - [任务 3][CUSTOMER] - PDK connector node released: HazelcastTargetPdkDataNode-a7966278-5ff7-41e2-8127-a37bc1a1f894 
[INFO ] 2024-07-17 16:21:48.916 - [任务 3][CUSTOMER] - Node CUSTOMER[a7966278-5ff7-41e2-8127-a37bc1a1f894] schema data cleaned 
[INFO ] 2024-07-17 16:21:48.916 - [任务 3][CUSTOMER] - Node CUSTOMER[a7966278-5ff7-41e2-8127-a37bc1a1f894] monitor closed 
[INFO ] 2024-07-17 16:21:49.120 - [任务 3][CUSTOMER] - Node CUSTOMER[a7966278-5ff7-41e2-8127-a37bc1a1f894] close complete, cost 28 ms 
[INFO ] 2024-07-17 16:21:49.599 - [任务 3][CUSTOMER] - Incremental sync completed 
[INFO ] 2024-07-17 16:21:50.831 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 16:21:50.832 - [任务 3] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@248f4e9a 
[INFO ] 2024-07-17 16:21:50.844 - [任务 3] - Stop task milestones: 66977dd3b92eda1a86f5153a(任务 3)  
[INFO ] 2024-07-17 16:21:50.986 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-07-17 16:21:50.986 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-07-17 16:21:51.054 - [任务 3] - Remove memory task client succeed, task: 任务 3[66977dd3b92eda1a86f5153a] 
[INFO ] 2024-07-17 16:21:51.054 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[66977dd3b92eda1a86f5153a] 
