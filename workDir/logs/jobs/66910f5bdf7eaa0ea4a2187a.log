[INFO ] 2024-07-13 06:18:07.964 - [测试时间开发任务][transftormTime] - Node transftormTime[de92070d-a63c-4df8-a23f-baa9cbf7d808] running status set to false 
[INFO ] 2024-07-13 06:18:07.988 - [测试时间开发任务][transftormTime] - PDK connector node stopped: HazelcastSourcePdkDataNode-de92070d-a63c-4df8-a23f-baa9cbf7d808 
[INFO ] 2024-07-13 06:18:07.988 - [测试时间开发任务][transftormTime] - PDK connector node released: HazelcastSourcePdkDataNode-de92070d-a63c-4df8-a23f-baa9cbf7d808 
[INFO ] 2024-07-13 06:18:07.988 - [测试时间开发任务][transftormTime] - Node transftormTime[de92070d-a63c-4df8-a23f-baa9cbf7d808] schema data cleaned 
[INFO ] 2024-07-13 06:18:07.988 - [测试时间开发任务][transftormTime] - Node transftormTime[de92070d-a63c-4df8-a23f-baa9cbf7d808] monitor closed 
[INFO ] 2024-07-13 06:18:07.989 - [测试时间开发任务][transftormTime] - Node transftormTime[de92070d-a63c-4df8-a23f-baa9cbf7d808] close complete, cost 34 ms 
[INFO ] 2024-07-13 06:18:07.989 - [测试时间开发任务][时间运算] - Node 时间运算[d6ec01b1-c426-45be-9256-24c4f1134bfc] running status set to false 
[INFO ] 2024-07-13 06:18:07.989 - [测试时间开发任务][时间运算] - Node 时间运算[d6ec01b1-c426-45be-9256-24c4f1134bfc] schema data cleaned 
[INFO ] 2024-07-13 06:18:07.989 - [测试时间开发任务][时间运算] - Node 时间运算[d6ec01b1-c426-45be-9256-24c4f1134bfc] monitor closed 
[INFO ] 2024-07-13 06:18:07.989 - [测试时间开发任务][时间运算] - Node 时间运算[d6ec01b1-c426-45be-9256-24c4f1134bfc] close complete, cost 1 ms 
[INFO ] 2024-07-13 06:18:07.989 - [测试时间开发任务][trans_time] - Node trans_time[8998f919-1a1e-41d4-85f8-58377dd9dc36] running status set to false 
[INFO ] 2024-07-13 06:18:08.058 - [测试时间开发任务][trans_time] - PDK connector node stopped: HazelcastTargetPdkDataNode-8998f919-1a1e-41d4-85f8-58377dd9dc36 
[INFO ] 2024-07-13 06:18:08.058 - [测试时间开发任务][trans_time] - PDK connector node released: HazelcastTargetPdkDataNode-8998f919-1a1e-41d4-85f8-58377dd9dc36 
[INFO ] 2024-07-13 06:18:08.059 - [测试时间开发任务][trans_time] - Node trans_time[8998f919-1a1e-41d4-85f8-58377dd9dc36] schema data cleaned 
[INFO ] 2024-07-13 06:18:08.059 - [测试时间开发任务][trans_time] - Node trans_time[8998f919-1a1e-41d4-85f8-58377dd9dc36] monitor closed 
[INFO ] 2024-07-13 06:18:08.261 - [测试时间开发任务][trans_time] - Node trans_time[8998f919-1a1e-41d4-85f8-58377dd9dc36] close complete, cost 91 ms 
[INFO ] 2024-07-13 06:18:08.944 - [测试时间开发任务][transftormTime] - Incremental sync completed 
[INFO ] 2024-07-13 06:18:08.990 - [测试时间开发任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-13 06:18:08.990 - [测试时间开发任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4a34d021 
[INFO ] 2024-07-13 06:18:09.122 - [测试时间开发任务] - Stop task milestones: 66910f5bdf7eaa0ea4a2187a(测试时间开发任务)  
[INFO ] 2024-07-13 06:18:09.122 - [测试时间开发任务] - Stopped task aspect(s) 
[INFO ] 2024-07-13 06:18:09.122 - [测试时间开发任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-13 06:18:09.122 - [测试时间开发任务] - Remove memory task client succeed, task: 测试时间开发任务[66910f5bdf7eaa0ea4a2187a] 
[INFO ] 2024-07-13 06:18:09.122 - [测试时间开发任务] - Destroy memory task client cache succeed, task: 测试时间开发任务[66910f5bdf7eaa0ea4a2187a] 
