[INFO ] 2024-05-10 11:27:13.744 - [任务 5] - Task initialization... 
[INFO ] 2024-05-10 11:27:13.745 - [任务 5] - Start task milestones: 663d92ee73967f3f672ef6b2(任务 5) 
[INFO ] 2024-05-10 11:27:13.745 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-10 11:27:13.746 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-10 11:27:13.958 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:27:13.961 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:27:14.082 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] preload schema finished, cost 122 ms 
[INFO ] 2024-05-10 11:27:14.082 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] preload schema finished, cost 121 ms 
[INFO ] 2024-05-10 11:27:15.322 - [任务 5][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-05-10 11:27:15.326 - [任务 5][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-05-10 11:27:15.326 - [任务 5][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-10 11:27:15.327 - [任务 5][CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-05-10 11:27:15.536 - [任务 5][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-10 11:27:15.556 - [任务 5][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-10 11:27:15.556 - [任务 5][CLAIM] - Initial sync started 
[INFO ] 2024-05-10 11:27:15.580 - [任务 5][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-05-10 11:27:15.593 - [任务 5][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-05-10 11:27:15.639 - [任务 5][CLAIM] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-05-10 11:27:15.691 - [任务 5][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-10 11:27:15.692 - [任务 5][CLAIM] - Initial sync completed 
[INFO ] 2024-05-10 11:27:16.880 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] running status set to false 
[INFO ] 2024-05-10 11:27:16.885 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] running status set to false 
[INFO ] 2024-05-10 11:27:16.936 - [任务 5][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:27:16.947 - [任务 5][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:27:16.947 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] schema data cleaned 
[INFO ] 2024-05-10 11:27:16.947 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] monitor closed 
[INFO ] 2024-05-10 11:27:16.953 - [任务 5][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:27:16.953 - [任务 5][test1] - PDK connector node released: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:27:16.954 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] schema data cleaned 
[INFO ] 2024-05-10 11:27:16.954 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] monitor closed 
[INFO ] 2024-05-10 11:27:16.965 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] close complete, cost 77 ms 
[INFO ] 2024-05-10 11:27:16.965 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] close complete, cost 83 ms 
[INFO ] 2024-05-10 11:27:19.673 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-10 11:27:19.724 - [任务 5] - Stop task milestones: 663d92ee73967f3f672ef6b2(任务 5)  
[INFO ] 2024-05-10 11:27:19.724 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-05-10 11:27:19.772 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-05-10 11:27:19.774 - [任务 5] - Remove memory task client succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:27:19.774 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:27:51.872 - [任务 5] - Task initialization... 
[INFO ] 2024-05-10 11:27:51.930 - [任务 5] - Start task milestones: 663d92ee73967f3f672ef6b2(任务 5) 
[INFO ] 2024-05-10 11:27:51.930 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-10 11:27:52.095 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-10 11:27:52.096 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:27:52.133 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:27:52.133 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] preload schema finished, cost 31 ms 
[INFO ] 2024-05-10 11:27:52.134 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] preload schema finished, cost 33 ms 
[INFO ] 2024-05-10 11:27:52.995 - [任务 5][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-10 11:27:53.127 - [任务 5][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-05-10 11:27:53.127 - [任务 5][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-05-10 11:27:53.129 - [任务 5][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-10 11:27:53.129 - [任务 5][CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-05-10 11:27:53.184 - [任务 5][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-10 11:27:53.184 - [任务 5][CLAIM] - Initial sync started 
[INFO ] 2024-05-10 11:27:53.189 - [任务 5][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-05-10 11:27:53.189 - [任务 5][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-05-10 11:27:53.263 - [任务 5][CLAIM] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-05-10 11:27:53.264 - [任务 5][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-10 11:27:53.472 - [任务 5][CLAIM] - Initial sync completed 
[INFO ] 2024-05-10 11:27:54.393 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] running status set to false 
[INFO ] 2024-05-10 11:27:54.419 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] running status set to false 
[INFO ] 2024-05-10 11:27:54.482 - [任务 5][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:27:54.483 - [任务 5][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:27:54.483 - [任务 5][test1] - PDK connector node released: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:27:54.483 - [任务 5][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:27:54.485 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] schema data cleaned 
[INFO ] 2024-05-10 11:27:54.485 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] schema data cleaned 
[INFO ] 2024-05-10 11:27:54.485 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] monitor closed 
[INFO ] 2024-05-10 11:27:54.488 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] monitor closed 
[INFO ] 2024-05-10 11:27:54.492 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] close complete, cost 110 ms 
[INFO ] 2024-05-10 11:27:54.492 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] close complete, cost 118 ms 
[INFO ] 2024-05-10 11:27:54.836 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-10 11:27:54.861 - [任务 5] - Stop task milestones: 663d92ee73967f3f672ef6b2(任务 5)  
[INFO ] 2024-05-10 11:27:54.864 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-05-10 11:27:54.864 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-05-10 11:27:54.882 - [任务 5] - Remove memory task client succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:27:54.884 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:28:23.432 - [任务 5] - Task initialization... 
[INFO ] 2024-05-10 11:28:23.457 - [任务 5] - Start task milestones: 663d92ee73967f3f672ef6b2(任务 5) 
[INFO ] 2024-05-10 11:28:23.458 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-10 11:28:23.562 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-10 11:28:23.562 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:28:23.562 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:28:23.592 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] preload schema finished, cost 25 ms 
[INFO ] 2024-05-10 11:28:23.799 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] preload schema finished, cost 26 ms 
[INFO ] 2024-05-10 11:28:24.602 - [任务 5][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-10 11:28:24.604 - [任务 5][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-05-10 11:28:24.604 - [任务 5][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-05-10 11:28:24.604 - [任务 5][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-10 11:28:24.604 - [任务 5][CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-05-10 11:28:24.604 - [任务 5][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-10 11:28:24.663 - [任务 5][CLAIM] - Initial sync started 
[INFO ] 2024-05-10 11:28:24.669 - [任务 5][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-05-10 11:28:24.669 - [任务 5][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-05-10 11:28:24.715 - [任务 5][CLAIM] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-05-10 11:28:24.716 - [任务 5][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-10 11:28:24.716 - [任务 5][CLAIM] - Initial sync completed 
[INFO ] 2024-05-10 11:28:25.843 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] running status set to false 
[INFO ] 2024-05-10 11:28:25.856 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] running status set to false 
[INFO ] 2024-05-10 11:28:25.890 - [任务 5][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:28:25.890 - [任务 5][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:28:25.891 - [任务 5][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:28:25.891 - [任务 5][test1] - PDK connector node released: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:28:25.892 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] schema data cleaned 
[INFO ] 2024-05-10 11:28:25.892 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] schema data cleaned 
[INFO ] 2024-05-10 11:28:25.896 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] monitor closed 
[INFO ] 2024-05-10 11:28:25.896 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] monitor closed 
[INFO ] 2024-05-10 11:28:25.901 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] close complete, cost 70 ms 
[INFO ] 2024-05-10 11:28:25.901 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] close complete, cost 76 ms 
[INFO ] 2024-05-10 11:28:29.979 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-10 11:28:30.018 - [任务 5] - Stop task milestones: 663d92ee73967f3f672ef6b2(任务 5)  
[INFO ] 2024-05-10 11:28:30.018 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-05-10 11:28:30.018 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-05-10 11:28:30.054 - [任务 5] - Remove memory task client succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:28:30.057 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:29:43.524 - [任务 5] - Task initialization... 
[INFO ] 2024-05-10 11:29:43.524 - [任务 5] - Start task milestones: 663d92ee73967f3f672ef6b2(任务 5) 
[INFO ] 2024-05-10 11:29:43.612 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-10 11:29:43.613 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-10 11:29:43.654 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:29:43.655 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:29:43.680 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] preload schema finished, cost 20 ms 
[INFO ] 2024-05-10 11:29:43.682 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] preload schema finished, cost 21 ms 
[INFO ] 2024-05-10 11:29:44.661 - [任务 5][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-10 11:29:44.661 - [任务 5][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-05-10 11:29:44.661 - [任务 5][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-05-10 11:29:44.661 - [任务 5][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-10 11:29:44.661 - [任务 5][CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-05-10 11:29:44.663 - [任务 5][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-10 11:29:44.705 - [任务 5][CLAIM] - Initial sync started 
[INFO ] 2024-05-10 11:29:44.709 - [任务 5][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-05-10 11:29:44.780 - [任务 5][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-05-10 11:29:44.781 - [任务 5][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-10 11:29:44.781 - [任务 5][CLAIM] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-05-10 11:29:44.781 - [任务 5][CLAIM] - Initial sync completed 
[INFO ] 2024-05-10 11:29:45.830 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] running status set to false 
[INFO ] 2024-05-10 11:29:45.835 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] running status set to false 
[INFO ] 2024-05-10 11:29:45.852 - [任务 5][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:29:45.853 - [任务 5][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:29:45.853 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] schema data cleaned 
[INFO ] 2024-05-10 11:29:45.856 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] monitor closed 
[INFO ] 2024-05-10 11:29:45.856 - [任务 5][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:29:45.859 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] close complete, cost 48 ms 
[INFO ] 2024-05-10 11:29:45.860 - [任务 5][test1] - PDK connector node released: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:29:45.861 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] schema data cleaned 
[INFO ] 2024-05-10 11:29:45.863 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] monitor closed 
[INFO ] 2024-05-10 11:29:45.864 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] close complete, cost 50 ms 
[INFO ] 2024-05-10 11:29:50.172 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-10 11:29:50.174 - [任务 5] - Stop task milestones: 663d92ee73967f3f672ef6b2(任务 5)  
[INFO ] 2024-05-10 11:29:50.190 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-05-10 11:29:50.191 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-05-10 11:29:50.268 - [任务 5] - Remove memory task client succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:29:50.272 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:45:00.238 - [任务 5] - Task initialization... 
[INFO ] 2024-05-10 11:45:00.309 - [任务 5] - Start task milestones: 663d92ee73967f3f672ef6b2(任务 5) 
[INFO ] 2024-05-10 11:45:00.310 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-10 11:45:00.371 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-10 11:45:00.422 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:45:00.422 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:45:00.447 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] preload schema finished, cost 25 ms 
[INFO ] 2024-05-10 11:45:00.448 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] preload schema finished, cost 25 ms 
[INFO ] 2024-05-10 11:45:00.757 - [任务 5][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-05-10 11:45:00.757 - [任务 5][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-05-10 11:45:00.786 - [任务 5][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-05-10 11:45:00.789 - [任务 5][CLAIM] - batch offset found: {"CLAIM":{"offset":null,"status":"RUNNING"}},stream offset not found. 
[INFO ] 2024-05-10 11:45:00.789 - [任务 5][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-10 11:45:00.858 - [任务 5][CLAIM] - Initial sync started 
[INFO ] 2024-05-10 11:45:00.866 - [任务 5][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-05-10 11:45:00.867 - [任务 5][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-05-10 11:45:00.912 - [任务 5][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-10 11:45:00.912 - [任务 5][CLAIM] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-05-10 11:45:00.912 - [任务 5][CLAIM] - Initial sync completed 
[INFO ] 2024-05-10 11:45:00.971 - [任务 5][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-10 11:45:02.027 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] running status set to false 
[INFO ] 2024-05-10 11:45:02.028 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] running status set to false 
[INFO ] 2024-05-10 11:45:02.060 - [任务 5][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:45:02.060 - [任务 5][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:45:02.060 - [任务 5][test1] - PDK connector node released: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:45:02.060 - [任务 5][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:45:02.063 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] schema data cleaned 
[INFO ] 2024-05-10 11:45:02.064 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] schema data cleaned 
[INFO ] 2024-05-10 11:45:02.064 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] monitor closed 
[INFO ] 2024-05-10 11:45:02.064 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] monitor closed 
[INFO ] 2024-05-10 11:45:02.068 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] close complete, cost 52 ms 
[INFO ] 2024-05-10 11:45:02.068 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] close complete, cost 59 ms 
[INFO ] 2024-05-10 11:45:06.287 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-10 11:45:06.289 - [任务 5] - Stop task milestones: 663d92ee73967f3f672ef6b2(任务 5)  
[INFO ] 2024-05-10 11:45:06.326 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-05-10 11:45:06.327 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-05-10 11:45:06.349 - [任务 5] - Remove memory task client succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:45:06.357 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:45:21.456 - [任务 5] - Task initialization... 
[INFO ] 2024-05-10 11:45:21.480 - [任务 5] - Start task milestones: 663d92ee73967f3f672ef6b2(任务 5) 
[INFO ] 2024-05-10 11:45:21.481 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-10 11:45:21.554 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-10 11:45:21.554 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:45:21.554 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:45:21.588 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] preload schema finished, cost 33 ms 
[INFO ] 2024-05-10 11:45:21.588 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] preload schema finished, cost 34 ms 
[INFO ] 2024-05-10 11:45:22.462 - [任务 5][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-10 11:45:22.590 - [任务 5][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-05-10 11:45:22.590 - [任务 5][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-05-10 11:45:22.591 - [任务 5][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-10 11:45:22.592 - [任务 5][CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-05-10 11:45:22.634 - [任务 5][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-10 11:45:22.634 - [任务 5][CLAIM] - Initial sync started 
[INFO ] 2024-05-10 11:45:22.634 - [任务 5][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-05-10 11:45:22.638 - [任务 5][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-05-10 11:45:22.671 - [任务 5][CLAIM] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-05-10 11:45:22.671 - [任务 5][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-10 11:45:22.876 - [任务 5][CLAIM] - Initial sync completed 
[INFO ] 2024-05-10 11:45:23.714 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] running status set to false 
[INFO ] 2024-05-10 11:45:23.717 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] running status set to false 
[INFO ] 2024-05-10 11:45:23.737 - [任务 5][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:45:23.737 - [任务 5][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:45:23.737 - [任务 5][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:45:23.737 - [任务 5][test1] - PDK connector node released: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:45:23.738 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] schema data cleaned 
[INFO ] 2024-05-10 11:45:23.738 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] schema data cleaned 
[INFO ] 2024-05-10 11:45:23.740 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] monitor closed 
[INFO ] 2024-05-10 11:45:23.741 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] monitor closed 
[INFO ] 2024-05-10 11:45:23.743 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] close complete, cost 33 ms 
[INFO ] 2024-05-10 11:45:23.746 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] close complete, cost 36 ms 
[INFO ] 2024-05-10 11:45:26.411 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-10 11:45:26.432 - [任务 5] - Stop task milestones: 663d92ee73967f3f672ef6b2(任务 5)  
[INFO ] 2024-05-10 11:45:26.434 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-05-10 11:45:26.434 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-05-10 11:45:26.450 - [任务 5] - Remove memory task client succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:45:26.452 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:45:44.791 - [任务 5] - Task initialization... 
[INFO ] 2024-05-10 11:45:44.835 - [任务 5] - Start task milestones: 663d92ee73967f3f672ef6b2(任务 5) 
[INFO ] 2024-05-10 11:45:44.835 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-10 11:45:44.978 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-10 11:45:44.978 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:45:45.002 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:45:45.003 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] preload schema finished, cost 24 ms 
[INFO ] 2024-05-10 11:45:45.003 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] preload schema finished, cost 25 ms 
[INFO ] 2024-05-10 11:45:45.409 - [任务 5][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-10 11:45:45.468 - [任务 5][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-05-10 11:45:45.468 - [任务 5][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-05-10 11:45:45.471 - [任务 5][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-05-10 11:45:45.471 - [任务 5][CLAIM] - batch offset found: {"CLAIM":{"offset":null,"status":"RUNNING"}},stream offset not found. 
[INFO ] 2024-05-10 11:45:45.530 - [任务 5][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-10 11:45:45.530 - [任务 5][CLAIM] - Initial sync started 
[INFO ] 2024-05-10 11:45:45.530 - [任务 5][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-05-10 11:45:45.536 - [任务 5][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-05-10 11:45:45.586 - [任务 5][CLAIM] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-05-10 11:45:45.586 - [任务 5][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-10 11:45:45.586 - [任务 5][CLAIM] - Initial sync completed 
[INFO ] 2024-05-10 11:45:46.630 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] running status set to false 
[INFO ] 2024-05-10 11:45:46.630 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] running status set to false 
[INFO ] 2024-05-10 11:45:46.673 - [任务 5][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:45:46.673 - [任务 5][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:45:46.673 - [任务 5][test1] - PDK connector node released: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:45:46.673 - [任务 5][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:45:46.674 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] schema data cleaned 
[INFO ] 2024-05-10 11:45:46.677 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] schema data cleaned 
[INFO ] 2024-05-10 11:45:46.678 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] monitor closed 
[INFO ] 2024-05-10 11:45:46.680 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] monitor closed 
[INFO ] 2024-05-10 11:45:46.681 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] close complete, cost 60 ms 
[INFO ] 2024-05-10 11:45:46.681 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] close complete, cost 59 ms 
[INFO ] 2024-05-10 11:45:51.519 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-10 11:45:51.565 - [任务 5] - Stop task milestones: 663d92ee73967f3f672ef6b2(任务 5)  
[INFO ] 2024-05-10 11:45:51.565 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-05-10 11:45:51.565 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-05-10 11:45:51.604 - [任务 5] - Remove memory task client succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:45:51.605 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:45:54.248 - [任务 5] - Task initialization... 
[INFO ] 2024-05-10 11:45:54.262 - [任务 5] - Start task milestones: 663d92ee73967f3f672ef6b2(任务 5) 
[INFO ] 2024-05-10 11:45:54.262 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-10 11:45:54.348 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-10 11:45:54.348 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:45:54.348 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:45:54.381 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] preload schema finished, cost 32 ms 
[INFO ] 2024-05-10 11:45:54.381 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] preload schema finished, cost 32 ms 
[INFO ] 2024-05-10 11:45:54.713 - [任务 5][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-10 11:45:54.856 - [任务 5][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-05-10 11:45:54.857 - [任务 5][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-05-10 11:45:54.857 - [任务 5][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-05-10 11:45:54.860 - [任务 5][CLAIM] - batch offset found: {"CLAIM":{"offset":null,"status":"RUNNING"}},stream offset not found. 
[INFO ] 2024-05-10 11:45:54.860 - [任务 5][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-10 11:45:54.935 - [任务 5][CLAIM] - Initial sync started 
[INFO ] 2024-05-10 11:45:54.941 - [任务 5][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-05-10 11:45:54.993 - [任务 5][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-05-10 11:45:54.993 - [任务 5][CLAIM] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-05-10 11:45:54.993 - [任务 5][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-10 11:45:55.199 - [任务 5][CLAIM] - Initial sync completed 
[INFO ] 2024-05-10 11:45:56.043 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] running status set to false 
[INFO ] 2024-05-10 11:45:56.043 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] running status set to false 
[INFO ] 2024-05-10 11:45:56.077 - [任务 5][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:45:56.077 - [任务 5][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:45:56.077 - [任务 5][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:45:56.077 - [任务 5][test1] - PDK connector node released: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:45:56.078 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] schema data cleaned 
[INFO ] 2024-05-10 11:45:56.078 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] schema data cleaned 
[INFO ] 2024-05-10 11:45:56.081 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] monitor closed 
[INFO ] 2024-05-10 11:45:56.081 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] monitor closed 
[INFO ] 2024-05-10 11:45:56.085 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] close complete, cost 48 ms 
[INFO ] 2024-05-10 11:45:56.085 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] close complete, cost 48 ms 
[INFO ] 2024-05-10 11:45:56.624 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-10 11:45:56.661 - [任务 5] - Stop task milestones: 663d92ee73967f3f672ef6b2(任务 5)  
[INFO ] 2024-05-10 11:45:56.662 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-05-10 11:45:56.663 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-05-10 11:45:56.681 - [任务 5] - Remove memory task client succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:45:56.681 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:45:58.822 - [任务 5] - Task initialization... 
[INFO ] 2024-05-10 11:45:58.822 - [任务 5] - Start task milestones: 663d92ee73967f3f672ef6b2(任务 5) 
[INFO ] 2024-05-10 11:45:58.891 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-10 11:45:58.891 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-10 11:45:58.934 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:45:58.934 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:45:58.953 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] preload schema finished, cost 17 ms 
[INFO ] 2024-05-10 11:45:58.953 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] preload schema finished, cost 17 ms 
[INFO ] 2024-05-10 11:45:59.233 - [任务 5][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-05-10 11:45:59.233 - [任务 5][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-05-10 11:45:59.234 - [任务 5][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-05-10 11:45:59.234 - [任务 5][CLAIM] - batch offset found: {"CLAIM":{"offset":null,"status":"RUNNING"}},stream offset not found. 
[INFO ] 2024-05-10 11:45:59.267 - [任务 5][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-10 11:45:59.267 - [任务 5][CLAIM] - Initial sync started 
[INFO ] 2024-05-10 11:45:59.267 - [任务 5][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-05-10 11:45:59.272 - [任务 5][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-05-10 11:45:59.322 - [任务 5][CLAIM] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-05-10 11:45:59.322 - [任务 5][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-10 11:45:59.322 - [任务 5][CLAIM] - Initial sync completed 
[INFO ] 2024-05-10 11:45:59.477 - [任务 5][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-10 11:46:00.561 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] running status set to false 
[INFO ] 2024-05-10 11:46:00.562 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] running status set to false 
[INFO ] 2024-05-10 11:46:00.622 - [任务 5][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:46:00.622 - [任务 5][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:46:00.622 - [任务 5][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:46:00.623 - [任务 5][test1] - PDK connector node released: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:46:00.623 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] schema data cleaned 
[INFO ] 2024-05-10 11:46:00.626 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] schema data cleaned 
[INFO ] 2024-05-10 11:46:00.627 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] monitor closed 
[INFO ] 2024-05-10 11:46:00.630 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] monitor closed 
[INFO ] 2024-05-10 11:46:00.631 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] close complete, cost 115 ms 
[INFO ] 2024-05-10 11:46:00.631 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] close complete, cost 116 ms 
[INFO ] 2024-05-10 11:46:01.739 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-10 11:46:01.739 - [任务 5] - Stop task milestones: 663d92ee73967f3f672ef6b2(任务 5)  
[INFO ] 2024-05-10 11:46:01.749 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-05-10 11:46:01.749 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-05-10 11:46:01.780 - [任务 5] - Remove memory task client succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:46:01.787 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:54:58.731 - [任务 5] - Task initialization... 
[INFO ] 2024-05-10 11:54:58.736 - [任务 5] - Start task milestones: 663d92ee73967f3f672ef6b2(任务 5) 
[INFO ] 2024-05-10 11:54:58.736 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-10 11:54:58.736 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-10 11:54:58.736 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:54:58.736 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:54:58.736 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] preload schema finished, cost 88 ms 
[INFO ] 2024-05-10 11:54:58.736 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] preload schema finished, cost 88 ms 
[INFO ] 2024-05-10 11:54:59.426 - [任务 5][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-10 11:54:59.587 - [任务 5][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-05-10 11:54:59.588 - [任务 5][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-05-10 11:54:59.588 - [任务 5][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-10 11:54:59.588 - [任务 5][CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-05-10 11:54:59.588 - [任务 5][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-10 11:54:59.678 - [任务 5][CLAIM] - Initial sync started 
[INFO ] 2024-05-10 11:54:59.679 - [任务 5][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-05-10 11:54:59.686 - [任务 5][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-05-10 11:54:59.740 - [任务 5][CLAIM] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-05-10 11:54:59.740 - [任务 5][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-10 11:54:59.941 - [任务 5][CLAIM] - Initial sync completed 
[INFO ] 2024-05-10 11:55:00.824 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] running status set to false 
[INFO ] 2024-05-10 11:55:00.825 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] running status set to false 
[INFO ] 2024-05-10 11:55:00.846 - [任务 5][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:55:00.847 - [任务 5][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:55:00.847 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] schema data cleaned 
[INFO ] 2024-05-10 11:55:00.850 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] monitor closed 
[INFO ] 2024-05-10 11:55:00.851 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] close complete, cost 85 ms 
[INFO ] 2024-05-10 11:55:00.866 - [任务 5][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:55:00.867 - [任务 5][test1] - PDK connector node released: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:55:00.869 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] schema data cleaned 
[INFO ] 2024-05-10 11:55:00.869 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] monitor closed 
[INFO ] 2024-05-10 11:55:01.076 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] close complete, cost 103 ms 
[INFO ] 2024-05-10 11:55:02.208 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-10 11:55:02.208 - [任务 5] - Stop task milestones: 663d92ee73967f3f672ef6b2(任务 5)  
[INFO ] 2024-05-10 11:55:02.229 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-05-10 11:55:02.229 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-05-10 11:55:02.251 - [任务 5] - Remove memory task client succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:55:02.251 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:58:50.271 - [任务 5] - Task initialization... 
[INFO ] 2024-05-10 11:58:50.297 - [任务 5] - Start task milestones: 663d92ee73967f3f672ef6b2(任务 5) 
[INFO ] 2024-05-10 11:58:50.372 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-10 11:58:50.372 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-10 11:58:50.442 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:58:50.442 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] start preload schema,table counts: 1 
[INFO ] 2024-05-10 11:58:50.465 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] preload schema finished, cost 26 ms 
[INFO ] 2024-05-10 11:58:50.466 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] preload schema finished, cost 24 ms 
[INFO ] 2024-05-10 11:58:51.276 - [任务 5][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-10 11:58:51.445 - [任务 5][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-05-10 11:58:51.445 - [任务 5][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-05-10 11:58:51.445 - [任务 5][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-10 11:58:51.446 - [任务 5][CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-05-10 11:58:51.446 - [任务 5][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-10 11:58:51.495 - [任务 5][CLAIM] - Initial sync started 
[INFO ] 2024-05-10 11:58:51.502 - [任务 5][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-05-10 11:58:51.502 - [任务 5][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-05-10 11:58:51.562 - [任务 5][CLAIM] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-05-10 11:58:51.564 - [任务 5][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-10 11:58:51.770 - [任务 5][CLAIM] - Initial sync completed 
[INFO ] 2024-05-10 11:58:52.689 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] running status set to false 
[INFO ] 2024-05-10 11:58:52.696 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] running status set to false 
[INFO ] 2024-05-10 11:58:52.727 - [任务 5][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:58:52.727 - [任务 5][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 11:58:52.727 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] schema data cleaned 
[INFO ] 2024-05-10 11:58:52.728 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] monitor closed 
[INFO ] 2024-05-10 11:58:52.734 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] close complete, cost 62 ms 
[INFO ] 2024-05-10 11:58:52.740 - [任务 5][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:58:52.740 - [任务 5][test1] - PDK connector node released: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 11:58:52.741 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] schema data cleaned 
[INFO ] 2024-05-10 11:58:52.741 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] monitor closed 
[INFO ] 2024-05-10 11:58:52.741 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] close complete, cost 66 ms 
[INFO ] 2024-05-10 11:58:57.504 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-10 11:58:57.531 - [任务 5] - Stop task milestones: 663d92ee73967f3f672ef6b2(任务 5)  
[INFO ] 2024-05-10 11:58:57.531 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-05-10 11:58:57.531 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-05-10 11:58:57.552 - [任务 5] - Remove memory task client succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 11:58:57.555 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 12:13:17.226 - [任务 5] - Task initialization... 
[INFO ] 2024-05-10 12:13:17.230 - [任务 5] - Start task milestones: 663d92ee73967f3f672ef6b2(任务 5) 
[INFO ] 2024-05-10 12:13:17.230 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-10 12:13:17.230 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-10 12:13:17.230 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] start preload schema,table counts: 1 
[INFO ] 2024-05-10 12:13:17.230 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] start preload schema,table counts: 1 
[INFO ] 2024-05-10 12:13:17.230 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] preload schema finished, cost 36 ms 
[INFO ] 2024-05-10 12:13:17.230 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] preload schema finished, cost 36 ms 
[INFO ] 2024-05-10 12:13:17.832 - [任务 5][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-10 12:13:17.993 - [任务 5][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-05-10 12:13:17.994 - [任务 5][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-05-10 12:13:17.994 - [任务 5][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-10 12:13:17.994 - [任务 5][CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-05-10 12:13:17.994 - [任务 5][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-10 12:13:18.052 - [任务 5][CLAIM] - Initial sync started 
[INFO ] 2024-05-10 12:13:21.417 - [任务 5][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-05-10 12:14:50.849 - [任务 5][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-05-10 12:20:11.892 - [任务 5][CLAIM] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-05-10 12:20:11.945 - [任务 5][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-10 12:20:13.269 - [任务 5][CLAIM] - Initial sync completed 
[INFO ] 2024-05-10 12:20:13.271 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] running status set to false 
[INFO ] 2024-05-10 12:20:13.271 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] running status set to false 
[INFO ] 2024-05-10 12:20:13.271 - [任务 5][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 12:20:13.271 - [任务 5][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 12:20:13.271 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] schema data cleaned 
[INFO ] 2024-05-10 12:20:13.274 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] monitor closed 
[INFO ] 2024-05-10 12:20:13.274 - [任务 5][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 12:20:13.275 - [任务 5][test1] - PDK connector node released: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 12:20:13.275 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] schema data cleaned 
[INFO ] 2024-05-10 12:20:13.280 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] monitor closed 
[INFO ] 2024-05-10 12:20:13.283 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] close complete, cost 56 ms 
[INFO ] 2024-05-10 12:20:13.284 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] close complete, cost 54 ms 
[INFO ] 2024-05-10 12:20:16.849 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-10 12:20:16.849 - [任务 5] - Stop task milestones: 663d92ee73967f3f672ef6b2(任务 5)  
[INFO ] 2024-05-10 12:20:16.861 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-05-10 12:20:16.861 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-05-10 12:20:16.880 - [任务 5] - Remove memory task client succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 12:20:16.880 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 12:26:22.989 - [任务 5] - Task initialization... 
[INFO ] 2024-05-10 12:26:22.990 - [任务 5] - Start task milestones: 663d92ee73967f3f672ef6b2(任务 5) 
[INFO ] 2024-05-10 12:26:22.990 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-10 12:26:22.990 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-10 12:26:22.990 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] start preload schema,table counts: 1 
[INFO ] 2024-05-10 12:26:22.990 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] start preload schema,table counts: 1 
[INFO ] 2024-05-10 12:26:22.990 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] preload schema finished, cost 38 ms 
[INFO ] 2024-05-10 12:26:22.990 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] preload schema finished, cost 38 ms 
[INFO ] 2024-05-10 12:26:23.498 - [任务 5][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-10 12:26:23.672 - [任务 5][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-05-10 12:26:23.673 - [任务 5][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-05-10 12:26:23.673 - [任务 5][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-10 12:26:23.673 - [任务 5][CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-05-10 12:26:23.673 - [任务 5][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-10 12:26:23.729 - [任务 5][CLAIM] - Initial sync started 
[INFO ] 2024-05-10 12:26:23.729 - [任务 5][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-05-10 12:26:34.694 - [任务 5][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-05-10 12:26:34.794 - [任务 5][CLAIM] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-05-10 12:27:40.966 - [任务 5][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-10 12:27:41.111 - [任务 5][CLAIM] - Initial sync completed 
[INFO ] 2024-05-10 12:27:42.476 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] running status set to false 
[INFO ] 2024-05-10 12:27:42.477 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] running status set to false 
[INFO ] 2024-05-10 12:27:42.509 - [任务 5][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 12:27:42.509 - [任务 5][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 12:27:42.510 - [任务 5][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 12:27:42.510 - [任务 5][test1] - PDK connector node released: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 12:27:42.510 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] schema data cleaned 
[INFO ] 2024-05-10 12:27:42.512 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] schema data cleaned 
[INFO ] 2024-05-10 12:27:42.513 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] monitor closed 
[INFO ] 2024-05-10 12:27:42.516 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] monitor closed 
[INFO ] 2024-05-10 12:27:42.517 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] close complete, cost 85 ms 
[INFO ] 2024-05-10 12:27:42.517 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] close complete, cost 86 ms 
[INFO ] 2024-05-10 12:27:46.020 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-10 12:27:46.020 - [任务 5] - Stop task milestones: 663d92ee73967f3f672ef6b2(任务 5)  
[INFO ] 2024-05-10 12:27:46.042 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-05-10 12:27:46.043 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-05-10 12:27:46.068 - [任务 5] - Remove memory task client succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 12:27:46.069 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 14:50:53.137 - [任务 5] - Task initialization... 
[INFO ] 2024-05-10 14:50:53.183 - [任务 5] - Start task milestones: 663d92ee73967f3f672ef6b2(任务 5) 
[INFO ] 2024-05-10 14:50:53.183 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-10 14:50:53.237 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-10 14:50:53.346 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] start preload schema,table counts: 1 
[INFO ] 2024-05-10 14:50:53.346 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] start preload schema,table counts: 1 
[INFO ] 2024-05-10 14:50:53.400 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] preload schema finished, cost 51 ms 
[INFO ] 2024-05-10 14:50:53.400 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] preload schema finished, cost 51 ms 
[INFO ] 2024-05-10 14:50:54.274 - [任务 5][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-10 14:50:54.458 - [任务 5][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-05-10 14:50:54.458 - [任务 5][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-05-10 14:50:54.458 - [任务 5][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-10 14:50:54.459 - [任务 5][CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-05-10 14:50:54.539 - [任务 5][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-10 14:50:54.539 - [任务 5][CLAIM] - Initial sync started 
[INFO ] 2024-05-10 14:50:54.539 - [任务 5][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-05-10 14:50:54.548 - [任务 5][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-05-10 14:50:54.663 - [任务 5][CLAIM] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-05-10 14:50:54.664 - [任务 5][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-10 14:50:54.869 - [任务 5][CLAIM] - Initial sync completed 
[INFO ] 2024-05-10 14:50:55.780 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] running status set to false 
[INFO ] 2024-05-10 14:50:55.825 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] running status set to false 
[INFO ] 2024-05-10 14:50:55.825 - [任务 5][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 14:50:55.826 - [任务 5][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 14:50:55.826 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] schema data cleaned 
[INFO ] 2024-05-10 14:50:55.829 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] monitor closed 
[INFO ] 2024-05-10 14:50:55.830 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] close complete, cost 61 ms 
[INFO ] 2024-05-10 14:50:55.830 - [任务 5][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 14:50:55.830 - [任务 5][test1] - PDK connector node released: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 14:50:55.831 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] schema data cleaned 
[INFO ] 2024-05-10 14:50:55.835 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] monitor closed 
[INFO ] 2024-05-10 14:50:55.835 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] close complete, cost 52 ms 
[INFO ] 2024-05-10 14:51:29.470 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-10 14:51:29.470 - [任务 5] - Stop task milestones: 663d92ee73967f3f672ef6b2(任务 5)  
[INFO ] 2024-05-10 14:51:29.484 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-05-10 14:51:56.510 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-05-10 14:52:51.846 - [任务 5] - Remove memory task client succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 14:52:51.848 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 15:01:29.323 - [任务 5] - Task initialization... 
[INFO ] 2024-05-10 15:01:29.324 - [任务 5] - Start task milestones: 663d92ee73967f3f672ef6b2(任务 5) 
[INFO ] 2024-05-10 15:01:29.324 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-10 15:01:29.324 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-10 15:01:29.324 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] start preload schema,table counts: 1 
[INFO ] 2024-05-10 15:01:29.324 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] start preload schema,table counts: 1 
[INFO ] 2024-05-10 15:01:29.324 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] preload schema finished, cost 19 ms 
[INFO ] 2024-05-10 15:01:29.324 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] preload schema finished, cost 19 ms 
[INFO ] 2024-05-10 15:01:29.841 - [任务 5][CLAIM] - Source node "CLAIM" read batch size: 500 
[INFO ] 2024-05-10 15:01:29.841 - [任务 5][CLAIM] - Source node "CLAIM" event queue capacity: 1000 
[INFO ] 2024-05-10 15:01:29.841 - [任务 5][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-10 15:01:29.841 - [任务 5][CLAIM] - batch offset found: {},stream offset not found. 
[INFO ] 2024-05-10 15:01:29.841 - [任务 5][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-10 15:01:29.898 - [任务 5][CLAIM] - Initial sync started 
[INFO ] 2024-05-10 15:03:49.117 - [任务 5][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-05-10 15:03:49.122 - [任务 5][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-05-10 15:08:09.275 - [任务 5][CLAIM] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-05-10 15:08:09.281 - [任务 5][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-10 15:08:09.312 - [任务 5][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-10 15:08:09.515 - [任务 5][CLAIM] - Initial sync completed 
[INFO ] 2024-05-10 15:08:10.289 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] running status set to false 
[INFO ] 2024-05-10 15:08:10.289 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] running status set to false 
[INFO ] 2024-05-10 15:08:10.324 - [任务 5][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 15:08:10.331 - [任务 5][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9144e2bc-d452-4542-95ef-e63be26c2e0d 
[INFO ] 2024-05-10 15:08:10.331 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] schema data cleaned 
[INFO ] 2024-05-10 15:08:10.331 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] monitor closed 
[INFO ] 2024-05-10 15:08:10.333 - [任务 5][CLAIM] - Node CLAIM[9144e2bc-d452-4542-95ef-e63be26c2e0d] close complete, cost 48 ms 
[INFO ] 2024-05-10 15:08:10.333 - [任务 5][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 15:08:10.334 - [任务 5][test1] - PDK connector node released: HazelcastTargetPdkDataNode-28ed256a-6e06-460b-bd26-55ad47a6878b 
[INFO ] 2024-05-10 15:08:10.334 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] schema data cleaned 
[INFO ] 2024-05-10 15:08:10.351 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] monitor closed 
[INFO ] 2024-05-10 15:08:10.351 - [任务 5][test1] - Node test1[28ed256a-6e06-460b-bd26-55ad47a6878b] close complete, cost 59 ms 
[INFO ] 2024-05-10 15:08:14.013 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-10 15:08:14.015 - [任务 5] - Stop task milestones: 663d92ee73967f3f672ef6b2(任务 5)  
[INFO ] 2024-05-10 15:08:14.027 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-05-10 15:08:14.029 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-05-10 15:08:14.051 - [任务 5] - Remove memory task client succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
[INFO ] 2024-05-10 15:08:14.054 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[663d92ee73967f3f672ef6b2] 
