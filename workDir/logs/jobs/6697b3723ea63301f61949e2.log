[INFO ] 2024-07-19 06:52:56.123 - [来自source的共享挖掘任务] - Start task milestones: 6697b3723ea63301f61949e2(来自source的共享挖掘任务) 
[INFO ] 2024-07-19 06:52:56.750 - [来自source的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-19 06:52:56.952 - [来自source的共享挖掘任务] - The engine receives 来自source的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-19 06:52:57.319 - [来自source的共享挖掘任务][source] - Node source[3f034b0fc44440439ec3703d2fbfe88e] start preload schema,table counts: 1 
[INFO ] 2024-07-19 06:52:57.320 - [来自source的共享挖掘任务][source] - Node source[3f034b0fc44440439ec3703d2fbfe88e] preload schema finished, cost 0 ms 
[INFO ] 2024-07-19 06:52:57.328 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-19 06:52:57.330 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-19 06:53:01.478 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6697b37266ab5ede8accc9a4, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697b3373ea63301f6194997_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_666389627, shareCdcTaskId=6697b3723ea63301f61949e2, connectionId=6697b3373ea63301f6194997) 
[INFO ] 2024-07-19 06:53:01.681 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自source的共享挖掘任务_POLICY', name space: 'tapdatav393.ExternalStorage_SHARE_CDC_666389627', head seq: 0, tail seq: -1 
[INFO ] 2024-07-19 06:53:01.883 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav393?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-19 06:53:02.373 - [来自source的共享挖掘任务][source] - Source node "source" read batch size: 2000 
[INFO ] 2024-07-19 06:53:02.377 - [来自source的共享挖掘任务][source] - Source node "source" event queue capacity: 4000 
[INFO ] 2024-07-19 06:53:02.387 - [来自source的共享挖掘任务][source] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-19 06:53:02.489 - [来自source的共享挖掘任务][source] - batch offset found: {},stream offset found: {"cdcOffset":1721218035,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-19 06:53:02.491 - [来自source的共享挖掘任务][source] - Starting stream read, table list: [POLICY], offset: {"cdcOffset":1721218035,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-19 06:53:02.652 - [来自source的共享挖掘任务][source] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-19 06:53:02.652 - [来自source的共享挖掘任务][source] - Exception skipping - The current exception does not match the skip exception strategy, message: Increment start point exceeds the log time window of mongodb, start point: 1721218035 
[ERROR] 2024-07-19 06:53:02.701 - [来自source的共享挖掘任务][source] - com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1721343182, "i": 10}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721343182, "i": 10}}, "signature": {"hash": {"$binary": {"base64": "u5fa8tAVar49tEYTQi09L/W+pS0=", "subType": "00"}}, "keyId": 7376103549123428362}}} <-- Error Message -->
com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1721343182, "i": 10}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721343182, "i": 10}}, "signature": {"hash": {"$binary": {"base64": "u5fa8tAVar49tEYTQi09L/W+pS0=", "subType": "00"}}, "keyId": 7376103549123428362}}}

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1721343182, "i": 10}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721343182, "i": 10}}, "signature": {"hash": {"$binary": {"base64": "u5fa8tAVar49tEYTQi09L/W+pS0=", "subType": "00"}}, "keyId": 7376103549123428362}}}
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:114)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:643)
	...

<-- Full Stack Trace -->
com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1721343182, "i": 10}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721343182, "i": 10}}, "signature": {"hash": {"$binary": {"base64": "u5fa8tAVar49tEYTQi09L/W+pS0=", "subType": "00"}}, "keyId": 7376103549123428362}}}
	at io.tapdata.mongodb.reader.MongodbV4StreamReader.read(MongodbV4StreamReader.java:258)
	at io.tapdata.mongodb.MongodbConnector.doStreamRead(MongodbConnector.java:1559)
	at io.tapdata.mongodb.MongodbConnector.streamRead(MongodbConnector.java:1553)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:612)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.mongodb.MongoCommandException: Command failed with error 286 (ChangeStreamHistoryLost): 'Resume of change stream was not possible, as the resume point may no longer be in the oplog.' on server localhost:27017. The full response is {"errorLabels": ["NonResumableChangeStreamError"], "operationTime": {"$timestamp": {"t": 1721343182, "i": 10}}, "ok": 0.0, "errmsg": "Resume of change stream was not possible, as the resume point may no longer be in the oplog.", "code": 286, "codeName": "ChangeStreamHistoryLost", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721343182, "i": 10}}, "signature": {"hash": {"$binary": {"base64": "u5fa8tAVar49tEYTQi09L/W+pS0=", "subType": "00"}}, "keyId": 7376103549123428362}}}
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:205)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:443)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:114)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:643)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:73)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:204)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:122)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:87)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:76)
	at com.mongodb.internal.connection.DefaultServer$OperationCountTrackingConnection.command(DefaultServer.java:288)
	at com.mongodb.internal.operation.CommandOperationHelper.createReadCommandAndExecute(CommandOperationHelper.java:239)
	at com.mongodb.internal.operation.CommandOperationHelper.lambda$executeRetryableRead$4(CommandOperationHelper.java:220)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$0(OperationHelper.java:358)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$1(OperationHelper.java:357)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.withSourceAndConnection(OperationHelper.java:356)
	at com.mongodb.internal.operation.CommandOperationHelper.lambda$executeRetryableRead$5(CommandOperationHelper.java:218)
	at com.mongodb.internal.async.function.RetryingSyncSupplier.get(RetryingSyncSupplier.java:67)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:223)
	at com.mongodb.internal.operation.CommandOperationHelper.executeRetryableRead(CommandOperationHelper.java:204)
	at com.mongodb.internal.operation.AggregateOperationImpl.execute(AggregateOperationImpl.java:191)
	at com.mongodb.internal.operation.ChangeStreamOperation.lambda$execute$0(ChangeStreamOperation.java:187)
	at com.mongodb.internal.operation.OperationHelper.withReadConnectionSource(OperationHelper.java:321)
	at com.mongodb.internal.operation.ChangeStreamOperation.execute(ChangeStreamOperation.java:185)
	at com.mongodb.internal.operation.ChangeStreamOperation.execute(ChangeStreamOperation.java:55)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:185)
	at com.mongodb.client.internal.ChangeStreamIterableImpl.execute(ChangeStreamIterableImpl.java:212)
	at com.mongodb.client.internal.ChangeStreamIterableImpl.cursor(ChangeStreamIterableImpl.java:187)
	at io.tapdata.mongodb.reader.MongodbV4StreamReader.read(MongodbV4StreamReader.java:104)
	... 22 more

[INFO ] 2024-07-19 06:53:02.702 - [来自source的共享挖掘任务][source] - Job suspend in error handle 
[INFO ] 2024-07-19 06:53:03.098 - [来自source的共享挖掘任务][source] - Node source[3f034b0fc44440439ec3703d2fbfe88e] running status set to false 
[INFO ] 2024-07-19 06:53:03.143 - [来自source的共享挖掘任务][source] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-3f034b0fc44440439ec3703d2fbfe88e 
[INFO ] 2024-07-19 06:53:03.144 - [来自source的共享挖掘任务][source] - PDK connector node released: HazelcastSourcePdkShareCDCNode-3f034b0fc44440439ec3703d2fbfe88e 
[INFO ] 2024-07-19 06:53:03.148 - [来自source的共享挖掘任务][source] - Node source[3f034b0fc44440439ec3703d2fbfe88e] schema data cleaned 
[INFO ] 2024-07-19 06:53:03.148 - [来自source的共享挖掘任务][source] - Node source[3f034b0fc44440439ec3703d2fbfe88e] monitor closed 
[INFO ] 2024-07-19 06:53:03.155 - [来自source的共享挖掘任务][source] - Node source[3f034b0fc44440439ec3703d2fbfe88e] close complete, cost 54 ms 
[INFO ] 2024-07-19 06:53:03.156 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[f50af6639357457c8ac933a7039ba3b0] running status set to false 
[INFO ] 2024-07-19 06:53:03.158 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-19 06:53:03.159 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-19 06:53:03.160 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[f50af6639357457c8ac933a7039ba3b0] schema data cleaned 
[INFO ] 2024-07-19 06:53:03.160 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[f50af6639357457c8ac933a7039ba3b0] monitor closed 
[INFO ] 2024-07-19 06:53:03.161 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[f50af6639357457c8ac933a7039ba3b0] close complete, cost 6 ms 
[INFO ] 2024-07-19 06:53:06.366 - [来自source的共享挖掘任务] - Task [来自source的共享挖掘任务] cannot retry, reason: Task retry service not start 
[INFO ] 2024-07-19 06:53:06.369 - [来自source的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-19 06:53:06.373 - [来自source的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1e1e7ee7 
[INFO ] 2024-07-19 06:53:06.373 - [来自source的共享挖掘任务] - Stop task milestones: 6697b3723ea63301f61949e2(来自source的共享挖掘任务)  
[INFO ] 2024-07-19 06:53:06.569 - [来自source的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-19 06:53:06.570 - [来自source的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-19 06:53:06.644 - [来自source的共享挖掘任务] - Remove memory task client succeed, task: 来自source的共享挖掘任务[6697b3723ea63301f61949e2] 
[INFO ] 2024-07-19 06:53:06.645 - [来自source的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自source的共享挖掘任务[6697b3723ea63301f61949e2] 
