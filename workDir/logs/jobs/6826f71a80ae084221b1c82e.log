[TRACE] 2025-05-16 16:41:58.825 - [任务 58] - Task initialization... 
[TRACE] 2025-05-16 16:41:58.825 - [任务 58] - Start task milestones: 6826f71a80ae084221b1c82e(任务 58) 
[INFO ] 2025-05-16 16:41:59.026 - [任务 58] - Loading table structure completed 
[TRACE] 2025-05-16 16:41:59.067 - [任务 58] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-05-16 16:41:59.067 - [任务 58] - The engine receives 任务 58 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-16 16:41:59.276 - [任务 58] - Task started 
[TRACE] 2025-05-16 16:41:59.411 - [任务 58][表编辑] - Node 表编辑[587de4a2-cfc2-4de1-9f1b-3b6a9753175d] start preload schema,table counts: 1 
[TRACE] 2025-05-16 16:41:59.415 - [任务 58][SybaseL] - Node SybaseL[a9053291-97bd-49a1-9b2a-9cabc7d5f1d2] start preload schema,table counts: 1 
[TRACE] 2025-05-16 16:41:59.418 - [任务 58][PG] - Node PG[888d9f9c-61e8-484e-9363-45ed32f27ac6] start preload schema,table counts: 1 
[TRACE] 2025-05-16 16:41:59.418 - [任务 58][SybaseL] - Node SybaseL[a9053291-97bd-49a1-9b2a-9cabc7d5f1d2] preload schema finished, cost 0 ms 
[TRACE] 2025-05-16 16:41:59.418 - [任务 58][表编辑] - Node 表编辑[587de4a2-cfc2-4de1-9f1b-3b6a9753175d] preload schema finished, cost 1 ms 
[TRACE] 2025-05-16 16:41:59.418 - [任务 58][PG] - Node PG[888d9f9c-61e8-484e-9363-45ed32f27ac6] preload schema finished, cost 0 ms 
[INFO ] 2025-05-16 16:41:59.418 - [任务 58][PG] - Enable partition table support for source database 
[TRACE] 2025-05-16 16:41:59.620 - [任务 58][表编辑] - Node table_rename_processor(表编辑: 587de4a2-cfc2-4de1-9f1b-3b6a9753175d) enable batch process 
[INFO ] 2025-05-16 16:42:00.155 - [任务 58][SybaseL] - Sink connector(SybaseL) initialization completed 
[TRACE] 2025-05-16 16:42:00.155 - [任务 58][SybaseL] - Node(SybaseL) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-16 16:42:00.155 - [任务 58][SybaseL] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-16 16:42:00.241 - [任务 58][SybaseL] - Apply table structure to target database 
[WARN ] 2025-05-16 16:42:00.241 - [任务 58][SybaseL] - Table goback_testSmallMoney not exists, skip drop 
[INFO ] 2025-05-16 16:42:00.414 - [任务 58][PG] - Source connector(PG) initialization completed 
[TRACE] 2025-05-16 16:42:00.414 - [任务 58][PG] - Source node "PG" read batch size: 100 
[TRACE] 2025-05-16 16:42:00.414 - [任务 58][PG] - Source node "PG" event queue capacity: 200 
[TRACE] 2025-05-16 16:42:00.414 - [任务 58][PG] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-16 16:42:00.469 - [任务 58][PG] - Starting batch read from 1 tables 
[TRACE] 2025-05-16 16:42:00.478 - [任务 58][PG] - Initial sync started 
[INFO ] 2025-05-16 16:42:00.478 - [任务 58][PG] - Starting batch read from table: testSmallMoney 
[TRACE] 2025-05-16 16:42:00.486 - [任务 58][PG] - Table testSmallMoney is going to be initial synced 
[TRACE] 2025-05-16 16:42:00.486 - [任务 58][PG] - Query snapshot row size completed: PG(888d9f9c-61e8-484e-9363-45ed32f27ac6) 
[INFO ] 2025-05-16 16:42:00.488 - [任务 58][PG] - Table testSmallMoney has been completed batch read 
[TRACE] 2025-05-16 16:42:00.488 - [任务 58][PG] - Initial sync completed 
[INFO ] 2025-05-16 16:42:00.488 - [任务 58][PG] - Batch read completed. 
[INFO ] 2025-05-16 16:42:00.488 - [任务 58][PG] - Task run completed 
[TRACE] 2025-05-16 16:42:01.913 - [任务 58][SybaseL] - Process after table "goback_testSmallMoney" initial sync finished, cost: 0 ms 
[INFO ] 2025-05-16 16:42:01.913 - [任务 58][SybaseL] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-05-16 16:42:01.977 - [任务 58][PG] - Node PG[888d9f9c-61e8-484e-9363-45ed32f27ac6] running status set to false 
[TRACE] 2025-05-16 16:42:01.980 - [任务 58][表编辑] - Node 表编辑[587de4a2-cfc2-4de1-9f1b-3b6a9753175d] running status set to false 
[TRACE] 2025-05-16 16:42:01.980 - [任务 58][SybaseL] - Node SybaseL[a9053291-97bd-49a1-9b2a-9cabc7d5f1d2] running status set to false 
[TRACE] 2025-05-16 16:42:01.991 - [任务 58][表编辑] - Node 表编辑[587de4a2-cfc2-4de1-9f1b-3b6a9753175d] schema data cleaned 
[TRACE] 2025-05-16 16:42:01.992 - [任务 58][表编辑] - Node 表编辑[587de4a2-cfc2-4de1-9f1b-3b6a9753175d] monitor closed 
[TRACE] 2025-05-16 16:42:02.011 - [任务 58][表编辑] - Node 表编辑[587de4a2-cfc2-4de1-9f1b-3b6a9753175d] close complete, cost 28 ms 
[TRACE] 2025-05-16 16:42:02.011 - [任务 58][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_888d9f9c-61e8-484e-9363-45ed32f27ac6_1747384920261 
[TRACE] 2025-05-16 16:42:02.012 - [任务 58][PG] - PDK connector node released: HazelcastSourcePdkDataNode_888d9f9c-61e8-484e-9363-45ed32f27ac6_1747384920261 
[TRACE] 2025-05-16 16:42:02.012 - [任务 58][PG] - Node PG[888d9f9c-61e8-484e-9363-45ed32f27ac6] schema data cleaned 
[TRACE] 2025-05-16 16:42:02.017 - [任务 58][PG] - Node PG[888d9f9c-61e8-484e-9363-45ed32f27ac6] monitor closed 
[TRACE] 2025-05-16 16:42:02.017 - [任务 58][PG] - Node PG[888d9f9c-61e8-484e-9363-45ed32f27ac6] close complete, cost 49 ms 
[TRACE] 2025-05-16 16:42:02.061 - [任务 58][SybaseL] - PDK connector node stopped: HazelcastTargetPdkDataNode_a9053291-97bd-49a1-9b2a-9cabc7d5f1d2_1747384920080 
[TRACE] 2025-05-16 16:42:02.061 - [任务 58][SybaseL] - PDK connector node released: HazelcastTargetPdkDataNode_a9053291-97bd-49a1-9b2a-9cabc7d5f1d2_1747384920080 
[TRACE] 2025-05-16 16:42:02.061 - [任务 58][SybaseL] - Node SybaseL[a9053291-97bd-49a1-9b2a-9cabc7d5f1d2] schema data cleaned 
[TRACE] 2025-05-16 16:42:02.061 - [任务 58][SybaseL] - Node SybaseL[a9053291-97bd-49a1-9b2a-9cabc7d5f1d2] monitor closed 
[TRACE] 2025-05-16 16:42:02.064 - [任务 58][SybaseL] - Node SybaseL[a9053291-97bd-49a1-9b2a-9cabc7d5f1d2] close complete, cost 92 ms 
[TRACE] 2025-05-16 16:42:05.532 - [任务 58] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-16 16:42:05.535 - [任务 58] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1981c168 
[TRACE] 2025-05-16 16:42:05.680 - [任务 58] - Stop task milestones: 6826f71a80ae084221b1c82e(任务 58)  
[TRACE] 2025-05-16 16:42:05.680 - [任务 58] - Stopped task aspect(s) 
[TRACE] 2025-05-16 16:42:05.680 - [任务 58] - Snapshot order controller have been removed 
[INFO ] 2025-05-16 16:42:05.681 - [任务 58] - Task stopped. 
[TRACE] 2025-05-16 16:42:10.706 - [任务 58] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-16 16:42:10.706 - [任务 58] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1981c168 
[TRACE] 2025-05-16 16:42:10.706 - [任务 58] - Stopped task aspect(s) 
[INFO ] 2025-05-16 16:42:10.706 - [任务 58] - Task stopped. 
[TRACE] 2025-05-16 16:42:10.758 - [任务 58] - Remove memory task client succeed, task: 任务 58[6826f71a80ae084221b1c82e] 
[TRACE] 2025-05-16 16:42:10.759 - [任务 58] - Destroy memory task client cache succeed, task: 任务 58[6826f71a80ae084221b1c82e] 
[TRACE] 2025-05-16 17:12:40.254 - [PG~Sybase] - Task initialization... 
[TRACE] 2025-05-16 17:12:40.254 - [PG~Sybase] - Start task milestones: 6826f71a80ae084221b1c82e(PG~Sybase) 
[INFO ] 2025-05-16 17:12:40.450 - [PG~Sybase] - Loading table structure completed 
[TRACE] 2025-05-16 17:12:40.450 - [PG~Sybase] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-16 17:12:40.530 - [PG~Sybase] - The engine receives PG~Sybase task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-16 17:12:40.530 - [PG~Sybase] - Task started 
[TRACE] 2025-05-16 17:12:40.550 - [PG~Sybase][表编辑] - Node 表编辑[587de4a2-cfc2-4de1-9f1b-3b6a9753175d] start preload schema,table counts: 1 
[TRACE] 2025-05-16 17:12:40.550 - [PG~Sybase][SybaseL] - Node SybaseL[a9053291-97bd-49a1-9b2a-9cabc7d5f1d2] start preload schema,table counts: 1 
[TRACE] 2025-05-16 17:12:40.550 - [PG~Sybase][PG] - Node PG[888d9f9c-61e8-484e-9363-45ed32f27ac6] start preload schema,table counts: 1 
[TRACE] 2025-05-16 17:12:40.550 - [PG~Sybase][SybaseL] - Node SybaseL[a9053291-97bd-49a1-9b2a-9cabc7d5f1d2] preload schema finished, cost 0 ms 
[TRACE] 2025-05-16 17:12:40.550 - [PG~Sybase][表编辑] - Node 表编辑[587de4a2-cfc2-4de1-9f1b-3b6a9753175d] preload schema finished, cost 0 ms 
[TRACE] 2025-05-16 17:12:40.550 - [PG~Sybase][PG] - Node PG[888d9f9c-61e8-484e-9363-45ed32f27ac6] preload schema finished, cost 0 ms 
[TRACE] 2025-05-16 17:12:40.550 - [PG~Sybase][表编辑] - Node table_rename_processor(表编辑: 587de4a2-cfc2-4de1-9f1b-3b6a9753175d) enable batch process 
[INFO ] 2025-05-16 17:12:40.550 - [PG~Sybase][PG] - Enable partition table support for source database 
[INFO ] 2025-05-16 17:12:41.214 - [PG~Sybase][PG] - Source connector(PG) initialization completed 
[TRACE] 2025-05-16 17:12:41.214 - [PG~Sybase][PG] - Source node "PG" read batch size: 100 
[TRACE] 2025-05-16 17:12:41.214 - [PG~Sybase][PG] - Source node "PG" event queue capacity: 200 
[TRACE] 2025-05-16 17:12:41.214 - [PG~Sybase][PG] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-16 17:12:41.238 - [PG~Sybase][SybaseL] - Sink connector(SybaseL) initialization completed 
[TRACE] 2025-05-16 17:12:41.241 - [PG~Sybase][SybaseL] - Node(SybaseL) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-16 17:12:41.241 - [PG~Sybase][SybaseL] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-16 17:12:41.269 - [PG~Sybase][SybaseL] - Apply table structure to target database 
[INFO ] 2025-05-16 17:12:41.269 - [PG~Sybase][PG] - Starting batch read from 1 tables 
[TRACE] 2025-05-16 17:12:41.275 - [PG~Sybase][PG] - Initial sync started 
[INFO ] 2025-05-16 17:12:41.275 - [PG~Sybase][PG] - Starting batch read from table: testSmallMoney 
[TRACE] 2025-05-16 17:12:41.275 - [PG~Sybase][PG] - Table testSmallMoney is going to be initial synced 
[TRACE] 2025-05-16 17:12:41.279 - [PG~Sybase][PG] - Query snapshot row size completed: PG(888d9f9c-61e8-484e-9363-45ed32f27ac6) 
[INFO ] 2025-05-16 17:12:41.279 - [PG~Sybase][PG] - Table testSmallMoney has been completed batch read 
[TRACE] 2025-05-16 17:12:41.279 - [PG~Sybase][PG] - Initial sync completed 
[INFO ] 2025-05-16 17:12:41.279 - [PG~Sybase][PG] - Batch read completed. 
[INFO ] 2025-05-16 17:12:41.303 - [PG~Sybase][PG] - Task run completed 
[TRACE] 2025-05-16 17:12:41.958 - [PG~Sybase][SybaseL] - Process after table "goback_testSmallMoney" initial sync finished, cost: 0 ms 
[INFO ] 2025-05-16 17:12:41.958 - [PG~Sybase][SybaseL] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-05-16 17:12:41.993 - [PG~Sybase][PG] - Node PG[888d9f9c-61e8-484e-9363-45ed32f27ac6] running status set to false 
[TRACE] 2025-05-16 17:12:41.993 - [PG~Sybase][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_888d9f9c-61e8-484e-9363-45ed32f27ac6_1747386761061 
[TRACE] 2025-05-16 17:12:41.994 - [PG~Sybase][表编辑] - Node 表编辑[587de4a2-cfc2-4de1-9f1b-3b6a9753175d] running status set to false 
[TRACE] 2025-05-16 17:12:41.994 - [PG~Sybase][PG] - PDK connector node released: HazelcastSourcePdkDataNode_888d9f9c-61e8-484e-9363-45ed32f27ac6_1747386761061 
[TRACE] 2025-05-16 17:12:41.994 - [PG~Sybase][PG] - Node PG[888d9f9c-61e8-484e-9363-45ed32f27ac6] schema data cleaned 
[TRACE] 2025-05-16 17:12:41.996 - [PG~Sybase][PG] - Node PG[888d9f9c-61e8-484e-9363-45ed32f27ac6] monitor closed 
[TRACE] 2025-05-16 17:12:41.997 - [PG~Sybase][表编辑] - Node 表编辑[587de4a2-cfc2-4de1-9f1b-3b6a9753175d] schema data cleaned 
[TRACE] 2025-05-16 17:12:41.997 - [PG~Sybase][PG] - Node PG[888d9f9c-61e8-484e-9363-45ed32f27ac6] close complete, cost 6 ms 
[TRACE] 2025-05-16 17:12:41.998 - [PG~Sybase][SybaseL] - Node SybaseL[a9053291-97bd-49a1-9b2a-9cabc7d5f1d2] running status set to false 
[TRACE] 2025-05-16 17:12:42.004 - [PG~Sybase][表编辑] - Node 表编辑[587de4a2-cfc2-4de1-9f1b-3b6a9753175d] monitor closed 
[TRACE] 2025-05-16 17:12:42.005 - [PG~Sybase][表编辑] - Node 表编辑[587de4a2-cfc2-4de1-9f1b-3b6a9753175d] close complete, cost 10 ms 
[TRACE] 2025-05-16 17:12:42.032 - [PG~Sybase][SybaseL] - PDK connector node stopped: HazelcastTargetPdkDataNode_a9053291-97bd-49a1-9b2a-9cabc7d5f1d2_1747386761182 
[TRACE] 2025-05-16 17:12:42.033 - [PG~Sybase][SybaseL] - PDK connector node released: HazelcastTargetPdkDataNode_a9053291-97bd-49a1-9b2a-9cabc7d5f1d2_1747386761182 
[TRACE] 2025-05-16 17:12:42.033 - [PG~Sybase][SybaseL] - Node SybaseL[a9053291-97bd-49a1-9b2a-9cabc7d5f1d2] schema data cleaned 
[TRACE] 2025-05-16 17:12:42.033 - [PG~Sybase][SybaseL] - Node SybaseL[a9053291-97bd-49a1-9b2a-9cabc7d5f1d2] monitor closed 
[TRACE] 2025-05-16 17:12:42.236 - [PG~Sybase][SybaseL] - Node SybaseL[a9053291-97bd-49a1-9b2a-9cabc7d5f1d2] close complete, cost 37 ms 
[TRACE] 2025-05-16 17:12:47.936 - [PG~Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-16 17:12:47.936 - [PG~Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1a55024b 
[TRACE] 2025-05-16 17:12:48.081 - [PG~Sybase] - Stop task milestones: 6826f71a80ae084221b1c82e(PG~Sybase)  
[TRACE] 2025-05-16 17:12:48.081 - [PG~Sybase] - Stopped task aspect(s) 
[TRACE] 2025-05-16 17:12:48.081 - [PG~Sybase] - Snapshot order controller have been removed 
[INFO ] 2025-05-16 17:12:48.287 - [PG~Sybase] - Task stopped. 
[TRACE] 2025-05-16 17:12:53.095 - [PG~Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-16 17:12:53.095 - [PG~Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1a55024b 
[TRACE] 2025-05-16 17:12:53.095 - [PG~Sybase] - Stopped task aspect(s) 
[INFO ] 2025-05-16 17:12:53.150 - [PG~Sybase] - Task stopped. 
[TRACE] 2025-05-16 17:12:53.154 - [PG~Sybase] - Remove memory task client succeed, task: PG~Sybase[6826f71a80ae084221b1c82e] 
[TRACE] 2025-05-16 17:12:53.154 - [PG~Sybase] - Destroy memory task client cache succeed, task: PG~Sybase[6826f71a80ae084221b1c82e] 
[TRACE] 2025-05-16 17:13:30.984 - [PG~Sybase] - Task initialization... 
[TRACE] 2025-05-16 17:13:31.085 - [PG~Sybase] - Start task milestones: 6826f71a80ae084221b1c82e(PG~Sybase) 
[INFO ] 2025-05-16 17:13:31.086 - [PG~Sybase] - Loading table structure completed 
[TRACE] 2025-05-16 17:13:31.197 - [PG~Sybase] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-16 17:13:31.197 - [PG~Sybase] - The engine receives PG~Sybase task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-16 17:13:31.221 - [PG~Sybase] - Task started 
[TRACE] 2025-05-16 17:13:31.221 - [PG~Sybase][PG] - Node PG[888d9f9c-61e8-484e-9363-45ed32f27ac6] start preload schema,table counts: 1 
[TRACE] 2025-05-16 17:13:31.221 - [PG~Sybase][表编辑] - Node 表编辑[587de4a2-cfc2-4de1-9f1b-3b6a9753175d] start preload schema,table counts: 1 
[TRACE] 2025-05-16 17:13:31.221 - [PG~Sybase][PG] - Node PG[888d9f9c-61e8-484e-9363-45ed32f27ac6] preload schema finished, cost 0 ms 
[TRACE] 2025-05-16 17:13:31.221 - [PG~Sybase][表编辑] - Node 表编辑[587de4a2-cfc2-4de1-9f1b-3b6a9753175d] preload schema finished, cost 0 ms 
[TRACE] 2025-05-16 17:13:31.221 - [PG~Sybase][SybaseL] - Node SybaseL[a9053291-97bd-49a1-9b2a-9cabc7d5f1d2] start preload schema,table counts: 1 
[INFO ] 2025-05-16 17:13:31.222 - [PG~Sybase][PG] - Enable partition table support for source database 
[TRACE] 2025-05-16 17:13:31.222 - [PG~Sybase][表编辑] - Node table_rename_processor(表编辑: 587de4a2-cfc2-4de1-9f1b-3b6a9753175d) enable batch process 
[TRACE] 2025-05-16 17:13:31.222 - [PG~Sybase][SybaseL] - Node SybaseL[a9053291-97bd-49a1-9b2a-9cabc7d5f1d2] preload schema finished, cost 0 ms 
[INFO ] 2025-05-16 17:13:31.737 - [PG~Sybase][SybaseL] - Sink connector(SybaseL) initialization completed 
[TRACE] 2025-05-16 17:13:31.740 - [PG~Sybase][SybaseL] - Node(SybaseL) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-16 17:13:31.740 - [PG~Sybase][SybaseL] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-05-16 17:13:31.942 - [PG~Sybase][SybaseL] - Apply table structure to target database 
[INFO ] 2025-05-16 17:13:32.021 - [PG~Sybase][PG] - Source connector(PG) initialization completed 
[TRACE] 2025-05-16 17:13:32.022 - [PG~Sybase][PG] - Source node "PG" read batch size: 100 
[TRACE] 2025-05-16 17:13:32.022 - [PG~Sybase][PG] - Source node "PG" event queue capacity: 200 
[TRACE] 2025-05-16 17:13:32.022 - [PG~Sybase][PG] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-05-16 17:13:32.064 - [PG~Sybase][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-05-16 17:13:32.064 - [PG~Sybase][PG] - new logical replication slot created, slotName:tapdata_cdc_3f6b7b8c_86a1_4e27_8f7d_72a3ec458bb2 
[INFO ] 2025-05-16 17:13:32.132 - [PG~Sybase][PG] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-05-16 17:13:32.132 - [PG~Sybase][PG] - Starting batch read from 1 tables 
[TRACE] 2025-05-16 17:13:32.139 - [PG~Sybase][PG] - Initial sync started 
[INFO ] 2025-05-16 17:13:32.139 - [PG~Sybase][PG] - Starting batch read from table: testSmallMoney 
[TRACE] 2025-05-16 17:13:32.143 - [PG~Sybase][PG] - Table testSmallMoney is going to be initial synced 
[TRACE] 2025-05-16 17:13:32.143 - [PG~Sybase][PG] - Query snapshot row size completed: PG(888d9f9c-61e8-484e-9363-45ed32f27ac6) 
[INFO ] 2025-05-16 17:13:32.143 - [PG~Sybase][PG] - Table testSmallMoney has been completed batch read 
[TRACE] 2025-05-16 17:13:32.143 - [PG~Sybase][PG] - Initial sync completed 
[INFO ] 2025-05-16 17:13:32.143 - [PG~Sybase][PG] - Batch read completed. 
[TRACE] 2025-05-16 17:13:32.143 - [PG~Sybase][PG] - Incremental sync starting... 
[TRACE] 2025-05-16 17:13:32.143 - [PG~Sybase][PG] - Initial sync completed 
[TRACE] 2025-05-16 17:13:32.143 - [PG~Sybase][PG] - Starting stream read, table list: [testSmallMoney], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-05-16 17:13:32.143 - [PG~Sybase][PG] - Starting incremental sync using database log parser 
[WARN ] 2025-05-16 17:13:32.149 - [PG~Sybase][PG] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-05-16 17:13:32.149 - [PG~Sybase][PG] - Using an existing logical replication slot, slotName:tapdata_cdc_3f6b7b8c_86a1_4e27_8f7d_72a3ec458bb2 
[TRACE] 2025-05-16 17:13:32.553 - [PG~Sybase][PG] - Connector PostgreSQL incremental start succeed, tables: [testSmallMoney], data change syncing 
[TRACE] 2025-05-16 17:13:33.440 - [PG~Sybase][SybaseL] - Process after table "goback_testSmallMoney" initial sync finished, cost: 0 ms 
[INFO ] 2025-05-16 17:13:33.441 - [PG~Sybase][SybaseL] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-05-16 17:23:19.577 - [PG~Sybase][PG] - Node PG[888d9f9c-61e8-484e-9363-45ed32f27ac6] running status set to false 
[TRACE] 2025-05-16 17:23:19.736 - [PG~Sybase][PG] - Incremental sync completed 
[TRACE] 2025-05-16 17:23:19.737 - [PG~Sybase][PG] - PDK connector node stopped: HazelcastSourcePdkDataNode_888d9f9c-61e8-484e-9363-45ed32f27ac6_1747386811873 
[TRACE] 2025-05-16 17:23:19.737 - [PG~Sybase][PG] - PDK connector node released: HazelcastSourcePdkDataNode_888d9f9c-61e8-484e-9363-45ed32f27ac6_1747386811873 
[TRACE] 2025-05-16 17:23:19.737 - [PG~Sybase][PG] - Node PG[888d9f9c-61e8-484e-9363-45ed32f27ac6] schema data cleaned 
[TRACE] 2025-05-16 17:23:19.737 - [PG~Sybase][PG] - Node PG[888d9f9c-61e8-484e-9363-45ed32f27ac6] monitor closed 
[TRACE] 2025-05-16 17:23:19.738 - [PG~Sybase][PG] - Node PG[888d9f9c-61e8-484e-9363-45ed32f27ac6] close complete, cost 161 ms 
[TRACE] 2025-05-16 17:23:19.738 - [PG~Sybase][表编辑] - Node 表编辑[587de4a2-cfc2-4de1-9f1b-3b6a9753175d] running status set to false 
[TRACE] 2025-05-16 17:23:19.738 - [PG~Sybase][表编辑] - Node 表编辑[587de4a2-cfc2-4de1-9f1b-3b6a9753175d] schema data cleaned 
[TRACE] 2025-05-16 17:23:19.738 - [PG~Sybase][表编辑] - Node 表编辑[587de4a2-cfc2-4de1-9f1b-3b6a9753175d] monitor closed 
[TRACE] 2025-05-16 17:23:19.738 - [PG~Sybase][表编辑] - Node 表编辑[587de4a2-cfc2-4de1-9f1b-3b6a9753175d] close complete, cost 0 ms 
[TRACE] 2025-05-16 17:23:19.739 - [PG~Sybase][SybaseL] - Node SybaseL[a9053291-97bd-49a1-9b2a-9cabc7d5f1d2] running status set to false 
[TRACE] 2025-05-16 17:23:19.840 - [PG~Sybase][SybaseL] - PDK connector node stopped: HazelcastTargetPdkDataNode_a9053291-97bd-49a1-9b2a-9cabc7d5f1d2_1747386811664 
[TRACE] 2025-05-16 17:23:19.840 - [PG~Sybase][SybaseL] - PDK connector node released: HazelcastTargetPdkDataNode_a9053291-97bd-49a1-9b2a-9cabc7d5f1d2_1747386811664 
[TRACE] 2025-05-16 17:23:19.840 - [PG~Sybase][SybaseL] - Node SybaseL[a9053291-97bd-49a1-9b2a-9cabc7d5f1d2] schema data cleaned 
[TRACE] 2025-05-16 17:23:19.840 - [PG~Sybase][SybaseL] - Node SybaseL[a9053291-97bd-49a1-9b2a-9cabc7d5f1d2] monitor closed 
[TRACE] 2025-05-16 17:23:19.840 - [PG~Sybase][SybaseL] - Node SybaseL[a9053291-97bd-49a1-9b2a-9cabc7d5f1d2] close complete, cost 101 ms 
[TRACE] 2025-05-16 17:23:23.751 - [PG~Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-16 17:23:23.752 - [PG~Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@57ea66b9 
[TRACE] 2025-05-16 17:23:23.914 - [PG~Sybase] - Stop task milestones: 6826f71a80ae084221b1c82e(PG~Sybase)  
[TRACE] 2025-05-16 17:23:23.915 - [PG~Sybase] - Stopped task aspect(s) 
[TRACE] 2025-05-16 17:23:23.915 - [PG~Sybase] - Snapshot order controller have been removed 
[INFO ] 2025-05-16 17:23:23.915 - [PG~Sybase] - Task stopped. 
[TRACE] 2025-05-16 17:23:28.931 - [PG~Sybase] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-16 17:23:28.931 - [PG~Sybase] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@57ea66b9 
[TRACE] 2025-05-16 17:23:28.932 - [PG~Sybase] - Stopped task aspect(s) 
[INFO ] 2025-05-16 17:23:28.932 - [PG~Sybase] - Task stopped. 
[TRACE] 2025-05-16 17:23:29.002 - [PG~Sybase] - Remove memory task client succeed, task: PG~Sybase[6826f71a80ae084221b1c82e] 
[TRACE] 2025-05-16 17:23:29.005 - [PG~Sybase] - Destroy memory task client cache succeed, task: PG~Sybase[6826f71a80ae084221b1c82e] 
