[INFO ] 2024-03-28 09:44:44.258 - [任务 30] - Start task milestones: 6604ca71c9eca62bdcca67ac(任务 30) 
[INFO ] 2024-03-28 09:44:44.258 - [任务 30] - Task initialization... 
[INFO ] 2024-03-28 09:44:44.291 - [任务 30] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 09:44:44.396 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 09:44:44.397 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:44:44.397 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:44:44.419 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] preload schema finished, cost 25 ms 
[INFO ] 2024-03-28 09:44:44.622 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] preload schema finished, cost 26 ms 
[INFO ] 2024-03-28 09:44:45.296 - [任务 30][test2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 09:44:45.333 - [任务 30][CLAIM1] - Source node "CLAIM1" read batch size: 100 
[INFO ] 2024-03-28 09:44:45.334 - [任务 30][CLAIM1] - Source node "CLAIM1" event queue capacity: 200 
[INFO ] 2024-03-28 09:44:45.334 - [任务 30][CLAIM1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 09:44:45.389 - [任务 30][CLAIM1] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":145946076,"gtidSet":""} 
[INFO ] 2024-03-28 09:44:45.389 - [任务 30][CLAIM1] - Initial sync started 
[INFO ] 2024-03-28 09:44:45.398 - [任务 30][CLAIM1] - Starting batch read, table name: CLAIM1, offset: null 
[INFO ] 2024-03-28 09:44:45.398 - [任务 30][CLAIM1] - Table CLAIM1 is going to be initial synced 
[INFO ] 2024-03-28 09:44:45.606 - [任务 30][CLAIM1] - Query table 'CLAIM1' counts: 1076 
[INFO ] 2024-03-28 09:44:45.675 - [任务 30][CLAIM1] - Initial sync completed 
[INFO ] 2024-03-28 09:44:45.684 - [任务 30][CLAIM1] - Incremental sync starting... 
[INFO ] 2024-03-28 09:44:45.685 - [任务 30][CLAIM1] - Initial sync completed 
[INFO ] 2024-03-28 09:44:45.690 - [任务 30][CLAIM1] - Starting stream read, table list: [CLAIM1], offset: {"filename":"binlog.000020","position":145946076,"gtidSet":""} 
[INFO ] 2024-03-28 09:44:45.815 - [任务 30][CLAIM1] - Starting mysql cdc, server name: 30b0894c-2ad9-4702-9762-e4790e8779d7 
[INFO ] 2024-03-28 09:44:45.816 - [任务 30][CLAIM1] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1230889168
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 30b0894c-2ad9-4702-9762-e4790e8779d7
  database.port: 3306
  threadName: Debezium-Mysql-Connector-30b0894c-2ad9-4702-9762-e4790e8779d7
  database.hostname: 127.0.0.1
  database.password: ********
  name: 30b0894c-2ad9-4702-9762-e4790e8779d7
  pdk.offset.string: {"name":"30b0894c-2ad9-4702-9762-e4790e8779d7","offset":{"{\"server\":\"30b0894c-2ad9-4702-9762-e4790e8779d7\"}":"{\"file\":\"binlog.000020\",\"pos\":145946076,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-28 09:44:45.993 - [任务 30][CLAIM1] - Connector Mysql incremental start succeed, tables: [CLAIM1], data change syncing 
[INFO ] 2024-03-28 09:52:44.882 - [任务 30] - Stop task milestones: 6604ca71c9eca62bdcca67ac(任务 30)  
[INFO ] 2024-03-28 09:52:45.463 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] running status set to false 
[INFO ] 2024-03-28 09:52:45.463 - [任务 30][CLAIM1] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-28 09:52:45.478 - [任务 30][CLAIM1] - Mysql binlog reader stopped 
[INFO ] 2024-03-28 09:52:45.478 - [任务 30][CLAIM1] - PDK connector node stopped: HazelcastSourcePdkDataNode-6bab6d49-5c62-4823-b829-70605a64185b 
[INFO ] 2024-03-28 09:52:45.478 - [任务 30][CLAIM1] - PDK connector node released: HazelcastSourcePdkDataNode-6bab6d49-5c62-4823-b829-70605a64185b 
[INFO ] 2024-03-28 09:52:45.478 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] schema data cleaned 
[INFO ] 2024-03-28 09:52:45.479 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] monitor closed 
[INFO ] 2024-03-28 09:52:45.479 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] close complete, cost 170 ms 
[INFO ] 2024-03-28 09:52:45.502 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] running status set to false 
[INFO ] 2024-03-28 09:52:45.502 - [任务 30][test2] - PDK connector node stopped: HazelcastTargetPdkDataNode-53ee739b-e7eb-4671-abae-e1347d901416 
[INFO ] 2024-03-28 09:52:45.502 - [任务 30][test2] - PDK connector node released: HazelcastTargetPdkDataNode-53ee739b-e7eb-4671-abae-e1347d901416 
[INFO ] 2024-03-28 09:52:45.502 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] schema data cleaned 
[INFO ] 2024-03-28 09:52:45.503 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] monitor closed 
[INFO ] 2024-03-28 09:52:45.505 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] close complete, cost 22 ms 
[INFO ] 2024-03-28 09:52:50.227 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 09:52:50.227 - [任务 30] - Stopped task aspect(s) 
[INFO ] 2024-03-28 09:52:50.228 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 09:52:50.268 - [任务 30] - Remove memory task client succeed, task: 任务 30[6604ca71c9eca62bdcca67ac] 
[INFO ] 2024-03-28 09:52:50.268 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[6604ca71c9eca62bdcca67ac] 
[INFO ] 2024-03-28 09:53:37.835 - [任务 30] - Start task milestones: 6604ca71c9eca62bdcca67ac(任务 30) 
[INFO ] 2024-03-28 09:53:37.844 - [任务 30] - Task initialization... 
[INFO ] 2024-03-28 09:53:38.049 - [任务 30] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 09:53:38.158 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 09:53:38.161 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:53:38.161 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:53:38.227 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] preload schema finished, cost 62 ms 
[INFO ] 2024-03-28 09:53:38.227 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] preload schema finished, cost 62 ms 
[INFO ] 2024-03-28 09:53:39.062 - [任务 30][test2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 09:53:39.099 - [任务 30][CLAIM1] - Source node "CLAIM1" read batch size: 100 
[INFO ] 2024-03-28 09:53:39.100 - [任务 30][CLAIM1] - Source node "CLAIM1" event queue capacity: 200 
[INFO ] 2024-03-28 09:53:39.100 - [任务 30][CLAIM1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 09:53:39.102 - [任务 30][CLAIM1] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":145946076,"gtidSet":""} 
[INFO ] 2024-03-28 09:53:39.177 - [任务 30][CLAIM1] - Initial sync started 
[INFO ] 2024-03-28 09:53:39.178 - [任务 30][CLAIM1] - Starting batch read, table name: CLAIM1, offset: null 
[INFO ] 2024-03-28 09:53:39.230 - [任务 30][CLAIM1] - Table CLAIM1 is going to be initial synced 
[INFO ] 2024-03-28 09:53:39.231 - [任务 30][CLAIM1] - Query table 'CLAIM1' counts: 1076 
[INFO ] 2024-03-28 09:53:39.286 - [任务 30][CLAIM1] - Initial sync completed 
[INFO ] 2024-03-28 09:53:39.288 - [任务 30][CLAIM1] - Incremental sync starting... 
[INFO ] 2024-03-28 09:53:39.288 - [任务 30][CLAIM1] - Initial sync completed 
[INFO ] 2024-03-28 09:53:39.295 - [任务 30][CLAIM1] - Starting stream read, table list: [CLAIM1], offset: {"filename":"binlog.000020","position":145946076,"gtidSet":""} 
[INFO ] 2024-03-28 09:53:39.340 - [任务 30][CLAIM1] - Starting mysql cdc, server name: ff0bcdc8-bb1e-4aab-a3a8-5a195229df66 
[INFO ] 2024-03-28 09:53:39.341 - [任务 30][CLAIM1] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1356028346
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ff0bcdc8-bb1e-4aab-a3a8-5a195229df66
  database.port: 3306
  threadName: Debezium-Mysql-Connector-ff0bcdc8-bb1e-4aab-a3a8-5a195229df66
  database.hostname: 127.0.0.1
  database.password: ********
  name: ff0bcdc8-bb1e-4aab-a3a8-5a195229df66
  pdk.offset.string: {"name":"ff0bcdc8-bb1e-4aab-a3a8-5a195229df66","offset":{"{\"server\":\"ff0bcdc8-bb1e-4aab-a3a8-5a195229df66\"}":"{\"file\":\"binlog.000020\",\"pos\":145946076,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-28 09:53:39.545 - [任务 30][CLAIM1] - Connector Mysql incremental start succeed, tables: [CLAIM1], data change syncing 
[INFO ] 2024-03-28 09:54:00.459 - [任务 30][CLAIM1] - Read DDL: alter table CLAIM1 rename column `name` to `name1`, about to be packaged as some event(s) 
[INFO ] 2024-03-28 09:54:00.460 - [任务 30][CLAIM1] - DDL event  - Table: CLAIM1
  - Event type: TapAlterFieldNameEvent
  - Offset: MysqlStreamOffset{name='ff0bcdc8-bb1e-4aab-a3a8-5a195229df66', offset={{"server":"ff0bcdc8-bb1e-4aab-a3a8-5a195229df66"}={"ts_sec":1711590839,"file":"binlog.000020","pos":145946338,"server_id":1}}} 
[INFO ] 2024-03-28 09:54:00.461 - [任务 30][CLAIM1] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='CLAIM1', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@7317c583} 
[WARN ] 2024-03-28 09:54:00.498 - [任务 30][CLAIM1] - DDL events are filtered
 - Event: TapAlterFieldNameEvent{tableId='CLAIM1', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@7317c583}
 - Filter: {} 
[INFO ] 2024-03-28 09:54:25.381 - [任务 30] - Stop task milestones: 6604ca71c9eca62bdcca67ac(任务 30)  
[INFO ] 2024-03-28 09:54:25.518 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] running status set to false 
[INFO ] 2024-03-28 09:54:25.519 - [任务 30][CLAIM1] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-28 09:54:25.519 - [任务 30][CLAIM1] - Mysql binlog reader stopped 
[INFO ] 2024-03-28 09:54:25.534 - [任务 30][CLAIM1] - PDK connector node stopped: HazelcastSourcePdkDataNode-6bab6d49-5c62-4823-b829-70605a64185b 
[INFO ] 2024-03-28 09:54:25.534 - [任务 30][CLAIM1] - PDK connector node released: HazelcastSourcePdkDataNode-6bab6d49-5c62-4823-b829-70605a64185b 
[INFO ] 2024-03-28 09:54:25.534 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] schema data cleaned 
[INFO ] 2024-03-28 09:54:25.534 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] monitor closed 
[INFO ] 2024-03-28 09:54:25.535 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] close complete, cost 118 ms 
[INFO ] 2024-03-28 09:54:25.535 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] running status set to false 
[INFO ] 2024-03-28 09:54:25.559 - [任务 30][test2] - PDK connector node stopped: HazelcastTargetPdkDataNode-53ee739b-e7eb-4671-abae-e1347d901416 
[INFO ] 2024-03-28 09:54:25.559 - [任务 30][test2] - PDK connector node released: HazelcastTargetPdkDataNode-53ee739b-e7eb-4671-abae-e1347d901416 
[INFO ] 2024-03-28 09:54:25.559 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] schema data cleaned 
[INFO ] 2024-03-28 09:54:25.561 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] monitor closed 
[INFO ] 2024-03-28 09:54:25.561 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] close complete, cost 25 ms 
[INFO ] 2024-03-28 09:54:30.379 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 09:54:30.380 - [任务 30] - Stopped task aspect(s) 
[INFO ] 2024-03-28 09:54:30.380 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 09:54:30.416 - [任务 30] - Remove memory task client succeed, task: 任务 30[6604ca71c9eca62bdcca67ac] 
[INFO ] 2024-03-28 09:54:30.417 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[6604ca71c9eca62bdcca67ac] 
[INFO ] 2024-03-28 09:54:59.319 - [任务 30] - Start task milestones: 6604ca71c9eca62bdcca67ac(任务 30) 
[INFO ] 2024-03-28 09:54:59.321 - [任务 30] - Task initialization... 
[INFO ] 2024-03-28 09:54:59.390 - [任务 30] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 09:54:59.390 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 09:54:59.422 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:54:59.422 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:54:59.439 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] preload schema finished, cost 17 ms 
[INFO ] 2024-03-28 09:54:59.439 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] preload schema finished, cost 17 ms 
[INFO ] 2024-03-28 09:55:00.182 - [任务 30][CLAIM1] - Source node "CLAIM1" read batch size: 100 
[INFO ] 2024-03-28 09:55:00.182 - [任务 30][CLAIM1] - Source node "CLAIM1" event queue capacity: 200 
[INFO ] 2024-03-28 09:55:00.187 - [任务 30][CLAIM1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 09:55:00.190 - [任务 30][CLAIM1] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":145946338,"gtidSet":""} 
[INFO ] 2024-03-28 09:55:00.242 - [任务 30][CLAIM1] - Initial sync started 
[INFO ] 2024-03-28 09:55:00.242 - [任务 30][CLAIM1] - Starting batch read, table name: CLAIM1, offset: null 
[INFO ] 2024-03-28 09:55:00.283 - [任务 30][CLAIM1] - Table CLAIM1 is going to be initial synced 
[INFO ] 2024-03-28 09:55:00.283 - [任务 30][CLAIM1] - Query table 'CLAIM1' counts: 1076 
[INFO ] 2024-03-28 09:55:00.292 - [任务 30][CLAIM1] - Initial sync completed 
[ERROR] 2024-03-28 09:55:00.296 - [任务 30][CLAIM1] - java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list' <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:253)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:147)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:175)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:308)
	... 10 more
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshot$13(HazelcastSourcePdkDataNode.java:316)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 12 more
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1140)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:94)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadV2(MysqlConnector.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:388)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 18 more

[INFO ] 2024-03-28 09:55:00.374 - [任务 30][CLAIM1] - Job suspend in error handle 
[INFO ] 2024-03-28 09:55:00.374 - [任务 30][test2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 09:55:00.584 - [任务 30] - Task [任务 30] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 09:55:00.822 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] running status set to false 
[INFO ] 2024-03-28 09:55:00.822 - [任务 30][CLAIM1] - PDK connector node stopped: HazelcastSourcePdkDataNode-6bab6d49-5c62-4823-b829-70605a64185b 
[INFO ] 2024-03-28 09:55:00.823 - [任务 30][CLAIM1] - PDK connector node released: HazelcastSourcePdkDataNode-6bab6d49-5c62-4823-b829-70605a64185b 
[INFO ] 2024-03-28 09:55:00.823 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] schema data cleaned 
[INFO ] 2024-03-28 09:55:00.828 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] monitor closed 
[INFO ] 2024-03-28 09:55:00.828 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] close complete, cost 38 ms 
[INFO ] 2024-03-28 09:55:00.828 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] running status set to false 
[INFO ] 2024-03-28 09:55:00.836 - [任务 30][test2] - PDK connector node stopped: HazelcastTargetPdkDataNode-53ee739b-e7eb-4671-abae-e1347d901416 
[INFO ] 2024-03-28 09:55:00.836 - [任务 30][test2] - PDK connector node released: HazelcastTargetPdkDataNode-53ee739b-e7eb-4671-abae-e1347d901416 
[INFO ] 2024-03-28 09:55:00.836 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] schema data cleaned 
[INFO ] 2024-03-28 09:55:00.836 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] monitor closed 
[INFO ] 2024-03-28 09:55:00.836 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] close complete, cost 8 ms 
[INFO ] 2024-03-28 09:55:02.606 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 09:55:02.625 - [任务 30] - Stop task milestones: 6604ca71c9eca62bdcca67ac(任务 30)  
[INFO ] 2024-03-28 09:55:02.625 - [任务 30] - Stopped task aspect(s) 
[INFO ] 2024-03-28 09:55:02.625 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 09:55:02.645 - [任务 30] - Remove memory task client succeed, task: 任务 30[6604ca71c9eca62bdcca67ac] 
[INFO ] 2024-03-28 09:55:02.647 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[6604ca71c9eca62bdcca67ac] 
[INFO ] 2024-03-28 09:55:39.247 - [任务 30] - Start task milestones: 6604ca71c9eca62bdcca67ac(任务 30) 
[INFO ] 2024-03-28 09:55:39.247 - [任务 30] - Task initialization... 
[INFO ] 2024-03-28 09:55:39.318 - [任务 30] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 09:55:39.318 - [任务 30] - The engine receives 任务 30 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 09:55:39.368 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:55:39.368 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 09:55:39.385 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] preload schema finished, cost 17 ms 
[INFO ] 2024-03-28 09:55:39.385 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] preload schema finished, cost 17 ms 
[INFO ] 2024-03-28 09:55:40.141 - [任务 30][CLAIM1] - Source node "CLAIM1" read batch size: 100 
[INFO ] 2024-03-28 09:55:40.141 - [任务 30][CLAIM1] - Source node "CLAIM1" event queue capacity: 200 
[INFO ] 2024-03-28 09:55:40.141 - [任务 30][CLAIM1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 09:55:40.143 - [任务 30][CLAIM1] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":145946600,"gtidSet":""} 
[INFO ] 2024-03-28 09:55:40.193 - [任务 30][CLAIM1] - Initial sync started 
[INFO ] 2024-03-28 09:55:40.193 - [任务 30][CLAIM1] - Starting batch read, table name: CLAIM1, offset: null 
[INFO ] 2024-03-28 09:55:40.200 - [任务 30][CLAIM1] - Table CLAIM1 is going to be initial synced 
[INFO ] 2024-03-28 09:55:40.310 - [任务 30][CLAIM1] - Query table 'CLAIM1' counts: 1076 
[INFO ] 2024-03-28 09:55:40.315 - [任务 30][test2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 09:55:40.511 - [任务 30][test2] - The table test2 has already exist. 
[INFO ] 2024-03-28 09:55:40.516 - [任务 30][CLAIM1] - Initial sync completed 
[INFO ] 2024-03-28 09:55:40.517 - [任务 30][CLAIM1] - Incremental sync starting... 
[INFO ] 2024-03-28 09:55:40.517 - [任务 30][CLAIM1] - Initial sync completed 
[INFO ] 2024-03-28 09:55:40.518 - [任务 30][CLAIM1] - Starting stream read, table list: [CLAIM1], offset: {"filename":"binlog.000020","position":145946600,"gtidSet":""} 
[INFO ] 2024-03-28 09:55:40.566 - [任务 30][CLAIM1] - Starting mysql cdc, server name: 51820f70-0c21-4cbb-a59b-5ad5a25f3cea 
[INFO ] 2024-03-28 09:55:40.629 - [任务 30][CLAIM1] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 298399741
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 51820f70-0c21-4cbb-a59b-5ad5a25f3cea
  database.port: 3306
  threadName: Debezium-Mysql-Connector-51820f70-0c21-4cbb-a59b-5ad5a25f3cea
  database.hostname: 127.0.0.1
  database.password: ********
  name: 51820f70-0c21-4cbb-a59b-5ad5a25f3cea
  pdk.offset.string: {"name":"51820f70-0c21-4cbb-a59b-5ad5a25f3cea","offset":{"{\"server\":\"51820f70-0c21-4cbb-a59b-5ad5a25f3cea\"}":"{\"file\":\"binlog.000020\",\"pos\":145946600,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM1
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-28 09:55:40.629 - [任务 30][CLAIM1] - Connector Mysql incremental start succeed, tables: [CLAIM1], data change syncing 
[INFO ] 2024-03-28 09:56:05.661 - [任务 30][CLAIM1] - Read DDL: alter table CLAIM1 rename column `name` to `name1`, about to be packaged as some event(s) 
[INFO ] 2024-03-28 09:56:05.664 - [任务 30][CLAIM1] - DDL event  - Table: CLAIM1
  - Event type: TapAlterFieldNameEvent
  - Offset: MysqlStreamOffset{name='51820f70-0c21-4cbb-a59b-5ad5a25f3cea', offset={{"server":"51820f70-0c21-4cbb-a59b-5ad5a25f3cea"}={"ts_sec":1711590965,"file":"binlog.000020","pos":145946862,"server_id":1}}} 
[INFO ] 2024-03-28 09:56:09.831 - [任务 30][CLAIM1] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='CLAIM1', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@1112d296} 
[INFO ] 2024-03-28 09:56:10.413 - [任务 30][CLAIM1] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM1_65fd534767def503a78ea02d_6604ca71c9eca62bdcca67ac 
[INFO ] 2024-03-28 09:56:10.559 - [任务 30][CLAIM1] - Alter table schema transform finished 
[INFO ] 2024-03-28 10:02:13.371 - [任务 30][CLAIM1] - Read DDL: alter table CLAIM1 rename column `name1` to `name`, about to be packaged as some event(s) 
[INFO ] 2024-03-28 10:02:13.400 - [任务 30][CLAIM1] - DDL event  - Table: CLAIM1
  - Event type: TapAlterFieldNameEvent
  - Offset: MysqlStreamOffset{name='51820f70-0c21-4cbb-a59b-5ad5a25f3cea', offset={{"server":"51820f70-0c21-4cbb-a59b-5ad5a25f3cea"}={"ts_sec":1711591327,"file":"binlog.000020","pos":145947124,"server_id":1}}} 
[INFO ] 2024-03-28 10:02:13.403 - [任务 30][CLAIM1] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='CLAIM1', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@19d72132} 
[INFO ] 2024-03-28 10:02:14.505 - [任务 30][CLAIM1] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM1_65fd534767def503a78ea02d_6604ca71c9eca62bdcca67ac 
[INFO ] 2024-03-28 10:02:14.710 - [任务 30][CLAIM1] - Alter table schema transform finished 
[INFO ] 2024-03-28 10:07:25.652 - [任务 30] - Stop task milestones: 6604ca71c9eca62bdcca67ac(任务 30)  
[INFO ] 2024-03-28 10:07:25.849 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] running status set to false 
[INFO ] 2024-03-28 10:07:25.850 - [任务 30][CLAIM1] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-28 10:07:25.864 - [任务 30][CLAIM1] - Mysql binlog reader stopped 
[INFO ] 2024-03-28 10:07:25.864 - [任务 30][CLAIM1] - PDK connector node stopped: HazelcastSourcePdkDataNode-6bab6d49-5c62-4823-b829-70605a64185b 
[INFO ] 2024-03-28 10:07:25.866 - [任务 30][CLAIM1] - PDK connector node released: HazelcastSourcePdkDataNode-6bab6d49-5c62-4823-b829-70605a64185b 
[INFO ] 2024-03-28 10:07:25.866 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] schema data cleaned 
[INFO ] 2024-03-28 10:07:25.867 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] monitor closed 
[INFO ] 2024-03-28 10:07:25.868 - [任务 30][CLAIM1] - Node CLAIM1[6bab6d49-5c62-4823-b829-70605a64185b] close complete, cost 129 ms 
[INFO ] 2024-03-28 10:07:25.868 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] running status set to false 
[INFO ] 2024-03-28 10:07:25.903 - [任务 30][test2] - PDK connector node stopped: HazelcastTargetPdkDataNode-53ee739b-e7eb-4671-abae-e1347d901416 
[INFO ] 2024-03-28 10:07:25.903 - [任务 30][test2] - PDK connector node released: HazelcastTargetPdkDataNode-53ee739b-e7eb-4671-abae-e1347d901416 
[INFO ] 2024-03-28 10:07:25.903 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] schema data cleaned 
[INFO ] 2024-03-28 10:07:25.904 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] monitor closed 
[INFO ] 2024-03-28 10:07:26.105 - [任务 30][test2] - Node test2[53ee739b-e7eb-4671-abae-e1347d901416] close complete, cost 37 ms 
[INFO ] 2024-03-28 10:07:27.136 - [任务 30] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 10:07:27.137 - [任务 30] - Stopped task aspect(s) 
[INFO ] 2024-03-28 10:07:27.137 - [任务 30] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 10:07:27.165 - [任务 30] - Remove memory task client succeed, task: 任务 30[6604ca71c9eca62bdcca67ac] 
[INFO ] 2024-03-28 10:07:27.165 - [任务 30] - Destroy memory task client cache succeed, task: 任务 30[6604ca71c9eca62bdcca67ac] 
