[INFO ] 2024-10-24 03:01:07.088 - [测试Offset - Copy] - Start task milestones: 671947ca488a1123340fd30e(测试Offset - Copy) 
[INFO ] 2024-10-24 03:01:07.296 - [测试Offset - Copy] - Task initialization... 
[INFO ] 2024-10-24 03:01:07.361 - [测试Offset - Copy] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-10-24 03:01:07.460 - [测试Offset - Copy] - The engine receives 测试Offset - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-24 03:01:07.460 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] start preload schema,table counts: 5 
[INFO ] 2024-10-24 03:01:07.460 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] start preload schema,table counts: 5 
[INFO ] 2024-10-24 03:01:07.460 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] preload schema finished, cost 0 ms 
[INFO ] 2024-10-24 03:01:07.460 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-24 03:01:08.288 - [测试Offset - Copy][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-10-24 03:01:08.288 - [测试Offset - Copy][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-10-24 03:01:08.294 - [测试Offset - Copy][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-24 03:01:08.294 - [测试Offset - Copy][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":946885,"gtidSet":""} 
[INFO ] 2024-10-24 03:01:08.386 - [测试Offset - Copy][Mysql3306] - Initial sync started 
[INFO ] 2024-10-24 03:01:08.386 - [测试Offset - Copy][Mysql3306] - Starting batch read, table name: BMSQL_NEW_ORDER 
[INFO ] 2024-10-24 03:01:08.405 - [测试Offset - Copy][Mysql3306] - Table BMSQL_NEW_ORDER is going to be initial synced 
[INFO ] 2024-10-24 03:01:08.450 - [测试Offset - Copy][Local27018] - Node(Local27018) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-24 03:01:08.450 - [测试Offset - Copy][Local27018] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-24 03:01:08.655 - [测试Offset - Copy][Mysql3306] - Query table 'BMSQL_NEW_ORDER' counts: 2899 
[INFO ] 2024-10-24 03:01:09.381 - [测试Offset - Copy][Mysql3306] - Table [BMSQL_NEW_ORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-24 03:01:09.383 - [测试Offset - Copy][Mysql3306] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-10-24 03:01:09.383 - [测试Offset - Copy][Mysql3306] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-10-24 03:01:09.398 - [测试Offset - Copy][Mysql3306] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-24 03:01:09.398 - [测试Offset - Copy][Mysql3306] - Query table 'BMSQL_ITEM' counts: 7 
[INFO ] 2024-10-24 03:01:09.399 - [测试Offset - Copy][Mysql3306] - Starting batch read, table name: BMSQL_DISTRICT 
[INFO ] 2024-10-24 03:01:09.399 - [测试Offset - Copy][Mysql3306] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-10-24 03:01:09.401 - [测试Offset - Copy][Mysql3306] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-10-24 03:01:09.401 - [测试Offset - Copy][Mysql3306] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-24 03:01:09.401 - [测试Offset - Copy][Mysql3306] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-10-24 03:01:09.401 - [测试Offset - Copy][Mysql3306] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-10-24 03:01:09.602 - [测试Offset - Copy][Mysql3306] - Query table 'BMSQL_CUSTOMER' counts: 9999 
[INFO ] 2024-10-24 03:01:17.773 - [测试Offset - Copy][Mysql3306] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-24 03:01:17.787 - [测试Offset - Copy][Mysql3306] - Starting batch read, table name: BMSQL_OORDER 
[INFO ] 2024-10-24 03:01:17.788 - [测试Offset - Copy][Mysql3306] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-10-24 03:01:17.804 - [测试Offset - Copy][Mysql3306] - Query table 'BMSQL_OORDER' counts: 0 
[INFO ] 2024-10-24 03:01:17.804 - [测试Offset - Copy][Mysql3306] - Table [BMSQL_OORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-24 03:01:17.805 - [测试Offset - Copy][Mysql3306] - Initial sync completed 
[INFO ] 2024-10-24 03:01:17.805 - [测试Offset - Copy][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-10-24 03:01:17.805 - [测试Offset - Copy][Mysql3306] - Initial sync completed 
[INFO ] 2024-10-24 03:01:17.806 - [测试Offset - Copy][Mysql3306] - Starting stream read, table list: [BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_OORDER], offset: {"filename":"binlog.000036","position":946885,"gtidSet":""} 
[INFO ] 2024-10-24 03:01:17.848 - [测试Offset - Copy][Mysql3306] - Starting mysql cdc, server name: fe4e0c53-51e0-4522-a166-3a6fb832266b 
[INFO ] 2024-10-24 03:01:17.849 - [测试Offset - Copy][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"fe4e0c53-51e0-4522-a166-3a6fb832266b","offset":{"{\"server\":\"fe4e0c53-51e0-4522-a166-3a6fb832266b\"}":"{\"file\":\"binlog.000036\",\"pos\":946885,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 156751155
  time.precision.mode: adaptive_time_microseconds
  database.server.name: fe4e0c53-51e0-4522-a166-3a6fb832266b
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-fe4e0c53-51e0-4522-a166-3a6fb832266b
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: fe4e0c53-51e0-4522-a166-3a6fb832266b
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_NEW_ORDER,test.BMSQL_ITEM,test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_OORDER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-24 03:01:18.058 - [测试Offset - Copy][Mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_OORDER], data change syncing 
[INFO ] 2024-10-24 03:01:35.627 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] running status set to false 
[INFO ] 2024-10-24 03:01:35.627 - [测试Offset - Copy][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-24 03:01:35.629 - [测试Offset - Copy][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-10-24 03:01:35.629 - [测试Offset - Copy][Mysql3306] - Incremental sync completed 
[INFO ] 2024-10-24 03:01:35.638 - [测试Offset - Copy][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-6d7f90bc-e9b6-440a-952c-17c4794db59e 
[INFO ] 2024-10-24 03:01:35.638 - [测试Offset - Copy][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-6d7f90bc-e9b6-440a-952c-17c4794db59e 
[INFO ] 2024-10-24 03:01:35.639 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] schema data cleaned 
[INFO ] 2024-10-24 03:01:35.639 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] monitor closed 
[INFO ] 2024-10-24 03:01:35.640 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] close complete, cost 92 ms 
[INFO ] 2024-10-24 03:01:35.640 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] running status set to false 
[INFO ] 2024-10-24 03:01:35.659 - [测试Offset - Copy][Local27018] - PDK connector node stopped: HazelcastTargetPdkDataNode-1d75c893-ba73-444e-a057-f4f7d00952e0 
[INFO ] 2024-10-24 03:01:35.659 - [测试Offset - Copy][Local27018] - PDK connector node released: HazelcastTargetPdkDataNode-1d75c893-ba73-444e-a057-f4f7d00952e0 
[INFO ] 2024-10-24 03:01:35.660 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] schema data cleaned 
[INFO ] 2024-10-24 03:01:35.660 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] monitor closed 
[INFO ] 2024-10-24 03:01:35.861 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] close complete, cost 19 ms 
[INFO ] 2024-10-24 03:01:37.810 - [测试Offset - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-24 03:01:37.810 - [测试Offset - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@447cf5bb 
[INFO ] 2024-10-24 03:01:37.810 - [测试Offset - Copy] - Stop task milestones: 671947ca488a1123340fd30e(测试Offset - Copy)  
[INFO ] 2024-10-24 03:01:37.941 - [测试Offset - Copy] - Stopped task aspect(s) 
[INFO ] 2024-10-24 03:01:37.941 - [测试Offset - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-10-24 03:01:37.961 - [测试Offset - Copy] - Remove memory task client succeed, task: 测试Offset - Copy[671947ca488a1123340fd30e] 
[INFO ] 2024-10-24 03:01:37.965 - [测试Offset - Copy] - Destroy memory task client cache succeed, task: 测试Offset - Copy[671947ca488a1123340fd30e] 
[INFO ] 2024-10-24 03:01:38.931 - [测试Offset - Copy] - Start task milestones: 671947ca488a1123340fd30e(测试Offset - Copy) 
[INFO ] 2024-10-24 03:01:38.932 - [测试Offset - Copy] - Task initialization... 
[INFO ] 2024-10-24 03:01:39.110 - [测试Offset - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-24 03:01:39.110 - [测试Offset - Copy] - The engine receives 测试Offset - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-24 03:01:39.182 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] start preload schema,table counts: 5 
[INFO ] 2024-10-24 03:01:39.182 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] start preload schema,table counts: 5 
[INFO ] 2024-10-24 03:01:39.182 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-24 03:01:39.182 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] preload schema finished, cost 0 ms 
[INFO ] 2024-10-24 03:01:39.458 - [测试Offset - Copy][Local27018] - Node(Local27018) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-24 03:01:39.458 - [测试Offset - Copy][Local27018] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-24 03:02:02.912 - [测试Offset - Copy][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-10-24 03:02:02.917 - [测试Offset - Copy][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-10-24 03:02:02.917 - [测试Offset - Copy][Mysql3306] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-24 03:02:02.917 - [测试Offset - Copy][Mysql3306] - batch offset found: {"BMSQL_NEW_ORDER":{"batch_read_connector_status":"OVER"},"BMSQL_ITEM":{"batch_read_connector_status":"OVER"},"BMSQL_DISTRICT":{"batch_read_connector_status":"OVER"},"BMSQL_CUSTOMER":{"batch_read_connector_status":"OVER"},"BMSQL_OORDER":{"batch_read_connector_status":"OVER"}},stream offset found: {"filename":"binlog.000036","position":946885,"gtidSet":""} 
[INFO ] 2024-10-24 03:02:02.968 - [测试Offset - Copy][Mysql3306] - Initial sync started 
[INFO ] 2024-10-24 03:02:02.968 - [测试Offset - Copy][Mysql3306] - Skip table [BMSQL_NEW_ORDER] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-24 03:02:02.968 - [测试Offset - Copy][Mysql3306] - Skip table [BMSQL_ITEM] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-24 03:02:02.968 - [测试Offset - Copy][Mysql3306] - Skip table [BMSQL_DISTRICT] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-24 03:02:02.968 - [测试Offset - Copy][Mysql3306] - Skip table [BMSQL_CUSTOMER] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-24 03:02:02.968 - [测试Offset - Copy][Mysql3306] - Skip table [BMSQL_OORDER] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-24 03:02:02.968 - [测试Offset - Copy][Mysql3306] - Initial sync completed 
[INFO ] 2024-10-24 03:02:02.968 - [测试Offset - Copy][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-10-24 03:02:02.974 - [测试Offset - Copy][Mysql3306] - Initial sync completed 
[INFO ] 2024-10-24 03:02:02.974 - [测试Offset - Copy][Mysql3306] - Starting stream read, table list: [BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_OORDER], offset: {"filename":"binlog.000036","position":946885,"gtidSet":""} 
[INFO ] 2024-10-24 03:02:03.017 - [测试Offset - Copy][Mysql3306] - Starting mysql cdc, server name: fe4e0c53-51e0-4522-a166-3a6fb832266b 
[INFO ] 2024-10-24 03:02:03.021 - [测试Offset - Copy][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"fe4e0c53-51e0-4522-a166-3a6fb832266b","offset":{"{\"server\":\"fe4e0c53-51e0-4522-a166-3a6fb832266b\"}":"{\"file\":\"binlog.000036\",\"pos\":946885,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 275555091
  time.precision.mode: adaptive_time_microseconds
  database.server.name: fe4e0c53-51e0-4522-a166-3a6fb832266b
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-fe4e0c53-51e0-4522-a166-3a6fb832266b
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: fe4e0c53-51e0-4522-a166-3a6fb832266b
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_NEW_ORDER,test.BMSQL_ITEM,test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_OORDER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-24 03:02:03.226 - [测试Offset - Copy][Mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_OORDER], data change syncing 
[INFO ] 2024-10-24 03:14:14.390 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] running status set to false 
[INFO ] 2024-10-24 03:14:14.396 - [测试Offset - Copy][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-24 03:14:14.396 - [测试Offset - Copy][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-10-24 03:14:14.396 - [测试Offset - Copy][Mysql3306] - Incremental sync completed 
[INFO ] 2024-10-24 03:14:14.408 - [测试Offset - Copy][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-6d7f90bc-e9b6-440a-952c-17c4794db59e 
[INFO ] 2024-10-24 03:14:14.408 - [测试Offset - Copy][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-6d7f90bc-e9b6-440a-952c-17c4794db59e 
[INFO ] 2024-10-24 03:14:14.408 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] schema data cleaned 
[INFO ] 2024-10-24 03:14:14.411 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] monitor closed 
[INFO ] 2024-10-24 03:14:14.415 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] close complete, cost 133 ms 
[INFO ] 2024-10-24 03:14:14.415 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] running status set to false 
[INFO ] 2024-10-24 03:14:14.426 - [测试Offset - Copy][Local27018] - PDK connector node stopped: HazelcastTargetPdkDataNode-1d75c893-ba73-444e-a057-f4f7d00952e0 
[INFO ] 2024-10-24 03:14:14.426 - [测试Offset - Copy][Local27018] - PDK connector node released: HazelcastTargetPdkDataNode-1d75c893-ba73-444e-a057-f4f7d00952e0 
[INFO ] 2024-10-24 03:14:14.427 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] schema data cleaned 
[INFO ] 2024-10-24 03:14:14.427 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] monitor closed 
[INFO ] 2024-10-24 03:14:14.427 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] close complete, cost 13 ms 
[INFO ] 2024-10-24 03:14:18.270 - [测试Offset - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-24 03:14:18.270 - [测试Offset - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4d9375be 
[INFO ] 2024-10-24 03:14:18.396 - [测试Offset - Copy] - Stop task milestones: 671947ca488a1123340fd30e(测试Offset - Copy)  
[INFO ] 2024-10-24 03:14:18.398 - [测试Offset - Copy] - Stopped task aspect(s) 
[INFO ] 2024-10-24 03:14:18.398 - [测试Offset - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-10-24 03:14:18.420 - [测试Offset - Copy] - Remove memory task client succeed, task: 测试Offset - Copy[671947ca488a1123340fd30e] 
[INFO ] 2024-10-24 03:14:18.425 - [测试Offset - Copy] - Destroy memory task client cache succeed, task: 测试Offset - Copy[671947ca488a1123340fd30e] 
[INFO ] 2024-10-24 10:26:47.004 - [测试Offset - Copy] - Task initialization... 
[INFO ] 2024-10-24 10:26:47.006 - [测试Offset - Copy] - Start task milestones: 671947ca488a1123340fd30e(测试Offset - Copy) 
[INFO ] 2024-10-24 10:26:48.083 - [测试Offset - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-24 10:26:48.304 - [测试Offset - Copy] - The engine receives 测试Offset - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-24 10:26:48.556 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] start preload schema,table counts: 5 
[INFO ] 2024-10-24 10:26:48.557 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] start preload schema,table counts: 5 
[INFO ] 2024-10-24 10:26:48.557 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] preload schema finished, cost 2 ms 
[INFO ] 2024-10-24 10:26:48.558 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] preload schema finished, cost 2 ms 
[INFO ] 2024-10-24 10:26:49.498 - [测试Offset - Copy][Local27018] - Node(Local27018) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-24 10:26:49.503 - [测试Offset - Copy][Local27018] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-24 10:26:56.208 - [测试Offset - Copy][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-10-24 10:26:59.666 - [测试Offset - Copy][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-10-24 10:26:59.667 - [测试Offset - Copy][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-24 10:26:59.779 - [测试Offset - Copy][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":946885,"gtidSet":""} 
[INFO ] 2024-10-24 10:27:00.030 - [测试Offset - Copy][Mysql3306] - Initial sync started 
[INFO ] 2024-10-24 10:27:00.044 - [测试Offset - Copy][Mysql3306] - Starting batch read, table name: BMSQL_NEW_ORDER 
[INFO ] 2024-10-24 10:27:00.104 - [测试Offset - Copy][Mysql3306] - Table BMSQL_NEW_ORDER is going to be initial synced 
[INFO ] 2024-10-24 10:27:00.105 - [测试Offset - Copy][Mysql3306] - Query table 'BMSQL_NEW_ORDER' counts: 2899 
[INFO ] 2024-10-24 10:27:01.023 - [测试Offset - Copy][Mysql3306] - Table [BMSQL_NEW_ORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-24 10:27:01.029 - [测试Offset - Copy][Mysql3306] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-10-24 10:27:01.029 - [测试Offset - Copy][Mysql3306] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-10-24 10:27:01.039 - [测试Offset - Copy][Mysql3306] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-24 10:27:01.039 - [测试Offset - Copy][Mysql3306] - Query table 'BMSQL_ITEM' counts: 7 
[INFO ] 2024-10-24 10:27:01.040 - [测试Offset - Copy][Mysql3306] - Starting batch read, table name: BMSQL_DISTRICT 
[INFO ] 2024-10-24 10:27:01.052 - [测试Offset - Copy][Mysql3306] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-10-24 10:27:01.053 - [测试Offset - Copy][Mysql3306] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-10-24 10:27:01.054 - [测试Offset - Copy][Mysql3306] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-24 10:27:01.055 - [测试Offset - Copy][Mysql3306] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-10-24 10:27:01.056 - [测试Offset - Copy][Mysql3306] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-10-24 10:27:01.261 - [测试Offset - Copy][Mysql3306] - Query table 'BMSQL_CUSTOMER' counts: 9999 
[INFO ] 2024-10-24 10:27:09.112 - [测试Offset - Copy][Mysql3306] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-24 10:27:09.116 - [测试Offset - Copy][Mysql3306] - Starting batch read, table name: BMSQL_OORDER 
[INFO ] 2024-10-24 10:27:09.117 - [测试Offset - Copy][Mysql3306] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2024-10-24 10:27:09.122 - [测试Offset - Copy][Mysql3306] - Table [BMSQL_OORDER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-24 10:27:09.123 - [测试Offset - Copy][Mysql3306] - Query table 'BMSQL_OORDER' counts: 0 
[INFO ] 2024-10-24 10:27:09.128 - [测试Offset - Copy][Mysql3306] - Initial sync completed 
[INFO ] 2024-10-24 10:27:09.128 - [测试Offset - Copy][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-10-24 10:27:09.128 - [测试Offset - Copy][Mysql3306] - Initial sync completed 
[INFO ] 2024-10-24 10:27:09.132 - [测试Offset - Copy][Mysql3306] - Starting stream read, table list: [BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_OORDER], offset: {"filename":"binlog.000036","position":946885,"gtidSet":""} 
[INFO ] 2024-10-24 10:27:09.250 - [测试Offset - Copy][Mysql3306] - Starting mysql cdc, server name: 09294bf6-91c1-4c47-afd2-478c6aae09ef 
[INFO ] 2024-10-24 10:27:09.256 - [测试Offset - Copy][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"09294bf6-91c1-4c47-afd2-478c6aae09ef","offset":{"{\"server\":\"09294bf6-91c1-4c47-afd2-478c6aae09ef\"}":"{\"file\":\"binlog.000036\",\"pos\":946885,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1603624328
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 09294bf6-91c1-4c47-afd2-478c6aae09ef
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-09294bf6-91c1-4c47-afd2-478c6aae09ef
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 09294bf6-91c1-4c47-afd2-478c6aae09ef
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_NEW_ORDER,test.BMSQL_ITEM,test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_OORDER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-24 10:27:09.458 - [测试Offset - Copy][Mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_OORDER], data change syncing 
[INFO ] 2024-10-24 10:39:08.137 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] running status set to false 
[INFO ] 2024-10-24 10:39:08.252 - [测试Offset - Copy][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-24 10:39:08.255 - [测试Offset - Copy][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-10-24 10:39:08.256 - [测试Offset - Copy][Mysql3306] - Incremental sync completed 
[INFO ] 2024-10-24 10:39:08.283 - [测试Offset - Copy][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-6d7f90bc-e9b6-440a-952c-17c4794db59e 
[INFO ] 2024-10-24 10:39:08.284 - [测试Offset - Copy][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-6d7f90bc-e9b6-440a-952c-17c4794db59e 
[INFO ] 2024-10-24 10:39:08.288 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] schema data cleaned 
[INFO ] 2024-10-24 10:39:08.288 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] monitor closed 
[INFO ] 2024-10-24 10:39:08.307 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] close complete, cost 184 ms 
[INFO ] 2024-10-24 10:39:08.307 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] running status set to false 
[INFO ] 2024-10-24 10:39:08.340 - [测试Offset - Copy][Local27018] - PDK connector node stopped: HazelcastTargetPdkDataNode-1d75c893-ba73-444e-a057-f4f7d00952e0 
[INFO ] 2024-10-24 10:39:08.341 - [测试Offset - Copy][Local27018] - PDK connector node released: HazelcastTargetPdkDataNode-1d75c893-ba73-444e-a057-f4f7d00952e0 
[INFO ] 2024-10-24 10:39:08.341 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] schema data cleaned 
[INFO ] 2024-10-24 10:39:08.342 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] monitor closed 
[INFO ] 2024-10-24 10:39:08.343 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] close complete, cost 36 ms 
[INFO ] 2024-10-24 10:39:10.213 - [测试Offset - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-24 10:39:10.338 - [测试Offset - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7599ae8f 
[INFO ] 2024-10-24 10:39:10.338 - [测试Offset - Copy] - Stop task milestones: 671947ca488a1123340fd30e(测试Offset - Copy)  
[INFO ] 2024-10-24 10:39:10.353 - [测试Offset - Copy] - Stopped task aspect(s) 
[INFO ] 2024-10-24 10:39:10.353 - [测试Offset - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-10-24 10:39:10.403 - [测试Offset - Copy] - Remove memory task client succeed, task: 测试Offset - Copy[671947ca488a1123340fd30e] 
[INFO ] 2024-10-24 10:39:10.403 - [测试Offset - Copy] - Destroy memory task client cache succeed, task: 测试Offset - Copy[671947ca488a1123340fd30e] 
[INFO ] 2024-10-24 10:39:11.414 - [测试Offset - Copy] - Task initialization... 
[INFO ] 2024-10-24 10:39:11.417 - [测试Offset - Copy] - Start task milestones: 671947ca488a1123340fd30e(测试Offset - Copy) 
[INFO ] 2024-10-24 10:39:11.721 - [测试Offset - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-24 10:39:11.723 - [测试Offset - Copy] - The engine receives 测试Offset - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-24 10:39:11.806 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] start preload schema,table counts: 5 
[INFO ] 2024-10-24 10:39:11.807 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] start preload schema,table counts: 5 
[INFO ] 2024-10-24 10:39:11.808 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-24 10:39:11.808 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] preload schema finished, cost 0 ms 
[INFO ] 2024-10-24 10:39:12.147 - [测试Offset - Copy][Local27018] - Node(Local27018) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-24 10:40:04.056 - [测试Offset - Copy][Local27018] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-24 10:40:10.120 - [测试Offset - Copy][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-10-24 10:40:12.083 - [测试Offset - Copy][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-10-24 10:40:12.147 - [测试Offset - Copy][Mysql3306] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-24 10:40:12.150 - [测试Offset - Copy][Mysql3306] - batch offset found: {"BMSQL_NEW_ORDER":{"batch_read_connector_status":"OVER"},"BMSQL_ITEM":{"batch_read_connector_status":"OVER"},"BMSQL_DISTRICT":{"batch_read_connector_status":"OVER"},"BMSQL_CUSTOMER":{"batch_read_connector_status":"OVER"},"BMSQL_OORDER":{"batch_read_connector_status":"OVER"}},stream offset found: {"name":"09294bf6-91c1-4c47-afd2-478c6aae09ef","offset":{"{\"server\":\"09294bf6-91c1-4c47-afd2-478c6aae09ef\"}":"{\"ts_sec\":1729736830,\"file\":\"binlog.000036\",\"pos\":946885,\"server_id\":1}"}} 
[INFO ] 2024-10-24 10:40:12.321 - [测试Offset - Copy][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-10-24 10:40:12.323 - [测试Offset - Copy][Mysql3306] - Initial sync completed 
[INFO ] 2024-10-24 10:40:12.374 - [测试Offset - Copy][Mysql3306] - Starting stream read, table list: [BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_OORDER], offset: {"name":"09294bf6-91c1-4c47-afd2-478c6aae09ef","offset":{"{\"server\":\"09294bf6-91c1-4c47-afd2-478c6aae09ef\"}":"{\"ts_sec\":1729736830,\"file\":\"binlog.000036\",\"pos\":946885,\"server_id\":1}"}} 
[INFO ] 2024-10-24 10:40:12.377 - [测试Offset - Copy][Mysql3306] - Starting mysql cdc, server name: 09294bf6-91c1-4c47-afd2-478c6aae09ef 
[INFO ] 2024-10-24 10:40:12.520 - [测试Offset - Copy][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"09294bf6-91c1-4c47-afd2-478c6aae09ef","offset":{"{\"server\":\"09294bf6-91c1-4c47-afd2-478c6aae09ef\"}":"{\"ts_sec\":1729736830,\"file\":\"binlog.000036\",\"pos\":946885,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1045214504
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 09294bf6-91c1-4c47-afd2-478c6aae09ef
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-09294bf6-91c1-4c47-afd2-478c6aae09ef
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 09294bf6-91c1-4c47-afd2-478c6aae09ef
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_NEW_ORDER,test.BMSQL_ITEM,test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_OORDER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-24 10:40:12.521 - [测试Offset - Copy][Mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_OORDER], data change syncing 
[INFO ] 2024-10-24 10:47:28.363 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] running status set to false 
[INFO ] 2024-10-24 10:47:28.398 - [测试Offset - Copy][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-24 10:47:28.398 - [测试Offset - Copy][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-10-24 10:47:28.399 - [测试Offset - Copy][Mysql3306] - Incremental sync completed 
[INFO ] 2024-10-24 10:47:28.409 - [测试Offset - Copy][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-6d7f90bc-e9b6-440a-952c-17c4794db59e 
[INFO ] 2024-10-24 10:47:28.410 - [测试Offset - Copy][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-6d7f90bc-e9b6-440a-952c-17c4794db59e 
[INFO ] 2024-10-24 10:47:28.410 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] schema data cleaned 
[INFO ] 2024-10-24 10:47:28.412 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] monitor closed 
[INFO ] 2024-10-24 10:47:28.416 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] close complete, cost 80 ms 
[INFO ] 2024-10-24 10:47:28.416 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] running status set to false 
[INFO ] 2024-10-24 10:47:28.443 - [测试Offset - Copy][Local27018] - PDK connector node stopped: HazelcastTargetPdkDataNode-1d75c893-ba73-444e-a057-f4f7d00952e0 
[INFO ] 2024-10-24 10:47:28.445 - [测试Offset - Copy][Local27018] - PDK connector node released: HazelcastTargetPdkDataNode-1d75c893-ba73-444e-a057-f4f7d00952e0 
[INFO ] 2024-10-24 10:47:28.445 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] schema data cleaned 
[INFO ] 2024-10-24 10:47:28.446 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] monitor closed 
[INFO ] 2024-10-24 10:47:28.660 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] close complete, cost 31 ms 
[INFO ] 2024-10-24 10:47:30.727 - [测试Offset - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-24 10:47:30.728 - [测试Offset - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@722159c3 
[INFO ] 2024-10-24 10:47:30.848 - [测试Offset - Copy] - Stop task milestones: 671947ca488a1123340fd30e(测试Offset - Copy)  
[INFO ] 2024-10-24 10:47:30.863 - [测试Offset - Copy] - Stopped task aspect(s) 
[INFO ] 2024-10-24 10:47:30.863 - [测试Offset - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-10-24 10:47:30.898 - [测试Offset - Copy] - Remove memory task client succeed, task: 测试Offset - Copy[671947ca488a1123340fd30e] 
[INFO ] 2024-10-24 10:47:30.899 - [测试Offset - Copy] - Destroy memory task client cache succeed, task: 测试Offset - Copy[671947ca488a1123340fd30e] 
[INFO ] 2024-10-24 10:47:32.149 - [测试Offset - Copy] - Task initialization... 
[INFO ] 2024-10-24 10:47:32.153 - [测试Offset - Copy] - Start task milestones: 671947ca488a1123340fd30e(测试Offset - Copy) 
[INFO ] 2024-10-24 10:47:32.434 - [测试Offset - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-24 10:47:32.434 - [测试Offset - Copy] - The engine receives 测试Offset - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-24 10:47:32.484 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] start preload schema,table counts: 5 
[INFO ] 2024-10-24 10:47:32.485 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] start preload schema,table counts: 5 
[INFO ] 2024-10-24 10:47:32.485 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-24 10:47:32.485 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] preload schema finished, cost 0 ms 
[INFO ] 2024-10-24 10:47:32.802 - [测试Offset - Copy][Local27018] - Node(Local27018) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-24 10:47:32.804 - [测试Offset - Copy][Local27018] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-24 10:47:32.837 - [测试Offset - Copy][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-10-24 10:47:32.840 - [测试Offset - Copy][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-10-24 10:47:32.842 - [测试Offset - Copy][Mysql3306] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-24 10:47:32.845 - [测试Offset - Copy][Mysql3306] - batch offset found: {"BMSQL_NEW_ORDER":{"batch_read_connector_status":"OVER"},"BMSQL_ITEM":{"batch_read_connector_status":"OVER"},"BMSQL_DISTRICT":{"batch_read_connector_status":"OVER"},"BMSQL_CUSTOMER":{"batch_read_connector_status":"OVER"},"BMSQL_OORDER":{"batch_read_connector_status":"OVER"}},stream offset found: {"name":"09294bf6-91c1-4c47-afd2-478c6aae09ef","offset":{"{\"server\":\"09294bf6-91c1-4c47-afd2-478c6aae09ef\"}":"{\"ts_sec\":1729738036,\"file\":\"binlog.000036\",\"pos\":946964,\"row\":1,\"server_id\":1,\"event\":4}"}} 
[INFO ] 2024-10-24 10:47:32.991 - [测试Offset - Copy][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-10-24 10:47:32.991 - [测试Offset - Copy][Mysql3306] - Initial sync completed 
[INFO ] 2024-10-24 10:47:33.017 - [测试Offset - Copy][Mysql3306] - Starting stream read, table list: [BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_OORDER], offset: {"name":"09294bf6-91c1-4c47-afd2-478c6aae09ef","offset":{"{\"server\":\"09294bf6-91c1-4c47-afd2-478c6aae09ef\"}":"{\"ts_sec\":1729738036,\"file\":\"binlog.000036\",\"pos\":946964,\"row\":1,\"server_id\":1,\"event\":4}"}} 
[INFO ] 2024-10-24 10:47:33.018 - [测试Offset - Copy][Mysql3306] - Starting mysql cdc, server name: 09294bf6-91c1-4c47-afd2-478c6aae09ef 
[INFO ] 2024-10-24 10:47:33.094 - [测试Offset - Copy][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"09294bf6-91c1-4c47-afd2-478c6aae09ef","offset":{"{\"server\":\"09294bf6-91c1-4c47-afd2-478c6aae09ef\"}":"{\"ts_sec\":1729738036,\"file\":\"binlog.000036\",\"pos\":946964,\"row\":1,\"server_id\":1,\"event\":4}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 925591935
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 09294bf6-91c1-4c47-afd2-478c6aae09ef
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-09294bf6-91c1-4c47-afd2-478c6aae09ef
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 09294bf6-91c1-4c47-afd2-478c6aae09ef
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_NEW_ORDER,test.BMSQL_ITEM,test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_OORDER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-24 10:47:33.095 - [测试Offset - Copy][Mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_NEW_ORDER, BMSQL_ITEM, BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_OORDER], data change syncing 
[INFO ] 2024-10-24 10:48:26.974 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] running status set to false 
[INFO ] 2024-10-24 10:48:27.111 - [测试Offset - Copy][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-24 10:48:27.120 - [测试Offset - Copy][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-10-24 10:48:27.121 - [测试Offset - Copy][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-6d7f90bc-e9b6-440a-952c-17c4794db59e 
[INFO ] 2024-10-24 10:48:27.121 - [测试Offset - Copy][Mysql3306] - Incremental sync completed 
[INFO ] 2024-10-24 10:48:27.121 - [测试Offset - Copy][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-6d7f90bc-e9b6-440a-952c-17c4794db59e 
[INFO ] 2024-10-24 10:48:27.123 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] schema data cleaned 
[INFO ] 2024-10-24 10:48:27.123 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] monitor closed 
[INFO ] 2024-10-24 10:48:27.124 - [测试Offset - Copy][Mysql3306] - Node Mysql3306[6d7f90bc-e9b6-440a-952c-17c4794db59e] close complete, cost 171 ms 
[INFO ] 2024-10-24 10:48:27.124 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] running status set to false 
[INFO ] 2024-10-24 10:48:27.150 - [测试Offset - Copy][Local27018] - PDK connector node stopped: HazelcastTargetPdkDataNode-1d75c893-ba73-444e-a057-f4f7d00952e0 
[INFO ] 2024-10-24 10:48:27.151 - [测试Offset - Copy][Local27018] - PDK connector node released: HazelcastTargetPdkDataNode-1d75c893-ba73-444e-a057-f4f7d00952e0 
[INFO ] 2024-10-24 10:48:27.151 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] schema data cleaned 
[INFO ] 2024-10-24 10:48:27.151 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] monitor closed 
[INFO ] 2024-10-24 10:48:27.360 - [测试Offset - Copy][Local27018] - Node Local27018[1d75c893-ba73-444e-a057-f4f7d00952e0] close complete, cost 32 ms 
[INFO ] 2024-10-24 10:48:30.944 - [测试Offset - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-24 10:48:30.945 - [测试Offset - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@599d1de2 
[INFO ] 2024-10-24 10:48:31.062 - [测试Offset - Copy] - Stop task milestones: 671947ca488a1123340fd30e(测试Offset - Copy)  
[INFO ] 2024-10-24 10:48:31.081 - [测试Offset - Copy] - Stopped task aspect(s) 
[INFO ] 2024-10-24 10:48:31.081 - [测试Offset - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-10-24 10:48:31.114 - [测试Offset - Copy] - Remove memory task client succeed, task: 测试Offset - Copy[671947ca488a1123340fd30e] 
[INFO ] 2024-10-24 10:48:31.114 - [测试Offset - Copy] - Destroy memory task client cache succeed, task: 测试Offset - Copy[671947ca488a1123340fd30e] 
