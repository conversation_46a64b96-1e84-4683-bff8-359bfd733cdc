[TRACE] 2025-02-20 12:22:28.016 - [任务 21] - Task initialization... 
[TRACE] 2025-02-20 12:22:28.018 - [任务 21] - Start task milestones: 67b6ade6f3077a243450ddd1(任务 21) 
[INFO ] 2025-02-20 12:22:28.119 - [任务 21] - Loading table structure completed 
[TRACE] 2025-02-20 12:22:28.208 - [任务 21] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-20 12:22:28.208 - [任务 21] - The engine receives 任务 21 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-20 12:22:28.280 - [任务 21] - Task started 
[TRACE] 2025-02-20 12:22:28.280 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] start preload schema,table counts: 1 
[TRACE] 2025-02-20 12:22:28.280 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] start preload schema,table counts: 1 
[TRACE] 2025-02-20 12:22:28.280 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] start preload schema,table counts: 1 
[TRACE] 2025-02-20 12:22:28.281 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 12:22:28.281 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 12:22:28.281 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 12:22:28.281 - [任务 21][表编辑] - Node table_rename_processor(表编辑: 4227886f-ae27-4951-9a8b-b38af8d32e3f) enable batch process 
[INFO ] 2025-02-20 12:22:28.281 - [任务 21][Pg] - Enable partition table support for source database 
[INFO ] 2025-02-20 12:22:28.952 - [任务 21][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-02-20 12:22:28.960 - [任务 21][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-20 12:22:28.961 - [任务 21][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-20 12:22:28.961 - [任务 21][Sybase] - Apply table structure to target database 
[INFO ] 2025-02-20 12:22:29.302 - [任务 21][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-02-20 12:22:29.302 - [任务 21][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-02-20 12:22:29.302 - [任务 21][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-02-20 12:22:29.302 - [任务 21][Pg] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-20 12:22:29.479 - [任务 21][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-20 12:22:29.479 - [任务 21][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-20 12:22:29.518 - [任务 21][Pg] - Starting batch read from 1 tables 
[TRACE] 2025-02-20 12:22:29.534 - [任务 21][Pg] - Initial sync started 
[INFO ] 2025-02-20 12:22:29.534 - [任务 21][Pg] - Starting batch read from table: testBit 
[TRACE] 2025-02-20 12:22:29.535 - [任务 21][Pg] - Table testBit is going to be initial synced 
[TRACE] 2025-02-20 12:22:29.545 - [任务 21][Pg] - Query snapshot row size completed: Pg(43db1cef-3e32-4cfe-bae4-331ca36f68c7) 
[INFO ] 2025-02-20 12:22:29.560 - [任务 21][Pg] - Table testBit has been completed batch read 
[TRACE] 2025-02-20 12:22:29.560 - [任务 21][Pg] - Initial sync completed 
[INFO ] 2025-02-20 12:22:29.560 - [任务 21][Pg] - Batch read completed. 
[TRACE] 2025-02-20 12:22:29.560 - [任务 21][Pg] - Incremental sync starting... 
[TRACE] 2025-02-20 12:22:29.560 - [任务 21][Pg] - Initial sync completed 
[TRACE] 2025-02-20 12:22:29.561 - [任务 21][Pg] - Starting stream read, table list: [testBit], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-02-20 12:22:29.561 - [任务 21][Pg] - Starting incremental sync using database log parser 
[WARN ] 2025-02-20 12:22:29.577 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 12:22:29.577 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 12:22:29.781 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 1/15, first retry time 2025-02-20 12:22:29, next retry time 2025-02-20 12:23:29 
[WARN ] 2025-02-20 12:23:29.844 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 12:23:29.921 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 12:23:29.921 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 2/15, first retry time 2025-02-20 12:22:29, next retry time 2025-02-20 12:24:29 
[WARN ] 2025-02-20 12:24:30.084 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 12:24:30.086 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 12:24:30.288 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 3/15, first retry time 2025-02-20 12:22:29, next retry time 2025-02-20 12:25:30 
[WARN ] 2025-02-20 12:25:30.295 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 12:25:30.296 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 12:25:30.498 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 4/15, first retry time 2025-02-20 12:22:29, next retry time 2025-02-20 12:26:30 
[TRACE] 2025-02-20 12:25:49.555 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] running status set to false 
[WARN ] 2025-02-20 12:25:49.568 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-20 12:25:49.569 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ failed, total cost 00:03:19.984000 
[TRACE] 2025-02-20 12:25:49.572 - [任务 21][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_43db1cef-3e32-4cfe-bae4-331ca36f68c7_1740025348763 
[TRACE] 2025-02-20 12:25:49.573 - [任务 21][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_43db1cef-3e32-4cfe-bae4-331ca36f68c7_1740025348763 
[TRACE] 2025-02-20 12:25:49.573 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] schema data cleaned 
[TRACE] 2025-02-20 12:25:49.575 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] monitor closed 
[TRACE] 2025-02-20 12:25:49.576 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] close complete, cost 24 ms 
[TRACE] 2025-02-20 12:25:49.576 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] running status set to false 
[TRACE] 2025-02-20 12:25:49.576 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] schema data cleaned 
[TRACE] 2025-02-20 12:25:49.576 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] monitor closed 
[TRACE] 2025-02-20 12:25:49.576 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] close complete, cost 0 ms 
[TRACE] 2025-02-20 12:25:49.576 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] running status set to false 
[TRACE] 2025-02-20 12:25:49.579 - [任务 21][Pg] - Incremental sync completed 
[TRACE] 2025-02-20 12:25:49.582 - [任务 21][Pg] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code null): when operate table: unknown, java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-75) has been closed. 
[ERROR] 2025-02-20 12:25:49.689 - [任务 21][Pg] - java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-75) has been closed. <-- Error Message -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-75) has been closed.

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-75) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	io.tapdata.connector.postgres.PostgresConnector.buildSlot(PostgresConnector.java:277)
	io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:479)
	...

<-- Full Stack Trace -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-75) has been closed.
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:146)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.postgres.PostgresConnector.buildSlot(PostgresConnector.java:277)
	at io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:901)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:923)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:913)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:801)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:292)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-75) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more

[TRACE] 2025-02-20 12:25:49.690 - [任务 21][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_f1c3551c-0130-41f2-8388-300eeba224cd_1740025348878 
[TRACE] 2025-02-20 12:25:49.690 - [任务 21][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_f1c3551c-0130-41f2-8388-300eeba224cd_1740025348878 
[TRACE] 2025-02-20 12:25:49.690 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] schema data cleaned 
[TRACE] 2025-02-20 12:25:49.690 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] monitor closed 
[TRACE] 2025-02-20 12:25:49.892 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] close complete, cost 113 ms 
[TRACE] 2025-02-20 12:25:50.819 - [任务 21] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-20 12:25:50.828 - [任务 21] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@25dad362 
[TRACE] 2025-02-20 12:25:50.939 - [任务 21] - Stop task milestones: 67b6ade6f3077a243450ddd1(任务 21)  
[TRACE] 2025-02-20 12:25:50.947 - [任务 21] - Stopped task aspect(s) 
[TRACE] 2025-02-20 12:25:50.947 - [任务 21] - Snapshot order controller have been removed 
[INFO ] 2025-02-20 12:25:50.982 - [任务 21] - Task stopped. 
[TRACE] 2025-02-20 12:25:50.982 - [任务 21] - Remove memory task client succeed, task: 任务 21[67b6ade6f3077a243450ddd1] 
[TRACE] 2025-02-20 12:25:50.982 - [任务 21] - Destroy memory task client cache succeed, task: 任务 21[67b6ade6f3077a243450ddd1] 
[TRACE] 2025-02-20 22:11:09.131 - [任务 21] - Task initialization... 
[TRACE] 2025-02-20 22:11:09.230 - [任务 21] - Start task milestones: 67b6ade6f3077a243450ddd1(任务 21) 
[INFO ] 2025-02-20 22:11:09.230 - [任务 21] - Loading table structure completed 
[TRACE] 2025-02-20 22:11:09.368 - [任务 21] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-20 22:11:09.673 - [任务 21] - The engine receives 任务 21 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-20 22:11:09.673 - [任务 21] - Task started 
[TRACE] 2025-02-20 22:11:09.714 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:11:09.715 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:11:09.716 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:11:09.716 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:11:09.716 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:11:09.716 - [任务 21][表编辑] - Node table_rename_processor(表编辑: 4227886f-ae27-4951-9a8b-b38af8d32e3f) enable batch process 
[TRACE] 2025-02-20 22:11:09.717 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] preload schema finished, cost 0 ms 
[INFO ] 2025-02-20 22:11:09.717 - [任务 21][Pg] - Enable partition table support for source database 
[INFO ] 2025-02-20 22:11:10.379 - [任务 21][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-02-20 22:11:10.386 - [任务 21][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-20 22:11:10.387 - [任务 21][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-20 22:11:10.387 - [任务 21][Sybase] - Apply table structure to target database 
[INFO ] 2025-02-20 22:11:10.772 - [任务 21][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-02-20 22:11:10.774 - [任务 21][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-02-20 22:11:10.774 - [任务 21][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-02-20 22:11:10.774 - [任务 21][Pg] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-20 22:11:11.454 - [任务 21][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-20 22:11:11.455 - [任务 21][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-20 22:11:11.546 - [任务 21][Pg] - Starting batch read from 1 tables 
[TRACE] 2025-02-20 22:11:11.550 - [任务 21][Pg] - Initial sync started 
[INFO ] 2025-02-20 22:11:11.551 - [任务 21][Pg] - Starting batch read from table: td_null_col 
[TRACE] 2025-02-20 22:11:11.551 - [任务 21][Pg] - Table td_null_col is going to be initial synced 
[TRACE] 2025-02-20 22:11:11.741 - [任务 21][Pg] - Query snapshot row size completed: Pg(43db1cef-3e32-4cfe-bae4-331ca36f68c7) 
[INFO ] 2025-02-20 22:11:11.749 - [任务 21][Pg] - Table td_null_col has been completed batch read 
[TRACE] 2025-02-20 22:11:11.749 - [任务 21][Pg] - Initial sync completed 
[INFO ] 2025-02-20 22:11:11.749 - [任务 21][Pg] - Batch read completed. 
[TRACE] 2025-02-20 22:11:11.749 - [任务 21][Pg] - Incremental sync starting... 
[TRACE] 2025-02-20 22:11:11.749 - [任务 21][Pg] - Initial sync completed 
[TRACE] 2025-02-20 22:11:11.749 - [任务 21][Pg] - Starting stream read, table list: [td_null_col], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-02-20 22:11:11.761 - [任务 21][Pg] - Starting incremental sync using database log parser 
[WARN ] 2025-02-20 22:11:11.762 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:11:11.905 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:11:11.906 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 1/15, first retry time 2025-02-20 22:11:11, next retry time 2025-02-20 22:12:11 
[WARN ] 2025-02-20 22:12:12.743 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:12:12.746 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:12:12.950 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 2/15, first retry time 2025-02-20 22:11:11, next retry time 2025-02-20 22:13:12 
[WARN ] 2025-02-20 22:13:13.599 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:13:13.713 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:13:13.918 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 3/15, first retry time 2025-02-20 22:11:11, next retry time 2025-02-20 22:14:13 
[WARN ] 2025-02-20 22:14:14.381 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:14:14.500 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:14:14.500 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 4/15, first retry time 2025-02-20 22:11:11, next retry time 2025-02-20 22:15:14 
[TRACE] 2025-02-20 22:14:48.764 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] running status set to false 
[WARN ] 2025-02-20 22:14:48.784 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[TRACE] 2025-02-20 22:14:48.784 - [任务 21][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_43db1cef-3e32-4cfe-bae4-331ca36f68c7_1740060670200 
[INFO ] 2025-02-20 22:14:48.785 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ failed, total cost 00:03:36.933000 
[TRACE] 2025-02-20 22:14:48.785 - [任务 21][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_43db1cef-3e32-4cfe-bae4-331ca36f68c7_1740060670200 
[TRACE] 2025-02-20 22:14:48.788 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] schema data cleaned 
[TRACE] 2025-02-20 22:14:48.788 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] monitor closed 
[TRACE] 2025-02-20 22:14:48.792 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] close complete, cost 42 ms 
[TRACE] 2025-02-20 22:14:48.793 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] running status set to false 
[TRACE] 2025-02-20 22:14:48.793 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] schema data cleaned 
[TRACE] 2025-02-20 22:14:48.793 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] monitor closed 
[TRACE] 2025-02-20 22:14:48.795 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] close complete, cost 4 ms 
[TRACE] 2025-02-20 22:14:48.795 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] running status set to false 
[TRACE] 2025-02-20 22:14:48.834 - [任务 21][Pg] - Incremental sync completed 
[TRACE] 2025-02-20 22:14:48.837 - [任务 21][Pg] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code null): when operate table: unknown, java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-55) has been closed. 
[ERROR] 2025-02-20 22:14:48.916 - [任务 21][Pg] - java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-55) has been closed. <-- Error Message -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-55) has been closed.

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-55) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	io.tapdata.connector.postgres.PostgresConnector.buildSlot(PostgresConnector.java:275)
	io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:477)
	...

<-- Full Stack Trace -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-55) has been closed.
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:146)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.postgres.PostgresConnector.buildSlot(PostgresConnector.java:275)
	at io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:477)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:901)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:923)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:913)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:801)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:292)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-55) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more

[TRACE] 2025-02-20 22:14:48.919 - [任务 21][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_f1c3551c-0130-41f2-8388-300eeba224cd_1740060670312 
[TRACE] 2025-02-20 22:14:48.919 - [任务 21][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_f1c3551c-0130-41f2-8388-300eeba224cd_1740060670312 
[TRACE] 2025-02-20 22:14:48.919 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] schema data cleaned 
[TRACE] 2025-02-20 22:14:48.919 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] monitor closed 
[TRACE] 2025-02-20 22:14:49.124 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] close complete, cost 124 ms 
[TRACE] 2025-02-20 22:14:50.665 - [任务 21] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-20 22:14:50.665 - [任务 21] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@8d187bd 
[TRACE] 2025-02-20 22:14:50.812 - [任务 21] - Stop task milestones: 67b6ade6f3077a243450ddd1(任务 21)  
[TRACE] 2025-02-20 22:14:50.812 - [任务 21] - Stopped task aspect(s) 
[TRACE] 2025-02-20 22:14:50.813 - [任务 21] - Snapshot order controller have been removed 
[INFO ] 2025-02-20 22:14:50.813 - [任务 21] - Task stopped. 
[TRACE] 2025-02-20 22:14:50.852 - [任务 21] - Remove memory task client succeed, task: 任务 21[67b6ade6f3077a243450ddd1] 
[TRACE] 2025-02-20 22:14:50.853 - [任务 21] - Destroy memory task client cache succeed, task: 任务 21[67b6ade6f3077a243450ddd1] 
[TRACE] 2025-02-20 22:14:58.655 - [任务 21] - Task initialization... 
[TRACE] 2025-02-20 22:14:58.753 - [任务 21] - Start task milestones: 67b6ade6f3077a243450ddd1(任务 21) 
[INFO ] 2025-02-20 22:14:58.753 - [任务 21] - Loading table structure completed 
[TRACE] 2025-02-20 22:14:58.823 - [任务 21] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-20 22:14:58.823 - [任务 21] - The engine receives 任务 21 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-20 22:14:58.873 - [任务 21] - Task started 
[TRACE] 2025-02-20 22:14:58.874 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:14:58.874 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:14:58.874 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:14:58.874 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:14:58.874 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:14:58.874 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:14:58.874 - [任务 21][表编辑] - Node table_rename_processor(表编辑: 4227886f-ae27-4951-9a8b-b38af8d32e3f) enable batch process 
[INFO ] 2025-02-20 22:14:58.874 - [任务 21][Pg] - Enable partition table support for source database 
[INFO ] 2025-02-20 22:14:59.674 - [任务 21][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-02-20 22:14:59.682 - [任务 21][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-20 22:14:59.682 - [任务 21][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-20 22:14:59.818 - [任务 21][Sybase] - Apply table structure to target database 
[TRACE] 2025-02-20 22:14:59.826 - [任务 21][Sybase] - The table test1_td_null_col has already exist. 
[INFO ] 2025-02-20 22:15:00.225 - [任务 21][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-02-20 22:15:00.225 - [任务 21][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-02-20 22:15:00.225 - [任务 21][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-02-20 22:15:00.226 - [任务 21][Pg] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-20 22:15:01.142 - [任务 21][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-20 22:15:01.142 - [任务 21][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-20 22:15:01.215 - [任务 21][Pg] - Starting batch read from 1 tables 
[TRACE] 2025-02-20 22:15:01.215 - [任务 21][Pg] - Initial sync started 
[INFO ] 2025-02-20 22:15:01.217 - [任务 21][Pg] - Starting batch read from table: td_null_col 
[TRACE] 2025-02-20 22:15:01.217 - [任务 21][Pg] - Table td_null_col is going to be initial synced 
[TRACE] 2025-02-20 22:15:01.417 - [任务 21][Pg] - Query snapshot row size completed: Pg(43db1cef-3e32-4cfe-bae4-331ca36f68c7) 
[INFO ] 2025-02-20 22:15:01.478 - [任务 21][Pg] - Table td_null_col has been completed batch read 
[TRACE] 2025-02-20 22:15:01.478 - [任务 21][Pg] - Initial sync completed 
[INFO ] 2025-02-20 22:15:01.478 - [任务 21][Pg] - Batch read completed. 
[TRACE] 2025-02-20 22:15:01.479 - [任务 21][Pg] - Incremental sync starting... 
[TRACE] 2025-02-20 22:15:01.479 - [任务 21][Pg] - Initial sync completed 
[TRACE] 2025-02-20 22:15:01.479 - [任务 21][Pg] - Starting stream read, table list: [td_null_col], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-02-20 22:15:01.479 - [任务 21][Pg] - Starting incremental sync using database log parser 
[WARN ] 2025-02-20 22:15:01.479 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:15:01.591 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:15:01.592 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 1/15, first retry time 2025-02-20 22:15:01, next retry time 2025-02-20 22:16:01 
[TRACE] 2025-02-20 22:15:23.820 - [任务 21][Sybase] - Table 'test1_td_null_col' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[WARN ] 2025-02-20 22:16:02.270 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:16:02.375 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:16:02.377 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 2/15, first retry time 2025-02-20 22:15:01, next retry time 2025-02-20 22:17:02 
[TRACE] 2025-02-20 22:16:09.266 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] running status set to false 
[WARN ] 2025-02-20 22:16:09.279 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-20 22:16:09.282 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ failed, total cost 00:01:07.690000 
[TRACE] 2025-02-20 22:16:09.282 - [任务 21][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_43db1cef-3e32-4cfe-bae4-331ca36f68c7_1740060899439 
[TRACE] 2025-02-20 22:16:09.282 - [任务 21][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_43db1cef-3e32-4cfe-bae4-331ca36f68c7_1740060899439 
[TRACE] 2025-02-20 22:16:09.282 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] schema data cleaned 
[TRACE] 2025-02-20 22:16:09.284 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] monitor closed 
[TRACE] 2025-02-20 22:16:09.284 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] close complete, cost 28 ms 
[TRACE] 2025-02-20 22:16:09.285 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] running status set to false 
[TRACE] 2025-02-20 22:16:09.285 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] schema data cleaned 
[TRACE] 2025-02-20 22:16:09.285 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] monitor closed 
[TRACE] 2025-02-20 22:16:09.285 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] close complete, cost 0 ms 
[TRACE] 2025-02-20 22:16:09.291 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] running status set to false 
[TRACE] 2025-02-20 22:16:09.294 - [任务 21][Pg] - Incremental sync completed 
[TRACE] 2025-02-20 22:16:09.294 - [任务 21][Pg] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code null): when operate table: unknown, java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-59) has been closed. 
[ERROR] 2025-02-20 22:16:09.415 - [任务 21][Pg] - java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-59) has been closed. <-- Error Message -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-59) has been closed.

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-59) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	io.tapdata.connector.postgres.PostgresConnector.buildSlot(PostgresConnector.java:275)
	io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:477)
	...

<-- Full Stack Trace -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-59) has been closed.
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:146)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.postgres.PostgresConnector.buildSlot(PostgresConnector.java:275)
	at io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:477)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:901)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:923)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:913)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:801)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:292)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-59) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more

[TRACE] 2025-02-20 22:16:09.421 - [任务 21][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_f1c3551c-0130-41f2-8388-300eeba224cd_1740060899588 
[TRACE] 2025-02-20 22:16:09.421 - [任务 21][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_f1c3551c-0130-41f2-8388-300eeba224cd_1740060899588 
[TRACE] 2025-02-20 22:16:09.421 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] schema data cleaned 
[TRACE] 2025-02-20 22:16:09.421 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] monitor closed 
[TRACE] 2025-02-20 22:16:09.623 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] close complete, cost 136 ms 
[TRACE] 2025-02-20 22:16:13.712 - [任务 21] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-20 22:16:13.713 - [任务 21] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6207af26 
[TRACE] 2025-02-20 22:16:13.844 - [任务 21] - Stop task milestones: 67b6ade6f3077a243450ddd1(任务 21)  
[TRACE] 2025-02-20 22:16:13.859 - [任务 21] - Stopped task aspect(s) 
[TRACE] 2025-02-20 22:16:13.859 - [任务 21] - Snapshot order controller have been removed 
[INFO ] 2025-02-20 22:16:13.889 - [任务 21] - Task stopped. 
[TRACE] 2025-02-20 22:16:13.889 - [任务 21] - Remove memory task client succeed, task: 任务 21[67b6ade6f3077a243450ddd1] 
[TRACE] 2025-02-20 22:16:13.889 - [任务 21] - Destroy memory task client cache succeed, task: 任务 21[67b6ade6f3077a243450ddd1] 
[TRACE] 2025-02-20 22:16:21.970 - [任务 21] - Task initialization... 
[TRACE] 2025-02-20 22:16:21.971 - [任务 21] - Start task milestones: 67b6ade6f3077a243450ddd1(任务 21) 
[INFO ] 2025-02-20 22:16:22.149 - [任务 21] - Loading table structure completed 
[TRACE] 2025-02-20 22:16:22.151 - [任务 21] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-20 22:16:22.210 - [任务 21] - The engine receives 任务 21 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-20 22:16:22.210 - [任务 21] - Task started 
[TRACE] 2025-02-20 22:16:22.252 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:16:22.253 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:16:22.253 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:16:22.253 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:16:22.253 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:16:22.253 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:16:22.253 - [任务 21][表编辑] - Node table_rename_processor(表编辑: 4227886f-ae27-4951-9a8b-b38af8d32e3f) enable batch process 
[INFO ] 2025-02-20 22:16:22.254 - [任务 21][Pg] - Enable partition table support for source database 
[INFO ] 2025-02-20 22:16:28.237 - [任务 21][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-02-20 22:16:28.238 - [任务 21][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-20 22:16:28.238 - [任务 21][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-20 22:16:28.257 - [任务 21][Sybase] - Apply table structure to target database 
[TRACE] 2025-02-20 22:16:28.464 - [任务 21][Sybase] - The table test1_td_null_col has already exist. 
[INFO ] 2025-02-20 22:16:31.679 - [任务 21][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-02-20 22:16:31.682 - [任务 21][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-02-20 22:16:31.682 - [任务 21][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-02-20 22:16:31.682 - [任务 21][Pg] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-20 22:16:32.548 - [任务 21][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-20 22:16:32.550 - [任务 21][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-20 22:16:32.628 - [任务 21][Pg] - Starting batch read from 1 tables 
[TRACE] 2025-02-20 22:16:32.629 - [任务 21][Pg] - Initial sync started 
[INFO ] 2025-02-20 22:16:32.633 - [任务 21][Pg] - Starting batch read from table: td_null_col 
[TRACE] 2025-02-20 22:16:32.633 - [任务 21][Pg] - Table td_null_col is going to be initial synced 
[TRACE] 2025-02-20 22:16:32.836 - [任务 21][Pg] - Query snapshot row size completed: Pg(43db1cef-3e32-4cfe-bae4-331ca36f68c7) 
[INFO ] 2025-02-20 22:16:32.846 - [任务 21][Pg] - Table td_null_col has been completed batch read 
[TRACE] 2025-02-20 22:16:32.846 - [任务 21][Pg] - Initial sync completed 
[INFO ] 2025-02-20 22:16:32.846 - [任务 21][Pg] - Batch read completed. 
[TRACE] 2025-02-20 22:16:32.847 - [任务 21][Pg] - Incremental sync starting... 
[TRACE] 2025-02-20 22:16:32.847 - [任务 21][Pg] - Initial sync completed 
[TRACE] 2025-02-20 22:16:32.851 - [任务 21][Pg] - Starting stream read, table list: [td_null_col], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-02-20 22:16:32.851 - [任务 21][Pg] - Starting incremental sync using database log parser 
[WARN ] 2025-02-20 22:16:32.938 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:16:32.939 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:16:33.140 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 1/15, first retry time 2025-02-20 22:16:32, next retry time 2025-02-20 22:17:32 
[TRACE] 2025-02-20 22:16:34.683 - [任务 21][Sybase] - Table 'test1_td_null_col' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-02-20 22:17:09.230 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] running status set to false 
[WARN ] 2025-02-20 22:17:09.255 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-20 22:17:09.259 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ failed, total cost 00:00:36.304000 
[TRACE] 2025-02-20 22:17:09.260 - [任务 21][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_43db1cef-3e32-4cfe-bae4-331ca36f68c7_1740060982855 
[TRACE] 2025-02-20 22:17:09.260 - [任务 21][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_43db1cef-3e32-4cfe-bae4-331ca36f68c7_1740060982855 
[TRACE] 2025-02-20 22:17:09.260 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] schema data cleaned 
[TRACE] 2025-02-20 22:17:09.264 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] monitor closed 
[TRACE] 2025-02-20 22:17:09.264 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] close complete, cost 63 ms 
[TRACE] 2025-02-20 22:17:09.264 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] running status set to false 
[TRACE] 2025-02-20 22:17:09.264 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] schema data cleaned 
[TRACE] 2025-02-20 22:17:09.264 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] monitor closed 
[TRACE] 2025-02-20 22:17:09.265 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] close complete, cost 1 ms 
[TRACE] 2025-02-20 22:17:09.266 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] running status set to false 
[TRACE] 2025-02-20 22:17:09.275 - [任务 21][Pg] - Incremental sync completed 
[TRACE] 2025-02-20 22:17:09.276 - [任务 21][Pg] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code null): when operate table: unknown, java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-61) has been closed. 
[ERROR] 2025-02-20 22:17:09.369 - [任务 21][Pg] - java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-61) has been closed. <-- Error Message -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-61) has been closed.

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-61) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	io.tapdata.connector.postgres.PostgresConnector.buildSlot(PostgresConnector.java:275)
	io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:477)
	...

<-- Full Stack Trace -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-61) has been closed.
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:146)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.postgres.PostgresConnector.buildSlot(PostgresConnector.java:275)
	at io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:477)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:901)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:923)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:913)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:801)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:292)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-61) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more

[TRACE] 2025-02-20 22:17:09.369 - [任务 21][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_f1c3551c-0130-41f2-8388-300eeba224cd_1740060982972 
[TRACE] 2025-02-20 22:17:09.369 - [任务 21][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_f1c3551c-0130-41f2-8388-300eeba224cd_1740060982972 
[TRACE] 2025-02-20 22:17:09.369 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] schema data cleaned 
[TRACE] 2025-02-20 22:17:09.369 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] monitor closed 
[TRACE] 2025-02-20 22:17:09.572 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] close complete, cost 104 ms 
[TRACE] 2025-02-20 22:17:11.507 - [任务 21] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-20 22:17:11.507 - [任务 21] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@a785bec 
[TRACE] 2025-02-20 22:17:11.656 - [任务 21] - Stop task milestones: 67b6ade6f3077a243450ddd1(任务 21)  
[TRACE] 2025-02-20 22:17:11.658 - [任务 21] - Stopped task aspect(s) 
[TRACE] 2025-02-20 22:17:11.658 - [任务 21] - Snapshot order controller have been removed 
[INFO ] 2025-02-20 22:17:11.658 - [任务 21] - Task stopped. 
[TRACE] 2025-02-20 22:17:11.700 - [任务 21] - Remove memory task client succeed, task: 任务 21[67b6ade6f3077a243450ddd1] 
[TRACE] 2025-02-20 22:17:11.700 - [任务 21] - Destroy memory task client cache succeed, task: 任务 21[67b6ade6f3077a243450ddd1] 
[TRACE] 2025-02-20 22:17:15.036 - [任务 21] - Task initialization... 
[TRACE] 2025-02-20 22:17:15.242 - [任务 21] - Start task milestones: 67b6ade6f3077a243450ddd1(任务 21) 
[INFO ] 2025-02-20 22:17:15.343 - [任务 21] - Loading table structure completed 
[TRACE] 2025-02-20 22:17:15.507 - [任务 21] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-20 22:17:15.509 - [任务 21] - The engine receives 任务 21 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-20 22:17:15.587 - [任务 21] - Task started 
[TRACE] 2025-02-20 22:17:15.588 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:17:15.588 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:17:15.588 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:17:15.589 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:17:15.589 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:17:15.589 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:17:15.589 - [任务 21][表编辑] - Node table_rename_processor(表编辑: 4227886f-ae27-4951-9a8b-b38af8d32e3f) enable batch process 
[INFO ] 2025-02-20 22:17:15.794 - [任务 21][Pg] - Enable partition table support for source database 
[INFO ] 2025-02-20 22:17:16.328 - [任务 21][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-02-20 22:17:16.328 - [任务 21][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-20 22:17:16.328 - [任务 21][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-20 22:17:16.334 - [任务 21][Sybase] - Apply table structure to target database 
[TRACE] 2025-02-20 22:17:16.540 - [任务 21][Sybase] - The table test1_td_null_col has already exist. 
[INFO ] 2025-02-20 22:17:16.896 - [任务 21][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-02-20 22:17:16.896 - [任务 21][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-02-20 22:17:16.896 - [任务 21][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-02-20 22:17:16.896 - [任务 21][Pg] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-20 22:17:17.701 - [任务 21][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-20 22:17:17.701 - [任务 21][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-20 22:17:17.810 - [任务 21][Pg] - Starting batch read from 1 tables 
[TRACE] 2025-02-20 22:17:17.810 - [任务 21][Pg] - Initial sync started 
[INFO ] 2025-02-20 22:17:17.858 - [任务 21][Pg] - Starting batch read from table: td_null_col 
[TRACE] 2025-02-20 22:17:17.858 - [任务 21][Pg] - Table td_null_col is going to be initial synced 
[TRACE] 2025-02-20 22:17:18.024 - [任务 21][Pg] - Query snapshot row size completed: Pg(43db1cef-3e32-4cfe-bae4-331ca36f68c7) 
[INFO ] 2025-02-20 22:17:18.024 - [任务 21][Pg] - Table td_null_col has been completed batch read 
[TRACE] 2025-02-20 22:17:18.024 - [任务 21][Pg] - Initial sync completed 
[INFO ] 2025-02-20 22:17:18.024 - [任务 21][Pg] - Batch read completed. 
[TRACE] 2025-02-20 22:17:18.024 - [任务 21][Pg] - Incremental sync starting... 
[TRACE] 2025-02-20 22:17:18.024 - [任务 21][Pg] - Initial sync completed 
[TRACE] 2025-02-20 22:17:18.025 - [任务 21][Pg] - Starting stream read, table list: [td_null_col], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-02-20 22:17:18.025 - [任务 21][Pg] - Starting incremental sync using database log parser 
[WARN ] 2025-02-20 22:17:18.112 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:17:18.112 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:17:18.313 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 1/15, first retry time 2025-02-20 22:17:18, next retry time 2025-02-20 22:18:18 
[TRACE] 2025-02-20 22:18:22.845 - [任务 21][Sybase] - Table 'test1_td_null_col' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[WARN ] 2025-02-20 22:18:23.392 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:18:23.393 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:18:23.594 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 2/15, first retry time 2025-02-20 22:17:18, next retry time 2025-02-20 22:19:23 
[WARN ] 2025-02-20 22:19:24.079 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:19:24.166 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:19:24.367 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 3/15, first retry time 2025-02-20 22:17:18, next retry time 2025-02-20 22:20:24 
[WARN ] 2025-02-20 22:20:24.914 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:20:25.021 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:20:25.023 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 4/15, first retry time 2025-02-20 22:17:18, next retry time 2025-02-20 22:21:25 
[WARN ] 2025-02-20 22:21:25.834 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:21:25.837 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:21:26.043 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 5/15, first retry time 2025-02-20 22:17:18, next retry time 2025-02-20 22:22:25 
[WARN ] 2025-02-20 22:22:26.436 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:22:26.466 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:22:26.467 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 6/15, first retry time 2025-02-20 22:17:18, next retry time 2025-02-20 22:23:26 
[WARN ] 2025-02-20 22:23:27.047 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:23:27.118 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:23:27.118 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 7/15, first retry time 2025-02-20 22:17:18, next retry time 2025-02-20 22:24:27 
[WARN ] 2025-02-20 22:24:27.765 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:24:27.835 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 8 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:24:27.835 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 8/15, first retry time 2025-02-20 22:17:18, next retry time 2025-02-20 22:25:27 
[WARN ] 2025-02-20 22:25:28.461 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:25:28.462 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 7 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:25:28.669 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 9/15, first retry time 2025-02-20 22:17:18, next retry time 2025-02-20 22:26:28 
[WARN ] 2025-02-20 22:26:29.010 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:26:29.083 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 6 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:26:29.084 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 10/15, first retry time 2025-02-20 22:17:18, next retry time 2025-02-20 22:27:29 
[WARN ] 2025-02-20 22:27:29.591 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:27:29.652 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 5 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:27:29.652 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 11/15, first retry time 2025-02-20 22:17:18, next retry time 2025-02-20 22:28:29 
[WARN ] 2025-02-20 22:28:30.123 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:28:30.194 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 4 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:28:30.194 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 12/15, first retry time 2025-02-20 22:17:18, next retry time 2025-02-20 22:29:30 
[TRACE] 2025-02-20 22:28:49.704 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] running status set to false 
[WARN ] 2025-02-20 22:28:49.708 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-20 22:28:49.708 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ failed, total cost 00:11:31.579000 
[TRACE] 2025-02-20 22:28:49.708 - [任务 21][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_43db1cef-3e32-4cfe-bae4-331ca36f68c7_1740061036163 
[TRACE] 2025-02-20 22:28:49.709 - [任务 21][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_43db1cef-3e32-4cfe-bae4-331ca36f68c7_1740061036163 
[TRACE] 2025-02-20 22:28:49.709 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] schema data cleaned 
[TRACE] 2025-02-20 22:28:49.712 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] monitor closed 
[TRACE] 2025-02-20 22:28:49.712 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] close complete, cost 40 ms 
[TRACE] 2025-02-20 22:28:49.713 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] running status set to false 
[TRACE] 2025-02-20 22:28:49.713 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] schema data cleaned 
[TRACE] 2025-02-20 22:28:49.714 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] monitor closed 
[TRACE] 2025-02-20 22:28:49.714 - [任务 21][表编辑] - Node 表编辑[4227886f-ae27-4951-9a8b-b38af8d32e3f] close complete, cost 1 ms 
[TRACE] 2025-02-20 22:28:49.731 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] running status set to false 
[TRACE] 2025-02-20 22:28:49.733 - [任务 21][Pg] - Incremental sync completed 
[TRACE] 2025-02-20 22:28:49.733 - [任务 21][Pg] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code null): when operate table: unknown, java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-77) has been closed. 
[ERROR] 2025-02-20 22:28:49.815 - [任务 21][Pg] - java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-77) has been closed. <-- Error Message -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-77) has been closed.

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-77) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	io.tapdata.connector.postgres.PostgresConnector.buildSlot(PostgresConnector.java:275)
	io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:477)
	...

<-- Full Stack Trace -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-77) has been closed.
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:146)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.postgres.PostgresConnector.buildSlot(PostgresConnector.java:275)
	at io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:477)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:901)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:923)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:913)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:801)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:292)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-77) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more

[TRACE] 2025-02-20 22:28:49.816 - [任务 21][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_f1c3551c-0130-41f2-8388-300eeba224cd_1740061036260 
[TRACE] 2025-02-20 22:28:49.816 - [任务 21][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_f1c3551c-0130-41f2-8388-300eeba224cd_1740061036260 
[TRACE] 2025-02-20 22:28:49.816 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] schema data cleaned 
[TRACE] 2025-02-20 22:28:49.816 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] monitor closed 
[TRACE] 2025-02-20 22:28:49.817 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] close complete, cost 102 ms 
[TRACE] 2025-02-20 22:28:53.256 - [任务 21] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-20 22:28:53.256 - [任务 21] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@b0f3bd6 
[TRACE] 2025-02-20 22:28:53.401 - [任务 21] - Stop task milestones: 67b6ade6f3077a243450ddd1(任务 21)  
[TRACE] 2025-02-20 22:28:53.414 - [任务 21] - Stopped task aspect(s) 
[TRACE] 2025-02-20 22:28:53.414 - [任务 21] - Snapshot order controller have been removed 
[INFO ] 2025-02-20 22:28:53.414 - [任务 21] - Task stopped. 
[TRACE] 2025-02-20 22:28:53.436 - [任务 21] - Remove memory task client succeed, task: 任务 21[67b6ade6f3077a243450ddd1] 
[TRACE] 2025-02-20 22:28:53.437 - [任务 21] - Destroy memory task client cache succeed, task: 任务 21[67b6ade6f3077a243450ddd1] 
[TRACE] 2025-02-20 22:46:58.156 - [任务 21] - Task initialization... 
[TRACE] 2025-02-20 22:46:58.259 - [任务 21] - Start task milestones: 67b6ade6f3077a243450ddd1(任务 21) 
[INFO ] 2025-02-20 22:46:58.260 - [任务 21] - Loading table structure completed 
[TRACE] 2025-02-20 22:46:58.398 - [任务 21] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-20 22:46:58.457 - [任务 21] - The engine receives 任务 21 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-20 22:46:58.457 - [任务 21] - Task started 
[TRACE] 2025-02-20 22:46:58.493 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:46:58.494 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:46:58.494 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:46:58.494 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] preload schema finished, cost 0 ms 
[INFO ] 2025-02-20 22:46:58.698 - [任务 21][Pg] - Enable partition table support for source database 
[INFO ] 2025-02-20 22:46:59.133 - [任务 21][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-02-20 22:46:59.141 - [任务 21][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-20 22:46:59.142 - [任务 21][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-20 22:46:59.276 - [任务 21][Sybase] - Apply table structure to target database 
[TRACE] 2025-02-20 22:46:59.277 - [任务 21][Sybase] - The table td_test_varchar has already exist. 
[INFO ] 2025-02-20 22:46:59.578 - [任务 21][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-02-20 22:46:59.578 - [任务 21][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-02-20 22:46:59.578 - [任务 21][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-02-20 22:46:59.578 - [任务 21][Pg] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-20 22:46:59.995 - [任务 21][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-02-20 22:46:59.995 - [任务 21][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-20 22:47:00.108 - [任务 21][Pg] - Starting batch read from 1 tables 
[TRACE] 2025-02-20 22:47:00.111 - [任务 21][Pg] - Initial sync started 
[INFO ] 2025-02-20 22:47:00.111 - [任务 21][Pg] - Starting batch read from table: td_test_varchar 
[TRACE] 2025-02-20 22:47:00.111 - [任务 21][Pg] - Table td_test_varchar is going to be initial synced 
[TRACE] 2025-02-20 22:47:00.163 - [任务 21][Pg] - Query snapshot row size completed: Pg(43db1cef-3e32-4cfe-bae4-331ca36f68c7) 
[INFO ] 2025-02-20 22:47:00.202 - [任务 21][Pg] - Table td_test_varchar has been completed batch read 
[TRACE] 2025-02-20 22:47:00.203 - [任务 21][Pg] - Initial sync completed 
[INFO ] 2025-02-20 22:47:00.203 - [任务 21][Pg] - Batch read completed. 
[TRACE] 2025-02-20 22:47:00.203 - [任务 21][Pg] - Incremental sync starting... 
[TRACE] 2025-02-20 22:47:00.203 - [任务 21][Pg] - Initial sync completed 
[TRACE] 2025-02-20 22:47:00.211 - [任务 21][Pg] - Starting stream read, table list: [td_test_varchar], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-02-20 22:47:00.211 - [任务 21][Pg] - Starting incremental sync using database log parser 
[WARN ] 2025-02-20 22:47:00.257 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[WARN ] 2025-02-20 22:47:00.257 - [任务 21][Pg] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: all replication slots are in use
  建议：Free one or increase max_replication_slots.
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2025-02-20 22:47:00.458 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ, retry times 1/15, first retry time 2025-02-20 22:47:00, next retry time 2025-02-20 22:48:00 
[TRACE] 2025-02-20 22:47:00.932 - [任务 21][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Unable to write data to sybase due to violation of unique constraint.
 - Table name: td_test_varchar
 - Target field: ["id"]
 - Data to be written(null): null
 - Unique constraint: null 
[ERROR] 2025-02-20 22:47:00.958 - [任务 21][Sybase] - Unable to write data to sybase due to violation of unique constraint.
 - Table name: td_test_varchar
 - Target field: ["id"]
 - Data to be written(null): null
 - Unique constraint: null <-- Error Message -->
Unable to write data to sybase due to violation of unique constraint.
 - Table name: td_test_varchar
 - Target field: ["id"]
 - Data to be written(null): null
 - Unique constraint: null

<-- Simple Stack Trace -->
Caused by: com.sybase.jdbc4.jdbc.SybBatchUpdateException: JZ0BE: BatchUpdateException: 执行批处理语句Attempt to insert duplicate key row in object 'td_test_varchar' with unique index 'td_test_va_id_10810518561'
时出现错误
	com.sybase.jdbc4.jdbc.ErrorMessage.raiseBatchUpdateException(ErrorMessage.java:1331)
	com.sybase.jdbc4.jdbc.SybStatement.batchLoop(SybStatement.java:2380)
	com.sybase.jdbc4.jdbc.SybStatement.sendBatch(SybStatement.java:2170)
	com.sybase.jdbc4.jdbc.SybStatement.executeBatch(SybStatement.java:2128)
	com.sybase.jdbc4.jdbc.SybStatement.executeBatch(SybStatement.java:2046)
	...

<-- Full Stack Trace -->
com.sybase.jdbc4.jdbc.SybBatchUpdateException: JZ0BE: BatchUpdateException: 执行批处理语句Attempt to insert duplicate key row in object 'td_test_varchar' with unique index 'td_test_va_id_10810518561'
时出现错误
	at io.tapdata.sybase.SybaseExceptionCollector.collectViolateUnique(SybaseExceptionCollector.java:28)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:139)
	at io.tapdata.sybase.SybaseConnectorV2.writeRecord(SybaseConnectorV2.java:611)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$68(HazelcastTargetPdkDataNode.java:1040)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$69(HazelcastTargetPdkDataNode.java:1036)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:991)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:966)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$36(HazelcastTargetPdkDataNode.java:665)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:665)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:925)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:843)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:792)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$23(HazelcastTargetPdkBaseNode.java:752)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:638)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:724)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:776)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:723)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.sybase.jdbc4.jdbc.SybBatchUpdateException: JZ0BE: BatchUpdateException: 执行批处理语句Attempt to insert duplicate key row in object 'td_test_varchar' with unique index 'td_test_va_id_10810518561'
时出现错误
	at com.sybase.jdbc4.jdbc.ErrorMessage.raiseBatchUpdateException(ErrorMessage.java:1331)
	at com.sybase.jdbc4.jdbc.SybStatement.batchLoop(SybStatement.java:2380)
	at com.sybase.jdbc4.jdbc.SybStatement.sendBatch(SybStatement.java:2170)
	at com.sybase.jdbc4.jdbc.SybStatement.executeBatch(SybStatement.java:2128)
	at com.sybase.jdbc4.jdbc.SybStatement.executeBatch(SybStatement.java:2046)
	at com.sybase.jdbc4.jdbc.SybPreparedStatement.executeBatch(SybPreparedStatement.java:2092)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:121)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:119)
	... 32 more

[TRACE] 2025-02-20 22:47:00.959 - [任务 21][Sybase] - Job suspend in error handle 
[TRACE] 2025-02-20 22:47:01.038 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] running status set to false 
[WARN ] 2025-02-20 22:47:01.040 - [任务 21][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-02-20 22:47:01.044 - [任务 21][Pg] - Retry operation SOURCE_STREAM_READ failed, total cost 00:00:00.748000 
[TRACE] 2025-02-20 22:47:01.044 - [任务 21][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_43db1cef-3e32-4cfe-bae4-331ca36f68c7_1740062819163 
[TRACE] 2025-02-20 22:47:01.045 - [任务 21][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_43db1cef-3e32-4cfe-bae4-331ca36f68c7_1740062819163 
[TRACE] 2025-02-20 22:47:01.045 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] schema data cleaned 
[TRACE] 2025-02-20 22:47:01.046 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] monitor closed 
[TRACE] 2025-02-20 22:47:01.052 - [任务 21][Pg] - Node Pg[43db1cef-3e32-4cfe-bae4-331ca36f68c7] close complete, cost 34 ms 
[TRACE] 2025-02-20 22:47:01.052 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] running status set to false 
[TRACE] 2025-02-20 22:47:01.066 - [任务 21][Pg] - Incremental sync completed 
[TRACE] 2025-02-20 22:47:01.066 - [任务 21][Pg] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code null): when operate table: unknown, java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-90) has been closed. 
[ERROR] 2025-02-20 22:47:01.074 - [任务 21][Pg] - java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-90) has been closed. <-- Error Message -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-90) has been closed.

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-90) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	io.tapdata.connector.postgres.PostgresConnector.buildSlot(PostgresConnector.java:276)
	io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:477)
	...

<-- Full Stack Trace -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-90) has been closed.
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:146)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.postgres.PostgresConnector.buildSlot(PostgresConnector.java:276)
	at io.tapdata.connector.postgres.PostgresConnector.streamRead(PostgresConnector.java:477)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:901)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$35(HazelcastSourcePdkDataNode.java:923)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:913)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:801)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:292)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-90) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 23 more

[TRACE] 2025-02-20 22:47:01.172 - [任务 21][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_f1c3551c-0130-41f2-8388-300eeba224cd_1740062819029 
[TRACE] 2025-02-20 22:47:01.172 - [任务 21][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_f1c3551c-0130-41f2-8388-300eeba224cd_1740062819029 
[TRACE] 2025-02-20 22:47:01.172 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] schema data cleaned 
[TRACE] 2025-02-20 22:47:01.172 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] monitor closed 
[TRACE] 2025-02-20 22:47:01.173 - [任务 21][Sybase] - Node Sybase[f1c3551c-0130-41f2-8388-300eeba224cd] close complete, cost 119 ms 
[INFO ] 2025-02-20 22:47:04.765 - [任务 21] - Task [任务 21] cannot retry, reason: Task retry service not start 
[TRACE] 2025-02-20 22:47:04.765 - [任务 21] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-20 22:47:04.765 - [任务 21] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7451f6a4 
[TRACE] 2025-02-20 22:47:04.894 - [任务 21] - Stop task milestones: 67b6ade6f3077a243450ddd1(任务 21)  
[TRACE] 2025-02-20 22:47:04.894 - [任务 21] - Stopped task aspect(s) 
[TRACE] 2025-02-20 22:47:04.894 - [任务 21] - Snapshot order controller have been removed 
[INFO ] 2025-02-20 22:47:04.894 - [任务 21] - Task stopped. 
[TRACE] 2025-02-20 22:47:04.911 - [任务 21] - Remove memory task client succeed, task: 任务 21[67b6ade6f3077a243450ddd1] 
[TRACE] 2025-02-20 22:47:04.915 - [任务 21] - Destroy memory task client cache succeed, task: 任务 21[67b6ade6f3077a243450ddd1] 
