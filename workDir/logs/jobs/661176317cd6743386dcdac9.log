[INFO ] 2024-04-07 00:21:42.255 - [任务 7] - Task initialization... 
[INFO ] 2024-04-07 00:21:42.256 - [任务 7] - Start task milestones: 661176317cd6743386dcdac9(任务 7) 
[INFO ] 2024-04-07 00:21:42.256 - [任务 7] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-04-07 00:21:42.256 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 00:21:42.256 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] start preload schema,table counts: 1 
[INFO ] 2024-04-07 00:21:42.257 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] start preload schema,table counts: 1 
[INFO ] 2024-04-07 00:21:42.261 - [任务 7][KafkaTest2] - Node <PERSON>est2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] preload schema finished, cost 45 ms 
[INFO ] 2024-04-07 00:21:42.261 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] preload schema finished, cost 46 ms 
[INFO ] 2024-04-07 00:21:42.875 - [任务 7][KafkaTest2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 00:21:43.139 - [任务 7][KafkaTest1] - Source node "KafkaTest1" read batch size: 100 
[INFO ] 2024-04-07 00:21:43.140 - [任务 7][KafkaTest1] - Source node "KafkaTest1" event queue capacity: 200 
[INFO ] 2024-04-07 00:21:43.140 - [任务 7][KafkaTest1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 00:21:43.143 - [任务 7][KafkaTest1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-04-07 00:21:43.201 - [任务 7][KafkaTest1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-04-07 00:21:43.201 - [任务 7][KafkaTest1] - Initial sync started 
[INFO ] 2024-04-07 00:21:43.202 - [任务 7][KafkaTest1] - Starting batch read, table name: KafkaTest1, offset: null 
[INFO ] 2024-04-07 00:21:43.202 - [任务 7][KafkaTest1] - Table KafkaTest1 is going to be initial synced 
[INFO ] 2024-04-07 00:24:45.361 - [任务 7][KafkaTest1] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@2dcfc00d: {"newFields":[{"autoInc":false,"dataType":"STRING","nullable":true,"partitionKey":false,"primaryKey":false}],"referenceTime":1712420684081,"tableId":"KafkaTest1","time":1712420684081,"type":209} 
[INFO ] 2024-04-07 00:24:45.361 - [任务 7][KafkaTest1] - Alter table in memory, qualified name: T_kafka_io_tapdata_1_0-SNAPSHOT_KafkaTest1_661147dd05642634b1daa0dd_661176317cd6743386dcdac9 
[INFO ] 2024-04-07 00:24:45.361 - [任务 7][KafkaTest1] - Alter table schema transform finished 
[ERROR] 2024-04-07 00:24:45.361 - [任务 7][KafkaTest2] - Table id: KafkaTest2, field name: null
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@2dcfc00d: {"newFields":[{"autoInc":false,"dataType":"STRING","nullable":true,"partitionKey":false,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1712420684081,"tableId":"KafkaTest2","time":1712420684081,"type":209} <-- Error Message -->
Table id: KafkaTest2, field name: null
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@2dcfc00d: {"newFields":[{"autoInc":false,"dataType":"STRING","nullable":true,"partitionKey":false,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1712420684081,"tableId":"KafkaTest2","time":1712420684081,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: KafkaTest2, field name: null
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@2dcfc00d: {"newFields":[{"autoInc":false,"dataType":"STRING","nullable":true,"partitionKey":false,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1712420684081,"tableId":"KafkaTest2","time":1712420684081,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: KafkaTest2, field name: null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:445)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-04-07 00:24:45.361 - [任务 7][KafkaTest2] - Job suspend in error handle 
[INFO ] 2024-04-07 00:24:46.052 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] running status set to false 
[INFO ] 2024-04-07 00:24:46.053 - [任务 7][KafkaTest1] - PDK connector node stopped: HazelcastSourcePdkDataNode-b3d6a119-c08d-4882-9b40-a5baafe2a45d 
[INFO ] 2024-04-07 00:24:46.053 - [任务 7][KafkaTest1] - PDK connector node released: HazelcastSourcePdkDataNode-b3d6a119-c08d-4882-9b40-a5baafe2a45d 
[INFO ] 2024-04-07 00:24:46.053 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] schema data cleaned 
[INFO ] 2024-04-07 00:24:46.054 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] monitor closed 
[INFO ] 2024-04-07 00:24:46.057 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] close complete, cost 50 ms 
[INFO ] 2024-04-07 00:24:46.057 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] running status set to false 
[INFO ] 2024-04-07 00:24:46.103 - [任务 7][KafkaTest2] - PDK connector node stopped: HazelcastTargetPdkDataNode-02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f 
[INFO ] 2024-04-07 00:24:46.103 - [任务 7][KafkaTest2] - PDK connector node released: HazelcastTargetPdkDataNode-02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f 
[INFO ] 2024-04-07 00:24:46.104 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] schema data cleaned 
[INFO ] 2024-04-07 00:24:46.104 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] monitor closed 
[INFO ] 2024-04-07 00:24:46.109 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] close complete, cost 48 ms 
[INFO ] 2024-04-07 00:24:46.109 - [任务 7][KafkaTest1] - Initial sync completed 
[INFO ] 2024-04-07 00:24:46.109 - [任务 7][KafkaTest1] - Incremental sync starting... 
[INFO ] 2024-04-07 00:24:46.109 - [任务 7][KafkaTest1] - Incremental sync completed 
[INFO ] 2024-04-07 00:24:49.206 - [任务 7] - Task [任务 7] cannot retry, reason: Task retry service not start 
[INFO ] 2024-04-07 00:24:49.217 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 00:24:49.217 - [任务 7] - Stop task milestones: 661176317cd6743386dcdac9(任务 7)  
[INFO ] 2024-04-07 00:24:49.231 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-04-07 00:24:49.231 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 00:24:49.246 - [任务 7] - Remove memory task client succeed, task: 任务 7[661176317cd6743386dcdac9] 
[INFO ] 2024-04-07 00:24:49.250 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[661176317cd6743386dcdac9] 
[INFO ] 2024-04-07 00:32:39.331 - [任务 7] - Task initialization... 
[INFO ] 2024-04-07 00:32:39.386 - [任务 7] - Start task milestones: 661176317cd6743386dcdac9(任务 7) 
[INFO ] 2024-04-07 00:32:39.522 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 00:32:39.523 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 00:32:39.606 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] start preload schema,table counts: 1 
[INFO ] 2024-04-07 00:32:39.607 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] start preload schema,table counts: 1 
[INFO ] 2024-04-07 00:32:39.644 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] preload schema finished, cost 38 ms 
[INFO ] 2024-04-07 00:32:39.645 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] preload schema finished, cost 37 ms 
[INFO ] 2024-04-07 00:32:45.885 - [任务 7][KafkaTest2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 00:32:47.989 - [任务 7][KafkaTest1] - Source node "KafkaTest1" read batch size: 100 
[INFO ] 2024-04-07 00:32:47.993 - [任务 7][KafkaTest1] - Source node "KafkaTest1" event queue capacity: 200 
[INFO ] 2024-04-07 00:32:47.994 - [任务 7][KafkaTest1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 00:32:47.994 - [任务 7][KafkaTest1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-04-07 00:32:48.154 - [任务 7][KafkaTest1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-04-07 00:32:48.158 - [任务 7][KafkaTest1] - Initial sync started 
[INFO ] 2024-04-07 00:32:48.160 - [任务 7][KafkaTest1] - Starting batch read, table name: KafkaTest1, offset: null 
[INFO ] 2024-04-07 00:32:48.363 - [任务 7][KafkaTest1] - Table KafkaTest1 is going to be initial synced 
[INFO ] 2024-04-07 00:34:30.908 - [任务 7][KafkaTest1] - Initial sync completed 
[INFO ] 2024-04-07 00:34:30.909 - [任务 7][KafkaTest1] - Incremental sync starting... 
[INFO ] 2024-04-07 00:34:30.909 - [任务 7][KafkaTest1] - Initial sync completed 
[INFO ] 2024-04-07 00:34:30.909 - [任务 7][KafkaTest1] - Starting stream read, table list: [KafkaTest1], offset: [] 
[INFO ] 2024-04-07 00:37:31.330 - [任务 7] - Stop task milestones: 661176317cd6743386dcdac9(任务 7)  
[INFO ] 2024-04-07 00:37:31.331 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] running status set to false 
[INFO ] 2024-04-07 00:37:31.331 - [任务 7][KafkaTest1] - PDK connector node stopped: HazelcastSourcePdkDataNode-b3d6a119-c08d-4882-9b40-a5baafe2a45d 
[INFO ] 2024-04-07 00:37:31.331 - [任务 7][KafkaTest1] - PDK connector node released: HazelcastSourcePdkDataNode-b3d6a119-c08d-4882-9b40-a5baafe2a45d 
[INFO ] 2024-04-07 00:37:31.331 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] schema data cleaned 
[INFO ] 2024-04-07 00:37:31.331 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] monitor closed 
[INFO ] 2024-04-07 00:37:31.331 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] close complete, cost 45 ms 
[INFO ] 2024-04-07 00:37:31.331 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] running status set to false 
[INFO ] 2024-04-07 00:37:31.331 - [任务 7][KafkaTest2] - PDK connector node stopped: HazelcastTargetPdkDataNode-02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f 
[INFO ] 2024-04-07 00:37:31.331 - [任务 7][KafkaTest2] - PDK connector node released: HazelcastTargetPdkDataNode-02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f 
[INFO ] 2024-04-07 00:37:31.331 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] schema data cleaned 
[INFO ] 2024-04-07 00:37:31.331 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] monitor closed 
[INFO ] 2024-04-07 00:37:31.331 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] close complete, cost 29 ms 
[INFO ] 2024-04-07 00:37:31.331 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 00:37:31.331 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-04-07 00:37:31.331 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 00:37:31.331 - [任务 7] - Remove memory task client succeed, task: 任务 7[661176317cd6743386dcdac9] 
[INFO ] 2024-04-07 00:37:31.331 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[661176317cd6743386dcdac9] 
[INFO ] 2024-04-07 00:40:12.239 - [任务 7] - Task initialization... 
[INFO ] 2024-04-07 00:40:12.281 - [任务 7] - Start task milestones: 661176317cd6743386dcdac9(任务 7) 
[INFO ] 2024-04-07 00:40:12.281 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 00:40:12.353 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 00:40:12.432 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] start preload schema,table counts: 1 
[INFO ] 2024-04-07 00:40:12.433 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] start preload schema,table counts: 1 
[INFO ] 2024-04-07 00:40:12.465 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] preload schema finished, cost 33 ms 
[INFO ] 2024-04-07 00:40:12.466 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] preload schema finished, cost 33 ms 
[INFO ] 2024-04-07 00:40:13.568 - [任务 7][KafkaTest2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 00:40:25.690 - [任务 7][KafkaTest1] - Source node "KafkaTest1" read batch size: 100 
[INFO ] 2024-04-07 00:40:25.691 - [任务 7][KafkaTest1] - Source node "KafkaTest1" event queue capacity: 200 
[INFO ] 2024-04-07 00:40:25.691 - [任务 7][KafkaTest1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 00:40:25.691 - [任务 7][KafkaTest1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-04-07 00:40:25.739 - [任务 7][KafkaTest1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-04-07 00:40:25.739 - [任务 7][KafkaTest1] - Initial sync started 
[INFO ] 2024-04-07 00:40:25.745 - [任务 7][KafkaTest1] - Starting batch read, table name: KafkaTest1, offset: null 
[INFO ] 2024-04-07 00:40:25.745 - [任务 7][KafkaTest1] - Table KafkaTest1 is going to be initial synced 
[INFO ] 2024-04-07 00:40:59.208 - [任务 7][KafkaTest1] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@78970ad3: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"primaryKey":false}],"referenceTime":1712421649274,"tableId":"KafkaTest1","time":1712421648662,"type":209} 
[INFO ] 2024-04-07 00:40:59.214 - [任务 7][KafkaTest1] - Alter table in memory, qualified name: T_kafka_io_tapdata_1_0-SNAPSHOT_KafkaTest1_661147dd05642634b1daa0dd_661176317cd6743386dcdac9 
[INFO ] 2024-04-07 00:41:19.970 - [任务 7][KafkaTest1] - Alter table schema transform finished 
[INFO ] 2024-04-07 00:41:19.970 - [任务 7][KafkaTest1] - Initial sync completed 
[INFO ] 2024-04-07 00:41:19.970 - [任务 7][KafkaTest1] - Incremental sync starting... 
[INFO ] 2024-04-07 00:41:19.970 - [任务 7][KafkaTest1] - Initial sync completed 
[INFO ] 2024-04-07 00:41:19.974 - [任务 7][KafkaTest1] - Starting stream read, table list: [KafkaTest1], offset: [] 
[INFO ] 2024-04-07 00:44:08.217 - [任务 7] - Stop task milestones: 661176317cd6743386dcdac9(任务 7)  
[INFO ] 2024-04-07 00:44:08.464 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] running status set to false 
[INFO ] 2024-04-07 00:44:08.464 - [任务 7][KafkaTest1] - PDK connector node stopped: HazelcastSourcePdkDataNode-b3d6a119-c08d-4882-9b40-a5baafe2a45d 
[INFO ] 2024-04-07 00:44:08.464 - [任务 7][KafkaTest1] - PDK connector node released: HazelcastSourcePdkDataNode-b3d6a119-c08d-4882-9b40-a5baafe2a45d 
[INFO ] 2024-04-07 00:44:08.465 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] schema data cleaned 
[INFO ] 2024-04-07 00:44:08.465 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] monitor closed 
[INFO ] 2024-04-07 00:44:08.469 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] close complete, cost 35 ms 
[INFO ] 2024-04-07 00:44:08.469 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] running status set to false 
[INFO ] 2024-04-07 00:44:08.517 - [任务 7][KafkaTest2] - PDK connector node stopped: HazelcastTargetPdkDataNode-02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f 
[INFO ] 2024-04-07 00:44:08.517 - [任务 7][KafkaTest2] - PDK connector node released: HazelcastTargetPdkDataNode-02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f 
[INFO ] 2024-04-07 00:44:08.517 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] schema data cleaned 
[INFO ] 2024-04-07 00:44:08.517 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] monitor closed 
[INFO ] 2024-04-07 00:44:08.518 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] close complete, cost 51 ms 
[INFO ] 2024-04-07 00:44:13.068 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 00:44:13.076 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-04-07 00:44:13.078 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 00:44:13.093 - [任务 7] - Remove memory task client succeed, task: 任务 7[661176317cd6743386dcdac9] 
[INFO ] 2024-04-07 00:44:13.096 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[661176317cd6743386dcdac9] 
[INFO ] 2024-04-07 00:57:16.326 - [任务 7] - Task initialization... 
[INFO ] 2024-04-07 00:57:16.328 - [任务 7] - Start task milestones: 661176317cd6743386dcdac9(任务 7) 
[INFO ] 2024-04-07 00:57:16.328 - [任务 7] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 00:57:16.329 - [任务 7] - The engine receives 任务 7 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 00:57:16.480 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] start preload schema,table counts: 1 
[INFO ] 2024-04-07 00:57:16.482 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] start preload schema,table counts: 1 
[INFO ] 2024-04-07 00:57:16.590 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] preload schema finished, cost 113 ms 
[INFO ] 2024-04-07 00:57:16.796 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] preload schema finished, cost 111 ms 
[INFO ] 2024-04-07 00:57:17.681 - [任务 7][KafkaTest1] - Source node "KafkaTest1" read batch size: 100 
[INFO ] 2024-04-07 00:57:17.682 - [任务 7][KafkaTest1] - Source node "KafkaTest1" event queue capacity: 200 
[INFO ] 2024-04-07 00:57:17.682 - [任务 7][KafkaTest1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 00:57:17.686 - [任务 7][KafkaTest1] - batch offset found: {},stream offset found: [] 
[INFO ] 2024-04-07 00:57:17.889 - [任务 7][KafkaTest2] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-04-07 00:57:17.944 - [任务 7][KafkaTest1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-04-07 00:57:17.945 - [任务 7][KafkaTest1] - Initial sync started 
[INFO ] 2024-04-07 00:57:17.945 - [任务 7][KafkaTest1] - Starting batch read, table name: KafkaTest1, offset: null 
[INFO ] 2024-04-07 00:57:17.958 - [任务 7][KafkaTest1] - Table KafkaTest1 is going to be initial synced 
[INFO ] 2024-04-07 00:57:26.864 - [任务 7][KafkaTest1] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@26e30868: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name","nullable":true,"partitionKey":false,"primaryKey":false}],"referenceTime":1712422640697,"tableId":"KafkaTest1","time":1712422640697,"type":209} 
[INFO ] 2024-04-07 00:57:26.865 - [任务 7][KafkaTest1] - Alter table in memory, qualified name: T_kafka_io_tapdata_1_0-SNAPSHOT_KafkaTest1_661147dd05642634b1daa0dd_661176317cd6743386dcdac9 
[INFO ] 2024-04-07 00:57:27.253 - [任务 7][KafkaTest1] - Alter table schema transform finished 
[INFO ] 2024-04-07 00:57:27.262 - [任务 7][KafkaTest1] - Initial sync completed 
[INFO ] 2024-04-07 00:57:27.263 - [任务 7][KafkaTest1] - Incremental sync starting... 
[INFO ] 2024-04-07 00:57:27.272 - [任务 7][KafkaTest1] - Initial sync completed 
[INFO ] 2024-04-07 00:57:27.274 - [任务 7][KafkaTest1] - Starting stream read, table list: [KafkaTest1], offset: [] 
[INFO ] 2024-04-07 00:57:51.656 - [任务 7] - Stop task milestones: 661176317cd6743386dcdac9(任务 7)  
[INFO ] 2024-04-07 00:57:51.764 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] running status set to false 
[INFO ] 2024-04-07 00:57:51.765 - [任务 7][KafkaTest1] - PDK connector node stopped: HazelcastSourcePdkDataNode-b3d6a119-c08d-4882-9b40-a5baafe2a45d 
[INFO ] 2024-04-07 00:57:51.765 - [任务 7][KafkaTest1] - PDK connector node released: HazelcastSourcePdkDataNode-b3d6a119-c08d-4882-9b40-a5baafe2a45d 
[INFO ] 2024-04-07 00:57:51.766 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] schema data cleaned 
[INFO ] 2024-04-07 00:57:51.767 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] monitor closed 
[INFO ] 2024-04-07 00:57:51.772 - [任务 7][KafkaTest1] - Node KafkaTest1[b3d6a119-c08d-4882-9b40-a5baafe2a45d] close complete, cost 44 ms 
[ERROR] 2024-04-07 00:57:51.774 - [任务 7][KafkaTest1] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:206)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:128)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:66)
	... 14 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:326)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 18 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:690)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:324)
	... 20 more

[INFO ] 2024-04-07 00:57:51.775 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] running status set to false 
[INFO ] 2024-04-07 00:57:51.827 - [任务 7][KafkaTest2] - PDK connector node stopped: HazelcastTargetPdkDataNode-02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f 
[INFO ] 2024-04-07 00:57:51.827 - [任务 7][KafkaTest2] - PDK connector node released: HazelcastTargetPdkDataNode-02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f 
[INFO ] 2024-04-07 00:57:51.827 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] schema data cleaned 
[INFO ] 2024-04-07 00:57:51.827 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] monitor closed 
[INFO ] 2024-04-07 00:57:51.827 - [任务 7][KafkaTest2] - Node KafkaTest2[02ecf79a-b3ba-4e8b-8e50-4a60c2997a5f] close complete, cost 55 ms 
[INFO ] 2024-04-07 00:57:52.387 - [任务 7] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 00:57:52.387 - [任务 7] - Stopped task aspect(s) 
[INFO ] 2024-04-07 00:57:52.387 - [任务 7] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 00:57:52.474 - [任务 7] - Remove memory task client succeed, task: 任务 7[661176317cd6743386dcdac9] 
[INFO ] 2024-04-07 00:57:52.675 - [任务 7] - Destroy memory task client cache succeed, task: 任务 7[661176317cd6743386dcdac9] 
