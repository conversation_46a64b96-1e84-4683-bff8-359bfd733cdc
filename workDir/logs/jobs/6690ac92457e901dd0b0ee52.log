[INFO ] 2024-07-12 12:09:55.025 - [Heartbeat-SourceMysqlTestHeartBeat] - Start task milestones: 6690ac92457e901dd0b0ee52(Heartbeat-SourceMysqlTestHeartBeat) 
[INFO ] 2024-07-12 12:09:55.125 - [Heartbeat-SourceMysqlTestHeartBeat] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-12 12:09:55.185 - [Heartbeat-SourceMysqlTestHeartBeat] - The engine receives Heartbeat-SourceMysqlTestHeartBeat task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-12 12:09:55.185 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[411a214a-d3fc-4a0d-98da-91836d933fcd] start preload schema,table counts: 1 
[INFO ] 2024-07-12 12:09:55.185 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[fc0689be-8e77-4192-ad4e-b4567b0acfa6] start preload schema,table counts: 1 
[INFO ] 2024-07-12 12:09:55.185 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[411a214a-d3fc-4a0d-98da-91836d933fcd] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 12:09:55.185 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[fc0689be-8e77-4192-ad4e-b4567b0acfa6] preload schema finished, cost 0 ms 
[INFO ] 2024-07-12 12:09:55.968 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-12 12:09:55.968 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-12 12:09:55.968 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-12 12:09:55.969 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1720757395967,"lastTimes":1720757395967,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-12 12:09:56.055 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-12 12:09:56.055 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-12 12:09:56.086 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-12 12:09:56.086 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-12 12:09:56.089 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-12 12:09:56.091 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1720757395967,"lastTimes":1720757395967,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-12 12:09:56.091 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-12 12:09:56.091 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-12 12:09:56.125 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-12 12:09:56.225 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Table "test._tapdata_heartbeat_table" exists, skip auto create table 
[INFO ] 2024-07-12 15:30:55.811 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Table 'test._tapdata_heartbeat_table' doesn't exist
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@578d1d50: {"after":{"id":"66909af97c91bf6e9824898b","ts":"2024-07-12 15:30:53.789000"},"containsIllegalDate":false,"referenceTime":1720769453789,"tableId":"_tapdata_heartbeat_table","time":1720769453789,"type":300}, nodeIds=[fc0689be-8e77-4192-ad4e-b4567b0acfa6], sourceTime=1720769453789, sourceSerialNo=null} 
[ERROR] 2024-07-12 15:30:55.856 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Table 'test._tapdata_heartbeat_table' doesn't exist
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@578d1d50: {"after":{"id":"66909af97c91bf6e9824898b","ts":"2024-07-12 15:30:53.789000"},"containsIllegalDate":false,"referenceTime":1720769453789,"tableId":"_tapdata_heartbeat_table","time":1720769453789,"type":300}, nodeIds=[fc0689be-8e77-4192-ad4e-b4567b0acfa6], sourceTime=1720769453789, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Table 'test._tapdata_heartbeat_table' doesn't exist
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@578d1d50: {"after":{"id":"66909af97c91bf6e9824898b","ts":"2024-07-12 15:30:53.789000"},"containsIllegalDate":false,"referenceTime":1720769453789,"tableId":"_tapdata_heartbeat_table","time":1720769453789,"type":300}, nodeIds=[fc0689be-8e77-4192-ad4e-b4567b0acfa6], sourceTime=1720769453789, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Table 'test._tapdata_heartbeat_table' doesn't exist
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Table 'test._tapdata_heartbeat_table' doesn't exist
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:626)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:595)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:576)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:525)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:488)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:494)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:540)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Table 'test._tapdata_heartbeat_table' doesn't exist
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:798)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:498)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:498)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:681)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:618)
	... 12 more
Caused by: java.sql.SQLSyntaxErrorException: Table 'test._tapdata_heartbeat_table' doesn't exist
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:128)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:435)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:852)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 19 more
Caused by: java.sql.SQLSyntaxErrorException: Table 'test._tapdata_heartbeat_table' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.dml.NormalWriteRecorder.executeBatch(NormalWriteRecorder.java:93)
	at io.tapdata.common.dml.NormalRecordWriter.write(NormalRecordWriter.java:108)
	... 28 more

[INFO ] 2024-07-12 15:30:55.857 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Job suspend in error handle 
[INFO ] 2024-07-12 15:30:56.306 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[fc0689be-8e77-4192-ad4e-b4567b0acfa6] running status set to false 
[INFO ] 2024-07-12 15:30:56.306 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-12 15:30:56.316 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-fc0689be-8e77-4192-ad4e-b4567b0acfa6 
[INFO ] 2024-07-12 15:30:56.316 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-fc0689be-8e77-4192-ad4e-b4567b0acfa6 
[INFO ] 2024-07-12 15:30:56.318 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[fc0689be-8e77-4192-ad4e-b4567b0acfa6] schema data cleaned 
[INFO ] 2024-07-12 15:30:56.318 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[fc0689be-8e77-4192-ad4e-b4567b0acfa6] monitor closed 
[INFO ] 2024-07-12 15:30:56.385 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[fc0689be-8e77-4192-ad4e-b4567b0acfa6] close complete, cost 20 ms 
[INFO ] 2024-07-12 15:30:56.385 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[411a214a-d3fc-4a0d-98da-91836d933fcd] running status set to false 
[INFO ] 2024-07-12 15:30:56.385 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-411a214a-d3fc-4a0d-98da-91836d933fcd 
[INFO ] 2024-07-12 15:30:56.385 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-411a214a-d3fc-4a0d-98da-91836d933fcd 
[INFO ] 2024-07-12 15:30:56.385 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[411a214a-d3fc-4a0d-98da-91836d933fcd] schema data cleaned 
[INFO ] 2024-07-12 15:30:56.386 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[411a214a-d3fc-4a0d-98da-91836d933fcd] monitor closed 
[INFO ] 2024-07-12 15:30:56.386 - [Heartbeat-SourceMysqlTestHeartBeat][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[411a214a-d3fc-4a0d-98da-91836d933fcd] close complete, cost 64 ms 
[INFO ] 2024-07-12 15:31:00.384 - [Heartbeat-SourceMysqlTestHeartBeat] - Task [Heartbeat-SourceMysqlTestHeartBeat] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-12 15:31:00.385 - [Heartbeat-SourceMysqlTestHeartBeat] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-12 15:31:00.385 - [Heartbeat-SourceMysqlTestHeartBeat] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@262d5ffc 
[INFO ] 2024-07-12 15:31:00.386 - [Heartbeat-SourceMysqlTestHeartBeat] - Stop task milestones: 6690ac92457e901dd0b0ee52(Heartbeat-SourceMysqlTestHeartBeat)  
[INFO ] 2024-07-12 15:31:00.505 - [Heartbeat-SourceMysqlTestHeartBeat] - Stopped task aspect(s) 
[INFO ] 2024-07-12 15:31:00.505 - [Heartbeat-SourceMysqlTestHeartBeat] - Snapshot order controller have been removed 
[INFO ] 2024-07-12 15:31:00.533 - [Heartbeat-SourceMysqlTestHeartBeat] - Remove memory task client succeed, task: Heartbeat-SourceMysqlTestHeartBeat[6690ac92457e901dd0b0ee52] 
[INFO ] 2024-07-12 15:31:00.538 - [Heartbeat-SourceMysqlTestHeartBeat] - Destroy memory task client cache succeed, task: Heartbeat-SourceMysqlTestHeartBeat[6690ac92457e901dd0b0ee52] 
