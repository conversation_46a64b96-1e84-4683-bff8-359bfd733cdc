[INFO ] 2024-07-15 11:31:42.135 - [任务 1] - Task initialization... 
[INFO ] 2024-07-15 11:31:42.136 - [任务 1] - Start task milestones: 669497f71df4b966216a48c2(任务 1) 
[INFO ] 2024-07-15 11:31:42.740 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 11:31:42.946 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 11:31:43.310 - [任务 1][POLICY] - Node POLICY[98ac45d6-f2ed-4374-b6a8-204e6c5061b1] start preload schema,table counts: 1 
[INFO ] 2024-07-15 11:31:43.311 - [任务 1][POLICY] - Node POLICY[6d4d8748-d976-427d-9329-59b7673e049e] start preload schema,table counts: 1 
[INFO ] 2024-07-15 11:31:43.311 - [任务 1][POLICY] - Node POLICY[98ac45d6-f2ed-4374-b6a8-204e6c5061b1] preload schema finished, cost 2 ms 
[INFO ] 2024-07-15 11:31:43.312 - [任务 1][POLICY] - Node POLICY[6d4d8748-d976-427d-9329-59b7673e049e] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 11:31:44.590 - [任务 1][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-15 11:31:44.590 - [任务 1][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-15 11:31:44.594 - [任务 1][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 11:31:44.777 - [任务 1][POLICY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 11:31:44.778 - [任务 1][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1721014304,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 11:31:44.913 - [任务 1][POLICY] - Initial sync started 
[INFO ] 2024-07-15 11:31:44.913 - [任务 1][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-15 11:31:44.973 - [任务 1][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-15 11:31:44.973 - [任务 1][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-15 11:31:45.063 - [任务 1][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-15 11:31:45.063 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 11:31:45.076 - [任务 1][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-15 11:31:45.078 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 11:31:45.078 - [任务 1][POLICY] - Starting stream read, table list: [POLICY], offset: {"cdcOffset":1721014304,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 11:31:45.143 - [任务 1][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-15 11:33:14.280 - [任务 1][POLICY] - Node POLICY[6d4d8748-d976-427d-9329-59b7673e049e] running status set to false 
[INFO ] 2024-07-15 11:33:14.311 - [任务 1][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-6d4d8748-d976-427d-9329-59b7673e049e 
[INFO ] 2024-07-15 11:33:14.312 - [任务 1][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-6d4d8748-d976-427d-9329-59b7673e049e 
[INFO ] 2024-07-15 11:33:14.316 - [任务 1][POLICY] - Node POLICY[6d4d8748-d976-427d-9329-59b7673e049e] schema data cleaned 
[INFO ] 2024-07-15 11:33:14.316 - [任务 1][POLICY] - Node POLICY[6d4d8748-d976-427d-9329-59b7673e049e] monitor closed 
[INFO ] 2024-07-15 11:33:14.339 - [任务 1][POLICY] - Node POLICY[6d4d8748-d976-427d-9329-59b7673e049e] close complete, cost 56 ms 
[INFO ] 2024-07-15 11:33:14.339 - [任务 1][POLICY] - Node POLICY[98ac45d6-f2ed-4374-b6a8-204e6c5061b1] running status set to false 
[INFO ] 2024-07-15 11:33:14.384 - [任务 1][POLICY] - PDK connector node stopped: HazelcastTargetPdkDataNode-98ac45d6-f2ed-4374-b6a8-204e6c5061b1 
[INFO ] 2024-07-15 11:33:14.384 - [任务 1][POLICY] - PDK connector node released: HazelcastTargetPdkDataNode-98ac45d6-f2ed-4374-b6a8-204e6c5061b1 
[INFO ] 2024-07-15 11:33:14.384 - [任务 1][POLICY] - Node POLICY[98ac45d6-f2ed-4374-b6a8-204e6c5061b1] schema data cleaned 
[INFO ] 2024-07-15 11:33:14.386 - [任务 1][POLICY] - Node POLICY[98ac45d6-f2ed-4374-b6a8-204e6c5061b1] monitor closed 
[INFO ] 2024-07-15 11:33:14.386 - [任务 1][POLICY] - Node POLICY[98ac45d6-f2ed-4374-b6a8-204e6c5061b1] close complete, cost 48 ms 
[INFO ] 2024-07-15 11:33:14.826 - [任务 1][POLICY] - Incremental sync completed 
[INFO ] 2024-07-15 11:33:18.554 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 11:33:18.560 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@322537ed 
[INFO ] 2024-07-15 11:33:18.677 - [任务 1] - Stop task milestones: 669497f71df4b966216a48c2(任务 1)  
[INFO ] 2024-07-15 11:33:18.704 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-15 11:33:18.705 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 11:33:18.777 - [任务 1] - Remove memory task client succeed, task: 任务 1[669497f71df4b966216a48c2] 
[INFO ] 2024-07-15 11:33:18.778 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[669497f71df4b966216a48c2] 
