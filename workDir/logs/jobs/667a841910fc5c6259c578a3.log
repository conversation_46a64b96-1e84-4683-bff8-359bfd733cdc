[INFO ] 2024-06-25 16:48:25.210 - [任务 32] - Start task milestones: 667a841910fc5c6259c578a3(任务 32) 
[INFO ] 2024-06-25 16:48:25.487 - [任务 32] - Task initialization... 
[INFO ] 2024-06-25 16:48:25.692 - [任务 32] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-06-25 16:48:25.718 - [任务 32] - The engine receives 任务 32 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 16:48:25.762 - [任务 32][SouceMysql] - Node SouceMysql[5615af09-8be0-4216-a5ae-a3e16744851e] start preload schema,table counts: 9 
[INFO ] 2024-06-25 16:48:25.762 - [任务 32][SourceMongo] - Node SourceMongo[81550b23-58dd-4cc5-99ac-4e5726fbc858] start preload schema,table counts: 9 
[INFO ] 2024-06-25 16:48:25.762 - [任务 32][SourceMongo] - Node SourceMongo[81550b23-58dd-4cc5-99ac-4e5726fbc858] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 16:48:25.763 - [任务 32][SouceMysql] - Node SouceMysql[5615af09-8be0-4216-a5ae-a3e16744851e] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 16:48:26.407 - [任务 32][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 16:48:26.421 - [任务 32][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 16:48:26.421 - [任务 32][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-06-25 16:48:26.421 - [任务 32][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-06-25 16:48:26.421 - [任务 32][SouceMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 16:48:26.422 - [任务 32][SouceMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000031","position":1060991226,"gtidSet":""} 
[INFO ] 2024-06-25 16:48:26.560 - [任务 32] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-06-25 16:48:26.564 - [任务 32][SouceMysql] - Initial sync started 
[INFO ] 2024-06-25 16:48:26.564 - [任务 32][SouceMysql] - Starting batch read, table name: Inspectwimtest9, offset: null 
[INFO ] 2024-06-25 16:48:26.564 - [任务 32][SouceMysql] - Table Inspectwimtest9 is going to be initial synced 
[INFO ] 2024-06-25 16:48:26.599 - [任务 32][SouceMysql] - Table [Inspectwimtest9] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:48:26.601 - [任务 32][SouceMysql] - Query table 'Inspectwimtest9' counts: 0 
[INFO ] 2024-06-25 16:48:26.601 - [任务 32][SouceMysql] - Starting batch read, table name: Inspectwimtest3, offset: null 
[INFO ] 2024-06-25 16:48:26.603 - [任务 32][SouceMysql] - Table Inspectwimtest3 is going to be initial synced 
[INFO ] 2024-06-25 16:48:26.634 - [任务 32][SouceMysql] - Table [Inspectwimtest3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:48:26.634 - [任务 32][SouceMysql] - Query table 'Inspectwimtest3' counts: 1 
[INFO ] 2024-06-25 16:48:26.635 - [任务 32][SouceMysql] - Starting batch read, table name: Inspectwimtest4, offset: null 
[INFO ] 2024-06-25 16:48:26.635 - [任务 32][SouceMysql] - Table Inspectwimtest4 is going to be initial synced 
[INFO ] 2024-06-25 16:48:26.843 - [任务 32][SouceMysql] - Query table 'Inspectwimtest4' counts: 1053 
[INFO ] 2024-06-25 16:48:27.010 - [任务 32][SourceMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: com.mongodb.MongoBulkWriteException: Bulk write operation error on server localhost:27017. Write errors: [BulkWriteError{index=0, code=40, message='Updating the path 'SETTLED_DATE.date' would create a conflict at 'SETTLED_DATE'', details={}}]. 
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@6a983542: {"after":{"SETTLED_DATE":"{}","CLAIM_ID":"1","SETTLED_AMOUNT":"{}","CLAIM_REASON":"zH25vvvvvvvvv9gvvvvvvvtvvdxS","POLICY_ID":"WIQZev2RlZ","CLAIM_DATE":"{}","LAST_CHANGE":"{}","_id":"656d73787bcd13be3654bc63","CLAIM_AMOUNT":"{}"},"containsIllegalDate":false,"tableId":"Inspectwimtest3","time":1719305306604,"type":300}, nodeIds=[5615af09-8be0-4216-a5ae-a3e16744851e], sourceTime=1719305306421, sourceSerialNo=null} 
[ERROR] 2024-06-25 16:48:27.024 - [任务 32][SourceMongo] - java.lang.RuntimeException: com.mongodb.MongoBulkWriteException: Bulk write operation error on server localhost:27017. Write errors: [BulkWriteError{index=0, code=40, message='Updating the path 'SETTLED_DATE.date' would create a conflict at 'SETTLED_DATE'', details={}}]. 
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@6a983542: {"after":{"SETTLED_DATE":"{}","CLAIM_ID":"1","SETTLED_AMOUNT":"{}","CLAIM_REASON":"zH25vvvvvvvvv9gvvvvvvvtvvdxS","POLICY_ID":"WIQZev2RlZ","CLAIM_DATE":"{}","LAST_CHANGE":"{}","_id":"656d73787bcd13be3654bc63","CLAIM_AMOUNT":"{}"},"containsIllegalDate":false,"tableId":"Inspectwimtest3","time":1719305306604,"type":300}, nodeIds=[5615af09-8be0-4216-a5ae-a3e16744851e], sourceTime=1719305306421, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: com.mongodb.MongoBulkWriteException: Bulk write operation error on server localhost:27017. Write errors: [BulkWriteError{index=0, code=40, message='Updating the path 'SETTLED_DATE.date' would create a conflict at 'SETTLED_DATE'', details={}}]. 
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@6a983542: {"after":{"SETTLED_DATE":"{}","CLAIM_ID":"1","SETTLED_AMOUNT":"{}","CLAIM_REASON":"zH25vvvvvvvvv9gvvvvvvvtvvdxS","POLICY_ID":"WIQZev2RlZ","CLAIM_DATE":"{}","LAST_CHANGE":"{}","_id":"656d73787bcd13be3654bc63","CLAIM_AMOUNT":"{}"},"containsIllegalDate":false,"tableId":"Inspectwimtest3","time":1719305306604,"type":300}, nodeIds=[5615af09-8be0-4216-a5ae-a3e16744851e], sourceTime=1719305306421, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoBulkWriteException: Bulk write operation error on server localhost:27017. Write errors: [BulkWriteError{index=0, code=40, message='Updating the path 'SETTLED_DATE.date' would create a conflict at 'SETTLED_DATE'', details={}}]. 
	io.tapdata.mongodb.writer.MongodbWriter.handleBulkWriteError(MongodbWriter.java:221)
	io.tapdata.mongodb.writer.MongodbWriter.writeRecord(MongodbWriter.java:120)
	io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1285)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:851)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: com.mongodb.MongoBulkWriteException: Bulk write operation error on server localhost:27017. Write errors: [BulkWriteError{index=0, code=40, message='Updating the path 'SETTLED_DATE.date' would create a conflict at 'SETTLED_DATE'', details={}}]. 
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:625)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:565)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:521)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:480)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:493)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:539)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: com.mongodb.MongoBulkWriteException: Bulk write operation error on server localhost:27017. Write errors: [BulkWriteError{index=0, code=40, message='Updating the path 'SETTLED_DATE.date' would create a conflict at 'SETTLED_DATE'', details={}}]. 
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:797)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:497)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:497)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:680)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:617)
	... 11 more
Caused by: com.mongodb.MongoBulkWriteException: Bulk write operation error on server localhost:27017. Write errors: [BulkWriteError{index=0, code=40, message='Updating the path 'SETTLED_DATE.date' would create a conflict at 'SETTLED_DATE'', details={}}]. 
	at io.tapdata.mongodb.MongodbExceptionCollector.revealException(MongodbExceptionCollector.java:41)
	at io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:851)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:803)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 18 more
Caused by: com.mongodb.MongoBulkWriteException: Bulk write operation error on server localhost:27017. Write errors: [BulkWriteError{index=0, code=40, message='Updating the path 'SETTLED_DATE.date' would create a conflict at 'SETTLED_DATE'', details={}}]. 
	at io.tapdata.mongodb.writer.MongodbWriter.handleBulkWriteError(MongodbWriter.java:221)
	at io.tapdata.mongodb.writer.MongodbWriter.writeRecord(MongodbWriter.java:120)
	at io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1285)
	... 26 more

[INFO ] 2024-06-25 16:48:27.024 - [任务 32][SourceMongo] - Job suspend in error handle 
[INFO ] 2024-06-25 16:48:27.429 - [任务 32] - Task [任务 32] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-06-25 16:48:27.972 - [任务 32][SouceMysql] - Node SouceMysql[5615af09-8be0-4216-a5ae-a3e16744851e] running status set to false 
[INFO ] 2024-06-25 16:48:27.988 - [任务 32][SouceMysql] - Table [Inspectwimtest4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:48:27.988 - [任务 32][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:48:27.988 - [任务 32][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 16:48:27.995 - [任务 32][SouceMysql] - Incremental sync completed 
[INFO ] 2024-06-25 16:48:28.001 - [任务 32][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-5615af09-8be0-4216-a5ae-a3e16744851e 
[INFO ] 2024-06-25 16:48:28.001 - [任务 32][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-5615af09-8be0-4216-a5ae-a3e16744851e 
[INFO ] 2024-06-25 16:48:28.003 - [任务 32][SouceMysql] - Node SouceMysql[5615af09-8be0-4216-a5ae-a3e16744851e] schema data cleaned 
[INFO ] 2024-06-25 16:48:28.003 - [任务 32][SouceMysql] - Node SouceMysql[5615af09-8be0-4216-a5ae-a3e16744851e] monitor closed 
[INFO ] 2024-06-25 16:48:28.006 - [任务 32][SouceMysql] - Node SouceMysql[5615af09-8be0-4216-a5ae-a3e16744851e] close complete, cost 34 ms 
[INFO ] 2024-06-25 16:48:28.006 - [任务 32][SourceMongo] - Node SourceMongo[81550b23-58dd-4cc5-99ac-4e5726fbc858] running status set to false 
[INFO ] 2024-06-25 16:48:28.015 - [任务 32][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-81550b23-58dd-4cc5-99ac-4e5726fbc858 
[INFO ] 2024-06-25 16:48:28.015 - [任务 32][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-81550b23-58dd-4cc5-99ac-4e5726fbc858 
[INFO ] 2024-06-25 16:48:28.015 - [任务 32][SourceMongo] - Node SourceMongo[81550b23-58dd-4cc5-99ac-4e5726fbc858] schema data cleaned 
[INFO ] 2024-06-25 16:48:28.015 - [任务 32][SourceMongo] - Node SourceMongo[81550b23-58dd-4cc5-99ac-4e5726fbc858] monitor closed 
[INFO ] 2024-06-25 16:48:28.015 - [任务 32][SourceMongo] - Node SourceMongo[81550b23-58dd-4cc5-99ac-4e5726fbc858] close complete, cost 9 ms 
[INFO ] 2024-06-25 16:48:32.400 - [任务 32] - Task [任务 32] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-06-25 16:48:32.400 - [任务 32] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 16:48:32.401 - [任务 32] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1c290222 
[INFO ] 2024-06-25 16:48:32.405 - [任务 32] - Stop task milestones: 667a841910fc5c6259c578a3(任务 32)  
[INFO ] 2024-06-25 16:48:32.425 - [任务 32] - Stopped task aspect(s) 
[INFO ] 2024-06-25 16:48:32.425 - [任务 32] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 16:48:32.445 - [任务 32] - Remove memory task client succeed, task: 任务 32[667a841910fc5c6259c578a3] 
[INFO ] 2024-06-25 16:48:32.445 - [任务 32] - Destroy memory task client cache succeed, task: 任务 32[667a841910fc5c6259c578a3] 
[INFO ] 2024-06-25 16:49:57.878 - [任务 32] - Start task milestones: 667a841910fc5c6259c578a3(任务 32) 
[INFO ] 2024-06-25 16:49:58.051 - [任务 32] - Task initialization... 
[INFO ] 2024-06-25 16:49:58.051 - [任务 32] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-25 16:49:58.085 - [任务 32] - The engine receives 任务 32 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 16:49:58.127 - [任务 32][SouceMysql] - Node SouceMysql[5615af09-8be0-4216-a5ae-a3e16744851e] start preload schema,table counts: 9 
[INFO ] 2024-06-25 16:49:58.127 - [任务 32][SourceMongo] - Node SourceMongo[81550b23-58dd-4cc5-99ac-4e5726fbc858] start preload schema,table counts: 9 
[INFO ] 2024-06-25 16:49:58.127 - [任务 32][SouceMysql] - Node SouceMysql[5615af09-8be0-4216-a5ae-a3e16744851e] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 16:49:58.127 - [任务 32][SourceMongo] - Node SourceMongo[81550b23-58dd-4cc5-99ac-4e5726fbc858] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 16:49:58.694 - [任务 32][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 16:49:58.694 - [任务 32][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 16:49:58.876 - [任务 32][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-06-25 16:49:58.876 - [任务 32][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-06-25 16:49:58.877 - [任务 32][SouceMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 16:49:58.877 - [任务 32][SouceMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000031","position":1060991226,"gtidSet":""} 
[INFO ] 2024-06-25 16:49:58.924 - [任务 32] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-06-25 16:49:58.928 - [任务 32][SouceMysql] - Initial sync started 
[INFO ] 2024-06-25 16:49:58.928 - [任务 32][SouceMysql] - Starting batch read, table name: Inspectwimtest9, offset: null 
[INFO ] 2024-06-25 16:49:58.928 - [任务 32][SouceMysql] - Table Inspectwimtest9 is going to be initial synced 
[INFO ] 2024-06-25 16:49:58.934 - [任务 32][SouceMysql] - Table [Inspectwimtest9] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:49:58.934 - [任务 32][SouceMysql] - Query table 'Inspectwimtest9' counts: 0 
[INFO ] 2024-06-25 16:49:58.934 - [任务 32][SouceMysql] - Starting batch read, table name: Inspectwimtest3, offset: null 
[INFO ] 2024-06-25 16:49:58.934 - [任务 32][SouceMysql] - Table Inspectwimtest3 is going to be initial synced 
[INFO ] 2024-06-25 16:49:58.946 - [任务 32][SouceMysql] - Query table 'Inspectwimtest3' counts: 1 
[INFO ] 2024-06-25 16:49:58.946 - [任务 32][SouceMysql] - Table [Inspectwimtest3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:49:58.946 - [任务 32][SouceMysql] - Starting batch read, table name: Inspectwimtest4, offset: null 
[INFO ] 2024-06-25 16:49:58.946 - [任务 32][SouceMysql] - Table Inspectwimtest4 is going to be initial synced 
[INFO ] 2024-06-25 16:49:59.165 - [任务 32][SouceMysql] - Query table 'Inspectwimtest4' counts: 1053 
[INFO ] 2024-06-25 16:49:59.642 - [任务 32][SouceMysql] - Table [Inspectwimtest4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:49:59.645 - [任务 32][SouceMysql] - Starting batch read, table name: Inspectwimtest1, offset: null 
[INFO ] 2024-06-25 16:49:59.645 - [任务 32][SouceMysql] - Table Inspectwimtest1 is going to be initial synced 
[INFO ] 2024-06-25 16:49:59.762 - [任务 32][SouceMysql] - Query table 'Inspectwimtest1' counts: 1053 
[INFO ] 2024-06-25 16:49:59.765 - [任务 32][SouceMysql] - Table [Inspectwimtest1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:49:59.765 - [任务 32][SouceMysql] - Starting batch read, table name: Inspectwimtest2, offset: null 
[INFO ] 2024-06-25 16:49:59.765 - [任务 32][SouceMysql] - Table Inspectwimtest2 is going to be initial synced 
[INFO ] 2024-06-25 16:49:59.865 - [任务 32][SouceMysql] - Query table 'Inspectwimtest2' counts: 1075 
[INFO ] 2024-06-25 16:49:59.866 - [任务 32][SouceMysql] - Table [Inspectwimtest2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:49:59.866 - [任务 32][SouceMysql] - Starting batch read, table name: Inspectwimtest7, offset: null 
[INFO ] 2024-06-25 16:49:59.866 - [任务 32][SouceMysql] - Table Inspectwimtest7 is going to be initial synced 
[INFO ] 2024-06-25 16:49:59.866 - [任务 32][SouceMysql] - Table [Inspectwimtest7] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:49:59.866 - [任务 32][SouceMysql] - Query table 'Inspectwimtest7' counts: 0 
[INFO ] 2024-06-25 16:49:59.866 - [任务 32][SouceMysql] - Starting batch read, table name: Inspectwimtest8, offset: null 
[INFO ] 2024-06-25 16:49:59.866 - [任务 32][SouceMysql] - Table Inspectwimtest8 is going to be initial synced 
[INFO ] 2024-06-25 16:49:59.867 - [任务 32][SouceMysql] - Table [Inspectwimtest8] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:49:59.867 - [任务 32][SouceMysql] - Query table 'Inspectwimtest8' counts: 0 
[INFO ] 2024-06-25 16:49:59.868 - [任务 32][SouceMysql] - Starting batch read, table name: Inspectwimtest5, offset: null 
[INFO ] 2024-06-25 16:49:59.868 - [任务 32][SouceMysql] - Table Inspectwimtest5 is going to be initial synced 
[INFO ] 2024-06-25 16:49:59.869 - [任务 32][SouceMysql] - Table [Inspectwimtest5] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:49:59.869 - [任务 32][SouceMysql] - Query table 'Inspectwimtest5' counts: 1 
[INFO ] 2024-06-25 16:49:59.869 - [任务 32][SouceMysql] - Starting batch read, table name: Inspectwimtest6, offset: null 
[INFO ] 2024-06-25 16:49:59.869 - [任务 32][SouceMysql] - Table Inspectwimtest6 is going to be initial synced 
[INFO ] 2024-06-25 16:49:59.870 - [任务 32][SouceMysql] - Query table 'Inspectwimtest6' counts: 0 
[INFO ] 2024-06-25 16:49:59.873 - [任务 32][SouceMysql] - Table [Inspectwimtest6] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:49:59.873 - [任务 32][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:49:59.873 - [任务 32][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 16:49:59.873 - [任务 32][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:49:59.873 - [任务 32][SouceMysql] - Starting stream read, table list: [Inspectwimtest9, Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest7, Inspectwimtest8, Inspectwimtest5, Inspectwimtest6], offset: {"filename":"binlog.000031","position":1060991226,"gtidSet":""} 
[INFO ] 2024-06-25 16:49:59.914 - [任务 32][SouceMysql] - Starting mysql cdc, server name: af95bf53-6b4b-4556-b1ea-4991ebdd22d2 
[INFO ] 2024-06-25 16:49:59.914 - [任务 32][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1168663429
  time.precision.mode: adaptive_time_microseconds
  database.server.name: af95bf53-6b4b-4556-b1ea-4991ebdd22d2
  database.port: 3306
  threadName: Debezium-Mysql-Connector-af95bf53-6b4b-4556-b1ea-4991ebdd22d2
  database.hostname: localhost
  database.password: ********
  name: af95bf53-6b4b-4556-b1ea-4991ebdd22d2
  pdk.offset.string: {"name":"af95bf53-6b4b-4556-b1ea-4991ebdd22d2","offset":{"{\"server\":\"af95bf53-6b4b-4556-b1ea-4991ebdd22d2\"}":"{\"file\":\"binlog.000031\",\"pos\":1060991226,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Inspectwimtest9,test.Inspectwimtest3,test.Inspectwimtest4,test.Inspectwimtest1,test.Inspectwimtest2,test.Inspectwimtest7,test.Inspectwimtest8,test.Inspectwimtest5,test.Inspectwimtest6
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 16:50:00.115 - [任务 32][SouceMysql] - Connector Mysql incremental start succeed, tables: [Inspectwimtest9, Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest7, Inspectwimtest8, Inspectwimtest5, Inspectwimtest6], data change syncing 
[INFO ] 2024-06-25 16:51:20.823 - [任务 32][SouceMysql] - Found new table(s): [Inspectwimtest10] 
[INFO ] 2024-06-25 16:51:20.967 - [任务 32][SouceMysql] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-06-25 16:51:21.000 - [任务 32][SouceMysql] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@33f773ee: {"table":{"comment":"","id":"Inspectwimtest10","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"}],"name":"PRIMARY","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"Inspectwimtest10","nameFieldMap":{"id":{"autoInc":false,"comment":"","dataType":"int","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"comment":"","dataType":"varchar(20)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"}},"unique":true}},"tableId":"Inspectwimtest10","type":206} 
[INFO ] 2024-06-25 16:51:21.002 - [任务 32][SouceMysql] - Create new table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_Inspectwimtest10_667413fd7b5e1f6c3b139e78_667a841910fc5c6259c578a3 
[INFO ] 2024-06-25 16:51:33.873 - [任务 32][SouceMysql] - Create new table schema transform finished: TapTable id Inspectwimtest10 name Inspectwimtest10 storageEngine null charset null number of fields 2 
[INFO ] 2024-06-25 16:51:33.986 - [任务 32][SouceMysql] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-06-25 16:51:33.987 - [任务 32][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-06-25 16:51:33.987 - [任务 32][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-06-25 16:51:33.988 - [任务 32][SouceMysql] - Incremental sync completed 
[INFO ] 2024-06-25 16:51:34.250 - [任务 32][SouceMysql] - Starting batch read, table name: Inspectwimtest10, offset: null 
[INFO ] 2024-06-25 16:51:34.265 - [任务 32][SouceMysql] - Table Inspectwimtest10 is going to be initial synced 
[INFO ] 2024-06-25 16:51:34.293 - [任务 32][SouceMysql] - Table [Inspectwimtest10] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:51:34.294 - [任务 32][SouceMysql] - Query table 'Inspectwimtest10' counts: 0 
[INFO ] 2024-06-25 16:51:34.297 - [任务 32][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:51:34.297 - [任务 32][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 16:51:34.298 - [任务 32][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:51:34.298 - [任务 32][SouceMysql] - Starting stream read, table list: [Inspectwimtest9, Inspectwimtest10, Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest7, Inspectwimtest8, Inspectwimtest5, Inspectwimtest6], offset: {"name":"af95bf53-6b4b-4556-b1ea-4991ebdd22d2","offset":{"{\"server\":\"af95bf53-6b4b-4556-b1ea-4991ebdd22d2\"}":"{\"ts_sec\":1719305400,\"file\":\"binlog.000031\",\"pos\":1060991507,\"server_id\":1}"}} 
[INFO ] 2024-06-25 16:51:34.319 - [任务 32][SouceMysql] - Starting mysql cdc, server name: af95bf53-6b4b-4556-b1ea-4991ebdd22d2 
[INFO ] 2024-06-25 16:51:34.319 - [任务 32][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1717497258
  time.precision.mode: adaptive_time_microseconds
  database.server.name: af95bf53-6b4b-4556-b1ea-4991ebdd22d2
  database.port: 3306
  threadName: Debezium-Mysql-Connector-af95bf53-6b4b-4556-b1ea-4991ebdd22d2
  database.hostname: localhost
  database.password: ********
  name: af95bf53-6b4b-4556-b1ea-4991ebdd22d2
  pdk.offset.string: {"name":"af95bf53-6b4b-4556-b1ea-4991ebdd22d2","offset":{"{\"server\":\"af95bf53-6b4b-4556-b1ea-4991ebdd22d2\"}":"{\"ts_sec\":1719305400,\"file\":\"binlog.000031\",\"pos\":1060991507,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Inspectwimtest9,test.Inspectwimtest10,test.Inspectwimtest3,test.Inspectwimtest4,test.Inspectwimtest1,test.Inspectwimtest2,test.Inspectwimtest7,test.Inspectwimtest8,test.Inspectwimtest5,test.Inspectwimtest6
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 16:51:34.523 - [任务 32][SouceMysql] - Connector Mysql incremental start succeed, tables: [Inspectwimtest9, Inspectwimtest10, Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest7, Inspectwimtest8, Inspectwimtest5, Inspectwimtest6], data change syncing 
[INFO ] 2024-06-25 16:58:42.675 - [任务 32][SouceMysql] - Node SouceMysql[5615af09-8be0-4216-a5ae-a3e16744851e] running status set to false 
[INFO ] 2024-06-25 16:58:42.759 - [任务 32][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[WARN ] 2024-06-25 16:58:42.771 - [任务 32][SouceMysql] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-5615af09-8be0-4216-a5ae-a3e16744851e 
