[INFO ] 2024-07-26 12:15:25.271 - [任务 33] - Task initialization... 
[INFO ] 2024-07-26 12:15:25.276 - [任务 33] - Start task milestones: 66a322ab8191787d1e5aea48(任务 33) 
[INFO ] 2024-07-26 12:15:25.418 - [任务 33] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 12:15:25.464 - [任务 33] - The engine receives 任务 33 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 12:15:25.635 - [任务 33][HreatBeatTest1] - Node HreatBeatTest1[93686409-08be-4a98-963a-c51fcbced058] start preload schema,table counts: 1 
[INFO ] 2024-07-26 12:15:25.637 - [任务 33][CLAIM] - Node CLAIM[566652b7-965a-433c-9479-d2ffed5e247c] start preload schema,table counts: 1 
[INFO ] 2024-07-26 12:15:25.637 - [任务 33][CLAIM] - Node CLAIM[566652b7-965a-433c-9479-d2ffed5e247c] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 12:15:25.637 - [任务 33][HreatBeatTest1] - Node HreatBeatTest1[93686409-08be-4a98-963a-c51fcbced058] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 12:15:26.971 - [任务 33][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-07-26 12:15:26.974 - [任务 33][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-07-26 12:15:26.975 - [任务 33][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 12:15:26.986 - [任务 33][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":214548423,"gtidSet":""} 
[INFO ] 2024-07-26 12:15:26.987 - [任务 33][CLAIM] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-26 12:15:27.108 - [任务 33][CLAIM] - Initial sync started 
[INFO ] 2024-07-26 12:15:27.108 - [任务 33][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-07-26 12:15:27.108 - [任务 33][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-07-26 12:15:27.173 - [任务 33][CLAIM] - Query table 'CLAIM' counts: 1129 
[INFO ] 2024-07-26 12:15:27.398 - [任务 33][HreatBeatTest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-26 12:15:27.652 - [任务 33][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 12:15:27.654 - [任务 33][CLAIM] - Initial sync completed 
[INFO ] 2024-07-26 12:15:27.654 - [任务 33][CLAIM] - Incremental sync starting... 
[INFO ] 2024-07-26 12:15:27.654 - [任务 33][CLAIM] - Initial sync completed 
[INFO ] 2024-07-26 12:15:27.658 - [任务 33][CLAIM] - Starting stream read, table list: [CLAIM, _tapdata_heartbeat_table], offset: {"filename":"binlog.000020","position":214548423,"gtidSet":""} 
[INFO ] 2024-07-26 12:15:27.698 - [任务 33][CLAIM] - Starting mysql cdc, server name: cc37e029-1f03-44f0-8fbe-3cffd4e26444 
[INFO ] 2024-07-26 12:15:27.698 - [任务 33][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 830690530
  time.precision.mode: adaptive_time_microseconds
  database.server.name: cc37e029-1f03-44f0-8fbe-3cffd4e26444
  database.port: 3307
  threadName: Debezium-Mysql-Connector-cc37e029-1f03-44f0-8fbe-3cffd4e26444
  database.hostname: localhost
  database.password: ********
  name: cc37e029-1f03-44f0-8fbe-3cffd4e26444
  pdk.offset.string: {"name":"cc37e029-1f03-44f0-8fbe-3cffd4e26444","offset":{"{\"server\":\"cc37e029-1f03-44f0-8fbe-3cffd4e26444\"}":"{\"file\":\"binlog.000020\",\"pos\":214548423,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM,test._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-26 12:15:27.904 - [任务 33][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 12:15:41.264 - [任务 33][CLAIM] - Node CLAIM[566652b7-965a-433c-9479-d2ffed5e247c] running status set to false 
[INFO ] 2024-07-26 12:15:41.368 - [任务 33][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-26 12:15:41.369 - [任务 33][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-07-26 12:15:41.371 - [任务 33][CLAIM] - Incremental sync completed 
[INFO ] 2024-07-26 12:15:41.371 - [任务 33][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-566652b7-965a-433c-9479-d2ffed5e247c 
[INFO ] 2024-07-26 12:15:41.372 - [任务 33][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-566652b7-965a-433c-9479-d2ffed5e247c 
[INFO ] 2024-07-26 12:15:41.373 - [任务 33][CLAIM] - Node CLAIM[566652b7-965a-433c-9479-d2ffed5e247c] schema data cleaned 
[INFO ] 2024-07-26 12:15:41.373 - [任务 33][CLAIM] - Node CLAIM[566652b7-965a-433c-9479-d2ffed5e247c] monitor closed 
[INFO ] 2024-07-26 12:15:41.383 - [任务 33][CLAIM] - Node CLAIM[566652b7-965a-433c-9479-d2ffed5e247c] close complete, cost 120 ms 
[INFO ] 2024-07-26 12:15:41.383 - [任务 33][HreatBeatTest1] - Node HreatBeatTest1[93686409-08be-4a98-963a-c51fcbced058] running status set to false 
[INFO ] 2024-07-26 12:15:41.414 - [任务 33][HreatBeatTest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-93686409-08be-4a98-963a-c51fcbced058 
[INFO ] 2024-07-26 12:15:41.414 - [任务 33][HreatBeatTest1] - PDK connector node released: HazelcastTargetPdkDataNode-93686409-08be-4a98-963a-c51fcbced058 
[INFO ] 2024-07-26 12:15:41.415 - [任务 33][HreatBeatTest1] - Node HreatBeatTest1[93686409-08be-4a98-963a-c51fcbced058] schema data cleaned 
[INFO ] 2024-07-26 12:15:41.415 - [任务 33][HreatBeatTest1] - Node HreatBeatTest1[93686409-08be-4a98-963a-c51fcbced058] monitor closed 
[INFO ] 2024-07-26 12:15:41.618 - [任务 33][HreatBeatTest1] - Node HreatBeatTest1[93686409-08be-4a98-963a-c51fcbced058] close complete, cost 35 ms 
[INFO ] 2024-07-26 12:15:42.904 - [任务 33] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 12:15:42.904 - [任务 33] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@42f9d791 
[INFO ] 2024-07-26 12:15:42.909 - [任务 33] - Stop task milestones: 66a322ab8191787d1e5aea48(任务 33)  
[INFO ] 2024-07-26 12:15:43.051 - [任务 33] - Stopped task aspect(s) 
[INFO ] 2024-07-26 12:15:43.051 - [任务 33] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 12:15:43.118 - [任务 33] - Remove memory task client succeed, task: 任务 33[66a322ab8191787d1e5aea48] 
[INFO ] 2024-07-26 12:15:43.118 - [任务 33] - Destroy memory task client cache succeed, task: 任务 33[66a322ab8191787d1e5aea48] 
