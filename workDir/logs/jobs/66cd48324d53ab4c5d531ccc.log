[INFO ] 2024-08-27 11:30:53.743 - [任务 1] - Start task milestones: 66cd48324d53ab4c5d531ccc(任务 1) 
[INFO ] 2024-08-27 11:30:53.745 - [任务 1] - Task initialization... 
[INFO ] 2024-08-27 11:30:54.319 - [任务 1] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-08-27 11:30:54.520 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-27 11:30:54.833 - [任务 1][Mongo] - Node <PERSON>go[5b153893-39db-4c4d-b418-85aa7d7d0a3c] start preload schema,table counts: 3 
[INFO ] 2024-08-27 11:30:54.836 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] start preload schema,table counts: 3 
[INFO ] 2024-08-27 11:30:54.839 - [任务 1][<PERSON><PERSON>] - <PERSON><PERSON>[5b153893-39db-4c4d-b418-85aa7d7d0a3c] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 11:30:54.840 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 11:30:55.603 - [任务 1][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-27 11:30:55.608 - [任务 1][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-27 11:30:55.799 - [任务 1][DM] - Source node "DM" read batch size: 100 
[INFO ] 2024-08-27 11:30:55.802 - [任务 1][DM] - Source node "DM" event queue capacity: 200 
[INFO ] 2024-08-27 11:30:55.802 - [任务 1][DM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-27 11:30:55.827 - [任务 1][DM] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53794157,"pendingScn":53794157,"timestamp":null} 
[INFO ] 2024-08-27 11:30:55.828 - [任务 1] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-27 11:30:55.902 - [任务 1][DM] - Initial sync started 
[INFO ] 2024-08-27 11:30:55.907 - [任务 1][DM] - Starting batch read, table name: bmsql_history_test 
[INFO ] 2024-08-27 11:30:55.914 - [任务 1][DM] - Table bmsql_history_test is going to be initial synced 
[INFO ] 2024-08-27 11:30:56.117 - [任务 1][DM] - Query table 'bmsql_history_test' counts: 11262 
[INFO ] 2024-08-27 11:30:57.123 - [任务 1][DM] - Table [bmsql_history_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-27 11:30:57.145 - [任务 1][DM] - Starting batch read, table name: bmsql_history 
[INFO ] 2024-08-27 11:30:57.172 - [任务 1][DM] - Table bmsql_history is going to be initial synced 
[INFO ] 2024-08-27 11:30:57.178 - [任务 1][DM] - Query table 'bmsql_history' counts: 133420 
[INFO ] 2024-08-27 11:31:06.394 - [任务 1][DM] - Table [bmsql_history] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-27 11:31:06.399 - [任务 1][DM] - Starting batch read, table name: bmsql_history_wtest 
[INFO ] 2024-08-27 11:31:06.399 - [任务 1][DM] - Table bmsql_history_wtest is going to be initial synced 
[INFO ] 2024-08-27 11:31:06.415 - [任务 1][DM] - Table [bmsql_history_wtest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-27 11:31:06.426 - [任务 1][DM] - Query table 'bmsql_history_wtest' counts: 0 
[INFO ] 2024-08-27 11:31:06.435 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 11:31:06.436 - [任务 1][DM] - Incremental sync starting... 
[INFO ] 2024-08-27 11:31:06.436 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 11:31:06.437 - [任务 1][DM] - Starting stream read, table list: [bmsql_history_test, bmsql_history, bmsql_history_wtest], offset: {"sortString":null,"offsetValue":null,"lastScn":53794157,"pendingScn":53794157,"timestamp":null} 
[INFO ] 2024-08-27 11:31:06.638 - [任务 1][DM] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-08-27_10-42-00.log 
[INFO ] 2024-08-27 12:15:46.016 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] running status set to false 
[INFO ] 2024-08-27 12:15:46.016 - [任务 1][DM] - Log Miner is shutting down... 
[WARN ] 2024-08-27 12:15:46.032 - [任务 1][DM] - Stop PDK connector node failed: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  | Associate id: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:15:46.033 - [任务 1][DM] - PDK connector node released: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:15:46.034 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] schema data cleaned 
[INFO ] 2024-08-27 12:15:46.045 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] monitor closed 
[INFO ] 2024-08-27 12:15:46.045 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] close complete, cost 41 ms 
[INFO ] 2024-08-27 12:15:46.045 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] running status set to false 
[INFO ] 2024-08-27 12:15:46.070 - [任务 1][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:15:46.072 - [任务 1][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:15:46.072 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] schema data cleaned 
[INFO ] 2024-08-27 12:15:46.074 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] monitor closed 
[INFO ] 2024-08-27 12:15:46.276 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] close complete, cost 29 ms 
[INFO ] 2024-08-27 12:15:48.043 - [任务 1][DM] - 【single miner】add log miner sql: null 
[INFO ] 2024-08-27 12:15:48.111 - [任务 1][DM] - Incremental sync completed 
[INFO ] 2024-08-27 12:15:48.112 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19  
[ERROR] 2024-08-27 12:15:48.313 - [任务 1][DM] - java.lang.RuntimeException: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19  <-- Error Message -->
java.lang.RuntimeException: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19 

<-- Simple Stack Trace -->
Caused by: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19 
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:790)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19 
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:90)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.startCdcRunner(DamengCdcRunner.java:38)
	at io.tapdata.connector.dameng.DamengConnector.streamRead(DamengConnector.java:413)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:769)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 18 more
Caused by: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19 
	at dm.jdbc.driver.DBError.throwException(DBError.java:696)
	at dm.jdbc.b.b.o.E(MSG.java:560)
	at dm.jdbc.b.b.o.B(MSG.java:520)
	at dm.jdbc.b.b.o.A(MSG.java:501)
	at dm.jdbc.b.a.a(DBAccess.java:230)
	at dm.jdbc.b.a.a(DBAccess.java:701)
	at dm.jdbc.driver.DmdbStatement.executeInner(DmdbStatement.java:723)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:189)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:181)
	at dm.jdbc.driver.DmdbStatement.execute(DmdbStatement.java:1230)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.startMiner(SingleDamengLogMiner.java:65)
	... 22 more

[INFO ] 2024-08-27 12:15:50.077 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-27 12:15:50.093 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7de13230 
[INFO ] 2024-08-27 12:15:50.093 - [任务 1] - Stop task milestones: 66cd48324d53ab4c5d531ccc(任务 1)  
[INFO ] 2024-08-27 12:15:50.235 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-27 12:15:50.235 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-27 12:15:50.348 - [任务 1] - Remove memory task client succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:15:50.349 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:15:54.706 - [任务 1] - Start task milestones: 66cd48324d53ab4c5d531ccc(任务 1) 
[INFO ] 2024-08-27 12:15:54.706 - [任务 1] - Task initialization... 
[INFO ] 2024-08-27 12:15:54.912 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-27 12:15:55.029 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-27 12:15:55.029 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] start preload schema,table counts: 3 
[INFO ] 2024-08-27 12:15:55.029 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] start preload schema,table counts: 3 
[INFO ] 2024-08-27 12:15:55.030 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:15:55.235 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:15:55.770 - [任务 1][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-27 12:15:55.775 - [任务 1][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-27 12:15:55.956 - [任务 1][DM] - Source node "DM" read batch size: 100 
[INFO ] 2024-08-27 12:15:55.956 - [任务 1][DM] - Source node "DM" event queue capacity: 200 
[INFO ] 2024-08-27 12:15:55.956 - [任务 1][DM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-27 12:16:10.391 - [任务 1][DM] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53795157,"pendingScn":53795157,"timestamp":null} 
[INFO ] 2024-08-27 12:16:10.448 - [任务 1] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-27 12:16:10.588 - [任务 1][DM] - Initial sync started 
[INFO ] 2024-08-27 12:16:10.596 - [任务 1][DM] - Starting batch read, table name: bmsql_history_test 
[INFO ] 2024-08-27 12:16:10.596 - [任务 1][DM] - Table bmsql_history_test is going to be initial synced 
[INFO ] 2024-08-27 12:16:10.695 - [任务 1][DM] - Query table 'bmsql_history_test' counts: 11262 
[INFO ] 2024-08-27 12:16:11.317 - [任务 1][DM] - Table [bmsql_history_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-27 12:16:11.328 - [任务 1][DM] - Starting batch read, table name: bmsql_history 
[INFO ] 2024-08-27 12:16:11.329 - [任务 1][DM] - Table bmsql_history is going to be initial synced 
[INFO ] 2024-08-27 12:16:11.340 - [任务 1][DM] - Query table 'bmsql_history' counts: 133420 
[INFO ] 2024-08-27 12:16:18.286 - [任务 1][DM] - Table [bmsql_history] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-27 12:16:18.294 - [任务 1][DM] - Starting batch read, table name: bmsql_history_wtest 
[INFO ] 2024-08-27 12:16:18.295 - [任务 1][DM] - Table bmsql_history_wtest is going to be initial synced 
[INFO ] 2024-08-27 12:16:18.323 - [任务 1][DM] - Query table 'bmsql_history_wtest' counts: 0 
[INFO ] 2024-08-27 12:16:18.324 - [任务 1][DM] - Table [bmsql_history_wtest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-27 12:16:18.325 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 12:16:18.328 - [任务 1][DM] - Incremental sync starting... 
[INFO ] 2024-08-27 12:16:18.328 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 12:16:18.328 - [任务 1][DM] - Starting stream read, table list: [bmsql_history_test, bmsql_history, bmsql_history_wtest], offset: {"sortString":null,"offsetValue":null,"lastScn":53795157,"pendingScn":53795157,"timestamp":null} 
[INFO ] 2024-08-27 12:16:18.531 - [任务 1][DM] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-08-27_10-42-00.log 
[INFO ] 2024-08-27 12:19:08.845 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] running status set to false 
[INFO ] 2024-08-27 12:19:08.855 - [任务 1][DM] - Log Miner is shutting down... 
[WARN ] 2024-08-27 12:19:08.856 - [任务 1][DM] - Stop PDK connector node failed: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  | Associate id: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:19:08.861 - [任务 1][DM] - PDK connector node released: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:19:08.863 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] schema data cleaned 
[INFO ] 2024-08-27 12:19:08.865 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] monitor closed 
[INFO ] 2024-08-27 12:19:08.868 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] close complete, cost 37 ms 
[INFO ] 2024-08-27 12:19:08.868 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] running status set to false 
[INFO ] 2024-08-27 12:19:08.907 - [任务 1][DM] - Incremental sync completed 
[INFO ] 2024-08-27 12:19:08.908 - [任务 1][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:19:08.908 - [任务 1][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:19:08.910 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] schema data cleaned 
[INFO ] 2024-08-27 12:19:08.911 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] monitor closed 
[INFO ] 2024-08-27 12:19:08.914 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] close complete, cost 45 ms 
[INFO ] 2024-08-27 12:19:10.545 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-27 12:19:10.562 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5bd9e0b4 
[INFO ] 2024-08-27 12:19:10.563 - [任务 1] - Stop task milestones: 66cd48324d53ab4c5d531ccc(任务 1)  
[INFO ] 2024-08-27 12:19:10.686 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-27 12:19:10.686 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-27 12:19:10.716 - [任务 1] - Remove memory task client succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:19:10.716 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:21:07.941 - [任务 1] - Start task milestones: 66cd48324d53ab4c5d531ccc(任务 1) 
[INFO ] 2024-08-27 12:21:07.941 - [任务 1] - Task initialization... 
[INFO ] 2024-08-27 12:21:08.121 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-27 12:21:08.208 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-27 12:21:08.208 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:21:08.209 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:21:08.209 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:21:08.209 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:21:08.902 - [任务 1][DM] - Source node "DM" read batch size: 100 
[INFO ] 2024-08-27 12:21:08.903 - [任务 1][DM] - Source node "DM" event queue capacity: 200 
[INFO ] 2024-08-27 12:21:08.903 - [任务 1][DM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-27 12:21:08.936 - [任务 1][DM] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53795567,"pendingScn":53795567,"timestamp":null} 
[INFO ] 2024-08-27 12:21:09.004 - [任务 1] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-27 12:21:09.005 - [任务 1][DM] - Initial sync started 
[INFO ] 2024-08-27 12:21:09.005 - [任务 1][DM] - Starting batch read, table name: BMSQL_HISTORY_TEST1 
[INFO ] 2024-08-27 12:21:09.068 - [任务 1][DM] - Table BMSQL_HISTORY_TEST1 is going to be initial synced 
[INFO ] 2024-08-27 12:21:09.068 - [任务 1][DM] - Table [BMSQL_HISTORY_TEST1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-27 12:21:09.068 - [任务 1][DM] - Query table 'BMSQL_HISTORY_TEST1' counts: 1 
[INFO ] 2024-08-27 12:21:09.070 - [任务 1][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-27 12:21:09.071 - [任务 1][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-27 12:21:09.071 - [任务 1][DM] - Starting batch read, table name: BMSQL_HISTORY 
[INFO ] 2024-08-27 12:21:09.152 - [任务 1][DM] - Table BMSQL_HISTORY is going to be initial synced 
[INFO ] 2024-08-27 12:21:09.152 - [任务 1][DM] - Query table 'BMSQL_HISTORY' counts: 10347 
[INFO ] 2024-08-27 12:21:09.973 - [任务 1][DM] - Table [BMSQL_HISTORY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-27 12:21:09.973 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 12:21:09.988 - [任务 1][DM] - Incremental sync starting... 
[INFO ] 2024-08-27 12:21:09.989 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 12:21:09.989 - [任务 1][DM] - Starting stream read, table list: [BMSQL_HISTORY_TEST1, BMSQL_HISTORY], offset: {"sortString":null,"offsetValue":null,"lastScn":53795567,"pendingScn":53795567,"timestamp":null} 
[INFO ] 2024-08-27 12:21:10.195 - [任务 1][DM] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-08-27_10-42-00.log 
[INFO ] 2024-08-27 12:22:09.038 - [任务 1][DM] - Found new table(s): [BMSQL_HISTORY_TEST2] 
[INFO ] 2024-08-27 12:22:09.429 - [任务 1][DM] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-08-27 12:22:09.429 - [任务 1][DM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@22254af1: {"table":{"id":"BMSQL_HISTORY_TEST2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"ID"}],"name":"INDEX33557311","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"BMSQL_HISTORY_TEST2","nameFieldMap":{"ID":{"autoInc":false,"dataType":"INTEGER","name":"ID","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"NAME":{"autoInc":false,"dataType":"VARCHAR(24)","name":"NAME","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"bytes":24,"defaultValue":8188,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"ID"}],"indexMap":{"ID":{"fieldAsc":true,"name":"ID"}},"unique":true}},"tableId":"BMSQL_HISTORY_TEST2","type":206} 
[INFO ] 2024-08-27 12:22:09.519 - [任务 1][DM] - Create new table in memory, qualified name: T_dameng_io_tapdata_1_0-SNAPSHOT_BMSQL_HISTORY_TEST2_66cd47244d53ab4c5d531ac3_66cd48324d53ab4c5d531ccc 
[INFO ] 2024-08-27 12:22:09.520 - [任务 1][DM] - Create new table schema transform finished: TapTable id BMSQL_HISTORY_TEST2 name BMSQL_HISTORY_TEST2 storageEngine null charset null number of fields 2 
[INFO ] 2024-08-27 12:22:09.524 - [任务 1][DM] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-08-27 12:22:09.524 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:09.541 - [任务 1][DM] - 【single miner】add log miner sql: null 
[INFO ] 2024-08-27 12:22:09.542 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  
[ERROR] 2024-08-27 12:22:09.560 - [任务 1][DM] - Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@44a7e0ac, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  <-- Error Message -->
Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@44a7e0ac, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 

<-- Simple Stack Trace -->
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...

<-- Full Stack Trace -->
dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.restartPdkConnector(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleNewTables(HazelcastSourcePdkBaseNode.java:935)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$null$13(HazelcastSourcePdkBaseNode.java:830)
	at com.tapdata.constant.LockUtil.runWithLock(LockUtil.java:29)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$handleTableMonitorResult$14(HazelcastSourcePdkBaseNode.java:794)
	at io.tapdata.flow.engine.V2.monitor.impl.TableMonitor.consume(TableMonitor.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleTableMonitorResult(HazelcastSourcePdkBaseNode.java:789)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at dm.jdbc.driver.DBError.throwException(DBError.java:696)
	at dm.jdbc.b.b.o.E(MSG.java:560)
	at dm.jdbc.b.b.o.B(MSG.java:520)
	at dm.jdbc.b.b.o.A(MSG.java:501)
	at dm.jdbc.b.a.a(DBAccess.java:230)
	at dm.jdbc.b.a.a(DBAccess.java:701)
	at dm.jdbc.driver.DmdbStatement.executeInner(DmdbStatement.java:723)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:189)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:181)
	at dm.jdbc.driver.DmdbStatement.execute(DmdbStatement.java:1230)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.stopMiner(DamengLogMiner.java:717)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.stopMiner(SingleDamengLogMiner.java:147)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.closeCdcRunner(DamengCdcRunner.java:43)
	at io.tapdata.connector.dameng.DamengConnector.onStop(DamengConnector.java:106)
	at io.tapdata.base.ConnectorBase.stop(ConnectorBase.java:320)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorStop(ConnectorNode.java:104)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$restartPdkConnector$23(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 21 more

[WARN ] 2024-08-27 12:22:09.568 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 12:22:09.568 - [任务 1][DM] - Job suspend in error handle 
[INFO ] 2024-08-27 12:22:09.873 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] running status set to false 
[INFO ] 2024-08-27 12:22:09.873 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:09.894 - [任务 1][DM] - PDK connector node stopped: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:09.894 - [任务 1][DM] - PDK connector node released: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:09.894 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] schema data cleaned 
[INFO ] 2024-08-27 12:22:09.909 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] monitor closed 
[INFO ] 2024-08-27 12:22:09.909 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] close complete, cost 46 ms 
[INFO ] 2024-08-27 12:22:09.933 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] running status set to false 
[INFO ] 2024-08-27 12:22:09.934 - [任务 1][DM] - Incremental sync completed 
[INFO ] 2024-08-27 12:22:09.948 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_HISTORY_TEST1" not exists, qualified name: null tableNameAndQualifiedNameMap: {} 
[INFO ] 2024-08-27 12:22:09.948 - [任务 1][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:09.950 - [任务 1][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:09.950 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] schema data cleaned 
[INFO ] 2024-08-27 12:22:09.951 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] monitor closed 
[INFO ] 2024-08-27 12:22:09.952 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] close complete, cost 41 ms 
[INFO ] 2024-08-27 12:22:10.954 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-27 12:22:10.955 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7d44a75b 
[INFO ] 2024-08-27 12:22:10.958 - [任务 1] - Stop task milestones: 66cd48324d53ab4c5d531ccc(任务 1)  
[INFO ] 2024-08-27 12:22:11.091 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-27 12:22:11.091 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-27 12:22:11.092 - [任务 1] - Remove memory task client succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:11.092 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:11.146 - [任务 1] - Resume task[任务 1] 
[INFO ] 2024-08-27 12:22:11.147 - [任务 1] - Start task milestones: 66cd48324d53ab4c5d531ccc(任务 1) 
[INFO ] 2024-08-27 12:22:11.336 - [任务 1] - Task initialization... 
[INFO ] 2024-08-27 12:22:11.336 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-27 12:22:11.433 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-27 12:22:11.433 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:11.433 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:11.433 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:11.433 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:11.613 - [任务 1][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-27 12:22:11.613 - [任务 1][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-27 12:22:11.647 - [任务 1][DM] - Source node "DM" read batch size: 100 
[INFO ] 2024-08-27 12:22:11.648 - [任务 1][DM] - Source node "DM" event queue capacity: 200 
[INFO ] 2024-08-27 12:22:11.650 - [任务 1][DM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-27 12:22:11.659 - [任务 1][DM] - batch offset found: {"BMSQL_HISTORY_TEST1":{"batch_read_connector_status":"OVER"},"BMSQL_HISTORY":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:11.659 - [任务 1] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-27 12:22:11.759 - [任务 1][DM] - Found new table(s): [BMSQL_HISTORY_TEST2] 
[INFO ] 2024-08-27 12:22:11.762 - [任务 1][DM] - Incremental sync starting... 
[INFO ] 2024-08-27 12:22:11.762 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 12:22:11.876 - [任务 1][DM] - Starting stream read, table list: [BMSQL_HISTORY_TEST1, BMSQL_HISTORY], offset: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:11.876 - [任务 1][DM] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-08-27_10-42-00.log 
[INFO ] 2024-08-27 12:22:11.887 - [任务 1][DM] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-08-27 12:22:11.887 - [任务 1][DM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@66485737: {"table":{"id":"BMSQL_HISTORY_TEST2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"ID"}],"name":"INDEX33557311","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"BMSQL_HISTORY_TEST2","nameFieldMap":{"ID":{"autoInc":false,"dataType":"INTEGER","name":"ID","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"NAME":{"autoInc":false,"dataType":"VARCHAR(24)","name":"NAME","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"bytes":24,"defaultValue":8188,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"ID"}],"indexMap":{"ID":{"fieldAsc":true,"name":"ID"}},"unique":true}},"tableId":"BMSQL_HISTORY_TEST2","type":206} 
[INFO ] 2024-08-27 12:22:11.953 - [任务 1][DM] - Create new table in memory, qualified name: T_dameng_io_tapdata_1_0-SNAPSHOT_BMSQL_HISTORY_TEST2_66cd47244d53ab4c5d531ac3_66cd48324d53ab4c5d531ccc 
[INFO ] 2024-08-27 12:22:11.953 - [任务 1][DM] - Create new table schema transform finished: TapTable id BMSQL_HISTORY_TEST2 name BMSQL_HISTORY_TEST2 storageEngine null charset null number of fields 2 
[INFO ] 2024-08-27 12:22:11.955 - [任务 1][DM] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-08-27 12:22:11.955 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:11.963 - [任务 1][DM] - 【single miner】add log miner sql: null 
[INFO ] 2024-08-27 12:22:11.963 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  
[WARN ] 2024-08-27 12:22:11.982 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[ERROR] 2024-08-27 12:22:11.991 - [任务 1][DM] - Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@40438e77, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  <-- Error Message -->
Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@40438e77, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 

<-- Simple Stack Trace -->
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...

<-- Full Stack Trace -->
dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.restartPdkConnector(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleNewTables(HazelcastSourcePdkBaseNode.java:935)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$null$13(HazelcastSourcePdkBaseNode.java:830)
	at com.tapdata.constant.LockUtil.runWithLock(LockUtil.java:29)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$handleTableMonitorResult$14(HazelcastSourcePdkBaseNode.java:794)
	at io.tapdata.flow.engine.V2.monitor.impl.TableMonitor.consume(TableMonitor.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleTableMonitorResult(HazelcastSourcePdkBaseNode.java:789)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at dm.jdbc.driver.DBError.throwException(DBError.java:696)
	at dm.jdbc.b.b.o.E(MSG.java:560)
	at dm.jdbc.b.b.o.B(MSG.java:520)
	at dm.jdbc.b.b.o.A(MSG.java:501)
	at dm.jdbc.b.a.a(DBAccess.java:230)
	at dm.jdbc.b.a.a(DBAccess.java:701)
	at dm.jdbc.driver.DmdbStatement.executeInner(DmdbStatement.java:723)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:189)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:181)
	at dm.jdbc.driver.DmdbStatement.execute(DmdbStatement.java:1230)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.stopMiner(DamengLogMiner.java:717)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.stopMiner(SingleDamengLogMiner.java:147)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.closeCdcRunner(DamengCdcRunner.java:43)
	at io.tapdata.connector.dameng.DamengConnector.onStop(DamengConnector.java:106)
	at io.tapdata.base.ConnectorBase.stop(ConnectorBase.java:320)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorStop(ConnectorNode.java:104)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$restartPdkConnector$23(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 21 more

[INFO ] 2024-08-27 12:22:12.192 - [任务 1][DM] - Job suspend in error handle 
[INFO ] 2024-08-27 12:22:12.263 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] running status set to false 
[INFO ] 2024-08-27 12:22:12.272 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:12.272 - [任务 1][DM] - PDK connector node stopped: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:12.272 - [任务 1][DM] - PDK connector node released: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:12.272 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] schema data cleaned 
[INFO ] 2024-08-27 12:22:12.273 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] monitor closed 
[INFO ] 2024-08-27 12:22:12.276 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] close complete, cost 14 ms 
[INFO ] 2024-08-27 12:22:12.276 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] running status set to false 
[INFO ] 2024-08-27 12:22:12.291 - [任务 1][DM] - Incremental sync completed 
[INFO ] 2024-08-27 12:22:12.291 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.NullPointerException 
[INFO ] 2024-08-27 12:22:12.292 - [任务 1][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:12.292 - [任务 1][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:12.292 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] schema data cleaned 
[INFO ] 2024-08-27 12:22:12.292 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] monitor closed 
[INFO ] 2024-08-27 12:22:12.493 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] close complete, cost 16 ms 
[INFO ] 2024-08-27 12:22:16.160 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-27 12:22:16.161 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2c2c97d6 
[INFO ] 2024-08-27 12:22:16.162 - [任务 1] - Stop task milestones: 66cd48324d53ab4c5d531ccc(任务 1)  
[INFO ] 2024-08-27 12:22:16.285 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-27 12:22:16.285 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-27 12:22:16.285 - [任务 1] - Remove memory task client succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:16.286 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:16.318 - [任务 1] - Resume task[任务 1] 
[INFO ] 2024-08-27 12:22:16.320 - [任务 1] - Start task milestones: 66cd48324d53ab4c5d531ccc(任务 1) 
[INFO ] 2024-08-27 12:22:16.441 - [任务 1] - Task initialization... 
[INFO ] 2024-08-27 12:22:16.441 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-27 12:22:16.510 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-27 12:22:16.510 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:16.510 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:16.511 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:16.727 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:16.744 - [任务 1][DM] - Source node "DM" read batch size: 100 
[INFO ] 2024-08-27 12:22:16.744 - [任务 1][DM] - Source node "DM" event queue capacity: 200 
[INFO ] 2024-08-27 12:22:16.748 - [任务 1][DM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-27 12:22:16.754 - [任务 1][DM] - batch offset found: {"BMSQL_HISTORY_TEST1":{"batch_read_connector_status":"OVER"},"BMSQL_HISTORY":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:16.754 - [任务 1] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-27 12:22:16.767 - [任务 1][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-27 12:22:16.767 - [任务 1][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-27 12:22:16.818 - [任务 1][DM] - Incremental sync starting... 
[INFO ] 2024-08-27 12:22:16.818 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 12:22:16.827 - [任务 1][DM] - Starting stream read, table list: [BMSQL_HISTORY_TEST1, BMSQL_HISTORY], offset: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:16.892 - [任务 1][DM] - Found new table(s): [BMSQL_HISTORY_TEST2] 
[INFO ] 2024-08-27 12:22:16.892 - [任务 1][DM] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-08-27_10-42-00.log 
[INFO ] 2024-08-27 12:22:16.996 - [任务 1][DM] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-08-27 12:22:16.997 - [任务 1][DM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@6e73bc82: {"table":{"id":"BMSQL_HISTORY_TEST2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"ID"}],"name":"INDEX33557311","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"BMSQL_HISTORY_TEST2","nameFieldMap":{"ID":{"autoInc":false,"dataType":"INTEGER","name":"ID","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"NAME":{"autoInc":false,"dataType":"VARCHAR(24)","name":"NAME","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"bytes":24,"defaultValue":8188,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"ID"}],"indexMap":{"ID":{"fieldAsc":true,"name":"ID"}},"unique":true}},"tableId":"BMSQL_HISTORY_TEST2","type":206} 
[INFO ] 2024-08-27 12:22:17.067 - [任务 1][DM] - Create new table in memory, qualified name: T_dameng_io_tapdata_1_0-SNAPSHOT_BMSQL_HISTORY_TEST2_66cd47244d53ab4c5d531ac3_66cd48324d53ab4c5d531ccc 
[INFO ] 2024-08-27 12:22:17.067 - [任务 1][DM] - Create new table schema transform finished: TapTable id BMSQL_HISTORY_TEST2 name BMSQL_HISTORY_TEST2 storageEngine null charset null number of fields 2 
[INFO ] 2024-08-27 12:22:17.071 - [任务 1][DM] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-08-27 12:22:17.071 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:17.094 - [任务 1][DM] - 【single miner】add log miner sql: null 
[INFO ] 2024-08-27 12:22:17.110 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  
[WARN ] 2024-08-27 12:22:17.116 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[ERROR] 2024-08-27 12:22:17.117 - [任务 1][DM] - Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@242981f, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  <-- Error Message -->
Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@242981f, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 

<-- Simple Stack Trace -->
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...

<-- Full Stack Trace -->
dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.restartPdkConnector(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleNewTables(HazelcastSourcePdkBaseNode.java:935)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$null$13(HazelcastSourcePdkBaseNode.java:830)
	at com.tapdata.constant.LockUtil.runWithLock(LockUtil.java:29)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$handleTableMonitorResult$14(HazelcastSourcePdkBaseNode.java:794)
	at io.tapdata.flow.engine.V2.monitor.impl.TableMonitor.consume(TableMonitor.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleTableMonitorResult(HazelcastSourcePdkBaseNode.java:789)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at dm.jdbc.driver.DBError.throwException(DBError.java:696)
	at dm.jdbc.b.b.o.E(MSG.java:560)
	at dm.jdbc.b.b.o.B(MSG.java:520)
	at dm.jdbc.b.b.o.A(MSG.java:501)
	at dm.jdbc.b.a.a(DBAccess.java:230)
	at dm.jdbc.b.a.a(DBAccess.java:701)
	at dm.jdbc.driver.DmdbStatement.executeInner(DmdbStatement.java:723)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:189)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:181)
	at dm.jdbc.driver.DmdbStatement.execute(DmdbStatement.java:1230)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.stopMiner(DamengLogMiner.java:717)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.stopMiner(SingleDamengLogMiner.java:147)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.closeCdcRunner(DamengCdcRunner.java:43)
	at io.tapdata.connector.dameng.DamengConnector.onStop(DamengConnector.java:106)
	at io.tapdata.base.ConnectorBase.stop(ConnectorBase.java:320)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorStop(ConnectorNode.java:104)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$restartPdkConnector$23(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 21 more

[INFO ] 2024-08-27 12:22:17.117 - [任务 1][DM] - Job suspend in error handle 
[INFO ] 2024-08-27 12:22:17.324 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] running status set to false 
[INFO ] 2024-08-27 12:22:17.325 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:17.332 - [任务 1][DM] - PDK connector node stopped: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:17.332 - [任务 1][DM] - PDK connector node released: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:17.332 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] schema data cleaned 
[INFO ] 2024-08-27 12:22:17.338 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] monitor closed 
[INFO ] 2024-08-27 12:22:17.338 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] close complete, cost 16 ms 
[INFO ] 2024-08-27 12:22:17.361 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] running status set to false 
[INFO ] 2024-08-27 12:22:17.361 - [任务 1][DM] - Incremental sync completed 
[INFO ] 2024-08-27 12:22:17.362 - [任务 1][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:17.363 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_HISTORY_TEST1" not exists, qualified name: null tableNameAndQualifiedNameMap: {} 
[INFO ] 2024-08-27 12:22:17.363 - [任务 1][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:17.363 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] schema data cleaned 
[INFO ] 2024-08-27 12:22:17.365 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] monitor closed 
[INFO ] 2024-08-27 12:22:17.366 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] close complete, cost 26 ms 
[INFO ] 2024-08-27 12:22:21.355 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-27 12:22:21.357 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3ac80a8e 
[INFO ] 2024-08-27 12:22:21.357 - [任务 1] - Stop task milestones: 66cd48324d53ab4c5d531ccc(任务 1)  
[INFO ] 2024-08-27 12:22:21.484 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-27 12:22:21.484 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-27 12:22:21.485 - [任务 1] - Remove memory task client succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:21.485 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:21.522 - [任务 1] - Resume task[任务 1] 
[INFO ] 2024-08-27 12:22:21.525 - [任务 1] - Start task milestones: 66cd48324d53ab4c5d531ccc(任务 1) 
[INFO ] 2024-08-27 12:22:21.648 - [任务 1] - Task initialization... 
[INFO ] 2024-08-27 12:22:21.648 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-27 12:22:21.743 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-27 12:22:21.743 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:21.743 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:21.743 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:21.743 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:21.971 - [任务 1][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-27 12:22:21.979 - [任务 1][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-27 12:22:21.992 - [任务 1][DM] - Source node "DM" read batch size: 100 
[INFO ] 2024-08-27 12:22:21.993 - [任务 1][DM] - Source node "DM" event queue capacity: 200 
[INFO ] 2024-08-27 12:22:21.993 - [任务 1][DM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-27 12:22:21.994 - [任务 1][DM] - batch offset found: {"BMSQL_HISTORY_TEST1":{"batch_read_connector_status":"OVER"},"BMSQL_HISTORY":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:22.003 - [任务 1] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-27 12:22:22.053 - [任务 1][DM] - Incremental sync starting... 
[INFO ] 2024-08-27 12:22:22.058 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 12:22:22.068 - [任务 1][DM] - Starting stream read, table list: [BMSQL_HISTORY_TEST1, BMSQL_HISTORY], offset: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:22.069 - [任务 1][DM] - Found new table(s): [BMSQL_HISTORY_TEST2] 
[INFO ] 2024-08-27 12:22:22.176 - [任务 1][DM] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-08-27 12:22:22.181 - [任务 1][DM] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-08-27_10-42-00.log 
[INFO ] 2024-08-27 12:22:22.182 - [任务 1][DM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@5f708b2: {"table":{"id":"BMSQL_HISTORY_TEST2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"ID"}],"name":"INDEX33557311","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"BMSQL_HISTORY_TEST2","nameFieldMap":{"ID":{"autoInc":false,"dataType":"INTEGER","name":"ID","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"NAME":{"autoInc":false,"dataType":"VARCHAR(24)","name":"NAME","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"bytes":24,"defaultValue":8188,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"ID"}],"indexMap":{"ID":{"fieldAsc":true,"name":"ID"}},"unique":true}},"tableId":"BMSQL_HISTORY_TEST2","type":206} 
[INFO ] 2024-08-27 12:22:22.248 - [任务 1][DM] - Create new table in memory, qualified name: T_dameng_io_tapdata_1_0-SNAPSHOT_BMSQL_HISTORY_TEST2_66cd47244d53ab4c5d531ac3_66cd48324d53ab4c5d531ccc 
[INFO ] 2024-08-27 12:22:22.248 - [任务 1][DM] - Create new table schema transform finished: TapTable id BMSQL_HISTORY_TEST2 name BMSQL_HISTORY_TEST2 storageEngine null charset null number of fields 2 
[INFO ] 2024-08-27 12:22:22.248 - [任务 1][DM] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-08-27 12:22:22.255 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:22.255 - [任务 1][DM] - 【single miner】add log miner sql: null 
[INFO ] 2024-08-27 12:22:22.272 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  
[WARN ] 2024-08-27 12:22:22.272 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[ERROR] 2024-08-27 12:22:22.307 - [任务 1][DM] - Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@153ec4eb, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  <-- Error Message -->
Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@153ec4eb, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 

<-- Simple Stack Trace -->
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...

<-- Full Stack Trace -->
dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.restartPdkConnector(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleNewTables(HazelcastSourcePdkBaseNode.java:935)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$null$13(HazelcastSourcePdkBaseNode.java:830)
	at com.tapdata.constant.LockUtil.runWithLock(LockUtil.java:29)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$handleTableMonitorResult$14(HazelcastSourcePdkBaseNode.java:794)
	at io.tapdata.flow.engine.V2.monitor.impl.TableMonitor.consume(TableMonitor.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleTableMonitorResult(HazelcastSourcePdkBaseNode.java:789)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at dm.jdbc.driver.DBError.throwException(DBError.java:696)
	at dm.jdbc.b.b.o.E(MSG.java:560)
	at dm.jdbc.b.b.o.B(MSG.java:520)
	at dm.jdbc.b.b.o.A(MSG.java:501)
	at dm.jdbc.b.a.a(DBAccess.java:230)
	at dm.jdbc.b.a.a(DBAccess.java:701)
	at dm.jdbc.driver.DmdbStatement.executeInner(DmdbStatement.java:723)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:189)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:181)
	at dm.jdbc.driver.DmdbStatement.execute(DmdbStatement.java:1230)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.stopMiner(DamengLogMiner.java:717)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.stopMiner(SingleDamengLogMiner.java:147)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.closeCdcRunner(DamengCdcRunner.java:43)
	at io.tapdata.connector.dameng.DamengConnector.onStop(DamengConnector.java:106)
	at io.tapdata.base.ConnectorBase.stop(ConnectorBase.java:320)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorStop(ConnectorNode.java:104)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$restartPdkConnector$23(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 21 more

[INFO ] 2024-08-27 12:22:22.307 - [任务 1][DM] - Job suspend in error handle 
[INFO ] 2024-08-27 12:22:22.565 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] running status set to false 
[INFO ] 2024-08-27 12:22:22.565 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:22.576 - [任务 1][DM] - PDK connector node stopped: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:22.576 - [任务 1][DM] - PDK connector node released: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:22.576 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] schema data cleaned 
[INFO ] 2024-08-27 12:22:22.587 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] monitor closed 
[INFO ] 2024-08-27 12:22:22.595 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] close complete, cost 27 ms 
[INFO ] 2024-08-27 12:22:22.595 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] running status set to false 
[INFO ] 2024-08-27 12:22:22.609 - [任务 1][DM] - Incremental sync completed 
[INFO ] 2024-08-27 12:22:22.609 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_HISTORY_TEST1" not exists, qualified name: null tableNameAndQualifiedNameMap: {} 
[INFO ] 2024-08-27 12:22:22.611 - [任务 1][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:22.611 - [任务 1][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:22.611 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] schema data cleaned 
[INFO ] 2024-08-27 12:22:22.611 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] monitor closed 
[INFO ] 2024-08-27 12:22:22.814 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] close complete, cost 24 ms 
[INFO ] 2024-08-27 12:22:26.560 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-27 12:22:26.560 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@63c46808 
[INFO ] 2024-08-27 12:22:26.695 - [任务 1] - Stop task milestones: 66cd48324d53ab4c5d531ccc(任务 1)  
[INFO ] 2024-08-27 12:22:26.695 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-27 12:22:26.695 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-27 12:22:26.695 - [任务 1] - Remove memory task client succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:26.695 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:26.723 - [任务 1] - Resume task[任务 1] 
[INFO ] 2024-08-27 12:22:26.726 - [任务 1] - Start task milestones: 66cd48324d53ab4c5d531ccc(任务 1) 
[INFO ] 2024-08-27 12:22:26.816 - [任务 1] - Task initialization... 
[INFO ] 2024-08-27 12:22:26.817 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-27 12:22:26.889 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-27 12:22:26.890 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:26.890 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:26.890 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:27.098 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:27.134 - [任务 1][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-27 12:22:27.134 - [任务 1][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-27 12:22:27.148 - [任务 1][DM] - Source node "DM" read batch size: 100 
[INFO ] 2024-08-27 12:22:27.148 - [任务 1][DM] - Source node "DM" event queue capacity: 200 
[INFO ] 2024-08-27 12:22:27.148 - [任务 1][DM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-27 12:22:27.165 - [任务 1][DM] - batch offset found: {"BMSQL_HISTORY_TEST1":{"batch_read_connector_status":"OVER"},"BMSQL_HISTORY":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:27.165 - [任务 1] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-27 12:22:27.237 - [任务 1][DM] - Incremental sync starting... 
[INFO ] 2024-08-27 12:22:27.237 - [任务 1][DM] - Found new table(s): [BMSQL_HISTORY_TEST2] 
[INFO ] 2024-08-27 12:22:27.238 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 12:22:27.239 - [任务 1][DM] - Starting stream read, table list: [BMSQL_HISTORY_TEST1, BMSQL_HISTORY], offset: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:27.370 - [任务 1][DM] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-08-27_10-42-00.log 
[INFO ] 2024-08-27 12:22:27.370 - [任务 1][DM] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-08-27 12:22:27.391 - [任务 1][DM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@6332ed97: {"table":{"id":"BMSQL_HISTORY_TEST2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"ID"}],"name":"INDEX33557311","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"BMSQL_HISTORY_TEST2","nameFieldMap":{"ID":{"autoInc":false,"dataType":"INTEGER","name":"ID","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"NAME":{"autoInc":false,"dataType":"VARCHAR(24)","name":"NAME","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"bytes":24,"defaultValue":8188,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"ID"}],"indexMap":{"ID":{"fieldAsc":true,"name":"ID"}},"unique":true}},"tableId":"BMSQL_HISTORY_TEST2","type":206} 
[INFO ] 2024-08-27 12:22:27.391 - [任务 1][DM] - Create new table in memory, qualified name: T_dameng_io_tapdata_1_0-SNAPSHOT_BMSQL_HISTORY_TEST2_66cd47244d53ab4c5d531ac3_66cd48324d53ab4c5d531ccc 
[INFO ] 2024-08-27 12:22:27.423 - [任务 1][DM] - Create new table schema transform finished: TapTable id BMSQL_HISTORY_TEST2 name BMSQL_HISTORY_TEST2 storageEngine null charset null number of fields 2 
[INFO ] 2024-08-27 12:22:27.423 - [任务 1][DM] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-08-27 12:22:27.426 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:27.426 - [任务 1][DM] - 【single miner】add log miner sql: null 
[INFO ] 2024-08-27 12:22:27.435 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  
[WARN ] 2024-08-27 12:22:27.435 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[ERROR] 2024-08-27 12:22:27.454 - [任务 1][DM] - Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@7ddf8c34, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  <-- Error Message -->
Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@7ddf8c34, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 

<-- Simple Stack Trace -->
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...

<-- Full Stack Trace -->
dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.restartPdkConnector(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleNewTables(HazelcastSourcePdkBaseNode.java:935)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$null$13(HazelcastSourcePdkBaseNode.java:830)
	at com.tapdata.constant.LockUtil.runWithLock(LockUtil.java:29)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$handleTableMonitorResult$14(HazelcastSourcePdkBaseNode.java:794)
	at io.tapdata.flow.engine.V2.monitor.impl.TableMonitor.consume(TableMonitor.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleTableMonitorResult(HazelcastSourcePdkBaseNode.java:789)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at dm.jdbc.driver.DBError.throwException(DBError.java:696)
	at dm.jdbc.b.b.o.E(MSG.java:560)
	at dm.jdbc.b.b.o.B(MSG.java:520)
	at dm.jdbc.b.b.o.A(MSG.java:501)
	at dm.jdbc.b.a.a(DBAccess.java:230)
	at dm.jdbc.b.a.a(DBAccess.java:701)
	at dm.jdbc.driver.DmdbStatement.executeInner(DmdbStatement.java:723)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:189)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:181)
	at dm.jdbc.driver.DmdbStatement.execute(DmdbStatement.java:1230)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.stopMiner(DamengLogMiner.java:717)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.stopMiner(SingleDamengLogMiner.java:147)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.closeCdcRunner(DamengCdcRunner.java:43)
	at io.tapdata.connector.dameng.DamengConnector.onStop(DamengConnector.java:106)
	at io.tapdata.base.ConnectorBase.stop(ConnectorBase.java:320)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorStop(ConnectorNode.java:104)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$restartPdkConnector$23(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 21 more

[INFO ] 2024-08-27 12:22:27.454 - [任务 1][DM] - Job suspend in error handle 
[INFO ] 2024-08-27 12:22:27.727 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] running status set to false 
[INFO ] 2024-08-27 12:22:27.727 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:27.733 - [任务 1][DM] - PDK connector node stopped: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:27.733 - [任务 1][DM] - PDK connector node released: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:27.734 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] schema data cleaned 
[INFO ] 2024-08-27 12:22:27.734 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] monitor closed 
[INFO ] 2024-08-27 12:22:27.736 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] close complete, cost 13 ms 
[INFO ] 2024-08-27 12:22:27.736 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] running status set to false 
[INFO ] 2024-08-27 12:22:27.754 - [任务 1][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:27.754 - [任务 1][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:27.756 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] schema data cleaned 
[INFO ] 2024-08-27 12:22:27.756 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] monitor closed 
[INFO ] 2024-08-27 12:22:27.770 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] close complete, cost 20 ms 
[INFO ] 2024-08-27 12:22:27.770 - [任务 1][DM] - Incremental sync completed 
[INFO ] 2024-08-27 12:22:27.973 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.NullPointerException 
[INFO ] 2024-08-27 12:22:31.759 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-27 12:22:31.759 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@120223f9 
[INFO ] 2024-08-27 12:22:31.897 - [任务 1] - Stop task milestones: 66cd48324d53ab4c5d531ccc(任务 1)  
[INFO ] 2024-08-27 12:22:31.897 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-27 12:22:31.900 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-27 12:22:31.900 - [任务 1] - Remove memory task client succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:31.918 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:31.918 - [任务 1] - Resume task[任务 1] 
[INFO ] 2024-08-27 12:22:31.956 - [任务 1] - Start task milestones: 66cd48324d53ab4c5d531ccc(任务 1) 
[INFO ] 2024-08-27 12:22:31.956 - [任务 1] - Task initialization... 
[INFO ] 2024-08-27 12:22:32.108 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-27 12:22:32.108 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-27 12:22:32.169 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:32.169 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:32.169 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:32.169 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:32.383 - [任务 1][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-27 12:22:32.383 - [任务 1][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-27 12:22:32.435 - [任务 1][DM] - Source node "DM" read batch size: 100 
[INFO ] 2024-08-27 12:22:32.440 - [任务 1][DM] - Source node "DM" event queue capacity: 200 
[INFO ] 2024-08-27 12:22:32.441 - [任务 1][DM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-27 12:22:32.450 - [任务 1][DM] - batch offset found: {"BMSQL_HISTORY_TEST1":{"batch_read_connector_status":"OVER"},"BMSQL_HISTORY":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:32.450 - [任务 1] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-27 12:22:32.525 - [任务 1][DM] - Incremental sync starting... 
[INFO ] 2024-08-27 12:22:32.525 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 12:22:32.526 - [任务 1][DM] - Starting stream read, table list: [BMSQL_HISTORY_TEST1, BMSQL_HISTORY], offset: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:32.715 - [任务 1][DM] - Found new table(s): [BMSQL_HISTORY_TEST2] 
[INFO ] 2024-08-27 12:22:32.716 - [任务 1][DM] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-08-27_10-42-00.log 
[INFO ] 2024-08-27 12:22:32.716 - [任务 1][DM] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-08-27 12:22:32.719 - [任务 1][DM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@41ba4689: {"table":{"id":"BMSQL_HISTORY_TEST2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"ID"}],"name":"INDEX33557311","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"BMSQL_HISTORY_TEST2","nameFieldMap":{"ID":{"autoInc":false,"dataType":"INTEGER","name":"ID","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"NAME":{"autoInc":false,"dataType":"VARCHAR(24)","name":"NAME","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"bytes":24,"defaultValue":8188,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"ID"}],"indexMap":{"ID":{"fieldAsc":true,"name":"ID"}},"unique":true}},"tableId":"BMSQL_HISTORY_TEST2","type":206} 
[INFO ] 2024-08-27 12:22:32.814 - [任务 1][DM] - Create new table in memory, qualified name: T_dameng_io_tapdata_1_0-SNAPSHOT_BMSQL_HISTORY_TEST2_66cd47244d53ab4c5d531ac3_66cd48324d53ab4c5d531ccc 
[INFO ] 2024-08-27 12:22:32.816 - [任务 1][DM] - Create new table schema transform finished: TapTable id BMSQL_HISTORY_TEST2 name BMSQL_HISTORY_TEST2 storageEngine null charset null number of fields 2 
[INFO ] 2024-08-27 12:22:32.816 - [任务 1][DM] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-08-27 12:22:32.817 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:32.826 - [任务 1][DM] - 【single miner】add log miner sql: null 
[INFO ] 2024-08-27 12:22:32.826 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  
[WARN ] 2024-08-27 12:22:32.834 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[ERROR] 2024-08-27 12:22:32.838 - [任务 1][DM] - Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@783d41f, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  <-- Error Message -->
Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@783d41f, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 

<-- Simple Stack Trace -->
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...

<-- Full Stack Trace -->
dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.restartPdkConnector(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleNewTables(HazelcastSourcePdkBaseNode.java:935)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$null$13(HazelcastSourcePdkBaseNode.java:830)
	at com.tapdata.constant.LockUtil.runWithLock(LockUtil.java:29)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$handleTableMonitorResult$14(HazelcastSourcePdkBaseNode.java:794)
	at io.tapdata.flow.engine.V2.monitor.impl.TableMonitor.consume(TableMonitor.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleTableMonitorResult(HazelcastSourcePdkBaseNode.java:789)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at dm.jdbc.driver.DBError.throwException(DBError.java:696)
	at dm.jdbc.b.b.o.E(MSG.java:560)
	at dm.jdbc.b.b.o.B(MSG.java:520)
	at dm.jdbc.b.b.o.A(MSG.java:501)
	at dm.jdbc.b.a.a(DBAccess.java:230)
	at dm.jdbc.b.a.a(DBAccess.java:701)
	at dm.jdbc.driver.DmdbStatement.executeInner(DmdbStatement.java:723)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:189)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:181)
	at dm.jdbc.driver.DmdbStatement.execute(DmdbStatement.java:1230)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.stopMiner(DamengLogMiner.java:717)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.stopMiner(SingleDamengLogMiner.java:147)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.closeCdcRunner(DamengCdcRunner.java:43)
	at io.tapdata.connector.dameng.DamengConnector.onStop(DamengConnector.java:106)
	at io.tapdata.base.ConnectorBase.stop(ConnectorBase.java:320)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorStop(ConnectorNode.java:104)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$restartPdkConnector$23(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 21 more

[INFO ] 2024-08-27 12:22:33.027 - [任务 1][DM] - Job suspend in error handle 
[INFO ] 2024-08-27 12:22:33.027 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] running status set to false 
[INFO ] 2024-08-27 12:22:33.038 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:33.038 - [任务 1][DM] - PDK connector node stopped: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:33.039 - [任务 1][DM] - PDK connector node released: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:33.039 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] schema data cleaned 
[INFO ] 2024-08-27 12:22:33.043 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] monitor closed 
[INFO ] 2024-08-27 12:22:33.043 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] close complete, cost 16 ms 
[INFO ] 2024-08-27 12:22:33.059 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] running status set to false 
[INFO ] 2024-08-27 12:22:33.059 - [任务 1][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:33.059 - [任务 1][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:33.059 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] schema data cleaned 
[INFO ] 2024-08-27 12:22:33.060 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] monitor closed 
[INFO ] 2024-08-27 12:22:33.060 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] close complete, cost 16 ms 
[INFO ] 2024-08-27 12:22:33.071 - [任务 1][DM] - Incremental sync completed 
[INFO ] 2024-08-27 12:22:33.072 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_HISTORY_TEST1" not exists, qualified name: null tableNameAndQualifiedNameMap: {} 
[INFO ] 2024-08-27 12:22:36.964 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-27 12:22:36.964 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@37f63c82 
[INFO ] 2024-08-27 12:22:36.965 - [任务 1] - Stop task milestones: 66cd48324d53ab4c5d531ccc(任务 1)  
[INFO ] 2024-08-27 12:22:37.085 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-27 12:22:37.085 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-27 12:22:37.085 - [任务 1] - Remove memory task client succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:37.085 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:37.121 - [任务 1] - Resume task[任务 1] 
[INFO ] 2024-08-27 12:22:37.122 - [任务 1] - Start task milestones: 66cd48324d53ab4c5d531ccc(任务 1) 
[INFO ] 2024-08-27 12:22:37.271 - [任务 1] - Task initialization... 
[INFO ] 2024-08-27 12:22:37.271 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-27 12:22:37.405 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-27 12:22:37.405 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:37.405 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:37.405 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:37.405 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:37.582 - [任务 1][DM] - Source node "DM" read batch size: 100 
[INFO ] 2024-08-27 12:22:37.582 - [任务 1][DM] - Source node "DM" event queue capacity: 200 
[INFO ] 2024-08-27 12:22:37.590 - [任务 1][DM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-27 12:22:37.591 - [任务 1][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-27 12:22:37.591 - [任务 1][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-27 12:22:37.593 - [任务 1][DM] - batch offset found: {"BMSQL_HISTORY_TEST1":{"batch_read_connector_status":"OVER"},"BMSQL_HISTORY":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:37.593 - [任务 1] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-27 12:22:37.643 - [任务 1][DM] - Incremental sync starting... 
[INFO ] 2024-08-27 12:22:37.643 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 12:22:37.655 - [任务 1][DM] - Starting stream read, table list: [BMSQL_HISTORY_TEST1, BMSQL_HISTORY], offset: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:37.655 - [任务 1][DM] - Found new table(s): [BMSQL_HISTORY_TEST2] 
[INFO ] 2024-08-27 12:22:37.767 - [任务 1][DM] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-08-27_10-42-00.log 
[INFO ] 2024-08-27 12:22:37.768 - [任务 1][DM] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-08-27 12:22:37.798 - [任务 1][DM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@2b293750: {"table":{"id":"BMSQL_HISTORY_TEST2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"ID"}],"name":"INDEX33557311","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"BMSQL_HISTORY_TEST2","nameFieldMap":{"ID":{"autoInc":false,"dataType":"INTEGER","name":"ID","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"NAME":{"autoInc":false,"dataType":"VARCHAR(24)","name":"NAME","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"bytes":24,"defaultValue":8188,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"ID"}],"indexMap":{"ID":{"fieldAsc":true,"name":"ID"}},"unique":true}},"tableId":"BMSQL_HISTORY_TEST2","type":206} 
[INFO ] 2024-08-27 12:22:37.799 - [任务 1][DM] - Create new table in memory, qualified name: T_dameng_io_tapdata_1_0-SNAPSHOT_BMSQL_HISTORY_TEST2_66cd47244d53ab4c5d531ac3_66cd48324d53ab4c5d531ccc 
[INFO ] 2024-08-27 12:22:37.858 - [任务 1][DM] - Create new table schema transform finished: TapTable id BMSQL_HISTORY_TEST2 name BMSQL_HISTORY_TEST2 storageEngine null charset null number of fields 2 
[INFO ] 2024-08-27 12:22:37.858 - [任务 1][DM] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-08-27 12:22:37.860 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:37.870 - [任务 1][DM] - 【single miner】add log miner sql: null 
[INFO ] 2024-08-27 12:22:37.870 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  
[WARN ] 2024-08-27 12:22:37.888 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[ERROR] 2024-08-27 12:22:37.892 - [任务 1][DM] - Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@3b1ca3de, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  <-- Error Message -->
Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@3b1ca3de, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 

<-- Simple Stack Trace -->
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...

<-- Full Stack Trace -->
dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.restartPdkConnector(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleNewTables(HazelcastSourcePdkBaseNode.java:935)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$null$13(HazelcastSourcePdkBaseNode.java:830)
	at com.tapdata.constant.LockUtil.runWithLock(LockUtil.java:29)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$handleTableMonitorResult$14(HazelcastSourcePdkBaseNode.java:794)
	at io.tapdata.flow.engine.V2.monitor.impl.TableMonitor.consume(TableMonitor.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleTableMonitorResult(HazelcastSourcePdkBaseNode.java:789)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at dm.jdbc.driver.DBError.throwException(DBError.java:696)
	at dm.jdbc.b.b.o.E(MSG.java:560)
	at dm.jdbc.b.b.o.B(MSG.java:520)
	at dm.jdbc.b.b.o.A(MSG.java:501)
	at dm.jdbc.b.a.a(DBAccess.java:230)
	at dm.jdbc.b.a.a(DBAccess.java:701)
	at dm.jdbc.driver.DmdbStatement.executeInner(DmdbStatement.java:723)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:189)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:181)
	at dm.jdbc.driver.DmdbStatement.execute(DmdbStatement.java:1230)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.stopMiner(DamengLogMiner.java:717)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.stopMiner(SingleDamengLogMiner.java:147)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.closeCdcRunner(DamengCdcRunner.java:43)
	at io.tapdata.connector.dameng.DamengConnector.onStop(DamengConnector.java:106)
	at io.tapdata.base.ConnectorBase.stop(ConnectorBase.java:320)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorStop(ConnectorNode.java:104)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$restartPdkConnector$23(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 21 more

[INFO ] 2024-08-27 12:22:38.104 - [任务 1][DM] - Job suspend in error handle 
[INFO ] 2024-08-27 12:22:38.163 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] running status set to false 
[INFO ] 2024-08-27 12:22:38.163 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:38.176 - [任务 1][DM] - PDK connector node stopped: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:38.177 - [任务 1][DM] - PDK connector node released: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:38.177 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] schema data cleaned 
[INFO ] 2024-08-27 12:22:38.182 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] monitor closed 
[INFO ] 2024-08-27 12:22:38.184 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] close complete, cost 30 ms 
[INFO ] 2024-08-27 12:22:38.184 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] running status set to false 
[INFO ] 2024-08-27 12:22:38.199 - [任务 1][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:38.199 - [任务 1][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:38.200 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] schema data cleaned 
[INFO ] 2024-08-27 12:22:38.200 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] monitor closed 
[INFO ] 2024-08-27 12:22:38.210 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] close complete, cost 17 ms 
[INFO ] 2024-08-27 12:22:38.210 - [任务 1][DM] - Incremental sync completed 
[INFO ] 2024-08-27 12:22:38.411 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_HISTORY_TEST1" not exists, qualified name: null tableNameAndQualifiedNameMap: {} 
[INFO ] 2024-08-27 12:22:42.131 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-27 12:22:42.131 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@421148fe 
[INFO ] 2024-08-27 12:22:42.132 - [任务 1] - Stop task milestones: 66cd48324d53ab4c5d531ccc(任务 1)  
[INFO ] 2024-08-27 12:22:42.250 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-27 12:22:42.250 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-27 12:22:42.250 - [任务 1] - Remove memory task client succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:42.280 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:42.282 - [任务 1] - Resume task[任务 1] 
[INFO ] 2024-08-27 12:22:42.282 - [任务 1] - Start task milestones: 66cd48324d53ab4c5d531ccc(任务 1) 
[INFO ] 2024-08-27 12:22:42.402 - [任务 1] - Task initialization... 
[INFO ] 2024-08-27 12:22:42.402 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-27 12:22:42.472 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-27 12:22:42.472 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:42.472 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:42.472 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:42.473 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:42.676 - [任务 1][DM] - Source node "DM" read batch size: 100 
[INFO ] 2024-08-27 12:22:42.676 - [任务 1][DM] - Source node "DM" event queue capacity: 200 
[INFO ] 2024-08-27 12:22:42.694 - [任务 1][DM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-27 12:22:42.709 - [任务 1][DM] - batch offset found: {"BMSQL_HISTORY_TEST1":{"batch_read_connector_status":"OVER"},"BMSQL_HISTORY":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:42.709 - [任务 1] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-27 12:22:42.719 - [任务 1][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-27 12:22:42.723 - [任务 1][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-27 12:22:42.767 - [任务 1][DM] - Found new table(s): [BMSQL_HISTORY_TEST2] 
[INFO ] 2024-08-27 12:22:42.767 - [任务 1][DM] - Incremental sync starting... 
[INFO ] 2024-08-27 12:22:42.767 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 12:22:42.768 - [任务 1][DM] - Starting stream read, table list: [BMSQL_HISTORY_TEST1, BMSQL_HISTORY], offset: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:42.956 - [任务 1][DM] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-08-27 12:22:42.958 - [任务 1][DM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@1d0f1d23: {"table":{"id":"BMSQL_HISTORY_TEST2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"ID"}],"name":"INDEX33557311","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"BMSQL_HISTORY_TEST2","nameFieldMap":{"ID":{"autoInc":false,"dataType":"INTEGER","name":"ID","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"NAME":{"autoInc":false,"dataType":"VARCHAR(24)","name":"NAME","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"bytes":24,"defaultValue":8188,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"ID"}],"indexMap":{"ID":{"fieldAsc":true,"name":"ID"}},"unique":true}},"tableId":"BMSQL_HISTORY_TEST2","type":206} 
[INFO ] 2024-08-27 12:22:42.958 - [任务 1][DM] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-08-27_10-42-00.log 
[INFO ] 2024-08-27 12:22:42.980 - [任务 1][DM] - Create new table in memory, qualified name: T_dameng_io_tapdata_1_0-SNAPSHOT_BMSQL_HISTORY_TEST2_66cd47244d53ab4c5d531ac3_66cd48324d53ab4c5d531ccc 
[INFO ] 2024-08-27 12:22:43.022 - [任务 1][DM] - Create new table schema transform finished: TapTable id BMSQL_HISTORY_TEST2 name BMSQL_HISTORY_TEST2 storageEngine null charset null number of fields 2 
[INFO ] 2024-08-27 12:22:43.022 - [任务 1][DM] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-08-27 12:22:43.037 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:43.037 - [任务 1][DM] - 【single miner】add log miner sql: null 
[INFO ] 2024-08-27 12:22:43.049 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  
[WARN ] 2024-08-27 12:22:43.054 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[ERROR] 2024-08-27 12:22:43.054 - [任务 1][DM] - Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@6d290d41, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  <-- Error Message -->
Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@6d290d41, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 

<-- Simple Stack Trace -->
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...

<-- Full Stack Trace -->
dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.restartPdkConnector(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleNewTables(HazelcastSourcePdkBaseNode.java:935)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$null$13(HazelcastSourcePdkBaseNode.java:830)
	at com.tapdata.constant.LockUtil.runWithLock(LockUtil.java:29)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$handleTableMonitorResult$14(HazelcastSourcePdkBaseNode.java:794)
	at io.tapdata.flow.engine.V2.monitor.impl.TableMonitor.consume(TableMonitor.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleTableMonitorResult(HazelcastSourcePdkBaseNode.java:789)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at dm.jdbc.driver.DBError.throwException(DBError.java:696)
	at dm.jdbc.b.b.o.E(MSG.java:560)
	at dm.jdbc.b.b.o.B(MSG.java:520)
	at dm.jdbc.b.b.o.A(MSG.java:501)
	at dm.jdbc.b.a.a(DBAccess.java:230)
	at dm.jdbc.b.a.a(DBAccess.java:701)
	at dm.jdbc.driver.DmdbStatement.executeInner(DmdbStatement.java:723)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:189)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:181)
	at dm.jdbc.driver.DmdbStatement.execute(DmdbStatement.java:1230)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.stopMiner(DamengLogMiner.java:717)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.stopMiner(SingleDamengLogMiner.java:147)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.closeCdcRunner(DamengCdcRunner.java:43)
	at io.tapdata.connector.dameng.DamengConnector.onStop(DamengConnector.java:106)
	at io.tapdata.base.ConnectorBase.stop(ConnectorBase.java:320)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorStop(ConnectorNode.java:104)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$restartPdkConnector$23(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 21 more

[INFO ] 2024-08-27 12:22:43.261 - [任务 1][DM] - Job suspend in error handle 
[INFO ] 2024-08-27 12:22:43.269 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] running status set to false 
[INFO ] 2024-08-27 12:22:43.269 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:43.274 - [任务 1][DM] - PDK connector node stopped: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:43.274 - [任务 1][DM] - PDK connector node released: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:43.275 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] schema data cleaned 
[INFO ] 2024-08-27 12:22:43.275 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] monitor closed 
[INFO ] 2024-08-27 12:22:43.277 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] close complete, cost 9 ms 
[INFO ] 2024-08-27 12:22:43.277 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] running status set to false 
[INFO ] 2024-08-27 12:22:43.287 - [任务 1][DM] - Incremental sync completed 
[INFO ] 2024-08-27 12:22:43.287 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.NullPointerException 
[INFO ] 2024-08-27 12:22:43.289 - [任务 1][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:43.289 - [任务 1][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:43.289 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] schema data cleaned 
[INFO ] 2024-08-27 12:22:43.289 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] monitor closed 
[INFO ] 2024-08-27 12:22:43.491 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] close complete, cost 12 ms 
[INFO ] 2024-08-27 12:22:47.300 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-27 12:22:47.301 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@691b89ed 
[INFO ] 2024-08-27 12:22:47.302 - [任务 1] - Stop task milestones: 66cd48324d53ab4c5d531ccc(任务 1)  
[INFO ] 2024-08-27 12:22:47.428 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-27 12:22:47.428 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-27 12:22:47.430 - [任务 1] - Remove memory task client succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:47.446 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:47.446 - [任务 1] - Resume task[任务 1] 
[INFO ] 2024-08-27 12:22:47.475 - [任务 1] - Start task milestones: 66cd48324d53ab4c5d531ccc(任务 1) 
[INFO ] 2024-08-27 12:22:47.475 - [任务 1] - Task initialization... 
[INFO ] 2024-08-27 12:22:47.591 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-27 12:22:47.591 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-27 12:22:47.622 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:47.622 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:47.622 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:47.622 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:47.792 - [任务 1][DM] - Source node "DM" read batch size: 100 
[INFO ] 2024-08-27 12:22:47.794 - [任务 1][DM] - Source node "DM" event queue capacity: 200 
[INFO ] 2024-08-27 12:22:47.799 - [任务 1][DM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-27 12:22:47.799 - [任务 1][DM] - batch offset found: {"BMSQL_HISTORY_TEST1":{"batch_read_connector_status":"OVER"},"BMSQL_HISTORY":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:47.800 - [任务 1][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-27 12:22:47.800 - [任务 1][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-27 12:22:47.856 - [任务 1] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-27 12:22:47.856 - [任务 1][DM] - Found new table(s): [BMSQL_HISTORY_TEST2] 
[INFO ] 2024-08-27 12:22:47.864 - [任务 1][DM] - Incremental sync starting... 
[INFO ] 2024-08-27 12:22:47.864 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 12:22:47.864 - [任务 1][DM] - Starting stream read, table list: [BMSQL_HISTORY_TEST1, BMSQL_HISTORY], offset: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:47.972 - [任务 1][DM] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-08-27_10-42-00.log 
[INFO ] 2024-08-27 12:22:47.972 - [任务 1][DM] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-08-27 12:22:47.981 - [任务 1][DM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@137fcaed: {"table":{"id":"BMSQL_HISTORY_TEST2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"ID"}],"name":"INDEX33557311","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"BMSQL_HISTORY_TEST2","nameFieldMap":{"ID":{"autoInc":false,"dataType":"INTEGER","name":"ID","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"NAME":{"autoInc":false,"dataType":"VARCHAR(24)","name":"NAME","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"bytes":24,"defaultValue":8188,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"ID"}],"indexMap":{"ID":{"fieldAsc":true,"name":"ID"}},"unique":true}},"tableId":"BMSQL_HISTORY_TEST2","type":206} 
[INFO ] 2024-08-27 12:22:47.981 - [任务 1][DM] - Create new table in memory, qualified name: T_dameng_io_tapdata_1_0-SNAPSHOT_BMSQL_HISTORY_TEST2_66cd47244d53ab4c5d531ac3_66cd48324d53ab4c5d531ccc 
[INFO ] 2024-08-27 12:22:48.020 - [任务 1][DM] - Create new table schema transform finished: TapTable id BMSQL_HISTORY_TEST2 name BMSQL_HISTORY_TEST2 storageEngine null charset null number of fields 2 
[INFO ] 2024-08-27 12:22:48.020 - [任务 1][DM] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-08-27 12:22:48.029 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:48.029 - [任务 1][DM] - 【single miner】add log miner sql: null 
[INFO ] 2024-08-27 12:22:48.037 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  
[WARN ] 2024-08-27 12:22:48.037 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[ERROR] 2024-08-27 12:22:48.049 - [任务 1][DM] - Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@10da28ea, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  <-- Error Message -->
Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@10da28ea, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 

<-- Simple Stack Trace -->
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...

<-- Full Stack Trace -->
dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.restartPdkConnector(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleNewTables(HazelcastSourcePdkBaseNode.java:935)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$null$13(HazelcastSourcePdkBaseNode.java:830)
	at com.tapdata.constant.LockUtil.runWithLock(LockUtil.java:29)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$handleTableMonitorResult$14(HazelcastSourcePdkBaseNode.java:794)
	at io.tapdata.flow.engine.V2.monitor.impl.TableMonitor.consume(TableMonitor.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleTableMonitorResult(HazelcastSourcePdkBaseNode.java:789)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at dm.jdbc.driver.DBError.throwException(DBError.java:696)
	at dm.jdbc.b.b.o.E(MSG.java:560)
	at dm.jdbc.b.b.o.B(MSG.java:520)
	at dm.jdbc.b.b.o.A(MSG.java:501)
	at dm.jdbc.b.a.a(DBAccess.java:230)
	at dm.jdbc.b.a.a(DBAccess.java:701)
	at dm.jdbc.driver.DmdbStatement.executeInner(DmdbStatement.java:723)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:189)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:181)
	at dm.jdbc.driver.DmdbStatement.execute(DmdbStatement.java:1230)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.stopMiner(DamengLogMiner.java:717)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.stopMiner(SingleDamengLogMiner.java:147)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.closeCdcRunner(DamengCdcRunner.java:43)
	at io.tapdata.connector.dameng.DamengConnector.onStop(DamengConnector.java:106)
	at io.tapdata.base.ConnectorBase.stop(ConnectorBase.java:320)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorStop(ConnectorNode.java:104)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$restartPdkConnector$23(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 21 more

[INFO ] 2024-08-27 12:22:48.049 - [任务 1][DM] - Job suspend in error handle 
[INFO ] 2024-08-27 12:22:48.386 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] running status set to false 
[INFO ] 2024-08-27 12:22:48.397 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:48.398 - [任务 1][DM] - PDK connector node stopped: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:48.399 - [任务 1][DM] - PDK connector node released: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:48.399 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] schema data cleaned 
[INFO ] 2024-08-27 12:22:48.406 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] monitor closed 
[INFO ] 2024-08-27 12:22:48.407 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] close complete, cost 34 ms 
[INFO ] 2024-08-27 12:22:48.407 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] running status set to false 
[INFO ] 2024-08-27 12:22:48.417 - [任务 1][DM] - Incremental sync completed 
[INFO ] 2024-08-27 12:22:48.424 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.NullPointerException 
[INFO ] 2024-08-27 12:22:48.424 - [任务 1][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:48.424 - [任务 1][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:48.424 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] schema data cleaned 
[INFO ] 2024-08-27 12:22:48.425 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] monitor closed 
[INFO ] 2024-08-27 12:22:48.426 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] close complete, cost 19 ms 
[INFO ] 2024-08-27 12:22:52.531 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-27 12:22:52.531 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3266cbcc 
[INFO ] 2024-08-27 12:22:52.680 - [任务 1] - Stop task milestones: 66cd48324d53ab4c5d531ccc(任务 1)  
[INFO ] 2024-08-27 12:22:52.680 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-27 12:22:52.680 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-27 12:22:52.680 - [任务 1] - Remove memory task client succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:52.698 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:52.698 - [任务 1] - Resume task[任务 1] 
[INFO ] 2024-08-27 12:22:52.726 - [任务 1] - Start task milestones: 66cd48324d53ab4c5d531ccc(任务 1) 
[INFO ] 2024-08-27 12:22:52.726 - [任务 1] - Task initialization... 
[INFO ] 2024-08-27 12:22:52.930 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-27 12:22:53.007 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-27 12:22:53.007 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:53.007 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:53.007 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:53.007 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:53.212 - [任务 1][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-27 12:22:53.212 - [任务 1][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-27 12:22:53.264 - [任务 1][DM] - Source node "DM" read batch size: 100 
[INFO ] 2024-08-27 12:22:53.264 - [任务 1][DM] - Source node "DM" event queue capacity: 200 
[INFO ] 2024-08-27 12:22:53.265 - [任务 1][DM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-27 12:22:53.268 - [任务 1][DM] - batch offset found: {"BMSQL_HISTORY_TEST1":{"batch_read_connector_status":"OVER"},"BMSQL_HISTORY":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:53.268 - [任务 1] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-27 12:22:53.379 - [任务 1][DM] - Found new table(s): [BMSQL_HISTORY_TEST2] 
[INFO ] 2024-08-27 12:22:53.379 - [任务 1][DM] - Incremental sync starting... 
[INFO ] 2024-08-27 12:22:53.379 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 12:22:53.380 - [任务 1][DM] - Starting stream read, table list: [BMSQL_HISTORY_TEST1, BMSQL_HISTORY], offset: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:53.447 - [任务 1][DM] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-08-27 12:22:53.448 - [任务 1][DM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@46212d20: {"table":{"id":"BMSQL_HISTORY_TEST2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"ID"}],"name":"INDEX33557311","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"BMSQL_HISTORY_TEST2","nameFieldMap":{"ID":{"autoInc":false,"dataType":"INTEGER","name":"ID","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"NAME":{"autoInc":false,"dataType":"VARCHAR(24)","name":"NAME","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"bytes":24,"defaultValue":8188,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"ID"}],"indexMap":{"ID":{"fieldAsc":true,"name":"ID"}},"unique":true}},"tableId":"BMSQL_HISTORY_TEST2","type":206} 
[INFO ] 2024-08-27 12:22:53.473 - [任务 1][DM] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-08-27_10-42-00.log 
[INFO ] 2024-08-27 12:22:53.473 - [任务 1][DM] - Create new table in memory, qualified name: T_dameng_io_tapdata_1_0-SNAPSHOT_BMSQL_HISTORY_TEST2_66cd47244d53ab4c5d531ac3_66cd48324d53ab4c5d531ccc 
[INFO ] 2024-08-27 12:22:53.521 - [任务 1][DM] - Create new table schema transform finished: TapTable id BMSQL_HISTORY_TEST2 name BMSQL_HISTORY_TEST2 storageEngine null charset null number of fields 2 
[INFO ] 2024-08-27 12:22:53.521 - [任务 1][DM] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-08-27 12:22:53.529 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:53.529 - [任务 1][DM] - 【single miner】add log miner sql: null 
[INFO ] 2024-08-27 12:22:53.563 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  
[WARN ] 2024-08-27 12:22:53.567 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[ERROR] 2024-08-27 12:22:53.572 - [任务 1][DM] - Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@417c5c20, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  <-- Error Message -->
Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@417c5c20, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 

<-- Simple Stack Trace -->
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...

<-- Full Stack Trace -->
dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.restartPdkConnector(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleNewTables(HazelcastSourcePdkBaseNode.java:935)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$null$13(HazelcastSourcePdkBaseNode.java:830)
	at com.tapdata.constant.LockUtil.runWithLock(LockUtil.java:29)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$handleTableMonitorResult$14(HazelcastSourcePdkBaseNode.java:794)
	at io.tapdata.flow.engine.V2.monitor.impl.TableMonitor.consume(TableMonitor.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleTableMonitorResult(HazelcastSourcePdkBaseNode.java:789)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at dm.jdbc.driver.DBError.throwException(DBError.java:696)
	at dm.jdbc.b.b.o.E(MSG.java:560)
	at dm.jdbc.b.b.o.B(MSG.java:520)
	at dm.jdbc.b.b.o.A(MSG.java:501)
	at dm.jdbc.b.a.a(DBAccess.java:230)
	at dm.jdbc.b.a.a(DBAccess.java:701)
	at dm.jdbc.driver.DmdbStatement.executeInner(DmdbStatement.java:723)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:189)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:181)
	at dm.jdbc.driver.DmdbStatement.execute(DmdbStatement.java:1230)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.stopMiner(DamengLogMiner.java:717)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.stopMiner(SingleDamengLogMiner.java:147)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.closeCdcRunner(DamengCdcRunner.java:43)
	at io.tapdata.connector.dameng.DamengConnector.onStop(DamengConnector.java:106)
	at io.tapdata.base.ConnectorBase.stop(ConnectorBase.java:320)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorStop(ConnectorNode.java:104)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$restartPdkConnector$23(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 21 more

[INFO ] 2024-08-27 12:22:53.572 - [任务 1][DM] - Job suspend in error handle 
[INFO ] 2024-08-27 12:22:53.892 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] running status set to false 
[INFO ] 2024-08-27 12:22:53.913 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:53.914 - [任务 1][DM] - PDK connector node stopped: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:53.925 - [任务 1][DM] - PDK connector node released: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:53.926 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] schema data cleaned 
[INFO ] 2024-08-27 12:22:53.927 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] monitor closed 
[INFO ] 2024-08-27 12:22:53.927 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] close complete, cost 34 ms 
[INFO ] 2024-08-27 12:22:53.927 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] running status set to false 
[INFO ] 2024-08-27 12:22:53.939 - [任务 1][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:53.939 - [任务 1][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:53.939 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] schema data cleaned 
[INFO ] 2024-08-27 12:22:53.939 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] monitor closed 
[INFO ] 2024-08-27 12:22:53.965 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] close complete, cost 14 ms 
[INFO ] 2024-08-27 12:22:53.968 - [任务 1][DM] - Incremental sync completed 
[INFO ] 2024-08-27 12:22:53.968 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Find schema failed, message: null 
[INFO ] 2024-08-27 12:22:57.767 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-27 12:22:57.767 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@41a0ae77 
[INFO ] 2024-08-27 12:22:57.888 - [任务 1] - Stop task milestones: 66cd48324d53ab4c5d531ccc(任务 1)  
[INFO ] 2024-08-27 12:22:57.888 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-27 12:22:57.888 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-27 12:22:57.889 - [任务 1] - Remove memory task client succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:57.889 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:22:57.908 - [任务 1] - Resume task[任务 1] 
[INFO ] 2024-08-27 12:22:57.940 - [任务 1] - Start task milestones: 66cd48324d53ab4c5d531ccc(任务 1) 
[INFO ] 2024-08-27 12:22:57.940 - [任务 1] - Task initialization... 
[INFO ] 2024-08-27 12:22:58.052 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-27 12:22:58.052 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-27 12:22:58.084 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:58.085 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:22:58.085 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:58.085 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:22:58.276 - [任务 1][DM] - Source node "DM" read batch size: 100 
[INFO ] 2024-08-27 12:22:58.276 - [任务 1][DM] - Source node "DM" event queue capacity: 200 
[INFO ] 2024-08-27 12:22:58.277 - [任务 1][DM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-27 12:22:58.285 - [任务 1][DM] - batch offset found: {"BMSQL_HISTORY_TEST1":{"batch_read_connector_status":"OVER"},"BMSQL_HISTORY":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:58.295 - [任务 1] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-27 12:22:58.305 - [任务 1][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-27 12:22:58.305 - [任务 1][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-27 12:22:58.339 - [任务 1][DM] - Incremental sync starting... 
[INFO ] 2024-08-27 12:22:58.340 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 12:22:58.340 - [任务 1][DM] - Starting stream read, table list: [BMSQL_HISTORY_TEST1, BMSQL_HISTORY], offset: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:22:58.453 - [任务 1][DM] - Found new table(s): [BMSQL_HISTORY_TEST2] 
[INFO ] 2024-08-27 12:22:58.453 - [任务 1][DM] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-08-27_10-42-00.log 
[INFO ] 2024-08-27 12:22:58.521 - [任务 1][DM] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-08-27 12:22:58.521 - [任务 1][DM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@6282d8b5: {"table":{"id":"BMSQL_HISTORY_TEST2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"ID"}],"name":"INDEX33557311","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"BMSQL_HISTORY_TEST2","nameFieldMap":{"ID":{"autoInc":false,"dataType":"INTEGER","name":"ID","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"NAME":{"autoInc":false,"dataType":"VARCHAR(24)","name":"NAME","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"bytes":24,"defaultValue":8188,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"ID"}],"indexMap":{"ID":{"fieldAsc":true,"name":"ID"}},"unique":true}},"tableId":"BMSQL_HISTORY_TEST2","type":206} 
[INFO ] 2024-08-27 12:22:58.598 - [任务 1][DM] - Create new table in memory, qualified name: T_dameng_io_tapdata_1_0-SNAPSHOT_BMSQL_HISTORY_TEST2_66cd47244d53ab4c5d531ac3_66cd48324d53ab4c5d531ccc 
[INFO ] 2024-08-27 12:22:58.598 - [任务 1][DM] - Create new table schema transform finished: TapTable id BMSQL_HISTORY_TEST2 name BMSQL_HISTORY_TEST2 storageEngine null charset null number of fields 2 
[INFO ] 2024-08-27 12:22:58.598 - [任务 1][DM] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-08-27 12:22:58.598 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:58.604 - [任务 1][DM] - 【single miner】add log miner sql: null 
[INFO ] 2024-08-27 12:22:58.613 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  
[WARN ] 2024-08-27 12:22:58.614 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[ERROR] 2024-08-27 12:22:58.629 - [任务 1][DM] - Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@43492099, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  <-- Error Message -->
Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@43492099, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 

<-- Simple Stack Trace -->
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...

<-- Full Stack Trace -->
dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.restartPdkConnector(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleNewTables(HazelcastSourcePdkBaseNode.java:935)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$null$13(HazelcastSourcePdkBaseNode.java:830)
	at com.tapdata.constant.LockUtil.runWithLock(LockUtil.java:29)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$handleTableMonitorResult$14(HazelcastSourcePdkBaseNode.java:794)
	at io.tapdata.flow.engine.V2.monitor.impl.TableMonitor.consume(TableMonitor.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleTableMonitorResult(HazelcastSourcePdkBaseNode.java:789)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at dm.jdbc.driver.DBError.throwException(DBError.java:696)
	at dm.jdbc.b.b.o.E(MSG.java:560)
	at dm.jdbc.b.b.o.B(MSG.java:520)
	at dm.jdbc.b.b.o.A(MSG.java:501)
	at dm.jdbc.b.a.a(DBAccess.java:230)
	at dm.jdbc.b.a.a(DBAccess.java:701)
	at dm.jdbc.driver.DmdbStatement.executeInner(DmdbStatement.java:723)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:189)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:181)
	at dm.jdbc.driver.DmdbStatement.execute(DmdbStatement.java:1230)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.stopMiner(DamengLogMiner.java:717)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.stopMiner(SingleDamengLogMiner.java:147)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.closeCdcRunner(DamengCdcRunner.java:43)
	at io.tapdata.connector.dameng.DamengConnector.onStop(DamengConnector.java:106)
	at io.tapdata.base.ConnectorBase.stop(ConnectorBase.java:320)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorStop(ConnectorNode.java:104)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$restartPdkConnector$23(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 21 more

[INFO ] 2024-08-27 12:22:58.632 - [任务 1][DM] - Job suspend in error handle 
[INFO ] 2024-08-27 12:22:58.848 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] running status set to false 
[INFO ] 2024-08-27 12:22:58.848 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:22:58.856 - [任务 1][DM] - PDK connector node stopped: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:58.856 - [任务 1][DM] - PDK connector node released: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:22:58.856 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] schema data cleaned 
[INFO ] 2024-08-27 12:22:58.857 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] monitor closed 
[INFO ] 2024-08-27 12:22:58.860 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] close complete, cost 15 ms 
[INFO ] 2024-08-27 12:22:58.860 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] running status set to false 
[INFO ] 2024-08-27 12:22:58.879 - [任务 1][DM] - Incremental sync completed 
[INFO ] 2024-08-27 12:22:58.880 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: Find schema failed, message: Table name "BMSQL_HISTORY_TEST1" not exists, qualified name: null tableNameAndQualifiedNameMap: {} 
[INFO ] 2024-08-27 12:22:58.882 - [任务 1][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:58.882 - [任务 1][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:22:58.882 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] schema data cleaned 
[INFO ] 2024-08-27 12:22:58.882 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] monitor closed 
[INFO ] 2024-08-27 12:22:58.883 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] close complete, cost 22 ms 
[INFO ] 2024-08-27 12:23:02.974 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-27 12:23:02.974 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@294e06e9 
[INFO ] 2024-08-27 12:23:02.976 - [任务 1] - Stop task milestones: 66cd48324d53ab4c5d531ccc(任务 1)  
[INFO ] 2024-08-27 12:23:03.111 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-27 12:23:03.111 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-27 12:23:03.111 - [任务 1] - Remove memory task client succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:23:03.112 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:23:03.152 - [任务 1] - Resume task[任务 1] 
[INFO ] 2024-08-27 12:23:03.153 - [任务 1] - Start task milestones: 66cd48324d53ab4c5d531ccc(任务 1) 
[INFO ] 2024-08-27 12:23:03.252 - [任务 1] - Task initialization... 
[INFO ] 2024-08-27 12:23:03.252 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-27 12:23:03.310 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-27 12:23:03.310 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:23:03.310 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:23:03.310 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:23:03.310 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:23:03.501 - [任务 1][DM] - Source node "DM" read batch size: 100 
[INFO ] 2024-08-27 12:23:03.510 - [任务 1][DM] - Source node "DM" event queue capacity: 200 
[INFO ] 2024-08-27 12:23:03.510 - [任务 1][DM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-27 12:23:03.516 - [任务 1][DM] - batch offset found: {"BMSQL_HISTORY_TEST1":{"batch_read_connector_status":"OVER"},"BMSQL_HISTORY":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:23:03.516 - [任务 1] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-27 12:23:03.541 - [任务 1][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-27 12:23:03.541 - [任务 1][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-27 12:23:03.583 - [任务 1][DM] - Found new table(s): [BMSQL_HISTORY_TEST2] 
[INFO ] 2024-08-27 12:23:03.590 - [任务 1][DM] - Incremental sync starting... 
[INFO ] 2024-08-27 12:23:03.590 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 12:23:03.706 - [任务 1][DM] - Starting stream read, table list: [BMSQL_HISTORY_TEST1, BMSQL_HISTORY], offset: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:23:03.707 - [任务 1][DM] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-08-27_10-42-00.log 
[INFO ] 2024-08-27 12:23:03.712 - [任务 1][DM] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-08-27 12:23:03.714 - [任务 1][DM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@1192b61f: {"table":{"id":"BMSQL_HISTORY_TEST2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"ID"}],"name":"INDEX33557311","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"BMSQL_HISTORY_TEST2","nameFieldMap":{"ID":{"autoInc":false,"dataType":"INTEGER","name":"ID","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"NAME":{"autoInc":false,"dataType":"VARCHAR(24)","name":"NAME","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"bytes":24,"defaultValue":8188,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"ID"}],"indexMap":{"ID":{"fieldAsc":true,"name":"ID"}},"unique":true}},"tableId":"BMSQL_HISTORY_TEST2","type":206} 
[INFO ] 2024-08-27 12:23:03.769 - [任务 1][DM] - Create new table in memory, qualified name: T_dameng_io_tapdata_1_0-SNAPSHOT_BMSQL_HISTORY_TEST2_66cd47244d53ab4c5d531ac3_66cd48324d53ab4c5d531ccc 
[INFO ] 2024-08-27 12:23:03.769 - [任务 1][DM] - Create new table schema transform finished: TapTable id BMSQL_HISTORY_TEST2 name BMSQL_HISTORY_TEST2 storageEngine null charset null number of fields 2 
[INFO ] 2024-08-27 12:23:03.770 - [任务 1][DM] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-08-27 12:23:03.770 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:23:03.781 - [任务 1][DM] - 【single miner】add log miner sql: null 
[INFO ] 2024-08-27 12:23:03.781 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  
[WARN ] 2024-08-27 12:23:03.789 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[ERROR] 2024-08-27 12:23:03.793 - [任务 1][DM] - Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@671150c8, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  <-- Error Message -->
Handle table monitor result failed, result: io.tapdata.flow.engine.V2.monitor.impl.TableMonitor$TableResult@671150c8, error: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 

<-- Simple Stack Trace -->
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	dm.jdbc.driver.DBError.throwException(DBError.java:696)
	dm.jdbc.b.b.o.E(MSG.java:560)
	dm.jdbc.b.b.o.B(MSG.java:520)
	dm.jdbc.b.b.o.A(MSG.java:501)
	dm.jdbc.b.a.a(DBAccess.java:230)
	...

<-- Full Stack Trace -->
dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.restartPdkConnector(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleNewTables(HazelcastSourcePdkBaseNode.java:935)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$null$13(HazelcastSourcePdkBaseNode.java:830)
	at com.tapdata.constant.LockUtil.runWithLock(LockUtil.java:29)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$handleTableMonitorResult$14(HazelcastSourcePdkBaseNode.java:794)
	at io.tapdata.flow.engine.V2.monitor.impl.TableMonitor.consume(TableMonitor.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.handleTableMonitorResult(HazelcastSourcePdkBaseNode.java:789)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:308)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24 
	at dm.jdbc.driver.DBError.throwException(DBError.java:696)
	at dm.jdbc.b.b.o.E(MSG.java:560)
	at dm.jdbc.b.b.o.B(MSG.java:520)
	at dm.jdbc.b.b.o.A(MSG.java:501)
	at dm.jdbc.b.a.a(DBAccess.java:230)
	at dm.jdbc.b.a.a(DBAccess.java:701)
	at dm.jdbc.driver.DmdbStatement.executeInner(DmdbStatement.java:723)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:189)
	at dm.jdbc.driver.DmdbStatement.do_execute(DmdbStatement.java:181)
	at dm.jdbc.driver.DmdbStatement.execute(DmdbStatement.java:1230)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.stopMiner(DamengLogMiner.java:717)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.stopMiner(SingleDamengLogMiner.java:147)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.closeCdcRunner(DamengCdcRunner.java:43)
	at io.tapdata.connector.dameng.DamengConnector.onStop(DamengConnector.java:106)
	at io.tapdata.base.ConnectorBase.stop(ConnectorBase.java:320)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorStop(ConnectorNode.java:104)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$restartPdkConnector$23(HazelcastSourcePdkBaseNode.java:957)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 21 more

[INFO ] 2024-08-27 12:23:04.002 - [任务 1][DM] - Job suspend in error handle 
[INFO ] 2024-08-27 12:23:04.120 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] running status set to false 
[INFO ] 2024-08-27 12:23:04.120 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:23:04.120 - [任务 1][DM] - PDK connector node stopped: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:23:04.121 - [任务 1][DM] - PDK connector node released: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:23:04.121 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] schema data cleaned 
[INFO ] 2024-08-27 12:23:04.122 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] monitor closed 
[INFO ] 2024-08-27 12:23:04.125 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] close complete, cost 25 ms 
[INFO ] 2024-08-27 12:23:04.125 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] running status set to false 
[INFO ] 2024-08-27 12:23:04.140 - [任务 1][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:23:04.140 - [任务 1][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:23:04.140 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] schema data cleaned 
[INFO ] 2024-08-27 12:23:04.141 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] monitor closed 
[INFO ] 2024-08-27 12:23:04.141 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] close complete, cost 15 ms 
[INFO ] 2024-08-27 12:23:04.158 - [任务 1][DM] - Incremental sync completed 
[INFO ] 2024-08-27 12:23:04.160 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Find schema failed, message: null 
[INFO ] 2024-08-27 12:23:08.197 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-27 12:23:08.197 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2dc00a54 
[INFO ] 2024-08-27 12:23:08.198 - [任务 1] - Stop task milestones: 66cd48324d53ab4c5d531ccc(任务 1)  
[INFO ] 2024-08-27 12:23:08.338 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-27 12:23:08.338 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-27 12:23:08.342 - [任务 1] - Remove memory task client succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:23:08.360 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:23:08.360 - [任务 1] - Resume task[任务 1] 
[INFO ] 2024-08-27 12:23:08.389 - [任务 1] - Start task milestones: 66cd48324d53ab4c5d531ccc(任务 1) 
[INFO ] 2024-08-27 12:23:08.389 - [任务 1] - Task initialization... 
[INFO ] 2024-08-27 12:23:08.485 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-27 12:23:08.486 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-27 12:23:08.521 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:23:08.522 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] start preload schema,table counts: 2 
[INFO ] 2024-08-27 12:23:08.522 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:23:08.522 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] preload schema finished, cost 0 ms 
[INFO ] 2024-08-27 12:23:08.783 - [任务 1][Mongo] - Node(Mongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-27 12:23:08.783 - [任务 1][Mongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-27 12:23:08.840 - [任务 1][DM] - Source node "DM" read batch size: 100 
[INFO ] 2024-08-27 12:23:08.840 - [任务 1][DM] - Source node "DM" event queue capacity: 200 
[INFO ] 2024-08-27 12:23:08.841 - [任务 1][DM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-08-27 12:23:08.846 - [任务 1][DM] - batch offset found: {"BMSQL_HISTORY_TEST1":{"batch_read_connector_status":"OVER"},"BMSQL_HISTORY":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:23:08.912 - [任务 1] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-08-27 12:23:08.912 - [任务 1][DM] - Incremental sync starting... 
[INFO ] 2024-08-27 12:23:08.912 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 12:23:08.912 - [任务 1][DM] - Starting stream read, table list: [BMSQL_HISTORY_TEST1, BMSQL_HISTORY], offset: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:23:08.913 - [任务 1][DM] - Found new table(s): [BMSQL_HISTORY_TEST2] 
[INFO ] 2024-08-27 12:23:09.029 - [任务 1][DM] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-08-27 12:23:09.051 - [任务 1][DM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@7042261d: {"table":{"id":"BMSQL_HISTORY_TEST2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"ID"}],"name":"INDEX33557311","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"BMSQL_HISTORY_TEST2","nameFieldMap":{"ID":{"autoInc":false,"dataType":"INTEGER","name":"ID","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"NAME":{"autoInc":false,"dataType":"VARCHAR(24)","name":"NAME","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"bytes":24,"defaultValue":8188,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"ID"}],"indexMap":{"ID":{"fieldAsc":true,"name":"ID"}},"unique":true}},"tableId":"BMSQL_HISTORY_TEST2","type":206} 
[INFO ] 2024-08-27 12:23:09.051 - [任务 1][DM] - Create new table in memory, qualified name: T_dameng_io_tapdata_1_0-SNAPSHOT_BMSQL_HISTORY_TEST2_66cd47244d53ab4c5d531ac3_66cd48324d53ab4c5d531ccc 
[INFO ] 2024-08-27 12:23:09.088 - [任务 1][DM] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-08-27_10-42-00.log 
[INFO ] 2024-08-27 12:23:09.088 - [任务 1][DM] - Create new table schema transform finished: TapTable id BMSQL_HISTORY_TEST2 name BMSQL_HISTORY_TEST2 storageEngine null charset null number of fields 2 
[INFO ] 2024-08-27 12:23:09.089 - [任务 1][DM] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-08-27 12:23:09.089 - [任务 1][DM] - Log Miner is shutting down... 
[WARN ] 2024-08-27 12:23:09.156 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: 
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 12:23:09.158 - [任务 1][DM] - Log Miner is shutting down... 
[WARN ] 2024-08-27 12:23:09.297 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.lang.NullPointerException: null
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:769)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 12:23:09.297 - [任务 1][DM] - Starting batch read, table name: BMSQL_HISTORY_TEST2 
[INFO ] 2024-08-27 12:23:09.314 - [任务 1][DM] - Table BMSQL_HISTORY_TEST2 is going to be initial synced 
[INFO ] 2024-08-27 12:23:09.314 - [任务 1][DM] - Table [BMSQL_HISTORY_TEST2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-27 12:23:09.328 - [任务 1][DM] - Query table 'BMSQL_HISTORY_TEST2' counts: 0 
[INFO ] 2024-08-27 12:23:09.329 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 12:23:09.329 - [任务 1][DM] - Incremental sync starting... 
[INFO ] 2024-08-27 12:23:09.329 - [任务 1][DM] - Initial sync completed 
[INFO ] 2024-08-27 12:23:09.475 - [任务 1][DM] - Starting stream read, table list: [BMSQL_HISTORY_TEST1, BMSQL_HISTORY_TEST2, BMSQL_HISTORY], offset: {"sortString":null,"offsetValue":null,"lastScn":53795697,"pendingScn":53795697,"timestamp":1724732526921} 
[INFO ] 2024-08-27 12:23:09.475 - [任务 1][DM] - 【single miner】add log miner sql: /bak/archlog/ARCHIVE_LOCAL1_0x73A5C29D_EP0_2024-08-27_10-42-00.log 
[INFO ] 2024-08-27 12:23:09.810 - [任务 1][Mongo] - The target node received dll event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@7042261d: {"table":{"id":"BMSQL_HISTORY_TEST2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"ID"}],"name":"INDEX33557311","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"BMSQL_HISTORY_TEST2","nameFieldMap":{"ID":{"autoInc":false,"dataType":"INTEGER","name":"ID","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"NAME":{"autoInc":false,"dataType":"VARCHAR2(24)","name":"NAME","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"bytes":24,"defaultValue":8188,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"ID"}],"indexMap":{"ID":{"fieldAsc":true,"name":"ID"}},"unique":true}},"tableId":"BMSQL_HISTORY_TEST2","type":206}). Wait for all previous events to be processed 
[INFO ] 2024-08-27 12:23:10.420 - [任务 1][Mongo] - The target node refreshes the memory model according to the ddl event(io.tapdata.entity.event.ddl.table.TapCreateTableEvent@7042261d: {"table":{"id":"BMSQL_HISTORY_TEST2","indexList":[{"indexFields":[{"fieldAsc":true,"name":"ID"}],"name":"INDEX33557311","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"BMSQL_HISTORY_TEST2","nameFieldMap":{"ID":{"autoInc":false,"dataType":"INTEGER","name":"ID","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"NAME":{"autoInc":false,"dataType":"VARCHAR2(24)","name":"NAME","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"bytes":24,"defaultValue":8188,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"ID"}],"indexMap":{"ID":{"fieldAsc":true,"name":"ID"}},"unique":true}},"tableId":"BMSQL_HISTORY_TEST2","type":206}) 
[INFO ] 2024-08-27 12:24:09.220 - [任务 1][DM] - Log Miner is shutting down... 
[WARN ] 2024-08-27 12:24:09.428 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-29) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.lambda$getColumnType$3(DamengLogMiner.java:125)
	java.util.ArrayList.forEach(ArrayList.java:1259)
	...
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 12:25:09.287 - [任务 1][DM] - Log Miner is shutting down... 
[WARN ] 2024-08-27 12:25:09.488 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-29) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.lambda$getColumnType$3(DamengLogMiner.java:125)
	java.util.ArrayList.forEach(ArrayList.java:1259)
	...
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 12:26:09.345 - [任务 1][DM] - Log Miner is shutting down... 
[WARN ] 2024-08-27 12:26:09.564 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-29) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.lambda$getColumnType$3(DamengLogMiner.java:125)
	java.util.ArrayList.forEach(ArrayList.java:1259)
	...
 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 12:27:09.396 - [任务 1][DM] - Log Miner is shutting down... 
[WARN ] 2024-08-27 12:27:09.397 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-29) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.lambda$getColumnType$3(DamengLogMiner.java:125)
	java.util.ArrayList.forEach(ArrayList.java:1259)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 12:28:09.426 - [任务 1][DM] - Log Miner is shutting down... 
[WARN ] 2024-08-27 12:28:09.426 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-29) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.lambda$getColumnType$3(DamengLogMiner.java:125)
	java.util.ArrayList.forEach(ArrayList.java:1259)
	...
 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 12:29:09.444 - [任务 1][DM] - Log Miner is shutting down... 
[WARN ] 2024-08-27 12:29:09.645 - [任务 1][DM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-29) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.lambda$getColumnType$3(DamengLogMiner.java:125)
	java.util.ArrayList.forEach(ArrayList.java:1259)
	...
 - Remaining retry 8 time(s)
 - Period 60 second(s) 
[INFO ] 2024-08-27 12:29:30.134 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] running status set to false 
[INFO ] 2024-08-27 12:29:30.136 - [任务 1][DM] - Log Miner is shutting down... 
[INFO ] 2024-08-27 12:29:30.136 - [任务 1][DM] - Log Miner is shutting down... 
[WARN ] 2024-08-27 12:29:30.178 - [任务 1][DM] - Stop PDK connector node failed: Unknown PDK exception occur, dm.jdbc.driver.DMException: 当前无活动的 LogMiner 会话
-2846: DBMS_LOGMNR.END_LOGMNR line 24  | Associate id: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:29:30.179 - [任务 1][DM] - PDK connector node released: HazelcastSourcePdkDataNode-276fe50d-696d-43dd-8d2d-4ef9b85f507d 
[INFO ] 2024-08-27 12:29:30.179 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] schema data cleaned 
[INFO ] 2024-08-27 12:29:30.182 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] monitor closed 
[INFO ] 2024-08-27 12:29:30.190 - [任务 1][DM] - Incremental sync completed 
[INFO ] 2024-08-27 12:29:30.190 - [任务 1][DM] - Node DM[276fe50d-696d-43dd-8d2d-4ef9b85f507d] close complete, cost 52 ms 
[INFO ] 2024-08-27 12:29:30.192 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] running status set to false 
[INFO ] 2024-08-27 12:29:30.209 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-29) has been closed. 
[ERROR] 2024-08-27 12:29:30.209 - [任务 1][DM] - java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-29) has been closed. <-- Error Message -->
java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-29) has been closed.

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-29) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.lambda$getColumnType$3(DamengLogMiner.java:125)
	java.util.ArrayList.forEach(ArrayList.java:1259)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-29) has been closed.
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:790)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-29) has been closed.
	at io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.lambda$getColumnType$3(DamengLogMiner.java:144)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.getColumnType(DamengLogMiner.java:123)
	at io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.init(DamengLogMiner.java:92)
	at io.tapdata.connector.dameng.cdc.logminer.SingleDamengLogMiner.init(SingleDamengLogMiner.java:37)
	at io.tapdata.connector.dameng.cdc.DamengCdcRunner.init(DamengCdcRunner.java:26)
	at io.tapdata.connector.dameng.DamengConnector.streamRead(DamengConnector.java:406)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:769)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 18 more
Caused by: java.lang.RuntimeException: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-29) has been closed.
	at io.tapdata.common.exception.AbstractExceptionCollector.collectUserPwdInvalid(AbstractExceptionCollector.java:23)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:50)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.dameng.cdc.logminer.DamengLogMiner.lambda$getColumnType$3(DamengLogMiner.java:125)
	... 26 more
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-29) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 28 more

[INFO ] 2024-08-27 12:29:30.223 - [任务 1][Mongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:29:30.223 - [任务 1][Mongo] - PDK connector node released: HazelcastTargetPdkDataNode-5b153893-39db-4c4d-b418-85aa7d7d0a3c 
[INFO ] 2024-08-27 12:29:30.223 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] schema data cleaned 
[INFO ] 2024-08-27 12:29:30.223 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] monitor closed 
[INFO ] 2024-08-27 12:29:30.224 - [任务 1][Mongo] - Node Mongo[5b153893-39db-4c4d-b418-85aa7d7d0a3c] close complete, cost 33 ms 
[INFO ] 2024-08-27 12:29:32.255 - [任务 1][DM] - 【single miner】add log miner sql: null 
[INFO ] 2024-08-27 12:29:32.321 - [任务 1][DM] - Incremental sync completed 
[INFO ] 2024-08-27 12:29:32.322 - [任务 1][DM] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: dm.jdbc.driver.DMException: 打开文件失败
-4546: DBMS_LOGMNR.ADD_LOGFILE line 19  
[INFO ] 2024-08-27 12:29:33.618 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-27 12:29:33.619 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7d27d1a4 
[INFO ] 2024-08-27 12:29:33.622 - [任务 1] - Stop task milestones: 66cd48324d53ab4c5d531ccc(任务 1)  
[INFO ] 2024-08-27 12:29:33.756 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-08-27 12:29:33.756 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-08-27 12:29:33.804 - [任务 1] - Remove memory task client succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
[INFO ] 2024-08-27 12:29:33.804 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66cd48324d53ab4c5d531ccc] 
