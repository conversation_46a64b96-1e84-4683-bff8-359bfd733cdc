[TRACE] 2025-03-04 17:14:28.342 - [任务 37] - Start task milestones: 67c6c4386ed4fc58ed97f950(任务 37) 
[TRACE] 2025-03-04 17:14:28.512 - [任务 37] - Task initialization... 
[INFO ] 2025-03-04 17:14:28.513 - [任务 37] - Loading table structure completed 
[TRACE] 2025-03-04 17:14:28.607 - [任务 37] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-03-04 17:14:28.682 - [任务 37] - The engine receives 任务 37 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-04 17:14:28.683 - [任务 37] - Task started 
[TRACE] 2025-03-04 17:14:28.722 - [任务 37][Pg] - Node Pg[06a0f647-93d9-4c90-aa90-052c2486ebac] start preload schema,table counts: 2 
[TRACE] 2025-03-04 17:14:28.722 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] start preload schema,table counts: 2 
[TRACE] 2025-03-04 17:14:28.724 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] preload schema finished, cost 2 ms 
[TRACE] 2025-03-04 17:14:28.724 - [任务 37][Pg] - Node Pg[06a0f647-93d9-4c90-aa90-052c2486ebac] preload schema finished, cost 2 ms 
[INFO ] 2025-03-04 17:14:29.422 - [任务 37][Sybase-Wim] - Source connector(Sybase-Wim) initialization completed 
[TRACE] 2025-03-04 17:14:29.424 - [任务 37][Sybase-Wim] - Source node "Sybase-Wim" read batch size: 100 
[TRACE] 2025-03-04 17:14:29.424 - [任务 37][Sybase-Wim] - Source node "Sybase-Wim" event queue capacity: 200 
[TRACE] 2025-03-04 17:14:29.424 - [任务 37][Sybase-Wim] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-03-04 17:14:29.424 - [任务 37][Sybase-Wim] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-03-04 17:14:29.620 - [任务 37][Sybase-Wim] - logs holder exists, will skip valid operation 
[INFO ] 2025-03-04 17:14:29.621 - [任务 37][Sybase-Wim] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[TRACE] 2025-03-04 17:14:29.621 - [任务 37][Sybase-Wim] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-03-04 17:14:29.687 - [任务 37][Sybase-Wim] - Starting batch read from 2 tables 
[TRACE] 2025-03-04 17:14:29.690 - [任务 37][Sybase-Wim] - Initial sync started 
[INFO ] 2025-03-04 17:14:29.697 - [任务 37][Sybase-Wim] - Starting batch read from table: testIdenSmallInt 
[TRACE] 2025-03-04 17:14:29.699 - [任务 37][Sybase-Wim] - Table testIdenSmallInt is going to be initial synced 
[INFO ] 2025-03-04 17:14:29.819 - [任务 37][Pg] - Sink connector(Pg) initialization completed 
[TRACE] 2025-03-04 17:14:29.820 - [任务 37][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-04 17:14:29.820 - [任务 37][Pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-04 17:14:29.821 - [任务 37][Pg] - Apply table structure to target database 
[WARN ] 2025-03-04 17:14:29.904 - [任务 37][Pg] - Table testIdenSmallInt not exists, skip drop 
[INFO ] 2025-03-04 17:14:29.904 - [任务 37][Sybase-Wim] - Table testIdenSmallInt has been completed batch read 
[INFO ] 2025-03-04 17:14:29.904 - [任务 37][Sybase-Wim] - Starting batch read from table: testIden 
[TRACE] 2025-03-04 17:14:29.906 - [任务 37][Sybase-Wim] - Table testIden is going to be initial synced 
[TRACE] 2025-03-04 17:14:30.064 - [任务 37][Pg] - Table: testIdenSmallInt already exists Index: testIdenSm_id_7415746491 and will no longer create index 
[TRACE] 2025-03-04 17:14:30.064 - [任务 37][Pg] - Table: testIdenSmallInt already exists Index list: [TapIndex name testIdenSm_id_7415746491 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[WARN ] 2025-03-04 17:14:30.120 - [任务 37][Pg] - Table testIden not exists, skip drop 
[TRACE] 2025-03-04 17:14:30.122 - [任务 37][Sybase-Wim] - Query snapshot row size completed: Sybase-Wim(21da900a-2b14-4437-afde-4ef2d77fee21) 
[INFO ] 2025-03-04 17:14:30.161 - [任务 37][Sybase-Wim] - Table testIden has been completed batch read 
[TRACE] 2025-03-04 17:14:30.163 - [任务 37][Sybase-Wim] - Initial sync completed 
[INFO ] 2025-03-04 17:14:30.163 - [任务 37][Sybase-Wim] - Batch read completed. 
[TRACE] 2025-03-04 17:14:30.163 - [任务 37][Sybase-Wim] - Incremental sync starting... 
[TRACE] 2025-03-04 17:14:30.163 - [任务 37][Sybase-Wim] - Initial sync completed 
[TRACE] 2025-03-04 17:14:30.163 - [任务 37][Sybase-Wim] - Starting stream read, table list: [testIdenSmallInt, testIden], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-03-04 17:14:30.163 - [任务 37][Sybase-Wim] - Starting incremental sync using database log parser 
[INFO ] 2025-03-04 17:14:30.300 - [任务 37][Sybase-Wim] - startRid: 368416, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-04 17:14:30.300 - [任务 37][Sybase-Wim] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-04 17:14:30.300 - [任务 37][Sybase-Wim] - sybase offset in database is: startRid: 368416, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-03-04 17:14:30.301 - [任务 37][Sybase-Wim] - we will use offset in database, how ever, this is safe: startRid: 368416, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-04 17:14:30.332 - [任务 37][Sybase-Wim] - sybase cdc work with mode v1: auto rescan 
[TRACE] 2025-03-04 17:14:30.536 - [任务 37][Pg] - Table: testIden already exists Index list: [] 
[INFO ] 2025-03-04 17:14:30.566 - [任务 37][Sybase-Wim] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-03-04 17:14:30.566 - [任务 37][Sybase-Wim] - turned off automatic log cleaning for sybase 
[INFO ] 2025-03-04 17:14:30.621 - [任务 37][Sybase-Wim] - opened cdc for tables: {dbo=[testIdenSmallInt, testIden]} 
[INFO ] 2025-03-04 17:14:30.621 - [任务 37][Sybase-Wim] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-03-04 17:14:30.822 - [任务 37][Sybase-Wim] - logs holder exists, will skip valid operation 
[INFO ] 2025-03-04 17:14:31.179 - [任务 37][Sybase-Wim] - trans timestamp offset: 28800000 
[INFO ] 2025-03-04 17:14:31.180 - [任务 37][Sybase-Wim] - sybase cdc debug log is disabled 
[INFO ] 2025-03-04 17:14:31.180 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368416, rowId: 0 
[WARN ] 2025-03-04 17:14:31.589 - [任务 37][Pg] - Failed to get auto increment value for table testIden field id 
[TRACE] 2025-03-04 17:14:31.589 - [任务 37][Pg] - Process after table "testIden" initial sync finished, cost: 52 ms 
[WARN ] 2025-03-04 17:14:31.607 - [任务 37][Pg] - Failed to get auto increment value for table testIdenSmallInt field id 
[TRACE] 2025-03-04 17:14:31.607 - [任务 37][Pg] - Process after table "testIdenSmallInt" initial sync finished, cost: 72 ms 
[INFO ] 2025-03-04 17:14:31.811 - [任务 37][Pg] - Process after all table(s) initial sync are finished，table number: 2 
[INFO ] 2025-03-04 17:14:34.190 - [任务 37][Sybase-Wim] - rebuild statement with 368416, 0 
[INFO ] 2025-03-04 17:14:34.301 - [任务 37][Sybase-Wim] - uncommit trans size: 0 
[INFO ] 2025-03-04 17:14:34.337 - [任务 37][Sybase-Wim] - uncommit trans: {} 
[INFO ] 2025-03-04 17:14:34.338 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:14:37.388 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:14:37.589 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:14:40.637 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:14:40.738 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:14:43.779 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:14:43.968 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:14:46.973 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:14:47.141 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:14:50.149 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:14:50.563 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:14:53.391 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:14:53.521 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:14:56.524 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:14:56.931 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:14:59.778 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:15:00.187 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:15:03.034 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:15:03.439 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:15:06.272 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:15:06.535 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:15:09.582 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:15:10.192 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:15:13.017 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:15:13.313 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:15:16.311 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:15:16.715 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:15:19.754 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:15:19.846 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:15:22.881 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:15:23.287 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:15:26.332 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:15:26.506 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:15:29.513 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:15:29.918 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:15:32.955 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:15:33.125 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:15:36.131 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:15:36.537 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:15:39.596 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:15:39.648 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:15:42.684 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:15:42.866 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:15:45.870 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:15:46.280 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:15:49.105 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:15:49.281 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:15:52.287 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:15:52.441 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:15:55.447 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:15:55.641 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:15:58.645 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:15:59.053 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:16:01.891 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:16:02.092 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:16:05.137 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:16:05.250 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:16:08.251 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:16:08.368 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:16:11.369 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:16:11.571 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:16:14.626 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:16:14.831 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:16:17.884 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:16:17.919 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:16:20.951 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:16:21.357 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:16:24.396 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:16:24.599 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:16:27.435 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:16:27.640 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:16:30.679 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:16:30.836 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:16:33.889 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:16:34.062 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:16:37.072 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:16:37.257 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:16:40.270 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:16:40.557 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:16:43.593 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:16:44.004 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:16:47.041 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:16:47.130 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:16:50.136 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:16:50.335 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:16:53.364 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:16:53.566 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:16:56.627 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:16:56.801 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:16:59.807 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:17:00.212 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:17:03.045 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:17:03.306 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:17:06.341 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:17:06.752 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:17:09.557 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:17:09.964 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:17:12.778 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:17:13.185 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:17:16.033 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:17:16.235 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:17:19.276 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:17:19.482 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:17:22.530 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:17:22.736 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:17:25.737 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:17:25.738 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:17:28.742 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:17:28.933 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:17:31.943 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:17:32.349 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:17:35.418 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:17:35.621 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:17:38.471 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:17:38.675 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:17:41.720 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:17:41.925 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:17:44.974 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:17:45.181 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:17:48.013 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:17:48.298 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:17:51.354 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:17:51.529 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:17:54.535 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:17:54.808 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:17:57.861 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:17:58.250 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:18:01.281 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:18:01.689 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:18:04.505 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:18:04.673 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:18:07.684 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:18:08.091 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:18:10.940 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:18:11.348 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:18:14.395 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:18:14.598 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:18:17.428 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:18:17.843 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:18:20.893 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:18:21.097 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:18:23.940 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:18:24.146 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:18:27.151 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:18:27.352 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:18:30.411 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:18:30.602 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:18:33.652 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:18:33.839 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:18:36.846 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:18:37.136 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:18:40.140 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:18:40.548 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:18:43.378 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:18:43.543 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:18:46.546 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:18:46.742 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:18:49.744 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:18:50.146 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:18:52.972 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:18:53.274 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:18:56.278 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:18:56.887 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:18:59.931 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:19:00.003 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:19:03.045 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:19:03.214 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:19:06.218 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:19:06.395 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:19:09.397 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:19:09.599 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:19:12.608 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:19:12.815 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:19:15.854 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:19:16.029 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:19:19.032 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:19:19.231 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:19:22.238 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:19:22.649 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:19:25.497 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:19:25.908 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:19:28.946 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:19:28.967 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:19:31.977 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:19:32.384 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:19:35.212 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:19:35.617 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:19:38.418 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:19:38.594 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:19:41.604 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:19:42.009 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:19:45.059 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:19:45.165 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:19:48.170 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:19:48.358 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:19:51.381 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:19:51.587 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:19:54.646 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:19:55.053 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:19:58.096 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:19:58.135 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:20:01.178 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:20:01.374 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:20:04.426 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:20:04.620 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:20:07.670 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:20:07.836 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:20:10.876 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:20:11.285 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:20:14.121 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:20:14.322 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:20:17.327 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:20:17.739 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:20:20.549 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:20:20.711 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:20:23.714 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:20:23.907 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:20:26.915 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:20:27.527 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:20:30.377 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:20:30.632 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:20:33.684 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:20:34.086 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:20:36.934 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:20:37.254 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:20:40.257 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:20:40.663 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:20:43.719 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:20:43.742 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:20:46.779 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:20:46.970 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:20:49.973 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:20:50.216 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:20:53.230 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:20:53.654 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:20:56.471 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:20:56.697 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:20:59.724 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:21:00.132 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:21:02.955 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:21:03.208 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:21:06.244 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:21:06.449 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:21:09.504 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:21:09.606 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:21:12.650 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:21:12.831 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:21:15.864 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:21:16.066 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:21:19.118 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:21:19.221 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:21:22.224 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:21:22.415 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:21:25.421 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:21:25.605 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:21:28.611 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:21:28.808 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:21:31.861 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:21:32.034 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:21:35.067 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:21:35.473 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:21:38.506 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:21:38.573 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:21:41.582 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:21:41.823 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:21:44.826 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:21:45.064 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:21:48.071 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:21:48.328 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:21:51.354 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:21:51.542 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:21:54.582 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:21:54.754 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:21:57.791 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:21:57.994 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:22:01.049 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:22:01.254 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:22:04.300 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:22:04.506 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:22:07.339 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:22:07.753 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:22:10.806 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:22:10.807 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:22:13.844 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:22:14.046 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:22:17.090 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:22:17.278 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:22:20.285 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:22:20.458 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:22:23.499 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:22:23.680 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:22:26.683 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:22:26.864 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:22:29.868 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:22:30.187 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:22:33.229 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:22:33.636 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:22:36.685 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:22:36.767 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:22:39.813 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:22:40.018 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:22:43.062 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:22:43.246 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:22:46.252 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:22:46.656 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:22:49.506 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:22:49.699 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:22:52.705 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:22:53.110 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:22:56.153 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:22:56.213 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:22:59.256 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:22:59.662 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:23:02.710 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:23:02.816 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:23:05.824 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:23:06.229 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:23:09.272 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:23:09.575 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:23:12.579 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:23:12.834 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:23:15.841 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:23:16.013 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:23:19.018 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:23:19.426 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:23:22.259 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:23:22.449 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:23:25.454 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:23:25.863 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:23:28.669 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:23:28.917 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:23:31.957 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:23:32.365 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:23:35.404 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:23:35.502 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:23:38.508 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:23:38.915 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:23:41.973 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:23:42.146 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:23:45.147 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:23:45.342 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:23:48.373 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:23:48.579 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:23:51.620 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:23:51.823 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:23:54.869 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:23:54.914 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:23:57.961 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:23:58.148 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:24:01.151 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:24:01.393 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:24:04.395 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:24:04.590 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:24:07.596 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:24:07.786 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:24:10.795 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:24:11.205 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:24:14.236 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:24:14.302 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:24:17.349 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:24:17.550 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:24:20.598 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:24:20.801 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:24:23.805 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:24:24.212 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:24:27.263 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:24:27.346 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:24:30.353 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:24:30.584 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:24:33.597 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:24:33.900 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:24:36.904 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:24:37.316 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:24:40.369 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:24:40.440 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:24:43.446 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:24:43.857 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:24:46.707 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:24:46.895 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:24:49.903 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:24:50.222 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:24:53.257 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:24:53.453 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:24:56.455 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:24:56.697 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:24:59.724 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:24:59.907 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:25:02.941 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:25:03.352 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:25:06.179 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:25:06.374 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:25:09.380 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:25:09.804 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:25:12.608 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:25:13.017 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:25:15.817 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:25:16.072 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:25:19.103 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:25:19.509 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:25:22.555 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:25:22.965 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:25:25.773 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:25:25.957 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:25:28.962 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:25:29.370 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:25:32.407 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:25:32.570 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:25:35.579 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:25:35.990 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:25:39.041 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:25:39.242 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:25:42.296 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:25:42.448 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:25:45.451 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:25:45.859 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:25:48.905 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:25:49.112 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:25:51.942 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:25:52.197 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:25:55.230 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:25:55.435 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:25:58.486 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:25:58.691 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:26:01.740 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:26:01.856 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:26:04.862 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:26:05.267 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:26:08.318 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:26:08.449 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:26:11.456 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:26:11.860 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:26:14.892 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:26:14.893 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:26:17.897 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:26:18.305 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:26:21.345 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:26:21.346 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:26:24.348 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:26:24.544 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:26:27.546 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:26:27.740 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:26:30.745 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:26:31.152 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:26:34.206 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:26:34.323 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:26:37.333 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:26:37.753 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:26:40.596 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:26:40.771 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:26:43.811 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:26:44.219 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:26:47.268 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:26:47.470 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:26:50.465 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:26:50.465 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:26:53.471 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:26:53.883 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:26:56.683 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:26:57.090 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:26:59.909 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:27:00.317 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:27:03.147 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:27:03.349 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:27:06.395 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:27:06.512 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:27:09.538 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:27:09.947 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:27:12.779 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:27:13.187 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:27:16.020 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:27:16.286 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:27:19.317 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:27:19.501 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:27:22.507 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:27:22.920 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:27:25.728 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:27:25.890 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:27:28.894 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:27:29.083 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:27:32.086 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:27:32.255 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:27:35.256 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:27:35.453 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:27:38.457 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:27:38.630 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:27:41.630 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:27:41.819 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:27:44.824 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:27:45.230 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:27:48.052 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:27:48.455 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:27:51.274 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:27:51.471 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:27:54.476 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:27:54.671 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:27:57.678 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:27:57.858 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:28:00.860 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:28:01.148 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:28:04.153 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:28:04.348 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:28:07.349 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:28:07.954 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:28:10.761 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:28:11.173 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:28:14.017 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:28:14.428 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:28:17.474 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:28:17.494 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:28:20.499 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:28:20.657 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[INFO ] 2025-03-04 17:28:23.684 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:28:24.092 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 81 
[TRACE] 2025-03-04 17:28:24.748 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] running status set to false 
[INFO ] 2025-03-04 17:28:24.748 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 81 
[INFO ] 2025-03-04 17:28:24.951 - [任务 37][Sybase-Wim] - Log Miner is shutting down... 
[TRACE] 2025-03-04 17:28:24.971 - [任务 37][Sybase-Wim] - PDK connector node stopped: HazelcastSourcePdkDataNode_21da900a-2b14-4437-afde-4ef2d77fee21_1741079669318 
[TRACE] 2025-03-04 17:28:24.971 - [任务 37][Sybase-Wim] - PDK connector node released: HazelcastSourcePdkDataNode_21da900a-2b14-4437-afde-4ef2d77fee21_1741079669318 
[TRACE] 2025-03-04 17:28:24.971 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] schema data cleaned 
[TRACE] 2025-03-04 17:28:24.971 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] monitor closed 
[TRACE] 2025-03-04 17:28:24.976 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] close complete, cost 230 ms 
[TRACE] 2025-03-04 17:28:24.976 - [任务 37][Pg] - Node Pg[06a0f647-93d9-4c90-aa90-052c2486ebac] running status set to false 
[TRACE] 2025-03-04 17:28:24.991 - [任务 37][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_06a0f647-93d9-4c90-aa90-052c2486ebac_1741079669463 
[TRACE] 2025-03-04 17:28:24.991 - [任务 37][Pg] - PDK connector node released: HazelcastTargetPdkDataNode_06a0f647-93d9-4c90-aa90-052c2486ebac_1741079669463 
[TRACE] 2025-03-04 17:28:24.991 - [任务 37][Pg] - Node Pg[06a0f647-93d9-4c90-aa90-052c2486ebac] schema data cleaned 
[TRACE] 2025-03-04 17:28:24.991 - [任务 37][Pg] - Node Pg[06a0f647-93d9-4c90-aa90-052c2486ebac] monitor closed 
[TRACE] 2025-03-04 17:28:24.992 - [任务 37][Pg] - Node Pg[06a0f647-93d9-4c90-aa90-052c2486ebac] close complete, cost 15 ms 
[TRACE] 2025-03-04 17:28:25.199 - [任务 37][Sybase-Wim] - Incremental sync completed 
[TRACE] 2025-03-04 17:28:26.288 - [任务 37] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-04 17:28:26.290 - [任务 37] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7f3feaa5 
[TRACE] 2025-03-04 17:28:26.290 - [任务 37] - Stop task milestones: 67c6c4386ed4fc58ed97f950(任务 37)  
[TRACE] 2025-03-04 17:28:26.426 - [任务 37] - Stopped task aspect(s) 
[TRACE] 2025-03-04 17:28:26.426 - [任务 37] - Snapshot order controller have been removed 
[INFO ] 2025-03-04 17:28:26.428 - [任务 37] - Task stopped. 
[TRACE] 2025-03-04 17:28:26.466 - [任务 37] - Remove memory task client succeed, task: 任务 37[67c6c4386ed4fc58ed97f950] 
[TRACE] 2025-03-04 17:28:26.466 - [任务 37] - Destroy memory task client cache succeed, task: 任务 37[67c6c4386ed4fc58ed97f950] 
[TRACE] 2025-03-04 17:29:42.790 - [任务 37] - Start task milestones: 67c6c4386ed4fc58ed97f950(任务 37) 
[TRACE] 2025-03-04 17:29:42.836 - [任务 37] - Task initialization... 
[INFO ] 2025-03-04 17:29:43.653 - [任务 37] - Loading table structure completed 
[TRACE] 2025-03-04 17:29:43.709 - [任务 37] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-04 17:29:43.709 - [任务 37] - The engine receives 任务 37 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-04 17:29:43.842 - [任务 37] - Task started 
[TRACE] 2025-03-04 17:29:43.844 - [任务 37][Pg] - Node Pg[06a0f647-93d9-4c90-aa90-052c2486ebac] start preload schema,table counts: 107 
[TRACE] 2025-03-04 17:29:43.844 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] start preload schema,table counts: 107 
[TRACE] 2025-03-04 17:29:43.844 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] preload schema finished, cost 1 ms 
[TRACE] 2025-03-04 17:29:43.844 - [任务 37][Pg] - Node Pg[06a0f647-93d9-4c90-aa90-052c2486ebac] preload schema finished, cost 1 ms 
[INFO ] 2025-03-04 17:29:44.693 - [任务 37][Sybase-Wim] - Source connector(Sybase-Wim) initialization completed 
[TRACE] 2025-03-04 17:29:44.693 - [任务 37][Sybase-Wim] - Source node "Sybase-Wim" read batch size: 100 
[TRACE] 2025-03-04 17:29:44.693 - [任务 37][Sybase-Wim] - Source node "Sybase-Wim" event queue capacity: 200 
[TRACE] 2025-03-04 17:29:44.694 - [任务 37][Sybase-Wim] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-03-04 17:29:44.694 - [任务 37][Sybase-Wim] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-03-04 17:29:44.861 - [任务 37][Sybase-Wim] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-03-04 17:29:44.861 - [任务 37][Pg] - Sink connector(Pg) initialization completed 
[TRACE] 2025-03-04 17:29:44.861 - [任务 37][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-04 17:29:44.861 - [任务 37][Pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-04 17:29:45.063 - [任务 37][Pg] - Apply table structure to target database 
[INFO ] 2025-03-04 17:29:45.173 - [任务 37][Sybase-Wim] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[TRACE] 2025-03-04 17:29:45.173 - [任务 37][Sybase-Wim] - Before the event is output to the target from source, it will automatically block field changes 
[TRACE] 2025-03-04 17:29:45.195 - [任务 37][Pg] - Table: pg_yyy_bmsql_new_order already exists Index: pg_yyy_bms_17120060991 and will no longer create index 
[TRACE] 2025-03-04 17:29:45.397 - [任务 37][Pg] - Table: pg_yyy_bmsql_new_order already exists Index list: [TapIndex name pg_yyy_bms_17120060991 indexFields: [TapIndexField name no_w_id fieldAsc true indexType null; TapIndexField name no_d_id fieldAsc true indexType null; TapIndexField name no_o_id fieldAsc true indexType null; ]] 
[INFO ] 2025-03-04 17:29:45.397 - [任务 37][Sybase-Wim] - Starting batch read from 107 tables 
[TRACE] 2025-03-04 17:29:45.422 - [任务 37][Sybase-Wim] - Initial sync started 
[INFO ] 2025-03-04 17:29:45.422 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_yyy_bmsql_new_order 
[TRACE] 2025-03-04 17:29:45.422 - [任务 37][Pg] - Table: td_char_not_null already exists Index list: [] 
[TRACE] 2025-03-04 17:29:45.624 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_new_order is going to be initial synced 
[TRACE] 2025-03-04 17:29:45.663 - [任务 37][Pg] - Table: bmsql_customer will create Index: TapIndex name bmsql_cust_7165265551 indexFields: [TapIndexField name c_id fieldAsc true indexType null; TapIndexField name c_d_id fieldAsc true indexType null; TapIndexField name c_w_id fieldAsc true indexType null; ] 
[WARN ] 2025-03-04 17:29:45.726 - [任务 37][Pg] - Cluster index failed, table:bmsql_customer, index:bmsql_cust_7165265551 
[TRACE] 2025-03-04 17:29:45.726 - [任务 37][Pg] - Table: bmsql_customer create Index: bmsql_cust_7165265551 successfully, cost 67ms 
[TRACE] 2025-03-04 17:29:45.727 - [任务 37][Pg] - Table: bmsql_customer synchronize indexes completed, cost 165ms totally 
[TRACE] 2025-03-04 17:29:45.964 - [任务 37][Pg] - Table: pg_yyy_bmsql_history already exists Index: pg_yyy_bms_18400065551 and will no longer create index 
[TRACE] 2025-03-04 17:29:46.167 - [任务 37][Pg] - Table: pg_yyy_bmsql_history already exists Index list: [TapIndex name pg_yyy_bms_18400065551 indexFields: [TapIndexField name hist_id fieldAsc true indexType null; TapIndexField name h_c_id fieldAsc true indexType null; TapIndexField name h_c_d_id fieldAsc true indexType null; TapIndexField name h_c_w_id fieldAsc true indexType null; TapIndexField name h_d_id fieldAsc true indexType null; TapIndexField name h_w_id fieldAsc true indexType null; TapIndexField name h_amount fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:46.369 - [任务 37][Pg] - Table: testIden already exists Index list: [] 
[TRACE] 2025-03-04 17:29:46.427 - [任务 37][Pg] - Table: pg_sync_test already exists Index: pg_sync_te_20320072391 and will no longer create index 
[TRACE] 2025-03-04 17:29:46.428 - [任务 37][Pg] - Table: pg_sync_test already exists Index list: [TapIndex name pg_sync_te_20320072391 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:46.571 - [任务 37][Pg] - Table: bmsql_config already exists Index: bmsql_conf_6845264411 and will no longer create index 
[TRACE] 2025-03-04 17:29:46.571 - [任务 37][Pg] - Table: bmsql_config already exists Index list: [TapIndex name bmsql_conf_6845264411 indexFields: [TapIndexField name _id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:46.778 - [任务 37][Pg] - Table: pg_bmsql_item already exists Index: pg_bmsql_i_21280075811 and will no longer create index 
[TRACE] 2025-03-04 17:29:46.779 - [任务 37][Pg] - Table: pg_bmsql_item already exists Index list: [TapIndex name pg_bmsql_i_21280075811 indexFields: [TapIndexField name i_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:47.008 - [任务 37][Pg] - Table: pg_yyy_bmsql_stock already exists Index: pg_yyy_bms_765242751 and will no longer create index 
[TRACE] 2025-03-04 17:29:47.208 - [任务 37][Pg] - Table: pg_yyy_bmsql_stock already exists Index list: [TapIndex name pg_yyy_bms_765242751 indexFields: [TapIndexField name s_w_id fieldAsc true indexType null; TapIndexField name s_i_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:47.210 - [任务 37][Pg] - Table: BMSQL_ITEM already exists Index: BMSQL_ITEM_7805267831 and will no longer create index 
[TRACE] 2025-03-04 17:29:47.210 - [任务 37][Pg] - Table: BMSQL_ITEM already exists Index list: [TapIndex name BMSQL_ITEM_7805267831 indexFields: [TapIndexField name I_ID fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:47.472 - [任务 37][Pg] - Table: pg_bmsql_customer already exists Index: pg_bmsql_c_1085243891 and will no longer create index 
[TRACE] 2025-03-04 17:29:47.472 - [任务 37][Pg] - Table: pg_bmsql_customer already exists Index list: [TapIndex name pg_bmsql_c_1085243891 indexFields: [TapIndexField name c_w_id fieldAsc true indexType null; TapIndexField name c_d_id fieldAsc true indexType null; TapIndexField name c_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:47.673 - [任务 37][Pg] - Table: a_testMoneyv already exists Index: a_testMone_5850500891 and will no longer create index 
[TRACE] 2025-03-04 17:29:47.673 - [任务 37][Pg] - Table: a_testMoneyv already exists Index list: [TapIndex name a_testMone_5850500891 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:47.973 - [任务 37][Pg] - Table: tdText already exists Index: uidx_tdtext and will no longer create index 
[TRACE] 2025-03-04 17:29:47.973 - [任务 37][Pg] - Table: tdText already exists Index: uidx_td_text and will no longer create index 
[TRACE] 2025-03-04 17:29:47.974 - [任务 37][Pg] - Table: tdText already exists Index list: [TapIndex name uidx_tdtext indexFields: [TapIndexField name id fieldAsc true indexType null; ], TapIndex name uidx_td_text indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:48.248 - [任务 37][Pg] - Table: pg_yyy_bmsql_order_line already exists Index: pg_yyy_bms_2045247311 and will no longer create index 
[TRACE] 2025-03-04 17:29:48.248 - [任务 37][Pg] - Table: pg_yyy_bmsql_order_line already exists Index list: [TapIndex name pg_yyy_bms_2045247311 indexFields: [TapIndexField name ol_w_id fieldAsc true indexType null; TapIndexField name ol_d_id fieldAsc true indexType null; TapIndexField name ol_o_id fieldAsc true indexType null; TapIndexField name ol_number fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:48.415 - [任务 37][Pg] - Table: pg_xxx_bmsql_item already exists Index: pg_xxx_bms_2365248451 and will no longer create index 
[TRACE] 2025-03-04 17:29:48.415 - [任务 37][Pg] - Table: pg_xxx_bmsql_item already exists Index list: [TapIndex name pg_xxx_bms_2365248451 indexFields: [TapIndexField name i_id fieldAsc true indexType null; ]] 
[WARN ] 2025-03-04 17:29:48.428 - [任务 37][Pg] - Table nouniquepdktableTarget not exists, skip drop 
[TRACE] 2025-03-04 17:29:48.629 - [任务 37][Pg] - Table: nouniquepdktableTarget already exists Index list: [] 
[TRACE] 2025-03-04 17:29:48.856 - [任务 37][Pg] - Table: testBit already exists Index: testBit_id_1850486641 and will no longer create index 
[TRACE] 2025-03-04 17:29:48.857 - [任务 37][Pg] - Table: testBit already exists Index list: [TapIndex name testBit_id_1850486641 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:49.061 - [任务 37][Pg] - Table: BMSQL_OORDER_BACK2 already exists Index: BMSQL_OORD_8125268971 and will no longer create index 
[TRACE] 2025-03-04 17:29:49.062 - [任务 37][Pg] - Table: BMSQL_OORDER_BACK2 already exists Index list: [TapIndex name BMSQL_OORD_8125268971 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:49.332 - [任务 37][Pg] - Table: BMSQL_OORDER_BACK already exists Index: BMSQL_OORD_8445270111 and will no longer create index 
[TRACE] 2025-03-04 17:29:49.332 - [任务 37][Pg] - Table: BMSQL_OORDER_BACK already exists Index list: [TapIndex name BMSQL_OORD_8445270111 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:49.737 - [任务 37][Pg] - Table: nouniquepdktableTest already exists Index list: [] 
[TRACE] 2025-03-04 17:29:49.931 - [任务 37][Pg] - Table: testTimeStampTwoWithNoIndex already exists Index list: [] 
[TRACE] 2025-03-04 17:29:49.931 - [任务 37][Pg] - Table: pg_yyy_bmsql_customer already exists Index: pg_yyy_bms_5245258711 and will no longer create index 
[TRACE] 2025-03-04 17:29:49.931 - [任务 37][Pg] - Table: pg_yyy_bmsql_customer already exists Index list: [TapIndex name pg_yyy_bms_5245258711 indexFields: [TapIndexField name c_w_id fieldAsc true indexType null; TapIndexField name c_d_id fieldAsc true indexType null; TapIndexField name c_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:50.184 - [任务 37][Pg] - Table: pg_bmsql_oorder already exists Index: pg_bmsql_o_5565259851 and will no longer create index 
[TRACE] 2025-03-04 17:29:50.184 - [任务 37][Pg] - Table: pg_bmsql_oorder already exists Index list: [TapIndex name pg_bmsql_o_5565259851 indexFields: [TapIndexField name o_w_id fieldAsc true indexType null; TapIndexField name o_d_id fieldAsc true indexType null; TapIndexField name o_id fieldAsc true indexType null; ]] 
[WARN ] 2025-03-04 17:29:50.390 - [任务 37][Pg] - Table td_empty_string_frompg not exists, skip drop 
[TRACE] 2025-03-04 17:29:50.589 - [任务 37][Pg] - Table: td_empty_string_frompg already exists Index list: [] 
[TRACE] 2025-03-04 17:29:50.589 - [任务 37][Pg] - Table: test_td_null_col already exists Index: test_td_nu_4090494621 and will no longer create index 
[TRACE] 2025-03-04 17:29:50.589 - [任务 37][Pg] - Table: test_td_null_col already exists Index list: [TapIndex name test_td_nu_4090494621 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:50.777 - [任务 37][Pg] - Table: BMSQL_HISTORY already exists Index: BMSQL_HIST_10685278091 and will no longer create index 
[TRACE] 2025-03-04 17:29:50.777 - [任务 37][Pg] - Table: BMSQL_HISTORY already exists Index list: [TapIndex name BMSQL_HIST_10685278091 indexFields: [TapIndexField name _id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:51.137 - [任务 37][Pg] - Table: test_date_with_text already exists Index: test_date__id_16250537941 and will no longer create index 
[TRACE] 2025-03-04 17:29:51.137 - [任务 37][Pg] - Table: test_date_with_text already exists Index list: [TapIndex name test_date__id_16250537941 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:51.458 - [任务 37][Pg] - Table: td_timestamp_col already exists Index: td_timesta_id_4410495761 and will no longer create index 
[TRACE] 2025-03-04 17:29:51.458 - [任务 37][Pg] - Table: td_timestamp_col already exists Index list: [TapIndex name td_timesta_id_4410495761 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:51.662 - [任务 37][Pg] - Table: nouniquepdktable1 already exists Index list: [] 
[TRACE] 2025-03-04 17:29:51.867 - [任务 37][Pg] - Table: ap_standard_list_alias_OLD already exists Index list: [] 
[TRACE] 2025-03-04 17:29:52.116 - [任务 37][Pg] - Table: pg_bmsql_order_line already exists Index: pg_bmsql_o_17760063271 and will no longer create index 
[TRACE] 2025-03-04 17:29:52.117 - [任务 37][Pg] - Table: pg_bmsql_order_line already exists Index list: [TapIndex name pg_bmsql_o_17760063271 indexFields: [TapIndexField name ol_w_id fieldAsc true indexType null; TapIndexField name ol_d_id fieldAsc true indexType null; TapIndexField name ol_o_id fieldAsc true indexType null; TapIndexField name ol_number fieldAsc true indexType null; ]] 
[WARN ] 2025-03-04 17:29:52.438 - [任务 37][Pg] - Create index failed ERROR: relation "testTimeStampWithUniqueIndex" already exists, rename testTimeStampWithUniqueIndex to testTimeStampWithUniqueIndex_261a and retry ... 
[TRACE] 2025-03-04 17:29:52.552 - [任务 37][Pg] - Table: testTimeStampTwoWithIndex already exists Index: testTimeStampWithUniqueIndex and will no longer create index 
[TRACE] 2025-03-04 17:29:52.554 - [任务 37][Pg] - Table: testTimeStampTwoWithIndex already exists Index list: [TapIndex name testTimeStampWithUniqueIndex indexFields: [TapIndexField name id fieldAsc true indexType null; TapIndexField name testTimeStamp fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:52.963 - [任务 37][Pg] - Table: td_empty_string already exists Index list: [] 
[TRACE] 2025-03-04 17:29:53.034 - [任务 37][Pg] - Table: example already exists Index list: [] 
[TRACE] 2025-03-04 17:29:53.307 - [任务 37][Pg] - Table: BMSQL_DISTRICT already exists Index: BMSQL_DIST_9725274671 and will no longer create index 
[TRACE] 2025-03-04 17:29:53.307 - [任务 37][Pg] - Table: BMSQL_DISTRICT already exists Index list: [TapIndex name BMSQL_DIST_9725274671 indexFields: [TapIndexField name _id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:53.526 - [任务 37][Pg] - Table: pg_xxx_bmsql_config already exists Index: pg_xxx_bms_18720066691 and will no longer create index 
[TRACE] 2025-03-04 17:29:53.727 - [任务 37][Pg] - Table: pg_xxx_bmsql_config already exists Index list: [TapIndex name pg_xxx_bms_18720066691 indexFields: [TapIndexField name cfg_name fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:53.809 - [任务 37][Pg] - Table: BMSQL_CONFIG already exists Index: BMSQL_CONF_10045275811 and will no longer create index 
[TRACE] 2025-03-04 17:29:53.809 - [任务 37][Pg] - Table: BMSQL_CONFIG already exists Index list: [TapIndex name BMSQL_CONF_10045275811 indexFields: [TapIndexField name _id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:54.011 - [任务 37][Pg] - Table: bmsql_history already exists Index: pk_bmsql_history and will no longer create index 
[TRACE] 2025-03-04 17:29:54.011 - [任务 37][Pg] - Table: bmsql_history already exists Index list: [TapIndex name pk_bmsql_history indexFields: [TapIndexField name hist_id fieldAsc true indexType null; TapIndexField name h_c_id fieldAsc true indexType null; TapIndexField name h_c_d_id fieldAsc true indexType null; TapIndexField name h_c_w_id fieldAsc true indexType null; TapIndexField name h_d_id fieldAsc true indexType null; TapIndexField name h_w_id fieldAsc true indexType null; TapIndexField name h_amount fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:54.186 - [任务 37][Pg] - Table: pg_bmsql_stock already exists Index: pg_bmsql_s_20640073531 and will no longer create index 
[TRACE] 2025-03-04 17:29:54.186 - [任务 37][Pg] - Table: pg_bmsql_stock already exists Index list: [TapIndex name pg_bmsql_s_20640073531 indexFields: [TapIndexField name s_w_id fieldAsc true indexType null; TapIndexField name s_i_id fieldAsc true indexType null; ]] 
[WARN ] 2025-03-04 17:29:54.349 - [任务 37][Pg] - Table test_nouniquepdktable not exists, skip drop 
[TRACE] 2025-03-04 17:29:54.349 - [任务 37][Pg] - Table: test_nouniquepdktable already exists Index list: [] 
[TRACE] 2025-03-04 17:29:54.550 - [任务 37][Pg] - Table: td_only_text already exists Index list: [] 
[WARN ] 2025-03-04 17:29:54.705 - [任务 37][Pg] - Table a_testMoney not exists, skip drop 
[TRACE] 2025-03-04 17:29:54.705 - [任务 37][Pg] - Table: a_testMoney already exists Index: a_testMone_5530499751 and will no longer create index 
[TRACE] 2025-03-04 17:29:54.705 - [任务 37][Pg] - Table: a_testMoney already exists Index list: [TapIndex name a_testMone_5530499751 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:54.914 - [任务 37][Pg] - Table: pg_xxx_sync_test already exists Index: pg_xxx_syn_445241611 and will no longer create index 
[TRACE] 2025-03-04 17:29:54.914 - [任务 37][Pg] - Table: pg_xxx_sync_test already exists Index list: [TapIndex name pg_xxx_syn_445241611 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:55.166 - [任务 37][Pg] - Table: pg_xxx_bmsql_warehouse already exists Index: pg_xxx_bms_1405245031 and will no longer create index 
[TRACE] 2025-03-04 17:29:55.166 - [任务 37][Pg] - Table: pg_xxx_bmsql_warehouse already exists Index list: [TapIndex name pg_xxx_bms_1405245031 indexFields: [TapIndexField name w_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:55.401 - [任务 37][Pg] - Table: pg_bmsql_new_order already exists Index: pg_bmsql_n_1725246171 and will no longer create index 
[TRACE] 2025-03-04 17:29:55.401 - [任务 37][Pg] - Table: pg_bmsql_new_order already exists Index list: [TapIndex name pg_bmsql_n_1725246171 indexFields: [TapIndexField name no_w_id fieldAsc true indexType null; TapIndexField name no_d_id fieldAsc true indexType null; TapIndexField name no_o_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:55.571 - [任务 37][Pg] - Table: testNotDuplicate will create Index: TapIndex name idx_testDuplicate1 indexFields: [TapIndexField name user_corp_acc fieldAsc true indexType null; TapIndexField name hospital fieldAsc true indexType null; TapIndexField name labno fieldAsc true indexType null; ] 
[TRACE] 2025-03-04 17:29:55.613 - [任务 37][Pg] - Table: testNotDuplicate create Index: idx_testDuplicate1 successfully, cost 42ms 
[TRACE] 2025-03-04 17:29:55.613 - [任务 37][Pg] - Table: testNotDuplicate synchronize indexes completed, cost 101ms totally 
[TRACE] 2025-03-04 17:29:55.834 - [任务 37][Pg] - Table: pg_xxx_bmsql_history already exists Index: pg_xxx_bms_3005250731 and will no longer create index 
[TRACE] 2025-03-04 17:29:55.835 - [任务 37][Pg] - Table: pg_xxx_bmsql_history already exists Index list: [TapIndex name pg_xxx_bms_3005250731 indexFields: [TapIndexField name hist_id fieldAsc true indexType null; TapIndexField name h_c_id fieldAsc true indexType null; TapIndexField name h_c_d_id fieldAsc true indexType null; TapIndexField name h_c_w_id fieldAsc true indexType null; TapIndexField name h_d_id fieldAsc true indexType null; TapIndexField name h_w_id fieldAsc true indexType null; TapIndexField name h_amount fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:56.100 - [任务 37][Pg] - Table: pg_yyy_bmsql_config already exists Index: pg_yyy_bms_3325251871 and will no longer create index 
[TRACE] 2025-03-04 17:29:56.101 - [任务 37][Pg] - Table: pg_yyy_bmsql_config already exists Index list: [TapIndex name pg_yyy_bms_3325251871 indexFields: [TapIndexField name cfg_name fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:56.372 - [任务 37][Pg] - Table: BMSQL_NEW_ORDER already exists Index: BMSQL_NEW__9085272391 and will no longer create index 
[TRACE] 2025-03-04 17:29:56.373 - [任务 37][Pg] - Table: BMSQL_NEW_ORDER already exists Index list: [TapIndex name BMSQL_NEW__9085272391 indexFields: [TapIndexField name _id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:56.584 - [任务 37][Pg] - Table: bmsql_new_order already exists Index: pk_new_order and will no longer create index 
[TRACE] 2025-03-04 17:29:56.584 - [任务 37][Pg] - Table: bmsql_new_order already exists Index list: [TapIndex name pk_new_order indexFields: [TapIndexField name no_w_id fieldAsc true indexType null; TapIndexField name no_d_id fieldAsc true indexType null; TapIndexField name no_o_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:56.829 - [任务 37][Pg] - Table: pg_yyy_bmsql_district already exists Index: pg_yyy_bms_3645253011 and will no longer create index 
[TRACE] 2025-03-04 17:29:56.830 - [任务 37][Pg] - Table: pg_yyy_bmsql_district already exists Index list: [TapIndex name pg_yyy_bms_3645253011 indexFields: [TapIndexField name d_w_id fieldAsc true indexType null; TapIndexField name d_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:57.019 - [任务 37][Pg] - Table: pg_bmsql_warehouse already exists Index: pg_bmsql_w_3965254151 and will no longer create index 
[TRACE] 2025-03-04 17:29:57.022 - [任务 37][Pg] - Table: pg_bmsql_warehouse already exists Index list: [TapIndex name pg_bmsql_w_3965254151 indexFields: [TapIndexField name w_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:57.418 - [任务 37][Pg] - Table: message_code already exists Index: message_code_idx and will no longer create index 
[TRACE] 2025-03-04 17:29:57.418 - [任务 37][Pg] - Table: message_code already exists Index list: [TapIndex name message_code_idx indexFields: [TapIndexField name msgcode_labno fieldAsc true indexType null; TapIndexField name msgcode_type fieldAsc true indexType null; TapIndexField name msgcode_code fieldAsc true indexType null; TapIndexField name msgcode_hospital fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:57.603 - [任务 37][Pg] - Table: bmsql_warehouse already exists Index: pk_warehouse and will no longer create index 
[TRACE] 2025-03-04 17:29:57.603 - [任务 37][Pg] - Table: bmsql_warehouse already exists Index list: [TapIndex name pk_warehouse indexFields: [TapIndexField name w_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:57.821 - [任务 37][Pg] - Table: pg_xxx_bmsql_new_order already exists Index: pg_xxx_bms_4605256431 and will no longer create index 
[TRACE] 2025-03-04 17:29:57.822 - [任务 37][Pg] - Table: pg_xxx_bmsql_new_order already exists Index list: [TapIndex name pg_xxx_bms_4605256431 indexFields: [TapIndexField name no_w_id fieldAsc true indexType null; TapIndexField name no_d_id fieldAsc true indexType null; TapIndexField name no_o_id fieldAsc true indexType null; ]] 
[WARN ] 2025-03-04 17:29:58.026 - [任务 37][Pg] - Table nouniquepdktable not exists, skip drop 
[WARN ] 2025-03-04 17:29:58.048 - [任务 37][Pg] - Create index failed ERROR: column "_no_pk_hash" does not exist, please execute it manually [create unique index "IDX_uepdktableecb709d766d0" on "public"."nouniquepdktable"("_no_pk_hash" asc)] 
[TRACE] 2025-03-04 17:29:58.190 - [任务 37][Pg] - Table: nouniquepdktable will create Index: TapIndex name IDX_uepdktableecb709d766d0 indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] 
[WARN ] 2025-03-04 17:29:58.191 - [任务 37][Pg] - Create index failed ERROR: column "_no_pk_hash" does not exist, please execute it manually [create unique index "IDX_uepdktableecb709d766d0" on "public"."nouniquepdktable"("_no_pk_hash" asc)] 
[TRACE] 2025-03-04 17:29:58.191 - [任务 37][Pg] - Table: nouniquepdktable create Index: IDX_uepdktableecb709d766d0 successfully, cost 91ms 
[TRACE] 2025-03-04 17:29:58.191 - [任务 37][Pg] - Table: nouniquepdktable synchronize indexes completed, cost 142ms totally 
[WARN ] 2025-03-04 17:29:58.233 - [任务 37][Pg] - Table t1_example not exists, skip drop 
[TRACE] 2025-03-04 17:29:58.539 - [任务 37][Pg] - Table: t1_example already exists Index: IDX_t1_example91adf6dc7ff8 and will no longer create index 
[TRACE] 2025-03-04 17:29:58.539 - [任务 37][Pg] - Table: t1_example already exists Index list: [TapIndex name IDX_t1_example91adf6dc7ff8 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[WARN ] 2025-03-04 17:29:58.743 - [任务 37][Pg] - Table td_fact_sec_null not exists, skip drop 
[TRACE] 2025-03-04 17:29:58.850 - [任务 37][Pg] - Table: td_fact_sec_null already exists Index list: [] 
[TRACE] 2025-03-04 17:29:59.298 - [任务 37][Pg] - Table: testIdenSmallInt already exists Index: testIdenSm_id_7415746491 and will no longer create index 
[TRACE] 2025-03-04 17:29:59.298 - [任务 37][Pg] - Table: testIdenSmallInt already exists Index list: [TapIndex name testIdenSm_id_7415746491 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:59.564 - [任务 37][Pg] - Table: pg_xxx_bmsql_district already exists Index: pg_xxx_bms_17440062131 and will no longer create index 
[TRACE] 2025-03-04 17:29:59.565 - [任务 37][Pg] - Table: pg_xxx_bmsql_district already exists Index list: [TapIndex name pg_xxx_bms_17440062131 indexFields: [TapIndexField name d_w_id fieldAsc true indexType null; TapIndexField name d_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:29:59.865 - [任务 37][Pg] - Table: pg_bmsql_district already exists Index: pg_bmsql_d_18080064411 and will no longer create index 
[TRACE] 2025-03-04 17:29:59.866 - [任务 37][Pg] - Table: pg_bmsql_district already exists Index list: [TapIndex name pg_bmsql_d_18080064411 indexFields: [TapIndexField name d_w_id fieldAsc true indexType null; TapIndexField name d_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:00.161 - [任务 37][Pg] - Table: bmsql_item already exists Index: pk_item and will no longer create index 
[TRACE] 2025-03-04 17:30:00.162 - [任务 37][Pg] - Table: bmsql_item already exists Index list: [TapIndex name pk_item indexFields: [TapIndexField name i_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:00.589 - [任务 37][Pg] - Table: BMSQL_STOCK already exists Index: BMSQL_STOC_8765271251 and will no longer create index 
[TRACE] 2025-03-04 17:30:00.589 - [任务 37][Pg] - Table: BMSQL_STOCK already exists Index list: [TapIndex name BMSQL_STOC_8765271251 indexFields: [TapIndexField name S_W_ID fieldAsc true indexType null; TapIndexField name S_I_ID fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:00.964 - [任务 37][Pg] - Table: ap_standard_list_subset already exists Index: ap_standard_list_subset_Idx and will no longer create index 
[TRACE] 2025-03-04 17:30:00.964 - [任务 37][Pg] - Table: ap_standard_list_subset already exists Index list: [TapIndex name ap_standard_list_subset_Idx indexFields: [TapIndexField name subset_id fieldAsc true indexType null; TapIndexField name term_id fieldAsc true indexType null; TapIndexField name node_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:01.218 - [任务 37][Pg] - Table: testChar already exists Index: testChar_id_12605284931 and will no longer create index 
[TRACE] 2025-03-04 17:30:01.218 - [任务 37][Pg] - Table: testChar already exists Index list: [TapIndex name testChar_id_12605284931 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:01.459 - [任务 37][Pg] - Table: pg_xxx_bmsql_order_line already exists Index: pg_xxx_bms_19040067831 and will no longer create index 
[TRACE] 2025-03-04 17:30:01.459 - [任务 37][Pg] - Table: pg_xxx_bmsql_order_line already exists Index list: [TapIndex name pg_xxx_bms_19040067831 indexFields: [TapIndexField name ol_w_id fieldAsc true indexType null; TapIndexField name ol_d_id fieldAsc true indexType null; TapIndexField name ol_o_id fieldAsc true indexType null; TapIndexField name ol_number fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:01.802 - [任务 37][Pg] - Table: pg_yyy_bmsql_warehouse already exists Index: pg_yyy_bms_20000071251 and will no longer create index 
[TRACE] 2025-03-04 17:30:01.802 - [任务 37][Pg] - Table: pg_yyy_bmsql_warehouse already exists Index list: [TapIndex name pg_yyy_bms_20000071251 indexFields: [TapIndexField name w_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:02.120 - [任务 37][Pg] - Table: BMSQL_CUSTOMER already exists Index: BMSQL_CUST_10365276951 and will no longer create index 
[TRACE] 2025-03-04 17:30:02.120 - [任务 37][Pg] - Table: BMSQL_CUSTOMER already exists Index list: [TapIndex name BMSQL_CUST_10365276951 indexFields: [TapIndexField name _id fieldAsc true indexType null; ]] 
[WARN ] 2025-03-04 17:30:02.305 - [任务 37][Pg] - Table testTimeStamp2 not exists, skip drop 
[TRACE] 2025-03-04 17:30:02.305 - [任务 37][Pg] - Table: testTimeStamp2 already exists Index: testTimeSt_id_19770550481 and will no longer create index 
[TRACE] 2025-03-04 17:30:02.305 - [任务 37][Pg] - Table: testTimeStamp2 already exists Index list: [TapIndex name testTimeSt_id_19770550481 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:02.578 - [任务 37][Pg] - Table: bmsql_district already exists Index: pk_district and will no longer create index 
[TRACE] 2025-03-04 17:30:02.578 - [任务 37][Pg] - Table: bmsql_district already exists Index list: [TapIndex name pk_district indexFields: [TapIndexField name d_w_id fieldAsc true indexType null; TapIndexField name d_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:02.993 - [任务 37][Pg] - Table: QC_Sample_Map will create Index: TapIndex name QC_Sample_Map_primary_Idx indexFields: [TapIndexField name lab_no fieldAsc true indexType null; TapIndexField name analyser_no fieldAsc true indexType null; TapIndexField name request_no fieldAsc true indexType null; ] 
[TRACE] 2025-03-04 17:30:03.177 - [任务 37][Pg] - Table: QC_Sample_Map create Index: QC_Sample_Map_primary_Idx successfully, cost 338ms 
[TRACE] 2025-03-04 17:30:03.177 - [任务 37][Pg] - Table: QC_Sample_Map synchronize indexes completed, cost 400ms totally 
[WARN ] 2025-03-04 17:30:03.581 - [任务 37][Pg] - Create index failed ERROR: relation "ap_operation_list_Idx" already exists, rename ap_operation_list_Idx to ap_operation_list_Idx_c25a and retry ... 
[TRACE] 2025-03-04 17:30:03.622 - [任务 37][Pg] - Table: ap_operation_list already exists Index: ap_operation_list_Idx and will no longer create index 
[TRACE] 2025-03-04 17:30:03.623 - [任务 37][Pg] - Table: ap_operation_list already exists Index list: [TapIndex name ap_operation_list_Idx indexFields: [TapIndexField name term_id fieldAsc true indexType null; TapIndexField name snomed_code fieldAsc true indexType null; TapIndexField name snomed_class fieldAsc true indexType null; TapIndexField name snomed_seq fieldAsc true indexType null; TapIndexField name valid_from fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:03.859 - [任务 37][Pg] - Table: td_null_col already exists Index: td_null_co_id_11130519701 and will no longer create index 
[TRACE] 2025-03-04 17:30:03.859 - [任务 37][Pg] - Table: td_null_col already exists Index list: [TapIndex name td_null_co_id_11130519701 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:04.218 - [任务 37][Pg] - Table: a_td_empty_string already exists Index: IDX_pty_stringd7e35f45b28e and will no longer create index 
[TRACE] 2025-03-04 17:30:04.218 - [任务 37][Pg] - Table: a_td_empty_string already exists Index list: [TapIndex name IDX_pty_stringd7e35f45b28e indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:04.376 - [任务 37][Pg] - Table: td_test_varchar already exists Index: td_test_va_id_10810518561 and will no longer create index 
[TRACE] 2025-03-04 17:30:04.377 - [任务 37][Pg] - Table: td_test_varchar already exists Index list: [TapIndex name td_test_va_id_10810518561 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:04.839 - [任务 37][Pg] - Table: test_td_timestamp_col already exists Index: test_td_ti_5210498611 and will no longer create index 
[TRACE] 2025-03-04 17:30:04.839 - [任务 37][Pg] - Table: test_td_timestamp_col already exists Index list: [TapIndex name test_td_ti_5210498611 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:05.203 - [任务 37][Pg] - Table: pg_bmsql_history already exists Index: pg_bmsql_h_4285255291 and will no longer create index 
[TRACE] 2025-03-04 17:30:05.203 - [任务 37][Pg] - Table: pg_bmsql_history already exists Index list: [TapIndex name pg_bmsql_h_4285255291 indexFields: [TapIndexField name hist_id fieldAsc true indexType null; TapIndexField name h_c_id fieldAsc true indexType null; TapIndexField name h_c_d_id fieldAsc true indexType null; TapIndexField name h_c_w_id fieldAsc true indexType null; TapIndexField name h_d_id fieldAsc true indexType null; TapIndexField name h_w_id fieldAsc true indexType null; TapIndexField name h_amount fieldAsc true indexType null; ]] 
[WARN ] 2025-03-04 17:30:05.408 - [任务 37][Pg] - Table pg_w_td_fact_sec not exists, skip drop 
[TRACE] 2025-03-04 17:30:05.491 - [任务 37][Pg] - Table: pg_w_td_fact_sec already exists Index: TAPIDX_d_fact_sec2b6369fc4004 and will no longer create index 
[TRACE] 2025-03-04 17:30:05.491 - [任务 37][Pg] - Table: pg_w_td_fact_sec already exists Index list: [TapIndex name TAPIDX_d_fact_sec2b6369fc4004 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:05.701 - [任务 37][Pg] - Table: pg_bmsql_config already exists Index: pg_bmsql_c_4925257571 and will no longer create index 
[TRACE] 2025-03-04 17:30:05.701 - [任务 37][Pg] - Table: pg_bmsql_config already exists Index list: [TapIndex name pg_bmsql_c_4925257571 indexFields: [TapIndexField name cfg_name fieldAsc true indexType null; ]] 
[WARN ] 2025-03-04 17:30:05.874 - [任务 37][Pg] - Table t1_td_char_not_null not exists, skip drop 
[TRACE] 2025-03-04 17:30:05.874 - [任务 37][Pg] - Table: t1_td_char_not_null will create Index: TapIndex name IDX_r_not_null7427970e8e91 indexFields: [TapIndexField name id fieldAsc true indexType null; ] 
[TRACE] 2025-03-04 17:30:05.932 - [任务 37][Pg] - Table: t1_td_char_not_null create Index: IDX_r_not_null7427970e8e91 successfully, cost 58ms 
[TRACE] 2025-03-04 17:30:05.932 - [任务 37][Pg] - Table: t1_td_char_not_null synchronize indexes completed, cost 101ms totally 
[TRACE] 2025-03-04 17:30:06.221 - [任务 37][Pg] - Table: td_char0 already exists Index: uidx_td_char0 and will no longer create index 
[TRACE] 2025-03-04 17:30:06.221 - [任务 37][Pg] - Table: td_char0 already exists Index list: [TapIndex name uidx_td_char0 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:06.468 - [任务 37][Pg] - Table: BMSQL_WAREHOUSE already exists Index: BMSQL_WARE_7485266691 and will no longer create index 
[TRACE] 2025-03-04 17:30:06.469 - [任务 37][Pg] - Table: BMSQL_WAREHOUSE already exists Index list: [TapIndex name BMSQL_WARE_7485266691 indexFields: [TapIndexField name W_ID fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:07.049 - [任务 37][Pg] - Table: s2p_text_idt already exists Index: s2p_text_idt_unique_index and will no longer create index 
[TRACE] 2025-03-04 17:30:07.049 - [任务 37][Pg] - Table: s2p_text_idt already exists Index list: [TapIndex name s2p_text_idt_unique_index indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:07.380 - [任务 37][Pg] - Table: bmsql_stock already exists Index: pk_stock and will no longer create index 
[TRACE] 2025-03-04 17:30:07.380 - [任务 37][Pg] - Table: bmsql_stock already exists Index list: [TapIndex name pk_stock indexFields: [TapIndexField name s_w_id fieldAsc true indexType null; TapIndexField name s_i_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:07.581 - [任务 37][Pg] - Table: td_fact_sec already exists Index list: [] 
[TRACE] 2025-03-04 17:30:07.782 - [任务 37][Sybase-Wim] - Query snapshot row size completed: Sybase-Wim(21da900a-2b14-4437-afde-4ef2d77fee21) 
[TRACE] 2025-03-04 17:30:07.961 - [任务 37][Pg] - Table: s2p_datetime_idt already exists Index: s2p_datetime_idt_unique_index and will no longer create index 
[TRACE] 2025-03-04 17:30:07.961 - [任务 37][Pg] - Table: s2p_datetime_idt already exists Index: s2p_datetime_idt_cahr_unique_index and will no longer create index 
[TRACE] 2025-03-04 17:30:07.962 - [任务 37][Pg] - Table: s2p_datetime_idt already exists Index list: [TapIndex name s2p_datetime_idt_unique_index indexFields: [TapIndexField name id fieldAsc true indexType null; ], TapIndex name s2p_datetime_idt_cahr_unique_index indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:08.248 - [任务 37][Pg] - Table: sync_test already exists Index: sync_test_id_5760020521 and will no longer create index 
[TRACE] 2025-03-04 17:30:08.252 - [任务 37][Pg] - Table: sync_test already exists Index list: [TapIndex name sync_test_id_5760020521 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:08.599 - [任务 37][Pg] - Table: s2p_text already exists Index: s2p_text_unique_index and will no longer create index 
[TRACE] 2025-03-04 17:30:08.599 - [任务 37][Pg] - Table: s2p_text already exists Index list: [TapIndex name s2p_text_unique_index indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:08.836 - [任务 37][Pg] - Table: pg_yyy_bmsql_oorder already exists Index: pg_yyy_bms_19360068971 and will no longer create index 
[TRACE] 2025-03-04 17:30:08.837 - [任务 37][Pg] - Table: pg_yyy_bmsql_oorder already exists Index list: [TapIndex name pg_yyy_bms_19360068971 indexFields: [TapIndexField name o_w_id fieldAsc true indexType null; TapIndexField name o_d_id fieldAsc true indexType null; TapIndexField name o_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:09.076 - [任务 37][Pg] - Table: pg_yyy_bmsql_item already exists Index: pg_yyy_bms_19680070111 and will no longer create index 
[TRACE] 2025-03-04 17:30:09.076 - [任务 37][Pg] - Table: pg_yyy_bmsql_item already exists Index list: [TapIndex name pg_yyy_bms_19680070111 indexFields: [TapIndexField name i_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:09.322 - [任务 37][Pg] - Table: pg_xxx_bmsql_stock already exists Index: pg_xxx_bms_20960074671 and will no longer create index 
[TRACE] 2025-03-04 17:30:09.322 - [任务 37][Pg] - Table: pg_xxx_bmsql_stock already exists Index list: [TapIndex name pg_xxx_bms_20960074671 indexFields: [TapIndexField name s_w_id fieldAsc true indexType null; TapIndexField name s_i_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:09.542 - [任务 37][Pg] - Table: pg_xxx_bmsql_oorder already exists Index: pg_xxx_bms_125240471 and will no longer create index 
[TRACE] 2025-03-04 17:30:09.543 - [任务 37][Pg] - Table: pg_xxx_bmsql_oorder already exists Index list: [TapIndex name pg_xxx_bms_125240471 indexFields: [TapIndexField name o_w_id fieldAsc true indexType null; TapIndexField name o_d_id fieldAsc true indexType null; TapIndexField name o_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:09.775 - [任务 37][Pg] - Table: BMSQL_OORDER already exists Index: BMSQL_OORD_11005279231 and will no longer create index 
[TRACE] 2025-03-04 17:30:09.775 - [任务 37][Pg] - Table: BMSQL_OORDER already exists Index list: [TapIndex name BMSQL_OORD_11005279231 indexFields: [TapIndexField name _id fieldAsc true indexType null; ]] 
[WARN ] 2025-03-04 17:30:09.976 - [任务 37][Pg] - Table pg_w_td_fact_sec_t not exists, skip drop 
[TRACE] 2025-03-04 17:30:10.027 - [任务 37][Pg] - Table: pg_w_td_fact_sec_t already exists Index list: [] 
[TRACE] 2025-03-04 17:30:10.432 - [任务 37][Pg] - Table: testTimeStampWithNoIndex already exists Index list: [] 
[TRACE] 2025-03-04 17:30:10.536 - [任务 37][Pg] - Table: test1_testBit already exists Index: test1_test_6170502031 and will no longer create index 
[TRACE] 2025-03-04 17:30:10.536 - [任务 37][Pg] - Table: test1_testBit already exists Index list: [TapIndex name test1_test_6170502031 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:10.789 - [任务 37][Pg] - Table: bmsql_oorder already exists Index: pk_oorder and will no longer create index 
[TRACE] 2025-03-04 17:30:10.789 - [任务 37][Pg] - Table: bmsql_oorder will create Index: TapIndex name bmsql_oorder_idx1 indexFields: [TapIndexField name o_w_id fieldAsc true indexType null; TapIndexField name o_d_id fieldAsc true indexType null; TapIndexField name o_carrier_id fieldAsc true indexType null; TapIndexField name o_id fieldAsc true indexType null; ] 
[TRACE] 2025-03-04 17:30:10.877 - [任务 37][Pg] - Table: bmsql_oorder create Index: bmsql_oorder_idx1 successfully, cost 88ms 
[TRACE] 2025-03-04 17:30:10.877 - [任务 37][Pg] - Table: bmsql_oorder synchronize indexes completed, cost 147ms totally 
[TRACE] 2025-03-04 17:30:11.073 - [任务 37][Pg] - Table: testMoney already exists Index: testMoney_id_2170487781 and will no longer create index 
[TRACE] 2025-03-04 17:30:11.074 - [任务 37][Pg] - Table: testMoney already exists Index list: [TapIndex name testMoney_id_2170487781 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:11.340 - [任务 37][Pg] - Table: test1_td_null_col already exists Index: test1_td_n_11450520841 and will no longer create index 
[TRACE] 2025-03-04 17:30:11.341 - [任务 37][Pg] - Table: test1_td_null_col already exists Index list: [TapIndex name test1_td_n_11450520841 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[WARN ] 2025-03-04 17:30:11.543 - [任务 37][Pg] - Table testTimeStampTarget not exists, skip drop 
[TRACE] 2025-03-04 17:30:11.543 - [任务 37][Pg] - Table: testTimeStampTarget already exists Index: testTimeSt_id_18810547061 and will no longer create index 
[TRACE] 2025-03-04 17:30:11.543 - [任务 37][Pg] - Table: testTimeStampTarget already exists Index list: [TapIndex name testTimeSt_id_18810547061 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:11.745 - [任务 37][Pg] - Table: pg_xxx_bmsql_customer already exists Index: pg_xxx_bms_2685249591 and will no longer create index 
[TRACE] 2025-03-04 17:30:11.745 - [任务 37][Pg] - Table: pg_xxx_bmsql_customer already exists Index list: [TapIndex name pg_xxx_bms_2685249591 indexFields: [TapIndexField name c_w_id fieldAsc true indexType null; TapIndexField name c_d_id fieldAsc true indexType null; TapIndexField name c_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:11.954 - [任务 37][Pg] - Table: testidentity already exists Index: testidenti_2490488921 and will no longer create index 
[TRACE] 2025-03-04 17:30:11.954 - [任务 37][Pg] - Table: testidentity already exists Index list: [TapIndex name testidenti_2490488921 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:12.209 - [任务 37][Pg] - Table: BMSQL_ORDER_LINE already exists Index: BMSQL_ORDE_9405273531 and will no longer create index 
[TRACE] 2025-03-04 17:30:12.210 - [任务 37][Pg] - Table: BMSQL_ORDER_LINE already exists Index list: [TapIndex name BMSQL_ORDE_9405273531 indexFields: [TapIndexField name OL_W_ID fieldAsc true indexType null; TapIndexField name OL_D_ID fieldAsc true indexType null; TapIndexField name OL_O_ID fieldAsc true indexType null; TapIndexField name OL_NUMBER fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:12.474 - [任务 37][Pg] - Table: td_text already exists Index: uidx_td_text and will no longer create index 
[TRACE] 2025-03-04 17:30:12.474 - [任务 37][Pg] - Table: td_text already exists Index list: [TapIndex name uidx_td_text indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:12.695 - [任务 37][Pg] - Table: testTimeStamp already exists Index: testTimeSt_id_20090551621 and will no longer create index 
[TRACE] 2025-03-04 17:30:12.695 - [任务 37][Pg] - Table: testTimeStamp already exists Index list: [TapIndex name testTimeSt_id_20090551621 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:12.934 - [任务 37][Pg] - Table: bmsql_order_line already exists Index: pk_order_line and will no longer create index 
[TRACE] 2025-03-04 17:30:12.935 - [任务 37][Pg] - Table: bmsql_order_line already exists Index list: [TapIndex name pk_order_line indexFields: [TapIndexField name ol_w_id fieldAsc true indexType null; TapIndexField name ol_d_id fieldAsc true indexType null; TapIndexField name ol_o_id fieldAsc true indexType null; TapIndexField name ol_number fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:30:13.256 - [任务 37][Pg] - Table: s2p_datetime_idt_char already exists Index: s2p_datetime_idt_cahr_unique_index and will no longer create index 
[TRACE] 2025-03-04 17:30:13.256 - [任务 37][Pg] - Table: s2p_datetime_idt_char already exists Index list: [TapIndex name s2p_datetime_idt_cahr_unique_index indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[INFO ] 2025-03-04 17:30:15.184 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_new_order has been completed batch read 
[INFO ] 2025-03-04 17:30:15.184 - [任务 37][Sybase-Wim] - Starting batch read from table: td_char_not_null 
[TRACE] 2025-03-04 17:30:15.353 - [任务 37][Sybase-Wim] - Table td_char_not_null is going to be initial synced 
[INFO ] 2025-03-04 17:30:15.353 - [任务 37][Sybase-Wim] - Table td_char_not_null has been completed batch read 
[INFO ] 2025-03-04 17:30:15.353 - [任务 37][Sybase-Wim] - Starting batch read from table: bmsql_customer 
[TRACE] 2025-03-04 17:30:15.353 - [任务 37][Sybase-Wim] - Table bmsql_customer is going to be initial synced 
[INFO ] 2025-03-04 17:30:15.510 - [任务 37][Sybase-Wim] - Table bmsql_customer has been completed batch read 
[INFO ] 2025-03-04 17:30:15.511 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_yyy_bmsql_history 
[TRACE] 2025-03-04 17:30:15.511 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_history is going to be initial synced 
[INFO ] 2025-03-04 17:30:23.230 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_history has been completed batch read 
[INFO ] 2025-03-04 17:30:23.238 - [任务 37][Sybase-Wim] - Starting batch read from table: testIden 
[TRACE] 2025-03-04 17:30:23.238 - [任务 37][Sybase-Wim] - Table testIden is going to be initial synced 
[INFO ] 2025-03-04 17:30:23.545 - [任务 37][Sybase-Wim] - Table testIden has been completed batch read 
[INFO ] 2025-03-04 17:30:23.546 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_sync_test 
[TRACE] 2025-03-04 17:30:23.734 - [任务 37][Sybase-Wim] - Table pg_sync_test is going to be initial synced 
[INFO ] 2025-03-04 17:30:23.735 - [任务 37][Sybase-Wim] - Table pg_sync_test has been completed batch read 
[INFO ] 2025-03-04 17:30:23.735 - [任务 37][Sybase-Wim] - Starting batch read from table: bmsql_config 
[TRACE] 2025-03-04 17:30:23.920 - [任务 37][Sybase-Wim] - Table bmsql_config is going to be initial synced 
[INFO ] 2025-03-04 17:30:23.920 - [任务 37][Sybase-Wim] - Table bmsql_config has been completed batch read 
[INFO ] 2025-03-04 17:30:23.920 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_bmsql_item 
[TRACE] 2025-03-04 17:30:24.127 - [任务 37][Sybase-Wim] - Table pg_bmsql_item is going to be initial synced 
[INFO ] 2025-03-04 17:30:27.417 - [任务 37][Sybase-Wim] - Table pg_bmsql_item has been completed batch read 
[INFO ] 2025-03-04 17:30:27.418 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_yyy_bmsql_stock 
[TRACE] 2025-03-04 17:30:27.626 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_stock is going to be initial synced 
[INFO ] 2025-03-04 17:30:31.782 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_stock has been completed batch read 
[INFO ] 2025-03-04 17:30:31.783 - [任务 37][Sybase-Wim] - Starting batch read from table: BMSQL_ITEM 
[TRACE] 2025-03-04 17:30:31.783 - [任务 37][Sybase-Wim] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2025-03-04 17:30:32.019 - [任务 37][Sybase-Wim] - Table BMSQL_ITEM has been completed batch read 
[INFO ] 2025-03-04 17:30:32.019 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_bmsql_customer 
[TRACE] 2025-03-04 17:30:32.020 - [任务 37][Sybase-Wim] - Table pg_bmsql_customer is going to be initial synced 
[INFO ] 2025-03-04 17:30:36.450 - [任务 37][Sybase-Wim] - Table pg_bmsql_customer has been completed batch read 
[INFO ] 2025-03-04 17:30:36.451 - [任务 37][Sybase-Wim] - Starting batch read from table: a_testMoneyv 
[TRACE] 2025-03-04 17:30:36.634 - [任务 37][Sybase-Wim] - Table a_testMoneyv is going to be initial synced 
[INFO ] 2025-03-04 17:30:36.634 - [任务 37][Sybase-Wim] - Table a_testMoneyv has been completed batch read 
[INFO ] 2025-03-04 17:30:36.634 - [任务 37][Sybase-Wim] - Starting batch read from table: tdText 
[TRACE] 2025-03-04 17:30:36.634 - [任务 37][Sybase-Wim] - Table tdText is going to be initial synced 
[INFO ] 2025-03-04 17:30:36.855 - [任务 37][Sybase-Wim] - Table tdText has been completed batch read 
[INFO ] 2025-03-04 17:30:36.855 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_yyy_bmsql_order_line 
[TRACE] 2025-03-04 17:30:36.855 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_order_line is going to be initial synced 
[INFO ] 2025-03-04 17:31:29.664 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_order_line has been completed batch read 
[INFO ] 2025-03-04 17:31:29.667 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_xxx_bmsql_item 
[TRACE] 2025-03-04 17:31:29.667 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_item is going to be initial synced 
[INFO ] 2025-03-04 17:31:33.911 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_item has been completed batch read 
[INFO ] 2025-03-04 17:31:33.911 - [任务 37][Sybase-Wim] - Starting batch read from table: nouniquepdktableTarget 
[TRACE] 2025-03-04 17:31:33.911 - [任务 37][Sybase-Wim] - Table nouniquepdktableTarget is going to be initial synced 
[INFO ] 2025-03-04 17:31:34.177 - [任务 37][Sybase-Wim] - Table nouniquepdktableTarget has been completed batch read 
[INFO ] 2025-03-04 17:31:34.177 - [任务 37][Sybase-Wim] - Starting batch read from table: testBit 
[TRACE] 2025-03-04 17:31:34.177 - [任务 37][Sybase-Wim] - Table testBit is going to be initial synced 
[INFO ] 2025-03-04 17:31:34.382 - [任务 37][Sybase-Wim] - Table testBit has been completed batch read 
[INFO ] 2025-03-04 17:31:34.383 - [任务 37][Sybase-Wim] - Starting batch read from table: BMSQL_OORDER_BACK2 
[TRACE] 2025-03-04 17:31:34.383 - [任务 37][Sybase-Wim] - Table BMSQL_OORDER_BACK2 is going to be initial synced 
[INFO ] 2025-03-04 17:31:34.601 - [任务 37][Sybase-Wim] - Table BMSQL_OORDER_BACK2 has been completed batch read 
[INFO ] 2025-03-04 17:31:34.602 - [任务 37][Sybase-Wim] - Starting batch read from table: BMSQL_OORDER_BACK 
[TRACE] 2025-03-04 17:31:34.602 - [任务 37][Sybase-Wim] - Table BMSQL_OORDER_BACK is going to be initial synced 
[INFO ] 2025-03-04 17:31:34.819 - [任务 37][Sybase-Wim] - Table BMSQL_OORDER_BACK has been completed batch read 
[INFO ] 2025-03-04 17:31:34.820 - [任务 37][Sybase-Wim] - Starting batch read from table: nouniquepdktableTest 
[TRACE] 2025-03-04 17:31:34.820 - [任务 37][Sybase-Wim] - Table nouniquepdktableTest is going to be initial synced 
[INFO ] 2025-03-04 17:31:34.955 - [任务 37][Sybase-Wim] - Table nouniquepdktableTest has been completed batch read 
[INFO ] 2025-03-04 17:31:34.956 - [任务 37][Sybase-Wim] - Starting batch read from table: testTimeStampTwoWithNoIndex 
[TRACE] 2025-03-04 17:31:34.956 - [任务 37][Sybase-Wim] - Table testTimeStampTwoWithNoIndex is going to be initial synced 
[INFO ] 2025-03-04 17:31:35.188 - [任务 37][Sybase-Wim] - Table testTimeStampTwoWithNoIndex has been completed batch read 
[INFO ] 2025-03-04 17:31:35.189 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_yyy_bmsql_customer 
[TRACE] 2025-03-04 17:31:35.189 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_customer is going to be initial synced 
[INFO ] 2025-03-04 17:31:39.215 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_customer has been completed batch read 
[INFO ] 2025-03-04 17:31:39.215 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_bmsql_oorder 
[TRACE] 2025-03-04 17:31:39.418 - [任务 37][Sybase-Wim] - Table pg_bmsql_oorder is going to be initial synced 
[INFO ] 2025-03-04 17:31:54.511 - [任务 37][Sybase-Wim] - Table pg_bmsql_oorder has been completed batch read 
[INFO ] 2025-03-04 17:31:54.511 - [任务 37][Sybase-Wim] - Starting batch read from table: td_empty_string_frompg 
[TRACE] 2025-03-04 17:31:54.511 - [任务 37][Sybase-Wim] - Table td_empty_string_frompg is going to be initial synced 
[INFO ] 2025-03-04 17:31:54.666 - [任务 37][Sybase-Wim] - Table td_empty_string_frompg has been completed batch read 
[INFO ] 2025-03-04 17:31:54.667 - [任务 37][Sybase-Wim] - Starting batch read from table: test_td_null_col 
[TRACE] 2025-03-04 17:31:54.667 - [任务 37][Sybase-Wim] - Table test_td_null_col is going to be initial synced 
[INFO ] 2025-03-04 17:31:54.866 - [任务 37][Sybase-Wim] - Table test_td_null_col has been completed batch read 
[INFO ] 2025-03-04 17:31:54.866 - [任务 37][Sybase-Wim] - Starting batch read from table: BMSQL_HISTORY 
[TRACE] 2025-03-04 17:31:54.866 - [任务 37][Sybase-Wim] - Table BMSQL_HISTORY is going to be initial synced 
[INFO ] 2025-03-04 17:31:58.175 - [任务 37][Sybase-Wim] - Table BMSQL_HISTORY has been completed batch read 
[INFO ] 2025-03-04 17:31:58.177 - [任务 37][Sybase-Wim] - Starting batch read from table: test_date_with_text 
[TRACE] 2025-03-04 17:31:58.177 - [任务 37][Sybase-Wim] - Table test_date_with_text is going to be initial synced 
[INFO ] 2025-03-04 17:31:58.327 - [任务 37][Sybase-Wim] - Table test_date_with_text has been completed batch read 
[INFO ] 2025-03-04 17:31:58.327 - [任务 37][Sybase-Wim] - Starting batch read from table: td_timestamp_col 
[TRACE] 2025-03-04 17:31:58.327 - [任务 37][Sybase-Wim] - Table td_timestamp_col is going to be initial synced 
[INFO ] 2025-03-04 17:31:58.508 - [任务 37][Sybase-Wim] - Table td_timestamp_col has been completed batch read 
[INFO ] 2025-03-04 17:31:58.508 - [任务 37][Sybase-Wim] - Starting batch read from table: nouniquepdktable1 
[TRACE] 2025-03-04 17:31:58.508 - [任务 37][Sybase-Wim] - Table nouniquepdktable1 is going to be initial synced 
[INFO ] 2025-03-04 17:31:58.693 - [任务 37][Sybase-Wim] - Table nouniquepdktable1 has been completed batch read 
[INFO ] 2025-03-04 17:31:58.693 - [任务 37][Sybase-Wim] - Starting batch read from table: ap_standard_list_alias_OLD 
[TRACE] 2025-03-04 17:31:58.693 - [任务 37][Sybase-Wim] - Table ap_standard_list_alias_OLD is going to be initial synced 
[INFO ] 2025-03-04 17:31:58.884 - [任务 37][Sybase-Wim] - Table ap_standard_list_alias_OLD has been completed batch read 
[INFO ] 2025-03-04 17:31:58.884 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_bmsql_order_line 
[TRACE] 2025-03-04 17:31:58.885 - [任务 37][Sybase-Wim] - Table pg_bmsql_order_line is going to be initial synced 
[INFO ] 2025-03-04 17:33:17.564 - [任务 37][Sybase-Wim] - Table pg_bmsql_order_line has been completed batch read 
[INFO ] 2025-03-04 17:33:17.566 - [任务 37][Sybase-Wim] - Starting batch read from table: testTimeStampTwoWithIndex 
[TRACE] 2025-03-04 17:33:17.566 - [任务 37][Sybase-Wim] - Table testTimeStampTwoWithIndex is going to be initial synced 
[INFO ] 2025-03-04 17:33:17.769 - [任务 37][Sybase-Wim] - Table testTimeStampTwoWithIndex has been completed batch read 
[INFO ] 2025-03-04 17:33:17.770 - [任务 37][Sybase-Wim] - Starting batch read from table: td_empty_string 
[TRACE] 2025-03-04 17:33:17.770 - [任务 37][Sybase-Wim] - Table td_empty_string is going to be initial synced 
[INFO ] 2025-03-04 17:33:18.036 - [任务 37][Sybase-Wim] - Table td_empty_string has been completed batch read 
[INFO ] 2025-03-04 17:33:18.036 - [任务 37][Sybase-Wim] - Starting batch read from table: example 
[TRACE] 2025-03-04 17:33:18.036 - [任务 37][Sybase-Wim] - Table example is going to be initial synced 
[INFO ] 2025-03-04 17:33:18.269 - [任务 37][Sybase-Wim] - Table example has been completed batch read 
[INFO ] 2025-03-04 17:33:18.269 - [任务 37][Sybase-Wim] - Starting batch read from table: BMSQL_DISTRICT 
[TRACE] 2025-03-04 17:33:18.269 - [任务 37][Sybase-Wim] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2025-03-04 17:33:18.458 - [任务 37][Sybase-Wim] - Table BMSQL_DISTRICT has been completed batch read 
[INFO ] 2025-03-04 17:33:18.458 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_xxx_bmsql_config 
[TRACE] 2025-03-04 17:33:18.637 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_config is going to be initial synced 
[INFO ] 2025-03-04 17:33:18.637 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_config has been completed batch read 
[INFO ] 2025-03-04 17:33:18.638 - [任务 37][Sybase-Wim] - Starting batch read from table: BMSQL_CONFIG 
[TRACE] 2025-03-04 17:33:18.812 - [任务 37][Sybase-Wim] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2025-03-04 17:33:18.812 - [任务 37][Sybase-Wim] - Table BMSQL_CONFIG has been completed batch read 
[INFO ] 2025-03-04 17:33:18.813 - [任务 37][Sybase-Wim] - Starting batch read from table: bmsql_history 
[TRACE] 2025-03-04 17:33:18.813 - [任务 37][Sybase-Wim] - Table bmsql_history is going to be initial synced 
[INFO ] 2025-03-04 17:33:36.686 - [任务 37][Sybase-Wim] - Table bmsql_history has been completed batch read 
[INFO ] 2025-03-04 17:33:36.689 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_bmsql_stock 
[TRACE] 2025-03-04 17:33:36.689 - [任务 37][Sybase-Wim] - Table pg_bmsql_stock is going to be initial synced 
[INFO ] 2025-03-04 17:33:39.823 - [任务 37][Sybase-Wim] - Table pg_bmsql_stock has been completed batch read 
[INFO ] 2025-03-04 17:33:39.824 - [任务 37][Sybase-Wim] - Starting batch read from table: test_nouniquepdktable 
[TRACE] 2025-03-04 17:33:39.824 - [任务 37][Sybase-Wim] - Table test_nouniquepdktable is going to be initial synced 
[INFO ] 2025-03-04 17:33:40.030 - [任务 37][Sybase-Wim] - Table test_nouniquepdktable has been completed batch read 
[INFO ] 2025-03-04 17:33:40.031 - [任务 37][Sybase-Wim] - Starting batch read from table: td_only_text 
[TRACE] 2025-03-04 17:33:40.234 - [任务 37][Sybase-Wim] - Table td_only_text is going to be initial synced 
[INFO ] 2025-03-04 17:33:40.236 - [任务 37][Sybase-Wim] - Table td_only_text has been completed batch read 
[INFO ] 2025-03-04 17:33:40.236 - [任务 37][Sybase-Wim] - Starting batch read from table: a_testMoney 
[TRACE] 2025-03-04 17:33:40.236 - [任务 37][Sybase-Wim] - Table a_testMoney is going to be initial synced 
[INFO ] 2025-03-04 17:33:40.440 - [任务 37][Sybase-Wim] - Table a_testMoney has been completed batch read 
[INFO ] 2025-03-04 17:33:40.440 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_xxx_sync_test 
[TRACE] 2025-03-04 17:33:40.441 - [任务 37][Sybase-Wim] - Table pg_xxx_sync_test is going to be initial synced 
[INFO ] 2025-03-04 17:33:40.597 - [任务 37][Sybase-Wim] - Table pg_xxx_sync_test has been completed batch read 
[INFO ] 2025-03-04 17:33:40.597 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_xxx_bmsql_warehouse 
[TRACE] 2025-03-04 17:33:40.598 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_warehouse is going to be initial synced 
[INFO ] 2025-03-04 17:33:40.846 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_warehouse has been completed batch read 
[INFO ] 2025-03-04 17:33:40.849 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_bmsql_new_order 
[TRACE] 2025-03-04 17:33:40.849 - [任务 37][Sybase-Wim] - Table pg_bmsql_new_order is going to be initial synced 
[INFO ] 2025-03-04 17:33:43.155 - [任务 37][Sybase-Wim] - Table pg_bmsql_new_order has been completed batch read 
[INFO ] 2025-03-04 17:33:43.155 - [任务 37][Sybase-Wim] - Starting batch read from table: testNotDuplicate 
[TRACE] 2025-03-04 17:33:43.156 - [任务 37][Sybase-Wim] - Table testNotDuplicate is going to be initial synced 
[INFO ] 2025-03-04 17:33:43.378 - [任务 37][Sybase-Wim] - Table testNotDuplicate has been completed batch read 
[INFO ] 2025-03-04 17:33:43.381 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_xxx_bmsql_history 
[TRACE] 2025-03-04 17:33:43.381 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_history is going to be initial synced 
[INFO ] 2025-03-04 17:33:49.219 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_history has been completed batch read 
[INFO ] 2025-03-04 17:33:49.224 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_yyy_bmsql_config 
[TRACE] 2025-03-04 17:33:49.224 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_config is going to be initial synced 
[INFO ] 2025-03-04 17:33:49.468 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_config has been completed batch read 
[INFO ] 2025-03-04 17:33:49.468 - [任务 37][Sybase-Wim] - Starting batch read from table: BMSQL_NEW_ORDER 
[TRACE] 2025-03-04 17:33:49.468 - [任务 37][Sybase-Wim] - Table BMSQL_NEW_ORDER is going to be initial synced 
[INFO ] 2025-03-04 17:33:51.347 - [任务 37][Sybase-Wim] - Table BMSQL_NEW_ORDER has been completed batch read 
[INFO ] 2025-03-04 17:33:51.348 - [任务 37][Sybase-Wim] - Starting batch read from table: bmsql_new_order 
[TRACE] 2025-03-04 17:33:51.349 - [任务 37][Sybase-Wim] - Table bmsql_new_order is going to be initial synced 
[INFO ] 2025-03-04 17:33:53.942 - [任务 37][Sybase-Wim] - Table bmsql_new_order has been completed batch read 
[INFO ] 2025-03-04 17:33:53.943 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_yyy_bmsql_district 
[TRACE] 2025-03-04 17:33:53.943 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_district is going to be initial synced 
[INFO ] 2025-03-04 17:33:54.284 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_district has been completed batch read 
[INFO ] 2025-03-04 17:33:54.284 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_bmsql_warehouse 
[TRACE] 2025-03-04 17:33:54.284 - [任务 37][Sybase-Wim] - Table pg_bmsql_warehouse is going to be initial synced 
[INFO ] 2025-03-04 17:33:54.511 - [任务 37][Sybase-Wim] - Table pg_bmsql_warehouse has been completed batch read 
[INFO ] 2025-03-04 17:33:54.511 - [任务 37][Sybase-Wim] - Starting batch read from table: message_code 
[TRACE] 2025-03-04 17:33:54.511 - [任务 37][Sybase-Wim] - Table message_code is going to be initial synced 
[INFO ] 2025-03-04 17:33:54.739 - [任务 37][Sybase-Wim] - Table message_code has been completed batch read 
[INFO ] 2025-03-04 17:33:54.739 - [任务 37][Sybase-Wim] - Starting batch read from table: bmsql_warehouse 
[TRACE] 2025-03-04 17:33:54.739 - [任务 37][Sybase-Wim] - Table bmsql_warehouse is going to be initial synced 
[INFO ] 2025-03-04 17:33:55.191 - [任务 37][Sybase-Wim] - Table bmsql_warehouse has been completed batch read 
[INFO ] 2025-03-04 17:33:55.191 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_xxx_bmsql_new_order 
[TRACE] 2025-03-04 17:33:55.191 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_new_order is going to be initial synced 
[INFO ] 2025-03-04 17:33:57.144 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_new_order has been completed batch read 
[INFO ] 2025-03-04 17:33:57.145 - [任务 37][Sybase-Wim] - Starting batch read from table: nouniquepdktable 
[TRACE] 2025-03-04 17:33:57.145 - [任务 37][Sybase-Wim] - Table nouniquepdktable is going to be initial synced 
[INFO ] 2025-03-04 17:33:57.342 - [任务 37][Sybase-Wim] - Table nouniquepdktable has been completed batch read 
[INFO ] 2025-03-04 17:33:57.342 - [任务 37][Sybase-Wim] - Starting batch read from table: t1_example 
[TRACE] 2025-03-04 17:33:57.548 - [任务 37][Sybase-Wim] - Table t1_example is going to be initial synced 
[INFO ] 2025-03-04 17:33:57.562 - [任务 37][Sybase-Wim] - Table t1_example has been completed batch read 
[INFO ] 2025-03-04 17:33:57.563 - [任务 37][Sybase-Wim] - Starting batch read from table: td_fact_sec_null 
[TRACE] 2025-03-04 17:33:57.563 - [任务 37][Sybase-Wim] - Table td_fact_sec_null is going to be initial synced 
[INFO ] 2025-03-04 17:33:57.789 - [任务 37][Sybase-Wim] - Table td_fact_sec_null has been completed batch read 
[INFO ] 2025-03-04 17:33:57.790 - [任务 37][Sybase-Wim] - Starting batch read from table: testIdenSmallInt 
[TRACE] 2025-03-04 17:33:57.790 - [任务 37][Sybase-Wim] - Table testIdenSmallInt is going to be initial synced 
[INFO ] 2025-03-04 17:33:58.106 - [任务 37][Sybase-Wim] - Table testIdenSmallInt has been completed batch read 
[INFO ] 2025-03-04 17:33:58.106 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_xxx_bmsql_district 
[TRACE] 2025-03-04 17:33:58.106 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_district is going to be initial synced 
[INFO ] 2025-03-04 17:33:58.315 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_district has been completed batch read 
[INFO ] 2025-03-04 17:33:58.347 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_bmsql_district 
[TRACE] 2025-03-04 17:33:58.367 - [任务 37][Sybase-Wim] - Table pg_bmsql_district is going to be initial synced 
[INFO ] 2025-03-04 17:33:58.554 - [任务 37][Sybase-Wim] - Table pg_bmsql_district has been completed batch read 
[INFO ] 2025-03-04 17:33:58.554 - [任务 37][Sybase-Wim] - Starting batch read from table: bmsql_item 
[TRACE] 2025-03-04 17:33:58.554 - [任务 37][Sybase-Wim] - Table bmsql_item is going to be initial synced 
[INFO ] 2025-03-04 17:34:02.282 - [任务 37][Sybase-Wim] - Table bmsql_item has been completed batch read 
[INFO ] 2025-03-04 17:34:02.286 - [任务 37][Sybase-Wim] - Starting batch read from table: BMSQL_STOCK 
[TRACE] 2025-03-04 17:34:02.286 - [任务 37][Sybase-Wim] - Table BMSQL_STOCK is going to be initial synced 
[INFO ] 2025-03-04 17:34:41.193 - [任务 37][Sybase-Wim] - Table BMSQL_STOCK has been completed batch read 
[INFO ] 2025-03-04 17:34:41.194 - [任务 37][Sybase-Wim] - Starting batch read from table: ap_standard_list_subset 
[TRACE] 2025-03-04 17:34:41.194 - [任务 37][Sybase-Wim] - Table ap_standard_list_subset is going to be initial synced 
[INFO ] 2025-03-04 17:34:41.371 - [任务 37][Sybase-Wim] - Table ap_standard_list_subset has been completed batch read 
[INFO ] 2025-03-04 17:34:41.371 - [任务 37][Sybase-Wim] - Starting batch read from table: testChar 
[TRACE] 2025-03-04 17:34:41.371 - [任务 37][Sybase-Wim] - Table testChar is going to be initial synced 
[INFO ] 2025-03-04 17:34:41.549 - [任务 37][Sybase-Wim] - Table testChar has been completed batch read 
[INFO ] 2025-03-04 17:34:41.550 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_xxx_bmsql_order_line 
[TRACE] 2025-03-04 17:34:41.550 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_order_line is going to be initial synced 
[INFO ] 2025-03-04 17:35:22.247 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_order_line has been completed batch read 
[INFO ] 2025-03-04 17:35:22.247 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_yyy_bmsql_warehouse 
[TRACE] 2025-03-04 17:35:22.247 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_warehouse is going to be initial synced 
[INFO ] 2025-03-04 17:35:22.432 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_warehouse has been completed batch read 
[INFO ] 2025-03-04 17:35:22.432 - [任务 37][Sybase-Wim] - Starting batch read from table: BMSQL_CUSTOMER 
[TRACE] 2025-03-04 17:35:22.433 - [任务 37][Sybase-Wim] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2025-03-04 17:35:26.560 - [任务 37][Sybase-Wim] - Table BMSQL_CUSTOMER has been completed batch read 
[INFO ] 2025-03-04 17:35:26.560 - [任务 37][Sybase-Wim] - Starting batch read from table: testTimeStamp2 
[TRACE] 2025-03-04 17:35:26.560 - [任务 37][Sybase-Wim] - Table testTimeStamp2 is going to be initial synced 
[INFO ] 2025-03-04 17:35:26.755 - [任务 37][Sybase-Wim] - Table testTimeStamp2 has been completed batch read 
[INFO ] 2025-03-04 17:35:26.755 - [任务 37][Sybase-Wim] - Starting batch read from table: bmsql_district 
[TRACE] 2025-03-04 17:35:26.755 - [任务 37][Sybase-Wim] - Table bmsql_district is going to be initial synced 
[INFO ] 2025-03-04 17:35:26.954 - [任务 37][Sybase-Wim] - Table bmsql_district has been completed batch read 
[INFO ] 2025-03-04 17:35:26.954 - [任务 37][Sybase-Wim] - Starting batch read from table: QC_Sample_Map 
[TRACE] 2025-03-04 17:35:26.954 - [任务 37][Sybase-Wim] - Table QC_Sample_Map is going to be initial synced 
[INFO ] 2025-03-04 17:35:27.208 - [任务 37][Sybase-Wim] - Table QC_Sample_Map has been completed batch read 
[INFO ] 2025-03-04 17:35:27.208 - [任务 37][Sybase-Wim] - Starting batch read from table: ap_operation_list 
[TRACE] 2025-03-04 17:35:27.208 - [任务 37][Sybase-Wim] - Table ap_operation_list is going to be initial synced 
[INFO ] 2025-03-04 17:35:27.482 - [任务 37][Sybase-Wim] - Table ap_operation_list has been completed batch read 
[INFO ] 2025-03-04 17:35:27.482 - [任务 37][Sybase-Wim] - Starting batch read from table: td_null_col 
[TRACE] 2025-03-04 17:35:27.482 - [任务 37][Sybase-Wim] - Table td_null_col is going to be initial synced 
[INFO ] 2025-03-04 17:35:27.688 - [任务 37][Sybase-Wim] - Table td_null_col has been completed batch read 
[INFO ] 2025-03-04 17:35:27.690 - [任务 37][Sybase-Wim] - Starting batch read from table: a_td_empty_string 
[TRACE] 2025-03-04 17:35:27.690 - [任务 37][Sybase-Wim] - Table a_td_empty_string is going to be initial synced 
[INFO ] 2025-03-04 17:35:27.906 - [任务 37][Sybase-Wim] - Table a_td_empty_string has been completed batch read 
[INFO ] 2025-03-04 17:35:27.907 - [任务 37][Sybase-Wim] - Starting batch read from table: td_test_varchar 
[TRACE] 2025-03-04 17:35:27.908 - [任务 37][Sybase-Wim] - Table td_test_varchar is going to be initial synced 
[INFO ] 2025-03-04 17:35:28.210 - [任务 37][Sybase-Wim] - Table td_test_varchar has been completed batch read 
[INFO ] 2025-03-04 17:35:28.210 - [任务 37][Sybase-Wim] - Starting batch read from table: test_td_timestamp_col 
[TRACE] 2025-03-04 17:35:28.210 - [任务 37][Sybase-Wim] - Table test_td_timestamp_col is going to be initial synced 
[INFO ] 2025-03-04 17:35:28.418 - [任务 37][Sybase-Wim] - Table test_td_timestamp_col has been completed batch read 
[INFO ] 2025-03-04 17:35:28.418 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_bmsql_history 
[TRACE] 2025-03-04 17:35:28.418 - [任务 37][Sybase-Wim] - Table pg_bmsql_history is going to be initial synced 
[INFO ] 2025-03-04 17:35:45.780 - [任务 37][Sybase-Wim] - Table pg_bmsql_history has been completed batch read 
[INFO ] 2025-03-04 17:35:45.781 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_w_td_fact_sec 
[TRACE] 2025-03-04 17:35:45.782 - [任务 37][Sybase-Wim] - Table pg_w_td_fact_sec is going to be initial synced 
[INFO ] 2025-03-04 17:35:45.984 - [任务 37][Sybase-Wim] - Table pg_w_td_fact_sec has been completed batch read 
[INFO ] 2025-03-04 17:35:45.985 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_bmsql_config 
[TRACE] 2025-03-04 17:35:45.985 - [任务 37][Sybase-Wim] - Table pg_bmsql_config is going to be initial synced 
[INFO ] 2025-03-04 17:35:46.230 - [任务 37][Sybase-Wim] - Table pg_bmsql_config has been completed batch read 
[INFO ] 2025-03-04 17:35:46.230 - [任务 37][Sybase-Wim] - Starting batch read from table: t1_td_char_not_null 
[TRACE] 2025-03-04 17:35:46.231 - [任务 37][Sybase-Wim] - Table t1_td_char_not_null is going to be initial synced 
[INFO ] 2025-03-04 17:35:46.451 - [任务 37][Sybase-Wim] - Table t1_td_char_not_null has been completed batch read 
[INFO ] 2025-03-04 17:35:46.452 - [任务 37][Sybase-Wim] - Starting batch read from table: td_char0 
[TRACE] 2025-03-04 17:35:46.453 - [任务 37][Sybase-Wim] - Table td_char0 is going to be initial synced 
[INFO ] 2025-03-04 17:35:46.733 - [任务 37][Sybase-Wim] - Table td_char0 has been completed batch read 
[INFO ] 2025-03-04 17:35:46.734 - [任务 37][Sybase-Wim] - Starting batch read from table: BMSQL_WAREHOUSE 
[TRACE] 2025-03-04 17:35:46.940 - [任务 37][Sybase-Wim] - Table BMSQL_WAREHOUSE is going to be initial synced 
[INFO ] 2025-03-04 17:35:46.956 - [任务 37][Sybase-Wim] - Table BMSQL_WAREHOUSE has been completed batch read 
[INFO ] 2025-03-04 17:35:46.957 - [任务 37][Sybase-Wim] - Starting batch read from table: s2p_text_idt 
[TRACE] 2025-03-04 17:35:46.957 - [任务 37][Sybase-Wim] - Table s2p_text_idt is going to be initial synced 
[INFO ] 2025-03-04 17:35:47.254 - [任务 37][Sybase-Wim] - Table s2p_text_idt has been completed batch read 
[INFO ] 2025-03-04 17:35:47.255 - [任务 37][Sybase-Wim] - Starting batch read from table: bmsql_stock 
[TRACE] 2025-03-04 17:35:47.255 - [任务 37][Sybase-Wim] - Table bmsql_stock is going to be initial synced 
[INFO ] 2025-03-04 17:35:51.305 - [任务 37][Sybase-Wim] - Table bmsql_stock has been completed batch read 
[INFO ] 2025-03-04 17:35:51.306 - [任务 37][Sybase-Wim] - Starting batch read from table: td_fact_sec 
[TRACE] 2025-03-04 17:35:51.306 - [任务 37][Sybase-Wim] - Table td_fact_sec is going to be initial synced 
[INFO ] 2025-03-04 17:35:51.471 - [任务 37][Sybase-Wim] - Table td_fact_sec has been completed batch read 
[INFO ] 2025-03-04 17:35:51.472 - [任务 37][Sybase-Wim] - Starting batch read from table: s2p_datetime_idt 
[TRACE] 2025-03-04 17:35:51.472 - [任务 37][Sybase-Wim] - Table s2p_datetime_idt is going to be initial synced 
[INFO ] 2025-03-04 17:35:51.619 - [任务 37][Sybase-Wim] - Table s2p_datetime_idt has been completed batch read 
[INFO ] 2025-03-04 17:35:51.620 - [任务 37][Sybase-Wim] - Starting batch read from table: sync_test 
[TRACE] 2025-03-04 17:35:51.620 - [任务 37][Sybase-Wim] - Table sync_test is going to be initial synced 
[INFO ] 2025-03-04 17:35:51.787 - [任务 37][Sybase-Wim] - Table sync_test has been completed batch read 
[INFO ] 2025-03-04 17:35:51.787 - [任务 37][Sybase-Wim] - Starting batch read from table: s2p_text 
[TRACE] 2025-03-04 17:35:51.787 - [任务 37][Sybase-Wim] - Table s2p_text is going to be initial synced 
[INFO ] 2025-03-04 17:35:51.963 - [任务 37][Sybase-Wim] - Table s2p_text has been completed batch read 
[INFO ] 2025-03-04 17:35:51.964 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_yyy_bmsql_oorder 
[TRACE] 2025-03-04 17:35:52.176 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_oorder is going to be initial synced 
[INFO ] 2025-03-04 17:36:00.687 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_oorder has been completed batch read 
[INFO ] 2025-03-04 17:36:00.687 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_yyy_bmsql_item 
[TRACE] 2025-03-04 17:36:00.687 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_item is going to be initial synced 
[INFO ] 2025-03-04 17:36:03.902 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_item has been completed batch read 
[INFO ] 2025-03-04 17:36:03.902 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_xxx_bmsql_stock 
[TRACE] 2025-03-04 17:36:03.902 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_stock is going to be initial synced 
[INFO ] 2025-03-04 17:36:07.985 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_stock has been completed batch read 
[INFO ] 2025-03-04 17:36:07.985 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_xxx_bmsql_oorder 
[TRACE] 2025-03-04 17:36:08.191 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_oorder is going to be initial synced 
[INFO ] 2025-03-04 17:36:14.079 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_oorder has been completed batch read 
[INFO ] 2025-03-04 17:36:14.079 - [任务 37][Sybase-Wim] - Starting batch read from table: BMSQL_OORDER 
[TRACE] 2025-03-04 17:36:14.079 - [任务 37][Sybase-Wim] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2025-03-04 17:36:18.194 - [任务 37][Sybase-Wim] - Table BMSQL_OORDER has been completed batch read 
[INFO ] 2025-03-04 17:36:18.194 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_w_td_fact_sec_t 
[TRACE] 2025-03-04 17:36:18.194 - [任务 37][Sybase-Wim] - Table pg_w_td_fact_sec_t is going to be initial synced 
[INFO ] 2025-03-04 17:36:18.423 - [任务 37][Sybase-Wim] - Table pg_w_td_fact_sec_t has been completed batch read 
[INFO ] 2025-03-04 17:36:18.424 - [任务 37][Sybase-Wim] - Starting batch read from table: testTimeStampWithNoIndex 
[TRACE] 2025-03-04 17:36:18.424 - [任务 37][Sybase-Wim] - Table testTimeStampWithNoIndex is going to be initial synced 
[INFO ] 2025-03-04 17:36:18.734 - [任务 37][Sybase-Wim] - Table testTimeStampWithNoIndex has been completed batch read 
[INFO ] 2025-03-04 17:36:18.734 - [任务 37][Sybase-Wim] - Starting batch read from table: test1_testBit 
[TRACE] 2025-03-04 17:36:18.735 - [任务 37][Sybase-Wim] - Table test1_testBit is going to be initial synced 
[INFO ] 2025-03-04 17:36:18.934 - [任务 37][Sybase-Wim] - Table test1_testBit has been completed batch read 
[INFO ] 2025-03-04 17:36:18.935 - [任务 37][Sybase-Wim] - Starting batch read from table: bmsql_oorder 
[TRACE] 2025-03-04 17:36:18.936 - [任务 37][Sybase-Wim] - Table bmsql_oorder is going to be initial synced 
[INFO ] 2025-03-04 17:36:36.127 - [任务 37][Sybase-Wim] - Table bmsql_oorder has been completed batch read 
[INFO ] 2025-03-04 17:36:36.128 - [任务 37][Sybase-Wim] - Starting batch read from table: testMoney 
[TRACE] 2025-03-04 17:36:36.128 - [任务 37][Sybase-Wim] - Table testMoney is going to be initial synced 
[INFO ] 2025-03-04 17:36:36.304 - [任务 37][Sybase-Wim] - Table testMoney has been completed batch read 
[INFO ] 2025-03-04 17:36:36.304 - [任务 37][Sybase-Wim] - Starting batch read from table: test1_td_null_col 
[TRACE] 2025-03-04 17:36:36.304 - [任务 37][Sybase-Wim] - Table test1_td_null_col is going to be initial synced 
[INFO ] 2025-03-04 17:36:36.487 - [任务 37][Sybase-Wim] - Table test1_td_null_col has been completed batch read 
[INFO ] 2025-03-04 17:36:36.488 - [任务 37][Sybase-Wim] - Starting batch read from table: testTimeStampTarget 
[TRACE] 2025-03-04 17:36:36.488 - [任务 37][Sybase-Wim] - Table testTimeStampTarget is going to be initial synced 
[INFO ] 2025-03-04 17:36:36.692 - [任务 37][Sybase-Wim] - Table testTimeStampTarget has been completed batch read 
[INFO ] 2025-03-04 17:36:36.693 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_xxx_bmsql_customer 
[TRACE] 2025-03-04 17:36:36.693 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_customer is going to be initial synced 
[INFO ] 2025-03-04 17:36:40.462 - [任务 37][Sybase-Wim] - Table pg_xxx_bmsql_customer has been completed batch read 
[INFO ] 2025-03-04 17:36:40.462 - [任务 37][Sybase-Wim] - Starting batch read from table: testidentity 
[TRACE] 2025-03-04 17:36:40.462 - [任务 37][Sybase-Wim] - Table testidentity is going to be initial synced 
[INFO ] 2025-03-04 17:36:40.656 - [任务 37][Sybase-Wim] - Table testidentity has been completed batch read 
[INFO ] 2025-03-04 17:36:40.656 - [任务 37][Sybase-Wim] - Starting batch read from table: BMSQL_ORDER_LINE 
[TRACE] 2025-03-04 17:36:40.656 - [任务 37][Sybase-Wim] - Table BMSQL_ORDER_LINE is going to be initial synced 
[INFO ] 2025-03-04 17:37:12.443 - [任务 37][Sybase-Wim] - Table BMSQL_ORDER_LINE has been completed batch read 
[INFO ] 2025-03-04 17:37:12.443 - [任务 37][Sybase-Wim] - Starting batch read from table: td_text 
[TRACE] 2025-03-04 17:37:12.443 - [任务 37][Sybase-Wim] - Table td_text is going to be initial synced 
[INFO ] 2025-03-04 17:37:12.660 - [任务 37][Sybase-Wim] - Table td_text has been completed batch read 
[INFO ] 2025-03-04 17:37:12.660 - [任务 37][Sybase-Wim] - Starting batch read from table: testTimeStamp 
[TRACE] 2025-03-04 17:37:12.660 - [任务 37][Sybase-Wim] - Table testTimeStamp is going to be initial synced 
[INFO ] 2025-03-04 17:37:12.841 - [任务 37][Sybase-Wim] - Table testTimeStamp has been completed batch read 
[INFO ] 2025-03-04 17:37:12.841 - [任务 37][Sybase-Wim] - Starting batch read from table: bmsql_order_line 
[TRACE] 2025-03-04 17:37:12.841 - [任务 37][Sybase-Wim] - Table bmsql_order_line is going to be initial synced 
[INFO ] 2025-03-04 17:38:23.601 - [任务 37][Sybase-Wim] - Table bmsql_order_line has been completed batch read 
[INFO ] 2025-03-04 17:38:23.602 - [任务 37][Sybase-Wim] - Starting batch read from table: s2p_datetime_idt_char 
[TRACE] 2025-03-04 17:38:23.602 - [任务 37][Sybase-Wim] - Table s2p_datetime_idt_char is going to be initial synced 
[INFO ] 2025-03-04 17:38:23.789 - [任务 37][Sybase-Wim] - Table s2p_datetime_idt_char has been completed batch read 
[TRACE] 2025-03-04 17:38:23.790 - [任务 37][Sybase-Wim] - Initial sync completed 
[INFO ] 2025-03-04 17:38:23.790 - [任务 37][Sybase-Wim] - Batch read completed. 
[TRACE] 2025-03-04 17:38:23.791 - [任务 37][Sybase-Wim] - Incremental sync starting... 
[TRACE] 2025-03-04 17:38:23.791 - [任务 37][Sybase-Wim] - Initial sync completed 
[TRACE] 2025-03-04 17:38:23.792 - [任务 37][Sybase-Wim] - Starting stream read, table list: [pg_yyy_bmsql_new_order, td_char_not_null, bmsql_customer, pg_yyy_bmsql_history, testIden, pg_sync_test, bmsql_config, pg_bmsql_item, pg_yyy_bmsql_stock, BMSQL_ITEM, pg_bmsql_customer, a_testMoneyv, tdText, pg_yyy_bmsql_order_line, pg_xxx_bmsql_item, nouniquepdktableTarget, testBit, BMSQL_OORDER_BACK2, BMSQL_OORDER_BACK, nouniquepdktableTest, testTimeStampTwoWithNoIndex, pg_yyy_bmsql_customer, pg_bmsql_oorder, td_empty_string_frompg, test_td_null_col, BMSQL_HISTORY, test_date_with_text, td_timestamp_col, nouniquepdktable1, ap_standard_list_alias_OLD, pg_bmsql_order_line, testTimeStampTwoWithIndex, td_empty_string, example, BMSQL_DISTRICT, pg_xxx_bmsql_config, BMSQL_CONFIG, bmsql_history, pg_bmsql_stock, test_nouniquepdktable, td_only_text, a_testMoney, pg_xxx_sync_test, pg_xxx_bmsql_warehouse, pg_bmsql_new_order, testNotDuplicate, pg_xxx_bmsql_history, pg_yyy_bmsql_config, BMSQL_NEW_ORDER, bmsql_new_order, pg_yyy_bmsql_district, pg_bmsql_warehouse, message_code, bmsql_warehouse, pg_xxx_bmsql_new_order, nouniquepdktable, t1_example, td_fact_sec_null, testIdenSmallInt, pg_xxx_bmsql_district, pg_bmsql_district, bmsql_item, BMSQL_STOCK, ap_standard_list_subset, testChar, pg_xxx_bmsql_order_line, pg_yyy_bmsql_warehouse, BMSQL_CUSTOMER, testTimeStamp2, bmsql_district, QC_Sample_Map, ap_operation_list, td_null_col, a_td_empty_string, td_test_varchar, test_td_timestamp_col, pg_bmsql_history, pg_w_td_fact_sec, pg_bmsql_config, t1_td_char_not_null, td_char0, BMSQL_WAREHOUSE, s2p_text_idt, bmsql_stock, td_fact_sec, s2p_datetime_idt, sync_test, s2p_text, pg_yyy_bmsql_oorder, pg_yyy_bmsql_item, pg_xxx_bmsql_stock, pg_xxx_bmsql_oorder, BMSQL_OORDER, pg_w_td_fact_sec_t, testTimeStampWithNoIndex, test1_testBit, bmsql_oorder, testMoney, test1_td_null_col, testTimeStampTarget, pg_xxx_bmsql_customer, testidentity, BMSQL_ORDER_LINE, td_text, testTimeStamp, bmsql_order_line, s2p_datetime_idt_char], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-03-04 17:38:23.792 - [任务 37][Sybase-Wim] - Starting incremental sync using database log parser 
[INFO ] 2025-03-04 17:38:23.965 - [任务 37][Sybase-Wim] - startRid: 368417, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-04 17:38:23.965 - [任务 37][Sybase-Wim] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-04 17:38:23.965 - [任务 37][Sybase-Wim] - sybase offset in database is: startRid: 368417, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-03-04 17:38:23.965 - [任务 37][Sybase-Wim] - we will use offset in database, how ever, this is safe: startRid: 368417, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-04 17:38:24.176 - [任务 37][Sybase-Wim] - sybase cdc work with mode v1: auto rescan 
[INFO ] 2025-03-04 17:38:24.367 - [任务 37][Sybase-Wim] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-03-04 17:38:24.442 - [任务 37][Sybase-Wim] - turned off automatic log cleaning for sybase 
[TRACE] 2025-03-04 17:38:24.443 - [任务 37][Pg] - Process after table "pg_yyy_bmsql_stock" initial sync finished, cost: 1 ms 
[TRACE] 2025-03-04 17:38:24.444 - [任务 37][Pg] - Process after table "td_char_not_null" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.444 - [任务 37][Pg] - Process after table "pg_yyy_bmsql_new_order" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.444 - [任务 37][Pg] - Process after table "pg_yyy_bmsql_history" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.444 - [任务 37][Pg] - Process after table "BMSQL_ITEM" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.444 - [任务 37][Pg] - Process after table "pg_bmsql_customer" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.444 - [任务 37][Pg] - Process after table "pg_bmsql_item" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.444 - [任务 37][Pg] - Process after table "pg_xxx_bmsql_item" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.444 - [任务 37][Pg] - Process after table "tdText" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.444 - [任务 37][Pg] - Process after table "pg_yyy_bmsql_order_line" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.444 - [任务 37][Pg] - Process after table "BMSQL_OORDER_BACK" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.444 - [任务 37][Pg] - Process after table "nouniquepdktableTest" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.444 - [任务 37][Pg] - Process after table "BMSQL_OORDER_BACK2" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.444 - [任务 37][Pg] - Process after table "testTimeStampTwoWithNoIndex" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.444 - [任务 37][Pg] - Process after table "pg_yyy_bmsql_customer" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.444 - [任务 37][Pg] - Process after table "pg_bmsql_oorder" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.444 - [任务 37][Pg] - Process after table "BMSQL_HISTORY" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.444 - [任务 37][Pg] - Process after table "test_td_null_col" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.444 - [任务 37][Pg] - Process after table "td_timestamp_col" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.452 - [任务 37][Pg] - Process after table "nouniquepdktable1" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.453 - [任务 37][Pg] - Process after table "pg_bmsql_order_line" initial sync finished, cost: 1 ms 
[TRACE] 2025-03-04 17:38:24.453 - [任务 37][Pg] - Process after table "testTimeStampTwoWithIndex" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.453 - [任务 37][Pg] - Process after table "td_empty_string" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.453 - [任务 37][Pg] - Process after table "example" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.453 - [任务 37][Pg] - Process after table "BMSQL_DISTRICT" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.453 - [任务 37][Pg] - Process after table "pg_xxx_bmsql_config" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.453 - [任务 37][Pg] - Process after table "BMSQL_CONFIG" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.453 - [任务 37][Pg] - Process after table "bmsql_history" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.455 - [任务 37][Pg] - Process after table "pg_bmsql_stock" initial sync finished, cost: 0 ms 
[WARN ] 2025-03-04 17:38:24.477 - [任务 37][Pg] - Failed to get auto increment value for table testIden field id 
[TRACE] 2025-03-04 17:38:24.477 - [任务 37][Pg] - Process after table "testIden" initial sync finished, cost: 34 ms 
[TRACE] 2025-03-04 17:38:24.506 - [任务 37][Pg] - Process after table "td_only_text" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.507 - [任务 37][Pg] - Process after table "bmsql_config" initial sync finished, cost: 62 ms 
[TRACE] 2025-03-04 17:38:24.528 - [任务 37][Pg] - Process after table "a_testMoneyv" initial sync finished, cost: 85 ms 
[TRACE] 2025-03-04 17:38:24.529 - [任务 37][Pg] - Process after table "pg_xxx_bmsql_warehouse" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.529 - [任务 37][Pg] - Process after table "pg_bmsql_new_order" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.529 - [任务 37][Pg] - Process after table "testNotDuplicate" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.529 - [任务 37][Pg] - Process after table "pg_xxx_bmsql_history" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.529 - [任务 37][Pg] - Process after table "pg_yyy_bmsql_config" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.529 - [任务 37][Pg] - Process after table "BMSQL_NEW_ORDER" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.529 - [任务 37][Pg] - Process after table "bmsql_new_order" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.529 - [任务 37][Pg] - Process after table "pg_yyy_bmsql_district" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.529 - [任务 37][Pg] - Process after table "pg_bmsql_warehouse" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.529 - [任务 37][Pg] - Process after table "message_code" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.529 - [任务 37][Pg] - Process after table "bmsql_warehouse" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.529 - [任务 37][Pg] - Process after table "pg_xxx_bmsql_new_order" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.543 - [任务 37][Pg] - Process after table "pg_sync_test" initial sync finished, cost: 97 ms 
[TRACE] 2025-03-04 17:38:24.543 - [任务 37][Pg] - Process after table "t1_example" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.543 - [任务 37][Pg] - Process after table "td_fact_sec_null" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.581 - [任务 37][Pg] - Process after table "bmsql_customer" initial sync finished, cost: 138 ms 
[TRACE] 2025-03-04 17:38:24.581 - [任务 37][Pg] - Process after table "pg_xxx_bmsql_district" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.581 - [任务 37][Pg] - Process after table "pg_bmsql_district" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.583 - [任务 37][Pg] - Process after table "bmsql_item" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.583 - [任务 37][Pg] - Process after table "BMSQL_STOCK" initial sync finished, cost: 0 ms 
[WARN ] 2025-03-04 17:38:24.583 - [任务 37][Pg] - Failed to get auto increment value for table testBit field id 
[TRACE] 2025-03-04 17:38:24.583 - [任务 37][Pg] - Process after table "testBit" initial sync finished, cost: 140 ms 
[TRACE] 2025-03-04 17:38:24.583 - [任务 37][Pg] - Process after table "testChar" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.583 - [任务 37][Pg] - Process after table "pg_xxx_bmsql_order_line" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.583 - [任务 37][Pg] - Process after table "pg_yyy_bmsql_warehouse" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.583 - [任务 37][Pg] - Process after table "BMSQL_CUSTOMER" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.647 - [任务 37][Pg] - Process after table "a_testMoney" initial sync finished, cost: 150 ms 
[TRACE] 2025-03-04 17:38:24.647 - [任务 37][Pg] - Process after table "ap_standard_list_alias_OLD" initial sync finished, cost: 200 ms 
[TRACE] 2025-03-04 17:38:24.647 - [任务 37][Pg] - Process after table "pg_xxx_sync_test" initial sync finished, cost: 139 ms 
[TRACE] 2025-03-04 17:38:24.647 - [任务 37][Pg] - Process after table "ap_operation_list" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.648 - [任务 37][Pg] - Process after table "td_null_col" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.648 - [任务 37][Pg] - Process after table "a_td_empty_string" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.679 - [任务 37][Pg] - Process after table "td_empty_string_frompg" initial sync finished, cost: 235 ms 
[WARN ] 2025-03-04 17:38:24.679 - [任务 37][Pg] - Failed to get auto increment value for table testIdenSmallInt field id 
[TRACE] 2025-03-04 17:38:24.679 - [任务 37][Pg] - Process after table "test_td_timestamp_col" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.680 - [任务 37][Pg] - Process after table "testIdenSmallInt" initial sync finished, cost: 136 ms 
[TRACE] 2025-03-04 17:38:24.680 - [任务 37][Pg] - Process after table "pg_bmsql_history" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.680 - [任务 37][Pg] - Process after table "pg_bmsql_config" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.680 - [任务 37][Pg] - Process after table "pg_w_td_fact_sec" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.680 - [任务 37][Pg] - Process after table "t1_td_char_not_null" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.680 - [任务 37][Pg] - Process after table "td_char0" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.700 - [任务 37][Pg] - Process after table "nouniquepdktableTarget" initial sync finished, cost: 242 ms 
[TRACE] 2025-03-04 17:38:24.700 - [任务 37][Pg] - Process after table "bmsql_stock" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.706 - [任务 37][Pg] - Process after table "td_fact_sec" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.706 - [任务 37][Pg] - Process after table "test_date_with_text" initial sync finished, cost: 262 ms 
[TRACE] 2025-03-04 17:38:24.730 - [任务 37][Pg] - Process after table "testTimeStamp2" initial sync finished, cost: 137 ms 
[TRACE] 2025-03-04 17:38:24.730 - [任务 37][Pg] - Process after table "s2p_text" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.730 - [任务 37][Pg] - Process after table "pg_yyy_bmsql_oorder" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.730 - [任务 37][Pg] - Process after table "pg_yyy_bmsql_item" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.730 - [任务 37][Pg] - Process after table "pg_xxx_bmsql_stock" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.730 - [任务 37][Pg] - Process after table "pg_xxx_bmsql_oorder" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.730 - [任务 37][Pg] - Process after table "BMSQL_OORDER" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.730 - [任务 37][Pg] - Process after table "pg_w_td_fact_sec_t" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.731 - [任务 37][Pg] - Process after table "bmsql_district" initial sync finished, cost: 103 ms 
[TRACE] 2025-03-04 17:38:24.731 - [任务 37][Pg] - Process after table "test1_testBit" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.731 - [任务 37][Pg] - Process after table "bmsql_oorder" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.731 - [任务 37][Pg] - Process after table "testMoney" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.731 - [任务 37][Pg] - Process after table "test1_td_null_col" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.731 - [任务 37][Pg] - Process after table "testTimeStampTarget" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.731 - [任务 37][Pg] - Process after table "pg_xxx_bmsql_customer" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.731 - [任务 37][Pg] - Process after table "testidentity" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.731 - [任务 37][Pg] - Process after table "BMSQL_ORDER_LINE" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.731 - [任务 37][Pg] - Process after table "td_text" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.735 - [任务 37][Pg] - Process after table "test_nouniquepdktable" initial sync finished, cost: 279 ms 
[TRACE] 2025-03-04 17:38:24.735 - [任务 37][Pg] - Process after table "bmsql_order_line" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-04 17:38:24.770 - [任务 37][Pg] - Process after table "QC_Sample_Map" initial sync finished, cost: 124 ms 
[TRACE] 2025-03-04 17:38:24.770 - [任务 37][Pg] - Process after table "nouniquepdktable" initial sync finished, cost: 240 ms 
[WARN ] 2025-03-04 17:38:24.782 - [任务 37][Pg] - Failed to get auto increment value for table s2p_text_idt field id 
[TRACE] 2025-03-04 17:38:24.782 - [任务 37][Pg] - Process after table "s2p_text_idt" initial sync finished, cost: 101 ms 
[TRACE] 2025-03-04 17:38:24.782 - [任务 37][Pg] - Process after table "BMSQL_WAREHOUSE" initial sync finished, cost: 101 ms 
[TRACE] 2025-03-04 17:38:24.818 - [任务 37][Pg] - Process after table "td_test_varchar" initial sync finished, cost: 170 ms 
[TRACE] 2025-03-04 17:38:24.818 - [任务 37][Pg] - Process after table "ap_standard_list_subset" initial sync finished, cost: 236 ms 
[TRACE] 2025-03-04 17:38:24.818 - [任务 37][Pg] - Process after table "sync_test" initial sync finished, cost: 111 ms 
[WARN ] 2025-03-04 17:38:24.818 - [任务 37][Pg] - Failed to get auto increment value for table s2p_datetime_idt field id 
[TRACE] 2025-03-04 17:38:24.818 - [任务 37][Pg] - Process after table "s2p_datetime_idt" initial sync finished, cost: 117 ms 
[TRACE] 2025-03-04 17:38:24.867 - [任务 37][Pg] - Process after table "testTimeStampWithNoIndex" initial sync finished, cost: 136 ms 
[WARN ] 2025-03-04 17:38:24.868 - [任务 37][Pg] - Failed to get auto increment value for table s2p_datetime_idt_char field id 
[TRACE] 2025-03-04 17:38:24.868 - [任务 37][Pg] - Process after table "testTimeStamp" initial sync finished, cost: 135 ms 
[TRACE] 2025-03-04 17:38:24.868 - [任务 37][Pg] - Process after table "s2p_datetime_idt_char" initial sync finished, cost: 132 ms 
[INFO ] 2025-03-04 17:38:25.073 - [任务 37][Pg] - Process after all table(s) initial sync are finished，table number: 107 
[INFO ] 2025-03-04 17:38:26.229 - [任务 37][Sybase-Wim] - opened cdc for tables: {dbo=[pg_yyy_bmsql_new_order, td_char_not_null, bmsql_customer, pg_yyy_bmsql_history, testIden, pg_sync_test, bmsql_config, pg_bmsql_item, pg_yyy_bmsql_stock, BMSQL_ITEM, pg_bmsql_customer, a_testMoneyv, tdText, pg_yyy_bmsql_order_line, pg_xxx_bmsql_item, nouniquepdktableTarget, testBit, BMSQL_OORDER_BACK2, BMSQL_OORDER_BACK, nouniquepdktableTest, testTimeStampTwoWithNoIndex, pg_yyy_bmsql_customer, pg_bmsql_oorder, td_empty_string_frompg, test_td_null_col, BMSQL_HISTORY, test_date_with_text, td_timestamp_col, nouniquepdktable1, ap_standard_list_alias_OLD, pg_bmsql_order_line, testTimeStampTwoWithIndex, td_empty_string, example, BMSQL_DISTRICT, pg_xxx_bmsql_config, BMSQL_CONFIG, bmsql_history, pg_bmsql_stock, test_nouniquepdktable, td_only_text, a_testMoney, pg_xxx_sync_test, pg_xxx_bmsql_warehouse, pg_bmsql_new_order, testNotDuplicate, pg_xxx_bmsql_history, pg_yyy_bmsql_config, BMSQL_NEW_ORDER, bmsql_new_order, pg_yyy_bmsql_district, pg_bmsql_warehouse, message_code, bmsql_warehouse, pg_xxx_bmsql_new_order, nouniquepdktable, t1_example, td_fact_sec_null, testIdenSmallInt, pg_xxx_bmsql_district, pg_bmsql_district, bmsql_item, BMSQL_STOCK, ap_standard_list_subset, testChar, pg_xxx_bmsql_order_line, pg_yyy_bmsql_warehouse, BMSQL_CUSTOMER, testTimeStamp2, bmsql_district, QC_Sample_Map, ap_operation_list, td_null_col, a_td_empty_string, td_test_varchar, test_td_timestamp_col, pg_bmsql_history, pg_w_td_fact_sec, pg_bmsql_config, t1_td_char_not_null, td_char0, BMSQL_WAREHOUSE, s2p_text_idt, bmsql_stock, td_fact_sec, s2p_datetime_idt, sync_test, s2p_text, pg_yyy_bmsql_oorder, pg_yyy_bmsql_item, pg_xxx_bmsql_stock, pg_xxx_bmsql_oorder, BMSQL_OORDER, pg_w_td_fact_sec_t, testTimeStampWithNoIndex, test1_testBit, bmsql_oorder, testMoney, test1_td_null_col, testTimeStampTarget, pg_xxx_bmsql_customer, testidentity, BMSQL_ORDER_LINE, td_text, testTimeStamp, bmsql_order_line, s2p_datetime_idt_char]} 
[INFO ] 2025-03-04 17:38:26.349 - [任务 37][Sybase-Wim] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-03-04 17:38:26.350 - [任务 37][Sybase-Wim] - logs holder exists, will skip valid operation 
[INFO ] 2025-03-04 17:38:26.638 - [任务 37][Sybase-Wim] - trans timestamp offset: 28800000 
[INFO ] 2025-03-04 17:38:26.638 - [任务 37][Sybase-Wim] - sybase cdc debug log is disabled 
[INFO ] 2025-03-04 17:38:26.639 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368417, rowId: 0 
[INFO ] 2025-03-04 17:38:29.743 - [任务 37][Sybase-Wim] - rebuild statement with 368417, 0 
[INFO ] 2025-03-04 17:38:29.800 - [任务 37][Sybase-Wim] - uncommit trans size: 0 
[INFO ] 2025-03-04 17:38:29.800 - [任务 37][Sybase-Wim] - uncommit trans: {} 
[INFO ] 2025-03-04 17:38:30.005 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:38:32.900 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:38:33.313 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:38:36.303 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:38:36.344 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:38:39.347 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:38:39.754 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:38:42.769 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:38:42.834 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:38:46.034 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:38:46.070 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:38:49.078 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:38:49.487 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:38:52.497 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:38:52.637 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:38:55.756 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:38:55.951 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:38:58.952 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:38:59.256 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:39:02.262 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:39:02.670 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:39:05.661 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:39:05.767 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:39:08.894 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:39:09.099 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:39:12.273 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:39:12.307 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:39:15.314 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:39:15.719 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:39:18.575 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:39:19.317 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:39:22.328 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:39:23.480 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:39:26.487 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:39:27.098 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:39:29.995 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:39:30.396 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:39:33.226 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:39:33.627 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:39:36.467 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:39:36.660 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:39:39.729 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:39:40.324 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:39:43.325 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:39:43.944 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:39:46.750 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:39:47.226 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:39:50.199 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:39:50.402 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:39:53.381 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:39:53.581 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:39:56.585 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:39:56.788 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:39:59.836 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:40:00.443 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:40:03.308 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:40:03.692 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:40:06.611 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:40:06.985 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:40:09.992 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:40:10.716 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:40:13.718 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:40:14.511 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:40:17.460 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:40:17.866 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:40:20.731 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:40:21.137 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:40:24.135 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:40:24.270 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:40:27.450 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:40:27.902 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:40:30.910 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:40:31.316 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:40:34.256 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:40:34.461 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:40:37.452 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:40:38.066 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:40:41.010 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:40:42.021 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:40:45.022 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:40:45.429 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:40:48.297 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:40:48.703 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:40:51.563 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:40:51.971 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:40:54.840 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:40:55.447 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:40:58.259 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:40:58.414 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:41:01.420 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:41:01.592 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:41:04.593 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:41:04.797 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:41:07.783 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:41:08.187 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:41:11.146 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:41:11.756 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:41:14.724 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:41:15.334 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:41:18.344 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:41:18.702 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:41:21.706 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:41:22.523 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:41:25.548 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:41:26.359 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:41:29.165 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:41:29.775 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:41:32.744 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:41:33.357 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:41:36.339 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:41:36.680 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:41:39.747 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:41:39.920 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:41:42.952 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:41:43.695 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:41:46.700 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:41:47.071 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:41:50.074 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:41:50.484 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:41:53.417 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:41:53.748 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:41:56.754 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:41:57.651 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:42:00.457 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:42:00.933 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:42:03.939 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:42:04.405 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:42:07.410 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:42:07.816 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:42:10.689 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:42:10.930 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:42:13.931 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:42:14.340 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:42:17.214 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:42:17.422 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:42:20.428 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:42:21.220 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:42:24.226 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:42:24.893 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:42:28.088 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:42:28.619 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:42:31.616 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:42:32.297 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:42:35.302 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:42:35.848 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:42:38.843 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:42:39.235 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:42:42.237 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:42:42.663 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:42:45.473 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:42:45.878 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:42:48.814 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:42:49.020 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:42:52.027 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:42:52.379 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:42:55.540 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:42:56.202 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:42:59.208 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:42:59.815 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:43:02.833 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:43:03.443 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:43:06.439 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:43:06.939 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:43:09.943 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:43:10.330 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:43:13.330 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:43:13.865 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:43:16.870 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:43:17.584 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:43:20.590 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:43:20.823 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:43:23.810 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:43:24.016 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:43:26.984 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:43:27.088 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:43:30.130 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:43:30.334 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:43:33.308 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:43:33.473 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:43:36.503 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:43:36.913 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:43:39.718 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:43:40.122 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:43:42.954 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:43:43.529 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:43:46.532 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:43:46.939 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:43:49.993 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:43:50.603 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:43:53.438 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:43:53.845 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:43:56.842 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:43:57.213 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[INFO ] 2025-03-04 17:44:00.369 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[INFO ] 2025-03-04 17:44:00.572 - [任务 37][Sybase-Wim] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 84 
[TRACE] 2025-03-04 17:44:03.003 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] running status set to false 
[INFO ] 2025-03-04 17:44:03.003 - [任务 37][Sybase-Wim] - Log Miner is shutting down... 
[INFO ] 2025-03-04 17:44:03.003 - [任务 37][Sybase-Wim] - rebuild statement with 368419, 84 
[TRACE] 2025-03-04 17:44:04.238 - [任务 37][Sybase-Wim] - PDK connector node stopped: HazelcastSourcePdkDataNode_21da900a-2b14-4437-afde-4ef2d77fee21_1741080584575 
[TRACE] 2025-03-04 17:44:04.239 - [任务 37][Sybase-Wim] - PDK connector node released: HazelcastSourcePdkDataNode_21da900a-2b14-4437-afde-4ef2d77fee21_1741080584575 
[TRACE] 2025-03-04 17:44:04.239 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] schema data cleaned 
[TRACE] 2025-03-04 17:44:04.239 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] monitor closed 
[TRACE] 2025-03-04 17:44:04.242 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] close complete, cost 1240 ms 
[TRACE] 2025-03-04 17:44:04.243 - [任务 37][Pg] - Node Pg[06a0f647-93d9-4c90-aa90-052c2486ebac] running status set to false 
[TRACE] 2025-03-04 17:44:04.266 - [任务 37][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_06a0f647-93d9-4c90-aa90-052c2486ebac_1741080584463 
[TRACE] 2025-03-04 17:44:04.266 - [任务 37][Pg] - PDK connector node released: HazelcastTargetPdkDataNode_06a0f647-93d9-4c90-aa90-052c2486ebac_1741080584463 
[TRACE] 2025-03-04 17:44:04.266 - [任务 37][Pg] - Node Pg[06a0f647-93d9-4c90-aa90-052c2486ebac] schema data cleaned 
[TRACE] 2025-03-04 17:44:04.266 - [任务 37][Pg] - Node Pg[06a0f647-93d9-4c90-aa90-052c2486ebac] monitor closed 
[TRACE] 2025-03-04 17:44:04.460 - [任务 37][Pg] - Node Pg[06a0f647-93d9-4c90-aa90-052c2486ebac] close complete, cost 23 ms 
[TRACE] 2025-03-04 17:44:07.502 - [任务 37] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-04 17:44:07.502 - [任务 37] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@30431736 
[TRACE] 2025-03-04 17:44:07.644 - [任务 37] - Stop task milestones: 67c6c4386ed4fc58ed97f950(任务 37)  
[TRACE] 2025-03-04 17:44:07.644 - [任务 37] - Stopped task aspect(s) 
[TRACE] 2025-03-04 17:44:07.644 - [任务 37] - Snapshot order controller have been removed 
[INFO ] 2025-03-04 17:44:07.644 - [任务 37] - Task stopped. 
[TRACE] 2025-03-04 17:44:07.680 - [任务 37] - Remove memory task client succeed, task: 任务 37[67c6c4386ed4fc58ed97f950] 
[TRACE] 2025-03-04 17:44:07.680 - [任务 37] - Destroy memory task client cache succeed, task: 任务 37[67c6c4386ed4fc58ed97f950] 
[TRACE] 2025-03-04 17:44:51.114 - [任务 37] - Start task milestones: 67c6c4386ed4fc58ed97f950(任务 37) 
[TRACE] 2025-03-04 17:44:51.114 - [任务 37] - Task initialization... 
[INFO ] 2025-03-04 17:44:51.700 - [任务 37] - Loading table structure completed 
[TRACE] 2025-03-04 17:44:51.836 - [任务 37] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-04 17:44:51.837 - [任务 37] - The engine receives 任务 37 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-04 17:44:52.142 - [任务 37] - Task started 
[TRACE] 2025-03-04 17:44:52.164 - [任务 37][Pg] - Node Pg[06a0f647-93d9-4c90-aa90-052c2486ebac] start preload schema,table counts: 107 
[TRACE] 2025-03-04 17:44:52.164 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] start preload schema,table counts: 107 
[TRACE] 2025-03-04 17:44:52.165 - [任务 37][Pg] - Node Pg[06a0f647-93d9-4c90-aa90-052c2486ebac] preload schema finished, cost 0 ms 
[TRACE] 2025-03-04 17:44:52.165 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] preload schema finished, cost 0 ms 
[INFO ] 2025-03-04 17:44:53.009 - [任务 37][Sybase-Wim] - Source connector(Sybase-Wim) initialization completed 
[TRACE] 2025-03-04 17:44:53.009 - [任务 37][Sybase-Wim] - Source node "Sybase-Wim" read batch size: 100 
[TRACE] 2025-03-04 17:44:53.010 - [任务 37][Sybase-Wim] - Source node "Sybase-Wim" event queue capacity: 200 
[TRACE] 2025-03-04 17:44:53.010 - [任务 37][Sybase-Wim] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-03-04 17:44:53.010 - [任务 37][Sybase-Wim] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-03-04 17:44:53.245 - [任务 37][Sybase-Wim] - Starting batch read from 107 tables 
[TRACE] 2025-03-04 17:44:53.284 - [任务 37][Sybase-Wim] - Initial sync started 
[INFO ] 2025-03-04 17:44:53.285 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_yyy_bmsql_new_order 
[TRACE] 2025-03-04 17:44:53.446 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_new_order is going to be initial synced 
[INFO ] 2025-03-04 17:44:53.447 - [任务 37][Pg] - Sink connector(Pg) initialization completed 
[TRACE] 2025-03-04 17:44:53.447 - [任务 37][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-04 17:44:53.447 - [任务 37][Pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-04 17:44:53.652 - [任务 37][Pg] - Apply table structure to target database 
[TRACE] 2025-03-04 17:44:54.123 - [任务 37][Pg] - Table: pg_yyy_bmsql_new_order already exists Index: pg_yyy_bms_17120060991 and will no longer create index 
[TRACE] 2025-03-04 17:44:54.124 - [任务 37][Pg] - Table: pg_yyy_bmsql_new_order already exists Index list: [TapIndex name pg_yyy_bms_17120060991 indexFields: [TapIndexField name no_w_id fieldAsc true indexType null; TapIndexField name no_d_id fieldAsc true indexType null; TapIndexField name no_o_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:44:54.963 - [任务 37][Pg] - Table: td_char_not_null already exists Index list: [] 
[TRACE] 2025-03-04 17:44:55.340 - [任务 37][Pg] - Table: bmsql_customer will create Index: TapIndex name bmsql_cust_7165265551 indexFields: [TapIndexField name c_id fieldAsc true indexType null; TapIndexField name c_d_id fieldAsc true indexType null; TapIndexField name c_w_id fieldAsc true indexType null; ] 
[WARN ] 2025-03-04 17:44:55.506 - [任务 37][Pg] - Cluster index failed, table:bmsql_customer, index:bmsql_cust_7165265551 
[TRACE] 2025-03-04 17:44:55.507 - [任务 37][Pg] - Table: bmsql_customer create Index: bmsql_cust_7165265551 successfully, cost 167ms 
[TRACE] 2025-03-04 17:44:55.507 - [任务 37][Pg] - Table: bmsql_customer synchronize indexes completed, cost 306ms totally 
[TRACE] 2025-03-04 17:44:56.202 - [任务 37][Pg] - Table: pg_yyy_bmsql_history already exists Index: pg_yyy_bms_18400065551 and will no longer create index 
[TRACE] 2025-03-04 17:44:56.203 - [任务 37][Pg] - Table: pg_yyy_bmsql_history already exists Index list: [TapIndex name pg_yyy_bms_18400065551 indexFields: [TapIndexField name hist_id fieldAsc true indexType null; TapIndexField name h_c_id fieldAsc true indexType null; TapIndexField name h_c_d_id fieldAsc true indexType null; TapIndexField name h_c_w_id fieldAsc true indexType null; TapIndexField name h_d_id fieldAsc true indexType null; TapIndexField name h_w_id fieldAsc true indexType null; TapIndexField name h_amount fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:44:56.935 - [任务 37][Pg] - Table: testIden already exists Index list: [] 
[TRACE] 2025-03-04 17:44:57.393 - [任务 37][Pg] - Table: pg_sync_test already exists Index: pg_sync_te_20320072391 and will no longer create index 
[TRACE] 2025-03-04 17:44:57.393 - [任务 37][Pg] - Table: pg_sync_test already exists Index list: [TapIndex name pg_sync_te_20320072391 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:44:58.009 - [任务 37][Pg] - Table: bmsql_config already exists Index: bmsql_conf_6845264411 and will no longer create index 
[TRACE] 2025-03-04 17:44:58.009 - [任务 37][Pg] - Table: bmsql_config already exists Index list: [TapIndex name bmsql_conf_6845264411 indexFields: [TapIndexField name _id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:44:58.490 - [任务 37][Pg] - Table: pg_bmsql_item already exists Index: pg_bmsql_i_21280075811 and will no longer create index 
[TRACE] 2025-03-04 17:44:58.490 - [任务 37][Pg] - Table: pg_bmsql_item already exists Index list: [TapIndex name pg_bmsql_i_21280075811 indexFields: [TapIndexField name i_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:44:58.935 - [任务 37][Pg] - Table: pg_yyy_bmsql_stock already exists Index: pg_yyy_bms_765242751 and will no longer create index 
[TRACE] 2025-03-04 17:44:58.935 - [任务 37][Pg] - Table: pg_yyy_bmsql_stock already exists Index list: [TapIndex name pg_yyy_bms_765242751 indexFields: [TapIndexField name s_w_id fieldAsc true indexType null; TapIndexField name s_i_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:44:59.396 - [任务 37][Pg] - Table: BMSQL_ITEM already exists Index: BMSQL_ITEM_7805267831 and will no longer create index 
[TRACE] 2025-03-04 17:44:59.397 - [任务 37][Pg] - Table: BMSQL_ITEM already exists Index list: [TapIndex name BMSQL_ITEM_7805267831 indexFields: [TapIndexField name I_ID fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:44:59.895 - [任务 37][Pg] - Table: pg_bmsql_customer already exists Index: pg_bmsql_c_1085243891 and will no longer create index 
[TRACE] 2025-03-04 17:44:59.895 - [任务 37][Pg] - Table: pg_bmsql_customer already exists Index list: [TapIndex name pg_bmsql_c_1085243891 indexFields: [TapIndexField name c_w_id fieldAsc true indexType null; TapIndexField name c_d_id fieldAsc true indexType null; TapIndexField name c_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:00.380 - [任务 37][Pg] - Table: a_testMoneyv already exists Index: a_testMone_5850500891 and will no longer create index 
[TRACE] 2025-03-04 17:45:00.381 - [任务 37][Pg] - Table: a_testMoneyv already exists Index list: [TapIndex name a_testMone_5850500891 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:01.759 - [任务 37][Pg] - Table: tdText already exists Index: uidx_tdtext and will no longer create index 
[TRACE] 2025-03-04 17:45:01.762 - [任务 37][Pg] - Table: tdText already exists Index: uidx_td_text and will no longer create index 
[TRACE] 2025-03-04 17:45:01.766 - [任务 37][Pg] - Table: tdText already exists Index list: [TapIndex name uidx_tdtext indexFields: [TapIndexField name id fieldAsc true indexType null; ], TapIndex name uidx_td_text indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:02.473 - [任务 37][Pg] - Table: pg_yyy_bmsql_order_line already exists Index: pg_yyy_bms_2045247311 and will no longer create index 
[TRACE] 2025-03-04 17:45:02.474 - [任务 37][Pg] - Table: pg_yyy_bmsql_order_line already exists Index list: [TapIndex name pg_yyy_bms_2045247311 indexFields: [TapIndexField name ol_w_id fieldAsc true indexType null; TapIndexField name ol_d_id fieldAsc true indexType null; TapIndexField name ol_o_id fieldAsc true indexType null; TapIndexField name ol_number fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:02.715 - [任务 37][Pg] - Table: pg_xxx_bmsql_item already exists Index: pg_xxx_bms_2365248451 and will no longer create index 
[TRACE] 2025-03-04 17:45:02.716 - [任务 37][Pg] - Table: pg_xxx_bmsql_item already exists Index list: [TapIndex name pg_xxx_bms_2365248451 indexFields: [TapIndexField name i_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:03.355 - [任务 37][Pg] - Table: nouniquepdktableTarget already exists Index list: [] 
[TRACE] 2025-03-04 17:45:03.747 - [任务 37][Pg] - Table: testBit already exists Index: testBit_id_1850486641 and will no longer create index 
[TRACE] 2025-03-04 17:45:03.747 - [任务 37][Pg] - Table: testBit already exists Index list: [TapIndex name testBit_id_1850486641 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:04.210 - [任务 37][Pg] - Table: BMSQL_OORDER_BACK2 already exists Index: BMSQL_OORD_8125268971 and will no longer create index 
[TRACE] 2025-03-04 17:45:04.210 - [任务 37][Pg] - Table: BMSQL_OORDER_BACK2 already exists Index list: [TapIndex name BMSQL_OORD_8125268971 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:04.475 - [任务 37][Pg] - Table: BMSQL_OORDER_BACK already exists Index: BMSQL_OORD_8445270111 and will no longer create index 
[TRACE] 2025-03-04 17:45:04.476 - [任务 37][Pg] - Table: BMSQL_OORDER_BACK already exists Index list: [TapIndex name BMSQL_OORD_8445270111 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:04.978 - [任务 37][Pg] - Table: nouniquepdktableTest already exists Index list: [] 
[TRACE] 2025-03-04 17:45:05.300 - [任务 37][Pg] - Table: testTimeStampTwoWithNoIndex already exists Index list: [] 
[TRACE] 2025-03-04 17:45:05.506 - [任务 37][Pg] - Table: pg_yyy_bmsql_customer already exists Index: pg_yyy_bms_5245258711 and will no longer create index 
[TRACE] 2025-03-04 17:45:05.506 - [任务 37][Pg] - Table: pg_yyy_bmsql_customer already exists Index list: [TapIndex name pg_yyy_bms_5245258711 indexFields: [TapIndexField name c_w_id fieldAsc true indexType null; TapIndexField name c_d_id fieldAsc true indexType null; TapIndexField name c_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:05.827 - [任务 37][Pg] - Table: pg_bmsql_oorder already exists Index: pg_bmsql_o_5565259851 and will no longer create index 
[TRACE] 2025-03-04 17:45:05.828 - [任务 37][Pg] - Table: pg_bmsql_oorder already exists Index list: [TapIndex name pg_bmsql_o_5565259851 indexFields: [TapIndexField name o_w_id fieldAsc true indexType null; TapIndexField name o_d_id fieldAsc true indexType null; TapIndexField name o_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:06.258 - [任务 37][Pg] - Table: td_empty_string_frompg already exists Index list: [] 
[TRACE] 2025-03-04 17:45:06.404 - [任务 37][Pg] - Table: test_td_null_col already exists Index: test_td_nu_4090494621 and will no longer create index 
[TRACE] 2025-03-04 17:45:06.404 - [任务 37][Pg] - Table: test_td_null_col already exists Index list: [TapIndex name test_td_nu_4090494621 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:06.589 - [任务 37][Pg] - Table: BMSQL_HISTORY already exists Index: BMSQL_HIST_10685278091 and will no longer create index 
[TRACE] 2025-03-04 17:45:06.589 - [任务 37][Pg] - Table: BMSQL_HISTORY already exists Index list: [TapIndex name BMSQL_HIST_10685278091 indexFields: [TapIndexField name _id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:06.836 - [任务 37][Pg] - Table: test_date_with_text already exists Index: test_date__id_16250537941 and will no longer create index 
[TRACE] 2025-03-04 17:45:06.836 - [任务 37][Pg] - Table: test_date_with_text already exists Index list: [TapIndex name test_date__id_16250537941 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:07.049 - [任务 37][Pg] - Table: td_timestamp_col already exists Index: td_timesta_id_4410495761 and will no longer create index 
[TRACE] 2025-03-04 17:45:07.049 - [任务 37][Pg] - Table: td_timestamp_col already exists Index list: [TapIndex name td_timesta_id_4410495761 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:07.440 - [任务 37][Pg] - Table: nouniquepdktable1 already exists Index list: [] 
[TRACE] 2025-03-04 17:45:07.622 - [任务 37][Pg] - Table: ap_standard_list_alias_OLD already exists Index list: [] 
[TRACE] 2025-03-04 17:45:07.829 - [任务 37][Pg] - Table: pg_bmsql_order_line already exists Index: pg_bmsql_o_17760063271 and will no longer create index 
[TRACE] 2025-03-04 17:45:07.830 - [任务 37][Pg] - Table: pg_bmsql_order_line already exists Index list: [TapIndex name pg_bmsql_o_17760063271 indexFields: [TapIndexField name ol_w_id fieldAsc true indexType null; TapIndexField name ol_d_id fieldAsc true indexType null; TapIndexField name ol_o_id fieldAsc true indexType null; TapIndexField name ol_number fieldAsc true indexType null; ]] 
[WARN ] 2025-03-04 17:45:08.111 - [任务 37][Pg] - Create index failed ERROR: relation "testTimeStampWithUniqueIndex" already exists, rename testTimeStampWithUniqueIndex to testTimeStampWithUniqueIndex_56d0 and retry ... 
[TRACE] 2025-03-04 17:45:08.204 - [任务 37][Pg] - Table: testTimeStampTwoWithIndex already exists Index: testTimeStampWithUniqueIndex and will no longer create index 
[TRACE] 2025-03-04 17:45:08.204 - [任务 37][Pg] - Table: testTimeStampTwoWithIndex already exists Index list: [TapIndex name testTimeStampWithUniqueIndex indexFields: [TapIndexField name id fieldAsc true indexType null; TapIndexField name testTimeStamp fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:08.444 - [任务 37][Pg] - Table: td_empty_string already exists Index list: [] 
[TRACE] 2025-03-04 17:45:08.857 - [任务 37][Pg] - Table: example already exists Index list: [] 
[TRACE] 2025-03-04 17:45:08.945 - [任务 37][Pg] - Table: BMSQL_DISTRICT already exists Index: BMSQL_DIST_9725274671 and will no longer create index 
[TRACE] 2025-03-04 17:45:08.945 - [任务 37][Pg] - Table: BMSQL_DISTRICT already exists Index list: [TapIndex name BMSQL_DIST_9725274671 indexFields: [TapIndexField name _id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:09.259 - [任务 37][Pg] - Table: pg_xxx_bmsql_config already exists Index: pg_xxx_bms_18720066691 and will no longer create index 
[TRACE] 2025-03-04 17:45:09.259 - [任务 37][Pg] - Table: pg_xxx_bmsql_config already exists Index list: [TapIndex name pg_xxx_bms_18720066691 indexFields: [TapIndexField name cfg_name fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:09.428 - [任务 37][Pg] - Table: BMSQL_CONFIG already exists Index: BMSQL_CONF_10045275811 and will no longer create index 
[TRACE] 2025-03-04 17:45:09.428 - [任务 37][Pg] - Table: BMSQL_CONFIG already exists Index list: [TapIndex name BMSQL_CONF_10045275811 indexFields: [TapIndexField name _id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:09.621 - [任务 37][Pg] - Table: bmsql_history already exists Index: pk_bmsql_history and will no longer create index 
[TRACE] 2025-03-04 17:45:09.621 - [任务 37][Pg] - Table: bmsql_history already exists Index list: [TapIndex name pk_bmsql_history indexFields: [TapIndexField name hist_id fieldAsc true indexType null; TapIndexField name h_c_id fieldAsc true indexType null; TapIndexField name h_c_d_id fieldAsc true indexType null; TapIndexField name h_c_w_id fieldAsc true indexType null; TapIndexField name h_d_id fieldAsc true indexType null; TapIndexField name h_w_id fieldAsc true indexType null; TapIndexField name h_amount fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:09.847 - [任务 37][Pg] - Table: pg_bmsql_stock already exists Index: pg_bmsql_s_20640073531 and will no longer create index 
[TRACE] 2025-03-04 17:45:09.847 - [任务 37][Pg] - Table: pg_bmsql_stock already exists Index list: [TapIndex name pg_bmsql_s_20640073531 indexFields: [TapIndexField name s_w_id fieldAsc true indexType null; TapIndexField name s_i_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:10.229 - [任务 37][Pg] - Table: test_nouniquepdktable already exists Index list: [] 
[TRACE] 2025-03-04 17:45:10.515 - [任务 37][Pg] - Table: td_only_text already exists Index list: [] 
[TRACE] 2025-03-04 17:45:10.740 - [任务 37][Pg] - Table: a_testMoney already exists Index: a_testMone_5530499751 and will no longer create index 
[TRACE] 2025-03-04 17:45:10.740 - [任务 37][Pg] - Table: a_testMoney already exists Index list: [TapIndex name a_testMone_5530499751 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:10.954 - [任务 37][Pg] - Table: pg_xxx_sync_test already exists Index: pg_xxx_syn_445241611 and will no longer create index 
[TRACE] 2025-03-04 17:45:10.954 - [任务 37][Pg] - Table: pg_xxx_sync_test already exists Index list: [TapIndex name pg_xxx_syn_445241611 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:11.167 - [任务 37][Pg] - Table: pg_xxx_bmsql_warehouse already exists Index: pg_xxx_bms_1405245031 and will no longer create index 
[TRACE] 2025-03-04 17:45:11.167 - [任务 37][Pg] - Table: pg_xxx_bmsql_warehouse already exists Index list: [TapIndex name pg_xxx_bms_1405245031 indexFields: [TapIndexField name w_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:11.331 - [任务 37][Pg] - Table: pg_bmsql_new_order already exists Index: pg_bmsql_n_1725246171 and will no longer create index 
[TRACE] 2025-03-04 17:45:11.331 - [任务 37][Pg] - Table: pg_bmsql_new_order already exists Index list: [TapIndex name pg_bmsql_n_1725246171 indexFields: [TapIndexField name no_w_id fieldAsc true indexType null; TapIndexField name no_d_id fieldAsc true indexType null; TapIndexField name no_o_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:11.661 - [任务 37][Pg] - Table: testNotDuplicate will create Index: TapIndex name idx_testDuplicate1 indexFields: [TapIndexField name user_corp_acc fieldAsc true indexType null; TapIndexField name hospital fieldAsc true indexType null; TapIndexField name labno fieldAsc true indexType null; ] 
[TRACE] 2025-03-04 17:45:11.667 - [任务 37][Pg] - Table: testNotDuplicate create Index: idx_testDuplicate1 successfully, cost 44ms 
[TRACE] 2025-03-04 17:45:11.667 - [任务 37][Pg] - Table: testNotDuplicate synchronize indexes completed, cost 102ms totally 
[TRACE] 2025-03-04 17:45:11.856 - [任务 37][Pg] - Table: pg_xxx_bmsql_history already exists Index: pg_xxx_bms_3005250731 and will no longer create index 
[TRACE] 2025-03-04 17:45:11.856 - [任务 37][Pg] - Table: pg_xxx_bmsql_history already exists Index list: [TapIndex name pg_xxx_bms_3005250731 indexFields: [TapIndexField name hist_id fieldAsc true indexType null; TapIndexField name h_c_id fieldAsc true indexType null; TapIndexField name h_c_d_id fieldAsc true indexType null; TapIndexField name h_c_w_id fieldAsc true indexType null; TapIndexField name h_d_id fieldAsc true indexType null; TapIndexField name h_w_id fieldAsc true indexType null; TapIndexField name h_amount fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:12.071 - [任务 37][Pg] - Table: pg_yyy_bmsql_config already exists Index: pg_yyy_bms_3325251871 and will no longer create index 
[TRACE] 2025-03-04 17:45:12.071 - [任务 37][Pg] - Table: pg_yyy_bmsql_config already exists Index list: [TapIndex name pg_yyy_bms_3325251871 indexFields: [TapIndexField name cfg_name fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:12.277 - [任务 37][Pg] - Table: BMSQL_NEW_ORDER already exists Index: BMSQL_NEW__9085272391 and will no longer create index 
[TRACE] 2025-03-04 17:45:12.277 - [任务 37][Pg] - Table: BMSQL_NEW_ORDER already exists Index list: [TapIndex name BMSQL_NEW__9085272391 indexFields: [TapIndexField name _id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:12.491 - [任务 37][Pg] - Table: bmsql_new_order already exists Index: pk_new_order and will no longer create index 
[TRACE] 2025-03-04 17:45:12.492 - [任务 37][Pg] - Table: bmsql_new_order already exists Index list: [TapIndex name pk_new_order indexFields: [TapIndexField name no_w_id fieldAsc true indexType null; TapIndexField name no_d_id fieldAsc true indexType null; TapIndexField name no_o_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:12.696 - [任务 37][Pg] - Table: pg_yyy_bmsql_district already exists Index: pg_yyy_bms_3645253011 and will no longer create index 
[TRACE] 2025-03-04 17:45:12.696 - [任务 37][Pg] - Table: pg_yyy_bmsql_district already exists Index list: [TapIndex name pg_yyy_bms_3645253011 indexFields: [TapIndexField name d_w_id fieldAsc true indexType null; TapIndexField name d_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:12.892 - [任务 37][Pg] - Table: pg_bmsql_warehouse already exists Index: pg_bmsql_w_3965254151 and will no longer create index 
[TRACE] 2025-03-04 17:45:12.892 - [任务 37][Pg] - Table: pg_bmsql_warehouse already exists Index list: [TapIndex name pg_bmsql_w_3965254151 indexFields: [TapIndexField name w_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:13.200 - [任务 37][Pg] - Table: message_code already exists Index: message_code_idx and will no longer create index 
[TRACE] 2025-03-04 17:45:13.200 - [任务 37][Pg] - Table: message_code already exists Index list: [TapIndex name message_code_idx indexFields: [TapIndexField name msgcode_labno fieldAsc true indexType null; TapIndexField name msgcode_type fieldAsc true indexType null; TapIndexField name msgcode_code fieldAsc true indexType null; TapIndexField name msgcode_hospital fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:13.345 - [任务 37][Pg] - Table: bmsql_warehouse already exists Index: pk_warehouse and will no longer create index 
[TRACE] 2025-03-04 17:45:13.346 - [任务 37][Pg] - Table: bmsql_warehouse already exists Index list: [TapIndex name pk_warehouse indexFields: [TapIndexField name w_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:13.471 - [任务 37][Pg] - Table: pg_xxx_bmsql_new_order already exists Index: pg_xxx_bms_4605256431 and will no longer create index 
[TRACE] 2025-03-04 17:45:13.471 - [任务 37][Pg] - Table: pg_xxx_bmsql_new_order already exists Index list: [TapIndex name pg_xxx_bms_4605256431 indexFields: [TapIndexField name no_w_id fieldAsc true indexType null; TapIndexField name no_d_id fieldAsc true indexType null; TapIndexField name no_o_id fieldAsc true indexType null; ]] 
[WARN ] 2025-03-04 17:45:13.703 - [任务 37][Pg] - Create index failed ERROR: column "_no_pk_hash" does not exist, please execute it manually [create unique index "IDX_uepdktableecb709d766d0" on "public"."nouniquepdktable"("_no_pk_hash" asc)] 
[TRACE] 2025-03-04 17:45:13.703 - [任务 37][Pg] - Table: nouniquepdktable will create Index: TapIndex name IDX_uepdktableecb709d766d0 indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] 
[WARN ] 2025-03-04 17:45:13.745 - [任务 37][Pg] - Create index failed ERROR: column "_no_pk_hash" does not exist, please execute it manually [create unique index "IDX_uepdktableecb709d766d0" on "public"."nouniquepdktable"("_no_pk_hash" asc)] 
[TRACE] 2025-03-04 17:45:13.745 - [任务 37][Pg] - Table: nouniquepdktable create Index: IDX_uepdktableecb709d766d0 successfully, cost 42ms 
[TRACE] 2025-03-04 17:45:13.745 - [任务 37][Pg] - Table: nouniquepdktable synchronize indexes completed, cost 115ms totally 
[TRACE] 2025-03-04 17:45:14.108 - [任务 37][Pg] - Table: t1_example already exists Index: IDX_t1_example91adf6dc7ff8 and will no longer create index 
[TRACE] 2025-03-04 17:45:14.109 - [任务 37][Pg] - Table: t1_example already exists Index list: [TapIndex name IDX_t1_example91adf6dc7ff8 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:14.453 - [任务 37][Pg] - Table: td_fact_sec_null already exists Index list: [] 
[TRACE] 2025-03-04 17:45:14.703 - [任务 37][Pg] - Table: testIdenSmallInt already exists Index: testIdenSm_id_7415746491 and will no longer create index 
[TRACE] 2025-03-04 17:45:14.703 - [任务 37][Pg] - Table: testIdenSmallInt already exists Index list: [TapIndex name testIdenSm_id_7415746491 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:14.949 - [任务 37][Pg] - Table: pg_xxx_bmsql_district already exists Index: pg_xxx_bms_17440062131 and will no longer create index 
[TRACE] 2025-03-04 17:45:14.949 - [任务 37][Pg] - Table: pg_xxx_bmsql_district already exists Index list: [TapIndex name pg_xxx_bms_17440062131 indexFields: [TapIndexField name d_w_id fieldAsc true indexType null; TapIndexField name d_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:15.127 - [任务 37][Pg] - Table: pg_bmsql_district already exists Index: pg_bmsql_d_18080064411 and will no longer create index 
[TRACE] 2025-03-04 17:45:15.127 - [任务 37][Pg] - Table: pg_bmsql_district already exists Index list: [TapIndex name pg_bmsql_d_18080064411 indexFields: [TapIndexField name d_w_id fieldAsc true indexType null; TapIndexField name d_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:15.303 - [任务 37][Pg] - Table: bmsql_item already exists Index: pk_item and will no longer create index 
[TRACE] 2025-03-04 17:45:15.303 - [任务 37][Pg] - Table: bmsql_item already exists Index list: [TapIndex name pk_item indexFields: [TapIndexField name i_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:15.534 - [任务 37][Pg] - Table: BMSQL_STOCK already exists Index: BMSQL_STOC_8765271251 and will no longer create index 
[TRACE] 2025-03-04 17:45:15.534 - [任务 37][Pg] - Table: BMSQL_STOCK already exists Index list: [TapIndex name BMSQL_STOC_8765271251 indexFields: [TapIndexField name S_W_ID fieldAsc true indexType null; TapIndexField name S_I_ID fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:15.838 - [任务 37][Pg] - Table: ap_standard_list_subset already exists Index: ap_standard_list_subset_Idx and will no longer create index 
[TRACE] 2025-03-04 17:45:15.838 - [任务 37][Pg] - Table: ap_standard_list_subset already exists Index list: [TapIndex name ap_standard_list_subset_Idx indexFields: [TapIndexField name subset_id fieldAsc true indexType null; TapIndexField name term_id fieldAsc true indexType null; TapIndexField name node_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:16.120 - [任务 37][Pg] - Table: testChar already exists Index: testChar_id_12605284931 and will no longer create index 
[TRACE] 2025-03-04 17:45:16.121 - [任务 37][Pg] - Table: testChar already exists Index list: [TapIndex name testChar_id_12605284931 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:16.272 - [任务 37][Pg] - Table: pg_xxx_bmsql_order_line already exists Index: pg_xxx_bms_19040067831 and will no longer create index 
[TRACE] 2025-03-04 17:45:16.272 - [任务 37][Pg] - Table: pg_xxx_bmsql_order_line already exists Index list: [TapIndex name pg_xxx_bms_19040067831 indexFields: [TapIndexField name ol_w_id fieldAsc true indexType null; TapIndexField name ol_d_id fieldAsc true indexType null; TapIndexField name ol_o_id fieldAsc true indexType null; TapIndexField name ol_number fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:16.436 - [任务 37][Pg] - Table: pg_yyy_bmsql_warehouse already exists Index: pg_yyy_bms_20000071251 and will no longer create index 
[TRACE] 2025-03-04 17:45:16.436 - [任务 37][Pg] - Table: pg_yyy_bmsql_warehouse already exists Index list: [TapIndex name pg_yyy_bms_20000071251 indexFields: [TapIndexField name w_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:16.586 - [任务 37][Pg] - Table: BMSQL_CUSTOMER already exists Index: BMSQL_CUST_10365276951 and will no longer create index 
[TRACE] 2025-03-04 17:45:16.586 - [任务 37][Pg] - Table: BMSQL_CUSTOMER already exists Index list: [TapIndex name BMSQL_CUST_10365276951 indexFields: [TapIndexField name _id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:16.753 - [任务 37][Pg] - Table: testTimeStamp2 already exists Index: testTimeSt_id_19770550481 and will no longer create index 
[TRACE] 2025-03-04 17:45:16.753 - [任务 37][Pg] - Table: testTimeStamp2 already exists Index list: [TapIndex name testTimeSt_id_19770550481 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:16.922 - [任务 37][Pg] - Table: bmsql_district already exists Index: pk_district and will no longer create index 
[TRACE] 2025-03-04 17:45:16.923 - [任务 37][Pg] - Table: bmsql_district already exists Index list: [TapIndex name pk_district indexFields: [TapIndexField name d_w_id fieldAsc true indexType null; TapIndexField name d_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:17.290 - [任务 37][Pg] - Table: QC_Sample_Map will create Index: TapIndex name QC_Sample_Map_primary_Idx indexFields: [TapIndexField name lab_no fieldAsc true indexType null; TapIndexField name analyser_no fieldAsc true indexType null; TapIndexField name request_no fieldAsc true indexType null; ] 
[TRACE] 2025-03-04 17:45:17.290 - [任务 37][Pg] - Table: QC_Sample_Map create Index: QC_Sample_Map_primary_Idx successfully, cost 75ms 
[TRACE] 2025-03-04 17:45:17.290 - [任务 37][Pg] - Table: QC_Sample_Map synchronize indexes completed, cost 114ms totally 
[WARN ] 2025-03-04 17:45:18.003 - [任务 37][Pg] - Create index failed ERROR: relation "ap_operation_list_Idx" already exists, rename ap_operation_list_Idx to ap_operation_list_Idx_c67d and retry ... 
[TRACE] 2025-03-04 17:45:18.218 - [任务 37][Pg] - Table: ap_operation_list already exists Index: ap_operation_list_Idx and will no longer create index 
[TRACE] 2025-03-04 17:45:18.219 - [任务 37][Pg] - Table: ap_operation_list already exists Index list: [TapIndex name ap_operation_list_Idx indexFields: [TapIndexField name term_id fieldAsc true indexType null; TapIndexField name snomed_code fieldAsc true indexType null; TapIndexField name snomed_class fieldAsc true indexType null; TapIndexField name snomed_seq fieldAsc true indexType null; TapIndexField name valid_from fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:18.554 - [任务 37][Pg] - Table: td_null_col already exists Index: td_null_co_id_11130519701 and will no longer create index 
[TRACE] 2025-03-04 17:45:18.557 - [任务 37][Pg] - Table: td_null_col already exists Index list: [TapIndex name td_null_co_id_11130519701 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:19.133 - [任务 37][Pg] - Table: a_td_empty_string already exists Index: IDX_pty_stringd7e35f45b28e and will no longer create index 
[TRACE] 2025-03-04 17:45:19.133 - [任务 37][Pg] - Table: a_td_empty_string already exists Index list: [TapIndex name IDX_pty_stringd7e35f45b28e indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:19.385 - [任务 37][Pg] - Table: td_test_varchar already exists Index: td_test_va_id_10810518561 and will no longer create index 
[TRACE] 2025-03-04 17:45:19.386 - [任务 37][Pg] - Table: td_test_varchar already exists Index list: [TapIndex name td_test_va_id_10810518561 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:19.633 - [任务 37][Pg] - Table: test_td_timestamp_col already exists Index: test_td_ti_5210498611 and will no longer create index 
[TRACE] 2025-03-04 17:45:19.636 - [任务 37][Pg] - Table: test_td_timestamp_col already exists Index list: [TapIndex name test_td_ti_5210498611 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:20.295 - [任务 37][Pg] - Table: pg_bmsql_history already exists Index: pg_bmsql_h_4285255291 and will no longer create index 
[TRACE] 2025-03-04 17:45:20.296 - [任务 37][Pg] - Table: pg_bmsql_history already exists Index list: [TapIndex name pg_bmsql_h_4285255291 indexFields: [TapIndexField name hist_id fieldAsc true indexType null; TapIndexField name h_c_id fieldAsc true indexType null; TapIndexField name h_c_d_id fieldAsc true indexType null; TapIndexField name h_c_w_id fieldAsc true indexType null; TapIndexField name h_d_id fieldAsc true indexType null; TapIndexField name h_w_id fieldAsc true indexType null; TapIndexField name h_amount fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:21.012 - [任务 37][Pg] - Table: pg_w_td_fact_sec already exists Index: TAPIDX_d_fact_sec2b6369fc4004 and will no longer create index 
[TRACE] 2025-03-04 17:45:21.012 - [任务 37][Pg] - Table: pg_w_td_fact_sec already exists Index list: [TapIndex name TAPIDX_d_fact_sec2b6369fc4004 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:21.394 - [任务 37][Sybase-Wim] - Query snapshot row size completed: Sybase-Wim(21da900a-2b14-4437-afde-4ef2d77fee21) 
[TRACE] 2025-03-04 17:45:21.394 - [任务 37][Pg] - Table: pg_bmsql_config already exists Index: pg_bmsql_c_4925257571 and will no longer create index 
[TRACE] 2025-03-04 17:45:21.394 - [任务 37][Pg] - Table: pg_bmsql_config already exists Index list: [TapIndex name pg_bmsql_c_4925257571 indexFields: [TapIndexField name cfg_name fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:21.763 - [任务 37][Pg] - Table: t1_td_char_not_null will create Index: TapIndex name IDX_r_not_null7427970e8e91 indexFields: [TapIndexField name id fieldAsc true indexType null; ] 
[TRACE] 2025-03-04 17:45:21.787 - [任务 37][Pg] - Table: t1_td_char_not_null create Index: IDX_r_not_null7427970e8e91 successfully, cost 58ms 
[TRACE] 2025-03-04 17:45:21.787 - [任务 37][Pg] - Table: t1_td_char_not_null synchronize indexes completed, cost 152ms totally 
[TRACE] 2025-03-04 17:45:22.092 - [任务 37][Pg] - Table: td_char0 already exists Index: uidx_td_char0 and will no longer create index 
[TRACE] 2025-03-04 17:45:22.092 - [任务 37][Pg] - Table: td_char0 already exists Index list: [TapIndex name uidx_td_char0 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:22.295 - [任务 37][Pg] - Table: BMSQL_WAREHOUSE already exists Index: BMSQL_WARE_7485266691 and will no longer create index 
[TRACE] 2025-03-04 17:45:22.296 - [任务 37][Pg] - Table: BMSQL_WAREHOUSE already exists Index list: [TapIndex name BMSQL_WARE_7485266691 indexFields: [TapIndexField name W_ID fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:22.550 - [任务 37][Pg] - Table: s2p_text_idt already exists Index: s2p_text_idt_unique_index and will no longer create index 
[TRACE] 2025-03-04 17:45:22.550 - [任务 37][Pg] - Table: s2p_text_idt already exists Index list: [TapIndex name s2p_text_idt_unique_index indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:22.751 - [任务 37][Pg] - Table: bmsql_stock already exists Index: pk_stock and will no longer create index 
[TRACE] 2025-03-04 17:45:22.751 - [任务 37][Pg] - Table: bmsql_stock already exists Index list: [TapIndex name pk_stock indexFields: [TapIndexField name s_w_id fieldAsc true indexType null; TapIndexField name s_i_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:23.161 - [任务 37][Pg] - Table: td_fact_sec already exists Index list: [] 
[TRACE] 2025-03-04 17:45:23.357 - [任务 37][Pg] - Table: s2p_datetime_idt already exists Index: s2p_datetime_idt_unique_index and will no longer create index 
[TRACE] 2025-03-04 17:45:23.358 - [任务 37][Pg] - Table: s2p_datetime_idt already exists Index: s2p_datetime_idt_cahr_unique_index and will no longer create index 
[TRACE] 2025-03-04 17:45:23.358 - [任务 37][Pg] - Table: s2p_datetime_idt already exists Index list: [TapIndex name s2p_datetime_idt_unique_index indexFields: [TapIndexField name id fieldAsc true indexType null; ], TapIndex name s2p_datetime_idt_cahr_unique_index indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:23.529 - [任务 37][Pg] - Table: sync_test already exists Index: sync_test_id_5760020521 and will no longer create index 
[TRACE] 2025-03-04 17:45:23.532 - [任务 37][Pg] - Table: sync_test already exists Index list: [TapIndex name sync_test_id_5760020521 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:23.896 - [任务 37][Pg] - Table: s2p_text already exists Index: s2p_text_unique_index and will no longer create index 
[TRACE] 2025-03-04 17:45:23.897 - [任务 37][Pg] - Table: s2p_text already exists Index list: [TapIndex name s2p_text_unique_index indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:24.152 - [任务 37][Pg] - Table: pg_yyy_bmsql_oorder already exists Index: pg_yyy_bms_19360068971 and will no longer create index 
[TRACE] 2025-03-04 17:45:24.153 - [任务 37][Pg] - Table: pg_yyy_bmsql_oorder already exists Index list: [TapIndex name pg_yyy_bms_19360068971 indexFields: [TapIndexField name o_w_id fieldAsc true indexType null; TapIndexField name o_d_id fieldAsc true indexType null; TapIndexField name o_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:24.413 - [任务 37][Pg] - Table: pg_yyy_bmsql_item already exists Index: pg_yyy_bms_19680070111 and will no longer create index 
[TRACE] 2025-03-04 17:45:24.413 - [任务 37][Pg] - Table: pg_yyy_bmsql_item already exists Index list: [TapIndex name pg_yyy_bms_19680070111 indexFields: [TapIndexField name i_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:24.695 - [任务 37][Pg] - Table: pg_xxx_bmsql_stock already exists Index: pg_xxx_bms_20960074671 and will no longer create index 
[TRACE] 2025-03-04 17:45:24.696 - [任务 37][Pg] - Table: pg_xxx_bmsql_stock already exists Index list: [TapIndex name pg_xxx_bms_20960074671 indexFields: [TapIndexField name s_w_id fieldAsc true indexType null; TapIndexField name s_i_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:24.912 - [任务 37][Pg] - Table: pg_xxx_bmsql_oorder already exists Index: pg_xxx_bms_125240471 and will no longer create index 
[TRACE] 2025-03-04 17:45:24.912 - [任务 37][Pg] - Table: pg_xxx_bmsql_oorder already exists Index list: [TapIndex name pg_xxx_bms_125240471 indexFields: [TapIndexField name o_w_id fieldAsc true indexType null; TapIndexField name o_d_id fieldAsc true indexType null; TapIndexField name o_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:25.174 - [任务 37][Pg] - Table: BMSQL_OORDER already exists Index: BMSQL_OORD_11005279231 and will no longer create index 
[TRACE] 2025-03-04 17:45:25.174 - [任务 37][Pg] - Table: BMSQL_OORDER already exists Index list: [TapIndex name BMSQL_OORD_11005279231 indexFields: [TapIndexField name _id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:25.516 - [任务 37][Pg] - Table: pg_w_td_fact_sec_t already exists Index list: [] 
[TRACE] 2025-03-04 17:45:25.908 - [任务 37][Pg] - Table: testTimeStampWithNoIndex already exists Index list: [] 
[TRACE] 2025-03-04 17:45:26.163 - [任务 37][Pg] - Table: test1_testBit already exists Index: test1_test_6170502031 and will no longer create index 
[TRACE] 2025-03-04 17:45:26.163 - [任务 37][Pg] - Table: test1_testBit already exists Index list: [TapIndex name test1_test_6170502031 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:26.363 - [任务 37][Pg] - Table: bmsql_oorder already exists Index: pk_oorder and will no longer create index 
[TRACE] 2025-03-04 17:45:26.363 - [任务 37][Pg] - Table: bmsql_oorder will create Index: TapIndex name bmsql_oorder_idx1 indexFields: [TapIndexField name o_w_id fieldAsc true indexType null; TapIndexField name o_d_id fieldAsc true indexType null; TapIndexField name o_carrier_id fieldAsc true indexType null; TapIndexField name o_id fieldAsc true indexType null; ] 
[TRACE] 2025-03-04 17:45:26.413 - [任务 37][Pg] - Table: bmsql_oorder create Index: bmsql_oorder_idx1 successfully, cost 49ms 
[TRACE] 2025-03-04 17:45:26.413 - [任务 37][Pg] - Table: bmsql_oorder synchronize indexes completed, cost 110ms totally 
[TRACE] 2025-03-04 17:45:26.642 - [任务 37][Pg] - Table: testMoney already exists Index: testMoney_id_2170487781 and will no longer create index 
[TRACE] 2025-03-04 17:45:26.643 - [任务 37][Pg] - Table: testMoney already exists Index list: [TapIndex name testMoney_id_2170487781 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:26.803 - [任务 37][Pg] - Table: test1_td_null_col already exists Index: test1_td_n_11450520841 and will no longer create index 
[TRACE] 2025-03-04 17:45:26.804 - [任务 37][Pg] - Table: test1_td_null_col already exists Index list: [TapIndex name test1_td_n_11450520841 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:26.980 - [任务 37][Pg] - Table: testTimeStampTarget already exists Index: testTimeSt_id_18810547061 and will no longer create index 
[TRACE] 2025-03-04 17:45:26.981 - [任务 37][Pg] - Table: testTimeStampTarget already exists Index list: [TapIndex name testTimeSt_id_18810547061 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:27.232 - [任务 37][Pg] - Table: pg_xxx_bmsql_customer already exists Index: pg_xxx_bms_2685249591 and will no longer create index 
[TRACE] 2025-03-04 17:45:27.233 - [任务 37][Pg] - Table: pg_xxx_bmsql_customer already exists Index list: [TapIndex name pg_xxx_bms_2685249591 indexFields: [TapIndexField name c_w_id fieldAsc true indexType null; TapIndexField name c_d_id fieldAsc true indexType null; TapIndexField name c_id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:27.401 - [任务 37][Pg] - Table: testidentity already exists Index: testidenti_2490488921 and will no longer create index 
[TRACE] 2025-03-04 17:45:27.401 - [任务 37][Pg] - Table: testidentity already exists Index list: [TapIndex name testidenti_2490488921 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:27.585 - [任务 37][Pg] - Table: BMSQL_ORDER_LINE already exists Index: BMSQL_ORDE_9405273531 and will no longer create index 
[TRACE] 2025-03-04 17:45:27.585 - [任务 37][Pg] - Table: BMSQL_ORDER_LINE already exists Index list: [TapIndex name BMSQL_ORDE_9405273531 indexFields: [TapIndexField name OL_W_ID fieldAsc true indexType null; TapIndexField name OL_D_ID fieldAsc true indexType null; TapIndexField name OL_O_ID fieldAsc true indexType null; TapIndexField name OL_NUMBER fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:27.837 - [任务 37][Pg] - Table: td_text already exists Index: uidx_td_text and will no longer create index 
[TRACE] 2025-03-04 17:45:27.848 - [任务 37][Pg] - Table: td_text already exists Index list: [TapIndex name uidx_td_text indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:28.021 - [任务 37][Pg] - Table: testTimeStamp already exists Index: testTimeSt_id_20090551621 and will no longer create index 
[TRACE] 2025-03-04 17:45:28.021 - [任务 37][Pg] - Table: testTimeStamp already exists Index list: [TapIndex name testTimeSt_id_20090551621 indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:28.221 - [任务 37][Pg] - Table: bmsql_order_line already exists Index: pk_order_line and will no longer create index 
[TRACE] 2025-03-04 17:45:28.221 - [任务 37][Pg] - Table: bmsql_order_line already exists Index list: [TapIndex name pk_order_line indexFields: [TapIndexField name ol_w_id fieldAsc true indexType null; TapIndexField name ol_d_id fieldAsc true indexType null; TapIndexField name ol_o_id fieldAsc true indexType null; TapIndexField name ol_number fieldAsc true indexType null; ]] 
[TRACE] 2025-03-04 17:45:28.474 - [任务 37][Pg] - Table: s2p_datetime_idt_char already exists Index: s2p_datetime_idt_cahr_unique_index and will no longer create index 
[TRACE] 2025-03-04 17:45:28.475 - [任务 37][Pg] - Table: s2p_datetime_idt_char already exists Index list: [TapIndex name s2p_datetime_idt_cahr_unique_index indexFields: [TapIndexField name id fieldAsc true indexType null; ]] 
[INFO ] 2025-03-04 17:45:30.098 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_new_order has been completed batch read 
[INFO ] 2025-03-04 17:45:30.098 - [任务 37][Sybase-Wim] - Starting batch read from table: td_char_not_null 
[TRACE] 2025-03-04 17:45:30.098 - [任务 37][Sybase-Wim] - Table td_char_not_null is going to be initial synced 
[INFO ] 2025-03-04 17:45:30.350 - [任务 37][Sybase-Wim] - Table td_char_not_null has been completed batch read 
[INFO ] 2025-03-04 17:45:30.350 - [任务 37][Sybase-Wim] - Starting batch read from table: bmsql_customer 
[TRACE] 2025-03-04 17:45:30.350 - [任务 37][Sybase-Wim] - Table bmsql_customer is going to be initial synced 
[INFO ] 2025-03-04 17:45:30.611 - [任务 37][Sybase-Wim] - Table bmsql_customer has been completed batch read 
[INFO ] 2025-03-04 17:45:30.612 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_yyy_bmsql_history 
[TRACE] 2025-03-04 17:45:30.612 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_history is going to be initial synced 
[INFO ] 2025-03-04 17:45:38.005 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_history has been completed batch read 
[INFO ] 2025-03-04 17:45:38.005 - [任务 37][Sybase-Wim] - Starting batch read from table: testIden 
[TRACE] 2025-03-04 17:45:38.006 - [任务 37][Sybase-Wim] - Table testIden is going to be initial synced 
[INFO ] 2025-03-04 17:45:38.219 - [任务 37][Sybase-Wim] - Table testIden has been completed batch read 
[INFO ] 2025-03-04 17:45:38.219 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_sync_test 
[TRACE] 2025-03-04 17:45:38.219 - [任务 37][Sybase-Wim] - Table pg_sync_test is going to be initial synced 
[INFO ] 2025-03-04 17:45:38.377 - [任务 37][Sybase-Wim] - Table pg_sync_test has been completed batch read 
[INFO ] 2025-03-04 17:45:38.377 - [任务 37][Sybase-Wim] - Starting batch read from table: bmsql_config 
[TRACE] 2025-03-04 17:45:38.377 - [任务 37][Sybase-Wim] - Table bmsql_config is going to be initial synced 
[INFO ] 2025-03-04 17:45:38.543 - [任务 37][Sybase-Wim] - Table bmsql_config has been completed batch read 
[INFO ] 2025-03-04 17:45:38.543 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_bmsql_item 
[TRACE] 2025-03-04 17:45:38.543 - [任务 37][Sybase-Wim] - Table pg_bmsql_item is going to be initial synced 
[WARN ] 2025-03-04 17:45:38.919 - [任务 37][Pg] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: org.postgresql.util.PSQLException: ERROR: column "_no_pk_hash" of relation "testIden" does not exist
  位置：48
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:315)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:868)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2025-03-04 17:45:39.131 - [任务 37][Pg] - Retry operation TARGET_WRITE_RECORD, retry times 1/15, first retry time 2025-03-04 17:45:38, next retry time 2025-03-04 17:46:38 
[TRACE] 2025-03-04 17:46:14.985 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] running status set to false 
[TRACE] 2025-03-04 17:46:15.183 - [任务 37][Sybase-Wim] - Initial sync completed 
[INFO ] 2025-03-04 17:46:15.187 - [任务 37][Sybase-Wim] - Batch read completed. 
[TRACE] 2025-03-04 17:46:15.187 - [任务 37][Sybase-Wim] - PDK connector node stopped: HazelcastSourcePdkDataNode_21da900a-2b14-4437-afde-4ef2d77fee21_1741081492947 
[TRACE] 2025-03-04 17:46:15.189 - [任务 37][Sybase-Wim] - PDK connector node released: HazelcastSourcePdkDataNode_21da900a-2b14-4437-afde-4ef2d77fee21_1741081492947 
[TRACE] 2025-03-04 17:46:15.190 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] schema data cleaned 
[TRACE] 2025-03-04 17:46:15.196 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] monitor closed 
[TRACE] 2025-03-04 17:46:15.196 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] close complete, cost 214 ms 
[TRACE] 2025-03-04 17:46:15.242 - [任务 37][Pg] - Node Pg[06a0f647-93d9-4c90-aa90-052c2486ebac] running status set to false 
[TRACE] 2025-03-04 17:46:15.242 - [任务 37][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_06a0f647-93d9-4c90-aa90-052c2486ebac_1741081492821 
[TRACE] 2025-03-04 17:46:15.242 - [任务 37][Pg] - PDK connector node released: HazelcastTargetPdkDataNode_06a0f647-93d9-4c90-aa90-052c2486ebac_1741081492821 
[TRACE] 2025-03-04 17:46:15.242 - [任务 37][Pg] - Node Pg[06a0f647-93d9-4c90-aa90-052c2486ebac] schema data cleaned 
[TRACE] 2025-03-04 17:46:15.242 - [任务 37][Pg] - Node Pg[06a0f647-93d9-4c90-aa90-052c2486ebac] monitor closed 
[TRACE] 2025-03-04 17:46:15.244 - [任务 37][Pg] - Node Pg[06a0f647-93d9-4c90-aa90-052c2486ebac] close complete, cost 47 ms 
[INFO ] 2025-03-04 17:46:15.278 - [任务 37][Pg] - Retry operation TARGET_WRITE_RECORD failed, total cost 00:00:36.326000 
[TRACE] 2025-03-04 17:46:15.279 - [任务 37][Pg] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, io.tapdata.exception.NodeException: Node is stopped, need to exit write_record 
[ERROR] 2025-03-04 17:46:15.385 - [任务 37][Pg] - Unknown PDK exception occur, io.tapdata.exception.NodeException: Node is stopped, need to exit write_record <-- Error Message -->
Unknown PDK exception occur, io.tapdata.exception.NodeException: Node is stopped, need to exit write_record

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$69(HazelcastTargetPdkDataNode.java:1044)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	...

<-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1014)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$36(HazelcastTargetPdkDataNode.java:713)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:713)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:924)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:842)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:791)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$23(HazelcastTargetPdkBaseNode.java:751)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:637)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:723)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:775)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:722)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$69(HazelcastTargetPdkDataNode.java:1044)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 27 more

[TRACE] 2025-03-04 17:46:17.805 - [任务 37] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-04 17:46:17.805 - [任务 37] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@696eb512 
[TRACE] 2025-03-04 17:46:17.958 - [任务 37] - Stop task milestones: 67c6c4386ed4fc58ed97f950(任务 37)  
[TRACE] 2025-03-04 17:46:17.958 - [任务 37] - Stopped task aspect(s) 
[TRACE] 2025-03-04 17:46:17.959 - [任务 37] - Snapshot order controller have been removed 
[INFO ] 2025-03-04 17:46:17.959 - [任务 37] - Task stopped. 
[TRACE] 2025-03-04 17:46:17.974 - [任务 37] - Remove memory task client succeed, task: 任务 37[67c6c4386ed4fc58ed97f950] 
[TRACE] 2025-03-04 17:46:17.976 - [任务 37] - Destroy memory task client cache succeed, task: 任务 37[67c6c4386ed4fc58ed97f950] 
[TRACE] 2025-03-04 17:47:37.890 - [任务 37] - Start task milestones: 67c6c4386ed4fc58ed97f950(任务 37) 
[TRACE] 2025-03-04 17:47:37.891 - [任务 37] - Task initialization... 
[INFO ] 2025-03-04 17:47:38.549 - [任务 37] - Loading table structure completed 
[TRACE] 2025-03-04 17:47:38.552 - [任务 37] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-04 17:47:38.637 - [任务 37] - The engine receives 任务 37 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-04 17:47:38.637 - [任务 37] - Task started 
[TRACE] 2025-03-04 17:47:38.700 - [任务 37][Pg] - Node Pg[6a72a021-2b5c-44d6-8b62-19e4c7622b14] start preload schema,table counts: 107 
[TRACE] 2025-03-04 17:47:38.700 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] start preload schema,table counts: 107 
[TRACE] 2025-03-04 17:47:38.700 - [任务 37][Pg] - Node Pg[6a72a021-2b5c-44d6-8b62-19e4c7622b14] preload schema finished, cost 0 ms 
[TRACE] 2025-03-04 17:47:38.700 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] preload schema finished, cost 0 ms 
[INFO ] 2025-03-04 17:47:39.309 - [任务 37][Sybase-Wim] - Source connector(Sybase-Wim) initialization completed 
[TRACE] 2025-03-04 17:47:39.309 - [任务 37][Sybase-Wim] - Source node "Sybase-Wim" read batch size: 100 
[TRACE] 2025-03-04 17:47:39.312 - [任务 37][Sybase-Wim] - Source node "Sybase-Wim" event queue capacity: 200 
[TRACE] 2025-03-04 17:47:39.312 - [任务 37][Sybase-Wim] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-03-04 17:47:39.312 - [任务 37][Sybase-Wim] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-03-04 17:47:39.549 - [任务 37][Sybase-Wim] - Starting batch read from 107 tables 
[TRACE] 2025-03-04 17:47:39.573 - [任务 37][Sybase-Wim] - Initial sync started 
[INFO ] 2025-03-04 17:47:39.574 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_yyy_bmsql_new_order 
[TRACE] 2025-03-04 17:47:39.574 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_new_order is going to be initial synced 
[INFO ] 2025-03-04 17:47:39.755 - [任务 37][Pg] - Sink connector(Pg) initialization completed 
[TRACE] 2025-03-04 17:47:39.755 - [任务 37][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-04 17:47:39.755 - [任务 37][Pg] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-03-04 17:47:39.755 - [任务 37][Pg] - Apply table structure to target database 
[TRACE] 2025-03-04 17:47:39.873 - [任务 37][Pg] - The table pg_yyy_bmsql_new_order has already exist. 
[TRACE] 2025-03-04 17:47:39.874 - [任务 37][Pg] - The table td_char_not_null has already exist. 
[TRACE] 2025-03-04 17:47:39.936 - [任务 37][Pg] - Table: td_char_not_null already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:40.037 - [任务 37][Pg] - The table bmsql_customer has already exist. 
[TRACE] 2025-03-04 17:47:40.037 - [任务 37][Pg] - The table pg_yyy_bmsql_history has already exist. 
[TRACE] 2025-03-04 17:47:40.116 - [任务 37][Pg] - The table testIden has already exist. 
[WARN ] 2025-03-04 17:47:40.257 - [任务 37][Pg] - Create index failed ERROR: column "_no_pk_hash" does not exist, please execute it manually [create index "IDX_testIden0bf867578af3" on "public"."testIden"("_no_pk_hash" asc)] 
[TRACE] 2025-03-04 17:47:40.362 - [任务 37][Pg] - The table pg_sync_test has already exist. 
[TRACE] 2025-03-04 17:47:40.362 - [任务 37][Pg] - The table bmsql_config has already exist. 
[TRACE] 2025-03-04 17:47:40.465 - [任务 37][Pg] - The table pg_bmsql_item has already exist. 
[TRACE] 2025-03-04 17:47:40.465 - [任务 37][Pg] - The table pg_yyy_bmsql_stock has already exist. 
[TRACE] 2025-03-04 17:47:40.580 - [任务 37][Pg] - The table BMSQL_ITEM has already exist. 
[TRACE] 2025-03-04 17:47:40.580 - [任务 37][Pg] - The table pg_bmsql_customer has already exist. 
[TRACE] 2025-03-04 17:47:40.689 - [任务 37][Pg] - The table a_testMoneyv has already exist. 
[TRACE] 2025-03-04 17:47:40.690 - [任务 37][Pg] - The table tdText has already exist. 
[TRACE] 2025-03-04 17:47:40.775 - [任务 37][Pg] - Table: tdText already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:40.776 - [任务 37][Pg] - The table pg_yyy_bmsql_order_line has already exist. 
[TRACE] 2025-03-04 17:47:40.897 - [任务 37][Pg] - The table pg_xxx_bmsql_item has already exist. 
[TRACE] 2025-03-04 17:47:40.897 - [任务 37][Pg] - The table nouniquepdktableTarget has already exist. 
[TRACE] 2025-03-04 17:47:41.019 - [任务 37][Pg] - Table: nouniquepdktableTarget already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:41.019 - [任务 37][Pg] - The table testBit has already exist. 
[TRACE] 2025-03-04 17:47:41.092 - [任务 37][Pg] - The table BMSQL_OORDER_BACK2 has already exist. 
[TRACE] 2025-03-04 17:47:41.174 - [任务 37][Pg] - The table BMSQL_OORDER_BACK has already exist. 
[TRACE] 2025-03-04 17:47:41.174 - [任务 37][Pg] - The table nouniquepdktableTest has already exist. 
[TRACE] 2025-03-04 17:47:41.250 - [任务 37][Pg] - Table: nouniquepdktableTest already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:41.250 - [任务 37][Pg] - The table testTimeStampTwoWithNoIndex has already exist. 
[TRACE] 2025-03-04 17:47:41.343 - [任务 37][Pg] - Table: testTimeStampTwoWithNoIndex already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:41.343 - [任务 37][Pg] - The table pg_yyy_bmsql_customer has already exist. 
[TRACE] 2025-03-04 17:47:41.469 - [任务 37][Pg] - The table pg_bmsql_oorder has already exist. 
[TRACE] 2025-03-04 17:47:41.469 - [任务 37][Pg] - The table td_empty_string_frompg has already exist. 
[TRACE] 2025-03-04 17:47:41.553 - [任务 37][Pg] - Table: td_empty_string_frompg already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:41.553 - [任务 37][Pg] - The table test_td_null_col has already exist. 
[TRACE] 2025-03-04 17:47:41.625 - [任务 37][Pg] - The table BMSQL_HISTORY has already exist. 
[TRACE] 2025-03-04 17:47:41.760 - [任务 37][Pg] - The table test_date_with_text has already exist. 
[TRACE] 2025-03-04 17:47:41.760 - [任务 37][Pg] - Table: test_date_with_text already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:41.778 - [任务 37][Pg] - The table td_timestamp_col has already exist. 
[TRACE] 2025-03-04 17:47:41.852 - [任务 37][Pg] - The table nouniquepdktable1 has already exist. 
[TRACE] 2025-03-04 17:47:41.961 - [任务 37][Pg] - Table: nouniquepdktable1 already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:41.962 - [任务 37][Pg] - The table ap_standard_list_alias_OLD has already exist. 
[TRACE] 2025-03-04 17:47:42.091 - [任务 37][Pg] - Table: ap_standard_list_alias_OLD already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:42.094 - [任务 37][Pg] - The table pg_bmsql_order_line has already exist. 
[TRACE] 2025-03-04 17:47:42.150 - [任务 37][Pg] - The table testTimeStampTwoWithIndex has already exist. 
[TRACE] 2025-03-04 17:47:42.254 - [任务 37][Pg] - Table: testTimeStampTwoWithIndex already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; TapIndexField name testTimeStamp fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:42.254 - [任务 37][Pg] - The table td_empty_string has already exist. 
[TRACE] 2025-03-04 17:47:42.328 - [任务 37][Pg] - Table: td_empty_string already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:42.328 - [任务 37][Pg] - The table example has already exist. 
[TRACE] 2025-03-04 17:47:42.398 - [任务 37][Pg] - Table: example already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:42.398 - [任务 37][Pg] - The table BMSQL_DISTRICT has already exist. 
[TRACE] 2025-03-04 17:47:42.508 - [任务 37][Pg] - The table pg_xxx_bmsql_config has already exist. 
[TRACE] 2025-03-04 17:47:42.508 - [任务 37][Pg] - The table BMSQL_CONFIG has already exist. 
[TRACE] 2025-03-04 17:47:42.602 - [任务 37][Pg] - The table bmsql_history has already exist. 
[TRACE] 2025-03-04 17:47:42.602 - [任务 37][Pg] - The table pg_bmsql_stock has already exist. 
[TRACE] 2025-03-04 17:47:42.691 - [任务 37][Pg] - The table test_nouniquepdktable has already exist. 
[TRACE] 2025-03-04 17:47:42.788 - [任务 37][Pg] - Table: test_nouniquepdktable already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:42.789 - [任务 37][Pg] - The table td_only_text has already exist. 
[TRACE] 2025-03-04 17:47:42.850 - [任务 37][Pg] - Table: td_only_text already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:42.946 - [任务 37][Pg] - The table a_testMoney has already exist. 
[TRACE] 2025-03-04 17:47:42.947 - [任务 37][Pg] - The table pg_xxx_sync_test has already exist. 
[TRACE] 2025-03-04 17:47:43.015 - [任务 37][Pg] - The table pg_xxx_bmsql_warehouse has already exist. 
[TRACE] 2025-03-04 17:47:43.086 - [任务 37][Pg] - The table pg_bmsql_new_order has already exist. 
[TRACE] 2025-03-04 17:47:43.086 - [任务 37][Pg] - The table testNotDuplicate has already exist. 
[TRACE] 2025-03-04 17:47:43.151 - [任务 37][Pg] - Table: testNotDuplicate already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:43.230 - [任务 37][Pg] - The table pg_xxx_bmsql_history has already exist. 
[TRACE] 2025-03-04 17:47:43.230 - [任务 37][Pg] - The table pg_yyy_bmsql_config has already exist. 
[TRACE] 2025-03-04 17:47:43.281 - [任务 37][Pg] - The table BMSQL_NEW_ORDER has already exist. 
[TRACE] 2025-03-04 17:47:43.304 - [任务 37][Pg] - The table bmsql_new_order has already exist. 
[TRACE] 2025-03-04 17:47:43.362 - [任务 37][Pg] - The table pg_yyy_bmsql_district has already exist. 
[TRACE] 2025-03-04 17:47:43.373 - [任务 37][Pg] - The table pg_bmsql_warehouse has already exist. 
[TRACE] 2025-03-04 17:47:43.496 - [任务 37][Pg] - The table message_code has already exist. 
[WARN ] 2025-03-04 17:47:43.632 - [任务 37][Pg] - Create index failed ERROR: relation "message_code_idx" already exists, rename message_code_idx to message_code_idx_60c4 and retry ... 
[TRACE] 2025-03-04 17:47:43.636 - [任务 37][Pg] - The table bmsql_warehouse has already exist. 
[TRACE] 2025-03-04 17:47:43.727 - [任务 37][Pg] - The table pg_xxx_bmsql_new_order has already exist. 
[TRACE] 2025-03-04 17:47:43.728 - [任务 37][Pg] - The table nouniquepdktable has already exist. 
[WARN ] 2025-03-04 17:47:43.841 - [任务 37][Pg] - Create index failed ERROR: column "_no_pk_hash" does not exist, please execute it manually [create index "IDX_uepdktableecb709d766d0" on "public"."nouniquepdktable"("_no_pk_hash" asc)] 
[TRACE] 2025-03-04 17:47:43.842 - [任务 37][Pg] - The table t1_example has already exist. 
[TRACE] 2025-03-04 17:47:43.919 - [任务 37][Pg] - Table: t1_example already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:43.988 - [任务 37][Pg] - The table td_fact_sec_null has already exist. 
[TRACE] 2025-03-04 17:47:43.988 - [任务 37][Pg] - Table: td_fact_sec_null already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:44.045 - [任务 37][Pg] - The table testIdenSmallInt has already exist. 
[TRACE] 2025-03-04 17:47:44.125 - [任务 37][Pg] - The table pg_xxx_bmsql_district has already exist. 
[TRACE] 2025-03-04 17:47:44.192 - [任务 37][Pg] - The table pg_bmsql_district has already exist. 
[TRACE] 2025-03-04 17:47:44.294 - [任务 37][Pg] - The table bmsql_item has already exist. 
[TRACE] 2025-03-04 17:47:44.294 - [任务 37][Pg] - The table BMSQL_STOCK has already exist. 
[TRACE] 2025-03-04 17:47:44.433 - [任务 37][Pg] - The table ap_standard_list_subset has already exist. 
[WARN ] 2025-03-04 17:47:44.483 - [任务 37][Pg] - Create index failed ERROR: relation "ap_standard_list_subset_Idx" already exists, rename ap_standard_list_subset_Idx to ap_standard_list_subset_Idx_a902 and retry ... 
[TRACE] 2025-03-04 17:47:44.548 - [任务 37][Pg] - The table testChar has already exist. 
[TRACE] 2025-03-04 17:47:44.600 - [任务 37][Pg] - The table pg_xxx_bmsql_order_line has already exist. 
[TRACE] 2025-03-04 17:47:44.667 - [任务 37][Pg] - The table pg_yyy_bmsql_warehouse has already exist. 
[TRACE] 2025-03-04 17:47:44.806 - [任务 37][Pg] - The table BMSQL_CUSTOMER has already exist. 
[TRACE] 2025-03-04 17:47:44.806 - [任务 37][Pg] - The table testTimeStamp2 has already exist. 
[TRACE] 2025-03-04 17:47:44.923 - [任务 37][Pg] - The table bmsql_district has already exist. 
[TRACE] 2025-03-04 17:47:44.924 - [任务 37][Pg] - The table QC_Sample_Map has already exist. 
[TRACE] 2025-03-04 17:47:45.048 - [任务 37][Pg] - Table: QC_Sample_Map already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:45.048 - [任务 37][Pg] - The table ap_operation_list has already exist. 
[TRACE] 2025-03-04 17:47:45.191 - [任务 37][Pg] - Table: ap_operation_list already exists Index: TapIndex indexFields: [TapIndexField name term_id fieldAsc true indexType null; TapIndexField name snomed_code fieldAsc true indexType null; TapIndexField name snomed_class fieldAsc true indexType null; TapIndexField name snomed_seq fieldAsc true indexType null; TapIndexField name valid_from fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:45.191 - [任务 37][Pg] - The table td_null_col has already exist. 
[TRACE] 2025-03-04 17:47:45.350 - [任务 37][Pg] - The table a_td_empty_string has already exist. 
[TRACE] 2025-03-04 17:47:45.350 - [任务 37][Pg] - Table: a_td_empty_string already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:45.449 - [任务 37][Pg] - The table td_test_varchar has already exist. 
[TRACE] 2025-03-04 17:47:45.449 - [任务 37][Pg] - The table test_td_timestamp_col has already exist. 
[TRACE] 2025-03-04 17:47:45.573 - [任务 37][Pg] - The table pg_bmsql_history has already exist. 
[TRACE] 2025-03-04 17:47:45.574 - [任务 37][Pg] - The table pg_w_td_fact_sec has already exist. 
[TRACE] 2025-03-04 17:47:45.646 - [任务 37][Pg] - Table: pg_w_td_fact_sec already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:45.646 - [任务 37][Pg] - The table pg_bmsql_config has already exist. 
[TRACE] 2025-03-04 17:47:45.752 - [任务 37][Pg] - The table t1_td_char_not_null has already exist. 
[TRACE] 2025-03-04 17:47:45.752 - [任务 37][Pg] - Table: t1_td_char_not_null already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:45.819 - [任务 37][Pg] - The table td_char0 has already exist. 
[TRACE] 2025-03-04 17:47:45.819 - [任务 37][Pg] - Table: td_char0 already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:45.952 - [任务 37][Pg] - The table BMSQL_WAREHOUSE has already exist. 
[TRACE] 2025-03-04 17:47:45.952 - [任务 37][Pg] - The table s2p_text_idt has already exist. 
[TRACE] 2025-03-04 17:47:46.067 - [任务 37][Pg] - Table: s2p_text_idt already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:46.067 - [任务 37][Pg] - The table bmsql_stock has already exist. 
[TRACE] 2025-03-04 17:47:46.224 - [任务 37][Pg] - The table td_fact_sec has already exist. 
[TRACE] 2025-03-04 17:47:46.224 - [任务 37][Pg] - Table: td_fact_sec already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:46.331 - [任务 37][Pg] - The table s2p_datetime_idt has already exist. 
[TRACE] 2025-03-04 17:47:46.332 - [任务 37][Pg] - Table: s2p_datetime_idt already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:46.419 - [任务 37][Pg] - The table sync_test has already exist. 
[TRACE] 2025-03-04 17:47:46.419 - [任务 37][Pg] - The table s2p_text has already exist. 
[TRACE] 2025-03-04 17:47:46.495 - [任务 37][Pg] - Table: s2p_text already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:46.495 - [任务 37][Pg] - The table pg_yyy_bmsql_oorder has already exist. 
[TRACE] 2025-03-04 17:47:46.612 - [任务 37][Pg] - The table pg_yyy_bmsql_item has already exist. 
[TRACE] 2025-03-04 17:47:46.612 - [任务 37][Pg] - The table pg_xxx_bmsql_stock has already exist. 
[TRACE] 2025-03-04 17:47:46.765 - [任务 37][Pg] - The table pg_xxx_bmsql_oorder has already exist. 
[TRACE] 2025-03-04 17:47:46.765 - [任务 37][Pg] - The table BMSQL_OORDER has already exist. 
[TRACE] 2025-03-04 17:47:46.873 - [任务 37][Pg] - The table pg_w_td_fact_sec_t has already exist. 
[TRACE] 2025-03-04 17:47:46.873 - [任务 37][Pg] - Table: pg_w_td_fact_sec_t already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:46.984 - [任务 37][Pg] - The table testTimeStampWithNoIndex has already exist. 
[TRACE] 2025-03-04 17:47:46.984 - [任务 37][Pg] - Table: testTimeStampWithNoIndex already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:47.109 - [任务 37][Pg] - The table test1_testBit has already exist. 
[TRACE] 2025-03-04 17:47:47.109 - [任务 37][Pg] - The table bmsql_oorder has already exist. 
[TRACE] 2025-03-04 17:47:47.250 - [任务 37][Pg] - The table testMoney has already exist. 
[TRACE] 2025-03-04 17:47:47.250 - [任务 37][Pg] - The table test1_td_null_col has already exist. 
[TRACE] 2025-03-04 17:47:47.342 - [任务 37][Pg] - The table testTimeStampTarget has already exist. 
[TRACE] 2025-03-04 17:47:47.343 - [任务 37][Pg] - The table pg_xxx_bmsql_customer has already exist. 
[TRACE] 2025-03-04 17:47:47.445 - [任务 37][Pg] - The table testidentity has already exist. 
[TRACE] 2025-03-04 17:47:47.445 - [任务 37][Pg] - The table BMSQL_ORDER_LINE has already exist. 
[TRACE] 2025-03-04 17:47:47.538 - [任务 37][Pg] - The table td_text has already exist. 
[TRACE] 2025-03-04 17:47:47.538 - [任务 37][Pg] - Table: td_text already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 17:47:47.639 - [任务 37][Pg] - The table testTimeStamp has already exist. 
[TRACE] 2025-03-04 17:47:47.639 - [任务 37][Pg] - The table bmsql_order_line has already exist. 
[TRACE] 2025-03-04 17:47:47.751 - [任务 37][Pg] - The table s2p_datetime_idt_char has already exist. 
[TRACE] 2025-03-04 17:47:47.751 - [任务 37][Pg] - Table: s2p_datetime_idt_char already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[INFO ] 2025-03-04 17:47:47.953 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_new_order has been completed batch read 
[INFO ] 2025-03-04 17:47:47.953 - [任务 37][Sybase-Wim] - Starting batch read from table: td_char_not_null 
[TRACE] 2025-03-04 17:47:47.953 - [任务 37][Sybase-Wim] - Table td_char_not_null is going to be initial synced 
[INFO ] 2025-03-04 17:47:48.161 - [任务 37][Sybase-Wim] - Table td_char_not_null has been completed batch read 
[INFO ] 2025-03-04 17:47:48.161 - [任务 37][Sybase-Wim] - Starting batch read from table: bmsql_customer 
[TRACE] 2025-03-04 17:47:48.161 - [任务 37][Sybase-Wim] - Table bmsql_customer is going to be initial synced 
[INFO ] 2025-03-04 17:47:48.314 - [任务 37][Sybase-Wim] - Table bmsql_customer has been completed batch read 
[INFO ] 2025-03-04 17:47:48.316 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_yyy_bmsql_history 
[TRACE] 2025-03-04 17:47:48.316 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_history is going to be initial synced 
[INFO ] 2025-03-04 17:47:51.424 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_history has been completed batch read 
[INFO ] 2025-03-04 17:47:51.424 - [任务 37][Sybase-Wim] - Starting batch read from table: testIden 
[TRACE] 2025-03-04 17:47:51.424 - [任务 37][Sybase-Wim] - Table testIden is going to be initial synced 
[INFO ] 2025-03-04 17:47:51.591 - [任务 37][Sybase-Wim] - Table testIden has been completed batch read 
[INFO ] 2025-03-04 17:47:51.592 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_sync_test 
[TRACE] 2025-03-04 17:47:51.592 - [任务 37][Sybase-Wim] - Table pg_sync_test is going to be initial synced 
[INFO ] 2025-03-04 17:47:51.740 - [任务 37][Sybase-Wim] - Table pg_sync_test has been completed batch read 
[INFO ] 2025-03-04 17:47:51.740 - [任务 37][Sybase-Wim] - Starting batch read from table: bmsql_config 
[TRACE] 2025-03-04 17:47:51.740 - [任务 37][Sybase-Wim] - Table bmsql_config is going to be initial synced 
[INFO ] 2025-03-04 17:47:51.849 - [任务 37][Sybase-Wim] - Table bmsql_config has been completed batch read 
[INFO ] 2025-03-04 17:47:51.849 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_bmsql_item 
[TRACE] 2025-03-04 17:47:51.849 - [任务 37][Sybase-Wim] - Table pg_bmsql_item is going to be initial synced 
[WARN ] 2025-03-04 17:47:52.326 - [任务 37][Pg] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: org.postgresql.util.PSQLException: ERROR: column "_no_pk_hash" of relation "testIden" does not exist
  位置：48
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:315)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:868)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2025-03-04 17:47:52.529 - [任务 37][Pg] - Retry operation TARGET_WRITE_RECORD, retry times 1/15, first retry time 2025-03-04 17:47:52, next retry time 2025-03-04 17:48:52 
[TRACE] 2025-03-04 17:47:58.205 - [任务 37][Sybase-Wim] - Query snapshot row size completed: Sybase-Wim(21da900a-2b14-4437-afde-4ef2d77fee21) 
[WARN ] 2025-03-04 17:48:52.631 - [任务 37][Pg] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: org.postgresql.util.PSQLException: ERROR: column "_no_pk_hash" of relation "testIden" does not exist
  位置：48
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:315)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:868)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2025-03-04 17:48:52.837 - [任务 37][Pg] - Retry operation TARGET_WRITE_RECORD, retry times 2/15, first retry time 2025-03-04 17:47:52, next retry time 2025-03-04 17:49:52 
[WARN ] 2025-03-04 17:49:52.834 - [任务 37][Pg] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: org.postgresql.util.PSQLException: ERROR: column "_no_pk_hash" of relation "testIden" does not exist
  位置：48
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:315)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:868)
	...
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[INFO ] 2025-03-04 17:49:52.835 - [任务 37][Pg] - Retry operation TARGET_WRITE_RECORD, retry times 3/15, first retry time 2025-03-04 17:47:52, next retry time 2025-03-04 17:50:52 
[WARN ] 2025-03-04 17:50:53.177 - [任务 37][Pg] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: org.postgresql.util.PSQLException: ERROR: column "_no_pk_hash" of relation "testIden" does not exist
  位置：48
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:315)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:868)
	...
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[INFO ] 2025-03-04 17:50:53.379 - [任务 37][Pg] - Retry operation TARGET_WRITE_RECORD, retry times 4/15, first retry time 2025-03-04 17:47:52, next retry time 2025-03-04 17:51:53 
[WARN ] 2025-03-04 17:51:53.457 - [任务 37][Pg] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: org.postgresql.util.PSQLException: ERROR: column "_no_pk_hash" of relation "testIden" does not exist
  位置：48
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:315)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:868)
	...
 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[INFO ] 2025-03-04 17:51:53.659 - [任务 37][Pg] - Retry operation TARGET_WRITE_RECORD, retry times 5/15, first retry time 2025-03-04 17:47:52, next retry time 2025-03-04 17:52:53 
[TRACE] 2025-03-04 17:52:15.464 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] running status set to false 
[TRACE] 2025-03-04 17:57:18.172 - [任务 37] - Task initialization... 
[TRACE] 2025-03-04 17:57:18.178 - [任务 37] - Start task milestones: 67c6c4386ed4fc58ed97f950(任务 37) 
[INFO ] 2025-03-04 17:57:21.704 - [任务 37] - Loading table structure completed 
[TRACE] 2025-03-04 17:57:21.839 - [任务 37] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-04 17:57:21.839 - [任务 37] - The engine receives 任务 37 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-04 17:57:22.106 - [任务 37] - Task started 
[TRACE] 2025-03-04 17:57:22.264 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] start preload schema,table counts: 107 
[TRACE] 2025-03-04 17:57:22.267 - [任务 37][Pg] - Node Pg[6a72a021-2b5c-44d6-8b62-19e4c7622b14] start preload schema,table counts: 107 
[TRACE] 2025-03-04 17:57:22.272 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] preload schema finished, cost 0 ms 
[TRACE] 2025-03-04 17:57:22.275 - [任务 37][Pg] - Node Pg[6a72a021-2b5c-44d6-8b62-19e4c7622b14] preload schema finished, cost 0 ms 
[INFO ] 2025-03-04 17:57:22.683 - [任务 37][Sybase-Wim] - Source connector(Sybase-Wim) initialization completed 
[TRACE] 2025-03-04 17:57:22.696 - [任务 37][Sybase-Wim] - Source node "Sybase-Wim" read batch size: 100 
[TRACE] 2025-03-04 17:57:22.703 - [任务 37][Sybase-Wim] - Source node "Sybase-Wim" event queue capacity: 200 
[INFO ] 2025-03-04 17:57:22.724 - [任务 37][Pg] - Sink connector(Pg) initialization completed 
[TRACE] 2025-03-04 17:57:22.725 - [任务 37][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2025-03-04 17:57:22.726 - [任务 37][Sybase-Wim] - Found exists breakpoint, will decode batch/stream offset 
[TRACE] 2025-03-04 17:57:22.734 - [任务 37][Pg] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-03-04 17:57:22.737 - [任务 37][Pg] - Apply table structure to target database 
[INFO ] 2025-03-04 17:57:22.742 - [任务 37][Sybase-Wim] - Use existing batch read offset: {"pg_yyy_bmsql_new_order":{"batch_read_connector_status":"OVER"},"td_char_not_null":{"batch_read_connector_status":"OVER"},"pg_yyy_bmsql_history":{"batch_read_connector_status":"OVER"},"bmsql_customer":{"batch_read_connector_status":"OVER"}} 
[TRACE] 2025-03-04 17:57:22.743 - [任务 37][Sybase-Wim] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-03-04 17:57:23.082 - [任务 37][Sybase-Wim] - Starting batch read from 107 tables 
[TRACE] 2025-03-04 17:57:23.084 - [任务 37][Sybase-Wim] - Initial sync started 
[TRACE] 2025-03-04 17:57:23.084 - [任务 37][Sybase-Wim] - Skip table [pg_yyy_bmsql_new_order] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-03-04 17:57:23.096 - [任务 37][Sybase-Wim] - Skip table [td_char_not_null] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-03-04 17:57:23.097 - [任务 37][Sybase-Wim] - Skip table [bmsql_customer] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-03-04 17:57:23.104 - [任务 37][Sybase-Wim] - Skip table [pg_yyy_bmsql_history] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2025-03-04 17:57:23.105 - [任务 37][Sybase-Wim] - Starting batch read from table: testIden 
[TRACE] 2025-03-04 17:57:23.105 - [任务 37][Sybase-Wim] - Table testIden is going to be initial synced 
[INFO ] 2025-03-04 17:57:23.352 - [任务 37][Sybase-Wim] - Table testIden has been completed batch read 
[INFO ] 2025-03-04 17:57:23.352 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_sync_test 
[TRACE] 2025-03-04 17:57:23.352 - [任务 37][Sybase-Wim] - Table pg_sync_test is going to be initial synced 
[INFO ] 2025-03-04 17:57:23.477 - [任务 37][Sybase-Wim] - Table pg_sync_test has been completed batch read 
[INFO ] 2025-03-04 17:57:23.480 - [任务 37][Sybase-Wim] - Starting batch read from table: bmsql_config 
[TRACE] 2025-03-04 17:57:23.480 - [任务 37][Sybase-Wim] - Table bmsql_config is going to be initial synced 
[INFO ] 2025-03-04 17:57:23.586 - [任务 37][Sybase-Wim] - Table bmsql_config has been completed batch read 
[INFO ] 2025-03-04 17:57:23.587 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_bmsql_item 
[TRACE] 2025-03-04 17:57:23.587 - [任务 37][Sybase-Wim] - Table pg_bmsql_item is going to be initial synced 
[WARN ] 2025-03-04 17:57:23.905 - [任务 37][Pg] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: org.postgresql.util.PSQLException: ERROR: column "_no_pk_hash" of relation "testIden" does not exist
  位置：48
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:315)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:868)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2025-03-04 17:57:23.979 - [任务 37][Pg] - Retry operation TARGET_WRITE_RECORD, retry times 1/15, first retry time 2025-03-04 17:57:23, next retry time 2025-03-04 17:58:23 
[TRACE] 2025-03-04 17:57:41.300 - [任务 37][Sybase-Wim] - Query snapshot row size completed: Sybase-Wim(21da900a-2b14-4437-afde-4ef2d77fee21) 
[WARN ] 2025-03-04 17:58:24.445 - [任务 37][Pg] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: org.postgresql.util.PSQLException: ERROR: column "_no_pk_hash" of relation "testIden" does not exist
  位置：48
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:315)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:868)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2025-03-04 17:58:24.446 - [任务 37][Pg] - Retry operation TARGET_WRITE_RECORD, retry times 2/15, first retry time 2025-03-04 17:57:23, next retry time 2025-03-04 17:59:24 
[WARN ] 2025-03-04 17:59:24.686 - [任务 37][Pg] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: org.postgresql.util.PSQLException: ERROR: column "_no_pk_hash" of relation "testIden" does not exist
  位置：48
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:315)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:868)
	...
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[INFO ] 2025-03-04 17:59:24.890 - [任务 37][Pg] - Retry operation TARGET_WRITE_RECORD, retry times 3/15, first retry time 2025-03-04 17:57:23, next retry time 2025-03-04 18:00:24 
[WARN ] 2025-03-04 18:00:24.977 - [任务 37][Pg] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: org.postgresql.util.PSQLException: ERROR: column "_no_pk_hash" of relation "testIden" does not exist
  位置：48
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:315)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:868)
	...
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[INFO ] 2025-03-04 18:00:25.182 - [任务 37][Pg] - Retry operation TARGET_WRITE_RECORD, retry times 4/15, first retry time 2025-03-04 17:57:23, next retry time 2025-03-04 18:01:24 
[WARN ] 2025-03-04 18:01:25.608 - [任务 37][Pg] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: org.postgresql.util.PSQLException: ERROR: column "_no_pk_hash" of relation "testIden" does not exist
  位置：48
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:315)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:868)
	...
 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[INFO ] 2025-03-04 18:01:25.812 - [任务 37][Pg] - Retry operation TARGET_WRITE_RECORD, retry times 5/15, first retry time 2025-03-04 17:57:23, next retry time 2025-03-04 18:02:25 
[WARN ] 2025-03-04 18:02:25.880 - [任务 37][Pg] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: org.postgresql.util.PSQLException: ERROR: column "_no_pk_hash" of relation "testIden" does not exist
  位置：48
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:315)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:868)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2025-03-04 18:02:26.084 - [任务 37][Pg] - Retry operation TARGET_WRITE_RECORD, retry times 6/15, first retry time 2025-03-04 17:57:23, next retry time 2025-03-04 18:03:25 
[TRACE] 2025-03-04 18:02:46.912 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] running status set to false 
[TRACE] 2025-03-04 18:02:47.147 - [任务 37][Sybase-Wim] - Initial sync completed 
[INFO ] 2025-03-04 18:02:47.147 - [任务 37][Sybase-Wim] - Batch read completed. 
[INFO ] 2025-03-04 18:02:47.147 - [任务 37][Sybase-Wim] - Task run completed 
[TRACE] 2025-03-04 18:02:47.157 - [任务 37][Sybase-Wim] - PDK connector node stopped: HazelcastSourcePdkDataNode_21da900a-2b14-4437-afde-4ef2d77fee21_1741082242387 
[TRACE] 2025-03-04 18:02:47.158 - [任务 37][Sybase-Wim] - PDK connector node released: HazelcastSourcePdkDataNode_21da900a-2b14-4437-afde-4ef2d77fee21_1741082242387 
[TRACE] 2025-03-04 18:02:47.159 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] schema data cleaned 
[TRACE] 2025-03-04 18:02:47.159 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] monitor closed 
[TRACE] 2025-03-04 18:02:47.162 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] close complete, cost 282 ms 
[TRACE] 2025-03-04 18:02:47.162 - [任务 37][Pg] - Node Pg[6a72a021-2b5c-44d6-8b62-19e4c7622b14] running status set to false 
[TRACE] 2025-03-04 18:02:47.177 - [任务 37][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_6a72a021-2b5c-44d6-8b62-19e4c7622b14_1741082242325 
[TRACE] 2025-03-04 18:02:47.177 - [任务 37][Pg] - PDK connector node released: HazelcastTargetPdkDataNode_6a72a021-2b5c-44d6-8b62-19e4c7622b14_1741082242325 
[TRACE] 2025-03-04 18:02:47.178 - [任务 37][Pg] - Node Pg[6a72a021-2b5c-44d6-8b62-19e4c7622b14] schema data cleaned 
[TRACE] 2025-03-04 18:02:47.178 - [任务 37][Pg] - Node Pg[6a72a021-2b5c-44d6-8b62-19e4c7622b14] monitor closed 
[INFO ] 2025-03-04 18:02:47.179 - [任务 37][Pg] - Retry operation TARGET_WRITE_RECORD failed, total cost 00:05:23.261000 
[TRACE] 2025-03-04 18:02:47.179 - [任务 37][Pg] - Node Pg[6a72a021-2b5c-44d6-8b62-19e4c7622b14] close complete, cost 16 ms 
[TRACE] 2025-03-04 18:02:47.215 - [任务 37][Pg] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, io.tapdata.exception.NodeException: Node is stopped, need to exit write_record 
[ERROR] 2025-03-04 18:02:47.216 - [任务 37][Pg] - Unknown PDK exception occur, io.tapdata.exception.NodeException: Node is stopped, need to exit write_record <-- Error Message -->
Unknown PDK exception occur, io.tapdata.exception.NodeException: Node is stopped, need to exit write_record

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$69(HazelcastTargetPdkDataNode.java:1044)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	...

<-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1014)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$36(HazelcastTargetPdkDataNode.java:713)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:713)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:924)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:842)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:791)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$23(HazelcastTargetPdkBaseNode.java:751)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:637)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:723)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:775)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:722)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$69(HazelcastTargetPdkDataNode.java:1044)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 27 more

[TRACE] 2025-03-04 18:02:48.357 - [任务 37] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-04 18:02:48.359 - [任务 37] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@14e5f9cd 
[TRACE] 2025-03-04 18:02:48.360 - [任务 37] - Stop task milestones: 67c6c4386ed4fc58ed97f950(任务 37)  
[TRACE] 2025-03-04 18:02:48.486 - [任务 37] - Stopped task aspect(s) 
[TRACE] 2025-03-04 18:02:48.486 - [任务 37] - Snapshot order controller have been removed 
[INFO ] 2025-03-04 18:02:48.487 - [任务 37] - Task stopped. 
[TRACE] 2025-03-04 18:02:48.511 - [任务 37] - Remove memory task client succeed, task: 任务 37[67c6c4386ed4fc58ed97f950] 
[TRACE] 2025-03-04 18:02:48.511 - [任务 37] - Destroy memory task client cache succeed, task: 任务 37[67c6c4386ed4fc58ed97f950] 
[TRACE] 2025-03-04 18:03:57.175 - [任务 37] - Task initialization... 
[TRACE] 2025-03-04 18:03:57.175 - [任务 37] - Start task milestones: 67c6c4386ed4fc58ed97f950(任务 37) 
[INFO ] 2025-03-04 18:03:57.894 - [任务 37] - Loading table structure completed 
[TRACE] 2025-03-04 18:03:57.894 - [任务 37] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-04 18:03:57.983 - [任务 37] - The engine receives 任务 37 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-04 18:03:57.983 - [任务 37] - Task started 
[TRACE] 2025-03-04 18:03:58.042 - [任务 37][Pg] - Node Pg[6a72a021-2b5c-44d6-8b62-19e4c7622b14] start preload schema,table counts: 107 
[TRACE] 2025-03-04 18:03:58.042 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] start preload schema,table counts: 107 
[TRACE] 2025-03-04 18:03:58.042 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] preload schema finished, cost 0 ms 
[TRACE] 2025-03-04 18:03:58.043 - [任务 37][Pg] - Node Pg[6a72a021-2b5c-44d6-8b62-19e4c7622b14] preload schema finished, cost 0 ms 
[INFO ] 2025-03-04 18:03:58.854 - [任务 37][Sybase-Wim] - Source connector(Sybase-Wim) initialization completed 
[TRACE] 2025-03-04 18:03:58.855 - [任务 37][Sybase-Wim] - Source node "Sybase-Wim" read batch size: 100 
[TRACE] 2025-03-04 18:03:58.855 - [任务 37][Sybase-Wim] - Source node "Sybase-Wim" event queue capacity: 200 
[TRACE] 2025-03-04 18:03:58.855 - [任务 37][Sybase-Wim] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-03-04 18:03:58.855 - [任务 37][Sybase-Wim] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-03-04 18:03:58.998 - [任务 37][Pg] - Sink connector(Pg) initialization completed 
[TRACE] 2025-03-04 18:03:58.998 - [任务 37][Pg] - Node(Pg) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-04 18:03:58.998 - [任务 37][Pg] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-04 18:03:59.000 - [任务 37][Pg] - Apply table structure to target database 
[TRACE] 2025-03-04 18:03:59.136 - [任务 37][Pg] - The table pg_yyy_bmsql_new_order has already exist. 
[INFO ] 2025-03-04 18:03:59.146 - [任务 37][Sybase-Wim] - Starting batch read from 107 tables 
[TRACE] 2025-03-04 18:03:59.167 - [任务 37][Sybase-Wim] - Initial sync started 
[INFO ] 2025-03-04 18:03:59.167 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_yyy_bmsql_new_order 
[TRACE] 2025-03-04 18:03:59.246 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_new_order is going to be initial synced 
[TRACE] 2025-03-04 18:03:59.246 - [任务 37][Pg] - The table td_char_not_null has already exist. 
[TRACE] 2025-03-04 18:03:59.246 - [任务 37][Pg] - Table: td_char_not_null already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:03:59.317 - [任务 37][Pg] - The table bmsql_customer has already exist. 
[TRACE] 2025-03-04 18:03:59.318 - [任务 37][Pg] - The table pg_yyy_bmsql_history has already exist. 
[TRACE] 2025-03-04 18:03:59.467 - [任务 37][Pg] - The table testIden has already exist. 
[WARN ] 2025-03-04 18:03:59.467 - [任务 37][Pg] - Create index failed ERROR: column "_no_pk_hash" does not exist, please execute it manually [create index "IDX_testIden2e1a3455bef6" on "public"."testIden"("_no_pk_hash" asc)] 
[TRACE] 2025-03-04 18:03:59.557 - [任务 37][Pg] - The table pg_sync_test has already exist. 
[TRACE] 2025-03-04 18:03:59.557 - [任务 37][Pg] - The table bmsql_config has already exist. 
[TRACE] 2025-03-04 18:03:59.646 - [任务 37][Pg] - The table pg_bmsql_item has already exist. 
[TRACE] 2025-03-04 18:03:59.648 - [任务 37][Pg] - The table pg_yyy_bmsql_stock has already exist. 
[TRACE] 2025-03-04 18:03:59.763 - [任务 37][Pg] - The table BMSQL_ITEM has already exist. 
[TRACE] 2025-03-04 18:03:59.763 - [任务 37][Pg] - The table pg_bmsql_customer has already exist. 
[TRACE] 2025-03-04 18:03:59.872 - [任务 37][Pg] - The table a_testMoneyv has already exist. 
[TRACE] 2025-03-04 18:03:59.872 - [任务 37][Pg] - The table tdText has already exist. 
[TRACE] 2025-03-04 18:03:59.947 - [任务 37][Pg] - Table: tdText already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:03:59.947 - [任务 37][Pg] - The table pg_yyy_bmsql_order_line has already exist. 
[TRACE] 2025-03-04 18:04:00.037 - [任务 37][Pg] - The table pg_xxx_bmsql_item has already exist. 
[TRACE] 2025-03-04 18:04:00.038 - [任务 37][Pg] - The table nouniquepdktableTarget has already exist. 
[TRACE] 2025-03-04 18:04:00.158 - [任务 37][Pg] - Table: nouniquepdktableTarget already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:00.159 - [任务 37][Pg] - The table testBit has already exist. 
[TRACE] 2025-03-04 18:04:00.281 - [任务 37][Pg] - The table BMSQL_OORDER_BACK2 has already exist. 
[TRACE] 2025-03-04 18:04:00.281 - [任务 37][Pg] - The table BMSQL_OORDER_BACK has already exist. 
[TRACE] 2025-03-04 18:04:00.395 - [任务 37][Pg] - The table nouniquepdktableTest has already exist. 
[TRACE] 2025-03-04 18:04:00.395 - [任务 37][Pg] - Table: nouniquepdktableTest already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:00.516 - [任务 37][Pg] - The table testTimeStampTwoWithNoIndex has already exist. 
[TRACE] 2025-03-04 18:04:00.516 - [任务 37][Pg] - Table: testTimeStampTwoWithNoIndex already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:00.611 - [任务 37][Pg] - The table pg_yyy_bmsql_customer has already exist. 
[TRACE] 2025-03-04 18:04:00.611 - [任务 37][Pg] - The table pg_bmsql_oorder has already exist. 
[TRACE] 2025-03-04 18:04:00.750 - [任务 37][Pg] - The table td_empty_string_frompg has already exist. 
[TRACE] 2025-03-04 18:04:00.750 - [任务 37][Pg] - Table: td_empty_string_frompg already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:00.833 - [任务 37][Pg] - The table test_td_null_col has already exist. 
[TRACE] 2025-03-04 18:04:00.833 - [任务 37][Pg] - The table BMSQL_HISTORY has already exist. 
[TRACE] 2025-03-04 18:04:00.999 - [任务 37][Pg] - The table test_date_with_text has already exist. 
[TRACE] 2025-03-04 18:04:00.999 - [任务 37][Pg] - Table: test_date_with_text already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:01.135 - [任务 37][Pg] - The table td_timestamp_col has already exist. 
[TRACE] 2025-03-04 18:04:01.135 - [任务 37][Pg] - The table nouniquepdktable1 has already exist. 
[TRACE] 2025-03-04 18:04:01.244 - [任务 37][Pg] - Table: nouniquepdktable1 already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:01.245 - [任务 37][Pg] - The table ap_standard_list_alias_OLD has already exist. 
[WARN ] 2025-03-04 18:04:01.537 - [任务 37][Pg] - Create index failed ERROR: column "_no_pk_hash" does not exist, please execute it manually [create index "IDX__alias_OLDb6daeffee997" on "public"."ap_standard_list_alias_OLD"("_no_pk_hash" asc)] 
[TRACE] 2025-03-04 18:04:01.537 - [任务 37][Pg] - The table pg_bmsql_order_line has already exist. 
[TRACE] 2025-03-04 18:04:01.697 - [任务 37][Pg] - The table testTimeStampTwoWithIndex has already exist. 
[TRACE] 2025-03-04 18:04:01.697 - [任务 37][Pg] - Table: testTimeStampTwoWithIndex already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; TapIndexField name testTimeStamp fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:01.795 - [任务 37][Pg] - The table td_empty_string has already exist. 
[TRACE] 2025-03-04 18:04:01.796 - [任务 37][Pg] - Table: td_empty_string already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:01.882 - [任务 37][Pg] - The table example has already exist. 
[TRACE] 2025-03-04 18:04:01.882 - [任务 37][Pg] - Table: example already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:02.007 - [任务 37][Pg] - The table BMSQL_DISTRICT has already exist. 
[TRACE] 2025-03-04 18:04:02.007 - [任务 37][Pg] - The table pg_xxx_bmsql_config has already exist. 
[TRACE] 2025-03-04 18:04:02.128 - [任务 37][Pg] - The table BMSQL_CONFIG has already exist. 
[TRACE] 2025-03-04 18:04:02.128 - [任务 37][Pg] - The table bmsql_history has already exist. 
[TRACE] 2025-03-04 18:04:02.259 - [任务 37][Pg] - The table pg_bmsql_stock has already exist. 
[TRACE] 2025-03-04 18:04:02.260 - [任务 37][Pg] - The table test_nouniquepdktable has already exist. 
[TRACE] 2025-03-04 18:04:02.333 - [任务 37][Pg] - Table: test_nouniquepdktable already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:02.334 - [任务 37][Pg] - The table td_only_text has already exist. 
[TRACE] 2025-03-04 18:04:02.410 - [任务 37][Pg] - Table: td_only_text already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:02.410 - [任务 37][Pg] - The table a_testMoney has already exist. 
[TRACE] 2025-03-04 18:04:02.496 - [任务 37][Pg] - The table pg_xxx_sync_test has already exist. 
[TRACE] 2025-03-04 18:04:02.497 - [任务 37][Pg] - The table pg_xxx_bmsql_warehouse has already exist. 
[TRACE] 2025-03-04 18:04:02.629 - [任务 37][Pg] - The table pg_bmsql_new_order has already exist. 
[TRACE] 2025-03-04 18:04:02.633 - [任务 37][Pg] - The table testNotDuplicate has already exist. 
[TRACE] 2025-03-04 18:04:02.738 - [任务 37][Pg] - Table: testNotDuplicate already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:02.738 - [任务 37][Pg] - The table pg_xxx_bmsql_history has already exist. 
[TRACE] 2025-03-04 18:04:02.825 - [任务 37][Pg] - The table pg_yyy_bmsql_config has already exist. 
[TRACE] 2025-03-04 18:04:02.825 - [任务 37][Pg] - The table BMSQL_NEW_ORDER has already exist. 
[TRACE] 2025-03-04 18:04:02.911 - [任务 37][Pg] - The table bmsql_new_order has already exist. 
[TRACE] 2025-03-04 18:04:02.911 - [任务 37][Pg] - The table pg_yyy_bmsql_district has already exist. 
[TRACE] 2025-03-04 18:04:03.060 - [任务 37][Pg] - The table pg_bmsql_warehouse has already exist. 
[TRACE] 2025-03-04 18:04:03.060 - [任务 37][Pg] - The table message_code has already exist. 
[WARN ] 2025-03-04 18:04:03.206 - [任务 37][Pg] - Create index failed ERROR: relation "message_code_idx" already exists, rename message_code_idx to message_code_idx_b2dd and retry ... 
[TRACE] 2025-03-04 18:04:03.328 - [任务 37][Pg] - The table bmsql_warehouse has already exist. 
[TRACE] 2025-03-04 18:04:03.329 - [任务 37][Pg] - The table pg_xxx_bmsql_new_order has already exist. 
[TRACE] 2025-03-04 18:04:03.492 - [任务 37][Pg] - The table nouniquepdktable has already exist. 
[WARN ] 2025-03-04 18:04:03.493 - [任务 37][Pg] - Create index failed ERROR: column "_no_pk_hash" does not exist, please execute it manually [create index "IDX_uepdktableecb709d766d0" on "public"."nouniquepdktable"("_no_pk_hash" asc)] 
[TRACE] 2025-03-04 18:04:03.591 - [任务 37][Pg] - The table t1_example has already exist. 
[TRACE] 2025-03-04 18:04:03.591 - [任务 37][Pg] - Table: t1_example already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:03.692 - [任务 37][Pg] - The table td_fact_sec_null has already exist. 
[TRACE] 2025-03-04 18:04:03.692 - [任务 37][Pg] - Table: td_fact_sec_null already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:03.787 - [任务 37][Pg] - The table testIdenSmallInt has already exist. 
[TRACE] 2025-03-04 18:04:03.787 - [任务 37][Pg] - The table pg_xxx_bmsql_district has already exist. 
[TRACE] 2025-03-04 18:04:03.943 - [任务 37][Pg] - The table pg_bmsql_district has already exist. 
[TRACE] 2025-03-04 18:04:03.943 - [任务 37][Pg] - The table bmsql_item has already exist. 
[TRACE] 2025-03-04 18:04:04.093 - [任务 37][Pg] - The table BMSQL_STOCK has already exist. 
[TRACE] 2025-03-04 18:04:04.093 - [任务 37][Pg] - The table ap_standard_list_subset has already exist. 
[WARN ] 2025-03-04 18:04:04.244 - [任务 37][Pg] - Create index failed ERROR: relation "ap_standard_list_subset_Idx" already exists, rename ap_standard_list_subset_Idx to ap_standard_list_subset_Idx_5514 and retry ... 
[TRACE] 2025-03-04 18:04:04.371 - [任务 37][Pg] - The table testChar has already exist. 
[TRACE] 2025-03-04 18:04:04.373 - [任务 37][Pg] - The table pg_xxx_bmsql_order_line has already exist. 
[TRACE] 2025-03-04 18:04:04.519 - [任务 37][Pg] - The table pg_yyy_bmsql_warehouse has already exist. 
[TRACE] 2025-03-04 18:04:04.519 - [任务 37][Pg] - The table BMSQL_CUSTOMER has already exist. 
[TRACE] 2025-03-04 18:04:04.628 - [任务 37][Pg] - The table testTimeStamp2 has already exist. 
[TRACE] 2025-03-04 18:04:04.628 - [任务 37][Pg] - The table bmsql_district has already exist. 
[TRACE] 2025-03-04 18:04:04.778 - [任务 37][Pg] - The table QC_Sample_Map has already exist. 
[TRACE] 2025-03-04 18:04:04.778 - [任务 37][Pg] - Table: QC_Sample_Map already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:04.880 - [任务 37][Pg] - The table ap_operation_list has already exist. 
[TRACE] 2025-03-04 18:04:04.880 - [任务 37][Pg] - Table: ap_operation_list already exists Index: TapIndex indexFields: [TapIndexField name term_id fieldAsc true indexType null; TapIndexField name snomed_code fieldAsc true indexType null; TapIndexField name snomed_class fieldAsc true indexType null; TapIndexField name snomed_seq fieldAsc true indexType null; TapIndexField name valid_from fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:04.960 - [任务 37][Pg] - The table td_null_col has already exist. 
[TRACE] 2025-03-04 18:04:04.960 - [任务 37][Pg] - The table a_td_empty_string has already exist. 
[TRACE] 2025-03-04 18:04:05.051 - [任务 37][Pg] - Table: a_td_empty_string already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:05.051 - [任务 37][Pg] - The table td_test_varchar has already exist. 
[TRACE] 2025-03-04 18:04:05.156 - [任务 37][Pg] - The table test_td_timestamp_col has already exist. 
[TRACE] 2025-03-04 18:04:05.156 - [任务 37][Pg] - The table pg_bmsql_history has already exist. 
[TRACE] 2025-03-04 18:04:05.254 - [任务 37][Pg] - The table pg_w_td_fact_sec has already exist. 
[TRACE] 2025-03-04 18:04:05.254 - [任务 37][Pg] - Table: pg_w_td_fact_sec already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:05.335 - [任务 37][Pg] - The table pg_bmsql_config has already exist. 
[TRACE] 2025-03-04 18:04:05.336 - [任务 37][Pg] - The table t1_td_char_not_null has already exist. 
[TRACE] 2025-03-04 18:04:05.411 - [任务 37][Pg] - Table: t1_td_char_not_null already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:05.411 - [任务 37][Pg] - The table td_char0 has already exist. 
[TRACE] 2025-03-04 18:04:05.488 - [任务 37][Pg] - Table: td_char0 already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:05.488 - [任务 37][Pg] - The table BMSQL_WAREHOUSE has already exist. 
[TRACE] 2025-03-04 18:04:05.632 - [任务 37][Pg] - The table s2p_text_idt has already exist. 
[TRACE] 2025-03-04 18:04:05.632 - [任务 37][Pg] - Table: s2p_text_idt already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:05.762 - [任务 37][Pg] - The table bmsql_stock has already exist. 
[TRACE] 2025-03-04 18:04:05.762 - [任务 37][Pg] - The table td_fact_sec has already exist. 
[TRACE] 2025-03-04 18:04:05.849 - [任务 37][Pg] - Table: td_fact_sec already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:05.850 - [任务 37][Pg] - The table s2p_datetime_idt has already exist. 
[TRACE] 2025-03-04 18:04:05.945 - [任务 37][Pg] - Table: s2p_datetime_idt already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:05.945 - [任务 37][Pg] - The table sync_test has already exist. 
[TRACE] 2025-03-04 18:04:06.102 - [任务 37][Pg] - The table s2p_text has already exist. 
[TRACE] 2025-03-04 18:04:06.102 - [任务 37][Pg] - Table: s2p_text already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:06.173 - [任务 37][Pg] - The table pg_yyy_bmsql_oorder has already exist. 
[TRACE] 2025-03-04 18:04:06.173 - [任务 37][Pg] - The table pg_yyy_bmsql_item has already exist. 
[TRACE] 2025-03-04 18:04:06.294 - [任务 37][Pg] - The table pg_xxx_bmsql_stock has already exist. 
[TRACE] 2025-03-04 18:04:06.294 - [任务 37][Pg] - The table pg_xxx_bmsql_oorder has already exist. 
[TRACE] 2025-03-04 18:04:06.421 - [任务 37][Pg] - The table BMSQL_OORDER has already exist. 
[TRACE] 2025-03-04 18:04:06.422 - [任务 37][Pg] - The table pg_w_td_fact_sec_t has already exist. 
[TRACE] 2025-03-04 18:04:06.517 - [任务 37][Pg] - Table: pg_w_td_fact_sec_t already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:06.517 - [任务 37][Pg] - The table testTimeStampWithNoIndex has already exist. 
[TRACE] 2025-03-04 18:04:06.607 - [任务 37][Pg] - Table: testTimeStampWithNoIndex already exists Index: TapIndex indexFields: [TapIndexField name _no_pk_hash fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:06.608 - [任务 37][Pg] - The table test1_testBit has already exist. 
[TRACE] 2025-03-04 18:04:06.728 - [任务 37][Pg] - The table bmsql_oorder has already exist. 
[TRACE] 2025-03-04 18:04:06.728 - [任务 37][Pg] - The table testMoney has already exist. 
[TRACE] 2025-03-04 18:04:06.840 - [任务 37][Pg] - The table test1_td_null_col has already exist. 
[TRACE] 2025-03-04 18:04:06.841 - [任务 37][Pg] - The table testTimeStampTarget has already exist. 
[TRACE] 2025-03-04 18:04:06.957 - [任务 37][Pg] - The table pg_xxx_bmsql_customer has already exist. 
[TRACE] 2025-03-04 18:04:06.957 - [任务 37][Pg] - The table testidentity has already exist. 
[TRACE] 2025-03-04 18:04:07.089 - [任务 37][Pg] - The table BMSQL_ORDER_LINE has already exist. 
[TRACE] 2025-03-04 18:04:07.094 - [任务 37][Pg] - The table td_text has already exist. 
[TRACE] 2025-03-04 18:04:07.209 - [任务 37][Pg] - Table: td_text already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:07.210 - [任务 37][Pg] - The table testTimeStamp has already exist. 
[TRACE] 2025-03-04 18:04:07.330 - [任务 37][Pg] - The table bmsql_order_line has already exist. 
[TRACE] 2025-03-04 18:04:07.332 - [任务 37][Pg] - The table s2p_datetime_idt_char has already exist. 
[TRACE] 2025-03-04 18:04:07.494 - [任务 37][Pg] - Table: s2p_datetime_idt_char already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; ] and will no longer create index 
[TRACE] 2025-03-04 18:04:07.494 - [任务 37][Pg] - Table 'pg_yyy_bmsql_new_order' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-03-04 18:04:07.567 - [任务 37][Pg] - Table 'pg_yyy_bmsql_new_order' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-03-04 18:04:07.567 - [任务 37][Pg] - Table 'pg_yyy_bmsql_new_order' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-03-04 18:04:07.634 - [任务 37][Pg] - Table 'pg_yyy_bmsql_new_order' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-03-04 18:04:07.634 - [任务 37][Pg] - Table 'pg_yyy_bmsql_new_order' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-03-04 18:04:07.691 - [任务 37][Pg] - Table 'pg_yyy_bmsql_new_order' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-03-04 18:04:07.691 - [任务 37][Pg] - Table 'pg_yyy_bmsql_new_order' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-03-04 18:04:07.767 - [任务 37][Pg] - Table 'pg_yyy_bmsql_new_order' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-03-04 18:04:07.767 - [任务 37][Pg] - Table 'pg_yyy_bmsql_new_order' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-03-04 18:04:07.856 - [任务 37][Pg] - Table 'pg_yyy_bmsql_new_order' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-03-04 18:04:07.859 - [任务 37][Pg] - Table 'pg_yyy_bmsql_new_order' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-03-04 18:04:07.859 - [任务 37][Pg] - Table 'pg_yyy_bmsql_new_order' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2025-03-04 18:04:09.045 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_new_order has been completed batch read 
[INFO ] 2025-03-04 18:04:09.050 - [任务 37][Sybase-Wim] - Starting batch read from table: td_char_not_null 
[TRACE] 2025-03-04 18:04:09.051 - [任务 37][Sybase-Wim] - Table td_char_not_null is going to be initial synced 
[INFO ] 2025-03-04 18:04:09.242 - [任务 37][Sybase-Wim] - Table td_char_not_null has been completed batch read 
[INFO ] 2025-03-04 18:04:09.242 - [任务 37][Sybase-Wim] - Starting batch read from table: bmsql_customer 
[TRACE] 2025-03-04 18:04:09.243 - [任务 37][Sybase-Wim] - Table bmsql_customer is going to be initial synced 
[INFO ] 2025-03-04 18:04:09.400 - [任务 37][Sybase-Wim] - Table bmsql_customer has been completed batch read 
[INFO ] 2025-03-04 18:04:09.401 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_yyy_bmsql_history 
[TRACE] 2025-03-04 18:04:09.401 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_history is going to be initial synced 
[TRACE] 2025-03-04 18:04:09.704 - [任务 37][Pg] - Table 'td_char_not_null' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-03-04 18:04:09.824 - [任务 37][Pg] - Table 'pg_yyy_bmsql_history' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[TRACE] 2025-03-04 18:04:09.929 - [任务 37][Pg] - Table 'pg_yyy_bmsql_history' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[TRACE] 2025-03-04 18:04:09.929 - [任务 37][Pg] - Table 'pg_yyy_bmsql_history' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[TRACE] 2025-03-04 18:04:10.051 - [任务 37][Pg] - Table 'pg_yyy_bmsql_history' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[TRACE] 2025-03-04 18:04:10.052 - [任务 37][Pg] - Table 'pg_yyy_bmsql_history' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[TRACE] 2025-03-04 18:04:10.206 - [任务 37][Pg] - Table 'pg_yyy_bmsql_history' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[TRACE] 2025-03-04 18:04:10.207 - [任务 37][Pg] - Table 'pg_yyy_bmsql_history' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[TRACE] 2025-03-04 18:04:10.299 - [任务 37][Pg] - Table 'pg_yyy_bmsql_history' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[TRACE] 2025-03-04 18:04:10.299 - [任务 37][Pg] - Table 'pg_yyy_bmsql_history' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[TRACE] 2025-03-04 18:04:10.404 - [任务 37][Pg] - Table 'pg_yyy_bmsql_history' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[TRACE] 2025-03-04 18:04:10.405 - [任务 37][Pg] - Table 'pg_yyy_bmsql_history' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[TRACE] 2025-03-04 18:04:10.406 - [任务 37][Pg] - Table 'pg_yyy_bmsql_history' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[TRACE] 2025-03-04 18:04:15.545 - [任务 37][Sybase-Wim] - Query snapshot row size completed: Sybase-Wim(21da900a-2b14-4437-afde-4ef2d77fee21) 
[INFO ] 2025-03-04 18:04:17.114 - [任务 37][Sybase-Wim] - Table pg_yyy_bmsql_history has been completed batch read 
[INFO ] 2025-03-04 18:04:17.116 - [任务 37][Sybase-Wim] - Starting batch read from table: testIden 
[TRACE] 2025-03-04 18:04:17.117 - [任务 37][Sybase-Wim] - Table testIden is going to be initial synced 
[INFO ] 2025-03-04 18:04:17.307 - [任务 37][Sybase-Wim] - Table testIden has been completed batch read 
[INFO ] 2025-03-04 18:04:17.308 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_sync_test 
[TRACE] 2025-03-04 18:04:17.308 - [任务 37][Sybase-Wim] - Table pg_sync_test is going to be initial synced 
[INFO ] 2025-03-04 18:04:17.427 - [任务 37][Sybase-Wim] - Table pg_sync_test has been completed batch read 
[INFO ] 2025-03-04 18:04:17.427 - [任务 37][Sybase-Wim] - Starting batch read from table: bmsql_config 
[TRACE] 2025-03-04 18:04:17.427 - [任务 37][Sybase-Wim] - Table bmsql_config is going to be initial synced 
[INFO ] 2025-03-04 18:04:17.545 - [任务 37][Sybase-Wim] - Table bmsql_config has been completed batch read 
[INFO ] 2025-03-04 18:04:17.546 - [任务 37][Sybase-Wim] - Starting batch read from table: pg_bmsql_item 
[TRACE] 2025-03-04 18:04:17.546 - [任务 37][Sybase-Wim] - Table pg_bmsql_item is going to be initial synced 
[WARN ] 2025-03-04 18:04:17.804 - [任务 37][Pg] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: org.postgresql.util.PSQLException: ERROR: column "_no_pk_hash" of relation "testIden" does not exist
  位置：48
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:315)
	org.postgresql.jdbc.PgStatement.internalExecuteBatch(PgStatement.java:868)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2025-03-04 18:04:17.804 - [任务 37][Pg] - Retry operation TARGET_WRITE_RECORD, retry times 1/15, first retry time 2025-03-04 18:04:17, next retry time 2025-03-04 18:05:17 
[TRACE] 2025-03-04 18:04:47.005 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] running status set to false 
[TRACE] 2025-03-04 18:04:47.010 - [任务 37][Sybase-Wim] - PDK connector node stopped: HazelcastSourcePdkDataNode_21da900a-2b14-4437-afde-4ef2d77fee21_1741082638750 
[TRACE] 2025-03-04 18:04:47.013 - [任务 37][Sybase-Wim] - PDK connector node released: HazelcastSourcePdkDataNode_21da900a-2b14-4437-afde-4ef2d77fee21_1741082638750 
[TRACE] 2025-03-04 18:04:47.013 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] schema data cleaned 
[TRACE] 2025-03-04 18:04:47.013 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] monitor closed 
[TRACE] 2025-03-04 18:04:47.014 - [任务 37][Sybase-Wim] - Node Sybase-Wim[21da900a-2b14-4437-afde-4ef2d77fee21] close complete, cost 200 ms 
[TRACE] 2025-03-04 18:04:47.035 - [任务 37][Pg] - Node Pg[6a72a021-2b5c-44d6-8b62-19e4c7622b14] running status set to false 
[TRACE] 2025-03-04 18:04:47.035 - [任务 37][Pg] - PDK connector node stopped: HazelcastTargetPdkDataNode_6a72a021-2b5c-44d6-8b62-19e4c7622b14_1741082638670 
[TRACE] 2025-03-04 18:04:47.036 - [任务 37][Pg] - PDK connector node released: HazelcastTargetPdkDataNode_6a72a021-2b5c-44d6-8b62-19e4c7622b14_1741082638670 
[TRACE] 2025-03-04 18:04:47.036 - [任务 37][Pg] - Node Pg[6a72a021-2b5c-44d6-8b62-19e4c7622b14] schema data cleaned 
[TRACE] 2025-03-04 18:04:47.037 - [任务 37][Pg] - Node Pg[6a72a021-2b5c-44d6-8b62-19e4c7622b14] monitor closed 
[TRACE] 2025-03-04 18:04:47.038 - [任务 37][Pg] - Node Pg[6a72a021-2b5c-44d6-8b62-19e4c7622b14] close complete, cost 22 ms 
[INFO ] 2025-03-04 18:04:47.038 - [任务 37][Pg] - Retry operation TARGET_WRITE_RECORD failed, total cost 00:00:29.239000 
[TRACE] 2025-03-04 18:04:47.068 - [任务 37][Sybase-Wim] - Initial sync completed 
[TRACE] 2025-03-04 18:04:47.069 - [任务 37][Pg] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code null): when operate table: testIden, java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-11) has been closed. 
[TRACE] 2025-03-04 18:04:47.084 - [任务 37][Sybase-Wim] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.RuntimeException: Find schema failed, message: Table name "pg_yyy_bmsql_stock" not exists, qualified name: null tableNameAndQualifiedNameMap: {} 
[ERROR] 2025-03-04 18:04:47.085 - [任务 37][Sybase-Wim] - Unknown PDK exception occur, java.lang.RuntimeException: Find schema failed, message: Table name "pg_yyy_bmsql_stock" not exists, qualified name: null tableNameAndQualifiedNameMap: {} <-- Error Message -->
Unknown PDK exception occur, java.lang.RuntimeException: Find schema failed, message: Table name "pg_yyy_bmsql_stock" not exists, qualified name: null tableNameAndQualifiedNameMap: {}

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Table name "pg_yyy_bmsql_stock" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:442)
	io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Find schema failed, message: Table name "pg_yyy_bmsql_stock" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:219)
	at io.tapdata.pdk.core.utils.RetryUtils.throwIfNeed(RetryUtils.java:282)
	at io.tapdata.pdk.core.utils.RetryUtils.retryFailed(RetryUtils.java:142)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:187)
	at io.tapdata.schema.TapTableMap.get(TapTableMap.java:175)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:391)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:356)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:269)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Find schema failed, message: Table name "pg_yyy_bmsql_stock" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:370)
	at io.tapdata.schema.TapTableMap.lambda$get$0(TapTableMap.java:173)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:173)
	... 12 more
Caused by: java.lang.RuntimeException: Table name "pg_yyy_bmsql_stock" not exists, qualified name: null tableNameAndQualifiedNameMap: {}
	at io.tapdata.schema.TapTableMap.findSchema(TapTableMap.java:399)
	at io.tapdata.schema.TapTableMap.lambda$getTapTable$3(TapTableMap.java:364)
	at io.tapdata.schema.TapTableMap.handleWithLock(TapTableMap.java:442)
	at io.tapdata.schema.TapTableMap.getTapTable(TapTableMap.java:361)
	... 14 more

[ERROR] 2025-03-04 18:04:47.086 - [任务 37][Pg] - PDK retry exception (Server Error Code null): when operate table: testIden, java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-11) has been closed. <-- Error Message -->
PDK retry exception (Server Error Code null): when operate table: testIden, java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-11) has been closed.

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-11) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:48)
	io.tapdata.connector.postgres.dml.PostgresRecordWriter.<init>(PostgresRecordWriter.java:23)
	io.tapdata.connector.postgres.PostgresConnector.writeRecord(PostgresConnector.java:443)
	...

<-- Full Stack Trace -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-11) has been closed.
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:151)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:48)
	at io.tapdata.connector.postgres.dml.PostgresRecordWriter.<init>(PostgresRecordWriter.java:23)
	at io.tapdata.connector.postgres.PostgresConnector.writeRecord(PostgresConnector.java:443)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$68(HazelcastTargetPdkDataNode.java:1088)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$69(HazelcastTargetPdkDataNode.java:1084)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$70(HazelcastTargetPdkDataNode.java:1039)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:1014)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$36(HazelcastTargetPdkDataNode.java:713)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:713)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:924)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:842)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:791)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$23(HazelcastTargetPdkBaseNode.java:751)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:637)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$24(HazelcastTargetPdkBaseNode.java:723)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:775)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:722)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-11) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 34 more

[TRACE] 2025-03-04 18:04:48.662 - [任务 37] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-04 18:04:48.663 - [任务 37] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@59e831e6 
[TRACE] 2025-03-04 18:04:48.665 - [任务 37] - Stop task milestones: 67c6c4386ed4fc58ed97f950(任务 37)  
[TRACE] 2025-03-04 18:04:48.805 - [任务 37] - Stopped task aspect(s) 
[TRACE] 2025-03-04 18:04:48.805 - [任务 37] - Snapshot order controller have been removed 
[INFO ] 2025-03-04 18:04:48.883 - [任务 37] - Task stopped. 
[TRACE] 2025-03-04 18:04:48.887 - [任务 37] - Remove memory task client succeed, task: 任务 37[67c6c4386ed4fc58ed97f950] 
[TRACE] 2025-03-04 18:04:48.887 - [任务 37] - Destroy memory task client cache succeed, task: 任务 37[67c6c4386ed4fc58ed97f950] 
