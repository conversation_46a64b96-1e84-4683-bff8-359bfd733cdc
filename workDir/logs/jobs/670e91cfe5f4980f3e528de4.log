[INFO ] 2024-10-16 00:01:43.145 - [任务 3] - Start task milestones: 670e91cfe5f4980f3e528de4(任务 3) 
[INFO ] 2024-10-16 00:01:43.146 - [任务 3] - Task initialization... 
[INFO ] 2024-10-16 00:01:44.726 - [任务 3] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-10-16 00:01:44.779 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-16 00:01:44.992 - [任务 3][TestMysql] - Node TestMysql[2bee360c-c91c-49bf-b924-6a206b23f1cd] start preload schema,table counts: 73 
[INFO ] 2024-10-16 00:01:44.992 - [任务 3][MysqlCrmeb] - Node MysqlCrmeb[f3947a0b-c177-4aa9-a4bf-f19033327883] start preload schema,table counts: 73 
[INFO ] 2024-10-16 00:01:44.997 - [任务 3][MysqlCrmeb] - Node MysqlCrmeb[f3947a0b-c177-4aa9-a4bf-f19033327883] preload schema finished, cost 1 ms 
[INFO ] 2024-10-16 00:01:44.998 - [任务 3][TestMysql] - Node TestMysql[2bee360c-c91c-49bf-b924-6a206b23f1cd] preload schema finished, cost 0 ms 
[INFO ] 2024-10-16 00:01:46.048 - [任务 3][TestMysql] - Node(TestMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-16 00:01:46.051 - [任务 3][TestMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-16 00:01:46.263 - [任务 3][MysqlCrmeb] - Source node "MysqlCrmeb" read batch size: 100 
[INFO ] 2024-10-16 00:01:46.266 - [任务 3][MysqlCrmeb] - Source node "MysqlCrmeb" event queue capacity: 200 
[INFO ] 2024-10-16 00:01:46.266 - [任务 3][MysqlCrmeb] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-10-16 00:01:46.372 - [任务 3][MysqlCrmeb] - Call timestamp to stream offset function failed, will stop task after snapshot, type: mysql-io.tapdata-1.0-SNAPSHOT-public, errors: SQLSyntaxErrorException  Access denied; you need (at least one of) the SUPER, REPLICATION CLIENT privilege(s) for this operation
java.sql.SQLSyntaxErrorException: Access denied; you need (at least one of) the SUPER, REPLICATION CLIENT privilege(s) for this operation
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.normalQuery(JdbcContext.java:107)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.readBinlogPosition(MysqlJdbcContextV2.java:143)
	at io.tapdata.connector.mysql.MysqlConnector.timestampToStreamOffset(MysqlConnector.java:711)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromTime$9(HazelcastSourcePdkBaseNode.java:672)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromTime(HazelcastSourcePdkBaseNode.java:670)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetInitialAndCDC(HazelcastSourcePdkBaseNode.java:650)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:584)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:467)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:359)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:236)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
 
[INFO ] 2024-10-16 00:01:46.372 - [任务 3][MysqlCrmeb] - batch offset found: {},stream offset not found. 
[INFO ] 2024-10-16 00:01:46.372 - [任务 3][MysqlCrmeb] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-16 00:01:46.607 - [任务 3][MysqlCrmeb] - Initial sync started 
[INFO ] 2024-10-16 00:01:46.612 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_order 
[INFO ] 2024-10-16 00:01:46.706 - [任务 3][MysqlCrmeb] - Table eb_store_order is going to be initial synced 
[INFO ] 2024-10-16 00:01:46.706 - [任务 3][MysqlCrmeb] - Table [eb_store_order] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:46.709 - [任务 3][MysqlCrmeb] - Query table 'eb_store_order' counts: 0 
[INFO ] 2024-10-16 00:01:46.709 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_attr_value 
[INFO ] 2024-10-16 00:01:46.766 - [任务 3][MysqlCrmeb] - Table eb_store_product_attr_value is going to be initial synced 
[INFO ] 2024-10-16 00:01:46.766 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_attr_value' counts: 38 
[INFO ] 2024-10-16 00:01:46.783 - [任务 3][MysqlCrmeb] - Table [eb_store_product_attr_value] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:46.785 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_wechat_callback 
[INFO ] 2024-10-16 00:01:46.840 - [任务 3][MysqlCrmeb] - Table eb_wechat_callback is going to be initial synced 
[INFO ] 2024-10-16 00:01:46.840 - [任务 3][MysqlCrmeb] - Query table 'eb_wechat_callback' counts: 0 
[INFO ] 2024-10-16 00:01:46.850 - [任务 3][MysqlCrmeb] - Table [eb_wechat_callback] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:46.850 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_shipping_templates_free 
[INFO ] 2024-10-16 00:01:46.909 - [任务 3][MysqlCrmeb] - Table eb_shipping_templates_free is going to be initial synced 
[INFO ] 2024-10-16 00:01:46.910 - [任务 3][MysqlCrmeb] - Query table 'eb_shipping_templates_free' counts: 32 
[INFO ] 2024-10-16 00:01:46.926 - [任务 3][MysqlCrmeb] - Table [eb_shipping_templates_free] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:46.926 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_notification 
[INFO ] 2024-10-16 00:01:46.984 - [任务 3][MysqlCrmeb] - Table eb_system_notification is going to be initial synced 
[INFO ] 2024-10-16 00:01:46.988 - [任务 3][MysqlCrmeb] - Query table 'eb_system_notification' counts: 12 
[INFO ] 2024-10-16 00:01:46.996 - [任务 3][MysqlCrmeb] - Table [eb_system_notification] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:46.996 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_heal_user 
[INFO ] 2024-10-16 00:01:47.055 - [任务 3][MysqlCrmeb] - Table eb_store_heal_user is going to be initial synced 
[INFO ] 2024-10-16 00:01:47.055 - [任务 3][MysqlCrmeb] - Query table 'eb_store_heal_user' counts: 9 
[INFO ] 2024-10-16 00:01:47.069 - [任务 3][MysqlCrmeb] - Table [eb_store_heal_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:47.069 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_level 
[INFO ] 2024-10-16 00:01:47.069 - [任务 3][MysqlCrmeb] - Table eb_user_level is going to be initial synced 
[INFO ] 2024-10-16 00:01:47.125 - [任务 3][MysqlCrmeb] - Query table 'eb_user_level' counts: 1 
[INFO ] 2024-10-16 00:01:47.135 - [任务 3][MysqlCrmeb] - Table [eb_user_level] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:47.135 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_token 
[INFO ] 2024-10-16 00:01:47.192 - [任务 3][MysqlCrmeb] - Table eb_user_token is going to be initial synced 
[INFO ] 2024-10-16 00:01:47.194 - [任务 3][MysqlCrmeb] - Query table 'eb_user_token' counts: 3 
[INFO ] 2024-10-16 00:01:47.201 - [任务 3][MysqlCrmeb] - Table [eb_user_token] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:47.203 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_group 
[INFO ] 2024-10-16 00:01:47.203 - [任务 3][MysqlCrmeb] - Table eb_system_group is going to be initial synced 
[INFO ] 2024-10-16 00:01:47.271 - [任务 3][MysqlCrmeb] - Query table 'eb_system_group' counts: 18 
[INFO ] 2024-10-16 00:01:47.271 - [任务 3][MysqlCrmeb] - Table [eb_system_group] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:47.272 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_config 
[INFO ] 2024-10-16 00:01:47.272 - [任务 3][MysqlCrmeb] - Table eb_system_config is going to be initial synced 
[INFO ] 2024-10-16 00:01:47.332 - [任务 3][MysqlCrmeb] - Query table 'eb_system_config' counts: 177 
[INFO ] 2024-10-16 00:01:47.356 - [任务 3][MysqlCrmeb] - Table [eb_system_config] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:47.360 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_sign 
[INFO ] 2024-10-16 00:01:47.360 - [任务 3][MysqlCrmeb] - Table eb_user_sign is going to be initial synced 
[INFO ] 2024-10-16 00:01:47.427 - [任务 3][MysqlCrmeb] - Query table 'eb_user_sign' counts: 1 
[INFO ] 2024-10-16 00:01:47.427 - [任务 3][MysqlCrmeb] - Table [eb_user_sign] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:47.428 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_cate 
[INFO ] 2024-10-16 00:01:47.428 - [任务 3][MysqlCrmeb] - Table eb_store_product_cate is going to be initial synced 
[INFO ] 2024-10-16 00:01:47.493 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_cate' counts: 0 
[INFO ] 2024-10-16 00:01:47.493 - [任务 3][MysqlCrmeb] - Table [eb_store_product_cate] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:47.493 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_experience_record 
[INFO ] 2024-10-16 00:01:47.494 - [任务 3][MysqlCrmeb] - Table eb_user_experience_record is going to be initial synced 
[INFO ] 2024-10-16 00:01:47.558 - [任务 3][MysqlCrmeb] - Query table 'eb_user_experience_record' counts: 1 
[INFO ] 2024-10-16 00:01:47.558 - [任务 3][MysqlCrmeb] - Table [eb_user_experience_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:47.558 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_brand_story 
[INFO ] 2024-10-16 00:01:47.558 - [任务 3][MysqlCrmeb] - Table eb_store_brand_story is going to be initial synced 
[INFO ] 2024-10-16 00:01:47.625 - [任务 3][MysqlCrmeb] - Query table 'eb_store_brand_story' counts: 1 
[INFO ] 2024-10-16 00:01:47.625 - [任务 3][MysqlCrmeb] - Table [eb_store_brand_story] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:47.626 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_heal 
[INFO ] 2024-10-16 00:01:47.626 - [任务 3][MysqlCrmeb] - Table eb_store_heal is going to be initial synced 
[INFO ] 2024-10-16 00:01:47.689 - [任务 3][MysqlCrmeb] - Query table 'eb_store_heal' counts: 5 
[INFO ] 2024-10-16 00:01:47.689 - [任务 3][MysqlCrmeb] - Table [eb_store_heal] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:47.690 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_description 
[INFO ] 2024-10-16 00:01:47.690 - [任务 3][MysqlCrmeb] - Table eb_store_product_description is going to be initial synced 
[INFO ] 2024-10-16 00:01:47.758 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_description' counts: 18 
[INFO ] 2024-10-16 00:01:47.758 - [任务 3][MysqlCrmeb] - Table [eb_store_product_description] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:47.763 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_reply 
[INFO ] 2024-10-16 00:01:47.764 - [任务 3][MysqlCrmeb] - Table eb_store_product_reply is going to be initial synced 
[INFO ] 2024-10-16 00:01:47.824 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_reply' counts: 1 
[INFO ] 2024-10-16 00:01:47.824 - [任务 3][MysqlCrmeb] - Table [eb_store_product_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:47.824 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_brokerage_record 
[INFO ] 2024-10-16 00:01:47.825 - [任务 3][MysqlCrmeb] - Table eb_user_brokerage_record is going to be initial synced 
[INFO ] 2024-10-16 00:01:47.888 - [任务 3][MysqlCrmeb] - Query table 'eb_user_brokerage_record' counts: 0 
[INFO ] 2024-10-16 00:01:47.888 - [任务 3][MysqlCrmeb] - Table [eb_user_brokerage_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:47.888 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_attachment 
[INFO ] 2024-10-16 00:01:47.888 - [任务 3][MysqlCrmeb] - Table eb_system_attachment is going to be initial synced 
[INFO ] 2024-10-16 00:01:48.093 - [任务 3][MysqlCrmeb] - Query table 'eb_system_attachment' counts: 288 
[WARN ] 2024-10-16 00:01:48.994 - [任务 3][TestMysql] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[f3947a0b-c177-4aa9-a4bf-f19033327883], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-10-16 00:01:48.994 - [任务 3][MysqlCrmeb] - Table [eb_system_attachment] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:48.994 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_store_staff 
[INFO ] 2024-10-16 00:01:48.995 - [任务 3][MysqlCrmeb] - Table eb_system_store_staff is going to be initial synced 
[INFO ] 2024-10-16 00:01:49.078 - [任务 3][MysqlCrmeb] - Query table 'eb_system_store_staff' counts: 0 
[INFO ] 2024-10-16 00:01:49.080 - [任务 3][MysqlCrmeb] - Table [eb_system_store_staff] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:49.080 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_wechat_reply 
[INFO ] 2024-10-16 00:01:49.081 - [任务 3][MysqlCrmeb] - Table eb_wechat_reply is going to be initial synced 
[INFO ] 2024-10-16 00:01:49.148 - [任务 3][MysqlCrmeb] - Query table 'eb_wechat_reply' counts: 1 
[INFO ] 2024-10-16 00:01:49.150 - [任务 3][MysqlCrmeb] - Table [eb_wechat_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:49.150 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_bargain_user_help 
[INFO ] 2024-10-16 00:01:49.209 - [任务 3][MysqlCrmeb] - Table eb_store_bargain_user_help is going to be initial synced 
[INFO ] 2024-10-16 00:01:49.209 - [任务 3][MysqlCrmeb] - Query table 'eb_store_bargain_user_help' counts: 0 
[INFO ] 2024-10-16 00:01:49.216 - [任务 3][MysqlCrmeb] - Table [eb_store_bargain_user_help] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:49.216 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_seckill 
[INFO ] 2024-10-16 00:01:49.274 - [任务 3][MysqlCrmeb] - Table eb_store_seckill is going to be initial synced 
[INFO ] 2024-10-16 00:01:49.274 - [任务 3][MysqlCrmeb] - Query table 'eb_store_seckill' counts: 4 
[INFO ] 2024-10-16 00:01:49.284 - [任务 3][MysqlCrmeb] - Table [eb_store_seckill] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:49.284 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_attr_result 
[INFO ] 2024-10-16 00:01:49.341 - [任务 3][MysqlCrmeb] - Table eb_store_product_attr_result is going to be initial synced 
[INFO ] 2024-10-16 00:01:49.341 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_attr_result' counts: 0 
[INFO ] 2024-10-16 00:01:49.347 - [任务 3][MysqlCrmeb] - Table [eb_store_product_attr_result] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:49.347 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_shipping_templates 
[INFO ] 2024-10-16 00:01:49.405 - [任务 3][MysqlCrmeb] - Table eb_shipping_templates is going to be initial synced 
[INFO ] 2024-10-16 00:01:49.405 - [任务 3][MysqlCrmeb] - Query table 'eb_shipping_templates' counts: 1 
[INFO ] 2024-10-16 00:01:49.412 - [任务 3][MysqlCrmeb] - Table [eb_shipping_templates] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:49.412 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_seckill_manger 
[INFO ] 2024-10-16 00:01:49.470 - [任务 3][MysqlCrmeb] - Table eb_store_seckill_manger is going to be initial synced 
[INFO ] 2024-10-16 00:01:49.471 - [任务 3][MysqlCrmeb] - Query table 'eb_store_seckill_manger' counts: 3 
[INFO ] 2024-10-16 00:01:49.477 - [任务 3][MysqlCrmeb] - Table [eb_store_seckill_manger] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:49.477 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user 
[INFO ] 2024-10-16 00:01:49.534 - [任务 3][MysqlCrmeb] - Table eb_user is going to be initial synced 
[INFO ] 2024-10-16 00:01:49.534 - [任务 3][MysqlCrmeb] - Query table 'eb_user' counts: 4 
[INFO ] 2024-10-16 00:01:49.547 - [任务 3][MysqlCrmeb] - Table [eb_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:49.547 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_order_status 
[INFO ] 2024-10-16 00:01:49.605 - [任务 3][MysqlCrmeb] - Table eb_store_order_status is going to be initial synced 
[INFO ] 2024-10-16 00:01:49.605 - [任务 3][MysqlCrmeb] - Query table 'eb_store_order_status' counts: 0 
[INFO ] 2024-10-16 00:01:49.617 - [任务 3][MysqlCrmeb] - Table [eb_store_order_status] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:49.617 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_form_temp 
[INFO ] 2024-10-16 00:01:49.680 - [任务 3][MysqlCrmeb] - Table eb_system_form_temp is going to be initial synced 
[INFO ] 2024-10-16 00:01:49.680 - [任务 3][MysqlCrmeb] - Query table 'eb_system_form_temp' counts: 60 
[INFO ] 2024-10-16 00:01:49.970 - [任务 3][MysqlCrmeb] - Table [eb_system_form_temp] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:49.970 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_reply 
[INFO ] 2024-10-16 00:01:50.044 - [任务 3][MysqlCrmeb] - Table eb_store_experience_reply is going to be initial synced 
[INFO ] 2024-10-16 00:01:50.048 - [任务 3][MysqlCrmeb] - Query table 'eb_store_experience_reply' counts: 2 
[INFO ] 2024-10-16 00:01:50.056 - [任务 3][MysqlCrmeb] - Table [eb_store_experience_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:50.056 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_bargain_user 
[INFO ] 2024-10-16 00:01:50.119 - [任务 3][MysqlCrmeb] - Table eb_store_bargain_user is going to be initial synced 
[INFO ] 2024-10-16 00:01:50.119 - [任务 3][MysqlCrmeb] - Query table 'eb_store_bargain_user' counts: 0 
[INFO ] 2024-10-16 00:01:50.122 - [任务 3][MysqlCrmeb] - Table [eb_store_bargain_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:50.122 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_store 
[INFO ] 2024-10-16 00:01:50.185 - [任务 3][MysqlCrmeb] - Table eb_system_store is going to be initial synced 
[INFO ] 2024-10-16 00:01:50.185 - [任务 3][MysqlCrmeb] - Query table 'eb_system_store' counts: 0 
[INFO ] 2024-10-16 00:01:50.194 - [任务 3][MysqlCrmeb] - Table [eb_system_store] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:50.201 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_shipping_templates_region 
[INFO ] 2024-10-16 00:01:50.203 - [任务 3][MysqlCrmeb] - Table eb_shipping_templates_region is going to be initial synced 
[INFO ] 2024-10-16 00:01:50.464 - [任务 3][MysqlCrmeb] - Query table 'eb_shipping_templates_region' counts: 369 
[INFO ] 2024-10-16 00:01:50.537 - [任务 3][MysqlCrmeb] - Table [eb_shipping_templates_region] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:50.538 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_recharge 
[INFO ] 2024-10-16 00:01:50.538 - [任务 3][MysqlCrmeb] - Table eb_user_recharge is going to be initial synced 
[INFO ] 2024-10-16 00:01:50.605 - [任务 3][MysqlCrmeb] - Table [eb_user_recharge] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:50.814 - [任务 3][MysqlCrmeb] - Query table 'eb_user_recharge' counts: 0 
[INFO ] 2024-10-16 00:01:50.814 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_coupon 
[INFO ] 2024-10-16 00:01:50.886 - [任务 3][MysqlCrmeb] - Table eb_store_product_coupon is going to be initial synced 
[INFO ] 2024-10-16 00:01:50.887 - [任务 3][MysqlCrmeb] - Table [eb_store_product_coupon] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:51.099 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_coupon' counts: 0 
[INFO ] 2024-10-16 00:01:51.099 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_wechat_pay_info 
[INFO ] 2024-10-16 00:01:51.175 - [任务 3][MysqlCrmeb] - Table eb_wechat_pay_info is going to be initial synced 
[INFO ] 2024-10-16 00:01:51.175 - [任务 3][MysqlCrmeb] - Table [eb_wechat_pay_info] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:51.438 - [任务 3][MysqlCrmeb] - Query table 'eb_wechat_pay_info' counts: 0 
[INFO ] 2024-10-16 00:01:51.438 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_combination 
[INFO ] 2024-10-16 00:01:51.494 - [任务 3][MysqlCrmeb] - Table eb_store_combination is going to be initial synced 
[INFO ] 2024-10-16 00:01:51.494 - [任务 3][MysqlCrmeb] - Query table 'eb_store_combination' counts: 4 
[INFO ] 2024-10-16 00:01:51.505 - [任务 3][MysqlCrmeb] - Table [eb_store_combination] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:51.507 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product 
[INFO ] 2024-10-16 00:01:51.507 - [任务 3][MysqlCrmeb] - Table eb_store_product is going to be initial synced 
[INFO ] 2024-10-16 00:01:51.568 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product' counts: 8 
[INFO ] 2024-10-16 00:01:51.569 - [任务 3][MysqlCrmeb] - Table [eb_store_product] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:51.569 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_article 
[INFO ] 2024-10-16 00:01:51.569 - [任务 3][MysqlCrmeb] - Table eb_article is going to be initial synced 
[INFO ] 2024-10-16 00:01:51.634 - [任务 3][MysqlCrmeb] - Query table 'eb_article' counts: 0 
[INFO ] 2024-10-16 00:01:51.634 - [任务 3][MysqlCrmeb] - Table [eb_article] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:51.634 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_bill 
[INFO ] 2024-10-16 00:01:51.634 - [任务 3][MysqlCrmeb] - Table eb_user_bill is going to be initial synced 
[INFO ] 2024-10-16 00:01:51.759 - [任务 3][MysqlCrmeb] - Query table 'eb_user_bill' counts: 0 
[INFO ] 2024-10-16 00:01:51.759 - [任务 3][MysqlCrmeb] - Table [eb_user_bill] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:51.759 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_relation 
[INFO ] 2024-10-16 00:01:51.759 - [任务 3][MysqlCrmeb] - Table eb_store_experience_relation is going to be initial synced 
[INFO ] 2024-10-16 00:01:51.824 - [任务 3][MysqlCrmeb] - Query table 'eb_store_experience_relation' counts: 2 
[INFO ] 2024-10-16 00:01:51.825 - [任务 3][MysqlCrmeb] - Table [eb_store_experience_relation] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:51.825 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_wechat_exceptions 
[INFO ] 2024-10-16 00:01:51.825 - [任务 3][MysqlCrmeb] - Table eb_wechat_exceptions is going to be initial synced 
[INFO ] 2024-10-16 00:01:51.888 - [任务 3][MysqlCrmeb] - Query table 'eb_wechat_exceptions' counts: 31 
[INFO ] 2024-10-16 00:01:51.888 - [任务 3][MysqlCrmeb] - Table [eb_wechat_exceptions] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:51.889 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_template_message 
[INFO ] 2024-10-16 00:01:51.889 - [任务 3][MysqlCrmeb] - Table eb_template_message is going to be initial synced 
[INFO ] 2024-10-16 00:01:51.956 - [任务 3][MysqlCrmeb] - Query table 'eb_template_message' counts: 15 
[INFO ] 2024-10-16 00:01:51.957 - [任务 3][MysqlCrmeb] - Table [eb_template_message] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:51.961 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_integral_record 
[INFO ] 2024-10-16 00:01:51.961 - [任务 3][MysqlCrmeb] - Table eb_user_integral_record is going to be initial synced 
[INFO ] 2024-10-16 00:01:52.024 - [任务 3][MysqlCrmeb] - Query table 'eb_user_integral_record' counts: 1 
[INFO ] 2024-10-16 00:01:52.024 - [任务 3][MysqlCrmeb] - Table [eb_user_integral_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:52.026 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_coupon 
[INFO ] 2024-10-16 00:01:52.026 - [任务 3][MysqlCrmeb] - Table eb_store_coupon is going to be initial synced 
[INFO ] 2024-10-16 00:01:52.095 - [任务 3][MysqlCrmeb] - Query table 'eb_store_coupon' counts: 2 
[INFO ] 2024-10-16 00:01:52.095 - [任务 3][MysqlCrmeb] - Table [eb_store_coupon] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:52.097 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_visit_record 
[INFO ] 2024-10-16 00:01:52.097 - [任务 3][MysqlCrmeb] - Table eb_user_visit_record is going to be initial synced 
[INFO ] 2024-10-16 00:01:52.221 - [任务 3][MysqlCrmeb] - Query table 'eb_user_visit_record' counts: 1238 
[INFO ] 2024-10-16 00:01:52.221 - [任务 3][MysqlCrmeb] - Table [eb_user_visit_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:52.221 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_role_menu 
[INFO ] 2024-10-16 00:01:52.221 - [任务 3][MysqlCrmeb] - Table eb_system_role_menu is going to be initial synced 
[INFO ] 2024-10-16 00:01:52.291 - [任务 3][MysqlCrmeb] - Query table 'eb_system_role_menu' counts: 595 
[INFO ] 2024-10-16 00:01:52.291 - [任务 3][MysqlCrmeb] - Table [eb_system_role_menu] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:52.292 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_log 
[INFO ] 2024-10-16 00:01:52.292 - [任务 3][MysqlCrmeb] - Table eb_store_product_log is going to be initial synced 
[INFO ] 2024-10-16 00:01:52.361 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_log' counts: 0 
[INFO ] 2024-10-16 00:01:52.362 - [任务 3][MysqlCrmeb] - Table [eb_store_product_log] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:52.370 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_series 
[INFO ] 2024-10-16 00:01:52.370 - [任务 3][MysqlCrmeb] - Table eb_store_product_series is going to be initial synced 
[INFO ] 2024-10-16 00:01:52.438 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_series' counts: 5 
[INFO ] 2024-10-16 00:01:52.438 - [任务 3][MysqlCrmeb] - Table [eb_store_product_series] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:52.439 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_attr 
[INFO ] 2024-10-16 00:01:52.440 - [任务 3][MysqlCrmeb] - Table eb_store_product_attr is going to be initial synced 
[INFO ] 2024-10-16 00:01:52.506 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_attr' counts: 24 
[INFO ] 2024-10-16 00:01:52.506 - [任务 3][MysqlCrmeb] - Table [eb_store_product_attr] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:52.506 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_bargain 
[INFO ] 2024-10-16 00:01:52.507 - [任务 3][MysqlCrmeb] - Table eb_store_bargain is going to be initial synced 
[INFO ] 2024-10-16 00:01:52.579 - [任务 3][MysqlCrmeb] - Query table 'eb_store_bargain' counts: 2 
[INFO ] 2024-10-16 00:01:52.579 - [任务 3][MysqlCrmeb] - Table [eb_store_bargain] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:52.580 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_audio 
[INFO ] 2024-10-16 00:01:52.580 - [任务 3][MysqlCrmeb] - Table eb_store_experience_audio is going to be initial synced 
[INFO ] 2024-10-16 00:01:52.647 - [任务 3][MysqlCrmeb] - Query table 'eb_store_experience_audio' counts: 5 
[INFO ] 2024-10-16 00:01:52.648 - [任务 3][MysqlCrmeb] - Table [eb_store_experience_audio] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:52.653 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_city 
[INFO ] 2024-10-16 00:01:52.653 - [任务 3][MysqlCrmeb] - Table eb_system_city is going to be initial synced 
[INFO ] 2024-10-16 00:01:52.858 - [任务 3][MysqlCrmeb] - Query table 'eb_system_city' counts: 3938 
[INFO ] 2024-10-16 00:01:56.321 - [任务 3][MysqlCrmeb] - Table [eb_system_city] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:56.329 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_sms_record 
[INFO ] 2024-10-16 00:01:56.332 - [任务 3][MysqlCrmeb] - Table eb_sms_record is going to be initial synced 
[INFO ] 2024-10-16 00:01:56.404 - [任务 3][MysqlCrmeb] - Query table 'eb_sms_record' counts: 0 
[INFO ] 2024-10-16 00:01:56.404 - [任务 3][MysqlCrmeb] - Table [eb_sms_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:56.404 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_extract 
[INFO ] 2024-10-16 00:01:56.404 - [任务 3][MysqlCrmeb] - Table eb_user_extract is going to be initial synced 
[INFO ] 2024-10-16 00:01:56.609 - [任务 3][MysqlCrmeb] - Query table 'eb_user_extract' counts: 0 
[INFO ] 2024-10-16 00:01:57.126 - [任务 3][MysqlCrmeb] - Table [eb_user_extract] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:57.127 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_group 
[INFO ] 2024-10-16 00:01:57.127 - [任务 3][MysqlCrmeb] - Table eb_user_group is going to be initial synced 
[INFO ] 2024-10-16 00:01:57.209 - [任务 3][MysqlCrmeb] - Table [eb_user_group] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:57.209 - [任务 3][MysqlCrmeb] - Query table 'eb_user_group' counts: 3 
[INFO ] 2024-10-16 00:01:57.211 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_rule 
[INFO ] 2024-10-16 00:01:57.212 - [任务 3][MysqlCrmeb] - Table eb_store_product_rule is going to be initial synced 
[INFO ] 2024-10-16 00:01:57.277 - [任务 3][MysqlCrmeb] - Table [eb_store_product_rule] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:57.277 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_rule' counts: 2 
[INFO ] 2024-10-16 00:01:57.280 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_cart 
[INFO ] 2024-10-16 00:01:57.280 - [任务 3][MysqlCrmeb] - Table eb_store_cart is going to be initial synced 
[INFO ] 2024-10-16 00:01:57.352 - [任务 3][MysqlCrmeb] - Table [eb_store_cart] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:57.353 - [任务 3][MysqlCrmeb] - Query table 'eb_store_cart' counts: 3 
[INFO ] 2024-10-16 00:01:57.353 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_admin 
[INFO ] 2024-10-16 00:01:57.421 - [任务 3][MysqlCrmeb] - Table eb_system_admin is going to be initial synced 
[INFO ] 2024-10-16 00:01:57.422 - [任务 3][MysqlCrmeb] - Table [eb_system_admin] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:57.426 - [任务 3][MysqlCrmeb] - Query table 'eb_system_admin' counts: 2 
[INFO ] 2024-10-16 00:01:57.426 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_menu 
[INFO ] 2024-10-16 00:01:57.426 - [任务 3][MysqlCrmeb] - Table eb_system_menu is going to be initial synced 
[INFO ] 2024-10-16 00:01:57.568 - [任务 3][MysqlCrmeb] - Query table 'eb_system_menu' counts: 369 
[INFO ] 2024-10-16 00:01:57.569 - [任务 3][MysqlCrmeb] - Table [eb_system_menu] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:57.569 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_user_level 
[INFO ] 2024-10-16 00:01:57.569 - [任务 3][MysqlCrmeb] - Table eb_system_user_level is going to be initial synced 
[INFO ] 2024-10-16 00:01:57.626 - [任务 3][MysqlCrmeb] - Table [eb_system_user_level] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:57.634 - [任务 3][MysqlCrmeb] - Query table 'eb_system_user_level' counts: 5 
[INFO ] 2024-10-16 00:01:57.634 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_category 
[INFO ] 2024-10-16 00:01:57.699 - [任务 3][MysqlCrmeb] - Table eb_category is going to be initial synced 
[INFO ] 2024-10-16 00:01:57.700 - [任务 3][MysqlCrmeb] - Query table 'eb_category' counts: 295 
[INFO ] 2024-10-16 00:01:57.945 - [任务 3][MysqlCrmeb] - Table [eb_category] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:57.946 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_sms_template 
[INFO ] 2024-10-16 00:01:57.946 - [任务 3][MysqlCrmeb] - Table eb_sms_template is going to be initial synced 
[INFO ] 2024-10-16 00:01:58.013 - [任务 3][MysqlCrmeb] - Table [eb_sms_template] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:58.014 - [任务 3][MysqlCrmeb] - Query table 'eb_sms_template' counts: 7 
[INFO ] 2024-10-16 00:01:58.017 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_tag 
[INFO ] 2024-10-16 00:01:58.018 - [任务 3][MysqlCrmeb] - Table eb_user_tag is going to be initial synced 
[INFO ] 2024-10-16 00:01:58.084 - [任务 3][MysqlCrmeb] - Table [eb_user_tag] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:58.085 - [任务 3][MysqlCrmeb] - Query table 'eb_user_tag' counts: 5 
[INFO ] 2024-10-16 00:01:58.086 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_order_info 
[INFO ] 2024-10-16 00:01:58.086 - [任务 3][MysqlCrmeb] - Table eb_store_order_info is going to be initial synced 
[INFO ] 2024-10-16 00:01:58.155 - [任务 3][MysqlCrmeb] - Table [eb_store_order_info] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:58.155 - [任务 3][MysqlCrmeb] - Query table 'eb_store_order_info' counts: 0 
[INFO ] 2024-10-16 00:01:58.155 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_coupon_user 
[INFO ] 2024-10-16 00:01:58.157 - [任务 3][MysqlCrmeb] - Table eb_store_coupon_user is going to be initial synced 
[INFO ] 2024-10-16 00:01:58.225 - [任务 3][MysqlCrmeb] - Table [eb_store_coupon_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:58.225 - [任务 3][MysqlCrmeb] - Query table 'eb_store_coupon_user' counts: 0 
[INFO ] 2024-10-16 00:01:58.227 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_role 
[INFO ] 2024-10-16 00:01:58.227 - [任务 3][MysqlCrmeb] - Table eb_system_role is going to be initial synced 
[INFO ] 2024-10-16 00:01:58.292 - [任务 3][MysqlCrmeb] - Table [eb_system_role] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:58.292 - [任务 3][MysqlCrmeb] - Query table 'eb_system_role' counts: 2 
[INFO ] 2024-10-16 00:01:58.294 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_audio_history 
[INFO ] 2024-10-16 00:01:58.294 - [任务 3][MysqlCrmeb] - Table eb_store_experience_audio_history is going to be initial synced 
[INFO ] 2024-10-16 00:01:58.498 - [任务 3][MysqlCrmeb] - Table [eb_store_experience_audio_history] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:58.575 - [任务 3][MysqlCrmeb] - Query table 'eb_store_experience_audio_history' counts: 4 
[INFO ] 2024-10-16 00:01:58.575 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_pink 
[INFO ] 2024-10-16 00:01:58.575 - [任务 3][MysqlCrmeb] - Table eb_store_pink is going to be initial synced 
[INFO ] 2024-10-16 00:01:58.777 - [任务 3][MysqlCrmeb] - Table [eb_store_pink] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:58.856 - [任务 3][MysqlCrmeb] - Query table 'eb_store_pink' counts: 0 
[INFO ] 2024-10-16 00:01:58.856 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_express 
[INFO ] 2024-10-16 00:01:58.857 - [任务 3][MysqlCrmeb] - Table eb_express is going to be initial synced 
[INFO ] 2024-10-16 00:01:58.971 - [任务 3][MysqlCrmeb] - Query table 'eb_express' counts: 1067 
[INFO ] 2024-10-16 00:01:58.971 - [任务 3][MysqlCrmeb] - Table [eb_express] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:58.971 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_relation 
[INFO ] 2024-10-16 00:01:58.971 - [任务 3][MysqlCrmeb] - Table eb_store_product_relation is going to be initial synced 
[INFO ] 2024-10-16 00:01:59.034 - [任务 3][MysqlCrmeb] - Table [eb_store_product_relation] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:59.034 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_relation' counts: 0 
[INFO ] 2024-10-16 00:01:59.034 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_group_data 
[INFO ] 2024-10-16 00:01:59.034 - [任务 3][MysqlCrmeb] - Table eb_system_group_data is going to be initial synced 
[INFO ] 2024-10-16 00:01:59.108 - [任务 3][MysqlCrmeb] - Query table 'eb_system_group_data' counts: 68 
[INFO ] 2024-10-16 00:01:59.108 - [任务 3][MysqlCrmeb] - Table [eb_system_group_data] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:59.108 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_address 
[INFO ] 2024-10-16 00:01:59.109 - [任务 3][MysqlCrmeb] - Table eb_user_address is going to be initial synced 
[INFO ] 2024-10-16 00:01:59.171 - [任务 3][MysqlCrmeb] - Table [eb_user_address] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:01:59.171 - [任务 3][MysqlCrmeb] - Query table 'eb_user_address' counts: 0 
[INFO ] 2024-10-16 00:01:59.376 - [任务 3][MysqlCrmeb] - Initial sync completed 
[INFO ] 2024-10-16 00:02:00.144 - [任务 3][TestMysql] - Exception skipping - The current exception does not match the skip exception strategy, message: java.sql.SQLSyntaxErrorException: Access denied; you need (at least one of) the SUPER, REPLICATION CLIENT privilege(s) for this operation
 - TapdataEvent{syncStage=null, tapEvent=null, nodeIds=[f3947a0b-c177-4aa9-a4bf-f19033327883], sourceTime=null, sourceSerialNo=null} 
[ERROR] 2024-10-16 00:02:00.169 - [任务 3][TestMysql] - java.sql.SQLSyntaxErrorException: Access denied; you need (at least one of) the SUPER, REPLICATION CLIENT privilege(s) for this operation
 - TapdataEvent{syncStage=null, tapEvent=null, nodeIds=[f3947a0b-c177-4aa9-a4bf-f19033327883], sourceTime=null, sourceSerialNo=null} <-- Error Message -->
java.sql.SQLSyntaxErrorException: Access denied; you need (at least one of) the SUPER, REPLICATION CLIENT privilege(s) for this operation
 - TapdataEvent{syncStage=null, tapEvent=null, nodeIds=[f3947a0b-c177-4aa9-a4bf-f19033327883], sourceTime=null, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Access denied; you need (at least one of) the SUPER, REPLICATION CLIENT privilege(s) for this operation
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: Access denied; you need (at least one of) the SUPER, REPLICATION CLIENT privilege(s) for this operation
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvent(HazelcastTargetPdkBaseNode.java:763)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:710)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:663)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$12(HazelcastTargetPdkBaseNode.java:623)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:516)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processQueueConsume$13(HazelcastTargetPdkBaseNode.java:595)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.drainAndRun(HazelcastTargetPdkBaseNode.java:647)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processQueueConsume(HazelcastTargetPdkBaseNode.java:594)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Access denied; you need (at least one of) the SUPER, REPLICATION CLIENT privilege(s) for this operation
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.normalQuery(JdbcContext.java:107)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.readBinlogPosition(MysqlJdbcContextV2.java:143)
	at io.tapdata.connector.mysql.MysqlConnector.timestampToStreamOffset(MysqlConnector.java:711)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$initStreamOffsetFromTime$9(HazelcastSourcePdkBaseNode.java:672)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetFromTime(HazelcastSourcePdkBaseNode.java:670)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initStreamOffsetInitialAndCDC(HazelcastSourcePdkBaseNode.java:650)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffsetFirstTime(HazelcastSourcePdkBaseNode.java:584)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initBatchAndStreamOffset(HazelcastSourcePdkBaseNode.java:467)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.initSyncProgress(HazelcastSourcePdkBaseNode.java:359)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:236)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more

[INFO ] 2024-10-16 00:02:00.169 - [任务 3][TestMysql] - Job suspend in error handle 
[INFO ] 2024-10-16 00:02:00.665 - [任务 3][MysqlCrmeb] - Node MysqlCrmeb[f3947a0b-c177-4aa9-a4bf-f19033327883] running status set to false 
[INFO ] 2024-10-16 00:02:00.666 - [任务 3][MysqlCrmeb] - PDK connector node stopped: HazelcastSourcePdkDataNode-f3947a0b-c177-4aa9-a4bf-f19033327883 
[INFO ] 2024-10-16 00:02:00.666 - [任务 3][MysqlCrmeb] - PDK connector node released: HazelcastSourcePdkDataNode-f3947a0b-c177-4aa9-a4bf-f19033327883 
[INFO ] 2024-10-16 00:02:00.666 - [任务 3][MysqlCrmeb] - Node MysqlCrmeb[f3947a0b-c177-4aa9-a4bf-f19033327883] schema data cleaned 
[INFO ] 2024-10-16 00:02:00.668 - [任务 3][MysqlCrmeb] - Node MysqlCrmeb[f3947a0b-c177-4aa9-a4bf-f19033327883] monitor closed 
[INFO ] 2024-10-16 00:02:00.668 - [任务 3][MysqlCrmeb] - Node MysqlCrmeb[f3947a0b-c177-4aa9-a4bf-f19033327883] close complete, cost 58 ms 
[INFO ] 2024-10-16 00:02:00.668 - [任务 3][TestMysql] - Node TestMysql[2bee360c-c91c-49bf-b924-6a206b23f1cd] running status set to false 
[INFO ] 2024-10-16 00:02:00.704 - [任务 3][TestMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-2bee360c-c91c-49bf-b924-6a206b23f1cd 
[INFO ] 2024-10-16 00:02:00.705 - [任务 3][TestMysql] - PDK connector node released: HazelcastTargetPdkDataNode-2bee360c-c91c-49bf-b924-6a206b23f1cd 
[INFO ] 2024-10-16 00:02:00.705 - [任务 3][TestMysql] - Node TestMysql[2bee360c-c91c-49bf-b924-6a206b23f1cd] schema data cleaned 
[INFO ] 2024-10-16 00:02:00.705 - [任务 3][TestMysql] - Node TestMysql[2bee360c-c91c-49bf-b924-6a206b23f1cd] monitor closed 
[INFO ] 2024-10-16 00:02:00.915 - [任务 3][TestMysql] - Node TestMysql[2bee360c-c91c-49bf-b924-6a206b23f1cd] close complete, cost 38 ms 
[INFO ] 2024-10-16 00:02:00.997 - [任务 3] - Task [任务 3] cannot retry, reason: Task retry service not start 
[INFO ] 2024-10-16 00:02:00.997 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-16 00:02:01.010 - [任务 3] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@34be049e 
[INFO ] 2024-10-16 00:02:01.011 - [任务 3] - Stop task milestones: 670e91cfe5f4980f3e528de4(任务 3)  
[INFO ] 2024-10-16 00:02:01.212 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-10-16 00:02:01.254 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-10-16 00:02:01.256 - [任务 3] - Remove memory task client succeed, task: 任务 3[670e91cfe5f4980f3e528de4] 
[INFO ] 2024-10-16 00:02:01.256 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[670e91cfe5f4980f3e528de4] 
[INFO ] 2024-10-16 00:02:32.396 - [任务 3] - Start task milestones: 670e91cfe5f4980f3e528de4(任务 3) 
[INFO ] 2024-10-16 00:02:32.397 - [任务 3] - Task initialization... 
[INFO ] 2024-10-16 00:02:33.528 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-16 00:02:33.528 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-16 00:02:33.655 - [任务 3][MysqlCrmeb] - Node MysqlCrmeb[f3947a0b-c177-4aa9-a4bf-f19033327883] start preload schema,table counts: 73 
[INFO ] 2024-10-16 00:02:33.657 - [任务 3][TestMysql] - Node TestMysql[2bee360c-c91c-49bf-b924-6a206b23f1cd] start preload schema,table counts: 73 
[INFO ] 2024-10-16 00:02:33.659 - [任务 3][TestMysql] - Node TestMysql[2bee360c-c91c-49bf-b924-6a206b23f1cd] preload schema finished, cost 0 ms 
[INFO ] 2024-10-16 00:02:33.661 - [任务 3][MysqlCrmeb] - Node MysqlCrmeb[f3947a0b-c177-4aa9-a4bf-f19033327883] preload schema finished, cost 0 ms 
[INFO ] 2024-10-16 00:02:34.428 - [任务 3][TestMysql] - Node(TestMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-16 00:02:34.431 - [任务 3][TestMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-16 00:02:34.449 - [任务 3][TestMysql] - Table "dragon.eb_store_order" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:34.449 - [任务 3][TestMysql] - The table eb_store_order has already exist. 
[INFO ] 2024-10-16 00:02:34.520 - [任务 3][TestMysql] - Table "dragon.eb_store_product_attr_value" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:34.553 - [任务 3][TestMysql] - The table eb_store_product_attr_value has already exist. 
[INFO ] 2024-10-16 00:02:34.553 - [任务 3][TestMysql] - Table "dragon.eb_wechat_callback" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:34.553 - [任务 3][TestMysql] - The table eb_wechat_callback has already exist. 
[INFO ] 2024-10-16 00:02:34.578 - [任务 3][TestMysql] - Table "dragon.eb_shipping_templates_free" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:34.578 - [任务 3][TestMysql] - The table eb_shipping_templates_free has already exist. 
[INFO ] 2024-10-16 00:02:34.619 - [任务 3][TestMysql] - Table "dragon.eb_system_notification" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:34.623 - [任务 3][TestMysql] - The table eb_system_notification has already exist. 
[INFO ] 2024-10-16 00:02:34.644 - [任务 3][TestMysql] - Table "dragon.eb_store_heal_user" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:34.644 - [任务 3][TestMysql] - The table eb_store_heal_user has already exist. 
[INFO ] 2024-10-16 00:02:34.669 - [任务 3][TestMysql] - Table "dragon.eb_user_level" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:34.669 - [任务 3][TestMysql] - The table eb_user_level has already exist. 
[INFO ] 2024-10-16 00:02:34.696 - [任务 3][TestMysql] - Table "dragon.eb_user_token" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:34.696 - [任务 3][TestMysql] - The table eb_user_token has already exist. 
[INFO ] 2024-10-16 00:02:34.722 - [任务 3][TestMysql] - Table "dragon.eb_system_group" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:34.722 - [任务 3][TestMysql] - The table eb_system_group has already exist. 
[INFO ] 2024-10-16 00:02:34.792 - [任务 3][TestMysql] - Table "dragon.eb_system_config" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:34.792 - [任务 3][TestMysql] - The table eb_system_config has already exist. 
[INFO ] 2024-10-16 00:02:34.814 - [任务 3][TestMysql] - Table "dragon.eb_user_sign" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:34.814 - [任务 3][TestMysql] - The table eb_user_sign has already exist. 
[INFO ] 2024-10-16 00:02:34.846 - [任务 3][TestMysql] - Table "dragon.eb_store_product_cate" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:34.846 - [任务 3][TestMysql] - The table eb_store_product_cate has already exist. 
[INFO ] 2024-10-16 00:02:34.875 - [任务 3][TestMysql] - Table "dragon.eb_user_experience_record" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:34.875 - [任务 3][TestMysql] - The table eb_user_experience_record has already exist. 
[INFO ] 2024-10-16 00:02:34.900 - [任务 3][MysqlCrmeb] - Source node "MysqlCrmeb" read batch size: 100 
[INFO ] 2024-10-16 00:02:34.900 - [任务 3][MysqlCrmeb] - Source node "MysqlCrmeb" event queue capacity: 200 
[INFO ] 2024-10-16 00:02:34.900 - [任务 3][MysqlCrmeb] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-16 00:02:34.901 - [任务 3][MysqlCrmeb] - batch offset found: {},stream offset not found. 
[INFO ] 2024-10-16 00:02:34.901 - [任务 3][MysqlCrmeb] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-16 00:02:34.907 - [任务 3][TestMysql] - Table "dragon.eb_store_brand_story" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:34.907 - [任务 3][TestMysql] - The table eb_store_brand_story has already exist. 
[INFO ] 2024-10-16 00:02:34.941 - [任务 3][TestMysql] - Table "dragon.eb_store_heal" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:34.941 - [任务 3][TestMysql] - The table eb_store_heal has already exist. 
[INFO ] 2024-10-16 00:02:34.971 - [任务 3][TestMysql] - Table "dragon.eb_store_product_description" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:34.971 - [任务 3][TestMysql] - The table eb_store_product_description has already exist. 
[INFO ] 2024-10-16 00:02:34.997 - [任务 3][TestMysql] - Table "dragon.eb_store_product_reply" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:34.997 - [任务 3][TestMysql] - The table eb_store_product_reply has already exist. 
[INFO ] 2024-10-16 00:02:35.027 - [任务 3][TestMysql] - Table "dragon.eb_user_brokerage_record" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.027 - [任务 3][TestMysql] - The table eb_user_brokerage_record has already exist. 
[INFO ] 2024-10-16 00:02:35.057 - [任务 3][TestMysql] - Table "dragon.eb_system_attachment" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.057 - [任务 3][TestMysql] - The table eb_system_attachment has already exist. 
[INFO ] 2024-10-16 00:02:35.082 - [任务 3][TestMysql] - Table "dragon.eb_system_store_staff" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.082 - [任务 3][TestMysql] - The table eb_system_store_staff has already exist. 
[INFO ] 2024-10-16 00:02:35.107 - [任务 3][TestMysql] - Table "dragon.eb_wechat_reply" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.107 - [任务 3][TestMysql] - The table eb_wechat_reply has already exist. 
[INFO ] 2024-10-16 00:02:35.114 - [任务 3][MysqlCrmeb] - Initial sync started 
[INFO ] 2024-10-16 00:02:35.114 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_order 
[INFO ] 2024-10-16 00:02:35.142 - [任务 3][MysqlCrmeb] - Table eb_store_order is going to be initial synced 
[INFO ] 2024-10-16 00:02:35.142 - [任务 3][TestMysql] - Table "dragon.eb_store_bargain_user_help" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.142 - [任务 3][TestMysql] - The table eb_store_bargain_user_help has already exist. 
[INFO ] 2024-10-16 00:02:35.177 - [任务 3][TestMysql] - Table "dragon.eb_store_seckill" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.177 - [任务 3][TestMysql] - The table eb_store_seckill has already exist. 
[INFO ] 2024-10-16 00:02:35.212 - [任务 3][MysqlCrmeb] - Table [eb_store_order] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:35.213 - [任务 3][TestMysql] - Table "dragon.eb_store_product_attr_result" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.213 - [任务 3][TestMysql] - The table eb_store_product_attr_result has already exist. 
[INFO ] 2024-10-16 00:02:35.213 - [任务 3][MysqlCrmeb] - Query table 'eb_store_order' counts: 0 
[INFO ] 2024-10-16 00:02:35.214 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_attr_value 
[INFO ] 2024-10-16 00:02:35.214 - [任务 3][MysqlCrmeb] - Table eb_store_product_attr_value is going to be initial synced 
[INFO ] 2024-10-16 00:02:35.238 - [任务 3][TestMysql] - Table "dragon.eb_shipping_templates" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.238 - [任务 3][TestMysql] - The table eb_shipping_templates has already exist. 
[INFO ] 2024-10-16 00:02:35.267 - [任务 3][TestMysql] - Table "dragon.eb_store_seckill_manger" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.268 - [任务 3][TestMysql] - The table eb_store_seckill_manger has already exist. 
[INFO ] 2024-10-16 00:02:35.278 - [任务 3][MysqlCrmeb] - Table [eb_store_product_attr_value] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:35.286 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_attr_value' counts: 38 
[INFO ] 2024-10-16 00:02:35.286 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_wechat_callback 
[INFO ] 2024-10-16 00:02:35.287 - [任务 3][MysqlCrmeb] - Table eb_wechat_callback is going to be initial synced 
[INFO ] 2024-10-16 00:02:35.305 - [任务 3][TestMysql] - Table "dragon.eb_user" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.305 - [任务 3][TestMysql] - The table eb_user has already exist. 
[INFO ] 2024-10-16 00:02:35.348 - [任务 3][MysqlCrmeb] - Table [eb_wechat_callback] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:35.350 - [任务 3][TestMysql] - Table "dragon.eb_store_order_status" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.350 - [任务 3][TestMysql] - The table eb_store_order_status has already exist. 
[INFO ] 2024-10-16 00:02:35.361 - [任务 3][MysqlCrmeb] - Query table 'eb_wechat_callback' counts: 0 
[INFO ] 2024-10-16 00:02:35.361 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_shipping_templates_free 
[INFO ] 2024-10-16 00:02:35.378 - [任务 3][MysqlCrmeb] - Table eb_shipping_templates_free is going to be initial synced 
[INFO ] 2024-10-16 00:02:35.379 - [任务 3][TestMysql] - Table "dragon.eb_system_form_temp" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.379 - [任务 3][TestMysql] - The table eb_system_form_temp has already exist. 
[INFO ] 2024-10-16 00:02:35.408 - [任务 3][TestMysql] - Table "dragon.eb_store_experience_reply" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.428 - [任务 3][TestMysql] - The table eb_store_experience_reply has already exist. 
[INFO ] 2024-10-16 00:02:35.435 - [任务 3][MysqlCrmeb] - Table [eb_shipping_templates_free] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:35.441 - [任务 3][MysqlCrmeb] - Query table 'eb_shipping_templates_free' counts: 32 
[INFO ] 2024-10-16 00:02:35.441 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_notification 
[INFO ] 2024-10-16 00:02:35.450 - [任务 3][MysqlCrmeb] - Table eb_system_notification is going to be initial synced 
[INFO ] 2024-10-16 00:02:35.450 - [任务 3][TestMysql] - Table "dragon.eb_store_bargain_user" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.518 - [任务 3][TestMysql] - The table eb_store_bargain_user has already exist. 
[INFO ] 2024-10-16 00:02:35.520 - [任务 3][TestMysql] - Table "dragon.eb_system_store" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.520 - [任务 3][TestMysql] - The table eb_system_store has already exist. 
[INFO ] 2024-10-16 00:02:35.520 - [任务 3][MysqlCrmeb] - Table [eb_system_notification] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:35.523 - [任务 3][MysqlCrmeb] - Query table 'eb_system_notification' counts: 12 
[INFO ] 2024-10-16 00:02:35.523 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_heal_user 
[INFO ] 2024-10-16 00:02:35.553 - [任务 3][MysqlCrmeb] - Table eb_store_heal_user is going to be initial synced 
[INFO ] 2024-10-16 00:02:35.553 - [任务 3][TestMysql] - Table "dragon.eb_shipping_templates_region" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.577 - [任务 3][TestMysql] - The table eb_shipping_templates_region has already exist. 
[INFO ] 2024-10-16 00:02:35.577 - [任务 3][TestMysql] - Table "dragon.eb_user_recharge" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.577 - [任务 3][TestMysql] - The table eb_user_recharge has already exist. 
[INFO ] 2024-10-16 00:02:35.585 - [任务 3][MysqlCrmeb] - Table [eb_store_heal_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:35.593 - [任务 3][MysqlCrmeb] - Query table 'eb_store_heal_user' counts: 9 
[INFO ] 2024-10-16 00:02:35.593 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_level 
[INFO ] 2024-10-16 00:02:35.607 - [任务 3][MysqlCrmeb] - Table eb_user_level is going to be initial synced 
[INFO ] 2024-10-16 00:02:35.607 - [任务 3][TestMysql] - Table "dragon.eb_store_product_coupon" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.635 - [任务 3][TestMysql] - The table eb_store_product_coupon has already exist. 
[INFO ] 2024-10-16 00:02:35.635 - [任务 3][TestMysql] - Table "dragon.eb_wechat_pay_info" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.635 - [任务 3][TestMysql] - The table eb_wechat_pay_info has already exist. 
[INFO ] 2024-10-16 00:02:35.655 - [任务 3][MysqlCrmeb] - Table [eb_user_level] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:35.661 - [任务 3][MysqlCrmeb] - Query table 'eb_user_level' counts: 1 
[INFO ] 2024-10-16 00:02:35.663 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_token 
[INFO ] 2024-10-16 00:02:35.663 - [任务 3][MysqlCrmeb] - Table eb_user_token is going to be initial synced 
[INFO ] 2024-10-16 00:02:35.669 - [任务 3][TestMysql] - Table "dragon.eb_store_combination" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.669 - [任务 3][TestMysql] - The table eb_store_combination has already exist. 
[INFO ] 2024-10-16 00:02:35.706 - [任务 3][TestMysql] - Table "dragon.eb_store_product" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.706 - [任务 3][TestMysql] - The table eb_store_product has already exist. 
[INFO ] 2024-10-16 00:02:35.730 - [任务 3][MysqlCrmeb] - Table [eb_user_token] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:35.731 - [任务 3][MysqlCrmeb] - Query table 'eb_user_token' counts: 3 
[INFO ] 2024-10-16 00:02:35.731 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_group 
[INFO ] 2024-10-16 00:02:35.731 - [任务 3][MysqlCrmeb] - Table eb_system_group is going to be initial synced 
[INFO ] 2024-10-16 00:02:35.747 - [任务 3][TestMysql] - Table "dragon.eb_article" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.747 - [任务 3][TestMysql] - The table eb_article has already exist. 
[INFO ] 2024-10-16 00:02:35.778 - [任务 3][TestMysql] - Table "dragon.eb_user_bill" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.778 - [任务 3][TestMysql] - The table eb_user_bill has already exist. 
[INFO ] 2024-10-16 00:02:35.801 - [任务 3][MysqlCrmeb] - Table [eb_system_group] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:35.802 - [任务 3][MysqlCrmeb] - Query table 'eb_system_group' counts: 18 
[INFO ] 2024-10-16 00:02:35.808 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_config 
[INFO ] 2024-10-16 00:02:35.808 - [任务 3][MysqlCrmeb] - Table eb_system_config is going to be initial synced 
[INFO ] 2024-10-16 00:02:35.816 - [任务 3][TestMysql] - Table "dragon.eb_store_experience_relation" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.816 - [任务 3][TestMysql] - The table eb_store_experience_relation has already exist. 
[INFO ] 2024-10-16 00:02:35.840 - [任务 3][TestMysql] - Table "dragon.eb_wechat_exceptions" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.840 - [任务 3][TestMysql] - The table eb_wechat_exceptions has already exist. 
[INFO ] 2024-10-16 00:02:35.865 - [任务 3][TestMysql] - Table "dragon.eb_template_message" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.865 - [任务 3][TestMysql] - The table eb_template_message has already exist. 
[INFO ] 2024-10-16 00:02:35.873 - [任务 3][MysqlCrmeb] - Query table 'eb_system_config' counts: 177 
[INFO ] 2024-10-16 00:02:35.873 - [任务 3][MysqlCrmeb] - Table [eb_system_config] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:35.874 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_sign 
[INFO ] 2024-10-16 00:02:35.874 - [任务 3][MysqlCrmeb] - Table eb_user_sign is going to be initial synced 
[INFO ] 2024-10-16 00:02:35.892 - [任务 3][TestMysql] - Table "dragon.eb_user_integral_record" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.892 - [任务 3][TestMysql] - The table eb_user_integral_record has already exist. 
[INFO ] 2024-10-16 00:02:35.922 - [任务 3][TestMysql] - Table "dragon.eb_store_coupon" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.922 - [任务 3][TestMysql] - The table eb_store_coupon has already exist. 
[INFO ] 2024-10-16 00:02:35.942 - [任务 3][MysqlCrmeb] - Table [eb_user_sign] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:35.942 - [任务 3][MysqlCrmeb] - Query table 'eb_user_sign' counts: 1 
[INFO ] 2024-10-16 00:02:35.943 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_cate 
[INFO ] 2024-10-16 00:02:35.943 - [任务 3][MysqlCrmeb] - Table eb_store_product_cate is going to be initial synced 
[INFO ] 2024-10-16 00:02:35.951 - [任务 3][TestMysql] - Table "dragon.eb_user_visit_record" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.951 - [任务 3][TestMysql] - The table eb_user_visit_record has already exist. 
[INFO ] 2024-10-16 00:02:35.975 - [任务 3][TestMysql] - Table "dragon.eb_system_role_menu" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.975 - [任务 3][TestMysql] - The table eb_system_role_menu has already exist. 
[INFO ] 2024-10-16 00:02:35.996 - [任务 3][TestMysql] - Table "dragon.eb_store_product_log" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:35.996 - [任务 3][TestMysql] - The table eb_store_product_log has already exist. 
[INFO ] 2024-10-16 00:02:36.009 - [任务 3][MysqlCrmeb] - Table [eb_store_product_cate] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:36.009 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_cate' counts: 0 
[INFO ] 2024-10-16 00:02:36.010 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_experience_record 
[INFO ] 2024-10-16 00:02:36.010 - [任务 3][MysqlCrmeb] - Table eb_user_experience_record is going to be initial synced 
[INFO ] 2024-10-16 00:02:36.031 - [任务 3][TestMysql] - Table "dragon.eb_store_product_series" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.032 - [任务 3][TestMysql] - The table eb_store_product_series has already exist. 
[INFO ] 2024-10-16 00:02:36.057 - [任务 3][TestMysql] - Table "dragon.eb_store_product_attr" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.057 - [任务 3][TestMysql] - The table eb_store_product_attr has already exist. 
[INFO ] 2024-10-16 00:02:36.077 - [任务 3][MysqlCrmeb] - Table [eb_user_experience_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:36.078 - [任务 3][MysqlCrmeb] - Query table 'eb_user_experience_record' counts: 1 
[INFO ] 2024-10-16 00:02:36.079 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_brand_story 
[INFO ] 2024-10-16 00:02:36.079 - [任务 3][MysqlCrmeb] - Table eb_store_brand_story is going to be initial synced 
[INFO ] 2024-10-16 00:02:36.081 - [任务 3][TestMysql] - Table "dragon.eb_store_bargain" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.081 - [任务 3][TestMysql] - The table eb_store_bargain has already exist. 
[INFO ] 2024-10-16 00:02:36.113 - [任务 3][TestMysql] - Table "dragon.eb_store_experience_audio" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.114 - [任务 3][TestMysql] - The table eb_store_experience_audio has already exist. 
[INFO ] 2024-10-16 00:02:36.137 - [任务 3][TestMysql] - Table "dragon.eb_system_city" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.137 - [任务 3][TestMysql] - The table eb_system_city has already exist. 
[INFO ] 2024-10-16 00:02:36.143 - [任务 3][MysqlCrmeb] - Table [eb_store_brand_story] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:36.143 - [任务 3][MysqlCrmeb] - Query table 'eb_store_brand_story' counts: 1 
[INFO ] 2024-10-16 00:02:36.143 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_heal 
[INFO ] 2024-10-16 00:02:36.143 - [任务 3][MysqlCrmeb] - Table eb_store_heal is going to be initial synced 
[INFO ] 2024-10-16 00:02:36.170 - [任务 3][TestMysql] - Table "dragon.eb_sms_record" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.170 - [任务 3][TestMysql] - The table eb_sms_record has already exist. 
[INFO ] 2024-10-16 00:02:36.201 - [任务 3][TestMysql] - Table "dragon.eb_user_extract" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.201 - [任务 3][TestMysql] - The table eb_user_extract has already exist. 
[INFO ] 2024-10-16 00:02:36.208 - [任务 3][MysqlCrmeb] - Table [eb_store_heal] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:36.213 - [任务 3][MysqlCrmeb] - Query table 'eb_store_heal' counts: 5 
[INFO ] 2024-10-16 00:02:36.213 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_description 
[INFO ] 2024-10-16 00:02:36.230 - [任务 3][MysqlCrmeb] - Table eb_store_product_description is going to be initial synced 
[INFO ] 2024-10-16 00:02:36.230 - [任务 3][TestMysql] - Table "dragon.eb_user_group" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.230 - [任务 3][TestMysql] - The table eb_user_group has already exist. 
[INFO ] 2024-10-16 00:02:36.255 - [任务 3][TestMysql] - Table "dragon.eb_store_product_rule" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.255 - [任务 3][TestMysql] - The table eb_store_product_rule has already exist. 
[INFO ] 2024-10-16 00:02:36.273 - [任务 3][MysqlCrmeb] - Table [eb_store_product_description] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:36.281 - [任务 3][TestMysql] - Table "dragon.eb_store_cart" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.281 - [任务 3][TestMysql] - The table eb_store_cart has already exist. 
[INFO ] 2024-10-16 00:02:36.308 - [任务 3][TestMysql] - Table "dragon.eb_system_admin" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.308 - [任务 3][TestMysql] - The table eb_system_admin has already exist. 
[INFO ] 2024-10-16 00:02:36.332 - [任务 3][TestMysql] - Table "dragon.eb_system_menu" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.332 - [任务 3][TestMysql] - The table eb_system_menu has already exist. 
[INFO ] 2024-10-16 00:02:36.354 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_description' counts: 18 
[INFO ] 2024-10-16 00:02:36.354 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_reply 
[INFO ] 2024-10-16 00:02:36.358 - [任务 3][MysqlCrmeb] - Table eb_store_product_reply is going to be initial synced 
[INFO ] 2024-10-16 00:02:36.359 - [任务 3][TestMysql] - Table "dragon.eb_system_user_level" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.359 - [任务 3][TestMysql] - The table eb_system_user_level has already exist. 
[INFO ] 2024-10-16 00:02:36.388 - [任务 3][TestMysql] - Table "dragon.eb_category" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.388 - [任务 3][TestMysql] - The table eb_category has already exist. 
[INFO ] 2024-10-16 00:02:36.421 - [任务 3][MysqlCrmeb] - Table [eb_store_product_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:36.424 - [任务 3][TestMysql] - Table "dragon.eb_sms_template" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.424 - [任务 3][TestMysql] - The table eb_sms_template has already exist. 
[INFO ] 2024-10-16 00:02:36.425 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_reply' counts: 1 
[INFO ] 2024-10-16 00:02:36.425 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_brokerage_record 
[INFO ] 2024-10-16 00:02:36.425 - [任务 3][MysqlCrmeb] - Table eb_user_brokerage_record is going to be initial synced 
[INFO ] 2024-10-16 00:02:36.450 - [任务 3][TestMysql] - Table "dragon.eb_user_tag" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.450 - [任务 3][TestMysql] - The table eb_user_tag has already exist. 
[INFO ] 2024-10-16 00:02:36.484 - [任务 3][TestMysql] - Table "dragon.eb_store_order_info" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.484 - [任务 3][TestMysql] - The table eb_store_order_info has already exist. 
[INFO ] 2024-10-16 00:02:36.493 - [任务 3][MysqlCrmeb] - Table [eb_user_brokerage_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:36.493 - [任务 3][MysqlCrmeb] - Query table 'eb_user_brokerage_record' counts: 0 
[INFO ] 2024-10-16 00:02:36.493 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_attachment 
[INFO ] 2024-10-16 00:02:36.493 - [任务 3][MysqlCrmeb] - Table eb_system_attachment is going to be initial synced 
[INFO ] 2024-10-16 00:02:36.514 - [任务 3][TestMysql] - Table "dragon.eb_store_coupon_user" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.515 - [任务 3][TestMysql] - The table eb_store_coupon_user has already exist. 
[INFO ] 2024-10-16 00:02:36.545 - [任务 3][TestMysql] - Table "dragon.eb_system_role" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.545 - [任务 3][TestMysql] - The table eb_system_role has already exist. 
[INFO ] 2024-10-16 00:02:36.569 - [任务 3][MysqlCrmeb] - Query table 'eb_system_attachment' counts: 288 
[INFO ] 2024-10-16 00:02:36.569 - [任务 3][TestMysql] - Table "dragon.eb_store_experience_audio_history" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.569 - [任务 3][TestMysql] - The table eb_store_experience_audio_history has already exist. 
[INFO ] 2024-10-16 00:02:36.594 - [任务 3][TestMysql] - Table "dragon.eb_store_pink" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.622 - [任务 3][TestMysql] - The table eb_store_pink has already exist. 
[INFO ] 2024-10-16 00:02:36.622 - [任务 3][TestMysql] - Table "dragon.eb_express" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.622 - [任务 3][TestMysql] - The table eb_express has already exist. 
[INFO ] 2024-10-16 00:02:36.646 - [任务 3][TestMysql] - Table "dragon.eb_store_product_relation" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.646 - [任务 3][TestMysql] - The table eb_store_product_relation has already exist. 
[INFO ] 2024-10-16 00:02:36.671 - [任务 3][TestMysql] - Table "dragon.eb_system_group_data" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.671 - [任务 3][TestMysql] - The table eb_system_group_data has already exist. 
[INFO ] 2024-10-16 00:02:36.695 - [任务 3][TestMysql] - Table "dragon.eb_user_address" exists, skip auto create table 
[INFO ] 2024-10-16 00:02:36.731 - [任务 3][TestMysql] - The table eb_user_address has already exist. 
[WARN ] 2024-10-16 00:02:36.735 - [任务 3][TestMysql] - Found sync stage is null when flush sync progress, event: TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[f3947a0b-c177-4aa9-a4bf-f19033327883], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[INFO ] 2024-10-16 00:02:36.764 - [任务 3][MysqlCrmeb] - Table [eb_system_attachment] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:36.764 - [任务 3][TestMysql] - Table 'eb_store_product_attr_value' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:36.765 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_store_staff 
[INFO ] 2024-10-16 00:02:36.765 - [任务 3][MysqlCrmeb] - Table eb_system_store_staff is going to be initial synced 
[INFO ] 2024-10-16 00:02:36.772 - [任务 3][TestMysql] - Table 'eb_shipping_templates_free' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:36.776 - [任务 3][TestMysql] - Table 'eb_system_notification' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:36.776 - [任务 3][TestMysql] - Table 'eb_store_heal_user' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:36.780 - [任务 3][TestMysql] - Table 'eb_user_level' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:36.780 - [任务 3][TestMysql] - Table 'eb_user_token' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:36.784 - [任务 3][TestMysql] - Table 'eb_user_token' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-10-16 00:02:36.784 - [任务 3][TestMysql] - Table 'eb_system_group' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:36.798 - [任务 3][TestMysql] - Table 'eb_system_config' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:36.798 - [任务 3][TestMysql] - Table 'eb_system_config' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-10-16 00:02:36.803 - [任务 3][TestMysql] - Table 'eb_user_sign' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:36.803 - [任务 3][TestMysql] - Table 'eb_user_experience_record' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:36.807 - [任务 3][TestMysql] - Table 'eb_store_brand_story' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:36.807 - [任务 3][TestMysql] - Table 'eb_store_heal' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:36.812 - [任务 3][TestMysql] - Table 'eb_store_product_description' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:36.813 - [任务 3][TestMysql] - Table 'eb_store_product_reply' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:36.824 - [任务 3][TestMysql] - Table 'eb_system_attachment' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:36.824 - [任务 3][MysqlCrmeb] - Table [eb_system_store_staff] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:36.835 - [任务 3][TestMysql] - Table 'eb_system_attachment' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-10-16 00:02:36.835 - [任务 3][MysqlCrmeb] - Query table 'eb_system_store_staff' counts: 0 
[INFO ] 2024-10-16 00:02:36.838 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_wechat_reply 
[INFO ] 2024-10-16 00:02:36.838 - [任务 3][MysqlCrmeb] - Table eb_wechat_reply is going to be initial synced 
[INFO ] 2024-10-16 00:02:36.898 - [任务 3][TestMysql] - Table 'eb_system_attachment' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-10-16 00:02:36.898 - [任务 3][MysqlCrmeb] - Table [eb_wechat_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:36.905 - [任务 3][MysqlCrmeb] - Query table 'eb_wechat_reply' counts: 1 
[INFO ] 2024-10-16 00:02:36.905 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_bargain_user_help 
[INFO ] 2024-10-16 00:02:36.962 - [任务 3][MysqlCrmeb] - Table eb_store_bargain_user_help is going to be initial synced 
[INFO ] 2024-10-16 00:02:36.962 - [任务 3][MysqlCrmeb] - Table [eb_store_bargain_user_help] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:36.971 - [任务 3][MysqlCrmeb] - Query table 'eb_store_bargain_user_help' counts: 0 
[INFO ] 2024-10-16 00:02:36.971 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_seckill 
[INFO ] 2024-10-16 00:02:36.971 - [任务 3][MysqlCrmeb] - Table eb_store_seckill is going to be initial synced 
[INFO ] 2024-10-16 00:02:37.033 - [任务 3][MysqlCrmeb] - Table [eb_store_seckill] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:37.040 - [任务 3][MysqlCrmeb] - Query table 'eb_store_seckill' counts: 4 
[INFO ] 2024-10-16 00:02:37.040 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_attr_result 
[INFO ] 2024-10-16 00:02:37.102 - [任务 3][MysqlCrmeb] - Table eb_store_product_attr_result is going to be initial synced 
[INFO ] 2024-10-16 00:02:37.102 - [任务 3][MysqlCrmeb] - Table [eb_store_product_attr_result] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:37.108 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_attr_result' counts: 0 
[INFO ] 2024-10-16 00:02:37.108 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_shipping_templates 
[INFO ] 2024-10-16 00:02:37.171 - [任务 3][MysqlCrmeb] - Table eb_shipping_templates is going to be initial synced 
[INFO ] 2024-10-16 00:02:37.171 - [任务 3][MysqlCrmeb] - Table [eb_shipping_templates] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:37.179 - [任务 3][MysqlCrmeb] - Query table 'eb_shipping_templates' counts: 1 
[INFO ] 2024-10-16 00:02:37.179 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_seckill_manger 
[INFO ] 2024-10-16 00:02:37.241 - [任务 3][MysqlCrmeb] - Table eb_store_seckill_manger is going to be initial synced 
[INFO ] 2024-10-16 00:02:37.241 - [任务 3][MysqlCrmeb] - Table [eb_store_seckill_manger] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:37.259 - [任务 3][MysqlCrmeb] - Query table 'eb_store_seckill_manger' counts: 3 
[INFO ] 2024-10-16 00:02:37.259 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user 
[INFO ] 2024-10-16 00:02:37.326 - [任务 3][MysqlCrmeb] - Table eb_user is going to be initial synced 
[INFO ] 2024-10-16 00:02:37.326 - [任务 3][MysqlCrmeb] - Table [eb_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:37.333 - [任务 3][MysqlCrmeb] - Query table 'eb_user' counts: 4 
[INFO ] 2024-10-16 00:02:37.333 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_order_status 
[INFO ] 2024-10-16 00:02:37.363 - [任务 3][MysqlCrmeb] - Table eb_store_order_status is going to be initial synced 
[INFO ] 2024-10-16 00:02:37.363 - [任务 3][TestMysql] - Table 'eb_system_attachment' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-10-16 00:02:37.375 - [任务 3][TestMysql] - Table 'eb_wechat_reply' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:37.375 - [任务 3][TestMysql] - Table 'eb_store_seckill' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:37.390 - [任务 3][TestMysql] - Table 'eb_shipping_templates' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:37.390 - [任务 3][TestMysql] - Table 'eb_store_seckill_manger' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:37.402 - [任务 3][MysqlCrmeb] - Table [eb_store_order_status] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:37.402 - [任务 3][MysqlCrmeb] - Query table 'eb_store_order_status' counts: 0 
[INFO ] 2024-10-16 00:02:37.403 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_form_temp 
[INFO ] 2024-10-16 00:02:37.403 - [任务 3][MysqlCrmeb] - Table eb_system_form_temp is going to be initial synced 
[INFO ] 2024-10-16 00:02:37.898 - [任务 3][MysqlCrmeb] - Table [eb_system_form_temp] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:37.898 - [任务 3][TestMysql] - Table 'eb_user' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:37.904 - [任务 3][MysqlCrmeb] - Query table 'eb_system_form_temp' counts: 60 
[INFO ] 2024-10-16 00:02:37.904 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_reply 
[INFO ] 2024-10-16 00:02:37.915 - [任务 3][MysqlCrmeb] - Table eb_store_experience_reply is going to be initial synced 
[INFO ] 2024-10-16 00:02:37.915 - [任务 3][TestMysql] - Table 'eb_system_form_temp' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:38.119 - [任务 3][MysqlCrmeb] - Table [eb_store_experience_reply] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:38.192 - [任务 3][MysqlCrmeb] - Query table 'eb_store_experience_reply' counts: 2 
[INFO ] 2024-10-16 00:02:38.193 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_bargain_user 
[INFO ] 2024-10-16 00:02:38.193 - [任务 3][MysqlCrmeb] - Table eb_store_bargain_user is going to be initial synced 
[INFO ] 2024-10-16 00:02:38.396 - [任务 3][MysqlCrmeb] - Table [eb_store_bargain_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:38.440 - [任务 3][TestMysql] - Table 'eb_system_form_temp' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-10-16 00:02:38.440 - [任务 3][TestMysql] - Table 'eb_store_experience_reply' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:38.489 - [任务 3][MysqlCrmeb] - Query table 'eb_store_bargain_user' counts: 0 
[INFO ] 2024-10-16 00:02:38.489 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_store 
[INFO ] 2024-10-16 00:02:38.550 - [任务 3][MysqlCrmeb] - Table eb_system_store is going to be initial synced 
[INFO ] 2024-10-16 00:02:38.550 - [任务 3][MysqlCrmeb] - Table [eb_system_store] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:38.554 - [任务 3][MysqlCrmeb] - Query table 'eb_system_store' counts: 0 
[INFO ] 2024-10-16 00:02:38.554 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_shipping_templates_region 
[INFO ] 2024-10-16 00:02:38.619 - [任务 3][MysqlCrmeb] - Table eb_shipping_templates_region is going to be initial synced 
[INFO ] 2024-10-16 00:02:38.619 - [任务 3][TestMysql] - Table 'eb_shipping_templates_region' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:38.625 - [任务 3][MysqlCrmeb] - Query table 'eb_shipping_templates_region' counts: 369 
[INFO ] 2024-10-16 00:02:38.625 - [任务 3][TestMysql] - Table 'eb_shipping_templates_region' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-10-16 00:02:38.635 - [任务 3][TestMysql] - Table 'eb_shipping_templates_region' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-10-16 00:02:38.635 - [任务 3][MysqlCrmeb] - Table [eb_shipping_templates_region] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:38.635 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_recharge 
[INFO ] 2024-10-16 00:02:38.635 - [任务 3][MysqlCrmeb] - Table eb_user_recharge is going to be initial synced 
[INFO ] 2024-10-16 00:02:38.700 - [任务 3][MysqlCrmeb] - Table [eb_user_recharge] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:38.700 - [任务 3][MysqlCrmeb] - Query table 'eb_user_recharge' counts: 0 
[INFO ] 2024-10-16 00:02:38.701 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_coupon 
[INFO ] 2024-10-16 00:02:38.701 - [任务 3][MysqlCrmeb] - Table eb_store_product_coupon is going to be initial synced 
[INFO ] 2024-10-16 00:02:38.779 - [任务 3][MysqlCrmeb] - Table [eb_store_product_coupon] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:38.779 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_coupon' counts: 0 
[INFO ] 2024-10-16 00:02:38.779 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_wechat_pay_info 
[INFO ] 2024-10-16 00:02:38.779 - [任务 3][MysqlCrmeb] - Table eb_wechat_pay_info is going to be initial synced 
[INFO ] 2024-10-16 00:02:38.852 - [任务 3][MysqlCrmeb] - Table [eb_wechat_pay_info] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:38.852 - [任务 3][MysqlCrmeb] - Query table 'eb_wechat_pay_info' counts: 0 
[INFO ] 2024-10-16 00:02:38.852 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_combination 
[INFO ] 2024-10-16 00:02:38.852 - [任务 3][MysqlCrmeb] - Table eb_store_combination is going to be initial synced 
[INFO ] 2024-10-16 00:02:38.920 - [任务 3][MysqlCrmeb] - Table [eb_store_combination] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:38.920 - [任务 3][MysqlCrmeb] - Query table 'eb_store_combination' counts: 4 
[INFO ] 2024-10-16 00:02:38.921 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product 
[INFO ] 2024-10-16 00:02:38.921 - [任务 3][MysqlCrmeb] - Table eb_store_product is going to be initial synced 
[INFO ] 2024-10-16 00:02:38.989 - [任务 3][MysqlCrmeb] - Table [eb_store_product] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:38.989 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product' counts: 8 
[INFO ] 2024-10-16 00:02:38.990 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_article 
[INFO ] 2024-10-16 00:02:38.990 - [任务 3][MysqlCrmeb] - Table eb_article is going to be initial synced 
[INFO ] 2024-10-16 00:02:39.058 - [任务 3][MysqlCrmeb] - Table [eb_article] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:39.058 - [任务 3][MysqlCrmeb] - Query table 'eb_article' counts: 0 
[INFO ] 2024-10-16 00:02:39.058 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_bill 
[INFO ] 2024-10-16 00:02:39.058 - [任务 3][MysqlCrmeb] - Table eb_user_bill is going to be initial synced 
[INFO ] 2024-10-16 00:02:39.129 - [任务 3][MysqlCrmeb] - Table [eb_user_bill] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:39.129 - [任务 3][MysqlCrmeb] - Query table 'eb_user_bill' counts: 0 
[INFO ] 2024-10-16 00:02:39.130 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_relation 
[INFO ] 2024-10-16 00:02:39.130 - [任务 3][MysqlCrmeb] - Table eb_store_experience_relation is going to be initial synced 
[INFO ] 2024-10-16 00:02:39.193 - [任务 3][TestMysql] - Table 'eb_shipping_templates_region' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-10-16 00:02:39.193 - [任务 3][MysqlCrmeb] - Table [eb_store_experience_relation] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:39.201 - [任务 3][MysqlCrmeb] - Query table 'eb_store_experience_relation' counts: 2 
[INFO ] 2024-10-16 00:02:39.203 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_wechat_exceptions 
[INFO ] 2024-10-16 00:02:39.203 - [任务 3][MysqlCrmeb] - Table eb_wechat_exceptions is going to be initial synced 
[INFO ] 2024-10-16 00:02:39.273 - [任务 3][MysqlCrmeb] - Table [eb_wechat_exceptions] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:39.273 - [任务 3][MysqlCrmeb] - Query table 'eb_wechat_exceptions' counts: 31 
[INFO ] 2024-10-16 00:02:39.274 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_template_message 
[INFO ] 2024-10-16 00:02:39.340 - [任务 3][MysqlCrmeb] - Table eb_template_message is going to be initial synced 
[INFO ] 2024-10-16 00:02:39.341 - [任务 3][MysqlCrmeb] - Table [eb_template_message] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:39.349 - [任务 3][MysqlCrmeb] - Query table 'eb_template_message' counts: 15 
[INFO ] 2024-10-16 00:02:39.349 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_integral_record 
[INFO ] 2024-10-16 00:02:39.412 - [任务 3][MysqlCrmeb] - Table eb_user_integral_record is going to be initial synced 
[INFO ] 2024-10-16 00:02:39.413 - [任务 3][MysqlCrmeb] - Table [eb_user_integral_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:39.419 - [任务 3][MysqlCrmeb] - Query table 'eb_user_integral_record' counts: 1 
[INFO ] 2024-10-16 00:02:39.420 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_coupon 
[INFO ] 2024-10-16 00:02:39.420 - [任务 3][MysqlCrmeb] - Table eb_store_coupon is going to be initial synced 
[INFO ] 2024-10-16 00:02:39.492 - [任务 3][MysqlCrmeb] - Table [eb_store_coupon] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:39.492 - [任务 3][MysqlCrmeb] - Query table 'eb_store_coupon' counts: 2 
[INFO ] 2024-10-16 00:02:39.493 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_visit_record 
[INFO ] 2024-10-16 00:02:39.493 - [任务 3][MysqlCrmeb] - Table eb_user_visit_record is going to be initial synced 
[INFO ] 2024-10-16 00:02:39.564 - [任务 3][TestMysql] - Table 'eb_shipping_templates_region' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-10-16 00:02:39.564 - [任务 3][MysqlCrmeb] - Query table 'eb_user_visit_record' counts: 1238 
[INFO ] 2024-10-16 00:02:39.575 - [任务 3][TestMysql] - Table 'eb_store_combination' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:39.575 - [任务 3][TestMysql] - Table 'eb_store_product' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:39.582 - [任务 3][TestMysql] - Table 'eb_store_experience_relation' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:39.586 - [任务 3][TestMysql] - Table 'eb_wechat_exceptions' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:39.586 - [任务 3][TestMysql] - Table 'eb_template_message' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:39.590 - [任务 3][TestMysql] - Table 'eb_user_integral_record' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:39.590 - [任务 3][TestMysql] - Table 'eb_store_coupon' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:39.594 - [任务 3][TestMysql] - Table 'eb_user_visit_record' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:39.594 - [任务 3][TestMysql] - Table 'eb_user_visit_record' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-10-16 00:02:39.602 - [任务 3][TestMysql] - Table 'eb_user_visit_record' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-10-16 00:02:39.602 - [任务 3][TestMysql] - Table 'eb_user_visit_record' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-10-16 00:02:39.610 - [任务 3][TestMysql] - Table 'eb_user_visit_record' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-10-16 00:02:39.610 - [任务 3][TestMysql] - Table 'eb_user_visit_record' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-10-16 00:02:39.616 - [任务 3][TestMysql] - Table 'eb_user_visit_record' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-10-16 00:02:39.616 - [任务 3][TestMysql] - Table 'eb_user_visit_record' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-10-16 00:02:39.623 - [任务 3][TestMysql] - Table 'eb_user_visit_record' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-10-16 00:02:39.623 - [任务 3][TestMysql] - Table 'eb_user_visit_record' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-10-16 00:02:39.623 - [任务 3][MysqlCrmeb] - Table [eb_user_visit_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:39.624 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_role_menu 
[INFO ] 2024-10-16 00:02:39.624 - [任务 3][MysqlCrmeb] - Table eb_system_role_menu is going to be initial synced 
[INFO ] 2024-10-16 00:02:39.626 - [任务 3][TestMysql] - Table 'eb_user_visit_record' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-10-16 00:02:39.626 - [任务 3][TestMysql] - Table 'eb_user_visit_record' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-10-16 00:02:39.683 - [任务 3][TestMysql] - Table 'eb_system_role_menu' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:39.683 - [任务 3][TestMysql] - Table 'eb_system_role_menu' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-10-16 00:02:39.687 - [任务 3][TestMysql] - Table 'eb_system_role_menu' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-10-16 00:02:39.687 - [任务 3][TestMysql] - Table 'eb_system_role_menu' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-10-16 00:02:39.693 - [任务 3][TestMysql] - Table 'eb_system_role_menu' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-10-16 00:02:39.693 - [任务 3][MysqlCrmeb] - Query table 'eb_system_role_menu' counts: 595 
[INFO ] 2024-10-16 00:02:39.694 - [任务 3][MysqlCrmeb] - Table [eb_system_role_menu] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:39.694 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_log 
[INFO ] 2024-10-16 00:02:39.695 - [任务 3][MysqlCrmeb] - Table eb_store_product_log is going to be initial synced 
[INFO ] 2024-10-16 00:02:39.763 - [任务 3][MysqlCrmeb] - Table [eb_store_product_log] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:39.763 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_log' counts: 0 
[INFO ] 2024-10-16 00:02:39.764 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_series 
[INFO ] 2024-10-16 00:02:39.764 - [任务 3][MysqlCrmeb] - Table eb_store_product_series is going to be initial synced 
[INFO ] 2024-10-16 00:02:39.832 - [任务 3][MysqlCrmeb] - Table [eb_store_product_series] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:39.832 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_series' counts: 5 
[INFO ] 2024-10-16 00:02:39.832 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_attr 
[INFO ] 2024-10-16 00:02:39.832 - [任务 3][MysqlCrmeb] - Table eb_store_product_attr is going to be initial synced 
[INFO ] 2024-10-16 00:02:39.894 - [任务 3][TestMysql] - Table 'eb_system_role_menu' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-10-16 00:02:39.894 - [任务 3][MysqlCrmeb] - Table [eb_store_product_attr] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:39.903 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_attr' counts: 24 
[INFO ] 2024-10-16 00:02:39.903 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_bargain 
[INFO ] 2024-10-16 00:02:39.963 - [任务 3][MysqlCrmeb] - Table eb_store_bargain is going to be initial synced 
[INFO ] 2024-10-16 00:02:39.963 - [任务 3][MysqlCrmeb] - Table [eb_store_bargain] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:39.972 - [任务 3][MysqlCrmeb] - Query table 'eb_store_bargain' counts: 2 
[INFO ] 2024-10-16 00:02:39.972 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_audio 
[INFO ] 2024-10-16 00:02:40.037 - [任务 3][MysqlCrmeb] - Table eb_store_experience_audio is going to be initial synced 
[INFO ] 2024-10-16 00:02:40.037 - [任务 3][MysqlCrmeb] - Table [eb_store_experience_audio] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:40.047 - [任务 3][MysqlCrmeb] - Query table 'eb_store_experience_audio' counts: 5 
[INFO ] 2024-10-16 00:02:40.048 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_city 
[INFO ] 2024-10-16 00:02:40.109 - [任务 3][MysqlCrmeb] - Table eb_system_city is going to be initial synced 
[INFO ] 2024-10-16 00:02:40.109 - [任务 3][TestMysql] - Table 'eb_system_role_menu' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-10-16 00:02:40.124 - [任务 3][MysqlCrmeb] - Query table 'eb_system_city' counts: 3938 
[INFO ] 2024-10-16 00:02:40.124 - [任务 3][TestMysql] - Table 'eb_store_product_series' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:40.130 - [任务 3][TestMysql] - Table 'eb_store_product_attr' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:40.130 - [任务 3][TestMysql] - Table 'eb_store_bargain' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:40.134 - [任务 3][TestMysql] - Table 'eb_store_experience_audio' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:40.134 - [任务 3][TestMysql] - Table 'eb_system_city' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:40.147 - [任务 3][TestMysql] - Table 'eb_system_city' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-10-16 00:02:40.147 - [任务 3][TestMysql] - Table 'eb_system_city' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-10-16 00:02:40.162 - [任务 3][TestMysql] - Table 'eb_system_city' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-10-16 00:02:40.162 - [任务 3][TestMysql] - Table 'eb_system_city' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-10-16 00:02:40.178 - [任务 3][TestMysql] - Table 'eb_system_city' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-10-16 00:02:40.178 - [任务 3][TestMysql] - Table 'eb_system_city' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-10-16 00:02:40.373 - [任务 3][TestMysql] - Table 'eb_system_city' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-10-16 00:02:40.373 - [任务 3][TestMysql] - Table 'eb_system_city' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-10-16 00:02:40.387 - [任务 3][TestMysql] - Table 'eb_system_city' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-10-16 00:02:40.592 - [任务 3][TestMysql] - Table 'eb_system_city' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-10-16 00:02:40.633 - [任务 3][TestMysql] - Table 'eb_system_city' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-10-16 00:02:43.613 - [任务 3][MysqlCrmeb] - Table [eb_system_city] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:43.613 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_sms_record 
[INFO ] 2024-10-16 00:02:43.680 - [任务 3][MysqlCrmeb] - Table eb_sms_record is going to be initial synced 
[INFO ] 2024-10-16 00:02:43.680 - [任务 3][MysqlCrmeb] - Table [eb_sms_record] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:43.710 - [任务 3][MysqlCrmeb] - Query table 'eb_sms_record' counts: 0 
[INFO ] 2024-10-16 00:02:43.710 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_extract 
[INFO ] 2024-10-16 00:02:43.779 - [任务 3][MysqlCrmeb] - Table eb_user_extract is going to be initial synced 
[INFO ] 2024-10-16 00:02:43.779 - [任务 3][MysqlCrmeb] - Table [eb_user_extract] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:44.001 - [任务 3][MysqlCrmeb] - Query table 'eb_user_extract' counts: 0 
[INFO ] 2024-10-16 00:02:44.001 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_group 
[INFO ] 2024-10-16 00:02:44.073 - [任务 3][MysqlCrmeb] - Table eb_user_group is going to be initial synced 
[INFO ] 2024-10-16 00:02:44.073 - [任务 3][MysqlCrmeb] - Table [eb_user_group] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:44.292 - [任务 3][MysqlCrmeb] - Query table 'eb_user_group' counts: 3 
[INFO ] 2024-10-16 00:02:44.292 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_rule 
[INFO ] 2024-10-16 00:02:44.357 - [任务 3][MysqlCrmeb] - Table eb_store_product_rule is going to be initial synced 
[INFO ] 2024-10-16 00:02:44.357 - [任务 3][MysqlCrmeb] - Table [eb_store_product_rule] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:44.368 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_rule' counts: 2 
[INFO ] 2024-10-16 00:02:44.369 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_cart 
[INFO ] 2024-10-16 00:02:44.432 - [任务 3][MysqlCrmeb] - Table eb_store_cart is going to be initial synced 
[INFO ] 2024-10-16 00:02:44.432 - [任务 3][MysqlCrmeb] - Table [eb_store_cart] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:44.442 - [任务 3][MysqlCrmeb] - Query table 'eb_store_cart' counts: 3 
[INFO ] 2024-10-16 00:02:44.442 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_admin 
[INFO ] 2024-10-16 00:02:44.505 - [任务 3][MysqlCrmeb] - Table eb_system_admin is going to be initial synced 
[INFO ] 2024-10-16 00:02:44.505 - [任务 3][MysqlCrmeb] - Table [eb_system_admin] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:44.526 - [任务 3][MysqlCrmeb] - Query table 'eb_system_admin' counts: 2 
[INFO ] 2024-10-16 00:02:44.526 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_menu 
[INFO ] 2024-10-16 00:02:44.526 - [任务 3][MysqlCrmeb] - Table eb_system_menu is going to be initial synced 
[INFO ] 2024-10-16 00:02:44.605 - [任务 3][MysqlCrmeb] - Query table 'eb_system_menu' counts: 369 
[INFO ] 2024-10-16 00:02:44.605 - [任务 3][TestMysql] - Table 'eb_user_group' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:44.611 - [任务 3][TestMysql] - Table 'eb_store_product_rule' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:44.611 - [任务 3][TestMysql] - Table 'eb_store_cart' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:44.617 - [任务 3][TestMysql] - Table 'eb_system_admin' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:44.617 - [任务 3][TestMysql] - Table 'eb_system_menu' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:44.634 - [任务 3][TestMysql] - Table 'eb_system_menu' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-10-16 00:02:44.634 - [任务 3][TestMysql] - Table 'eb_system_menu' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-10-16 00:02:44.646 - [任务 3][MysqlCrmeb] - Table [eb_system_menu] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:44.646 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_user_level 
[INFO ] 2024-10-16 00:02:44.704 - [任务 3][MysqlCrmeb] - Table eb_system_user_level is going to be initial synced 
[INFO ] 2024-10-16 00:02:44.704 - [任务 3][MysqlCrmeb] - Table [eb_system_user_level] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:44.714 - [任务 3][MysqlCrmeb] - Query table 'eb_system_user_level' counts: 5 
[INFO ] 2024-10-16 00:02:44.714 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_category 
[INFO ] 2024-10-16 00:02:44.779 - [任务 3][MysqlCrmeb] - Table eb_category is going to be initial synced 
[INFO ] 2024-10-16 00:02:44.779 - [任务 3][TestMysql] - Table 'eb_system_menu' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-10-16 00:02:44.783 - [任务 3][TestMysql] - Table 'eb_system_menu' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-10-16 00:02:44.783 - [任务 3][TestMysql] - Table 'eb_system_user_level' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:44.790 - [任务 3][MysqlCrmeb] - Query table 'eb_category' counts: 295 
[INFO ] 2024-10-16 00:02:44.790 - [任务 3][TestMysql] - Table 'eb_category' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:44.798 - [任务 3][TestMysql] - Table 'eb_category' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-10-16 00:02:44.798 - [任务 3][MysqlCrmeb] - Table [eb_category] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:44.798 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_sms_template 
[INFO ] 2024-10-16 00:02:44.798 - [任务 3][MysqlCrmeb] - Table eb_sms_template is going to be initial synced 
[INFO ] 2024-10-16 00:02:44.882 - [任务 3][MysqlCrmeb] - Table [eb_sms_template] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:44.883 - [任务 3][MysqlCrmeb] - Query table 'eb_sms_template' counts: 7 
[INFO ] 2024-10-16 00:02:44.884 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_tag 
[INFO ] 2024-10-16 00:02:44.884 - [任务 3][MysqlCrmeb] - Table eb_user_tag is going to be initial synced 
[INFO ] 2024-10-16 00:02:44.956 - [任务 3][MysqlCrmeb] - Table [eb_user_tag] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:44.956 - [任务 3][MysqlCrmeb] - Query table 'eb_user_tag' counts: 5 
[INFO ] 2024-10-16 00:02:44.956 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_order_info 
[INFO ] 2024-10-16 00:02:44.957 - [任务 3][MysqlCrmeb] - Table eb_store_order_info is going to be initial synced 
[INFO ] 2024-10-16 00:02:45.026 - [任务 3][MysqlCrmeb] - Table [eb_store_order_info] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:45.026 - [任务 3][MysqlCrmeb] - Query table 'eb_store_order_info' counts: 0 
[INFO ] 2024-10-16 00:02:45.026 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_coupon_user 
[INFO ] 2024-10-16 00:02:45.090 - [任务 3][MysqlCrmeb] - Table eb_store_coupon_user is going to be initial synced 
[INFO ] 2024-10-16 00:02:45.091 - [任务 3][MysqlCrmeb] - Table [eb_store_coupon_user] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:45.099 - [任务 3][MysqlCrmeb] - Query table 'eb_store_coupon_user' counts: 0 
[INFO ] 2024-10-16 00:02:45.099 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_role 
[INFO ] 2024-10-16 00:02:45.167 - [任务 3][MysqlCrmeb] - Table eb_system_role is going to be initial synced 
[INFO ] 2024-10-16 00:02:45.168 - [任务 3][MysqlCrmeb] - Table [eb_system_role] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:45.172 - [任务 3][MysqlCrmeb] - Query table 'eb_system_role' counts: 2 
[INFO ] 2024-10-16 00:02:45.173 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_experience_audio_history 
[INFO ] 2024-10-16 00:02:45.174 - [任务 3][MysqlCrmeb] - Table eb_store_experience_audio_history is going to be initial synced 
[INFO ] 2024-10-16 00:02:45.174 - [任务 3][TestMysql] - Table 'eb_category' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-10-16 00:02:45.271 - [任务 3][MysqlCrmeb] - Table [eb_store_experience_audio_history] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:45.272 - [任务 3][MysqlCrmeb] - Query table 'eb_store_experience_audio_history' counts: 4 
[INFO ] 2024-10-16 00:02:45.273 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_pink 
[INFO ] 2024-10-16 00:02:45.274 - [任务 3][MysqlCrmeb] - Table eb_store_pink is going to be initial synced 
[INFO ] 2024-10-16 00:02:45.344 - [任务 3][MysqlCrmeb] - Table [eb_store_pink] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:45.344 - [任务 3][MysqlCrmeb] - Query table 'eb_store_pink' counts: 0 
[INFO ] 2024-10-16 00:02:45.398 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_express 
[INFO ] 2024-10-16 00:02:45.398 - [任务 3][MysqlCrmeb] - Table eb_express is going to be initial synced 
[INFO ] 2024-10-16 00:02:45.398 - [任务 3][TestMysql] - Table 'eb_category' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-10-16 00:02:45.407 - [任务 3][TestMysql] - Table 'eb_sms_template' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:45.408 - [任务 3][TestMysql] - Table 'eb_user_tag' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:45.414 - [任务 3][TestMysql] - Table 'eb_system_role' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:45.414 - [任务 3][TestMysql] - Table 'eb_store_experience_audio_history' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:45.422 - [任务 3][MysqlCrmeb] - Query table 'eb_express' counts: 1067 
[INFO ] 2024-10-16 00:02:45.422 - [任务 3][TestMysql] - Table 'eb_express' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:45.434 - [任务 3][TestMysql] - Table 'eb_express' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-10-16 00:02:45.434 - [任务 3][TestMysql] - Table 'eb_express' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-10-16 00:02:45.445 - [任务 3][TestMysql] - Table 'eb_express' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-10-16 00:02:45.445 - [任务 3][MysqlCrmeb] - Table [eb_express] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:45.445 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_store_product_relation 
[INFO ] 2024-10-16 00:02:45.446 - [任务 3][MysqlCrmeb] - Table eb_store_product_relation is going to be initial synced 
[INFO ] 2024-10-16 00:02:45.457 - [任务 3][TestMysql] - Table 'eb_express' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-10-16 00:02:45.457 - [任务 3][TestMysql] - Table 'eb_express' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-10-16 00:02:45.472 - [任务 3][TestMysql] - Table 'eb_express' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-10-16 00:02:45.473 - [任务 3][TestMysql] - Table 'eb_express' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-10-16 00:02:45.481 - [任务 3][TestMysql] - Table 'eb_express' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-10-16 00:02:45.481 - [任务 3][TestMysql] - Table 'eb_express' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-10-16 00:02:45.521 - [任务 3][MysqlCrmeb] - Table [eb_store_product_relation] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:45.521 - [任务 3][MysqlCrmeb] - Query table 'eb_store_product_relation' counts: 0 
[INFO ] 2024-10-16 00:02:45.521 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_system_group_data 
[INFO ] 2024-10-16 00:02:45.522 - [任务 3][MysqlCrmeb] - Table eb_system_group_data is going to be initial synced 
[INFO ] 2024-10-16 00:02:45.598 - [任务 3][TestMysql] - Table 'eb_express' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-10-16 00:02:45.598 - [任务 3][MysqlCrmeb] - Table [eb_system_group_data] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:45.810 - [任务 3][MysqlCrmeb] - Query table 'eb_system_group_data' counts: 68 
[INFO ] 2024-10-16 00:02:45.812 - [任务 3][MysqlCrmeb] - Starting batch read, table name: eb_user_address 
[INFO ] 2024-10-16 00:02:45.813 - [任务 3][MysqlCrmeb] - Table eb_user_address is going to be initial synced 
[INFO ] 2024-10-16 00:02:45.884 - [任务 3][MysqlCrmeb] - Table [eb_user_address] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-16 00:02:45.884 - [任务 3][MysqlCrmeb] - Query table 'eb_user_address' counts: 0 
[INFO ] 2024-10-16 00:02:46.088 - [任务 3][MysqlCrmeb] - Initial sync completed 
[INFO ] 2024-10-16 00:02:46.116 - [任务 3][TestMysql] - Table 'eb_express' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-10-16 00:02:46.116 - [任务 3][TestMysql] - Table 'eb_system_group_data' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-16 00:02:46.605 - [任务 3][MysqlCrmeb] - Node MysqlCrmeb[f3947a0b-c177-4aa9-a4bf-f19033327883] running status set to false 
[INFO ] 2024-10-16 00:02:46.606 - [任务 3][TestMysql] - Node TestMysql[2bee360c-c91c-49bf-b924-6a206b23f1cd] running status set to false 
[INFO ] 2024-10-16 00:02:46.623 - [任务 3][TestMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-2bee360c-c91c-49bf-b924-6a206b23f1cd 
[INFO ] 2024-10-16 00:02:46.623 - [任务 3][TestMysql] - PDK connector node released: HazelcastTargetPdkDataNode-2bee360c-c91c-49bf-b924-6a206b23f1cd 
[INFO ] 2024-10-16 00:02:46.623 - [任务 3][TestMysql] - Node TestMysql[2bee360c-c91c-49bf-b924-6a206b23f1cd] schema data cleaned 
[INFO ] 2024-10-16 00:02:46.628 - [任务 3][TestMysql] - Node TestMysql[2bee360c-c91c-49bf-b924-6a206b23f1cd] monitor closed 
[INFO ] 2024-10-16 00:02:46.628 - [任务 3][TestMysql] - Node TestMysql[2bee360c-c91c-49bf-b924-6a206b23f1cd] close complete, cost 22 ms 
[INFO ] 2024-10-16 00:02:46.636 - [任务 3][MysqlCrmeb] - PDK connector node stopped: HazelcastSourcePdkDataNode-f3947a0b-c177-4aa9-a4bf-f19033327883 
[INFO ] 2024-10-16 00:02:46.636 - [任务 3][MysqlCrmeb] - PDK connector node released: HazelcastSourcePdkDataNode-f3947a0b-c177-4aa9-a4bf-f19033327883 
[INFO ] 2024-10-16 00:02:46.636 - [任务 3][MysqlCrmeb] - Node MysqlCrmeb[f3947a0b-c177-4aa9-a4bf-f19033327883] schema data cleaned 
[INFO ] 2024-10-16 00:02:46.636 - [任务 3][MysqlCrmeb] - Node MysqlCrmeb[f3947a0b-c177-4aa9-a4bf-f19033327883] monitor closed 
[INFO ] 2024-10-16 00:02:46.844 - [任务 3][MysqlCrmeb] - Node MysqlCrmeb[f3947a0b-c177-4aa9-a4bf-f19033327883] close complete, cost 44 ms 
[INFO ] 2024-10-16 00:02:51.322 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-16 00:02:51.324 - [任务 3] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1e6c9cab 
[INFO ] 2024-10-16 00:02:51.324 - [任务 3] - Stop task milestones: 670e91cfe5f4980f3e528de4(任务 3)  
[INFO ] 2024-10-16 00:02:51.455 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-10-16 00:02:51.455 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-10-16 00:02:51.481 - [任务 3] - Remove memory task client succeed, task: 任务 3[670e91cfe5f4980f3e528de4] 
[INFO ] 2024-10-16 00:02:51.485 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[670e91cfe5f4980f3e528de4] 
