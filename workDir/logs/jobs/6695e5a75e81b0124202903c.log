[INFO ] 2024-07-21 22:06:05.199 - [任务 2][testCustomer] - Node testCustomer[081567ec-1785-470a-ac9b-74e3f0358207] running status set to false 
[INFO ] 2024-07-21 22:06:05.756 - [任务 2] - Task initialization... 
[INFO ] 2024-07-21 22:06:05.785 - [任务 2] - Start task milestones: 6695e5a75e81b0124202903c(任务 2) 
[INFO ] 2024-07-21 22:06:06.793 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-21 22:06:06.869 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-21 22:06:07.674 - [任务 2][CUSTOMER] - Node CUSTOMER[5e66cce3-0885-4972-9f21-96a620f88b96] start preload schema,table counts: 1 
[INFO ] 2024-07-21 22:06:07.676 - [任务 2][CUSTOMER] - Node CUSTOMER[5e66cce3-0885-4972-9f21-96a620f88b96] preload schema finished, cost 0 ms 
[INFO ] 2024-07-21 22:06:07.717 - [任务 2][testCustomer] - Node testCustomer[081567ec-1785-470a-ac9b-74e3f0358207] start preload schema,table counts: 1 
[INFO ] 2024-07-21 22:06:07.720 - [任务 2][testCustomer] - Node testCustomer[081567ec-1785-470a-ac9b-74e3f0358207] preload schema finished, cost 0 ms 
[INFO ] 2024-07-21 22:06:09.759 - [任务 2][testCustomer] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-21 22:06:09.912 - [任务 2][CUSTOMER] - Source node "CUSTOMER" read batch size: 100 
[INFO ] 2024-07-21 22:06:09.912 - [任务 2][CUSTOMER] - Source node "CUSTOMER" event queue capacity: 200 
[INFO ] 2024-07-21 22:06:09.916 - [任务 2][CUSTOMER] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-21 22:06:09.945 - [任务 2][CUSTOMER] - batch offset found: {"CUSTOMER":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"sequenceMap":{"CUSTOMER":1,"_tapdata_heartbeat_table":79987},"streamOffset":{"name":"52923cd5-0c72-4472-af4c-00cbdaba2e6f","offset":{"{\"server\":\"52923cd5-0c72-4472-af4c-00cbdaba2e6f\"}":"{\"ts_sec\":1721386924,\"file\":\"binlog.000033\",\"pos\":13902081,\"row\":1,\"server_id\":1,\"event\":2}"}}} 
[INFO ] 2024-07-21 22:06:10.058 - [任务 2][CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-07-21 22:06:10.059 - [任务 2][CUSTOMER] - Initial sync completed 
[INFO ] 2024-07-21 22:06:10.155 - [任务 2][CUSTOMER] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-21 22:06:10.159 - [任务 2][CUSTOMER] - [Share CDC Task HZ Reader] - Step 1 - Check connection Mysql enable share cdc: true 
[INFO ] 2024-07-21 22:06:10.164 - [任务 2][CUSTOMER] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 2 enable share cdc: true 
[INFO ] 2024-07-21 22:06:10.243 - [任务 2][CUSTOMER] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自Mysql的共享挖掘任务 
[INFO ] 2024-07-21 22:06:10.245 - [任务 2][CUSTOMER] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav392?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-21 22:06:10.387 - [任务 2][CUSTOMER] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695e5c566ab5ede8aa012f4, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6695b8aa6d76494ed53f3882_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_45392933, shareCdcTaskId=6695e5c55e81b0124202907b, connectionId=6695b8aa6d76494ed53f3882) 
[INFO ] 2024-07-21 22:06:10.387 - [任务 2][CUSTOMER] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务_CUSTOMER_任务 2', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_45392933', head seq: 0, tail seq: 0 
[INFO ] 2024-07-21 22:06:10.424 - [任务 2][CUSTOMER] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695e5c566ab5ede8aa012f2, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6695b8aa6d76494ed53f3882__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_1469738849, shareCdcTaskId=6695e5c55e81b0124202907b, connectionId=6695b8aa6d76494ed53f3882) 
[INFO ] 2024-07-21 22:06:10.425 - [任务 2][CUSTOMER] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务__tapdata_heartbeat_table_任务 2', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_1469738849', head seq: 0, tail seq: 79988 
[INFO ] 2024-07-21 22:06:10.428 - [任务 2][CUSTOMER] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-21 22:06:10.429 - [任务 2][CUSTOMER] - Init share cdc reader completed 
[INFO ] 2024-07-21 22:06:10.431 - [任务 2][CUSTOMER] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-21 22:06:10.440 - [任务 2][CUSTOMER] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-21 22:06:10.442 - [任务 2][CUSTOMER] - [Share CDC Task HZ Reader] - Read table count: 2, partition size: 1, read thread number: 2 
[INFO ] 2024-07-21 22:06:10.461 - [任务 2][CUSTOMER] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695e5c566ab5ede8aa012f4, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6695b8aa6d76494ed53f3882_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_45392933, shareCdcTaskId=6695e5c55e81b0124202907b, connectionId=6695b8aa6d76494ed53f3882) 
[INFO ] 2024-07-21 22:06:10.461 - [任务 2][CUSTOMER] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务_CUSTOMER_任务 2', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_45392933', head seq: 0, tail seq: 0 
[INFO ] 2024-07-21 22:06:10.466 - [任务 2][CUSTOMER] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自Mysql的共享挖掘任务_CUSTOMER_任务 2, external storage name: ExternalStorage_SHARE_CDC_45392933 
[INFO ] 2024-07-21 22:06:10.467 - [任务 2][CUSTOMER] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CUSTOMER] 
[INFO ] 2024-07-21 22:06:10.471 - [任务 2][CUSTOMER] - [Share CDC Task HZ Reader] - Starting read 'CUSTOMER' log, sequence: 1 
[INFO ] 2024-07-21 22:06:10.472 - [任务 2][CUSTOMER] - [Share CDC Task HZ Reader] - Find by CUSTOMER filter: {sequence=1} 
[INFO ] 2024-07-21 22:06:10.494 - [任务 2][CUSTOMER] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695e5c566ab5ede8aa012f2, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6695b8aa6d76494ed53f3882__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_1469738849, shareCdcTaskId=6695e5c55e81b0124202907b, connectionId=6695b8aa6d76494ed53f3882) 
[INFO ] 2024-07-21 22:06:10.495 - [任务 2][CUSTOMER] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务__tapdata_heartbeat_table_任务 2', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_1469738849', head seq: 0, tail seq: 79988 
[INFO ] 2024-07-21 22:06:10.499 - [任务 2][CUSTOMER] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自Mysql的共享挖掘任务__tapdata_heartbeat_table_任务 2, external storage name: ExternalStorage_SHARE_CDC_1469738849 
[INFO ] 2024-07-21 22:06:10.499 - [任务 2][CUSTOMER] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [_tapdata_heartbeat_table] 
[INFO ] 2024-07-21 22:06:10.500 - [任务 2][CUSTOMER] - Connector Mysql incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-21 22:06:10.505 - [任务 2][CUSTOMER] - [Share CDC Task HZ Reader] - Starting read '_tapdata_heartbeat_table' log, sequence: 79987 
[INFO ] 2024-07-21 22:06:10.523 - [任务 2][CUSTOMER] - [Share CDC Task HZ Reader] - Find by _tapdata_heartbeat_table filter: {sequence=79987} 
[INFO ] 2024-07-21 22:06:10.524 - [任务 2][CUSTOMER] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=_tapdata_heartbeat_table, timestamp=1721386926000, date=Fri Jul 19 19:02:06 CST 2024, before=Document{{id=6695b8aa6d76494ed53f3882, ts=Fri Jul 19 19:02:03 CST 2024}}, after=Document{{id=6695b8aa6d76494ed53f3882, ts=Fri Jul 19 19:02:04 CST 2024}}, op=u, offsetString=gAEBrO0ABXNyADNpby50YXBkYXRhLmNvbm5lY3Rvci5teXNxbC5lbnRpdHkuTXlzcWxTdHJlYW1P
ZmZzZXRioy3XaM2NhgIAAkwABG5hbWV0ABJMamF2YS9sYW5nL1N0cmluZztMAAZvZmZzZXR0AA9M
amF2YS91dGlsL01hcDt4cHQAJDUyOTIzY2Q1LTBjNzItNDQ3Mi1hZjRjLTAwY2JkYWJhMmU2ZnNy
ABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhw
P0AAAAAAAAF3CAAAAAIAAAABdAAxeyJzZXJ2ZXIiOiI1MjkyM2NkNS0wYzcyLTQ0NzItYWY0Yy0w
MGNiZGFiYTJlNmYifXQAW3sidHNfc2VjIjoxNzIxMzg2OTI2LCJmaWxlIjoiYmlubG9nLjAwMDAz
MyIsInBvcyI6MTM5MDI4MzEsInJvdyI6MSwic2VydmVyX2lkIjoxLCJldmVudCI6Mn14
, type=DATA, connectionId=6695b8aa6d76494ed53f3882, isReplaceEvent=false, _ts=1721386927}} 
[INFO ] 2024-07-21 22:08:01.179 - [任务 2][CUSTOMER] - Node CUSTOMER[5e66cce3-0885-4972-9f21-96a620f88b96] running status set to false 
[INFO ] 2024-07-21 22:08:01.200 - [任务 2][CUSTOMER] - Incremental sync completed 
[INFO ] 2024-07-21 22:08:01.200 - [任务 2][CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode-5e66cce3-0885-4972-9f21-96a620f88b96 
[INFO ] 2024-07-21 22:08:01.200 - [任务 2][CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode-5e66cce3-0885-4972-9f21-96a620f88b96 
[INFO ] 2024-07-21 22:08:01.201 - [任务 2][CUSTOMER] - Node CUSTOMER[5e66cce3-0885-4972-9f21-96a620f88b96] schema data cleaned 
[INFO ] 2024-07-21 22:08:01.206 - [任务 2][CUSTOMER] - Node CUSTOMER[5e66cce3-0885-4972-9f21-96a620f88b96] monitor closed 
[INFO ] 2024-07-21 22:08:01.207 - [任务 2][CUSTOMER] - Node CUSTOMER[5e66cce3-0885-4972-9f21-96a620f88b96] close complete, cost 38 ms 
[INFO ] 2024-07-21 22:08:01.237 - [任务 2][testCustomer] - Node testCustomer[081567ec-1785-470a-ac9b-74e3f0358207] running status set to false 
[INFO ] 2024-07-21 22:08:01.238 - [任务 2][testCustomer] - PDK connector node stopped: HazelcastTargetPdkDataNode-081567ec-1785-470a-ac9b-74e3f0358207 
[INFO ] 2024-07-21 22:08:01.238 - [任务 2][testCustomer] - PDK connector node released: HazelcastTargetPdkDataNode-081567ec-1785-470a-ac9b-74e3f0358207 
[INFO ] 2024-07-21 22:08:01.238 - [任务 2][testCustomer] - Node testCustomer[081567ec-1785-470a-ac9b-74e3f0358207] schema data cleaned 
[INFO ] 2024-07-21 22:08:01.239 - [任务 2][testCustomer] - Node testCustomer[081567ec-1785-470a-ac9b-74e3f0358207] monitor closed 
[INFO ] 2024-07-21 22:08:01.239 - [任务 2][testCustomer] - Node testCustomer[081567ec-1785-470a-ac9b-74e3f0358207] close complete, cost 32 ms 
[INFO ] 2024-07-21 22:08:03.844 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-21 22:08:03.849 - [任务 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@38912967 
[INFO ] 2024-07-21 22:08:03.973 - [任务 2] - Stop task milestones: 6695e5a75e81b0124202903c(任务 2)  
[INFO ] 2024-07-21 22:08:03.989 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-07-21 22:08:03.990 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-07-21 22:08:04.025 - [任务 2] - Remove memory task client succeed, task: 任务 2[6695e5a75e81b0124202903c] 
[INFO ] 2024-07-21 22:08:04.025 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[6695e5a75e81b0124202903c] 
