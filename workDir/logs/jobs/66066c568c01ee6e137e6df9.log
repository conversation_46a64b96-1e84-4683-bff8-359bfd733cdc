[INFO ] 2024-03-29 15:23:06.302 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:23:06.306 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 15:23:06.306 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:23:06.308 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:06.309 - [orders_import_import_import_import_import_import(100)][80ecf806-b445-4360-bf40-3dd844849083] - Node 80ecf806-b445-4360-bf40-3dd844849083[80ecf806-b445-4360-bf40-3dd844849083] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:23:06.323 - [orders_import_import_import_import_import_import(100)][80ecf806-b445-4360-bf40-3dd844849083] - Node 80ecf806-b445-4360-bf40-3dd844849083[80ecf806-b445-4360-bf40-3dd844849083] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:06.612 - [orders_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:23:06.819 - [orders_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@789b655d error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@789b655d error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@789b655d error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:23:08.854 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] running status set to false 
[INFO ] 2024-03-29 15:23:08.856 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:23:08.876 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:23:08.885 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] schema data cleaned 
[INFO ] 2024-03-29 15:23:08.887 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] monitor closed 
[INFO ] 2024-03-29 15:23:09.091 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] close complete, cost 169 ms 
[INFO ] 2024-03-29 15:23:09.384 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] running status set to false 
[INFO ] 2024-03-29 15:23:09.388 - [orders_import_import_import_import_import_import(100)][80ecf806-b445-4360-bf40-3dd844849083] - Node 80ecf806-b445-4360-bf40-3dd844849083[80ecf806-b445-4360-bf40-3dd844849083] running status set to false 
[INFO ] 2024-03-29 15:23:09.388 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] schema data cleaned 
[INFO ] 2024-03-29 15:23:09.419 - [orders_import_import_import_import_import_import(100)][80ecf806-b445-4360-bf40-3dd844849083] - Node 80ecf806-b445-4360-bf40-3dd844849083[80ecf806-b445-4360-bf40-3dd844849083] schema data cleaned 
[INFO ] 2024-03-29 15:23:09.420 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] monitor closed 
[INFO ] 2024-03-29 15:23:09.427 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] close complete, cost 62 ms 
[INFO ] 2024-03-29 15:23:09.428 - [orders_import_import_import_import_import_import(100)][80ecf806-b445-4360-bf40-3dd844849083] - Node 80ecf806-b445-4360-bf40-3dd844849083[80ecf806-b445-4360-bf40-3dd844849083] monitor closed 
[INFO ] 2024-03-29 15:23:09.433 - [orders_import_import_import_import_import_import(100)][80ecf806-b445-4360-bf40-3dd844849083] - Node 80ecf806-b445-4360-bf40-3dd844849083[80ecf806-b445-4360-bf40-3dd844849083] close complete, cost 79 ms 
[INFO ] 2024-03-29 15:23:09.635 - [orders_import_import_import_import_import_import(100)] - load tapTable task 66066c568c01ee6e137e6df9-80ecf806-b445-4360-bf40-3dd844849083 complete, cost 3992ms 
[INFO ] 2024-03-29 15:23:24.854 - [orders_import_import_import_import_import_import(100)][b23660d0-a665-4fdf-9ec0-e5cc48acb4fc] - Node b23660d0-a665-4fdf-9ec0-e5cc48acb4fc[b23660d0-a665-4fdf-9ec0-e5cc48acb4fc] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:23:24.856 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:23:24.869 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:23:24.871 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:24.872 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:24.872 - [orders_import_import_import_import_import_import(100)][b23660d0-a665-4fdf-9ec0-e5cc48acb4fc] - Node b23660d0-a665-4fdf-9ec0-e5cc48acb4fc[b23660d0-a665-4fdf-9ec0-e5cc48acb4fc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:24.941 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:23:24.941 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:23:24.941 - [orders_import_import_import_import_import_import(100)][ee26f79a-8602-4fb4-9643-72bf86d5a6de] - Node ee26f79a-8602-4fb4-9643-72bf86d5a6de[ee26f79a-8602-4fb4-9643-72bf86d5a6de] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:23:24.941 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:24.941 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:24.943 - [orders_import_import_import_import_import_import(100)][ee26f79a-8602-4fb4-9643-72bf86d5a6de] - Node ee26f79a-8602-4fb4-9643-72bf86d5a6de[ee26f79a-8602-4fb4-9643-72bf86d5a6de] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:24.982 - [orders_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 15:23:24.984 - [orders_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 15:23:24.987 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:23:24.987 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:23:24.987 - [orders_import_import_import_import_import_import(100)][8d6c7770-753b-4b6b-923e-ee064d575e7e] - Node 8d6c7770-753b-4b6b-923e-ee064d575e7e[8d6c7770-753b-4b6b-923e-ee064d575e7e] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:23:24.987 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:24.987 - [orders_import_import_import_import_import_import(100)][8d6c7770-753b-4b6b-923e-ee064d575e7e] - Node 8d6c7770-753b-4b6b-923e-ee064d575e7e[8d6c7770-753b-4b6b-923e-ee064d575e7e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:24.987 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 15:23:25.005 - [orders_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7ee00254 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7ee00254 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7ee00254 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-29 15:23:25.009 - [orders_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@b6e0580 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@b6e0580 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@b6e0580 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:23:25.009 - [orders_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:23:25.187 - [orders_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@22a809d8 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@22a809d8 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@22a809d8 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:23:25.188 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] running status set to false 
[INFO ] 2024-03-29 15:23:25.227 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:23:25.227 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:23:25.228 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] schema data cleaned 
[INFO ] 2024-03-29 15:23:25.228 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] monitor closed 
[INFO ] 2024-03-29 15:23:25.230 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] close complete, cost 55 ms 
[INFO ] 2024-03-29 15:23:25.334 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] running status set to false 
[INFO ] 2024-03-29 15:23:25.334 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:23:25.336 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:23:25.336 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] schema data cleaned 
[INFO ] 2024-03-29 15:23:25.337 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] monitor closed 
[INFO ] 2024-03-29 15:23:25.337 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] close complete, cost 13 ms 
[INFO ] 2024-03-29 15:23:25.539 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] running status set to false 
[INFO ] 2024-03-29 15:23:25.565 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:23:25.566 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:23:25.566 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] schema data cleaned 
[INFO ] 2024-03-29 15:23:25.566 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] monitor closed 
[INFO ] 2024-03-29 15:23:25.569 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] close complete, cost 49 ms 
[INFO ] 2024-03-29 15:23:25.713 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:23:25.714 - [orders_import_import_import_import_import_import(100)][dfd3848c-c813-4032-8679-1ede33eb038e] - Node dfd3848c-c813-4032-8679-1ede33eb038e[dfd3848c-c813-4032-8679-1ede33eb038e] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:23:25.714 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:23:25.715 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:25.716 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:25.718 - [orders_import_import_import_import_import_import(100)][dfd3848c-c813-4032-8679-1ede33eb038e] - Node dfd3848c-c813-4032-8679-1ede33eb038e[dfd3848c-c813-4032-8679-1ede33eb038e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:25.831 - [orders_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:23:25.831 - [orders_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@36021eb2 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@36021eb2 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@36021eb2 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:23:25.974 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] running status set to false 
[INFO ] 2024-03-29 15:23:25.975 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:23:25.978 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:23:25.978 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] schema data cleaned 
[INFO ] 2024-03-29 15:23:25.981 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] monitor closed 
[INFO ] 2024-03-29 15:23:25.982 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] close complete, cost 33 ms 
[INFO ] 2024-03-29 15:23:27.547 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] running status set to false 
[INFO ] 2024-03-29 15:23:27.548 - [orders_import_import_import_import_import_import(100)][ee26f79a-8602-4fb4-9643-72bf86d5a6de] - Node ee26f79a-8602-4fb4-9643-72bf86d5a6de[ee26f79a-8602-4fb4-9643-72bf86d5a6de] running status set to false 
[INFO ] 2024-03-29 15:23:27.572 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] schema data cleaned 
[INFO ] 2024-03-29 15:23:27.579 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] running status set to false 
[INFO ] 2024-03-29 15:23:27.579 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] running status set to false 
[INFO ] 2024-03-29 15:23:27.582 - [orders_import_import_import_import_import_import(100)][b23660d0-a665-4fdf-9ec0-e5cc48acb4fc] - Node b23660d0-a665-4fdf-9ec0-e5cc48acb4fc[b23660d0-a665-4fdf-9ec0-e5cc48acb4fc] running status set to false 
[INFO ] 2024-03-29 15:23:27.582 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] monitor closed 
[INFO ] 2024-03-29 15:23:27.582 - [orders_import_import_import_import_import_import(100)][ee26f79a-8602-4fb4-9643-72bf86d5a6de] - Node ee26f79a-8602-4fb4-9643-72bf86d5a6de[ee26f79a-8602-4fb4-9643-72bf86d5a6de] schema data cleaned 
[INFO ] 2024-03-29 15:23:27.582 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] schema data cleaned 
[INFO ] 2024-03-29 15:23:27.582 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] schema data cleaned 
[INFO ] 2024-03-29 15:23:27.582 - [orders_import_import_import_import_import_import(100)][8d6c7770-753b-4b6b-923e-ee064d575e7e] - Node 8d6c7770-753b-4b6b-923e-ee064d575e7e[8d6c7770-753b-4b6b-923e-ee064d575e7e] running status set to false 
[INFO ] 2024-03-29 15:23:27.582 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] close complete, cost 34 ms 
[INFO ] 2024-03-29 15:23:27.582 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] monitor closed 
[INFO ] 2024-03-29 15:23:27.582 - [orders_import_import_import_import_import_import(100)][b23660d0-a665-4fdf-9ec0-e5cc48acb4fc] - Node b23660d0-a665-4fdf-9ec0-e5cc48acb4fc[b23660d0-a665-4fdf-9ec0-e5cc48acb4fc] schema data cleaned 
[INFO ] 2024-03-29 15:23:27.582 - [orders_import_import_import_import_import_import(100)][ee26f79a-8602-4fb4-9643-72bf86d5a6de] - Node ee26f79a-8602-4fb4-9643-72bf86d5a6de[ee26f79a-8602-4fb4-9643-72bf86d5a6de] monitor closed 
[INFO ] 2024-03-29 15:23:27.584 - [orders_import_import_import_import_import_import(100)][8d6c7770-753b-4b6b-923e-ee064d575e7e] - Node 8d6c7770-753b-4b6b-923e-ee064d575e7e[8d6c7770-753b-4b6b-923e-ee064d575e7e] schema data cleaned 
[INFO ] 2024-03-29 15:23:27.587 - [orders_import_import_import_import_import_import(100)][b23660d0-a665-4fdf-9ec0-e5cc48acb4fc] - Node b23660d0-a665-4fdf-9ec0-e5cc48acb4fc[b23660d0-a665-4fdf-9ec0-e5cc48acb4fc] monitor closed 
[INFO ] 2024-03-29 15:23:27.590 - [orders_import_import_import_import_import_import(100)][8d6c7770-753b-4b6b-923e-ee064d575e7e] - Node 8d6c7770-753b-4b6b-923e-ee064d575e7e[8d6c7770-753b-4b6b-923e-ee064d575e7e] monitor closed 
[INFO ] 2024-03-29 15:23:27.590 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] close complete, cost 45 ms 
[INFO ] 2024-03-29 15:23:27.590 - [orders_import_import_import_import_import_import(100)][ee26f79a-8602-4fb4-9643-72bf86d5a6de] - Node ee26f79a-8602-4fb4-9643-72bf86d5a6de[ee26f79a-8602-4fb4-9643-72bf86d5a6de] close complete, cost 42 ms 
[INFO ] 2024-03-29 15:23:27.590 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] monitor closed 
[INFO ] 2024-03-29 15:23:27.591 - [orders_import_import_import_import_import_import(100)][8d6c7770-753b-4b6b-923e-ee064d575e7e] - Node 8d6c7770-753b-4b6b-923e-ee064d575e7e[8d6c7770-753b-4b6b-923e-ee064d575e7e] close complete, cost 38 ms 
[INFO ] 2024-03-29 15:23:27.592 - [orders_import_import_import_import_import_import(100)] - load tapTable task 66066c568c01ee6e137e6df9-ee26f79a-8602-4fb4-9643-72bf86d5a6de complete, cost 2760ms 
[INFO ] 2024-03-29 15:23:27.592 - [orders_import_import_import_import_import_import(100)][b23660d0-a665-4fdf-9ec0-e5cc48acb4fc] - Node b23660d0-a665-4fdf-9ec0-e5cc48acb4fc[b23660d0-a665-4fdf-9ec0-e5cc48acb4fc] close complete, cost 40 ms 
[INFO ] 2024-03-29 15:23:27.595 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] close complete, cost 46 ms 
[INFO ] 2024-03-29 15:23:27.595 - [orders_import_import_import_import_import_import(100)] - load tapTable task 66066c568c01ee6e137e6df9-8d6c7770-753b-4b6b-923e-ee064d575e7e complete, cost 2706ms 
[INFO ] 2024-03-29 15:23:27.595 - [orders_import_import_import_import_import_import(100)] - load tapTable task 66066c568c01ee6e137e6df9-b23660d0-a665-4fdf-9ec0-e5cc48acb4fc complete, cost 2875ms 
[INFO ] 2024-03-29 15:23:27.619 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:23:27.620 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:23:27.624 - [orders_import_import_import_import_import_import(100)][ce8f2f26-de08-4f02-b166-8a33f2379000] - Node ce8f2f26-de08-4f02-b166-8a33f2379000[ce8f2f26-de08-4f02-b166-8a33f2379000] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:23:27.624 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:27.624 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:27.624 - [orders_import_import_import_import_import_import(100)][ce8f2f26-de08-4f02-b166-8a33f2379000] - Node ce8f2f26-de08-4f02-b166-8a33f2379000[ce8f2f26-de08-4f02-b166-8a33f2379000] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:27.827 - [orders_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:23:27.827 - [orders_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@38e4f94a error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@38e4f94a error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@38e4f94a error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:23:27.874 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] running status set to false 
[INFO ] 2024-03-29 15:23:27.874 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:23:27.875 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:23:27.875 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] schema data cleaned 
[INFO ] 2024-03-29 15:23:27.877 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] monitor closed 
[INFO ] 2024-03-29 15:23:27.877 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] close complete, cost 22 ms 
[INFO ] 2024-03-29 15:23:28.367 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] running status set to false 
[INFO ] 2024-03-29 15:23:28.370 - [orders_import_import_import_import_import_import(100)][dfd3848c-c813-4032-8679-1ede33eb038e] - Node dfd3848c-c813-4032-8679-1ede33eb038e[dfd3848c-c813-4032-8679-1ede33eb038e] running status set to false 
[INFO ] 2024-03-29 15:23:28.370 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] schema data cleaned 
[INFO ] 2024-03-29 15:23:28.370 - [orders_import_import_import_import_import_import(100)][dfd3848c-c813-4032-8679-1ede33eb038e] - Node dfd3848c-c813-4032-8679-1ede33eb038e[dfd3848c-c813-4032-8679-1ede33eb038e] schema data cleaned 
[INFO ] 2024-03-29 15:23:28.370 - [orders_import_import_import_import_import_import(100)][dfd3848c-c813-4032-8679-1ede33eb038e] - Node dfd3848c-c813-4032-8679-1ede33eb038e[dfd3848c-c813-4032-8679-1ede33eb038e] monitor closed 
[INFO ] 2024-03-29 15:23:28.371 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] monitor closed 
[INFO ] 2024-03-29 15:23:28.371 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] close complete, cost 10 ms 
[INFO ] 2024-03-29 15:23:28.371 - [orders_import_import_import_import_import_import(100)][dfd3848c-c813-4032-8679-1ede33eb038e] - Node dfd3848c-c813-4032-8679-1ede33eb038e[dfd3848c-c813-4032-8679-1ede33eb038e] close complete, cost 4 ms 
[INFO ] 2024-03-29 15:23:28.578 - [orders_import_import_import_import_import_import(100)] - load tapTable task 66066c568c01ee6e137e6df9-dfd3848c-c813-4032-8679-1ede33eb038e complete, cost 2721ms 
[INFO ] 2024-03-29 15:23:29.495 - [orders_import_import_import_import_import_import(100)][883bbb61-47fe-4938-b54e-227b6a208733] - Node 883bbb61-47fe-4938-b54e-227b6a208733[883bbb61-47fe-4938-b54e-227b6a208733] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:23:29.495 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:23:29.496 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:23:29.498 - [orders_import_import_import_import_import_import(100)][883bbb61-47fe-4938-b54e-227b6a208733] - Node 883bbb61-47fe-4938-b54e-227b6a208733[883bbb61-47fe-4938-b54e-227b6a208733] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:29.498 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:29.498 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:29.605 - [orders_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:23:29.605 - [orders_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@626b191 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@626b191 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@626b191 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:23:29.757 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] running status set to false 
[INFO ] 2024-03-29 15:23:29.757 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:23:29.761 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:23:29.762 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] schema data cleaned 
[INFO ] 2024-03-29 15:23:29.762 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] monitor closed 
[INFO ] 2024-03-29 15:23:29.762 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] close complete, cost 18 ms 
[INFO ] 2024-03-29 15:23:30.348 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] running status set to false 
[INFO ] 2024-03-29 15:23:30.348 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] schema data cleaned 
[INFO ] 2024-03-29 15:23:30.348 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] monitor closed 
[INFO ] 2024-03-29 15:23:30.349 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] close complete, cost 1 ms 
[INFO ] 2024-03-29 15:23:30.354 - [orders_import_import_import_import_import_import(100)][ce8f2f26-de08-4f02-b166-8a33f2379000] - Node ce8f2f26-de08-4f02-b166-8a33f2379000[ce8f2f26-de08-4f02-b166-8a33f2379000] running status set to false 
[INFO ] 2024-03-29 15:23:30.358 - [orders_import_import_import_import_import_import(100)][ce8f2f26-de08-4f02-b166-8a33f2379000] - Node ce8f2f26-de08-4f02-b166-8a33f2379000[ce8f2f26-de08-4f02-b166-8a33f2379000] schema data cleaned 
[INFO ] 2024-03-29 15:23:30.358 - [orders_import_import_import_import_import_import(100)][ce8f2f26-de08-4f02-b166-8a33f2379000] - Node ce8f2f26-de08-4f02-b166-8a33f2379000[ce8f2f26-de08-4f02-b166-8a33f2379000] monitor closed 
[INFO ] 2024-03-29 15:23:30.358 - [orders_import_import_import_import_import_import(100)][ce8f2f26-de08-4f02-b166-8a33f2379000] - Node ce8f2f26-de08-4f02-b166-8a33f2379000[ce8f2f26-de08-4f02-b166-8a33f2379000] close complete, cost 1 ms 
[INFO ] 2024-03-29 15:23:30.358 - [orders_import_import_import_import_import_import(100)] - load tapTable task 66066c568c01ee6e137e6df9-ce8f2f26-de08-4f02-b166-8a33f2379000 complete, cost 2806ms 
[INFO ] 2024-03-29 15:23:31.095 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:23:31.096 - [orders_import_import_import_import_import_import(100)][00a1a3e8-ec14-4bf6-a306-a3f1f0b2d4de] - Node 00a1a3e8-ec14-4bf6-a306-a3f1f0b2d4de[00a1a3e8-ec14-4bf6-a306-a3f1f0b2d4de] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:23:31.096 - [orders_import_import_import_import_import_import(100)][4306c1ac-263a-409b-b1f8-1a1d959b0f06] - Node 4306c1ac-263a-409b-b1f8-1a1d959b0f06[4306c1ac-263a-409b-b1f8-1a1d959b0f06] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:23:31.096 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:23:31.096 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:23:31.096 - [orders_import_import_import_import_import_import(100)][00a1a3e8-ec14-4bf6-a306-a3f1f0b2d4de] - Node 00a1a3e8-ec14-4bf6-a306-a3f1f0b2d4de[00a1a3e8-ec14-4bf6-a306-a3f1f0b2d4de] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:31.096 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:31.096 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:23:31.096 - [orders_import_import_import_import_import_import(100)][4306c1ac-263a-409b-b1f8-1a1d959b0f06] - Node 4306c1ac-263a-409b-b1f8-1a1d959b0f06[4306c1ac-263a-409b-b1f8-1a1d959b0f06] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:31.096 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:31.097 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:31.188 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:23:31.190 - [orders_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 15:23:31.191 - [orders_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:23:31.221 - [orders_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@12ddf1ac error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@12ddf1ac error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@12ddf1ac error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-29 15:23:31.223 - [orders_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7ac3aeff error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7ac3aeff error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7ac3aeff error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:23:31.373 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] running status set to false 
[INFO ] 2024-03-29 15:23:31.385 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:23:31.386 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:23:31.386 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] schema data cleaned 
[INFO ] 2024-03-29 15:23:31.386 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] monitor closed 
[INFO ] 2024-03-29 15:23:31.387 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] close complete, cost 22 ms 
[INFO ] 2024-03-29 15:23:31.514 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] running status set to false 
[INFO ] 2024-03-29 15:23:31.514 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:23:31.514 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:23:31.514 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] schema data cleaned 
[INFO ] 2024-03-29 15:23:31.517 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] monitor closed 
[INFO ] 2024-03-29 15:23:31.518 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] close complete, cost 9 ms 
[INFO ] 2024-03-29 15:23:32.136 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] running status set to false 
[INFO ] 2024-03-29 15:23:32.139 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] schema data cleaned 
[INFO ] 2024-03-29 15:23:32.139 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] monitor closed 
[INFO ] 2024-03-29 15:23:32.139 - [orders_import_import_import_import_import_import(100)][883bbb61-47fe-4938-b54e-227b6a208733] - Node 883bbb61-47fe-4938-b54e-227b6a208733[883bbb61-47fe-4938-b54e-227b6a208733] running status set to false 
[INFO ] 2024-03-29 15:23:32.141 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] close complete, cost 6 ms 
[INFO ] 2024-03-29 15:23:32.142 - [orders_import_import_import_import_import_import(100)][883bbb61-47fe-4938-b54e-227b6a208733] - Node 883bbb61-47fe-4938-b54e-227b6a208733[883bbb61-47fe-4938-b54e-227b6a208733] schema data cleaned 
[INFO ] 2024-03-29 15:23:32.142 - [orders_import_import_import_import_import_import(100)][883bbb61-47fe-4938-b54e-227b6a208733] - Node 883bbb61-47fe-4938-b54e-227b6a208733[883bbb61-47fe-4938-b54e-227b6a208733] monitor closed 
[INFO ] 2024-03-29 15:23:32.142 - [orders_import_import_import_import_import_import(100)][883bbb61-47fe-4938-b54e-227b6a208733] - Node 883bbb61-47fe-4938-b54e-227b6a208733[883bbb61-47fe-4938-b54e-227b6a208733] close complete, cost 5 ms 
[INFO ] 2024-03-29 15:23:32.352 - [orders_import_import_import_import_import_import(100)] - load tapTable task 66066c568c01ee6e137e6df9-883bbb61-47fe-4938-b54e-227b6a208733 complete, cost 2728ms 
[INFO ] 2024-03-29 15:23:33.750 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] running status set to false 
[INFO ] 2024-03-29 15:23:33.750 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] running status set to false 
[INFO ] 2024-03-29 15:23:33.755 - [orders_import_import_import_import_import_import(100)][00a1a3e8-ec14-4bf6-a306-a3f1f0b2d4de] - Node 00a1a3e8-ec14-4bf6-a306-a3f1f0b2d4de[00a1a3e8-ec14-4bf6-a306-a3f1f0b2d4de] running status set to false 
[INFO ] 2024-03-29 15:23:33.756 - [orders_import_import_import_import_import_import(100)][4306c1ac-263a-409b-b1f8-1a1d959b0f06] - Node 4306c1ac-263a-409b-b1f8-1a1d959b0f06[4306c1ac-263a-409b-b1f8-1a1d959b0f06] running status set to false 
[INFO ] 2024-03-29 15:23:33.756 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] schema data cleaned 
[INFO ] 2024-03-29 15:23:33.756 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] schema data cleaned 
[INFO ] 2024-03-29 15:23:33.756 - [orders_import_import_import_import_import_import(100)][00a1a3e8-ec14-4bf6-a306-a3f1f0b2d4de] - Node 00a1a3e8-ec14-4bf6-a306-a3f1f0b2d4de[00a1a3e8-ec14-4bf6-a306-a3f1f0b2d4de] schema data cleaned 
[INFO ] 2024-03-29 15:23:33.757 - [orders_import_import_import_import_import_import(100)][4306c1ac-263a-409b-b1f8-1a1d959b0f06] - Node 4306c1ac-263a-409b-b1f8-1a1d959b0f06[4306c1ac-263a-409b-b1f8-1a1d959b0f06] schema data cleaned 
[INFO ] 2024-03-29 15:23:33.757 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] monitor closed 
[INFO ] 2024-03-29 15:23:33.760 - [orders_import_import_import_import_import_import(100)][00a1a3e8-ec14-4bf6-a306-a3f1f0b2d4de] - Node 00a1a3e8-ec14-4bf6-a306-a3f1f0b2d4de[00a1a3e8-ec14-4bf6-a306-a3f1f0b2d4de] monitor closed 
[INFO ] 2024-03-29 15:23:33.760 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] monitor closed 
[INFO ] 2024-03-29 15:23:33.760 - [orders_import_import_import_import_import_import(100)][00a1a3e8-ec14-4bf6-a306-a3f1f0b2d4de] - Node 00a1a3e8-ec14-4bf6-a306-a3f1f0b2d4de[00a1a3e8-ec14-4bf6-a306-a3f1f0b2d4de] close complete, cost 13 ms 
[INFO ] 2024-03-29 15:23:33.761 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] close complete, cost 12 ms 
[INFO ] 2024-03-29 15:23:33.761 - [orders_import_import_import_import_import_import(100)][4306c1ac-263a-409b-b1f8-1a1d959b0f06] - Node 4306c1ac-263a-409b-b1f8-1a1d959b0f06[4306c1ac-263a-409b-b1f8-1a1d959b0f06] monitor closed 
[INFO ] 2024-03-29 15:23:33.761 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] close complete, cost 14 ms 
[INFO ] 2024-03-29 15:23:33.761 - [orders_import_import_import_import_import_import(100)][4306c1ac-263a-409b-b1f8-1a1d959b0f06] - Node 4306c1ac-263a-409b-b1f8-1a1d959b0f06[4306c1ac-263a-409b-b1f8-1a1d959b0f06] close complete, cost 15 ms 
[INFO ] 2024-03-29 15:23:33.762 - [orders_import_import_import_import_import_import(100)] - load tapTable task 66066c568c01ee6e137e6df9-00a1a3e8-ec14-4bf6-a306-a3f1f0b2d4de complete, cost 2814ms 
[INFO ] 2024-03-29 15:23:33.967 - [orders_import_import_import_import_import_import(100)] - load tapTable task 66066c568c01ee6e137e6df9-4306c1ac-263a-409b-b1f8-1a1d959b0f06 complete, cost 2799ms 
[INFO ] 2024-03-29 15:42:52.092 - [orders_import_import_import_import_import_import(100)][c3f97d86-b47f-4680-828b-d78f6fb5115c] - Node c3f97d86-b47f-4680-828b-d78f6fb5115c[c3f97d86-b47f-4680-828b-d78f6fb5115c] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:42:52.094 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:42:52.095 - [orders_import_import_import_import_import_import(100)][ac9a8e47-76da-4f08-9742-6a162bada659] - Node ac9a8e47-76da-4f08-9742-6a162bada659[ac9a8e47-76da-4f08-9742-6a162bada659] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:42:52.111 - [orders_import_import_import_import_import_import(100)][c3f97d86-b47f-4680-828b-d78f6fb5115c] - Node c3f97d86-b47f-4680-828b-d78f6fb5115c[c3f97d86-b47f-4680-828b-d78f6fb5115c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:52.122 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:52.122 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:42:52.123 - [orders_import_import_import_import_import_import(100)][ac9a8e47-76da-4f08-9742-6a162bada659] - Node ac9a8e47-76da-4f08-9742-6a162bada659[ac9a8e47-76da-4f08-9742-6a162bada659] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:52.124 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:42:52.126 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:42:52.126 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:52.126 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:52.126 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:52.219 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:42:52.220 - [orders_import_import_import_import_import_import(100)][e615595b-2984-43ec-888a-292876748794] - Node e615595b-2984-43ec-888a-292876748794[e615595b-2984-43ec-888a-292876748794] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:42:52.220 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:42:52.220 - [orders_import_import_import_import_import_import(100)][e615595b-2984-43ec-888a-292876748794] - Node e615595b-2984-43ec-888a-292876748794[e615595b-2984-43ec-888a-292876748794] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:52.220 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:52.221 - [orders_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 15:42:52.221 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 15:42:52.221 - [orders_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 15:42:52.235 - [orders_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:42:52.253 - [orders_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4680fb25 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4680fb25 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4680fb25 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-29 15:42:52.254 - [orders_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@197eb06 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@197eb06 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@197eb06 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-29 15:42:52.254 - [orders_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@586219f6 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@586219f6 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@586219f6 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:42:52.257 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:42:52.257 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:42:52.257 - [orders_import_import_import_import_import_import(100)][8adf82fd-79bd-4d5a-bfa8-26db39db8d89] - Node 8adf82fd-79bd-4d5a-bfa8-26db39db8d89[8adf82fd-79bd-4d5a-bfa8-26db39db8d89] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:42:52.258 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:52.258 - [orders_import_import_import_import_import_import(100)][8adf82fd-79bd-4d5a-bfa8-26db39db8d89] - Node 8adf82fd-79bd-4d5a-bfa8-26db39db8d89[8adf82fd-79bd-4d5a-bfa8-26db39db8d89] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:52.310 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:52.311 - [orders_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:42:52.393 - [orders_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@34f49898 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@34f49898 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@34f49898 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:42:52.398 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] running status set to false 
[INFO ] 2024-03-29 15:42:52.407 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:42:52.407 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:42:52.407 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] schema data cleaned 
[INFO ] 2024-03-29 15:42:52.407 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] monitor closed 
[INFO ] 2024-03-29 15:42:52.543 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] close complete, cost 23 ms 
[INFO ] 2024-03-29 15:42:52.543 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] running status set to false 
[INFO ] 2024-03-29 15:42:52.552 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:42:52.552 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:42:52.552 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] schema data cleaned 
[INFO ] 2024-03-29 15:42:52.554 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] monitor closed 
[INFO ] 2024-03-29 15:42:52.555 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] close complete, cost 11 ms 
[INFO ] 2024-03-29 15:42:52.697 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] running status set to false 
[INFO ] 2024-03-29 15:42:52.697 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:42:52.697 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:42:52.697 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] schema data cleaned 
[INFO ] 2024-03-29 15:42:52.697 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] monitor closed 
[INFO ] 2024-03-29 15:42:52.698 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] close complete, cost 7 ms 
[INFO ] 2024-03-29 15:42:52.899 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] running status set to false 
[INFO ] 2024-03-29 15:42:52.900 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:42:52.900 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:42:52.900 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] schema data cleaned 
[INFO ] 2024-03-29 15:42:52.900 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] monitor closed 
[INFO ] 2024-03-29 15:42:53.108 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] close complete, cost 30 ms 
[INFO ] 2024-03-29 15:42:53.160 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:42:53.160 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:42:53.161 - [orders_import_import_import_import_import_import(100)][bfbcdb0d-5bb6-43ae-8519-14e814317978] - Node bfbcdb0d-5bb6-43ae-8519-14e814317978[bfbcdb0d-5bb6-43ae-8519-14e814317978] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:42:53.162 - [orders_import_import_import_import_import_import(100)][bfbcdb0d-5bb6-43ae-8519-14e814317978] - Node bfbcdb0d-5bb6-43ae-8519-14e814317978[bfbcdb0d-5bb6-43ae-8519-14e814317978] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:53.162 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:53.162 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:53.263 - [orders_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 15:42:53.264 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:42:53.264 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:42:53.265 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:53.265 - [orders_import_import_import_import_import_import(100)][933fc5df-df85-45da-8235-4aee86003338] - Node 933fc5df-df85-45da-8235-4aee86003338[933fc5df-df85-45da-8235-4aee86003338] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:42:53.265 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:53.274 - [orders_import_import_import_import_import_import(100)][933fc5df-df85-45da-8235-4aee86003338] - Node 933fc5df-df85-45da-8235-4aee86003338[933fc5df-df85-45da-8235-4aee86003338] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 15:42:53.281 - [orders_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@77a7e95e error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@77a7e95e error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@77a7e95e error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:42:53.314 - [orders_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:42:53.315 - [orders_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@779ff487 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@779ff487 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@779ff487 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:42:53.341 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:42:53.341 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:42:53.341 - [orders_import_import_import_import_import_import(100)][c29931b0-4771-4f7e-9a21-4e8be5d0cb68] - Node c29931b0-4771-4f7e-9a21-4e8be5d0cb68[c29931b0-4771-4f7e-9a21-4e8be5d0cb68] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:42:53.341 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:53.341 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:53.341 - [orders_import_import_import_import_import_import(100)][c29931b0-4771-4f7e-9a21-4e8be5d0cb68] - Node c29931b0-4771-4f7e-9a21-4e8be5d0cb68[c29931b0-4771-4f7e-9a21-4e8be5d0cb68] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:53.353 - [orders_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:42:53.411 - [orders_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@450a8fad error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@450a8fad error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@450a8fad error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:42:53.411 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] running status set to false 
[INFO ] 2024-03-29 15:42:53.419 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:42:53.419 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:42:53.419 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] schema data cleaned 
[INFO ] 2024-03-29 15:42:53.419 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] monitor closed 
[INFO ] 2024-03-29 15:42:53.419 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] close complete, cost 9 ms 
[INFO ] 2024-03-29 15:42:53.623 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] running status set to false 
[INFO ] 2024-03-29 15:42:53.623 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:42:53.624 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:42:53.624 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] schema data cleaned 
[INFO ] 2024-03-29 15:42:53.624 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] monitor closed 
[INFO ] 2024-03-29 15:42:53.736 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] close complete, cost 63 ms 
[INFO ] 2024-03-29 15:42:53.736 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] running status set to false 
[INFO ] 2024-03-29 15:42:53.746 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:42:53.746 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:42:53.747 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] schema data cleaned 
[INFO ] 2024-03-29 15:42:53.748 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] monitor closed 
[INFO ] 2024-03-29 15:42:53.959 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] close complete, cost 16 ms 
[INFO ] 2024-03-29 15:42:54.825 - [orders_import_import_import_import_import_import(100)][c3f97d86-b47f-4680-828b-d78f6fb5115c] - Node c3f97d86-b47f-4680-828b-d78f6fb5115c[c3f97d86-b47f-4680-828b-d78f6fb5115c] running status set to false 
[INFO ] 2024-03-29 15:42:54.825 - [orders_import_import_import_import_import_import(100)][ac9a8e47-76da-4f08-9742-6a162bada659] - Node ac9a8e47-76da-4f08-9742-6a162bada659[ac9a8e47-76da-4f08-9742-6a162bada659] running status set to false 
[INFO ] 2024-03-29 15:42:54.825 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] running status set to false 
[INFO ] 2024-03-29 15:42:54.828 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] running status set to false 
[INFO ] 2024-03-29 15:42:54.828 - [orders_import_import_import_import_import_import(100)][e615595b-2984-43ec-888a-292876748794] - Node e615595b-2984-43ec-888a-292876748794[e615595b-2984-43ec-888a-292876748794] running status set to false 
[INFO ] 2024-03-29 15:42:54.828 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] running status set to false 
[INFO ] 2024-03-29 15:42:54.828 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] schema data cleaned 
[INFO ] 2024-03-29 15:42:54.828 - [orders_import_import_import_import_import_import(100)][ac9a8e47-76da-4f08-9742-6a162bada659] - Node ac9a8e47-76da-4f08-9742-6a162bada659[ac9a8e47-76da-4f08-9742-6a162bada659] schema data cleaned 
[INFO ] 2024-03-29 15:42:54.830 - [orders_import_import_import_import_import_import(100)][c3f97d86-b47f-4680-828b-d78f6fb5115c] - Node c3f97d86-b47f-4680-828b-d78f6fb5115c[c3f97d86-b47f-4680-828b-d78f6fb5115c] schema data cleaned 
[INFO ] 2024-03-29 15:42:54.830 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] schema data cleaned 
[INFO ] 2024-03-29 15:42:54.830 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] schema data cleaned 
[INFO ] 2024-03-29 15:42:54.830 - [orders_import_import_import_import_import_import(100)][e615595b-2984-43ec-888a-292876748794] - Node e615595b-2984-43ec-888a-292876748794[e615595b-2984-43ec-888a-292876748794] schema data cleaned 
[INFO ] 2024-03-29 15:42:54.830 - [orders_import_import_import_import_import_import(100)][c3f97d86-b47f-4680-828b-d78f6fb5115c] - Node c3f97d86-b47f-4680-828b-d78f6fb5115c[c3f97d86-b47f-4680-828b-d78f6fb5115c] monitor closed 
[INFO ] 2024-03-29 15:42:54.830 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] monitor closed 
[INFO ] 2024-03-29 15:42:54.830 - [orders_import_import_import_import_import_import(100)][ac9a8e47-76da-4f08-9742-6a162bada659] - Node ac9a8e47-76da-4f08-9742-6a162bada659[ac9a8e47-76da-4f08-9742-6a162bada659] monitor closed 
[INFO ] 2024-03-29 15:42:54.834 - [orders_import_import_import_import_import_import(100)][e615595b-2984-43ec-888a-292876748794] - Node e615595b-2984-43ec-888a-292876748794[e615595b-2984-43ec-888a-292876748794] monitor closed 
[INFO ] 2024-03-29 15:42:54.834 - [orders_import_import_import_import_import_import(100)][c3f97d86-b47f-4680-828b-d78f6fb5115c] - Node c3f97d86-b47f-4680-828b-d78f6fb5115c[c3f97d86-b47f-4680-828b-d78f6fb5115c] close complete, cost 25 ms 
[INFO ] 2024-03-29 15:42:54.834 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] monitor closed 
[INFO ] 2024-03-29 15:42:54.834 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] monitor closed 
[INFO ] 2024-03-29 15:42:54.834 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] close complete, cost 30 ms 
[INFO ] 2024-03-29 15:42:54.834 - [orders_import_import_import_import_import_import(100)][ac9a8e47-76da-4f08-9742-6a162bada659] - Node ac9a8e47-76da-4f08-9742-6a162bada659[ac9a8e47-76da-4f08-9742-6a162bada659] close complete, cost 30 ms 
[INFO ] 2024-03-29 15:42:54.834 - [orders_import_import_import_import_import_import(100)][e615595b-2984-43ec-888a-292876748794] - Node e615595b-2984-43ec-888a-292876748794[e615595b-2984-43ec-888a-292876748794] close complete, cost 24 ms 
[INFO ] 2024-03-29 15:42:54.834 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] close complete, cost 34 ms 
[INFO ] 2024-03-29 15:42:54.834 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] close complete, cost 35 ms 
[INFO ] 2024-03-29 15:42:54.839 - [orders_import_import_import_import_import_import(100)] - load tapTable task 66066c568c01ee6e137e6df9-e615595b-2984-43ec-888a-292876748794 complete, cost 2747ms 
[INFO ] 2024-03-29 15:42:54.839 - [orders_import_import_import_import_import_import(100)] - load tapTable task 66066c568c01ee6e137e6df9-ac9a8e47-76da-4f08-9742-6a162bada659 complete, cost 2998ms 
[INFO ] 2024-03-29 15:42:54.856 - [orders_import_import_import_import_import_import(100)] - load tapTable task 66066c568c01ee6e137e6df9-c3f97d86-b47f-4680-828b-d78f6fb5115c complete, cost 2960ms 
[INFO ] 2024-03-29 15:42:54.856 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] running status set to false 
[INFO ] 2024-03-29 15:42:54.857 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] schema data cleaned 
[INFO ] 2024-03-29 15:42:54.857 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] monitor closed 
[INFO ] 2024-03-29 15:42:54.862 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] close complete, cost 1 ms 
[INFO ] 2024-03-29 15:42:54.862 - [orders_import_import_import_import_import_import(100)][8adf82fd-79bd-4d5a-bfa8-26db39db8d89] - Node 8adf82fd-79bd-4d5a-bfa8-26db39db8d89[8adf82fd-79bd-4d5a-bfa8-26db39db8d89] running status set to false 
[INFO ] 2024-03-29 15:42:54.862 - [orders_import_import_import_import_import_import(100)][8adf82fd-79bd-4d5a-bfa8-26db39db8d89] - Node 8adf82fd-79bd-4d5a-bfa8-26db39db8d89[8adf82fd-79bd-4d5a-bfa8-26db39db8d89] schema data cleaned 
[INFO ] 2024-03-29 15:42:54.862 - [orders_import_import_import_import_import_import(100)][8adf82fd-79bd-4d5a-bfa8-26db39db8d89] - Node 8adf82fd-79bd-4d5a-bfa8-26db39db8d89[8adf82fd-79bd-4d5a-bfa8-26db39db8d89] monitor closed 
[INFO ] 2024-03-29 15:42:54.864 - [orders_import_import_import_import_import_import(100)][8adf82fd-79bd-4d5a-bfa8-26db39db8d89] - Node 8adf82fd-79bd-4d5a-bfa8-26db39db8d89[8adf82fd-79bd-4d5a-bfa8-26db39db8d89] close complete, cost 0 ms 
[INFO ] 2024-03-29 15:42:54.864 - [orders_import_import_import_import_import_import(100)] - load tapTable task 66066c568c01ee6e137e6df9-8adf82fd-79bd-4d5a-bfa8-26db39db8d89 complete, cost 2668ms 
[INFO ] 2024-03-29 15:42:55.845 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] running status set to false 
[INFO ] 2024-03-29 15:42:55.845 - [orders_import_import_import_import_import_import(100)][bfbcdb0d-5bb6-43ae-8519-14e814317978] - Node bfbcdb0d-5bb6-43ae-8519-14e814317978[bfbcdb0d-5bb6-43ae-8519-14e814317978] running status set to false 
[INFO ] 2024-03-29 15:42:55.848 - [orders_import_import_import_import_import_import(100)][bfbcdb0d-5bb6-43ae-8519-14e814317978] - Node bfbcdb0d-5bb6-43ae-8519-14e814317978[bfbcdb0d-5bb6-43ae-8519-14e814317978] schema data cleaned 
[INFO ] 2024-03-29 15:42:55.848 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] schema data cleaned 
[INFO ] 2024-03-29 15:42:55.849 - [orders_import_import_import_import_import_import(100)][bfbcdb0d-5bb6-43ae-8519-14e814317978] - Node bfbcdb0d-5bb6-43ae-8519-14e814317978[bfbcdb0d-5bb6-43ae-8519-14e814317978] monitor closed 
[INFO ] 2024-03-29 15:42:55.849 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] running status set to false 
[INFO ] 2024-03-29 15:42:55.849 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] monitor closed 
[INFO ] 2024-03-29 15:42:55.849 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] schema data cleaned 
[INFO ] 2024-03-29 15:42:55.850 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] close complete, cost 28 ms 
[INFO ] 2024-03-29 15:42:55.850 - [orders_import_import_import_import_import_import(100)][bfbcdb0d-5bb6-43ae-8519-14e814317978] - Node bfbcdb0d-5bb6-43ae-8519-14e814317978[bfbcdb0d-5bb6-43ae-8519-14e814317978] close complete, cost 22 ms 
[INFO ] 2024-03-29 15:42:55.854 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] monitor closed 
[INFO ] 2024-03-29 15:42:55.855 - [orders_import_import_import_import_import_import(100)] - load tapTable task 66066c568c01ee6e137e6df9-bfbcdb0d-5bb6-43ae-8519-14e814317978 complete, cost 2781ms 
[INFO ] 2024-03-29 15:42:55.855 - [orders_import_import_import_import_import_import(100)][933fc5df-df85-45da-8235-4aee86003338] - Node 933fc5df-df85-45da-8235-4aee86003338[933fc5df-df85-45da-8235-4aee86003338] running status set to false 
[INFO ] 2024-03-29 15:42:55.855 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] close complete, cost 8 ms 
[INFO ] 2024-03-29 15:42:55.855 - [orders_import_import_import_import_import_import(100)][933fc5df-df85-45da-8235-4aee86003338] - Node 933fc5df-df85-45da-8235-4aee86003338[933fc5df-df85-45da-8235-4aee86003338] schema data cleaned 
[INFO ] 2024-03-29 15:42:55.856 - [orders_import_import_import_import_import_import(100)][933fc5df-df85-45da-8235-4aee86003338] - Node 933fc5df-df85-45da-8235-4aee86003338[933fc5df-df85-45da-8235-4aee86003338] monitor closed 
[INFO ] 2024-03-29 15:42:55.858 - [orders_import_import_import_import_import_import(100)][933fc5df-df85-45da-8235-4aee86003338] - Node 933fc5df-df85-45da-8235-4aee86003338[933fc5df-df85-45da-8235-4aee86003338] close complete, cost 5 ms 
[INFO ] 2024-03-29 15:42:55.861 - [orders_import_import_import_import_import_import(100)] - load tapTable task 66066c568c01ee6e137e6df9-933fc5df-df85-45da-8235-4aee86003338 complete, cost 2628ms 
[INFO ] 2024-03-29 15:42:55.899 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] running status set to false 
[INFO ] 2024-03-29 15:42:55.899 - [orders_import_import_import_import_import_import(100)][c29931b0-4771-4f7e-9a21-4e8be5d0cb68] - Node c29931b0-4771-4f7e-9a21-4e8be5d0cb68[c29931b0-4771-4f7e-9a21-4e8be5d0cb68] running status set to false 
[INFO ] 2024-03-29 15:42:55.899 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] schema data cleaned 
[INFO ] 2024-03-29 15:42:55.900 - [orders_import_import_import_import_import_import(100)][c29931b0-4771-4f7e-9a21-4e8be5d0cb68] - Node c29931b0-4771-4f7e-9a21-4e8be5d0cb68[c29931b0-4771-4f7e-9a21-4e8be5d0cb68] schema data cleaned 
[INFO ] 2024-03-29 15:42:55.900 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] monitor closed 
[INFO ] 2024-03-29 15:42:55.900 - [orders_import_import_import_import_import_import(100)][c29931b0-4771-4f7e-9a21-4e8be5d0cb68] - Node c29931b0-4771-4f7e-9a21-4e8be5d0cb68[c29931b0-4771-4f7e-9a21-4e8be5d0cb68] monitor closed 
[INFO ] 2024-03-29 15:42:55.900 - [orders_import_import_import_import_import_import(100)][c29931b0-4771-4f7e-9a21-4e8be5d0cb68] - Node c29931b0-4771-4f7e-9a21-4e8be5d0cb68[c29931b0-4771-4f7e-9a21-4e8be5d0cb68] close complete, cost 1 ms 
[INFO ] 2024-03-29 15:42:55.901 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] close complete, cost 2 ms 
[INFO ] 2024-03-29 15:42:55.902 - [orders_import_import_import_import_import_import(100)] - load tapTable task 66066c568c01ee6e137e6df9-c29931b0-4771-4f7e-9a21-4e8be5d0cb68 complete, cost 2599ms 
[INFO ] 2024-03-29 15:42:57.940 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:42:57.940 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:42:57.940 - [orders_import_import_import_import_import_import(100)][05928105-ce2f-4cb7-8290-aefddaa71282] - Node 05928105-ce2f-4cb7-8290-aefddaa71282[05928105-ce2f-4cb7-8290-aefddaa71282] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:42:57.944 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:57.944 - [orders_import_import_import_import_import_import(100)][05928105-ce2f-4cb7-8290-aefddaa71282] - Node 05928105-ce2f-4cb7-8290-aefddaa71282[05928105-ce2f-4cb7-8290-aefddaa71282] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:57.944 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:42:58.013 - [orders_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:42:58.013 - [orders_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@396641bc error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@396641bc error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@396641bc error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:42:58.174 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] running status set to false 
[INFO ] 2024-03-29 15:42:58.174 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:42:58.174 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:42:58.174 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] schema data cleaned 
[INFO ] 2024-03-29 15:42:58.174 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] monitor closed 
[INFO ] 2024-03-29 15:42:58.175 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] close complete, cost 15 ms 
[INFO ] 2024-03-29 15:43:00.580 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] running status set to false 
[INFO ] 2024-03-29 15:43:00.581 - [orders_import_import_import_import_import_import(100)][05928105-ce2f-4cb7-8290-aefddaa71282] - Node 05928105-ce2f-4cb7-8290-aefddaa71282[05928105-ce2f-4cb7-8290-aefddaa71282] running status set to false 
[INFO ] 2024-03-29 15:43:00.584 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] schema data cleaned 
[INFO ] 2024-03-29 15:43:00.592 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] monitor closed 
[INFO ] 2024-03-29 15:43:00.593 - [orders_import_import_import_import_import_import(100)][05928105-ce2f-4cb7-8290-aefddaa71282] - Node 05928105-ce2f-4cb7-8290-aefddaa71282[05928105-ce2f-4cb7-8290-aefddaa71282] schema data cleaned 
[INFO ] 2024-03-29 15:43:00.593 - [orders_import_import_import_import_import_import(100)][05928105-ce2f-4cb7-8290-aefddaa71282] - Node 05928105-ce2f-4cb7-8290-aefddaa71282[05928105-ce2f-4cb7-8290-aefddaa71282] monitor closed 
[INFO ] 2024-03-29 15:43:00.593 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] close complete, cost 54 ms 
[INFO ] 2024-03-29 15:43:00.596 - [orders_import_import_import_import_import_import(100)][05928105-ce2f-4cb7-8290-aefddaa71282] - Node 05928105-ce2f-4cb7-8290-aefddaa71282[05928105-ce2f-4cb7-8290-aefddaa71282] close complete, cost 50 ms 
[INFO ] 2024-03-29 15:43:00.596 - [orders_import_import_import_import_import_import(100)] - load tapTable task 66066c568c01ee6e137e6df9-05928105-ce2f-4cb7-8290-aefddaa71282 complete, cost 2715ms 
[INFO ] 2024-03-29 15:43:04.644 - [orders_import_import_import_import_import_import(100)][35ffcdb7-6be1-458a-b0ea-00863453ae56] - Node 35ffcdb7-6be1-458a-b0ea-00863453ae56[35ffcdb7-6be1-458a-b0ea-00863453ae56] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:43:04.645 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:43:04.645 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:43:04.645 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:43:04.645 - [orders_import_import_import_import_import_import(100)][35ffcdb7-6be1-458a-b0ea-00863453ae56] - Node 35ffcdb7-6be1-458a-b0ea-00863453ae56[35ffcdb7-6be1-458a-b0ea-00863453ae56] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:43:04.646 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:43:04.716 - [orders_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:43:04.717 - [orders_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@143397be error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@143397be error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@143397be error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:43:04.887 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] running status set to false 
[INFO ] 2024-03-29 15:43:04.887 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:43:04.887 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:43:04.887 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] schema data cleaned 
[INFO ] 2024-03-29 15:43:04.887 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] monitor closed 
[INFO ] 2024-03-29 15:43:05.093 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] close complete, cost 12 ms 
[INFO ] 2024-03-29 15:43:07.278 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] running status set to false 
[INFO ] 2024-03-29 15:43:07.281 - [orders_import_import_import_import_import_import(100)][35ffcdb7-6be1-458a-b0ea-00863453ae56] - Node 35ffcdb7-6be1-458a-b0ea-00863453ae56[35ffcdb7-6be1-458a-b0ea-00863453ae56] running status set to false 
[INFO ] 2024-03-29 15:43:07.281 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] schema data cleaned 
[INFO ] 2024-03-29 15:43:07.281 - [orders_import_import_import_import_import_import(100)][35ffcdb7-6be1-458a-b0ea-00863453ae56] - Node 35ffcdb7-6be1-458a-b0ea-00863453ae56[35ffcdb7-6be1-458a-b0ea-00863453ae56] schema data cleaned 
[INFO ] 2024-03-29 15:43:07.281 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] monitor closed 
[INFO ] 2024-03-29 15:43:07.281 - [orders_import_import_import_import_import_import(100)][35ffcdb7-6be1-458a-b0ea-00863453ae56] - Node 35ffcdb7-6be1-458a-b0ea-00863453ae56[35ffcdb7-6be1-458a-b0ea-00863453ae56] monitor closed 
[INFO ] 2024-03-29 15:43:07.281 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] close complete, cost 15 ms 
[INFO ] 2024-03-29 15:43:07.281 - [orders_import_import_import_import_import_import(100)][35ffcdb7-6be1-458a-b0ea-00863453ae56] - Node 35ffcdb7-6be1-458a-b0ea-00863453ae56[35ffcdb7-6be1-458a-b0ea-00863453ae56] close complete, cost 15 ms 
[INFO ] 2024-03-29 15:43:07.496 - [orders_import_import_import_import_import_import(100)] - load tapTable task 66066c568c01ee6e137e6df9-35ffcdb7-6be1-458a-b0ea-00863453ae56 complete, cost 2731ms 
[INFO ] 2024-03-29 15:53:52.609 - [orders_import_import_import_import_import_import(100)][b830906c-edb8-402f-836d-9969ce8c84d6] - Node b830906c-edb8-402f-836d-9969ce8c84d6[b830906c-edb8-402f-836d-9969ce8c84d6] start preload schema,table counts: 0 
[INFO ] 2024-03-29 15:53:52.629 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:53:52.633 - [orders_import_import_import_import_import_import(100)][b830906c-edb8-402f-836d-9969ce8c84d6] - Node b830906c-edb8-402f-836d-9969ce8c84d6[b830906c-edb8-402f-836d-9969ce8c84d6] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 15:53:52.636 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 15:53:52.641 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:53:52.642 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 15:53:52.801 - [orders_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 15:53:52.967 - [orders_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@289f0746 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@289f0746 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@289f0746 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 15:53:58.037 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] running status set to false 
[INFO ] 2024-03-29 15:53:58.060 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] running status set to false 
[INFO ] 2024-03-29 15:53:58.063 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] schema data cleaned 
[INFO ] 2024-03-29 15:53:58.063 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] monitor closed 
[INFO ] 2024-03-29 15:53:58.064 - [orders_import_import_import_import_import_import(100)][b830906c-edb8-402f-836d-9969ce8c84d6] - Node b830906c-edb8-402f-836d-9969ce8c84d6[b830906c-edb8-402f-836d-9969ce8c84d6] running status set to false 
[INFO ] 2024-03-29 15:53:58.073 - [orders_import_import_import_import_import_import(100)][b830906c-edb8-402f-836d-9969ce8c84d6] - Node b830906c-edb8-402f-836d-9969ce8c84d6[b830906c-edb8-402f-836d-9969ce8c84d6] schema data cleaned 
[INFO ] 2024-03-29 15:53:58.077 - [orders_import_import_import_import_import_import(100)][b830906c-edb8-402f-836d-9969ce8c84d6] - Node b830906c-edb8-402f-836d-9969ce8c84d6[b830906c-edb8-402f-836d-9969ce8c84d6] monitor closed 
[INFO ] 2024-03-29 15:53:58.082 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] close complete, cost 10 ms 
[INFO ] 2024-03-29 15:53:58.090 - [orders_import_import_import_import_import_import(100)][b830906c-edb8-402f-836d-9969ce8c84d6] - Node b830906c-edb8-402f-836d-9969ce8c84d6[b830906c-edb8-402f-836d-9969ce8c84d6] close complete, cost 15 ms 
[INFO ] 2024-03-29 15:53:58.093 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:53:58.099 - [orders_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f69f5315-82c0-4828-8999-d7e0f2942273 
[INFO ] 2024-03-29 15:53:58.099 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] schema data cleaned 
[INFO ] 2024-03-29 15:53:58.099 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] monitor closed 
[INFO ] 2024-03-29 15:53:58.100 - [orders_import_import_import_import_import_import(100)][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] close complete, cost 55 ms 
[INFO ] 2024-03-29 15:53:58.100 - [orders_import_import_import_import_import_import(100)] - load tapTable task 66066c568c01ee6e137e6df9-b830906c-edb8-402f-836d-9969ce8c84d6 complete, cost 5953ms 
