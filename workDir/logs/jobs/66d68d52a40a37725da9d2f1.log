[WARN ] 2024-09-04 01:58:12.869 - [任务 5][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 17410, message: Caused by: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:855)
	oracle.jdbc.driver.T4CMAREngineNIO.getNBytes(T4CMAREngineNIO.java:636)
	oracle.jdbc.driver.T4CMAREngineNIO.unmarshalNBytes(T4CMAREngineNIO.java:605)
	oracle.jdbc.driver.DynamicByteArray.unmarshalBuffer(DynamicByteArray.java:304)
	oracle.jdbc.driver.DynamicByteArray.unmarshalCLR(DynamicByteArray.java:219)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-04 02:25:48.565 - [任务 5][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-09-04 02:25:48.766 - [任务 5][Oracle] - Log Miner has been closed! 
[INFO ] 2024-09-04 02:41:57.581 - [任务 5][Oracle] - total start mining scn: 106536873 
[INFO ] 2024-09-04 02:41:58.760 - [任务 5][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_09_03/o1_mf_1_2556_mfg6mpj6_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-09-04 02:42:05.706 - [任务 5][Oracle] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-04 02:42:06.931 - [任务 5][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_09_03/o1_mf_1_2557_mfgbkqdp_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[WARN ] 2024-09-04 03:17:51.434 - [任务 5][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:855)
	oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-04 03:27:54.116 - [任务 5][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-09-04 03:27:54.116 - [任务 5][Oracle] - Log Miner has been closed! 
[INFO ] 2024-09-04 03:43:53.419 - [任务 5][Oracle] - total start mining scn: 106582978 
[INFO ] 2024-09-04 03:43:54.671 - [任务 5][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_09_03/o1_mf_1_2557_mfgbkqdp_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-09-04 03:44:36.813 - [任务 5][Oracle] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-04 03:44:38.044 - [任务 5][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_09_04/o1_mf_1_2558_mfgmp4b4_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-09-04 03:44:52.421 - [任务 5][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[WARN ] 2024-09-04 06:19:59.335 - [任务 5][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 17410, message: Caused by: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:855)
	oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-04 06:45:10.734 - [任务 5][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-09-04 06:45:10.734 - [任务 5][Oracle] - Log Miner has been closed! 
[INFO ] 2024-09-04 06:45:41.010 - [任务 5][Oracle] - total start mining scn: 106677484 
[INFO ] 2024-09-04 06:45:42.228 - [任务 5][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-09-04 06:45:45.126 - [任务 5][Oracle] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-09-04 07:57:52.529 - [任务 5][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 17410, message: Caused by: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:855)
	oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-04 07:57:55.028 - [任务 5][Oracle] - Node Oracle[63c772bf-110a-47fc-8106-6615f081af97] running status set to false 
[INFO ] 2024-09-04 07:57:55.028 - [任务 5][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-09-04 07:57:55.028 - [任务 5][Oracle] - Log Miner has been closed! 
[INFO ] 2024-09-04 07:57:55.031 - [任务 5][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-09-04 07:57:55.084 - [任务 5][Oracle] - Log Miner has been closed! 
[INFO ] 2024-09-04 07:57:55.084 - [任务 5][Oracle] - Incremental sync completed 
[INFO ] 2024-09-04 08:08:03.567 - [任务 5][Oracle] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-119) has been closed. 
[ERROR] 2024-09-04 08:08:03.581 - [任务 5][Oracle] - java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-119) has been closed. <-- Error Message -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-119) has been closed.

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-119) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	...

<-- Full Stack Trace -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-119) has been closed.
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:437)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:769)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:790)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-119) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 24 more

[INFO ] 2024-09-04 08:08:03.684 - [任务 5] - Task initialization... 
[INFO ] 2024-09-04 08:08:03.893 - [任务 5] - Start task milestones: 66d68d52a40a37725da9d2f1(任务 5) 
[INFO ] 2024-09-04 08:08:04.502 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-04 08:08:04.706 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-04 08:08:05.297 - [任务 5][Kafka] - Node Kafka[67c2f244-4c4c-4cd1-820d-b2b59c5b0664] start preload schema,table counts: 1 
[INFO ] 2024-09-04 08:08:05.298 - [任务 5][Oracle] - Node Oracle[63c772bf-110a-47fc-8106-6615f081af97] start preload schema,table counts: 1 
[INFO ] 2024-09-04 08:08:05.302 - [任务 5][Kafka] - Node Kafka[67c2f244-4c4c-4cd1-820d-b2b59c5b0664] preload schema finished, cost 0 ms 
[INFO ] 2024-09-04 08:08:05.304 - [任务 5][Oracle] - Node Oracle[63c772bf-110a-47fc-8106-6615f081af97] preload schema finished, cost 0 ms 
[INFO ] 2024-09-04 08:08:08.364 - [任务 5][Oracle] - Source node "Oracle" read batch size: 100 
[INFO ] 2024-09-04 08:08:08.369 - [任务 5][Oracle] - Source node "Oracle" event queue capacity: 200 
[INFO ] 2024-09-04 08:08:08.387 - [任务 5][Oracle] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-04 08:08:08.387 - [任务 5][Oracle] - batch offset found: {"TESTBLOB":{"batch_read_connector_status":"OVER"}},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":106711084,"pendingScn":106711130,"timestamp":1725403552000,"hexScn":null,"fno":0} 
[INFO ] 2024-09-04 08:08:08.558 - [任务 5][Oracle] - Incremental sync starting... 
[INFO ] 2024-09-04 08:08:08.559 - [任务 5][Oracle] - Initial sync completed 
[INFO ] 2024-09-04 08:08:08.564 - [任务 5][Oracle] - Starting stream read, table list: [TESTBLOB], offset: {"sortString":null,"offsetValue":null,"lastScn":106711084,"pendingScn":106711130,"timestamp":1725403552000,"hexScn":null,"fno":0} 
[INFO ] 2024-09-04 08:08:08.640 - [任务 5][Kafka] - Node(Kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-04 08:08:08.644 - [任务 5][Kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-04 08:08:08.934 - [任务 5][Oracle] - total start mining scn: 106711084 
[INFO ] 2024-09-04 08:08:10.150 - [任务 5][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[WARN ] 2024-09-04 08:09:36.690 - [任务 5][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:855)
	oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-04 08:10:21.615 - [任务 5][Oracle] - Node Oracle[63c772bf-110a-47fc-8106-6615f081af97] running status set to false 
[INFO ] 2024-09-04 08:10:58.568 - [任务 5][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-09-04 08:10:58.577 - [任务 5][Oracle] - Log Miner has been closed! 
[INFO ] 2024-09-04 08:10:58.582 - [任务 5][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-09-04 08:10:58.583 - [任务 5][Oracle] - Log Miner has been closed! 
[INFO ] 2024-09-04 08:10:58.597 - [任务 5][Oracle] - Incremental sync completed 
[INFO ] 2024-09-04 08:10:58.617 - [任务 5][Oracle] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-1) has been closed. 
[ERROR] 2024-09-04 08:10:58.617 - [任务 5][Oracle] - java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-1) has been closed. <-- Error Message -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-1) has been closed.

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-1) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	...

<-- Full Stack Trace -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-1) has been closed.
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.getInstanceInfos(OracleCdcRunner.java:130)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.<init>(OracleCdcRunner.java:41)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:437)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:769)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:790)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-1) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 24 more

[INFO ] 2024-09-04 08:11:08.645 - [任务 5][Oracle] - PDK connector node stopped: HazelcastSourcePdkDataNode-63c772bf-110a-47fc-8106-6615f081af97 
[INFO ] 2024-09-04 08:11:08.646 - [任务 5][Oracle] - PDK connector node released: HazelcastSourcePdkDataNode-63c772bf-110a-47fc-8106-6615f081af97 
[INFO ] 2024-09-04 08:11:08.650 - [任务 5][Oracle] - Node Oracle[63c772bf-110a-47fc-8106-6615f081af97] schema data cleaned 
[INFO ] 2024-09-04 08:11:08.651 - [任务 5][Oracle] - Node Oracle[63c772bf-110a-47fc-8106-6615f081af97] monitor closed 
[INFO ] 2024-09-04 08:11:08.656 - [任务 5][Oracle] - Node Oracle[63c772bf-110a-47fc-8106-6615f081af97] close complete, cost 47046 ms 
[INFO ] 2024-09-04 08:11:08.656 - [任务 5][Kafka] - Node Kafka[67c2f244-4c4c-4cd1-820d-b2b59c5b0664] running status set to false 
[INFO ] 2024-09-04 08:11:08.686 - [任务 5][Kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode-67c2f244-4c4c-4cd1-820d-b2b59c5b0664 
[INFO ] 2024-09-04 08:11:08.686 - [任务 5][Kafka] - PDK connector node released: HazelcastTargetPdkDataNode-67c2f244-4c4c-4cd1-820d-b2b59c5b0664 
[INFO ] 2024-09-04 08:11:08.688 - [任务 5][Kafka] - Node Kafka[67c2f244-4c4c-4cd1-820d-b2b59c5b0664] schema data cleaned 
[INFO ] 2024-09-04 08:11:08.688 - [任务 5][Kafka] - Node Kafka[67c2f244-4c4c-4cd1-820d-b2b59c5b0664] monitor closed 
[INFO ] 2024-09-04 08:11:08.892 - [任务 5][Kafka] - Node Kafka[67c2f244-4c4c-4cd1-820d-b2b59c5b0664] close complete, cost 32 ms 
[INFO ] 2024-09-04 08:11:12.786 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-04 08:11:12.787 - [任务 5] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@162c8669 
[INFO ] 2024-09-04 08:11:12.930 - [任务 5] - Stop task milestones: 66d68d52a40a37725da9d2f1(任务 5)  
[INFO ] 2024-09-04 08:11:12.932 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-09-04 08:11:13.006 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-09-04 08:11:13.007 - [任务 5] - Remove memory task client succeed, task: 任务 5[66d68d52a40a37725da9d2f1] 
[INFO ] 2024-09-04 08:11:13.007 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[66d68d52a40a37725da9d2f1] 
[INFO ] 2024-09-04 08:11:53.278 - [任务 5] - Task initialization... 
[INFO ] 2024-09-04 08:11:53.279 - [任务 5] - Start task milestones: 66d68d52a40a37725da9d2f1(任务 5) 
[INFO ] 2024-09-04 08:11:53.430 - [任务 5] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-04 08:11:53.431 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-04 08:11:53.483 - [任务 5][Oracle] - Node Oracle[63c772bf-110a-47fc-8106-6615f081af97] start preload schema,table counts: 1 
[INFO ] 2024-09-04 08:11:53.487 - [任务 5][Kafka] - Node Kafka[67c2f244-4c4c-4cd1-820d-b2b59c5b0664] start preload schema,table counts: 1 
[INFO ] 2024-09-04 08:11:53.487 - [任务 5][Oracle] - Node Oracle[63c772bf-110a-47fc-8106-6615f081af97] preload schema finished, cost 0 ms 
[INFO ] 2024-09-04 08:11:53.488 - [任务 5][Kafka] - Node Kafka[67c2f244-4c4c-4cd1-820d-b2b59c5b0664] preload schema finished, cost 0 ms 
[INFO ] 2024-09-04 08:11:54.404 - [任务 5][Kafka] - Node(Kafka) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-04 08:11:54.404 - [任务 5][Kafka] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-04 08:11:57.417 - [任务 5][Kafka] - The table TESTBLOB has already exist. 
[INFO ] 2024-09-04 08:11:57.429 - [任务 5][Oracle] - Source node "Oracle" read batch size: 100 
[INFO ] 2024-09-04 08:11:57.430 - [任务 5][Oracle] - Source node "Oracle" event queue capacity: 200 
[INFO ] 2024-09-04 08:11:57.430 - [任务 5][Oracle] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-04 08:11:57.680 - [任务 5][Oracle] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":106728596,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-09-04 08:11:57.746 - [任务 5][Oracle] - Initial sync started 
[INFO ] 2024-09-04 08:11:57.750 - [任务 5][Oracle] - Starting batch read, table name: TESTBLOB 
[INFO ] 2024-09-04 08:11:57.773 - [任务 5][Oracle] - Table TESTBLOB is going to be initial synced 
[INFO ] 2024-09-04 08:11:57.780 - [任务 5][Oracle] - batchRead, splitSql[3]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=2 
[INFO ] 2024-09-04 08:11:57.783 - [任务 5][Oracle] - batchRead, splitSql[1]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=0 
[INFO ] 2024-09-04 08:11:57.783 - [任务 5][Oracle] - batchRead, splitSql[4]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=3 
[INFO ] 2024-09-04 08:11:57.783 - [任务 5][Oracle] - batchRead, splitSql[2]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=1 
[INFO ] 2024-09-04 08:11:57.875 - [任务 5][Oracle] - Query table 'TESTBLOB' counts: 20 
[INFO ] 2024-09-04 08:11:57.875 - [任务 5][Oracle] - batchRead, splitSql[5]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=4 
[INFO ] 2024-09-04 08:11:57.875 - [任务 5][Oracle] - batchRead, splitSql[6]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=5 
[INFO ] 2024-09-04 08:11:57.911 - [任务 5][Oracle] - batchRead, splitSql[7]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=6 
[INFO ] 2024-09-04 08:11:57.911 - [任务 5][Oracle] - batchRead, splitSql[8]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=7 
[INFO ] 2024-09-04 08:11:57.947 - [任务 5][Oracle] - batchRead, splitSql[9]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=8 
[INFO ] 2024-09-04 08:11:57.947 - [任务 5][Oracle] - batchRead, splitSql[10]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=9 
[INFO ] 2024-09-04 08:11:57.976 - [任务 5][Oracle] - batchRead, splitSql[11]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=10 
[INFO ] 2024-09-04 08:11:57.976 - [任务 5][Oracle] - batchRead, splitSql[12]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=11 
[INFO ] 2024-09-04 08:11:58.000 - [任务 5][Oracle] - batchRead, splitSql[13]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=12 
[INFO ] 2024-09-04 08:11:58.001 - [任务 5][Oracle] - batchRead, splitSql[15]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=14 
[INFO ] 2024-09-04 08:11:58.017 - [任务 5][Oracle] - batchRead, splitSql[14]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=13 
[INFO ] 2024-09-04 08:11:58.017 - [任务 5][Oracle] - batchRead, splitSql[16]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=15 
[INFO ] 2024-09-04 08:11:58.039 - [任务 5][Oracle] - batchRead, splitSql[17]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=16 
[INFO ] 2024-09-04 08:11:58.039 - [任务 5][Oracle] - batchRead, splitSql[19]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=18 
[INFO ] 2024-09-04 08:11:58.049 - [任务 5][Oracle] - batchRead, splitSql[18]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=17 
[INFO ] 2024-09-04 08:11:58.049 - [任务 5][Oracle] - batchRead, splitSql[20]: SELECT "A1","A2" FROM "C##TAPDATA"."TESTBLOB" WHERE mod(ORA_HASH(ROWID),20)=19 
[INFO ] 2024-09-04 08:11:58.112 - [任务 5][Oracle] - Table [TESTBLOB] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-04 08:11:58.112 - [任务 5][Oracle] - Initial sync completed 
[INFO ] 2024-09-04 08:11:58.112 - [任务 5][Oracle] - Incremental sync starting... 
[INFO ] 2024-09-04 08:11:58.117 - [任务 5][Oracle] - Initial sync completed 
[INFO ] 2024-09-04 08:11:58.117 - [任务 5][Oracle] - Starting stream read, table list: [TESTBLOB], offset: {"sortString":null,"offsetValue":null,"lastScn":106728596,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-09-04 08:11:58.320 - [任务 5][Oracle] - total start mining scn: 106728596 
[INFO ] 2024-09-04 08:11:59.549 - [任务 5][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[WARN ] 2024-09-04 10:15:03.858 - [任务 5][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 17410, message: Caused by: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:855)
	oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-04 10:16:03.906 - [任务 5][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-09-04 10:16:03.906 - [任务 5][Oracle] - Log Miner has been closed! 
[INFO ] 2024-09-04 10:16:04.944 - [任务 5][Oracle] - total start mining scn: 106739832 
[INFO ] 2024-09-04 10:16:06.166 - [任务 5][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-09-04 10:16:14.551 - [任务 5][Oracle] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-09-04 10:44:46.111 - [任务 5][Oracle] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:855)
	oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-04 10:45:46.162 - [任务 5][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-09-04 10:45:46.162 - [任务 5][Oracle] - Log Miner has been closed! 
[INFO ] 2024-09-04 10:45:47.322 - [任务 5][Oracle] - total start mining scn: 106760162 
[INFO ] 2024-09-04 10:45:48.461 - [任务 5][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-09-04 10:45:57.230 - [任务 5][Oracle] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-04 13:00:27.293 - [任务 5][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_09_04/o1_mf_1_2559_mfhtc6ft_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-09-04 13:00:39.377 - [任务 5][Oracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-09-04 17:59:57.658 - [任务 5][Oracle] - Node Oracle[63c772bf-110a-47fc-8106-6615f081af97] running status set to false 
[INFO ] 2024-09-04 17:59:57.663 - [任务 5][Oracle] - Log Miner is shutting down... 
[INFO ] 2024-09-04 17:59:57.663 - [任务 5][Oracle] - Log Miner has been closed! 
[INFO ] 2024-09-04 17:59:57.675 - [任务 5][Oracle] - Incremental sync completed 
[INFO ] 2024-09-04 17:59:57.677 - [任务 5][Oracle] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 17002): java.io.InterruptedIOException: Socket read interrupted 
[ERROR] 2024-09-04 17:59:57.698 - [任务 5][Oracle] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:444)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:769)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:790)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:780)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:669)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:90)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-09-04 17:59:57.700 - [任务 5][Oracle] - PDK connector node stopped: HazelcastSourcePdkDataNode-63c772bf-110a-47fc-8106-6615f081af97 
[INFO ] 2024-09-04 17:59:57.700 - [任务 5][Oracle] - PDK connector node released: HazelcastSourcePdkDataNode-63c772bf-110a-47fc-8106-6615f081af97 
[INFO ] 2024-09-04 17:59:57.700 - [任务 5][Oracle] - Node Oracle[63c772bf-110a-47fc-8106-6615f081af97] schema data cleaned 
[INFO ] 2024-09-04 17:59:57.701 - [任务 5][Oracle] - Node Oracle[63c772bf-110a-47fc-8106-6615f081af97] monitor closed 
[INFO ] 2024-09-04 17:59:57.701 - [任务 5][Oracle] - Node Oracle[63c772bf-110a-47fc-8106-6615f081af97] close complete, cost 65 ms 
[INFO ] 2024-09-04 17:59:57.723 - [任务 5][Kafka] - Node Kafka[67c2f244-4c4c-4cd1-820d-b2b59c5b0664] running status set to false 
[INFO ] 2024-09-04 17:59:57.723 - [任务 5][Kafka] - PDK connector node stopped: HazelcastTargetPdkDataNode-67c2f244-4c4c-4cd1-820d-b2b59c5b0664 
[INFO ] 2024-09-04 17:59:57.723 - [任务 5][Kafka] - PDK connector node released: HazelcastTargetPdkDataNode-67c2f244-4c4c-4cd1-820d-b2b59c5b0664 
[INFO ] 2024-09-04 17:59:57.723 - [任务 5][Kafka] - Node Kafka[67c2f244-4c4c-4cd1-820d-b2b59c5b0664] schema data cleaned 
[INFO ] 2024-09-04 17:59:57.930 - [任务 5][Kafka] - Node Kafka[67c2f244-4c4c-4cd1-820d-b2b59c5b0664] monitor closed 
[INFO ] 2024-09-04 17:59:57.931 - [任务 5][Kafka] - Node Kafka[67c2f244-4c4c-4cd1-820d-b2b59c5b0664] close complete, cost 23 ms 
[INFO ] 2024-09-04 17:59:58.993 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-04 17:59:58.993 - [任务 5] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2551fe57 
[INFO ] 2024-09-04 17:59:59.111 - [任务 5] - Stop task milestones: 66d68d52a40a37725da9d2f1(任务 5)  
[INFO ] 2024-09-04 17:59:59.126 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-09-04 17:59:59.126 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-09-04 17:59:59.144 - [任务 5] - Remove memory task client succeed, task: 任务 5[66d68d52a40a37725da9d2f1] 
[INFO ] 2024-09-04 17:59:59.147 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[66d68d52a40a37725da9d2f1] 
