[TRACE] 2025-05-16 09:59:07.843 - [任务 56] - Task initialization... 
[TRACE] 2025-05-16 09:59:08.044 - [任务 56] - Start task milestones: 68269bb080ae084221b15991(任务 56) 
[INFO ] 2025-05-16 09:59:08.214 - [任务 56] - Loading table structure completed 
[TRACE] 2025-05-16 09:59:08.214 - [任务 56] - Node performs snapshot read asynchronously 
[TRACE] 2025-05-16 09:59:08.393 - [任务 56] - The engine receives 任务 56 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-05-16 09:59:08.495 - [任务 56] - Task started 
[TRACE] 2025-05-16 09:59:08.608 - [任务 56][COMdb] - Node COMdb[58d719e6-9fad-483b-9583-c76c60bbd30b] start preload schema,table counts: 1 
[TRACE] 2025-05-16 09:59:08.611 - [任务 56][PG] - Node PG[5a4fa5c6-a63a-48fe-b081-8d823e37c5b3] start preload schema,table counts: 1 
[TRACE] 2025-05-16 09:59:08.611 - [任务 56][COMdb] - Node COMdb[58d719e6-9fad-483b-9583-c76c60bbd30b] preload schema finished, cost 0 ms 
[TRACE] 2025-05-16 09:59:08.611 - [任务 56][PG] - Node PG[5a4fa5c6-a63a-48fe-b081-8d823e37c5b3] preload schema finished, cost 0 ms 
[INFO ] 2025-05-16 09:59:09.401 - [任务 56][PG] - Sink connector(PG) initialization completed 
[TRACE] 2025-05-16 09:59:09.401 - [任务 56][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-05-16 09:59:09.401 - [任务 56][PG] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2025-05-16 09:59:09.422 - [任务 56][COMdb] - Source connector(COMdb) initialization completed 
[TRACE] 2025-05-16 09:59:09.422 - [任务 56][COMdb] - Source node "COMdb" read batch size: 100 
[TRACE] 2025-05-16 09:59:09.422 - [任务 56][COMdb] - Source node "COMdb" event queue capacity: 200 
[TRACE] 2025-05-16 09:59:09.422 - [任务 56][COMdb] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-05-16 09:59:09.533 - [任务 56][PG] - Apply table structure to target database 
[INFO ] 2025-05-16 09:59:09.533 - [任务 56][COMdb] - Starting batch read from 1 tables 
[TRACE] 2025-05-16 09:59:09.540 - [任务 56][COMdb] - Initial sync started 
[INFO ] 2025-05-16 09:59:09.540 - [任务 56][COMdb] - Starting batch read from table: corp_test 
[TRACE] 2025-05-16 09:59:09.540 - [任务 56][COMdb] - Table corp_test is going to be initial synced 
[INFO ] 2025-05-16 09:59:09.674 - [任务 56][COMdb] - Table corp_test has been completed batch read 
[TRACE] 2025-05-16 09:59:09.674 - [任务 56][COMdb] - Query snapshot row size completed: COMdb(58d719e6-9fad-483b-9583-c76c60bbd30b) 
[TRACE] 2025-05-16 09:59:09.682 - [任务 56][COMdb] - Initial sync completed 
[INFO ] 2025-05-16 09:59:09.683 - [任务 56][COMdb] - Batch read completed. 
[INFO ] 2025-05-16 09:59:09.684 - [任务 56][COMdb] - Task run completed 
[WARN ] 2025-05-16 09:59:10.491 - [任务 56][PG] - Found sync stage is null when flush sync progress, event: TapdataEvent{eventId=68269bedbf48d302f1363594, syncStage=INITIAL_SYNC, tapEvent=null, nodeIds=[58d719e6-9fad-483b-9583-c76c60bbd30b], sourceTime=null, sourceSerialNo=null}[com.tapdata.entity.TapdataCompleteTableSnapshotEvent] 
[TRACE] 2025-05-16 09:59:10.491 - [任务 56][PG] - Process after table "corp_test" initial sync finished, cost: 14 ms 
[INFO ] 2025-05-16 09:59:10.602 - [任务 56][PG] - Process after all table(s) initial sync are finished，table number: 1 
[TRACE] 2025-05-16 09:59:10.603 - [任务 56][COMdb] - Node COMdb[58d719e6-9fad-483b-9583-c76c60bbd30b] running status set to false 
[TRACE] 2025-05-16 09:59:10.626 - [任务 56][PG] - Node PG[5a4fa5c6-a63a-48fe-b081-8d823e37c5b3] running status set to false 
[TRACE] 2025-05-16 09:59:10.626 - [任务 56][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode_5a4fa5c6-a63a-48fe-b081-8d823e37c5b3_1747360749237 
[TRACE] 2025-05-16 09:59:10.626 - [任务 56][PG] - PDK connector node released: HazelcastTargetPdkDataNode_5a4fa5c6-a63a-48fe-b081-8d823e37c5b3_1747360749237 
[TRACE] 2025-05-16 09:59:10.626 - [任务 56][PG] - Node PG[5a4fa5c6-a63a-48fe-b081-8d823e37c5b3] schema data cleaned 
[TRACE] 2025-05-16 09:59:10.626 - [任务 56][PG] - Node PG[5a4fa5c6-a63a-48fe-b081-8d823e37c5b3] monitor closed 
[TRACE] 2025-05-16 09:59:10.731 - [任务 56][PG] - Node PG[5a4fa5c6-a63a-48fe-b081-8d823e37c5b3] close complete, cost 8 ms 
[TRACE] 2025-05-16 09:59:10.731 - [任务 56][COMdb] - PDK connector node stopped: HazelcastSourcePdkDataNode_58d719e6-9fad-483b-9583-c76c60bbd30b_1747360749355 
[TRACE] 2025-05-16 09:59:10.731 - [任务 56][COMdb] - PDK connector node released: HazelcastSourcePdkDataNode_58d719e6-9fad-483b-9583-c76c60bbd30b_1747360749355 
[TRACE] 2025-05-16 09:59:10.732 - [任务 56][COMdb] - Node COMdb[58d719e6-9fad-483b-9583-c76c60bbd30b] schema data cleaned 
[TRACE] 2025-05-16 09:59:10.732 - [任务 56][COMdb] - Node COMdb[58d719e6-9fad-483b-9583-c76c60bbd30b] monitor closed 
[TRACE] 2025-05-16 09:59:10.936 - [任务 56][COMdb] - Node COMdb[58d719e6-9fad-483b-9583-c76c60bbd30b] close complete, cost 134 ms 
[TRACE] 2025-05-16 09:59:16.994 - [任务 56] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-16 09:59:17.000 - [任务 56] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6c7a4390 
[TRACE] 2025-05-16 09:59:17.000 - [任务 56] - Stop task milestones: 68269bb080ae084221b15991(任务 56)  
[TRACE] 2025-05-16 09:59:17.134 - [任务 56] - Stopped task aspect(s) 
[TRACE] 2025-05-16 09:59:17.134 - [任务 56] - Snapshot order controller have been removed 
[INFO ] 2025-05-16 09:59:17.135 - [任务 56] - Task stopped. 
[TRACE] 2025-05-16 09:59:23.812 - [任务 56] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-05-16 09:59:23.815 - [任务 56] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6c7a4390 
[TRACE] 2025-05-16 09:59:23.816 - [任务 56] - Stopped task aspect(s) 
[INFO ] 2025-05-16 09:59:23.816 - [任务 56] - Task stopped. 
[TRACE] 2025-05-16 09:59:23.879 - [任务 56] - Remove memory task client succeed, task: 任务 56[68269bb080ae084221b15991] 
[TRACE] 2025-05-16 09:59:23.879 - [任务 56] - Destroy memory task client cache succeed, task: 任务 56[68269bb080ae084221b15991] 
