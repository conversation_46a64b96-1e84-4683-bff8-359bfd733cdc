[INFO ] 2024-06-25 16:45:06.672 - [任务 31 - Copy] - Start task milestones: 667a837b10fc5c6259c57671(任务 31 - Copy) 
[INFO ] 2024-06-25 16:45:06.672 - [任务 31 - Copy] - Task initialization... 
[INFO ] 2024-06-25 16:45:06.842 - [任务 31 - Co<PERSON>] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-06-25 16:45:06.882 - [任务 31 - Copy] - The engine receives 任务 31 - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 16:45:06.965 - [任务 31 - Copy][SouceMysql] - Node SouceMysql[ad3cbdb3-dd60-44d9-9561-5bd835320bfb] start preload schema,table counts: 8 
[INFO ] 2024-06-25 16:45:06.965 - [任务 31 - Co<PERSON>][SourceMongo] - Node <PERSON>[393e09aa-5cc8-4459-9829-b42a53d6ce4e] start preload schema,table counts: 8 
[INFO ] 2024-06-25 16:45:06.965 - [任务 31 - Copy][SouceMysql] - Node SouceMysql[ad3cbdb3-dd60-44d9-9561-5bd835320bfb] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 16:45:06.965 - [任务 31 - Copy][SourceMongo] - Node SourceMongo[393e09aa-5cc8-4459-9829-b42a53d6ce4e] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 16:45:07.615 - [任务 31 - Copy][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 16:45:07.615 - [任务 31 - Copy][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 16:45:07.793 - [任务 31 - Copy][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-06-25 16:45:07.807 - [任务 31 - Copy][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-06-25 16:45:07.814 - [任务 31 - Copy][SouceMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-25 16:45:07.814 - [任务 31 - Copy][SouceMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000031","position":1060990946,"gtidSet":""} 
[INFO ] 2024-06-25 16:45:07.814 - [任务 31 - Copy] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-06-25 16:45:07.871 - [任务 31 - Copy][SouceMysql] - Initial sync started 
[INFO ] 2024-06-25 16:45:07.871 - [任务 31 - Copy][SouceMysql] - Starting batch read, table name: Inspectwimtest3, offset: null 
[INFO ] 2024-06-25 16:45:07.883 - [任务 31 - Copy][SouceMysql] - Table Inspectwimtest3 is going to be initial synced 
[INFO ] 2024-06-25 16:45:07.883 - [任务 31 - Copy][SouceMysql] - Table [Inspectwimtest3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:45:07.887 - [任务 31 - Copy][SouceMysql] - Query table 'Inspectwimtest3' counts: 1 
[INFO ] 2024-06-25 16:45:07.899 - [任务 31 - Copy][SouceMysql] - Starting batch read, table name: Inspectwimtest4, offset: null 
[INFO ] 2024-06-25 16:45:07.903 - [任务 31 - Copy][SouceMysql] - Table Inspectwimtest4 is going to be initial synced 
[INFO ] 2024-06-25 16:45:08.106 - [任务 31 - Copy][SouceMysql] - Query table 'Inspectwimtest4' counts: 1053 
[INFO ] 2024-06-25 16:45:08.339 - [任务 31 - Copy][SouceMysql] - Table [Inspectwimtest4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:45:08.341 - [任务 31 - Copy][SouceMysql] - Starting batch read, table name: Inspectwimtest1, offset: null 
[INFO ] 2024-06-25 16:45:08.341 - [任务 31 - Copy][SouceMysql] - Table Inspectwimtest1 is going to be initial synced 
[INFO ] 2024-06-25 16:45:08.460 - [任务 31 - Copy][SouceMysql] - Query table 'Inspectwimtest1' counts: 1053 
[INFO ] 2024-06-25 16:45:08.460 - [任务 31 - Copy][SouceMysql] - Table [Inspectwimtest1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:45:08.460 - [任务 31 - Copy][SouceMysql] - Starting batch read, table name: Inspectwimtest2, offset: null 
[INFO ] 2024-06-25 16:45:08.460 - [任务 31 - Copy][SouceMysql] - Table Inspectwimtest2 is going to be initial synced 
[INFO ] 2024-06-25 16:45:08.558 - [任务 31 - Copy][SouceMysql] - Query table 'Inspectwimtest2' counts: 1075 
[INFO ] 2024-06-25 16:45:08.558 - [任务 31 - Copy][SouceMysql] - Table [Inspectwimtest2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:45:08.559 - [任务 31 - Copy][SouceMysql] - Starting batch read, table name: Inspectwimtest7, offset: null 
[INFO ] 2024-06-25 16:45:08.559 - [任务 31 - Copy][SouceMysql] - Table Inspectwimtest7 is going to be initial synced 
[INFO ] 2024-06-25 16:45:08.560 - [任务 31 - Copy][SouceMysql] - Table [Inspectwimtest7] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:45:08.563 - [任务 31 - Copy][SouceMysql] - Query table 'Inspectwimtest7' counts: 0 
[INFO ] 2024-06-25 16:45:08.563 - [任务 31 - Copy][SouceMysql] - Starting batch read, table name: Inspectwimtest8, offset: null 
[INFO ] 2024-06-25 16:45:08.563 - [任务 31 - Copy][SouceMysql] - Table Inspectwimtest8 is going to be initial synced 
[INFO ] 2024-06-25 16:45:08.563 - [任务 31 - Copy][SouceMysql] - Table [Inspectwimtest8] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:45:08.563 - [任务 31 - Copy][SouceMysql] - Query table 'Inspectwimtest8' counts: 0 
[INFO ] 2024-06-25 16:45:08.563 - [任务 31 - Copy][SouceMysql] - Starting batch read, table name: Inspectwimtest5, offset: null 
[INFO ] 2024-06-25 16:45:08.563 - [任务 31 - Copy][SouceMysql] - Table Inspectwimtest5 is going to be initial synced 
[INFO ] 2024-06-25 16:45:08.563 - [任务 31 - Copy][SouceMysql] - Table [Inspectwimtest5] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:45:08.564 - [任务 31 - Copy][SouceMysql] - Query table 'Inspectwimtest5' counts: 1 
[INFO ] 2024-06-25 16:45:08.564 - [任务 31 - Copy][SouceMysql] - Starting batch read, table name: Inspectwimtest6, offset: null 
[INFO ] 2024-06-25 16:45:08.564 - [任务 31 - Copy][SouceMysql] - Table Inspectwimtest6 is going to be initial synced 
[INFO ] 2024-06-25 16:45:08.564 - [任务 31 - Copy][SouceMysql] - Table [Inspectwimtest6] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:45:08.567 - [任务 31 - Copy][SouceMysql] - Query table 'Inspectwimtest6' counts: 0 
[INFO ] 2024-06-25 16:45:08.567 - [任务 31 - Copy][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:45:08.567 - [任务 31 - Copy][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 16:45:08.567 - [任务 31 - Copy][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:45:08.567 - [任务 31 - Copy][SouceMysql] - Starting stream read, table list: [Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest7, Inspectwimtest8, Inspectwimtest5, Inspectwimtest6], offset: {"filename":"binlog.000031","position":1060990946,"gtidSet":""} 
[INFO ] 2024-06-25 16:45:08.590 - [任务 31 - Copy][SouceMysql] - Starting mysql cdc, server name: f7c1b728-b9ff-4e83-ba96-24b33e837700 
[INFO ] 2024-06-25 16:45:08.590 - [任务 31 - Copy][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 159128277
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f7c1b728-b9ff-4e83-ba96-24b33e837700
  database.port: 3306
  threadName: Debezium-Mysql-Connector-f7c1b728-b9ff-4e83-ba96-24b33e837700
  database.hostname: localhost
  database.password: ********
  name: f7c1b728-b9ff-4e83-ba96-24b33e837700
  pdk.offset.string: {"name":"f7c1b728-b9ff-4e83-ba96-24b33e837700","offset":{"{\"server\":\"f7c1b728-b9ff-4e83-ba96-24b33e837700\"}":"{\"file\":\"binlog.000031\",\"pos\":1060990946,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Inspectwimtest3,test.Inspectwimtest4,test.Inspectwimtest1,test.Inspectwimtest2,test.Inspectwimtest7,test.Inspectwimtest8,test.Inspectwimtest5,test.Inspectwimtest6
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 16:45:08.796 - [任务 31 - Copy][SouceMysql] - Connector Mysql incremental start succeed, tables: [Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest7, Inspectwimtest8, Inspectwimtest5, Inspectwimtest6], data change syncing 
[INFO ] 2024-06-25 16:46:17.282 - [任务 31 - Copy][SouceMysql] - Found new table(s): [Inspectwimtest9] 
[INFO ] 2024-06-25 16:46:17.286 - [任务 31 - Copy][SouceMysql] - Load new table(s) schema finished, loaded schema count: 1 
[INFO ] 2024-06-25 16:46:17.286 - [任务 31 - Copy][SouceMysql] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapCreateTableEvent@63669e0e: {"table":{"comment":"","id":"Inspectwimtest9","indexList":[{"indexFields":[{"fieldAsc":true,"name":"id"}],"name":"PRIMARY","primary":true,"unique":true}],"maxPKPos":1,"maxPos":2,"name":"Inspectwimtest9","nameFieldMap":{"id":{"autoInc":false,"comment":"","dataType":"int","name":"id","nullable":false,"partitionKey":false,"pos":1,"primaryKey":true,"primaryKeyPos":1,"tapType":{"bit":32,"maxValue":2147483647,"minValue":-2147483648,"precision":10,"type":8},"virtual":false},"name":{"autoInc":false,"comment":"","dataType":"varchar(20)","name":"name","nullable":true,"partitionKey":false,"pos":2,"primaryKey":false,"primaryKeyPos":0,"tapType":{"byteRatio":3,"bytes":20,"defaultValue":1,"type":10},"virtual":false}},"partitionIndex":{"indexFields":[{"fieldAsc":true,"name":"id"}],"indexMap":{"id":{"fieldAsc":true,"name":"id"}},"unique":true}},"tableId":"Inspectwimtest9","type":206} 
[INFO ] 2024-06-25 16:46:17.339 - [任务 31 - Copy][SouceMysql] - Create new table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_Inspectwimtest9_667413fd7b5e1f6c3b139e78_667a837b10fc5c6259c57671 
[INFO ] 2024-06-25 16:46:17.373 - [任务 31 - Copy][SouceMysql] - Create new table schema transform finished: TapTable id Inspectwimtest9 name Inspectwimtest9 storageEngine null charset null number of fields 2 
[INFO ] 2024-06-25 16:46:17.427 - [任务 31 - Copy][SouceMysql] - It is detected that the snapshot reading has ended, and the reading thread will be restarted 
[INFO ] 2024-06-25 16:46:17.428 - [任务 31 - Copy][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-06-25 16:46:17.428 - [任务 31 - Copy][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-06-25 16:46:17.429 - [任务 31 - Copy][SouceMysql] - Incremental sync completed 
[INFO ] 2024-06-25 16:46:17.803 - [任务 31 - Copy][SouceMysql] - Starting batch read, table name: Inspectwimtest9, offset: null 
[INFO ] 2024-06-25 16:46:17.804 - [任务 31 - Copy][SouceMysql] - Table Inspectwimtest9 is going to be initial synced 
[INFO ] 2024-06-25 16:46:17.860 - [任务 31 - Copy][SouceMysql] - Query table 'Inspectwimtest9' counts: 0 
[INFO ] 2024-06-25 16:46:17.863 - [任务 31 - Copy][SouceMysql] - Table [Inspectwimtest9] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-25 16:46:17.866 - [任务 31 - Copy][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:46:17.866 - [任务 31 - Copy][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 16:46:17.866 - [任务 31 - Copy][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 16:46:17.880 - [任务 31 - Copy][SouceMysql] - Starting stream read, table list: [Inspectwimtest9, Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest7, Inspectwimtest8, Inspectwimtest5, Inspectwimtest6], offset: {"name":"f7c1b728-b9ff-4e83-ba96-24b33e837700","offset":{"{\"server\":\"f7c1b728-b9ff-4e83-ba96-24b33e837700\"}":"{\"ts_sec\":1719305108,\"file\":\"binlog.000031\",\"pos\":1060991226,\"server_id\":1}"}} 
[INFO ] 2024-06-25 16:46:17.883 - [任务 31 - Copy][SouceMysql] - Starting mysql cdc, server name: f7c1b728-b9ff-4e83-ba96-24b33e837700 
[INFO ] 2024-06-25 16:46:17.966 - [任务 31 - Copy][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1604252253
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f7c1b728-b9ff-4e83-ba96-24b33e837700
  database.port: 3306
  threadName: Debezium-Mysql-Connector-f7c1b728-b9ff-4e83-ba96-24b33e837700
  database.hostname: localhost
  database.password: ********
  name: f7c1b728-b9ff-4e83-ba96-24b33e837700
  pdk.offset.string: {"name":"f7c1b728-b9ff-4e83-ba96-24b33e837700","offset":{"{\"server\":\"f7c1b728-b9ff-4e83-ba96-24b33e837700\"}":"{\"ts_sec\":1719305108,\"file\":\"binlog.000031\",\"pos\":1060991226,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Inspectwimtest9,test.Inspectwimtest3,test.Inspectwimtest4,test.Inspectwimtest1,test.Inspectwimtest2,test.Inspectwimtest7,test.Inspectwimtest8,test.Inspectwimtest5,test.Inspectwimtest6
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 16:46:17.966 - [任务 31 - Copy][SouceMysql] - Connector Mysql incremental start succeed, tables: [Inspectwimtest9, Inspectwimtest3, Inspectwimtest4, Inspectwimtest1, Inspectwimtest2, Inspectwimtest7, Inspectwimtest8, Inspectwimtest5, Inspectwimtest6], data change syncing 
[INFO ] 2024-06-25 16:47:13.791 - [任务 31 - Copy] - Stop task milestones: 667a837b10fc5c6259c57671(任务 31 - Copy)  
[INFO ] 2024-06-25 16:47:14.072 - [任务 31 - Copy][SouceMysql] - Node SouceMysql[ad3cbdb3-dd60-44d9-9561-5bd835320bfb] running status set to false 
[INFO ] 2024-06-25 16:47:14.079 - [任务 31 - Copy][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-06-25 16:47:14.082 - [任务 31 - Copy][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-06-25 16:47:14.088 - [任务 31 - Copy][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-ad3cbdb3-dd60-44d9-9561-5bd835320bfb 
[INFO ] 2024-06-25 16:47:14.089 - [任务 31 - Copy][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-ad3cbdb3-dd60-44d9-9561-5bd835320bfb 
[INFO ] 2024-06-25 16:47:14.089 - [任务 31 - Copy][SouceMysql] - Node SouceMysql[ad3cbdb3-dd60-44d9-9561-5bd835320bfb] schema data cleaned 
[INFO ] 2024-06-25 16:47:14.089 - [任务 31 - Copy][SouceMysql] - Node SouceMysql[ad3cbdb3-dd60-44d9-9561-5bd835320bfb] monitor closed 
[INFO ] 2024-06-25 16:47:14.089 - [任务 31 - Copy][SouceMysql] - Node SouceMysql[ad3cbdb3-dd60-44d9-9561-5bd835320bfb] close complete, cost 34 ms 
[INFO ] 2024-06-25 16:47:14.100 - [任务 31 - Copy][SourceMongo] - Node SourceMongo[393e09aa-5cc8-4459-9829-b42a53d6ce4e] running status set to false 
[INFO ] 2024-06-25 16:47:14.100 - [任务 31 - Copy][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-393e09aa-5cc8-4459-9829-b42a53d6ce4e 
[INFO ] 2024-06-25 16:47:14.100 - [任务 31 - Copy][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-393e09aa-5cc8-4459-9829-b42a53d6ce4e 
[INFO ] 2024-06-25 16:47:14.100 - [任务 31 - Copy][SourceMongo] - Node SourceMongo[393e09aa-5cc8-4459-9829-b42a53d6ce4e] schema data cleaned 
[INFO ] 2024-06-25 16:47:14.100 - [任务 31 - Copy][SourceMongo] - Node SourceMongo[393e09aa-5cc8-4459-9829-b42a53d6ce4e] monitor closed 
[INFO ] 2024-06-25 16:47:14.301 - [任务 31 - Copy][SourceMongo] - Node SourceMongo[393e09aa-5cc8-4459-9829-b42a53d6ce4e] close complete, cost 10 ms 
[INFO ] 2024-06-25 16:47:17.194 - [任务 31 - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 16:47:17.195 - [任务 31 - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4813b1a4 
[INFO ] 2024-06-25 16:47:17.195 - [任务 31 - Copy] - Stopped task aspect(s) 
[INFO ] 2024-06-25 16:47:17.195 - [任务 31 - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 16:47:17.225 - [任务 31 - Copy] - Remove memory task client succeed, task: 任务 31 - Copy[667a837b10fc5c6259c57671] 
[INFO ] 2024-06-25 16:47:17.227 - [任务 31 - Copy] - Destroy memory task client cache succeed, task: 任务 31 - Copy[667a837b10fc5c6259c57671] 
