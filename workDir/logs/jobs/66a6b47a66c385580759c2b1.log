[INFO ] 2024-07-29 05:14:04.385 - [TestCk<PERSON>rra<PERSON> - Co<PERSON>] - Task initialization... 
[INFO ] 2024-07-29 05:14:04.386 - [TestCkArray - Copy] - Start task milestones: 66a6b47a66c385580759c2b1(TestCkArray - Copy) 
[INFO ] 2024-07-29 05:14:04.564 - [TestCk<PERSON>rra<PERSON> - Co<PERSON>] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-29 05:14:04.564 - [TestCkArray - Copy] - The engine receives TestCkArray - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-29 05:14:04.623 - [TestCkArray - Copy][targetCKArray] - Node targetCKArray[7f308541-b3cf-4df4-88c1-63533f44ec1d] start preload schema,table counts: 1 
[INFO ] 2024-07-29 05:14:04.623 - [TestCkArray - Co<PERSON>][my_array_table] - Node my_array_table[c891de2b-7530-4f82-8845-c59b0153036d] start preload schema,table counts: 1 
[INFO ] 2024-07-29 05:14:04.624 - [TestCkArray - Copy][targetCKArray] - Node targetCKArray[7f308541-b3cf-4df4-88c1-63533f44ec1d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 05:14:04.624 - [TestCkArray - Copy][my_array_table] - Node my_array_table[c891de2b-7530-4f82-8845-c59b0153036d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 05:14:05.375 - [TestCkArray - Copy][my_array_table] - Source node "my_array_table" read batch size: 100 
[INFO ] 2024-07-29 05:14:05.376 - [TestCkArray - Copy][my_array_table] - Source node "my_array_table" event queue capacity: 200 
[INFO ] 2024-07-29 05:14:05.376 - [TestCkArray - Copy][my_array_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-29 05:14:05.376 - [TestCkArray - Copy][my_array_table] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-29 05:14:05.376 - [TestCkArray - Copy][my_array_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-29 05:14:05.450 - [TestCkArray - Copy][my_array_table] - Initial sync started 
[INFO ] 2024-07-29 05:14:05.450 - [TestCkArray - Copy][my_array_table] - Starting batch read, table name: my_array_table, offset: null 
[INFO ] 2024-07-29 05:14:05.451 - [TestCkArray - Copy][my_array_table] - Table my_array_table is going to be initial synced 
[INFO ] 2024-07-29 05:14:05.468 - [TestCkArray - Copy][targetCKArray] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-29 05:14:05.468 - [TestCkArray - Copy][my_array_table] - Table [my_array_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-29 05:14:05.485 - [TestCkArray - Copy][my_array_table] - Query table 'my_array_table' counts: 3 
[INFO ] 2024-07-29 05:14:05.486 - [TestCkArray - Copy][my_array_table] - Initial sync completed 
[WARN ] 2024-07-29 05:14:05.987 - [TestCkArray - Copy][targetCKArray] - Skip error failed:write javaBean error, fastjson version 1.2.83, class io.tapdata.entity.schema.value.TapRawValue, fieldName : arrayCol, write javaBean error, fastjson version 1.2.83, class ru.yandex.clickhouse.ClickHouseArray, fieldName : value 
[INFO ] 2024-07-29 05:14:45.746 - [TestCkArray - Copy][my_array_table] - Node my_array_table[c891de2b-7530-4f82-8845-c59b0153036d] running status set to false 
[INFO ] 2024-07-29 05:14:45.746 - [TestCkArray - Copy][my_array_table] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-29 05:14:45.791 - [TestCkArray - Copy][my_array_table] - Clickhouse Optimize Table end 
[INFO ] 2024-07-29 05:14:45.792 - [TestCkArray - Copy][my_array_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-c891de2b-7530-4f82-8845-c59b0153036d 
[INFO ] 2024-07-29 05:14:45.792 - [TestCkArray - Copy][my_array_table] - PDK connector node released: HazelcastSourcePdkDataNode-c891de2b-7530-4f82-8845-c59b0153036d 
[INFO ] 2024-07-29 05:14:45.792 - [TestCkArray - Copy][my_array_table] - Node my_array_table[c891de2b-7530-4f82-8845-c59b0153036d] schema data cleaned 
[INFO ] 2024-07-29 05:14:45.792 - [TestCkArray - Copy][my_array_table] - Node my_array_table[c891de2b-7530-4f82-8845-c59b0153036d] monitor closed 
[INFO ] 2024-07-29 05:14:45.793 - [TestCkArray - Copy][my_array_table] - Node my_array_table[c891de2b-7530-4f82-8845-c59b0153036d] close complete, cost 51 ms 
[INFO ] 2024-07-29 05:14:45.793 - [TestCkArray - Copy][targetCKArray] - Node targetCKArray[7f308541-b3cf-4df4-88c1-63533f44ec1d] running status set to false 
[INFO ] 2024-07-29 05:14:45.813 - [TestCkArray - Copy][targetCKArray] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-29 05:14:45.813 - [TestCkArray - Copy][targetCKArray] - Clickhouse Optimize Table end 
[INFO ] 2024-07-29 05:14:45.824 - [TestCkArray - Copy][targetCKArray] - PDK connector node stopped: HazelcastTargetPdkDataNode-7f308541-b3cf-4df4-88c1-63533f44ec1d 
[INFO ] 2024-07-29 05:14:45.825 - [TestCkArray - Copy][targetCKArray] - PDK connector node released: HazelcastTargetPdkDataNode-7f308541-b3cf-4df4-88c1-63533f44ec1d 
[INFO ] 2024-07-29 05:14:45.825 - [TestCkArray - Copy][targetCKArray] - Node targetCKArray[7f308541-b3cf-4df4-88c1-63533f44ec1d] schema data cleaned 
[INFO ] 2024-07-29 05:14:45.825 - [TestCkArray - Copy][targetCKArray] - Node targetCKArray[7f308541-b3cf-4df4-88c1-63533f44ec1d] monitor closed 
[INFO ] 2024-07-29 05:14:46.026 - [TestCkArray - Copy][targetCKArray] - Node targetCKArray[7f308541-b3cf-4df4-88c1-63533f44ec1d] close complete, cost 32 ms 
[INFO ] 2024-07-29 05:14:46.713 - [TestCkArray - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-29 05:14:46.714 - [TestCkArray - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7f356e43 
[INFO ] 2024-07-29 05:14:46.854 - [TestCkArray - Copy] - Stop task milestones: 66a6b47a66c385580759c2b1(TestCkArray - Copy)  
[INFO ] 2024-07-29 05:14:46.854 - [TestCkArray - Copy] - Stopped task aspect(s) 
[INFO ] 2024-07-29 05:14:46.854 - [TestCkArray - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-07-29 05:14:46.890 - [TestCkArray - Copy] - Remove memory task client succeed, task: TestCkArray - Copy[66a6b47a66c385580759c2b1] 
[INFO ] 2024-07-29 05:14:46.891 - [TestCkArray - Copy] - Destroy memory task client cache succeed, task: TestCkArray - Copy[66a6b47a66c385580759c2b1] 
