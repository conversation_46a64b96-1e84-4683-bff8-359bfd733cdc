[INFO ] 2024-07-01 14:15:51.202 - [任务 38] - Start task milestones: 6682496fbb97eb19fc05e098(任务 38) 
[INFO ] 2024-07-01 14:15:51.203 - [任务 38] - Task initialization... 
[INFO ] 2024-07-01 14:15:51.468 - [任务 38] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-01 14:15:51.468 - [任务 38] - The engine receives 任务 38 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-01 14:15:51.525 - [任务 38][SourceMongo] - Node SourceMongo[2e6ff5b5-0770-4aa9-a959-4846459a5c47] start preload schema,table counts: 2 
[INFO ] 2024-07-01 14:15:51.526 - [任务 38][SouceMysql] - Node SouceMysql[bbdefc41-1496-416b-a105-5ef6b10993c7] start preload schema,table counts: 2 
[INFO ] 2024-07-01 14:15:51.528 - [任务 38][SourceMongo] - Node SourceMongo[2e6ff5b5-0770-4aa9-a959-4846459a5c47] preload schema finished, cost 1 ms 
[INFO ] 2024-07-01 14:15:51.529 - [任务 38][SouceMysql] - Node SouceMysql[bbdefc41-1496-416b-a105-5ef6b10993c7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 14:15:52.459 - [任务 38][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-01 14:15:52.468 - [任务 38][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-01 14:15:52.498 - [任务 38][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-07-01 14:15:52.499 - [任务 38][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-07-01 14:15:52.503 - [任务 38][SourceMongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-01 14:15:52.664 - [任务 38][SourceMongo] - batch offset found: {},stream offset found: {"cdcOffset":1719814552,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 14:15:52.734 - [任务 38][SourceMongo] - Initial sync started 
[INFO ] 2024-07-01 14:15:52.751 - [任务 38][SourceMongo] - Starting batch read, table name: CLAIM1, offset: null 
[INFO ] 2024-07-01 14:15:52.751 - [任务 38][SourceMongo] - Table CLAIM1 is going to be initial synced 
[INFO ] 2024-07-01 14:15:52.802 - [任务 38][SourceMongo] - Table [CLAIM1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 14:15:52.802 - [任务 38][SourceMongo] - Query table 'CLAIM1' counts: 0 
[INFO ] 2024-07-01 14:15:52.807 - [任务 38][SourceMongo] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-07-01 14:15:52.808 - [任务 38][SourceMongo] - Table CLAIM is going to be initial synced 
[INFO ] 2024-07-01 14:15:52.809 - [任务 38][SourceMongo] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-07-01 14:15:52.976 - [任务 38][SourceMongo] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 14:15:52.976 - [任务 38][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-01 14:15:52.978 - [任务 38][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-01 14:15:52.978 - [任务 38][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-01 14:15:52.983 - [任务 38][SourceMongo] - Starting stream read, table list: [CLAIM1, CLAIM], offset: {"cdcOffset":1719814552,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 14:15:52.983 - [任务 38][SourceMongo] - Connector MongoDB incremental start succeed, tables: [CLAIM1, CLAIM], data change syncing 
[INFO ] 2024-07-01 14:16:05.559 - [任务 38] - Stop task milestones: 6682496fbb97eb19fc05e098(任务 38)  
[INFO ] 2024-07-01 14:16:05.754 - [任务 38][SourceMongo] - Node SourceMongo[2e6ff5b5-0770-4aa9-a959-4846459a5c47] running status set to false 
[INFO ] 2024-07-01 14:16:05.755 - [任务 38][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-2e6ff5b5-0770-4aa9-a959-4846459a5c47 
[INFO ] 2024-07-01 14:16:05.755 - [任务 38][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-2e6ff5b5-0770-4aa9-a959-4846459a5c47 
[INFO ] 2024-07-01 14:16:05.756 - [任务 38][SourceMongo] - Node SourceMongo[2e6ff5b5-0770-4aa9-a959-4846459a5c47] schema data cleaned 
[INFO ] 2024-07-01 14:16:05.757 - [任务 38][SourceMongo] - Node SourceMongo[2e6ff5b5-0770-4aa9-a959-4846459a5c47] monitor closed 
[INFO ] 2024-07-01 14:16:05.757 - [任务 38][SourceMongo] - Node SourceMongo[2e6ff5b5-0770-4aa9-a959-4846459a5c47] close complete, cost 37 ms 
[INFO ] 2024-07-01 14:16:05.786 - [任务 38][SouceMysql] - Node SouceMysql[bbdefc41-1496-416b-a105-5ef6b10993c7] running status set to false 
[INFO ] 2024-07-01 14:16:05.787 - [任务 38][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-bbdefc41-1496-416b-a105-5ef6b10993c7 
[INFO ] 2024-07-01 14:16:05.787 - [任务 38][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-bbdefc41-1496-416b-a105-5ef6b10993c7 
[INFO ] 2024-07-01 14:16:05.787 - [任务 38][SouceMysql] - Node SouceMysql[bbdefc41-1496-416b-a105-5ef6b10993c7] schema data cleaned 
[INFO ] 2024-07-01 14:16:05.788 - [任务 38][SouceMysql] - Node SouceMysql[bbdefc41-1496-416b-a105-5ef6b10993c7] monitor closed 
[INFO ] 2024-07-01 14:16:05.789 - [任务 38][SouceMysql] - Node SouceMysql[bbdefc41-1496-416b-a105-5ef6b10993c7] close complete, cost 31 ms 
[INFO ] 2024-07-01 14:16:07.008 - [任务 38] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-01 14:16:07.009 - [任务 38] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2462dcdb 
[INFO ] 2024-07-01 14:16:07.011 - [任务 38] - Stopped task aspect(s) 
[INFO ] 2024-07-01 14:16:07.011 - [任务 38] - Snapshot order controller have been removed 
[INFO ] 2024-07-01 14:16:07.033 - [任务 38] - Remove memory task client succeed, task: 任务 38[6682496fbb97eb19fc05e098] 
[INFO ] 2024-07-01 14:16:07.033 - [任务 38] - Destroy memory task client cache succeed, task: 任务 38[6682496fbb97eb19fc05e098] 
[INFO ] 2024-07-01 14:16:33.942 - [任务 38] - Start task milestones: 6682496fbb97eb19fc05e098(任务 38) 
[INFO ] 2024-07-01 14:16:34.147 - [任务 38] - Task initialization... 
[INFO ] 2024-07-01 14:16:34.203 - [任务 38] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-01 14:16:34.203 - [任务 38] - The engine receives 任务 38 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-01 14:16:34.249 - [任务 38][SourceMongo] - Node SourceMongo[2e6ff5b5-0770-4aa9-a959-4846459a5c47] start preload schema,table counts: 3 
[INFO ] 2024-07-01 14:16:34.249 - [任务 38][SouceMysql] - Node SouceMysql[bbdefc41-1496-416b-a105-5ef6b10993c7] start preload schema,table counts: 3 
[INFO ] 2024-07-01 14:16:34.250 - [任务 38][SourceMongo] - Node SourceMongo[2e6ff5b5-0770-4aa9-a959-4846459a5c47] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 14:16:34.250 - [任务 38][SouceMysql] - Node SouceMysql[bbdefc41-1496-416b-a105-5ef6b10993c7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-01 14:16:34.558 - [任务 38][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-07-01 14:16:34.558 - [任务 38][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-07-01 14:16:34.562 - [任务 38][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-01 14:16:34.637 - [任务 38][SourceMongo] - batch offset found: {"CLAIM1":{"batch_read_connector_offset":null,"batch_read_connector_status":"OVER"},"CLAIM":{"batch_read_connector_offset":{"sortKey":"_id","value":"657baffa62a1bacd962c870f","objectId":null},"batch_read_connector_status":"RUNNING"}},stream offset found: {"cdcOffset":1719814552,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 14:16:34.647 - [任务 38][SourceMongo] - Initial sync started 
[INFO ] 2024-07-01 14:16:34.647 - [任务 38][SourceMongo] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-01 14:16:34.648 - [任务 38][SourceMongo] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-01 14:16:34.674 - [任务 38][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-01 14:16:34.719 - [任务 38][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-01 14:16:34.720 - [任务 38][SourceMongo] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-01 14:16:34.807 - [任务 38][SourceMongo] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-01 14:16:34.808 - [任务 38][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-01 14:16:34.809 - [任务 38][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-01 14:16:34.811 - [任务 38][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-01 14:16:34.865 - [任务 38][SourceMongo] - Starting stream read, table list: [POLICY, CLAIM1, CLAIM], offset: {"cdcOffset":1719814552,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-01 14:16:34.867 - [任务 38][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY, CLAIM1, CLAIM], data change syncing 
[INFO ] 2024-07-01 14:20:18.590 - [任务 38] - Stop task milestones: 6682496fbb97eb19fc05e098(任务 38)  
[INFO ] 2024-07-01 14:20:19.134 - [任务 38][SourceMongo] - Node SourceMongo[2e6ff5b5-0770-4aa9-a959-4846459a5c47] running status set to false 
[INFO ] 2024-07-01 14:20:19.134 - [任务 38][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-2e6ff5b5-0770-4aa9-a959-4846459a5c47 
[INFO ] 2024-07-01 14:20:19.134 - [任务 38][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-2e6ff5b5-0770-4aa9-a959-4846459a5c47 
[INFO ] 2024-07-01 14:20:19.134 - [任务 38][SourceMongo] - Node SourceMongo[2e6ff5b5-0770-4aa9-a959-4846459a5c47] schema data cleaned 
[INFO ] 2024-07-01 14:20:19.134 - [任务 38][SourceMongo] - Node SourceMongo[2e6ff5b5-0770-4aa9-a959-4846459a5c47] monitor closed 
[INFO ] 2024-07-01 14:20:19.135 - [任务 38][SourceMongo] - Node SourceMongo[2e6ff5b5-0770-4aa9-a959-4846459a5c47] close complete, cost 82 ms 
[INFO ] 2024-07-01 14:20:19.135 - [任务 38][SouceMysql] - Node SouceMysql[bbdefc41-1496-416b-a105-5ef6b10993c7] running status set to false 
[INFO ] 2024-07-01 14:20:19.170 - [任务 38][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-bbdefc41-1496-416b-a105-5ef6b10993c7 
[INFO ] 2024-07-01 14:20:19.171 - [任务 38][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-bbdefc41-1496-416b-a105-5ef6b10993c7 
[INFO ] 2024-07-01 14:20:19.171 - [任务 38][SouceMysql] - Node SouceMysql[bbdefc41-1496-416b-a105-5ef6b10993c7] schema data cleaned 
[INFO ] 2024-07-01 14:20:19.171 - [任务 38][SouceMysql] - Node SouceMysql[bbdefc41-1496-416b-a105-5ef6b10993c7] monitor closed 
[INFO ] 2024-07-01 14:20:19.171 - [任务 38][SouceMysql] - Node SouceMysql[bbdefc41-1496-416b-a105-5ef6b10993c7] close complete, cost 39 ms 
[INFO ] 2024-07-01 14:20:22.348 - [任务 38] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-01 14:20:22.349 - [任务 38] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5ea04dbb 
[INFO ] 2024-07-01 14:20:22.350 - [任务 38] - Stopped task aspect(s) 
[INFO ] 2024-07-01 14:20:22.379 - [任务 38] - Snapshot order controller have been removed 
[INFO ] 2024-07-01 14:20:22.380 - [任务 38] - Remove memory task client succeed, task: 任务 38[6682496fbb97eb19fc05e098] 
[INFO ] 2024-07-01 14:20:22.583 - [任务 38] - Destroy memory task client cache succeed, task: 任务 38[6682496fbb97eb19fc05e098] 
