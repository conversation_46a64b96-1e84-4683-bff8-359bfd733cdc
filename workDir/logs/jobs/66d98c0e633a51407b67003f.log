[INFO ] 2024-09-06 16:42:01.450 - [任务 1] - Task initialization... 
[INFO ] 2024-09-06 16:42:01.586 - [任务 1] - Start task milestones: 66d98c0e633a51407b67003f(任务 1) 
[INFO ] 2024-09-06 16:42:01.586 - [任务 1] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-09-06 16:42:01.659 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-06 16:42:01.659 - [任务 1][Mysql] - Node Mysql[bd285254-cbdf-476e-9991-a36ad4ae35cc] start preload schema,table counts: 1 
[INFO ] 2024-09-06 16:42:01.660 - [任务 1][Mysql3306] - Node Mysql3306[cb751308-a1e1-455e-b7f0-312a1f11e28b] start preload schema,table counts: 1 
[INFO ] 2024-09-06 16:42:01.660 - [任务 1][Mysql] - Node <PERSON>ql[bd285254-cbdf-476e-9991-a36ad4ae35cc] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 16:42:01.660 - [任务 1][Mysql3306] - Node Mysql3306[cb751308-a1e1-455e-b7f0-312a1f11e28b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 16:42:02.522 - [任务 1][Mysql3306] - Node(Mysql3306) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-06 16:42:02.522 - [任务 1][Mysql3306] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-09-06 16:42:03.539 - [任务 1][Mysql] - The time of each node is inconsistent, please check nodes: {hostPort=*************:23306, time=1725612122944} and {hostPort=*************:33306, time=1725583322903} 
[INFO ] 2024-09-06 16:42:03.598 - [任务 1][Mysql] - Source node "Mysql" read batch size: 100 
[INFO ] 2024-09-06 16:42:03.598 - [任务 1][Mysql] - Source node "Mysql" event queue capacity: 200 
[INFO ] 2024-09-06 16:42:03.598 - [任务 1][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-06 16:42:03.687 - [任务 1][Mysql] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000047","position":43488243,"gtidSet":""} 
[INFO ] 2024-09-06 16:42:03.687 - [任务 1][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-06 16:42:03.766 - [任务 1][Mysql] - Initial sync started 
[INFO ] 2024-09-06 16:42:03.773 - [任务 1][Mysql] - Starting batch read, table name: t2, offset: null 
[INFO ] 2024-09-06 16:42:03.839 - [任务 1][Mysql] - Table t2 is going to be initial synced 
[INFO ] 2024-09-06 16:42:03.840 - [任务 1][Mysql] - Table [t2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-06 16:42:03.840 - [任务 1][Mysql] - Query table 't2' counts: 0 
[INFO ] 2024-09-06 16:42:03.841 - [任务 1][Mysql] - Initial sync completed 
[INFO ] 2024-09-06 16:42:03.842 - [任务 1][Mysql] - Incremental sync starting... 
[INFO ] 2024-09-06 16:42:03.845 - [任务 1][Mysql] - Initial sync completed 
[INFO ] 2024-09-06 16:42:03.845 - [任务 1][Mysql] - Starting stream read, table list: [t2], offset: {"filename":"mysql-bin.000047","position":43488243,"gtidSet":""} 
[INFO ] 2024-09-06 16:42:04.040 - [任务 1][Mysql] - Starting mysql cdc, server name: d91af2dc-42dd-4fc4-b9ba-894b52f649de 
[INFO ] 2024-09-06 16:42:04.041 - [任务 1][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 119371269
  time.precision.mode: adaptive_time_microseconds
  database.server.name: d91af2dc-42dd-4fc4-b9ba-894b52f649de
  database.port: 23306
  threadName: Debezium-Mysql-Connector-d91af2dc-42dd-4fc4-b9ba-894b52f649de
  database.hostname: *************
  database.password: ********
  name: d91af2dc-42dd-4fc4-b9ba-894b52f649de
  pdk.offset.string: {"name":"d91af2dc-42dd-4fc4-b9ba-894b52f649de","offset":{"{\"server\":\"d91af2dc-42dd-4fc4-b9ba-894b52f649de\"}":"{\"file\":\"mysql-bin.000047\",\"pos\":43488243,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-06 16:42:04.244 - [任务 1][Mysql] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[INFO ] 2024-09-06 16:44:41.208 - [任务 1][Mysql] - Mysql binlog reader stopped 
[WARN ] 2024-09-06 16:44:41.414 - [任务 1][Mysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.io.EOFException: Failed to read next byte from position 10822078
	com.github.shyiko.mysql.binlog.io.ByteArrayInputStream.read(ByteArrayInputStream.java:213)
	com.github.shyiko.mysql.binlog.io.ByteArrayInputStream.readInteger(ByteArrayInputStream.java:52)
	com.github.shyiko.mysql.binlog.event.deserialization.EventHeaderV4Deserializer.deserialize(EventHeaderV4Deserializer.java:33)
	com.github.shyiko.mysql.binlog.event.deserialization.EventHeaderV4Deserializer.deserialize(EventHeaderV4Deserializer.java:27)
	com.github.shyiko.mysql.binlog.event.deserialization.EventDeserializer.nextEvent(EventDeserializer.java:232)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-06 16:45:44.465 - [任务 1][Mysql] - Starting mysql cdc, server name: d91af2dc-42dd-4fc4-b9ba-894b52f649de 
[INFO ] 2024-09-06 16:45:44.671 - [任务 1][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1465155880
  time.precision.mode: adaptive_time_microseconds
  database.server.name: d91af2dc-42dd-4fc4-b9ba-894b52f649de
  database.port: 23306
  threadName: Debezium-Mysql-Connector-d91af2dc-42dd-4fc4-b9ba-894b52f649de
  database.hostname: *************
  database.password: ********
  name: d91af2dc-42dd-4fc4-b9ba-894b52f649de
  pdk.offset.string: {"name":"d91af2dc-42dd-4fc4-b9ba-894b52f649de","offset":{"{\"server\":\"d91af2dc-42dd-4fc4-b9ba-894b52f649de\"}":"{\"file\":\"mysql-bin.000047\",\"pos\":53489741,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-06 16:45:44.730 - [任务 1][Mysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-09-06 16:45:44.930 - [任务 1][Mysql] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[INFO ] 2024-09-06 16:52:11.953 - [任务 1] - Stop task milestones: 66d98c0e633a51407b67003f(任务 1)  
[INFO ] 2024-09-06 16:52:11.953 - [任务 1][Mysql] - Node Mysql[bd285254-cbdf-476e-9991-a36ad4ae35cc] running status set to false 
[INFO ] 2024-09-06 16:52:12.029 - [任务 1][Mysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-06 16:52:12.029 - [任务 1][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-09-06 16:52:12.082 - [任务 1][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-bd285254-cbdf-476e-9991-a36ad4ae35cc 
[INFO ] 2024-09-06 16:52:12.082 - [任务 1][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-bd285254-cbdf-476e-9991-a36ad4ae35cc 
[INFO ] 2024-09-06 16:52:12.082 - [任务 1][Mysql] - Node Mysql[bd285254-cbdf-476e-9991-a36ad4ae35cc] schema data cleaned 
[INFO ] 2024-09-06 16:52:12.083 - [任务 1][Mysql] - Node Mysql[bd285254-cbdf-476e-9991-a36ad4ae35cc] monitor closed 
[INFO ] 2024-09-06 16:52:12.084 - [任务 1][Mysql] - Node Mysql[bd285254-cbdf-476e-9991-a36ad4ae35cc] close complete, cost 133 ms 
[INFO ] 2024-09-06 16:52:12.084 - [任务 1][Mysql3306] - Node Mysql3306[cb751308-a1e1-455e-b7f0-312a1f11e28b] running status set to false 
[INFO ] 2024-09-06 16:52:12.116 - [任务 1][Mysql3306] - PDK connector node stopped: HazelcastTargetPdkDataNode-cb751308-a1e1-455e-b7f0-312a1f11e28b 
[INFO ] 2024-09-06 16:52:12.116 - [任务 1][Mysql3306] - PDK connector node released: HazelcastTargetPdkDataNode-cb751308-a1e1-455e-b7f0-312a1f11e28b 
[INFO ] 2024-09-06 16:52:12.116 - [任务 1][Mysql3306] - Node Mysql3306[cb751308-a1e1-455e-b7f0-312a1f11e28b] schema data cleaned 
[INFO ] 2024-09-06 16:52:12.117 - [任务 1][Mysql3306] - Node Mysql3306[cb751308-a1e1-455e-b7f0-312a1f11e28b] monitor closed 
[INFO ] 2024-09-06 16:52:12.117 - [任务 1][Mysql3306] - Node Mysql3306[cb751308-a1e1-455e-b7f0-312a1f11e28b] close complete, cost 32 ms 
[INFO ] 2024-09-06 16:52:13.298 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-06 16:52:13.298 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-09-06 16:52:13.298 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-09-06 16:52:13.322 - [任务 1] - Remove memory task client succeed, task: 任务 1[66d98c0e633a51407b67003f] 
[INFO ] 2024-09-06 16:52:13.322 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66d98c0e633a51407b67003f] 
