[INFO ] 2024-06-25 01:57:41.109 - [任务 25] - Task initialization... 
[INFO ] 2024-06-25 01:57:41.127 - [任务 25] - Start task milestones: 667992f42f628b2546e0d32d(任务 25) 
[INFO ] 2024-06-25 01:57:42.007 - [任务 25] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-25 01:57:42.168 - [任务 25] - The engine receives 任务 25 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-25 01:57:42.678 - [任务 25][表编辑] - Node 表编辑[1f6b0ae2-2d5a-437e-bcc5-51c8406d3441] start preload schema,table counts: 1 
[INFO ] 2024-06-25 01:57:42.679 - [任务 25][表编辑] - Node 表编辑[1f6b0ae2-2d5a-437e-bcc5-51c8406d3441] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 01:57:42.725 - [任务 25][SourceMongo] - Node SourceMongo[9164b072-65be-4ce1-8e00-fa2ed7ebfa0a] start preload schema,table counts: 1 
[INFO ] 2024-06-25 01:57:42.725 - [任务 25][SouceMysql] - Node SouceMysql[10fc96e8-b8a7-4b74-9be0-48a7e8d2e363] start preload schema,table counts: 1 
[INFO ] 2024-06-25 01:57:42.726 - [任务 25][SourceMongo] - Node SourceMongo[9164b072-65be-4ce1-8e00-fa2ed7ebfa0a] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 01:57:42.931 - [任务 25][SouceMysql] - Node SouceMysql[10fc96e8-b8a7-4b74-9be0-48a7e8d2e363] preload schema finished, cost 0 ms 
[INFO ] 2024-06-25 01:57:43.093 - [任务 25][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-25 01:57:43.099 - [任务 25][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-25 01:57:43.153 - [任务 25][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-06-25 01:57:43.156 - [任务 25][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-06-25 01:57:43.156 - [任务 25][SouceMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-25 01:57:43.364 - [任务 25][SouceMysql] - batch offset found: {"CLAIM":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"name":"0f36abcf-b52c-46d9-a8a1-c8f1f6ecb59a","offset":{"{\"server\":\"0f36abcf-b52c-46d9-a8a1-c8f1f6ecb59a\"}":"{\"ts_sec\":1719243857,\"file\":\"binlog.000031\",\"pos\":1058090632}"}} 
[INFO ] 2024-06-25 01:57:43.380 - [任务 25][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-25 01:57:43.383 - [任务 25][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-25 01:57:43.405 - [任务 25][SouceMysql] - Starting stream read, table list: [CLAIM], offset: {"name":"0f36abcf-b52c-46d9-a8a1-c8f1f6ecb59a","offset":{"{\"server\":\"0f36abcf-b52c-46d9-a8a1-c8f1f6ecb59a\"}":"{\"ts_sec\":1719243857,\"file\":\"binlog.000031\",\"pos\":1058090632}"}} 
[INFO ] 2024-06-25 01:57:43.440 - [任务 25][SouceMysql] - Starting mysql cdc, server name: 0f36abcf-b52c-46d9-a8a1-c8f1f6ecb59a 
[INFO ] 2024-06-25 01:57:43.644 - [任务 25][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 67372030
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 0f36abcf-b52c-46d9-a8a1-c8f1f6ecb59a
  database.port: 3306
  threadName: Debezium-Mysql-Connector-0f36abcf-b52c-46d9-a8a1-c8f1f6ecb59a
  database.hostname: localhost
  database.password: ********
  name: 0f36abcf-b52c-46d9-a8a1-c8f1f6ecb59a
  pdk.offset.string: {"name":"0f36abcf-b52c-46d9-a8a1-c8f1f6ecb59a","offset":{"{\"server\":\"0f36abcf-b52c-46d9-a8a1-c8f1f6ecb59a\"}":"{\"ts_sec\":1719243857,\"file\":\"binlog.000031\",\"pos\":1058090632}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-25 01:57:44.230 - [任务 25][SouceMysql] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-25 10:26:37.147 - [任务 25] - Stop task milestones: 667992f42f628b2546e0d32d(任务 25)  
[INFO ] 2024-06-25 10:26:37.454 - [任务 25][SouceMysql] - Node SouceMysql[10fc96e8-b8a7-4b74-9be0-48a7e8d2e363] running status set to false 
[INFO ] 2024-06-25 10:26:37.454 - [任务 25][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-06-25 10:26:37.454 - [任务 25][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-06-25 10:26:37.501 - [任务 25][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-10fc96e8-b8a7-4b74-9be0-48a7e8d2e363 
[INFO ] 2024-06-25 10:26:37.503 - [任务 25][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-10fc96e8-b8a7-4b74-9be0-48a7e8d2e363 
[INFO ] 2024-06-25 10:26:37.503 - [任务 25][SouceMysql] - Node SouceMysql[10fc96e8-b8a7-4b74-9be0-48a7e8d2e363] schema data cleaned 
[INFO ] 2024-06-25 10:26:37.508 - [任务 25][SouceMysql] - Node SouceMysql[10fc96e8-b8a7-4b74-9be0-48a7e8d2e363] monitor closed 
[INFO ] 2024-06-25 10:26:37.509 - [任务 25][SouceMysql] - Node SouceMysql[10fc96e8-b8a7-4b74-9be0-48a7e8d2e363] close complete, cost 103 ms 
[INFO ] 2024-06-25 10:26:37.511 - [任务 25][表编辑] - Node 表编辑[1f6b0ae2-2d5a-437e-bcc5-51c8406d3441] running status set to false 
[INFO ] 2024-06-25 10:26:37.511 - [任务 25][表编辑] - Node 表编辑[1f6b0ae2-2d5a-437e-bcc5-51c8406d3441] schema data cleaned 
[INFO ] 2024-06-25 10:26:37.511 - [任务 25][表编辑] - Node 表编辑[1f6b0ae2-2d5a-437e-bcc5-51c8406d3441] monitor closed 
[INFO ] 2024-06-25 10:26:37.514 - [任务 25][表编辑] - Node 表编辑[1f6b0ae2-2d5a-437e-bcc5-51c8406d3441] close complete, cost 4 ms 
[INFO ] 2024-06-25 10:26:37.546 - [任务 25][SourceMongo] - Node SourceMongo[9164b072-65be-4ce1-8e00-fa2ed7ebfa0a] running status set to false 
[INFO ] 2024-06-25 10:26:37.547 - [任务 25][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-9164b072-65be-4ce1-8e00-fa2ed7ebfa0a 
[INFO ] 2024-06-25 10:26:37.549 - [任务 25][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-9164b072-65be-4ce1-8e00-fa2ed7ebfa0a 
[INFO ] 2024-06-25 10:26:37.550 - [任务 25][SourceMongo] - Node SourceMongo[9164b072-65be-4ce1-8e00-fa2ed7ebfa0a] schema data cleaned 
[INFO ] 2024-06-25 10:26:37.551 - [任务 25][SourceMongo] - Node SourceMongo[9164b072-65be-4ce1-8e00-fa2ed7ebfa0a] monitor closed 
[INFO ] 2024-06-25 10:26:37.551 - [任务 25][SourceMongo] - Node SourceMongo[9164b072-65be-4ce1-8e00-fa2ed7ebfa0a] close complete, cost 36 ms 
[INFO ] 2024-06-25 10:26:38.061 - [任务 25] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-25 10:26:38.069 - [任务 25] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1e1178a7 
[INFO ] 2024-06-25 10:26:38.070 - [任务 25] - Stopped task aspect(s) 
[INFO ] 2024-06-25 10:26:38.231 - [任务 25] - Snapshot order controller have been removed 
[INFO ] 2024-06-25 10:26:38.233 - [任务 25] - Remove memory task client succeed, task: 任务 25[667992f42f628b2546e0d32d] 
[INFO ] 2024-06-25 10:26:38.233 - [任务 25] - Destroy memory task client cache succeed, task: 任务 25[667992f42f628b2546e0d32d] 
