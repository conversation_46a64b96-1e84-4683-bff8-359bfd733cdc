[INFO ] 2024-07-28 13:18:01.529 - [Heartbeat-SourceMongo] - Start task milestones: 66a5d44947ea9d572a7e9a35(Heartbeat-SourceMongo) 
[INFO ] 2024-07-28 13:18:02.606 - [Heartbeat-SourceMongo] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-28 13:18:02.615 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 13:18:02.617 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4758878a 
[INFO ] 2024-07-28 13:18:02.661 - [Heartbeat-SourceMongo] - Stop task milestones: 66a5d44947ea9d572a7e9a35(Heartbeat-SourceMongo)  
[INFO ] 2024-07-28 13:18:02.755 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-28 13:18:02.757 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-28 13:18:02.763 - [Heartbeat-SourceMongo] - Node [id 7ed13acf-cb3e-4622-99b1-7b1a2564760f, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id 7ed13acf-cb3e-4622-99b1-7b1a2564760f, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-28 13:22:45.653 - [Heartbeat-SourceMongo] - Start task milestones: 66a5d44947ea9d572a7e9a35(Heartbeat-SourceMongo) 
[INFO ] 2024-07-28 13:22:45.826 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 13:22:45.828 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 13:22:45.830 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@447d81a7 
[INFO ] 2024-07-28 13:22:45.833 - [Heartbeat-SourceMongo] - Stop task milestones: 66a5d44947ea9d572a7e9a35(Heartbeat-SourceMongo)  
[INFO ] 2024-07-28 13:22:45.971 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-28 13:22:45.974 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-28 13:22:45.979 - [Heartbeat-SourceMongo] - Node [id 7ed13acf-cb3e-4622-99b1-7b1a2564760f, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id 7ed13acf-cb3e-4622-99b1-7b1a2564760f, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-28 13:22:46.102 - [Heartbeat-SourceMongo] - Start task milestones: 66a5d44947ea9d572a7e9a35(Heartbeat-SourceMongo) 
[INFO ] 2024-07-28 13:22:46.264 - [Heartbeat-SourceMongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-28 13:22:46.265 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-28 13:22:46.267 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@216349 
[INFO ] 2024-07-28 13:22:46.270 - [Heartbeat-SourceMongo] - Stop task milestones: 66a5d44947ea9d572a7e9a35(Heartbeat-SourceMongo)  
[INFO ] 2024-07-28 13:22:46.396 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-28 13:22:46.396 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-28 13:22:46.455 - [Heartbeat-SourceMongo] - Node [id 7ed13acf-cb3e-4622-99b1-7b1a2564760f, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id 7ed13acf-cb3e-4622-99b1-7b1a2564760f, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

