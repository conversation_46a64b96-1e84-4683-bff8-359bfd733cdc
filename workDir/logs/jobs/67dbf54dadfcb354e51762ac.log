[WARN ] 2025-03-21 04:57:51.827 - [任务 13][Sybase] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: io.tapdata.entity.error.CoreException: Can't get connection from ********************************************, error is: JZ00L: <PERSON><PERSON> failed.  Examine the SQLWarnings chained to this exception for the reason(s).
	io.tapdata.sybase.extend.SybaseContext.getConnection(SybaseContext.java:134)
	io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:48)
	io.tapdata.sybase.dml.SybaseRecordWriter.<init>(SybaseRecordWriter.java:14)
	io.tapdata.sybase.SybaseConnectorV2.writeRecord(SybaseConnectorV2.java:610)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$68(HazelcastTargetPdkDataNode.java:1089)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2025-03-21 04:57:51.857 - [任务 13][Sybase] - Retry operation TARGET_WRITE_RECORD, retry times 1/15, first retry time 2025-03-21 04:57:51, next retry time 2025-03-21 04:58:51 
[TRACE] 2025-03-21 05:04:29.307 - [任务 13][Sybase] - [Auto Retry] Method (target_write_record) retry succeed 
[INFO ] 2025-03-21 05:04:29.307 - [任务 13][Sybase] - Retry operation TARGET_WRITE_RECORD success, total cost 00:06:37.471000 
[WARN ] 2025-03-21 08:14:30.850 - [任务 13][Sybase] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: io.tapdata.entity.error.CoreException: Can't get connection from ********************************************, error is: JZ00L: Login failed.  Examine the SQLWarnings chained to this exception for the reason(s).
	io.tapdata.sybase.extend.SybaseContext.getConnection(SybaseContext.java:134)
	io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:48)
	io.tapdata.sybase.dml.SybaseRecordWriter.<init>(SybaseRecordWriter.java:14)
	io.tapdata.sybase.SybaseConnectorV2.writeRecord(SybaseConnectorV2.java:610)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$68(HazelcastTargetPdkDataNode.java:1089)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2025-03-21 08:14:31.059 - [任务 13][Sybase] - Retry operation TARGET_WRITE_RECORD, retry times 1/15, first retry time 2025-03-21 08:14:30, next retry time 2025-03-21 08:15:30 
[WARN ] 2025-03-21 08:34:16.067 - [任务 13][Sybase] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Caused by: io.tapdata.entity.error.CoreException: Can't get connection from ********************************************, error is: JZ00L: Login failed.  Examine the SQLWarnings chained to this exception for the reason(s).
	io.tapdata.sybase.extend.SybaseContext.getConnection(SybaseContext.java:134)
	io.tapdata.common.dml.NormalRecordWriter.<init>(NormalRecordWriter.java:48)
	io.tapdata.sybase.dml.SybaseRecordWriter.<init>(SybaseRecordWriter.java:14)
	io.tapdata.sybase.SybaseConnectorV2.writeRecord(SybaseConnectorV2.java:610)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$68(HazelcastTargetPdkDataNode.java:1089)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2025-03-21 08:34:16.277 - [任务 13][Sybase] - Retry operation TARGET_WRITE_RECORD, retry times 2/15, first retry time 2025-03-21 08:14:30, next retry time 2025-03-21 08:35:16 
[TRACE] 2025-03-21 09:51:31.169 - [任务 13][Sybase] - [Auto Retry] Method (target_write_record) retry succeed 
[INFO ] 2025-03-21 09:51:31.169 - [任务 13][Sybase] - Retry operation TARGET_WRITE_RECORD success, total cost 01:37:00.307000 
[TRACE] 2025-03-21 12:08:58.477 - [任务 13][TestDummy] - Node TestDummy[30e7dacc-5a95-4b2f-bc18-aaa6586d21cd] running status set to false 
[INFO ] 2025-03-21 12:08:58.478 - [任务 13][TestDummy] - Stop connector 
[TRACE] 2025-03-21 12:08:58.481 - [任务 13][TestDummy] - Incremental sync completed 
[WARN ] 2025-03-21 12:08:58.493 - [任务 13][TestDummy] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode_30e7dacc-5a95-4b2f-bc18-aaa6586d21cd_1742468441958 
[TRACE] 2025-03-21 12:08:58.493 - [任务 13][TestDummy] - PDK connector node released: HazelcastSourcePdkDataNode_30e7dacc-5a95-4b2f-bc18-aaa6586d21cd_1742468441958 
[TRACE] 2025-03-21 12:08:58.494 - [任务 13][TestDummy] - Node TestDummy[30e7dacc-5a95-4b2f-bc18-aaa6586d21cd] schema data cleaned 
[TRACE] 2025-03-21 12:08:58.494 - [任务 13][TestDummy] - Node TestDummy[30e7dacc-5a95-4b2f-bc18-aaa6586d21cd] monitor closed 
[TRACE] 2025-03-21 12:08:58.500 - [任务 13][TestDummy] - Node TestDummy[30e7dacc-5a95-4b2f-bc18-aaa6586d21cd] close complete, cost 22 ms 
[TRACE] 2025-03-21 12:08:58.500 - [任务 13][Sybase] - Node Sybase[15ee049c-286f-4cce-b010-9bb0d1f6a20a] running status set to false 
