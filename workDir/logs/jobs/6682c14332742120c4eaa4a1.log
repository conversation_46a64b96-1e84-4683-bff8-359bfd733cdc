[INFO ] 2024-07-02 00:11:24.064 - [Task 2] - Task initialization... 
[INFO ] 2024-07-02 00:11:59.964 - [Task 2] - Start task milestones: 6682c14332742120c4eaa4a1(Task 2) 
[INFO ] 2024-07-02 00:12:00.541 - [Task 2] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-02 00:12:00.686 - [Task 2] - The engine receives Task 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 00:12:00.686 - [Task 2][SourceMongo] - Node SourceMongo[0cdd758f-4a4b-4a1e-b744-1c46b0db5e44] start preload schema,table counts: 1 
[INFO ] 2024-07-02 00:12:00.686 - [Task 2][SouceMysql] - Node SouceMysql[fc4a3cb1-3cda-4c51-8bbc-fa2e9edf7e95] start preload schema,table counts: 1 
[INFO ] 2024-07-02 00:12:00.691 - [Task 2][SourceMongo] - Node SourceMongo[0cdd758f-4a4b-4a1e-b744-1c46b0db5e44] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 00:12:00.691 - [Task 2][SouceMysql] - Node SouceMysql[fc4a3cb1-3cda-4c51-8bbc-fa2e9edf7e95] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 00:12:00.928 - [Task 2][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-07-02 00:12:00.929 - [Task 2][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-07-02 00:12:00.952 - [Task 2][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 00:12:01.020 - [Task 2][SourceMongo] - batch offset found: {"POLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":true},"batch_read_connector_status":"RUNNING"}},stream offset found: {"_data":{"value":"826682C1E2000000022B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-02 00:12:01.021 - [Task 2][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 00:12:01.021 - [Task 2][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 00:12:01.046 - [Task 2][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 00:12:01.047 - [Task 2][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 00:12:01.048 - [Task 2][SourceMongo] - Starting stream read, table list: [POLICY], offset: {"_data":{"value":"826682C1E2000000022B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004","bsonType":"STRING","document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"boolean":false,"double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-02 00:12:01.251 - [Task 2][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 01:49:33.278 - [Task 2] - Start task milestones: 6682c14332742120c4eaa4a1(Task 2) 
[INFO ] 2024-07-02 01:49:33.279 - [Task 2] - Task initialization... 
[INFO ] 2024-07-02 01:49:33.555 - [Task 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-02 01:49:33.556 - [Task 2] - The engine receives Task 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 01:49:33.636 - [Task 2][SourceMongo] - Node SourceMongo[0cdd758f-4a4b-4a1e-b744-1c46b0db5e44] start preload schema,table counts: 1 
[INFO ] 2024-07-02 01:49:33.636 - [Task 2][SouceMysql] - Node SouceMysql[fc4a3cb1-3cda-4c51-8bbc-fa2e9edf7e95] start preload schema,table counts: 1 
[INFO ] 2024-07-02 01:49:33.637 - [Task 2][SourceMongo] - Node SourceMongo[0cdd758f-4a4b-4a1e-b744-1c46b0db5e44] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 01:49:33.842 - [Task 2][SouceMysql] - Node SouceMysql[fc4a3cb1-3cda-4c51-8bbc-fa2e9edf7e95] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 01:49:33.864 - [Task 2][SourceMongo] - Source node "SourceMongo" read batch size: 100 
[INFO ] 2024-07-02 01:49:33.865 - [Task 2][SourceMongo] - Source node "SourceMongo" event queue capacity: 200 
[INFO ] 2024-07-02 01:49:33.913 - [Task 2][SourceMongo] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-02 01:49:33.913 - [Task 2][SouceMysql] - Node(SouceMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-02 01:49:33.913 - [Task 2][SouceMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 01:49:33.917 - [Task 2][SourceMongo] - batch offset found: {},stream offset found: {"_data":{"value":"826682D49B000000022B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004","bsonType":"STRING","boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-02 01:49:34.009 - [Task 2][SourceMongo] - Incremental sync starting... 
[INFO ] 2024-07-02 01:49:34.011 - [Task 2][SourceMongo] - Initial sync completed 
[INFO ] 2024-07-02 01:49:34.092 - [Task 2][SourceMongo] - Starting stream read, table list: [POLICY], offset: {"_data":{"value":"826682D49B000000022B022C0100296E5A100419AC2CE45AF24FD4B42569AC18C6F8C746645F696400646510F74CA270A1CF5533CEF30004","bsonType":"STRING","boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"array":false,"null":false,"number":false}} 
[INFO ] 2024-07-02 01:49:34.092 - [Task 2][SourceMongo] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 12:19:23.864 - [Task 2][SourceMongo] - Node SourceMongo[0cdd758f-4a4b-4a1e-b744-1c46b0db5e44] running status set to false 
[INFO ] 2024-07-02 12:19:23.864 - [Task 2][SourceMongo] - PDK connector node stopped: HazelcastSourcePdkDataNode-0cdd758f-4a4b-4a1e-b744-1c46b0db5e44 
[INFO ] 2024-07-02 12:19:23.864 - [Task 2][SourceMongo] - PDK connector node released: HazelcastSourcePdkDataNode-0cdd758f-4a4b-4a1e-b744-1c46b0db5e44 
[INFO ] 2024-07-02 12:19:23.864 - [Task 2][SourceMongo] - Node SourceMongo[0cdd758f-4a4b-4a1e-b744-1c46b0db5e44] schema data cleaned 
[INFO ] 2024-07-02 12:19:23.865 - [Task 2][SourceMongo] - Node SourceMongo[0cdd758f-4a4b-4a1e-b744-1c46b0db5e44] monitor closed 
[INFO ] 2024-07-02 12:19:23.866 - [Task 2][SourceMongo] - Node SourceMongo[0cdd758f-4a4b-4a1e-b744-1c46b0db5e44] close complete, cost 18 ms 
[INFO ] 2024-07-02 12:19:23.878 - [Task 2][SouceMysql] - Node SouceMysql[fc4a3cb1-3cda-4c51-8bbc-fa2e9edf7e95] running status set to false 
[INFO ] 2024-07-02 12:19:23.878 - [Task 2][SouceMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-fc4a3cb1-3cda-4c51-8bbc-fa2e9edf7e95 
[INFO ] 2024-07-02 12:19:23.878 - [Task 2][SouceMysql] - PDK connector node released: HazelcastTargetPdkDataNode-fc4a3cb1-3cda-4c51-8bbc-fa2e9edf7e95 
[INFO ] 2024-07-02 12:19:23.878 - [Task 2][SouceMysql] - Node SouceMysql[fc4a3cb1-3cda-4c51-8bbc-fa2e9edf7e95] schema data cleaned 
[INFO ] 2024-07-02 12:19:23.879 - [Task 2][SouceMysql] - Node SouceMysql[fc4a3cb1-3cda-4c51-8bbc-fa2e9edf7e95] monitor closed 
[INFO ] 2024-07-02 12:19:23.879 - [Task 2][SouceMysql] - Node SouceMysql[fc4a3cb1-3cda-4c51-8bbc-fa2e9edf7e95] close complete, cost 13 ms 
[INFO ] 2024-07-02 12:19:24.421 - [Task 2][SourceMongo] - Incremental sync completed 
[INFO ] 2024-07-02 12:19:25.385 - [Task 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 12:19:25.392 - [Task 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5d925281 
[INFO ] 2024-07-02 12:19:25.392 - [Task 2] - Stop task milestones: 6682c14332742120c4eaa4a1(Task 2)  
[INFO ] 2024-07-02 12:19:25.540 - [Task 2] - Stopped task aspect(s) 
[INFO ] 2024-07-02 12:19:25.541 - [Task 2] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 12:19:25.574 - [Task 2] - Remove memory task client succeed, task: Task 2[6682c14332742120c4eaa4a1] 
[INFO ] 2024-07-02 12:19:25.574 - [Task 2] - Destroy memory task client cache succeed, task: Task 2[6682c14332742120c4eaa4a1] 
