[INFO ] 2024-07-18 10:22:05.853 - [任务 6] - Task initialization... 
[INFO ] 2024-07-18 10:22:06.295 - [任务 6] - Start task milestones: 66979bb6b92eda1a86f521ab(任务 6) 
[INFO ] 2024-07-18 10:22:07.907 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 10:22:08.480 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 10:22:09.326 - [任务 6][TESTE] - Node TESTE[c9e9968e-d008-4f58-b4b7-2eae423ab808] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:22:09.359 - [任务 6][TESTE] - Node TESTE[c9e9968e-d008-4f58-b4b7-2eae423ab808] preload schema finished, cost 1 ms 
[INFO ] 2024-07-18 10:22:09.360 - [任务 6][POLICY] - Node POLICY[db02ac52-0144-403d-9d5a-b8ba4ee501b6] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:22:09.369 - [任务 6][POLICY] - Node POLICY[db02ac52-0144-403d-9d5a-b8ba4ee501b6] preload schema finished, cost 0 ms 
[WARN ] 2024-07-18 10:22:09.688 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=eff57c38-6a04-43e1-bdb5-43d6d91cc09a}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2880 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-18 10:22:10.884 - [任务 6][TESTE] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-07-18 10:23:09.823 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=bf3ecd42-a7b7-4c69-b343-7ad3b8322284}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2879 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:24:10.346 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=7d1e8ed7-40e6-4b7a-becd-d6924888b6f1}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2878 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:25:10.449 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=10549c31-1f96-4068-8ec8-7360ab5b2ed0}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2877 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:26:10.897 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=1c2a3dbb-73a9-4a38-a40a-aca17926d7cb}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2876 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:27:10.954 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=5893d57f-5710-4a8e-baa8-e1c52232ac9a}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2875 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:28:11.265 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=13c3b190-e0f3-472a-a518-831c616c3f1a}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2874 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:29:11.385 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=b74daf19-5fdd-4d97-8073-0b08522d3a6b}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2873 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:30:11.557 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=21c4f810-9d03-45a6-bc0e-2a1215e983a6}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2872 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:31:11.737 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=0314077c-dd35-46f5-8533-c17e7cd9f2ee}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2871 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:32:11.859 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=1c512a7b-e309-4f9f-85dc-d6bb05e179d9}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2870 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:33:11.948 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=8489fae8-e65a-4876-975b-6f97d3668e39}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2869 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:34:12.117 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=fa8387c7-43bf-4f33-9b32-c8e19d22bf5d}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2868 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:35:12.256 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=e7391d40-27f5-42ae-8816-9b76b3bfd2bd}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2867 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:36:12.360 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=7669032f-43b3-4be3-8f0b-b4ddfed0d78d}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2866 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:37:12.485 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=cc0a0879-7a74-4fa3-ae06-eeccaed27a35}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2865 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:38:12.738 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=3e4d0039-5546-4ba6-aa2a-03498c93d7e6}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2864 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:39:12.896 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=28fe96f8-0559-460e-941e-a9b04bfb01a5}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2863 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 10:40:13.005 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=d0fb6981535842dd905580990e1576547e6b44f197254066a0c59ae36c26b210&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=020cbc22-29ca-4772-84ff-47248654e86e}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2862 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-18 10:41:13.360 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) retry succeed 
[INFO ] 2024-07-18 10:41:14.254 - [任务 6][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-18 10:41:14.258 - [任务 6][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-18 10:41:14.264 - [任务 6][POLICY] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-18 10:41:14.371 - [任务 6][POLICY] - batch offset found: {"POLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":null},"batch_read_connector_status":"RUNNING"}},stream offset found: {"sequenceMap":{"POLICY":3,"_tapdata_heartbeat_table":2640},"streamOffset":{"_data":{"value":"826697A634000000022B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F6964006466979BDB66AB5EDE8ACB84400004","bsonType":"STRING","double":false,"binary":false,"string":true,"int32":false,"int64":false,"symbol":false,"boolean":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"array":false,"null":false,"number":false}}} 
[INFO ] 2024-07-18 10:41:14.585 - [任务 6][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-18 10:41:14.686 - [任务 6][POLICY] - Initial sync completed 
[INFO ] 2024-07-18 10:41:14.686 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-18 10:41:14.687 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection LocalMongo enable share cdc: true 
[INFO ] 2024-07-18 10:41:14.689 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 6 enable share cdc: true 
[INFO ] 2024-07-18 10:41:14.852 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自LocalMongo的共享挖掘任务 
[INFO ] 2024-07-18 10:41:14.897 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://root:******@192.168.1.184:57017/tapdatav310?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-18 10:41:15.024 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979b368e4a90a908ef0949, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697799db92eda1a86f5135f_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1610411823, shareCdcTaskId=66979b35b92eda1a86f520ca, connectionId=6697799db92eda1a86f5135f) 
[INFO ] 2024-07-18 10:41:15.062 - [任务 6][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自LocalMongo的共享挖掘任务_POLICY_任务 6', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_1610411823', head seq: 0, tail seq: 2 
[INFO ] 2024-07-18 10:41:15.095 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979b368e4a90a908ef094a, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697799db92eda1a86f5135f__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1588831785, shareCdcTaskId=66979b35b92eda1a86f520ca, connectionId=6697799db92eda1a86f5135f) 
[INFO ] 2024-07-18 10:41:15.333 - [任务 6][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自LocalMongo的共享挖掘任务__tapdata_heartbeat_table_任务 6', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_-1588831785', head seq: 0, tail seq: 2639 
[INFO ] 2024-07-18 10:41:15.334 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-18 10:41:15.334 - [任务 6][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-18 10:41:15.334 - [任务 6][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-18 10:41:15.334 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-18 10:41:15.336 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Read table count: 2, partition size: 1, read thread number: 2 
[INFO ] 2024-07-18 10:41:15.362 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979b368e4a90a908ef0949, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697799db92eda1a86f5135f_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1610411823, shareCdcTaskId=66979b35b92eda1a86f520ca, connectionId=6697799db92eda1a86f5135f) 
[INFO ] 2024-07-18 10:41:15.376 - [任务 6][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自LocalMongo的共享挖掘任务_POLICY_任务 6', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_1610411823', head seq: 0, tail seq: 2 
[INFO ] 2024-07-18 10:41:15.381 - [任务 6][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自LocalMongo的共享挖掘任务_POLICY_任务 6, external storage name: ExternalStorage_SHARE_CDC_1610411823 
[INFO ] 2024-07-18 10:41:15.381 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-18 10:41:15.384 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 3 
[INFO ] 2024-07-18 10:41:15.389 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=3} 
[INFO ] 2024-07-18 10:41:15.407 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979b368e4a90a908ef094a, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697799db92eda1a86f5135f__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1588831785, shareCdcTaskId=66979b35b92eda1a86f520ca, connectionId=6697799db92eda1a86f5135f) 
[INFO ] 2024-07-18 10:41:15.420 - [任务 6][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自LocalMongo的共享挖掘任务__tapdata_heartbeat_table_任务 6', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_-1588831785', head seq: 0, tail seq: 2639 
[INFO ] 2024-07-18 10:41:15.421 - [任务 6][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自LocalMongo的共享挖掘任务__tapdata_heartbeat_table_任务 6, external storage name: ExternalStorage_SHARE_CDC_-1588831785 
[INFO ] 2024-07-18 10:41:15.422 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [_tapdata_heartbeat_table] 
[INFO ] 2024-07-18 10:41:15.424 - [任务 6][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 10:41:15.424 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Starting read '_tapdata_heartbeat_table' log, sequence: 2640 
[INFO ] 2024-07-18 10:41:15.439 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Find by _tapdata_heartbeat_table filter: {sequence=2640} 
[INFO ] 2024-07-18 17:27:27.807 - [任务 6] - Start task milestones: 66979bb6b92eda1a86f521ab(任务 6) 
[INFO ] 2024-07-18 17:27:27.814 - [任务 6] - Task initialization... 
[INFO ] 2024-07-18 17:27:29.992 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 17:27:30.354 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 17:27:30.824 - [任务 6][POLICY] - Node POLICY[db02ac52-0144-403d-9d5a-b8ba4ee501b6] start preload schema,table counts: 1 
[INFO ] 2024-07-18 17:27:30.830 - [任务 6][POLICY] - Node POLICY[db02ac52-0144-403d-9d5a-b8ba4ee501b6] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 17:27:30.967 - [任务 6][TESTE] - Node TESTE[c9e9968e-d008-4f58-b4b7-2eae423ab808] start preload schema,table counts: 1 
[INFO ] 2024-07-18 17:27:30.968 - [任务 6][TESTE] - Node TESTE[c9e9968e-d008-4f58-b4b7-2eae423ab808] preload schema finished, cost 0 ms 
[WARN ] 2024-07-18 17:27:31.278 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=a5c1947dfcb94892a47148470a708e4d38eb696d10f1482babb2e454b0c2da7e&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=487fc48a-accb-425d-b567-98e88e095f97}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2880 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-18 17:27:32.782 - [任务 6][TESTE] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-07-18 17:28:31.477 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=a5c1947dfcb94892a47148470a708e4d38eb696d10f1482babb2e454b0c2da7e&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=adf5fdf0-7a9f-41ff-a718-777d017fd73b}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2879 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 17:29:31.614 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=a5c1947dfcb94892a47148470a708e4d38eb696d10f1482babb2e454b0c2da7e&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=1a4cd6e5-8242-45f3-963d-afc63fd90f27}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2878 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-18 17:30:25.741 - [任务 6][POLICY] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2806/1724376542@6574e274 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
 
[ERROR] 2024-07-18 17:30:25.875 - [任务 6][POLICY] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2806/1724376542@6574e274 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2806/1724376542@6574e274 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more


<-- Simple Stack Trace -->
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2806/1724376542@6574e274 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2806/1724376542@6574e274 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more

[INFO ] 2024-07-18 17:30:25.881 - [任务 6][POLICY] - Node POLICY[db02ac52-0144-403d-9d5a-b8ba4ee501b6] running status set to false 
[INFO ] 2024-07-18 17:30:25.883 - [任务 6][POLICY] - PDK connector node stopped: null 
[INFO ] 2024-07-18 17:30:25.886 - [任务 6][POLICY] - PDK connector node released: null 
[INFO ] 2024-07-18 17:30:25.887 - [任务 6][POLICY] - Node POLICY[db02ac52-0144-403d-9d5a-b8ba4ee501b6] schema data cleaned 
[INFO ] 2024-07-18 17:30:25.893 - [任务 6][POLICY] - Node POLICY[db02ac52-0144-403d-9d5a-b8ba4ee501b6] monitor closed 
[INFO ] 2024-07-18 17:30:25.903 - [任务 6][POLICY] - Node POLICY[db02ac52-0144-403d-9d5a-b8ba4ee501b6] close complete, cost 19 ms 
[INFO ] 2024-07-18 17:30:25.904 - [任务 6][TESTE] - Node TESTE[c9e9968e-d008-4f58-b4b7-2eae423ab808] running status set to false 
[WARN ] 2024-07-18 17:30:25.967 - [任务 6][TESTE] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastTargetPdkDataNode-c9e9968e-d008-4f58-b4b7-2eae423ab808 
[INFO ] 2024-07-18 17:30:25.970 - [任务 6][TESTE] - PDK connector node released: HazelcastTargetPdkDataNode-c9e9968e-d008-4f58-b4b7-2eae423ab808 
[INFO ] 2024-07-18 17:30:25.970 - [任务 6][TESTE] - Node TESTE[c9e9968e-d008-4f58-b4b7-2eae423ab808] schema data cleaned 
[INFO ] 2024-07-18 17:30:25.984 - [任务 6][TESTE] - Node TESTE[c9e9968e-d008-4f58-b4b7-2eae423ab808] monitor closed 
[INFO ] 2024-07-18 17:30:25.985 - [任务 6][TESTE] - Node TESTE[c9e9968e-d008-4f58-b4b7-2eae423ab808] close complete, cost 82 ms 
[INFO ] 2024-07-18 18:04:14.237 - [任务 6] - Start task milestones: 66979bb6b92eda1a86f521ab(任务 6) 
[INFO ] 2024-07-18 18:04:14.284 - [任务 6] - Task initialization... 
[INFO ] 2024-07-18 18:04:16.260 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 18:04:16.532 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 18:04:17.440 - [任务 6][TESTE] - Node TESTE[c9e9968e-d008-4f58-b4b7-2eae423ab808] start preload schema,table counts: 1 
[INFO ] 2024-07-18 18:04:17.472 - [任务 6][TESTE] - Node TESTE[c9e9968e-d008-4f58-b4b7-2eae423ab808] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 18:04:17.486 - [任务 6][POLICY] - Node POLICY[db02ac52-0144-403d-9d5a-b8ba4ee501b6] start preload schema,table counts: 1 
[INFO ] 2024-07-18 18:04:17.487 - [任务 6][POLICY] - Node POLICY[db02ac52-0144-403d-9d5a-b8ba4ee501b6] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 18:04:19.223 - [任务 6][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-18 18:04:19.231 - [任务 6][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-18 18:04:19.232 - [任务 6][POLICY] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-18 18:04:19.303 - [任务 6][POLICY] - batch offset found: {"POLICY":{"batch_read_connector_offset":{"sortKey":"_id","value":"6510f74ca270a1cf5533d1b0","objectId":null},"batch_read_connector_status":"RUNNING"}},stream offset found: {"sequenceMap":{"POLICY":3,"_tapdata_heartbeat_table":2640},"streamOffset":{"_data":{"value":"826697A634000000022B022C0100296E5A100411434BEBD0B34C1CB90DA36B490F1CE546645F6964006466979BDB66AB5EDE8ACB84400004","bsonType":"STRING","string":true,"int32":false,"int64":false,"symbol":false,"double":false,"binary":false,"dateTime":false,"regularExpression":false,"javaScript":false,"javaScriptWithScope":false,"document":false,"decimal128":false,"objectId":false,"dbpointer":false,"timestamp":false,"boolean":false,"number":false,"array":false,"null":false}}} 
[INFO ] 2024-07-18 18:04:19.565 - [任务 6][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-18 18:04:19.566 - [任务 6][POLICY] - Initial sync completed 
[INFO ] 2024-07-18 18:04:19.678 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-18 18:04:19.678 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection LocalMongo enable share cdc: true 
[INFO ] 2024-07-18 18:04:19.681 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 6 enable share cdc: true 
[INFO ] 2024-07-18 18:04:19.702 - [任务 6][TESTE] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-18 18:04:19.823 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自LocalMongo的共享挖掘任务 
[INFO ] 2024-07-18 18:04:19.969 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://root:******@192.168.1.184:57017/tapdatav310?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-18 18:04:20.012 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979b368e4a90a908ef0949, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697799db92eda1a86f5135f_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1610411823, shareCdcTaskId=66979b35b92eda1a86f520ca, connectionId=6697799db92eda1a86f5135f) 
[INFO ] 2024-07-18 18:04:20.298 - [任务 6][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自LocalMongo的共享挖掘任务_POLICY_任务 6', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_1610411823', head seq: 0, tail seq: 2 
[INFO ] 2024-07-18 18:04:20.369 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979b368e4a90a908ef094a, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697799db92eda1a86f5135f__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1588831785, shareCdcTaskId=66979b35b92eda1a86f520ca, connectionId=6697799db92eda1a86f5135f) 
[INFO ] 2024-07-18 18:04:20.655 - [任务 6][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自LocalMongo的共享挖掘任务__tapdata_heartbeat_table_任务 6', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_-1588831785', head seq: 0, tail seq: 2639 
[INFO ] 2024-07-18 18:04:20.656 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-18 18:04:20.657 - [任务 6][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-18 18:04:20.657 - [任务 6][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-18 18:04:20.657 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-18 18:04:20.668 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Read table count: 2, partition size: 1, read thread number: 2 
[INFO ] 2024-07-18 18:04:20.742 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979b368e4a90a908ef0949, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697799db92eda1a86f5135f_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_1610411823, shareCdcTaskId=66979b35b92eda1a86f520ca, connectionId=6697799db92eda1a86f5135f) 
[INFO ] 2024-07-18 18:04:20.770 - [任务 6][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自LocalMongo的共享挖掘任务_POLICY_任务 6', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_1610411823', head seq: 0, tail seq: 2 
[INFO ] 2024-07-18 18:04:20.774 - [任务 6][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自LocalMongo的共享挖掘任务_POLICY_任务 6, external storage name: ExternalStorage_SHARE_CDC_1610411823 
[INFO ] 2024-07-18 18:04:20.779 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-18 18:04:20.779 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 3 
[INFO ] 2024-07-18 18:04:20.793 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=3} 
[INFO ] 2024-07-18 18:04:20.816 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=66979b368e4a90a908ef094a, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697799db92eda1a86f5135f__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1588831785, shareCdcTaskId=66979b35b92eda1a86f520ca, connectionId=6697799db92eda1a86f5135f) 
[INFO ] 2024-07-18 18:04:20.900 - [任务 6][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自LocalMongo的共享挖掘任务__tapdata_heartbeat_table_任务 6', name space: 'tapdatav310.ExternalStorage_SHARE_CDC_-1588831785', head seq: 0, tail seq: 2639 
[INFO ] 2024-07-18 18:04:20.901 - [任务 6][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自LocalMongo的共享挖掘任务__tapdata_heartbeat_table_任务 6, external storage name: ExternalStorage_SHARE_CDC_-1588831785 
[INFO ] 2024-07-18 18:04:20.906 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [_tapdata_heartbeat_table] 
[INFO ] 2024-07-18 18:04:20.910 - [任务 6][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 18:04:20.912 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Starting read '_tapdata_heartbeat_table' log, sequence: 2640 
[INFO ] 2024-07-18 18:04:20.912 - [任务 6][POLICY] - [Share CDC Task HZ Reader] - Find by _tapdata_heartbeat_table filter: {sequence=2640} 
[INFO ] 2024-07-18 18:09:04.444 - [任务 6][POLICY] - Node POLICY[db02ac52-0144-403d-9d5a-b8ba4ee501b6] running status set to false 
[INFO ] 2024-07-18 18:48:24.390 - [任务 6] - Start task milestones: 66979bb6b92eda1a86f521ab(任务 6) 
[INFO ] 2024-07-18 18:48:30.210 - [任务 6] - Task initialization... 
[INFO ] 2024-07-18 18:48:31.916 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 18:48:32.306 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 18:48:33.161 - [任务 6][TESTE] - Node TESTE[c9e9968e-d008-4f58-b4b7-2eae423ab808] start preload schema,table counts: 1 
[INFO ] 2024-07-18 18:48:33.272 - [任务 6][TESTE] - Node TESTE[c9e9968e-d008-4f58-b4b7-2eae423ab808] preload schema finished, cost 4 ms 
[INFO ] 2024-07-18 18:48:33.289 - [任务 6][POLICY] - Node POLICY[db02ac52-0144-403d-9d5a-b8ba4ee501b6] start preload schema,table counts: 1 
[INFO ] 2024-07-18 18:48:33.290 - [任务 6][POLICY] - Node POLICY[db02ac52-0144-403d-9d5a-b8ba4ee501b6] preload schema finished, cost 0 ms 
[WARN ] 2024-07-18 18:48:34.062 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=5b7a21ed8bf146e9b2dea744d6e911bb0008903d8a194450b21c1df450b8f49d&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=e27b3fbe-f16e-4a11-be90-54503b5db7dd}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2880 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-18 18:48:38.200 - [任务 6][TESTE] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-07-18 18:49:34.157 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=5b7a21ed8bf146e9b2dea744d6e911bb0008903d8a194450b21c1df450b8f49d&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=62b09b46-ba1f-401d-9121-b13faa49cd01}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2879 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 18:50:34.275 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=5b7a21ed8bf146e9b2dea744d6e911bb0008903d8a194450b21c1df450b8f49d&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=c9c9e0b7-7be8-409a-9b1e-540f572b95ea}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2878 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 18:51:34.422 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=5b7a21ed8bf146e9b2dea744d6e911bb0008903d8a194450b21c1df450b8f49d&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=c936aac7-9bbe-4d35-b7ae-2d85c33e96dd}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2877 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 18:52:34.593 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=5b7a21ed8bf146e9b2dea744d6e911bb0008903d8a194450b21c1df450b8f49d&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=913f31b0-1fe3-48dd-9bab-dbc0f3ebbadf}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2876 time(s)
 - Period 60 second(s) 
[WARN ] 2024-07-18 18:53:34.728 - [任务 6][POLICY] - [Auto Retry] Method (iengine_find_schema) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: io.tapdata.exception.RestDoNotRetryException: RestException{uri='http://127.0.0.1:3000/api/MetadataInstances/tapTables?access_token=5b7a21ed8bf146e9b2dea744d6e911bb0008903d8a194450b21c1df450b8f49d&filter=%7B%20%22where%22%20%3A%20%7B%20%22qualified_name%22%20%3A%20%22T_mongodb_io_tapdata_1_0-SNAPSHOT__tapdata_heartbeat_table_6697799db92eda1a86f5135f_66979bd8b92eda1a86f521f4%22%20%7D%20%7D', method='GET', param=<{Cookie=[isAdmin=1;user_id=62bc5008d4958d013d97c7a6]}>, code='SystemError', data=null, reqId=9bc29938-e9da-4907-a1e2-38381181aafb}: System error: null
	com.tapdata.mongo.RestTemplateOperator.handleRequestFailed(RestTemplateOperator.java:635)
	com.tapdata.mongo.RestTemplateOperator.lambda$getOne$11(RestTemplateOperator.java:464)
	com.tapdata.mongo.RestTemplateOperator.retryWrap(RestTemplateOperator.java:726)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:422)
	com.tapdata.mongo.RestTemplateOperator.getOne(RestTemplateOperator.java:418)
	...
 - Remaining retry 2875 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-18 18:54:30.406 - [任务 6][POLICY] - Exception skipping - The current exception does not match the skip exception strategy, message: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2998/718494524@76948182 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
 
[ERROR] 2024-07-18 18:54:30.409 - [任务 6][POLICY] - code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2998/718494524@76948182 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more
 <-- Error Message -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2998/718494524@76948182 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more


<-- Simple Stack Trace -->
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	...

<-- Full Stack Trace -->
code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2998/718494524@76948182 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more

	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:135)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: code: 90002 | message: Submit sync task io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode$$Lambda$2998/718494524@76948182 failed, java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more

	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.submitSync(ThreadPoolExecutorEx.java:74)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doInit(HazelcastSourcePdkBaseNode.java:200)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doInit(HazelcastSourcePdkDataNode.java:132)
	... 13 more
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:68)
	... 6 more
Caused by: io.tapdata.exception.NodeException: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:209)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	... 6 more
Caused by: Map name: PdkStateMap_db02ac52-0144-403d-9d5a-b8ba4ee501b6
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:199)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doInit$2(HazelcastSourcePdkBaseNode.java:203)
	... 7 more
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: HazelcastInstance[[10.200.4.162]:5701] is not active!
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.getService(ProxyRegistry.java:91)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyRegistry.<init>(ProxyRegistry.java:73)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.lambda$new$0(ProxyServiceImpl.java:84)
	at com.hazelcast.internal.util.ConcurrencyUtil.getOrPutIfAbsent(ConcurrencyUtil.java:154)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getOrCreateRegistry(ProxyServiceImpl.java:144)
	at com.hazelcast.spi.impl.proxyservice.impl.ProxyServiceImpl.getDistributedObject(ProxyServiceImpl.java:152)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getDistributedObject(HazelcastInstanceImpl.java:355)
	at com.hazelcast.instance.impl.HazelcastInstanceImpl.getMap(HazelcastInstanceImpl.java:174)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:40)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 12 more

[INFO ] 2024-07-18 18:54:30.426 - [任务 6][POLICY] - Node POLICY[db02ac52-0144-403d-9d5a-b8ba4ee501b6] running status set to false 
[INFO ] 2024-07-18 18:54:30.426 - [任务 6][POLICY] - PDK connector node stopped: null 
[INFO ] 2024-07-18 18:54:30.427 - [任务 6][POLICY] - PDK connector node released: null 
[INFO ] 2024-07-18 18:54:30.427 - [任务 6][POLICY] - Node POLICY[db02ac52-0144-403d-9d5a-b8ba4ee501b6] schema data cleaned 
[INFO ] 2024-07-18 18:54:30.427 - [任务 6][POLICY] - Node POLICY[db02ac52-0144-403d-9d5a-b8ba4ee501b6] monitor closed 
[INFO ] 2024-07-18 18:54:30.429 - [任务 6][POLICY] - Node POLICY[db02ac52-0144-403d-9d5a-b8ba4ee501b6] close complete, cost 14 ms 
