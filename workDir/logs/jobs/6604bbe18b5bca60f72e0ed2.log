[INFO ] 2024-03-28 08:38:05.707 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:38:05.707 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:38:05.708 - [任务 29(100)][23783260-194b-40f1-be0f-dd541c062019] - Node 23783260-194b-40f1-be0f-dd541c062019[23783260-194b-40f1-be0f-dd541c062019] start preload schema,table counts: 0 
[INFO ] 2024-03-28 08:38:05.717 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:38:05.717 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:38:05.719 - [任务 29(100)][23783260-194b-40f1-be0f-dd541c062019] - Node 23783260-194b-40f1-be0f-dd541c062019[23783260-194b-40f1-be0f-dd541c062019] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:38:06.269 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:38:06.270 - [任务 29(100)][e0ffd84c-c839-4873-941f-92f78870972d] - Node e0ffd84c-c839-4873-941f-92f78870972d[e0ffd84c-c839-4873-941f-92f78870972d] start preload schema,table counts: 0 
[INFO ] 2024-03-28 08:38:06.270 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:38:06.270 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:38:06.270 - [任务 29(100)][e0ffd84c-c839-4873-941f-92f78870972d] - Node e0ffd84c-c839-4873-941f-92f78870972d[e0ffd84c-c839-4873-941f-92f78870972d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:38:06.271 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] preload schema finished, cost 3 ms 
[INFO ] 2024-03-28 08:38:11.146 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] running status set to false 
[INFO ] 2024-03-28 08:38:11.161 - [任务 29(100)][test1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-eb012f4d-83a1-4bbe-8e1d-6da309b4e58f 
[INFO ] 2024-03-28 08:38:11.161 - [任务 29(100)][test1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-eb012f4d-83a1-4bbe-8e1d-6da309b4e58f 
[INFO ] 2024-03-28 08:38:11.161 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] schema data cleaned 
[INFO ] 2024-03-28 08:38:11.161 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] monitor closed 
[INFO ] 2024-03-28 08:38:11.372 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] close complete, cost 20 ms 
[INFO ] 2024-03-28 08:38:11.899 - [任务 29(100)][6fdd1674-7960-407a-af42-6bf0eeb914f1] - Node 6fdd1674-7960-407a-af42-6bf0eeb914f1[6fdd1674-7960-407a-af42-6bf0eeb914f1] start preload schema,table counts: 0 
[INFO ] 2024-03-28 08:38:11.899 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:38:11.899 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:38:11.900 - [任务 29(100)][6fdd1674-7960-407a-af42-6bf0eeb914f1] - Node 6fdd1674-7960-407a-af42-6bf0eeb914f1[6fdd1674-7960-407a-af42-6bf0eeb914f1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:38:11.900 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:38:11.900 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:38:14.249 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:38:14.249 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:38:14.249 - [任务 29(100)][8e0fb213-ba48-4714-a2bd-4d0ce0c75cce] - Node 8e0fb213-ba48-4714-a2bd-4d0ce0c75cce[8e0fb213-ba48-4714-a2bd-4d0ce0c75cce] start preload schema,table counts: 0 
[INFO ] 2024-03-28 08:38:14.249 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:38:14.251 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:38:14.251 - [任务 29(100)][8e0fb213-ba48-4714-a2bd-4d0ce0c75cce] - Node 8e0fb213-ba48-4714-a2bd-4d0ce0c75cce[8e0fb213-ba48-4714-a2bd-4d0ce0c75cce] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:38:15.746 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:38:15.746 - [任务 29(100)][c7be7354-5aa4-4fd4-aba4-88421e5f42e5] - Node c7be7354-5aa4-4fd4-aba4-88421e5f42e5[c7be7354-5aa4-4fd4-aba4-88421e5f42e5] start preload schema,table counts: 0 
[INFO ] 2024-03-28 08:38:15.747 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:38:15.747 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:38:15.748 - [任务 29(100)][c7be7354-5aa4-4fd4-aba4-88421e5f42e5] - Node c7be7354-5aa4-4fd4-aba4-88421e5f42e5[c7be7354-5aa4-4fd4-aba4-88421e5f42e5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:38:15.748 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:38:17.053 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:38:17.053 - [任务 29(100)][f98fbe1f-67b2-42de-ad64-71d378a1532f] - Node f98fbe1f-67b2-42de-ad64-71d378a1532f[f98fbe1f-67b2-42de-ad64-71d378a1532f] start preload schema,table counts: 0 
[INFO ] 2024-03-28 08:38:17.053 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:38:17.053 - [任务 29(100)][f98fbe1f-67b2-42de-ad64-71d378a1532f] - Node f98fbe1f-67b2-42de-ad64-71d378a1532f[f98fbe1f-67b2-42de-ad64-71d378a1532f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:38:17.054 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:38:17.054 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:38:19.386 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] running status set to false 
[INFO ] 2024-03-28 08:38:19.386 - [任务 29(100)][test1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-eb012f4d-83a1-4bbe-8e1d-6da309b4e58f 
[INFO ] 2024-03-28 08:38:19.386 - [任务 29(100)][test1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-eb012f4d-83a1-4bbe-8e1d-6da309b4e58f 
[INFO ] 2024-03-28 08:38:19.387 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] schema data cleaned 
[INFO ] 2024-03-28 08:38:19.387 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] monitor closed 
[INFO ] 2024-03-28 08:38:19.592 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] close complete, cost 24 ms 
[INFO ] 2024-03-28 08:38:26.167 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] running status set to false 
[INFO ] 2024-03-28 08:38:26.168 - [任务 29(100)][test1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-eb012f4d-83a1-4bbe-8e1d-6da309b4e58f 
[INFO ] 2024-03-28 08:38:26.168 - [任务 29(100)][test1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-eb012f4d-83a1-4bbe-8e1d-6da309b4e58f 
[INFO ] 2024-03-28 08:38:26.168 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] schema data cleaned 
[INFO ] 2024-03-28 08:38:26.168 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] monitor closed 
[INFO ] 2024-03-28 08:38:26.188 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] close complete, cost 19 ms 
[INFO ] 2024-03-28 08:38:26.189 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] running status set to false 
[INFO ] 2024-03-28 08:38:26.192 - [任务 29(100)][e0ffd84c-c839-4873-941f-92f78870972d] - Node e0ffd84c-c839-4873-941f-92f78870972d[e0ffd84c-c839-4873-941f-92f78870972d] running status set to false 
[INFO ] 2024-03-28 08:38:26.192 - [任务 29(100)][e0ffd84c-c839-4873-941f-92f78870972d] - Node e0ffd84c-c839-4873-941f-92f78870972d[e0ffd84c-c839-4873-941f-92f78870972d] schema data cleaned 
[INFO ] 2024-03-28 08:38:26.192 - [任务 29(100)][e0ffd84c-c839-4873-941f-92f78870972d] - Node e0ffd84c-c839-4873-941f-92f78870972d[e0ffd84c-c839-4873-941f-92f78870972d] monitor closed 
[INFO ] 2024-03-28 08:38:26.196 - [任务 29(100)][e0ffd84c-c839-4873-941f-92f78870972d] - Node e0ffd84c-c839-4873-941f-92f78870972d[e0ffd84c-c839-4873-941f-92f78870972d] close complete, cost 0 ms 
[INFO ] 2024-03-28 08:38:26.196 - [任务 29(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-ee3c0a7a-5529-478f-ac8d-80369c91942e 
[INFO ] 2024-03-28 08:38:26.196 - [任务 29(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-ee3c0a7a-5529-478f-ac8d-80369c91942e 
[INFO ] 2024-03-28 08:38:26.196 - [任务 29(100)][增强JS] - [ScriptExecutorsManager-6604bbe18b5bca60f72e0ed2-7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 08:38:26.222 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] schema data cleaned 
[INFO ] 2024-03-28 08:38:26.222 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] monitor closed 
[INFO ] 2024-03-28 08:38:26.224 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] close complete, cost 35 ms 
[INFO ] 2024-03-28 08:38:26.224 - [任务 29(100)] - load tapTable task 6604bbe18b5bca60f72e0ed2-e0ffd84c-c839-4873-941f-92f78870972d complete, cost 20077ms 
[INFO ] 2024-03-28 08:38:33.534 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] running status set to false 
[INFO ] 2024-03-28 08:38:33.535 - [任务 29(100)][23783260-194b-40f1-be0f-dd541c062019] - Node 23783260-194b-40f1-be0f-dd541c062019[23783260-194b-40f1-be0f-dd541c062019] running status set to false 
[INFO ] 2024-03-28 08:38:33.535 - [任务 29(100)][23783260-194b-40f1-be0f-dd541c062019] - Node 23783260-194b-40f1-be0f-dd541c062019[23783260-194b-40f1-be0f-dd541c062019] schema data cleaned 
[INFO ] 2024-03-28 08:38:33.535 - [任务 29(100)][23783260-194b-40f1-be0f-dd541c062019] - Node 23783260-194b-40f1-be0f-dd541c062019[23783260-194b-40f1-be0f-dd541c062019] monitor closed 
[INFO ] 2024-03-28 08:38:33.535 - [任务 29(100)][23783260-194b-40f1-be0f-dd541c062019] - Node 23783260-194b-40f1-be0f-dd541c062019[23783260-194b-40f1-be0f-dd541c062019] close complete, cost 2 ms 
[INFO ] 2024-03-28 08:38:33.539 - [任务 29(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-048ae975-183e-45bf-8328-ae9d08b4a50b 
[INFO ] 2024-03-28 08:38:33.539 - [任务 29(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-048ae975-183e-45bf-8328-ae9d08b4a50b 
[INFO ] 2024-03-28 08:38:33.539 - [任务 29(100)][增强JS] - [ScriptExecutorsManager-6604bbe18b5bca60f72e0ed2-7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 08:38:33.540 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] schema data cleaned 
[INFO ] 2024-03-28 08:38:33.541 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] monitor closed 
[INFO ] 2024-03-28 08:38:33.542 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] close complete, cost 10 ms 
[INFO ] 2024-03-28 08:38:33.542 - [任务 29(100)] - load tapTable task 6604bbe18b5bca60f72e0ed2-23783260-194b-40f1-be0f-dd541c062019 complete, cost 27900ms 
[INFO ] 2024-03-28 08:38:34.405 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] running status set to false 
[INFO ] 2024-03-28 08:38:34.426 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] running status set to false 
[INFO ] 2024-03-28 08:38:34.426 - [任务 29(100)][test1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-eb012f4d-83a1-4bbe-8e1d-6da309b4e58f 
[INFO ] 2024-03-28 08:38:34.426 - [任务 29(100)][test1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-eb012f4d-83a1-4bbe-8e1d-6da309b4e58f 
[INFO ] 2024-03-28 08:38:34.427 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] schema data cleaned 
[INFO ] 2024-03-28 08:38:34.427 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] monitor closed 
[INFO ] 2024-03-28 08:38:34.427 - [任务 29(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-845eaefe-2d04-4976-83ec-e8a8af6f326f 
[INFO ] 2024-03-28 08:38:34.427 - [任务 29(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-845eaefe-2d04-4976-83ec-e8a8af6f326f 
[INFO ] 2024-03-28 08:38:34.427 - [任务 29(100)][增强JS] - [ScriptExecutorsManager-6604bbe18b5bca60f72e0ed2-7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 08:38:34.429 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] close complete, cost 30 ms 
[INFO ] 2024-03-28 08:38:34.429 - [任务 29(100)][f98fbe1f-67b2-42de-ad64-71d378a1532f] - Node f98fbe1f-67b2-42de-ad64-71d378a1532f[f98fbe1f-67b2-42de-ad64-71d378a1532f] running status set to false 
[INFO ] 2024-03-28 08:38:34.429 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] schema data cleaned 
[INFO ] 2024-03-28 08:38:34.429 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] monitor closed 
[INFO ] 2024-03-28 08:38:34.429 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] close complete, cost 4 ms 
[INFO ] 2024-03-28 08:38:34.429 - [任务 29(100)][f98fbe1f-67b2-42de-ad64-71d378a1532f] - Node f98fbe1f-67b2-42de-ad64-71d378a1532f[f98fbe1f-67b2-42de-ad64-71d378a1532f] schema data cleaned 
[INFO ] 2024-03-28 08:38:34.429 - [任务 29(100)][f98fbe1f-67b2-42de-ad64-71d378a1532f] - Node f98fbe1f-67b2-42de-ad64-71d378a1532f[f98fbe1f-67b2-42de-ad64-71d378a1532f] monitor closed 
[INFO ] 2024-03-28 08:38:34.429 - [任务 29(100)][f98fbe1f-67b2-42de-ad64-71d378a1532f] - Node f98fbe1f-67b2-42de-ad64-71d378a1532f[f98fbe1f-67b2-42de-ad64-71d378a1532f] close complete, cost 0 ms 
[INFO ] 2024-03-28 08:38:34.634 - [任务 29(100)] - load tapTable task 6604bbe18b5bca60f72e0ed2-f98fbe1f-67b2-42de-ad64-71d378a1532f complete, cost 17467ms 
[INFO ] 2024-03-28 08:38:51.304 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] running status set to false 
[INFO ] 2024-03-28 08:38:51.318 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] running status set to false 
[INFO ] 2024-03-28 08:38:51.318 - [任务 29(100)][c7be7354-5aa4-4fd4-aba4-88421e5f42e5] - Node c7be7354-5aa4-4fd4-aba4-88421e5f42e5[c7be7354-5aa4-4fd4-aba4-88421e5f42e5] running status set to false 
[INFO ] 2024-03-28 08:38:51.318 - [任务 29(100)][c7be7354-5aa4-4fd4-aba4-88421e5f42e5] - Node c7be7354-5aa4-4fd4-aba4-88421e5f42e5[c7be7354-5aa4-4fd4-aba4-88421e5f42e5] schema data cleaned 
[INFO ] 2024-03-28 08:38:51.318 - [任务 29(100)][c7be7354-5aa4-4fd4-aba4-88421e5f42e5] - Node c7be7354-5aa4-4fd4-aba4-88421e5f42e5[c7be7354-5aa4-4fd4-aba4-88421e5f42e5] monitor closed 
[INFO ] 2024-03-28 08:38:51.319 - [任务 29(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-e49e1f83-8c5b-4e2f-81f9-c3388a0ab238 
[INFO ] 2024-03-28 08:38:51.319 - [任务 29(100)][c7be7354-5aa4-4fd4-aba4-88421e5f42e5] - Node c7be7354-5aa4-4fd4-aba4-88421e5f42e5[c7be7354-5aa4-4fd4-aba4-88421e5f42e5] close complete, cost 2 ms 
[INFO ] 2024-03-28 08:38:51.319 - [任务 29(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-e49e1f83-8c5b-4e2f-81f9-c3388a0ab238 
[INFO ] 2024-03-28 08:38:51.319 - [任务 29(100)][增强JS] - [ScriptExecutorsManager-6604bbe18b5bca60f72e0ed2-7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 08:38:51.321 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] schema data cleaned 
[INFO ] 2024-03-28 08:38:51.321 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] monitor closed 
[INFO ] 2024-03-28 08:38:51.321 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] close complete, cost 8 ms 
[INFO ] 2024-03-28 08:38:51.326 - [任务 29(100)][test1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-eb012f4d-83a1-4bbe-8e1d-6da309b4e58f 
[INFO ] 2024-03-28 08:38:51.326 - [任务 29(100)][test1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-eb012f4d-83a1-4bbe-8e1d-6da309b4e58f 
[INFO ] 2024-03-28 08:38:51.326 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] schema data cleaned 
[INFO ] 2024-03-28 08:38:51.326 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] monitor closed 
[INFO ] 2024-03-28 08:38:51.326 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] close complete, cost 31 ms 
[INFO ] 2024-03-28 08:38:51.327 - [任务 29(100)] - load tapTable task 6604bbe18b5bca60f72e0ed2-c7be7354-5aa4-4fd4-aba4-88421e5f42e5 complete, cost 35633ms 
[INFO ] 2024-03-28 08:39:06.002 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] running status set to false 
[INFO ] 2024-03-28 08:39:06.021 - [任务 29(100)][test1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-eb012f4d-83a1-4bbe-8e1d-6da309b4e58f 
[INFO ] 2024-03-28 08:39:06.021 - [任务 29(100)][test1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-eb012f4d-83a1-4bbe-8e1d-6da309b4e58f 
[INFO ] 2024-03-28 08:39:06.023 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] schema data cleaned 
[INFO ] 2024-03-28 08:39:06.023 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] monitor closed 
[INFO ] 2024-03-28 08:39:06.040 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] close complete, cost 25 ms 
[INFO ] 2024-03-28 08:39:06.040 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] running status set to false 
[INFO ] 2024-03-28 08:39:06.046 - [任务 29(100)][8e0fb213-ba48-4714-a2bd-4d0ce0c75cce] - Node 8e0fb213-ba48-4714-a2bd-4d0ce0c75cce[8e0fb213-ba48-4714-a2bd-4d0ce0c75cce] running status set to false 
[INFO ] 2024-03-28 08:39:06.047 - [任务 29(100)][8e0fb213-ba48-4714-a2bd-4d0ce0c75cce] - Node 8e0fb213-ba48-4714-a2bd-4d0ce0c75cce[8e0fb213-ba48-4714-a2bd-4d0ce0c75cce] schema data cleaned 
[INFO ] 2024-03-28 08:39:06.047 - [任务 29(100)][8e0fb213-ba48-4714-a2bd-4d0ce0c75cce] - Node 8e0fb213-ba48-4714-a2bd-4d0ce0c75cce[8e0fb213-ba48-4714-a2bd-4d0ce0c75cce] monitor closed 
[INFO ] 2024-03-28 08:39:06.048 - [任务 29(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-a8e2a7e1-7b91-4de5-b9bc-d6d6e0978f8a 
[INFO ] 2024-03-28 08:39:06.048 - [任务 29(100)][8e0fb213-ba48-4714-a2bd-4d0ce0c75cce] - Node 8e0fb213-ba48-4714-a2bd-4d0ce0c75cce[8e0fb213-ba48-4714-a2bd-4d0ce0c75cce] close complete, cost 2 ms 
[INFO ] 2024-03-28 08:39:06.048 - [任务 29(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-a8e2a7e1-7b91-4de5-b9bc-d6d6e0978f8a 
[INFO ] 2024-03-28 08:39:06.052 - [任务 29(100)][增强JS] - [ScriptExecutorsManager-6604bbe18b5bca60f72e0ed2-7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 08:39:06.052 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] schema data cleaned 
[INFO ] 2024-03-28 08:39:06.052 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] monitor closed 
[INFO ] 2024-03-28 08:39:06.052 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] close complete, cost 14 ms 
[INFO ] 2024-03-28 08:39:06.261 - [任务 29(100)] - load tapTable task 6604bbe18b5bca60f72e0ed2-8e0fb213-ba48-4714-a2bd-4d0ce0c75cce complete, cost 51837ms 
[INFO ] 2024-03-28 08:39:14.448 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] running status set to false 
[INFO ] 2024-03-28 08:39:14.450 - [任务 29(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-2d9238d4-24a4-4ce7-95f2-1b37d0766a57 
[INFO ] 2024-03-28 08:39:14.450 - [任务 29(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-2d9238d4-24a4-4ce7-95f2-1b37d0766a57 
[INFO ] 2024-03-28 08:39:14.450 - [任务 29(100)][增强JS] - [ScriptExecutorsManager-6604bbe18b5bca60f72e0ed2-7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 08:39:14.451 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] schema data cleaned 
[INFO ] 2024-03-28 08:39:14.451 - [任务 29(100)][6fdd1674-7960-407a-af42-6bf0eeb914f1] - Node 6fdd1674-7960-407a-af42-6bf0eeb914f1[6fdd1674-7960-407a-af42-6bf0eeb914f1] running status set to false 
[INFO ] 2024-03-28 08:39:14.451 - [任务 29(100)][6fdd1674-7960-407a-af42-6bf0eeb914f1] - Node 6fdd1674-7960-407a-af42-6bf0eeb914f1[6fdd1674-7960-407a-af42-6bf0eeb914f1] schema data cleaned 
[INFO ] 2024-03-28 08:39:14.451 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] monitor closed 
[INFO ] 2024-03-28 08:39:14.451 - [任务 29(100)][6fdd1674-7960-407a-af42-6bf0eeb914f1] - Node 6fdd1674-7960-407a-af42-6bf0eeb914f1[6fdd1674-7960-407a-af42-6bf0eeb914f1] monitor closed 
[INFO ] 2024-03-28 08:39:14.452 - [任务 29(100)][6fdd1674-7960-407a-af42-6bf0eeb914f1] - Node 6fdd1674-7960-407a-af42-6bf0eeb914f1[6fdd1674-7960-407a-af42-6bf0eeb914f1] close complete, cost 0 ms 
[INFO ] 2024-03-28 08:39:14.452 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] close complete, cost 4 ms 
[INFO ] 2024-03-28 08:39:14.653 - [任务 29(100)] - load tapTable task 6604bbe18b5bca60f72e0ed2-6fdd1674-7960-407a-af42-6bf0eeb914f1 complete, cost 62607ms 
[INFO ] 2024-03-28 08:39:46.927 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:39:46.928 - [任务 29(100)][08f0170d-a189-404f-ba3b-2d3de26ffdf3] - Node 08f0170d-a189-404f-ba3b-2d3de26ffdf3[08f0170d-a189-404f-ba3b-2d3de26ffdf3] start preload schema,table counts: 0 
[INFO ] 2024-03-28 08:39:46.929 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:39:46.929 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:39:46.929 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:39:46.929 - [任务 29(100)][08f0170d-a189-404f-ba3b-2d3de26ffdf3] - Node 08f0170d-a189-404f-ba3b-2d3de26ffdf3[08f0170d-a189-404f-ba3b-2d3de26ffdf3] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:39:53.701 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] running status set to false 
[INFO ] 2024-03-28 08:39:53.737 - [任务 29(100)][test1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-eb012f4d-83a1-4bbe-8e1d-6da309b4e58f 
[INFO ] 2024-03-28 08:39:53.737 - [任务 29(100)][test1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-eb012f4d-83a1-4bbe-8e1d-6da309b4e58f 
[INFO ] 2024-03-28 08:39:53.737 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] schema data cleaned 
[INFO ] 2024-03-28 08:39:53.738 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] monitor closed 
[INFO ] 2024-03-28 08:39:53.738 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] close complete, cost 41 ms 
[INFO ] 2024-03-28 08:39:53.792 - [任务 29(100)][5efda91a-c53f-4fe3-b167-3f1ad8bf4547] - Node 5efda91a-c53f-4fe3-b167-3f1ad8bf4547[5efda91a-c53f-4fe3-b167-3f1ad8bf4547] start preload schema,table counts: 0 
[INFO ] 2024-03-28 08:39:53.796 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:39:53.796 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:39:53.797 - [任务 29(100)][5efda91a-c53f-4fe3-b167-3f1ad8bf4547] - Node 5efda91a-c53f-4fe3-b167-3f1ad8bf4547[5efda91a-c53f-4fe3-b167-3f1ad8bf4547] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:39:53.797 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 08:39:53.797 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 08:39:53.934 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:39:53.934 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:39:53.934 - [任务 29(100)][6c9463c0-94ed-4f7c-bf96-540296866b83] - Node 6c9463c0-94ed-4f7c-bf96-540296866b83[6c9463c0-94ed-4f7c-bf96-540296866b83] start preload schema,table counts: 0 
[INFO ] 2024-03-28 08:39:53.936 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 08:39:53.936 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:39:53.936 - [任务 29(100)][6c9463c0-94ed-4f7c-bf96-540296866b83] - Node 6c9463c0-94ed-4f7c-bf96-540296866b83[6c9463c0-94ed-4f7c-bf96-540296866b83] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:39:55.465 - [任务 29(100)][8a3464ae-a4cd-4d39-9477-694e398b872a] - Node 8a3464ae-a4cd-4d39-9477-694e398b872a[8a3464ae-a4cd-4d39-9477-694e398b872a] start preload schema,table counts: 0 
[INFO ] 2024-03-28 08:39:55.465 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:39:55.465 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:39:55.465 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:39:55.466 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:39:55.466 - [任务 29(100)][8a3464ae-a4cd-4d39-9477-694e398b872a] - Node 8a3464ae-a4cd-4d39-9477-694e398b872a[8a3464ae-a4cd-4d39-9477-694e398b872a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:39:56.897 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:39:56.897 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:39:56.897 - [任务 29(100)][c04bbb95-816e-462b-8a84-0638abc835c9] - Node c04bbb95-816e-462b-8a84-0638abc835c9[c04bbb95-816e-462b-8a84-0638abc835c9] start preload schema,table counts: 0 
[INFO ] 2024-03-28 08:39:56.897 - [任务 29(100)][c04bbb95-816e-462b-8a84-0638abc835c9] - Node c04bbb95-816e-462b-8a84-0638abc835c9[c04bbb95-816e-462b-8a84-0638abc835c9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:39:56.898 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:39:57.102 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 08:40:00.699 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] running status set to false 
[INFO ] 2024-03-28 08:40:00.704 - [任务 29(100)][08f0170d-a189-404f-ba3b-2d3de26ffdf3] - Node 08f0170d-a189-404f-ba3b-2d3de26ffdf3[08f0170d-a189-404f-ba3b-2d3de26ffdf3] running status set to false 
[INFO ] 2024-03-28 08:40:00.704 - [任务 29(100)][08f0170d-a189-404f-ba3b-2d3de26ffdf3] - Node 08f0170d-a189-404f-ba3b-2d3de26ffdf3[08f0170d-a189-404f-ba3b-2d3de26ffdf3] schema data cleaned 
[INFO ] 2024-03-28 08:40:00.705 - [任务 29(100)][08f0170d-a189-404f-ba3b-2d3de26ffdf3] - Node 08f0170d-a189-404f-ba3b-2d3de26ffdf3[08f0170d-a189-404f-ba3b-2d3de26ffdf3] monitor closed 
[INFO ] 2024-03-28 08:40:00.705 - [任务 29(100)][08f0170d-a189-404f-ba3b-2d3de26ffdf3] - Node 08f0170d-a189-404f-ba3b-2d3de26ffdf3[08f0170d-a189-404f-ba3b-2d3de26ffdf3] close complete, cost 3 ms 
[INFO ] 2024-03-28 08:40:00.709 - [任务 29(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Test-911b928c-bb06-485b-a618-ac1603918b9f 
[INFO ] 2024-03-28 08:40:00.709 - [任务 29(100)][增强JS] - PDK connector node released: ScriptExecutor-Test-911b928c-bb06-485b-a618-ac1603918b9f 
[INFO ] 2024-03-28 08:40:00.709 - [任务 29(100)][增强JS] - [ScriptExecutorsManager-6604bbe18b5bca60f72e0ed2-7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-28 08:40:00.711 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] schema data cleaned 
[INFO ] 2024-03-28 08:40:00.711 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] monitor closed 
[INFO ] 2024-03-28 08:40:00.711 - [任务 29(100)][增强JS] - Node 增强JS[7d56f7ef-2ef2-4020-80f5-3e2e9a0a9fd4] close complete, cost 14 ms 
[INFO ] 2024-03-28 08:40:00.712 - [任务 29(100)] - load tapTable task 6604bbe18b5bca60f72e0ed2-08f0170d-a189-404f-ba3b-2d3de26ffdf3 complete, cost 13835ms 
[INFO ] 2024-03-28 08:40:00.873 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] running status set to false 
[INFO ] 2024-03-28 08:40:00.873 - [任务 29(100)][test1] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-eb012f4d-83a1-4bbe-8e1d-6da309b4e58f 
[INFO ] 2024-03-28 08:40:00.874 - [任务 29(100)][test1] - PDK connector node released: HazelcastSampleSourcePdkDataNode-eb012f4d-83a1-4bbe-8e1d-6da309b4e58f 
[INFO ] 2024-03-28 08:40:00.874 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] schema data cleaned 
[INFO ] 2024-03-28 08:40:00.874 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] monitor closed 
[INFO ] 2024-03-28 08:40:01.076 - [任务 29(100)][test1] - Node test1[eb012f4d-83a1-4bbe-8e1d-6da309b4e58f] close complete, cost 25 ms 
[ERROR] 2024-03-28 08:40:04.853 - [任务 29(100)] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.util.concurrent.CompletionException: com.hazelcast.core.HazelcastInstanceNotActiveException: Hazelcast instance is not active!
	at java.util.concurrent.CompletableFuture.reportJoin(CompletableFuture.java:375)
	at java.util.concurrent.CompletableFuture.join(CompletableFuture.java:1947)
	at com.hazelcast.jet.Job.join(Job.java:78)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.join(HazelcastTaskClient.java:187)
	at io.tapdata.websocket.handler.DeduceSchemaHandler$1.execTask(DeduceSchemaHandler.java:139)
	at io.tapdata.websocket.handler.DeduceSchemaHandler$1.loadTapTable(DeduceSchemaHandler.java:95)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:116)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:176)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:323)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:320)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:313)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:596)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:596)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:145)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:49)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:288)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: Hazelcast instance is not active!
	at com.hazelcast.spi.impl.operationservice.impl.InvocationRegistry.shutdown(InvocationRegistry.java:229)
	at com.hazelcast.spi.impl.operationservice.impl.OperationServiceImpl.shutdownInvocations(OperationServiceImpl.java:534)
	at com.hazelcast.spi.impl.NodeEngineImpl.shutdown(NodeEngineImpl.java:558)
	at com.hazelcast.instance.impl.Node.shutdownServices(Node.java:627)
	at com.hazelcast.instance.impl.Node.shutdown(Node.java:563)
	at com.hazelcast.instance.impl.LifecycleServiceImpl.shutdown(LifecycleServiceImpl.java:101)
	at com.hazelcast.instance.impl.LifecycleServiceImpl.terminate(LifecycleServiceImpl.java:89)
	at com.hazelcast.instance.impl.Node$NodeShutdownHookThread.run(Node.java:806)

[ERROR] 2024-03-28 08:40:04.856 - [任务 29(100)] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.util.concurrent.CompletionException: com.hazelcast.core.HazelcastInstanceNotActiveException: Hazelcast instance is not active!
	at java.util.concurrent.CompletableFuture.reportJoin(CompletableFuture.java:375)
	at java.util.concurrent.CompletableFuture.join(CompletableFuture.java:1947)
	at com.hazelcast.jet.Job.join(Job.java:78)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.join(HazelcastTaskClient.java:187)
	at io.tapdata.websocket.handler.DeduceSchemaHandler$1.execTask(DeduceSchemaHandler.java:139)
	at io.tapdata.websocket.handler.DeduceSchemaHandler$1.loadTapTable(DeduceSchemaHandler.java:95)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:116)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:176)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:323)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:320)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:313)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:596)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:596)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:145)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:49)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:288)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: Hazelcast instance is not active!
	at com.hazelcast.spi.impl.operationservice.impl.InvocationRegistry.shutdown(InvocationRegistry.java:229)
	at com.hazelcast.spi.impl.operationservice.impl.OperationServiceImpl.shutdownInvocations(OperationServiceImpl.java:534)
	at com.hazelcast.spi.impl.NodeEngineImpl.shutdown(NodeEngineImpl.java:558)
	at com.hazelcast.instance.impl.Node.shutdownServices(Node.java:627)
	at com.hazelcast.instance.impl.Node.shutdown(Node.java:563)
	at com.hazelcast.instance.impl.LifecycleServiceImpl.shutdown(LifecycleServiceImpl.java:101)
	at com.hazelcast.instance.impl.LifecycleServiceImpl.terminate(LifecycleServiceImpl.java:89)
	at com.hazelcast.instance.impl.Node$NodeShutdownHookThread.run(Node.java:806)

[ERROR] 2024-03-28 08:40:04.856 - [任务 29(100)] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.util.concurrent.CompletionException: com.hazelcast.core.HazelcastInstanceNotActiveException: Hazelcast instance is not active!
	at java.util.concurrent.CompletableFuture.reportJoin(CompletableFuture.java:375)
	at java.util.concurrent.CompletableFuture.join(CompletableFuture.java:1947)
	at com.hazelcast.jet.Job.join(Job.java:78)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.join(HazelcastTaskClient.java:187)
	at io.tapdata.websocket.handler.DeduceSchemaHandler$1.execTask(DeduceSchemaHandler.java:139)
	at io.tapdata.websocket.handler.DeduceSchemaHandler$1.loadTapTable(DeduceSchemaHandler.java:95)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:116)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:176)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:323)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:320)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:313)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:596)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:596)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:145)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:49)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:288)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: Hazelcast instance is not active!
	at com.hazelcast.spi.impl.operationservice.impl.InvocationRegistry.shutdown(InvocationRegistry.java:229)
	at com.hazelcast.spi.impl.operationservice.impl.OperationServiceImpl.shutdownInvocations(OperationServiceImpl.java:534)
	at com.hazelcast.spi.impl.NodeEngineImpl.shutdown(NodeEngineImpl.java:558)
	at com.hazelcast.instance.impl.Node.shutdownServices(Node.java:627)
	at com.hazelcast.instance.impl.Node.shutdown(Node.java:563)
	at com.hazelcast.instance.impl.LifecycleServiceImpl.shutdown(LifecycleServiceImpl.java:101)
	at com.hazelcast.instance.impl.LifecycleServiceImpl.terminate(LifecycleServiceImpl.java:89)
	at com.hazelcast.instance.impl.Node$NodeShutdownHookThread.run(Node.java:806)

[ERROR] 2024-03-28 08:40:04.856 - [任务 29(100)] - An error occurred while obtaining the results of model deduction <-- Full Stack Trace -->
java.util.concurrent.CompletionException: com.hazelcast.core.HazelcastInstanceNotActiveException: Hazelcast instance is not active!
	at java.util.concurrent.CompletableFuture.reportJoin(CompletableFuture.java:375)
	at java.util.concurrent.CompletableFuture.join(CompletableFuture.java:1947)
	at com.hazelcast.jet.Job.join(Job.java:78)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskClient.join(HazelcastTaskClient.java:187)
	at io.tapdata.websocket.handler.DeduceSchemaHandler$1.execTask(DeduceSchemaHandler.java:139)
	at io.tapdata.websocket.handler.DeduceSchemaHandler$1.loadTapTable(DeduceSchemaHandler.java:95)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.getTapTable(ScriptProcessNode.java:50)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:116)
	at com.tapdata.tm.commons.dag.process.script.ScriptProcessNode.loadSchema(ScriptProcessNode.java:39)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:176)
	at com.tapdata.tm.commons.dag.Node.lambda$next$4(Node.java:323)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.Node.next(Node.java:320)
	at com.tapdata.tm.commons.dag.Node.transformSchema(Node.java:313)
	at com.tapdata.tm.commons.dag.DAG.lambda$transformSchema$9(DAG.java:596)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.tapdata.tm.commons.dag.DAG.transformSchema(DAG.java:596)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:145)
	at io.tapdata.websocket.handler.DeduceSchemaHandler.handle(DeduceSchemaHandler.java:49)
	at io.tapdata.websocket.WebSocketEventHandler.handle(WebSocketEventHandler.java:60)
	at io.tapdata.websocket.ManagementWebsocketHandler.lambda$handleMessage$6(ManagementWebsocketHandler.java:288)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.hazelcast.core.HazelcastInstanceNotActiveException: Hazelcast instance is not active!
	at com.hazelcast.spi.impl.operationservice.impl.InvocationRegistry.shutdown(InvocationRegistry.java:229)
	at com.hazelcast.spi.impl.operationservice.impl.OperationServiceImpl.shutdownInvocations(OperationServiceImpl.java:534)
	at com.hazelcast.spi.impl.NodeEngineImpl.shutdown(NodeEngineImpl.java:558)
	at com.hazelcast.instance.impl.Node.shutdownServices(Node.java:627)
	at com.hazelcast.instance.impl.Node.shutdown(Node.java:563)
	at com.hazelcast.instance.impl.LifecycleServiceImpl.shutdown(LifecycleServiceImpl.java:101)
	at com.hazelcast.instance.impl.LifecycleServiceImpl.terminate(LifecycleServiceImpl.java:89)
	at com.hazelcast.instance.impl.Node$NodeShutdownHookThread.run(Node.java:806)

