[INFO ] 2024-07-02 16:31:59.305 - [Task 4(101)] - 6682d405494d8d2a5c957f36 task start 
[INFO ] 2024-07-02 16:31:59.855 - [Task 4(101)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] start preload schema,table counts: 1 
[INFO ] 2024-07-02 16:31:59.856 - [Task 4(101)][c5fd46b2-0a32-40d1-96af-1c2c64bdc7dc] - Node c5fd46b2-0a32-40d1-96af-1c2c64bdc7dc[c5fd46b2-0a32-40d1-96af-1c2c64bdc7dc] start preload schema,table counts: 0 
[INFO ] 2024-07-02 16:31:59.857 - [Task 4(101)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] start preload schema,table counts: 1 
[INFO ] 2024-07-02 16:31:59.857 - [Task 4(101)][c5fd46b2-0a32-40d1-96af-1c2c64bdc7dc] - Node c5fd46b2-0a32-40d1-96af-1c2c64bdc7dc[c5fd46b2-0a32-40d1-96af-1c2c64bdc7dc] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 16:32:00.624 - [Task 4(101)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] preload schema finished, cost 744 ms 
[INFO ] 2024-07-02 16:32:00.635 - [Task 4(101)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] preload schema finished, cost 741 ms 
[INFO ] 2024-07-02 16:32:01.913 - [Task 4(101)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] running status set to false 
[INFO ] 2024-07-02 16:32:01.954 - [Task 4(101)][POLICY] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-32a9e251-e83a-4311-915f-f151503bb049 
[INFO ] 2024-07-02 16:32:01.957 - [Task 4(101)][POLICY] - PDK connector node released: HazelcastSampleSourcePdkDataNode-32a9e251-e83a-4311-915f-f151503bb049 
[INFO ] 2024-07-02 16:32:01.957 - [Task 4(101)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] schema data cleaned 
[INFO ] 2024-07-02 16:32:01.965 - [Task 4(101)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] monitor closed 
[INFO ] 2024-07-02 16:32:01.966 - [Task 4(101)][POLICY] - Node POLICY[32a9e251-e83a-4311-915f-f151503bb049] close complete, cost 85 ms 
[INFO ] 2024-07-02 16:32:02.039 - [Task 4(101)][增强JS] - INITIAL_SYNC 
[INFO ] 2024-07-02 16:32:02.040 - [Task 4(101)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] running status set to false 
[INFO ] 2024-07-02 16:32:02.051 - [Task 4(101)][c5fd46b2-0a32-40d1-96af-1c2c64bdc7dc] - Node c5fd46b2-0a32-40d1-96af-1c2c64bdc7dc[c5fd46b2-0a32-40d1-96af-1c2c64bdc7dc] running status set to false 
[INFO ] 2024-07-02 16:32:02.051 - [Task 4(101)][c5fd46b2-0a32-40d1-96af-1c2c64bdc7dc] - Node c5fd46b2-0a32-40d1-96af-1c2c64bdc7dc[c5fd46b2-0a32-40d1-96af-1c2c64bdc7dc] schema data cleaned 
[INFO ] 2024-07-02 16:32:02.052 - [Task 4(101)][c5fd46b2-0a32-40d1-96af-1c2c64bdc7dc] - Node c5fd46b2-0a32-40d1-96af-1c2c64bdc7dc[c5fd46b2-0a32-40d1-96af-1c2c64bdc7dc] monitor closed 
[INFO ] 2024-07-02 16:32:02.078 - [Task 4(101)][c5fd46b2-0a32-40d1-96af-1c2c64bdc7dc] - Node c5fd46b2-0a32-40d1-96af-1c2c64bdc7dc[c5fd46b2-0a32-40d1-96af-1c2c64bdc7dc] close complete, cost 11 ms 
[INFO ] 2024-07-02 16:32:02.078 - [Task 4(101)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-0ca2c607-d462-4a87-934c-f11fb22a8ec8 
[INFO ] 2024-07-02 16:32:02.079 - [Task 4(101)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-0ca2c607-d462-4a87-934c-f11fb22a8ec8 
[INFO ] 2024-07-02 16:32:02.080 - [Task 4(101)][增强JS] - [ScriptExecutorsManager-6682d405494d8d2a5c957f36-bca58ceb-88e7-4364-bfef-bde381562d81-6674feb868ca1e3afc2a0d99] schema data cleaned 
[INFO ] 2024-07-02 16:32:02.090 - [Task 4(101)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] schema data cleaned 
[INFO ] 2024-07-02 16:32:02.091 - [Task 4(101)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] monitor closed 
[INFO ] 2024-07-02 16:32:02.092 - [Task 4(101)][增强JS] - Node 增强JS[bca58ceb-88e7-4364-bfef-bde381562d81] close complete, cost 57 ms 
[INFO ] 2024-07-02 16:32:02.136 - [Task 4(101)] - Closed task monitor(s)
null 
[INFO ] 2024-07-02 16:32:02.139 - [Task 4(101)] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@9fc8dbf 
[INFO ] 2024-07-02 16:32:02.139 - [Task 4(101)] - Stopped task aspect(s) 
[INFO ] 2024-07-02 16:32:02.348 - [Task 4(101)] - test run task 6682d405494d8d2a5c957f36 complete, cost 3666ms 
