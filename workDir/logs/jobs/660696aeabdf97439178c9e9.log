[INFO ] 2024-03-29 18:23:44.649 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:23:44.650 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:23:44.650 - [suppliers_import_import_import(100)][7e82a682-b2bf-4885-b99a-7637d716e4a5] - Node 7e82a682-b2bf-4885-b99a-7637d716e4a5[7e82a682-b2bf-4885-b99a-7637d716e4a5] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:23:44.651 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:23:44.651 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:23:44.651 - [suppliers_import_import_import(100)][7e82a682-b2bf-4885-b99a-7637d716e4a5] - Node 7e82a682-b2bf-4885-b99a-7637d716e4a5[7e82a682-b2bf-4885-b99a-7637d716e4a5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:23:44.722 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:23:44.927 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@55e820ad error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@55e820ad error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@55e820ad error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:23:45.158 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:23:45.158 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:23:45.158 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:23:45.158 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:23:45.363 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:23:45.363 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 22 ms 
[INFO ] 2024-03-29 18:23:47.271 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:23:47.271 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:23:47.271 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:23:47.271 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:23:47.271 - [suppliers_import_import_import(100)][7e82a682-b2bf-4885-b99a-7637d716e4a5] - Node 7e82a682-b2bf-4885-b99a-7637d716e4a5[7e82a682-b2bf-4885-b99a-7637d716e4a5] running status set to false 
[INFO ] 2024-03-29 18:23:47.271 - [suppliers_import_import_import(100)][7e82a682-b2bf-4885-b99a-7637d716e4a5] - Node 7e82a682-b2bf-4885-b99a-7637d716e4a5[7e82a682-b2bf-4885-b99a-7637d716e4a5] schema data cleaned 
[INFO ] 2024-03-29 18:23:47.271 - [suppliers_import_import_import(100)][7e82a682-b2bf-4885-b99a-7637d716e4a5] - Node 7e82a682-b2bf-4885-b99a-7637d716e4a5[7e82a682-b2bf-4885-b99a-7637d716e4a5] monitor closed 
[INFO ] 2024-03-29 18:23:47.271 - [suppliers_import_import_import(100)][7e82a682-b2bf-4885-b99a-7637d716e4a5] - Node 7e82a682-b2bf-4885-b99a-7637d716e4a5[7e82a682-b2bf-4885-b99a-7637d716e4a5] close complete, cost 2 ms 
[INFO ] 2024-03-29 18:23:47.476 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-7e82a682-b2bf-4885-b99a-7637d716e4a5 complete, cost 2723ms 
[INFO ] 2024-03-29 18:23:55.790 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:23:55.790 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:23:55.791 - [suppliers_import_import_import(100)][dc7b57e2-1856-4b54-aa1a-e793df859163] - Node dc7b57e2-1856-4b54-aa1a-e793df859163[dc7b57e2-1856-4b54-aa1a-e793df859163] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:23:55.791 - [suppliers_import_import_import(100)][dc7b57e2-1856-4b54-aa1a-e793df859163] - Node dc7b57e2-1856-4b54-aa1a-e793df859163[dc7b57e2-1856-4b54-aa1a-e793df859163] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:23:55.791 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:23:55.791 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:23:55.858 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:23:56.050 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6bdfdbd error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6bdfdbd error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6bdfdbd error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:23:56.065 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:23:56.073 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:23:56.073 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:23:56.073 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:23:56.073 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:23:56.278 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 25 ms 
[INFO ] 2024-03-29 18:23:58.405 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:23:58.405 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:23:58.405 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:23:58.405 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 2 ms 
[INFO ] 2024-03-29 18:23:58.410 - [suppliers_import_import_import(100)][dc7b57e2-1856-4b54-aa1a-e793df859163] - Node dc7b57e2-1856-4b54-aa1a-e793df859163[dc7b57e2-1856-4b54-aa1a-e793df859163] running status set to false 
[INFO ] 2024-03-29 18:23:58.412 - [suppliers_import_import_import(100)][dc7b57e2-1856-4b54-aa1a-e793df859163] - Node dc7b57e2-1856-4b54-aa1a-e793df859163[dc7b57e2-1856-4b54-aa1a-e793df859163] schema data cleaned 
[INFO ] 2024-03-29 18:23:58.412 - [suppliers_import_import_import(100)][dc7b57e2-1856-4b54-aa1a-e793df859163] - Node dc7b57e2-1856-4b54-aa1a-e793df859163[dc7b57e2-1856-4b54-aa1a-e793df859163] monitor closed 
[INFO ] 2024-03-29 18:23:58.416 - [suppliers_import_import_import(100)][dc7b57e2-1856-4b54-aa1a-e793df859163] - Node dc7b57e2-1856-4b54-aa1a-e793df859163[dc7b57e2-1856-4b54-aa1a-e793df859163] close complete, cost 3 ms 
[INFO ] 2024-03-29 18:23:58.416 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-dc7b57e2-1856-4b54-aa1a-e793df859163 complete, cost 2737ms 
[INFO ] 2024-03-29 18:23:58.957 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:23:58.957 - [suppliers_import_import_import(100)][ff89a465-f00e-4b6d-b17c-7b327512811b] - Node ff89a465-f00e-4b6d-b17c-7b327512811b[ff89a465-f00e-4b6d-b17c-7b327512811b] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:23:58.958 - [suppliers_import_import_import(100)][ff89a465-f00e-4b6d-b17c-7b327512811b] - Node ff89a465-f00e-4b6d-b17c-7b327512811b[ff89a465-f00e-4b6d-b17c-7b327512811b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:23:58.958 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:23:58.958 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:23:58.958 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:23:58.958 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:23:59.159 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@e1de6c9 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@e1de6c9 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@e1de6c9 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:23:59.219 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:23:59.219 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:23:59.219 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:23:59.219 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:23:59.219 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:23:59.424 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 14 ms 
[INFO ] 2024-03-29 18:24:00.829 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:00.830 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:00.836 - [suppliers_import_import_import(100)][862c8ba4-bca0-4bd6-aedf-f44ec7f5c55f] - Node 862c8ba4-bca0-4bd6-aedf-f44ec7f5c55f[862c8ba4-bca0-4bd6-aedf-f44ec7f5c55f] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:24:00.845 - [suppliers_import_import_import(100)][862c8ba4-bca0-4bd6-aedf-f44ec7f5c55f] - Node 862c8ba4-bca0-4bd6-aedf-f44ec7f5c55f[862c8ba4-bca0-4bd6-aedf-f44ec7f5c55f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:00.846 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 5 ms 
[INFO ] 2024-03-29 18:24:00.846 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:00.925 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:24:01.124 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6089e75 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6089e75 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6089e75 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:24:01.136 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:24:01.146 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:01.146 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:01.146 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:24:01.147 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:24:01.147 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 27 ms 
[INFO ] 2024-03-29 18:24:01.554 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:24:01.554 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:24:01.555 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:24:01.555 - [suppliers_import_import_import(100)][ff89a465-f00e-4b6d-b17c-7b327512811b] - Node ff89a465-f00e-4b6d-b17c-7b327512811b[ff89a465-f00e-4b6d-b17c-7b327512811b] running status set to false 
[INFO ] 2024-03-29 18:24:01.555 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:24:01.555 - [suppliers_import_import_import(100)][ff89a465-f00e-4b6d-b17c-7b327512811b] - Node ff89a465-f00e-4b6d-b17c-7b327512811b[ff89a465-f00e-4b6d-b17c-7b327512811b] schema data cleaned 
[INFO ] 2024-03-29 18:24:01.556 - [suppliers_import_import_import(100)][ff89a465-f00e-4b6d-b17c-7b327512811b] - Node ff89a465-f00e-4b6d-b17c-7b327512811b[ff89a465-f00e-4b6d-b17c-7b327512811b] monitor closed 
[INFO ] 2024-03-29 18:24:01.556 - [suppliers_import_import_import(100)][ff89a465-f00e-4b6d-b17c-7b327512811b] - Node ff89a465-f00e-4b6d-b17c-7b327512811b[ff89a465-f00e-4b6d-b17c-7b327512811b] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:24:01.757 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-ff89a465-f00e-4b6d-b17c-7b327512811b complete, cost 2827ms 
[INFO ] 2024-03-29 18:24:02.330 - [suppliers_import_import_import(100)][1f4ad82e-9aa8-4318-a41e-6e827755adef] - Node 1f4ad82e-9aa8-4318-a41e-6e827755adef[1f4ad82e-9aa8-4318-a41e-6e827755adef] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:24:02.330 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:02.330 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:02.330 - [suppliers_import_import_import(100)][1f4ad82e-9aa8-4318-a41e-6e827755adef] - Node 1f4ad82e-9aa8-4318-a41e-6e827755adef[1f4ad82e-9aa8-4318-a41e-6e827755adef] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:02.330 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:02.330 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:02.376 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:24:02.377 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@798c4b83 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@798c4b83 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@798c4b83 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:24:02.578 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:24:02.578 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:02.578 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:02.578 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:24:02.578 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:24:02.781 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 14 ms 
[INFO ] 2024-03-29 18:24:03.478 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:24:03.478 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:24:03.478 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:24:03.483 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:24:03.483 - [suppliers_import_import_import(100)][862c8ba4-bca0-4bd6-aedf-f44ec7f5c55f] - Node 862c8ba4-bca0-4bd6-aedf-f44ec7f5c55f[862c8ba4-bca0-4bd6-aedf-f44ec7f5c55f] running status set to false 
[INFO ] 2024-03-29 18:24:03.483 - [suppliers_import_import_import(100)][862c8ba4-bca0-4bd6-aedf-f44ec7f5c55f] - Node 862c8ba4-bca0-4bd6-aedf-f44ec7f5c55f[862c8ba4-bca0-4bd6-aedf-f44ec7f5c55f] schema data cleaned 
[INFO ] 2024-03-29 18:24:03.483 - [suppliers_import_import_import(100)][862c8ba4-bca0-4bd6-aedf-f44ec7f5c55f] - Node 862c8ba4-bca0-4bd6-aedf-f44ec7f5c55f[862c8ba4-bca0-4bd6-aedf-f44ec7f5c55f] monitor closed 
[INFO ] 2024-03-29 18:24:03.483 - [suppliers_import_import_import(100)][862c8ba4-bca0-4bd6-aedf-f44ec7f5c55f] - Node 862c8ba4-bca0-4bd6-aedf-f44ec7f5c55f[862c8ba4-bca0-4bd6-aedf-f44ec7f5c55f] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:24:03.685 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-862c8ba4-bca0-4bd6-aedf-f44ec7f5c55f complete, cost 2758ms 
[INFO ] 2024-03-29 18:24:04.522 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:04.522 - [suppliers_import_import_import(100)][de1a75f2-5df4-4839-8386-324d3ab406c4] - Node de1a75f2-5df4-4839-8386-324d3ab406c4[de1a75f2-5df4-4839-8386-324d3ab406c4] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:24:04.522 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:04.522 - [suppliers_import_import_import(100)][de1a75f2-5df4-4839-8386-324d3ab406c4] - Node de1a75f2-5df4-4839-8386-324d3ab406c4[de1a75f2-5df4-4839-8386-324d3ab406c4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:04.523 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:04.523 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:04.551 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:24:04.552 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4a1b38cc error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4a1b38cc error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4a1b38cc error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:24:04.647 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:04.647 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:04.647 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:04.647 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:04.648 - [suppliers_import_import_import(100)][1a2aeafd-a6fe-460d-9a8e-1370217c9900] - Node 1a2aeafd-a6fe-460d-9a8e-1370217c9900[1a2aeafd-a6fe-460d-9a8e-1370217c9900] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:24:04.655 - [suppliers_import_import_import(100)][1a2aeafd-a6fe-460d-9a8e-1370217c9900] - Node 1a2aeafd-a6fe-460d-9a8e-1370217c9900[1a2aeafd-a6fe-460d-9a8e-1370217c9900] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:04.655 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:24:04.748 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1e21a6be error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1e21a6be error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1e21a6be error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:24:04.748 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:24:04.772 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:04.773 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:04.773 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:24:04.773 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:24:04.799 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 27 ms 
[INFO ] 2024-03-29 18:24:04.800 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:04.800 - [suppliers_import_import_import(100)][2dfe6652-9317-45eb-a03a-a4bf3310f4cf] - Node 2dfe6652-9317-45eb-a03a-a4bf3310f4cf[2dfe6652-9317-45eb-a03a-a4bf3310f4cf] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:24:04.800 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:04.800 - [suppliers_import_import_import(100)][2dfe6652-9317-45eb-a03a-a4bf3310f4cf] - Node 2dfe6652-9317-45eb-a03a-a4bf3310f4cf[2dfe6652-9317-45eb-a03a-a4bf3310f4cf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:04.800 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:04.800 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:04.854 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:24:04.898 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7ff85835 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7ff85835 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7ff85835 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:24:04.900 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:24:04.900 - [suppliers_import_import_import(100)][1f4ad82e-9aa8-4318-a41e-6e827755adef] - Node 1f4ad82e-9aa8-4318-a41e-6e827755adef[1f4ad82e-9aa8-4318-a41e-6e827755adef] running status set to false 
[INFO ] 2024-03-29 18:24:04.900 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:24:04.900 - [suppliers_import_import_import(100)][1f4ad82e-9aa8-4318-a41e-6e827755adef] - Node 1f4ad82e-9aa8-4318-a41e-6e827755adef[1f4ad82e-9aa8-4318-a41e-6e827755adef] schema data cleaned 
[INFO ] 2024-03-29 18:24:04.900 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:24:04.900 - [suppliers_import_import_import(100)][1f4ad82e-9aa8-4318-a41e-6e827755adef] - Node 1f4ad82e-9aa8-4318-a41e-6e827755adef[1f4ad82e-9aa8-4318-a41e-6e827755adef] monitor closed 
[INFO ] 2024-03-29 18:24:04.900 - [suppliers_import_import_import(100)][1f4ad82e-9aa8-4318-a41e-6e827755adef] - Node 1f4ad82e-9aa8-4318-a41e-6e827755adef[1f4ad82e-9aa8-4318-a41e-6e827755adef] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:24:04.900 - [suppliers_import_import_import(100)][52281c43-8766-429a-b21c-ab182c367a14] - Node 52281c43-8766-429a-b21c-ab182c367a14[52281c43-8766-429a-b21c-ab182c367a14] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:24:04.900 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 5 ms 
[INFO ] 2024-03-29 18:24:04.900 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:04.900 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:04.900 - [suppliers_import_import_import(100)][52281c43-8766-429a-b21c-ab182c367a14] - Node 52281c43-8766-429a-b21c-ab182c367a14[52281c43-8766-429a-b21c-ab182c367a14] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:04.901 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:04.901 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:04.902 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-1f4ad82e-9aa8-4318-a41e-6e827755adef complete, cost 2618ms 
[INFO ] 2024-03-29 18:24:04.951 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:24:04.951 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1e2aa18 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1e2aa18 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1e2aa18 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:24:04.979 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:24:04.980 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:04.980 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:04.981 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:24:04.982 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:24:05.094 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 29 ms 
[INFO ] 2024-03-29 18:24:05.096 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:24:05.098 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:05.098 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:05.098 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:24:05.098 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:24:05.100 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 28 ms 
[INFO ] 2024-03-29 18:24:05.242 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:24:05.254 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:05.254 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:05.254 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:24:05.254 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:24:05.255 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 13 ms 
[INFO ] 2024-03-29 18:24:06.546 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:06.546 - [suppliers_import_import_import(100)][63b8b98f-74b6-45d5-9f18-783d731a058e] - Node 63b8b98f-74b6-45d5-9f18-783d731a058e[63b8b98f-74b6-45d5-9f18-783d731a058e] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:24:06.547 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:06.547 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:06.547 - [suppliers_import_import_import(100)][63b8b98f-74b6-45d5-9f18-783d731a058e] - Node 63b8b98f-74b6-45d5-9f18-783d731a058e[63b8b98f-74b6-45d5-9f18-783d731a058e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:06.547 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:06.628 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:24:06.628 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@71ab8540 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@71ab8540 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@71ab8540 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:24:06.825 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:24:06.825 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:06.825 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:06.825 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:24:06.825 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:24:06.825 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 24 ms 
[INFO ] 2024-03-29 18:24:07.084 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:24:07.084 - [suppliers_import_import_import(100)][de1a75f2-5df4-4839-8386-324d3ab406c4] - Node de1a75f2-5df4-4839-8386-324d3ab406c4[de1a75f2-5df4-4839-8386-324d3ab406c4] running status set to false 
[INFO ] 2024-03-29 18:24:07.085 - [suppliers_import_import_import(100)][de1a75f2-5df4-4839-8386-324d3ab406c4] - Node de1a75f2-5df4-4839-8386-324d3ab406c4[de1a75f2-5df4-4839-8386-324d3ab406c4] schema data cleaned 
[INFO ] 2024-03-29 18:24:07.085 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:24:07.085 - [suppliers_import_import_import(100)][de1a75f2-5df4-4839-8386-324d3ab406c4] - Node de1a75f2-5df4-4839-8386-324d3ab406c4[de1a75f2-5df4-4839-8386-324d3ab406c4] monitor closed 
[INFO ] 2024-03-29 18:24:07.085 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:24:07.085 - [suppliers_import_import_import(100)][de1a75f2-5df4-4839-8386-324d3ab406c4] - Node de1a75f2-5df4-4839-8386-324d3ab406c4[de1a75f2-5df4-4839-8386-324d3ab406c4] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:24:07.085 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 2 ms 
[INFO ] 2024-03-29 18:24:07.195 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-de1a75f2-5df4-4839-8386-324d3ab406c4 complete, cost 2652ms 
[INFO ] 2024-03-29 18:24:07.195 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:24:07.195 - [suppliers_import_import_import(100)][1a2aeafd-a6fe-460d-9a8e-1370217c9900] - Node 1a2aeafd-a6fe-460d-9a8e-1370217c9900[1a2aeafd-a6fe-460d-9a8e-1370217c9900] running status set to false 
[INFO ] 2024-03-29 18:24:07.196 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:24:07.196 - [suppliers_import_import_import(100)][1a2aeafd-a6fe-460d-9a8e-1370217c9900] - Node 1a2aeafd-a6fe-460d-9a8e-1370217c9900[1a2aeafd-a6fe-460d-9a8e-1370217c9900] schema data cleaned 
[INFO ] 2024-03-29 18:24:07.196 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:24:07.196 - [suppliers_import_import_import(100)][1a2aeafd-a6fe-460d-9a8e-1370217c9900] - Node 1a2aeafd-a6fe-460d-9a8e-1370217c9900[1a2aeafd-a6fe-460d-9a8e-1370217c9900] monitor closed 
[INFO ] 2024-03-29 18:24:07.196 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 4 ms 
[INFO ] 2024-03-29 18:24:07.196 - [suppliers_import_import_import(100)][1a2aeafd-a6fe-460d-9a8e-1370217c9900] - Node 1a2aeafd-a6fe-460d-9a8e-1370217c9900[1a2aeafd-a6fe-460d-9a8e-1370217c9900] close complete, cost 4 ms 
[INFO ] 2024-03-29 18:24:07.409 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-1a2aeafd-a6fe-460d-9a8e-1370217c9900 complete, cost 2606ms 
[INFO ] 2024-03-29 18:24:07.409 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:24:07.410 - [suppliers_import_import_import(100)][2dfe6652-9317-45eb-a03a-a4bf3310f4cf] - Node 2dfe6652-9317-45eb-a03a-a4bf3310f4cf[2dfe6652-9317-45eb-a03a-a4bf3310f4cf] running status set to false 
[INFO ] 2024-03-29 18:24:07.411 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:24:07.411 - [suppliers_import_import_import(100)][2dfe6652-9317-45eb-a03a-a4bf3310f4cf] - Node 2dfe6652-9317-45eb-a03a-a4bf3310f4cf[2dfe6652-9317-45eb-a03a-a4bf3310f4cf] schema data cleaned 
[INFO ] 2024-03-29 18:24:07.411 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:24:07.411 - [suppliers_import_import_import(100)][2dfe6652-9317-45eb-a03a-a4bf3310f4cf] - Node 2dfe6652-9317-45eb-a03a-a4bf3310f4cf[2dfe6652-9317-45eb-a03a-a4bf3310f4cf] monitor closed 
[INFO ] 2024-03-29 18:24:07.411 - [suppliers_import_import_import(100)][2dfe6652-9317-45eb-a03a-a4bf3310f4cf] - Node 2dfe6652-9317-45eb-a03a-a4bf3310f4cf[2dfe6652-9317-45eb-a03a-a4bf3310f4cf] close complete, cost 9 ms 
[INFO ] 2024-03-29 18:24:07.411 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 14 ms 
[INFO ] 2024-03-29 18:24:07.496 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-2dfe6652-9317-45eb-a03a-a4bf3310f4cf complete, cost 2689ms 
[INFO ] 2024-03-29 18:24:07.496 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:24:07.496 - [suppliers_import_import_import(100)][52281c43-8766-429a-b21c-ab182c367a14] - Node 52281c43-8766-429a-b21c-ab182c367a14[52281c43-8766-429a-b21c-ab182c367a14] running status set to false 
[INFO ] 2024-03-29 18:24:07.497 - [suppliers_import_import_import(100)][52281c43-8766-429a-b21c-ab182c367a14] - Node 52281c43-8766-429a-b21c-ab182c367a14[52281c43-8766-429a-b21c-ab182c367a14] schema data cleaned 
[INFO ] 2024-03-29 18:24:07.497 - [suppliers_import_import_import(100)][52281c43-8766-429a-b21c-ab182c367a14] - Node 52281c43-8766-429a-b21c-ab182c367a14[52281c43-8766-429a-b21c-ab182c367a14] monitor closed 
[INFO ] 2024-03-29 18:24:07.497 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:24:07.497 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:24:07.497 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 3 ms 
[INFO ] 2024-03-29 18:24:07.497 - [suppliers_import_import_import(100)][52281c43-8766-429a-b21c-ab182c367a14] - Node 52281c43-8766-429a-b21c-ab182c367a14[52281c43-8766-429a-b21c-ab182c367a14] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:24:07.498 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-52281c43-8766-429a-b21c-ab182c367a14 complete, cost 2657ms 
[INFO ] 2024-03-29 18:24:08.248 - [suppliers_import_import_import(100)][ab6045f4-0e38-4ebb-9382-203a756b2499] - Node ab6045f4-0e38-4ebb-9382-203a756b2499[ab6045f4-0e38-4ebb-9382-203a756b2499] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:24:08.248 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:08.248 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:08.248 - [suppliers_import_import_import(100)][ab6045f4-0e38-4ebb-9382-203a756b2499] - Node ab6045f4-0e38-4ebb-9382-203a756b2499[ab6045f4-0e38-4ebb-9382-203a756b2499] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:08.249 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:08.249 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:08.325 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:24:08.325 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@40b9ecc5 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@40b9ecc5 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@40b9ecc5 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:24:08.537 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:24:08.546 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:08.546 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:08.546 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:24:08.546 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:24:08.546 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 58 ms 
[INFO ] 2024-03-29 18:24:09.156 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:24:09.157 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:24:09.158 - [suppliers_import_import_import(100)][63b8b98f-74b6-45d5-9f18-783d731a058e] - Node 63b8b98f-74b6-45d5-9f18-783d731a058e[63b8b98f-74b6-45d5-9f18-783d731a058e] running status set to false 
[INFO ] 2024-03-29 18:24:09.159 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:24:09.159 - [suppliers_import_import_import(100)][63b8b98f-74b6-45d5-9f18-783d731a058e] - Node 63b8b98f-74b6-45d5-9f18-783d731a058e[63b8b98f-74b6-45d5-9f18-783d731a058e] schema data cleaned 
[INFO ] 2024-03-29 18:24:09.159 - [suppliers_import_import_import(100)][63b8b98f-74b6-45d5-9f18-783d731a058e] - Node 63b8b98f-74b6-45d5-9f18-783d731a058e[63b8b98f-74b6-45d5-9f18-783d731a058e] monitor closed 
[INFO ] 2024-03-29 18:24:09.159 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 5 ms 
[INFO ] 2024-03-29 18:24:09.161 - [suppliers_import_import_import(100)][63b8b98f-74b6-45d5-9f18-783d731a058e] - Node 63b8b98f-74b6-45d5-9f18-783d731a058e[63b8b98f-74b6-45d5-9f18-783d731a058e] close complete, cost 2 ms 
[INFO ] 2024-03-29 18:24:09.161 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-63b8b98f-74b6-45d5-9f18-783d731a058e complete, cost 2661ms 
[INFO ] 2024-03-29 18:24:09.835 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:09.835 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:09.835 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:09.835 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:09.836 - [suppliers_import_import_import(100)][7102b6cb-e729-41e3-92ac-28a1d3af57ea] - Node 7102b6cb-e729-41e3-92ac-28a1d3af57ea[7102b6cb-e729-41e3-92ac-28a1d3af57ea] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:24:09.836 - [suppliers_import_import_import(100)][7102b6cb-e729-41e3-92ac-28a1d3af57ea] - Node 7102b6cb-e729-41e3-92ac-28a1d3af57ea[7102b6cb-e729-41e3-92ac-28a1d3af57ea] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:09.853 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:24:09.853 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3ca3b8e3 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3ca3b8e3 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3ca3b8e3 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:24:09.936 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:09.936 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:09.936 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:09.936 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:09.936 - [suppliers_import_import_import(100)][136e8ee0-8d76-4c8d-ba6f-91b5ffa8304a] - Node 136e8ee0-8d76-4c8d-ba6f-91b5ffa8304a[136e8ee0-8d76-4c8d-ba6f-91b5ffa8304a] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:24:09.945 - [suppliers_import_import_import(100)][136e8ee0-8d76-4c8d-ba6f-91b5ffa8304a] - Node 136e8ee0-8d76-4c8d-ba6f-91b5ffa8304a[136e8ee0-8d76-4c8d-ba6f-91b5ffa8304a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:09.945 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:24:10.023 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@718f87e8 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@718f87e8 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@718f87e8 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:24:10.023 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:24:10.042 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:10.042 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:10.043 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:24:10.043 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:24:10.144 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 21 ms 
[INFO ] 2024-03-29 18:24:10.144 - [suppliers_import_import_import(100)][1706b944-4de7-4274-9756-979a86d8ffd1] - Node 1706b944-4de7-4274-9756-979a86d8ffd1[1706b944-4de7-4274-9756-979a86d8ffd1] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:24:10.144 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:10.144 - [suppliers_import_import_import(100)][1706b944-4de7-4274-9756-979a86d8ffd1] - Node 1706b944-4de7-4274-9756-979a86d8ffd1[1706b944-4de7-4274-9756-979a86d8ffd1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:10.145 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:10.148 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:10.149 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:10.186 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:24:10.187 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6829255a error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6829255a error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6829255a error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:24:10.231 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:24:10.231 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:10.231 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:10.232 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:24:10.232 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:24:10.232 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 25 ms 
[INFO ] 2024-03-29 18:24:10.245 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:10.245 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:10.245 - [suppliers_import_import_import(100)][7c24fd2e-8e96-4a45-9173-8f4e658c9dfe] - Node 7c24fd2e-8e96-4a45-9173-8f4e658c9dfe[7c24fd2e-8e96-4a45-9173-8f4e658c9dfe] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:24:10.245 - [suppliers_import_import_import(100)][7c24fd2e-8e96-4a45-9173-8f4e658c9dfe] - Node 7c24fd2e-8e96-4a45-9173-8f4e658c9dfe[7c24fd2e-8e96-4a45-9173-8f4e658c9dfe] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:10.246 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:10.276 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:10.276 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:24:10.418 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7ca66e7a error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7ca66e7a error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7ca66e7a error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:24:10.430 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:24:10.439 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:10.439 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:10.439 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:24:10.439 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:24:10.440 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 47 ms 
[INFO ] 2024-03-29 18:24:10.603 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:24:10.603 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:10.603 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:10.603 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:24:10.603 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:24:10.604 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 20 ms 
[INFO ] 2024-03-29 18:24:10.742 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:10.742 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:10.742 - [suppliers_import_import_import(100)][9d93d71d-e61c-4489-b06f-db3aae936569] - Node 9d93d71d-e61c-4489-b06f-db3aae936569[9d93d71d-e61c-4489-b06f-db3aae936569] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:24:10.742 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:10.742 - [suppliers_import_import_import(100)][9d93d71d-e61c-4489-b06f-db3aae936569] - Node 9d93d71d-e61c-4489-b06f-db3aae936569[9d93d71d-e61c-4489-b06f-db3aae936569] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:10.742 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:24:10.793 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:24:10.849 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@602d8c88 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@602d8c88 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@602d8c88 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:24:10.849 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:24:10.849 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:24:10.849 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:24:10.850 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:24:10.850 - [suppliers_import_import_import(100)][ab6045f4-0e38-4ebb-9382-203a756b2499] - Node ab6045f4-0e38-4ebb-9382-203a756b2499[ab6045f4-0e38-4ebb-9382-203a756b2499] running status set to false 
[INFO ] 2024-03-29 18:24:10.850 - [suppliers_import_import_import(100)][ab6045f4-0e38-4ebb-9382-203a756b2499] - Node ab6045f4-0e38-4ebb-9382-203a756b2499[ab6045f4-0e38-4ebb-9382-203a756b2499] schema data cleaned 
[INFO ] 2024-03-29 18:24:10.850 - [suppliers_import_import_import(100)][ab6045f4-0e38-4ebb-9382-203a756b2499] - Node ab6045f4-0e38-4ebb-9382-203a756b2499[ab6045f4-0e38-4ebb-9382-203a756b2499] monitor closed 
[INFO ] 2024-03-29 18:24:10.851 - [suppliers_import_import_import(100)][ab6045f4-0e38-4ebb-9382-203a756b2499] - Node ab6045f4-0e38-4ebb-9382-203a756b2499[ab6045f4-0e38-4ebb-9382-203a756b2499] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:24:10.851 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-ab6045f4-0e38-4ebb-9382-203a756b2499 complete, cost 2661ms 
[INFO ] 2024-03-29 18:24:10.997 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:24:10.998 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:10.998 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:10.998 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:24:10.998 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:24:11.203 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 19 ms 
[INFO ] 2024-03-29 18:24:11.729 - [suppliers_import_import_import(100)][18401fee-4214-4c8c-bd69-20c4061041d6] - Node 18401fee-4214-4c8c-bd69-20c4061041d6[18401fee-4214-4c8c-bd69-20c4061041d6] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:24:11.729 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:11.729 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:11.729 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:11.729 - [suppliers_import_import_import(100)][18401fee-4214-4c8c-bd69-20c4061041d6] - Node 18401fee-4214-4c8c-bd69-20c4061041d6[18401fee-4214-4c8c-bd69-20c4061041d6] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:11.729 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:11.741 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:24:11.741 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@f5b3728 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@f5b3728 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@f5b3728 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:24:11.937 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:24:11.937 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:11.937 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:11.937 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:24:11.938 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:24:11.938 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 18 ms 
[INFO ] 2024-03-29 18:24:12.377 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:24:12.380 - [suppliers_import_import_import(100)][7102b6cb-e729-41e3-92ac-28a1d3af57ea] - Node 7102b6cb-e729-41e3-92ac-28a1d3af57ea[7102b6cb-e729-41e3-92ac-28a1d3af57ea] running status set to false 
[INFO ] 2024-03-29 18:24:12.380 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:24:12.380 - [suppliers_import_import_import(100)][7102b6cb-e729-41e3-92ac-28a1d3af57ea] - Node 7102b6cb-e729-41e3-92ac-28a1d3af57ea[7102b6cb-e729-41e3-92ac-28a1d3af57ea] schema data cleaned 
[INFO ] 2024-03-29 18:24:12.380 - [suppliers_import_import_import(100)][7102b6cb-e729-41e3-92ac-28a1d3af57ea] - Node 7102b6cb-e729-41e3-92ac-28a1d3af57ea[7102b6cb-e729-41e3-92ac-28a1d3af57ea] monitor closed 
[INFO ] 2024-03-29 18:24:12.380 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:24:12.380 - [suppliers_import_import_import(100)][7102b6cb-e729-41e3-92ac-28a1d3af57ea] - Node 7102b6cb-e729-41e3-92ac-28a1d3af57ea[7102b6cb-e729-41e3-92ac-28a1d3af57ea] close complete, cost 2 ms 
[INFO ] 2024-03-29 18:24:12.380 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 4 ms 
[INFO ] 2024-03-29 18:24:12.487 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-7102b6cb-e729-41e3-92ac-28a1d3af57ea complete, cost 2576ms 
[INFO ] 2024-03-29 18:24:12.487 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:24:12.491 - [suppliers_import_import_import(100)][136e8ee0-8d76-4c8d-ba6f-91b5ffa8304a] - Node 136e8ee0-8d76-4c8d-ba6f-91b5ffa8304a[136e8ee0-8d76-4c8d-ba6f-91b5ffa8304a] running status set to false 
[INFO ] 2024-03-29 18:24:12.491 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:24:12.491 - [suppliers_import_import_import(100)][136e8ee0-8d76-4c8d-ba6f-91b5ffa8304a] - Node 136e8ee0-8d76-4c8d-ba6f-91b5ffa8304a[136e8ee0-8d76-4c8d-ba6f-91b5ffa8304a] schema data cleaned 
[INFO ] 2024-03-29 18:24:12.491 - [suppliers_import_import_import(100)][136e8ee0-8d76-4c8d-ba6f-91b5ffa8304a] - Node 136e8ee0-8d76-4c8d-ba6f-91b5ffa8304a[136e8ee0-8d76-4c8d-ba6f-91b5ffa8304a] monitor closed 
[INFO ] 2024-03-29 18:24:12.491 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:24:12.492 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 17 ms 
[INFO ] 2024-03-29 18:24:12.492 - [suppliers_import_import_import(100)][136e8ee0-8d76-4c8d-ba6f-91b5ffa8304a] - Node 136e8ee0-8d76-4c8d-ba6f-91b5ffa8304a[136e8ee0-8d76-4c8d-ba6f-91b5ffa8304a] close complete, cost 13 ms 
[INFO ] 2024-03-29 18:24:12.693 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-136e8ee0-8d76-4c8d-ba6f-91b5ffa8304a complete, cost 2591ms 
[INFO ] 2024-03-29 18:24:12.722 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:24:12.722 - [suppliers_import_import_import(100)][1706b944-4de7-4274-9756-979a86d8ffd1] - Node 1706b944-4de7-4274-9756-979a86d8ffd1[1706b944-4de7-4274-9756-979a86d8ffd1] running status set to false 
[INFO ] 2024-03-29 18:24:12.723 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:24:12.723 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:24:12.723 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 4 ms 
[INFO ] 2024-03-29 18:24:12.730 - [suppliers_import_import_import(100)][1706b944-4de7-4274-9756-979a86d8ffd1] - Node 1706b944-4de7-4274-9756-979a86d8ffd1[1706b944-4de7-4274-9756-979a86d8ffd1] schema data cleaned 
[INFO ] 2024-03-29 18:24:12.731 - [suppliers_import_import_import(100)][1706b944-4de7-4274-9756-979a86d8ffd1] - Node 1706b944-4de7-4274-9756-979a86d8ffd1[1706b944-4de7-4274-9756-979a86d8ffd1] monitor closed 
[INFO ] 2024-03-29 18:24:12.731 - [suppliers_import_import_import(100)][1706b944-4de7-4274-9756-979a86d8ffd1] - Node 1706b944-4de7-4274-9756-979a86d8ffd1[1706b944-4de7-4274-9756-979a86d8ffd1] close complete, cost 4 ms 
[INFO ] 2024-03-29 18:24:12.731 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-1706b944-4de7-4274-9756-979a86d8ffd1 complete, cost 2677ms 
[INFO ] 2024-03-29 18:24:12.820 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:24:12.820 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:24:12.820 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:24:12.820 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:24:12.821 - [suppliers_import_import_import(100)][7c24fd2e-8e96-4a45-9173-8f4e658c9dfe] - Node 7c24fd2e-8e96-4a45-9173-8f4e658c9dfe[7c24fd2e-8e96-4a45-9173-8f4e658c9dfe] running status set to false 
[INFO ] 2024-03-29 18:24:12.821 - [suppliers_import_import_import(100)][7c24fd2e-8e96-4a45-9173-8f4e658c9dfe] - Node 7c24fd2e-8e96-4a45-9173-8f4e658c9dfe[7c24fd2e-8e96-4a45-9173-8f4e658c9dfe] schema data cleaned 
[INFO ] 2024-03-29 18:24:12.821 - [suppliers_import_import_import(100)][7c24fd2e-8e96-4a45-9173-8f4e658c9dfe] - Node 7c24fd2e-8e96-4a45-9173-8f4e658c9dfe[7c24fd2e-8e96-4a45-9173-8f4e658c9dfe] monitor closed 
[INFO ] 2024-03-29 18:24:12.821 - [suppliers_import_import_import(100)][7c24fd2e-8e96-4a45-9173-8f4e658c9dfe] - Node 7c24fd2e-8e96-4a45-9173-8f4e658c9dfe[7c24fd2e-8e96-4a45-9173-8f4e658c9dfe] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:24:12.822 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-7c24fd2e-8e96-4a45-9173-8f4e658c9dfe complete, cost 2655ms 
[INFO ] 2024-03-29 18:24:13.331 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:24:13.331 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:24:13.331 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:24:13.331 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:24:13.333 - [suppliers_import_import_import(100)][9d93d71d-e61c-4489-b06f-db3aae936569] - Node 9d93d71d-e61c-4489-b06f-db3aae936569[9d93d71d-e61c-4489-b06f-db3aae936569] running status set to false 
[INFO ] 2024-03-29 18:24:13.333 - [suppliers_import_import_import(100)][9d93d71d-e61c-4489-b06f-db3aae936569] - Node 9d93d71d-e61c-4489-b06f-db3aae936569[9d93d71d-e61c-4489-b06f-db3aae936569] schema data cleaned 
[INFO ] 2024-03-29 18:24:13.333 - [suppliers_import_import_import(100)][9d93d71d-e61c-4489-b06f-db3aae936569] - Node 9d93d71d-e61c-4489-b06f-db3aae936569[9d93d71d-e61c-4489-b06f-db3aae936569] monitor closed 
[INFO ] 2024-03-29 18:24:13.334 - [suppliers_import_import_import(100)][9d93d71d-e61c-4489-b06f-db3aae936569] - Node 9d93d71d-e61c-4489-b06f-db3aae936569[9d93d71d-e61c-4489-b06f-db3aae936569] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:24:13.334 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-9d93d71d-e61c-4489-b06f-db3aae936569 complete, cost 2655ms 
[INFO ] 2024-03-29 18:24:14.168 - [suppliers_import_import_import(100)][7e86a7da-bed0-4af7-b0bf-0dfbbe3ae005] - Node 7e86a7da-bed0-4af7-b0bf-0dfbbe3ae005[7e86a7da-bed0-4af7-b0bf-0dfbbe3ae005] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:24:14.169 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:14.169 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:14.169 - [suppliers_import_import_import(100)][7e86a7da-bed0-4af7-b0bf-0dfbbe3ae005] - Node 7e86a7da-bed0-4af7-b0bf-0dfbbe3ae005[7e86a7da-bed0-4af7-b0bf-0dfbbe3ae005] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:14.169 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:14.169 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:14.235 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:24:14.236 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:14.236 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:14.236 - [suppliers_import_import_import(100)][27b34c51-9b1d-4062-9049-ae092bba5c8b] - Node 27b34c51-9b1d-4062-9049-ae092bba5c8b[27b34c51-9b1d-4062-9049-ae092bba5c8b] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:24:14.236 - [suppliers_import_import_import(100)][27b34c51-9b1d-4062-9049-ae092bba5c8b] - Node 27b34c51-9b1d-4062-9049-ae092bba5c8b[27b34c51-9b1d-4062-9049-ae092bba5c8b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:14.236 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:14.236 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:14.250 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:24:14.254 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2fd32f38 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2fd32f38 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2fd32f38 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-29 18:24:14.255 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@231aa0fa error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@231aa0fa error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@231aa0fa error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:24:14.265 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:24:14.265 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:24:14.265 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:24:14.270 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:24:14.270 - [suppliers_import_import_import(100)][18401fee-4214-4c8c-bd69-20c4061041d6] - Node 18401fee-4214-4c8c-bd69-20c4061041d6[18401fee-4214-4c8c-bd69-20c4061041d6] running status set to false 
[INFO ] 2024-03-29 18:24:14.270 - [suppliers_import_import_import(100)][18401fee-4214-4c8c-bd69-20c4061041d6] - Node 18401fee-4214-4c8c-bd69-20c4061041d6[18401fee-4214-4c8c-bd69-20c4061041d6] schema data cleaned 
[INFO ] 2024-03-29 18:24:14.270 - [suppliers_import_import_import(100)][18401fee-4214-4c8c-bd69-20c4061041d6] - Node 18401fee-4214-4c8c-bd69-20c4061041d6[18401fee-4214-4c8c-bd69-20c4061041d6] monitor closed 
[INFO ] 2024-03-29 18:24:14.270 - [suppliers_import_import_import(100)][18401fee-4214-4c8c-bd69-20c4061041d6] - Node 18401fee-4214-4c8c-bd69-20c4061041d6[18401fee-4214-4c8c-bd69-20c4061041d6] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:24:14.417 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-18401fee-4214-4c8c-bd69-20c4061041d6 complete, cost 2574ms 
[INFO ] 2024-03-29 18:24:14.417 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:24:14.436 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:14.436 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:14.436 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:24:14.436 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:24:14.436 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 22 ms 
[INFO ] 2024-03-29 18:24:14.492 - [suppliers_import_import_import(100)][a7c1395b-8f4d-44c7-a7ff-9cdfda9a1f7a] - Node a7c1395b-8f4d-44c7-a7ff-9cdfda9a1f7a[a7c1395b-8f4d-44c7-a7ff-9cdfda9a1f7a] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:24:14.492 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:14.492 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:14.492 - [suppliers_import_import_import(100)][a7c1395b-8f4d-44c7-a7ff-9cdfda9a1f7a] - Node a7c1395b-8f4d-44c7-a7ff-9cdfda9a1f7a[a7c1395b-8f4d-44c7-a7ff-9cdfda9a1f7a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:14.492 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:14.492 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:14.519 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:24:14.588 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@25230dac error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@25230dac error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@25230dac error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:24:14.594 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:24:14.600 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:14.600 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:14.600 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:24:14.600 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:24:14.600 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 13 ms 
[INFO ] 2024-03-29 18:24:14.720 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:24:14.728 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:14.728 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:14.729 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:24:14.729 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:24:14.930 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 10 ms 
[INFO ] 2024-03-29 18:24:16.068 - [suppliers_import_import_import(100)][cf0437df-39fa-47c5-aef0-94e8327e5c19] - Node cf0437df-39fa-47c5-aef0-94e8327e5c19[cf0437df-39fa-47c5-aef0-94e8327e5c19] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:24:16.068 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:16.068 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:24:16.068 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:16.068 - [suppliers_import_import_import(100)][cf0437df-39fa-47c5-aef0-94e8327e5c19] - Node cf0437df-39fa-47c5-aef0-94e8327e5c19[cf0437df-39fa-47c5-aef0-94e8327e5c19] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:24:16.068 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:24:16.131 - [suppliers_import_import_import(100)][Suppliers] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:24:16.318 - [suppliers_import_import_import(100)][Suppliers] - Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1732a204 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1732a204 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:6)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["contact.firstName"] = record["ContactName"].split(' ')[0];
    record["contact.lastName"] = record["ContactName"].split(' ')[1];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1732a204 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:6)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:24:16.331 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] running status set to false 
[INFO ] 2024-03-29 18:24:16.335 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:16.335 - [suppliers_import_import_import(100)][Suppliers] - PDK connector node released: HazelcastSampleSourcePdkDataNode-4c9e81e3-f7ef-4485-8b80-368dcd6e9219 
[INFO ] 2024-03-29 18:24:16.335 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] schema data cleaned 
[INFO ] 2024-03-29 18:24:16.336 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] monitor closed 
[INFO ] 2024-03-29 18:24:16.540 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[4c9e81e3-f7ef-4485-8b80-368dcd6e9219] close complete, cost 19 ms 
[INFO ] 2024-03-29 18:24:16.770 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:24:16.770 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:24:16.770 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:24:16.770 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:24:16.773 - [suppliers_import_import_import(100)][7e86a7da-bed0-4af7-b0bf-0dfbbe3ae005] - Node 7e86a7da-bed0-4af7-b0bf-0dfbbe3ae005[7e86a7da-bed0-4af7-b0bf-0dfbbe3ae005] running status set to false 
[INFO ] 2024-03-29 18:24:16.773 - [suppliers_import_import_import(100)][7e86a7da-bed0-4af7-b0bf-0dfbbe3ae005] - Node 7e86a7da-bed0-4af7-b0bf-0dfbbe3ae005[7e86a7da-bed0-4af7-b0bf-0dfbbe3ae005] schema data cleaned 
[INFO ] 2024-03-29 18:24:16.773 - [suppliers_import_import_import(100)][7e86a7da-bed0-4af7-b0bf-0dfbbe3ae005] - Node 7e86a7da-bed0-4af7-b0bf-0dfbbe3ae005[7e86a7da-bed0-4af7-b0bf-0dfbbe3ae005] monitor closed 
[INFO ] 2024-03-29 18:24:16.773 - [suppliers_import_import_import(100)][7e86a7da-bed0-4af7-b0bf-0dfbbe3ae005] - Node 7e86a7da-bed0-4af7-b0bf-0dfbbe3ae005[7e86a7da-bed0-4af7-b0bf-0dfbbe3ae005] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:24:16.777 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-7e86a7da-bed0-4af7-b0bf-0dfbbe3ae005 complete, cost 2669ms 
[INFO ] 2024-03-29 18:24:16.777 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:24:16.778 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:24:16.778 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:24:16.783 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:24:16.783 - [suppliers_import_import_import(100)][27b34c51-9b1d-4062-9049-ae092bba5c8b] - Node 27b34c51-9b1d-4062-9049-ae092bba5c8b[27b34c51-9b1d-4062-9049-ae092bba5c8b] running status set to false 
[INFO ] 2024-03-29 18:24:16.783 - [suppliers_import_import_import(100)][27b34c51-9b1d-4062-9049-ae092bba5c8b] - Node 27b34c51-9b1d-4062-9049-ae092bba5c8b[27b34c51-9b1d-4062-9049-ae092bba5c8b] schema data cleaned 
[INFO ] 2024-03-29 18:24:16.783 - [suppliers_import_import_import(100)][27b34c51-9b1d-4062-9049-ae092bba5c8b] - Node 27b34c51-9b1d-4062-9049-ae092bba5c8b[27b34c51-9b1d-4062-9049-ae092bba5c8b] monitor closed 
[INFO ] 2024-03-29 18:24:16.783 - [suppliers_import_import_import(100)][27b34c51-9b1d-4062-9049-ae092bba5c8b] - Node 27b34c51-9b1d-4062-9049-ae092bba5c8b[27b34c51-9b1d-4062-9049-ae092bba5c8b] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:24:16.784 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-27b34c51-9b1d-4062-9049-ae092bba5c8b complete, cost 2585ms 
[INFO ] 2024-03-29 18:24:17.077 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:24:17.081 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:24:17.081 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:24:17.081 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:24:17.081 - [suppliers_import_import_import(100)][a7c1395b-8f4d-44c7-a7ff-9cdfda9a1f7a] - Node a7c1395b-8f4d-44c7-a7ff-9cdfda9a1f7a[a7c1395b-8f4d-44c7-a7ff-9cdfda9a1f7a] running status set to false 
[INFO ] 2024-03-29 18:24:17.081 - [suppliers_import_import_import(100)][a7c1395b-8f4d-44c7-a7ff-9cdfda9a1f7a] - Node a7c1395b-8f4d-44c7-a7ff-9cdfda9a1f7a[a7c1395b-8f4d-44c7-a7ff-9cdfda9a1f7a] schema data cleaned 
[INFO ] 2024-03-29 18:24:17.081 - [suppliers_import_import_import(100)][a7c1395b-8f4d-44c7-a7ff-9cdfda9a1f7a] - Node a7c1395b-8f4d-44c7-a7ff-9cdfda9a1f7a[a7c1395b-8f4d-44c7-a7ff-9cdfda9a1f7a] monitor closed 
[INFO ] 2024-03-29 18:24:17.081 - [suppliers_import_import_import(100)][a7c1395b-8f4d-44c7-a7ff-9cdfda9a1f7a] - Node a7c1395b-8f4d-44c7-a7ff-9cdfda9a1f7a[a7c1395b-8f4d-44c7-a7ff-9cdfda9a1f7a] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:24:17.081 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-a7c1395b-8f4d-44c7-a7ff-9cdfda9a1f7a complete, cost 2634ms 
[INFO ] 2024-03-29 18:24:18.690 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] running status set to false 
[INFO ] 2024-03-29 18:24:18.690 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] schema data cleaned 
[INFO ] 2024-03-29 18:24:18.690 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] monitor closed 
[INFO ] 2024-03-29 18:24:18.691 - [suppliers_import_import_import(100)][Suppliers] - Node Suppliers[393e3c91-861a-4855-98fb-afceec42cd73] close complete, cost 3 ms 
[INFO ] 2024-03-29 18:24:18.692 - [suppliers_import_import_import(100)][cf0437df-39fa-47c5-aef0-94e8327e5c19] - Node cf0437df-39fa-47c5-aef0-94e8327e5c19[cf0437df-39fa-47c5-aef0-94e8327e5c19] running status set to false 
[INFO ] 2024-03-29 18:24:18.692 - [suppliers_import_import_import(100)][cf0437df-39fa-47c5-aef0-94e8327e5c19] - Node cf0437df-39fa-47c5-aef0-94e8327e5c19[cf0437df-39fa-47c5-aef0-94e8327e5c19] schema data cleaned 
[INFO ] 2024-03-29 18:24:18.692 - [suppliers_import_import_import(100)][cf0437df-39fa-47c5-aef0-94e8327e5c19] - Node cf0437df-39fa-47c5-aef0-94e8327e5c19[cf0437df-39fa-47c5-aef0-94e8327e5c19] monitor closed 
[INFO ] 2024-03-29 18:24:18.692 - [suppliers_import_import_import(100)][cf0437df-39fa-47c5-aef0-94e8327e5c19] - Node cf0437df-39fa-47c5-aef0-94e8327e5c19[cf0437df-39fa-47c5-aef0-94e8327e5c19] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:24:18.898 - [suppliers_import_import_import(100)] - load tapTable task 660696aeabdf97439178c9e9-cf0437df-39fa-47c5-aef0-94e8327e5c19 complete, cost 2723ms 
