[INFO ] 2024-07-23 02:57:24.711 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务_CUSTOMER', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_45392933', head seq: 0, tail seq: 0 
[INFO ] 2024-07-23 02:57:24.780 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_1469738849', head seq: 0, tail seq: 149338 
[INFO ] 2024-07-23 05:13:47.054 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_1469738849', head seq: 0, tail seq: 153209 
[INFO ] 2024-07-23 05:13:47.056 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务_CUSTOMER', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_45392933', head seq: 0, tail seq: 0 
[INFO ] 2024-07-23 06:18:27.228 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务_CUSTOMER', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_45392933', head seq: 0, tail seq: 0 
[INFO ] 2024-07-23 06:18:27.233 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_1469738849', head seq: 0, tail seq: 157071 
[INFO ] 2024-07-23 07:23:47.885 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务_CUSTOMER', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_45392933', head seq: 0, tail seq: 0 
[INFO ] 2024-07-23 07:23:47.888 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_1469738849', head seq: 0, tail seq: 160971 
[INFO ] 2024-07-23 07:43:23.368 - [来自Mysql的共享挖掘任务][Mysql] - Mysql binlog reader stopped 
[WARN ] 2024-07-23 07:43:23.376 - [来自Mysql的共享挖掘任务][Mysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
	com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:986)
	com.github.shyiko.mysql.binlog.BinaryLogClient$SpawnKeepAliveThread.run(BinaryLogClient.java:907)
	java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	java.util.concurrent.FutureTask.run(FutureTask.java)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-23 07:44:23.447 - [来自Mysql的共享挖掘任务][Mysql] - Starting mysql cdc, server name: 52923cd5-0c72-4472-af4c-00cbdaba2e6f 
[INFO ] 2024-07-23 07:44:23.545 - [来自Mysql的共享挖掘任务][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1491771609
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 52923cd5-0c72-4472-af4c-00cbdaba2e6f
  database.port: 3306
  threadName: Debezium-Mysql-Connector-52923cd5-0c72-4472-af4c-00cbdaba2e6f
  database.hostname: localhost
  database.password: ********
  name: 52923cd5-0c72-4472-af4c-00cbdaba2e6f
  pdk.offset.string: {"name":"52923cd5-0c72-4472-af4c-00cbdaba2e6f","offset":{"{\"server\":\"52923cd5-0c72-4472-af4c-00cbdaba2e6f\"}":"{\"ts_sec\":1721691712,\"file\":\"binlog.000033\",\"pos\":43762171,\"row\":1,\"server_id\":1,\"event\":2}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CUSTOMER,test._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-23 07:44:23.547 - [来自Mysql的共享挖掘任务][Mysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-07-23 07:44:23.755 - [来自Mysql的共享挖掘任务][Mysql] - Connector Mysql incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-23 07:58:36.500 - [来自Mysql的共享挖掘任务][Mysql] - Node Mysql[a41332d4fa724759ac1ca658fe8b9ae7] running status set to false 
[INFO ] 2024-07-23 08:03:32.663 - [来自Mysql的共享挖掘任务] - Start task milestones: 6695e5c55e81b0124202907b(来自Mysql的共享挖掘任务) 
[INFO ] 2024-07-23 08:03:32.821 - [来自Mysql的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-23 08:03:32.822 - [来自Mysql的共享挖掘任务] - The engine receives 来自Mysql的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-23 08:03:33.048 - [来自Mysql的共享挖掘任务][Mysql] - Node Mysql[a41332d4fa724759ac1ca658fe8b9ae7] start preload schema,table counts: 2 
[INFO ] 2024-07-23 08:03:33.050 - [来自Mysql的共享挖掘任务][Mysql] - Node Mysql[a41332d4fa724759ac1ca658fe8b9ae7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 08:03:33.053 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-23 08:03:33.053 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-23 08:03:33.226 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695e5c566ab5ede8aa012f4, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6695b8aa6d76494ed53f3882_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_45392933, shareCdcTaskId=6695e5c55e81b0124202907b, connectionId=6695b8aa6d76494ed53f3882) 
[INFO ] 2024-07-23 08:03:33.227 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695e5c566ab5ede8aa012f2, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6695b8aa6d76494ed53f3882__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_1469738849, shareCdcTaskId=6695e5c55e81b0124202907b, connectionId=6695b8aa6d76494ed53f3882) 
[INFO ] 2024-07-23 08:03:33.282 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_1469738849', head seq: 0, tail seq: 162905 
[INFO ] 2024-07-23 08:03:33.320 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务_CUSTOMER', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_45392933', head seq: 0, tail seq: 0 
[INFO ] 2024-07-23 08:03:33.321 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav392?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-23 08:03:33.712 - [来自Mysql的共享挖掘任务][Mysql] - Source node "Mysql" read batch size: 2000 
[INFO ] 2024-07-23 08:03:33.715 - [来自Mysql的共享挖掘任务][Mysql] - Source node "Mysql" event queue capacity: 4000 
[INFO ] 2024-07-23 08:03:33.715 - [来自Mysql的共享挖掘任务][Mysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-23 08:03:33.729 - [来自Mysql的共享挖掘任务][Mysql] - batch offset found: {},stream offset found: {"name":"52923cd5-0c72-4472-af4c-00cbdaba2e6f","offset":{"{\"server\":\"52923cd5-0c72-4472-af4c-00cbdaba2e6f\"}":"{\"ts_sec\":1721692710,\"file\":\"binlog.000033\",\"pos\":44082835,\"row\":1,\"server_id\":1,\"event\":2}"}} 
[INFO ] 2024-07-23 08:03:33.731 - [来自Mysql的共享挖掘任务][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-23 08:03:33.803 - [来自Mysql的共享挖掘任务][Mysql] - Starting stream read, table list: [CUSTOMER, _tapdata_heartbeat_table], offset: {"name":"52923cd5-0c72-4472-af4c-00cbdaba2e6f","offset":{"{\"server\":\"52923cd5-0c72-4472-af4c-00cbdaba2e6f\"}":"{\"ts_sec\":1721692710,\"file\":\"binlog.000033\",\"pos\":44082835,\"row\":1,\"server_id\":1,\"event\":2}"}} 
[INFO ] 2024-07-23 08:03:33.808 - [来自Mysql的共享挖掘任务][Mysql] - Starting mysql cdc, server name: 52923cd5-0c72-4472-af4c-00cbdaba2e6f 
[INFO ] 2024-07-23 08:03:34.016 - [来自Mysql的共享挖掘任务][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 3721041
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 52923cd5-0c72-4472-af4c-00cbdaba2e6f
  database.port: 3306
  threadName: Debezium-Mysql-Connector-52923cd5-0c72-4472-af4c-00cbdaba2e6f
  database.hostname: localhost
  database.password: ********
  name: 52923cd5-0c72-4472-af4c-00cbdaba2e6f
  pdk.offset.string: {"name":"52923cd5-0c72-4472-af4c-00cbdaba2e6f","offset":{"{\"server\":\"52923cd5-0c72-4472-af4c-00cbdaba2e6f\"}":"{\"ts_sec\":1721692710,\"file\":\"binlog.000033\",\"pos\":44082835,\"row\":1,\"server_id\":1,\"event\":2}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CUSTOMER,test._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-23 08:03:34.692 - [来自Mysql的共享挖掘任务][Mysql] - Connector Mysql incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-23 10:12:51.888 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_1469738849', head seq: 0, tail seq: 166436 
[INFO ] 2024-07-23 10:12:51.892 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务_CUSTOMER', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_45392933', head seq: 0, tail seq: 0 
[INFO ] 2024-07-23 11:17:13.222 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_1469738849', head seq: 0, tail seq: 169485 
[INFO ] 2024-07-23 11:17:13.223 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务_CUSTOMER', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_45392933', head seq: 0, tail seq: 0 
[INFO ] 2024-07-23 11:31:04.733 - [来自Mysql的共享挖掘任务][Mysql] - Mysql binlog reader stopped 
[WARN ] 2024-07-23 11:31:04.937 - [来自Mysql的共享挖掘任务][Mysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.EOFException: null
	com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:580)
	com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	java.lang.Thread.run(Thread.java:750)
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-23 11:32:04.836 - [来自Mysql的共享挖掘任务][Mysql] - Starting mysql cdc, server name: 52923cd5-0c72-4472-af4c-00cbdaba2e6f 
[INFO ] 2024-07-23 11:32:04.948 - [来自Mysql的共享挖掘任务][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 13723361
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 52923cd5-0c72-4472-af4c-00cbdaba2e6f
  database.port: 3306
  threadName: Debezium-Mysql-Connector-52923cd5-0c72-4472-af4c-00cbdaba2e6f
  database.hostname: localhost
  database.password: ********
  name: 52923cd5-0c72-4472-af4c-00cbdaba2e6f
  pdk.offset.string: {"name":"52923cd5-0c72-4472-af4c-00cbdaba2e6f","offset":{"{\"server\":\"52923cd5-0c72-4472-af4c-00cbdaba2e6f\"}":"{\"ts_sec\":1721705463,\"file\":\"binlog.000033\",\"pos\":46764461,\"row\":1,\"server_id\":1,\"event\":2}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CUSTOMER,test._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-23 11:32:04.949 - [来自Mysql的共享挖掘任务][Mysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-07-23 11:32:05.157 - [来自Mysql的共享挖掘任务][Mysql] - Connector Mysql incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-23 11:48:48.038 - [来自Mysql的共享挖掘任务][Mysql] - Mysql binlog reader stopped 
[WARN ] 2024-07-23 11:48:48.040 - [来自Mysql的共享挖掘任务][Mysql] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.net.SocketException: Socket closed
	java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:118)
	java.net.SocketOutputStream.write(SocketOutputStream.java:143)
	com.github.shyiko.mysql.binlog.io.ByteArrayOutputStream.write(ByteArrayOutputStream.java:83)
	com.github.shyiko.mysql.binlog.network.protocol.PacketChannel.write(PacketChannel.java:88)
	com.github.shyiko.mysql.binlog.network.Authenticator.authenticate(Authenticator.java:69)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-07-23 11:49:48.132 - [来自Mysql的共享挖掘任务][Mysql] - Starting mysql cdc, server name: 52923cd5-0c72-4472-af4c-00cbdaba2e6f 
[INFO ] 2024-07-23 11:49:48.230 - [来自Mysql的共享挖掘任务][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 612383417
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 52923cd5-0c72-4472-af4c-00cbdaba2e6f
  database.port: 3306
  threadName: Debezium-Mysql-Connector-52923cd5-0c72-4472-af4c-00cbdaba2e6f
  database.hostname: localhost
  database.password: ********
  name: 52923cd5-0c72-4472-af4c-00cbdaba2e6f
  pdk.offset.string: {"name":"52923cd5-0c72-4472-af4c-00cbdaba2e6f","offset":{"{\"server\":\"52923cd5-0c72-4472-af4c-00cbdaba2e6f\"}":"{\"ts_sec\":1721706171,\"file\":\"binlog.000033\",\"pos\":46867810,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CUSTOMER,test._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-23 11:49:48.230 - [来自Mysql的共享挖掘任务][Mysql] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-07-23 11:49:48.435 - [来自Mysql的共享挖掘任务][Mysql] - Connector Mysql incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-23 12:20:51.628 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_1469738849', head seq: 0, tail seq: 171643 
[INFO ] 2024-07-23 12:20:51.631 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务_CUSTOMER', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_45392933', head seq: 0, tail seq: 0 
[INFO ] 2024-07-23 12:50:58.495 - [来自Mysql的共享挖掘任务][Mysql] - Node Mysql[a41332d4fa724759ac1ca658fe8b9ae7] running status set to false 
[INFO ] 2024-07-23 12:55:58.649 - [来自Mysql的共享挖掘任务] - Start task milestones: 6695e5c55e81b0124202907b(来自Mysql的共享挖掘任务) 
[INFO ] 2024-07-23 12:55:58.894 - [来自Mysql的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-23 12:55:58.895 - [来自Mysql的共享挖掘任务] - The engine receives 来自Mysql的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-23 12:55:59.207 - [来自Mysql的共享挖掘任务][Mysql] - Node Mysql[a41332d4fa724759ac1ca658fe8b9ae7] start preload schema,table counts: 2 
[INFO ] 2024-07-23 12:55:59.207 - [来自Mysql的共享挖掘任务][Mysql] - Node Mysql[a41332d4fa724759ac1ca658fe8b9ae7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 12:55:59.207 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-23 12:55:59.229 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-23 12:55:59.321 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695e5c566ab5ede8aa012f2, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6695b8aa6d76494ed53f3882__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_1469738849, shareCdcTaskId=6695e5c55e81b0124202907b, connectionId=6695b8aa6d76494ed53f3882) 
[INFO ] 2024-07-23 12:55:59.321 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695e5c566ab5ede8aa012f4, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6695b8aa6d76494ed53f3882_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_45392933, shareCdcTaskId=6695e5c55e81b0124202907b, connectionId=6695b8aa6d76494ed53f3882) 
[INFO ] 2024-07-23 12:55:59.530 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务_CUSTOMER', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_45392933', head seq: 0, tail seq: 0 
[INFO ] 2024-07-23 12:55:59.533 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_1469738849', head seq: 0, tail seq: 173363 
[INFO ] 2024-07-23 12:55:59.721 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav392?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-23 12:55:59.721 - [来自Mysql的共享挖掘任务][Mysql] - Source node "Mysql" read batch size: 2000 
[INFO ] 2024-07-23 12:55:59.721 - [来自Mysql的共享挖掘任务][Mysql] - Source node "Mysql" event queue capacity: 4000 
[INFO ] 2024-07-23 12:55:59.726 - [来自Mysql的共享挖掘任务][Mysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-23 12:55:59.775 - [来自Mysql的共享挖掘任务][Mysql] - batch offset found: {},stream offset found: {"name":"52923cd5-0c72-4472-af4c-00cbdaba2e6f","offset":{"{\"server\":\"52923cd5-0c72-4472-af4c-00cbdaba2e6f\"}":"{\"ts_sec\":1721710252,\"file\":\"binlog.000033\",\"pos\":47977999,\"row\":1,\"server_id\":1,\"event\":2}"}} 
[INFO ] 2024-07-23 12:55:59.776 - [来自Mysql的共享挖掘任务][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-23 12:55:59.835 - [来自Mysql的共享挖掘任务][Mysql] - Starting stream read, table list: [CUSTOMER, _tapdata_heartbeat_table], offset: {"name":"52923cd5-0c72-4472-af4c-00cbdaba2e6f","offset":{"{\"server\":\"52923cd5-0c72-4472-af4c-00cbdaba2e6f\"}":"{\"ts_sec\":1721710252,\"file\":\"binlog.000033\",\"pos\":47977999,\"row\":1,\"server_id\":1,\"event\":2}"}} 
[INFO ] 2024-07-23 12:55:59.955 - [来自Mysql的共享挖掘任务][Mysql] - Starting mysql cdc, server name: 52923cd5-0c72-4472-af4c-00cbdaba2e6f 
[INFO ] 2024-07-23 12:56:00.004 - [来自Mysql的共享挖掘任务][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 459263185
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 52923cd5-0c72-4472-af4c-00cbdaba2e6f
  database.port: 3306
  threadName: Debezium-Mysql-Connector-52923cd5-0c72-4472-af4c-00cbdaba2e6f
  database.hostname: localhost
  database.password: ********
  name: 52923cd5-0c72-4472-af4c-00cbdaba2e6f
  pdk.offset.string: {"name":"52923cd5-0c72-4472-af4c-00cbdaba2e6f","offset":{"{\"server\":\"52923cd5-0c72-4472-af4c-00cbdaba2e6f\"}":"{\"ts_sec\":1721710252,\"file\":\"binlog.000033\",\"pos\":47977999,\"row\":1,\"server_id\":1,\"event\":2}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CUSTOMER,test._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-23 12:56:01.235 - [来自Mysql的共享挖掘任务][Mysql] - Connector Mysql incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-23 12:57:56.197 - [来自Mysql的共享挖掘任务][Mysql] - Node Mysql[a41332d4fa724759ac1ca658fe8b9ae7] running status set to false 
[INFO ] 2024-07-23 14:21:30.713 - [来自Mysql的共享挖掘任务] - Start task milestones: 6695e5c55e81b0124202907b(来自Mysql的共享挖掘任务) 
[INFO ] 2024-07-23 14:21:31.426 - [来自Mysql的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-23 14:21:31.609 - [来自Mysql的共享挖掘任务] - The engine receives 来自Mysql的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-23 14:21:32.459 - [来自Mysql的共享挖掘任务][Mysql] - Node Mysql[a41332d4fa724759ac1ca658fe8b9ae7] start preload schema,table counts: 2 
[INFO ] 2024-07-23 14:21:32.459 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-23 14:21:32.460 - [来自Mysql的共享挖掘任务][Mysql] - Node Mysql[a41332d4fa724759ac1ca658fe8b9ae7] preload schema finished, cost 0 ms 
[INFO ] 2024-07-23 14:21:32.462 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-23 14:21:32.601 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695e5c566ab5ede8aa012f2, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6695b8aa6d76494ed53f3882__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_1469738849, shareCdcTaskId=6695e5c55e81b0124202907b, connectionId=6695b8aa6d76494ed53f3882) 
[INFO ] 2024-07-23 14:21:32.632 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695e5c566ab5ede8aa012f4, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6695b8aa6d76494ed53f3882_CUSTOMER, version=v2, tableName=CUSTOMER, externalStorageTableName=ExternalStorage_SHARE_CDC_45392933, shareCdcTaskId=6695e5c55e81b0124202907b, connectionId=6695b8aa6d76494ed53f3882) 
[INFO ] 2024-07-23 14:21:32.634 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务_CUSTOMER', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_45392933', head seq: 0, tail seq: 0 
[INFO ] 2024-07-23 14:21:32.713 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mysql的共享挖掘任务__tapdata_heartbeat_table', name space: 'tapdatav392.ExternalStorage_SHARE_CDC_1469738849', head seq: 0, tail seq: 173483 
[INFO ] 2024-07-23 14:21:32.714 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav392?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-23 14:21:33.755 - [来自Mysql的共享挖掘任务][Mysql] - Source node "Mysql" read batch size: 100 
[INFO ] 2024-07-23 14:21:33.755 - [来自Mysql的共享挖掘任务][Mysql] - Source node "Mysql" event queue capacity: 200 
[INFO ] 2024-07-23 14:21:33.758 - [来自Mysql的共享挖掘任务][Mysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-23 14:21:33.771 - [来自Mysql的共享挖掘任务][Mysql] - batch offset found: {},stream offset found: {"name":"52923cd5-0c72-4472-af4c-00cbdaba2e6f","offset":{"{\"server\":\"52923cd5-0c72-4472-af4c-00cbdaba2e6f\"}":"{\"ts_sec\":1721710668,\"file\":\"binlog.000033\",\"pos\":48021070,\"row\":1,\"server_id\":1,\"event\":2}"}} 
[INFO ] 2024-07-23 14:21:33.772 - [来自Mysql的共享挖掘任务][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-23 14:21:33.933 - [来自Mysql的共享挖掘任务][Mysql] - Starting stream read, table list: [CUSTOMER, _tapdata_heartbeat_table], offset: {"name":"52923cd5-0c72-4472-af4c-00cbdaba2e6f","offset":{"{\"server\":\"52923cd5-0c72-4472-af4c-00cbdaba2e6f\"}":"{\"ts_sec\":1721710668,\"file\":\"binlog.000033\",\"pos\":48021070,\"row\":1,\"server_id\":1,\"event\":2}"}} 
[INFO ] 2024-07-23 14:21:33.938 - [来自Mysql的共享挖掘任务][Mysql] - Starting mysql cdc, server name: 52923cd5-0c72-4472-af4c-00cbdaba2e6f 
[INFO ] 2024-07-23 14:21:34.004 - [来自Mysql的共享挖掘任务][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2115188641
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 52923cd5-0c72-4472-af4c-00cbdaba2e6f
  database.port: 3306
  threadName: Debezium-Mysql-Connector-52923cd5-0c72-4472-af4c-00cbdaba2e6f
  database.hostname: localhost
  database.password: ********
  name: 52923cd5-0c72-4472-af4c-00cbdaba2e6f
  pdk.offset.string: {"name":"52923cd5-0c72-4472-af4c-00cbdaba2e6f","offset":{"{\"server\":\"52923cd5-0c72-4472-af4c-00cbdaba2e6f\"}":"{\"ts_sec\":1721710668,\"file\":\"binlog.000033\",\"pos\":48021070,\"row\":1,\"server_id\":1,\"event\":2}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CUSTOMER,test._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-23 14:21:34.887 - [来自Mysql的共享挖掘任务][Mysql] - Connector Mysql incremental start succeed, tables: [CUSTOMER, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-23 14:29:45.948 - [来自Mysql的共享挖掘任务][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-07-23 14:29:45.972 - [来自Mysql的共享挖掘任务][Mysql] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-23 14:29:45.972 - [来自Mysql的共享挖掘任务][Mysql] - java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected <-- Error Message -->
java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: BinaryLogClient is already connected
	com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:566)
	com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:762)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:752)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected
	at io.tapdata.common.exception.AbstractExceptionCollector.collectCdcConfigInvalid(AbstractExceptionCollector.java:63)
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:159)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:370)
	at io.tapdata.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:655)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:741)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.IllegalStateException: BinaryLogClient is already connected
	at com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:566)
	at com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	... 1 more

[INFO ] 2024-07-23 14:29:46.216 - [来自Mysql的共享挖掘任务][Mysql] - Job suspend in error handle 
[INFO ] 2024-07-23 14:30:06.468 - [来自Mysql的共享挖掘任务][Mysql] - Node Mysql[a41332d4fa724759ac1ca658fe8b9ae7] running status set to false 
[INFO ] 2024-07-23 14:30:06.471 - [来自Mysql的共享挖掘任务] - Task [来自Mysql的共享挖掘任务] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-23 14:30:14.298 - [来自Mysql的共享挖掘任务][Mysql] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-a41332d4fa724759ac1ca658fe8b9ae7 
[INFO ] 2024-07-23 14:30:14.511 - [来自Mysql的共享挖掘任务][Mysql] - PDK connector node released: HazelcastSourcePdkShareCDCNode-a41332d4fa724759ac1ca658fe8b9ae7 
[INFO ] 2024-07-23 14:30:14.629 - [来自Mysql的共享挖掘任务][Mysql] - Node Mysql[a41332d4fa724759ac1ca658fe8b9ae7] schema data cleaned 
[INFO ] 2024-07-23 14:30:14.641 - [来自Mysql的共享挖掘任务] - Task [来自Mysql的共享挖掘任务] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-23 14:30:14.647 - [来自Mysql的共享挖掘任务][Mysql] - Node Mysql[a41332d4fa724759ac1ca658fe8b9ae7] monitor closed 
[INFO ] 2024-07-23 14:30:14.658 - [来自Mysql的共享挖掘任务][Mysql] - Node Mysql[a41332d4fa724759ac1ca658fe8b9ae7] close complete, cost 8312 ms 
[INFO ] 2024-07-23 14:30:14.679 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[52f10c7915604588926fbda6bea4f9a5] running status set to false 
[INFO ] 2024-07-23 14:30:14.737 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-23 14:30:14.737 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-23 14:30:14.737 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[52f10c7915604588926fbda6bea4f9a5] schema data cleaned 
[INFO ] 2024-07-23 14:30:14.742 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[52f10c7915604588926fbda6bea4f9a5] monitor closed 
[INFO ] 2024-07-23 14:30:14.742 - [来自Mysql的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[52f10c7915604588926fbda6bea4f9a5] close complete, cost 84 ms 
[INFO ] 2024-07-23 14:30:27.838 - [来自Mysql的共享挖掘任务] - Task [来自Mysql的共享挖掘任务] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-23 14:30:27.853 - [来自Mysql的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-23 14:30:27.854 - [来自Mysql的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@73d56d23 
[INFO ] 2024-07-23 14:30:27.986 - [来自Mysql的共享挖掘任务] - Stop task milestones: 6695e5c55e81b0124202907b(来自Mysql的共享挖掘任务)  
[INFO ] 2024-07-23 14:30:27.986 - [来自Mysql的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-23 14:30:27.987 - [来自Mysql的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-23 14:30:28.021 - [来自Mysql的共享挖掘任务] - Remove memory task client succeed, task: 来自Mysql的共享挖掘任务[6695e5c55e81b0124202907b] 
[INFO ] 2024-07-23 14:30:28.023 - [来自Mysql的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自Mysql的共享挖掘任务[6695e5c55e81b0124202907b] 
