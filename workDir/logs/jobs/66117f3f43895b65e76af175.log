[INFO ] 2024-04-07 01:00:33.491 - [Dummy-<PERSON>sql] - Task initialization... 
[INFO ] 2024-04-07 01:00:33.492 - [Dummy-Mysql] - Start task milestones: 66117f3f43895b65e76af175(Dummy-Mysql) 
[INFO ] 2024-04-07 01:00:33.492 - [Dummy-<PERSON>sq<PERSON>] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-04-07 01:00:33.492 - [Dummy-Mysql] - The engine receives Dummy-Mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 01:00:33.492 - [Dummy-Mysql][dummy_test] - Node dummy_test[34aa3ce2-5aac-4263-8e33-037f945c054f] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:00:33.492 - [Dummy-<PERSON>sq<PERSON>][KafkaTest] - Node <PERSON>est[8fd9edbd-c620-496c-9a75-e8e1943777fc] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:00:33.492 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] preload schema finished, cost 56 ms 
[INFO ] 2024-04-07 01:00:33.492 - [Dummy-Mysql][dummy_test] - Node dummy_test[34aa3ce2-5aac-4263-8e33-037f945c054f] preload schema finished, cost 58 ms 
[INFO ] 2024-04-07 01:00:34.057 - [Dummy-Mysql][KafkaTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 01:00:34.160 - [Dummy-Mysql][dummy_test] - Start dummy connector 
[INFO ] 2024-04-07 01:00:34.161 - [Dummy-Mysql][dummy_test] - Source node "dummy_test" read batch size: 100 
[INFO ] 2024-04-07 01:00:34.164 - [Dummy-Mysql][dummy_test] - Source node "dummy_test" event queue capacity: 200 
[INFO ] 2024-04-07 01:00:34.165 - [Dummy-Mysql][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 01:00:34.172 - [Dummy-Mysql][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1712422834161,"lastTimes":1712422834161,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-04-07 01:00:34.256 - [Dummy-Mysql][dummy_test] - Initial sync started 
[INFO ] 2024-04-07 01:00:34.260 - [Dummy-Mysql][dummy_test] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-04-07 01:00:34.261 - [Dummy-Mysql][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-04-07 01:00:34.306 - [Dummy-Mysql][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-04-07 01:00:34.306 - [Dummy-Mysql][dummy_test] - Query table 'dummy_test' counts: 1000 
[INFO ] 2024-04-07 01:00:34.368 - [Dummy-Mysql][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-04-07 01:00:34.368 - [Dummy-Mysql][dummy_test] - Initial sync completed 
[INFO ] 2024-04-07 01:00:34.368 - [Dummy-Mysql][dummy_test] - Incremental sync starting... 
[INFO ] 2024-04-07 01:00:34.368 - [Dummy-Mysql][dummy_test] - Initial sync completed 
[INFO ] 2024-04-07 01:00:34.370 - [Dummy-Mysql][dummy_test] - Starting stream read, table list: [dummy_test], offset: {"syncStage":null,"beginTimes":1712422834161,"lastTimes":1712422834161,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-04-07 01:00:34.370 - [Dummy-Mysql][dummy_test] - Start [dummy_test] stream read 
[INFO ] 2024-04-07 01:00:34.576 - [Dummy-Mysql][dummy_test] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[INFO ] 2024-04-07 01:00:55.477 - [Dummy-Mysql] - Stop task milestones: 66117f3f43895b65e76af175(Dummy-Mysql)  
[INFO ] 2024-04-07 01:00:55.505 - [Dummy-Mysql][dummy_test] - Node dummy_test[34aa3ce2-5aac-4263-8e33-037f945c054f] running status set to false 
[INFO ] 2024-04-07 01:00:55.508 - [Dummy-Mysql][dummy_test] - Stop connector 
[INFO ] 2024-04-07 01:00:55.521 - [Dummy-Mysql][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-34aa3ce2-5aac-4263-8e33-037f945c054f 
[INFO ] 2024-04-07 01:00:55.522 - [Dummy-Mysql][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-34aa3ce2-5aac-4263-8e33-037f945c054f 
[INFO ] 2024-04-07 01:00:55.522 - [Dummy-Mysql][dummy_test] - Node dummy_test[34aa3ce2-5aac-4263-8e33-037f945c054f] schema data cleaned 
[INFO ] 2024-04-07 01:00:55.528 - [Dummy-Mysql][dummy_test] - Node dummy_test[34aa3ce2-5aac-4263-8e33-037f945c054f] monitor closed 
[INFO ] 2024-04-07 01:00:55.528 - [Dummy-Mysql][dummy_test] - Node dummy_test[34aa3ce2-5aac-4263-8e33-037f945c054f] close complete, cost 29 ms 
[INFO ] 2024-04-07 01:00:55.545 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] running status set to false 
[INFO ] 2024-04-07 01:00:55.545 - [Dummy-Mysql][KafkaTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-8fd9edbd-c620-496c-9a75-e8e1943777fc 
[INFO ] 2024-04-07 01:00:55.545 - [Dummy-Mysql][KafkaTest] - PDK connector node released: HazelcastTargetPdkDataNode-8fd9edbd-c620-496c-9a75-e8e1943777fc 
[INFO ] 2024-04-07 01:00:55.546 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] schema data cleaned 
[INFO ] 2024-04-07 01:00:55.549 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] monitor closed 
[INFO ] 2024-04-07 01:00:55.552 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] close complete, cost 18 ms 
[INFO ] 2024-04-07 01:00:57.652 - [Dummy-Mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 01:00:57.652 - [Dummy-Mysql] - Stopped task aspect(s) 
[INFO ] 2024-04-07 01:00:57.654 - [Dummy-Mysql] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 01:00:57.704 - [Dummy-Mysql] - Remove memory task client succeed, task: Dummy-Mysql[66117f3f43895b65e76af175] 
[INFO ] 2024-04-07 01:00:57.704 - [Dummy-Mysql] - Destroy memory task client cache succeed, task: Dummy-Mysql[66117f3f43895b65e76af175] 
[INFO ] 2024-04-07 01:01:37.759 - [Dummy-Mysql] - Task initialization... 
[INFO ] 2024-04-07 01:01:37.797 - [Dummy-Mysql] - Start task milestones: 66117f3f43895b65e76af175(Dummy-Mysql) 
[INFO ] 2024-04-07 01:01:37.797 - [Dummy-Mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 01:01:37.893 - [Dummy-Mysql] - The engine receives Dummy-Mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 01:01:37.893 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:01:37.894 - [Dummy-Mysql][dummy_test] - Node dummy_test[34aa3ce2-5aac-4263-8e33-037f945c054f] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:01:37.937 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] preload schema finished, cost 40 ms 
[INFO ] 2024-04-07 01:01:37.938 - [Dummy-Mysql][dummy_test] - Node dummy_test[34aa3ce2-5aac-4263-8e33-037f945c054f] preload schema finished, cost 39 ms 
[INFO ] 2024-04-07 01:01:38.042 - [Dummy-Mysql][dummy_test] - Start dummy connector 
[INFO ] 2024-04-07 01:01:38.042 - [Dummy-Mysql][dummy_test] - Source node "dummy_test" read batch size: 100 
[INFO ] 2024-04-07 01:01:38.070 - [Dummy-Mysql][dummy_test] - Source node "dummy_test" event queue capacity: 200 
[INFO ] 2024-04-07 01:01:38.070 - [Dummy-Mysql][dummy_test] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-04-07 01:01:38.143 - [Dummy-Mysql][dummy_test] - batch offset found: {"dummy_test":{"syncStage":"Initial","beginTimes":1712422834265,"lastTimes":1712422834363,"lastTN":1000,"tableStats":{"dummy_test":{"insertTotals":1000,"updateTotals":0,"deleteTotals":0}}}},stream offset found: {"syncStage":"Incremental","beginTimes":1712422834161,"lastTimes":1712422854484,"lastTN":3000,"tableStats":{"dummy_test":{"insertTotals":2000,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-04-07 01:01:38.145 - [Dummy-Mysql][dummy_test] - Incremental sync starting... 
[INFO ] 2024-04-07 01:01:38.146 - [Dummy-Mysql][dummy_test] - Initial sync completed 
[INFO ] 2024-04-07 01:01:38.147 - [Dummy-Mysql][dummy_test] - Starting stream read, table list: [dummy_test], offset: {"syncStage":"Incremental","beginTimes":1712422834161,"lastTimes":1712422854484,"lastTN":3000,"tableStats":{"dummy_test":{"insertTotals":2000,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-04-07 01:01:38.148 - [Dummy-Mysql][dummy_test] - Start [dummy_test] stream read 
[INFO ] 2024-04-07 01:01:38.262 - [Dummy-Mysql][dummy_test] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[INFO ] 2024-04-07 01:01:38.262 - [Dummy-Mysql][KafkaTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 01:04:00.298 - [Dummy-Mysql] - Stop task milestones: 66117f3f43895b65e76af175(Dummy-Mysql)  
[INFO ] 2024-04-07 01:04:00.300 - [Dummy-Mysql][dummy_test] - Node dummy_test[34aa3ce2-5aac-4263-8e33-037f945c054f] running status set to false 
[INFO ] 2024-04-07 01:04:00.302 - [Dummy-Mysql][dummy_test] - Stop connector 
[INFO ] 2024-04-07 01:04:00.314 - [Dummy-Mysql][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-34aa3ce2-5aac-4263-8e33-037f945c054f 
[INFO ] 2024-04-07 01:04:00.316 - [Dummy-Mysql][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-34aa3ce2-5aac-4263-8e33-037f945c054f 
[INFO ] 2024-04-07 01:04:00.316 - [Dummy-Mysql][dummy_test] - Node dummy_test[34aa3ce2-5aac-4263-8e33-037f945c054f] schema data cleaned 
[INFO ] 2024-04-07 01:04:00.318 - [Dummy-Mysql][dummy_test] - Node dummy_test[34aa3ce2-5aac-4263-8e33-037f945c054f] monitor closed 
[INFO ] 2024-04-07 01:04:00.319 - [Dummy-Mysql][dummy_test] - Node dummy_test[34aa3ce2-5aac-4263-8e33-037f945c054f] close complete, cost 43 ms 
[INFO ] 2024-04-07 01:04:00.348 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] running status set to false 
[INFO ] 2024-04-07 01:04:00.353 - [Dummy-Mysql][KafkaTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-8fd9edbd-c620-496c-9a75-e8e1943777fc 
[INFO ] 2024-04-07 01:04:00.353 - [Dummy-Mysql][KafkaTest] - PDK connector node released: HazelcastTargetPdkDataNode-8fd9edbd-c620-496c-9a75-e8e1943777fc 
[INFO ] 2024-04-07 01:04:00.354 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] schema data cleaned 
[INFO ] 2024-04-07 01:04:00.354 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] monitor closed 
[INFO ] 2024-04-07 01:04:00.354 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] close complete, cost 30 ms 
[INFO ] 2024-04-07 01:04:03.044 - [Dummy-Mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 01:04:03.044 - [Dummy-Mysql] - Stopped task aspect(s) 
[INFO ] 2024-04-07 01:04:03.044 - [Dummy-Mysql] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 01:04:03.079 - [Dummy-Mysql] - Remove memory task client succeed, task: Dummy-Mysql[66117f3f43895b65e76af175] 
[INFO ] 2024-04-07 01:04:03.284 - [Dummy-Mysql] - Destroy memory task client cache succeed, task: Dummy-Mysql[66117f3f43895b65e76af175] 
[INFO ] 2024-04-07 01:07:48.969 - [Dummy-Mysql] - Task initialization... 
[INFO ] 2024-04-07 01:07:48.970 - [Dummy-Mysql] - Start task milestones: 66117f3f43895b65e76af175(Dummy-Mysql) 
[INFO ] 2024-04-07 01:07:49.073 - [Dummy-Mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 01:07:49.074 - [Dummy-Mysql] - The engine receives Dummy-Mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 01:07:49.208 - [Dummy-Mysql][dummy_test] - Node dummy_test[34aa3ce2-5aac-4263-8e33-037f945c054f] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:07:49.208 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:07:49.208 - [Dummy-Mysql][dummy_test] - Node dummy_test[34aa3ce2-5aac-4263-8e33-037f945c054f] preload schema finished, cost 50 ms 
[INFO ] 2024-04-07 01:07:49.208 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] preload schema finished, cost 50 ms 
[INFO ] 2024-04-07 01:07:49.326 - [Dummy-Mysql][dummy_test] - Start dummy connector 
[INFO ] 2024-04-07 01:07:49.326 - [Dummy-Mysql][dummy_test] - Source node "dummy_test" read batch size: 100 
[INFO ] 2024-04-07 01:07:49.326 - [Dummy-Mysql][dummy_test] - Source node "dummy_test" event queue capacity: 200 
[INFO ] 2024-04-07 01:07:49.327 - [Dummy-Mysql][dummy_test] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-04-07 01:07:49.375 - [Dummy-Mysql][dummy_test] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1712422834161,"lastTimes":1712423038247,"lastTN":8000,"tableStats":{"dummy_test":{"insertTotals":10000,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-04-07 01:07:49.375 - [Dummy-Mysql][dummy_test] - Incremental sync starting... 
[INFO ] 2024-04-07 01:07:49.377 - [Dummy-Mysql][dummy_test] - Initial sync completed 
[INFO ] 2024-04-07 01:07:49.377 - [Dummy-Mysql][dummy_test] - Starting stream read, table list: [dummy_test], offset: {"syncStage":"Incremental","beginTimes":1712422834161,"lastTimes":1712423038247,"lastTN":8000,"tableStats":{"dummy_test":{"insertTotals":10000,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-04-07 01:07:49.378 - [Dummy-Mysql][dummy_test] - Start [dummy_test] stream read 
[INFO ] 2024-04-07 01:07:49.378 - [Dummy-Mysql][dummy_test] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[INFO ] 2024-04-07 01:07:49.580 - [Dummy-Mysql][KafkaTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 01:14:41.043 - [Dummy-Mysql] - Stop task milestones: 66117f3f43895b65e76af175(Dummy-Mysql)  
[INFO ] 2024-04-07 01:14:41.204 - [Dummy-Mysql][dummy_test] - Node dummy_test[34aa3ce2-5aac-4263-8e33-037f945c054f] running status set to false 
[INFO ] 2024-04-07 01:14:41.212 - [Dummy-Mysql][dummy_test] - Stop connector 
[INFO ] 2024-04-07 01:14:41.212 - [Dummy-Mysql][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-34aa3ce2-5aac-4263-8e33-037f945c054f 
[INFO ] 2024-04-07 01:14:41.212 - [Dummy-Mysql][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-34aa3ce2-5aac-4263-8e33-037f945c054f 
[INFO ] 2024-04-07 01:14:41.213 - [Dummy-Mysql][dummy_test] - Node dummy_test[34aa3ce2-5aac-4263-8e33-037f945c054f] schema data cleaned 
[INFO ] 2024-04-07 01:14:41.214 - [Dummy-Mysql][dummy_test] - Node dummy_test[34aa3ce2-5aac-4263-8e33-037f945c054f] monitor closed 
[INFO ] 2024-04-07 01:14:41.214 - [Dummy-Mysql][dummy_test] - Node dummy_test[34aa3ce2-5aac-4263-8e33-037f945c054f] close complete, cost 15 ms 
[INFO ] 2024-04-07 01:14:41.214 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] running status set to false 
[INFO ] 2024-04-07 01:14:41.243 - [Dummy-Mysql][KafkaTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-8fd9edbd-c620-496c-9a75-e8e1943777fc 
[INFO ] 2024-04-07 01:14:41.243 - [Dummy-Mysql][KafkaTest] - PDK connector node released: HazelcastTargetPdkDataNode-8fd9edbd-c620-496c-9a75-e8e1943777fc 
[INFO ] 2024-04-07 01:14:41.244 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] schema data cleaned 
[INFO ] 2024-04-07 01:14:41.245 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] monitor closed 
[INFO ] 2024-04-07 01:14:41.245 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] close complete, cost 30 ms 
[INFO ] 2024-04-07 01:14:43.925 - [Dummy-Mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 01:14:43.926 - [Dummy-Mysql] - Stopped task aspect(s) 
[INFO ] 2024-04-07 01:14:43.949 - [Dummy-Mysql] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 01:14:43.952 - [Dummy-Mysql] - Remove memory task client succeed, task: Dummy-Mysql[66117f3f43895b65e76af175] 
[INFO ] 2024-04-07 01:14:43.952 - [Dummy-Mysql] - Destroy memory task client cache succeed, task: Dummy-Mysql[66117f3f43895b65e76af175] 
[INFO ] 2024-04-07 01:16:09.428 - [Dummy-Mysql] - Task initialization... 
[INFO ] 2024-04-07 01:16:09.428 - [Dummy-Mysql] - Start task milestones: 66117f3f43895b65e76af175(Dummy-Mysql) 
[INFO ] 2024-04-07 01:16:09.428 - [Dummy-Mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 01:16:09.428 - [Dummy-Mysql] - The engine receives Dummy-Mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 01:16:09.428 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:16:09.428 - [Dummy-Mysql][dummy_test] - Node dummy_test[ca686cfc-02ec-43d3-b015-859f87b6b32b] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:16:09.428 - [Dummy-Mysql][dummy_test] - Node dummy_test[ca686cfc-02ec-43d3-b015-859f87b6b32b] preload schema finished, cost 26 ms 
[INFO ] 2024-04-07 01:16:09.428 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] preload schema finished, cost 25 ms 
[INFO ] 2024-04-07 01:16:09.872 - [Dummy-Mysql][dummy_test] - Start dummy connector 
[INFO ] 2024-04-07 01:16:09.873 - [Dummy-Mysql][dummy_test] - Source node "dummy_test" read batch size: 100 
[INFO ] 2024-04-07 01:16:09.873 - [Dummy-Mysql][dummy_test] - Source node "dummy_test" event queue capacity: 200 
[INFO ] 2024-04-07 01:16:09.873 - [Dummy-Mysql][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 01:16:10.005 - [Dummy-Mysql][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1712423769873,"lastTimes":1712423769873,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-04-07 01:16:10.007 - [Dummy-Mysql][dummy_test] - Initial sync started 
[INFO ] 2024-04-07 01:16:10.007 - [Dummy-Mysql][dummy_test] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-04-07 01:16:10.014 - [Dummy-Mysql][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-04-07 01:16:10.015 - [Dummy-Mysql][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-04-07 01:16:10.216 - [Dummy-Mysql][dummy_test] - Query table 'dummy_test' counts: 1000 
[INFO ] 2024-04-07 01:16:10.425 - [Dummy-Mysql][KafkaTest] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 01:16:10.434 - [Dummy-Mysql][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-04-07 01:16:10.434 - [Dummy-Mysql][dummy_test] - Initial sync completed 
[INFO ] 2024-04-07 01:16:10.434 - [Dummy-Mysql][dummy_test] - Incremental sync starting... 
[INFO ] 2024-04-07 01:16:10.446 - [Dummy-Mysql][dummy_test] - Initial sync completed 
[INFO ] 2024-04-07 01:16:10.448 - [Dummy-Mysql][dummy_test] - Starting stream read, table list: [dummy_test], offset: {"syncStage":null,"beginTimes":1712423769873,"lastTimes":1712423769873,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-04-07 01:16:10.451 - [Dummy-Mysql][dummy_test] - Start [dummy_test] stream read 
[INFO ] 2024-04-07 01:16:10.451 - [Dummy-Mysql][dummy_test] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[INFO ] 2024-04-07 01:23:52.307 - [Dummy-Mysql] - Stop task milestones: 66117f3f43895b65e76af175(Dummy-Mysql)  
[INFO ] 2024-04-07 01:23:52.627 - [Dummy-Mysql][dummy_test] - Node dummy_test[ca686cfc-02ec-43d3-b015-859f87b6b32b] running status set to false 
[INFO ] 2024-04-07 01:23:52.628 - [Dummy-Mysql][dummy_test] - Stop connector 
[INFO ] 2024-04-07 01:23:52.640 - [Dummy-Mysql][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-ca686cfc-02ec-43d3-b015-859f87b6b32b 
[INFO ] 2024-04-07 01:23:52.640 - [Dummy-Mysql][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-ca686cfc-02ec-43d3-b015-859f87b6b32b 
[INFO ] 2024-04-07 01:23:52.640 - [Dummy-Mysql][dummy_test] - Node dummy_test[ca686cfc-02ec-43d3-b015-859f87b6b32b] schema data cleaned 
[INFO ] 2024-04-07 01:23:52.641 - [Dummy-Mysql][dummy_test] - Node dummy_test[ca686cfc-02ec-43d3-b015-859f87b6b32b] monitor closed 
[INFO ] 2024-04-07 01:23:52.642 - [Dummy-Mysql][dummy_test] - Node dummy_test[ca686cfc-02ec-43d3-b015-859f87b6b32b] close complete, cost 18 ms 
[INFO ] 2024-04-07 01:23:52.642 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] running status set to false 
[INFO ] 2024-04-07 01:23:52.678 - [Dummy-Mysql][KafkaTest] - PDK connector node stopped: HazelcastTargetPdkDataNode-8fd9edbd-c620-496c-9a75-e8e1943777fc 
[INFO ] 2024-04-07 01:23:52.679 - [Dummy-Mysql][KafkaTest] - PDK connector node released: HazelcastTargetPdkDataNode-8fd9edbd-c620-496c-9a75-e8e1943777fc 
[INFO ] 2024-04-07 01:23:52.679 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] schema data cleaned 
[INFO ] 2024-04-07 01:23:52.679 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] monitor closed 
[INFO ] 2024-04-07 01:23:52.680 - [Dummy-Mysql][KafkaTest] - Node KafkaTest[8fd9edbd-c620-496c-9a75-e8e1943777fc] close complete, cost 37 ms 
[INFO ] 2024-04-07 01:23:54.534 - [Dummy-Mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 01:23:54.535 - [Dummy-Mysql] - Stopped task aspect(s) 
[INFO ] 2024-04-07 01:23:54.535 - [Dummy-Mysql] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 01:23:54.580 - [Dummy-Mysql] - Remove memory task client succeed, task: Dummy-Mysql[66117f3f43895b65e76af175] 
[INFO ] 2024-04-07 01:23:54.583 - [Dummy-Mysql] - Destroy memory task client cache succeed, task: Dummy-Mysql[66117f3f43895b65e76af175] 
