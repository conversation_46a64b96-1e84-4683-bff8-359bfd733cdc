[TRACE] 2025-03-11 10:54:24.032 - [Test5] - Task initialization... 
[TRACE] 2025-03-11 10:54:24.033 - [Test5] - Start task milestones: 67cfa5b40f0fca3a975c8eed(Test5) 
[INFO ] 2025-03-11 10:54:24.033 - [Test5] - Loading table structure completed 
[TRACE] 2025-03-11 10:54:24.033 - [Test5] - <PERSON>de performs snapshot read asynchronously 
[TRACE] 2025-03-11 10:54:24.033 - [Test5] - The engine receives Test5 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-11 10:54:24.033 - [Test5] - Task started 
[TRACE] 2025-03-11 10:54:24.129 - [Test5][Test5] - Node Test5[6b6ac02b-8595-466d-828b-18dcc9df2ceb] start preload schema,table counts: 90 
[TRACE] 2025-03-11 10:54:24.130 - [Test5][Sybase] - Node Sybase[80c4607d-3f4f-4915-b031-4281951fb478] start preload schema,table counts: 90 
[TRACE] 2025-03-11 10:54:24.130 - [Test5][Sybase] - Node Sybase[80c4607d-3f4f-4915-b031-4281951fb478] preload schema finished, cost 0 ms 
[TRACE] 2025-03-11 10:54:24.130 - [Test5][Test5] - Node Test5[6b6ac02b-8595-466d-828b-18dcc9df2ceb] preload schema finished, cost 1 ms 
[INFO ] 2025-03-11 10:54:24.869 - [Test5][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-03-11 10:54:24.873 - [Test5][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-03-11 10:54:24.873 - [Test5][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-03-11 10:54:24.873 - [Test5][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-03-11 10:54:24.873 - [Test5][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-03-11 10:54:25.145 - [Test5][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-03-11 10:54:25.145 - [Test5][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-03-11 10:54:25.407 - [Test5][Sybase] - Starting batch read from 90 tables 
[TRACE] 2025-03-11 10:54:25.410 - [Test5][Sybase] - Initial sync started 
[INFO ] 2025-03-11 10:54:25.410 - [Test5][Sybase] - Starting batch read from table: pg_yyy_bmsql_new_order 
[TRACE] 2025-03-11 10:54:25.410 - [Test5][Sybase] - Table pg_yyy_bmsql_new_order is going to be initial synced 
[INFO ] 2025-03-11 10:54:25.431 - [Test5][Test5] - Sink connector(Test5) initialization completed 
[TRACE] 2025-03-11 10:54:25.431 - [Test5][Test5] - Node(Test5) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-11 10:54:25.431 - [Test5][Test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-11 10:54:25.539 - [Test5][Test5] - Apply table structure to target database 
[TRACE] 2025-03-11 10:54:25.539 - [Test5][Test5] - Exception skipping - The current exception does not match the skip exception strategy, message: Missing privileges when write data on postgres. 
 - Executing operation: createTable
 - Table name: [pg_yyy_bmsql_new_order]
 - Missing privileges: [] 
[ERROR] 2025-03-11 10:54:25.554 - [Test5][Test5] - Missing privileges when write data on postgres. 
 - Executing operation: createTable
 - Table name: [pg_yyy_bmsql_new_order]
 - Missing privileges: [] <-- Error Message -->
Missing privileges when write data on postgres. 
 - Executing operation: createTable
 - Table name: [pg_yyy_bmsql_new_order]
 - Missing privileges: []

<-- Simple Stack Trace -->
Caused by: org.postgresql.util.PSQLException: ERROR: permission denied for schema test5
  位置：14
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...

<-- Full Stack Trace -->
org.postgresql.util.PSQLException: ERROR: permission denied for schema test5
  位置：14
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.collectWritePrivileges(PostgresExceptionCollector.java:51)
	at io.tapdata.common.CommonDbConnector.createTable(CommonDbConnector.java:203)
	at io.tapdata.common.CommonDbConnector.createTableV2(CommonDbConnector.java:212)
	at io.tapdata.connector.postgres.PostgresConnector.createTableV2(PostgresConnector.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$18(HazelcastTargetPdkBaseNode.java:447)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:77)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$19(HazelcastTargetPdkBaseNode.java:445)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$createTable$20(HazelcastTargetPdkBaseNode.java:439)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.doCreateTable(HazelcastTargetPdkBaseNode.java:331)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createTable(HazelcastTargetPdkBaseNode.java:438)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:238)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$3(HazelcastTargetPdkDataNode.java:176)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:167)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:161)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:112)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:240)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.postgresql.util.PSQLException: ERROR: permission denied for schema test5
  位置：14
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:329)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:315)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:291)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:286)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createTable(CommonDbConnector.java:201)
	... 34 more

[TRACE] 2025-03-11 10:54:25.554 - [Test5][Test5] - Job suspend in error handle 
[TRACE] 2025-03-11 10:54:25.663 - [Test5][Sybase] - Node Sybase[80c4607d-3f4f-4915-b031-4281951fb478] running status set to false 
[TRACE] 2025-03-11 10:54:25.663 - [Test5][Sybase] - Query snapshot row size completed: Sybase(80c4607d-3f4f-4915-b031-4281951fb478) 
[TRACE] 2025-03-11 10:54:25.823 - [Test5][Sybase] - Initial sync completed 
[INFO ] 2025-03-11 10:54:25.824 - [Test5][Sybase] - Batch read completed. 
[TRACE] 2025-03-11 10:54:25.824 - [Test5][Sybase] - Incremental sync starting... 
[TRACE] 2025-03-11 10:54:25.824 - [Test5][Sybase] - Incremental sync completed 
[TRACE] 2025-03-11 10:54:25.878 - [Test5][Sybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_80c4607d-3f4f-4915-b031-4281951fb478_1741661664782 
[TRACE] 2025-03-11 10:54:25.878 - [Test5][Sybase] - PDK connector node released: HazelcastSourcePdkDataNode_80c4607d-3f4f-4915-b031-4281951fb478_1741661664782 
[TRACE] 2025-03-11 10:54:25.878 - [Test5][Sybase] - Node Sybase[80c4607d-3f4f-4915-b031-4281951fb478] schema data cleaned 
[TRACE] 2025-03-11 10:54:25.881 - [Test5][Sybase] - Node Sybase[80c4607d-3f4f-4915-b031-4281951fb478] monitor closed 
[TRACE] 2025-03-11 10:54:25.881 - [Test5][Sybase] - Node Sybase[80c4607d-3f4f-4915-b031-4281951fb478] close complete, cost 308 ms 
[TRACE] 2025-03-11 10:54:25.892 - [Test5][Test5] - Node Test5[6b6ac02b-8595-466d-828b-18dcc9df2ceb] running status set to false 
[TRACE] 2025-03-11 10:54:25.892 - [Test5][Test5] - PDK connector node stopped: HazelcastTargetPdkDataNode_6b6ac02b-8595-466d-828b-18dcc9df2ceb_1741661665028 
[TRACE] 2025-03-11 10:54:25.892 - [Test5][Test5] - PDK connector node released: HazelcastTargetPdkDataNode_6b6ac02b-8595-466d-828b-18dcc9df2ceb_1741661665028 
[TRACE] 2025-03-11 10:54:25.892 - [Test5][Test5] - Node Test5[6b6ac02b-8595-466d-828b-18dcc9df2ceb] schema data cleaned 
[TRACE] 2025-03-11 10:54:25.893 - [Test5][Test5] - Node Test5[6b6ac02b-8595-466d-828b-18dcc9df2ceb] monitor closed 
[TRACE] 2025-03-11 10:54:25.893 - [Test5][Test5] - Node Test5[6b6ac02b-8595-466d-828b-18dcc9df2ceb] close complete, cost 11 ms 
[INFO ] 2025-03-11 10:54:29.956 - [Test5] - Task [Test5] cannot retry, reason: Task retry service not start 
[TRACE] 2025-03-11 10:54:29.964 - [Test5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-11 10:54:29.964 - [Test5] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3af04694 
[TRACE] 2025-03-11 10:54:30.101 - [Test5] - Stop task milestones: 67cfa5b40f0fca3a975c8eed(Test5)  
[TRACE] 2025-03-11 10:54:30.102 - [Test5] - Stopped task aspect(s) 
[TRACE] 2025-03-11 10:54:30.102 - [Test5] - Snapshot order controller have been removed 
[INFO ] 2025-03-11 10:54:30.102 - [Test5] - Task stopped. 
[TRACE] 2025-03-11 10:54:30.128 - [Test5] - Remove memory task client succeed, task: Test5[67cfa5b40f0fca3a975c8eed] 
[TRACE] 2025-03-11 10:54:30.132 - [Test5] - Destroy memory task client cache succeed, task: Test5[67cfa5b40f0fca3a975c8eed] 
[TRACE] 2025-03-11 10:55:07.196 - [Test5] - Task initialization... 
[TRACE] 2025-03-11 10:55:07.403 - [Test5] - Start task milestones: 67cfa5b40f0fca3a975c8eed(Test5) 
[INFO ] 2025-03-11 10:55:07.693 - [Test5] - Loading table structure completed 
[TRACE] 2025-03-11 10:55:07.794 - [Test5] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-11 10:55:07.795 - [Test5] - The engine receives Test5 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-11 10:55:07.932 - [Test5] - Task started 
[TRACE] 2025-03-11 10:55:07.932 - [Test5][Sybase] - Node Sybase[80c4607d-3f4f-4915-b031-4281951fb478] start preload schema,table counts: 90 
[TRACE] 2025-03-11 10:55:07.932 - [Test5][Test5] - Node Test5[6b6ac02b-8595-466d-828b-18dcc9df2ceb] start preload schema,table counts: 90 
[TRACE] 2025-03-11 10:55:07.933 - [Test5][Sybase] - Node Sybase[80c4607d-3f4f-4915-b031-4281951fb478] preload schema finished, cost 2 ms 
[TRACE] 2025-03-11 10:55:07.933 - [Test5][Test5] - Node Test5[6b6ac02b-8595-466d-828b-18dcc9df2ceb] preload schema finished, cost 3 ms 
[INFO ] 2025-03-11 10:55:08.700 - [Test5][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-03-11 10:55:08.702 - [Test5][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-03-11 10:55:08.702 - [Test5][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-03-11 10:55:08.702 - [Test5][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-03-11 10:55:08.702 - [Test5][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-03-11 10:55:08.903 - [Test5][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-03-11 10:55:09.108 - [Test5][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-03-11 10:55:09.130 - [Test5][Sybase] - Starting batch read from 90 tables 
[TRACE] 2025-03-11 10:55:09.150 - [Test5][Sybase] - Initial sync started 
[INFO ] 2025-03-11 10:55:09.151 - [Test5][Sybase] - Starting batch read from table: pg_yyy_bmsql_new_order 
[TRACE] 2025-03-11 10:55:09.151 - [Test5][Sybase] - Table pg_yyy_bmsql_new_order is going to be initial synced 
[INFO ] 2025-03-11 10:55:09.261 - [Test5][Test5] - Sink connector(Test5) initialization completed 
[TRACE] 2025-03-11 10:55:09.261 - [Test5][Test5] - Node(Test5) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-11 10:55:09.261 - [Test5][Test5] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-11 10:55:09.462 - [Test5][Test5] - Apply table structure to target database 
[WARN ] 2025-03-11 10:55:12.708 - [Test5][Test5] - Create index failed ERROR: column "_no_pk_hash" does not exist, please execute it manually [create unique index "IDX_uepdktableecb709d766d0" on "test5"."nouniquepdktableTarget"("_no_pk_hash" asc)] 
[WARN ] 2025-03-11 10:55:20.209 - [Test5][Test5] - Create index failed ERROR: column "_no_pk_hash" does not exist, please execute it manually [create unique index "IDX_uepdktableecb709d766d0" on "test5"."nouniquepdktable"("_no_pk_hash" asc)] 
[WARN ] 2025-03-11 10:55:29.870 - [Test5][Test5] - Create index failed ERROR: column "_no_pk_hash" does not exist, please execute it manually [create unique index "IDX_ithNoIndex021292472fb1" on "test5"."testTimeStampWithNoIndex"("_no_pk_hash" asc)] 
[INFO ] 2025-03-11 10:55:35.719 - [Test5][Sybase] - Table pg_yyy_bmsql_new_order has been completed batch read 
[INFO ] 2025-03-11 10:55:35.720 - [Test5][Sybase] - Starting batch read from table: bmsql_customer 
[TRACE] 2025-03-11 10:55:35.720 - [Test5][Sybase] - Table bmsql_customer is going to be initial synced 
[INFO ] 2025-03-11 10:55:36.023 - [Test5][Sybase] - Table bmsql_customer has been completed batch read 
[INFO ] 2025-03-11 10:55:36.228 - [Test5][Sybase] - Starting batch read from table: pg_yyy_bmsql_history 
[TRACE] 2025-03-11 10:55:36.228 - [Test5][Sybase] - Table pg_yyy_bmsql_history is going to be initial synced 
[TRACE] 2025-03-11 10:55:39.876 - [Test5][Sybase] - Query snapshot row size completed: Sybase(80c4607d-3f4f-4915-b031-4281951fb478) 
[INFO ] 2025-03-11 10:55:47.393 - [Test5][Sybase] - Table pg_yyy_bmsql_history has been completed batch read 
[INFO ] 2025-03-11 10:55:47.394 - [Test5][Sybase] - Starting batch read from table: pg_sync_test 
[TRACE] 2025-03-11 10:55:47.395 - [Test5][Sybase] - Table pg_sync_test is going to be initial synced 
[INFO ] 2025-03-11 10:55:47.557 - [Test5][Sybase] - Table pg_sync_test has been completed batch read 
[INFO ] 2025-03-11 10:55:47.557 - [Test5][Sybase] - Starting batch read from table: bmsql_config 
[TRACE] 2025-03-11 10:55:47.557 - [Test5][Sybase] - Table bmsql_config is going to be initial synced 
[INFO ] 2025-03-11 10:55:47.844 - [Test5][Sybase] - Table bmsql_config has been completed batch read 
[INFO ] 2025-03-11 10:55:47.844 - [Test5][Sybase] - Starting batch read from table: pg_bmsql_item 
[TRACE] 2025-03-11 10:55:47.845 - [Test5][Sybase] - Table pg_bmsql_item is going to be initial synced 
[INFO ] 2025-03-11 10:55:53.237 - [Test5][Sybase] - Table pg_bmsql_item has been completed batch read 
[INFO ] 2025-03-11 10:55:53.239 - [Test5][Sybase] - Starting batch read from table: pg_yyy_bmsql_stock 
[TRACE] 2025-03-11 10:55:53.239 - [Test5][Sybase] - Table pg_yyy_bmsql_stock is going to be initial synced 
[INFO ] 2025-03-11 10:55:59.647 - [Test5][Sybase] - Table pg_yyy_bmsql_stock has been completed batch read 
[INFO ] 2025-03-11 10:55:59.649 - [Test5][Sybase] - Starting batch read from table: BMSQL_ITEM 
[TRACE] 2025-03-11 10:55:59.649 - [Test5][Sybase] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2025-03-11 10:56:00.013 - [Test5][Sybase] - Table BMSQL_ITEM has been completed batch read 
[INFO ] 2025-03-11 10:56:00.017 - [Test5][Sybase] - Starting batch read from table: pg_bmsql_customer 
[TRACE] 2025-03-11 10:56:00.017 - [Test5][Sybase] - Table pg_bmsql_customer is going to be initial synced 
[INFO ] 2025-03-11 10:56:06.643 - [Test5][Sybase] - Table pg_bmsql_customer has been completed batch read 
[INFO ] 2025-03-11 10:56:06.644 - [Test5][Sybase] - Starting batch read from table: a_testMoneyv 
[TRACE] 2025-03-11 10:56:06.644 - [Test5][Sybase] - Table a_testMoneyv is going to be initial synced 
[INFO ] 2025-03-11 10:56:06.959 - [Test5][Sybase] - Table a_testMoneyv has been completed batch read 
[INFO ] 2025-03-11 10:56:06.960 - [Test5][Sybase] - Starting batch read from table: tdText 
[TRACE] 2025-03-11 10:56:06.960 - [Test5][Sybase] - Table tdText is going to be initial synced 
[INFO ] 2025-03-11 10:56:07.218 - [Test5][Sybase] - Table tdText has been completed batch read 
[INFO ] 2025-03-11 10:56:07.419 - [Test5][Sybase] - Starting batch read from table: pg_yyy_bmsql_order_line 
[TRACE] 2025-03-11 10:56:07.422 - [Test5][Sybase] - Table pg_yyy_bmsql_order_line is going to be initial synced 
[INFO ] 2025-03-11 10:57:27.518 - [Test5][Sybase] - Table pg_yyy_bmsql_order_line has been completed batch read 
[INFO ] 2025-03-11 10:57:27.519 - [Test5][Sybase] - Starting batch read from table: pg_xxx_bmsql_item 
[TRACE] 2025-03-11 10:57:27.519 - [Test5][Sybase] - Table pg_xxx_bmsql_item is going to be initial synced 
[INFO ] 2025-03-11 10:57:32.777 - [Test5][Sybase] - Table pg_xxx_bmsql_item has been completed batch read 
[INFO ] 2025-03-11 10:57:32.778 - [Test5][Sybase] - Starting batch read from table: nouniquepdktableTarget 
[TRACE] 2025-03-11 10:57:32.983 - [Test5][Sybase] - Table nouniquepdktableTarget is going to be initial synced 
[INFO ] 2025-03-11 10:57:33.038 - [Test5][Sybase] - Table nouniquepdktableTarget has been completed batch read 
[INFO ] 2025-03-11 10:57:33.038 - [Test5][Sybase] - Starting batch read from table: testBit 
[TRACE] 2025-03-11 10:57:33.038 - [Test5][Sybase] - Table testBit is going to be initial synced 
[INFO ] 2025-03-11 10:57:33.306 - [Test5][Sybase] - Table testBit has been completed batch read 
[INFO ] 2025-03-11 10:57:33.308 - [Test5][Sybase] - Starting batch read from table: BMSQL_OORDER_BACK2 
[TRACE] 2025-03-11 10:57:33.308 - [Test5][Sybase] - Table BMSQL_OORDER_BACK2 is going to be initial synced 
[INFO ] 2025-03-11 10:57:33.660 - [Test5][Sybase] - Table BMSQL_OORDER_BACK2 has been completed batch read 
[INFO ] 2025-03-11 10:57:33.660 - [Test5][Sybase] - Starting batch read from table: BMSQL_OORDER_BACK 
[TRACE] 2025-03-11 10:57:33.660 - [Test5][Sybase] - Table BMSQL_OORDER_BACK is going to be initial synced 
[INFO ] 2025-03-11 10:57:33.905 - [Test5][Sybase] - Table BMSQL_OORDER_BACK has been completed batch read 
[INFO ] 2025-03-11 10:57:33.907 - [Test5][Sybase] - Starting batch read from table: pg_yyy_bmsql_customer 
[TRACE] 2025-03-11 10:57:33.907 - [Test5][Sybase] - Table pg_yyy_bmsql_customer is going to be initial synced 
[INFO ] 2025-03-11 10:57:40.769 - [Test5][Sybase] - Table pg_yyy_bmsql_customer has been completed batch read 
[INFO ] 2025-03-11 10:57:40.771 - [Test5][Sybase] - Starting batch read from table: pg_bmsql_oorder 
[TRACE] 2025-03-11 10:57:40.771 - [Test5][Sybase] - Table pg_bmsql_oorder is going to be initial synced 
[INFO ] 2025-03-11 10:58:07.860 - [Test5][Sybase] - Table pg_bmsql_oorder has been completed batch read 
[INFO ] 2025-03-11 10:58:07.862 - [Test5][Sybase] - Starting batch read from table: test_td_null_col 
[TRACE] 2025-03-11 10:58:07.862 - [Test5][Sybase] - Table test_td_null_col is going to be initial synced 
[INFO ] 2025-03-11 10:58:08.298 - [Test5][Sybase] - Table test_td_null_col has been completed batch read 
[INFO ] 2025-03-11 10:58:08.299 - [Test5][Sybase] - Starting batch read from table: BMSQL_HISTORY 
[TRACE] 2025-03-11 10:58:08.299 - [Test5][Sybase] - Table BMSQL_HISTORY is going to be initial synced 
[INFO ] 2025-03-11 10:58:14.528 - [Test5][Sybase] - Table BMSQL_HISTORY has been completed batch read 
[INFO ] 2025-03-11 10:58:14.530 - [Test5][Sybase] - Starting batch read from table: td_timestamp_col 
[TRACE] 2025-03-11 10:58:14.530 - [Test5][Sybase] - Table td_timestamp_col is going to be initial synced 
[INFO ] 2025-03-11 10:58:14.773 - [Test5][Sybase] - Table td_timestamp_col has been completed batch read 
[INFO ] 2025-03-11 10:58:14.774 - [Test5][Sybase] - Starting batch read from table: ap_standard_list_alias_OLD 
[TRACE] 2025-03-11 10:58:14.774 - [Test5][Sybase] - Table ap_standard_list_alias_OLD is going to be initial synced 
[INFO ] 2025-03-11 10:58:14.936 - [Test5][Sybase] - Table ap_standard_list_alias_OLD has been completed batch read 
[INFO ] 2025-03-11 10:58:14.936 - [Test5][Sybase] - Starting batch read from table: pg_bmsql_order_line 
[TRACE] 2025-03-11 10:58:14.936 - [Test5][Sybase] - Table pg_bmsql_order_line is going to be initial synced 
[INFO ] 2025-03-11 10:59:23.920 - [Test5][Sybase] - Table pg_bmsql_order_line has been completed batch read 
[INFO ] 2025-03-11 10:59:23.926 - [Test5][Sybase] - Starting batch read from table: testTimeStampTwoWithIndex 
[TRACE] 2025-03-11 10:59:23.926 - [Test5][Sybase] - Table testTimeStampTwoWithIndex is going to be initial synced 
[INFO ] 2025-03-11 10:59:24.066 - [Test5][Sybase] - Table testTimeStampTwoWithIndex has been completed batch read 
[INFO ] 2025-03-11 10:59:24.066 - [Test5][Sybase] - Starting batch read from table: BMSQL_DISTRICT 
[TRACE] 2025-03-11 10:59:24.067 - [Test5][Sybase] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2025-03-11 10:59:24.173 - [Test5][Sybase] - Table BMSQL_DISTRICT has been completed batch read 
[INFO ] 2025-03-11 10:59:24.173 - [Test5][Sybase] - Starting batch read from table: pg_xxx_bmsql_config 
[TRACE] 2025-03-11 10:59:24.173 - [Test5][Sybase] - Table pg_xxx_bmsql_config is going to be initial synced 
[INFO ] 2025-03-11 10:59:24.375 - [Test5][Sybase] - Table pg_xxx_bmsql_config has been completed batch read 
[INFO ] 2025-03-11 10:59:24.376 - [Test5][Sybase] - Starting batch read from table: BMSQL_CONFIG 
[TRACE] 2025-03-11 10:59:24.491 - [Test5][Sybase] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2025-03-11 10:59:24.492 - [Test5][Sybase] - Table BMSQL_CONFIG has been completed batch read 
[INFO ] 2025-03-11 10:59:24.493 - [Test5][Sybase] - Starting batch read from table: bmsql_history 
[TRACE] 2025-03-11 10:59:24.493 - [Test5][Sybase] - Table bmsql_history is going to be initial synced 
[INFO ] 2025-03-11 10:59:34.888 - [Test5][Sybase] - Table bmsql_history has been completed batch read 
[INFO ] 2025-03-11 10:59:34.888 - [Test5][Sybase] - Starting batch read from table: pg_bmsql_stock 
[TRACE] 2025-03-11 10:59:34.888 - [Test5][Sybase] - Table pg_bmsql_stock is going to be initial synced 
[INFO ] 2025-03-11 10:59:37.945 - [Test5][Sybase] - Table pg_bmsql_stock has been completed batch read 
[INFO ] 2025-03-11 10:59:37.947 - [Test5][Sybase] - Starting batch read from table: a_testMoney 
[TRACE] 2025-03-11 10:59:37.948 - [Test5][Sybase] - Table a_testMoney is going to be initial synced 
[INFO ] 2025-03-11 10:59:38.112 - [Test5][Sybase] - Table a_testMoney has been completed batch read 
[INFO ] 2025-03-11 10:59:38.112 - [Test5][Sybase] - Starting batch read from table: pg_xxx_sync_test 
[TRACE] 2025-03-11 10:59:38.113 - [Test5][Sybase] - Table pg_xxx_sync_test is going to be initial synced 
[INFO ] 2025-03-11 10:59:38.226 - [Test5][Sybase] - Table pg_xxx_sync_test has been completed batch read 
[INFO ] 2025-03-11 10:59:38.227 - [Test5][Sybase] - Starting batch read from table: pg_xxx_bmsql_warehouse 
[TRACE] 2025-03-11 10:59:38.227 - [Test5][Sybase] - Table pg_xxx_bmsql_warehouse is going to be initial synced 
[INFO ] 2025-03-11 10:59:38.364 - [Test5][Sybase] - Table pg_xxx_bmsql_warehouse has been completed batch read 
[INFO ] 2025-03-11 10:59:38.365 - [Test5][Sybase] - Starting batch read from table: pg_bmsql_new_order 
[TRACE] 2025-03-11 10:59:38.365 - [Test5][Sybase] - Table pg_bmsql_new_order is going to be initial synced 
[INFO ] 2025-03-11 10:59:40.426 - [Test5][Sybase] - Table pg_bmsql_new_order has been completed batch read 
[INFO ] 2025-03-11 10:59:40.427 - [Test5][Sybase] - Starting batch read from table: pg_xxx_bmsql_history 
[TRACE] 2025-03-11 10:59:40.427 - [Test5][Sybase] - Table pg_xxx_bmsql_history is going to be initial synced 
[INFO ] 2025-03-11 10:59:44.915 - [Test5][Sybase] - Table pg_xxx_bmsql_history has been completed batch read 
[INFO ] 2025-03-11 10:59:44.916 - [Test5][Sybase] - Starting batch read from table: pg_yyy_bmsql_config 
[TRACE] 2025-03-11 10:59:44.916 - [Test5][Sybase] - Table pg_yyy_bmsql_config is going to be initial synced 
[INFO ] 2025-03-11 10:59:45.034 - [Test5][Sybase] - Table pg_yyy_bmsql_config has been completed batch read 
[INFO ] 2025-03-11 10:59:45.035 - [Test5][Sybase] - Starting batch read from table: BMSQL_NEW_ORDER 
[TRACE] 2025-03-11 10:59:45.035 - [Test5][Sybase] - Table BMSQL_NEW_ORDER is going to be initial synced 
[INFO ] 2025-03-11 10:59:46.048 - [Test5][Sybase] - Table BMSQL_NEW_ORDER has been completed batch read 
[INFO ] 2025-03-11 10:59:46.054 - [Test5][Sybase] - Starting batch read from table: bmsql_new_order 
[TRACE] 2025-03-11 10:59:46.055 - [Test5][Sybase] - Table bmsql_new_order is going to be initial synced 
[INFO ] 2025-03-11 10:59:47.381 - [Test5][Sybase] - Table bmsql_new_order has been completed batch read 
[INFO ] 2025-03-11 10:59:47.381 - [Test5][Sybase] - Starting batch read from table: pg_yyy_bmsql_district 
[TRACE] 2025-03-11 10:59:47.381 - [Test5][Sybase] - Table pg_yyy_bmsql_district is going to be initial synced 
[INFO ] 2025-03-11 10:59:47.488 - [Test5][Sybase] - Table pg_yyy_bmsql_district has been completed batch read 
[INFO ] 2025-03-11 10:59:47.488 - [Test5][Sybase] - Starting batch read from table: pg_bmsql_warehouse 
[TRACE] 2025-03-11 10:59:47.489 - [Test5][Sybase] - Table pg_bmsql_warehouse is going to be initial synced 
[INFO ] 2025-03-11 10:59:47.574 - [Test5][Sybase] - Table pg_bmsql_warehouse has been completed batch read 
[INFO ] 2025-03-11 10:59:47.574 - [Test5][Sybase] - Starting batch read from table: message_code 
[TRACE] 2025-03-11 10:59:47.574 - [Test5][Sybase] - Table message_code is going to be initial synced 
[INFO ] 2025-03-11 10:59:47.683 - [Test5][Sybase] - Table message_code has been completed batch read 
[INFO ] 2025-03-11 10:59:47.684 - [Test5][Sybase] - Starting batch read from table: bmsql_warehouse 
[TRACE] 2025-03-11 10:59:47.684 - [Test5][Sybase] - Table bmsql_warehouse is going to be initial synced 
[INFO ] 2025-03-11 10:59:47.868 - [Test5][Sybase] - Table bmsql_warehouse has been completed batch read 
[INFO ] 2025-03-11 10:59:47.869 - [Test5][Sybase] - Starting batch read from table: pg_xxx_bmsql_new_order 
[TRACE] 2025-03-11 10:59:47.869 - [Test5][Sybase] - Table pg_xxx_bmsql_new_order is going to be initial synced 
[INFO ] 2025-03-11 10:59:49.627 - [Test5][Sybase] - Table pg_xxx_bmsql_new_order has been completed batch read 
[INFO ] 2025-03-11 10:59:49.628 - [Test5][Sybase] - Starting batch read from table: nouniquepdktable 
[TRACE] 2025-03-11 10:59:49.628 - [Test5][Sybase] - Table nouniquepdktable is going to be initial synced 
[INFO ] 2025-03-11 10:59:49.779 - [Test5][Sybase] - Table nouniquepdktable has been completed batch read 
[INFO ] 2025-03-11 10:59:49.780 - [Test5][Sybase] - Starting batch read from table: t1_example 
[TRACE] 2025-03-11 10:59:49.780 - [Test5][Sybase] - Table t1_example is going to be initial synced 
[INFO ] 2025-03-11 10:59:49.923 - [Test5][Sybase] - Table t1_example has been completed batch read 
[INFO ] 2025-03-11 10:59:49.924 - [Test5][Sybase] - Starting batch read from table: testIdenSmallInt 
[TRACE] 2025-03-11 10:59:49.924 - [Test5][Sybase] - Table testIdenSmallInt is going to be initial synced 
[INFO ] 2025-03-11 10:59:50.043 - [Test5][Sybase] - Table testIdenSmallInt has been completed batch read 
[INFO ] 2025-03-11 10:59:50.045 - [Test5][Sybase] - Starting batch read from table: pg_xxx_bmsql_district 
[TRACE] 2025-03-11 10:59:50.045 - [Test5][Sybase] - Table pg_xxx_bmsql_district is going to be initial synced 
[INFO ] 2025-03-11 10:59:50.152 - [Test5][Sybase] - Table pg_xxx_bmsql_district has been completed batch read 
[INFO ] 2025-03-11 10:59:50.153 - [Test5][Sybase] - Starting batch read from table: pg_bmsql_district 
[TRACE] 2025-03-11 10:59:50.153 - [Test5][Sybase] - Table pg_bmsql_district is going to be initial synced 
[INFO ] 2025-03-11 10:59:50.250 - [Test5][Sybase] - Table pg_bmsql_district has been completed batch read 
[INFO ] 2025-03-11 10:59:50.250 - [Test5][Sybase] - Starting batch read from table: bmsql_item 
[TRACE] 2025-03-11 10:59:50.251 - [Test5][Sybase] - Table bmsql_item is going to be initial synced 
[INFO ] 2025-03-11 10:59:52.336 - [Test5][Sybase] - Table bmsql_item has been completed batch read 
[INFO ] 2025-03-11 10:59:52.336 - [Test5][Sybase] - Starting batch read from table: BMSQL_STOCK 
[TRACE] 2025-03-11 10:59:52.336 - [Test5][Sybase] - Table BMSQL_STOCK is going to be initial synced 
[INFO ] 2025-03-11 11:00:26.745 - [Test5][Sybase] - Table BMSQL_STOCK has been completed batch read 
[INFO ] 2025-03-11 11:00:26.745 - [Test5][Sybase] - Starting batch read from table: ap_standard_list_subset 
[TRACE] 2025-03-11 11:00:26.745 - [Test5][Sybase] - Table ap_standard_list_subset is going to be initial synced 
[INFO ] 2025-03-11 11:00:26.923 - [Test5][Sybase] - Table ap_standard_list_subset has been completed batch read 
[INFO ] 2025-03-11 11:00:26.923 - [Test5][Sybase] - Starting batch read from table: testChar 
[TRACE] 2025-03-11 11:00:26.923 - [Test5][Sybase] - Table testChar is going to be initial synced 
[INFO ] 2025-03-11 11:00:27.143 - [Test5][Sybase] - Table testChar has been completed batch read 
[INFO ] 2025-03-11 11:00:27.144 - [Test5][Sybase] - Starting batch read from table: pg_xxx_bmsql_order_line 
[TRACE] 2025-03-11 11:00:27.144 - [Test5][Sybase] - Table pg_xxx_bmsql_order_line is going to be initial synced 
[INFO ] 2025-03-11 11:01:08.100 - [Test5][Sybase] - Table pg_xxx_bmsql_order_line has been completed batch read 
[INFO ] 2025-03-11 11:01:08.101 - [Test5][Sybase] - Starting batch read from table: pg_yyy_bmsql_warehouse 
[TRACE] 2025-03-11 11:01:08.101 - [Test5][Sybase] - Table pg_yyy_bmsql_warehouse is going to be initial synced 
[INFO ] 2025-03-11 11:01:08.331 - [Test5][Sybase] - Table pg_yyy_bmsql_warehouse has been completed batch read 
[INFO ] 2025-03-11 11:01:08.337 - [Test5][Sybase] - Starting batch read from table: BMSQL_CUSTOMER 
[TRACE] 2025-03-11 11:01:08.337 - [Test5][Sybase] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2025-03-11 11:01:12.480 - [Test5][Sybase] - Table BMSQL_CUSTOMER has been completed batch read 
[INFO ] 2025-03-11 11:01:12.480 - [Test5][Sybase] - Starting batch read from table: testTimeStamp2 
[TRACE] 2025-03-11 11:01:12.481 - [Test5][Sybase] - Table testTimeStamp2 is going to be initial synced 
[INFO ] 2025-03-11 11:01:12.634 - [Test5][Sybase] - Table testTimeStamp2 has been completed batch read 
[INFO ] 2025-03-11 11:01:12.635 - [Test5][Sybase] - Starting batch read from table: bmsql_district 
[TRACE] 2025-03-11 11:01:12.635 - [Test5][Sybase] - Table bmsql_district is going to be initial synced 
[INFO ] 2025-03-11 11:01:12.815 - [Test5][Sybase] - Table bmsql_district has been completed batch read 
[INFO ] 2025-03-11 11:01:12.816 - [Test5][Sybase] - Starting batch read from table: ap_operation_list 
[TRACE] 2025-03-11 11:01:12.816 - [Test5][Sybase] - Table ap_operation_list is going to be initial synced 
[INFO ] 2025-03-11 11:01:12.980 - [Test5][Sybase] - Table ap_operation_list has been completed batch read 
[INFO ] 2025-03-11 11:01:12.980 - [Test5][Sybase] - Starting batch read from table: td_null_col 
[TRACE] 2025-03-11 11:01:12.980 - [Test5][Sybase] - Table td_null_col is going to be initial synced 
[INFO ] 2025-03-11 11:01:13.231 - [Test5][Sybase] - Table td_null_col has been completed batch read 
[INFO ] 2025-03-11 11:01:13.233 - [Test5][Sybase] - Starting batch read from table: a_td_empty_string 
[TRACE] 2025-03-11 11:01:13.233 - [Test5][Sybase] - Table a_td_empty_string is going to be initial synced 
[INFO ] 2025-03-11 11:01:13.423 - [Test5][Sybase] - Table a_td_empty_string has been completed batch read 
[INFO ] 2025-03-11 11:01:13.424 - [Test5][Sybase] - Starting batch read from table: td_test_varchar 
[TRACE] 2025-03-11 11:01:13.424 - [Test5][Sybase] - Table td_test_varchar is going to be initial synced 
[INFO ] 2025-03-11 11:01:13.626 - [Test5][Sybase] - Table td_test_varchar has been completed batch read 
[INFO ] 2025-03-11 11:01:13.626 - [Test5][Sybase] - Starting batch read from table: test_td_timestamp_col 
[TRACE] 2025-03-11 11:01:13.627 - [Test5][Sybase] - Table test_td_timestamp_col is going to be initial synced 
[INFO ] 2025-03-11 11:01:13.802 - [Test5][Sybase] - Table test_td_timestamp_col has been completed batch read 
[INFO ] 2025-03-11 11:01:13.804 - [Test5][Sybase] - Starting batch read from table: pg_bmsql_history 
[TRACE] 2025-03-11 11:01:13.804 - [Test5][Sybase] - Table pg_bmsql_history is going to be initial synced 
[INFO ] 2025-03-11 11:01:31.542 - [Test5][Sybase] - Table pg_bmsql_history has been completed batch read 
[INFO ] 2025-03-11 11:01:31.545 - [Test5][Sybase] - Starting batch read from table: pg_w_td_fact_sec 
[TRACE] 2025-03-11 11:01:31.545 - [Test5][Sybase] - Table pg_w_td_fact_sec is going to be initial synced 
[INFO ] 2025-03-11 11:01:31.714 - [Test5][Sybase] - Table pg_w_td_fact_sec has been completed batch read 
[INFO ] 2025-03-11 11:01:31.714 - [Test5][Sybase] - Starting batch read from table: pg_bmsql_config 
[TRACE] 2025-03-11 11:01:31.714 - [Test5][Sybase] - Table pg_bmsql_config is going to be initial synced 
[INFO ] 2025-03-11 11:01:31.928 - [Test5][Sybase] - Table pg_bmsql_config has been completed batch read 
[INFO ] 2025-03-11 11:01:31.929 - [Test5][Sybase] - Starting batch read from table: td_char0 
[TRACE] 2025-03-11 11:01:31.929 - [Test5][Sybase] - Table td_char0 is going to be initial synced 
[INFO ] 2025-03-11 11:01:32.153 - [Test5][Sybase] - Table td_char0 has been completed batch read 
[INFO ] 2025-03-11 11:01:32.154 - [Test5][Sybase] - Starting batch read from table: BMSQL_WAREHOUSE 
[TRACE] 2025-03-11 11:01:32.154 - [Test5][Sybase] - Table BMSQL_WAREHOUSE is going to be initial synced 
[INFO ] 2025-03-11 11:01:32.366 - [Test5][Sybase] - Table BMSQL_WAREHOUSE has been completed batch read 
[INFO ] 2025-03-11 11:01:32.367 - [Test5][Sybase] - Starting batch read from table: s2p_text_idt 
[TRACE] 2025-03-11 11:01:32.367 - [Test5][Sybase] - Table s2p_text_idt is going to be initial synced 
[INFO ] 2025-03-11 11:01:32.521 - [Test5][Sybase] - Table s2p_text_idt has been completed batch read 
[INFO ] 2025-03-11 11:01:32.521 - [Test5][Sybase] - Starting batch read from table: bmsql_stock 
[TRACE] 2025-03-11 11:01:32.521 - [Test5][Sybase] - Table bmsql_stock is going to be initial synced 
[INFO ] 2025-03-11 11:01:36.536 - [Test5][Sybase] - Table bmsql_stock has been completed batch read 
[INFO ] 2025-03-11 11:01:36.537 - [Test5][Sybase] - Starting batch read from table: s2p_datetime_idt 
[TRACE] 2025-03-11 11:01:36.537 - [Test5][Sybase] - Table s2p_datetime_idt is going to be initial synced 
[INFO ] 2025-03-11 11:01:36.679 - [Test5][Sybase] - Table s2p_datetime_idt has been completed batch read 
[INFO ] 2025-03-11 11:01:36.680 - [Test5][Sybase] - Starting batch read from table: sync_test 
[TRACE] 2025-03-11 11:01:36.680 - [Test5][Sybase] - Table sync_test is going to be initial synced 
[INFO ] 2025-03-11 11:01:36.798 - [Test5][Sybase] - Table sync_test has been completed batch read 
[INFO ] 2025-03-11 11:01:36.799 - [Test5][Sybase] - Starting batch read from table: s2p_text 
[TRACE] 2025-03-11 11:01:36.799 - [Test5][Sybase] - Table s2p_text is going to be initial synced 
[INFO ] 2025-03-11 11:01:36.919 - [Test5][Sybase] - Table s2p_text has been completed batch read 
[INFO ] 2025-03-11 11:01:36.919 - [Test5][Sybase] - Starting batch read from table: pg_yyy_bmsql_oorder 
[TRACE] 2025-03-11 11:01:36.920 - [Test5][Sybase] - Table pg_yyy_bmsql_oorder is going to be initial synced 
[INFO ] 2025-03-11 11:01:44.508 - [Test5][Sybase] - Table pg_yyy_bmsql_oorder has been completed batch read 
[INFO ] 2025-03-11 11:01:44.509 - [Test5][Sybase] - Starting batch read from table: pg_yyy_bmsql_item 
[TRACE] 2025-03-11 11:01:44.509 - [Test5][Sybase] - Table pg_yyy_bmsql_item is going to be initial synced 
[INFO ] 2025-03-11 11:01:47.224 - [Test5][Sybase] - Table pg_yyy_bmsql_item has been completed batch read 
[INFO ] 2025-03-11 11:01:47.224 - [Test5][Sybase] - Starting batch read from table: pg_xxx_bmsql_stock 
[TRACE] 2025-03-11 11:01:47.224 - [Test5][Sybase] - Table pg_xxx_bmsql_stock is going to be initial synced 
[INFO ] 2025-03-11 11:01:49.524 - [Test5][Sybase] - Table pg_xxx_bmsql_stock has been completed batch read 
[INFO ] 2025-03-11 11:01:49.527 - [Test5][Sybase] - Starting batch read from table: pg_xxx_bmsql_oorder 
[TRACE] 2025-03-11 11:01:49.527 - [Test5][Sybase] - Table pg_xxx_bmsql_oorder is going to be initial synced 
[INFO ] 2025-03-11 11:01:53.886 - [Test5][Sybase] - Table pg_xxx_bmsql_oorder has been completed batch read 
[INFO ] 2025-03-11 11:01:53.886 - [Test5][Sybase] - Starting batch read from table: BMSQL_OORDER 
[TRACE] 2025-03-11 11:01:53.886 - [Test5][Sybase] - Table BMSQL_OORDER is going to be initial synced 
[INFO ] 2025-03-11 11:01:56.553 - [Test5][Sybase] - Table BMSQL_OORDER has been completed batch read 
[INFO ] 2025-03-11 11:01:56.554 - [Test5][Sybase] - Starting batch read from table: testTimeStampWithNoIndex 
[TRACE] 2025-03-11 11:01:56.555 - [Test5][Sybase] - Table testTimeStampWithNoIndex is going to be initial synced 
[INFO ] 2025-03-11 11:01:56.719 - [Test5][Sybase] - Table testTimeStampWithNoIndex has been completed batch read 
[INFO ] 2025-03-11 11:01:56.720 - [Test5][Sybase] - Starting batch read from table: test1_testBit 
[TRACE] 2025-03-11 11:01:56.720 - [Test5][Sybase] - Table test1_testBit is going to be initial synced 
[INFO ] 2025-03-11 11:01:56.842 - [Test5][Sybase] - Table test1_testBit has been completed batch read 
[INFO ] 2025-03-11 11:01:56.843 - [Test5][Sybase] - Starting batch read from table: bmsql_oorder 
[TRACE] 2025-03-11 11:01:56.843 - [Test5][Sybase] - Table bmsql_oorder is going to be initial synced 
[INFO ] 2025-03-11 11:02:09.543 - [Test5][Sybase] - Table bmsql_oorder has been completed batch read 
[INFO ] 2025-03-11 11:02:09.544 - [Test5][Sybase] - Starting batch read from table: testMoney 
[TRACE] 2025-03-11 11:02:09.544 - [Test5][Sybase] - Table testMoney is going to be initial synced 
[INFO ] 2025-03-11 11:02:09.738 - [Test5][Sybase] - Table testMoney has been completed batch read 
[INFO ] 2025-03-11 11:02:09.739 - [Test5][Sybase] - Starting batch read from table: test1_td_null_col 
[TRACE] 2025-03-11 11:02:09.739 - [Test5][Sybase] - Table test1_td_null_col is going to be initial synced 
[INFO ] 2025-03-11 11:02:09.922 - [Test5][Sybase] - Table test1_td_null_col has been completed batch read 
[INFO ] 2025-03-11 11:02:09.923 - [Test5][Sybase] - Starting batch read from table: testTimeStampTarget 
[TRACE] 2025-03-11 11:02:09.923 - [Test5][Sybase] - Table testTimeStampTarget is going to be initial synced 
[INFO ] 2025-03-11 11:02:10.074 - [Test5][Sybase] - Table testTimeStampTarget has been completed batch read 
[INFO ] 2025-03-11 11:02:10.074 - [Test5][Sybase] - Starting batch read from table: pg_xxx_bmsql_customer 
[TRACE] 2025-03-11 11:02:10.075 - [Test5][Sybase] - Table pg_xxx_bmsql_customer is going to be initial synced 
[INFO ] 2025-03-11 11:02:12.940 - [Test5][Sybase] - Table pg_xxx_bmsql_customer has been completed batch read 
[INFO ] 2025-03-11 11:02:12.941 - [Test5][Sybase] - Starting batch read from table: testidentity 
[TRACE] 2025-03-11 11:02:12.941 - [Test5][Sybase] - Table testidentity is going to be initial synced 
[INFO ] 2025-03-11 11:02:13.143 - [Test5][Sybase] - Table testidentity has been completed batch read 
[INFO ] 2025-03-11 11:02:13.144 - [Test5][Sybase] - Starting batch read from table: BMSQL_ORDER_LINE 
[TRACE] 2025-03-11 11:02:13.144 - [Test5][Sybase] - Table BMSQL_ORDER_LINE is going to be initial synced 
[INFO ] 2025-03-11 11:02:36.479 - [Test5][Sybase] - Table BMSQL_ORDER_LINE has been completed batch read 
[INFO ] 2025-03-11 11:02:36.481 - [Test5][Sybase] - Starting batch read from table: td_text 
[TRACE] 2025-03-11 11:02:36.482 - [Test5][Sybase] - Table td_text is going to be initial synced 
[INFO ] 2025-03-11 11:02:36.880 - [Test5][Sybase] - Table td_text has been completed batch read 
[INFO ] 2025-03-11 11:02:36.881 - [Test5][Sybase] - Starting batch read from table: testTimeStamp 
[TRACE] 2025-03-11 11:02:36.882 - [Test5][Sybase] - Table testTimeStamp is going to be initial synced 
[INFO ] 2025-03-11 11:02:36.981 - [Test5][Sybase] - Table testTimeStamp has been completed batch read 
[INFO ] 2025-03-11 11:02:36.981 - [Test5][Sybase] - Starting batch read from table: bmsql_order_line 
[TRACE] 2025-03-11 11:02:36.981 - [Test5][Sybase] - Table bmsql_order_line is going to be initial synced 
[INFO ] 2025-03-11 11:03:32.845 - [Test5][Sybase] - Table bmsql_order_line has been completed batch read 
[INFO ] 2025-03-11 11:03:32.845 - [Test5][Sybase] - Starting batch read from table: s2p_datetime_idt_char 
[TRACE] 2025-03-11 11:03:32.845 - [Test5][Sybase] - Table s2p_datetime_idt_char is going to be initial synced 
[INFO ] 2025-03-11 11:03:33.013 - [Test5][Sybase] - Table s2p_datetime_idt_char has been completed batch read 
[TRACE] 2025-03-11 11:03:33.014 - [Test5][Sybase] - Initial sync completed 
[INFO ] 2025-03-11 11:03:33.014 - [Test5][Sybase] - Batch read completed. 
[TRACE] 2025-03-11 11:03:33.034 - [Test5][Sybase] - Incremental sync starting... 
[TRACE] 2025-03-11 11:03:33.034 - [Test5][Sybase] - Initial sync completed 
[TRACE] 2025-03-11 11:03:33.063 - [Test5][Sybase] - Starting stream read, table list: [pg_yyy_bmsql_new_order, bmsql_customer, pg_yyy_bmsql_history, pg_sync_test, bmsql_config, pg_bmsql_item, pg_yyy_bmsql_stock, BMSQL_ITEM, pg_bmsql_customer, a_testMoneyv, tdText, pg_yyy_bmsql_order_line, pg_xxx_bmsql_item, nouniquepdktableTarget, testBit, BMSQL_OORDER_BACK2, BMSQL_OORDER_BACK, pg_yyy_bmsql_customer, pg_bmsql_oorder, test_td_null_col, BMSQL_HISTORY, td_timestamp_col, ap_standard_list_alias_OLD, pg_bmsql_order_line, testTimeStampTwoWithIndex, BMSQL_DISTRICT, pg_xxx_bmsql_config, BMSQL_CONFIG, bmsql_history, pg_bmsql_stock, a_testMoney, pg_xxx_sync_test, pg_xxx_bmsql_warehouse, pg_bmsql_new_order, pg_xxx_bmsql_history, pg_yyy_bmsql_config, BMSQL_NEW_ORDER, bmsql_new_order, pg_yyy_bmsql_district, pg_bmsql_warehouse, message_code, bmsql_warehouse, pg_xxx_bmsql_new_order, nouniquepdktable, t1_example, testIdenSmallInt, pg_xxx_bmsql_district, pg_bmsql_district, bmsql_item, BMSQL_STOCK, ap_standard_list_subset, testChar, pg_xxx_bmsql_order_line, pg_yyy_bmsql_warehouse, BMSQL_CUSTOMER, testTimeStamp2, bmsql_district, ap_operation_list, td_null_col, a_td_empty_string, td_test_varchar, test_td_timestamp_col, pg_bmsql_history, pg_w_td_fact_sec, pg_bmsql_config, td_char0, BMSQL_WAREHOUSE, s2p_text_idt, bmsql_stock, s2p_datetime_idt, sync_test, s2p_text, pg_yyy_bmsql_oorder, pg_yyy_bmsql_item, pg_xxx_bmsql_stock, pg_xxx_bmsql_oorder, BMSQL_OORDER, testTimeStampWithNoIndex, test1_testBit, bmsql_oorder, testMoney, test1_td_null_col, testTimeStampTarget, pg_xxx_bmsql_customer, testidentity, BMSQL_ORDER_LINE, td_text, testTimeStamp, bmsql_order_line, s2p_datetime_idt_char], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-03-11 11:03:33.064 - [Test5][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-03-11 11:03:33.134 - [Test5][Sybase] - startRid: 368419, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-11 11:03:33.134 - [Test5][Sybase] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-11 11:03:33.134 - [Test5][Sybase] - sybase offset in database is: startRid: 368419, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-03-11 11:03:33.134 - [Test5][Sybase] - we will use offset in database, how ever, this is safe: startRid: 368419, rowId: 0, h: 0, l: 0 
[INFO ] 2025-03-11 11:03:33.191 - [Test5][Sybase] - sybase cdc work with mode v1: auto rescan 
[INFO ] 2025-03-11 11:03:33.377 - [Test5][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-03-11 11:03:33.581 - [Test5][Sybase] - turned off automatic log cleaning for sybase 
[TRACE] 2025-03-11 11:03:34.187 - [Test5][Test5] - Process after table "pg_yyy_bmsql_stock" initial sync finished, cost: 24 ms 
[TRACE] 2025-03-11 11:03:34.188 - [Test5][Test5] - Process after table "pg_bmsql_item" initial sync finished, cost: 23 ms 
[TRACE] 2025-03-11 11:03:34.188 - [Test5][Test5] - Process after table "tdText" initial sync finished, cost: 7 ms 
[TRACE] 2025-03-11 11:03:34.188 - [Test5][Test5] - Process after table "pg_bmsql_customer" initial sync finished, cost: 8 ms 
[TRACE] 2025-03-11 11:03:34.188 - [Test5][Test5] - Process after table "pg_yyy_bmsql_history" initial sync finished, cost: 22 ms 
[TRACE] 2025-03-11 11:03:34.188 - [Test5][Test5] - Process after table "BMSQL_ITEM" initial sync finished, cost: 31 ms 
[TRACE] 2025-03-11 11:03:34.188 - [Test5][Test5] - Process after table "bmsql_config" initial sync finished, cost: 64 ms 
[TRACE] 2025-03-11 11:03:34.188 - [Test5][Test5] - Process after table "pg_yyy_bmsql_order_line" initial sync finished, cost: 6 ms 
[TRACE] 2025-03-11 11:03:34.188 - [Test5][Test5] - Process after table "pg_xxx_bmsql_item" initial sync finished, cost: 2 ms 
[TRACE] 2025-03-11 11:03:34.188 - [Test5][Test5] - Process after table "BMSQL_OORDER_BACK2" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.188 - [Test5][Test5] - Process after table "pg_yyy_bmsql_customer" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.189 - [Test5][Test5] - Process after table "BMSQL_OORDER_BACK" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.189 - [Test5][Test5] - Process after table "testBit" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.189 - [Test5][Test5] - Process after table "bmsql_customer" initial sync finished, cost: 67 ms 
[TRACE] 2025-03-11 11:03:34.189 - [Test5][Test5] - Process after table "test_td_null_col" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.189 - [Test5][Test5] - Process after table "pg_bmsql_order_line" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.189 - [Test5][Test5] - Process after table "pg_bmsql_oorder" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.189 - [Test5][Test5] - Process after table "BMSQL_HISTORY" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.189 - [Test5][Test5] - Process after table "testTimeStampTwoWithIndex" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.190 - [Test5][Test5] - Process after table "pg_yyy_bmsql_new_order" initial sync finished, cost: 29 ms 
[TRACE] 2025-03-11 11:03:34.190 - [Test5][Test5] - Process after table "pg_xxx_bmsql_config" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.190 - [Test5][Test5] - Process after table "td_timestamp_col" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.190 - [Test5][Test5] - Process after table "BMSQL_CONFIG" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.190 - [Test5][Test5] - Process after table "BMSQL_DISTRICT" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.190 - [Test5][Test5] - Process after table "bmsql_history" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.190 - [Test5][Test5] - Process after table "pg_bmsql_stock" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.190 - [Test5][Test5] - Process after table "pg_yyy_bmsql_config" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.190 - [Test5][Test5] - Process after table "BMSQL_NEW_ORDER" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.190 - [Test5][Test5] - Process after table "pg_xxx_bmsql_history" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.190 - [Test5][Test5] - Process after table "pg_bmsql_new_order" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.190 - [Test5][Test5] - Process after table "pg_yyy_bmsql_district" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.190 - [Test5][Test5] - Process after table "bmsql_new_order" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.191 - [Test5][Test5] - Process after table "pg_bmsql_warehouse" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.191 - [Test5][Test5] - Process after table "pg_xxx_bmsql_warehouse" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.191 - [Test5][Test5] - Process after table "message_code" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.191 - [Test5][Test5] - Process after table "pg_xxx_bmsql_new_order" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.191 - [Test5][Test5] - Process after table "testIdenSmallInt" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.191 - [Test5][Test5] - Process after table "bmsql_warehouse" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.191 - [Test5][Test5] - Process after table "t1_example" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.191 - [Test5][Test5] - Process after table "pg_bmsql_district" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.191 - [Test5][Test5] - Process after table "BMSQL_STOCK" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.191 - [Test5][Test5] - Process after table "pg_xxx_bmsql_district" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.191 - [Test5][Test5] - Process after table "bmsql_item" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.192 - [Test5][Test5] - Process after table "pg_xxx_bmsql_order_line" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.192 - [Test5][Test5] - Process after table "pg_yyy_bmsql_warehouse" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.192 - [Test5][Test5] - Process after table "testChar" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.192 - [Test5][Test5] - Process after table "BMSQL_CUSTOMER" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.192 - [Test5][Test5] - Process after table "ap_operation_list" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.192 - [Test5][Test5] - Process after table "td_null_col" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.192 - [Test5][Test5] - Process after table "a_td_empty_string" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.226 - [Test5][Test5] - Process after table "pg_sync_test" initial sync finished, cost: 98 ms 
[TRACE] 2025-03-11 11:03:34.226 - [Test5][Test5] - Process after table "test_td_timestamp_col" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.226 - [Test5][Test5] - Process after table "pg_bmsql_history" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.226 - [Test5][Test5] - Process after table "pg_w_td_fact_sec" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.226 - [Test5][Test5] - Process after table "pg_bmsql_config" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.226 - [Test5][Test5] - Process after table "td_char0" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.226 - [Test5][Test5] - Process after table "a_testMoneyv" initial sync finished, cost: 87 ms 
[TRACE] 2025-03-11 11:03:34.226 - [Test5][Test5] - Process after table "s2p_text_idt" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.226 - [Test5][Test5] - Process after table "bmsql_stock" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.226 - [Test5][Test5] - Process after table "s2p_datetime_idt" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.249 - [Test5][Test5] - Process after table "a_testMoney" initial sync finished, cost: 60 ms 
[TRACE] 2025-03-11 11:03:34.249 - [Test5][Test5] - Process after table "s2p_text" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.249 - [Test5][Test5] - Process after table "pg_yyy_bmsql_oorder" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.249 - [Test5][Test5] - Process after table "pg_yyy_bmsql_item" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.250 - [Test5][Test5] - Process after table "pg_xxx_bmsql_stock" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.250 - [Test5][Test5] - Process after table "pg_xxx_bmsql_oorder" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.250 - [Test5][Test5] - Process after table "BMSQL_OORDER" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.269 - [Test5][Test5] - Process after table "pg_xxx_sync_test" initial sync finished, cost: 80 ms 
[TRACE] 2025-03-11 11:03:34.269 - [Test5][Test5] - Process after table "test1_testBit" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.269 - [Test5][Test5] - Process after table "bmsql_oorder" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.269 - [Test5][Test5] - Process after table "testMoney" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.269 - [Test5][Test5] - Process after table "test1_td_null_col" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.269 - [Test5][Test5] - Process after table "testTimeStampTarget" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.269 - [Test5][Test5] - Process after table "pg_xxx_bmsql_customer" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.269 - [Test5][Test5] - Process after table "testidentity" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.270 - [Test5][Test5] - Process after table "BMSQL_ORDER_LINE" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.270 - [Test5][Test5] - Process after table "td_text" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.292 - [Test5][Test5] - Process after table "bmsql_district" initial sync finished, cost: 99 ms 
[TRACE] 2025-03-11 11:03:34.292 - [Test5][Test5] - Process after table "bmsql_order_line" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.292 - [Test5][Test5] - Process after table "s2p_datetime_idt_char" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-11 11:03:34.322 - [Test5][Test5] - Process after table "testTimeStamp2" initial sync finished, cost: 104 ms 
[TRACE] 2025-03-11 11:03:34.322 - [Test5][Test5] - Process after table "BMSQL_WAREHOUSE" initial sync finished, cost: 88 ms 
[TRACE] 2025-03-11 11:03:34.330 - [Test5][Test5] - Process after table "sync_test" initial sync finished, cost: 102 ms 
[TRACE] 2025-03-11 11:03:34.330 - [Test5][Test5] - Process after table "td_test_varchar" initial sync finished, cost: 137 ms 
[TRACE] 2025-03-11 11:03:34.348 - [Test5][Test5] - Process after table "ap_standard_list_alias_OLD" initial sync finished, cost: 153 ms 
[TRACE] 2025-03-11 11:03:34.348 - [Test5][Test5] - Process after table "nouniquepdktableTarget" initial sync finished, cost: 161 ms 
[TRACE] 2025-03-11 11:03:34.368 - [Test5][Test5] - Process after table "nouniquepdktable" initial sync finished, cost: 172 ms 
[TRACE] 2025-03-11 11:03:34.369 - [Test5][Test5] - Process after table "testTimeStamp" initial sync finished, cost: 98 ms 
[TRACE] 2025-03-11 11:03:34.382 - [Test5][Test5] - Process after table "ap_standard_list_subset" initial sync finished, cost: 185 ms 
[TRACE] 2025-03-11 11:03:34.382 - [Test5][Test5] - Process after table "testTimeStampWithNoIndex" initial sync finished, cost: 132 ms 
[INFO ] 2025-03-11 11:03:34.411 - [Test5][Test5] - Process after all table(s) initial sync are finished，table number: 90 
[INFO ] 2025-03-11 11:03:34.411 - [Test5][Sybase] - opened cdc for tables: {dbo=[pg_yyy_bmsql_new_order, bmsql_customer, pg_yyy_bmsql_history, pg_sync_test, bmsql_config, pg_bmsql_item, pg_yyy_bmsql_stock, BMSQL_ITEM, pg_bmsql_customer, a_testMoneyv, tdText, pg_yyy_bmsql_order_line, pg_xxx_bmsql_item, nouniquepdktableTarget, testBit, BMSQL_OORDER_BACK2, BMSQL_OORDER_BACK, pg_yyy_bmsql_customer, pg_bmsql_oorder, test_td_null_col, BMSQL_HISTORY, td_timestamp_col, ap_standard_list_alias_OLD, pg_bmsql_order_line, testTimeStampTwoWithIndex, BMSQL_DISTRICT, pg_xxx_bmsql_config, BMSQL_CONFIG, bmsql_history, pg_bmsql_stock, a_testMoney, pg_xxx_sync_test, pg_xxx_bmsql_warehouse, pg_bmsql_new_order, pg_xxx_bmsql_history, pg_yyy_bmsql_config, BMSQL_NEW_ORDER, bmsql_new_order, pg_yyy_bmsql_district, pg_bmsql_warehouse, message_code, bmsql_warehouse, pg_xxx_bmsql_new_order, nouniquepdktable, t1_example, testIdenSmallInt, pg_xxx_bmsql_district, pg_bmsql_district, bmsql_item, BMSQL_STOCK, ap_standard_list_subset, testChar, pg_xxx_bmsql_order_line, pg_yyy_bmsql_warehouse, BMSQL_CUSTOMER, testTimeStamp2, bmsql_district, ap_operation_list, td_null_col, a_td_empty_string, td_test_varchar, test_td_timestamp_col, pg_bmsql_history, pg_w_td_fact_sec, pg_bmsql_config, td_char0, BMSQL_WAREHOUSE, s2p_text_idt, bmsql_stock, s2p_datetime_idt, sync_test, s2p_text, pg_yyy_bmsql_oorder, pg_yyy_bmsql_item, pg_xxx_bmsql_stock, pg_xxx_bmsql_oorder, BMSQL_OORDER, testTimeStampWithNoIndex, test1_testBit, bmsql_oorder, testMoney, test1_td_null_col, testTimeStampTarget, pg_xxx_bmsql_customer, testidentity, BMSQL_ORDER_LINE, td_text, testTimeStamp, bmsql_order_line, s2p_datetime_idt_char]} 
[INFO ] 2025-03-11 11:03:34.479 - [Test5][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-03-11 11:03:34.482 - [Test5][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-03-11 11:03:34.690 - [Test5][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-03-11 11:03:34.691 - [Test5][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-03-11 11:03:34.691 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 0 
[INFO ] 2025-03-11 11:03:37.695 - [Test5][Sybase] - rebuild statement with 368419, 0 
[INFO ] 2025-03-11 11:03:37.881 - [Test5][Sybase] - uncommit trans size: 0 
[INFO ] 2025-03-11 11:03:37.882 - [Test5][Sybase] - uncommit trans: {} 
[INFO ] 2025-03-11 11:03:37.933 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:03:40.939 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:03:41.347 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:03:44.188 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:03:44.372 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:03:47.376 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:03:47.490 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:03:50.494 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:03:50.685 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:03:53.691 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:03:54.099 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:03:56.907 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:03:57.142 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:04:00.144 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:04:00.270 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:04:03.279 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:04:03.481 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:04:06.532 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:04:06.734 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:04:09.762 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:04:09.762 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:04:12.764 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:04:12.941 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:04:15.946 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:04:16.141 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:04:19.147 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:04:19.300 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:04:22.305 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:04:22.506 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:04:25.591 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:04:25.793 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:04:28.632 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:04:28.839 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:04:31.885 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:04:32.090 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:04:34.930 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:04:35.135 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:04:38.180 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:04:38.286 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:04:41.306 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:04:41.510 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:04:44.559 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:04:44.764 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:04:47.607 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:04:47.755 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:04:50.760 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:04:50.961 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:04:53.967 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:04:54.168 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:04:57.226 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:04:57.431 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:05:00.448 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:05:00.451 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:05:03.452 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:05:03.617 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:05:06.634 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:05:06.837 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:05:09.896 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:05:10.110 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:05:12.950 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:05:13.156 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:05:16.197 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:05:16.403 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:05:19.250 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:05:19.455 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:05:22.496 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:05:22.496 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:05:25.502 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:05:25.652 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:05:28.707 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:05:28.887 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:05:31.893 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:05:32.098 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:05:35.141 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:05:35.345 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:05:38.395 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:05:38.600 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:05:41.595 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:05:41.596 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:05:44.601 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:05:44.748 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:05:47.752 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:05:47.956 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:05:51.014 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:05:51.149 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:05:54.199 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:05:54.404 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:05:57.451 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:05:57.653 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:06:00.493 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:06:00.619 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:06:03.637 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:06:03.841 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:06:06.843 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:06:07.048 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:06:10.097 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:06:10.303 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:06:13.105 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:06:13.279 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:06:16.281 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:06:16.442 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:06:19.447 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:06:19.652 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:06:22.664 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:06:22.665 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:06:25.670 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:06:25.873 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:06:28.921 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:06:29.123 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:06:32.173 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:06:32.405 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:06:35.209 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:06:35.331 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:06:38.333 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:06:38.520 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:06:41.584 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:06:41.786 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:06:44.830 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:06:45.035 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:06:48.073 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:06:48.084 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:06:51.126 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:06:51.523 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:06:54.558 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:06:54.964 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:06:57.788 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:06:58.198 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:07:01.011 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:07:01.269 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:07:04.320 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:07:04.521 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:07:07.575 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:07:07.781 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:07:10.600 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:07:10.779 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:07:13.784 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:07:13.942 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:07:16.944 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:07:17.150 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:07:20.201 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:07:20.307 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:07:23.314 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:07:23.487 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:07:26.493 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:07:26.845 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:07:29.898 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:07:30.104 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:07:33.151 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:07:33.276 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:07:36.326 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:07:36.521 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:07:39.528 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:07:39.934 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:07:42.770 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:07:42.922 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:07:45.926 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:07:46.071 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:07:49.103 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:07:49.307 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:07:52.318 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:07:52.319 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:07:55.324 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:07:55.437 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[INFO ] 2025-03-11 11:07:58.468 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:07:58.669 - [Test5][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368419, rowId: 95 
[TRACE] 2025-03-11 11:08:00.640 - [Test5][Sybase] - Node Sybase[80c4607d-3f4f-4915-b031-4281951fb478] running status set to false 
[INFO ] 2025-03-11 11:08:00.645 - [Test5][Sybase] - rebuild statement with 368419, 95 
[INFO ] 2025-03-11 11:08:00.646 - [Test5][Sybase] - Log Miner is shutting down... 
[TRACE] 2025-03-11 11:08:00.796 - [Test5][Sybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_80c4607d-3f4f-4915-b031-4281951fb478_1741661708613 
[TRACE] 2025-03-11 11:08:00.797 - [Test5][Sybase] - PDK connector node released: HazelcastSourcePdkDataNode_80c4607d-3f4f-4915-b031-4281951fb478_1741661708613 
[TRACE] 2025-03-11 11:08:00.797 - [Test5][Sybase] - Incremental sync completed 
[TRACE] 2025-03-11 11:08:00.797 - [Test5][Sybase] - Node Sybase[80c4607d-3f4f-4915-b031-4281951fb478] schema data cleaned 
[TRACE] 2025-03-11 11:08:00.797 - [Test5][Sybase] - Node Sybase[80c4607d-3f4f-4915-b031-4281951fb478] monitor closed 
[TRACE] 2025-03-11 11:08:00.798 - [Test5][Sybase] - Node Sybase[80c4607d-3f4f-4915-b031-4281951fb478] close complete, cost 161 ms 
[TRACE] 2025-03-11 11:08:00.818 - [Test5][Test5] - Node Test5[6b6ac02b-8595-466d-828b-18dcc9df2ceb] running status set to false 
[TRACE] 2025-03-11 11:08:00.818 - [Test5][Test5] - PDK connector node stopped: HazelcastTargetPdkDataNode_6b6ac02b-8595-466d-828b-18dcc9df2ceb_1741661708802 
[TRACE] 2025-03-11 11:08:00.818 - [Test5][Test5] - PDK connector node released: HazelcastTargetPdkDataNode_6b6ac02b-8595-466d-828b-18dcc9df2ceb_1741661708802 
[TRACE] 2025-03-11 11:08:00.818 - [Test5][Test5] - Node Test5[6b6ac02b-8595-466d-828b-18dcc9df2ceb] schema data cleaned 
[TRACE] 2025-03-11 11:08:00.818 - [Test5][Test5] - Node Test5[6b6ac02b-8595-466d-828b-18dcc9df2ceb] monitor closed 
[TRACE] 2025-03-11 11:08:00.873 - [Test5][Test5] - Node Test5[6b6ac02b-8595-466d-828b-18dcc9df2ceb] close complete, cost 20 ms 
[TRACE] 2025-03-11 11:08:00.873 - [Test5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-11 11:08:00.873 - [Test5] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6bd15b4b 
[TRACE] 2025-03-11 11:08:00.988 - [Test5] - Stop task milestones: 67cfa5b40f0fca3a975c8eed(Test5)  
[TRACE] 2025-03-11 11:08:01.000 - [Test5] - Stopped task aspect(s) 
[TRACE] 2025-03-11 11:08:01.000 - [Test5] - Snapshot order controller have been removed 
[INFO ] 2025-03-11 11:08:01.000 - [Test5] - Task stopped. 
[TRACE] 2025-03-11 11:08:01.019 - [Test5] - Remove memory task client succeed, task: Test5[67cfa5b40f0fca3a975c8eed] 
[TRACE] 2025-03-11 11:08:01.022 - [Test5] - Destroy memory task client cache succeed, task: Test5[67cfa5b40f0fca3a975c8eed] 
