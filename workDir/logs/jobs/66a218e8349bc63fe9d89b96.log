[INFO ] 2024-07-25 17:21:10.336 - [任务 32] - Task initialization... 
[INFO ] 2024-07-25 17:21:10.337 - [任务 32] - Start task milestones: 66a218e8349bc63fe9d89b96(任务 32) 
[INFO ] 2024-07-25 17:21:10.482 - [任务 32] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-25 17:21:10.483 - [任务 32] - The engine receives 任务 32 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 17:21:10.549 - [任务 32][Mysql3307] - Node Mysql3307[4d6e5acd-3015-4191-9999-6980ea2d0861] start preload schema,table counts: 1 
[INFO ] 2024-07-25 17:21:10.550 - [任务 32][Mysql] - Node <PERSON>s<PERSON>l[54b3c97e-27d3-42f2-b483-1fafa916aefa] start preload schema,table counts: 1 
[INFO ] 2024-07-25 17:21:10.550 - [任务 32][Mysql] - <PERSON>de <PERSON>[54b3c97e-27d3-42f2-b483-1fafa916aefa] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 17:21:10.550 - [任务 32][Mysql3307] - Node Mysql3307[4d6e5acd-3015-4191-9999-6980ea2d0861] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 17:21:11.262 - [任务 32][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-25 17:21:11.262 - [任务 32][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 17:21:11.400 - [任务 32][Mysql] - Source node "Mysql" read batch size: 100 
[INFO ] 2024-07-25 17:21:11.400 - [任务 32][Mysql] - Source node "Mysql" event queue capacity: 200 
[INFO ] 2024-07-25 17:21:11.400 - [任务 32][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 17:21:11.402 - [任务 32][Mysql] - batch offset found: {},stream offset found: {"filename":"binlog.000033","position":59974958,"gtidSet":""} 
[INFO ] 2024-07-25 17:21:11.402 - [任务 32][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-25 17:21:11.443 - [任务 32][Mysql] - Initial sync started 
[INFO ] 2024-07-25 17:21:11.449 - [任务 32][Mysql] - Starting batch read, table name: testAutoInspect, offset: null 
[INFO ] 2024-07-25 17:21:11.450 - [任务 32][Mysql] - Table testAutoInspect is going to be initial synced 
[INFO ] 2024-07-25 17:21:11.461 - [任务 32][Mysql] - Table [testAutoInspect] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 17:21:11.461 - [任务 32][Mysql] - Query table 'testAutoInspect' counts: 4 
[INFO ] 2024-07-25 17:21:11.461 - [任务 32][Mysql] - Initial sync completed 
[INFO ] 2024-07-25 17:21:11.461 - [任务 32][Mysql] - Incremental sync starting... 
[INFO ] 2024-07-25 17:21:11.462 - [任务 32][Mysql] - Initial sync completed 
[INFO ] 2024-07-25 17:21:11.476 - [任务 32][Mysql] - Starting stream read, table list: [testAutoInspect, _tapdata_heartbeat_table], offset: {"filename":"binlog.000033","position":59974958,"gtidSet":""} 
[INFO ] 2024-07-25 17:21:11.478 - [任务 32][Mysql] - Starting mysql cdc, server name: 801fa5db-620f-4ce2-bedf-e426fd07df7a 
[INFO ] 2024-07-25 17:21:11.516 - [任务 32][Mysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 204201250
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 801fa5db-620f-4ce2-bedf-e426fd07df7a
  database.port: 3306
  threadName: Debezium-Mysql-Connector-801fa5db-620f-4ce2-bedf-e426fd07df7a
  database.hostname: localhost
  database.password: ********
  name: 801fa5db-620f-4ce2-bedf-e426fd07df7a
  pdk.offset.string: {"name":"801fa5db-620f-4ce2-bedf-e426fd07df7a","offset":{"{\"server\":\"801fa5db-620f-4ce2-bedf-e426fd07df7a\"}":"{\"file\":\"binlog.000033\",\"pos\":59974958,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.testAutoInspect,test._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-25 17:21:11.516 - [任务 32][Mysql] - Connector Mysql incremental start succeed, tables: [testAutoInspect, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-25 17:47:11.560 - [任务 32][Mysql] - Mysql binlog reader stopped 
[INFO ] 2024-07-25 17:47:11.560 - [任务 32][Mysql] - Incremental sync completed 
[INFO ] 2024-07-25 17:47:11.563 - [任务 32][Mysql] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-25 17:47:11.575 - [任务 32][Mysql] - java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected <-- Error Message -->
java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: BinaryLogClient is already connected
	com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:566)
	com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.IllegalStateException: BinaryLogClient is already connected
	at io.tapdata.common.exception.AbstractExceptionCollector.collectCdcConfigInvalid(AbstractExceptionCollector.java:63)
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:159)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:370)
	at io.tapdata.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:655)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.IllegalStateException: BinaryLogClient is already connected
	at com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:566)
	at com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	... 1 more

[INFO ] 2024-07-25 17:47:11.576 - [任务 32][Mysql] - Job suspend in error handle 
[INFO ] 2024-07-25 17:47:11.690 - [任务 32][Mysql] - Node Mysql[54b3c97e-27d3-42f2-b483-1fafa916aefa] running status set to false 
[INFO ] 2024-07-25 17:47:11.690 - [任务 32][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-54b3c97e-27d3-42f2-b483-1fafa916aefa 
[INFO ] 2024-07-25 17:47:11.690 - [任务 32][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-54b3c97e-27d3-42f2-b483-1fafa916aefa 
[INFO ] 2024-07-25 17:47:11.691 - [任务 32][Mysql] - Node Mysql[54b3c97e-27d3-42f2-b483-1fafa916aefa] schema data cleaned 
[INFO ] 2024-07-25 17:47:11.691 - [任务 32][Mysql] - Node Mysql[54b3c97e-27d3-42f2-b483-1fafa916aefa] monitor closed 
[INFO ] 2024-07-25 17:47:11.694 - [任务 32][Mysql] - Node Mysql[54b3c97e-27d3-42f2-b483-1fafa916aefa] close complete, cost 19 ms 
[INFO ] 2024-07-25 17:47:11.702 - [任务 32][Mysql3307] - Node Mysql3307[4d6e5acd-3015-4191-9999-6980ea2d0861] running status set to false 
[INFO ] 2024-07-25 17:47:11.711 - [任务 32][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-4d6e5acd-3015-4191-9999-6980ea2d0861 
[INFO ] 2024-07-25 17:47:11.711 - [任务 32][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-4d6e5acd-3015-4191-9999-6980ea2d0861 
[INFO ] 2024-07-25 17:47:11.711 - [任务 32][Mysql3307] - Node Mysql3307[4d6e5acd-3015-4191-9999-6980ea2d0861] schema data cleaned 
[INFO ] 2024-07-25 17:47:11.711 - [任务 32][Mysql3307] - Node Mysql3307[4d6e5acd-3015-4191-9999-6980ea2d0861] monitor closed 
[INFO ] 2024-07-25 17:47:11.711 - [任务 32][Mysql3307] - Node Mysql3307[4d6e5acd-3015-4191-9999-6980ea2d0861] close complete, cost 8 ms 
[INFO ] 2024-07-25 17:49:11.423 - [任务 32] - Task [任务 32] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-25 17:49:16.199 - [任务 32] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 17:51:40.578 - [任务 32] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@52b9fd26 
[INFO ] 2024-07-25 17:53:18.707 - [任务 32] - Stop task milestones: 66a218e8349bc63fe9d89b96(任务 32)  
[INFO ] 2024-07-25 17:53:34.996 - [任务 32] - Stopped task aspect(s) 
[INFO ] 2024-07-25 17:53:34.996 - [任务 32] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 17:53:35.041 - [任务 32] - Remove memory task client succeed, task: 任务 32[66a218e8349bc63fe9d89b96] 
[INFO ] 2024-07-25 17:53:35.041 - [任务 32] - Destroy memory task client cache succeed, task: 任务 32[66a218e8349bc63fe9d89b96] 
