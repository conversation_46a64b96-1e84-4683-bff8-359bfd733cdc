[INFO ] 2024-07-18 10:22:05.812 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167] - Task initialization... 
[INFO ] 2024-07-18 10:22:06.043 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167] - Start task milestones: 6697aca8b92eda1a86f52667(t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167) 
[INFO ] 2024-07-18 10:22:07.911 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-18 10:22:08.479 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167] - The engine receives t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 10:22:09.296 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[58eef254-dd5e-498c-8392-e96e79437ba9] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:22:09.297 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[58eef254-dd5e-498c-8392-e96e79437ba9] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 10:22:09.313 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[c06786ec-18ca-46cf-b5c6-b4127d0ac671] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:22:09.315 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[c06786ec-18ca-46cf-b5c6-b4127d0ac671] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 10:22:10.277 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Source node "qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 10:22:10.309 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 10:22:10.313 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Source node "qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 10:22:10.347 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-18 10:22:10.554 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - batch offset found: {"mock_1000w":{"batch_read_connector_offset":{"syncStage":"Initial","beginTimes":1721216179856,"lastTimes":1721216200186,"lastTN":5333000,"tableStats":{"mock_1000w":{"insertTotals":5333000,"updateTotals":0,"deleteTotals":0}}},"batch_read_connector_status":"RUNNING"}},stream offset not found. 
[INFO ] 2024-07-18 10:22:10.590 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 10:22:10.606 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Starting batch read, table name: mock_1000w, offset: io.tapdata.dummy.po.DummyOffset@22b68aaa 
[INFO ] 2024-07-18 10:22:10.607 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Table mock_1000w is going to be initial synced 
[INFO ] 2024-07-18 10:22:10.652 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Start mock_1000w batch read 
[INFO ] 2024-07-18 10:22:10.653 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Query table 'mock_1000w' counts: 10000000 
[INFO ] 2024-07-18 10:22:51.237 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Compile mock_1000w batch read 
[INFO ] 2024-07-18 10:22:51.238 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Table [mock_1000w] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 10:22:51.239 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 10:22:57.341 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[58eef254-dd5e-498c-8392-e96e79437ba9] running status set to false 
[INFO ] 2024-07-18 10:22:57.346 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[c06786ec-18ca-46cf-b5c6-b4127d0ac671] running status set to false 
[INFO ] 2024-07-18 10:22:57.346 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Stop connector 
[INFO ] 2024-07-18 10:22:57.369 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-58eef254-dd5e-498c-8392-e96e79437ba9 
[INFO ] 2024-07-18 10:22:57.378 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-58eef254-dd5e-498c-8392-e96e79437ba9 
[INFO ] 2024-07-18 10:22:57.379 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[58eef254-dd5e-498c-8392-e96e79437ba9] schema data cleaned 
[INFO ] 2024-07-18 10:22:57.380 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Stop connector: first 1721269330644 426ms, last 1721269371212 3022ms, counts: 10000000/43590ms, min: 7, max: 3051, QPS: 232558/s 
[INFO ] 2024-07-18 10:22:57.387 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[58eef254-dd5e-498c-8392-e96e79437ba9] monitor closed 
[INFO ] 2024-07-18 10:22:57.388 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[58eef254-dd5e-498c-8392-e96e79437ba9] close complete, cost 54 ms 
[INFO ] 2024-07-18 10:22:57.401 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-c06786ec-18ca-46cf-b5c6-b4127d0ac671 
[INFO ] 2024-07-18 10:22:57.401 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-c06786ec-18ca-46cf-b5c6-b4127d0ac671 
[INFO ] 2024-07-18 10:22:57.402 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[c06786ec-18ca-46cf-b5c6-b4127d0ac671] schema data cleaned 
[INFO ] 2024-07-18 10:22:57.402 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[c06786ec-18ca-46cf-b5c6-b4127d0ac671] monitor closed 
[INFO ] 2024-07-18 10:22:57.603 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167][qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537] - Node qa_mock_1000w_cdc_1000qps_3fields_1717403468657_3537[c06786ec-18ca-46cf-b5c6-b4127d0ac671] close complete, cost 68 ms 
[INFO ] 2024-07-18 10:23:02.143 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 10:23:02.327 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1dc30a35 
[INFO ] 2024-07-18 10:23:02.329 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167] - Stop task milestones: 6697aca8b92eda1a86f52667(t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167)  
[INFO ] 2024-07-18 10:23:02.370 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167] - Stopped task aspect(s) 
[INFO ] 2024-07-18 10:23:02.438 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 10:23:02.440 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167] - Remove memory task client succeed, task: t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167[6697aca8b92eda1a86f52667] 
[INFO ] 2024-07-18 10:23:02.440 - [t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167] - Destroy memory task client cache succeed, task: t_1.2-mock_to_mock_3fields_1717403468657_3537-1721216167[6697aca8b92eda1a86f52667] 
