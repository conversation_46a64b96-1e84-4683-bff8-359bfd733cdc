[INFO ] 2024-03-29 17:10:01.438 - [orders_import_import_import_import_import_import_import_import_import_import(100)][Order Details] - Node Order Details[79118b8b-2db8-43dc-b194-d7d5184234ac] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:10:01.439 - [orders_import_import_import_import_import_import_import_import_import_import(100)][Order Details] - Node Order Details[db3f0ee8-7fbf-4107-abcf-a2c735ebe4a3] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:10:01.445 - [orders_import_import_import_import_import_import_import_import_import_import(100)][Order Details] - Node Order Details[db3f0ee8-7fbf-4107-abcf-a2c735ebe4a3] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 17:10:01.446 - [orders_import_import_import_import_import_import_import_import_import_import(100)][Order Details] - Node Order Details[79118b8b-2db8-43dc-b194-d7d5184234ac] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 17:10:01.448 - [orders_import_import_import_import_import_import_import_import_import_import(100)][78875f9d-f527-4fbd-ba4d-f02a05c7fb98] - Node 78875f9d-f527-4fbd-ba4d-f02a05c7fb98[78875f9d-f527-4fbd-ba4d-f02a05c7fb98] start preload schema,table counts: 0 
[INFO ] 2024-03-29 17:10:01.448 - [orders_import_import_import_import_import_import_import_import_import_import(100)][78875f9d-f527-4fbd-ba4d-f02a05c7fb98] - Node 78875f9d-f527-4fbd-ba4d-f02a05c7fb98[78875f9d-f527-4fbd-ba4d-f02a05c7fb98] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 17:10:01.862 - [orders_import_import_import_import_import_import_import_import_import_import(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 17:10:02.166 - [orders_import_import_import_import_import_import_import_import_import_import(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@59f47b3e error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@59f47b3e error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@59f47b3e error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 17:10:03.373 - [orders_import_import_import_import_import_import_import_import_import_import(100)][Order Details] - Node Order Details[db3f0ee8-7fbf-4107-abcf-a2c735ebe4a3] running status set to false 
[INFO ] 2024-03-29 17:10:03.421 - [orders_import_import_import_import_import_import_import_import_import_import(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-db3f0ee8-7fbf-4107-abcf-a2c735ebe4a3 
[INFO ] 2024-03-29 17:10:03.422 - [orders_import_import_import_import_import_import_import_import_import_import(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-db3f0ee8-7fbf-4107-abcf-a2c735ebe4a3 
[INFO ] 2024-03-29 17:10:03.424 - [orders_import_import_import_import_import_import_import_import_import_import(100)][Order Details] - Node Order Details[db3f0ee8-7fbf-4107-abcf-a2c735ebe4a3] schema data cleaned 
[INFO ] 2024-03-29 17:10:03.427 - [orders_import_import_import_import_import_import_import_import_import_import(100)][Order Details] - Node Order Details[db3f0ee8-7fbf-4107-abcf-a2c735ebe4a3] monitor closed 
[INFO ] 2024-03-29 17:10:03.433 - [orders_import_import_import_import_import_import_import_import_import_import(100)][Order Details] - Node Order Details[db3f0ee8-7fbf-4107-abcf-a2c735ebe4a3] close complete, cost 87 ms 
[INFO ] 2024-03-29 17:10:04.719 - [orders_import_import_import_import_import_import_import_import_import_import(100)][Order Details] - Node Order Details[79118b8b-2db8-43dc-b194-d7d5184234ac] running status set to false 
[INFO ] 2024-03-29 17:10:04.730 - [orders_import_import_import_import_import_import_import_import_import_import(100)][78875f9d-f527-4fbd-ba4d-f02a05c7fb98] - Node 78875f9d-f527-4fbd-ba4d-f02a05c7fb98[78875f9d-f527-4fbd-ba4d-f02a05c7fb98] running status set to false 
[INFO ] 2024-03-29 17:10:04.736 - [orders_import_import_import_import_import_import_import_import_import_import(100)][Order Details] - Node Order Details[79118b8b-2db8-43dc-b194-d7d5184234ac] schema data cleaned 
[INFO ] 2024-03-29 17:10:04.740 - [orders_import_import_import_import_import_import_import_import_import_import(100)][78875f9d-f527-4fbd-ba4d-f02a05c7fb98] - Node 78875f9d-f527-4fbd-ba4d-f02a05c7fb98[78875f9d-f527-4fbd-ba4d-f02a05c7fb98] schema data cleaned 
[INFO ] 2024-03-29 17:10:04.741 - [orders_import_import_import_import_import_import_import_import_import_import(100)][Order Details] - Node Order Details[79118b8b-2db8-43dc-b194-d7d5184234ac] monitor closed 
[INFO ] 2024-03-29 17:10:04.747 - [orders_import_import_import_import_import_import_import_import_import_import(100)][78875f9d-f527-4fbd-ba4d-f02a05c7fb98] - Node 78875f9d-f527-4fbd-ba4d-f02a05c7fb98[78875f9d-f527-4fbd-ba4d-f02a05c7fb98] monitor closed 
[INFO ] 2024-03-29 17:10:04.755 - [orders_import_import_import_import_import_import_import_import_import_import(100)][78875f9d-f527-4fbd-ba4d-f02a05c7fb98] - Node 78875f9d-f527-4fbd-ba4d-f02a05c7fb98[78875f9d-f527-4fbd-ba4d-f02a05c7fb98] close complete, cost 23 ms 
[INFO ] 2024-03-29 17:10:04.765 - [orders_import_import_import_import_import_import_import_import_import_import(100)][Order Details] - Node Order Details[79118b8b-2db8-43dc-b194-d7d5184234ac] close complete, cost 26 ms 
[INFO ] 2024-03-29 17:10:04.777 - [orders_import_import_import_import_import_import_import_import_import_import(100)] - load tapTable task 6606856504c06403f62f870c-78875f9d-f527-4fbd-ba4d-f02a05c7fb98 complete, cost 4336ms 
