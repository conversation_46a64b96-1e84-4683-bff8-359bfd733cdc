[INFO ] 2024-10-10 12:16:40.207 - [任务 2(100)][2cb9d683-18f9-4185-92ff-49fab1c51bbd] - Node 2cb9d683-18f9-4185-92ff-49fab1c51bbd[2cb9d683-18f9-4185-92ff-49fab1c51bbd] start preload schema,table counts: 0 
[INFO ] 2024-10-10 12:16:40.215 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:16:40.217 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:16:40.217 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:16:40.217 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:16:40.217 - [任务 2(100)][2cb9d683-18f9-4185-92ff-49fab1c51bbd] - Node 2cb9d683-18f9-4185-92ff-49fab1c51bbd[2cb9d683-18f9-4185-92ff-49fab1c51bbd] preload schema finished, cost 3 ms 
[INFO ] 2024-10-10 12:16:40.217 - [任务 2(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[WARN ] 2024-10-10 12:16:46.553 - [任务 2(100)][testNotNull] - Source table is empty, trying to mock data 
[INFO ] 2024-10-10 12:16:46.638 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 12:16:46.639 - [任务 2(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:16:46.642 - [任务 2(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:16:46.642 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 12:16:46.652 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 12:16:46.653 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 85 ms 
[INFO ] 2024-10-10 12:16:52.712 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 12:16:52.717 - [任务 2(100)][2cb9d683-18f9-4185-92ff-49fab1c51bbd] - Node 2cb9d683-18f9-4185-92ff-49fab1c51bbd[2cb9d683-18f9-4185-92ff-49fab1c51bbd] running status set to false 
[INFO ] 2024-10-10 12:16:52.717 - [任务 2(100)][2cb9d683-18f9-4185-92ff-49fab1c51bbd] - Node 2cb9d683-18f9-4185-92ff-49fab1c51bbd[2cb9d683-18f9-4185-92ff-49fab1c51bbd] schema data cleaned 
[INFO ] 2024-10-10 12:16:52.718 - [任务 2(100)][2cb9d683-18f9-4185-92ff-49fab1c51bbd] - Node 2cb9d683-18f9-4185-92ff-49fab1c51bbd[2cb9d683-18f9-4185-92ff-49fab1c51bbd] monitor closed 
[INFO ] 2024-10-10 12:16:52.718 - [任务 2(100)][2cb9d683-18f9-4185-92ff-49fab1c51bbd] - Node 2cb9d683-18f9-4185-92ff-49fab1c51bbd[2cb9d683-18f9-4185-92ff-49fab1c51bbd] close complete, cost 30 ms 
[INFO ] 2024-10-10 12:16:52.735 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-57d25353-12bc-4352-907e-570e985824dc 
[INFO ] 2024-10-10 12:16:52.738 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-57d25353-12bc-4352-907e-570e985824dc 
[INFO ] 2024-10-10 12:16:52.739 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 12:16:52.751 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 12:16:52.751 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 12:16:52.762 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 69 ms 
[INFO ] 2024-10-10 12:16:52.764 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 12:16:52.764 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 12:16:52.971 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 12:16:57.462 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:16:57.464 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:16:57.466 - [任务 2(100)][7872251a-53d8-45ca-9761-42451b3aa1c8] - Node 7872251a-53d8-45ca-9761-42451b3aa1c8[7872251a-53d8-45ca-9761-42451b3aa1c8] start preload schema,table counts: 0 
[INFO ] 2024-10-10 12:16:57.466 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:16:57.469 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:16:57.474 - [任务 2(100)][7872251a-53d8-45ca-9761-42451b3aa1c8] - Node 7872251a-53d8-45ca-9761-42451b3aa1c8[7872251a-53d8-45ca-9761-42451b3aa1c8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:16:57.475 - [任务 2(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 12:16:57.519 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:16:57.519 - [任务 2(100)][d93a0722-a122-4167-978d-7e85fb0b1121] - Node d93a0722-a122-4167-978d-7e85fb0b1121[d93a0722-a122-4167-978d-7e85fb0b1121] start preload schema,table counts: 0 
[INFO ] 2024-10-10 12:16:57.520 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:16:57.520 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:16:57.520 - [任务 2(100)][d93a0722-a122-4167-978d-7e85fb0b1121] - Node d93a0722-a122-4167-978d-7e85fb0b1121[d93a0722-a122-4167-978d-7e85fb0b1121] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:16:57.521 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:16:57.521 - [任务 2(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[WARN ] 2024-10-10 12:17:03.110 - [任务 2(100)][testNotNull] - Source table is empty, trying to mock data 
[INFO ] 2024-10-10 12:17:03.111 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 12:17:03.174 - [任务 2(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:17:03.177 - [任务 2(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:17:03.177 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 12:17:03.178 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 12:17:03.179 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 70 ms 
[WARN ] 2024-10-10 12:17:08.550 - [任务 2(100)][testNotNull] - Source table is empty, trying to mock data 
[INFO ] 2024-10-10 12:17:08.595 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 12:17:08.595 - [任务 2(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:17:08.596 - [任务 2(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:17:08.596 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 12:17:08.597 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 12:17:08.598 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 47 ms 
[INFO ] 2024-10-10 12:17:08.752 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 12:17:08.756 - [任务 2(100)][7872251a-53d8-45ca-9761-42451b3aa1c8] - Node 7872251a-53d8-45ca-9761-42451b3aa1c8[7872251a-53d8-45ca-9761-42451b3aa1c8] running status set to false 
[INFO ] 2024-10-10 12:17:08.756 - [任务 2(100)][7872251a-53d8-45ca-9761-42451b3aa1c8] - Node 7872251a-53d8-45ca-9761-42451b3aa1c8[7872251a-53d8-45ca-9761-42451b3aa1c8] schema data cleaned 
[INFO ] 2024-10-10 12:17:08.756 - [任务 2(100)][7872251a-53d8-45ca-9761-42451b3aa1c8] - Node 7872251a-53d8-45ca-9761-42451b3aa1c8[7872251a-53d8-45ca-9761-42451b3aa1c8] monitor closed 
[INFO ] 2024-10-10 12:17:08.759 - [任务 2(100)][7872251a-53d8-45ca-9761-42451b3aa1c8] - Node 7872251a-53d8-45ca-9761-42451b3aa1c8[7872251a-53d8-45ca-9761-42451b3aa1c8] close complete, cost 3 ms 
[INFO ] 2024-10-10 12:17:08.784 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-193c2cf4-f5bc-4947-80d2-eef8a064a4a6 
[INFO ] 2024-10-10 12:17:08.784 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-193c2cf4-f5bc-4947-80d2-eef8a064a4a6 
[INFO ] 2024-10-10 12:17:08.785 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 12:17:08.787 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 12:17:08.787 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 12:17:08.791 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 47 ms 
[INFO ] 2024-10-10 12:17:08.798 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 12:17:08.798 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 12:17:09.000 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 12:17:14.209 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 12:17:14.210 - [任务 2(100)][d93a0722-a122-4167-978d-7e85fb0b1121] - Node d93a0722-a122-4167-978d-7e85fb0b1121[d93a0722-a122-4167-978d-7e85fb0b1121] running status set to false 
[INFO ] 2024-10-10 12:17:14.210 - [任务 2(100)][d93a0722-a122-4167-978d-7e85fb0b1121] - Node d93a0722-a122-4167-978d-7e85fb0b1121[d93a0722-a122-4167-978d-7e85fb0b1121] schema data cleaned 
[INFO ] 2024-10-10 12:17:14.210 - [任务 2(100)][d93a0722-a122-4167-978d-7e85fb0b1121] - Node d93a0722-a122-4167-978d-7e85fb0b1121[d93a0722-a122-4167-978d-7e85fb0b1121] monitor closed 
[INFO ] 2024-10-10 12:17:14.210 - [任务 2(100)][d93a0722-a122-4167-978d-7e85fb0b1121] - Node d93a0722-a122-4167-978d-7e85fb0b1121[d93a0722-a122-4167-978d-7e85fb0b1121] close complete, cost 3 ms 
[INFO ] 2024-10-10 12:17:14.234 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-062ff240-fe4f-4d4d-90a0-da420fde8774 
[INFO ] 2024-10-10 12:17:14.235 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-062ff240-fe4f-4d4d-90a0-da420fde8774 
[INFO ] 2024-10-10 12:17:14.237 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 12:17:14.237 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 12:17:14.238 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 12:17:14.238 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 35 ms 
[INFO ] 2024-10-10 12:17:14.241 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 12:17:14.241 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 12:17:14.242 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 12:17:57.560 - [任务 2(100)][5b454001-d107-4742-bee1-40fc5974a103] - Node 5b454001-d107-4742-bee1-40fc5974a103[5b454001-d107-4742-bee1-40fc5974a103] start preload schema,table counts: 0 
[INFO ] 2024-10-10 12:17:57.560 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:17:57.560 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:17:57.563 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:17:57.563 - [任务 2(100)][5b454001-d107-4742-bee1-40fc5974a103] - Node 5b454001-d107-4742-bee1-40fc5974a103[5b454001-d107-4742-bee1-40fc5974a103] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:17:57.564 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:17:57.771 - [任务 2(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[WARN ] 2024-10-10 12:18:03.086 - [任务 2(100)][testNotNull] - Source table is empty, trying to mock data 
[INFO ] 2024-10-10 12:18:03.086 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 12:18:03.143 - [任务 2(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:18:03.144 - [任务 2(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:18:03.144 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 12:18:03.144 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 12:18:03.145 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 59 ms 
[INFO ] 2024-10-10 12:18:08.630 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 12:18:08.645 - [任务 2(100)][5b454001-d107-4742-bee1-40fc5974a103] - Node 5b454001-d107-4742-bee1-40fc5974a103[5b454001-d107-4742-bee1-40fc5974a103] running status set to false 
[INFO ] 2024-10-10 12:18:08.647 - [任务 2(100)][5b454001-d107-4742-bee1-40fc5974a103] - Node 5b454001-d107-4742-bee1-40fc5974a103[5b454001-d107-4742-bee1-40fc5974a103] schema data cleaned 
[INFO ] 2024-10-10 12:18:08.648 - [任务 2(100)][5b454001-d107-4742-bee1-40fc5974a103] - Node 5b454001-d107-4742-bee1-40fc5974a103[5b454001-d107-4742-bee1-40fc5974a103] monitor closed 
[INFO ] 2024-10-10 12:18:08.648 - [任务 2(100)][5b454001-d107-4742-bee1-40fc5974a103] - Node 5b454001-d107-4742-bee1-40fc5974a103[5b454001-d107-4742-bee1-40fc5974a103] close complete, cost 5 ms 
[INFO ] 2024-10-10 12:18:08.659 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-a31953bd-5897-4544-b6d6-1901944d37a4 
[INFO ] 2024-10-10 12:18:08.661 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-a31953bd-5897-4544-b6d6-1901944d37a4 
[INFO ] 2024-10-10 12:18:08.661 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 12:18:08.663 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 12:18:08.663 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 12:18:08.665 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 45 ms 
[INFO ] 2024-10-10 12:18:08.665 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 12:18:08.666 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 12:18:08.873 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 12:18:10.265 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:18:10.266 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:18:10.273 - [任务 2(100)][6a3daf6d-8a5f-4ffb-a7d5-50df542c5296] - Node 6a3daf6d-8a5f-4ffb-a7d5-50df542c5296[6a3daf6d-8a5f-4ffb-a7d5-50df542c5296] start preload schema,table counts: 0 
[INFO ] 2024-10-10 12:18:10.274 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:18:10.277 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:18:10.277 - [任务 2(100)][6a3daf6d-8a5f-4ffb-a7d5-50df542c5296] - Node 6a3daf6d-8a5f-4ffb-a7d5-50df542c5296[6a3daf6d-8a5f-4ffb-a7d5-50df542c5296] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:18:10.479 - [任务 2(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[WARN ] 2024-10-10 12:18:15.777 - [任务 2(100)][testNotNull] - Source table is empty, trying to mock data 
[INFO ] 2024-10-10 12:18:15.777 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 12:18:15.820 - [任务 2(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:18:15.820 - [任务 2(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:18:15.820 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 12:18:15.821 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 12:18:15.821 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 46 ms 
[INFO ] 2024-10-10 12:18:21.478 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 12:18:21.487 - [任务 2(100)][6a3daf6d-8a5f-4ffb-a7d5-50df542c5296] - Node 6a3daf6d-8a5f-4ffb-a7d5-50df542c5296[6a3daf6d-8a5f-4ffb-a7d5-50df542c5296] running status set to false 
[INFO ] 2024-10-10 12:18:21.493 - [任务 2(100)][6a3daf6d-8a5f-4ffb-a7d5-50df542c5296] - Node 6a3daf6d-8a5f-4ffb-a7d5-50df542c5296[6a3daf6d-8a5f-4ffb-a7d5-50df542c5296] schema data cleaned 
[INFO ] 2024-10-10 12:18:21.494 - [任务 2(100)][6a3daf6d-8a5f-4ffb-a7d5-50df542c5296] - Node 6a3daf6d-8a5f-4ffb-a7d5-50df542c5296[6a3daf6d-8a5f-4ffb-a7d5-50df542c5296] monitor closed 
[INFO ] 2024-10-10 12:18:21.494 - [任务 2(100)][6a3daf6d-8a5f-4ffb-a7d5-50df542c5296] - Node 6a3daf6d-8a5f-4ffb-a7d5-50df542c5296[6a3daf6d-8a5f-4ffb-a7d5-50df542c5296] close complete, cost 6 ms 
[INFO ] 2024-10-10 12:18:21.516 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-cecdaf4f-9659-4bba-bcaf-28f17ba6bdb7 
[INFO ] 2024-10-10 12:18:21.517 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-cecdaf4f-9659-4bba-bcaf-28f17ba6bdb7 
[INFO ] 2024-10-10 12:18:21.517 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 12:18:21.519 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 12:18:21.519 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 12:18:21.520 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 49 ms 
[INFO ] 2024-10-10 12:18:21.522 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 12:18:21.523 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 12:18:21.523 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 12:18:23.556 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:18:23.557 - [任务 2(100)][2e3ee1c9-c23b-4412-82f1-50cfb5cf8d7b] - Node 2e3ee1c9-c23b-4412-82f1-50cfb5cf8d7b[2e3ee1c9-c23b-4412-82f1-50cfb5cf8d7b] start preload schema,table counts: 0 
[INFO ] 2024-10-10 12:18:23.558 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:18:23.562 - [任务 2(100)][2e3ee1c9-c23b-4412-82f1-50cfb5cf8d7b] - Node 2e3ee1c9-c23b-4412-82f1-50cfb5cf8d7b[2e3ee1c9-c23b-4412-82f1-50cfb5cf8d7b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:18:23.562 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:18:23.562 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:18:23.562 - [任务 2(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[WARN ] 2024-10-10 12:18:29.071 - [任务 2(100)][testNotNull] - Source table is empty, trying to mock data 
[INFO ] 2024-10-10 12:18:29.083 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 12:18:29.113 - [任务 2(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:18:29.113 - [任务 2(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:18:29.113 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 12:18:29.113 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 12:18:29.114 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 54 ms 
[INFO ] 2024-10-10 12:18:34.726 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 12:18:34.726 - [任务 2(100)][2e3ee1c9-c23b-4412-82f1-50cfb5cf8d7b] - Node 2e3ee1c9-c23b-4412-82f1-50cfb5cf8d7b[2e3ee1c9-c23b-4412-82f1-50cfb5cf8d7b] running status set to false 
[INFO ] 2024-10-10 12:18:34.726 - [任务 2(100)][2e3ee1c9-c23b-4412-82f1-50cfb5cf8d7b] - Node 2e3ee1c9-c23b-4412-82f1-50cfb5cf8d7b[2e3ee1c9-c23b-4412-82f1-50cfb5cf8d7b] schema data cleaned 
[INFO ] 2024-10-10 12:18:34.727 - [任务 2(100)][2e3ee1c9-c23b-4412-82f1-50cfb5cf8d7b] - Node 2e3ee1c9-c23b-4412-82f1-50cfb5cf8d7b[2e3ee1c9-c23b-4412-82f1-50cfb5cf8d7b] monitor closed 
[INFO ] 2024-10-10 12:18:34.759 - [任务 2(100)][2e3ee1c9-c23b-4412-82f1-50cfb5cf8d7b] - Node 2e3ee1c9-c23b-4412-82f1-50cfb5cf8d7b[2e3ee1c9-c23b-4412-82f1-50cfb5cf8d7b] close complete, cost 8 ms 
[INFO ] 2024-10-10 12:18:34.759 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-298ca46d-a834-41be-959f-235c31cf29d5 
[INFO ] 2024-10-10 12:18:34.759 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-298ca46d-a834-41be-959f-235c31cf29d5 
[INFO ] 2024-10-10 12:18:34.760 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 12:18:34.762 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 12:18:34.762 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 12:18:34.762 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 46 ms 
[INFO ] 2024-10-10 12:18:34.765 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 12:18:34.765 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 12:18:34.973 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 12:18:46.814 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:18:46.816 - [任务 2(100)][234bb5c7-2dfb-49c0-b71d-a91a0116cf92] - Node 234bb5c7-2dfb-49c0-b71d-a91a0116cf92[234bb5c7-2dfb-49c0-b71d-a91a0116cf92] start preload schema,table counts: 0 
[INFO ] 2024-10-10 12:18:46.816 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:18:46.816 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:18:46.816 - [任务 2(100)][234bb5c7-2dfb-49c0-b71d-a91a0116cf92] - Node 234bb5c7-2dfb-49c0-b71d-a91a0116cf92[234bb5c7-2dfb-49c0-b71d-a91a0116cf92] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:18:46.816 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:18:46.819 - [任务 2(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 12:18:47.369 - [任务 2(100)][f669bc9e-d665-42f6-b629-d70968f47cb9] - Node f669bc9e-d665-42f6-b629-d70968f47cb9[f669bc9e-d665-42f6-b629-d70968f47cb9] start preload schema,table counts: 0 
[INFO ] 2024-10-10 12:18:47.369 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:18:47.426 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:18:47.427 - [任务 2(100)][f669bc9e-d665-42f6-b629-d70968f47cb9] - Node f669bc9e-d665-42f6-b629-d70968f47cb9[f669bc9e-d665-42f6-b629-d70968f47cb9] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:18:47.427 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:18:47.427 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:18:47.628 - [任务 2(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[WARN ] 2024-10-10 12:18:52.336 - [任务 2(100)][testNotNull] - Source table is empty, trying to mock data 
[INFO ] 2024-10-10 12:18:52.398 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 12:18:52.398 - [任务 2(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:18:52.400 - [任务 2(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:18:52.400 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 12:18:52.400 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 12:18:52.401 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 61 ms 
[WARN ] 2024-10-10 12:18:57.530 - [任务 2(100)][testNotNull] - Source table is empty, trying to mock data 
[INFO ] 2024-10-10 12:18:57.533 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 12:18:57.581 - [任务 2(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:18:57.581 - [任务 2(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:18:57.583 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 12:18:57.583 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 12:18:57.791 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 57 ms 
[INFO ] 2024-10-10 12:18:59.731 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 12:18:59.732 - [任务 2(100)][234bb5c7-2dfb-49c0-b71d-a91a0116cf92] - Node 234bb5c7-2dfb-49c0-b71d-a91a0116cf92[234bb5c7-2dfb-49c0-b71d-a91a0116cf92] running status set to false 
[INFO ] 2024-10-10 12:18:59.741 - [任务 2(100)][234bb5c7-2dfb-49c0-b71d-a91a0116cf92] - Node 234bb5c7-2dfb-49c0-b71d-a91a0116cf92[234bb5c7-2dfb-49c0-b71d-a91a0116cf92] schema data cleaned 
[INFO ] 2024-10-10 12:18:59.753 - [任务 2(100)][234bb5c7-2dfb-49c0-b71d-a91a0116cf92] - Node 234bb5c7-2dfb-49c0-b71d-a91a0116cf92[234bb5c7-2dfb-49c0-b71d-a91a0116cf92] monitor closed 
[INFO ] 2024-10-10 12:18:59.755 - [任务 2(100)][234bb5c7-2dfb-49c0-b71d-a91a0116cf92] - Node 234bb5c7-2dfb-49c0-b71d-a91a0116cf92[234bb5c7-2dfb-49c0-b71d-a91a0116cf92] close complete, cost 9 ms 
[INFO ] 2024-10-10 12:18:59.768 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-9a86f5bc-69b2-4d25-90df-a4bbf51d1385 
[INFO ] 2024-10-10 12:18:59.768 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-9a86f5bc-69b2-4d25-90df-a4bbf51d1385 
[INFO ] 2024-10-10 12:18:59.769 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 12:18:59.771 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 12:18:59.771 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 12:18:59.775 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 50 ms 
[INFO ] 2024-10-10 12:18:59.775 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 12:18:59.776 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 12:18:59.980 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 12:19:05.029 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 12:19:05.038 - [任务 2(100)][f669bc9e-d665-42f6-b629-d70968f47cb9] - Node f669bc9e-d665-42f6-b629-d70968f47cb9[f669bc9e-d665-42f6-b629-d70968f47cb9] running status set to false 
[INFO ] 2024-10-10 12:19:05.044 - [任务 2(100)][f669bc9e-d665-42f6-b629-d70968f47cb9] - Node f669bc9e-d665-42f6-b629-d70968f47cb9[f669bc9e-d665-42f6-b629-d70968f47cb9] schema data cleaned 
[INFO ] 2024-10-10 12:19:05.046 - [任务 2(100)][f669bc9e-d665-42f6-b629-d70968f47cb9] - Node f669bc9e-d665-42f6-b629-d70968f47cb9[f669bc9e-d665-42f6-b629-d70968f47cb9] monitor closed 
[INFO ] 2024-10-10 12:19:05.049 - [任务 2(100)][f669bc9e-d665-42f6-b629-d70968f47cb9] - Node f669bc9e-d665-42f6-b629-d70968f47cb9[f669bc9e-d665-42f6-b629-d70968f47cb9] close complete, cost 24 ms 
[INFO ] 2024-10-10 12:19:05.056 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-3a275fae-14b7-4cfc-b274-330d24410094 
[INFO ] 2024-10-10 12:19:05.056 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-3a275fae-14b7-4cfc-b274-330d24410094 
[INFO ] 2024-10-10 12:19:05.057 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 12:19:05.062 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 12:19:05.062 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 12:19:05.064 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 45 ms 
[INFO ] 2024-10-10 12:19:05.065 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 12:19:05.065 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 12:19:05.269 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 12:19:07.429 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:19:07.430 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:19:07.432 - [任务 2(100)][72d6d2e9-354d-4451-8e49-fbfa844eccbf] - Node 72d6d2e9-354d-4451-8e49-fbfa844eccbf[72d6d2e9-354d-4451-8e49-fbfa844eccbf] start preload schema,table counts: 0 
[INFO ] 2024-10-10 12:19:07.433 - [任务 2(100)][72d6d2e9-354d-4451-8e49-fbfa844eccbf] - Node 72d6d2e9-354d-4451-8e49-fbfa844eccbf[72d6d2e9-354d-4451-8e49-fbfa844eccbf] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:19:07.433 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 12:19:07.433 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 12:19:07.434 - [任务 2(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[WARN ] 2024-10-10 12:19:12.936 - [任务 2(100)][testNotNull] - Source table is empty, trying to mock data 
[INFO ] 2024-10-10 12:19:12.982 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 12:19:12.983 - [任务 2(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:19:12.983 - [任务 2(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:19:12.983 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 12:19:12.984 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 12:19:12.984 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 47 ms 
[INFO ] 2024-10-10 12:19:18.547 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 12:19:18.553 - [任务 2(100)][72d6d2e9-354d-4451-8e49-fbfa844eccbf] - Node 72d6d2e9-354d-4451-8e49-fbfa844eccbf[72d6d2e9-354d-4451-8e49-fbfa844eccbf] running status set to false 
[INFO ] 2024-10-10 12:19:18.575 - [任务 2(100)][72d6d2e9-354d-4451-8e49-fbfa844eccbf] - Node 72d6d2e9-354d-4451-8e49-fbfa844eccbf[72d6d2e9-354d-4451-8e49-fbfa844eccbf] schema data cleaned 
[INFO ] 2024-10-10 12:19:18.575 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-fa293d17-dd12-4fa4-a3c8-84c38af36bbc 
[INFO ] 2024-10-10 12:19:18.575 - [任务 2(100)][72d6d2e9-354d-4451-8e49-fbfa844eccbf] - Node 72d6d2e9-354d-4451-8e49-fbfa844eccbf[72d6d2e9-354d-4451-8e49-fbfa844eccbf] monitor closed 
[INFO ] 2024-10-10 12:19:18.575 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-fa293d17-dd12-4fa4-a3c8-84c38af36bbc 
[INFO ] 2024-10-10 12:19:18.576 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 12:19:18.576 - [任务 2(100)][72d6d2e9-354d-4451-8e49-fbfa844eccbf] - Node 72d6d2e9-354d-4451-8e49-fbfa844eccbf[72d6d2e9-354d-4451-8e49-fbfa844eccbf] close complete, cost 46 ms 
[INFO ] 2024-10-10 12:19:18.579 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 12:19:18.579 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 12:19:18.580 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 53 ms 
[INFO ] 2024-10-10 12:19:18.581 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 12:19:18.582 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 12:19:18.784 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 12:19:22.018 - [任务 2(100)][16b248ea-9037-4a8e-a8f2-b3aaa00c4169] - Node 16b248ea-9037-4a8e-a8f2-b3aaa00c4169[16b248ea-9037-4a8e-a8f2-b3aaa00c4169] start preload schema,table counts: 0 
[INFO ] 2024-10-10 12:19:22.019 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:19:22.019 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 12:19:22.019 - [任务 2(100)][16b248ea-9037-4a8e-a8f2-b3aaa00c4169] - Node 16b248ea-9037-4a8e-a8f2-b3aaa00c4169[16b248ea-9037-4a8e-a8f2-b3aaa00c4169] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:19:22.020 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:19:22.020 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 12:19:22.021 - [任务 2(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[WARN ] 2024-10-10 12:19:27.720 - [任务 2(100)][testNotNull] - Source table is empty, trying to mock data 
[INFO ] 2024-10-10 12:19:27.726 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 12:19:27.788 - [任务 2(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:19:27.788 - [任务 2(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 12:19:27.791 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 12:19:27.792 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 12:19:28.000 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 73 ms 
[INFO ] 2024-10-10 12:19:33.405 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 12:19:33.407 - [任务 2(100)][16b248ea-9037-4a8e-a8f2-b3aaa00c4169] - Node 16b248ea-9037-4a8e-a8f2-b3aaa00c4169[16b248ea-9037-4a8e-a8f2-b3aaa00c4169] running status set to false 
[INFO ] 2024-10-10 12:19:33.407 - [任务 2(100)][16b248ea-9037-4a8e-a8f2-b3aaa00c4169] - Node 16b248ea-9037-4a8e-a8f2-b3aaa00c4169[16b248ea-9037-4a8e-a8f2-b3aaa00c4169] schema data cleaned 
[INFO ] 2024-10-10 12:19:33.407 - [任务 2(100)][16b248ea-9037-4a8e-a8f2-b3aaa00c4169] - Node 16b248ea-9037-4a8e-a8f2-b3aaa00c4169[16b248ea-9037-4a8e-a8f2-b3aaa00c4169] monitor closed 
[INFO ] 2024-10-10 12:19:33.408 - [任务 2(100)][16b248ea-9037-4a8e-a8f2-b3aaa00c4169] - Node 16b248ea-9037-4a8e-a8f2-b3aaa00c4169[16b248ea-9037-4a8e-a8f2-b3aaa00c4169] close complete, cost 3 ms 
[INFO ] 2024-10-10 12:19:33.434 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-086459f3-b76c-4dd2-82a2-f6f9ac3deae2 
[INFO ] 2024-10-10 12:19:33.434 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-086459f3-b76c-4dd2-82a2-f6f9ac3deae2 
[INFO ] 2024-10-10 12:19:33.437 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 12:19:33.437 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 12:19:33.442 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 12:19:33.442 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 42 ms 
[INFO ] 2024-10-10 12:19:33.442 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 12:19:33.442 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 12:19:33.442 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 14:06:14.995 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:06:14.996 - [任务 2(100)][7e82d38b-6005-457b-a7eb-8fcf2727f3cc] - Node 7e82d38b-6005-457b-a7eb-8fcf2727f3cc[7e82d38b-6005-457b-a7eb-8fcf2727f3cc] start preload schema,table counts: 0 
[INFO ] 2024-10-10 14:06:14.996 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:06:14.996 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 14:06:14.996 - [任务 2(100)][7e82d38b-6005-457b-a7eb-8fcf2727f3cc] - Node 7e82d38b-6005-457b-a7eb-8fcf2727f3cc[7e82d38b-6005-457b-a7eb-8fcf2727f3cc] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:06:14.996 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:06:14.999 - [任务 2(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 14:06:20.966 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 14:06:21.006 - [任务 2(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 14:06:21.006 - [任务 2(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 14:06:21.006 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 14:06:21.006 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 14:06:21.007 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 55 ms 
[INFO ] 2024-10-10 14:06:26.732 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 14:06:26.735 - [任务 2(100)][7e82d38b-6005-457b-a7eb-8fcf2727f3cc] - Node 7e82d38b-6005-457b-a7eb-8fcf2727f3cc[7e82d38b-6005-457b-a7eb-8fcf2727f3cc] running status set to false 
[INFO ] 2024-10-10 14:06:26.735 - [任务 2(100)][7e82d38b-6005-457b-a7eb-8fcf2727f3cc] - Node 7e82d38b-6005-457b-a7eb-8fcf2727f3cc[7e82d38b-6005-457b-a7eb-8fcf2727f3cc] schema data cleaned 
[INFO ] 2024-10-10 14:06:26.735 - [任务 2(100)][7e82d38b-6005-457b-a7eb-8fcf2727f3cc] - Node 7e82d38b-6005-457b-a7eb-8fcf2727f3cc[7e82d38b-6005-457b-a7eb-8fcf2727f3cc] monitor closed 
[INFO ] 2024-10-10 14:06:26.768 - [任务 2(100)][7e82d38b-6005-457b-a7eb-8fcf2727f3cc] - Node 7e82d38b-6005-457b-a7eb-8fcf2727f3cc[7e82d38b-6005-457b-a7eb-8fcf2727f3cc] close complete, cost 10 ms 
[INFO ] 2024-10-10 14:06:26.769 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-7b45134d-4ac8-43f4-a4f6-b707ff4a36fc 
[INFO ] 2024-10-10 14:06:26.769 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-7b45134d-4ac8-43f4-a4f6-b707ff4a36fc 
[INFO ] 2024-10-10 14:06:26.769 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 14:06:26.772 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 14:06:26.772 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 14:06:26.773 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 56 ms 
[INFO ] 2024-10-10 14:06:26.776 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 14:06:26.777 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 14:06:26.777 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 14:06:36.085 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:06:36.085 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:06:36.085 - [任务 2(100)][254860bf-4610-4829-9996-1dd9ba94cbd1] - Node 254860bf-4610-4829-9996-1dd9ba94cbd1[254860bf-4610-4829-9996-1dd9ba94cbd1] start preload schema,table counts: 0 
[INFO ] 2024-10-10 14:06:36.087 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:06:36.088 - [任务 2(100)][254860bf-4610-4829-9996-1dd9ba94cbd1] - Node 254860bf-4610-4829-9996-1dd9ba94cbd1[254860bf-4610-4829-9996-1dd9ba94cbd1] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:06:36.088 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:06:36.293 - [任务 2(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 14:06:41.792 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 14:06:41.803 - [任务 2(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 14:06:41.804 - [任务 2(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 14:06:41.804 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 14:06:41.804 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 14:06:42.009 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 118 ms 
[INFO ] 2024-10-10 14:06:47.370 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 14:06:47.372 - [任务 2(100)][254860bf-4610-4829-9996-1dd9ba94cbd1] - Node 254860bf-4610-4829-9996-1dd9ba94cbd1[254860bf-4610-4829-9996-1dd9ba94cbd1] running status set to false 
[INFO ] 2024-10-10 14:06:47.372 - [任务 2(100)][254860bf-4610-4829-9996-1dd9ba94cbd1] - Node 254860bf-4610-4829-9996-1dd9ba94cbd1[254860bf-4610-4829-9996-1dd9ba94cbd1] schema data cleaned 
[INFO ] 2024-10-10 14:06:47.372 - [任务 2(100)][254860bf-4610-4829-9996-1dd9ba94cbd1] - Node 254860bf-4610-4829-9996-1dd9ba94cbd1[254860bf-4610-4829-9996-1dd9ba94cbd1] monitor closed 
[INFO ] 2024-10-10 14:06:47.372 - [任务 2(100)][254860bf-4610-4829-9996-1dd9ba94cbd1] - Node 254860bf-4610-4829-9996-1dd9ba94cbd1[254860bf-4610-4829-9996-1dd9ba94cbd1] close complete, cost 3 ms 
[INFO ] 2024-10-10 14:06:47.399 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-e8fa2a7d-d3e3-4be4-8532-0d52f584f7f5 
[INFO ] 2024-10-10 14:06:47.400 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-e8fa2a7d-d3e3-4be4-8532-0d52f584f7f5 
[INFO ] 2024-10-10 14:06:47.400 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 14:06:47.402 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 14:06:47.403 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 14:06:47.403 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 43 ms 
[INFO ] 2024-10-10 14:06:47.405 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 14:06:47.406 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 14:06:47.406 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 14:06:50.519 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:06:50.523 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:06:50.524 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 14:06:50.526 - [任务 2(100)][d3f393ab-8b8e-4779-8d95-ee222d803d99] - Node d3f393ab-8b8e-4779-8d95-ee222d803d99[d3f393ab-8b8e-4779-8d95-ee222d803d99] start preload schema,table counts: 0 
[INFO ] 2024-10-10 14:06:50.527 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:06:50.527 - [任务 2(100)][d3f393ab-8b8e-4779-8d95-ee222d803d99] - Node d3f393ab-8b8e-4779-8d95-ee222d803d99[d3f393ab-8b8e-4779-8d95-ee222d803d99] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 14:06:50.528 - [任务 2(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 14:06:56.188 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 14:06:56.262 - [任务 2(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 14:06:56.263 - [任务 2(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 14:06:56.263 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 14:06:56.263 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 14:06:56.469 - [任务 2(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 95 ms 
[INFO ] 2024-10-10 14:07:01.844 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 14:07:01.846 - [任务 2(100)][d3f393ab-8b8e-4779-8d95-ee222d803d99] - Node d3f393ab-8b8e-4779-8d95-ee222d803d99[d3f393ab-8b8e-4779-8d95-ee222d803d99] running status set to false 
[INFO ] 2024-10-10 14:07:01.846 - [任务 2(100)][d3f393ab-8b8e-4779-8d95-ee222d803d99] - Node d3f393ab-8b8e-4779-8d95-ee222d803d99[d3f393ab-8b8e-4779-8d95-ee222d803d99] schema data cleaned 
[INFO ] 2024-10-10 14:07:01.846 - [任务 2(100)][d3f393ab-8b8e-4779-8d95-ee222d803d99] - Node d3f393ab-8b8e-4779-8d95-ee222d803d99[d3f393ab-8b8e-4779-8d95-ee222d803d99] monitor closed 
[INFO ] 2024-10-10 14:07:01.846 - [任务 2(100)][d3f393ab-8b8e-4779-8d95-ee222d803d99] - Node d3f393ab-8b8e-4779-8d95-ee222d803d99[d3f393ab-8b8e-4779-8d95-ee222d803d99] close complete, cost 10 ms 
[INFO ] 2024-10-10 14:07:01.876 - [任务 2(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-a815bd9f-e733-4353-ac2e-acf2e6586b45 
[INFO ] 2024-10-10 14:07:01.877 - [任务 2(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-a815bd9f-e733-4353-ac2e-acf2e6586b45 
[INFO ] 2024-10-10 14:07:01.877 - [任务 2(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 14:07:01.879 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 14:07:01.879 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 14:07:01.882 - [任务 2(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 49 ms 
[INFO ] 2024-10-10 14:07:01.882 - [任务 2(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 14:07:01.882 - [任务 2(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 14:07:01.941 - [任务 2(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:10:56.427 - [测试PDK 错误，不能写入Null值(100)][b12119f2-ddf5-442b-bae5-a5a91f81625a] - Node b12119f2-ddf5-442b-bae5-a5a91f81625a[b12119f2-ddf5-442b-bae5-a5a91f81625a] start preload schema,table counts: 0 
[INFO ] 2024-10-10 16:10:56.429 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:10:56.429 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:10:56.429 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:10:56.429 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:10:56.429 - [测试PDK 错误，不能写入Null值(100)][b12119f2-ddf5-442b-bae5-a5a91f81625a] - Node b12119f2-ddf5-442b-bae5-a5a91f81625a[b12119f2-ddf5-442b-bae5-a5a91f81625a] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:10:56.429 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:11:02.773 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:11:02.774 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:11:02.774 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:11:02.774 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:11:02.774 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:11:02.774 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 55 ms 
[INFO ] 2024-10-10 16:11:08.421 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:11:08.422 - [测试PDK 错误，不能写入Null值(100)][b12119f2-ddf5-442b-bae5-a5a91f81625a] - Node b12119f2-ddf5-442b-bae5-a5a91f81625a[b12119f2-ddf5-442b-bae5-a5a91f81625a] running status set to false 
[INFO ] 2024-10-10 16:11:08.422 - [测试PDK 错误，不能写入Null值(100)][b12119f2-ddf5-442b-bae5-a5a91f81625a] - Node b12119f2-ddf5-442b-bae5-a5a91f81625a[b12119f2-ddf5-442b-bae5-a5a91f81625a] schema data cleaned 
[INFO ] 2024-10-10 16:11:08.422 - [测试PDK 错误，不能写入Null值(100)][b12119f2-ddf5-442b-bae5-a5a91f81625a] - Node b12119f2-ddf5-442b-bae5-a5a91f81625a[b12119f2-ddf5-442b-bae5-a5a91f81625a] monitor closed 
[INFO ] 2024-10-10 16:11:08.423 - [测试PDK 错误，不能写入Null值(100)][b12119f2-ddf5-442b-bae5-a5a91f81625a] - Node b12119f2-ddf5-442b-bae5-a5a91f81625a[b12119f2-ddf5-442b-bae5-a5a91f81625a] close complete, cost 3 ms 
[INFO ] 2024-10-10 16:11:08.443 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-ab72b5bd-28f2-42a4-88fc-4536320edb47 
[INFO ] 2024-10-10 16:11:08.443 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-ab72b5bd-28f2-42a4-88fc-4536320edb47 
[INFO ] 2024-10-10 16:11:08.443 - [测试PDK 错误，不能写入Null值(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:11:08.447 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:11:08.448 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:11:08.448 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 40 ms 
[INFO ] 2024-10-10 16:11:08.451 - [测试PDK 错误，不能写入Null值(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 16:11:08.451 - [测试PDK 错误，不能写入Null值(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 16:11:08.658 - [测试PDK 错误，不能写入Null值(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:11:11.308 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:11:11.311 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:11:11.312 - [测试PDK 错误，不能写入Null值(100)][4b9855e2-54bb-485b-bbda-a0957a19115e] - Node 4b9855e2-54bb-485b-bbda-a0957a19115e[4b9855e2-54bb-485b-bbda-a0957a19115e] start preload schema,table counts: 0 
[INFO ] 2024-10-10 16:11:11.312 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:11:11.312 - [测试PDK 错误，不能写入Null值(100)][4b9855e2-54bb-485b-bbda-a0957a19115e] - Node 4b9855e2-54bb-485b-bbda-a0957a19115e[4b9855e2-54bb-485b-bbda-a0957a19115e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:11:11.312 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:11:11.312 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:11:16.886 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:11:16.888 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:11:16.888 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:11:16.889 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:11:16.889 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:11:17.097 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 37 ms 
[INFO ] 2024-10-10 16:11:22.530 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:11:22.532 - [测试PDK 错误，不能写入Null值(100)][4b9855e2-54bb-485b-bbda-a0957a19115e] - Node 4b9855e2-54bb-485b-bbda-a0957a19115e[4b9855e2-54bb-485b-bbda-a0957a19115e] running status set to false 
[INFO ] 2024-10-10 16:11:22.532 - [测试PDK 错误，不能写入Null值(100)][4b9855e2-54bb-485b-bbda-a0957a19115e] - Node 4b9855e2-54bb-485b-bbda-a0957a19115e[4b9855e2-54bb-485b-bbda-a0957a19115e] schema data cleaned 
[INFO ] 2024-10-10 16:11:22.532 - [测试PDK 错误，不能写入Null值(100)][4b9855e2-54bb-485b-bbda-a0957a19115e] - Node 4b9855e2-54bb-485b-bbda-a0957a19115e[4b9855e2-54bb-485b-bbda-a0957a19115e] monitor closed 
[INFO ] 2024-10-10 16:11:22.532 - [测试PDK 错误，不能写入Null值(100)][4b9855e2-54bb-485b-bbda-a0957a19115e] - Node 4b9855e2-54bb-485b-bbda-a0957a19115e[4b9855e2-54bb-485b-bbda-a0957a19115e] close complete, cost 6 ms 
[INFO ] 2024-10-10 16:11:22.560 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-56b38780-1b8d-47cb-a29a-87d95967bce9 
[INFO ] 2024-10-10 16:11:22.561 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-56b38780-1b8d-47cb-a29a-87d95967bce9 
[INFO ] 2024-10-10 16:11:22.561 - [测试PDK 错误，不能写入Null值(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:11:22.562 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:11:22.562 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:11:22.562 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 46 ms 
[INFO ] 2024-10-10 16:11:22.564 - [测试PDK 错误，不能写入Null值(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 16:11:22.564 - [测试PDK 错误，不能写入Null值(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 16:11:22.771 - [测试PDK 错误，不能写入Null值(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:13:49.033 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:13:49.035 - [测试PDK 错误，不能写入Null值(100)][7c275bef-a071-4250-8434-b3816e8f0105] - Node 7c275bef-a071-4250-8434-b3816e8f0105[7c275bef-a071-4250-8434-b3816e8f0105] start preload schema,table counts: 0 
[INFO ] 2024-10-10 16:13:49.036 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:13:49.036 - [测试PDK 错误，不能写入Null值(100)][7c275bef-a071-4250-8434-b3816e8f0105] - Node 7c275bef-a071-4250-8434-b3816e8f0105[7c275bef-a071-4250-8434-b3816e8f0105] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:13:49.036 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 2 ms 
[INFO ] 2024-10-10 16:13:49.036 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 2 ms 
[INFO ] 2024-10-10 16:13:49.036 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:13:54.866 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:13:54.918 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:13:54.918 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:13:54.918 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:13:54.919 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:13:55.125 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 63 ms 
[INFO ] 2024-10-10 16:14:00.544 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:14:00.544 - [测试PDK 错误，不能写入Null值(100)][7c275bef-a071-4250-8434-b3816e8f0105] - Node 7c275bef-a071-4250-8434-b3816e8f0105[7c275bef-a071-4250-8434-b3816e8f0105] running status set to false 
[INFO ] 2024-10-10 16:14:00.544 - [测试PDK 错误，不能写入Null值(100)][7c275bef-a071-4250-8434-b3816e8f0105] - Node 7c275bef-a071-4250-8434-b3816e8f0105[7c275bef-a071-4250-8434-b3816e8f0105] schema data cleaned 
[INFO ] 2024-10-10 16:14:00.544 - [测试PDK 错误，不能写入Null值(100)][7c275bef-a071-4250-8434-b3816e8f0105] - Node 7c275bef-a071-4250-8434-b3816e8f0105[7c275bef-a071-4250-8434-b3816e8f0105] monitor closed 
[INFO ] 2024-10-10 16:14:00.545 - [测试PDK 错误，不能写入Null值(100)][7c275bef-a071-4250-8434-b3816e8f0105] - Node 7c275bef-a071-4250-8434-b3816e8f0105[7c275bef-a071-4250-8434-b3816e8f0105] close complete, cost 2 ms 
[INFO ] 2024-10-10 16:14:00.565 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-e8db24fd-8ab1-4756-84bc-ec085378e82e 
[INFO ] 2024-10-10 16:14:00.566 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-e8db24fd-8ab1-4756-84bc-ec085378e82e 
[INFO ] 2024-10-10 16:14:00.566 - [测试PDK 错误，不能写入Null值(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:14:00.569 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:14:00.569 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:14:00.569 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 33 ms 
[INFO ] 2024-10-10 16:14:00.572 - [测试PDK 错误，不能写入Null值(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 16:14:00.572 - [测试PDK 错误，不能写入Null值(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 16:14:00.779 - [测试PDK 错误，不能写入Null值(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:14:03.283 - [测试PDK 错误，不能写入Null值(100)][2a240ad9-7a14-431a-ac2b-49e8cbd0766d] - Node 2a240ad9-7a14-431a-ac2b-49e8cbd0766d[2a240ad9-7a14-431a-ac2b-49e8cbd0766d] start preload schema,table counts: 0 
[INFO ] 2024-10-10 16:14:03.284 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:14:03.284 - [测试PDK 错误，不能写入Null值(100)][2a240ad9-7a14-431a-ac2b-49e8cbd0766d] - Node 2a240ad9-7a14-431a-ac2b-49e8cbd0766d[2a240ad9-7a14-431a-ac2b-49e8cbd0766d] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:14:03.284 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:14:03.284 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:14:03.285 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 3 ms 
[INFO ] 2024-10-10 16:14:03.296 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:14:09.050 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:14:09.053 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:14:09.054 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:14:09.055 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:14:09.055 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:14:09.057 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 39 ms 
[INFO ] 2024-10-10 16:14:14.663 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:14:14.665 - [测试PDK 错误，不能写入Null值(100)][2a240ad9-7a14-431a-ac2b-49e8cbd0766d] - Node 2a240ad9-7a14-431a-ac2b-49e8cbd0766d[2a240ad9-7a14-431a-ac2b-49e8cbd0766d] running status set to false 
[INFO ] 2024-10-10 16:14:14.665 - [测试PDK 错误，不能写入Null值(100)][2a240ad9-7a14-431a-ac2b-49e8cbd0766d] - Node 2a240ad9-7a14-431a-ac2b-49e8cbd0766d[2a240ad9-7a14-431a-ac2b-49e8cbd0766d] schema data cleaned 
[INFO ] 2024-10-10 16:14:14.665 - [测试PDK 错误，不能写入Null值(100)][2a240ad9-7a14-431a-ac2b-49e8cbd0766d] - Node 2a240ad9-7a14-431a-ac2b-49e8cbd0766d[2a240ad9-7a14-431a-ac2b-49e8cbd0766d] monitor closed 
[INFO ] 2024-10-10 16:14:14.665 - [测试PDK 错误，不能写入Null值(100)][2a240ad9-7a14-431a-ac2b-49e8cbd0766d] - Node 2a240ad9-7a14-431a-ac2b-49e8cbd0766d[2a240ad9-7a14-431a-ac2b-49e8cbd0766d] close complete, cost 3 ms 
[INFO ] 2024-10-10 16:14:14.686 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-038c6798-efba-497a-baf4-cd0d1b8097df 
[INFO ] 2024-10-10 16:14:14.686 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-038c6798-efba-497a-baf4-cd0d1b8097df 
[INFO ] 2024-10-10 16:14:14.686 - [测试PDK 错误，不能写入Null值(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:14:14.689 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:14:14.690 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:14:14.690 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 36 ms 
[INFO ] 2024-10-10 16:14:14.693 - [测试PDK 错误，不能写入Null值(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 16:14:14.693 - [测试PDK 错误，不能写入Null值(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 16:14:14.756 - [测试PDK 错误，不能写入Null值(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:16:33.111 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:16:33.112 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:16:33.113 - [测试PDK 错误，不能写入Null值(100)][421eb891-bb65-4a71-b9fc-db4ef3c6355b] - Node 421eb891-bb65-4a71-b9fc-db4ef3c6355b[421eb891-bb65-4a71-b9fc-db4ef3c6355b] start preload schema,table counts: 0 
[INFO ] 2024-10-10 16:16:33.113 - [测试PDK 错误，不能写入Null值(100)][421eb891-bb65-4a71-b9fc-db4ef3c6355b] - Node 421eb891-bb65-4a71-b9fc-db4ef3c6355b[421eb891-bb65-4a71-b9fc-db4ef3c6355b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:16:33.113 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:16:33.113 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:16:33.113 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:16:39.346 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:16:39.347 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:16:39.347 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:16:39.347 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:16:39.347 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:16:39.347 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 94 ms 
[INFO ] 2024-10-10 16:16:44.959 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:16:44.960 - [测试PDK 错误，不能写入Null值(100)][421eb891-bb65-4a71-b9fc-db4ef3c6355b] - Node 421eb891-bb65-4a71-b9fc-db4ef3c6355b[421eb891-bb65-4a71-b9fc-db4ef3c6355b] running status set to false 
[INFO ] 2024-10-10 16:16:44.960 - [测试PDK 错误，不能写入Null值(100)][421eb891-bb65-4a71-b9fc-db4ef3c6355b] - Node 421eb891-bb65-4a71-b9fc-db4ef3c6355b[421eb891-bb65-4a71-b9fc-db4ef3c6355b] schema data cleaned 
[INFO ] 2024-10-10 16:16:44.961 - [测试PDK 错误，不能写入Null值(100)][421eb891-bb65-4a71-b9fc-db4ef3c6355b] - Node 421eb891-bb65-4a71-b9fc-db4ef3c6355b[421eb891-bb65-4a71-b9fc-db4ef3c6355b] monitor closed 
[INFO ] 2024-10-10 16:16:44.961 - [测试PDK 错误，不能写入Null值(100)][421eb891-bb65-4a71-b9fc-db4ef3c6355b] - Node 421eb891-bb65-4a71-b9fc-db4ef3c6355b[421eb891-bb65-4a71-b9fc-db4ef3c6355b] close complete, cost 3 ms 
[INFO ] 2024-10-10 16:16:44.988 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-b6531748-079d-4f10-aa3f-e2f34621ce09 
[INFO ] 2024-10-10 16:16:44.988 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-b6531748-079d-4f10-aa3f-e2f34621ce09 
[INFO ] 2024-10-10 16:16:44.988 - [测试PDK 错误，不能写入Null值(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:16:44.989 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:16:44.990 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:16:44.991 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 35 ms 
[INFO ] 2024-10-10 16:16:44.994 - [测试PDK 错误，不能写入Null值(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 16:16:44.994 - [测试PDK 错误，不能写入Null值(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 16:16:44.994 - [测试PDK 错误，不能写入Null值(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:16:53.267 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:16:53.269 - [测试PDK 错误，不能写入Null值(100)][10dc1837-b72f-43e6-a784-f700afe2687a] - Node 10dc1837-b72f-43e6-a784-f700afe2687a[10dc1837-b72f-43e6-a784-f700afe2687a] start preload schema,table counts: 0 
[INFO ] 2024-10-10 16:16:53.269 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:16:53.270 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:16:53.270 - [测试PDK 错误，不能写入Null值(100)][10dc1837-b72f-43e6-a784-f700afe2687a] - Node 10dc1837-b72f-43e6-a784-f700afe2687a[10dc1837-b72f-43e6-a784-f700afe2687a] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:16:53.270 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:16:53.270 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:16:58.829 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:16:58.829 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:16:58.830 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:16:58.830 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:16:58.830 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:16:59.041 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 23 ms 
[INFO ] 2024-10-10 16:17:04.580 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:17:04.581 - [测试PDK 错误，不能写入Null值(100)][10dc1837-b72f-43e6-a784-f700afe2687a] - Node 10dc1837-b72f-43e6-a784-f700afe2687a[10dc1837-b72f-43e6-a784-f700afe2687a] running status set to false 
[INFO ] 2024-10-10 16:17:04.581 - [测试PDK 错误，不能写入Null值(100)][10dc1837-b72f-43e6-a784-f700afe2687a] - Node 10dc1837-b72f-43e6-a784-f700afe2687a[10dc1837-b72f-43e6-a784-f700afe2687a] schema data cleaned 
[INFO ] 2024-10-10 16:17:04.581 - [测试PDK 错误，不能写入Null值(100)][10dc1837-b72f-43e6-a784-f700afe2687a] - Node 10dc1837-b72f-43e6-a784-f700afe2687a[10dc1837-b72f-43e6-a784-f700afe2687a] monitor closed 
[INFO ] 2024-10-10 16:17:04.581 - [测试PDK 错误，不能写入Null值(100)][10dc1837-b72f-43e6-a784-f700afe2687a] - Node 10dc1837-b72f-43e6-a784-f700afe2687a[10dc1837-b72f-43e6-a784-f700afe2687a] close complete, cost 1 ms 
[INFO ] 2024-10-10 16:17:04.604 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-54c3df8c-6a40-4bb8-8350-70c0028747b2 
[INFO ] 2024-10-10 16:17:04.604 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-54c3df8c-6a40-4bb8-8350-70c0028747b2 
[INFO ] 2024-10-10 16:17:04.605 - [测试PDK 错误，不能写入Null值(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:17:04.609 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:17:04.609 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:17:04.609 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 38 ms 
[INFO ] 2024-10-10 16:17:04.613 - [测试PDK 错误，不能写入Null值(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 16:17:04.613 - [测试PDK 错误，不能写入Null值(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 16:17:04.820 - [测试PDK 错误，不能写入Null值(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:17:08.506 - [测试PDK 错误，不能写入Null值(100)][fd7f96c3-6eef-411b-9918-8d6a5a46d654] - Node fd7f96c3-6eef-411b-9918-8d6a5a46d654[fd7f96c3-6eef-411b-9918-8d6a5a46d654] start preload schema,table counts: 0 
[INFO ] 2024-10-10 16:17:08.506 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:17:08.507 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:17:08.507 - [测试PDK 错误，不能写入Null值(100)][fd7f96c3-6eef-411b-9918-8d6a5a46d654] - Node fd7f96c3-6eef-411b-9918-8d6a5a46d654[fd7f96c3-6eef-411b-9918-8d6a5a46d654] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:17:08.507 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:17:08.507 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:17:08.507 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:17:14.117 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:17:14.117 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:17:14.117 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:17:14.118 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:17:14.118 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:17:14.326 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 40 ms 
[INFO ] 2024-10-10 16:17:19.799 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:17:19.800 - [测试PDK 错误，不能写入Null值(100)][fd7f96c3-6eef-411b-9918-8d6a5a46d654] - Node fd7f96c3-6eef-411b-9918-8d6a5a46d654[fd7f96c3-6eef-411b-9918-8d6a5a46d654] running status set to false 
[INFO ] 2024-10-10 16:17:19.800 - [测试PDK 错误，不能写入Null值(100)][fd7f96c3-6eef-411b-9918-8d6a5a46d654] - Node fd7f96c3-6eef-411b-9918-8d6a5a46d654[fd7f96c3-6eef-411b-9918-8d6a5a46d654] schema data cleaned 
[INFO ] 2024-10-10 16:17:19.800 - [测试PDK 错误，不能写入Null值(100)][fd7f96c3-6eef-411b-9918-8d6a5a46d654] - Node fd7f96c3-6eef-411b-9918-8d6a5a46d654[fd7f96c3-6eef-411b-9918-8d6a5a46d654] monitor closed 
[INFO ] 2024-10-10 16:17:19.800 - [测试PDK 错误，不能写入Null值(100)][fd7f96c3-6eef-411b-9918-8d6a5a46d654] - Node fd7f96c3-6eef-411b-9918-8d6a5a46d654[fd7f96c3-6eef-411b-9918-8d6a5a46d654] close complete, cost 4 ms 
[INFO ] 2024-10-10 16:17:19.819 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-f3a93531-8436-4a53-9e5b-494c57b5cf5c 
[INFO ] 2024-10-10 16:17:19.819 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-f3a93531-8436-4a53-9e5b-494c57b5cf5c 
[INFO ] 2024-10-10 16:17:19.820 - [测试PDK 错误，不能写入Null值(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:17:19.824 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:17:19.824 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:17:19.827 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 36 ms 
[INFO ] 2024-10-10 16:17:19.827 - [测试PDK 错误，不能写入Null值(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 16:17:19.827 - [测试PDK 错误，不能写入Null值(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 16:17:20.029 - [测试PDK 错误，不能写入Null值(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:17:21.914 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:17:21.914 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:17:21.915 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:17:21.915 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:17:21.915 - [测试PDK 错误，不能写入Null值(100)][74de4fec-7547-4421-9a40-b49f1c662d1f] - Node 74de4fec-7547-4421-9a40-b49f1c662d1f[74de4fec-7547-4421-9a40-b49f1c662d1f] start preload schema,table counts: 0 
[INFO ] 2024-10-10 16:17:21.915 - [测试PDK 错误，不能写入Null值(100)][74de4fec-7547-4421-9a40-b49f1c662d1f] - Node 74de4fec-7547-4421-9a40-b49f1c662d1f[74de4fec-7547-4421-9a40-b49f1c662d1f] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:17:21.918 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:17:27.512 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:17:27.550 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:17:27.551 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:17:27.551 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:17:27.551 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:17:27.553 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 53 ms 
[INFO ] 2024-10-10 16:17:33.248 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:17:33.249 - [测试PDK 错误，不能写入Null值(100)][74de4fec-7547-4421-9a40-b49f1c662d1f] - Node 74de4fec-7547-4421-9a40-b49f1c662d1f[74de4fec-7547-4421-9a40-b49f1c662d1f] running status set to false 
[INFO ] 2024-10-10 16:17:33.249 - [测试PDK 错误，不能写入Null值(100)][74de4fec-7547-4421-9a40-b49f1c662d1f] - Node 74de4fec-7547-4421-9a40-b49f1c662d1f[74de4fec-7547-4421-9a40-b49f1c662d1f] schema data cleaned 
[INFO ] 2024-10-10 16:17:33.249 - [测试PDK 错误，不能写入Null值(100)][74de4fec-7547-4421-9a40-b49f1c662d1f] - Node 74de4fec-7547-4421-9a40-b49f1c662d1f[74de4fec-7547-4421-9a40-b49f1c662d1f] monitor closed 
[INFO ] 2024-10-10 16:17:33.249 - [测试PDK 错误，不能写入Null值(100)][74de4fec-7547-4421-9a40-b49f1c662d1f] - Node 74de4fec-7547-4421-9a40-b49f1c662d1f[74de4fec-7547-4421-9a40-b49f1c662d1f] close complete, cost 2 ms 
[INFO ] 2024-10-10 16:17:33.272 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-e01db056-09d2-46d9-bcc3-1f97e1fec4dd 
[INFO ] 2024-10-10 16:17:33.272 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-e01db056-09d2-46d9-bcc3-1f97e1fec4dd 
[INFO ] 2024-10-10 16:17:33.272 - [测试PDK 错误，不能写入Null值(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:17:33.277 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:17:33.277 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:17:33.281 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 38 ms 
[INFO ] 2024-10-10 16:17:33.281 - [测试PDK 错误，不能写入Null值(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 16:17:33.281 - [测试PDK 错误，不能写入Null值(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 16:17:33.485 - [测试PDK 错误，不能写入Null值(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:17:36.845 - [测试PDK 错误，不能写入Null值(100)][89ca5614-ddab-4705-829e-72448f0728c6] - Node 89ca5614-ddab-4705-829e-72448f0728c6[89ca5614-ddab-4705-829e-72448f0728c6] start preload schema,table counts: 0 
[INFO ] 2024-10-10 16:17:36.846 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:17:36.847 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:17:36.847 - [测试PDK 错误，不能写入Null值(100)][89ca5614-ddab-4705-829e-72448f0728c6] - Node 89ca5614-ddab-4705-829e-72448f0728c6[89ca5614-ddab-4705-829e-72448f0728c6] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:17:36.847 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 2 ms 
[INFO ] 2024-10-10 16:17:36.847 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:17:36.847 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:17:42.421 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:17:42.455 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:17:42.455 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:17:42.456 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:17:42.456 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:17:42.663 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 38 ms 
[INFO ] 2024-10-10 16:17:48.508 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:17:48.511 - [测试PDK 错误，不能写入Null值(100)][89ca5614-ddab-4705-829e-72448f0728c6] - Node 89ca5614-ddab-4705-829e-72448f0728c6[89ca5614-ddab-4705-829e-72448f0728c6] running status set to false 
[INFO ] 2024-10-10 16:17:48.511 - [测试PDK 错误，不能写入Null值(100)][89ca5614-ddab-4705-829e-72448f0728c6] - Node 89ca5614-ddab-4705-829e-72448f0728c6[89ca5614-ddab-4705-829e-72448f0728c6] schema data cleaned 
[INFO ] 2024-10-10 16:17:48.512 - [测试PDK 错误，不能写入Null值(100)][89ca5614-ddab-4705-829e-72448f0728c6] - Node 89ca5614-ddab-4705-829e-72448f0728c6[89ca5614-ddab-4705-829e-72448f0728c6] monitor closed 
[INFO ] 2024-10-10 16:17:48.514 - [测试PDK 错误，不能写入Null值(100)][89ca5614-ddab-4705-829e-72448f0728c6] - Node 89ca5614-ddab-4705-829e-72448f0728c6[89ca5614-ddab-4705-829e-72448f0728c6] close complete, cost 4 ms 
[INFO ] 2024-10-10 16:17:48.540 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-7169c902-d5b6-400e-9840-9bc58a16fda6 
[INFO ] 2024-10-10 16:17:48.540 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-7169c902-d5b6-400e-9840-9bc58a16fda6 
[INFO ] 2024-10-10 16:17:48.540 - [测试PDK 错误，不能写入Null值(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:17:48.543 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:17:48.544 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:17:48.544 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 53 ms 
[INFO ] 2024-10-10 16:17:48.547 - [测试PDK 错误，不能写入Null值(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 16:17:48.547 - [测试PDK 错误，不能写入Null值(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 16:17:48.548 - [测试PDK 错误，不能写入Null值(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:17:51.127 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:17:51.128 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 2 ms 
[INFO ] 2024-10-10 16:17:51.128 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:17:51.128 - [测试PDK 错误，不能写入Null值(100)][830cb5d1-cffa-463a-a8bf-82b6465ccc53] - Node 830cb5d1-cffa-463a-a8bf-82b6465ccc53[830cb5d1-cffa-463a-a8bf-82b6465ccc53] start preload schema,table counts: 0 
[INFO ] 2024-10-10 16:17:51.128 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:17:51.128 - [测试PDK 错误，不能写入Null值(100)][830cb5d1-cffa-463a-a8bf-82b6465ccc53] - Node 830cb5d1-cffa-463a-a8bf-82b6465ccc53[830cb5d1-cffa-463a-a8bf-82b6465ccc53] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:17:51.128 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:17:56.755 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:17:56.785 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:17:56.785 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:17:56.785 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:17:56.785 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:17:56.994 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 32 ms 
[INFO ] 2024-10-10 16:18:03.065 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:18:03.067 - [测试PDK 错误，不能写入Null值(100)][830cb5d1-cffa-463a-a8bf-82b6465ccc53] - Node 830cb5d1-cffa-463a-a8bf-82b6465ccc53[830cb5d1-cffa-463a-a8bf-82b6465ccc53] running status set to false 
[INFO ] 2024-10-10 16:18:03.067 - [测试PDK 错误，不能写入Null值(100)][830cb5d1-cffa-463a-a8bf-82b6465ccc53] - Node 830cb5d1-cffa-463a-a8bf-82b6465ccc53[830cb5d1-cffa-463a-a8bf-82b6465ccc53] schema data cleaned 
[INFO ] 2024-10-10 16:18:03.067 - [测试PDK 错误，不能写入Null值(100)][830cb5d1-cffa-463a-a8bf-82b6465ccc53] - Node 830cb5d1-cffa-463a-a8bf-82b6465ccc53[830cb5d1-cffa-463a-a8bf-82b6465ccc53] monitor closed 
[INFO ] 2024-10-10 16:18:03.067 - [测试PDK 错误，不能写入Null值(100)][830cb5d1-cffa-463a-a8bf-82b6465ccc53] - Node 830cb5d1-cffa-463a-a8bf-82b6465ccc53[830cb5d1-cffa-463a-a8bf-82b6465ccc53] close complete, cost 3 ms 
[INFO ] 2024-10-10 16:18:03.091 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-cdb4a8ad-63a0-4049-bb57-7796739dcab9 
[INFO ] 2024-10-10 16:18:03.092 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-cdb4a8ad-63a0-4049-bb57-7796739dcab9 
[INFO ] 2024-10-10 16:18:03.092 - [测试PDK 错误，不能写入Null值(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:18:03.095 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:18:03.095 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:18:03.095 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 38 ms 
[INFO ] 2024-10-10 16:18:03.098 - [测试PDK 错误，不能写入Null值(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 16:18:03.098 - [测试PDK 错误，不能写入Null值(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 16:18:03.176 - [测试PDK 错误，不能写入Null值(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:22:52.082 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:22:52.085 - [测试PDK 错误，不能写入Null值(100)][bfc94b7c-729b-41be-8044-2e07250f5ce8] - Node bfc94b7c-729b-41be-8044-2e07250f5ce8[bfc94b7c-729b-41be-8044-2e07250f5ce8] start preload schema,table counts: 0 
[INFO ] 2024-10-10 16:22:52.085 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:22:52.085 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:22:52.085 - [测试PDK 错误，不能写入Null值(100)][bfc94b7c-729b-41be-8044-2e07250f5ce8] - Node bfc94b7c-729b-41be-8044-2e07250f5ce8[bfc94b7c-729b-41be-8044-2e07250f5ce8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:22:52.085 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:22:52.085 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:22:58.358 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:22:58.414 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:22:58.416 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:22:58.416 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:22:58.416 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:22:58.421 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 88 ms 
[INFO ] 2024-10-10 16:23:04.318 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:23:04.323 - [测试PDK 错误，不能写入Null值(100)][bfc94b7c-729b-41be-8044-2e07250f5ce8] - Node bfc94b7c-729b-41be-8044-2e07250f5ce8[bfc94b7c-729b-41be-8044-2e07250f5ce8] running status set to false 
[INFO ] 2024-10-10 16:23:04.334 - [测试PDK 错误，不能写入Null值(100)][bfc94b7c-729b-41be-8044-2e07250f5ce8] - Node bfc94b7c-729b-41be-8044-2e07250f5ce8[bfc94b7c-729b-41be-8044-2e07250f5ce8] schema data cleaned 
[INFO ] 2024-10-10 16:23:04.339 - [测试PDK 错误，不能写入Null值(100)][bfc94b7c-729b-41be-8044-2e07250f5ce8] - Node bfc94b7c-729b-41be-8044-2e07250f5ce8[bfc94b7c-729b-41be-8044-2e07250f5ce8] monitor closed 
[INFO ] 2024-10-10 16:23:04.339 - [测试PDK 错误，不能写入Null值(100)][bfc94b7c-729b-41be-8044-2e07250f5ce8] - Node bfc94b7c-729b-41be-8044-2e07250f5ce8[bfc94b7c-729b-41be-8044-2e07250f5ce8] close complete, cost 2 ms 
[INFO ] 2024-10-10 16:23:04.356 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-f1718a64-32a8-4885-b093-d02f3ed5a185 
[INFO ] 2024-10-10 16:23:04.356 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-f1718a64-32a8-4885-b093-d02f3ed5a185 
[INFO ] 2024-10-10 16:23:04.359 - [测试PDK 错误，不能写入Null值(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:23:04.359 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:23:04.359 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:23:04.360 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 55 ms 
[INFO ] 2024-10-10 16:23:04.363 - [测试PDK 错误，不能写入Null值(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 16:23:04.364 - [测试PDK 错误，不能写入Null值(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 16:23:04.364 - [测试PDK 错误，不能写入Null值(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:26:53.698 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:26:53.698 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:26:53.699 - [测试PDK 错误，不能写入Null值(100)][dc07ebb7-5ee1-4fd4-811c-35164aef937f] - Node dc07ebb7-5ee1-4fd4-811c-35164aef937f[dc07ebb7-5ee1-4fd4-811c-35164aef937f] start preload schema,table counts: 0 
[INFO ] 2024-10-10 16:26:53.699 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:26:53.699 - [测试PDK 错误，不能写入Null值(100)][dc07ebb7-5ee1-4fd4-811c-35164aef937f] - Node dc07ebb7-5ee1-4fd4-811c-35164aef937f[dc07ebb7-5ee1-4fd4-811c-35164aef937f] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:26:53.699 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:26:53.699 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:26:59.706 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:26:59.773 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:26:59.773 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:26:59.774 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:26:59.774 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:26:59.976 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 87 ms 
[INFO ] 2024-10-10 16:27:05.487 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:27:05.491 - [测试PDK 错误，不能写入Null值(100)][dc07ebb7-5ee1-4fd4-811c-35164aef937f] - Node dc07ebb7-5ee1-4fd4-811c-35164aef937f[dc07ebb7-5ee1-4fd4-811c-35164aef937f] running status set to false 
[INFO ] 2024-10-10 16:27:05.491 - [测试PDK 错误，不能写入Null值(100)][dc07ebb7-5ee1-4fd4-811c-35164aef937f] - Node dc07ebb7-5ee1-4fd4-811c-35164aef937f[dc07ebb7-5ee1-4fd4-811c-35164aef937f] schema data cleaned 
[INFO ] 2024-10-10 16:27:05.491 - [测试PDK 错误，不能写入Null值(100)][dc07ebb7-5ee1-4fd4-811c-35164aef937f] - Node dc07ebb7-5ee1-4fd4-811c-35164aef937f[dc07ebb7-5ee1-4fd4-811c-35164aef937f] monitor closed 
[INFO ] 2024-10-10 16:27:05.491 - [测试PDK 错误，不能写入Null值(100)][dc07ebb7-5ee1-4fd4-811c-35164aef937f] - Node dc07ebb7-5ee1-4fd4-811c-35164aef937f[dc07ebb7-5ee1-4fd4-811c-35164aef937f] close complete, cost 5 ms 
[INFO ] 2024-10-10 16:27:05.522 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-a9433f15-4321-4c50-b9a6-211659087b31 
[INFO ] 2024-10-10 16:27:05.522 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-a9433f15-4321-4c50-b9a6-211659087b31 
[INFO ] 2024-10-10 16:27:05.522 - [测试PDK 错误，不能写入Null值(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:27:05.527 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:27:05.527 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:27:05.527 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 56 ms 
[INFO ] 2024-10-10 16:27:05.532 - [测试PDK 错误，不能写入Null值(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 16:27:05.532 - [测试PDK 错误，不能写入Null值(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 16:27:05.590 - [测试PDK 错误，不能写入Null值(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:33:41.914 - [测试PDK 错误，不能写入Null值(100)][955fd029-a8eb-4bd7-b89e-aaaa04f7ee7c] - Node 955fd029-a8eb-4bd7-b89e-aaaa04f7ee7c[955fd029-a8eb-4bd7-b89e-aaaa04f7ee7c] start preload schema,table counts: 0 
[INFO ] 2024-10-10 16:33:41.917 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:33:41.917 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:33:41.917 - [测试PDK 错误，不能写入Null值(100)][955fd029-a8eb-4bd7-b89e-aaaa04f7ee7c] - Node 955fd029-a8eb-4bd7-b89e-aaaa04f7ee7c[955fd029-a8eb-4bd7-b89e-aaaa04f7ee7c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:33:41.917 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:33:41.917 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:33:41.917 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:33:47.898 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:33:47.899 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:33:47.899 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:33:47.900 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:33:47.900 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:33:47.905 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 104 ms 
[INFO ] 2024-10-10 16:33:53.778 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:33:53.779 - [测试PDK 错误，不能写入Null值(100)][955fd029-a8eb-4bd7-b89e-aaaa04f7ee7c] - Node 955fd029-a8eb-4bd7-b89e-aaaa04f7ee7c[955fd029-a8eb-4bd7-b89e-aaaa04f7ee7c] running status set to false 
[INFO ] 2024-10-10 16:33:53.779 - [测试PDK 错误，不能写入Null值(100)][955fd029-a8eb-4bd7-b89e-aaaa04f7ee7c] - Node 955fd029-a8eb-4bd7-b89e-aaaa04f7ee7c[955fd029-a8eb-4bd7-b89e-aaaa04f7ee7c] schema data cleaned 
[INFO ] 2024-10-10 16:33:53.779 - [测试PDK 错误，不能写入Null值(100)][955fd029-a8eb-4bd7-b89e-aaaa04f7ee7c] - Node 955fd029-a8eb-4bd7-b89e-aaaa04f7ee7c[955fd029-a8eb-4bd7-b89e-aaaa04f7ee7c] monitor closed 
[INFO ] 2024-10-10 16:33:53.779 - [测试PDK 错误，不能写入Null值(100)][955fd029-a8eb-4bd7-b89e-aaaa04f7ee7c] - Node 955fd029-a8eb-4bd7-b89e-aaaa04f7ee7c[955fd029-a8eb-4bd7-b89e-aaaa04f7ee7c] close complete, cost 3 ms 
[INFO ] 2024-10-10 16:33:53.814 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-46495a52-ef0f-48d1-b188-169170d1f182 
[INFO ] 2024-10-10 16:33:53.814 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-46495a52-ef0f-48d1-b188-169170d1f182 
[INFO ] 2024-10-10 16:33:53.814 - [测试PDK 错误，不能写入Null值(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:33:53.816 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:33:53.816 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:33:53.819 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 45 ms 
[INFO ] 2024-10-10 16:33:53.820 - [测试PDK 错误，不能写入Null值(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 16:33:53.820 - [测试PDK 错误，不能写入Null值(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 16:33:53.821 - [测试PDK 错误，不能写入Null值(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:53:32.170 - [测试PDK 错误，不能写入Null值(100)][099b4a48-afdc-4c3c-a03c-a2e782899b10] - Node 099b4a48-afdc-4c3c-a03c-a2e782899b10[099b4a48-afdc-4c3c-a03c-a2e782899b10] start preload schema,table counts: 0 
[INFO ] 2024-10-10 16:53:32.173 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:53:32.174 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:53:32.176 - [测试PDK 错误，不能写入Null值(100)][099b4a48-afdc-4c3c-a03c-a2e782899b10] - Node 099b4a48-afdc-4c3c-a03c-a2e782899b10[099b4a48-afdc-4c3c-a03c-a2e782899b10] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:53:32.177 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:53:32.180 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:53:32.181 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:53:38.431 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:53:38.489 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:53:38.499 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:53:38.499 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:53:38.509 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:53:38.510 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 75 ms 
[INFO ] 2024-10-10 16:53:44.237 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:53:44.252 - [测试PDK 错误，不能写入Null值(100)][099b4a48-afdc-4c3c-a03c-a2e782899b10] - Node 099b4a48-afdc-4c3c-a03c-a2e782899b10[099b4a48-afdc-4c3c-a03c-a2e782899b10] running status set to false 
[INFO ] 2024-10-10 16:53:44.270 - [测试PDK 错误，不能写入Null值(100)][099b4a48-afdc-4c3c-a03c-a2e782899b10] - Node 099b4a48-afdc-4c3c-a03c-a2e782899b10[099b4a48-afdc-4c3c-a03c-a2e782899b10] schema data cleaned 
[INFO ] 2024-10-10 16:53:44.271 - [测试PDK 错误，不能写入Null值(100)][099b4a48-afdc-4c3c-a03c-a2e782899b10] - Node 099b4a48-afdc-4c3c-a03c-a2e782899b10[099b4a48-afdc-4c3c-a03c-a2e782899b10] monitor closed 
[INFO ] 2024-10-10 16:53:44.271 - [测试PDK 错误，不能写入Null值(100)][099b4a48-afdc-4c3c-a03c-a2e782899b10] - Node 099b4a48-afdc-4c3c-a03c-a2e782899b10[099b4a48-afdc-4c3c-a03c-a2e782899b10] close complete, cost 30 ms 
[INFO ] 2024-10-10 16:53:44.291 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-c999014f-7777-43c0-979d-491e9a829ce6 
[INFO ] 2024-10-10 16:53:44.294 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-c999014f-7777-43c0-979d-491e9a829ce6 
[INFO ] 2024-10-10 16:53:44.294 - [测试PDK 错误，不能写入Null值(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:53:44.306 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:53:44.306 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:53:44.317 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 76 ms 
[INFO ] 2024-10-10 16:53:44.320 - [测试PDK 错误，不能写入Null值(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 16:53:44.321 - [测试PDK 错误，不能写入Null值(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 16:53:44.528 - [测试PDK 错误，不能写入Null值(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-10 16:53:46.172 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:53:46.174 - [测试PDK 错误，不能写入Null值(100)][4e14ed06-2341-4c7b-a73c-4c50d805562b] - Node 4e14ed06-2341-4c7b-a73c-4c50d805562b[4e14ed06-2341-4c7b-a73c-4c50d805562b] start preload schema,table counts: 0 
[INFO ] 2024-10-10 16:53:46.174 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 16:53:46.174 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 16:53:46.174 - [测试PDK 错误，不能写入Null值(100)][4e14ed06-2341-4c7b-a73c-4c50d805562b] - Node 4e14ed06-2341-4c7b-a73c-4c50d805562b[4e14ed06-2341-4c7b-a73c-4c50d805562b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:53:46.175 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 16:53:46.381 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node js_processor(增强JS: ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b) enable batch process 
[INFO ] 2024-10-10 16:53:52.048 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] running status set to false 
[INFO ] 2024-10-10 16:53:52.049 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:53:52.049 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - PDK connector node released: HazelcastSampleSourcePdkDataNode-f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8 
[INFO ] 2024-10-10 16:53:52.050 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] schema data cleaned 
[INFO ] 2024-10-10 16:53:52.052 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] monitor closed 
[INFO ] 2024-10-10 16:53:52.260 - [测试PDK 错误，不能写入Null值(100)][testNotNull] - Node testNotNull[f8ab5b50-aba5-42e6-9b8d-24c4b3ccb2d8] close complete, cost 39 ms 
[INFO ] 2024-10-10 16:53:58.021 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] running status set to false 
[INFO ] 2024-10-10 16:53:58.043 - [测试PDK 错误，不能写入Null值(100)][4e14ed06-2341-4c7b-a73c-4c50d805562b] - Node 4e14ed06-2341-4c7b-a73c-4c50d805562b[4e14ed06-2341-4c7b-a73c-4c50d805562b] running status set to false 
[INFO ] 2024-10-10 16:53:58.046 - [测试PDK 错误，不能写入Null值(100)][4e14ed06-2341-4c7b-a73c-4c50d805562b] - Node 4e14ed06-2341-4c7b-a73c-4c50d805562b[4e14ed06-2341-4c7b-a73c-4c50d805562b] schema data cleaned 
[INFO ] 2024-10-10 16:53:58.046 - [测试PDK 错误，不能写入Null值(100)][4e14ed06-2341-4c7b-a73c-4c50d805562b] - Node 4e14ed06-2341-4c7b-a73c-4c50d805562b[4e14ed06-2341-4c7b-a73c-4c50d805562b] monitor closed 
[INFO ] 2024-10-10 16:53:58.046 - [测试PDK 错误，不能写入Null值(100)][4e14ed06-2341-4c7b-a73c-4c50d805562b] - Node 4e14ed06-2341-4c7b-a73c-4c50d805562b[4e14ed06-2341-4c7b-a73c-4c50d805562b] close complete, cost 25 ms 
[INFO ] 2024-10-10 16:53:58.056 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-a74a167a-204d-4b74-ae31-8a64622a4db9 
[INFO ] 2024-10-10 16:53:58.057 - [测试PDK 错误，不能写入Null值(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-a74a167a-204d-4b74-ae31-8a64622a4db9 
[INFO ] 2024-10-10 16:53:58.058 - [测试PDK 错误，不能写入Null值(100)][增强JS] - [ScriptExecutorsManager-6707547d5fe35676bdffab43-ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-10 16:53:58.061 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] schema data cleaned 
[INFO ] 2024-10-10 16:53:58.062 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] monitor closed 
[INFO ] 2024-10-10 16:53:58.062 - [测试PDK 错误，不能写入Null值(100)][增强JS] - Node 增强JS[ed6965e0-9a9d-46a5-bf6e-875fc45d7f0b] close complete, cost 61 ms 
[INFO ] 2024-10-10 16:53:58.075 - [测试PDK 错误，不能写入Null值(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-10 16:53:58.076 - [测试PDK 错误，不能写入Null值(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-10 16:53:58.076 - [测试PDK 错误，不能写入Null值(100)] - Stopped task aspect(s) 
