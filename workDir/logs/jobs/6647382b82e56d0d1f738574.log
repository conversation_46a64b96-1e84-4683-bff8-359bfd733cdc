[INFO ] 2024-05-17 19:28:03.164 - [任务 11] - Task initialization... 
[INFO ] 2024-05-17 19:28:03.164 - [任务 11] - Start task milestones: 6647382b82e56d0d1f738574(任务 11) 
[INFO ] 2024-05-17 19:28:03.164 - [任务 11] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-17 19:28:03.164 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-17 19:28:03.164 - [任务 11][targetCK] - Node targetCK[7d8b5370-0db1-4356-9361-f7d78ef48bd0] start preload schema,table counts: 1 
[INFO ] 2024-05-17 19:28:03.164 - [任务 11][SourcePG] - Node SourcePG[66717fec-cf3d-40a6-bed1-16aeb90da645] start preload schema,table counts: 1 
[INFO ] 2024-05-17 19:28:03.164 - [任务 11][targetCK] - Node targetCK[7d8b5370-0db1-4356-9361-f7d78ef48bd0] preload schema finished, cost 20 ms 
[INFO ] 2024-05-17 19:28:03.165 - [任务 11][SourcePG] - Node SourcePG[66717fec-cf3d-40a6-bed1-16aeb90da645] preload schema finished, cost 20 ms 
[INFO ] 2024-05-17 19:28:04.148 - [任务 11][SourcePG] - Source node "SourcePG" read batch size: 100 
[INFO ] 2024-05-17 19:28:04.150 - [任务 11][SourcePG] - Source node "SourcePG" event queue capacity: 200 
[INFO ] 2024-05-17 19:28:04.151 - [任务 11][SourcePG] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-17 19:28:04.346 - [任务 11][SourcePG] - new logical replication slot created, slotName:tapdata_cdc_1dfae4a4_07cb_4957_a173_de852015693b 
[INFO ] 2024-05-17 19:28:04.349 - [任务 11][SourcePG] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2024-05-17 19:28:04.349 - [任务 11][SourcePG] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-17 19:28:04.414 - [任务 11][SourcePG] - Initial sync started 
[INFO ] 2024-05-17 19:28:04.414 - [任务 11][SourcePG] - Starting batch read, table name: testtime1, offset: null 
[INFO ] 2024-05-17 19:28:04.604 - [任务 11][SourcePG] - Table testtime1 is going to be initial synced 
[INFO ] 2024-05-17 19:28:04.605 - [任务 11][SourcePG] - Table [testtime1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-05-17 19:28:04.625 - [任务 11][SourcePG] - Query table 'testtime1' counts: 7 
[INFO ] 2024-05-17 19:28:04.630 - [任务 11][SourcePG] - Initial sync completed 
[INFO ] 2024-05-17 19:28:04.632 - [任务 11][SourcePG] - Incremental sync starting... 
[INFO ] 2024-05-17 19:28:04.632 - [任务 11][SourcePG] - Initial sync completed 
[INFO ] 2024-05-17 19:28:04.633 - [任务 11][SourcePG] - Starting stream read, table list: [testtime1], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2024-05-17 19:28:04.683 - [任务 11][SourcePG] - Using an existing logical replication slot, slotName:tapdata_cdc_1dfae4a4_07cb_4957_a173_de852015693b 
[INFO ] 2024-05-17 19:28:05.496 - [任务 11][SourcePG] - Connector PostgreSQL incremental start succeed, tables: [testtime1], data change syncing 
[ERROR] 2024-05-17 19:28:09.786 - [任务 11][targetCK] - com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: ru.yandex.clickhouse.except.ClickHouseUnknownException: ClickHouse exception, code: 1002, host: ************, port: 8123; Connection reset <-- Error Message -->
com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: ru.yandex.clickhouse.except.ClickHouseUnknownException: ClickHouse exception, code: 1002, host: ************, port: 8123; Connection reset

<-- Simple Stack Trace -->
Caused by: java.net.SocketException: Connection reset
	java.net.SocketInputStream.read(SocketInputStream.java:210)
	java.net.SocketInputStream.read(SocketInputStream.java:141)
	org.apache.http.impl.io.SessionInputBufferImpl.streamRead(SessionInputBufferImpl.java:139)
	org.apache.http.impl.io.SessionInputBufferImpl.fillBuffer(SessionInputBufferImpl.java:155)
	org.apache.http.impl.io.SessionInputBufferImpl.readLine(SessionInputBufferImpl.java:284)
	...

<-- Full Stack Trace -->
com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: ru.yandex.clickhouse.except.ClickHouseUnknownException: ClickHouse exception, code: 1002, host: ************, port: 8123; Connection reset
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:190)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:128)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:128)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:112)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:106)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:83)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:177)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:406)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:174)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.zaxxer.hikari.pool.HikariPool$PoolInitializationException: Failed to initialize pool: ru.yandex.clickhouse.except.ClickHouseUnknownException: ClickHouse exception, code: 1002, host: ************, port: 8123; Connection reset
	at com.zaxxer.hikari.pool.HikariPool.throwPoolInitializationException(HikariPool.java:596)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:582)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	at io.tapdata.common.JdbcContext.queryVersion(JdbcContext.java:63)
	at io.tapdata.connector.clickhouse.ClickhouseJdbcContext.<init>(ClickhouseJdbcContext.java:28)
	at io.tapdata.connector.clickhouse.ClickhouseConnector.initConnection(ClickhouseConnector.java:80)
	at io.tapdata.connector.clickhouse.ClickhouseConnector.onStart(ClickhouseConnector.java:60)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:268)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:177)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:168)
	... 16 more
Caused by: java.lang.RuntimeException: ru.yandex.clickhouse.except.ClickHouseUnknownException: ClickHouse exception, code: 1002, host: ************, port: 8123; Connection reset
	at ru.yandex.clickhouse.ClickHouseConnectionImpl.initTimeZone(ClickHouseConnectionImpl.java:99)
	at ru.yandex.clickhouse.ClickHouseConnectionImpl.<init>(ClickHouseConnectionImpl.java:80)
	at ru.yandex.clickhouse.ClickHouseDriver.connect(ClickHouseDriver.java:55)
	at ru.yandex.clickhouse.ClickHouseDriver.connect(ClickHouseDriver.java:47)
	at ru.yandex.clickhouse.ClickHouseDriver.connect(ClickHouseDriver.java:29)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	... 27 more
Caused by: ru.yandex.clickhouse.except.ClickHouseUnknownException: ClickHouse exception, code: 1002, host: ************, port: 8123; Connection reset
	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.getException(ClickHouseExceptionSpecifier.java:91)
	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:55)
	at ru.yandex.clickhouse.except.ClickHouseExceptionSpecifier.specify(ClickHouseExceptionSpecifier.java:24)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:633)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:117)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:100)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:95)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.executeQuery(ClickHouseStatementImpl.java:90)
	at ru.yandex.clickhouse.ClickHouseConnectionImpl.initTimeZone(ClickHouseConnectionImpl.java:94)
	... 36 more
Caused by: java.net.SocketException: Connection reset
	at java.net.SocketInputStream.read(SocketInputStream.java:210)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at org.apache.http.impl.io.SessionInputBufferImpl.streamRead(SessionInputBufferImpl.java:139)
	at org.apache.http.impl.io.SessionInputBufferImpl.fillBuffer(SessionInputBufferImpl.java:155)
	at org.apache.http.impl.io.SessionInputBufferImpl.readLine(SessionInputBufferImpl.java:284)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:140)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:57)
	at org.apache.http.impl.io.AbstractMessageParser.parse(AbstractMessageParser.java:261)
	at org.apache.http.impl.DefaultBHttpClientConnection.receiveResponseHeader(DefaultBHttpClientConnection.java:165)
	at org.apache.http.impl.conn.CPoolProxy.receiveResponseHeader(CPoolProxy.java:167)
	at org.apache.http.protocol.HttpRequestExecutor.doReceiveResponse(HttpRequestExecutor.java:272)
	at org.apache.http.protocol.HttpRequestExecutor.execute(HttpRequestExecutor.java:124)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:271)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:184)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:88)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:184)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:82)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:107)
	at ru.yandex.clickhouse.ClickHouseStatementImpl.getInputStream(ClickHouseStatementImpl.java:614)
	... 41 more

[INFO ] 2024-05-17 19:28:09.792 - [任务 11][targetCK] - Job suspend in error handle 
[INFO ] 2024-05-17 19:28:09.996 - [任务 11][SourcePG] - Node SourcePG[66717fec-cf3d-40a6-bed1-16aeb90da645] running status set to false 
[INFO ] 2024-05-17 19:28:10.203 - [任务 11][SourcePG] - Incremental sync completed 
[INFO ] 2024-05-17 19:28:10.207 - [任务 11][SourcePG] - PDK connector node stopped: HazelcastSourcePdkDataNode-66717fec-cf3d-40a6-bed1-16aeb90da645 
[INFO ] 2024-05-17 19:28:10.208 - [任务 11][SourcePG] - PDK connector node released: HazelcastSourcePdkDataNode-66717fec-cf3d-40a6-bed1-16aeb90da645 
[INFO ] 2024-05-17 19:28:10.209 - [任务 11][SourcePG] - Node SourcePG[66717fec-cf3d-40a6-bed1-16aeb90da645] schema data cleaned 
[INFO ] 2024-05-17 19:28:10.209 - [任务 11][SourcePG] - Node SourcePG[66717fec-cf3d-40a6-bed1-16aeb90da645] monitor closed 
[INFO ] 2024-05-17 19:28:10.229 - [任务 11][SourcePG] - Node SourcePG[66717fec-cf3d-40a6-bed1-16aeb90da645] close complete, cost 343 ms 
[INFO ] 2024-05-17 19:28:10.233 - [任务 11][targetCK] - Node targetCK[7d8b5370-0db1-4356-9361-f7d78ef48bd0] running status set to false 
[WARN ] 2024-05-17 19:28:10.234 - [任务 11][targetCK] - Stop PDK connector node failed: java.lang.NullPointerException | Associate id: HazelcastTargetPdkDataNode-7d8b5370-0db1-4356-9361-f7d78ef48bd0 
[INFO ] 2024-05-17 19:28:10.235 - [任务 11][targetCK] - PDK connector node released: HazelcastTargetPdkDataNode-7d8b5370-0db1-4356-9361-f7d78ef48bd0 
[INFO ] 2024-05-17 19:28:10.235 - [任务 11][targetCK] - Node targetCK[7d8b5370-0db1-4356-9361-f7d78ef48bd0] schema data cleaned 
[INFO ] 2024-05-17 19:28:10.235 - [任务 11][targetCK] - Node targetCK[7d8b5370-0db1-4356-9361-f7d78ef48bd0] monitor closed 
[INFO ] 2024-05-17 19:28:10.362 - [任务 11][targetCK] - Node targetCK[7d8b5370-0db1-4356-9361-f7d78ef48bd0] close complete, cost 6 ms 
[INFO ] 2024-05-17 19:28:10.363 - [任务 11] - Task [任务 11] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-05-17 19:28:10.386 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-17 19:28:10.386 - [任务 11] - Stop task milestones: 6647382b82e56d0d1f738574(任务 11)  
[INFO ] 2024-05-17 19:28:10.405 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-05-17 19:28:10.405 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-05-17 19:28:10.419 - [任务 11] - Remove memory task client succeed, task: 任务 11[6647382b82e56d0d1f738574] 
[INFO ] 2024-05-17 19:28:10.419 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[6647382b82e56d0d1f738574] 
