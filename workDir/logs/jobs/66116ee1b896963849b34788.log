[INFO ] 2024-04-07 00:02:16.746 - [任务 5] - Task initialization... 
[INFO ] 2024-04-07 00:02:16.747 - [任务 5] - Start task milestones: 66116ee1b896963849b34788(任务 5) 
[INFO ] 2024-04-07 00:02:16.747 - [任务 5] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-04-07 00:02:16.748 - [任务 5] - The engine receives 任务 5 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 00:02:22.240 - [任务 5][KafkaTest1] - Node KafkaTest1[eae0cfd1-ce67-431f-9edc-ac49bd120d6f] start preload schema,table counts: 1 
[INFO ] 2024-04-07 00:02:22.242 - [任务 5][dummy_test] - Node dummy_test[0b505a19-5093-4623-b295-f19511fea3a0] start preload schema,table counts: 1 
[INFO ] 2024-04-07 00:02:22.345 - [任务 5][dummy_test] - Node dummy_test[0b505a19-5093-4623-b295-f19511fea3a0] preload schema finished, cost 104 ms 
[INFO ] 2024-04-07 00:02:22.346 - [任务 5][KafkaTest1] - Node KafkaTest1[eae0cfd1-ce67-431f-9edc-ac49bd120d6f] preload schema finished, cost 107 ms 
[INFO ] 2024-04-07 00:02:23.327 - [任务 5][dummy_test] - Start dummy connector 
[INFO ] 2024-04-07 00:02:23.333 - [任务 5][dummy_test] - Source node "dummy_test" read batch size: 100 
[INFO ] 2024-04-07 00:02:23.339 - [任务 5][dummy_test] - Source node "dummy_test" event queue capacity: 200 
[INFO ] 2024-04-07 00:02:23.341 - [任务 5][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 00:02:23.553 - [任务 5][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1712419343331,"lastTimes":1712419343331,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-04-07 00:02:23.596 - [任务 5][dummy_test] - Initial sync started 
[INFO ] 2024-04-07 00:02:23.608 - [任务 5][dummy_test] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-04-07 00:02:23.609 - [任务 5][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-04-07 00:02:23.620 - [任务 5][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-04-07 00:02:23.624 - [任务 5][dummy_test] - Query table 'dummy_test' counts: 1000 
[INFO ] 2024-04-07 00:02:23.937 - [任务 5][KafkaTest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 00:02:24.111 - [任务 5][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-04-07 00:02:24.116 - [任务 5][dummy_test] - Initial sync completed 
[INFO ] 2024-04-07 00:02:24.119 - [任务 5][dummy_test] - Incremental sync starting... 
[INFO ] 2024-04-07 00:02:24.120 - [任务 5][dummy_test] - Initial sync completed 
[INFO ] 2024-04-07 00:02:24.125 - [任务 5][dummy_test] - Starting stream read, table list: [dummy_test], offset: {"syncStage":null,"beginTimes":1712419343331,"lastTimes":1712419343331,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-04-07 00:02:24.130 - [任务 5][dummy_test] - Start [dummy_test] stream read 
[INFO ] 2024-04-07 00:02:24.130 - [任务 5][dummy_test] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[INFO ] 2024-04-07 00:02:39.772 - [任务 5] - Stop task milestones: 66116ee1b896963849b34788(任务 5)  
[INFO ] 2024-04-07 00:02:39.788 - [任务 5][dummy_test] - Node dummy_test[0b505a19-5093-4623-b295-f19511fea3a0] running status set to false 
[INFO ] 2024-04-07 00:02:39.789 - [任务 5][dummy_test] - Stop connector 
[INFO ] 2024-04-07 00:02:39.808 - [任务 5][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-0b505a19-5093-4623-b295-f19511fea3a0 
[INFO ] 2024-04-07 00:02:39.808 - [任务 5][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-0b505a19-5093-4623-b295-f19511fea3a0 
[INFO ] 2024-04-07 00:02:39.814 - [任务 5][dummy_test] - Node dummy_test[0b505a19-5093-4623-b295-f19511fea3a0] schema data cleaned 
[INFO ] 2024-04-07 00:02:39.814 - [任务 5][dummy_test] - Node dummy_test[0b505a19-5093-4623-b295-f19511fea3a0] monitor closed 
[INFO ] 2024-04-07 00:02:39.823 - [任务 5][dummy_test] - Node dummy_test[0b505a19-5093-4623-b295-f19511fea3a0] close complete, cost 36 ms 
[INFO ] 2024-04-07 00:02:39.824 - [任务 5][KafkaTest1] - Node KafkaTest1[eae0cfd1-ce67-431f-9edc-ac49bd120d6f] running status set to false 
[INFO ] 2024-04-07 00:02:40.005 - [任务 5][KafkaTest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-eae0cfd1-ce67-431f-9edc-ac49bd120d6f 
[INFO ] 2024-04-07 00:02:40.006 - [任务 5][KafkaTest1] - PDK connector node released: HazelcastTargetPdkDataNode-eae0cfd1-ce67-431f-9edc-ac49bd120d6f 
[INFO ] 2024-04-07 00:02:40.006 - [任务 5][KafkaTest1] - Node KafkaTest1[eae0cfd1-ce67-431f-9edc-ac49bd120d6f] schema data cleaned 
[INFO ] 2024-04-07 00:02:40.006 - [任务 5][KafkaTest1] - Node KafkaTest1[eae0cfd1-ce67-431f-9edc-ac49bd120d6f] monitor closed 
[INFO ] 2024-04-07 00:02:40.007 - [任务 5][KafkaTest1] - Node KafkaTest1[eae0cfd1-ce67-431f-9edc-ac49bd120d6f] close complete, cost 184 ms 
[INFO ] 2024-04-07 00:02:43.101 - [任务 5] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 00:02:43.104 - [任务 5] - Stopped task aspect(s) 
[INFO ] 2024-04-07 00:02:43.105 - [任务 5] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 00:02:43.217 - [任务 5] - Remove memory task client succeed, task: 任务 5[66116ee1b896963849b34788] 
[INFO ] 2024-04-07 00:02:43.217 - [任务 5] - Destroy memory task client cache succeed, task: 任务 5[66116ee1b896963849b34788] 
