[INFO ] 2024-10-11 00:21:15.855 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:21:15.855 - [任务 4(100)][35db412e-2445-4eec-8560-5b1c1481ee4b] - Node 35db412e-2445-4eec-8560-5b1c1481ee4b[35db412e-2445-4eec-8560-5b1c1481ee4b] start preload schema,table counts: 0 
[INFO ] 2024-10-11 00:21:15.855 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:21:15.855 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:21:15.855 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:21:15.855 - [任务 4(100)][35db412e-2445-4eec-8560-5b1c1481ee4b] - Node 35db412e-2445-4eec-8560-5b1c1481ee4b[35db412e-2445-4eec-8560-5b1c1481ee4b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:21:15.855 - [任务 4(100)][增强JS] - Node js_processor(增强JS: d4d38d76-bfb5-4fb0-94ba-477a8e43dbde) enable batch process 
[INFO ] 2024-10-11 00:21:16.770 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:21:16.770 - [任务 4(100)][417cf8ac-e95a-4bba-a878-aa4840a9d64c] - Node 417cf8ac-e95a-4bba-a878-aa4840a9d64c[417cf8ac-e95a-4bba-a878-aa4840a9d64c] start preload schema,table counts: 0 
[INFO ] 2024-10-11 00:21:16.770 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:21:16.770 - [任务 4(100)][417cf8ac-e95a-4bba-a878-aa4840a9d64c] - Node 417cf8ac-e95a-4bba-a878-aa4840a9d64c[417cf8ac-e95a-4bba-a878-aa4840a9d64c] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:21:16.771 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:21:16.771 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:21:16.771 - [任务 4(100)][增强JS] - Node js_processor(增强JS: d4d38d76-bfb5-4fb0-94ba-477a8e43dbde) enable batch process 
[INFO ] 2024-10-11 00:21:21.786 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] running status set to false 
[INFO ] 2024-10-11 00:21:21.873 - [任务 4(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:21:21.883 - [任务 4(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:21:21.883 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] schema data cleaned 
[INFO ] 2024-10-11 00:21:21.885 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] monitor closed 
[INFO ] 2024-10-11 00:21:21.885 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] close complete, cost 109 ms 
[INFO ] 2024-10-11 00:21:27.144 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] running status set to false 
[INFO ] 2024-10-11 00:21:27.205 - [任务 4(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:21:27.213 - [任务 4(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:21:27.214 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] schema data cleaned 
[INFO ] 2024-10-11 00:21:27.215 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] monitor closed 
[INFO ] 2024-10-11 00:21:27.417 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] close complete, cost 102 ms 
[INFO ] 2024-10-11 00:21:27.631 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:21:27.632 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:21:27.632 - [任务 4(100)][3b892d4f-70f3-4da8-8c83-e25d7031561f] - Node 3b892d4f-70f3-4da8-8c83-e25d7031561f[3b892d4f-70f3-4da8-8c83-e25d7031561f] start preload schema,table counts: 0 
[INFO ] 2024-10-11 00:21:27.632 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:21:27.632 - [任务 4(100)][3b892d4f-70f3-4da8-8c83-e25d7031561f] - Node 3b892d4f-70f3-4da8-8c83-e25d7031561f[3b892d4f-70f3-4da8-8c83-e25d7031561f] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:21:27.632 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 00:21:27.634 - [任务 4(100)][增强JS] - Node js_processor(增强JS: d4d38d76-bfb5-4fb0-94ba-477a8e43dbde) enable batch process 
[INFO ] 2024-10-11 00:21:28.327 - [任务 4(100)][35db412e-2445-4eec-8560-5b1c1481ee4b] - Node 35db412e-2445-4eec-8560-5b1c1481ee4b[35db412e-2445-4eec-8560-5b1c1481ee4b] running status set to false 
[INFO ] 2024-10-11 00:21:28.344 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] running status set to false 
[INFO ] 2024-10-11 00:21:28.344 - [任务 4(100)][35db412e-2445-4eec-8560-5b1c1481ee4b] - Node 35db412e-2445-4eec-8560-5b1c1481ee4b[35db412e-2445-4eec-8560-5b1c1481ee4b] schema data cleaned 
[INFO ] 2024-10-11 00:21:28.344 - [任务 4(100)][35db412e-2445-4eec-8560-5b1c1481ee4b] - Node 35db412e-2445-4eec-8560-5b1c1481ee4b[35db412e-2445-4eec-8560-5b1c1481ee4b] monitor closed 
[INFO ] 2024-10-11 00:21:28.344 - [任务 4(100)][35db412e-2445-4eec-8560-5b1c1481ee4b] - Node 35db412e-2445-4eec-8560-5b1c1481ee4b[35db412e-2445-4eec-8560-5b1c1481ee4b] close complete, cost 10 ms 
[INFO ] 2024-10-11 00:21:28.363 - [任务 4(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-71fd97f5-e9d1-4af1-8830-25184b930bda 
[INFO ] 2024-10-11 00:21:28.364 - [任务 4(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-71fd97f5-e9d1-4af1-8830-25184b930bda 
[INFO ] 2024-10-11 00:21:28.378 - [任务 4(100)][增强JS] - [ScriptExecutorsManager-6707feed82af0a589c4e5934-d4d38d76-bfb5-4fb0-94ba-477a8e43dbde-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 00:21:28.379 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] schema data cleaned 
[INFO ] 2024-10-11 00:21:28.379 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] monitor closed 
[INFO ] 2024-10-11 00:21:28.392 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] close complete, cost 169 ms 
[INFO ] 2024-10-11 00:21:28.397 - [任务 4(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 00:21:28.397 - [任务 4(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 00:21:28.397 - [任务 4(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 00:21:33.368 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] running status set to false 
[INFO ] 2024-10-11 00:21:33.412 - [任务 4(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:21:33.414 - [任务 4(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:21:33.418 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] schema data cleaned 
[INFO ] 2024-10-11 00:21:33.418 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] monitor closed 
[INFO ] 2024-10-11 00:21:33.418 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] close complete, cost 58 ms 
[INFO ] 2024-10-11 00:21:38.749 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] running status set to false 
[INFO ] 2024-10-11 00:21:38.754 - [任务 4(100)][417cf8ac-e95a-4bba-a878-aa4840a9d64c] - Node 417cf8ac-e95a-4bba-a878-aa4840a9d64c[417cf8ac-e95a-4bba-a878-aa4840a9d64c] running status set to false 
[INFO ] 2024-10-11 00:21:38.759 - [任务 4(100)][417cf8ac-e95a-4bba-a878-aa4840a9d64c] - Node 417cf8ac-e95a-4bba-a878-aa4840a9d64c[417cf8ac-e95a-4bba-a878-aa4840a9d64c] schema data cleaned 
[INFO ] 2024-10-11 00:21:38.769 - [任务 4(100)][417cf8ac-e95a-4bba-a878-aa4840a9d64c] - Node 417cf8ac-e95a-4bba-a878-aa4840a9d64c[417cf8ac-e95a-4bba-a878-aa4840a9d64c] monitor closed 
[INFO ] 2024-10-11 00:21:38.769 - [任务 4(100)][417cf8ac-e95a-4bba-a878-aa4840a9d64c] - Node 417cf8ac-e95a-4bba-a878-aa4840a9d64c[417cf8ac-e95a-4bba-a878-aa4840a9d64c] close complete, cost 6 ms 
[INFO ] 2024-10-11 00:21:38.787 - [任务 4(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-2548a879-1ab2-432c-a77e-93e0c450c849 
[INFO ] 2024-10-11 00:21:38.788 - [任务 4(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-2548a879-1ab2-432c-a77e-93e0c450c849 
[INFO ] 2024-10-11 00:21:38.791 - [任务 4(100)][增强JS] - [ScriptExecutorsManager-6707feed82af0a589c4e5934-d4d38d76-bfb5-4fb0-94ba-477a8e43dbde-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 00:21:38.792 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] schema data cleaned 
[INFO ] 2024-10-11 00:21:38.792 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] monitor closed 
[INFO ] 2024-10-11 00:21:38.796 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] close complete, cost 55 ms 
[INFO ] 2024-10-11 00:21:38.797 - [任务 4(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 00:21:38.797 - [任务 4(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 00:21:38.845 - [任务 4(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 00:21:38.847 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] running status set to false 
[INFO ] 2024-10-11 00:21:38.868 - [任务 4(100)][3b892d4f-70f3-4da8-8c83-e25d7031561f] - Node 3b892d4f-70f3-4da8-8c83-e25d7031561f[3b892d4f-70f3-4da8-8c83-e25d7031561f] running status set to false 
[INFO ] 2024-10-11 00:21:38.868 - [任务 4(100)][3b892d4f-70f3-4da8-8c83-e25d7031561f] - Node 3b892d4f-70f3-4da8-8c83-e25d7031561f[3b892d4f-70f3-4da8-8c83-e25d7031561f] schema data cleaned 
[INFO ] 2024-10-11 00:21:38.870 - [任务 4(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-eb04ce9b-9970-4b4e-b795-ada6c9c86b89 
[INFO ] 2024-10-11 00:21:38.870 - [任务 4(100)][3b892d4f-70f3-4da8-8c83-e25d7031561f] - Node 3b892d4f-70f3-4da8-8c83-e25d7031561f[3b892d4f-70f3-4da8-8c83-e25d7031561f] monitor closed 
[INFO ] 2024-10-11 00:21:38.870 - [任务 4(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-eb04ce9b-9970-4b4e-b795-ada6c9c86b89 
[INFO ] 2024-10-11 00:21:38.870 - [任务 4(100)][增强JS] - [ScriptExecutorsManager-6707feed82af0a589c4e5934-d4d38d76-bfb5-4fb0-94ba-477a8e43dbde-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 00:21:38.871 - [任务 4(100)][3b892d4f-70f3-4da8-8c83-e25d7031561f] - Node 3b892d4f-70f3-4da8-8c83-e25d7031561f[3b892d4f-70f3-4da8-8c83-e25d7031561f] close complete, cost 24 ms 
[INFO ] 2024-10-11 00:21:38.877 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] schema data cleaned 
[INFO ] 2024-10-11 00:21:38.880 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] monitor closed 
[INFO ] 2024-10-11 00:21:38.880 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] close complete, cost 37 ms 
[INFO ] 2024-10-11 00:21:38.881 - [任务 4(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 00:21:38.881 - [任务 4(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 00:21:38.881 - [任务 4(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 00:39:40.991 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:39:40.992 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:39:40.993 - [任务 4(100)][936576e3-3079-45ad-b600-afbe68a738bd] - Node 936576e3-3079-45ad-b600-afbe68a738bd[936576e3-3079-45ad-b600-afbe68a738bd] start preload schema,table counts: 0 
[INFO ] 2024-10-11 00:39:40.993 - [任务 4(100)][5b1381be-a889-4da4-89ec-25cb5f24ed45] - Node 5b1381be-a889-4da4-89ec-25cb5f24ed45[5b1381be-a889-4da4-89ec-25cb5f24ed45] start preload schema,table counts: 0 
[INFO ] 2024-10-11 00:39:40.993 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:39:40.993 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:39:40.993 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] preload schema finished, cost 3 ms 
[INFO ] 2024-10-11 00:39:40.993 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:39:40.993 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:39:40.993 - [任务 4(100)][936576e3-3079-45ad-b600-afbe68a738bd] - Node 936576e3-3079-45ad-b600-afbe68a738bd[936576e3-3079-45ad-b600-afbe68a738bd] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 00:39:40.993 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 00:39:40.993 - [任务 4(100)][5b1381be-a889-4da4-89ec-25cb5f24ed45] - Node 5b1381be-a889-4da4-89ec-25cb5f24ed45[5b1381be-a889-4da4-89ec-25cb5f24ed45] preload schema finished, cost 2 ms 
[INFO ] 2024-10-11 00:39:40.999 - [任务 4(100)][增强JS] - Node js_processor(增强JS: d4d38d76-bfb5-4fb0-94ba-477a8e43dbde) enable batch process 
[INFO ] 2024-10-11 00:39:41.203 - [任务 4(100)][增强JS] - Node js_processor(增强JS: d4d38d76-bfb5-4fb0-94ba-477a8e43dbde) enable batch process 
[INFO ] 2024-10-11 00:39:46.584 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] running status set to false 
[INFO ] 2024-10-11 00:39:46.663 - [任务 4(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:39:46.664 - [任务 4(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:39:46.664 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] schema data cleaned 
[INFO ] 2024-10-11 00:39:46.664 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] monitor closed 
[INFO ] 2024-10-11 00:39:46.664 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] close complete, cost 102 ms 
[INFO ] 2024-10-11 00:39:46.708 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:39:46.709 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:39:46.709 - [任务 4(100)][c5a94c20-53f9-4117-a605-9f3bb65e6d35] - Node c5a94c20-53f9-4117-a605-9f3bb65e6d35[c5a94c20-53f9-4117-a605-9f3bb65e6d35] start preload schema,table counts: 0 
[INFO ] 2024-10-11 00:39:46.709 - [任务 4(100)][c5a94c20-53f9-4117-a605-9f3bb65e6d35] - Node c5a94c20-53f9-4117-a605-9f3bb65e6d35[c5a94c20-53f9-4117-a605-9f3bb65e6d35] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:39:46.709 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:39:46.710 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 00:39:46.710 - [任务 4(100)][增强JS] - Node js_processor(增强JS: d4d38d76-bfb5-4fb0-94ba-477a8e43dbde) enable batch process 
[INFO ] 2024-10-11 00:39:52.913 - [任务 4(100)][BMSQL_ITEM] - Exception skipping - The current exception does not match the skip exception strategy, message: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:39:52.920 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] running status set to false 
[ERROR] 2024-10-11 00:39:53.024 - [任务 4(100)][BMSQL_ITEM] - start source runner failed: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f
	io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:573)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:191)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:61)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:207)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.initNode(HazelcastSampleSourcePdkDataNode.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:85)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f
	at io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:221)
	... 17 more

[INFO ] 2024-10-11 00:39:53.103 - [任务 4(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:39:53.104 - [任务 4(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:39:53.104 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] schema data cleaned 
[INFO ] 2024-10-11 00:39:53.105 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] monitor closed 
[INFO ] 2024-10-11 00:39:53.106 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] close complete, cost 235 ms 
[INFO ] 2024-10-11 00:39:55.596 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] running status set to false 
[INFO ] 2024-10-11 00:39:55.597 - [任务 4(100)][BMSQL_ITEM] - PDK connector node stopped: null 
[INFO ] 2024-10-11 00:39:55.597 - [任务 4(100)][BMSQL_ITEM] - PDK connector node released: null 
[INFO ] 2024-10-11 00:39:55.597 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] schema data cleaned 
[INFO ] 2024-10-11 00:39:55.597 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] monitor closed 
[INFO ] 2024-10-11 00:39:55.598 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] close complete, cost 5 ms 
[INFO ] 2024-10-11 00:39:55.598 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] running status set to false 
[INFO ] 2024-10-11 00:39:55.600 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] schema data cleaned 
[INFO ] 2024-10-11 00:39:55.601 - [任务 4(100)][c5a94c20-53f9-4117-a605-9f3bb65e6d35] - Node c5a94c20-53f9-4117-a605-9f3bb65e6d35[c5a94c20-53f9-4117-a605-9f3bb65e6d35] running status set to false 
[INFO ] 2024-10-11 00:39:55.601 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] monitor closed 
[INFO ] 2024-10-11 00:39:55.601 - [任务 4(100)][c5a94c20-53f9-4117-a605-9f3bb65e6d35] - Node c5a94c20-53f9-4117-a605-9f3bb65e6d35[c5a94c20-53f9-4117-a605-9f3bb65e6d35] schema data cleaned 
[INFO ] 2024-10-11 00:39:55.602 - [任务 4(100)][c5a94c20-53f9-4117-a605-9f3bb65e6d35] - Node c5a94c20-53f9-4117-a605-9f3bb65e6d35[c5a94c20-53f9-4117-a605-9f3bb65e6d35] monitor closed 
[INFO ] 2024-10-11 00:39:55.603 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] close complete, cost 4 ms 
[INFO ] 2024-10-11 00:39:55.603 - [任务 4(100)][c5a94c20-53f9-4117-a605-9f3bb65e6d35] - Node c5a94c20-53f9-4117-a605-9f3bb65e6d35[c5a94c20-53f9-4117-a605-9f3bb65e6d35] close complete, cost 3 ms 
[INFO ] 2024-10-11 00:39:55.606 - [任务 4(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 00:39:55.606 - [任务 4(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 00:39:55.812 - [任务 4(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 00:39:58.328 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] running status set to false 
[INFO ] 2024-10-11 00:39:58.329 - [任务 4(100)][5b1381be-a889-4da4-89ec-25cb5f24ed45] - Node 5b1381be-a889-4da4-89ec-25cb5f24ed45[5b1381be-a889-4da4-89ec-25cb5f24ed45] running status set to false 
[INFO ] 2024-10-11 00:39:58.329 - [任务 4(100)][5b1381be-a889-4da4-89ec-25cb5f24ed45] - Node 5b1381be-a889-4da4-89ec-25cb5f24ed45[5b1381be-a889-4da4-89ec-25cb5f24ed45] schema data cleaned 
[INFO ] 2024-10-11 00:39:58.330 - [任务 4(100)][5b1381be-a889-4da4-89ec-25cb5f24ed45] - Node 5b1381be-a889-4da4-89ec-25cb5f24ed45[5b1381be-a889-4da4-89ec-25cb5f24ed45] monitor closed 
[INFO ] 2024-10-11 00:39:58.330 - [任务 4(100)][5b1381be-a889-4da4-89ec-25cb5f24ed45] - Node 5b1381be-a889-4da4-89ec-25cb5f24ed45[5b1381be-a889-4da4-89ec-25cb5f24ed45] close complete, cost 3 ms 
[INFO ] 2024-10-11 00:39:58.360 - [任务 4(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-0310d772-8f66-4cf4-80cf-d7fb9300cd45 
[INFO ] 2024-10-11 00:39:58.361 - [任务 4(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-0310d772-8f66-4cf4-80cf-d7fb9300cd45 
[INFO ] 2024-10-11 00:39:58.361 - [任务 4(100)][增强JS] - [ScriptExecutorsManager-6707feed82af0a589c4e5934-d4d38d76-bfb5-4fb0-94ba-477a8e43dbde-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 00:39:58.364 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] schema data cleaned 
[INFO ] 2024-10-11 00:39:58.364 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] monitor closed 
[INFO ] 2024-10-11 00:39:58.367 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] close complete, cost 49 ms 
[INFO ] 2024-10-11 00:39:58.368 - [任务 4(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 00:39:58.368 - [任务 4(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 00:39:58.578 - [任务 4(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 00:40:00.651 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] running status set to false 
[INFO ] 2024-10-11 00:40:00.653 - [任务 4(100)][936576e3-3079-45ad-b600-afbe68a738bd] - Node 936576e3-3079-45ad-b600-afbe68a738bd[936576e3-3079-45ad-b600-afbe68a738bd] running status set to false 
[INFO ] 2024-10-11 00:40:00.653 - [任务 4(100)][936576e3-3079-45ad-b600-afbe68a738bd] - Node 936576e3-3079-45ad-b600-afbe68a738bd[936576e3-3079-45ad-b600-afbe68a738bd] schema data cleaned 
[INFO ] 2024-10-11 00:40:00.653 - [任务 4(100)][936576e3-3079-45ad-b600-afbe68a738bd] - Node 936576e3-3079-45ad-b600-afbe68a738bd[936576e3-3079-45ad-b600-afbe68a738bd] monitor closed 
[INFO ] 2024-10-11 00:40:00.653 - [任务 4(100)][936576e3-3079-45ad-b600-afbe68a738bd] - Node 936576e3-3079-45ad-b600-afbe68a738bd[936576e3-3079-45ad-b600-afbe68a738bd] close complete, cost 14 ms 
[INFO ] 2024-10-11 00:40:00.692 - [任务 4(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-96bc5d0c-0d92-4707-833d-5c0c2ec8d0b5 
[INFO ] 2024-10-11 00:40:00.693 - [任务 4(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-96bc5d0c-0d92-4707-833d-5c0c2ec8d0b5 
[INFO ] 2024-10-11 00:40:00.693 - [任务 4(100)][增强JS] - [ScriptExecutorsManager-6707feed82af0a589c4e5934-d4d38d76-bfb5-4fb0-94ba-477a8e43dbde-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 00:40:00.698 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] schema data cleaned 
[INFO ] 2024-10-11 00:40:00.699 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] monitor closed 
[INFO ] 2024-10-11 00:40:00.701 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] close complete, cost 64 ms 
[INFO ] 2024-10-11 00:40:00.703 - [任务 4(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 00:40:00.704 - [任务 4(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 00:40:00.704 - [任务 4(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 00:40:32.497 - [任务 4(100)][69ff1171-3863-43e5-adb8-2288940ab17e] - Node 69ff1171-3863-43e5-adb8-2288940ab17e[69ff1171-3863-43e5-adb8-2288940ab17e] start preload schema,table counts: 0 
[INFO ] 2024-10-11 00:40:32.498 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:40:32.498 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:40:32.498 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:40:32.498 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:40:32.498 - [任务 4(100)][69ff1171-3863-43e5-adb8-2288940ab17e] - Node 69ff1171-3863-43e5-adb8-2288940ab17e[69ff1171-3863-43e5-adb8-2288940ab17e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:40:32.499 - [任务 4(100)][增强JS] - Node js_processor(增强JS: d4d38d76-bfb5-4fb0-94ba-477a8e43dbde) enable batch process 
[INFO ] 2024-10-11 00:40:35.497 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:40:35.497 - [任务 4(100)][32463856-9d1a-47ec-9095-5fc1d6f07f8f] - Node 32463856-9d1a-47ec-9095-5fc1d6f07f8f[32463856-9d1a-47ec-9095-5fc1d6f07f8f] start preload schema,table counts: 0 
[INFO ] 2024-10-11 00:40:35.497 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:40:35.497 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:40:35.497 - [任务 4(100)][32463856-9d1a-47ec-9095-5fc1d6f07f8f] - Node 32463856-9d1a-47ec-9095-5fc1d6f07f8f[32463856-9d1a-47ec-9095-5fc1d6f07f8f] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:40:35.497 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:40:35.702 - [任务 4(100)][增强JS] - Node js_processor(增强JS: d4d38d76-bfb5-4fb0-94ba-477a8e43dbde) enable batch process 
[INFO ] 2024-10-11 00:40:36.372 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:40:36.372 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:40:36.372 - [任务 4(100)][af6342e2-efda-4203-9046-467158365171] - Node af6342e2-efda-4203-9046-467158365171[af6342e2-efda-4203-9046-467158365171] start preload schema,table counts: 0 
[INFO ] 2024-10-11 00:40:36.372 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:40:36.372 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:40:36.373 - [任务 4(100)][af6342e2-efda-4203-9046-467158365171] - Node af6342e2-efda-4203-9046-467158365171[af6342e2-efda-4203-9046-467158365171] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:40:36.373 - [任务 4(100)][增强JS] - Node js_processor(增强JS: d4d38d76-bfb5-4fb0-94ba-477a8e43dbde) enable batch process 
[INFO ] 2024-10-11 00:40:38.135 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] running status set to false 
[INFO ] 2024-10-11 00:40:38.205 - [任务 4(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:40:38.206 - [任务 4(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:40:38.206 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] schema data cleaned 
[INFO ] 2024-10-11 00:40:38.207 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] monitor closed 
[INFO ] 2024-10-11 00:40:38.208 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] close complete, cost 99 ms 
[INFO ] 2024-10-11 00:40:43.473 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] running status set to false 
[INFO ] 2024-10-11 00:40:43.499 - [任务 4(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:40:43.500 - [任务 4(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:40:43.500 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] schema data cleaned 
[INFO ] 2024-10-11 00:40:43.500 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] monitor closed 
[INFO ] 2024-10-11 00:40:43.582 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] close complete, cost 74 ms 
[INFO ] 2024-10-11 00:40:43.582 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] running status set to false 
[INFO ] 2024-10-11 00:40:43.583 - [任务 4(100)][69ff1171-3863-43e5-adb8-2288940ab17e] - Node 69ff1171-3863-43e5-adb8-2288940ab17e[69ff1171-3863-43e5-adb8-2288940ab17e] running status set to false 
[INFO ] 2024-10-11 00:40:43.595 - [任务 4(100)][69ff1171-3863-43e5-adb8-2288940ab17e] - Node 69ff1171-3863-43e5-adb8-2288940ab17e[69ff1171-3863-43e5-adb8-2288940ab17e] schema data cleaned 
[INFO ] 2024-10-11 00:40:43.599 - [任务 4(100)][69ff1171-3863-43e5-adb8-2288940ab17e] - Node 69ff1171-3863-43e5-adb8-2288940ab17e[69ff1171-3863-43e5-adb8-2288940ab17e] monitor closed 
[INFO ] 2024-10-11 00:40:43.599 - [任务 4(100)][69ff1171-3863-43e5-adb8-2288940ab17e] - Node 69ff1171-3863-43e5-adb8-2288940ab17e[69ff1171-3863-43e5-adb8-2288940ab17e] close complete, cost 3 ms 
[INFO ] 2024-10-11 00:40:43.618 - [任务 4(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-a935793e-7542-47b2-ba3e-7207d371aaea 
[INFO ] 2024-10-11 00:40:43.618 - [任务 4(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-a935793e-7542-47b2-ba3e-7207d371aaea 
[INFO ] 2024-10-11 00:40:43.624 - [任务 4(100)][增强JS] - [ScriptExecutorsManager-6707feed82af0a589c4e5934-d4d38d76-bfb5-4fb0-94ba-477a8e43dbde-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 00:40:43.624 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] schema data cleaned 
[INFO ] 2024-10-11 00:40:43.624 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] monitor closed 
[INFO ] 2024-10-11 00:40:43.624 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] close complete, cost 49 ms 
[INFO ] 2024-10-11 00:40:43.631 - [任务 4(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 00:40:43.631 - [任务 4(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 00:40:43.839 - [任务 4(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 00:40:45.159 - [任务 4(100)][28e1873f-3487-476b-8584-04c216158ae5] - Node 28e1873f-3487-476b-8584-04c216158ae5[28e1873f-3487-476b-8584-04c216158ae5] start preload schema,table counts: 0 
[INFO ] 2024-10-11 00:40:45.159 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:40:45.160 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:40:45.160 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:40:45.160 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:40:45.160 - [任务 4(100)][28e1873f-3487-476b-8584-04c216158ae5] - Node 28e1873f-3487-476b-8584-04c216158ae5[28e1873f-3487-476b-8584-04c216158ae5] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:40:45.160 - [任务 4(100)][增强JS] - Node js_processor(增强JS: d4d38d76-bfb5-4fb0-94ba-477a8e43dbde) enable batch process 
[INFO ] 2024-10-11 00:40:45.215 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:40:45.215 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:40:45.236 - [任务 4(100)][82f98d60-1557-4325-b8b8-19a69a50a2fa] - Node 82f98d60-1557-4325-b8b8-19a69a50a2fa[82f98d60-1557-4325-b8b8-19a69a50a2fa] start preload schema,table counts: 0 
[INFO ] 2024-10-11 00:40:45.237 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:40:45.237 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:40:45.237 - [任务 4(100)][82f98d60-1557-4325-b8b8-19a69a50a2fa] - Node 82f98d60-1557-4325-b8b8-19a69a50a2fa[82f98d60-1557-4325-b8b8-19a69a50a2fa] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:40:45.237 - [任务 4(100)][增强JS] - Node js_processor(增强JS: d4d38d76-bfb5-4fb0-94ba-477a8e43dbde) enable batch process 
[INFO ] 2024-10-11 00:40:46.951 - [任务 4(100)][6c0ab3a7-1a16-4f92-972d-40496df166f3] - Node 6c0ab3a7-1a16-4f92-972d-40496df166f3[6c0ab3a7-1a16-4f92-972d-40496df166f3] start preload schema,table counts: 0 
[INFO ] 2024-10-11 00:40:46.952 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:40:46.952 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:40:46.952 - [任务 4(100)][6c0ab3a7-1a16-4f92-972d-40496df166f3] - Node 6c0ab3a7-1a16-4f92-972d-40496df166f3[6c0ab3a7-1a16-4f92-972d-40496df166f3] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:40:46.952 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:40:46.952 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:40:46.954 - [任务 4(100)][增强JS] - Node js_processor(增强JS: d4d38d76-bfb5-4fb0-94ba-477a8e43dbde) enable batch process 
[INFO ] 2024-10-11 00:40:48.772 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] running status set to false 
[INFO ] 2024-10-11 00:40:48.833 - [任务 4(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:40:48.833 - [任务 4(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:40:48.834 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] schema data cleaned 
[INFO ] 2024-10-11 00:40:48.835 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] monitor closed 
[INFO ] 2024-10-11 00:40:49.040 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] close complete, cost 76 ms 
[INFO ] 2024-10-11 00:40:49.553 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] running status set to false 
[INFO ] 2024-10-11 00:40:49.555 - [任务 4(100)][af6342e2-efda-4203-9046-467158365171] - Node af6342e2-efda-4203-9046-467158365171[af6342e2-efda-4203-9046-467158365171] running status set to false 
[INFO ] 2024-10-11 00:40:49.555 - [任务 4(100)][af6342e2-efda-4203-9046-467158365171] - Node af6342e2-efda-4203-9046-467158365171[af6342e2-efda-4203-9046-467158365171] schema data cleaned 
[INFO ] 2024-10-11 00:40:49.555 - [任务 4(100)][af6342e2-efda-4203-9046-467158365171] - Node af6342e2-efda-4203-9046-467158365171[af6342e2-efda-4203-9046-467158365171] monitor closed 
[INFO ] 2024-10-11 00:40:49.555 - [任务 4(100)][af6342e2-efda-4203-9046-467158365171] - Node af6342e2-efda-4203-9046-467158365171[af6342e2-efda-4203-9046-467158365171] close complete, cost 12 ms 
[INFO ] 2024-10-11 00:40:49.600 - [任务 4(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-00741419-a000-4849-9ba6-84fc338151db 
[INFO ] 2024-10-11 00:40:49.601 - [任务 4(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-00741419-a000-4849-9ba6-84fc338151db 
[INFO ] 2024-10-11 00:40:49.601 - [任务 4(100)][增强JS] - [ScriptExecutorsManager-6707feed82af0a589c4e5934-d4d38d76-bfb5-4fb0-94ba-477a8e43dbde-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 00:40:49.607 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] schema data cleaned 
[INFO ] 2024-10-11 00:40:49.610 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] monitor closed 
[INFO ] 2024-10-11 00:40:49.611 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] close complete, cost 69 ms 
[INFO ] 2024-10-11 00:40:49.614 - [任务 4(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 00:40:49.614 - [任务 4(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 00:40:49.616 - [任务 4(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 00:40:54.813 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] running status set to false 
[INFO ] 2024-10-11 00:40:54.815 - [任务 4(100)][32463856-9d1a-47ec-9095-5fc1d6f07f8f] - Node 32463856-9d1a-47ec-9095-5fc1d6f07f8f[32463856-9d1a-47ec-9095-5fc1d6f07f8f] running status set to false 
[INFO ] 2024-10-11 00:40:54.815 - [任务 4(100)][32463856-9d1a-47ec-9095-5fc1d6f07f8f] - Node 32463856-9d1a-47ec-9095-5fc1d6f07f8f[32463856-9d1a-47ec-9095-5fc1d6f07f8f] schema data cleaned 
[INFO ] 2024-10-11 00:40:54.815 - [任务 4(100)][32463856-9d1a-47ec-9095-5fc1d6f07f8f] - Node 32463856-9d1a-47ec-9095-5fc1d6f07f8f[32463856-9d1a-47ec-9095-5fc1d6f07f8f] monitor closed 
[INFO ] 2024-10-11 00:40:54.815 - [任务 4(100)][32463856-9d1a-47ec-9095-5fc1d6f07f8f] - Node 32463856-9d1a-47ec-9095-5fc1d6f07f8f[32463856-9d1a-47ec-9095-5fc1d6f07f8f] close complete, cost 8 ms 
[INFO ] 2024-10-11 00:40:54.839 - [任务 4(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-20309cad-1656-4ead-b05a-a36cd201efcb 
[INFO ] 2024-10-11 00:40:54.839 - [任务 4(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-20309cad-1656-4ead-b05a-a36cd201efcb 
[INFO ] 2024-10-11 00:40:54.839 - [任务 4(100)][增强JS] - [ScriptExecutorsManager-6707feed82af0a589c4e5934-d4d38d76-bfb5-4fb0-94ba-477a8e43dbde-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 00:40:54.843 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] schema data cleaned 
[INFO ] 2024-10-11 00:40:54.843 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] monitor closed 
[INFO ] 2024-10-11 00:40:54.848 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] close complete, cost 44 ms 
[INFO ] 2024-10-11 00:40:54.848 - [任务 4(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 00:40:54.848 - [任务 4(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 00:40:54.848 - [任务 4(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 00:41:00.187 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] running status set to false 
[INFO ] 2024-10-11 00:41:00.234 - [任务 4(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:41:00.234 - [任务 4(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:41:00.235 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] schema data cleaned 
[INFO ] 2024-10-11 00:41:00.236 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] monitor closed 
[INFO ] 2024-10-11 00:41:00.237 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] close complete, cost 65 ms 
[INFO ] 2024-10-11 00:41:05.624 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] running status set to false 
[INFO ] 2024-10-11 00:41:05.663 - [任务 4(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:41:05.663 - [任务 4(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:41:05.664 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] schema data cleaned 
[INFO ] 2024-10-11 00:41:05.664 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] monitor closed 
[INFO ] 2024-10-11 00:41:05.665 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] close complete, cost 54 ms 
[INFO ] 2024-10-11 00:41:05.816 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] running status set to false 
[INFO ] 2024-10-11 00:41:05.817 - [任务 4(100)][6c0ab3a7-1a16-4f92-972d-40496df166f3] - Node 6c0ab3a7-1a16-4f92-972d-40496df166f3[6c0ab3a7-1a16-4f92-972d-40496df166f3] running status set to false 
[INFO ] 2024-10-11 00:41:05.817 - [任务 4(100)][6c0ab3a7-1a16-4f92-972d-40496df166f3] - Node 6c0ab3a7-1a16-4f92-972d-40496df166f3[6c0ab3a7-1a16-4f92-972d-40496df166f3] schema data cleaned 
[INFO ] 2024-10-11 00:41:05.817 - [任务 4(100)][6c0ab3a7-1a16-4f92-972d-40496df166f3] - Node 6c0ab3a7-1a16-4f92-972d-40496df166f3[6c0ab3a7-1a16-4f92-972d-40496df166f3] monitor closed 
[INFO ] 2024-10-11 00:41:05.817 - [任务 4(100)][6c0ab3a7-1a16-4f92-972d-40496df166f3] - Node 6c0ab3a7-1a16-4f92-972d-40496df166f3[6c0ab3a7-1a16-4f92-972d-40496df166f3] close complete, cost 1 ms 
[INFO ] 2024-10-11 00:41:05.838 - [任务 4(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-12002540-4a8e-43e0-ad20-329a57df4d11 
[INFO ] 2024-10-11 00:41:05.839 - [任务 4(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-12002540-4a8e-43e0-ad20-329a57df4d11 
[INFO ] 2024-10-11 00:41:05.840 - [任务 4(100)][增强JS] - [ScriptExecutorsManager-6707feed82af0a589c4e5934-d4d38d76-bfb5-4fb0-94ba-477a8e43dbde-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 00:41:05.842 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] schema data cleaned 
[INFO ] 2024-10-11 00:41:05.842 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] monitor closed 
[INFO ] 2024-10-11 00:41:05.845 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] close complete, cost 30 ms 
[INFO ] 2024-10-11 00:41:05.845 - [任务 4(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 00:41:05.845 - [任务 4(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 00:41:06.053 - [任务 4(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 00:41:10.938 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] running status set to false 
[INFO ] 2024-10-11 00:41:10.939 - [任务 4(100)][28e1873f-3487-476b-8584-04c216158ae5] - Node 28e1873f-3487-476b-8584-04c216158ae5[28e1873f-3487-476b-8584-04c216158ae5] running status set to false 
[INFO ] 2024-10-11 00:41:10.939 - [任务 4(100)][28e1873f-3487-476b-8584-04c216158ae5] - Node 28e1873f-3487-476b-8584-04c216158ae5[28e1873f-3487-476b-8584-04c216158ae5] schema data cleaned 
[INFO ] 2024-10-11 00:41:10.939 - [任务 4(100)][28e1873f-3487-476b-8584-04c216158ae5] - Node 28e1873f-3487-476b-8584-04c216158ae5[28e1873f-3487-476b-8584-04c216158ae5] monitor closed 
[INFO ] 2024-10-11 00:41:10.939 - [任务 4(100)][28e1873f-3487-476b-8584-04c216158ae5] - Node 28e1873f-3487-476b-8584-04c216158ae5[28e1873f-3487-476b-8584-04c216158ae5] close complete, cost 4 ms 
[INFO ] 2024-10-11 00:41:10.973 - [任务 4(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-b5caf9fd-6ed1-4615-9225-f78aa8660371 
[INFO ] 2024-10-11 00:41:10.974 - [任务 4(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-b5caf9fd-6ed1-4615-9225-f78aa8660371 
[INFO ] 2024-10-11 00:41:10.974 - [任务 4(100)][增强JS] - [ScriptExecutorsManager-6707feed82af0a589c4e5934-d4d38d76-bfb5-4fb0-94ba-477a8e43dbde-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 00:41:10.977 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] schema data cleaned 
[INFO ] 2024-10-11 00:41:10.977 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] monitor closed 
[INFO ] 2024-10-11 00:41:10.978 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] close complete, cost 50 ms 
[INFO ] 2024-10-11 00:41:10.980 - [任务 4(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 00:41:10.980 - [任务 4(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 00:41:11.185 - [任务 4(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 00:41:13.088 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] running status set to false 
[INFO ] 2024-10-11 00:41:13.089 - [任务 4(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:41:13.090 - [任务 4(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:41:13.090 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] schema data cleaned 
[INFO ] 2024-10-11 00:41:13.090 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] monitor closed 
[INFO ] 2024-10-11 00:41:13.091 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] close complete, cost 49 ms 
[INFO ] 2024-10-11 00:41:18.858 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] running status set to false 
[INFO ] 2024-10-11 00:41:18.859 - [任务 4(100)][82f98d60-1557-4325-b8b8-19a69a50a2fa] - Node 82f98d60-1557-4325-b8b8-19a69a50a2fa[82f98d60-1557-4325-b8b8-19a69a50a2fa] running status set to false 
[INFO ] 2024-10-11 00:41:18.859 - [任务 4(100)][82f98d60-1557-4325-b8b8-19a69a50a2fa] - Node 82f98d60-1557-4325-b8b8-19a69a50a2fa[82f98d60-1557-4325-b8b8-19a69a50a2fa] schema data cleaned 
[INFO ] 2024-10-11 00:41:18.859 - [任务 4(100)][82f98d60-1557-4325-b8b8-19a69a50a2fa] - Node 82f98d60-1557-4325-b8b8-19a69a50a2fa[82f98d60-1557-4325-b8b8-19a69a50a2fa] monitor closed 
[INFO ] 2024-10-11 00:41:18.859 - [任务 4(100)][82f98d60-1557-4325-b8b8-19a69a50a2fa] - Node 82f98d60-1557-4325-b8b8-19a69a50a2fa[82f98d60-1557-4325-b8b8-19a69a50a2fa] close complete, cost 3 ms 
[INFO ] 2024-10-11 00:41:18.879 - [任务 4(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-c71b4c56-333a-4f8b-b81a-3b89e0624d97 
[INFO ] 2024-10-11 00:41:18.879 - [任务 4(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-c71b4c56-333a-4f8b-b81a-3b89e0624d97 
[INFO ] 2024-10-11 00:41:18.879 - [任务 4(100)][增强JS] - [ScriptExecutorsManager-6707feed82af0a589c4e5934-d4d38d76-bfb5-4fb0-94ba-477a8e43dbde-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 00:41:18.883 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] schema data cleaned 
[INFO ] 2024-10-11 00:41:18.883 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] monitor closed 
[INFO ] 2024-10-11 00:41:18.884 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] close complete, cost 38 ms 
[INFO ] 2024-10-11 00:41:18.886 - [任务 4(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 00:41:18.886 - [任务 4(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 00:41:18.886 - [任务 4(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 00:41:23.850 - [任务 4(100)][ec5ce21a-3465-42ea-8678-cc3c00cb78dc] - Node ec5ce21a-3465-42ea-8678-cc3c00cb78dc[ec5ce21a-3465-42ea-8678-cc3c00cb78dc] start preload schema,table counts: 0 
[INFO ] 2024-10-11 00:41:23.850 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:41:23.850 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:41:23.851 - [任务 4(100)][ec5ce21a-3465-42ea-8678-cc3c00cb78dc] - Node ec5ce21a-3465-42ea-8678-cc3c00cb78dc[ec5ce21a-3465-42ea-8678-cc3c00cb78dc] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:41:23.851 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] preload schema finished, cost 2 ms 
[INFO ] 2024-10-11 00:41:23.851 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] preload schema finished, cost 1 ms 
[INFO ] 2024-10-11 00:41:23.851 - [任务 4(100)][增强JS] - Node js_processor(增强JS: d4d38d76-bfb5-4fb0-94ba-477a8e43dbde) enable batch process 
[INFO ] 2024-10-11 00:41:23.898 - [任务 4(100)][fab9143c-6bd0-4fb5-a2c5-4706c0a8e09e] - Node fab9143c-6bd0-4fb5-a2c5-4706c0a8e09e[fab9143c-6bd0-4fb5-a2c5-4706c0a8e09e] start preload schema,table counts: 0 
[INFO ] 2024-10-11 00:41:23.899 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:41:23.899 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] start preload schema,table counts: 1 
[INFO ] 2024-10-11 00:41:23.899 - [任务 4(100)][fab9143c-6bd0-4fb5-a2c5-4706c0a8e09e] - Node fab9143c-6bd0-4fb5-a2c5-4706c0a8e09e[fab9143c-6bd0-4fb5-a2c5-4706c0a8e09e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:41:23.899 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:41:23.899 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] preload schema finished, cost 0 ms 
[INFO ] 2024-10-11 00:41:24.104 - [任务 4(100)][增强JS] - Node js_processor(增强JS: d4d38d76-bfb5-4fb0-94ba-477a8e43dbde) enable batch process 
[INFO ] 2024-10-11 00:41:29.560 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] running status set to false 
[INFO ] 2024-10-11 00:41:29.560 - [任务 4(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:41:29.564 - [任务 4(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:41:29.564 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] schema data cleaned 
[INFO ] 2024-10-11 00:41:29.564 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] monitor closed 
[INFO ] 2024-10-11 00:41:29.565 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] close complete, cost 47 ms 
[INFO ] 2024-10-11 00:41:34.777 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] running status set to false 
[INFO ] 2024-10-11 00:41:34.807 - [任务 4(100)][BMSQL_ITEM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:41:34.807 - [任务 4(100)][BMSQL_ITEM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-0d5017b6-b6a4-4010-8cb9-aded26bcf58f 
[INFO ] 2024-10-11 00:41:34.808 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] schema data cleaned 
[INFO ] 2024-10-11 00:41:34.809 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] monitor closed 
[INFO ] 2024-10-11 00:41:34.810 - [任务 4(100)][BMSQL_ITEM] - Node BMSQL_ITEM[0d5017b6-b6a4-4010-8cb9-aded26bcf58f] close complete, cost 48 ms 
[INFO ] 2024-10-11 00:41:37.071 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] running status set to false 
[INFO ] 2024-10-11 00:41:37.072 - [任务 4(100)][ec5ce21a-3465-42ea-8678-cc3c00cb78dc] - Node ec5ce21a-3465-42ea-8678-cc3c00cb78dc[ec5ce21a-3465-42ea-8678-cc3c00cb78dc] running status set to false 
[INFO ] 2024-10-11 00:41:37.072 - [任务 4(100)][ec5ce21a-3465-42ea-8678-cc3c00cb78dc] - Node ec5ce21a-3465-42ea-8678-cc3c00cb78dc[ec5ce21a-3465-42ea-8678-cc3c00cb78dc] schema data cleaned 
[INFO ] 2024-10-11 00:41:37.072 - [任务 4(100)][ec5ce21a-3465-42ea-8678-cc3c00cb78dc] - Node ec5ce21a-3465-42ea-8678-cc3c00cb78dc[ec5ce21a-3465-42ea-8678-cc3c00cb78dc] monitor closed 
[INFO ] 2024-10-11 00:41:37.072 - [任务 4(100)][ec5ce21a-3465-42ea-8678-cc3c00cb78dc] - Node ec5ce21a-3465-42ea-8678-cc3c00cb78dc[ec5ce21a-3465-42ea-8678-cc3c00cb78dc] close complete, cost 6 ms 
[INFO ] 2024-10-11 00:41:37.102 - [任务 4(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-111446a8-6b91-4124-af9e-ad37f8862ff5 
[INFO ] 2024-10-11 00:41:37.102 - [任务 4(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-111446a8-6b91-4124-af9e-ad37f8862ff5 
[INFO ] 2024-10-11 00:41:37.102 - [任务 4(100)][增强JS] - [ScriptExecutorsManager-6707feed82af0a589c4e5934-d4d38d76-bfb5-4fb0-94ba-477a8e43dbde-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 00:41:37.106 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] schema data cleaned 
[INFO ] 2024-10-11 00:41:37.106 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] monitor closed 
[INFO ] 2024-10-11 00:41:37.109 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] close complete, cost 52 ms 
[INFO ] 2024-10-11 00:41:37.109 - [任务 4(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 00:41:37.109 - [任务 4(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 00:41:37.315 - [任务 4(100)] - Stopped task aspect(s) 
[INFO ] 2024-10-11 00:41:42.421 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] running status set to false 
[INFO ] 2024-10-11 00:41:42.425 - [任务 4(100)][fab9143c-6bd0-4fb5-a2c5-4706c0a8e09e] - Node fab9143c-6bd0-4fb5-a2c5-4706c0a8e09e[fab9143c-6bd0-4fb5-a2c5-4706c0a8e09e] running status set to false 
[INFO ] 2024-10-11 00:41:42.425 - [任务 4(100)][fab9143c-6bd0-4fb5-a2c5-4706c0a8e09e] - Node fab9143c-6bd0-4fb5-a2c5-4706c0a8e09e[fab9143c-6bd0-4fb5-a2c5-4706c0a8e09e] schema data cleaned 
[INFO ] 2024-10-11 00:41:42.425 - [任务 4(100)][fab9143c-6bd0-4fb5-a2c5-4706c0a8e09e] - Node fab9143c-6bd0-4fb5-a2c5-4706c0a8e09e[fab9143c-6bd0-4fb5-a2c5-4706c0a8e09e] monitor closed 
[INFO ] 2024-10-11 00:41:42.425 - [任务 4(100)][fab9143c-6bd0-4fb5-a2c5-4706c0a8e09e] - Node fab9143c-6bd0-4fb5-a2c5-4706c0a8e09e[fab9143c-6bd0-4fb5-a2c5-4706c0a8e09e] close complete, cost 8 ms 
[INFO ] 2024-10-11 00:41:42.439 - [任务 4(100)][增强JS] - PDK connector node stopped: ScriptExecutor-mysql3306-6ceb3a84-9ec6-4384-977a-7ca6bf2f4cd1 
[INFO ] 2024-10-11 00:41:42.440 - [任务 4(100)][增强JS] - PDK connector node released: ScriptExecutor-mysql3306-6ceb3a84-9ec6-4384-977a-7ca6bf2f4cd1 
[INFO ] 2024-10-11 00:41:42.440 - [任务 4(100)][增强JS] - [ScriptExecutorsManager-6707feed82af0a589c4e5934-d4d38d76-bfb5-4fb0-94ba-477a8e43dbde-670754025fe35676bdffab25] schema data cleaned 
[INFO ] 2024-10-11 00:41:42.444 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] schema data cleaned 
[INFO ] 2024-10-11 00:41:42.444 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] monitor closed 
[INFO ] 2024-10-11 00:41:42.446 - [任务 4(100)][增强JS] - Node 增强JS[d4d38d76-bfb5-4fb0-94ba-477a8e43dbde] close complete, cost 35 ms 
[INFO ] 2024-10-11 00:41:42.448 - [任务 4(100)] - Closed task monitor(s)
null 
[INFO ] 2024-10-11 00:41:42.448 - [任务 4(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-10-11 00:41:42.655 - [任务 4(100)] - Stopped task aspect(s) 
