[INFO ] 2024-09-27 15:49:33.990 - [任务 1] - Start task milestones: 66ee8759eb78352e9f08fc71(任务 1) 
[INFO ] 2024-09-27 15:49:34.002 - [任务 1] - Task initialization... 
[INFO ] 2024-09-27 15:49:34.393 - [任务 1] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-09-27 15:49:34.395 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 15:49:34.445 - [任务 1][t2] - Node t2[902f1e71-72d8-4d2a-bf2d-4d535ceafdaa] start preload schema,table counts: 1 
[INFO ] 2024-09-27 15:49:34.446 - [任务 1][t3] - Node t3[be7a5377-0fea-4226-be34-37f45f8356db] start preload schema,table counts: 1 
[INFO ] 2024-09-27 15:49:34.447 - [任务 1][t2] - Node t2[902f1e71-72d8-4d2a-bf2d-4d535ceafdaa] preload schema finished, cost 0 ms 
[INFO ] 2024-09-27 15:49:34.659 - [任务 1][t3] - Node t3[be7a5377-0fea-4226-be34-37f45f8356db] preload schema finished, cost 0 ms 
[WARN ] 2024-09-27 15:49:35.311 - [任务 1][t2] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1727423374998} and {hostPort=localhost:43306, time=1727394574995} 
[INFO ] 2024-09-27 15:49:35.312 - [任务 1][t2] - Source node "t2" read batch size: 100 
[INFO ] 2024-09-27 15:49:35.312 - [任务 1][t2] - Source node "t2" event queue capacity: 200 
[INFO ] 2024-09-27 15:49:35.312 - [任务 1][t2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-27 15:49:35.327 - [任务 1][t2] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000043","position":285784822,"gtidSet":""} 
[INFO ] 2024-09-27 15:49:35.327 - [任务 1][t2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-27 15:49:35.382 - [任务 1][t2] - Initial sync started 
[INFO ] 2024-09-27 15:49:35.383 - [任务 1][t2] - Starting batch read, table name: t2 
[INFO ] 2024-09-27 15:49:35.508 - [任务 1][t2] - Table t2 is going to be initial synced 
[WARN ] 2024-09-27 15:49:35.509 - [任务 1][t3] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1727423374993} and {hostPort=localhost:43306, time=1727394574989} 
[INFO ] 2024-09-27 15:49:35.712 - [任务 1][t3] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-27 15:49:35.713 - [任务 1][t2] - Query table 't2' counts: 2178000 
[INFO ] 2024-09-27 15:50:37.135 - [任务 1][t2] - Table [t2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-09-27 15:50:37.142 - [任务 1][t2] - Initial sync completed 
[INFO ] 2024-09-27 15:50:37.142 - [任务 1][t2] - Incremental sync starting... 
[INFO ] 2024-09-27 15:50:37.144 - [任务 1][t2] - Initial sync completed 
[INFO ] 2024-09-27 15:50:37.144 - [任务 1][t2] - Starting stream read, table list: [t2], offset: {"filename":"mysql-bin.000043","position":285784822,"gtidSet":""} 
[INFO ] 2024-09-27 15:50:37.269 - [任务 1][t2] - Starting mysql cdc, server name: 272ccb02-a02e-4e10-8e09-871920c08844 
[INFO ] 2024-09-27 15:50:37.269 - [任务 1][t2] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"272ccb02-a02e-4e10-8e09-871920c08844","offset":{"{\"server\":\"272ccb02-a02e-4e10-8e09-871920c08844\"}":"{\"file\":\"mysql-bin.000043\",\"pos\":285784822,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1753839086
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 272ccb02-a02e-4e10-8e09-871920c08844
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 33306
  threadName: Debezium-Mysql-Connector-272ccb02-a02e-4e10-8e09-871920c08844
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 272ccb02-a02e-4e10-8e09-871920c08844
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-27 15:50:37.467 - [任务 1][t2] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[INFO ] 2024-09-27 15:50:38.690 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@6eca5432: {"time":1727423438473,"type":501} 
[INFO ] 2024-09-27 15:51:27.589 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@1ca96b54: {"time":1727423487460,"type":501} 
[INFO ] 2024-09-27 15:52:15.466 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@66925c8a: {"time":1727423535464,"type":501} 
[INFO ] 2024-09-27 15:53:03.465 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@52f6179f: {"time":1727423583463,"type":501} 
[INFO ] 2024-09-27 15:53:51.472 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@2da9a4f7: {"time":1727423631464,"type":501} 
[INFO ] 2024-09-27 15:54:39.469 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@5bcf87e4: {"time":1727423679462,"type":501} 
[INFO ] 2024-09-27 15:55:27.464 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@48850539: {"time":1727423727461,"type":501} 
[INFO ] 2024-09-27 15:56:15.654 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@5cbc6c83: {"time":1727423775462,"type":501} 
[INFO ] 2024-09-27 15:57:03.568 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@224867a1: {"time":1727423823462,"type":501} 
[INFO ] 2024-09-27 15:57:51.472 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@1e68f160: {"time":1727423871463,"type":501} 
[INFO ] 2024-09-27 15:58:39.466 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@181e8e68: {"time":1727423919458,"type":501} 
[INFO ] 2024-09-27 15:59:27.462 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@7e1265ee: {"time":1727423967461,"type":501} 
[INFO ] 2024-09-27 16:00:28.251 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@6c8435d8: {"time":1727424027725,"type":501} 
[INFO ] 2024-09-27 16:01:03.472 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@646a0531: {"time":1727424063460,"type":501} 
[INFO ] 2024-09-27 16:01:51.467 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@5d12961c: {"time":1727424111464,"type":501} 
[INFO ] 2024-09-27 16:02:39.464 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@27969a73: {"time":1727424159463,"type":501} 
[INFO ] 2024-09-27 16:03:27.460 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@532b9593: {"time":1727424207459,"type":501} 
[INFO ] 2024-09-27 16:04:16.987 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@343e4c4: {"time":1727424256976,"type":501} 
[INFO ] 2024-09-27 16:05:03.462 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@1406f2a8: {"time":1727424303462,"type":501} 
[INFO ] 2024-09-27 16:05:51.626 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@355ac522: {"time":1727424351458,"type":501} 
[INFO ] 2024-09-27 16:06:39.469 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@7ed6fe3b: {"time":1727424399460,"type":501} 
[INFO ] 2024-09-27 16:07:27.465 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@2f5f24d8: {"time":1727424447463,"type":501} 
[INFO ] 2024-09-27 16:08:15.465 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@d185681: {"time":1727424495460,"type":501} 
[INFO ] 2024-09-27 16:09:03.461 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@6b956424: {"time":1727424543460,"type":501} 
[INFO ] 2024-09-27 16:09:51.459 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@520e4979: {"time":1727424591459,"type":501} 
[INFO ] 2024-09-27 16:10:39.462 - [任务 1][t2] - last event is io.tapdata.entity.event.control.HeartbeatEvent@78df6d64: {"time":1727424639462,"type":501} 
[INFO ] 2024-09-27 16:11:08.988 - [任务 1][t2] - Node t2[902f1e71-72d8-4d2a-bf2d-4d535ceafdaa] running status set to false 
[WARN ] 2024-09-27 16:11:09.000 - [任务 1][t2] - Stop PDK connector node failed: Unknown PDK exception occur, com.hazelcast.core.HazelcastInstanceNotActiveException: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-902f1e71-72d8-4d2a-bf2d-4d535ceafdaa 
[INFO ] 2024-09-27 16:11:09.000 - [任务 1][t2] - PDK connector node released: HazelcastSourcePdkDataNode-902f1e71-72d8-4d2a-bf2d-4d535ceafdaa 
[INFO ] 2024-09-27 16:11:09.001 - [任务 1][t2] - Node t2[902f1e71-72d8-4d2a-bf2d-4d535ceafdaa] schema data cleaned 
[INFO ] 2024-09-27 16:11:09.001 - [任务 1][t2] - Node t2[902f1e71-72d8-4d2a-bf2d-4d535ceafdaa] monitor closed 
[INFO ] 2024-09-27 16:11:09.017 - [任务 1][t2] - Node t2[902f1e71-72d8-4d2a-bf2d-4d535ceafdaa] close complete, cost 52 ms 
[INFO ] 2024-09-27 16:11:09.017 - [任务 1][t3] - Node t3[be7a5377-0fea-4226-be34-37f45f8356db] running status set to false 
[INFO ] 2024-09-27 16:11:09.020 - [任务 1][t2] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-27 16:11:09.022 - [任务 1][t2] - Mysql binlog reader stopped 
[INFO ] 2024-09-27 16:11:09.022 - [任务 1][t2] - Incremental sync completed 
[WARN ] 2024-09-27 16:11:09.072 - [任务 1][t3] - Stop PDK connector node failed: Unknown PDK exception occur, com.hazelcast.core.HazelcastInstanceNotActiveException: Hazelcast instance is not active! | Associate id: HazelcastTargetPdkDataNode-be7a5377-0fea-4226-be34-37f45f8356db 
[INFO ] 2024-09-27 16:11:09.098 - [任务 1][t3] - PDK connector node released: HazelcastTargetPdkDataNode-be7a5377-0fea-4226-be34-37f45f8356db 
[INFO ] 2024-09-27 16:11:09.110 - [任务 1][t3] - Node t3[be7a5377-0fea-4226-be34-37f45f8356db] schema data cleaned 
[INFO ] 2024-09-27 16:11:09.111 - [任务 1][t3] - Node t3[be7a5377-0fea-4226-be34-37f45f8356db] monitor closed 
[INFO ] 2024-09-27 16:11:09.111 - [任务 1][t3] - Node t3[be7a5377-0fea-4226-be34-37f45f8356db] close complete, cost 49 ms 
[INFO ] 2024-09-27 16:23:22.471 - [任务 1] - Task initialization... 
[INFO ] 2024-09-27 16:23:22.647 - [任务 1] - Start task milestones: 66ee8759eb78352e9f08fc71(任务 1) 
[INFO ] 2024-09-27 16:23:24.069 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-09-27 16:23:24.234 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-27 16:23:25.001 - [任务 1][t3] - Node t3[be7a5377-0fea-4226-be34-37f45f8356db] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:23:25.012 - [任务 1][t2] - Node t2[902f1e71-72d8-4d2a-bf2d-4d535ceafdaa] start preload schema,table counts: 1 
[INFO ] 2024-09-27 16:23:25.016 - [任务 1][t3] - Node t3[be7a5377-0fea-4226-be34-37f45f8356db] preload schema finished, cost 1 ms 
[INFO ] 2024-09-27 16:23:25.016 - [任务 1][t2] - Node t2[902f1e71-72d8-4d2a-bf2d-4d535ceafdaa] preload schema finished, cost 0 ms 
[WARN ] 2024-09-27 16:23:28.484 - [任务 1][t3] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1727425407997} and {hostPort=localhost:43306, time=1727396607994} 
[INFO ] 2024-09-27 16:23:28.585 - [任务 1][t3] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-09-27 16:23:29.031 - [任务 1][t2] - The time of each node is inconsistent, please check nodes: {hostPort=localhost:33306, time=1727425407995} and {hostPort=localhost:43306, time=1727396607960} 
[INFO ] 2024-09-27 16:23:29.106 - [任务 1][t2] - Source node "t2" read batch size: 100 
[INFO ] 2024-09-27 16:23:29.107 - [任务 1][t2] - Source node "t2" event queue capacity: 200 
[INFO ] 2024-09-27 16:23:29.119 - [任务 1][t2] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-09-27 16:23:29.123 - [任务 1][t2] - batch offset found: {"t2":{"batch_read_connector_status":"OVER"}},stream offset found: {"name":"272ccb02-a02e-4e10-8e09-871920c08844","offset":{"{\"server\":\"272ccb02-a02e-4e10-8e09-871920c08844\"}":"{\"ts_sec\":1727423438,\"file\":\"mysql-bin.000043\",\"pos\":333484037,\"server_id\":1}"}} 
[INFO ] 2024-09-27 16:23:29.123 - [任务 1][t2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-27 16:23:29.192 - [任务 1][t2] - Incremental sync starting... 
[INFO ] 2024-09-27 16:23:29.193 - [任务 1][t2] - Initial sync completed 
[INFO ] 2024-09-27 16:23:29.413 - [任务 1][t2] - Starting stream read, table list: [t2], offset: {"name":"272ccb02-a02e-4e10-8e09-871920c08844","offset":{"{\"server\":\"272ccb02-a02e-4e10-8e09-871920c08844\"}":"{\"ts_sec\":1727423438,\"file\":\"mysql-bin.000043\",\"pos\":333484037,\"server_id\":1}"}} 
[INFO ] 2024-09-27 16:23:38.704 - [任务 1][t2] - Starting mysql cdc, server name: 272ccb02-a02e-4e10-8e09-871920c08844 
[INFO ] 2024-09-27 16:23:38.882 - [任务 1][t2] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"272ccb02-a02e-4e10-8e09-871920c08844","offset":{"{\"server\":\"272ccb02-a02e-4e10-8e09-871920c08844\"}":"{\"file\":\"mysql-bin.000043\",\"pos\":333483890,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1778061899
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 272ccb02-a02e-4e10-8e09-871920c08844
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 33306
  threadName: Debezium-Mysql-Connector-272ccb02-a02e-4e10-8e09-871920c08844
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 272ccb02-a02e-4e10-8e09-871920c08844
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.t2
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-09-27 16:23:38.890 - [任务 1][t2] - Connector Mysql incremental start succeed, tables: [t2], data change syncing 
[INFO ] 2024-09-27 16:26:04.776 - [任务 1][t2] - Node t2[902f1e71-72d8-4d2a-bf2d-4d535ceafdaa] running status set to false 
[INFO ] 2024-09-27 16:26:04.776 - [任务 1][t2] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-09-27 16:26:04.776 - [任务 1][t2] - Mysql binlog reader stopped 
[INFO ] 2024-09-27 16:26:04.790 - [任务 1][t2] - Incremental sync completed 
[INFO ] 2024-09-27 16:26:04.800 - [任务 1][t2] - PDK connector node stopped: HazelcastSourcePdkDataNode-902f1e71-72d8-4d2a-bf2d-4d535ceafdaa 
[INFO ] 2024-09-27 16:26:04.800 - [任务 1][t2] - PDK connector node released: HazelcastSourcePdkDataNode-902f1e71-72d8-4d2a-bf2d-4d535ceafdaa 
[INFO ] 2024-09-27 16:26:04.800 - [任务 1][t2] - Node t2[902f1e71-72d8-4d2a-bf2d-4d535ceafdaa] schema data cleaned 
[INFO ] 2024-09-27 16:26:04.803 - [任务 1][t2] - Node t2[902f1e71-72d8-4d2a-bf2d-4d535ceafdaa] monitor closed 
[INFO ] 2024-09-27 16:26:04.803 - [任务 1][t2] - Node t2[902f1e71-72d8-4d2a-bf2d-4d535ceafdaa] close complete, cost 65 ms 
[INFO ] 2024-09-27 16:26:04.840 - [任务 1][t3] - Node t3[be7a5377-0fea-4226-be34-37f45f8356db] running status set to false 
[INFO ] 2024-09-27 16:26:04.841 - [任务 1][t3] - PDK connector node stopped: HazelcastTargetPdkDataNode-be7a5377-0fea-4226-be34-37f45f8356db 
[INFO ] 2024-09-27 16:26:04.841 - [任务 1][t3] - PDK connector node released: HazelcastTargetPdkDataNode-be7a5377-0fea-4226-be34-37f45f8356db 
[INFO ] 2024-09-27 16:26:04.841 - [任务 1][t3] - Node t3[be7a5377-0fea-4226-be34-37f45f8356db] schema data cleaned 
[INFO ] 2024-09-27 16:26:04.841 - [任务 1][t3] - Node t3[be7a5377-0fea-4226-be34-37f45f8356db] monitor closed 
[INFO ] 2024-09-27 16:26:05.052 - [任务 1][t3] - Node t3[be7a5377-0fea-4226-be34-37f45f8356db] close complete, cost 39 ms 
[INFO ] 2024-09-27 16:26:09.446 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-27 16:26:09.448 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@772cf3aa 
[INFO ] 2024-09-27 16:26:09.561 - [任务 1] - Stop task milestones: 66ee8759eb78352e9f08fc71(任务 1)  
[INFO ] 2024-09-27 16:26:09.575 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-09-27 16:26:09.575 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-09-27 16:26:09.595 - [任务 1] - Remove memory task client succeed, task: 任务 1[66ee8759eb78352e9f08fc71] 
[INFO ] 2024-09-27 16:26:09.598 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[66ee8759eb78352e9f08fc71] 
