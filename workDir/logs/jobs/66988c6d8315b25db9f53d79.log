[INFO ] 2024-07-18 11:31:04.653 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449] - Task initialization... 
[INFO ] 2024-07-18 11:31:04.863 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449] - Start task milestones: 66988c6d8315b25db9f53d79(t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449) 
[INFO ] 2024-07-18 11:31:04.963 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 11:31:05.165 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449] - The engine receives t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 11:31:05.282 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[60487490-e7bb-4ad1-a841-52174b92b1fb] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:31:05.282 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][TableRename] - Node TableRename[1c90fa45-e964-40ef-b4e2-bb4da958faa1] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:31:05.282 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[fded9511-ba8e-49df-9bb5-8185f4404c9f] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:31:05.282 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][TableRename] - Node TableRename[1c90fa45-e964-40ef-b4e2-bb4da958faa1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:31:05.282 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[fded9511-ba8e-49df-9bb5-8185f4404c9f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:31:05.282 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[60487490-e7bb-4ad1-a841-52174b92b1fb] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:31:05.829 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_t_1717403468657_3537] - Node(qa_mysql_repl_33306_t_1717403468657_3537) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-18 11:31:05.829 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_t_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 11:31:06.022 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Source node "qa_mysql_repl_33306_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 11:31:06.024 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Source node "qa_mysql_repl_33306_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 11:31:06.024 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 11:31:06.146 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000304","position":467458316,"gtidSet":""} 
[INFO ] 2024-07-18 11:31:06.146 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-18 11:31:06.318 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449] - Dynamic table monitor started, interval: 60 seconds 
[INFO ] 2024-07-18 11:31:06.318 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 11:31:06.318 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Starting batch read, table name: t_3_6_2_ddl, offset: null 
[INFO ] 2024-07-18 11:31:06.370 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Table t_3_6_2_ddl is going to be initial synced 
[INFO ] 2024-07-18 11:31:06.371 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Table [t_3_6_2_ddl] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:31:06.411 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Query table 't_3_6_2_ddl' counts: 1 
[INFO ] 2024-07-18 11:31:06.411 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:31:06.411 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 11:31:06.411 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:31:06.518 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Starting stream read, table list: [t_3_6_2_ddl, _tapdata_heartbeat_table], offset: {"filename":"mysql-bin.000304","position":467458316,"gtidSet":""} 
[INFO ] 2024-07-18 11:31:06.519 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Starting mysql cdc, server name: 33880a3b-1cda-45c8-84b3-bcaa8f211f88 
[INFO ] 2024-07-18 11:31:06.734 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1666420598
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 33880a3b-1cda-45c8-84b3-bcaa8f211f88
  database.port: 3306
  threadName: Debezium-Mysql-Connector-33880a3b-1cda-45c8-84b3-bcaa8f211f88
  database.hostname: *************
  database.password: ********
  name: 33880a3b-1cda-45c8-84b3-bcaa8f211f88
  pdk.offset.string: {"name":"33880a3b-1cda-45c8-84b3-bcaa8f211f88","offset":{"{\"server\":\"33880a3b-1cda-45c8-84b3-bcaa8f211f88\"}":"{\"file\":\"mysql-bin.000304\",\"pos\":467458316,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: t0.t_3_6_2_ddl,t0._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: t0
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-18 11:31:06.938 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Connector Mysql incremental start succeed, tables: [t_3_6_2_ddl, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 11:31:21.183 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Read DDL: alter table t0.t_3_6_2_ddl drop column c1, about to be packaged as some event(s) 
[INFO ] 2024-07-18 11:31:21.184 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - DDL event  - Table: t_3_6_2_ddl
  - Event type: TapDropFieldEvent
  - Offset: MysqlStreamOffset{name='33880a3b-1cda-45c8-84b3-bcaa8f211f88', offset={{"server":"33880a3b-1cda-45c8-84b3-bcaa8f211f88"}={"ts_sec":1721273481,"file":"mysql-bin.000304","pos":467749904,"server_id":1121}}} 
[INFO ] 2024-07-18 11:31:21.191 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Source node received an ddl event: TapDropFieldEvent{tableId='t_3_6_2_ddl', fieldName='c1'} 
[INFO ] 2024-07-18 11:31:21.191 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_t_3_6_2_ddl_6697a4cfb92eda1a86f5244d_66988c6d8315b25db9f53d79 
[INFO ] 2024-07-18 11:31:21.397 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Alter table schema transform finished 
[INFO ] 2024-07-18 11:33:45.743 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[60487490-e7bb-4ad1-a841-52174b92b1fb] running status set to false 
[INFO ] 2024-07-18 11:33:45.813 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-18 11:33:45.814 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Mysql binlog reader stopped 
[INFO ] 2024-07-18 11:33:45.824 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 11:33:45.825 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-60487490-e7bb-4ad1-a841-52174b92b1fb 
[INFO ] 2024-07-18 11:33:45.825 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-60487490-e7bb-4ad1-a841-52174b92b1fb 
[INFO ] 2024-07-18 11:33:45.825 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[60487490-e7bb-4ad1-a841-52174b92b1fb] schema data cleaned 
[INFO ] 2024-07-18 11:33:45.827 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[60487490-e7bb-4ad1-a841-52174b92b1fb] monitor closed 
[INFO ] 2024-07-18 11:33:45.827 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[60487490-e7bb-4ad1-a841-52174b92b1fb] close complete, cost 145 ms 
[INFO ] 2024-07-18 11:33:45.827 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][TableRename] - Node TableRename[1c90fa45-e964-40ef-b4e2-bb4da958faa1] running status set to false 
[INFO ] 2024-07-18 11:33:45.828 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][TableRename] - Node TableRename[1c90fa45-e964-40ef-b4e2-bb4da958faa1] schema data cleaned 
[INFO ] 2024-07-18 11:33:45.828 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][TableRename] - Node TableRename[1c90fa45-e964-40ef-b4e2-bb4da958faa1] monitor closed 
[INFO ] 2024-07-18 11:33:45.828 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][TableRename] - Node TableRename[1c90fa45-e964-40ef-b4e2-bb4da958faa1] close complete, cost 0 ms 
[INFO ] 2024-07-18 11:33:45.852 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[fded9511-ba8e-49df-9bb5-8185f4404c9f] running status set to false 
[INFO ] 2024-07-18 11:33:45.852 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_t_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-fded9511-ba8e-49df-9bb5-8185f4404c9f 
[INFO ] 2024-07-18 11:33:45.853 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_t_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-fded9511-ba8e-49df-9bb5-8185f4404c9f 
[INFO ] 2024-07-18 11:33:45.853 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[fded9511-ba8e-49df-9bb5-8185f4404c9f] schema data cleaned 
[INFO ] 2024-07-18 11:33:45.853 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[fded9511-ba8e-49df-9bb5-8185f4404c9f] monitor closed 
[INFO ] 2024-07-18 11:33:46.056 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[fded9511-ba8e-49df-9bb5-8185f4404c9f] close complete, cost 25 ms 
[INFO ] 2024-07-18 11:33:48.589 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 11:33:48.589 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3fef27b8 
[INFO ] 2024-07-18 11:33:48.708 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449] - Stop task milestones: 66988c6d8315b25db9f53d79(t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449)  
[INFO ] 2024-07-18 11:33:48.734 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449] - Stopped task aspect(s) 
[INFO ] 2024-07-18 11:33:48.734 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 11:33:48.803 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449] - Remove memory task client succeed, task: t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449[66988c6d8315b25db9f53d79] 
[INFO ] 2024-07-18 11:33:48.814 - [t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449] - Destroy memory task client cache succeed, task: t_3.6.2-mysql_to_mysql_ddl_drop_1717403468657_3537-1721273449[66988c6d8315b25db9f53d79] 
