[INFO ] 2024-03-29 18:40:34.618 - [categories] - Start task milestones: 66069a1d0aba7657dd9a0eb8(categories) 
[INFO ] 2024-03-29 18:40:34.700 - [categories] - Task initialization... 
[INFO ] 2024-03-29 18:40:34.700 - [categories] - <PERSON><PERSON> performs snapshot read by order list: [ null ] -> [ null ] 
[INFO ] 2024-03-29 18:40:34.989 - [categories] - The engine receives categories task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 18:40:34.989 - [categories][Products] - Node Products[8f4833cd-b4e7-4251-8db8-14f2e4adf031] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:34.989 - [categories][categories] - Node categories[ca3fd938-7f71-47f0-9b51-0bca4979c533] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:34.989 - [categories][Delete Categories] - Node Delete Categories[ab4d9901-c63f-4617-9bed-485d4792f08a] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:34.989 - [categories][Categories] - Node Categories[99ebd956-2a8d-4629-a022-b489839c906e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:34.992 - [categories][merge] - Node merge[e71fb5a1-5b88-4db8-8857-8a7305105fea] start preload schema,table counts: 3 
[INFO ] 2024-03-29 18:40:34.992 - [categories][Rename Products] - Node Rename Products[74b0c501-d767-447f-8675-72f9bd24b652] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:35.012 - [categories][Delete Products] - Node Delete Products[30362368-cbf2-45f3-8c76-c8b28451dd31] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:35.012 - [categories][Rename Categories] - Node Rename Categories[50898c90-1236-4b7c-b3ee-d81fce0ed66c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:35.016 - [categories][Categories] - Node Categories[99ebd956-2a8d-4629-a022-b489839c906e] preload schema finished, cost 27 ms 
[INFO ] 2024-03-29 18:40:35.016 - [categories][Delete Categories] - Node Delete Categories[ab4d9901-c63f-4617-9bed-485d4792f08a] preload schema finished, cost 28 ms 
[INFO ] 2024-03-29 18:40:35.016 - [categories][categories] - Node categories[ca3fd938-7f71-47f0-9b51-0bca4979c533] preload schema finished, cost 28 ms 
[INFO ] 2024-03-29 18:40:35.016 - [categories][Products] - Node Products[8f4833cd-b4e7-4251-8db8-14f2e4adf031] preload schema finished, cost 29 ms 
[INFO ] 2024-03-29 18:40:35.016 - [categories][Rename Products] - Node Rename Products[74b0c501-d767-447f-8675-72f9bd24b652] preload schema finished, cost 22 ms 
[INFO ] 2024-03-29 18:40:35.016 - [categories][Delete Products] - Node Delete Products[30362368-cbf2-45f3-8c76-c8b28451dd31] preload schema finished, cost 22 ms 
[INFO ] 2024-03-29 18:40:35.047 - [categories][Rename Categories] - Node Rename Categories[50898c90-1236-4b7c-b3ee-d81fce0ed66c] preload schema finished, cost 22 ms 
[INFO ] 2024-03-29 18:40:35.047 - [categories][merge] - Node merge[e71fb5a1-5b88-4db8-8857-8a7305105fea] preload schema finished, cost 58 ms 
[INFO ] 2024-03-29 18:40:35.048 - [categories][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 18:40:35.051 - [categories][merge] - 
Merge lookup relation{
  Categories(ab4d9901-c63f-4617-9bed-485d4792f08a)
    ->Products(30362368-cbf2-45f3-8c76-c8b28451dd31)
} 
[INFO ] 2024-03-29 18:40:35.302 - [categories][merge] - Create merge cache imap name: HazelcastMergeNode_Products_30362368-cbf2-45f3-8c76-c8b28451dd31__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:40:35.326 - [categories][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 18:40:35.327 - [categories][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 18:40:36.008 - [categories][Categories] - Source node "Categories" read batch size: 500 
[INFO ] 2024-03-29 18:40:36.008 - [categories][Categories] - Source node "Categories" event queue capacity: 1000 
[INFO ] 2024-03-29 18:40:36.008 - [categories][Categories] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:40:36.015 - [categories][Categories] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:40:36.071 - [categories][Categories] - Initial sync started 
[INFO ] 2024-03-29 18:40:36.080 - [categories][Categories] - Starting batch read, table name: Categories, offset: null 
[INFO ] 2024-03-29 18:40:36.080 - [categories][Categories] - Table Categories is going to be initial synced 
[INFO ] 2024-03-29 18:40:36.104 - [categories][Categories] - Query table 'Categories' counts: 1 
[INFO ] 2024-03-29 18:40:36.105 - [categories][Categories] - Initial sync completed 
[INFO ] 2024-03-29 18:40:36.188 - [categories][Products] - Source node "Products" read batch size: 500 
[INFO ] 2024-03-29 18:40:36.190 - [categories][Products] - Source node "Products" event queue capacity: 1000 
[INFO ] 2024-03-29 18:40:36.190 - [categories][Products] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:40:36.250 - [categories][Products] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:40:36.250 - [categories][categories] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 18:40:36.456 - [categories] - Node[Products] is waiting for running 
[INFO ] 2024-03-29 18:40:37.286 - [categories] - Node[Categories] finish, notify next layer to run 
[INFO ] 2024-03-29 18:40:37.286 - [categories] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 18:40:37.286 - [categories][Products] - Initial sync started 
[INFO ] 2024-03-29 18:40:37.287 - [categories][Products] - Starting batch read, table name: Products, offset: null 
[INFO ] 2024-03-29 18:40:37.287 - [categories][Products] - Table Products is going to be initial synced 
[INFO ] 2024-03-29 18:40:37.322 - [categories][Products] - Query table 'Products' counts: 1 
[INFO ] 2024-03-29 18:40:37.322 - [categories][Products] - Initial sync completed 
[INFO ] 2024-03-29 18:40:39.119 - [categories][Categories] - Incremental sync starting... 
[INFO ] 2024-03-29 18:40:39.120 - [categories][Categories] - Initial sync completed 
[INFO ] 2024-03-29 18:40:39.121 - [categories][Categories] - Starting stream read, table list: [Categories], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:40:39.245 - [categories][Categories] - Starting mysql cdc, server name: 0ba60f96-8a34-4615-bdd3-e96310b1876f 
[INFO ] 2024-03-29 18:40:39.246 - [categories][Categories] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1283811596
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 0ba60f96-8a34-4615-bdd3-e96310b1876f
  database.port: 3307
  threadName: Debezium-Mysql-Connector-0ba60f96-8a34-4615-bdd3-e96310b1876f
  database.hostname: 127.0.0.1
  database.password: ********
  name: 0ba60f96-8a34-4615-bdd3-e96310b1876f
  pdk.offset.string: {"name":"0ba60f96-8a34-4615-bdd3-e96310b1876f","offset":{"{\"server\":\"0ba60f96-8a34-4615-bdd3-e96310b1876f\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Categories
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:40:39.335 - [categories][Categories] - Connector Mysql incremental start succeed, tables: [Categories], data change syncing 
[INFO ] 2024-03-29 18:40:39.335 - [categories][Products] - Incremental sync starting... 
[INFO ] 2024-03-29 18:40:39.335 - [categories][Products] - Initial sync completed 
[INFO ] 2024-03-29 18:40:39.363 - [categories][Products] - Starting stream read, table list: [Products], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:40:39.485 - [categories][Products] - Starting mysql cdc, server name: 778b2c2a-595a-408c-91d8-c2e9006a7ff9 
[INFO ] 2024-03-29 18:40:39.485 - [categories][Products] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 709075476
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 778b2c2a-595a-408c-91d8-c2e9006a7ff9
  database.port: 3307
  threadName: Debezium-Mysql-Connector-778b2c2a-595a-408c-91d8-c2e9006a7ff9
  database.hostname: 127.0.0.1
  database.password: ********
  name: 778b2c2a-595a-408c-91d8-c2e9006a7ff9
  pdk.offset.string: {"name":"778b2c2a-595a-408c-91d8-c2e9006a7ff9","offset":{"{\"server\":\"778b2c2a-595a-408c-91d8-c2e9006a7ff9\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Products
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:40:39.692 - [categories][Products] - Connector Mysql incremental start succeed, tables: [Products], data change syncing 
[INFO ] 2024-03-29 18:53:20.047 - [categories] - Stop task milestones: 66069a1d0aba7657dd9a0eb8(categories)  
[INFO ] 2024-03-29 18:53:20.321 - [categories][Products] - Node Products[8f4833cd-b4e7-4251-8db8-14f2e4adf031] running status set to false 
[INFO ] 2024-03-29 18:53:20.424 - [categories][Products] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:53:20.424 - [categories][Products] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:53:20.433 - [categories][Products] - PDK connector node stopped: HazelcastSourcePdkDataNode-8f4833cd-b4e7-4251-8db8-14f2e4adf031 
[INFO ] 2024-03-29 18:53:20.433 - [categories][Products] - PDK connector node released: HazelcastSourcePdkDataNode-8f4833cd-b4e7-4251-8db8-14f2e4adf031 
[INFO ] 2024-03-29 18:53:20.433 - [categories][Products] - Node Products[8f4833cd-b4e7-4251-8db8-14f2e4adf031] schema data cleaned 
[INFO ] 2024-03-29 18:53:20.433 - [categories][Products] - Node Products[8f4833cd-b4e7-4251-8db8-14f2e4adf031] monitor closed 
[INFO ] 2024-03-29 18:53:20.434 - [categories][Products] - Node Products[8f4833cd-b4e7-4251-8db8-14f2e4adf031] close complete, cost 117 ms 
[INFO ] 2024-03-29 18:53:20.434 - [categories][Rename Products] - Node Rename Products[74b0c501-d767-447f-8675-72f9bd24b652] running status set to false 
[INFO ] 2024-03-29 18:53:20.502 - [categories][Rename Products] - Node Rename Products[74b0c501-d767-447f-8675-72f9bd24b652] schema data cleaned 
[INFO ] 2024-03-29 18:53:20.502 - [categories][Rename Products] - Node Rename Products[74b0c501-d767-447f-8675-72f9bd24b652] monitor closed 
[INFO ] 2024-03-29 18:53:20.502 - [categories][Rename Products] - Node Rename Products[74b0c501-d767-447f-8675-72f9bd24b652] close complete, cost 68 ms 
[INFO ] 2024-03-29 18:53:20.543 - [categories][Delete Products] - Node Delete Products[30362368-cbf2-45f3-8c76-c8b28451dd31] running status set to false 
[INFO ] 2024-03-29 18:53:20.543 - [categories][Delete Products] - Node Delete Products[30362368-cbf2-45f3-8c76-c8b28451dd31] schema data cleaned 
[INFO ] 2024-03-29 18:53:20.543 - [categories][Delete Products] - Node Delete Products[30362368-cbf2-45f3-8c76-c8b28451dd31] monitor closed 
[INFO ] 2024-03-29 18:53:20.543 - [categories][Delete Products] - Node Delete Products[30362368-cbf2-45f3-8c76-c8b28451dd31] close complete, cost 40 ms 
[INFO ] 2024-03-29 18:53:20.654 - [categories][Categories] - Node Categories[99ebd956-2a8d-4629-a022-b489839c906e] running status set to false 
[INFO ] 2024-03-29 18:53:20.655 - [categories][Categories] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:53:20.655 - [categories][Categories] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:53:20.662 - [categories][Categories] - PDK connector node stopped: HazelcastSourcePdkDataNode-99ebd956-2a8d-4629-a022-b489839c906e 
[INFO ] 2024-03-29 18:53:20.662 - [categories][Categories] - PDK connector node released: HazelcastSourcePdkDataNode-99ebd956-2a8d-4629-a022-b489839c906e 
[INFO ] 2024-03-29 18:53:20.662 - [categories][Categories] - Node Categories[99ebd956-2a8d-4629-a022-b489839c906e] schema data cleaned 
[INFO ] 2024-03-29 18:53:20.662 - [categories][Categories] - Node Categories[99ebd956-2a8d-4629-a022-b489839c906e] monitor closed 
[INFO ] 2024-03-29 18:53:20.662 - [categories][Categories] - Node Categories[99ebd956-2a8d-4629-a022-b489839c906e] close complete, cost 118 ms 
[INFO ] 2024-03-29 18:53:20.706 - [categories][Rename Categories] - Node Rename Categories[50898c90-1236-4b7c-b3ee-d81fce0ed66c] running status set to false 
[INFO ] 2024-03-29 18:53:20.706 - [categories][Rename Categories] - Node Rename Categories[50898c90-1236-4b7c-b3ee-d81fce0ed66c] schema data cleaned 
[INFO ] 2024-03-29 18:53:20.706 - [categories][Rename Categories] - Node Rename Categories[50898c90-1236-4b7c-b3ee-d81fce0ed66c] monitor closed 
[INFO ] 2024-03-29 18:53:20.706 - [categories][Rename Categories] - Node Rename Categories[50898c90-1236-4b7c-b3ee-d81fce0ed66c] close complete, cost 43 ms 
[INFO ] 2024-03-29 18:53:20.744 - [categories][Delete Categories] - Node Delete Categories[ab4d9901-c63f-4617-9bed-485d4792f08a] running status set to false 
[INFO ] 2024-03-29 18:53:20.744 - [categories][Delete Categories] - Node Delete Categories[ab4d9901-c63f-4617-9bed-485d4792f08a] schema data cleaned 
[INFO ] 2024-03-29 18:53:20.745 - [categories][Delete Categories] - Node Delete Categories[ab4d9901-c63f-4617-9bed-485d4792f08a] monitor closed 
[INFO ] 2024-03-29 18:53:20.745 - [categories][Delete Categories] - Node Delete Categories[ab4d9901-c63f-4617-9bed-485d4792f08a] close complete, cost 38 ms 
[INFO ] 2024-03-29 18:53:20.745 - [categories][merge] - Node merge[e71fb5a1-5b88-4db8-8857-8a7305105fea] running status set to false 
[INFO ] 2024-03-29 18:53:20.745 - [categories][merge] - Destroy merge cache resource: HazelcastMergeNode_Products_30362368-cbf2-45f3-8c76-c8b28451dd31__TPORIG 
[INFO ] 2024-03-29 18:53:20.755 - [categories][merge] - Node merge[e71fb5a1-5b88-4db8-8857-8a7305105fea] schema data cleaned 
[INFO ] 2024-03-29 18:53:20.756 - [categories][merge] - Node merge[e71fb5a1-5b88-4db8-8857-8a7305105fea] monitor closed 
[INFO ] 2024-03-29 18:53:20.756 - [categories][merge] - Node merge[e71fb5a1-5b88-4db8-8857-8a7305105fea] close complete, cost 10 ms 
[INFO ] 2024-03-29 18:53:20.756 - [categories][categories] - Node categories[ca3fd938-7f71-47f0-9b51-0bca4979c533] running status set to false 
[INFO ] 2024-03-29 18:53:20.786 - [categories][categories] - PDK connector node stopped: HazelcastTargetPdkDataNode-ca3fd938-7f71-47f0-9b51-0bca4979c533 
[INFO ] 2024-03-29 18:53:20.787 - [categories][categories] - PDK connector node released: HazelcastTargetPdkDataNode-ca3fd938-7f71-47f0-9b51-0bca4979c533 
[INFO ] 2024-03-29 18:53:20.787 - [categories][categories] - Node categories[ca3fd938-7f71-47f0-9b51-0bca4979c533] schema data cleaned 
[INFO ] 2024-03-29 18:53:20.787 - [categories][categories] - Node categories[ca3fd938-7f71-47f0-9b51-0bca4979c533] monitor closed 
[INFO ] 2024-03-29 18:53:20.787 - [categories][categories] - Node categories[ca3fd938-7f71-47f0-9b51-0bca4979c533] close complete, cost 31 ms 
[INFO ] 2024-03-29 18:53:25.087 - [categories] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-29 18:53:25.087 - [categories] - Stopped task aspect(s) 
[INFO ] 2024-03-29 18:53:25.087 - [categories] - Snapshot order controller have been removed 
[INFO ] 2024-03-29 18:53:25.107 - [categories] - Remove memory task client succeed, task: categories[66069a1d0aba7657dd9a0eb8] 
[INFO ] 2024-03-29 18:53:25.108 - [categories] - Destroy memory task client cache succeed, task: categories[66069a1d0aba7657dd9a0eb8] 
