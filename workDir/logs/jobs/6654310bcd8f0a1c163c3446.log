[INFO ] 2024-05-27 15:07:25.622 - [任务 16(100)][f0e175c9-3d91-45c4-b9f6-303f3dc4f086] - Node f0e175c9-3d91-45c4-b9f6-303f3dc4f086[f0e175c9-3d91-45c4-b9f6-303f3dc4f086] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:07:25.623 - [任务 16(100)][CAR.CLAIM] - Node CAR.CLAIM[33c3983a-91c8-47b5-9e77-e00e63f38fda] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:25.625 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:25.627 - [任务 16(100)][f0e175c9-3d91-45c4-b9f6-303f3dc4f086] - Node f0e175c9-3d91-45c4-b9f6-303f3dc4f086[f0e175c9-3d91-45c4-b9f6-303f3dc4f086] preload schema finished, cost 2 ms 
[INFO ] 2024-05-27 15:07:25.627 - [任务 16(100)][CAR.CLAIM] - Node CAR.CLAIM[33c3983a-91c8-47b5-9e77-e00e63f38fda] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:25.628 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:26.216 - [任务 16(100)][CAR.CLAIM] - Node CAR.CLAIM[33c3983a-91c8-47b5-9e77-e00e63f38fda] running status set to false 
[INFO ] 2024-05-27 15:07:26.219 - [任务 16(100)][CAR.CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:26.220 - [任务 16(100)][CAR.CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:26.221 - [任务 16(100)][CAR.CLAIM] - Node CAR.CLAIM[33c3983a-91c8-47b5-9e77-e00e63f38fda] schema data cleaned 
[INFO ] 2024-05-27 15:07:26.221 - [任务 16(100)][CAR.CLAIM] - Node CAR.CLAIM[33c3983a-91c8-47b5-9e77-e00e63f38fda] monitor closed 
[INFO ] 2024-05-27 15:07:26.221 - [任务 16(100)][CAR.CLAIM] - Node CAR.CLAIM[33c3983a-91c8-47b5-9e77-e00e63f38fda] close complete, cost 19 ms 
[INFO ] 2024-05-27 15:07:26.338 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] running status set to false 
[INFO ] 2024-05-27 15:07:26.339 - [任务 16(100)][f0e175c9-3d91-45c4-b9f6-303f3dc4f086] - Node f0e175c9-3d91-45c4-b9f6-303f3dc4f086[f0e175c9-3d91-45c4-b9f6-303f3dc4f086] running status set to false 
[INFO ] 2024-05-27 15:07:26.339 - [任务 16(100)][f0e175c9-3d91-45c4-b9f6-303f3dc4f086] - Node f0e175c9-3d91-45c4-b9f6-303f3dc4f086[f0e175c9-3d91-45c4-b9f6-303f3dc4f086] schema data cleaned 
[INFO ] 2024-05-27 15:07:26.340 - [任务 16(100)][f0e175c9-3d91-45c4-b9f6-303f3dc4f086] - Node f0e175c9-3d91-45c4-b9f6-303f3dc4f086[f0e175c9-3d91-45c4-b9f6-303f3dc4f086] monitor closed 
[INFO ] 2024-05-27 15:07:26.340 - [任务 16(100)][f0e175c9-3d91-45c4-b9f6-303f3dc4f086] - Node f0e175c9-3d91-45c4-b9f6-303f3dc4f086[f0e175c9-3d91-45c4-b9f6-303f3dc4f086] close complete, cost 2 ms 
[INFO ] 2024-05-27 15:07:26.350 - [任务 16(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-187af110-96f5-410d-b3ce-302863d6e6b1 
[INFO ] 2024-05-27 15:07:26.352 - [任务 16(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-187af110-96f5-410d-b3ce-302863d6e6b1 
[INFO ] 2024-05-27 15:07:26.353 - [任务 16(100)][增强JS] - [ScriptExecutorsManager-6654310bcd8f0a1c163c3446-7d099e27-a4a8-4eab-b05a-8e2692d9420f-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:07:26.360 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] schema data cleaned 
[INFO ] 2024-05-27 15:07:26.361 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] monitor closed 
[INFO ] 2024-05-27 15:07:26.369 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] close complete, cost 32 ms 
[INFO ] 2024-05-27 15:07:26.370 - [任务 16(100)] - load tapTable task 6654310bcd8f0a1c163c3446-f0e175c9-3d91-45c4-b9f6-303f3dc4f086 complete, cost 869ms 
[INFO ] 2024-05-27 15:07:32.479 - [任务 16(100)][a8858aa8-2010-4f34-a233-4d39a58860df] - Node a8858aa8-2010-4f34-a233-4d39a58860df[a8858aa8-2010-4f34-a233-4d39a58860df] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:07:32.479 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:32.480 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:32.484 - [任务 16(100)][a8858aa8-2010-4f34-a233-4d39a58860df] - Node a8858aa8-2010-4f34-a233-4d39a58860df[a8858aa8-2010-4f34-a233-4d39a58860df] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:32.489 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:32.489 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:32.873 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] running status set to false 
[INFO ] 2024-05-27 15:07:32.940 - [任务 16(100)][CAR.CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:32.940 - [任务 16(100)][CAR.CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:32.940 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] schema data cleaned 
[INFO ] 2024-05-27 15:07:32.941 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] monitor closed 
[INFO ] 2024-05-27 15:07:32.941 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] close complete, cost 83 ms 
[INFO ] 2024-05-27 15:07:32.966 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] running status set to false 
[INFO ] 2024-05-27 15:07:32.966 - [任务 16(100)][a8858aa8-2010-4f34-a233-4d39a58860df] - Node a8858aa8-2010-4f34-a233-4d39a58860df[a8858aa8-2010-4f34-a233-4d39a58860df] running status set to false 
[INFO ] 2024-05-27 15:07:32.966 - [任务 16(100)][a8858aa8-2010-4f34-a233-4d39a58860df] - Node a8858aa8-2010-4f34-a233-4d39a58860df[a8858aa8-2010-4f34-a233-4d39a58860df] schema data cleaned 
[INFO ] 2024-05-27 15:07:32.966 - [任务 16(100)][a8858aa8-2010-4f34-a233-4d39a58860df] - Node a8858aa8-2010-4f34-a233-4d39a58860df[a8858aa8-2010-4f34-a233-4d39a58860df] monitor closed 
[INFO ] 2024-05-27 15:07:32.970 - [任务 16(100)][a8858aa8-2010-4f34-a233-4d39a58860df] - Node a8858aa8-2010-4f34-a233-4d39a58860df[a8858aa8-2010-4f34-a233-4d39a58860df] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:07:32.970 - [任务 16(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-c281012d-3f71-4aeb-a870-32dccc70a278 
[INFO ] 2024-05-27 15:07:32.970 - [任务 16(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-c281012d-3f71-4aeb-a870-32dccc70a278 
[INFO ] 2024-05-27 15:07:32.970 - [任务 16(100)][增强JS] - [ScriptExecutorsManager-6654310bcd8f0a1c163c3446-7d099e27-a4a8-4eab-b05a-8e2692d9420f-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:07:32.971 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] schema data cleaned 
[INFO ] 2024-05-27 15:07:32.972 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] monitor closed 
[INFO ] 2024-05-27 15:07:32.973 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] close complete, cost 10 ms 
[INFO ] 2024-05-27 15:07:33.179 - [任务 16(100)] - load tapTable task 6654310bcd8f0a1c163c3446-a8858aa8-2010-4f34-a233-4d39a58860df complete, cost 566ms 
[INFO ] 2024-05-27 15:07:39.008 - [任务 16(100)][094a7be4-245e-4626-bbb0-0292041e1f5d] - Node 094a7be4-245e-4626-bbb0-0292041e1f5d[094a7be4-245e-4626-bbb0-0292041e1f5d] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:07:39.009 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:39.009 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:39.009 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:39.009 - [任务 16(100)][094a7be4-245e-4626-bbb0-0292041e1f5d] - Node 094a7be4-245e-4626-bbb0-0292041e1f5d[094a7be4-245e-4626-bbb0-0292041e1f5d] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:39.010 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:39.243 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] running status set to false 
[INFO ] 2024-05-27 15:07:39.243 - [任务 16(100)][CAR.CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:39.244 - [任务 16(100)][CAR.CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:39.244 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] schema data cleaned 
[INFO ] 2024-05-27 15:07:39.245 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] monitor closed 
[INFO ] 2024-05-27 15:07:39.245 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] close complete, cost 12 ms 
[INFO ] 2024-05-27 15:07:39.294 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] running status set to false 
[INFO ] 2024-05-27 15:07:39.294 - [任务 16(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-e9091dc3-c3e3-45d7-8a49-3286e6c4bd00 
[INFO ] 2024-05-27 15:07:39.297 - [任务 16(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-e9091dc3-c3e3-45d7-8a49-3286e6c4bd00 
[INFO ] 2024-05-27 15:07:39.297 - [任务 16(100)][094a7be4-245e-4626-bbb0-0292041e1f5d] - Node 094a7be4-245e-4626-bbb0-0292041e1f5d[094a7be4-245e-4626-bbb0-0292041e1f5d] running status set to false 
[INFO ] 2024-05-27 15:07:39.298 - [任务 16(100)][094a7be4-245e-4626-bbb0-0292041e1f5d] - Node 094a7be4-245e-4626-bbb0-0292041e1f5d[094a7be4-245e-4626-bbb0-0292041e1f5d] schema data cleaned 
[INFO ] 2024-05-27 15:07:39.299 - [任务 16(100)][增强JS] - [ScriptExecutorsManager-6654310bcd8f0a1c163c3446-7d099e27-a4a8-4eab-b05a-8e2692d9420f-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:07:39.299 - [任务 16(100)][094a7be4-245e-4626-bbb0-0292041e1f5d] - Node 094a7be4-245e-4626-bbb0-0292041e1f5d[094a7be4-245e-4626-bbb0-0292041e1f5d] monitor closed 
[INFO ] 2024-05-27 15:07:39.301 - [任务 16(100)][094a7be4-245e-4626-bbb0-0292041e1f5d] - Node 094a7be4-245e-4626-bbb0-0292041e1f5d[094a7be4-245e-4626-bbb0-0292041e1f5d] close complete, cost 3 ms 
[INFO ] 2024-05-27 15:07:39.301 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] schema data cleaned 
[INFO ] 2024-05-27 15:07:39.301 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] monitor closed 
[INFO ] 2024-05-27 15:07:39.302 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] close complete, cost 12 ms 
[INFO ] 2024-05-27 15:07:39.510 - [任务 16(100)] - load tapTable task 6654310bcd8f0a1c163c3446-094a7be4-245e-4626-bbb0-0292041e1f5d complete, cost 428ms 
[INFO ] 2024-05-27 15:07:41.453 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:41.453 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:41.455 - [任务 16(100)][505d2628-2573-4a4e-a17a-06f2ced389ae] - Node 505d2628-2573-4a4e-a17a-06f2ced389ae[505d2628-2573-4a4e-a17a-06f2ced389ae] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:07:41.455 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:41.455 - [任务 16(100)][505d2628-2573-4a4e-a17a-06f2ced389ae] - Node 505d2628-2573-4a4e-a17a-06f2ced389ae[505d2628-2573-4a4e-a17a-06f2ced389ae] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:41.456 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:41.668 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] running status set to false 
[INFO ] 2024-05-27 15:07:41.668 - [任务 16(100)][CAR.CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:41.670 - [任务 16(100)][CAR.CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:41.670 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] schema data cleaned 
[INFO ] 2024-05-27 15:07:41.670 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] monitor closed 
[INFO ] 2024-05-27 15:07:41.671 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] close complete, cost 12 ms 
[INFO ] 2024-05-27 15:07:41.741 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] running status set to false 
[INFO ] 2024-05-27 15:07:41.741 - [任务 16(100)][505d2628-2573-4a4e-a17a-06f2ced389ae] - Node 505d2628-2573-4a4e-a17a-06f2ced389ae[505d2628-2573-4a4e-a17a-06f2ced389ae] running status set to false 
[INFO ] 2024-05-27 15:07:41.741 - [任务 16(100)][505d2628-2573-4a4e-a17a-06f2ced389ae] - Node 505d2628-2573-4a4e-a17a-06f2ced389ae[505d2628-2573-4a4e-a17a-06f2ced389ae] schema data cleaned 
[INFO ] 2024-05-27 15:07:41.742 - [任务 16(100)][505d2628-2573-4a4e-a17a-06f2ced389ae] - Node 505d2628-2573-4a4e-a17a-06f2ced389ae[505d2628-2573-4a4e-a17a-06f2ced389ae] monitor closed 
[INFO ] 2024-05-27 15:07:41.750 - [任务 16(100)][505d2628-2573-4a4e-a17a-06f2ced389ae] - Node 505d2628-2573-4a4e-a17a-06f2ced389ae[505d2628-2573-4a4e-a17a-06f2ced389ae] close complete, cost 2 ms 
[INFO ] 2024-05-27 15:07:41.750 - [任务 16(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-52bed9f1-7b7c-4673-8393-630b767fde3a 
[INFO ] 2024-05-27 15:07:41.750 - [任务 16(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-52bed9f1-7b7c-4673-8393-630b767fde3a 
[INFO ] 2024-05-27 15:07:41.751 - [任务 16(100)][增强JS] - [ScriptExecutorsManager-6654310bcd8f0a1c163c3446-7d099e27-a4a8-4eab-b05a-8e2692d9420f-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:07:41.753 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] schema data cleaned 
[INFO ] 2024-05-27 15:07:41.753 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] monitor closed 
[INFO ] 2024-05-27 15:07:41.756 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] close complete, cost 17 ms 
[INFO ] 2024-05-27 15:07:41.756 - [任务 16(100)] - load tapTable task 6654310bcd8f0a1c163c3446-505d2628-2573-4a4e-a17a-06f2ced389ae complete, cost 379ms 
[INFO ] 2024-05-27 15:07:42.031 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:42.031 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:42.033 - [任务 16(100)][cccd2807-3cb1-4aae-8132-6b01cf048bb2] - Node cccd2807-3cb1-4aae-8132-6b01cf048bb2[cccd2807-3cb1-4aae-8132-6b01cf048bb2] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:07:42.033 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:42.033 - [任务 16(100)][cccd2807-3cb1-4aae-8132-6b01cf048bb2] - Node cccd2807-3cb1-4aae-8132-6b01cf048bb2[cccd2807-3cb1-4aae-8132-6b01cf048bb2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:42.033 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:42.187 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:42.188 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:42.188 - [任务 16(100)][1d869ff2-554e-4cc6-94a5-f466a043f1f2] - Node 1d869ff2-554e-4cc6-94a5-f466a043f1f2[1d869ff2-554e-4cc6-94a5-f466a043f1f2] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:07:42.189 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:42.189 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:42.189 - [任务 16(100)][1d869ff2-554e-4cc6-94a5-f466a043f1f2] - Node 1d869ff2-554e-4cc6-94a5-f466a043f1f2[1d869ff2-554e-4cc6-94a5-f466a043f1f2] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:42.275 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] running status set to false 
[INFO ] 2024-05-27 15:07:42.281 - [任务 16(100)][CAR.CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:42.281 - [任务 16(100)][CAR.CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:42.288 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] schema data cleaned 
[INFO ] 2024-05-27 15:07:42.288 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] monitor closed 
[INFO ] 2024-05-27 15:07:42.334 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] close complete, cost 18 ms 
[INFO ] 2024-05-27 15:07:42.335 - [任务 16(100)][cccd2807-3cb1-4aae-8132-6b01cf048bb2] - Node cccd2807-3cb1-4aae-8132-6b01cf048bb2[cccd2807-3cb1-4aae-8132-6b01cf048bb2] running status set to false 
[INFO ] 2024-05-27 15:07:42.335 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] running status set to false 
[INFO ] 2024-05-27 15:07:42.335 - [任务 16(100)][cccd2807-3cb1-4aae-8132-6b01cf048bb2] - Node cccd2807-3cb1-4aae-8132-6b01cf048bb2[cccd2807-3cb1-4aae-8132-6b01cf048bb2] schema data cleaned 
[INFO ] 2024-05-27 15:07:42.335 - [任务 16(100)][cccd2807-3cb1-4aae-8132-6b01cf048bb2] - Node cccd2807-3cb1-4aae-8132-6b01cf048bb2[cccd2807-3cb1-4aae-8132-6b01cf048bb2] monitor closed 
[INFO ] 2024-05-27 15:07:42.335 - [任务 16(100)][cccd2807-3cb1-4aae-8132-6b01cf048bb2] - Node cccd2807-3cb1-4aae-8132-6b01cf048bb2[cccd2807-3cb1-4aae-8132-6b01cf048bb2] close complete, cost 3 ms 
[INFO ] 2024-05-27 15:07:42.339 - [任务 16(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-a403c8ad-8a0c-4c2c-9f3f-5872a4506a00 
[INFO ] 2024-05-27 15:07:42.339 - [任务 16(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-a403c8ad-8a0c-4c2c-9f3f-5872a4506a00 
[INFO ] 2024-05-27 15:07:42.340 - [任务 16(100)][增强JS] - [ScriptExecutorsManager-6654310bcd8f0a1c163c3446-7d099e27-a4a8-4eab-b05a-8e2692d9420f-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:07:42.342 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] schema data cleaned 
[INFO ] 2024-05-27 15:07:42.342 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] monitor closed 
[INFO ] 2024-05-27 15:07:42.345 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] close complete, cost 11 ms 
[INFO ] 2024-05-27 15:07:42.345 - [任务 16(100)] - load tapTable task 6654310bcd8f0a1c163c3446-cccd2807-3cb1-4aae-8132-6b01cf048bb2 complete, cost 405ms 
[INFO ] 2024-05-27 15:07:42.433 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] running status set to false 
[INFO ] 2024-05-27 15:07:42.434 - [任务 16(100)][CAR.CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:42.434 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] running status set to false 
[INFO ] 2024-05-27 15:07:42.434 - [任务 16(100)][1d869ff2-554e-4cc6-94a5-f466a043f1f2] - Node 1d869ff2-554e-4cc6-94a5-f466a043f1f2[1d869ff2-554e-4cc6-94a5-f466a043f1f2] running status set to false 
[INFO ] 2024-05-27 15:07:42.434 - [任务 16(100)][CAR.CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:42.434 - [任务 16(100)][1d869ff2-554e-4cc6-94a5-f466a043f1f2] - Node 1d869ff2-554e-4cc6-94a5-f466a043f1f2[1d869ff2-554e-4cc6-94a5-f466a043f1f2] schema data cleaned 
[INFO ] 2024-05-27 15:07:42.434 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] schema data cleaned 
[INFO ] 2024-05-27 15:07:42.435 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] monitor closed 
[INFO ] 2024-05-27 15:07:42.435 - [任务 16(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-9b2a9836-2b7f-4b1c-bd97-792e68934a35 
[INFO ] 2024-05-27 15:07:42.435 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] close complete, cost 28 ms 
[INFO ] 2024-05-27 15:07:42.435 - [任务 16(100)][1d869ff2-554e-4cc6-94a5-f466a043f1f2] - Node 1d869ff2-554e-4cc6-94a5-f466a043f1f2[1d869ff2-554e-4cc6-94a5-f466a043f1f2] monitor closed 
[INFO ] 2024-05-27 15:07:42.435 - [任务 16(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-9b2a9836-2b7f-4b1c-bd97-792e68934a35 
[INFO ] 2024-05-27 15:07:42.435 - [任务 16(100)][1d869ff2-554e-4cc6-94a5-f466a043f1f2] - Node 1d869ff2-554e-4cc6-94a5-f466a043f1f2[1d869ff2-554e-4cc6-94a5-f466a043f1f2] close complete, cost 2 ms 
[INFO ] 2024-05-27 15:07:42.435 - [任务 16(100)][增强JS] - [ScriptExecutorsManager-6654310bcd8f0a1c163c3446-7d099e27-a4a8-4eab-b05a-8e2692d9420f-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:07:42.436 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] schema data cleaned 
[INFO ] 2024-05-27 15:07:42.437 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] monitor closed 
[INFO ] 2024-05-27 15:07:42.437 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] close complete, cost 5 ms 
[INFO ] 2024-05-27 15:07:42.647 - [任务 16(100)] - load tapTable task 6654310bcd8f0a1c163c3446-1d869ff2-554e-4cc6-94a5-f466a043f1f2 complete, cost 299ms 
[INFO ] 2024-05-27 15:07:43.893 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:43.894 - [任务 16(100)][7c8fc814-c9bc-4d2b-b65f-0946e1eadca8] - Node 7c8fc814-c9bc-4d2b-b65f-0946e1eadca8[7c8fc814-c9bc-4d2b-b65f-0946e1eadca8] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:07:43.894 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:43.894 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] preload schema finished, cost 1 ms 
[INFO ] 2024-05-27 15:07:43.894 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] preload schema finished, cost 1 ms 
[INFO ] 2024-05-27 15:07:43.894 - [任务 16(100)][7c8fc814-c9bc-4d2b-b65f-0946e1eadca8] - Node 7c8fc814-c9bc-4d2b-b65f-0946e1eadca8[7c8fc814-c9bc-4d2b-b65f-0946e1eadca8] preload schema finished, cost 1 ms 
[INFO ] 2024-05-27 15:07:44.041 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] running status set to false 
[INFO ] 2024-05-27 15:07:44.041 - [任务 16(100)][CAR.CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:44.042 - [任务 16(100)][CAR.CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:44.042 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] schema data cleaned 
[INFO ] 2024-05-27 15:07:44.042 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] monitor closed 
[INFO ] 2024-05-27 15:07:44.042 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] close complete, cost 14 ms 
[INFO ] 2024-05-27 15:07:44.082 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] running status set to false 
[INFO ] 2024-05-27 15:07:44.083 - [任务 16(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-4514f860-09a1-447e-baba-7e52ffe162bd 
[INFO ] 2024-05-27 15:07:44.083 - [任务 16(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-4514f860-09a1-447e-baba-7e52ffe162bd 
[INFO ] 2024-05-27 15:07:44.083 - [任务 16(100)][增强JS] - [ScriptExecutorsManager-6654310bcd8f0a1c163c3446-7d099e27-a4a8-4eab-b05a-8e2692d9420f-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:07:44.084 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] schema data cleaned 
[INFO ] 2024-05-27 15:07:44.085 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] monitor closed 
[INFO ] 2024-05-27 15:07:44.085 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] close complete, cost 6 ms 
[INFO ] 2024-05-27 15:07:44.085 - [任务 16(100)][7c8fc814-c9bc-4d2b-b65f-0946e1eadca8] - Node 7c8fc814-c9bc-4d2b-b65f-0946e1eadca8[7c8fc814-c9bc-4d2b-b65f-0946e1eadca8] running status set to false 
[INFO ] 2024-05-27 15:07:44.085 - [任务 16(100)][7c8fc814-c9bc-4d2b-b65f-0946e1eadca8] - Node 7c8fc814-c9bc-4d2b-b65f-0946e1eadca8[7c8fc814-c9bc-4d2b-b65f-0946e1eadca8] schema data cleaned 
[INFO ] 2024-05-27 15:07:44.085 - [任务 16(100)][7c8fc814-c9bc-4d2b-b65f-0946e1eadca8] - Node 7c8fc814-c9bc-4d2b-b65f-0946e1eadca8[7c8fc814-c9bc-4d2b-b65f-0946e1eadca8] monitor closed 
[INFO ] 2024-05-27 15:07:44.086 - [任务 16(100)][7c8fc814-c9bc-4d2b-b65f-0946e1eadca8] - Node 7c8fc814-c9bc-4d2b-b65f-0946e1eadca8[7c8fc814-c9bc-4d2b-b65f-0946e1eadca8] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:07:44.289 - [任务 16(100)] - load tapTable task 6654310bcd8f0a1c163c3446-7c8fc814-c9bc-4d2b-b65f-0946e1eadca8 complete, cost 254ms 
[INFO ] 2024-05-27 15:07:44.629 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:44.630 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:44.630 - [任务 16(100)][062bc950-44c6-4d77-ae19-be8fa0c87b3d] - Node 062bc950-44c6-4d77-ae19-be8fa0c87b3d[062bc950-44c6-4d77-ae19-be8fa0c87b3d] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:07:44.630 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:44.631 - [任务 16(100)][062bc950-44c6-4d77-ae19-be8fa0c87b3d] - Node 062bc950-44c6-4d77-ae19-be8fa0c87b3d[062bc950-44c6-4d77-ae19-be8fa0c87b3d] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:44.714 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:44.714 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] running status set to false 
[INFO ] 2024-05-27 15:07:44.717 - [任务 16(100)][CAR.CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:44.717 - [任务 16(100)][CAR.CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:44.734 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] schema data cleaned 
[INFO ] 2024-05-27 15:07:44.734 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] monitor closed 
[INFO ] 2024-05-27 15:07:44.798 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] close complete, cost 20 ms 
[INFO ] 2024-05-27 15:07:44.799 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] running status set to false 
[INFO ] 2024-05-27 15:07:44.801 - [任务 16(100)][062bc950-44c6-4d77-ae19-be8fa0c87b3d] - Node 062bc950-44c6-4d77-ae19-be8fa0c87b3d[062bc950-44c6-4d77-ae19-be8fa0c87b3d] running status set to false 
[INFO ] 2024-05-27 15:07:44.801 - [任务 16(100)][062bc950-44c6-4d77-ae19-be8fa0c87b3d] - Node 062bc950-44c6-4d77-ae19-be8fa0c87b3d[062bc950-44c6-4d77-ae19-be8fa0c87b3d] schema data cleaned 
[INFO ] 2024-05-27 15:07:44.801 - [任务 16(100)][062bc950-44c6-4d77-ae19-be8fa0c87b3d] - Node 062bc950-44c6-4d77-ae19-be8fa0c87b3d[062bc950-44c6-4d77-ae19-be8fa0c87b3d] monitor closed 
[INFO ] 2024-05-27 15:07:44.801 - [任务 16(100)][062bc950-44c6-4d77-ae19-be8fa0c87b3d] - Node 062bc950-44c6-4d77-ae19-be8fa0c87b3d[062bc950-44c6-4d77-ae19-be8fa0c87b3d] close complete, cost 1 ms 
[INFO ] 2024-05-27 15:07:44.803 - [任务 16(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-b45f02da-6eff-46e7-97a9-2ac7dd65acc3 
[INFO ] 2024-05-27 15:07:44.804 - [任务 16(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-b45f02da-6eff-46e7-97a9-2ac7dd65acc3 
[INFO ] 2024-05-27 15:07:44.804 - [任务 16(100)][增强JS] - [ScriptExecutorsManager-6654310bcd8f0a1c163c3446-7d099e27-a4a8-4eab-b05a-8e2692d9420f-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:07:44.806 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] schema data cleaned 
[INFO ] 2024-05-27 15:07:44.806 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] monitor closed 
[INFO ] 2024-05-27 15:07:44.808 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] close complete, cost 11 ms 
[INFO ] 2024-05-27 15:07:44.808 - [任务 16(100)] - load tapTable task 6654310bcd8f0a1c163c3446-062bc950-44c6-4d77-ae19-be8fa0c87b3d complete, cost 214ms 
[INFO ] 2024-05-27 15:07:47.454 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:47.455 - [任务 16(100)][f2fc430d-1453-49d8-8f36-463c4ebb19eb] - Node f2fc430d-1453-49d8-8f36-463c4ebb19eb[f2fc430d-1453-49d8-8f36-463c4ebb19eb] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:07:47.455 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:47.457 - [任务 16(100)][f2fc430d-1453-49d8-8f36-463c4ebb19eb] - Node f2fc430d-1453-49d8-8f36-463c4ebb19eb[f2fc430d-1453-49d8-8f36-463c4ebb19eb] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:47.457 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] preload schema finished, cost 1 ms 
[INFO ] 2024-05-27 15:07:47.457 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] preload schema finished, cost 1 ms 
[INFO ] 2024-05-27 15:07:47.586 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] running status set to false 
[INFO ] 2024-05-27 15:07:47.587 - [任务 16(100)][CAR.CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:47.587 - [任务 16(100)][CAR.CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:47.587 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] schema data cleaned 
[INFO ] 2024-05-27 15:07:47.587 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] monitor closed 
[INFO ] 2024-05-27 15:07:47.587 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] close complete, cost 9 ms 
[INFO ] 2024-05-27 15:07:47.632 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] running status set to false 
[INFO ] 2024-05-27 15:07:47.634 - [任务 16(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-0937ead7-f204-4466-92b1-afe3b081843e 
[INFO ] 2024-05-27 15:07:47.634 - [任务 16(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-0937ead7-f204-4466-92b1-afe3b081843e 
[INFO ] 2024-05-27 15:07:47.634 - [任务 16(100)][增强JS] - [ScriptExecutorsManager-6654310bcd8f0a1c163c3446-7d099e27-a4a8-4eab-b05a-8e2692d9420f-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:07:47.635 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] schema data cleaned 
[INFO ] 2024-05-27 15:07:47.635 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] monitor closed 
[INFO ] 2024-05-27 15:07:47.637 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] close complete, cost 5 ms 
[INFO ] 2024-05-27 15:07:47.637 - [任务 16(100)][f2fc430d-1453-49d8-8f36-463c4ebb19eb] - Node f2fc430d-1453-49d8-8f36-463c4ebb19eb[f2fc430d-1453-49d8-8f36-463c4ebb19eb] running status set to false 
[INFO ] 2024-05-27 15:07:47.637 - [任务 16(100)][f2fc430d-1453-49d8-8f36-463c4ebb19eb] - Node f2fc430d-1453-49d8-8f36-463c4ebb19eb[f2fc430d-1453-49d8-8f36-463c4ebb19eb] schema data cleaned 
[INFO ] 2024-05-27 15:07:47.637 - [任务 16(100)][f2fc430d-1453-49d8-8f36-463c4ebb19eb] - Node f2fc430d-1453-49d8-8f36-463c4ebb19eb[f2fc430d-1453-49d8-8f36-463c4ebb19eb] monitor closed 
[INFO ] 2024-05-27 15:07:47.638 - [任务 16(100)][f2fc430d-1453-49d8-8f36-463c4ebb19eb] - Node f2fc430d-1453-49d8-8f36-463c4ebb19eb[f2fc430d-1453-49d8-8f36-463c4ebb19eb] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:07:47.638 - [任务 16(100)] - load tapTable task 6654310bcd8f0a1c163c3446-f2fc430d-1453-49d8-8f36-463c4ebb19eb complete, cost 264ms 
[INFO ] 2024-05-27 15:07:50.608 - [任务 16(100)][207a593b-eac6-4d64-bfc7-25f14aefe70a] - Node 207a593b-eac6-4d64-bfc7-25f14aefe70a[207a593b-eac6-4d64-bfc7-25f14aefe70a] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:07:50.608 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:50.609 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:50.609 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:50.610 - [任务 16(100)][207a593b-eac6-4d64-bfc7-25f14aefe70a] - Node 207a593b-eac6-4d64-bfc7-25f14aefe70a[207a593b-eac6-4d64-bfc7-25f14aefe70a] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:50.699 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:50.699 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] running status set to false 
[INFO ] 2024-05-27 15:07:50.702 - [任务 16(100)][CAR.CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:50.702 - [任务 16(100)][CAR.CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:50.702 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] schema data cleaned 
[INFO ] 2024-05-27 15:07:50.702 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] monitor closed 
[INFO ] 2024-05-27 15:07:50.702 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] close complete, cost 6 ms 
[INFO ] 2024-05-27 15:07:50.772 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] running status set to false 
[INFO ] 2024-05-27 15:07:50.781 - [任务 16(100)][207a593b-eac6-4d64-bfc7-25f14aefe70a] - Node 207a593b-eac6-4d64-bfc7-25f14aefe70a[207a593b-eac6-4d64-bfc7-25f14aefe70a] running status set to false 
[INFO ] 2024-05-27 15:07:50.781 - [任务 16(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-b279d49f-788e-4bb1-98eb-4daf53fec9bc 
[INFO ] 2024-05-27 15:07:50.782 - [任务 16(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-b279d49f-788e-4bb1-98eb-4daf53fec9bc 
[INFO ] 2024-05-27 15:07:50.782 - [任务 16(100)][207a593b-eac6-4d64-bfc7-25f14aefe70a] - Node 207a593b-eac6-4d64-bfc7-25f14aefe70a[207a593b-eac6-4d64-bfc7-25f14aefe70a] schema data cleaned 
[INFO ] 2024-05-27 15:07:50.782 - [任务 16(100)][增强JS] - [ScriptExecutorsManager-6654310bcd8f0a1c163c3446-7d099e27-a4a8-4eab-b05a-8e2692d9420f-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:07:50.782 - [任务 16(100)][207a593b-eac6-4d64-bfc7-25f14aefe70a] - Node 207a593b-eac6-4d64-bfc7-25f14aefe70a[207a593b-eac6-4d64-bfc7-25f14aefe70a] monitor closed 
[INFO ] 2024-05-27 15:07:50.782 - [任务 16(100)][207a593b-eac6-4d64-bfc7-25f14aefe70a] - Node 207a593b-eac6-4d64-bfc7-25f14aefe70a[207a593b-eac6-4d64-bfc7-25f14aefe70a] close complete, cost 7 ms 
[INFO ] 2024-05-27 15:07:50.784 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] schema data cleaned 
[INFO ] 2024-05-27 15:07:50.785 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] monitor closed 
[INFO ] 2024-05-27 15:07:50.785 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] close complete, cost 14 ms 
[INFO ] 2024-05-27 15:07:50.995 - [任务 16(100)] - load tapTable task 6654310bcd8f0a1c163c3446-207a593b-eac6-4d64-bfc7-25f14aefe70a complete, cost 221ms 
[INFO ] 2024-05-27 15:07:52.156 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:52.158 - [任务 16(100)][a72e6e2b-8ea5-4a89-ab93-ee3d878f3c77] - Node a72e6e2b-8ea5-4a89-ab93-ee3d878f3c77[a72e6e2b-8ea5-4a89-ab93-ee3d878f3c77] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:07:52.158 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:52.158 - [任务 16(100)][a72e6e2b-8ea5-4a89-ab93-ee3d878f3c77] - Node a72e6e2b-8ea5-4a89-ab93-ee3d878f3c77[a72e6e2b-8ea5-4a89-ab93-ee3d878f3c77] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:52.158 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:52.158 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] preload schema finished, cost 1 ms 
[INFO ] 2024-05-27 15:07:52.302 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] running status set to false 
[INFO ] 2024-05-27 15:07:52.305 - [任务 16(100)][CAR.CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:52.305 - [任务 16(100)][CAR.CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:52.306 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] schema data cleaned 
[INFO ] 2024-05-27 15:07:52.306 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] monitor closed 
[INFO ] 2024-05-27 15:07:52.352 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] close complete, cost 6 ms 
[INFO ] 2024-05-27 15:07:52.352 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] running status set to false 
[INFO ] 2024-05-27 15:07:52.356 - [任务 16(100)][a72e6e2b-8ea5-4a89-ab93-ee3d878f3c77] - Node a72e6e2b-8ea5-4a89-ab93-ee3d878f3c77[a72e6e2b-8ea5-4a89-ab93-ee3d878f3c77] running status set to false 
[INFO ] 2024-05-27 15:07:52.356 - [任务 16(100)][a72e6e2b-8ea5-4a89-ab93-ee3d878f3c77] - Node a72e6e2b-8ea5-4a89-ab93-ee3d878f3c77[a72e6e2b-8ea5-4a89-ab93-ee3d878f3c77] schema data cleaned 
[INFO ] 2024-05-27 15:07:52.357 - [任务 16(100)][a72e6e2b-8ea5-4a89-ab93-ee3d878f3c77] - Node a72e6e2b-8ea5-4a89-ab93-ee3d878f3c77[a72e6e2b-8ea5-4a89-ab93-ee3d878f3c77] monitor closed 
[INFO ] 2024-05-27 15:07:52.357 - [任务 16(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-ccf18921-d5d6-44f6-88b5-a2cb9a94fd08 
[INFO ] 2024-05-27 15:07:52.358 - [任务 16(100)][a72e6e2b-8ea5-4a89-ab93-ee3d878f3c77] - Node a72e6e2b-8ea5-4a89-ab93-ee3d878f3c77[a72e6e2b-8ea5-4a89-ab93-ee3d878f3c77] close complete, cost 6 ms 
[INFO ] 2024-05-27 15:07:52.358 - [任务 16(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-ccf18921-d5d6-44f6-88b5-a2cb9a94fd08 
[INFO ] 2024-05-27 15:07:52.360 - [任务 16(100)][增强JS] - [ScriptExecutorsManager-6654310bcd8f0a1c163c3446-7d099e27-a4a8-4eab-b05a-8e2692d9420f-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:07:52.360 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] schema data cleaned 
[INFO ] 2024-05-27 15:07:52.361 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] monitor closed 
[INFO ] 2024-05-27 15:07:52.361 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] close complete, cost 12 ms 
[INFO ] 2024-05-27 15:07:52.565 - [任务 16(100)] - load tapTable task 6654310bcd8f0a1c163c3446-a72e6e2b-8ea5-4a89-ab93-ee3d878f3c77 complete, cost 275ms 
[INFO ] 2024-05-27 15:07:56.395 - [任务 16(100)][c802cfad-b365-4b9d-b64f-3f66d40f3932] - Node c802cfad-b365-4b9d-b64f-3f66d40f3932[c802cfad-b365-4b9d-b64f-3f66d40f3932] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:07:56.396 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:56.396 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:07:56.396 - [任务 16(100)][c802cfad-b365-4b9d-b64f-3f66d40f3932] - Node c802cfad-b365-4b9d-b64f-3f66d40f3932[c802cfad-b365-4b9d-b64f-3f66d40f3932] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:56.396 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:56.396 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:07:56.490 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] running status set to false 
[INFO ] 2024-05-27 15:07:56.492 - [任务 16(100)][CAR.CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:56.492 - [任务 16(100)][CAR.CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:07:56.492 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] schema data cleaned 
[INFO ] 2024-05-27 15:07:56.492 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] monitor closed 
[INFO ] 2024-05-27 15:07:56.492 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] close complete, cost 4 ms 
[INFO ] 2024-05-27 15:07:56.628 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] running status set to false 
[INFO ] 2024-05-27 15:07:56.628 - [任务 16(100)][c802cfad-b365-4b9d-b64f-3f66d40f3932] - Node c802cfad-b365-4b9d-b64f-3f66d40f3932[c802cfad-b365-4b9d-b64f-3f66d40f3932] running status set to false 
[INFO ] 2024-05-27 15:07:56.628 - [任务 16(100)][c802cfad-b365-4b9d-b64f-3f66d40f3932] - Node c802cfad-b365-4b9d-b64f-3f66d40f3932[c802cfad-b365-4b9d-b64f-3f66d40f3932] schema data cleaned 
[INFO ] 2024-05-27 15:07:56.628 - [任务 16(100)][c802cfad-b365-4b9d-b64f-3f66d40f3932] - Node c802cfad-b365-4b9d-b64f-3f66d40f3932[c802cfad-b365-4b9d-b64f-3f66d40f3932] monitor closed 
[INFO ] 2024-05-27 15:07:56.628 - [任务 16(100)][c802cfad-b365-4b9d-b64f-3f66d40f3932] - Node c802cfad-b365-4b9d-b64f-3f66d40f3932[c802cfad-b365-4b9d-b64f-3f66d40f3932] close complete, cost 2 ms 
[INFO ] 2024-05-27 15:07:56.632 - [任务 16(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-0f05b0dc-38d2-471b-8e83-d938548600b2 
[INFO ] 2024-05-27 15:07:56.632 - [任务 16(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-0f05b0dc-38d2-471b-8e83-d938548600b2 
[INFO ] 2024-05-27 15:07:56.634 - [任务 16(100)][增强JS] - [ScriptExecutorsManager-6654310bcd8f0a1c163c3446-7d099e27-a4a8-4eab-b05a-8e2692d9420f-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:07:56.634 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] schema data cleaned 
[INFO ] 2024-05-27 15:07:56.634 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] monitor closed 
[INFO ] 2024-05-27 15:07:56.635 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] close complete, cost 10 ms 
[INFO ] 2024-05-27 15:07:56.840 - [任务 16(100)] - load tapTable task 6654310bcd8f0a1c163c3446-c802cfad-b365-4b9d-b64f-3f66d40f3932 complete, cost 282ms 
[INFO ] 2024-05-27 15:08:05.376 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:08:05.376 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:08:05.382 - [任务 16(100)][e1bad15d-41b3-46d9-91c4-922dd05de526] - Node e1bad15d-41b3-46d9-91c4-922dd05de526[e1bad15d-41b3-46d9-91c4-922dd05de526] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:08:05.383 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:08:05.383 - [任务 16(100)][e1bad15d-41b3-46d9-91c4-922dd05de526] - Node e1bad15d-41b3-46d9-91c4-922dd05de526[e1bad15d-41b3-46d9-91c4-922dd05de526] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:08:05.383 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:08:05.510 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] running status set to false 
[INFO ] 2024-05-27 15:08:05.510 - [任务 16(100)][CAR.CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:08:05.511 - [任务 16(100)][CAR.CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:08:05.511 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] schema data cleaned 
[INFO ] 2024-05-27 15:08:05.511 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] monitor closed 
[INFO ] 2024-05-27 15:08:05.511 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] close complete, cost 5 ms 
[INFO ] 2024-05-27 15:08:05.561 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] running status set to false 
[INFO ] 2024-05-27 15:08:05.561 - [任务 16(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-6a5efef8-2c91-4090-93bc-6dbf0cda73fc 
[INFO ] 2024-05-27 15:08:05.561 - [任务 16(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-6a5efef8-2c91-4090-93bc-6dbf0cda73fc 
[INFO ] 2024-05-27 15:08:05.561 - [任务 16(100)][增强JS] - [ScriptExecutorsManager-6654310bcd8f0a1c163c3446-7d099e27-a4a8-4eab-b05a-8e2692d9420f-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:08:05.562 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] schema data cleaned 
[INFO ] 2024-05-27 15:08:05.562 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] monitor closed 
[INFO ] 2024-05-27 15:08:05.613 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] close complete, cost 4 ms 
[INFO ] 2024-05-27 15:08:05.613 - [任务 16(100)][e1bad15d-41b3-46d9-91c4-922dd05de526] - Node e1bad15d-41b3-46d9-91c4-922dd05de526[e1bad15d-41b3-46d9-91c4-922dd05de526] running status set to false 
[INFO ] 2024-05-27 15:08:05.614 - [任务 16(100)][e1bad15d-41b3-46d9-91c4-922dd05de526] - Node e1bad15d-41b3-46d9-91c4-922dd05de526[e1bad15d-41b3-46d9-91c4-922dd05de526] schema data cleaned 
[INFO ] 2024-05-27 15:08:05.614 - [任务 16(100)][e1bad15d-41b3-46d9-91c4-922dd05de526] - Node e1bad15d-41b3-46d9-91c4-922dd05de526[e1bad15d-41b3-46d9-91c4-922dd05de526] monitor closed 
[INFO ] 2024-05-27 15:08:05.614 - [任务 16(100)][e1bad15d-41b3-46d9-91c4-922dd05de526] - Node e1bad15d-41b3-46d9-91c4-922dd05de526[e1bad15d-41b3-46d9-91c4-922dd05de526] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:08:05.817 - [任务 16(100)] - load tapTable task 6654310bcd8f0a1c163c3446-e1bad15d-41b3-46d9-91c4-922dd05de526 complete, cost 316ms 
[INFO ] 2024-05-27 15:08:44.226 - [任务 16(100)][6e7b5c3d-afa5-4be9-b804-8a62ed0e539a] - Node 6e7b5c3d-afa5-4be9-b804-8a62ed0e539a[6e7b5c3d-afa5-4be9-b804-8a62ed0e539a] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:08:44.227 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:08:44.227 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:08:44.227 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] preload schema finished, cost 1 ms 
[INFO ] 2024-05-27 15:08:44.227 - [任务 16(100)][6e7b5c3d-afa5-4be9-b804-8a62ed0e539a] - Node 6e7b5c3d-afa5-4be9-b804-8a62ed0e539a[6e7b5c3d-afa5-4be9-b804-8a62ed0e539a] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:08:44.227 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:08:44.385 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] running status set to false 
[INFO ] 2024-05-27 15:08:44.388 - [任务 16(100)][CAR.CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:08:44.388 - [任务 16(100)][CAR.CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:08:44.389 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] schema data cleaned 
[INFO ] 2024-05-27 15:08:44.389 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] monitor closed 
[INFO ] 2024-05-27 15:08:44.432 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] close complete, cost 9 ms 
[INFO ] 2024-05-27 15:08:44.432 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] running status set to false 
[INFO ] 2024-05-27 15:08:44.434 - [任务 16(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-df1f34d6-6283-4158-9064-fd0447444a7b 
[INFO ] 2024-05-27 15:08:44.449 - [任务 16(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-df1f34d6-6283-4158-9064-fd0447444a7b 
[INFO ] 2024-05-27 15:08:44.453 - [任务 16(100)][增强JS] - [ScriptExecutorsManager-6654310bcd8f0a1c163c3446-7d099e27-a4a8-4eab-b05a-8e2692d9420f-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:08:44.453 - [任务 16(100)][6e7b5c3d-afa5-4be9-b804-8a62ed0e539a] - Node 6e7b5c3d-afa5-4be9-b804-8a62ed0e539a[6e7b5c3d-afa5-4be9-b804-8a62ed0e539a] running status set to false 
[INFO ] 2024-05-27 15:08:44.453 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] schema data cleaned 
[INFO ] 2024-05-27 15:08:44.453 - [任务 16(100)][6e7b5c3d-afa5-4be9-b804-8a62ed0e539a] - Node 6e7b5c3d-afa5-4be9-b804-8a62ed0e539a[6e7b5c3d-afa5-4be9-b804-8a62ed0e539a] schema data cleaned 
[INFO ] 2024-05-27 15:08:44.453 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] monitor closed 
[INFO ] 2024-05-27 15:08:44.453 - [任务 16(100)][6e7b5c3d-afa5-4be9-b804-8a62ed0e539a] - Node 6e7b5c3d-afa5-4be9-b804-8a62ed0e539a[6e7b5c3d-afa5-4be9-b804-8a62ed0e539a] monitor closed 
[INFO ] 2024-05-27 15:08:44.453 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] close complete, cost 6 ms 
[INFO ] 2024-05-27 15:08:44.454 - [任务 16(100)][6e7b5c3d-afa5-4be9-b804-8a62ed0e539a] - Node 6e7b5c3d-afa5-4be9-b804-8a62ed0e539a[6e7b5c3d-afa5-4be9-b804-8a62ed0e539a] close complete, cost 1 ms 
[INFO ] 2024-05-27 15:08:44.454 - [任务 16(100)] - load tapTable task 6654310bcd8f0a1c163c3446-6e7b5c3d-afa5-4be9-b804-8a62ed0e539a complete, cost 319ms 
[INFO ] 2024-05-27 15:08:57.792 - [任务 16(100)][b484a55c-7c3e-4ab1-a8a9-c45e5d1f5bad] - Node b484a55c-7c3e-4ab1-a8a9-c45e5d1f5bad[b484a55c-7c3e-4ab1-a8a9-c45e5d1f5bad] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:08:57.792 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:08:57.793 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:08:57.793 - [任务 16(100)][b484a55c-7c3e-4ab1-a8a9-c45e5d1f5bad] - Node b484a55c-7c3e-4ab1-a8a9-c45e5d1f5bad[b484a55c-7c3e-4ab1-a8a9-c45e5d1f5bad] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:08:57.793 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:08:57.793 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:08:57.875 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] running status set to false 
[INFO ] 2024-05-27 15:08:57.876 - [任务 16(100)][CAR.CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:08:57.876 - [任务 16(100)][CAR.CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:08:57.876 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] schema data cleaned 
[INFO ] 2024-05-27 15:08:57.877 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] monitor closed 
[INFO ] 2024-05-27 15:08:57.964 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] close complete, cost 3 ms 
[INFO ] 2024-05-27 15:08:57.964 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] running status set to false 
[INFO ] 2024-05-27 15:08:57.965 - [任务 16(100)][b484a55c-7c3e-4ab1-a8a9-c45e5d1f5bad] - Node b484a55c-7c3e-4ab1-a8a9-c45e5d1f5bad[b484a55c-7c3e-4ab1-a8a9-c45e5d1f5bad] running status set to false 
[INFO ] 2024-05-27 15:08:57.965 - [任务 16(100)][b484a55c-7c3e-4ab1-a8a9-c45e5d1f5bad] - Node b484a55c-7c3e-4ab1-a8a9-c45e5d1f5bad[b484a55c-7c3e-4ab1-a8a9-c45e5d1f5bad] schema data cleaned 
[INFO ] 2024-05-27 15:08:57.965 - [任务 16(100)][b484a55c-7c3e-4ab1-a8a9-c45e5d1f5bad] - Node b484a55c-7c3e-4ab1-a8a9-c45e5d1f5bad[b484a55c-7c3e-4ab1-a8a9-c45e5d1f5bad] monitor closed 
[INFO ] 2024-05-27 15:08:57.965 - [任务 16(100)][b484a55c-7c3e-4ab1-a8a9-c45e5d1f5bad] - Node b484a55c-7c3e-4ab1-a8a9-c45e5d1f5bad[b484a55c-7c3e-4ab1-a8a9-c45e5d1f5bad] close complete, cost 1 ms 
[INFO ] 2024-05-27 15:08:57.969 - [任务 16(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-2f51dc99-8d54-47aa-b16f-314390f7e2fe 
[INFO ] 2024-05-27 15:08:57.970 - [任务 16(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-2f51dc99-8d54-47aa-b16f-314390f7e2fe 
[INFO ] 2024-05-27 15:08:57.970 - [任务 16(100)][增强JS] - [ScriptExecutorsManager-6654310bcd8f0a1c163c3446-7d099e27-a4a8-4eab-b05a-8e2692d9420f-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:08:57.972 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] schema data cleaned 
[INFO ] 2024-05-27 15:08:57.972 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] monitor closed 
[INFO ] 2024-05-27 15:08:57.974 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] close complete, cost 11 ms 
[INFO ] 2024-05-27 15:08:57.974 - [任务 16(100)] - load tapTable task 6654310bcd8f0a1c163c3446-b484a55c-7c3e-4ab1-a8a9-c45e5d1f5bad complete, cost 227ms 
[INFO ] 2024-05-27 15:08:58.976 - [任务 16(100)][9b9bb806-5870-480e-9257-3218946f7e94] - Node 9b9bb806-5870-480e-9257-3218946f7e94[9b9bb806-5870-480e-9257-3218946f7e94] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:08:58.977 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:08:58.977 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:08:58.981 - [任务 16(100)][9b9bb806-5870-480e-9257-3218946f7e94] - Node 9b9bb806-5870-480e-9257-3218946f7e94[9b9bb806-5870-480e-9257-3218946f7e94] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:08:58.981 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] preload schema finished, cost 2 ms 
[INFO ] 2024-05-27 15:08:58.981 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:08:59.182 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] running status set to false 
[INFO ] 2024-05-27 15:08:59.186 - [任务 16(100)][CAR.CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:08:59.186 - [任务 16(100)][CAR.CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:08:59.186 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] schema data cleaned 
[INFO ] 2024-05-27 15:08:59.186 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] monitor closed 
[INFO ] 2024-05-27 15:08:59.214 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] close complete, cost 8 ms 
[INFO ] 2024-05-27 15:08:59.215 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] running status set to false 
[INFO ] 2024-05-27 15:08:59.216 - [任务 16(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-76fee8c8-cc44-4ef6-8689-fa79bf49fad3 
[INFO ] 2024-05-27 15:08:59.217 - [任务 16(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-76fee8c8-cc44-4ef6-8689-fa79bf49fad3 
[INFO ] 2024-05-27 15:08:59.217 - [任务 16(100)][增强JS] - [ScriptExecutorsManager-6654310bcd8f0a1c163c3446-7d099e27-a4a8-4eab-b05a-8e2692d9420f-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:08:59.219 - [任务 16(100)][9b9bb806-5870-480e-9257-3218946f7e94] - Node 9b9bb806-5870-480e-9257-3218946f7e94[9b9bb806-5870-480e-9257-3218946f7e94] running status set to false 
[INFO ] 2024-05-27 15:08:59.219 - [任务 16(100)][9b9bb806-5870-480e-9257-3218946f7e94] - Node 9b9bb806-5870-480e-9257-3218946f7e94[9b9bb806-5870-480e-9257-3218946f7e94] schema data cleaned 
[INFO ] 2024-05-27 15:08:59.219 - [任务 16(100)][9b9bb806-5870-480e-9257-3218946f7e94] - Node 9b9bb806-5870-480e-9257-3218946f7e94[9b9bb806-5870-480e-9257-3218946f7e94] monitor closed 
[INFO ] 2024-05-27 15:08:59.219 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] schema data cleaned 
[INFO ] 2024-05-27 15:08:59.219 - [任务 16(100)][9b9bb806-5870-480e-9257-3218946f7e94] - Node 9b9bb806-5870-480e-9257-3218946f7e94[9b9bb806-5870-480e-9257-3218946f7e94] close complete, cost 0 ms 
[INFO ] 2024-05-27 15:08:59.219 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] monitor closed 
[INFO ] 2024-05-27 15:08:59.220 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] close complete, cost 5 ms 
[INFO ] 2024-05-27 15:08:59.220 - [任务 16(100)] - load tapTable task 6654310bcd8f0a1c163c3446-9b9bb806-5870-480e-9257-3218946f7e94 complete, cost 329ms 
[INFO ] 2024-05-27 15:08:59.613 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:08:59.614 - [任务 16(100)][2ef9cfba-a675-4728-b60c-17edab5fbaee] - Node 2ef9cfba-a675-4728-b60c-17edab5fbaee[2ef9cfba-a675-4728-b60c-17edab5fbaee] start preload schema,table counts: 0 
[INFO ] 2024-05-27 15:08:59.614 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:08:59.614 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] start preload schema,table counts: 1 
[INFO ] 2024-05-27 15:08:59.615 - [任务 16(100)][2ef9cfba-a675-4728-b60c-17edab5fbaee] - Node 2ef9cfba-a675-4728-b60c-17edab5fbaee[2ef9cfba-a675-4728-b60c-17edab5fbaee] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:08:59.615 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] preload schema finished, cost 0 ms 
[INFO ] 2024-05-27 15:08:59.709 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] running status set to false 
[INFO ] 2024-05-27 15:08:59.709 - [任务 16(100)][CAR.CLAIM] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:08:59.710 - [任务 16(100)][CAR.CLAIM] - PDK connector node released: HazelcastSampleSourcePdkDataNode-33c3983a-91c8-47b5-9e77-e00e63f38fda 
[INFO ] 2024-05-27 15:08:59.710 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] schema data cleaned 
[INFO ] 2024-05-27 15:08:59.711 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] monitor closed 
[INFO ] 2024-05-27 15:08:59.711 - [任务 16(100)][CAR.CLAIM] - Node TEST2[33c3983a-91c8-47b5-9e77-e00e63f38fda] close complete, cost 10 ms 
[INFO ] 2024-05-27 15:08:59.779 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] running status set to false 
[INFO ] 2024-05-27 15:08:59.780 - [任务 16(100)][2ef9cfba-a675-4728-b60c-17edab5fbaee] - Node 2ef9cfba-a675-4728-b60c-17edab5fbaee[2ef9cfba-a675-4728-b60c-17edab5fbaee] running status set to false 
[INFO ] 2024-05-27 15:08:59.780 - [任务 16(100)][2ef9cfba-a675-4728-b60c-17edab5fbaee] - Node 2ef9cfba-a675-4728-b60c-17edab5fbaee[2ef9cfba-a675-4728-b60c-17edab5fbaee] schema data cleaned 
[INFO ] 2024-05-27 15:08:59.780 - [任务 16(100)][2ef9cfba-a675-4728-b60c-17edab5fbaee] - Node 2ef9cfba-a675-4728-b60c-17edab5fbaee[2ef9cfba-a675-4728-b60c-17edab5fbaee] monitor closed 
[INFO ] 2024-05-27 15:08:59.781 - [任务 16(100)][2ef9cfba-a675-4728-b60c-17edab5fbaee] - Node 2ef9cfba-a675-4728-b60c-17edab5fbaee[2ef9cfba-a675-4728-b60c-17edab5fbaee] close complete, cost 1 ms 
[INFO ] 2024-05-27 15:08:59.781 - [任务 16(100)][增强JS] - PDK connector node stopped: ScriptExecutor-SourceMongo-07809bda-6fc0-4ec9-8385-ec874e1efec8 
[INFO ] 2024-05-27 15:08:59.781 - [任务 16(100)][增强JS] - PDK connector node released: ScriptExecutor-SourceMongo-07809bda-6fc0-4ec9-8385-ec874e1efec8 
[INFO ] 2024-05-27 15:08:59.781 - [任务 16(100)][增强JS] - [ScriptExecutorsManager-6654310bcd8f0a1c163c3446-7d099e27-a4a8-4eab-b05a-8e2692d9420f-6641f7f559c27f6193737bca] schema data cleaned 
[INFO ] 2024-05-27 15:08:59.783 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] schema data cleaned 
[INFO ] 2024-05-27 15:08:59.783 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] monitor closed 
[INFO ] 2024-05-27 15:08:59.784 - [任务 16(100)][增强JS] - Node 增强JS[7d099e27-a4a8-4eab-b05a-8e2692d9420f] close complete, cost 10 ms 
[INFO ] 2024-05-27 15:08:59.785 - [任务 16(100)] - load tapTable task 6654310bcd8f0a1c163c3446-2ef9cfba-a675-4728-b60c-17edab5fbaee complete, cost 212ms 
