[INFO ] 2024-05-27 10:35:26.293 - [任务 1] - Task initialization... 
[INFO ] 2024-05-27 10:35:26.294 - [任务 1] - Start task milestones: 6653f0ee92b629235ddaf232(任务 1) 
[INFO ] 2024-05-27 10:35:26.294 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-27 10:35:26.295 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-27 10:35:26.682 - [任务 1][TEST2] - Node TEST2[6feb7b6a-242f-4e32-bbb6-fc3b18adf56c] start preload schema,table counts: 1 
[INFO ] 2024-05-27 10:35:26.853 - [任务 1][TEST2] - Node TEST2[147d9bee-535d-4152-823e-737d1ab6cec8] start preload schema,table counts: 1 
[INFO ] 2024-05-27 10:35:26.853 - [任务 1][TEST2] - Node TEST2[6feb7b6a-242f-4e32-bbb6-fc3b18adf56c] preload schema finished, cost 172 ms 
[INFO ] 2024-05-27 10:35:27.054 - [任务 1][TEST2] - Node TEST2[147d9bee-535d-4152-823e-737d1ab6cec8] preload schema finished, cost 145 ms 
[INFO ] 2024-05-27 10:35:27.910 - [任务 1][TEST2] - Source node "TEST2" read batch size: 100 
[INFO ] 2024-05-27 10:35:27.913 - [任务 1][TEST2] - Source node "TEST2" event queue capacity: 200 
[INFO ] 2024-05-27 10:35:27.914 - [任务 1][TEST2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-27 10:35:28.010 - [任务 1][TEST2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-27 10:35:28.218 - [任务 1][TEST2] - batch offset found: {},stream offset found: {"cdcOffset":1716777327,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-05-27 10:35:28.257 - [任务 1][TEST2] - Initial sync started 
[INFO ] 2024-05-27 10:35:28.263 - [任务 1][TEST2] - Starting batch read, table name: TEST2, offset: null 
[INFO ] 2024-05-27 10:35:28.356 - [任务 1][TEST2] - Table TEST2 is going to be initial synced 
[INFO ] 2024-05-27 10:35:28.356 - [任务 1][TEST2] - Query table 'TEST2' counts: 1076 
[INFO ] 2024-05-27 10:35:28.501 - [任务 1][TEST2] - Initial sync completed 
[INFO ] 2024-05-27 10:35:28.501 - [任务 1][TEST2] - Incremental sync starting... 
[INFO ] 2024-05-27 10:35:28.501 - [任务 1][TEST2] - Initial sync completed 
[INFO ] 2024-05-27 10:35:28.516 - [任务 1][TEST2] - Starting stream read, table list: [TEST2], offset: {"cdcOffset":1716777327,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-05-27 10:35:28.722 - [任务 1][TEST2] - Connector MongoDB incremental start succeed, tables: [TEST2], data change syncing 
[WARN ] 2024-05-27 10:36:34.335 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: record Time Out
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-27 10:37:35.478 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-05-27 10:37:46.622 - [任务 1][TEST2] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLException: Connection is closed
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-27 10:38:49.268 - [任务 1][TEST2] - [Auto Retry] Method (target_write_record) retry succeed 
[WARN ] 2024-05-27 10:39:09.742 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: record Time Out
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-05-27 10:40:09.854 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: time out
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-27 10:41:11.220 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-05-27 10:42:04.395 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: record Time Out
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-27 10:46:04.497 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-05-27 10:46:04.715 - [任务 1][TEST2] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLException: Connection is closed
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-27 10:47:06.602 - [任务 1][TEST2] - [Auto Retry] Method (target_write_record) retry succeed 
[WARN ] 2024-05-27 10:47:51.487 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: record Time Out
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-05-27 10:48:51.567 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: time out
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-27 10:49:52.649 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-05-27 10:51:52.201 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: record Time Out
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-27 10:52:53.416 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-05-27 10:53:16.354 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: record Time Out
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-05-27 10:54:16.262 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): com.mongodb.MongoTimeoutException: time out
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-27 10:55:17.695 - [任务 1][TEST2] - [Auto Retry] Method (source_stream_read) retry succeed 
