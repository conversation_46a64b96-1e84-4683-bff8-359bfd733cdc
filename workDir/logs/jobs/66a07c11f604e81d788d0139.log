[INFO ] 2024-07-24 11:59:13.866 - [Heartbeat-Mssql] - Start task milestones: 66a07c11f604e81d788d0139(Heartbeat-Mssql) 
[INFO ] 2024-07-24 11:59:14.045 - [Heartbeat-Mssql] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-24 11:59:14.095 - [Heartbeat-Mssql] - The engine receives Heartbeat-Mssql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 11:59:14.165 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] start preload schema,table counts: 1 
[INFO ] 2024-07-24 11:59:14.171 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] start preload schema,table counts: 1 
[INFO ] 2024-07-24 11:59:14.178 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 11:59:14.185 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 11:59:15.344 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-24 11:59:15.474 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-24 11:59:15.474 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-24 11:59:15.474 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-24 11:59:15.474 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721793555473,"lastTimes":1721793555473,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-24 11:59:15.561 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-24 11:59:15.561 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-24 11:59:15.577 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-24 11:59:15.581 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 11:59:15.581 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-24 11:59:15.582 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1721793555473,"lastTimes":1721793555473,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-24 11:59:15.584 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-24 11:59:15.584 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-24 12:06:32.843 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] running status set to false 
[INFO ] 2024-07-24 12:06:32.844 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-24 12:06:32.866 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-de8fa15d-cb43-45e8-a873-af6458e90786 
[INFO ] 2024-07-24 12:06:32.866 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-de8fa15d-cb43-45e8-a873-af6458e90786 
[INFO ] 2024-07-24 12:06:32.866 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] schema data cleaned 
[INFO ] 2024-07-24 12:06:32.866 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] monitor closed 
[INFO ] 2024-07-24 12:06:32.868 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] close complete, cost 35 ms 
[INFO ] 2024-07-24 12:06:32.868 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] running status set to false 
[INFO ] 2024-07-24 12:06:32.883 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-23fbfb68-045b-4d36-81a3-e8c3669d00f3 
[INFO ] 2024-07-24 12:06:32.883 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-23fbfb68-045b-4d36-81a3-e8c3669d00f3 
[INFO ] 2024-07-24 12:06:32.884 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] schema data cleaned 
[INFO ] 2024-07-24 12:06:32.884 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] monitor closed 
[INFO ] 2024-07-24 12:06:32.886 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] close complete, cost 16 ms 
[INFO ] 2024-07-24 12:06:37.180 - [Heartbeat-Mssql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 12:06:37.181 - [Heartbeat-Mssql] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5deb67e1 
[INFO ] 2024-07-24 12:06:37.181 - [Heartbeat-Mssql] - Stop task milestones: 66a07c11f604e81d788d0139(Heartbeat-Mssql)  
[INFO ] 2024-07-24 12:06:37.298 - [Heartbeat-Mssql] - Stopped task aspect(s) 
[INFO ] 2024-07-24 12:06:37.299 - [Heartbeat-Mssql] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 12:06:37.327 - [Heartbeat-Mssql] - Remove memory task client succeed, task: Heartbeat-Mssql[66a07c11f604e81d788d0139] 
[INFO ] 2024-07-24 12:06:37.327 - [Heartbeat-Mssql] - Destroy memory task client cache succeed, task: Heartbeat-Mssql[66a07c11f604e81d788d0139] 
[INFO ] 2024-07-24 12:06:43.770 - [Heartbeat-Mssql] - Start task milestones: 66a07c11f604e81d788d0139(Heartbeat-Mssql) 
[INFO ] 2024-07-24 12:06:43.868 - [Heartbeat-Mssql] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 12:06:43.899 - [Heartbeat-Mssql] - The engine receives Heartbeat-Mssql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 12:06:43.976 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] start preload schema,table counts: 1 
[INFO ] 2024-07-24 12:06:43.977 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] start preload schema,table counts: 1 
[INFO ] 2024-07-24 12:06:43.978 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] preload schema finished, cost 1 ms 
[INFO ] 2024-07-24 12:06:43.981 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 12:06:44.175 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-24 12:06:44.176 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-24 12:06:44.179 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-24 12:06:44.282 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - batch offset found: {"_tapdata_heartbeat_table":{"batch_read_connector_offset":{"syncStage":"Initial","beginTimes":1721793555561,"lastTimes":1721793555563,"lastTN":1,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1,"updateTotals":0,"deleteTotals":0}}},"batch_read_connector_status":"RUNNING"}},stream offset found: {"syncStage":"Incremental","beginTimes":1721793555473,"lastTimes":1721793992825,"lastTN":385,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":384,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-24 12:06:44.282 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721793555473,"lastTimes":1721793992825,"lastTN":385,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":384,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-24 12:06:44.283 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-24 12:06:44.283 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-24 12:06:44.292 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-24 12:15:46.751 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] running status set to false 
[INFO ] 2024-07-24 12:15:46.751 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Stop connector 
[WARN ] 2024-07-24 12:15:46.849 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-de8fa15d-cb43-45e8-a873-af6458e90786 
[INFO ] 2024-07-24 12:18:03.704 - [Heartbeat-Mssql] - Start task milestones: 66a07c11f604e81d788d0139(Heartbeat-Mssql) 
[INFO ] 2024-07-24 12:18:03.704 - [Heartbeat-Mssql] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 12:18:03.813 - [Heartbeat-Mssql] - The engine receives Heartbeat-Mssql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 12:18:03.813 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] start preload schema,table counts: 1 
[INFO ] 2024-07-24 12:18:03.816 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] start preload schema,table counts: 1 
[INFO ] 2024-07-24 12:18:03.818 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 12:18:03.818 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 12:18:04.155 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-24 12:18:04.156 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-24 12:18:04.156 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-24 12:18:04.185 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-24 12:18:04.185 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1721793555473,"lastTimes":1721794544812,"lastTN":443,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":827,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-24 12:18:04.185 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-24 12:18:04.287 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721793555473,"lastTimes":1721794544812,"lastTN":443,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":827,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-24 12:18:04.287 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-24 12:18:04.288 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-24 12:22:09.704 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] running status set to false 
[INFO ] 2024-07-24 12:22:09.713 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-24 12:22:09.745 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-de8fa15d-cb43-45e8-a873-af6458e90786 
[INFO ] 2024-07-24 12:22:09.746 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-de8fa15d-cb43-45e8-a873-af6458e90786 
[INFO ] 2024-07-24 12:22:09.746 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] schema data cleaned 
[INFO ] 2024-07-24 12:22:09.746 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] monitor closed 
[INFO ] 2024-07-24 12:22:09.757 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] close complete, cost 70 ms 
[INFO ] 2024-07-24 12:22:09.758 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] running status set to false 
[INFO ] 2024-07-24 12:22:09.795 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-23fbfb68-045b-4d36-81a3-e8c3669d00f3 
[INFO ] 2024-07-24 12:22:09.797 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-23fbfb68-045b-4d36-81a3-e8c3669d00f3 
[INFO ] 2024-07-24 12:22:09.797 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] schema data cleaned 
[INFO ] 2024-07-24 12:22:09.797 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] monitor closed 
[INFO ] 2024-07-24 12:22:09.798 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] close complete, cost 40 ms 
[INFO ] 2024-07-24 12:22:10.945 - [Heartbeat-Mssql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 12:22:11.064 - [Heartbeat-Mssql] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@494baac0 
[INFO ] 2024-07-24 12:22:11.065 - [Heartbeat-Mssql] - Stop task milestones: 66a07c11f604e81d788d0139(Heartbeat-Mssql)  
[INFO ] 2024-07-24 12:22:11.095 - [Heartbeat-Mssql] - Stopped task aspect(s) 
[INFO ] 2024-07-24 12:22:11.095 - [Heartbeat-Mssql] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 12:22:11.134 - [Heartbeat-Mssql] - Remove memory task client succeed, task: Heartbeat-Mssql[66a07c11f604e81d788d0139] 
[INFO ] 2024-07-24 12:22:11.134 - [Heartbeat-Mssql] - Destroy memory task client cache succeed, task: Heartbeat-Mssql[66a07c11f604e81d788d0139] 
[INFO ] 2024-07-24 12:23:27.996 - [Heartbeat-Mssql] - Start task milestones: 66a07c11f604e81d788d0139(Heartbeat-Mssql) 
[INFO ] 2024-07-24 12:23:28.148 - [Heartbeat-Mssql] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 12:23:28.223 - [Heartbeat-Mssql] - The engine receives Heartbeat-Mssql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 12:23:28.305 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] start preload schema,table counts: 1 
[INFO ] 2024-07-24 12:23:28.308 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] preload schema finished, cost 1 ms 
[INFO ] 2024-07-24 12:23:28.309 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] start preload schema,table counts: 1 
[INFO ] 2024-07-24 12:23:28.312 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 12:23:28.563 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-24 12:23:28.845 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-24 12:23:28.847 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-24 12:23:28.847 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-24 12:23:28.852 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1721793555473,"lastTimes":1721794929167,"lastTN":173,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1000,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-24 12:23:28.852 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-24 12:23:28.925 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721793555473,"lastTimes":1721794929167,"lastTN":173,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1000,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-24 12:23:28.929 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-24 12:23:28.929 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-24 15:18:03.040 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] running status set to false 
[INFO ] 2024-07-24 15:18:03.047 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-24 15:18:03.076 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-de8fa15d-cb43-45e8-a873-af6458e90786 
[INFO ] 2024-07-24 15:18:03.077 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-de8fa15d-cb43-45e8-a873-af6458e90786 
[INFO ] 2024-07-24 15:18:03.077 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] schema data cleaned 
[INFO ] 2024-07-24 15:18:03.078 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] monitor closed 
[INFO ] 2024-07-24 15:18:03.079 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] close complete, cost 47 ms 
[INFO ] 2024-07-24 15:18:03.079 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] running status set to false 
[INFO ] 2024-07-24 15:18:03.096 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-23fbfb68-045b-4d36-81a3-e8c3669d00f3 
[INFO ] 2024-07-24 15:18:03.096 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-23fbfb68-045b-4d36-81a3-e8c3669d00f3 
[INFO ] 2024-07-24 15:18:03.096 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] schema data cleaned 
[INFO ] 2024-07-24 15:18:03.096 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] monitor closed 
[INFO ] 2024-07-24 15:18:03.303 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] close complete, cost 17 ms 
[INFO ] 2024-07-24 15:18:06.666 - [Heartbeat-Mssql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 15:18:06.794 - [Heartbeat-Mssql] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7fc783f2 
[INFO ] 2024-07-24 15:18:06.795 - [Heartbeat-Mssql] - Stop task milestones: 66a07c11f604e81d788d0139(Heartbeat-Mssql)  
[INFO ] 2024-07-24 15:18:06.818 - [Heartbeat-Mssql] - Stopped task aspect(s) 
[INFO ] 2024-07-24 15:18:06.818 - [Heartbeat-Mssql] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 15:18:06.846 - [Heartbeat-Mssql] - Remove memory task client succeed, task: Heartbeat-Mssql[66a07c11f604e81d788d0139] 
[INFO ] 2024-07-24 15:18:06.848 - [Heartbeat-Mssql] - Destroy memory task client cache succeed, task: Heartbeat-Mssql[66a07c11f604e81d788d0139] 
[INFO ] 2024-07-24 15:55:00.397 - [Heartbeat-Mssql] - Start task milestones: 66a07c11f604e81d788d0139(Heartbeat-Mssql) 
[INFO ] 2024-07-24 15:55:00.552 - [Heartbeat-Mssql] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-24 15:55:00.552 - [Heartbeat-Mssql] - The engine receives Heartbeat-Mssql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 15:55:00.637 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] start preload schema,table counts: 1 
[INFO ] 2024-07-24 15:55:00.637 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] start preload schema,table counts: 1 
[INFO ] 2024-07-24 15:55:00.637 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 15:55:00.638 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 15:55:00.776 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-24 15:55:00.776 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-24 15:55:00.778 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-24 15:55:00.782 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1721793555473,"lastTimes":1721805483025,"lastTN":9604,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":10604,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-24 15:55:00.782 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-24 15:55:00.844 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721793555473,"lastTimes":1721805483025,"lastTN":9604,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":10604,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-24 15:55:00.848 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-24 15:55:00.848 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-24 15:55:01.053 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-24 19:12:35.294 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.net.SocketException: Connection reset
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@7d6791dd: {"after":{"id":"669f67fce29f7d4f8d0974cb","ts":"2024-07-24T19:01:39.310"},"containsIllegalDate":false,"referenceTime":1721818899310,"tableId":"_tapdata_heartbeat_table","time":1721818899310,"type":300}, nodeIds=[de8fa15d-cb43-45e8-a873-af6458e90786], sourceTime=1721818899310, sourceSerialNo=null} 
[ERROR] 2024-07-24 19:12:35.296 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - java.lang.RuntimeException: java.net.SocketException: Connection reset
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@7d6791dd: {"after":{"id":"669f67fce29f7d4f8d0974cb","ts":"2024-07-24T19:01:39.310"},"containsIllegalDate":false,"referenceTime":1721818899310,"tableId":"_tapdata_heartbeat_table","time":1721818899310,"type":300}, nodeIds=[de8fa15d-cb43-45e8-a873-af6458e90786], sourceTime=1721818899310, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: java.net.SocketException: Connection reset
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@7d6791dd: {"after":{"id":"669f67fce29f7d4f8d0974cb","ts":"2024-07-24T19:01:39.310"},"containsIllegalDate":false,"referenceTime":1721818899310,"tableId":"_tapdata_heartbeat_table","time":1721818899310,"type":300}, nodeIds=[de8fa15d-cb43-45e8-a873-af6458e90786], sourceTime=1721818899310, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.net.SocketException: Connection reset
	java.net.SocketInputStream.read(SocketInputStream.java:210)
	java.net.SocketInputStream.read(SocketInputStream.java:141)
	com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2058)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.Prelogin(SQLServerConnection.java:3133)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:2966)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.net.SocketException: Connection reset
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:633)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:602)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:583)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:532)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:495)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:501)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:547)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.net.SocketException: Connection reset
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:799)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:499)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:499)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:688)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:625)
	... 12 more
Caused by: java.net.SocketException: Connection reset
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mssql.MssqlJdbcRunner.getConnection(MssqlJdbcRunner.java:33)
	at io.tapdata.common.RecordWriter.<init>(RecordWriter.java:37)
	at io.tapdata.connector.mssql.MssqlRecordWriter.<init>(MssqlRecordWriter.java:15)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:320)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:853)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:805)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 19 more
Caused by: java.net.SocketException: Connection reset
	at java.net.SocketInputStream.read(SocketInputStream.java:210)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2058)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.Prelogin(SQLServerConnection.java:3133)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:2966)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:2628)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectInternal(SQLServerConnection.java:2471)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:1470)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:915)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.access$100(HikariPool.java:71)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:712)
	... 5 more

[INFO ] 2024-07-24 19:12:35.296 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Job suspend in error handle 
[INFO ] 2024-07-24 19:12:35.491 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] running status set to false 
[INFO ] 2024-07-24 19:12:35.494 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-24 19:12:35.494 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-de8fa15d-cb43-45e8-a873-af6458e90786 
[INFO ] 2024-07-24 19:12:35.494 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-de8fa15d-cb43-45e8-a873-af6458e90786 
[INFO ] 2024-07-24 19:12:35.495 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] schema data cleaned 
[INFO ] 2024-07-24 19:12:35.495 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] monitor closed 
[INFO ] 2024-07-24 19:12:35.495 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[de8fa15d-cb43-45e8-a873-af6458e90786] close complete, cost 7 ms 
[INFO ] 2024-07-24 19:12:35.495 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] running status set to false 
[INFO ] 2024-07-24 19:12:39.809 - [Heartbeat-Mssql] - Task [Heartbeat-Mssql] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-24 19:12:44.826 - [Heartbeat-Mssql] - Task [Heartbeat-Mssql] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-24 19:12:50.040 - [Heartbeat-Mssql] - Task [Heartbeat-Mssql] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-24 19:12:54.848 - [Heartbeat-Mssql] - Task [Heartbeat-Mssql] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-24 19:29:22.228 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-23fbfb68-045b-4d36-81a3-e8c3669d00f3 
[INFO ] 2024-07-24 19:29:22.228 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-23fbfb68-045b-4d36-81a3-e8c3669d00f3 
[INFO ] 2024-07-24 19:29:22.229 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] schema data cleaned 
[INFO ] 2024-07-24 19:29:22.230 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] monitor closed 
[INFO ] 2024-07-24 19:29:22.232 - [Heartbeat-Mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[23fbfb68-045b-4d36-81a3-e8c3669d00f3] close complete, cost 22318 ms 
[INFO ] 2024-07-24 19:46:25.285 - [Heartbeat-Mssql] - Task [Heartbeat-Mssql] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-24 19:46:25.291 - [Heartbeat-Mssql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 19:46:25.291 - [Heartbeat-Mssql] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6de4f368 
[INFO ] 2024-07-24 19:46:25.410 - [Heartbeat-Mssql] - Stop task milestones: 66a07c11f604e81d788d0139(Heartbeat-Mssql)  
[INFO ] 2024-07-24 19:46:25.432 - [Heartbeat-Mssql] - Stopped task aspect(s) 
[INFO ] 2024-07-24 19:46:25.432 - [Heartbeat-Mssql] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 19:46:25.454 - [Heartbeat-Mssql] - Remove memory task client succeed, task: Heartbeat-Mssql[66a07c11f604e81d788d0139] 
[INFO ] 2024-07-24 19:46:25.456 - [Heartbeat-Mssql] - Destroy memory task client cache succeed, task: Heartbeat-Mssql[66a07c11f604e81d788d0139] 
