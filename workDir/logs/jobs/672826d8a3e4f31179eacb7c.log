[INFO ] 2024-11-04 09:52:21.920 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 09:52:21.921 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 09:52:22.123 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 09:52:22.289 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 09:52:22.292 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 09:52:22.292 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 09:52:22.295 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 1 ms 
[INFO ] 2024-11-04 09:52:22.295 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 1 ms 
[INFO ] 2024-11-04 09:52:23.248 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 09:52:23.370 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 09:52:23.370 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 09:52:23.371 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 09:52:23.375 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 09:52:23.375 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 09:52:23.531 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 09:52:23.532 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 09:52:23.532 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 09:52:23.532 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 09:52:23.538 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 09:52:23.538 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 09:52:23.539 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 09:52:23.539 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 09:52:23.542 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 09:52:23.590 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: 2771dee6-3c73-497c-8c95-1d03aea251b3 
[INFO ] 2024-11-04 09:52:23.593 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"2771dee6-3c73-497c-8c95-1d03aea251b3","offset":{"{\"server\":\"2771dee6-3c73-497c-8c95-1d03aea251b3\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1388546472
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 2771dee6-3c73-497c-8c95-1d03aea251b3
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-2771dee6-3c73-497c-8c95-1d03aea251b3
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 2771dee6-3c73-497c-8c95-1d03aea251b3
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 09:52:23.802 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 09:52:36.722 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 09:52:36.777 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 09:52:36.777 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 09:52:36.777 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 09:52:36.782 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 09:52:36.783 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 09:52:36.783 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 09:52:36.783 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 09:52:36.787 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 91 ms 
[INFO ] 2024-11-04 09:52:36.787 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 09:52:36.815 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 09:52:36.816 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 09:52:36.816 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 09:52:36.817 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 09:52:37.024 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 31 ms 
[INFO ] 2024-11-04 09:52:41.271 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 09:52:41.271 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6920dfc9 
[INFO ] 2024-11-04 09:52:41.273 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 09:52:41.401 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 09:52:41.403 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 09:52:41.432 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 09:52:41.432 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 09:52:57.059 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 09:52:57.059 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 09:52:57.171 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 09:52:57.267 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 09:52:57.327 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 09:52:57.329 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 09:52:57.329 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 09:52:57.329 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 09:52:58.171 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 09:52:58.172 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 09:52:58.172 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 09:52:58.176 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 09:52:58.176 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 09:52:58.300 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 09:52:58.301 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 09:52:58.302 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 09:52:58.302 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 09:52:58.305 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 09:52:58.305 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 09:52:58.306 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 09:52:58.306 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 09:52:58.350 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 09:52:58.350 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: 846b6e9a-9abf-478d-a6f1-dcd7ad2c642f 
[INFO ] 2024-11-04 09:52:58.363 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"846b6e9a-9abf-478d-a6f1-dcd7ad2c642f","offset":{"{\"server\":\"846b6e9a-9abf-478d-a6f1-dcd7ad2c642f\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 131862943
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 846b6e9a-9abf-478d-a6f1-dcd7ad2c642f
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-846b6e9a-9abf-478d-a6f1-dcd7ad2c642f
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 846b6e9a-9abf-478d-a6f1-dcd7ad2c642f
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 09:52:58.363 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 09:52:58.416 - [测试查询存在索引失败][Target_test_index] - Table "test.Target_test_index" exists, skip auto create table 
[INFO ] 2024-11-04 09:52:58.416 - [测试查询存在索引失败][Target_test_index] - The table Target_test_index has already exist. 
[INFO ] 2024-11-04 09:52:58.416 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[WARN ] 2024-11-04 09:52:58.489 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index already exists and will no longer synchronize indexes 
[INFO ] 2024-11-04 09:52:59.503 - [测试查询存在索引失败][Target_test_index] - Table 'Target_test_index' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-04 09:53:11.016 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 09:53:11.016 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 09:53:11.017 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 09:53:11.017 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 09:53:11.028 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 09:53:11.029 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 09:53:11.029 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 09:53:11.030 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 09:53:11.033 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 65 ms 
[INFO ] 2024-11-04 09:53:11.033 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 09:53:11.078 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 09:53:11.078 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 09:53:11.079 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 09:53:11.080 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 09:53:11.297 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 47 ms 
[INFO ] 2024-11-04 09:53:11.554 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 09:53:11.558 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4524b86e 
[INFO ] 2024-11-04 09:53:11.558 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 09:53:11.688 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 09:53:11.688 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 09:53:11.710 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 09:53:11.712 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 09:57:09.614 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 09:57:09.780 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 09:57:09.781 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 09:57:09.818 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 09:57:09.871 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 09:57:09.871 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 09:57:09.871 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 09:57:09.871 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 09:57:10.748 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 09:57:10.749 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 09:57:10.749 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 09:57:10.753 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 09:57:10.753 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 09:57:10.897 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 09:57:10.898 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 09:57:10.898 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 09:57:10.904 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 09:57:10.904 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 09:57:10.905 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 09:57:10.905 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 09:57:10.908 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 09:57:10.909 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 09:57:10.957 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: 69d4b237-a0f2-422b-a52d-e5d2e4f60cdf 
[INFO ] 2024-11-04 09:57:10.957 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 09:57:10.984 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"69d4b237-a0f2-422b-a52d-e5d2e4f60cdf","offset":{"{\"server\":\"69d4b237-a0f2-422b-a52d-e5d2e4f60cdf\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1985968104
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 69d4b237-a0f2-422b-a52d-e5d2e4f60cdf
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-69d4b237-a0f2-422b-a52d-e5d2e4f60cdf
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 69d4b237-a0f2-422b-a52d-e5d2e4f60cdf
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 09:57:10.984 - [测试查询存在索引失败][Target_test_index] - Table "test.Target_test_index" exists, skip auto create table 
[INFO ] 2024-11-04 09:57:10.984 - [测试查询存在索引失败][Target_test_index] - The table Target_test_index has already exist. 
[INFO ] 2024-11-04 09:57:11.041 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[WARN ] 2024-11-04 09:57:11.041 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index already exists and will no longer synchronize indexes 
[INFO ] 2024-11-04 09:57:12.046 - [测试查询存在索引失败][Target_test_index] - Table 'Target_test_index' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-04 09:57:23.577 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 09:57:23.615 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 09:57:23.615 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 09:57:23.624 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 09:57:23.624 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 09:57:23.625 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 09:57:23.625 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 09:57:23.633 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 09:57:23.634 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 69 ms 
[INFO ] 2024-11-04 09:57:23.634 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 09:57:23.665 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 09:57:23.666 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 09:57:23.666 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 09:57:23.670 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 09:57:23.670 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 36 ms 
[INFO ] 2024-11-04 09:57:26.972 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 09:57:26.975 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@314e9ac3 
[INFO ] 2024-11-04 09:57:27.114 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 09:57:27.114 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 09:57:27.118 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 09:57:27.142 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 09:57:27.143 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 09:57:52.927 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 09:57:53.054 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 09:57:53.055 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 09:57:53.130 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 09:57:53.131 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 09:57:53.131 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 09:57:53.131 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 09:57:53.131 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 09:57:54.140 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 09:57:54.149 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: Table name: Target_test_indexio.tapdata.entity.event.ddl.table.TapDropTableEvent@b41d627: {"tableId":"Target_test_index","time":1730685474100,"type":208}
 
[ERROR] 2024-11-04 09:57:54.152 - [测试查询存在索引失败][Target_test_index] - Table name: Target_test_indexio.tapdata.entity.event.ddl.table.TapDropTableEvent@b41d627: {"tableId":"Target_test_index","time":1730685474100,"type":208}
 <-- Error Message -->
Table name: Target_test_indexio.tapdata.entity.event.ddl.table.TapDropTableEvent@b41d627: {"tableId":"Target_test_index","time":1730685474100,"type":208}


<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: DROP command denied to user 'wim'@'************' for table 'Target_test_index'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
Table name: Target_test_index
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.dropTable(HazelcastTargetPdkDataNode.java:488)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:205)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: DROP command denied to user 'wim'@'************' for table 'Target_test_index'
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.dropTable(HazelcastTargetPdkDataNode.java:468)
	... 19 more
Caused by: java.sql.SQLSyntaxErrorException: DROP command denied to user 'wim'@'************' for table 'Target_test_index'
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$dropTable$23(HazelcastTargetPdkDataNode.java:474)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 21 more
Caused by: java.sql.SQLSyntaxErrorException: DROP command denied to user 'wim'@'************' for table 'Target_test_index'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.execute(JdbcContext.java:155)
	at io.tapdata.common.CommonDbConnector.dropTable(CommonDbConnector.java:352)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$22(HazelcastTargetPdkDataNode.java:474)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 28 more

[INFO ] 2024-11-04 09:57:54.215 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 09:57:54.215 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 09:57:54.215 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 09:57:54.215 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 09:57:54.217 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 09:57:54.286 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 09:57:54.286 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 09:57:54.292 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 09:57:54.295 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 09:57:54.304 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 09:57:54.304 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 09:57:54.305 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 09:57:54.306 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 09:57:54.306 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 09:57:54.306 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 09:57:54.332 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: 67a396c2-76de-45f2-959b-c5c652c88fa5 
[INFO ] 2024-11-04 09:57:54.333 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"67a396c2-76de-45f2-959b-c5c652c88fa5","offset":{"{\"server\":\"67a396c2-76de-45f2-959b-c5c652c88fa5\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 459217964
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 67a396c2-76de-45f2-959b-c5c652c88fa5
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-67a396c2-76de-45f2-959b-c5c652c88fa5
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 67a396c2-76de-45f2-959b-c5c652c88fa5
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 09:57:54.545 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 09:57:54.796 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 09:57:54.939 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 09:57:54.940 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 09:57:54.958 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 09:57:54.958 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 09:57:54.958 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 09:57:54.958 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 09:57:54.962 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 09:57:54.963 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 176 ms 
[INFO ] 2024-11-04 09:57:54.963 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 09:57:54.978 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 09:57:54.978 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 09:57:54.978 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 09:57:54.978 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 09:57:55.181 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 16 ms 
[INFO ] 2024-11-04 09:57:57.184 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 09:57:57.185 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 09:57:57.187 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@38500f44 
[INFO ] 2024-11-04 09:57:57.187 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 09:57:57.324 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 09:57:57.324 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 09:57:57.340 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 09:57:57.344 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 10:00:42.210 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 10:00:42.432 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 10:00:42.601 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 10:00:42.601 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 10:00:42.654 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:00:42.654 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:00:42.654 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:00:42.860 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:00:43.433 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 10:00:43.434 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 10:00:43.434 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 10:00:43.442 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 10:00:43.443 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 10:00:43.533 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 10:00:43.534 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 10:00:43.534 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 10:00:43.535 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 10:00:43.539 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 10:00:43.539 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:00:43.540 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 10:00:43.540 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:00:43.587 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 10:00:43.588 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: babea88d-08d9-4b80-9831-e981b321495c 
[INFO ] 2024-11-04 10:00:43.644 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"babea88d-08d9-4b80-9831-e981b321495c","offset":{"{\"server\":\"babea88d-08d9-4b80-9831-e981b321495c\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1076673926
  time.precision.mode: adaptive_time_microseconds
  database.server.name: babea88d-08d9-4b80-9831-e981b321495c
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-babea88d-08d9-4b80-9831-e981b321495c
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: babea88d-08d9-4b80-9831-e981b321495c
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 10:00:43.652 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 10:00:43.658 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 10:00:43.856 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-04 10:00:43.859 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index will create Index: TapIndex name name_id indexFields: [TapIndexField name name_id fieldAsc true indexType null; TapIndexField name name fieldAsc true indexType null; ] 
[INFO ] 2024-11-04 10:00:43.905 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: Table name: Target_test_indexio.tapdata.entity.event.ddl.index.TapCreateIndexEvent@7bcacb08: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"name_id"},{"fieldAsc":true,"name":"name"}],"name":"name_id","primary":false,"unique":true}],"tableId":"Target_test_index","time":1730685643852,"type":101}
 
[ERROR] 2024-11-04 10:00:43.928 - [测试查询存在索引失败][Target_test_index] - Table name: Target_test_indexio.tapdata.entity.event.ddl.index.TapCreateIndexEvent@7bcacb08: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"name_id"},{"fieldAsc":true,"name":"name"}],"name":"name_id","primary":false,"unique":true}],"tableId":"Target_test_index","time":1730685643852,"type":101}
 <-- Error Message -->
Table name: Target_test_indexio.tapdata.entity.event.ddl.index.TapCreateIndexEvent@7bcacb08: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"name_id"},{"fieldAsc":true,"name":"name"}],"name":"name_id","primary":false,"unique":true}],"tableId":"Target_test_index","time":1730685643852,"type":101}


<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: INDEX command denied to user 'wim'@'************' for table 'Target_test_index'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
Table name: Target_test_index
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:211)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: INDEX command denied to user 'wim'@'************' for table 'Target_test_index'
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$syncIndex$14(HazelcastTargetPdkDataNode.java:364)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:360)
	... 19 more
Caused by: java.sql.SQLSyntaxErrorException: INDEX command denied to user 'wim'@'************' for table 'Target_test_index'
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$13(HazelcastTargetPdkDataNode.java:369)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 23 more
Caused by: java.sql.SQLSyntaxErrorException: INDEX command denied to user 'wim'@'************' for table 'Target_test_index'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:404)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$12(HazelcastTargetPdkDataNode.java:371)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 30 more

[INFO ] 2024-11-04 10:00:43.928 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 10:00:44.067 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 10:00:44.069 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 10:00:44.069 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 10:00:44.070 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 10:00:44.084 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 10:00:44.084 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 10:00:44.085 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 10:00:44.085 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 10:00:44.090 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 82 ms 
[INFO ] 2024-11-04 10:00:44.090 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 10:00:44.098 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 10:00:44.099 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 10:00:44.099 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 10:00:44.099 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 10:00:44.099 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 10 ms 
[INFO ] 2024-11-04 10:00:47.516 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 10:00:47.516 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 10:00:47.519 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3085d93a 
[INFO ] 2024-11-04 10:00:47.519 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 10:00:47.659 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 10:00:47.659 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 10:00:47.690 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 10:00:47.692 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 10:07:15.885 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 10:07:15.886 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 10:07:15.988 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 10:07:16.092 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 10:07:16.092 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:07:16.092 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:07:16.092 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:07:16.092 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:07:17.108 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 10:07:17.161 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-04 10:07:17.161 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index will create Index: TapIndex name name_id indexFields: [TapIndexField name name_id fieldAsc true indexType null; TapIndexField name name fieldAsc true indexType null; ] 
[INFO ] 2024-11-04 10:07:17.161 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 10:07:17.162 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 10:07:17.162 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 10:07:17.168 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 10:07:17.168 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 10:07:17.183 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index create Index: name_id successfully, cost 20ms 
[INFO ] 2024-11-04 10:07:17.183 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index synchronize indexes completed, cost 51ms totally 
[INFO ] 2024-11-04 10:07:17.256 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 10:07:17.256 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 10:07:17.257 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 10:07:17.257 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 10:07:17.261 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 10:07:17.261 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:07:17.261 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 10:07:17.261 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:07:17.290 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 10:07:17.292 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: b60d0ef3-5522-4e8f-8f0e-d24790a4b2ca 
[INFO ] 2024-11-04 10:07:17.325 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"b60d0ef3-5522-4e8f-8f0e-d24790a4b2ca","offset":{"{\"server\":\"b60d0ef3-5522-4e8f-8f0e-d24790a4b2ca\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 798783940
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b60d0ef3-5522-4e8f-8f0e-d24790a4b2ca
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b60d0ef3-5522-4e8f-8f0e-d24790a4b2ca
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: b60d0ef3-5522-4e8f-8f0e-d24790a4b2ca
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 10:07:17.325 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 10:16:30.378 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 10:16:30.476 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 10:16:30.481 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 10:16:31.051 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 10:16:31.253 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 10:16:31.716 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:16:31.718 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:16:31.720 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:16:31.721 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:16:32.840 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 10:16:32.865 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 10:16:32.867 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 10:16:32.873 - [测试查询存在索引失败][test_Index] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-11-04 10:16:32.889 - [测试查询存在索引失败][test_Index] - batch offset found: {"test_Index":{"batch_read_connector_status":"OVER"}},stream offset found: {"name":"b60d0ef3-5522-4e8f-8f0e-d24790a4b2ca","offset":{"{\"server\":\"b60d0ef3-5522-4e8f-8f0e-d24790a4b2ca\"}":"{\"ts_sec\":1730686037,\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":1}"}} 
[INFO ] 2024-11-04 10:16:32.890 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 10:16:32.938 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 10:16:32.940 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:16:32.944 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"name":"b60d0ef3-5522-4e8f-8f0e-d24790a4b2ca","offset":{"{\"server\":\"b60d0ef3-5522-4e8f-8f0e-d24790a4b2ca\"}":"{\"ts_sec\":1730686037,\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":1}"}} 
[INFO ] 2024-11-04 10:16:33.013 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: b60d0ef3-5522-4e8f-8f0e-d24790a4b2ca 
[INFO ] 2024-11-04 10:16:33.014 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"b60d0ef3-5522-4e8f-8f0e-d24790a4b2ca","offset":{"{\"server\":\"b60d0ef3-5522-4e8f-8f0e-d24790a4b2ca\"}":"{\"ts_sec\":1730686037,\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1270229556
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b60d0ef3-5522-4e8f-8f0e-d24790a4b2ca
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b60d0ef3-5522-4e8f-8f0e-d24790a4b2ca
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: b60d0ef3-5522-4e8f-8f0e-d24790a4b2ca
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 10:16:33.824 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 10:33:56.392 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 10:33:56.443 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 10:33:56.443 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 10:33:57.034 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 10:33:57.099 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 10:33:57.508 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:33:57.511 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:33:57.512 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:33:57.514 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:33:58.734 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 10:33:58.735 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 10:33:58.743 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 10:33:58.746 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 10:33:58.774 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 10:33:58.871 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 10:33:58.872 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 10:33:58.873 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 10:33:58.873 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 10:33:58.883 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 10:33:58.920 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 10:33:58.926 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:33:58.932 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 10:33:58.932 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:33:58.932 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 10:33:58.965 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-04 10:33:58.969 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index will create Index: TapIndex name name_id indexFields: [TapIndexField name name_id fieldAsc true indexType null; TapIndexField name name fieldAsc true indexType null; ] 
[INFO ] 2024-11-04 10:33:58.992 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: 889afdad-11a1-445b-824c-0df8ac8d29cf 
[INFO ] 2024-11-04 10:33:58.992 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index create Index: name_id successfully, cost 28ms 
[INFO ] 2024-11-04 10:33:58.992 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index synchronize indexes completed, cost 58ms totally 
[INFO ] 2024-11-04 10:33:59.198 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"889afdad-11a1-445b-824c-0df8ac8d29cf","offset":{"{\"server\":\"889afdad-11a1-445b-824c-0df8ac8d29cf\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1238509931
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 889afdad-11a1-445b-824c-0df8ac8d29cf
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-889afdad-11a1-445b-824c-0df8ac8d29cf
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 889afdad-11a1-445b-824c-0df8ac8d29cf
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 10:33:59.264 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 10:34:13.075 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 10:34:13.152 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 10:34:13.153 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 10:34:13.159 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 10:34:13.177 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 10:34:13.181 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 10:34:13.189 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 10:34:13.194 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 10:34:13.198 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 122 ms 
[INFO ] 2024-11-04 10:34:13.242 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 10:34:13.243 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 10:34:13.244 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 10:34:13.245 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 10:34:13.247 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 10:34:13.248 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 50 ms 
[INFO ] 2024-11-04 10:34:14.554 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 10:34:14.554 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7892081 
[INFO ] 2024-11-04 10:34:14.564 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 10:34:14.696 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 10:34:14.696 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 10:34:14.735 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 10:34:14.735 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 10:34:57.121 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 10:34:57.273 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 10:34:57.273 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 10:34:57.342 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 10:34:57.395 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:34:57.396 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:34:57.397 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:34:57.398 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:34:57.804 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 10:34:57.907 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 10:34:57.908 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 10:34:57.909 - [测试查询存在索引失败][test_Index] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-11-04 10:34:57.916 - [测试查询存在索引失败][test_Index] - batch offset found: {"test_Index":{"batch_read_connector_status":"OVER"}},stream offset found: {"name":"889afdad-11a1-445b-824c-0df8ac8d29cf","offset":{"{\"server\":\"889afdad-11a1-445b-824c-0df8ac8d29cf\"}":"{\"ts_sec\":1730687639,\"file\":\"binlog.000037\",\"pos\":20478}"}} 
[INFO ] 2024-11-04 10:34:57.916 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 10:34:57.987 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 10:34:57.987 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:34:57.987 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"name":"889afdad-11a1-445b-824c-0df8ac8d29cf","offset":{"{\"server\":\"889afdad-11a1-445b-824c-0df8ac8d29cf\"}":"{\"ts_sec\":1730687639,\"file\":\"binlog.000037\",\"pos\":20478}"}} 
[INFO ] 2024-11-04 10:34:58.008 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: 889afdad-11a1-445b-824c-0df8ac8d29cf 
[INFO ] 2024-11-04 10:34:58.009 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"889afdad-11a1-445b-824c-0df8ac8d29cf","offset":{"{\"server\":\"889afdad-11a1-445b-824c-0df8ac8d29cf\"}":"{\"ts_sec\":1730687639,\"file\":\"binlog.000037\",\"pos\":20478}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1474766970
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 889afdad-11a1-445b-824c-0df8ac8d29cf
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-889afdad-11a1-445b-824c-0df8ac8d29cf
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 889afdad-11a1-445b-824c-0df8ac8d29cf
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 10:34:58.218 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 10:35:10.732 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 10:35:10.853 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 10:35:10.854 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 10:35:10.870 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 10:35:10.871 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 10:35:10.871 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 10:35:10.871 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 10:35:10.875 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 10:35:10.876 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 148 ms 
[INFO ] 2024-11-04 10:35:10.876 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 10:35:10.893 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 10:35:10.893 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 10:35:10.893 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 10:35:10.895 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 10:35:10.896 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 20 ms 
[INFO ] 2024-11-04 10:35:14.798 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 10:35:14.800 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@201ad5ad 
[INFO ] 2024-11-04 10:35:14.928 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 10:35:14.928 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 10:35:14.928 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 10:35:14.965 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 10:35:14.968 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 10:35:21.948 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 10:35:22.084 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 10:35:22.085 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 10:35:22.170 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 10:35:22.171 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:35:22.174 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:35:22.175 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:35:22.175 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:35:23.189 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 10:35:45.593 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 10:35:45.594 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 10:35:45.594 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 10:35:45.598 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 10:35:45.601 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 10:35:45.700 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 10:35:45.701 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 10:35:45.701 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 10:35:45.701 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 10:35:45.706 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 10:35:45.706 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:35:45.707 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 10:35:45.712 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:35:45.712 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 10:35:45.745 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: 85e2a01c-691f-4640-a46c-b198858fe6a5 
[INFO ] 2024-11-04 10:35:45.747 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"85e2a01c-691f-4640-a46c-b198858fe6a5","offset":{"{\"server\":\"85e2a01c-691f-4640-a46c-b198858fe6a5\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1234300201
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 85e2a01c-691f-4640-a46c-b198858fe6a5
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-85e2a01c-691f-4640-a46c-b198858fe6a5
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 85e2a01c-691f-4640-a46c-b198858fe6a5
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 10:35:45.949 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 10:36:15.545 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) 
[ERROR] 2024-11-04 10:36:15.603 - [测试查询存在索引失败][Target_test_index] - PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) <-- Error Message -->
PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)

<-- Simple Stack Trace -->
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.common.JdbcContext.queryAllIndexes(JdbcContext.java:235)
	at io.tapdata.common.CommonDbConnector.discoverIndex(CommonDbConnector.java:427)
	at io.tapdata.common.CommonDbConnector.queryIndexes(CommonDbConnector.java:753)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.queryExistsIndexes(HazelcastTargetPdkDataNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:211)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.access$100(HikariPool.java:71)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:712)
	... 5 more

[INFO ] 2024-11-04 10:36:15.650 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 10:36:15.650 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 10:36:15.807 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 10:36:15.807 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 10:36:15.810 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 10:36:15.818 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 10:36:15.820 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 10:36:15.820 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 10:36:15.825 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 179 ms 
[INFO ] 2024-11-04 10:36:15.825 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 10:36:20.510 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 10:36:21.776 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 10:36:21.779 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 10:36:21.780 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 10:36:21.780 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 10:36:21.785 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 5954 ms 
[INFO ] 2024-11-04 10:36:25.522 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 10:36:25.546 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 10:36:25.546 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6692783c 
[INFO ] 2024-11-04 10:36:25.550 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 10:36:25.681 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 10:36:25.681 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 10:36:25.703 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 10:36:25.705 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 10:38:42.099 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 10:38:42.101 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 10:38:42.277 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 10:38:42.277 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 10:38:42.328 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:38:42.329 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:38:42.329 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:38:42.329 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:38:43.203 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 10:38:43.207 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 10:38:43.207 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 10:38:43.210 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 10:38:43.210 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 10:38:43.306 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 10:38:43.306 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 10:38:43.306 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 10:38:43.306 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 10:38:43.309 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 10:38:43.311 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:38:43.313 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 10:38:43.313 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:38:43.336 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 10:38:43.338 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: 9b490cbe-f904-4477-b903-ffda7fcc9558 
[INFO ] 2024-11-04 10:38:43.374 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"9b490cbe-f904-4477-b903-ffda7fcc9558","offset":{"{\"server\":\"9b490cbe-f904-4477-b903-ffda7fcc9558\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1142081034
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9b490cbe-f904-4477-b903-ffda7fcc9558
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-9b490cbe-f904-4477-b903-ffda7fcc9558
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 9b490cbe-f904-4477-b903-ffda7fcc9558
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 10:38:43.375 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 10:38:43.579 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 10:40:23.478 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) 
[ERROR] 2024-11-04 10:40:23.489 - [测试查询存在索引失败][Target_test_index] - PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) <-- Error Message -->
PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)

<-- Simple Stack Trace -->
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.common.JdbcContext.queryAllIndexes(JdbcContext.java:235)
	at io.tapdata.common.CommonDbConnector.discoverIndex(CommonDbConnector.java:427)
	at io.tapdata.common.CommonDbConnector.queryIndexes(CommonDbConnector.java:753)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.queryExistsIndexes(HazelcastTargetPdkDataNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:211)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.access$100(HikariPool.java:71)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:712)
	... 5 more

[INFO ] 2024-11-04 10:40:23.694 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 10:40:23.813 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 10:40:23.917 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 10:40:23.918 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 10:40:23.919 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 10:40:23.929 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 10:40:23.930 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 10:40:23.931 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 10:40:23.937 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 10:40:23.938 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 124 ms 
[INFO ] 2024-11-04 10:40:23.938 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 10:40:28.283 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 10:40:28.283 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 10:40:28.286 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 10:40:28.287 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 10:40:28.392 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 4351 ms 
[INFO ] 2024-11-04 10:40:28.392 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 10:40:28.425 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 10:40:28.429 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@17e050d9 
[INFO ] 2024-11-04 10:40:28.430 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 10:40:28.536 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 10:40:28.537 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 10:40:28.554 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 10:40:28.557 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 10:41:02.657 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 10:41:02.776 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 10:41:02.776 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 10:41:02.844 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 10:41:02.845 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:41:02.845 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:41:02.845 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:41:03.047 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:41:04.057 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 10:41:04.058 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 10:41:04.058 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 10:41:04.064 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 10:41:04.064 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 10:41:04.123 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 10:41:04.123 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 10:41:04.124 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 10:41:04.132 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 10:41:04.132 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 10:41:04.133 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:41:04.133 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 10:41:04.138 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:41:04.138 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 10:41:04.179 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: b5b9ed70-de13-41e0-8e54-573426db54d8 
[INFO ] 2024-11-04 10:41:04.181 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"b5b9ed70-de13-41e0-8e54-573426db54d8","offset":{"{\"server\":\"b5b9ed70-de13-41e0-8e54-573426db54d8\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 407631484
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b5b9ed70-de13-41e0-8e54-573426db54d8
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b5b9ed70-de13-41e0-8e54-573426db54d8
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: b5b9ed70-de13-41e0-8e54-573426db54d8
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 10:41:04.386 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 10:41:08.929 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) 
[ERROR] 2024-11-04 10:41:08.943 - [测试查询存在索引失败][Target_test_index] - PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) <-- Error Message -->
PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)

<-- Simple Stack Trace -->
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryVersion(MysqlJdbcContextV2.java:43)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:117)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:190)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:190)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:183)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 24 more

[INFO ] 2024-11-04 10:41:08.943 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 10:41:09.158 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 10:41:09.172 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 10:41:09.172 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 10:41:09.181 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 10:41:09.181 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 10:41:09.181 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 10:41:09.181 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 10:41:09.183 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 10:41:09.186 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 29 ms 
[INFO ] 2024-11-04 10:41:09.186 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 10:41:09.194 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 10:41:09.194 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 10:41:09.194 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 10:41:09.195 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 10:41:09.400 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 10 ms 
[INFO ] 2024-11-04 10:41:13.621 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 10:41:13.634 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 10:41:13.636 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6c5bc88d 
[INFO ] 2024-11-04 10:41:13.774 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 10:41:13.775 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 10:41:13.775 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 10:41:13.790 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 10:41:13.792 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 10:51:04.322 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 10:51:04.410 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 10:51:04.410 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 10:51:04.495 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 10:51:04.496 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:51:04.496 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:51:04.496 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:51:04.496 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:51:05.362 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 10:51:05.362 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 10:51:05.363 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 10:51:05.365 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 10:51:05.431 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 10:51:05.432 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 10:51:05.435 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 10:51:05.436 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 10:51:05.436 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 10:51:05.439 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 10:51:05.439 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:51:05.439 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 10:51:05.439 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:51:05.470 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 10:51:05.471 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: f72a9522-339f-4cb1-be0d-f51f56a53414 
[INFO ] 2024-11-04 10:51:05.528 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"f72a9522-339f-4cb1-be0d-f51f56a53414","offset":{"{\"server\":\"f72a9522-339f-4cb1-be0d-f51f56a53414\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 929075198
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f72a9522-339f-4cb1-be0d-f51f56a53414
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-f72a9522-339f-4cb1-be0d-f51f56a53414
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: f72a9522-339f-4cb1-be0d-f51f56a53414
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 10:51:05.528 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 10:52:02.000 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 10:52:31.953 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) 
[ERROR] 2024-11-04 10:52:31.956 - [测试查询存在索引失败][Target_test_index] - PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) <-- Error Message -->
PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)

<-- Simple Stack Trace -->
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.common.JdbcContext.queryAllIndexes(JdbcContext.java:235)
	at io.tapdata.common.CommonDbConnector.discoverIndex(CommonDbConnector.java:427)
	at io.tapdata.common.CommonDbConnector.queryIndexes(CommonDbConnector.java:753)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.queryExistsIndexes(HazelcastTargetPdkDataNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:211)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.access$100(HikariPool.java:71)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:712)
	... 5 more

[INFO ] 2024-11-04 10:52:31.957 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 10:52:32.158 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 10:52:32.532 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 10:52:32.540 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 10:52:32.543 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 10:52:32.543 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 10:52:32.554 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 10:52:32.554 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 10:52:32.554 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 10:52:32.561 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 10:52:32.562 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 152 ms 
[INFO ] 2024-11-04 10:52:32.562 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 10:52:37.066 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 10:52:38.325 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 10:52:38.325 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 10:52:38.325 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 10:52:38.326 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 10:52:38.532 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 5767 ms 
[INFO ] 2024-11-04 10:52:42.080 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 10:52:42.080 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 10:52:42.081 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@56a75dfd 
[INFO ] 2024-11-04 10:52:42.083 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 10:52:42.209 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 10:52:42.209 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 10:52:42.225 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 10:52:42.227 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 10:53:16.214 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 10:53:16.215 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 10:53:16.381 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 10:53:16.382 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 10:53:16.446 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:53:16.449 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:53:16.449 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:53:16.449 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:53:20.028 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 10:53:20.149 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-04 10:53:20.150 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index will create Index: TapIndex name name_id indexFields: [TapIndexField name name_id fieldAsc true indexType null; TapIndexField name name fieldAsc true indexType null; ] 
[INFO ] 2024-11-04 10:53:20.162 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 10:53:20.162 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 10:53:20.162 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 10:53:20.166 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 10:53:20.167 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 10:53:20.200 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index create Index: name_id successfully, cost 43ms 
[INFO ] 2024-11-04 10:53:20.201 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index synchronize indexes completed, cost 2765ms totally 
[INFO ] 2024-11-04 10:53:20.277 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 10:53:20.280 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 10:53:20.280 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 10:53:20.280 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 10:53:20.284 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 10:53:20.284 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:53:20.285 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 10:53:20.285 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:53:20.286 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 10:53:20.334 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: 33a44fde-b58b-4874-808e-5fc5361cf630 
[INFO ] 2024-11-04 10:53:20.337 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"33a44fde-b58b-4874-808e-5fc5361cf630","offset":{"{\"server\":\"33a44fde-b58b-4874-808e-5fc5361cf630\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 471860019
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 33a44fde-b58b-4874-808e-5fc5361cf630
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-33a44fde-b58b-4874-808e-5fc5361cf630
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 33a44fde-b58b-4874-808e-5fc5361cf630
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 10:53:20.538 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 10:53:36.502 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 10:53:36.524 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 10:53:36.525 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 10:53:36.525 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 10:53:36.538 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 10:53:36.538 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 10:53:36.538 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 10:53:36.539 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 10:53:36.540 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 72 ms 
[INFO ] 2024-11-04 10:53:36.540 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 10:53:36.558 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 10:53:36.558 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 10:53:36.558 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 10:53:36.558 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 10:53:36.763 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 19 ms 
[INFO ] 2024-11-04 10:53:37.295 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 10:53:37.303 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@61f6a2ec 
[INFO ] 2024-11-04 10:53:37.303 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 10:53:37.437 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 10:53:37.438 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 10:53:37.457 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 10:53:37.459 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 10:53:45.932 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 10:53:46.076 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 10:53:46.076 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 10:53:46.161 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 10:53:46.161 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:53:46.161 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 10:53:46.161 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:53:46.162 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 10:53:47.002 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 10:53:47.003 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 10:53:47.003 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 10:53:47.009 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 10:53:47.010 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 10:53:47.101 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 10:53:47.102 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 10:53:47.102 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 10:53:47.102 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 10:53:47.105 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 10:53:47.106 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:53:47.106 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 10:53:47.110 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 10:53:47.110 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 10:53:47.131 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: 60326128-5472-497b-8c74-516e3913cdfc 
[INFO ] 2024-11-04 10:53:47.131 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"60326128-5472-497b-8c74-516e3913cdfc","offset":{"{\"server\":\"60326128-5472-497b-8c74-516e3913cdfc\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1643163713
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 60326128-5472-497b-8c74-516e3913cdfc
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-60326128-5472-497b-8c74-516e3913cdfc
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 60326128-5472-497b-8c74-516e3913cdfc
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 10:53:47.191 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 10:53:47.191 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 10:55:08.265 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) 
[ERROR] 2024-11-04 10:55:08.388 - [测试查询存在索引失败][Target_test_index] - PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) <-- Error Message -->
PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)

<-- Simple Stack Trace -->
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.common.JdbcContext.queryAllIndexes(JdbcContext.java:235)
	at io.tapdata.common.CommonDbConnector.discoverIndex(CommonDbConnector.java:427)
	at io.tapdata.common.CommonDbConnector.queryIndexes(CommonDbConnector.java:753)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.queryExistsIndexes(HazelcastTargetPdkDataNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:211)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.access$100(HikariPool.java:71)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:712)
	... 5 more

[INFO ] 2024-11-04 10:55:08.391 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 10:55:08.722 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 10:55:08.726 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 10:55:08.726 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 10:55:08.726 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 10:55:08.746 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 10:55:08.746 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 10:55:08.748 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 10:55:08.749 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 10:55:08.752 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 123 ms 
[INFO ] 2024-11-04 10:55:08.752 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 10:55:13.013 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 10:55:13.013 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 10:55:13.013 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 10:55:13.013 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 10:55:13.018 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 4263 ms 
[INFO ] 2024-11-04 10:55:13.175 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 10:55:13.175 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 10:55:13.176 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5e35b408 
[INFO ] 2024-11-04 10:55:13.176 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 10:55:13.320 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 10:55:13.322 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 10:55:13.351 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 10:55:13.354 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:00:22.447 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 11:00:22.447 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 11:00:22.621 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 11:00:22.621 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 11:00:22.680 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:00:22.681 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:00:22.681 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:00:22.681 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:00:23.828 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 11:00:23.829 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 11:00:23.829 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 11:00:23.829 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 11:00:23.857 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:00:23.862 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 11:00:23.942 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 11:00:23.942 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 11:00:23.945 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 11:00:23.945 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 11:00:23.962 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 11:00:23.966 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:00:23.967 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 11:00:23.967 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:00:23.967 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:01:48.282 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-04 11:01:50.003 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index will create Index: TapIndex name name_id indexFields: [TapIndexField name name_id fieldAsc true indexType null; TapIndexField name name fieldAsc true indexType null; ] 
[INFO ] 2024-11-04 11:01:50.124 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: ecf8b7e5-d38c-4046-914c-c1e2a20597f5 
[INFO ] 2024-11-04 11:01:50.126 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index create Index: name_id successfully, cost 1845ms 
[INFO ] 2024-11-04 11:01:50.126 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index synchronize indexes completed, cost 86155ms totally 
[INFO ] 2024-11-04 11:01:50.322 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"ecf8b7e5-d38c-4046-914c-c1e2a20597f5","offset":{"{\"server\":\"ecf8b7e5-d38c-4046-914c-c1e2a20597f5\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 281189871
  time.precision.mode: adaptive_time_microseconds
  database.server.name: ecf8b7e5-d38c-4046-914c-c1e2a20597f5
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-ecf8b7e5-d38c-4046-914c-c1e2a20597f5
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: ecf8b7e5-d38c-4046-914c-c1e2a20597f5
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 11:01:50.323 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 11:02:13.427 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 11:02:13.504 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 11:02:13.507 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 11:02:13.520 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 11:02:13.520 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:02:13.520 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:02:13.520 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 11:02:13.521 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 11:02:13.522 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 119 ms 
[INFO ] 2024-11-04 11:02:13.522 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 11:02:13.533 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:02:13.533 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:02:13.533 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 11:02:13.533 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 11:02:13.534 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 12 ms 
[INFO ] 2024-11-04 11:02:18.243 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 11:02:18.244 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@37305f55 
[INFO ] 2024-11-04 11:02:18.372 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 11:02:18.372 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 11:02:18.372 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 11:02:18.408 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:02:18.408 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:03:20.444 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 11:03:20.540 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 11:03:20.541 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 11:03:20.615 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 11:03:20.615 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:03:20.615 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:03:20.615 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:03:20.615 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:03:21.451 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 11:03:21.452 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 11:03:21.452 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 11:03:21.456 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:03:21.456 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 11:03:21.569 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 11:03:21.570 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 11:03:21.570 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 11:03:21.570 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 11:03:21.575 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 11:03:21.576 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:03:21.576 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 11:03:21.579 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:03:21.579 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:03:21.621 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: dba3566b-67de-4f1c-a48d-0093893ca6b8 
[INFO ] 2024-11-04 11:03:21.621 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"dba3566b-67de-4f1c-a48d-0093893ca6b8","offset":{"{\"server\":\"dba3566b-67de-4f1c-a48d-0093893ca6b8\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1093666608
  time.precision.mode: adaptive_time_microseconds
  database.server.name: dba3566b-67de-4f1c-a48d-0093893ca6b8
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-dba3566b-67de-4f1c-a48d-0093893ca6b8
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: dba3566b-67de-4f1c-a48d-0093893ca6b8
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 11:03:21.743 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 11:03:21.743 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 11:04:05.027 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-04 11:04:42.987 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index will create Index: TapIndex name name_id indexFields: [TapIndexField name name_id fieldAsc true indexType null; TapIndexField name name fieldAsc true indexType null; ] 
[INFO ] 2024-11-04 11:04:43.326 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index create Index: name_id successfully, cost 37956ms 
[INFO ] 2024-11-04 11:04:43.342 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index synchronize indexes completed, cost 81433ms totally 
[INFO ] 2024-11-04 11:04:56.315 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 11:04:56.316 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 11:04:56.343 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 11:04:56.343 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 11:04:56.343 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:04:56.346 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:04:56.346 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 11:04:56.354 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 11:04:56.355 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 142 ms 
[INFO ] 2024-11-04 11:04:56.355 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 11:04:56.379 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:04:56.380 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:04:56.380 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 11:04:56.385 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 11:04:56.390 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 29 ms 
[INFO ] 2024-11-04 11:04:58.063 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 11:04:58.064 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@62f9b66a 
[INFO ] 2024-11-04 11:04:58.065 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 11:04:58.189 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 11:04:58.189 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 11:04:58.224 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:04:58.225 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:05:01.967 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 11:05:01.967 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 11:05:02.132 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 11:05:02.132 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 11:05:02.214 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:05:02.215 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:05:02.215 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:05:02.215 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:05:03.225 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 11:05:42.996 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-04 11:05:42.997 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index will create Index: TapIndex name name_id indexFields: [TapIndexField name name_id fieldAsc true indexType null; TapIndexField name name fieldAsc true indexType null; ] 
[INFO ] 2024-11-04 11:05:44.824 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index create Index: name_id successfully, cost 1842ms 
[INFO ] 2024-11-04 11:05:44.827 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 11:05:44.827 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index synchronize indexes completed, cost 41529ms totally 
[INFO ] 2024-11-04 11:05:44.827 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 11:05:44.827 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 11:05:44.828 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:05:44.833 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 11:05:44.936 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 11:05:44.938 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 11:05:44.938 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 11:05:44.938 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 11:05:44.938 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 11:05:44.938 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:05:44.938 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 11:05:44.938 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:05:44.967 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:05:44.970 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: 859fa1e1-273a-46ea-b914-7c55560405e6 
[INFO ] 2024-11-04 11:05:45.004 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"859fa1e1-273a-46ea-b914-7c55560405e6","offset":{"{\"server\":\"859fa1e1-273a-46ea-b914-7c55560405e6\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1768888210
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 859fa1e1-273a-46ea-b914-7c55560405e6
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-859fa1e1-273a-46ea-b914-7c55560405e6
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 859fa1e1-273a-46ea-b914-7c55560405e6
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 11:05:45.004 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 11:05:58.149 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 11:05:58.210 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 11:05:58.214 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 11:05:58.214 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 11:05:58.229 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:05:58.229 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:05:58.229 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 11:05:58.229 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 11:05:58.232 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 104 ms 
[INFO ] 2024-11-04 11:05:58.232 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 11:05:58.252 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:05:58.252 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:05:58.252 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 11:05:58.252 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 11:05:58.253 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 21 ms 
[INFO ] 2024-11-04 11:06:02.997 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 11:06:02.997 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@fe7a1af 
[INFO ] 2024-11-04 11:06:02.999 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 11:06:03.134 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 11:06:03.135 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 11:06:03.165 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:06:03.166 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:07:14.623 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 11:07:14.775 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 11:07:14.776 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 11:07:14.870 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 11:07:14.871 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:07:14.871 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:07:14.871 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:07:14.871 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:07:15.716 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 11:07:15.717 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 11:07:15.717 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 11:07:15.722 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:07:15.722 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 11:07:15.840 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 11:07:15.841 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 11:07:15.841 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 11:07:15.841 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 11:07:15.850 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 11:07:15.850 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:07:15.850 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 11:07:15.850 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:07:15.893 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:07:15.895 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: 141b709e-2445-479a-8a0b-6e187b576a49 
[INFO ] 2024-11-04 11:07:15.960 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"141b709e-2445-479a-8a0b-6e187b576a49","offset":{"{\"server\":\"141b709e-2445-479a-8a0b-6e187b576a49\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1565305167
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 141b709e-2445-479a-8a0b-6e187b576a49
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-141b709e-2445-479a-8a0b-6e187b576a49
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 141b709e-2445-479a-8a0b-6e187b576a49
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 11:07:15.964 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 11:07:15.966 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 11:07:58.008 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) 
[ERROR] 2024-11-04 11:07:58.012 - [测试查询存在索引失败][Target_test_index] - PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) <-- Error Message -->
PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)

<-- Simple Stack Trace -->
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.common.JdbcContext.queryAllIndexes(JdbcContext.java:235)
	at io.tapdata.common.CommonDbConnector.discoverIndex(CommonDbConnector.java:427)
	at io.tapdata.common.CommonDbConnector.queryIndexes(CommonDbConnector.java:753)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.queryExistsIndexes(HazelcastTargetPdkDataNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:211)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.access$100(HikariPool.java:71)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:712)
	... 5 more

[INFO ] 2024-11-04 11:07:58.013 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 11:07:58.218 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 11:07:58.515 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 11:07:58.583 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 11:07:58.584 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 11:07:58.602 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 11:07:58.602 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:07:58.603 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:07:58.603 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 11:07:58.608 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 11:07:58.609 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 93 ms 
[INFO ] 2024-11-04 11:07:58.609 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 11:08:03.053 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 11:08:04.343 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:08:04.345 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:08:04.346 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 11:08:04.346 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 11:08:04.547 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 5739 ms 
[INFO ] 2024-11-04 11:08:08.067 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 11:08:08.067 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 11:08:08.068 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5b648b63 
[INFO ] 2024-11-04 11:08:08.069 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 11:08:08.211 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 11:08:08.211 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 11:08:08.227 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:08:08.231 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:09:35.816 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 11:09:35.973 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 11:09:35.973 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 11:09:36.180 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 11:09:36.219 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:09:36.220 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:09:36.220 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:09:36.220 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:09:37.303 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 11:09:37.303 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 11:09:37.303 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 11:09:37.308 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:09:37.308 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 11:09:37.414 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 11:09:37.415 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 11:09:37.425 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 11:09:37.425 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 11:09:37.426 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 11:09:37.426 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:09:37.427 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 11:09:37.427 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:09:37.468 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:09:37.470 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: 23f8d562-6fd3-4c11-aa9b-1146dd7733a3 
[INFO ] 2024-11-04 11:09:37.520 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"23f8d562-6fd3-4c11-aa9b-1146dd7733a3","offset":{"{\"server\":\"23f8d562-6fd3-4c11-aa9b-1146dd7733a3\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 120691907
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 23f8d562-6fd3-4c11-aa9b-1146dd7733a3
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-23f8d562-6fd3-4c11-aa9b-1146dd7733a3
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 23f8d562-6fd3-4c11-aa9b-1146dd7733a3
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 11:09:37.520 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 11:10:06.564 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) 
[ERROR] 2024-11-04 11:10:06.577 - [测试查询存在索引失败][Target_test_index] - PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) <-- Error Message -->
PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)

<-- Simple Stack Trace -->
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:72)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryVersion(MysqlJdbcContextV2.java:43)
	at io.tapdata.connector.mysql.MysqlConnector.onStart(MysqlConnector.java:117)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:284)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:190)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:102)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:79)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:190)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:183)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:66)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 24 more

[INFO ] 2024-11-04 11:10:06.790 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 11:10:06.996 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 11:10:07.007 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 11:10:07.007 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 11:10:07.007 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 11:10:07.031 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:10:07.031 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:10:07.031 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 11:10:07.035 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 11:10:07.036 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 116 ms 
[INFO ] 2024-11-04 11:10:07.036 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 11:10:07.039 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:10:07.039 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:10:07.039 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 11:10:07.039 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 11:10:07.241 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 3 ms 
[INFO ] 2024-11-04 11:10:11.475 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 11:10:11.476 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 11:10:11.476 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5fc7ba7f 
[INFO ] 2024-11-04 11:10:11.478 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 11:10:11.616 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 11:10:11.617 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 11:10:11.629 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:10:11.632 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:14:30.549 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 11:14:30.549 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 11:14:30.674 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 11:14:30.674 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 11:14:30.718 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:14:30.718 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:14:30.718 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:14:30.718 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:14:31.678 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 11:14:31.798 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 11:14:31.799 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 11:14:31.799 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 11:14:31.806 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:14:31.808 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 11:15:07.205 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 11:15:07.207 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 11:15:07.207 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 11:15:07.207 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 11:15:07.214 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 11:15:07.220 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:15:07.220 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 11:15:07.220 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:15:07.277 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:15:07.280 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: c1bed66e-bafb-414f-bce7-c72773559e75 
[INFO ] 2024-11-04 11:15:07.342 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"c1bed66e-bafb-414f-bce7-c72773559e75","offset":{"{\"server\":\"c1bed66e-bafb-414f-bce7-c72773559e75\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 325040361
  time.precision.mode: adaptive_time_microseconds
  database.server.name: c1bed66e-bafb-414f-bce7-c72773559e75
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-c1bed66e-bafb-414f-bce7-c72773559e75
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: c1bed66e-bafb-414f-bce7-c72773559e75
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 11:15:07.343 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 11:16:07.214 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) 
[ERROR] 2024-11-04 11:16:07.222 - [测试查询存在索引失败][Target_test_index] - PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) <-- Error Message -->
PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)

<-- Simple Stack Trace -->
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.common.JdbcContext.queryAllIndexes(JdbcContext.java:235)
	at io.tapdata.common.CommonDbConnector.discoverIndex(CommonDbConnector.java:427)
	at io.tapdata.common.CommonDbConnector.queryIndexes(CommonDbConnector.java:753)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.queryExistsIndexes(HazelcastTargetPdkDataNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:211)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.access$100(HikariPool.java:71)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:712)
	... 5 more

[INFO ] 2024-11-04 11:16:07.426 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 11:16:07.579 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 11:16:07.695 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 11:16:07.695 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 11:16:07.716 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 11:16:07.716 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:16:07.716 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:16:07.716 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 11:16:07.723 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 11:16:07.724 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 160 ms 
[INFO ] 2024-11-04 11:16:07.724 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 11:16:12.006 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:16:12.006 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:16:12.006 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 11:16:12.007 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 11:16:12.009 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 4285 ms 
[INFO ] 2024-11-04 11:16:12.113 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 11:16:12.114 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 11:16:12.114 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@18465a4d 
[INFO ] 2024-11-04 11:16:12.116 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 11:16:12.264 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 11:16:12.264 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 11:16:12.280 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:16:12.282 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:21:34.337 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 11:21:34.338 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 11:21:34.508 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 11:21:34.509 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 11:21:34.725 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:21:34.726 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:21:34.726 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:21:34.726 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:21:35.705 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 11:21:35.706 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 11:21:35.706 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 11:21:35.725 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:21:35.725 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 11:21:35.843 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 11:21:35.845 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 11:21:35.845 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 11:21:35.845 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 11:21:35.853 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 11:21:35.853 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:21:35.853 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 11:21:35.853 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:21:35.906 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:21:35.907 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: a693cb68-6091-409b-a3e2-3499ce999f4e 
[INFO ] 2024-11-04 11:21:35.957 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 11:21:35.958 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"a693cb68-6091-409b-a3e2-3499ce999f4e","offset":{"{\"server\":\"a693cb68-6091-409b-a3e2-3499ce999f4e\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1419380764
  time.precision.mode: adaptive_time_microseconds
  database.server.name: a693cb68-6091-409b-a3e2-3499ce999f4e
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-a693cb68-6091-409b-a3e2-3499ce999f4e
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: a693cb68-6091-409b-a3e2-3499ce999f4e
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 11:21:55.068 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 11:22:24.571 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) 
[ERROR] 2024-11-04 11:22:24.586 - [测试查询存在索引失败][Target_test_index] - PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) <-- Error Message -->
PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)

<-- Simple Stack Trace -->
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.common.JdbcContext.queryAllIndexes(JdbcContext.java:235)
	at io.tapdata.common.CommonDbConnector.discoverIndex(CommonDbConnector.java:427)
	at io.tapdata.common.CommonDbConnector.queryIndexes(CommonDbConnector.java:753)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.queryExistsIndexes(HazelcastTargetPdkDataNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:211)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.access$100(HikariPool.java:71)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:712)
	... 5 more

[INFO ] 2024-11-04 11:22:24.587 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 11:22:24.792 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 11:22:24.947 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 11:22:25.056 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 11:22:25.062 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 11:22:25.065 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 11:22:25.077 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:22:25.077 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:22:25.078 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 11:22:25.078 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 11:22:25.080 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 133 ms 
[INFO ] 2024-11-04 11:22:25.080 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 11:22:29.652 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 11:22:30.819 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:22:30.820 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:22:30.820 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 11:22:30.821 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 11:22:30.821 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 5741 ms 
[INFO ] 2024-11-04 11:22:34.664 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 11:22:34.684 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 11:22:34.695 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@364eebbd 
[INFO ] 2024-11-04 11:22:34.695 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 11:22:34.815 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 11:22:34.819 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 11:22:34.838 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:22:34.846 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:23:56.812 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 11:23:56.932 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 11:23:56.932 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 11:23:57.009 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 11:23:57.010 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:23:57.010 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:23:57.010 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:23:57.010 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:23:57.938 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 11:23:57.938 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 11:23:57.938 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 11:23:57.944 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:23:58.055 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 11:23:58.055 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 11:23:58.055 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 11:23:58.055 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 11:23:58.055 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 11:23:58.063 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 11:23:58.064 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:23:58.064 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 11:23:58.069 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:23:58.069 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:23:58.159 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: f9bc9c09-8f15-4b57-a178-49cdafa56271 
[INFO ] 2024-11-04 11:23:58.159 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"f9bc9c09-8f15-4b57-a178-49cdafa56271","offset":{"{\"server\":\"f9bc9c09-8f15-4b57-a178-49cdafa56271\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1986133152
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f9bc9c09-8f15-4b57-a178-49cdafa56271
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-f9bc9c09-8f15-4b57-a178-49cdafa56271
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: f9bc9c09-8f15-4b57-a178-49cdafa56271
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 11:23:58.223 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 11:23:58.223 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 11:23:58.330 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-04 11:23:58.334 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index will create Index: TapIndex name name_id indexFields: [TapIndexField name name_id fieldAsc true indexType null; TapIndexField name name fieldAsc true indexType null; ] 
[INFO ] 2024-11-04 11:23:58.359 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index create Index: name_id successfully, cost 29ms 
[INFO ] 2024-11-04 11:23:58.359 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index synchronize indexes completed, cost 49ms totally 
[INFO ] 2024-11-04 11:24:13.250 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 11:24:13.401 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 11:24:13.409 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 11:24:13.409 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 11:24:13.418 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:24:13.418 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:24:13.419 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 11:24:13.422 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 11:24:13.424 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 181 ms 
[INFO ] 2024-11-04 11:24:13.424 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 11:24:13.447 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:24:13.448 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:24:13.448 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 11:24:13.448 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 11:24:13.664 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 24 ms 
[INFO ] 2024-11-04 11:24:14.956 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 11:24:14.958 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3c234615 
[INFO ] 2024-11-04 11:24:14.958 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 11:24:15.095 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 11:24:15.095 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 11:24:15.127 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:24:15.127 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:24:19.066 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 11:24:19.148 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 11:24:19.148 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 11:24:19.233 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 11:24:19.233 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:24:19.233 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:24:19.233 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:24:19.234 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 1 ms 
[INFO ] 2024-11-04 11:24:19.982 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 11:24:19.982 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 11:24:19.982 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 11:24:19.993 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:24:19.995 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 11:24:20.093 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 11:24:20.094 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 11:24:20.094 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 11:24:20.094 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 11:24:20.098 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 11:24:20.098 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:24:20.098 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 11:24:20.098 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:24:20.099 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:24:20.139 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: fb4096f3-ca9d-4d44-b0fa-154e7fda5224 
[INFO ] 2024-11-04 11:24:20.140 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"fb4096f3-ca9d-4d44-b0fa-154e7fda5224","offset":{"{\"server\":\"fb4096f3-ca9d-4d44-b0fa-154e7fda5224\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 486451288
  time.precision.mode: adaptive_time_microseconds
  database.server.name: fb4096f3-ca9d-4d44-b0fa-154e7fda5224
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-fb4096f3-ca9d-4d44-b0fa-154e7fda5224
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: fb4096f3-ca9d-4d44-b0fa-154e7fda5224
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 11:24:20.241 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 11:24:20.241 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 11:26:18.620 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) 
[ERROR] 2024-11-04 11:26:18.662 - [测试查询存在索引失败][Target_test_index] - PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) <-- Error Message -->
PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)

<-- Simple Stack Trace -->
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.common.JdbcContext.queryAllIndexes(JdbcContext.java:235)
	at io.tapdata.common.CommonDbConnector.discoverIndex(CommonDbConnector.java:427)
	at io.tapdata.common.CommonDbConnector.queryIndexes(CommonDbConnector.java:753)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.queryExistsIndexes(HazelcastTargetPdkDataNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:211)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.access$100(HikariPool.java:71)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:712)
	... 5 more

[INFO ] 2024-11-04 11:26:18.886 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 11:26:19.066 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 11:26:19.066 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 11:26:19.067 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 11:26:19.067 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 11:26:19.089 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:26:19.089 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:26:19.089 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 11:26:19.090 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 11:26:19.093 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 99 ms 
[INFO ] 2024-11-04 11:26:19.093 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 11:26:23.404 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:26:23.404 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:26:23.404 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 11:26:23.404 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 11:26:23.527 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 4311 ms 
[INFO ] 2024-11-04 11:26:23.528 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 11:26:23.539 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 11:26:23.539 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@20f61033 
[INFO ] 2024-11-04 11:26:23.540 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 11:26:23.665 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 11:26:23.665 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 11:26:23.684 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:26:23.684 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:26:51.499 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 11:26:51.500 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 11:26:51.684 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 11:26:51.689 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 11:26:51.795 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:26:51.796 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:26:51.796 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:26:51.796 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:26:52.608 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 11:27:17.370 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 11:27:17.371 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 11:27:17.374 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 11:27:17.374 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:27:17.374 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 11:27:17.441 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 11:27:17.442 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 11:27:17.442 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 11:27:17.447 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 11:27:17.447 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 11:27:17.449 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:27:17.449 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 11:27:17.449 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:27:17.453 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:27:17.481 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: 531bfdc3-2c61-43d4-9e5e-5380e952c869 
[INFO ] 2024-11-04 11:27:17.481 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"531bfdc3-2c61-43d4-9e5e-5380e952c869","offset":{"{\"server\":\"531bfdc3-2c61-43d4-9e5e-5380e952c869\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 355508472
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 531bfdc3-2c61-43d4-9e5e-5380e952c869
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-531bfdc3-2c61-43d4-9e5e-5380e952c869
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 531bfdc3-2c61-43d4-9e5e-5380e952c869
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 11:27:17.692 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 11:27:47.233 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) 
[ERROR] 2024-11-04 11:27:47.251 - [测试查询存在索引失败][Target_test_index] - PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) <-- Error Message -->
PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)

<-- Simple Stack Trace -->
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.common.JdbcContext.queryAllIndexes(JdbcContext.java:235)
	at io.tapdata.common.CommonDbConnector.discoverIndex(CommonDbConnector.java:427)
	at io.tapdata.common.CommonDbConnector.queryIndexes(CommonDbConnector.java:753)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.queryExistsIndexes(HazelcastTargetPdkDataNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:211)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.access$100(HikariPool.java:71)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:712)
	... 5 more

[INFO ] 2024-11-04 11:27:47.254 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 11:27:47.455 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 11:27:47.538 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 11:27:47.538 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 11:27:47.554 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 11:27:47.554 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:27:47.554 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:27:47.554 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 11:27:47.562 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 11:27:47.562 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 142 ms 
[INFO ] 2024-11-04 11:27:47.562 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 11:27:52.428 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 11:27:53.616 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:27:53.618 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:27:53.618 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 11:27:53.622 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 11:27:53.632 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 6060 ms 
[INFO ] 2024-11-04 11:27:57.273 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 11:27:57.273 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 11:27:57.274 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@72608f71 
[INFO ] 2024-11-04 11:27:57.411 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 11:27:57.411 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 11:27:57.411 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 11:27:57.448 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:27:57.452 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:40:19.222 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 11:40:19.243 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 11:40:19.406 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 11:40:19.407 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 11:40:19.472 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:40:19.472 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:40:19.472 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:40:19.472 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:40:20.485 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 11:40:43.037 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 11:40:43.037 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 11:40:43.037 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 11:40:43.040 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:40:43.040 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 11:41:04.744 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-04 11:41:04.854 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index will create Index: TapIndex name name_id indexFields: [TapIndexField name name_id fieldAsc true indexType null; TapIndexField name name fieldAsc true indexType null; ] 
[INFO ] 2024-11-04 11:41:04.927 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 11:41:04.929 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 11:41:04.950 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 11:41:04.950 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 11:41:04.951 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:41:04.951 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 11:41:04.951 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:41:04.952 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:41:04.993 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 11:41:04.994 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: a3c8fa70-3f20-44cf-b5f4-64ac5a86988e 
[INFO ] 2024-11-04 11:41:05.025 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"a3c8fa70-3f20-44cf-b5f4-64ac5a86988e","offset":{"{\"server\":\"a3c8fa70-3f20-44cf-b5f4-64ac5a86988e\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 116047630
  time.precision.mode: adaptive_time_microseconds
  database.server.name: a3c8fa70-3f20-44cf-b5f4-64ac5a86988e
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-a3c8fa70-3f20-44cf-b5f4-64ac5a86988e
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: a3c8fa70-3f20-44cf-b5f4-64ac5a86988e
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 11:41:05.025 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 11:41:41.205 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) 
[ERROR] 2024-11-04 11:41:41.214 - [测试查询存在索引失败][Target_test_index] - PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) <-- Error Message -->
PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)

<-- Simple Stack Trace -->
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.common.JdbcContext.queryAllIndexes(JdbcContext.java:235)
	at io.tapdata.common.CommonDbConnector.discoverIndex(CommonDbConnector.java:427)
	at io.tapdata.common.CommonDbConnector.lambda$createIndex$20(CommonDbConnector.java:396)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:174)
	at java.util.Collections$2.tryAdvance(Collections.java:4719)
	at java.util.Collections$2.forEachRemaining(Collections.java:4727)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:482)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:708)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:566)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:399)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$12(HazelcastTargetPdkDataNode.java:388)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$13(HazelcastTargetPdkDataNode.java:386)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$syncIndex$14(HazelcastTargetPdkDataNode.java:381)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:377)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:211)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.access$100(HikariPool.java:71)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:712)
	... 5 more

[INFO ] 2024-11-04 11:41:41.418 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 11:41:41.654 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 11:41:41.768 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 11:41:41.768 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 11:41:41.769 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 11:41:41.789 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:41:41.789 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:41:41.790 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 11:41:41.792 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 11:41:41.792 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 144 ms 
[INFO ] 2024-11-04 11:41:41.792 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 11:41:45.997 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:41:45.998 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:41:45.999 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 11:41:45.999 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 11:41:46.185 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 4207 ms 
[INFO ] 2024-11-04 11:41:46.186 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 11:41:46.202 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 11:41:46.206 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5fe2c999 
[INFO ] 2024-11-04 11:41:46.206 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 11:41:46.340 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 11:41:46.342 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 11:41:46.358 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:41:46.360 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:42:05.728 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 11:42:05.823 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 11:42:05.824 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 11:42:05.902 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 11:42:05.902 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:42:05.902 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:42:05.902 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:42:05.904 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 1 ms 
[INFO ] 2024-11-04 11:42:06.834 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 11:42:11.485 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 11:42:11.485 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 11:42:11.485 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 11:42:11.488 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:42:38.466 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 11:42:38.664 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 11:42:38.669 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 11:42:38.669 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 11:42:38.683 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 11:42:38.689 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:42:38.689 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 11:42:38.689 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:42:38.696 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:42:38.696 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 11:42:38.736 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: c1d9cd3b-6fc0-4959-b874-b8b2c62effbb 
[INFO ] 2024-11-04 11:42:38.737 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"c1d9cd3b-6fc0-4959-b874-b8b2c62effbb","offset":{"{\"server\":\"c1d9cd3b-6fc0-4959-b874-b8b2c62effbb\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 114509300
  time.precision.mode: adaptive_time_microseconds
  database.server.name: c1d9cd3b-6fc0-4959-b874-b8b2c62effbb
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-c1d9cd3b-6fc0-4959-b874-b8b2c62effbb
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: c1d9cd3b-6fc0-4959-b874-b8b2c62effbb
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 11:42:38.943 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 11:43:20.015 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) 
[ERROR] 2024-11-04 11:43:20.026 - [测试查询存在索引失败][Target_test_index] - PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) <-- Error Message -->
PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)

<-- Simple Stack Trace -->
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.common.JdbcContext.queryAllIndexes(JdbcContext.java:235)
	at io.tapdata.common.CommonDbConnector.discoverIndex(CommonDbConnector.java:427)
	at io.tapdata.common.CommonDbConnector.queryIndexes(CommonDbConnector.java:753)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.queryExistsIndexes(HazelcastTargetPdkDataNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:211)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.access$100(HikariPool.java:71)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:712)
	... 5 more

[INFO ] 2024-11-04 11:43:20.028 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 11:43:20.499 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 11:43:20.547 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 11:43:20.547 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 11:43:20.575 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 11:43:20.575 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:43:20.575 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:43:20.575 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 11:43:20.575 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 11:43:20.581 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 90 ms 
[INFO ] 2024-11-04 11:43:20.581 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 11:43:24.843 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:43:24.843 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:43:24.843 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 11:43:24.843 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 11:43:24.963 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 4263 ms 
[INFO ] 2024-11-04 11:43:24.963 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 11:43:24.977 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 11:43:24.977 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@f13d4b7 
[INFO ] 2024-11-04 11:43:24.985 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 11:43:25.118 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 11:43:25.118 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 11:43:25.134 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:43:25.138 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:45:17.860 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 11:45:18.077 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 11:45:18.136 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 11:45:18.136 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 11:45:18.209 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:45:18.210 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:45:18.210 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:45:18.210 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 1 ms 
[INFO ] 2024-11-04 11:45:25.236 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 11:45:27.210 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 11:45:27.212 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 11:45:27.212 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 11:45:27.251 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:45:27.251 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 11:46:24.456 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 11:46:24.457 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 11:46:24.460 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 11:46:24.502 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 11:46:24.505 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:46:24.505 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 11:46:24.505 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:46:24.539 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:46:24.539 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 11:46:24.611 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: 307da7d9-d3d7-449d-9a44-6afee3b73d25 
[INFO ] 2024-11-04 11:46:24.611 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"307da7d9-d3d7-449d-9a44-6afee3b73d25","offset":{"{\"server\":\"307da7d9-d3d7-449d-9a44-6afee3b73d25\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1083164884
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 307da7d9-d3d7-449d-9a44-6afee3b73d25
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-307da7d9-d3d7-449d-9a44-6afee3b73d25
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 307da7d9-d3d7-449d-9a44-6afee3b73d25
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 11:46:25.014 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 11:47:07.907 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) 
[ERROR] 2024-11-04 11:47:07.912 - [测试查询存在索引失败][Target_test_index] - PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused) <-- Error Message -->
PDK retry exception (Server Error Code 0): java.net.ConnectException: Connection refused (Connection refused)

<-- Simple Stack Trace -->
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	java.net.PlainSocketImpl.socketConnect(Native Method)
	java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	...

<-- Full Stack Trace -->
java.net.ConnectException: Connection refused (Connection refused)
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.common.JdbcContext.queryAllIndexes(JdbcContext.java:235)
	at io.tapdata.common.CommonDbConnector.discoverIndex(CommonDbConnector.java:427)
	at io.tapdata.common.CommonDbConnector.queryIndexes(CommonDbConnector.java:753)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.queryExistsIndexes(HazelcastTargetPdkDataNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:211)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:613)
	at com.mysql.cj.protocol.StandardSocketFactory.connect(StandardSocketFactory.java:153)
	at com.mysql.cj.protocol.a.NativeSocketConnection.connect(NativeSocketConnection.java:63)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:121)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.access$100(HikariPool.java:71)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:712)
	... 5 more

[INFO ] 2024-11-04 11:47:08.120 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 11:47:08.393 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 11:47:08.394 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 11:47:08.394 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 11:47:08.394 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 11:47:08.412 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:47:08.412 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:47:08.412 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 11:47:08.412 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 11:47:08.415 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 95 ms 
[INFO ] 2024-11-04 11:47:08.415 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 11:47:11.099 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:47:11.101 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:47:11.101 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 11:47:11.101 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 11:47:11.307 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 2687 ms 
[INFO ] 2024-11-04 11:47:12.835 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 11:47:12.835 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 11:47:12.837 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@8187bc5 
[INFO ] 2024-11-04 11:47:12.837 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 11:47:12.970 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 11:47:12.970 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 11:47:12.989 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:47:12.993 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:52:10.357 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 11:52:10.361 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 11:52:11.087 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 11:52:11.089 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 11:52:11.595 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:52:11.596 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:52:11.596 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 1 ms 
[INFO ] 2024-11-04 11:52:11.597 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:52:12.811 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 11:52:12.823 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 11:52:12.830 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 11:52:12.833 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 11:52:12.855 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:52:12.856 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 11:52:12.976 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 11:52:12.978 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 11:52:12.979 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 11:52:12.984 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 11:52:13.020 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 11:52:13.021 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:52:13.022 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 11:52:13.023 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:52:13.028 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:52:27.067 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: c3d30b47-13d7-4192-98e5-34d7968ce32e 
[INFO ] 2024-11-04 11:52:27.153 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-04 11:52:27.170 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index will create Index: TapIndex name name_id indexFields: [TapIndexField name name_id fieldAsc true indexType null; TapIndexField name name fieldAsc true indexType null; ] 
[INFO ] 2024-11-04 11:52:27.199 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"c3d30b47-13d7-4192-98e5-34d7968ce32e","offset":{"{\"server\":\"c3d30b47-13d7-4192-98e5-34d7968ce32e\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1928406688
  time.precision.mode: adaptive_time_microseconds
  database.server.name: c3d30b47-13d7-4192-98e5-34d7968ce32e
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-c3d30b47-13d7-4192-98e5-34d7968ce32e
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: c3d30b47-13d7-4192-98e5-34d7968ce32e
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 11:52:27.202 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index create Index: name_id successfully, cost 64ms 
[INFO ] 2024-11-04 11:52:27.205 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index synchronize indexes completed, cost 14093ms totally 
[INFO ] 2024-11-04 11:52:27.611 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 11:52:57.750 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 11:52:57.801 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 11:52:57.805 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 11:52:57.813 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 11:52:57.814 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:52:57.818 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:52:57.819 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 11:52:57.839 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 11:52:57.841 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 170 ms 
[INFO ] 2024-11-04 11:52:57.841 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 11:52:57.878 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:52:57.879 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:52:57.879 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 11:52:57.879 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 11:52:58.085 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 41 ms 
[INFO ] 2024-11-04 11:53:02.171 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 11:53:02.178 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@414edec1 
[INFO ] 2024-11-04 11:53:02.289 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 11:53:02.316 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 11:53:02.318 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 11:53:02.366 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:53:02.366 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:53:31.071 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 11:53:31.276 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 11:53:31.276 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 11:53:31.358 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 11:53:31.406 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:53:31.407 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 11:53:31.409 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:53:31.615 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 11:53:32.428 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 11:53:36.992 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 11:54:02.044 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 11:54:02.141 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 11:54:02.142 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:54:02.142 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 11:54:02.317 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 11:54:02.318 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 11:54:02.318 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 11:54:02.318 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 11:54:02.326 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 11:54:02.327 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:54:02.328 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 11:54:02.328 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 11:54:02.329 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 11:54:02.408 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: 35cf10c7-e532-4c16-b947-cf3afde3cd27 
[INFO ] 2024-11-04 11:54:02.410 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"35cf10c7-e532-4c16-b947-cf3afde3cd27","offset":{"{\"server\":\"35cf10c7-e532-4c16-b947-cf3afde3cd27\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1273040308
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 35cf10c7-e532-4c16-b947-cf3afde3cd27
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-35cf10c7-e532-4c16-b947-cf3afde3cd27
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 35cf10c7-e532-4c16-b947-cf3afde3cd27
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 11:54:02.585 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 11:55:47.408 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: Table name: Target_test_index 
[ERROR] 2024-11-04 11:55:47.488 - [测试查询存在索引失败][Target_test_index] - Table name: Target_test_index <-- Error Message -->
Table name: Target_test_index

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	io.tapdata.connector.mysql.MysqlExceptionCollector.collectTerminateByServer(MysqlExceptionCollector.java:34)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:50)
	io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	io.tapdata.common.JdbcContext.query(JdbcContext.java:90)
	io.tapdata.common.JdbcContext.queryAllIndexes(JdbcContext.java:236)
	...

<-- Full Stack Trace -->
Table name: Target_test_index
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:371)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:211)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectTerminateByServer(MysqlExceptionCollector.java:34)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:50)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.getConnection(MysqlJdbcContextV2.java:95)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:90)
	at io.tapdata.common.JdbcContext.queryAllIndexes(JdbcContext.java:236)
	at io.tapdata.common.CommonDbConnector.discoverIndex(CommonDbConnector.java:427)
	at io.tapdata.common.CommonDbConnector.queryIndexes(CommonDbConnector.java:753)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.queryExistsIndexes(HazelcastTargetPdkDataNode.java:411)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:355)
	... 19 more

[INFO ] 2024-11-04 11:55:47.490 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 11:55:47.751 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 11:55:47.753 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 11:55:47.761 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 11:55:47.761 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 11:55:47.771 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:55:47.771 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 11:55:47.772 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 11:55:47.774 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 11:55:47.775 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 45 ms 
[INFO ] 2024-11-04 11:55:47.775 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 11:55:52.183 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:55:52.184 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 11:55:52.186 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 11:55:52.187 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 11:55:52.294 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 4410 ms 
[INFO ] 2024-11-04 11:55:52.295 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 11:55:52.299 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 11:55:52.300 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1587bf17 
[INFO ] 2024-11-04 11:55:52.418 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 11:55:52.427 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 11:55:52.427 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 11:55:52.463 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 11:55:52.464 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 14:50:54.415 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 14:50:54.519 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 14:50:54.519 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 14:50:54.595 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 14:50:54.595 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 14:50:54.595 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 14:50:54.595 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 14:50:54.595 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 14:50:55.569 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 14:50:55.570 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: Missing privileges when write data on mysql. 
 - Executing operation: createTable
 - Table name: [Target_test_index]
 - Missing privileges: [] 
[ERROR] 2024-11-04 14:50:55.587 - [测试查询存在索引失败][Target_test_index] - Missing privileges when write data on mysql. 
 - Executing operation: createTable
 - Table name: [Target_test_index]
 - Missing privileges: [] <-- Error Message -->
Missing privileges when write data on mysql. 
 - Executing operation: createTable
 - Table name: [Target_test_index]
 - Missing privileges: []

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: CREATE command denied to user 'wim'@'************' for table 'Target_test_index'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: CREATE command denied to user 'wim'@'************' for table 'Target_test_index'
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectWritePrivileges(MysqlExceptionCollector.java:69)
	at io.tapdata.connector.mysql.MysqlConnector.createTableV2(MysqlConnector.java:393)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$8(HazelcastTargetPdkBaseNode.java:318)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$createTable$9(HazelcastTargetPdkBaseNode.java:316)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createTable(HazelcastTargetPdkBaseNode.java:310)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:207)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: CREATE command denied to user 'wim'@'************' for table 'Target_test_index'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:167)
	at io.tapdata.connector.mysql.MysqlConnector.createTableV2(MysqlConnector.java:388)
	... 30 more

[INFO ] 2024-11-04 14:50:55.587 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 14:50:55.640 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 14:50:55.640 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 14:50:55.640 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 14:50:55.643 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 14:50:55.643 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 14:50:55.740 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 14:50:55.741 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 14:50:55.741 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 14:50:55.741 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 14:50:55.747 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 14:50:55.747 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 14:50:55.748 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 14:50:55.748 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 14:50:55.748 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 14:50:55.773 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: 3f0ff2dc-8465-49f9-9d20-933a41f300e3 
[INFO ] 2024-11-04 14:50:55.774 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"3f0ff2dc-8465-49f9-9d20-933a41f300e3","offset":{"{\"server\":\"3f0ff2dc-8465-49f9-9d20-933a41f300e3\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 617406290
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 3f0ff2dc-8465-49f9-9d20-933a41f300e3
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-3f0ff2dc-8465-49f9-9d20-933a41f300e3
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 3f0ff2dc-8465-49f9-9d20-933a41f300e3
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 14:50:55.993 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 14:50:56.309 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 14:50:56.309 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 14:50:56.309 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 14:50:56.321 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 14:50:56.322 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 14:50:56.322 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 14:50:56.327 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 14:50:56.329 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 14:50:56.330 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 103 ms 
[INFO ] 2024-11-04 14:50:56.330 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 14:50:56.332 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 14:50:56.332 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 14:50:56.333 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 14:50:56.333 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 14:50:56.536 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 7 ms 
[INFO ] 2024-11-04 14:51:00.322 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 14:51:00.341 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 14:51:00.342 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2b0e611e 
[INFO ] 2024-11-04 14:51:00.476 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 14:51:00.477 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 14:51:00.477 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 14:51:00.502 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 14:51:00.504 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 14:56:52.816 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 14:56:52.942 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 14:56:52.942 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 14:56:53.042 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 14:56:53.042 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 14:56:53.043 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 14:56:53.043 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 14:56:53.043 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 14:56:53.843 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 14:56:53.845 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 14:56:53.845 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 14:56:53.849 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 14:56:53.850 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 14:56:53.964 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 14:56:53.964 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 14:56:53.964 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 14:56:53.965 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 14:56:53.968 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 14:56:53.968 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 14:56:53.968 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 14:56:53.971 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 14:56:53.971 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 14:56:53.996 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: fb82839d-794e-4966-a762-27d729e6f35d 
[INFO ] 2024-11-04 14:56:53.996 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"fb82839d-794e-4966-a762-27d729e6f35d","offset":{"{\"server\":\"fb82839d-794e-4966-a762-27d729e6f35d\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2104204148
  time.precision.mode: adaptive_time_microseconds
  database.server.name: fb82839d-794e-4966-a762-27d729e6f35d
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-fb82839d-794e-4966-a762-27d729e6f35d
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: fb82839d-794e-4966-a762-27d729e6f35d
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 14:56:54.052 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 14:56:54.053 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 14:56:54.096 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: Missing privileges when write data on mysql. 
 - Executing operation: createTable
 - Table name: [Target_test_index]
 - Missing privileges: [] 
[ERROR] 2024-11-04 14:56:54.098 - [测试查询存在索引失败][Target_test_index] - Missing privileges when write data on mysql. 
 - Executing operation: createTable
 - Table name: [Target_test_index]
 - Missing privileges: [] <-- Error Message -->
Missing privileges when write data on mysql. 
 - Executing operation: createTable
 - Table name: [Target_test_index]
 - Missing privileges: []

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: CREATE command denied to user 'wim'@'************' for table 'Target_test_index'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: CREATE command denied to user 'wim'@'************' for table 'Target_test_index'
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectWritePrivileges(MysqlExceptionCollector.java:69)
	at io.tapdata.connector.mysql.MysqlConnector.createTableV2(MysqlConnector.java:393)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$8(HazelcastTargetPdkBaseNode.java:318)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$createTable$9(HazelcastTargetPdkBaseNode.java:316)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createTable(HazelcastTargetPdkBaseNode.java:310)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:207)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: CREATE command denied to user 'wim'@'************' for table 'Target_test_index'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:167)
	at io.tapdata.connector.mysql.MysqlConnector.createTableV2(MysqlConnector.java:388)
	... 30 more

[INFO ] 2024-11-04 14:56:54.310 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 14:56:54.514 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 14:56:54.559 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 14:56:54.559 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 14:56:54.575 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 14:56:54.575 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 14:56:54.575 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 14:56:54.578 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 14:56:54.579 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 14:56:54.589 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 141 ms 
[INFO ] 2024-11-04 14:56:54.589 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 14:56:54.593 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 14:56:54.593 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 14:56:54.596 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 14:56:54.596 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 14:56:54.797 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 13 ms 
[INFO ] 2024-11-04 14:56:55.808 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 14:56:55.808 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 14:56:55.952 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@45baea59 
[INFO ] 2024-11-04 14:56:55.960 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 14:56:55.991 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 14:56:55.991 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 14:56:56.016 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 14:56:56.017 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 14:57:11.452 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 14:57:11.462 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 14:57:11.569 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 14:57:11.569 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 14:57:11.600 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 14:57:11.600 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 14:57:11.600 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 14:57:11.600 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 14:57:12.613 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 14:57:12.637 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-04 14:57:12.640 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index will create Index: TapIndex name name_id indexFields: [TapIndexField name name_id fieldAsc true indexType null; TapIndexField name name fieldAsc true indexType null; ] 
[INFO ] 2024-11-04 14:57:12.671 - [测试查询存在索引失败][Target_test_index] - Exception skipping - The current exception does not match the skip exception strategy, message: Table name: Target_test_indexio.tapdata.entity.event.ddl.index.TapCreateIndexEvent@2197d535: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"name_id"},{"fieldAsc":true,"name":"name"}],"name":"name_id","primary":false,"unique":true}],"tableId":"Target_test_index","time":1730703432636,"type":101}
 
[INFO ] 2024-11-04 14:57:12.671 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 14:57:12.671 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 14:57:12.671 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 14:57:12.674 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 14:57:12.677 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[ERROR] 2024-11-04 14:57:12.681 - [测试查询存在索引失败][Target_test_index] - Table name: Target_test_indexio.tapdata.entity.event.ddl.index.TapCreateIndexEvent@2197d535: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"name_id"},{"fieldAsc":true,"name":"name"}],"name":"name_id","primary":false,"unique":true}],"tableId":"Target_test_index","time":1730703432636,"type":101}
 <-- Error Message -->
Table name: Target_test_indexio.tapdata.entity.event.ddl.index.TapCreateIndexEvent@2197d535: {"indexList":[{"indexFields":[{"fieldAsc":true,"name":"name_id"},{"fieldAsc":true,"name":"name"}],"name":"name_id","primary":false,"unique":true}],"tableId":"Target_test_index","time":1730703432636,"type":101}


<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'name' used in key specification without a key length
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
Table name: Target_test_index
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:397)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:211)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'name' used in key specification without a key length
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$syncIndex$14(HazelcastTargetPdkDataNode.java:381)
	at java.util.ArrayList.forEach(ArrayList.java:1259)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:377)
	... 19 more
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'name' used in key specification without a key length
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$13(HazelcastTargetPdkDataNode.java:386)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 23 more
Caused by: java.sql.SQLSyntaxErrorException: BLOB/TEXT column 'name' used in key specification without a key length
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:167)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:404)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$12(HazelcastTargetPdkDataNode.java:388)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 30 more

[INFO ] 2024-11-04 14:57:12.737 - [测试查询存在索引失败][Target_test_index] - Job suspend in error handle 
[INFO ] 2024-11-04 14:57:12.737 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 14:57:12.737 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 14:57:12.738 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 14:57:12.742 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 14:57:12.742 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 14:57:12.743 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 14:57:12.743 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 14:57:12.743 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 14:57:12.744 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 14:57:12.777 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: d1d3a748-b647-464c-840b-c71d5fc8542c 
[INFO ] 2024-11-04 14:57:12.777 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"d1d3a748-b647-464c-840b-c71d5fc8542c","offset":{"{\"server\":\"d1d3a748-b647-464c-840b-c71d5fc8542c\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1102537792
  time.precision.mode: adaptive_time_microseconds
  database.server.name: d1d3a748-b647-464c-840b-c71d5fc8542c
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-d1d3a748-b647-464c-840b-c71d5fc8542c
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: d1d3a748-b647-464c-840b-c71d5fc8542c
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 14:57:12.989 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 14:57:13.242 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 14:57:13.313 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 14:57:13.313 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 14:57:13.320 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 14:57:13.337 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 14:57:13.337 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 14:57:13.337 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 14:57:13.339 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 14:57:13.340 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 109 ms 
[INFO ] 2024-11-04 14:57:13.340 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 14:57:13.348 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 14:57:13.349 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 14:57:13.349 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 14:57:13.349 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 14:57:13.349 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 9 ms 
[INFO ] 2024-11-04 14:57:16.045 - [测试查询存在索引失败] - Task [测试查询存在索引失败] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-04 14:57:16.045 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 14:57:16.045 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@72df3012 
[INFO ] 2024-11-04 14:57:16.171 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 14:57:16.191 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 14:57:16.193 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 14:57:16.214 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 14:57:16.216 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 14:58:28.817 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 14:58:28.817 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 14:58:28.945 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 14:58:28.945 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 14:58:28.984 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 14:58:28.984 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 14:58:28.984 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 14:58:28.984 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 14:58:29.893 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 14:58:29.896 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 14:58:29.896 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 14:58:29.899 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 14:58:29.967 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 14:58:29.967 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 14:58:29.977 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 14:58:29.977 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 14:58:29.978 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 14:58:29.981 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 14:58:29.981 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 14:58:29.981 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 14:58:29.985 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 14:58:29.985 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 14:58:30.017 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: 8b321145-2685-4201-a4db-1a78246b0cda 
[INFO ] 2024-11-04 14:58:30.018 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"8b321145-2685-4201-a4db-1a78246b0cda","offset":{"{\"server\":\"8b321145-2685-4201-a4db-1a78246b0cda\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 775882873
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 8b321145-2685-4201-a4db-1a78246b0cda
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-8b321145-2685-4201-a4db-1a78246b0cda
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 8b321145-2685-4201-a4db-1a78246b0cda
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 14:58:30.073 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 14:58:30.073 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 14:58:30.224 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-04 14:58:30.226 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index will create Index: TapIndex name name_id indexFields: [TapIndexField name name_id fieldAsc true indexType null; TapIndexField name name fieldAsc true indexType null; ] 
[INFO ] 2024-11-04 14:58:30.267 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index create Index: name_id successfully, cost 48ms 
[INFO ] 2024-11-04 14:58:30.267 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index synchronize indexes completed, cost 73ms totally 
[INFO ] 2024-11-04 14:58:48.776 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 14:58:48.872 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 14:58:48.872 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 14:58:48.877 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 14:58:48.890 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 14:58:48.890 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 14:58:48.891 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 14:58:48.894 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 14:58:48.899 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 123 ms 
[INFO ] 2024-11-04 14:58:48.899 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 14:58:48.921 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 14:58:48.922 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 14:58:48.922 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 14:58:48.923 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 14:58:48.923 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 25 ms 
[INFO ] 2024-11-04 14:58:51.356 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 14:58:51.361 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2c327877 
[INFO ] 2024-11-04 14:58:51.481 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 14:58:51.495 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 14:58:51.495 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 14:58:51.521 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 14:58:51.525 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 16:50:04.117 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 16:50:04.326 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 16:50:04.694 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 16:50:04.900 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 16:50:05.021 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 16:50:05.022 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 16:50:05.022 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 1 ms 
[INFO ] 2024-11-04 16:50:05.023 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 16:50:06.250 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 16:50:06.259 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 16:50:06.260 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 16:50:06.261 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 16:50:06.284 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 16:50:06.286 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 16:50:06.389 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 16:50:06.390 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 16:50:06.391 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 16:50:06.397 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 16:50:10.000 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 16:50:10.045 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 16:50:10.087 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 16:50:10.087 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 16:50:10.112 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 16:50:10.113 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-04 16:50:10.117 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index will create Index: TapIndex name name_id indexFields: [TapIndexField name name_id fieldAsc true indexType null; TapIndexField name name fieldAsc true indexType null; ] 
[INFO ] 2024-11-04 16:50:10.165 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index create Index: name_id successfully, cost 49ms 
[INFO ] 2024-11-04 16:50:10.167 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index synchronize indexes completed, cost 3732ms totally 
[INFO ] 2024-11-04 16:50:10.226 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: 6b1c38c1-1eb0-4ee9-9380-e6514ea6f119 
[INFO ] 2024-11-04 16:50:10.228 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"6b1c38c1-1eb0-4ee9-9380-e6514ea6f119","offset":{"{\"server\":\"6b1c38c1-1eb0-4ee9-9380-e6514ea6f119\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1879915623
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 6b1c38c1-1eb0-4ee9-9380-e6514ea6f119
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-6b1c38c1-1eb0-4ee9-9380-e6514ea6f119
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 6b1c38c1-1eb0-4ee9-9380-e6514ea6f119
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 16:50:10.634 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 16:50:24.435 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 16:50:24.451 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 16:50:24.454 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 16:50:24.532 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 16:50:24.534 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 16:50:24.536 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 16:50:24.536 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 16:50:24.558 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 16:50:24.560 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 204 ms 
[INFO ] 2024-11-04 16:50:24.560 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 16:50:24.610 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 16:50:24.612 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 16:50:24.613 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 16:50:24.614 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 16:50:24.614 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 55 ms 
[INFO ] 2024-11-04 16:50:25.191 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 16:50:25.312 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1bef49ca 
[INFO ] 2024-11-04 16:50:25.315 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 16:50:25.369 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 16:50:25.370 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 16:50:25.407 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 16:50:25.408 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 16:51:25.106 - [测试查询存在索引失败] - Task initialization... 
[INFO ] 2024-11-04 16:51:25.299 - [测试查询存在索引失败] - Start task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败) 
[INFO ] 2024-11-04 16:51:25.300 - [测试查询存在索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-04 16:51:25.350 - [测试查询存在索引失败] - The engine receives 测试查询存在索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-04 16:51:25.404 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] start preload schema,table counts: 1 
[INFO ] 2024-11-04 16:51:25.404 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] start preload schema,table counts: 1 
[INFO ] 2024-11-04 16:51:25.407 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 16:51:25.408 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] preload schema finished, cost 0 ms 
[INFO ] 2024-11-04 16:51:26.366 - [测试查询存在索引失败][Target_test_index] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-04 16:51:38.468 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index already exists Index: PRIMARY and will no longer create index 
[INFO ] 2024-11-04 16:51:38.468 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index will create Index: TapIndex name name_id indexFields: [TapIndexField name name_id fieldAsc true indexType null; TapIndexField name name fieldAsc true indexType null; ] 
[INFO ] 2024-11-04 16:51:38.470 - [测试查询存在索引失败][test_Index] - Source node "test_Index" read batch size: 100 
[INFO ] 2024-11-04 16:51:38.470 - [测试查询存在索引失败][test_Index] - Source node "test_Index" event queue capacity: 200 
[INFO ] 2024-11-04 16:51:38.475 - [测试查询存在索引失败][test_Index] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-04 16:51:38.475 - [测试查询存在索引失败][test_Index] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 16:51:38.476 - [测试查询存在索引失败][test_Index] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-04 16:51:38.513 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index create Index: name_id successfully, cost 37ms 
[INFO ] 2024-11-04 16:51:38.514 - [测试查询存在索引失败][Target_test_index] - Table: Target_test_index synchronize indexes completed, cost 12042ms totally 
[INFO ] 2024-11-04 16:51:38.567 - [测试查询存在索引失败][test_Index] - Initial sync started 
[INFO ] 2024-11-04 16:51:38.568 - [测试查询存在索引失败][test_Index] - Starting batch read, table name: test_Index 
[INFO ] 2024-11-04 16:51:38.568 - [测试查询存在索引失败][test_Index] - Query snapshot row size completed: test_Index(3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec) 
[INFO ] 2024-11-04 16:51:38.568 - [测试查询存在索引失败][test_Index] - Table test_Index is going to be initial synced 
[INFO ] 2024-11-04 16:51:38.573 - [测试查询存在索引失败][test_Index] - Table [test_Index] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-04 16:51:38.573 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 16:51:38.574 - [测试查询存在索引失败][test_Index] - Incremental sync starting... 
[INFO ] 2024-11-04 16:51:38.574 - [测试查询存在索引失败][test_Index] - Initial sync completed 
[INFO ] 2024-11-04 16:51:38.611 - [测试查询存在索引失败][test_Index] - Starting stream read, table list: [test_Index], offset: {"filename":"binlog.000037","position":20478,"gtidSet":""} 
[INFO ] 2024-11-04 16:51:38.612 - [测试查询存在索引失败][test_Index] - Starting mysql cdc, server name: d3d46377-7437-434a-943f-d32b857afc06 
[INFO ] 2024-11-04 16:51:38.663 - [测试查询存在索引失败][test_Index] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"d3d46377-7437-434a-943f-d32b857afc06","offset":{"{\"server\":\"d3d46377-7437-434a-943f-d32b857afc06\"}":"{\"file\":\"binlog.000037\",\"pos\":20478,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 573113025
  time.precision.mode: adaptive_time_microseconds
  database.server.name: d3d46377-7437-434a-943f-d32b857afc06
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-d3d46377-7437-434a-943f-d32b857afc06
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: d3d46377-7437-434a-943f-d32b857afc06
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.test_Index
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-04 16:51:38.664 - [测试查询存在索引失败][test_Index] - Connector Mysql incremental start succeed, tables: [test_Index], data change syncing 
[INFO ] 2024-11-04 16:52:12.181 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] running status set to false 
[INFO ] 2024-11-04 16:52:12.261 - [测试查询存在索引失败][test_Index] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-04 16:52:12.261 - [测试查询存在索引失败][test_Index] - Mysql binlog reader stopped 
[INFO ] 2024-11-04 16:52:12.271 - [测试查询存在索引失败][test_Index] - Incremental sync completed 
[INFO ] 2024-11-04 16:52:12.271 - [测试查询存在索引失败][test_Index] - PDK connector node stopped: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 16:52:12.273 - [测试查询存在索引失败][test_Index] - PDK connector node released: HazelcastSourcePdkDataNode-3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec 
[INFO ] 2024-11-04 16:52:12.273 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] schema data cleaned 
[INFO ] 2024-11-04 16:52:12.284 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] monitor closed 
[INFO ] 2024-11-04 16:52:12.285 - [测试查询存在索引失败][test_Index] - Node test_Index[3726e9d4-4fc5-497f-bb25-0f15e7b5d6ec] close complete, cost 165 ms 
[INFO ] 2024-11-04 16:52:12.285 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] running status set to false 
[INFO ] 2024-11-04 16:52:12.320 - [测试查询存在索引失败][Target_test_index] - PDK connector node stopped: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 16:52:12.320 - [测试查询存在索引失败][Target_test_index] - PDK connector node released: HazelcastTargetPdkDataNode-9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a 
[INFO ] 2024-11-04 16:52:12.321 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] schema data cleaned 
[INFO ] 2024-11-04 16:52:12.322 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] monitor closed 
[INFO ] 2024-11-04 16:52:12.533 - [测试查询存在索引失败][Target_test_index] - Node Target_test_index[9472b5d7-d8c0-4d52-a7e7-ab64ce2bd87a] close complete, cost 38 ms 
[INFO ] 2024-11-04 16:52:13.469 - [测试查询存在索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-04 16:52:13.590 - [测试查询存在索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1033fed3 
[INFO ] 2024-11-04 16:52:13.590 - [测试查询存在索引失败] - Stop task milestones: 672826d8a3e4f31179eacb7c(测试查询存在索引失败)  
[INFO ] 2024-11-04 16:52:13.600 - [测试查询存在索引失败] - Stopped task aspect(s) 
[INFO ] 2024-11-04 16:52:13.603 - [测试查询存在索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-11-04 16:52:13.628 - [测试查询存在索引失败] - Remove memory task client succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
[INFO ] 2024-11-04 16:52:13.835 - [测试查询存在索引失败] - Destroy memory task client cache succeed, task: 测试查询存在索引失败[672826d8a3e4f31179eacb7c] 
