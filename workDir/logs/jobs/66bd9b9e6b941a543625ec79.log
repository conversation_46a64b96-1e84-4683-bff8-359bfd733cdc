[INFO ] 2024-08-15 14:23:12.549 - [任务 6] - Start task milestones: 66bd9b9e6b941a543625ec79(任务 6) 
[INFO ] 2024-08-15 14:23:12.550 - [任务 6] - Task initialization... 
[INFO ] 2024-08-15 14:23:12.697 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-15 14:23:12.794 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-15 14:23:13.235 - [任务 6][DB2_TESTCUSTOMER] - Node DB2_TESTCUSTOMER[9f11d46e-30ee-4988-80a6-d37ae5f240f0] start preload schema,table counts: 1 
[INFO ] 2024-08-15 14:23:13.240 - [任务 6][CUSTOMERS] - Node CUSTOMERS[6f5107ed-28ad-463b-bd30-1c5a3e954c40] start preload schema,table counts: 1 
[INFO ] 2024-08-15 14:23:13.319 - [任务 6][DB2_TESTCUSTOMER] - Node DB2_TESTCUSTOMER[9f11d46e-30ee-4988-80a6-d37ae5f240f0] preload schema finished, cost 85 ms 
[INFO ] 2024-08-15 14:23:13.319 - [任务 6][CUSTOMERS] - Node CUSTOMERS[6f5107ed-28ad-463b-bd30-1c5a3e954c40] preload schema finished, cost 84 ms 
[INFO ] 2024-08-15 14:23:14.035 - [任务 6][CUSTOMERS] - Source node "CUSTOMERS" read batch size: 100 
[INFO ] 2024-08-15 14:23:14.037 - [任务 6][CUSTOMERS] - Source node "CUSTOMERS" event queue capacity: 200 
[INFO ] 2024-08-15 14:23:14.037 - [任务 6][CUSTOMERS] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-08-15 14:23:14.212 - [任务 6][CUSTOMERS] - Table [CUSTOMERS] not open CDC 
[INFO ] 2024-08-15 14:23:14.213 - [任务 6][CUSTOMERS] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1723702994197} 
[INFO ] 2024-08-15 14:23:14.407 - [任务 6][CUSTOMERS] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-15 14:23:14.408 - [任务 6][CUSTOMERS] - Initial sync started 
[INFO ] 2024-08-15 14:23:14.433 - [任务 6][CUSTOMERS] - Starting batch read, table name: CUSTOMERS, offset: null 
[INFO ] 2024-08-15 14:23:14.435 - [任务 6][CUSTOMERS] - Table CUSTOMERS is going to be initial synced 
[INFO ] 2024-08-15 14:23:14.515 - [任务 6][CUSTOMERS] - Table [CUSTOMERS] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-15 14:23:14.516 - [任务 6][CUSTOMERS] - Query table 'CUSTOMERS' counts: 1 
[INFO ] 2024-08-15 14:23:14.520 - [任务 6][CUSTOMERS] - Initial sync completed 
[INFO ] 2024-08-15 14:23:14.520 - [任务 6][CUSTOMERS] - Incremental sync starting... 
[INFO ] 2024-08-15 14:23:14.520 - [任务 6][CUSTOMERS] - Initial sync completed 
[INFO ] 2024-08-15 14:23:14.525 - [任务 6][CUSTOMERS] - Starting stream read, table list: [CUSTOMERS], offset: {"sortString":null,"offsetValue":null,"lastScn":null,"pendingScn":null,"timestamp":1723702994197} 
[INFO ] 2024-08-15 14:23:14.732 - [任务 6][DB2_TESTCUSTOMER] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-15 14:51:29.780 - [任务 6] - Stop task milestones: 66bd9b9e6b941a543625ec79(任务 6)  
[INFO ] 2024-08-15 14:51:30.023 - [任务 6][CUSTOMERS] - Node CUSTOMERS[6f5107ed-28ad-463b-bd30-1c5a3e954c40] running status set to false 
[INFO ] 2024-08-15 14:51:30.025 - [任务 6][CUSTOMERS] - Log Miner is shutting down... 
[ERROR] 2024-08-15 14:51:30.083 - [任务 6][CUSTOMERS] - io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted <-- Full Stack Trace -->
java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:673)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:567)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:223)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	... 6 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: io.grpc.StatusRuntimeException: CANCELLED: Thread interrupted
	at io.grpc.Status.asRuntimeException(Status.java:535)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:649)
	at io.tapdata.connector.db2.cdc.grpc.Db2GrpcLogMiner.startMiner(Db2GrpcLogMiner.java:175)
	at io.tapdata.connector.db2.cdc.Db2CdcRunner.startCdcRunner(Db2CdcRunner.java:35)
	at io.tapdata.connector.db2.Db2Connector.streamRead(Db2Connector.java:280)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.lang.InterruptedException
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.throwIfInterrupted(ClientCalls.java:750)
	at io.grpc.stub.ClientCalls$ThreadlessExecutor.waitAndDrain(ClientCalls.java:733)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.waitForNext(ClientCalls.java:623)
	at io.grpc.stub.ClientCalls$BlockingResponseStream.hasNext(ClientCalls.java:644)
	... 21 more

[INFO ] 2024-08-15 14:51:31.760 - [任务 6][CUSTOMERS] - PDK connector node stopped: HazelcastSourcePdkDataNode-6f5107ed-28ad-463b-bd30-1c5a3e954c40 
[INFO ] 2024-08-15 14:51:31.761 - [任务 6][CUSTOMERS] - PDK connector node released: HazelcastSourcePdkDataNode-6f5107ed-28ad-463b-bd30-1c5a3e954c40 
[INFO ] 2024-08-15 14:51:31.764 - [任务 6][CUSTOMERS] - Node CUSTOMERS[6f5107ed-28ad-463b-bd30-1c5a3e954c40] schema data cleaned 
[INFO ] 2024-08-15 14:51:31.764 - [任务 6][CUSTOMERS] - Node CUSTOMERS[6f5107ed-28ad-463b-bd30-1c5a3e954c40] monitor closed 
[INFO ] 2024-08-15 14:51:31.771 - [任务 6][CUSTOMERS] - Node CUSTOMERS[6f5107ed-28ad-463b-bd30-1c5a3e954c40] close complete, cost 1763 ms 
[INFO ] 2024-08-15 14:51:31.771 - [任务 6][DB2_TESTCUSTOMER] - Node DB2_TESTCUSTOMER[9f11d46e-30ee-4988-80a6-d37ae5f240f0] running status set to false 
[INFO ] 2024-08-15 14:51:31.790 - [任务 6][DB2_TESTCUSTOMER] - PDK connector node stopped: HazelcastTargetPdkDataNode-9f11d46e-30ee-4988-80a6-d37ae5f240f0 
[INFO ] 2024-08-15 14:51:31.790 - [任务 6][DB2_TESTCUSTOMER] - PDK connector node released: HazelcastTargetPdkDataNode-9f11d46e-30ee-4988-80a6-d37ae5f240f0 
[INFO ] 2024-08-15 14:51:31.790 - [任务 6][DB2_TESTCUSTOMER] - Node DB2_TESTCUSTOMER[9f11d46e-30ee-4988-80a6-d37ae5f240f0] schema data cleaned 
[INFO ] 2024-08-15 14:51:31.790 - [任务 6][DB2_TESTCUSTOMER] - Node DB2_TESTCUSTOMER[9f11d46e-30ee-4988-80a6-d37ae5f240f0] monitor closed 
[INFO ] 2024-08-15 14:51:31.791 - [任务 6][DB2_TESTCUSTOMER] - Node DB2_TESTCUSTOMER[9f11d46e-30ee-4988-80a6-d37ae5f240f0] close complete, cost 21 ms 
[INFO ] 2024-08-15 14:51:35.379 - [任务 6] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-15 14:51:35.380 - [任务 6] - Stopped task aspect(s) 
[INFO ] 2024-08-15 14:51:35.431 - [任务 6] - Snapshot order controller have been removed 
[INFO ] 2024-08-15 14:51:35.432 - [任务 6] - Remove memory task client succeed, task: 任务 6[66bd9b9e6b941a543625ec79] 
[INFO ] 2024-08-15 14:51:35.432 - [任务 6] - Destroy memory task client cache succeed, task: 任务 6[66bd9b9e6b941a543625ec79] 
