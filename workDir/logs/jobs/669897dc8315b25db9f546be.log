[INFO ] 2024-07-18 12:19:51.124 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379] - Task initialization... 
[INFO ] 2024-07-18 12:19:51.328 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379] - Start task milestones: 669897dc8315b25db9f546be(t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379) 
[INFO ] 2024-07-18 12:19:51.386 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 12:19:51.658 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379] - The engine receives t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 12:19:51.659 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[6626d121-e69c-4a8a-b016-b6654d28c729] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:19:51.659 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[2d64f8f5-1ea9-49ad-9ec9-7ad16ab3e044] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:19:51.659 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[96d302c4-cffe-4bd0-960a-ef3fb5659597] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:19:51.659 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[6626d121-e69c-4a8a-b016-b6654d28c729] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 12:19:51.659 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][Union] - Node Union[81583d31-da87-4878-8e07-20f1fecb2128] start preload schema,table counts: 1 
[INFO ] 2024-07-18 12:19:51.659 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[2d64f8f5-1ea9-49ad-9ec9-7ad16ab3e044] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 12:19:51.659 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[96d302c4-cffe-4bd0-960a-ef3fb5659597] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 12:19:51.659 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][Union] - Node Union[81583d31-da87-4878-8e07-20f1fecb2128] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 12:19:52.018 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Source node "qa_mongodb_cluster_27017_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 12:19:52.019 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Source node "qa_mongodb_cluster_27017_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 12:19:52.019 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 12:19:52.019 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-18 12:19:52.084 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Source node "qa_mongodb_cluster_27017_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 12:19:52.084 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Source node "qa_mongodb_cluster_27017_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 12:19:52.084 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 12:19:52.084 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-18 12:19:52.165 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 12:19:52.165 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Starting batch read, table name: i4_9_2_s2, offset: null 
[INFO ] 2024-07-18 12:19:52.225 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Table i4_9_2_s2 is going to be initial synced 
[INFO ] 2024-07-18 12:19:52.225 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 12:19:52.225 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Starting batch read, table name: i4_9_2_s1, offset: null 
[INFO ] 2024-07-18 12:19:52.226 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Table i4_9_2_s1 is going to be initial synced 
[INFO ] 2024-07-18 12:19:52.239 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Table [i4_9_2_s2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 12:19:52.239 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Query table 'i4_9_2_s2' counts: 1 
[INFO ] 2024-07-18 12:19:52.280 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 12:19:52.281 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mysql_repl_33306_t_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 12:19:52.319 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Query table 'i4_9_2_s1' counts: 2 
[INFO ] 2024-07-18 12:19:52.337 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Table [i4_9_2_s1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 12:19:52.337 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 12:20:04.457 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[6626d121-e69c-4a8a-b016-b6654d28c729] running status set to false 
[INFO ] 2024-07-18 12:20:04.457 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-6626d121-e69c-4a8a-b016-b6654d28c729 
[INFO ] 2024-07-18 12:20:04.457 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-6626d121-e69c-4a8a-b016-b6654d28c729 
[INFO ] 2024-07-18 12:20:04.457 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[6626d121-e69c-4a8a-b016-b6654d28c729] schema data cleaned 
[INFO ] 2024-07-18 12:20:04.458 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[6626d121-e69c-4a8a-b016-b6654d28c729] monitor closed 
[INFO ] 2024-07-18 12:20:04.458 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[6626d121-e69c-4a8a-b016-b6654d28c729] close complete, cost 23 ms 
[INFO ] 2024-07-18 12:20:04.501 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[96d302c4-cffe-4bd0-960a-ef3fb5659597] running status set to false 
[INFO ] 2024-07-18 12:20:04.501 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][Union] - Node Union[81583d31-da87-4878-8e07-20f1fecb2128] running status set to false 
[INFO ] 2024-07-18 12:20:04.502 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][Union] - Node Union[81583d31-da87-4878-8e07-20f1fecb2128] schema data cleaned 
[INFO ] 2024-07-18 12:20:04.502 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][Union] - Node Union[81583d31-da87-4878-8e07-20f1fecb2128] monitor closed 
[INFO ] 2024-07-18 12:20:04.507 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][Union] - Node Union[81583d31-da87-4878-8e07-20f1fecb2128] close complete, cost 0 ms 
[INFO ] 2024-07-18 12:20:04.507 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[2d64f8f5-1ea9-49ad-9ec9-7ad16ab3e044] running status set to false 
[INFO ] 2024-07-18 12:20:04.517 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-96d302c4-cffe-4bd0-960a-ef3fb5659597 
[INFO ] 2024-07-18 12:20:04.517 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-96d302c4-cffe-4bd0-960a-ef3fb5659597 
[INFO ] 2024-07-18 12:20:04.517 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[96d302c4-cffe-4bd0-960a-ef3fb5659597] schema data cleaned 
[INFO ] 2024-07-18 12:20:04.517 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[96d302c4-cffe-4bd0-960a-ef3fb5659597] monitor closed 
[INFO ] 2024-07-18 12:20:04.517 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[96d302c4-cffe-4bd0-960a-ef3fb5659597] close complete, cost 18 ms 
[INFO ] 2024-07-18 12:20:04.569 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mysql_repl_33306_t_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-2d64f8f5-1ea9-49ad-9ec9-7ad16ab3e044 
[INFO ] 2024-07-18 12:20:04.569 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mysql_repl_33306_t_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-2d64f8f5-1ea9-49ad-9ec9-7ad16ab3e044 
[INFO ] 2024-07-18 12:20:04.569 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[2d64f8f5-1ea9-49ad-9ec9-7ad16ab3e044] schema data cleaned 
[INFO ] 2024-07-18 12:20:04.571 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[2d64f8f5-1ea9-49ad-9ec9-7ad16ab3e044] monitor closed 
[INFO ] 2024-07-18 12:20:04.576 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379][qa_mysql_repl_33306_t_1717403468657_3537] - Node qa_mysql_repl_33306_t_1717403468657_3537[2d64f8f5-1ea9-49ad-9ec9-7ad16ab3e044] close complete, cost 63 ms 
[INFO ] 2024-07-18 12:20:09.184 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 12:20:09.184 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@77a12cff 
[INFO ] 2024-07-18 12:20:09.337 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379] - Stop task milestones: 669897dc8315b25db9f546be(t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379)  
[INFO ] 2024-07-18 12:20:09.337 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379] - Stopped task aspect(s) 
[INFO ] 2024-07-18 12:20:09.337 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 12:20:09.373 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379] - Remove memory task client succeed, task: t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379[669897dc8315b25db9f546be] 
[INFO ] 2024-07-18 12:20:09.373 - [t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379] - Destroy memory task client cache succeed, task: t_4.9.2-MongoDB 2 Union 2 MySQL_1717403468657_3537-1721276379[669897dc8315b25db9f546be] 
