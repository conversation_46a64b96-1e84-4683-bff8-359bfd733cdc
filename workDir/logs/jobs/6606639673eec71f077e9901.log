[INFO ] 2024-03-29 14:45:45.733 - [orders(100)][102de3f5-f8fe-4e63-8a4c-851743020e69] - Node 102de3f5-f8fe-4e63-8a4c-851743020e69[102de3f5-f8fe-4e63-8a4c-851743020e69] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:45:45.733 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:45:45.734 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:45:45.734 - [orders(100)][102de3f5-f8fe-4e63-8a4c-851743020e69] - Node 102de3f5-f8fe-4e63-8a4c-851743020e69[102de3f5-f8fe-4e63-8a4c-851743020e69] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:45:45.735 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 2 ms 
[INFO ] 2024-03-29 14:45:45.736 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:45:46.059 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:45:46.470 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7e9685e error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7e9685e error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7e9685e error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:45:50.905 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:45:50.914 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:45:50.923 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:45:50.927 - [orders(100)][102de3f5-f8fe-4e63-8a4c-851743020e69] - Node 102de3f5-f8fe-4e63-8a4c-851743020e69[102de3f5-f8fe-4e63-8a4c-851743020e69] running status set to false 
[INFO ] 2024-03-29 14:45:50.927 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:45:50.927 - [orders(100)][102de3f5-f8fe-4e63-8a4c-851743020e69] - Node 102de3f5-f8fe-4e63-8a4c-851743020e69[102de3f5-f8fe-4e63-8a4c-851743020e69] schema data cleaned 
[INFO ] 2024-03-29 14:45:50.928 - [orders(100)][102de3f5-f8fe-4e63-8a4c-851743020e69] - Node 102de3f5-f8fe-4e63-8a4c-851743020e69[102de3f5-f8fe-4e63-8a4c-851743020e69] monitor closed 
[INFO ] 2024-03-29 14:45:50.930 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 25 ms 
[INFO ] 2024-03-29 14:45:50.934 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6f94114f-0b82-4057-acb5-5e028c893aca 
[INFO ] 2024-03-29 14:45:50.936 - [orders(100)][102de3f5-f8fe-4e63-8a4c-851743020e69] - Node 102de3f5-f8fe-4e63-8a4c-851743020e69[102de3f5-f8fe-4e63-8a4c-851743020e69] close complete, cost 29 ms 
[INFO ] 2024-03-29 14:45:50.940 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6f94114f-0b82-4057-acb5-5e028c893aca 
[INFO ] 2024-03-29 14:45:50.944 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:45:50.944 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:45:50.944 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 56 ms 
[INFO ] 2024-03-29 14:45:50.944 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-102de3f5-f8fe-4e63-8a4c-851743020e69 complete, cost 5778ms 
[INFO ] 2024-03-29 14:45:56.698 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:45:56.704 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:45:56.707 - [orders(100)][df02c815-5e82-4109-a5f5-33d92e0a1c30] - Node df02c815-5e82-4109-a5f5-33d92e0a1c30[df02c815-5e82-4109-a5f5-33d92e0a1c30] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:45:56.707 - [orders(100)][df02c815-5e82-4109-a5f5-33d92e0a1c30] - Node df02c815-5e82-4109-a5f5-33d92e0a1c30[df02c815-5e82-4109-a5f5-33d92e0a1c30] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:45:56.707 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:45:56.715 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:45:56.729 - [orders(100)][0c9f257f-acef-4c8d-9dbc-1c18e7331933] - Node 0c9f257f-acef-4c8d-9dbc-1c18e7331933[0c9f257f-acef-4c8d-9dbc-1c18e7331933] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:45:56.734 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:45:56.740 - [orders(100)][0c9f257f-acef-4c8d-9dbc-1c18e7331933] - Node 0c9f257f-acef-4c8d-9dbc-1c18e7331933[0c9f257f-acef-4c8d-9dbc-1c18e7331933] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:45:56.744 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:45:56.748 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:45:56.748 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:45:56.748 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:45:56.756 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:45:56.757 - [orders(100)][3448a78d-c12c-4fa2-9e49-015c59aecd9f] - Node 3448a78d-c12c-4fa2-9e49-015c59aecd9f[3448a78d-c12c-4fa2-9e49-015c59aecd9f] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:45:56.757 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:45:56.757 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:45:56.757 - [orders(100)][3448a78d-c12c-4fa2-9e49-015c59aecd9f] - Node 3448a78d-c12c-4fa2-9e49-015c59aecd9f[3448a78d-c12c-4fa2-9e49-015c59aecd9f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:45:56.799 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 14:45:56.800 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 14:45:56.804 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:45:56.968 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@377f3a21 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@377f3a21 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@377f3a21 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-29 14:45:56.974 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4db0bd3e error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4db0bd3e error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4db0bd3e error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-29 14:45:57.120 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7037485b error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7037485b error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7037485b error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:45:57.121 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:45:57.143 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6f94114f-0b82-4057-acb5-5e028c893aca 
[INFO ] 2024-03-29 14:45:57.147 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6f94114f-0b82-4057-acb5-5e028c893aca 
[INFO ] 2024-03-29 14:45:57.147 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:45:57.148 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:45:57.298 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 31 ms 
[INFO ] 2024-03-29 14:45:57.315 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:45:57.319 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6f94114f-0b82-4057-acb5-5e028c893aca 
[INFO ] 2024-03-29 14:45:57.319 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6f94114f-0b82-4057-acb5-5e028c893aca 
[INFO ] 2024-03-29 14:45:57.320 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:45:57.320 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:45:57.321 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 29 ms 
[INFO ] 2024-03-29 14:45:57.401 - [orders(100)][b5a5c858-ee6e-4447-9b5c-1a3243e000a6] - Node b5a5c858-ee6e-4447-9b5c-1a3243e000a6[b5a5c858-ee6e-4447-9b5c-1a3243e000a6] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:45:57.401 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:45:57.402 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:45:57.402 - [orders(100)][b5a5c858-ee6e-4447-9b5c-1a3243e000a6] - Node b5a5c858-ee6e-4447-9b5c-1a3243e000a6[b5a5c858-ee6e-4447-9b5c-1a3243e000a6] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:45:57.405 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:45:57.405 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:45:57.503 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 14:45:57.514 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:45:57.525 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-6f94114f-0b82-4057-acb5-5e028c893aca 
[INFO ] 2024-03-29 14:45:57.526 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-6f94114f-0b82-4057-acb5-5e028c893aca 
[INFO ] 2024-03-29 14:45:57.527 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:45:57.528 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:45:57.551 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 45 ms 
[ERROR] 2024-03-29 14:45:57.552 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[ERROR] 2024-03-29 14:45:57.552 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@b7c7b3f error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@b7c7b3f error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@b7c7b3f error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:45:57.571 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:45:57.571 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:45:57.571 - [orders(100)][bf015f37-7722-479e-820c-28fbfb971e2a] - Node bf015f37-7722-479e-820c-28fbfb971e2a[bf015f37-7722-479e-820c-28fbfb971e2a] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:45:57.571 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:45:57.571 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:45:57.571 - [orders(100)][bf015f37-7722-479e-820c-28fbfb971e2a] - Node bf015f37-7722-479e-820c-28fbfb971e2a[bf015f37-7722-479e-820c-28fbfb971e2a] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:45:57.592 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:45:57.643 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:45:57.644 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@214dec43 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@214dec43 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@214dec43 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:45:59.505 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:45:59.508 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:45:59.509 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:45:59.511 - [orders(100)][df02c815-5e82-4109-a5f5-33d92e0a1c30] - Node df02c815-5e82-4109-a5f5-33d92e0a1c30[df02c815-5e82-4109-a5f5-33d92e0a1c30] running status set to false 
[INFO ] 2024-03-29 14:45:59.511 - [orders(100)][0c9f257f-acef-4c8d-9dbc-1c18e7331933] - Node 0c9f257f-acef-4c8d-9dbc-1c18e7331933[0c9f257f-acef-4c8d-9dbc-1c18e7331933] running status set to false 
[INFO ] 2024-03-29 14:45:59.511 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:45:59.511 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:45:59.511 - [orders(100)][3448a78d-c12c-4fa2-9e49-015c59aecd9f] - Node 3448a78d-c12c-4fa2-9e49-015c59aecd9f[3448a78d-c12c-4fa2-9e49-015c59aecd9f] running status set to false 
[INFO ] 2024-03-29 14:45:59.511 - [orders(100)][0c9f257f-acef-4c8d-9dbc-1c18e7331933] - Node 0c9f257f-acef-4c8d-9dbc-1c18e7331933[0c9f257f-acef-4c8d-9dbc-1c18e7331933] schema data cleaned 
[INFO ] 2024-03-29 14:45:59.511 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:45:59.512 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:45:59.513 - [orders(100)][3448a78d-c12c-4fa2-9e49-015c59aecd9f] - Node 3448a78d-c12c-4fa2-9e49-015c59aecd9f[3448a78d-c12c-4fa2-9e49-015c59aecd9f] schema data cleaned 
[INFO ] 2024-03-29 14:45:59.513 - [orders(100)][df02c815-5e82-4109-a5f5-33d92e0a1c30] - Node df02c815-5e82-4109-a5f5-33d92e0a1c30[df02c815-5e82-4109-a5f5-33d92e0a1c30] schema data cleaned 
[INFO ] 2024-03-29 14:45:59.513 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 12 ms 
[INFO ] 2024-03-29 14:45:59.513 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:45:59.516 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 15 ms 
[INFO ] 2024-03-29 14:45:59.516 - [orders(100)][3448a78d-c12c-4fa2-9e49-015c59aecd9f] - Node 3448a78d-c12c-4fa2-9e49-015c59aecd9f[3448a78d-c12c-4fa2-9e49-015c59aecd9f] monitor closed 
[INFO ] 2024-03-29 14:45:59.518 - [orders(100)][df02c815-5e82-4109-a5f5-33d92e0a1c30] - Node df02c815-5e82-4109-a5f5-33d92e0a1c30[df02c815-5e82-4109-a5f5-33d92e0a1c30] monitor closed 
[INFO ] 2024-03-29 14:45:59.524 - [orders(100)][0c9f257f-acef-4c8d-9dbc-1c18e7331933] - Node 0c9f257f-acef-4c8d-9dbc-1c18e7331933[0c9f257f-acef-4c8d-9dbc-1c18e7331933] monitor closed 
[INFO ] 2024-03-29 14:45:59.524 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:45:59.525 - [orders(100)][3448a78d-c12c-4fa2-9e49-015c59aecd9f] - Node 3448a78d-c12c-4fa2-9e49-015c59aecd9f[3448a78d-c12c-4fa2-9e49-015c59aecd9f] close complete, cost 13 ms 
[INFO ] 2024-03-29 14:45:59.527 - [orders(100)][df02c815-5e82-4109-a5f5-33d92e0a1c30] - Node df02c815-5e82-4109-a5f5-33d92e0a1c30[df02c815-5e82-4109-a5f5-33d92e0a1c30] close complete, cost 15 ms 
[INFO ] 2024-03-29 14:45:59.528 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 17 ms 
[INFO ] 2024-03-29 14:45:59.528 - [orders(100)][0c9f257f-acef-4c8d-9dbc-1c18e7331933] - Node 0c9f257f-acef-4c8d-9dbc-1c18e7331933[0c9f257f-acef-4c8d-9dbc-1c18e7331933] close complete, cost 14 ms 
[INFO ] 2024-03-29 14:45:59.528 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-df02c815-5e82-4109-a5f5-33d92e0a1c30 complete, cost 3027ms 
[INFO ] 2024-03-29 14:45:59.530 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-3448a78d-c12c-4fa2-9e49-015c59aecd9f complete, cost 3014ms 
[INFO ] 2024-03-29 14:45:59.532 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-0c9f257f-acef-4c8d-9dbc-1c18e7331933 complete, cost 3019ms 
[INFO ] 2024-03-29 14:46:00.095 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:00.096 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:00.098 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:00.100 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:00.100 - [orders(100)][b5a5c858-ee6e-4447-9b5c-1a3243e000a6] - Node b5a5c858-ee6e-4447-9b5c-1a3243e000a6[b5a5c858-ee6e-4447-9b5c-1a3243e000a6] running status set to false 
[INFO ] 2024-03-29 14:46:00.100 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:00.102 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:00.102 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:00.102 - [orders(100)][b5a5c858-ee6e-4447-9b5c-1a3243e000a6] - Node b5a5c858-ee6e-4447-9b5c-1a3243e000a6[b5a5c858-ee6e-4447-9b5c-1a3243e000a6] schema data cleaned 
[INFO ] 2024-03-29 14:46:00.102 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:00.103 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 34 ms 
[INFO ] 2024-03-29 14:46:00.103 - [orders(100)][b5a5c858-ee6e-4447-9b5c-1a3243e000a6] - Node b5a5c858-ee6e-4447-9b5c-1a3243e000a6[b5a5c858-ee6e-4447-9b5c-1a3243e000a6] monitor closed 
[INFO ] 2024-03-29 14:46:00.105 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 26 ms 
[INFO ] 2024-03-29 14:46:00.105 - [orders(100)][b5a5c858-ee6e-4447-9b5c-1a3243e000a6] - Node b5a5c858-ee6e-4447-9b5c-1a3243e000a6[b5a5c858-ee6e-4447-9b5c-1a3243e000a6] close complete, cost 26 ms 
[INFO ] 2024-03-29 14:46:00.123 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-b5a5c858-ee6e-4447-9b5c-1a3243e000a6 complete, cost 2899ms 
[INFO ] 2024-03-29 14:46:00.123 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:00.126 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:00.126 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:00.129 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:00.129 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:00.161 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 8 ms 
[INFO ] 2024-03-29 14:46:00.161 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:00.161 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:00.161 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:00.169 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 2 ms 
[INFO ] 2024-03-29 14:46:00.169 - [orders(100)][bf015f37-7722-479e-820c-28fbfb971e2a] - Node bf015f37-7722-479e-820c-28fbfb971e2a[bf015f37-7722-479e-820c-28fbfb971e2a] running status set to false 
[INFO ] 2024-03-29 14:46:00.177 - [orders(100)][bf015f37-7722-479e-820c-28fbfb971e2a] - Node bf015f37-7722-479e-820c-28fbfb971e2a[bf015f37-7722-479e-820c-28fbfb971e2a] schema data cleaned 
[INFO ] 2024-03-29 14:46:00.177 - [orders(100)][bf015f37-7722-479e-820c-28fbfb971e2a] - Node bf015f37-7722-479e-820c-28fbfb971e2a[bf015f37-7722-479e-820c-28fbfb971e2a] monitor closed 
[INFO ] 2024-03-29 14:46:00.179 - [orders(100)][bf015f37-7722-479e-820c-28fbfb971e2a] - Node bf015f37-7722-479e-820c-28fbfb971e2a[bf015f37-7722-479e-820c-28fbfb971e2a] close complete, cost 11 ms 
[INFO ] 2024-03-29 14:46:00.179 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-bf015f37-7722-479e-820c-28fbfb971e2a complete, cost 2812ms 
[INFO ] 2024-03-29 14:46:03.009 - [orders(100)][25ab3265-7fb0-4960-a558-a33037196afb] - Node 25ab3265-7fb0-4960-a558-a33037196afb[25ab3265-7fb0-4960-a558-a33037196afb] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:46:03.009 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:03.010 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:03.012 - [orders(100)][25ab3265-7fb0-4960-a558-a33037196afb] - Node 25ab3265-7fb0-4960-a558-a33037196afb[25ab3265-7fb0-4960-a558-a33037196afb] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:03.012 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:03.012 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:46:03.027 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:46:03.061 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:46:03.066 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@77f0178f error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@77f0178f error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@77f0178f error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:46:04.383 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:04.383 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:04.383 - [orders(100)][94896a61-f8fc-413a-92d9-33918f3527d5] - Node 94896a61-f8fc-413a-92d9-33918f3527d5[94896a61-f8fc-413a-92d9-33918f3527d5] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:46:04.383 - [orders(100)][94896a61-f8fc-413a-92d9-33918f3527d5] - Node 94896a61-f8fc-413a-92d9-33918f3527d5[94896a61-f8fc-413a-92d9-33918f3527d5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:04.383 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:04.384 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:46:04.405 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:46:04.407 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:46:04.611 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3cc99ac3 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3cc99ac3 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3cc99ac3 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:46:05.549 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:05.549 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:05.549 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:05.549 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:05.552 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:05.552 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 5 ms 
[INFO ] 2024-03-29 14:46:05.585 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:05.587 - [orders(100)][25ab3265-7fb0-4960-a558-a33037196afb] - Node 25ab3265-7fb0-4960-a558-a33037196afb[25ab3265-7fb0-4960-a558-a33037196afb] running status set to false 
[INFO ] 2024-03-29 14:46:05.587 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:05.587 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:05.587 - [orders(100)][25ab3265-7fb0-4960-a558-a33037196afb] - Node 25ab3265-7fb0-4960-a558-a33037196afb[25ab3265-7fb0-4960-a558-a33037196afb] schema data cleaned 
[INFO ] 2024-03-29 14:46:05.587 - [orders(100)][25ab3265-7fb0-4960-a558-a33037196afb] - Node 25ab3265-7fb0-4960-a558-a33037196afb[25ab3265-7fb0-4960-a558-a33037196afb] monitor closed 
[INFO ] 2024-03-29 14:46:05.587 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 3 ms 
[INFO ] 2024-03-29 14:46:05.587 - [orders(100)][25ab3265-7fb0-4960-a558-a33037196afb] - Node 25ab3265-7fb0-4960-a558-a33037196afb[25ab3265-7fb0-4960-a558-a33037196afb] close complete, cost 3 ms 
[INFO ] 2024-03-29 14:46:05.800 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-25ab3265-7fb0-4960-a558-a33037196afb complete, cost 2619ms 
[INFO ] 2024-03-29 14:46:06.930 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:06.930 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:06.930 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:06.931 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:06.931 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:06.932 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 7 ms 
[INFO ] 2024-03-29 14:46:06.963 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:06.963 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:06.964 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:06.964 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 1 ms 
[INFO ] 2024-03-29 14:46:06.967 - [orders(100)][94896a61-f8fc-413a-92d9-33918f3527d5] - Node 94896a61-f8fc-413a-92d9-33918f3527d5[94896a61-f8fc-413a-92d9-33918f3527d5] running status set to false 
[INFO ] 2024-03-29 14:46:06.968 - [orders(100)][94896a61-f8fc-413a-92d9-33918f3527d5] - Node 94896a61-f8fc-413a-92d9-33918f3527d5[94896a61-f8fc-413a-92d9-33918f3527d5] schema data cleaned 
[INFO ] 2024-03-29 14:46:06.968 - [orders(100)][94896a61-f8fc-413a-92d9-33918f3527d5] - Node 94896a61-f8fc-413a-92d9-33918f3527d5[94896a61-f8fc-413a-92d9-33918f3527d5] monitor closed 
[INFO ] 2024-03-29 14:46:06.968 - [orders(100)][94896a61-f8fc-413a-92d9-33918f3527d5] - Node 94896a61-f8fc-413a-92d9-33918f3527d5[94896a61-f8fc-413a-92d9-33918f3527d5] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:07.173 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-94896a61-f8fc-413a-92d9-33918f3527d5 complete, cost 2625ms 
[INFO ] 2024-03-29 14:46:08.977 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:08.977 - [orders(100)][05a1ab77-d5bd-42a5-acf7-3bc12d243215] - Node 05a1ab77-d5bd-42a5-acf7-3bc12d243215[05a1ab77-d5bd-42a5-acf7-3bc12d243215] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:46:08.978 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:08.980 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:08.984 - [orders(100)][05a1ab77-d5bd-42a5-acf7-3bc12d243215] - Node 05a1ab77-d5bd-42a5-acf7-3bc12d243215[05a1ab77-d5bd-42a5-acf7-3bc12d243215] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:08.984 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:46:09.014 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:46:09.058 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:46:09.058 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@16a98c26 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@16a98c26 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@16a98c26 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:46:11.382 - [orders(100)][47c838dc-9a32-46e7-bcbb-f7fb1991624d] - Node 47c838dc-9a32-46e7-bcbb-f7fb1991624d[47c838dc-9a32-46e7-bcbb-f7fb1991624d] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:46:11.382 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:11.382 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:11.382 - [orders(100)][47c838dc-9a32-46e7-bcbb-f7fb1991624d] - Node 47c838dc-9a32-46e7-bcbb-f7fb1991624d[47c838dc-9a32-46e7-bcbb-f7fb1991624d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:11.384 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:11.384 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:46:11.402 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:46:11.402 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:46:11.547 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5a57982 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5a57982 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5a57982 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:46:11.548 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:11.551 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:11.551 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:11.554 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:11.554 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:11.578 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 11 ms 
[INFO ] 2024-03-29 14:46:11.581 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:11.585 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:11.586 - [orders(100)][05a1ab77-d5bd-42a5-acf7-3bc12d243215] - Node 05a1ab77-d5bd-42a5-acf7-3bc12d243215[05a1ab77-d5bd-42a5-acf7-3bc12d243215] running status set to false 
[INFO ] 2024-03-29 14:46:11.587 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:11.588 - [orders(100)][05a1ab77-d5bd-42a5-acf7-3bc12d243215] - Node 05a1ab77-d5bd-42a5-acf7-3bc12d243215[05a1ab77-d5bd-42a5-acf7-3bc12d243215] schema data cleaned 
[INFO ] 2024-03-29 14:46:11.588 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 12 ms 
[INFO ] 2024-03-29 14:46:11.589 - [orders(100)][05a1ab77-d5bd-42a5-acf7-3bc12d243215] - Node 05a1ab77-d5bd-42a5-acf7-3bc12d243215[05a1ab77-d5bd-42a5-acf7-3bc12d243215] monitor closed 
[INFO ] 2024-03-29 14:46:11.590 - [orders(100)][05a1ab77-d5bd-42a5-acf7-3bc12d243215] - Node 05a1ab77-d5bd-42a5-acf7-3bc12d243215[05a1ab77-d5bd-42a5-acf7-3bc12d243215] close complete, cost 9 ms 
[INFO ] 2024-03-29 14:46:11.590 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-05a1ab77-d5bd-42a5-acf7-3bc12d243215 complete, cost 2659ms 
[INFO ] 2024-03-29 14:46:12.736 - [orders(100)][9daa7ff8-be74-41cb-940d-aeaa8fe0a29c] - Node 9daa7ff8-be74-41cb-940d-aeaa8fe0a29c[9daa7ff8-be74-41cb-940d-aeaa8fe0a29c] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:46:12.736 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:12.737 - [orders(100)][9daa7ff8-be74-41cb-940d-aeaa8fe0a29c] - Node 9daa7ff8-be74-41cb-940d-aeaa8fe0a29c[9daa7ff8-be74-41cb-940d-aeaa8fe0a29c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:12.738 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:12.738 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:12.749 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:46:12.749 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:46:12.768 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:46:12.768 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4feb6efc error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4feb6efc error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4feb6efc error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:46:13.238 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:13.238 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:13.238 - [orders(100)][8a4f5328-29b5-4700-b9d7-48a40583a957] - Node 8a4f5328-29b5-4700-b9d7-48a40583a957[8a4f5328-29b5-4700-b9d7-48a40583a957] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:46:13.238 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:13.238 - [orders(100)][8a4f5328-29b5-4700-b9d7-48a40583a957] - Node 8a4f5328-29b5-4700-b9d7-48a40583a957[8a4f5328-29b5-4700-b9d7-48a40583a957] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:13.238 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:46:13.248 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:46:13.248 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:46:13.353 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@31908306 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@31908306 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@31908306 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:46:13.353 - [orders(100)][a2fc8d36-6bc3-4746-8c43-34abe552cce8] - Node a2fc8d36-6bc3-4746-8c43-34abe552cce8[a2fc8d36-6bc3-4746-8c43-34abe552cce8] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:46:13.353 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:13.353 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:13.356 - [orders(100)][a2fc8d36-6bc3-4746-8c43-34abe552cce8] - Node a2fc8d36-6bc3-4746-8c43-34abe552cce8[a2fc8d36-6bc3-4746-8c43-34abe552cce8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:13.356 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:13.362 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:46:13.362 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:46:13.382 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:46:13.382 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@30bad073 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@30bad073 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@30bad073 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:46:13.925 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:13.925 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:13.925 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:13.926 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:13.926 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:13.942 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 2 ms 
[INFO ] 2024-03-29 14:46:13.942 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:13.942 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:13.942 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:13.942 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:13.947 - [orders(100)][47c838dc-9a32-46e7-bcbb-f7fb1991624d] - Node 47c838dc-9a32-46e7-bcbb-f7fb1991624d[47c838dc-9a32-46e7-bcbb-f7fb1991624d] running status set to false 
[INFO ] 2024-03-29 14:46:13.948 - [orders(100)][47c838dc-9a32-46e7-bcbb-f7fb1991624d] - Node 47c838dc-9a32-46e7-bcbb-f7fb1991624d[47c838dc-9a32-46e7-bcbb-f7fb1991624d] schema data cleaned 
[INFO ] 2024-03-29 14:46:13.948 - [orders(100)][47c838dc-9a32-46e7-bcbb-f7fb1991624d] - Node 47c838dc-9a32-46e7-bcbb-f7fb1991624d[47c838dc-9a32-46e7-bcbb-f7fb1991624d] monitor closed 
[INFO ] 2024-03-29 14:46:13.948 - [orders(100)][47c838dc-9a32-46e7-bcbb-f7fb1991624d] - Node 47c838dc-9a32-46e7-bcbb-f7fb1991624d[47c838dc-9a32-46e7-bcbb-f7fb1991624d] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:13.949 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-47c838dc-9a32-46e7-bcbb-f7fb1991624d complete, cost 2602ms 
[INFO ] 2024-03-29 14:46:14.177 - [orders(100)][1de99b22-6825-4762-b79e-8e44b1f59390] - Node 1de99b22-6825-4762-b79e-8e44b1f59390[1de99b22-6825-4762-b79e-8e44b1f59390] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:46:14.177 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:14.177 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:14.178 - [orders(100)][1de99b22-6825-4762-b79e-8e44b1f59390] - Node 1de99b22-6825-4762-b79e-8e44b1f59390[1de99b22-6825-4762-b79e-8e44b1f59390] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:14.178 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:14.178 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:46:14.194 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:46:14.194 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:46:14.316 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7702652 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7702652 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7702652 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:46:14.316 - [orders(100)][805b8a3b-956b-495d-a367-ac4e9cf2c263] - Node 805b8a3b-956b-495d-a367-ac4e9cf2c263[805b8a3b-956b-495d-a367-ac4e9cf2c263] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:46:14.316 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:14.316 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:14.316 - [orders(100)][805b8a3b-956b-495d-a367-ac4e9cf2c263] - Node 805b8a3b-956b-495d-a367-ac4e9cf2c263[805b8a3b-956b-495d-a367-ac4e9cf2c263] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:14.316 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:14.317 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:46:14.328 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:46:14.332 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:46:14.378 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@70228fec error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@70228fec error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@70228fec error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:46:14.378 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:14.378 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:14.378 - [orders(100)][b7c03c40-8364-437c-86bf-e56407456a77] - Node b7c03c40-8364-437c-86bf-e56407456a77[b7c03c40-8364-437c-86bf-e56407456a77] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:46:14.378 - [orders(100)][b7c03c40-8364-437c-86bf-e56407456a77] - Node b7c03c40-8364-437c-86bf-e56407456a77[b7c03c40-8364-437c-86bf-e56407456a77] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:14.378 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:14.378 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:46:14.392 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:46:14.392 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:46:14.596 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@77df7745 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@77df7745 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@77df7745 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:46:14.920 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:14.920 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:14.920 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:14.921 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:14.921 - [orders(100)][417cfd03-b5a1-4dce-81ba-6031ab4193a4] - Node 417cfd03-b5a1-4dce-81ba-6031ab4193a4[417cfd03-b5a1-4dce-81ba-6031ab4193a4] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:46:14.934 - [orders(100)][417cfd03-b5a1-4dce-81ba-6031ab4193a4] - Node 417cfd03-b5a1-4dce-81ba-6031ab4193a4[417cfd03-b5a1-4dce-81ba-6031ab4193a4] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:46:14.935 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:46:14.964 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:46:14.964 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@52275f28 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@52275f28 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@52275f28 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:46:15.033 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:15.033 - [orders(100)][c7c6efdb-e233-42bb-8bfd-6e34d134dc62] - Node c7c6efdb-e233-42bb-8bfd-6e34d134dc62[c7c6efdb-e233-42bb-8bfd-6e34d134dc62] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:46:15.033 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:15.034 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 14:46:15.034 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:15.034 - [orders(100)][c7c6efdb-e233-42bb-8bfd-6e34d134dc62] - Node c7c6efdb-e233-42bb-8bfd-6e34d134dc62[c7c6efdb-e233-42bb-8bfd-6e34d134dc62] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:46:15.041 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:46:15.061 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:46:15.062 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5de08b92 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5de08b92 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5de08b92 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:46:15.114 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:15.114 - [orders(100)][6fd5813c-7501-4396-9d6a-46db85c1df36] - Node 6fd5813c-7501-4396-9d6a-46db85c1df36[6fd5813c-7501-4396-9d6a-46db85c1df36] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:46:15.114 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:15.114 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:15.114 - [orders(100)][6fd5813c-7501-4396-9d6a-46db85c1df36] - Node 6fd5813c-7501-4396-9d6a-46db85c1df36[6fd5813c-7501-4396-9d6a-46db85c1df36] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:15.127 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:46:15.127 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:46:15.144 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:46:15.144 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5ff83b51 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5ff83b51 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5ff83b51 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:46:15.268 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:15.269 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:15.270 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:15.270 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:15.270 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:15.271 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 3 ms 
[INFO ] 2024-03-29 14:46:15.297 - [orders(100)][9daa7ff8-be74-41cb-940d-aeaa8fe0a29c] - Node 9daa7ff8-be74-41cb-940d-aeaa8fe0a29c[9daa7ff8-be74-41cb-940d-aeaa8fe0a29c] running status set to false 
[INFO ] 2024-03-29 14:46:15.298 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:15.298 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:15.298 - [orders(100)][9daa7ff8-be74-41cb-940d-aeaa8fe0a29c] - Node 9daa7ff8-be74-41cb-940d-aeaa8fe0a29c[9daa7ff8-be74-41cb-940d-aeaa8fe0a29c] schema data cleaned 
[INFO ] 2024-03-29 14:46:15.298 - [orders(100)][9daa7ff8-be74-41cb-940d-aeaa8fe0a29c] - Node 9daa7ff8-be74-41cb-940d-aeaa8fe0a29c[9daa7ff8-be74-41cb-940d-aeaa8fe0a29c] monitor closed 
[INFO ] 2024-03-29 14:46:15.298 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:15.298 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 8 ms 
[INFO ] 2024-03-29 14:46:15.300 - [orders(100)][9daa7ff8-be74-41cb-940d-aeaa8fe0a29c] - Node 9daa7ff8-be74-41cb-940d-aeaa8fe0a29c[9daa7ff8-be74-41cb-940d-aeaa8fe0a29c] close complete, cost 7 ms 
[INFO ] 2024-03-29 14:46:15.300 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-9daa7ff8-be74-41cb-940d-aeaa8fe0a29c complete, cost 2655ms 
[INFO ] 2024-03-29 14:46:15.631 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:15.635 - [orders(100)][6e4890a6-df82-40dc-a7a3-c58377fa64e6] - Node 6e4890a6-df82-40dc-a7a3-c58377fa64e6[6e4890a6-df82-40dc-a7a3-c58377fa64e6] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:46:15.635 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:15.636 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:15.636 - [orders(100)][6e4890a6-df82-40dc-a7a3-c58377fa64e6] - Node 6e4890a6-df82-40dc-a7a3-c58377fa64e6[6e4890a6-df82-40dc-a7a3-c58377fa64e6] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:15.661 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:46:15.662 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:46:15.730 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:46:15.730 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6eaefa01 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6eaefa01 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6eaefa01 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:46:15.780 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:15.780 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:15.781 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:15.781 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:15.782 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:15.782 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 7 ms 
[INFO ] 2024-03-29 14:46:15.797 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:15.798 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:15.798 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:15.798 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:15.803 - [orders(100)][8a4f5328-29b5-4700-b9d7-48a40583a957] - Node 8a4f5328-29b5-4700-b9d7-48a40583a957[8a4f5328-29b5-4700-b9d7-48a40583a957] running status set to false 
[INFO ] 2024-03-29 14:46:15.803 - [orders(100)][8a4f5328-29b5-4700-b9d7-48a40583a957] - Node 8a4f5328-29b5-4700-b9d7-48a40583a957[8a4f5328-29b5-4700-b9d7-48a40583a957] schema data cleaned 
[INFO ] 2024-03-29 14:46:15.803 - [orders(100)][8a4f5328-29b5-4700-b9d7-48a40583a957] - Node 8a4f5328-29b5-4700-b9d7-48a40583a957[8a4f5328-29b5-4700-b9d7-48a40583a957] monitor closed 
[INFO ] 2024-03-29 14:46:15.804 - [orders(100)][8a4f5328-29b5-4700-b9d7-48a40583a957] - Node 8a4f5328-29b5-4700-b9d7-48a40583a957[8a4f5328-29b5-4700-b9d7-48a40583a957] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:15.804 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-8a4f5328-29b5-4700-b9d7-48a40583a957 complete, cost 2600ms 
[INFO ] 2024-03-29 14:46:15.885 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:15.885 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:15.885 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:15.885 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:15.885 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:15.885 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 2 ms 
[INFO ] 2024-03-29 14:46:15.905 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:15.905 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:15.905 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:15.909 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:15.909 - [orders(100)][a2fc8d36-6bc3-4746-8c43-34abe552cce8] - Node a2fc8d36-6bc3-4746-8c43-34abe552cce8[a2fc8d36-6bc3-4746-8c43-34abe552cce8] running status set to false 
[INFO ] 2024-03-29 14:46:15.909 - [orders(100)][a2fc8d36-6bc3-4746-8c43-34abe552cce8] - Node a2fc8d36-6bc3-4746-8c43-34abe552cce8[a2fc8d36-6bc3-4746-8c43-34abe552cce8] schema data cleaned 
[INFO ] 2024-03-29 14:46:15.909 - [orders(100)][a2fc8d36-6bc3-4746-8c43-34abe552cce8] - Node a2fc8d36-6bc3-4746-8c43-34abe552cce8[a2fc8d36-6bc3-4746-8c43-34abe552cce8] monitor closed 
[INFO ] 2024-03-29 14:46:15.910 - [orders(100)][a2fc8d36-6bc3-4746-8c43-34abe552cce8] - Node a2fc8d36-6bc3-4746-8c43-34abe552cce8[a2fc8d36-6bc3-4746-8c43-34abe552cce8] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:15.910 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-a2fc8d36-6bc3-4746-8c43-34abe552cce8 complete, cost 2590ms 
[INFO ] 2024-03-29 14:46:16.132 - [orders(100)][558c26aa-645f-4e4a-bd01-52f494da13a2] - Node 558c26aa-645f-4e4a-bd01-52f494da13a2[558c26aa-645f-4e4a-bd01-52f494da13a2] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:46:16.132 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:16.132 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:16.133 - [orders(100)][558c26aa-645f-4e4a-bd01-52f494da13a2] - Node 558c26aa-645f-4e4a-bd01-52f494da13a2[558c26aa-645f-4e4a-bd01-52f494da13a2] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:16.133 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:16.133 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:46:16.139 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:46:16.156 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:46:16.156 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4fada4fe error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4fada4fe error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4fada4fe error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:46:16.711 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:16.711 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:16.711 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:16.711 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:16.712 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:16.737 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 2 ms 
[INFO ] 2024-03-29 14:46:16.739 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:16.740 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:16.740 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:16.740 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:16.741 - [orders(100)][1de99b22-6825-4762-b79e-8e44b1f59390] - Node 1de99b22-6825-4762-b79e-8e44b1f59390[1de99b22-6825-4762-b79e-8e44b1f59390] running status set to false 
[INFO ] 2024-03-29 14:46:16.741 - [orders(100)][1de99b22-6825-4762-b79e-8e44b1f59390] - Node 1de99b22-6825-4762-b79e-8e44b1f59390[1de99b22-6825-4762-b79e-8e44b1f59390] schema data cleaned 
[INFO ] 2024-03-29 14:46:16.741 - [orders(100)][1de99b22-6825-4762-b79e-8e44b1f59390] - Node 1de99b22-6825-4762-b79e-8e44b1f59390[1de99b22-6825-4762-b79e-8e44b1f59390] monitor closed 
[INFO ] 2024-03-29 14:46:16.741 - [orders(100)][1de99b22-6825-4762-b79e-8e44b1f59390] - Node 1de99b22-6825-4762-b79e-8e44b1f59390[1de99b22-6825-4762-b79e-8e44b1f59390] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:16.855 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-1de99b22-6825-4762-b79e-8e44b1f59390 complete, cost 2605ms 
[INFO ] 2024-03-29 14:46:16.855 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:16.855 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:16.855 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:16.855 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:16.855 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:16.874 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 2 ms 
[INFO ] 2024-03-29 14:46:16.874 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:16.874 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:16.874 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:16.874 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:16.879 - [orders(100)][805b8a3b-956b-495d-a367-ac4e9cf2c263] - Node 805b8a3b-956b-495d-a367-ac4e9cf2c263[805b8a3b-956b-495d-a367-ac4e9cf2c263] running status set to false 
[INFO ] 2024-03-29 14:46:16.879 - [orders(100)][805b8a3b-956b-495d-a367-ac4e9cf2c263] - Node 805b8a3b-956b-495d-a367-ac4e9cf2c263[805b8a3b-956b-495d-a367-ac4e9cf2c263] schema data cleaned 
[INFO ] 2024-03-29 14:46:16.879 - [orders(100)][805b8a3b-956b-495d-a367-ac4e9cf2c263] - Node 805b8a3b-956b-495d-a367-ac4e9cf2c263[805b8a3b-956b-495d-a367-ac4e9cf2c263] monitor closed 
[INFO ] 2024-03-29 14:46:16.880 - [orders(100)][805b8a3b-956b-495d-a367-ac4e9cf2c263] - Node 805b8a3b-956b-495d-a367-ac4e9cf2c263[805b8a3b-956b-495d-a367-ac4e9cf2c263] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:16.915 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-805b8a3b-956b-495d-a367-ac4e9cf2c263 complete, cost 2603ms 
[INFO ] 2024-03-29 14:46:16.915 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:16.915 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:16.915 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:16.915 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:16.915 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:16.932 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 3 ms 
[INFO ] 2024-03-29 14:46:16.932 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:16.932 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:16.932 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:16.937 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:16.937 - [orders(100)][b7c03c40-8364-437c-86bf-e56407456a77] - Node b7c03c40-8364-437c-86bf-e56407456a77[b7c03c40-8364-437c-86bf-e56407456a77] running status set to false 
[INFO ] 2024-03-29 14:46:16.937 - [orders(100)][b7c03c40-8364-437c-86bf-e56407456a77] - Node b7c03c40-8364-437c-86bf-e56407456a77[b7c03c40-8364-437c-86bf-e56407456a77] schema data cleaned 
[INFO ] 2024-03-29 14:46:16.938 - [orders(100)][b7c03c40-8364-437c-86bf-e56407456a77] - Node b7c03c40-8364-437c-86bf-e56407456a77[b7c03c40-8364-437c-86bf-e56407456a77] monitor closed 
[INFO ] 2024-03-29 14:46:16.939 - [orders(100)][b7c03c40-8364-437c-86bf-e56407456a77] - Node b7c03c40-8364-437c-86bf-e56407456a77[b7c03c40-8364-437c-86bf-e56407456a77] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:16.939 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-b7c03c40-8364-437c-86bf-e56407456a77 complete, cost 2594ms 
[INFO ] 2024-03-29 14:46:17.457 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:17.457 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:17.457 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:17.457 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:17.457 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:17.485 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 2 ms 
[INFO ] 2024-03-29 14:46:17.485 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:17.485 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:17.485 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:17.486 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:17.487 - [orders(100)][417cfd03-b5a1-4dce-81ba-6031ab4193a4] - Node 417cfd03-b5a1-4dce-81ba-6031ab4193a4[417cfd03-b5a1-4dce-81ba-6031ab4193a4] running status set to false 
[INFO ] 2024-03-29 14:46:17.487 - [orders(100)][417cfd03-b5a1-4dce-81ba-6031ab4193a4] - Node 417cfd03-b5a1-4dce-81ba-6031ab4193a4[417cfd03-b5a1-4dce-81ba-6031ab4193a4] schema data cleaned 
[INFO ] 2024-03-29 14:46:17.487 - [orders(100)][417cfd03-b5a1-4dce-81ba-6031ab4193a4] - Node 417cfd03-b5a1-4dce-81ba-6031ab4193a4[417cfd03-b5a1-4dce-81ba-6031ab4193a4] monitor closed 
[INFO ] 2024-03-29 14:46:17.488 - [orders(100)][417cfd03-b5a1-4dce-81ba-6031ab4193a4] - Node 417cfd03-b5a1-4dce-81ba-6031ab4193a4[417cfd03-b5a1-4dce-81ba-6031ab4193a4] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:17.488 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-417cfd03-b5a1-4dce-81ba-6031ab4193a4 complete, cost 2605ms 
[INFO ] 2024-03-29 14:46:17.569 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:17.569 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:17.570 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:17.570 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:17.571 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:17.571 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 6 ms 
[INFO ] 2024-03-29 14:46:17.597 - [orders(100)][c7c6efdb-e233-42bb-8bfd-6e34d134dc62] - Node c7c6efdb-e233-42bb-8bfd-6e34d134dc62[c7c6efdb-e233-42bb-8bfd-6e34d134dc62] running status set to false 
[INFO ] 2024-03-29 14:46:17.597 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:17.597 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:17.597 - [orders(100)][c7c6efdb-e233-42bb-8bfd-6e34d134dc62] - Node c7c6efdb-e233-42bb-8bfd-6e34d134dc62[c7c6efdb-e233-42bb-8bfd-6e34d134dc62] schema data cleaned 
[INFO ] 2024-03-29 14:46:17.597 - [orders(100)][c7c6efdb-e233-42bb-8bfd-6e34d134dc62] - Node c7c6efdb-e233-42bb-8bfd-6e34d134dc62[c7c6efdb-e233-42bb-8bfd-6e34d134dc62] monitor closed 
[INFO ] 2024-03-29 14:46:17.597 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:17.598 - [orders(100)][c7c6efdb-e233-42bb-8bfd-6e34d134dc62] - Node c7c6efdb-e233-42bb-8bfd-6e34d134dc62[c7c6efdb-e233-42bb-8bfd-6e34d134dc62] close complete, cost 6 ms 
[INFO ] 2024-03-29 14:46:17.598 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 6 ms 
[INFO ] 2024-03-29 14:46:17.653 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-c7c6efdb-e233-42bb-8bfd-6e34d134dc62 complete, cost 2605ms 
[INFO ] 2024-03-29 14:46:17.653 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:17.653 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:17.654 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:17.654 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:17.654 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:17.678 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 7 ms 
[INFO ] 2024-03-29 14:46:17.678 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:17.678 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:17.679 - [orders(100)][6fd5813c-7501-4396-9d6a-46db85c1df36] - Node 6fd5813c-7501-4396-9d6a-46db85c1df36[6fd5813c-7501-4396-9d6a-46db85c1df36] running status set to false 
[INFO ] 2024-03-29 14:46:17.679 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:17.679 - [orders(100)][6fd5813c-7501-4396-9d6a-46db85c1df36] - Node 6fd5813c-7501-4396-9d6a-46db85c1df36[6fd5813c-7501-4396-9d6a-46db85c1df36] schema data cleaned 
[INFO ] 2024-03-29 14:46:17.679 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 6 ms 
[INFO ] 2024-03-29 14:46:17.679 - [orders(100)][6fd5813c-7501-4396-9d6a-46db85c1df36] - Node 6fd5813c-7501-4396-9d6a-46db85c1df36[6fd5813c-7501-4396-9d6a-46db85c1df36] monitor closed 
[INFO ] 2024-03-29 14:46:17.679 - [orders(100)][6fd5813c-7501-4396-9d6a-46db85c1df36] - Node 6fd5813c-7501-4396-9d6a-46db85c1df36[6fd5813c-7501-4396-9d6a-46db85c1df36] close complete, cost 1 ms 
[INFO ] 2024-03-29 14:46:17.682 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-6fd5813c-7501-4396-9d6a-46db85c1df36 complete, cost 2603ms 
[INFO ] 2024-03-29 14:46:17.751 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:17.751 - [orders(100)][b05e0aad-988a-44d7-904e-29c3ac752eb8] - Node b05e0aad-988a-44d7-904e-29c3ac752eb8[b05e0aad-988a-44d7-904e-29c3ac752eb8] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:46:17.752 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:17.752 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 14:46:17.752 - [orders(100)][b05e0aad-988a-44d7-904e-29c3ac752eb8] - Node b05e0aad-988a-44d7-904e-29c3ac752eb8[b05e0aad-988a-44d7-904e-29c3ac752eb8] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:17.752 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:46:17.794 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:46:17.794 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:46:17.997 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3451a2c5 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3451a2c5 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3451a2c5 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:46:18.182 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:18.182 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:18.182 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:18.182 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:18.182 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:18.183 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 3 ms 
[INFO ] 2024-03-29 14:46:18.251 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:18.252 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:18.252 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:18.255 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 1 ms 
[INFO ] 2024-03-29 14:46:18.255 - [orders(100)][6e4890a6-df82-40dc-a7a3-c58377fa64e6] - Node 6e4890a6-df82-40dc-a7a3-c58377fa64e6[6e4890a6-df82-40dc-a7a3-c58377fa64e6] running status set to false 
[INFO ] 2024-03-29 14:46:18.255 - [orders(100)][6e4890a6-df82-40dc-a7a3-c58377fa64e6] - Node 6e4890a6-df82-40dc-a7a3-c58377fa64e6[6e4890a6-df82-40dc-a7a3-c58377fa64e6] schema data cleaned 
[INFO ] 2024-03-29 14:46:18.255 - [orders(100)][6e4890a6-df82-40dc-a7a3-c58377fa64e6] - Node 6e4890a6-df82-40dc-a7a3-c58377fa64e6[6e4890a6-df82-40dc-a7a3-c58377fa64e6] monitor closed 
[INFO ] 2024-03-29 14:46:18.255 - [orders(100)][6e4890a6-df82-40dc-a7a3-c58377fa64e6] - Node 6e4890a6-df82-40dc-a7a3-c58377fa64e6[6e4890a6-df82-40dc-a7a3-c58377fa64e6] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:18.459 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-6e4890a6-df82-40dc-a7a3-c58377fa64e6 complete, cost 2690ms 
[INFO ] 2024-03-29 14:46:18.659 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:18.659 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:18.660 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:18.660 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:18.660 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:18.660 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 1 ms 
[INFO ] 2024-03-29 14:46:18.688 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:18.688 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:18.688 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:18.688 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 1 ms 
[INFO ] 2024-03-29 14:46:18.688 - [orders(100)][558c26aa-645f-4e4a-bd01-52f494da13a2] - Node 558c26aa-645f-4e4a-bd01-52f494da13a2[558c26aa-645f-4e4a-bd01-52f494da13a2] running status set to false 
[INFO ] 2024-03-29 14:46:18.688 - [orders(100)][558c26aa-645f-4e4a-bd01-52f494da13a2] - Node 558c26aa-645f-4e4a-bd01-52f494da13a2[558c26aa-645f-4e4a-bd01-52f494da13a2] schema data cleaned 
[INFO ] 2024-03-29 14:46:18.688 - [orders(100)][558c26aa-645f-4e4a-bd01-52f494da13a2] - Node 558c26aa-645f-4e4a-bd01-52f494da13a2[558c26aa-645f-4e4a-bd01-52f494da13a2] monitor closed 
[INFO ] 2024-03-29 14:46:18.688 - [orders(100)][558c26aa-645f-4e4a-bd01-52f494da13a2] - Node 558c26aa-645f-4e4a-bd01-52f494da13a2[558c26aa-645f-4e4a-bd01-52f494da13a2] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:18.891 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-558c26aa-645f-4e4a-bd01-52f494da13a2 complete, cost 2588ms 
[INFO ] 2024-03-29 14:46:19.189 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:19.189 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:19.189 - [orders(100)][0ba7ca43-4060-4512-85c8-4e26e69b400a] - Node 0ba7ca43-4060-4512-85c8-4e26e69b400a[0ba7ca43-4060-4512-85c8-4e26e69b400a] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:46:19.189 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:19.189 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:19.189 - [orders(100)][0ba7ca43-4060-4512-85c8-4e26e69b400a] - Node 0ba7ca43-4060-4512-85c8-4e26e69b400a[0ba7ca43-4060-4512-85c8-4e26e69b400a] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:46:19.198 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:46:19.229 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:46:19.229 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@34ee2085 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@34ee2085 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@34ee2085 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:46:20.304 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:20.304 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:20.304 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:20.305 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:20.305 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:20.349 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 2 ms 
[INFO ] 2024-03-29 14:46:20.349 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:20.349 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:20.350 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:20.351 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:20.351 - [orders(100)][b05e0aad-988a-44d7-904e-29c3ac752eb8] - Node b05e0aad-988a-44d7-904e-29c3ac752eb8[b05e0aad-988a-44d7-904e-29c3ac752eb8] running status set to false 
[INFO ] 2024-03-29 14:46:20.351 - [orders(100)][b05e0aad-988a-44d7-904e-29c3ac752eb8] - Node b05e0aad-988a-44d7-904e-29c3ac752eb8[b05e0aad-988a-44d7-904e-29c3ac752eb8] schema data cleaned 
[INFO ] 2024-03-29 14:46:20.351 - [orders(100)][b05e0aad-988a-44d7-904e-29c3ac752eb8] - Node b05e0aad-988a-44d7-904e-29c3ac752eb8[b05e0aad-988a-44d7-904e-29c3ac752eb8] monitor closed 
[INFO ] 2024-03-29 14:46:20.351 - [orders(100)][b05e0aad-988a-44d7-904e-29c3ac752eb8] - Node b05e0aad-988a-44d7-904e-29c3ac752eb8[b05e0aad-988a-44d7-904e-29c3ac752eb8] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:20.552 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-b05e0aad-988a-44d7-904e-29c3ac752eb8 complete, cost 2725ms 
[INFO ] 2024-03-29 14:46:21.748 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:21.748 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:21.748 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:21.748 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:21.748 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:21.750 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 15 ms 
[INFO ] 2024-03-29 14:46:21.751 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:21.751 - [orders(100)][0ba7ca43-4060-4512-85c8-4e26e69b400a] - Node 0ba7ca43-4060-4512-85c8-4e26e69b400a[0ba7ca43-4060-4512-85c8-4e26e69b400a] running status set to false 
[INFO ] 2024-03-29 14:46:21.751 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:21.752 - [orders(100)][0ba7ca43-4060-4512-85c8-4e26e69b400a] - Node 0ba7ca43-4060-4512-85c8-4e26e69b400a[0ba7ca43-4060-4512-85c8-4e26e69b400a] schema data cleaned 
[INFO ] 2024-03-29 14:46:21.752 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:21.752 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 1 ms 
[INFO ] 2024-03-29 14:46:21.752 - [orders(100)][0ba7ca43-4060-4512-85c8-4e26e69b400a] - Node 0ba7ca43-4060-4512-85c8-4e26e69b400a[0ba7ca43-4060-4512-85c8-4e26e69b400a] monitor closed 
[INFO ] 2024-03-29 14:46:21.755 - [orders(100)][0ba7ca43-4060-4512-85c8-4e26e69b400a] - Node 0ba7ca43-4060-4512-85c8-4e26e69b400a[0ba7ca43-4060-4512-85c8-4e26e69b400a] close complete, cost 1 ms 
[INFO ] 2024-03-29 14:46:21.755 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-0ba7ca43-4060-4512-85c8-4e26e69b400a complete, cost 2602ms 
[INFO ] 2024-03-29 14:46:22.932 - [orders(100)][f6874a95-17e9-4bac-b743-89c4d763a6b3] - Node f6874a95-17e9-4bac-b743-89c4d763a6b3[f6874a95-17e9-4bac-b743-89c4d763a6b3] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:46:22.932 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:22.932 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:22.932 - [orders(100)][f6874a95-17e9-4bac-b743-89c4d763a6b3] - Node f6874a95-17e9-4bac-b743-89c4d763a6b3[f6874a95-17e9-4bac-b743-89c4d763a6b3] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:22.932 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:22.950 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:46:22.951 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:46:23.014 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:46:23.014 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7a8170ea error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7a8170ea error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7a8170ea error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:46:24.091 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:24.091 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 14:46:24.091 - [orders(100)][f253f405-c1e4-406e-961b-124a5e06de7b] - Node f253f405-c1e4-406e-961b-124a5e06de7b[f253f405-c1e4-406e-961b-124a5e06de7b] start preload schema,table counts: 0 
[INFO ] 2024-03-29 14:46:24.091 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:24.091 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 14:46:24.091 - [orders(100)][f253f405-c1e4-406e-961b-124a5e06de7b] - Node f253f405-c1e4-406e-961b-124a5e06de7b[f253f405-c1e4-406e-961b-124a5e06de7b] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 14:46:24.098 - [orders(100)][Order Details] - start source runner failed: null <-- Error Message -->
start source runner failed: null

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	...

<-- Full Stack Trace -->
java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at com.hazelcast.persistence.store.impl.MongoDBIMap.isEmpty(MongoDBIMap.java:261)
	at com.hazelcast.persistence.PersistenceStorage.isEmpty(PersistenceStorage.java:420)
	at io.tapdata.construct.constructImpl.ConstructIMap.isEmpty(ConstructIMap.java:128)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:95)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:188)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more

[INFO ] 2024-03-29 14:46:24.111 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 14:46:24.111 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@34babd98 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@34babd98 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@34babd98 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 14:46:25.479 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:25.479 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:25.479 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:25.479 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:25.479 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:25.479 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 1 ms 
[INFO ] 2024-03-29 14:46:25.535 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:25.535 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:25.535 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:25.535 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:25.536 - [orders(100)][f6874a95-17e9-4bac-b743-89c4d763a6b3] - Node f6874a95-17e9-4bac-b743-89c4d763a6b3[f6874a95-17e9-4bac-b743-89c4d763a6b3] running status set to false 
[INFO ] 2024-03-29 14:46:25.536 - [orders(100)][f6874a95-17e9-4bac-b743-89c4d763a6b3] - Node f6874a95-17e9-4bac-b743-89c4d763a6b3[f6874a95-17e9-4bac-b743-89c4d763a6b3] schema data cleaned 
[INFO ] 2024-03-29 14:46:25.536 - [orders(100)][f6874a95-17e9-4bac-b743-89c4d763a6b3] - Node f6874a95-17e9-4bac-b743-89c4d763a6b3[f6874a95-17e9-4bac-b743-89c4d763a6b3] monitor closed 
[INFO ] 2024-03-29 14:46:25.537 - [orders(100)][f6874a95-17e9-4bac-b743-89c4d763a6b3] - Node f6874a95-17e9-4bac-b743-89c4d763a6b3[f6874a95-17e9-4bac-b743-89c4d763a6b3] close complete, cost 0 ms 
[INFO ] 2024-03-29 14:46:25.739 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-f6874a95-17e9-4bac-b743-89c4d763a6b3 complete, cost 2654ms 
[INFO ] 2024-03-29 14:46:26.622 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] running status set to false 
[INFO ] 2024-03-29 14:46:26.623 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 14:46:26.623 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 14:46:26.623 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] schema data cleaned 
[INFO ] 2024-03-29 14:46:26.623 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] monitor closed 
[INFO ] 2024-03-29 14:46:26.625 - [orders(100)][Order Details] - Node Order Details[6f94114f-0b82-4057-acb5-5e028c893aca] close complete, cost 1 ms 
[INFO ] 2024-03-29 14:46:26.631 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] running status set to false 
[INFO ] 2024-03-29 14:46:26.631 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] schema data cleaned 
[INFO ] 2024-03-29 14:46:26.632 - [orders(100)][f253f405-c1e4-406e-961b-124a5e06de7b] - Node f253f405-c1e4-406e-961b-124a5e06de7b[f253f405-c1e4-406e-961b-124a5e06de7b] running status set to false 
[INFO ] 2024-03-29 14:46:26.632 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] monitor closed 
[INFO ] 2024-03-29 14:46:26.632 - [orders(100)][f253f405-c1e4-406e-961b-124a5e06de7b] - Node f253f405-c1e4-406e-961b-124a5e06de7b[f253f405-c1e4-406e-961b-124a5e06de7b] schema data cleaned 
[INFO ] 2024-03-29 14:46:26.633 - [orders(100)][f253f405-c1e4-406e-961b-124a5e06de7b] - Node f253f405-c1e4-406e-961b-124a5e06de7b[f253f405-c1e4-406e-961b-124a5e06de7b] monitor closed 
[INFO ] 2024-03-29 14:46:26.633 - [orders(100)][Order Details] - Node Order Details[45d7c6f6-e0f1-4220-8bfe-c03c2d5792e9] close complete, cost 5 ms 
[INFO ] 2024-03-29 14:46:26.633 - [orders(100)][f253f405-c1e4-406e-961b-124a5e06de7b] - Node f253f405-c1e4-406e-961b-124a5e06de7b[f253f405-c1e4-406e-961b-124a5e06de7b] close complete, cost 2 ms 
[INFO ] 2024-03-29 14:46:26.633 - [orders(100)] - load tapTable task 6606639673eec71f077e9901-f253f405-c1e4-406e-961b-124a5e06de7b complete, cost 2575ms 
