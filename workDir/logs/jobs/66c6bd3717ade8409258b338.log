[INFO ] 2024-08-22 12:23:50.754 - [任务 14] - Start task milestones: 66c6bd3717ade8409258b338(任务 14) 
[INFO ] 2024-08-22 12:23:50.754 - [任务 14] - Task initialization... 
[INFO ] 2024-08-22 12:23:50.775 - [任务 14] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-22 12:23:50.859 - [任务 14] - The engine receives 任务 14 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-22 12:23:50.859 - [任务 14][Mysql3306] - Node Mysql3306[ba89167b-e4ef-46f6-8cb8-557de50a4ae4] start preload schema,table counts: 3 
[INFO ] 2024-08-22 12:23:50.859 - [任务 14][DB2132WIMTEST] - Node DB2132WIMTEST[53e67033-331f-49e8-9958-fea69aaf5dc3] start preload schema,table counts: 3 
[INFO ] 2024-08-22 12:23:50.948 - [任务 14][Mysql3306] - Node Mysql3306[ba89167b-e4ef-46f6-8cb8-557de50a4ae4] preload schema finished, cost 89 ms 
[INFO ] 2024-08-22 12:23:50.948 - [任务 14][DB2132WIMTEST] - Node DB2132WIMTEST[53e67033-331f-49e8-9958-fea69aaf5dc3] preload schema finished, cost 88 ms 
[INFO ] 2024-08-22 12:23:51.724 - [任务 14][DB2132WIMTEST] - Node(DB2132WIMTEST) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-22 12:23:51.728 - [任务 14][DB2132WIMTEST] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-22 12:23:51.995 - [任务 14][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-08-22 12:23:51.996 - [任务 14][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-08-22 12:23:51.996 - [任务 14][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-22 12:23:52.016 - [任务 14][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000034","position":315631111,"gtidSet":""} 
[INFO ] 2024-08-22 12:23:52.016 - [任务 14][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-22 12:23:52.071 - [任务 14][Mysql3306] - Initial sync started 
[INFO ] 2024-08-22 12:23:52.073 - [任务 14][Mysql3306] - Starting batch read, table name: BMSQL_DISTRICT, offset: null 
[INFO ] 2024-08-22 12:23:52.073 - [任务 14][Mysql3306] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-22 12:23:52.135 - [任务 14][Mysql3306] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-22 12:23:52.135 - [任务 14][Mysql3306] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 12:23:52.135 - [任务 14][Mysql3306] - Starting batch read, table name: BMSQL_CUSTOMER, offset: null 
[INFO ] 2024-08-22 12:23:52.136 - [任务 14][Mysql3306] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-08-22 12:23:52.140 - [任务 14][Mysql3306] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 12:23:52.140 - [任务 14][Mysql3306] - Query table 'BMSQL_CUSTOMER' counts: 0 
[INFO ] 2024-08-22 12:23:52.140 - [任务 14][Mysql3306] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-08-22 12:23:52.140 - [任务 14][Mysql3306] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-22 12:23:52.143 - [任务 14][Mysql3306] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-22 12:23:52.143 - [任务 14][Mysql3306] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 12:23:52.143 - [任务 14][Mysql3306] - Initial sync completed 
[INFO ] 2024-08-22 12:23:52.144 - [任务 14][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-08-22 12:23:52.144 - [任务 14][Mysql3306] - Initial sync completed 
[INFO ] 2024-08-22 12:23:52.188 - [任务 14][Mysql3306] - Starting stream read, table list: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], offset: {"filename":"binlog.000034","position":315631111,"gtidSet":""} 
[INFO ] 2024-08-22 12:23:52.189 - [任务 14][Mysql3306] - Starting mysql cdc, server name: 9225a6af-4453-44b0-a732-5ee974abd4a4 
[INFO ] 2024-08-22 12:23:52.399 - [任务 14][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 326794258
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9225a6af-4453-44b0-a732-5ee974abd4a4
  database.port: 3306
  threadName: Debezium-Mysql-Connector-9225a6af-4453-44b0-a732-5ee974abd4a4
  database.hostname: localhost
  database.password: ********
  name: 9225a6af-4453-44b0-a732-5ee974abd4a4
  pdk.offset.string: {"name":"9225a6af-4453-44b0-a732-5ee974abd4a4","offset":{"{\"server\":\"9225a6af-4453-44b0-a732-5ee974abd4a4\"}":"{\"file\":\"binlog.000034\",\"pos\":315631111,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-22 12:23:52.510 - [任务 14][Mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-22 12:24:09.295 - [任务 14] - Stop task milestones: 66c6bd3717ade8409258b338(任务 14)  
[INFO ] 2024-08-22 12:24:09.320 - [任务 14][Mysql3306] - Node Mysql3306[ba89167b-e4ef-46f6-8cb8-557de50a4ae4] running status set to false 
[INFO ] 2024-08-22 12:24:09.424 - [任务 14][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-22 12:24:09.426 - [任务 14][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-08-22 12:24:09.436 - [任务 14][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-ba89167b-e4ef-46f6-8cb8-557de50a4ae4 
[INFO ] 2024-08-22 12:24:09.436 - [任务 14][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-ba89167b-e4ef-46f6-8cb8-557de50a4ae4 
[INFO ] 2024-08-22 12:24:09.436 - [任务 14][Mysql3306] - Node Mysql3306[ba89167b-e4ef-46f6-8cb8-557de50a4ae4] schema data cleaned 
[INFO ] 2024-08-22 12:24:09.436 - [任务 14][Mysql3306] - Node Mysql3306[ba89167b-e4ef-46f6-8cb8-557de50a4ae4] monitor closed 
[INFO ] 2024-08-22 12:24:09.436 - [任务 14][Mysql3306] - Node Mysql3306[ba89167b-e4ef-46f6-8cb8-557de50a4ae4] close complete, cost 119 ms 
[INFO ] 2024-08-22 12:24:09.461 - [任务 14][DB2132WIMTEST] - Node DB2132WIMTEST[53e67033-331f-49e8-9958-fea69aaf5dc3] running status set to false 
[INFO ] 2024-08-22 12:24:09.462 - [任务 14][DB2132WIMTEST] - PDK connector node stopped: HazelcastTargetPdkDataNode-53e67033-331f-49e8-9958-fea69aaf5dc3 
[INFO ] 2024-08-22 12:24:09.463 - [任务 14][DB2132WIMTEST] - PDK connector node released: HazelcastTargetPdkDataNode-53e67033-331f-49e8-9958-fea69aaf5dc3 
[INFO ] 2024-08-22 12:24:09.463 - [任务 14][DB2132WIMTEST] - Node DB2132WIMTEST[53e67033-331f-49e8-9958-fea69aaf5dc3] schema data cleaned 
[INFO ] 2024-08-22 12:24:09.463 - [任务 14][DB2132WIMTEST] - Node DB2132WIMTEST[53e67033-331f-49e8-9958-fea69aaf5dc3] monitor closed 
[INFO ] 2024-08-22 12:24:09.464 - [任务 14][DB2132WIMTEST] - Node DB2132WIMTEST[53e67033-331f-49e8-9958-fea69aaf5dc3] close complete, cost 26 ms 
[INFO ] 2024-08-22 12:24:09.886 - [任务 14] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-22 12:24:09.887 - [任务 14] - Stopped task aspect(s) 
[INFO ] 2024-08-22 12:24:09.887 - [任务 14] - Snapshot order controller have been removed 
[INFO ] 2024-08-22 12:24:09.931 - [任务 14] - Remove memory task client succeed, task: 任务 14[66c6bd3717ade8409258b338] 
[INFO ] 2024-08-22 12:24:09.940 - [任务 14] - Destroy memory task client cache succeed, task: 任务 14[66c6bd3717ade8409258b338] 
[INFO ] 2024-08-22 14:10:51.480 - [MYSQL-DB2 MOCK DATA] - Start task milestones: 66c6bd3717ade8409258b338(MYSQL-DB2 MOCK DATA) 
[INFO ] 2024-08-22 14:10:51.481 - [MYSQL-DB2 MOCK DATA] - Task initialization... 
[INFO ] 2024-08-22 14:10:51.579 - [MYSQL-DB2 MOCK DATA] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-22 14:10:51.580 - [MYSQL-DB2 MOCK DATA] - The engine receives MYSQL-DB2 MOCK DATA task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-22 14:10:51.638 - [MYSQL-DB2 MOCK DATA][DB2132WIMTEST] - Node DB2132WIMTEST[53e67033-331f-49e8-9958-fea69aaf5dc3] start preload schema,table counts: 3 
[INFO ] 2024-08-22 14:10:51.639 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Node Mysql3306[ba89167b-e4ef-46f6-8cb8-557de50a4ae4] start preload schema,table counts: 3 
[INFO ] 2024-08-22 14:10:51.720 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Node Mysql3306[ba89167b-e4ef-46f6-8cb8-557de50a4ae4] preload schema finished, cost 71 ms 
[INFO ] 2024-08-22 14:10:51.720 - [MYSQL-DB2 MOCK DATA][DB2132WIMTEST] - Node DB2132WIMTEST[53e67033-331f-49e8-9958-fea69aaf5dc3] preload schema finished, cost 71 ms 
[INFO ] 2024-08-22 14:10:52.555 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-08-22 14:10:52.555 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-08-22 14:10:52.555 - [MYSQL-DB2 MOCK DATA][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-22 14:10:52.559 - [MYSQL-DB2 MOCK DATA][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000034","position":315631111,"gtidSet":""} 
[INFO ] 2024-08-22 14:10:52.560 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-22 14:10:52.564 - [MYSQL-DB2 MOCK DATA][DB2132WIMTEST] - Node(DB2132WIMTEST) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-08-22 14:10:52.564 - [MYSQL-DB2 MOCK DATA][DB2132WIMTEST] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-22 14:10:52.607 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Initial sync started 
[INFO ] 2024-08-22 14:10:52.607 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Starting batch read, table name: BMSQL_DISTRICT, offset: null 
[INFO ] 2024-08-22 14:10:52.636 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Table BMSQL_DISTRICT is going to be initial synced 
[INFO ] 2024-08-22 14:10:52.640 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Table [BMSQL_DISTRICT] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 14:10:52.644 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Query table 'BMSQL_DISTRICT' counts: 100 
[INFO ] 2024-08-22 14:10:52.644 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Starting batch read, table name: BMSQL_CUSTOMER, offset: null 
[INFO ] 2024-08-22 14:10:52.658 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-08-22 14:10:52.659 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 14:10:52.659 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Query table 'BMSQL_CUSTOMER' counts: 0 
[INFO ] 2024-08-22 14:10:52.659 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Starting batch read, table name: BMSQL_CONFIG, offset: null 
[INFO ] 2024-08-22 14:10:52.662 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Table BMSQL_CONFIG is going to be initial synced 
[INFO ] 2024-08-22 14:10:52.662 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Query table 'BMSQL_CONFIG' counts: 6 
[INFO ] 2024-08-22 14:10:52.664 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Table [BMSQL_CONFIG] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-22 14:10:52.664 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Initial sync completed 
[INFO ] 2024-08-22 14:10:52.664 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-08-22 14:10:52.664 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Initial sync completed 
[INFO ] 2024-08-22 14:10:52.684 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Starting stream read, table list: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], offset: {"filename":"binlog.000034","position":315631111,"gtidSet":""} 
[INFO ] 2024-08-22 14:10:52.685 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Starting mysql cdc, server name: d9c42316-5b62-45e9-a05c-f9f8df94731f 
[INFO ] 2024-08-22 14:10:52.735 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1574689432
  time.precision.mode: adaptive_time_microseconds
  database.server.name: d9c42316-5b62-45e9-a05c-f9f8df94731f
  database.port: 3306
  threadName: Debezium-Mysql-Connector-d9c42316-5b62-45e9-a05c-f9f8df94731f
  database.hostname: localhost
  database.password: ********
  name: d9c42316-5b62-45e9-a05c-f9f8df94731f
  pdk.offset.string: {"name":"d9c42316-5b62-45e9-a05c-f9f8df94731f","offset":{"{\"server\":\"d9c42316-5b62-45e9-a05c-f9f8df94731f\"}":"{\"file\":\"binlog.000034\",\"pos\":315631111,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_DISTRICT,test.BMSQL_CUSTOMER,test.BMSQL_CONFIG
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-22 14:10:52.735 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_DISTRICT, BMSQL_CUSTOMER, BMSQL_CONFIG], data change syncing 
[INFO ] 2024-08-22 14:45:25.967 - [MYSQL-DB2 MOCK DATA] - Stop task milestones: 66c6bd3717ade8409258b338(MYSQL-DB2 MOCK DATA)  
[INFO ] 2024-08-22 14:45:26.487 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Node Mysql3306[ba89167b-e4ef-46f6-8cb8-557de50a4ae4] running status set to false 
[INFO ] 2024-08-22 14:45:26.497 - [MYSQL-DB2 MOCK DATA][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-22 14:45:26.499 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-08-22 14:45:26.504 - [MYSQL-DB2 MOCK DATA][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-ba89167b-e4ef-46f6-8cb8-557de50a4ae4 
[INFO ] 2024-08-22 14:45:26.505 - [MYSQL-DB2 MOCK DATA][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-ba89167b-e4ef-46f6-8cb8-557de50a4ae4 
[INFO ] 2024-08-22 14:45:26.505 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Node Mysql3306[ba89167b-e4ef-46f6-8cb8-557de50a4ae4] schema data cleaned 
[INFO ] 2024-08-22 14:45:26.505 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Node Mysql3306[ba89167b-e4ef-46f6-8cb8-557de50a4ae4] monitor closed 
[INFO ] 2024-08-22 14:45:26.505 - [MYSQL-DB2 MOCK DATA][Mysql3306] - Node Mysql3306[ba89167b-e4ef-46f6-8cb8-557de50a4ae4] close complete, cost 127 ms 
[INFO ] 2024-08-22 14:45:26.505 - [MYSQL-DB2 MOCK DATA][DB2132WIMTEST] - Node DB2132WIMTEST[53e67033-331f-49e8-9958-fea69aaf5dc3] running status set to false 
[INFO ] 2024-08-22 14:45:26.531 - [MYSQL-DB2 MOCK DATA][DB2132WIMTEST] - PDK connector node stopped: HazelcastTargetPdkDataNode-53e67033-331f-49e8-9958-fea69aaf5dc3 
[INFO ] 2024-08-22 14:45:26.531 - [MYSQL-DB2 MOCK DATA][DB2132WIMTEST] - PDK connector node released: HazelcastTargetPdkDataNode-53e67033-331f-49e8-9958-fea69aaf5dc3 
[INFO ] 2024-08-22 14:45:26.531 - [MYSQL-DB2 MOCK DATA][DB2132WIMTEST] - Node DB2132WIMTEST[53e67033-331f-49e8-9958-fea69aaf5dc3] schema data cleaned 
[INFO ] 2024-08-22 14:45:26.531 - [MYSQL-DB2 MOCK DATA][DB2132WIMTEST] - Node DB2132WIMTEST[53e67033-331f-49e8-9958-fea69aaf5dc3] monitor closed 
[INFO ] 2024-08-22 14:45:26.735 - [MYSQL-DB2 MOCK DATA][DB2132WIMTEST] - Node DB2132WIMTEST[53e67033-331f-49e8-9958-fea69aaf5dc3] close complete, cost 26 ms 
[INFO ] 2024-08-22 14:45:27.610 - [MYSQL-DB2 MOCK DATA] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-22 14:45:27.610 - [MYSQL-DB2 MOCK DATA] - Stopped task aspect(s) 
[INFO ] 2024-08-22 14:45:27.610 - [MYSQL-DB2 MOCK DATA] - Snapshot order controller have been removed 
[INFO ] 2024-08-22 14:45:27.626 - [MYSQL-DB2 MOCK DATA] - Remove memory task client succeed, task: MYSQL-DB2 MOCK DATA[66c6bd3717ade8409258b338] 
[INFO ] 2024-08-22 14:45:27.626 - [MYSQL-DB2 MOCK DATA] - Destroy memory task client cache succeed, task: MYSQL-DB2 MOCK DATA[66c6bd3717ade8409258b338] 
