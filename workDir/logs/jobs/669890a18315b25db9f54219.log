[INFO ] 2024-07-18 11:49:00.843 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502] - Task initialization... 
[INFO ] 2024-07-18 11:49:00.889 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502] - Start task milestones: 669890a18315b25db9f54219(t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502) 
[INFO ] 2024-07-18 11:49:01.070 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502] - No<PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-18 11:49:01.240 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502] - The engine receives t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 11:49:01.288 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - Node qa_mongodb_repl_36230_1717403468657_3537[e2b0df07-5c69-43ac-8040-513ed5eb1360] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:49:01.288 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[bba45ff6-e8ff-49fa-9efc-0ee29a2a69fc] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:49:01.288 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - Node qa_mongodb_repl_36230_1717403468657_3537[e2b0df07-5c69-43ac-8040-513ed5eb1360] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:49:01.289 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[bba45ff6-e8ff-49fa-9efc-0ee29a2a69fc] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:49:01.851 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_6040_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 11:49:01.851 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - Source node "qa_mongodb_repl_36230_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 11:49:01.851 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - Source node "qa_mongodb_repl_36230_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 11:49:01.851 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 11:49:02.258 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - batch offset found: {},stream offset found: {"cdcOffset":{"rs0":{"seconds":1721274541,"inc":0}},"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 11:49:02.343 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 11:49:02.343 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - Starting batch read, table name: T413, offset: null 
[INFO ] 2024-07-18 11:49:02.364 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - Table T413 is going to be initial synced 
[INFO ] 2024-07-18 11:49:02.364 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - Table [T413] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:49:02.371 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - Query table 'T413' counts: 1 
[INFO ] 2024-07-18 11:49:02.371 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:49:02.371 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 11:49:02.371 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:49:02.574 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - Starting stream read, table list: [T413, _tapdata_heartbeat_table], offset: {"cdcOffset":{"rs0":{"seconds":1721274541,"inc":0}},"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-18 11:49:02.775 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - Connector MongoDB incremental start succeed, tables: [T413, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 11:51:09.589 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - Node qa_mongodb_repl_36230_1717403468657_3537[e2b0df07-5c69-43ac-8040-513ed5eb1360] running status set to false 
[INFO ] 2024-07-18 11:51:09.590 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 11:51:10.131 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-e2b0df07-5c69-43ac-8040-513ed5eb1360 
[INFO ] 2024-07-18 11:51:10.131 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-e2b0df07-5c69-43ac-8040-513ed5eb1360 
[INFO ] 2024-07-18 11:51:10.131 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - Node qa_mongodb_repl_36230_1717403468657_3537[e2b0df07-5c69-43ac-8040-513ed5eb1360] schema data cleaned 
[INFO ] 2024-07-18 11:51:10.131 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - Node qa_mongodb_repl_36230_1717403468657_3537[e2b0df07-5c69-43ac-8040-513ed5eb1360] monitor closed 
[INFO ] 2024-07-18 11:51:10.133 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_36230_1717403468657_3537] - Node qa_mongodb_repl_36230_1717403468657_3537[e2b0df07-5c69-43ac-8040-513ed5eb1360] close complete, cost 565 ms 
[INFO ] 2024-07-18 11:51:10.133 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[bba45ff6-e8ff-49fa-9efc-0ee29a2a69fc] running status set to false 
[INFO ] 2024-07-18 11:51:10.172 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-bba45ff6-e8ff-49fa-9efc-0ee29a2a69fc 
[INFO ] 2024-07-18 11:51:10.172 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_6040_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-bba45ff6-e8ff-49fa-9efc-0ee29a2a69fc 
[INFO ] 2024-07-18 11:51:10.172 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[bba45ff6-e8ff-49fa-9efc-0ee29a2a69fc] schema data cleaned 
[INFO ] 2024-07-18 11:51:10.172 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[bba45ff6-e8ff-49fa-9efc-0ee29a2a69fc] monitor closed 
[INFO ] 2024-07-18 11:51:10.375 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502][qa_mongodb_repl_6040_1717403468657_3537] - Node qa_mongodb_repl_6040_1717403468657_3537[bba45ff6-e8ff-49fa-9efc-0ee29a2a69fc] close complete, cost 39 ms 
[INFO ] 2024-07-18 11:51:10.951 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 11:51:11.071 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@507edfe4 
[INFO ] 2024-07-18 11:51:11.072 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502] - Stop task milestones: 669890a18315b25db9f54219(t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502)  
[INFO ] 2024-07-18 11:51:11.099 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502] - Stopped task aspect(s) 
[INFO ] 2024-07-18 11:51:11.099 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 11:51:11.158 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502] - Remove memory task client succeed, task: t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502[669890a18315b25db9f54219] 
[INFO ] 2024-07-18 11:51:11.158 - [t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502] - Destroy memory task client cache succeed, task: t_4.1.3-mdb-v3.6.23_to_mdb_with_check_data_1717403468657_3537-1721274502[669890a18315b25db9f54219] 
