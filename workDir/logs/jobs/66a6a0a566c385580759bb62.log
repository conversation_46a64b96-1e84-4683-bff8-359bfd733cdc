[INFO ] 2024-07-29 03:49:43.263 - [测试CKUINT8~64] - Task initialization... 
[INFO ] 2024-07-29 03:49:43.410 - [测试CKUINT8~64] - Start task milestones: 66a6a0a566c385580759bb62(测试CKUINT8~64) 
[INFO ] 2024-07-29 03:49:43.410 - [测试CKUINT8~64] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-29 03:49:43.550 - [测试CKUINT8~64] - The engine receives 测试CKUINT8~64 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-29 03:49:43.550 - [测试CKUINT8~64][TUINT] - Node TUINT[db9af648-b403-435d-a2d4-1faab616ffbb] start preload schema,table counts: 1 
[INFO ] 2024-07-29 03:49:43.550 - [测试CKUINT8~64][testUInt] - Node testUInt[beafd214-3a6e-4ffc-ba5b-06ab45436beb] start preload schema,table counts: 1 
[INFO ] 2024-07-29 03:49:43.551 - [测试CKUINT8~64][TUINT] - Node TUINT[db9af648-b403-435d-a2d4-1faab616ffbb] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 03:49:43.551 - [测试CKUINT8~64][testUInt] - Node testUInt[beafd214-3a6e-4ffc-ba5b-06ab45436beb] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 03:49:44.247 - [测试CKUINT8~64][testUInt] - Source node "testUInt" read batch size: 100 
[INFO ] 2024-07-29 03:49:44.248 - [测试CKUINT8~64][testUInt] - Source node "testUInt" event queue capacity: 200 
[INFO ] 2024-07-29 03:49:44.248 - [测试CKUINT8~64][testUInt] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-29 03:49:44.248 - [测试CKUINT8~64][testUInt] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-29 03:49:44.248 - [测试CKUINT8~64][testUInt] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-29 03:49:44.334 - [测试CKUINT8~64][testUInt] - Initial sync started 
[INFO ] 2024-07-29 03:49:44.341 - [测试CKUINT8~64][testUInt] - Starting batch read, table name: testUInt, offset: null 
[INFO ] 2024-07-29 03:49:44.343 - [测试CKUINT8~64][testUInt] - Table testUInt is going to be initial synced 
[INFO ] 2024-07-29 03:49:44.353 - [测试CKUINT8~64][testUInt] - Table [testUInt] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-29 03:49:44.365 - [测试CKUINT8~64][testUInt] - Query table 'testUInt' counts: 1 
[INFO ] 2024-07-29 03:49:44.365 - [测试CKUINT8~64][testUInt] - Initial sync completed 
[INFO ] 2024-07-29 03:49:44.568 - [测试CKUINT8~64][TUINT] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-29 03:49:45.403 - [测试CKUINT8~64][testUInt] - Node testUInt[beafd214-3a6e-4ffc-ba5b-06ab45436beb] running status set to false 
[INFO ] 2024-07-29 03:49:45.405 - [测试CKUINT8~64][TUINT] - Node TUINT[db9af648-b403-435d-a2d4-1faab616ffbb] running status set to false 
[INFO ] 2024-07-29 03:49:45.429 - [测试CKUINT8~64][testUInt] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-29 03:49:45.430 - [测试CKUINT8~64][TUINT] - PDK connector node stopped: HazelcastTargetPdkDataNode-db9af648-b403-435d-a2d4-1faab616ffbb 
[INFO ] 2024-07-29 03:49:45.430 - [测试CKUINT8~64][TUINT] - PDK connector node released: HazelcastTargetPdkDataNode-db9af648-b403-435d-a2d4-1faab616ffbb 
[INFO ] 2024-07-29 03:49:45.430 - [测试CKUINT8~64][TUINT] - Node TUINT[db9af648-b403-435d-a2d4-1faab616ffbb] schema data cleaned 
[INFO ] 2024-07-29 03:49:45.430 - [测试CKUINT8~64][TUINT] - Node TUINT[db9af648-b403-435d-a2d4-1faab616ffbb] monitor closed 
[INFO ] 2024-07-29 03:49:45.431 - [测试CKUINT8~64][TUINT] - Node TUINT[db9af648-b403-435d-a2d4-1faab616ffbb] close complete, cost 40 ms 
[INFO ] 2024-07-29 03:49:45.449 - [测试CKUINT8~64][testUInt] - Clickhouse Optimize Table end 
[INFO ] 2024-07-29 03:49:45.450 - [测试CKUINT8~64][testUInt] - PDK connector node stopped: HazelcastSourcePdkDataNode-beafd214-3a6e-4ffc-ba5b-06ab45436beb 
[INFO ] 2024-07-29 03:49:45.450 - [测试CKUINT8~64][testUInt] - PDK connector node released: HazelcastSourcePdkDataNode-beafd214-3a6e-4ffc-ba5b-06ab45436beb 
[INFO ] 2024-07-29 03:49:45.450 - [测试CKUINT8~64][testUInt] - Node testUInt[beafd214-3a6e-4ffc-ba5b-06ab45436beb] schema data cleaned 
[INFO ] 2024-07-29 03:49:45.451 - [测试CKUINT8~64][testUInt] - Node testUInt[beafd214-3a6e-4ffc-ba5b-06ab45436beb] monitor closed 
[INFO ] 2024-07-29 03:49:45.452 - [测试CKUINT8~64][testUInt] - Node testUInt[beafd214-3a6e-4ffc-ba5b-06ab45436beb] close complete, cost 67 ms 
[INFO ] 2024-07-29 03:49:49.562 - [测试CKUINT8~64] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-29 03:49:49.563 - [测试CKUINT8~64] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5828f377 
[INFO ] 2024-07-29 03:49:49.696 - [测试CKUINT8~64] - Stop task milestones: 66a6a0a566c385580759bb62(测试CKUINT8~64)  
[INFO ] 2024-07-29 03:49:49.696 - [测试CKUINT8~64] - Stopped task aspect(s) 
[INFO ] 2024-07-29 03:49:49.715 - [测试CKUINT8~64] - Snapshot order controller have been removed 
[INFO ] 2024-07-29 03:49:49.717 - [测试CKUINT8~64] - Remove memory task client succeed, task: 测试CKUINT8~64[66a6a0a566c385580759bb62] 
[INFO ] 2024-07-29 03:49:49.717 - [测试CKUINT8~64] - Destroy memory task client cache succeed, task: 测试CKUINT8~64[66a6a0a566c385580759bb62] 
