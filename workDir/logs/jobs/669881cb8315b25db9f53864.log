[INFO ] 2024-07-18 10:45:43.004 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730] - Task initialization... 
[INFO ] 2024-07-18 10:45:43.207 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730] - Start task milestones: 669881cb8315b25db9f53864(t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730) 
[INFO ] 2024-07-18 10:45:43.614 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 10:45:43.687 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730] - The engine receives t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 10:45:43.687 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[91d20052-e8b3-4097-aa0b-c5ead5e9e2a8] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:45:43.687 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[5c2e91c8-e805-4ec6-8a5d-6bf5856446e0] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:45:43.687 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[5c2e91c8-e805-4ec6-8a5d-6bf5856446e0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 10:45:43.889 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[91d20052-e8b3-4097-aa0b-c5ead5e9e2a8] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 10:45:44.197 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mongodb_repl_42240_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 10:45:44.238 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Source node "qa_mock_100w_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 10:45:44.239 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Source node "qa_mock_100w_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 10:45:44.239 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 10:45:44.446 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721270744240,"lastTimes":1721270744240,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-18 10:45:44.483 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 10:45:44.483 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Starting batch read, table name: mock_100w, offset: null 
[INFO ] 2024-07-18 10:45:44.491 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Table mock_100w is going to be initial synced 
[INFO ] 2024-07-18 10:45:44.516 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Start mock_100w batch read 
[INFO ] 2024-07-18 10:45:44.516 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Query table 'mock_100w' counts: 1000000 
[INFO ] 2024-07-18 10:47:19.415 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Compile mock_100w batch read 
[INFO ] 2024-07-18 10:47:19.415 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Table [mock_100w] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 10:47:19.417 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 10:47:19.417 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 10:47:19.417 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 10:47:19.425 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Starting stream read, table list: [mock_100w], offset: {"syncStage":null,"beginTimes":1721270744240,"lastTimes":1721270744240,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-18 10:47:19.431 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Start [mock_100w] stream read 
[INFO ] 2024-07-18 10:47:19.431 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Connector Dummy incremental start succeed, tables: [mock_100w], data change syncing 
[INFO ] 2024-07-18 12:33:33.910 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[91d20052-e8b3-4097-aa0b-c5ead5e9e2a8] running status set to false 
[INFO ] 2024-07-18 12:33:33.911 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Compile [mock_100w] batch read 
[INFO ] 2024-07-18 12:33:33.911 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Stop connector 
[INFO ] 2024-07-18 12:33:33.912 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 12:33:33.927 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-91d20052-e8b3-4097-aa0b-c5ead5e9e2a8 
[INFO ] 2024-07-18 12:33:33.928 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-91d20052-e8b3-4097-aa0b-c5ead5e9e2a8 
[INFO ] 2024-07-18 12:33:33.928 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[91d20052-e8b3-4097-aa0b-c5ead5e9e2a8] schema data cleaned 
[INFO ] 2024-07-18 12:33:33.928 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[91d20052-e8b3-4097-aa0b-c5ead5e9e2a8] monitor closed 
[INFO ] 2024-07-18 12:33:33.929 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mock_100w_1717403468657_3537] - Node qa_mock_100w_1717403468657_3537[91d20052-e8b3-4097-aa0b-c5ead5e9e2a8] close complete, cost 42 ms 
[INFO ] 2024-07-18 12:33:33.948 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[5c2e91c8-e805-4ec6-8a5d-6bf5856446e0] running status set to false 
[INFO ] 2024-07-18 12:33:33.948 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-5c2e91c8-e805-4ec6-8a5d-6bf5856446e0 
[INFO ] 2024-07-18 12:33:33.948 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mongodb_repl_42240_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-5c2e91c8-e805-4ec6-8a5d-6bf5856446e0 
[INFO ] 2024-07-18 12:33:33.949 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[5c2e91c8-e805-4ec6-8a5d-6bf5856446e0] schema data cleaned 
[INFO ] 2024-07-18 12:33:33.949 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[5c2e91c8-e805-4ec6-8a5d-6bf5856446e0] monitor closed 
[INFO ] 2024-07-18 12:33:33.949 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mongodb_repl_42240_1717403468657_3537] - Node qa_mongodb_repl_42240_1717403468657_3537[5c2e91c8-e805-4ec6-8a5d-6bf5856446e0] close complete, cost 20 ms 
[INFO ] 2024-07-18 12:33:36.298 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 12:33:36.298 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1c4e1e75 
[INFO ] 2024-07-18 12:33:36.438 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730] - Stop task milestones: 669881cb8315b25db9f53864(t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730)  
[INFO ] 2024-07-18 12:33:36.438 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730] - Stopped task aspect(s) 
[INFO ] 2024-07-18 12:33:36.492 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 12:33:36.493 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730] - Remove memory task client succeed, task: t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730[669881cb8315b25db9f53864] 
[INFO ] 2024-07-18 12:33:36.493 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730] - Destroy memory task client cache succeed, task: t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730[669881cb8315b25db9f53864] 
[INFO ] 2024-07-18 12:33:46.511 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mongodb_repl_42240_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: sink_100w
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@2c47e065: {"after":{"rstring_3":"W6X0CAB4","rstring_2":"W6X0CAB4","rstring_1":"W6X0CAB4","created":1721273700191,"rstring_9":"W6X0CAB4","rstring_8":"W6X0CAB4","title":"W6X0CAB4","rstring_7":"W6X0CAB4","rint_1":1521.7499,"rstring_6":"W6X0CAB4","rstring_5":"W6X0CAB4","rstring_4":"W6X0CAB4","rint_3":1521.7499,"rint_2":1521.7499,"rstring_10":"W6X0CAB4","id":"a525ee03-216c-4639-aac6-c6fef7e86100"},"before":{"rstring_3":"W6X0CAB4","rstring_2":"W6X0CAB4","rstring_1":"W6X0CAB4","created":1721273700191,"rstring_9":"W6X0CAB4","rstring_8":"W6X0CAB4","title":"W6X0CAB4","rstring_7":"W6X0CAB4","rint_1":1521.7499,"rstring_6":"W6X0CAB4","rstring_5":"W6X0CAB4","rstring_4":"W6X0CAB4","rint_3":1521.7499,"rint_2":1521.7499,"rstring_10":"W6X0CAB4","id":"a525ee03-216c-4639-aac6-c6fef7e86100"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721273700191,"tableId":"mock_100w","time":1721273700191,"type":302}, nodeIds=[91d20052-e8b3-4097-aa0b-c5ead5e9e2a8], sourceTime=1721273700191, sourceSerialNo=null} 
[ERROR] 2024-07-18 12:33:46.511 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mongodb_repl_42240_1717403468657_3537] - target write record(s) failed <-- Full Stack Trace -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: sink_100w
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:173)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:123)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: sink_100w
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:632)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:189)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:167)
	... 7 more
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: sink_100w
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:798)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:498)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:498)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:624)
	... 9 more
Caused by: Execute PDK method: TARGET_WRITE_RECORD, tableName: sink_100w
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:858)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:804)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 16 more
Caused by: java.lang.InterruptedException
	at io.tapdata.mongodb.MongodbExceptionCollector.collectUserPwdInvalid(MongodbExceptionCollector.java:70)
	at io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1305)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:852)
	... 25 more
Caused by: java.lang.InterruptedException
	at java.util.concurrent.locks.AbstractQueuedSynchronizer.acquireInterruptibly(AbstractQueuedSynchronizer.java:1223)
	at java.util.concurrent.locks.ReentrantLock.lockInterruptibly(ReentrantLock.java:340)
	at com.mongodb.internal.Locks.checkedWithLock(Locks.java:41)
	at com.mongodb.internal.Locks.withLock(Locks.java:36)
	at com.mongodb.internal.Locks.withLock(Locks.java:29)
	at com.mongodb.internal.connection.BaseCluster.withLock(BaseCluster.java:275)
	at com.mongodb.internal.connection.SingleServerCluster.withLock(SingleServerCluster.java:46)
	at com.mongodb.internal.connection.DefaultSdamServerDescriptionManager.handleExceptionAfterHandshake(DefaultSdamServerDescriptionManager.java:92)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:206)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:122)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:87)
	at com.mongodb.internal.connection.DefaultServer$OperationCountTrackingConnection.command(DefaultServer.java:297)
	at com.mongodb.internal.operation.MixedBulkWriteOperation.executeCommand(MixedBulkWriteOperation.java:393)
	at com.mongodb.internal.operation.MixedBulkWriteOperation.executeBulkWriteBatch(MixedBulkWriteOperation.java:257)
	at com.mongodb.internal.operation.MixedBulkWriteOperation.lambda$execute$0(MixedBulkWriteOperation.java:198)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$0(OperationHelper.java:358)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$1(OperationHelper.java:357)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.withSourceAndConnection(OperationHelper.java:356)
	at com.mongodb.internal.operation.MixedBulkWriteOperation.lambda$execute$1(MixedBulkWriteOperation.java:181)
	at com.mongodb.internal.async.function.RetryingSyncSupplier.get(RetryingSyncSupplier.java:67)
	at com.mongodb.internal.operation.MixedBulkWriteOperation.execute(MixedBulkWriteOperation.java:202)
	at com.mongodb.internal.operation.MixedBulkWriteOperation.execute(MixedBulkWriteOperation.java:76)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:206)
	at com.mongodb.client.internal.MongoCollectionImpl.executeBulkWrite(MongoCollectionImpl.java:447)
	at com.mongodb.client.internal.MongoCollectionImpl.bulkWrite(MongoCollectionImpl.java:428)
	at io.tapdata.mongodb.writer.MongodbWriter.writeRecord(MongodbWriter.java:117)
	at io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1302)
	... 26 more

[INFO ] 2024-07-18 12:33:53.505 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mongodb_repl_42240_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: sink_100w
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@8474d28: {"after":{"rstring_3":"W6X0CAB4","rstring_2":"W6X0CAB4","rstring_1":"W6X0CAB4","created":1721274735079,"rstring_9":"W6X0CAB4","rstring_8":"W6X0CAB4","title":"W6X0CAB4","rstring_7":"W6X0CAB4","rint_1":1521.7499,"rstring_6":"W6X0CAB4","rstring_5":"W6X0CAB4","rstring_4":"W6X0CAB4","rint_3":1521.7499,"rint_2":1521.7499,"rstring_10":"W6X0CAB4","id":"dd8eea58-9dba-4f25-9a41-fc0685201577"},"before":{"rstring_3":"W6X0CAB4","rstring_2":"W6X0CAB4","rstring_1":"W6X0CAB4","created":1721274735079,"rstring_9":"W6X0CAB4","rstring_8":"W6X0CAB4","title":"W6X0CAB4","rstring_7":"W6X0CAB4","rint_1":1521.7499,"rstring_6":"W6X0CAB4","rstring_5":"W6X0CAB4","rstring_4":"W6X0CAB4","rint_3":1521.7499,"rint_2":1521.7499,"rstring_10":"W6X0CAB4","id":"dd8eea58-9dba-4f25-9a41-fc0685201577"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721274735079,"tableId":"mock_100w","time":1721274735079,"type":302}, nodeIds=[91d20052-e8b3-4097-aa0b-c5ead5e9e2a8], sourceTime=1721274735079, sourceSerialNo=null} 
[INFO ] 2024-07-18 12:33:54.449 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mongodb_repl_42240_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: sink_100w
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@2aa6f4b3: {"after":{"rstring_3":"W6X0CAB4","rstring_2":"W6X0CAB4","rstring_1":"W6X0CAB4","created":1721274735083,"rstring_9":"W6X0CAB4","rstring_8":"W6X0CAB4","title":"W6X0CAB4","rstring_7":"W6X0CAB4","rint_1":1521.7499,"rstring_6":"W6X0CAB4","rstring_5":"W6X0CAB4","rstring_4":"W6X0CAB4","rint_3":1521.7499,"rint_2":1521.7499,"rstring_10":"W6X0CAB4","id":"cf71a414-8501-454f-801f-b81df59e3c76"},"containsIllegalDate":false,"referenceTime":1721274735083,"tableId":"mock_100w","time":1721274735083,"type":300}, nodeIds=[91d20052-e8b3-4097-aa0b-c5ead5e9e2a8], sourceTime=1721274735083, sourceSerialNo=null} 
[INFO ] 2024-07-18 12:34:24.768 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mongodb_repl_42240_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: sink_100w
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@2ddfa96b: {"after":{"rstring_3":"W6X0CAB4","rstring_2":"W6X0CAB4","rstring_1":"W6X0CAB4","created":1721274650485,"rstring_9":"W6X0CAB4","rstring_8":"W6X0CAB4","title":"W6X0CAB4","rstring_7":"W6X0CAB4","rint_1":1521.7499,"rstring_6":"W6X0CAB4","rstring_5":"W6X0CAB4","rstring_4":"W6X0CAB4","rint_3":1521.7499,"rint_2":1521.7499,"rstring_10":"W6X0CAB4","id":"f704685a-e616-4231-a1f3-afdea8b45a0c"},"before":{"rstring_3":"W6X0CAB4","rstring_2":"W6X0CAB4","rstring_1":"W6X0CAB4","created":1721274650485,"rstring_9":"W6X0CAB4","rstring_8":"W6X0CAB4","title":"W6X0CAB4","rstring_7":"W6X0CAB4","rint_1":1521.7499,"rstring_6":"W6X0CAB4","rstring_5":"W6X0CAB4","rstring_4":"W6X0CAB4","rint_3":1521.7499,"rint_2":1521.7499,"rstring_10":"W6X0CAB4","id":"f704685a-e616-4231-a1f3-afdea8b45a0c"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721274650485,"tableId":"mock_100w","time":1721274650485,"type":302}, nodeIds=[91d20052-e8b3-4097-aa0b-c5ead5e9e2a8], sourceTime=1721274650485, sourceSerialNo=null} 
[INFO ] 2024-07-18 12:34:29.636 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mongodb_repl_42240_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: sink_100w
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@42385b4c: {"after":{"rstring_3":"W6X0CAB4","rstring_2":"W6X0CAB4","rstring_1":"W6X0CAB4","created":1721273241751,"rstring_9":"W6X0CAB4","rstring_8":"W6X0CAB4","title":"W6X0CAB4","rstring_7":"W6X0CAB4","rint_1":1521.7499,"rstring_6":"W6X0CAB4","rstring_5":"W6X0CAB4","rstring_4":"W6X0CAB4","rint_3":1521.7499,"rint_2":1521.7499,"rstring_10":"W6X0CAB4","id":"75459edb-1fab-495f-b9b2-26fdac7c3a8e"},"before":{"rstring_3":"W6X0CAB4","rstring_2":"W6X0CAB4","rstring_1":"W6X0CAB4","created":1721273241751,"rstring_9":"W6X0CAB4","rstring_8":"W6X0CAB4","title":"W6X0CAB4","rstring_7":"W6X0CAB4","rint_1":1521.7499,"rstring_6":"W6X0CAB4","rstring_5":"W6X0CAB4","rstring_4":"W6X0CAB4","rint_3":1521.7499,"rint_2":1521.7499,"rstring_10":"W6X0CAB4","id":"75459edb-1fab-495f-b9b2-26fdac7c3a8e"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721273241751,"tableId":"mock_100w","time":1721273241751,"type":302}, nodeIds=[91d20052-e8b3-4097-aa0b-c5ead5e9e2a8], sourceTime=1721273241751, sourceSerialNo=null} 
[INFO ] 2024-07-18 12:34:43.835 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mongodb_repl_42240_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: sink_100w
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@4ace79f6: {"after":{"rstring_3":"W6X0CAB4","rstring_2":"W6X0CAB4","rstring_1":"W6X0CAB4","created":1721273700202,"rstring_9":"W6X0CAB4","rstring_8":"W6X0CAB4","title":"W6X0CAB4","rstring_7":"W6X0CAB4","rint_1":1521.7499,"rstring_6":"W6X0CAB4","rstring_5":"W6X0CAB4","rstring_4":"W6X0CAB4","rint_3":1521.7499,"rint_2":1521.7499,"rstring_10":"W6X0CAB4","id":"7a9ca21b-5899-4e6e-9b2e-8e39e6df380a"},"before":{"rstring_3":"W6X0CAB4","rstring_2":"W6X0CAB4","rstring_1":"W6X0CAB4","created":1721273700202,"rstring_9":"W6X0CAB4","rstring_8":"W6X0CAB4","title":"W6X0CAB4","rstring_7":"W6X0CAB4","rint_1":1521.7499,"rstring_6":"W6X0CAB4","rstring_5":"W6X0CAB4","rstring_4":"W6X0CAB4","rint_3":1521.7499,"rint_2":1521.7499,"rstring_10":"W6X0CAB4","id":"7a9ca21b-5899-4e6e-9b2e-8e39e6df380a"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721273700202,"tableId":"mock_100w","time":1721273700202,"type":302}, nodeIds=[91d20052-e8b3-4097-aa0b-c5ead5e9e2a8], sourceTime=1721273700202, sourceSerialNo=null} 
[INFO ] 2024-07-18 12:34:51.853 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mongodb_repl_42240_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: sink_100w
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@468eea39: {"after":{"rstring_3":"W6X0CAB4","rstring_2":"W6X0CAB4","rstring_1":"W6X0CAB4","created":1721274650454,"rstring_9":"W6X0CAB4","rstring_8":"W6X0CAB4","title":"W6X0CAB4","rstring_7":"W6X0CAB4","rint_1":1521.7499,"rstring_6":"W6X0CAB4","rstring_5":"W6X0CAB4","rstring_4":"W6X0CAB4","rint_3":1521.7499,"rint_2":1521.7499,"rstring_10":"W6X0CAB4","id":"6bdabcec-e5e8-43b4-b7a8-f0f624a67d41"},"before":{"rstring_3":"W6X0CAB4","rstring_2":"W6X0CAB4","rstring_1":"W6X0CAB4","created":1721274650454,"rstring_9":"W6X0CAB4","rstring_8":"W6X0CAB4","title":"W6X0CAB4","rstring_7":"W6X0CAB4","rint_1":1521.7499,"rstring_6":"W6X0CAB4","rstring_5":"W6X0CAB4","rstring_4":"W6X0CAB4","rint_3":1521.7499,"rint_2":1521.7499,"rstring_10":"W6X0CAB4","id":"6bdabcec-e5e8-43b4-b7a8-f0f624a67d41"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721274650454,"tableId":"mock_100w","time":1721274650454,"type":302}, nodeIds=[91d20052-e8b3-4097-aa0b-c5ead5e9e2a8], sourceTime=1721274650454, sourceSerialNo=null} 
[INFO ] 2024-07-18 12:34:54.891 - [t_2.1-mock_to_mdb_20fields_1717403468657_3537-1721270730][qa_mongodb_repl_42240_1717403468657_3537] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: sink_100w
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapUpdateRecordEvent@56a4541e: {"after":{"rstring_3":"W6X0CAB4","rstring_2":"W6X0CAB4","rstring_1":"W6X0CAB4","created":1721275199342,"rstring_9":"W6X0CAB4","rstring_8":"W6X0CAB4","title":"W6X0CAB4","rstring_7":"W6X0CAB4","rint_1":1521.7499,"rstring_6":"W6X0CAB4","rstring_5":"W6X0CAB4","rstring_4":"W6X0CAB4","rint_3":1521.7499,"rint_2":1521.7499,"rstring_10":"W6X0CAB4","id":"364d6e45-fce2-4d55-a096-681483259208"},"before":{"rstring_3":"W6X0CAB4","rstring_2":"W6X0CAB4","rstring_1":"W6X0CAB4","created":1721275199342,"rstring_9":"W6X0CAB4","rstring_8":"W6X0CAB4","title":"W6X0CAB4","rstring_7":"W6X0CAB4","rint_1":1521.7499,"rstring_6":"W6X0CAB4","rstring_5":"W6X0CAB4","rstring_4":"W6X0CAB4","rint_3":1521.7499,"rint_2":1521.7499,"rstring_10":"W6X0CAB4","id":"364d6e45-fce2-4d55-a096-681483259208"},"containsIllegalDate":false,"isReplaceEvent":false,"referenceTime":1721275199342,"tableId":"mock_100w","time":1721275199342,"type":302}, nodeIds=[91d20052-e8b3-4097-aa0b-c5ead5e9e2a8], sourceTime=1721275199342, sourceSerialNo=null} 
