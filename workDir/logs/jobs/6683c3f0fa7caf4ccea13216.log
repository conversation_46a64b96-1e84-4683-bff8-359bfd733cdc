[INFO ] 2024-07-02 17:10:43.435 - [任务 41] - Start task milestones: 6683c3f0fa7caf4ccea13216(任务 41) 
[INFO ] 2024-07-02 17:10:43.439 - [任务 41] - Task initialization... 
[INFO ] 2024-07-02 17:10:44.606 - [任务 41] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-02 17:10:44.609 - [任务 41] - The engine receives 任务 41 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-02 17:10:44.712 - [任务 41][POLICY] - Node POLICY[10bc8649-f221-4fda-b26d-28a41e061e00] start preload schema,table counts: 1 
[INFO ] 2024-07-02 17:10:44.712 - [任务 41][POLICY] - Node POLICY[58400236-6c82-48d0-9472-e2f2ec78d5ae] start preload schema,table counts: 1 
[INFO ] 2024-07-02 17:10:44.714 - [任务 41][POLICY] - Node POLICY[10bc8649-f221-4fda-b26d-28a41e061e00] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 17:10:44.715 - [任务 41][POLICY] - Node POLICY[58400236-6c82-48d0-9472-e2f2ec78d5ae] preload schema finished, cost 0 ms 
[INFO ] 2024-07-02 17:10:46.306 - [任务 41][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-02 17:10:46.306 - [任务 41][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-02 17:10:46.308 - [任务 41][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-02 17:10:46.439 - [任务 41][POLICY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-02 17:10:46.517 - [任务 41][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1719911446,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-02 17:10:46.567 - [任务 41][POLICY] - Initial sync started 
[INFO ] 2024-07-02 17:10:46.578 - [任务 41][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-02 17:10:46.579 - [任务 41][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-02 17:10:46.783 - [任务 41][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-02 17:10:46.789 - [任务 41][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-02 17:10:46.790 - [任务 41][POLICY] - Initial sync completed 
[INFO ] 2024-07-02 17:10:46.791 - [任务 41][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-02 17:10:46.791 - [任务 41][POLICY] - Initial sync completed 
[INFO ] 2024-07-02 17:10:46.856 - [任务 41][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-02 17:10:46.856 - [任务 41][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceMongo enable share cdc: true 
[INFO ] 2024-07-02 17:10:46.885 - [任务 41][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 41 enable share cdc: true 
[INFO ] 2024-07-02 17:10:46.885 - [任务 41][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceMongo的共享挖掘任务 
[INFO ] 2024-07-02 17:10:46.915 - [任务 41][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-02 17:10:46.916 - [任务 41][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 17:10:47.058 - [任务 41][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 41', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 0 
[INFO ] 2024-07-02 17:10:47.058 - [任务 41][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-02 17:10:47.058 - [任务 41][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-02 17:10:47.058 - [任务 41][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-02 17:10:47.058 - [任务 41][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-02 17:10:47.062 - [任务 41][POLICY] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-02 17:10:47.087 - [任务 41][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6683c41266ab5ede8a9b87af, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6674feb868ca1e3afc2a0d99_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_553058525, shareCdcTaskId=6683c412fa7caf4ccea13264, connectionId=6674feb868ca1e3afc2a0d99) 
[INFO ] 2024-07-02 17:10:47.088 - [任务 41][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 41', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_553058525', head seq: 0, tail seq: 0 
[INFO ] 2024-07-02 17:10:47.092 - [任务 41][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceMongo的共享挖掘任务_POLICY_任务 41, external storage name: ExternalStorage_SHARE_CDC_553058525 
[INFO ] 2024-07-02 17:10:47.092 - [任务 41][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-02 17:10:47.099 - [任务 41][POLICY] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-02T09:10:46.305Z): 1 
[INFO ] 2024-07-02 17:10:47.099 - [任务 41][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-02 17:10:47.105 - [任务 41][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 1 
[INFO ] 2024-07-02 17:10:47.109 - [任务 41][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=1} 
[INFO ] 2024-07-02 17:11:34.740 - [任务 41][POLICY] - [Share CDC Task HZ Reader] - Successfully read first log data: Document{{fromTable=POLICY, timestamp=1719911488000, date=Tue Jul 02 17:11:28 CST 2024, before=Document{{}}, after=Document{{_id=[B@32e52985, POLICY_ID=PC_000000007, CAR_MODEL=Volkswageolf, COVER_START=Fri Jul 31 16:57:37 CST 2009, CUSTOMER_ID=C000022027, LAST_ANN_PREMIUM_GROSS=11.94, LAST_CHANGE=Fri Jul 05 09:13:42 CST 2019, MAX_COVERED=100000.0}}, op=u, offsetString=gAFkABVvcmcuYnNvbi5Cc29uRG9jdW1lbnQBFAAFX2RhdGEBAgATb3JnLmJzb24uQnNvblN0cmlu
ZwAAAdR7ImFycmF5IjpmYWxzZSwiYmluYXJ5IjpmYWxzZSwiYm9vbGVhbiI6ZmFsc2UsImJzb25U
eXBlIjoiU1RSSU5HIiwiZEJQb2ludGVyIjpmYWxzZSwiZGF0ZVRpbWUiOmZhbHNlLCJkZWNpbWFs
MTI4IjpmYWxzZSwiZG9jdW1lbnQiOmZhbHNlLCJkb3VibGUiOmZhbHNlLCJpbnQzMiI6ZmFsc2Us
ImludDY0IjpmYWxzZSwiamF2YVNjcmlwdCI6ZmFsc2UsImphdmFTY3JpcHRXaXRoU2NvcGUiOmZh
bHNlLCJudWxsIjpmYWxzZSwibnVtYmVyIjpmYWxzZSwib2JqZWN0SWQiOmZhbHNlLCJyZWd1bGFy
RXhwcmVzc2lvbiI6ZmFsc2UsInN0cmluZyI6dHJ1ZSwic3ltYm9sIjpmYWxzZSwidGltZXN0YW1w
IjpmYWxzZSwidmFsdWUiOiI4MjY2ODNDNDQwMDAwMDAwMDMyQjAyMkMwMTAwMjk2RTVBMTAwNDE5
QUMyQ0U0NUFGMjRGRDRCNDI1NjlBQzE4QzZGOEM3NDY2NDVGNjk2NDAwNjQ2NTEwRjc0Q0EyNzBB
MUNGNTUzM0NFRjkwMDA0In2o
, type=DATA, connectionId=6674feb868ca1e3afc2a0d99, isReplaceEvent=false, _ts=1719911489}} 
[INFO ] 2024-07-02 17:20:43.992 - [任务 41][POLICY] - Node POLICY[58400236-6c82-48d0-9472-e2f2ec78d5ae] running status set to false 
[INFO ] 2024-07-02 17:20:44.024 - [任务 41][POLICY] - Incremental sync completed 
[INFO ] 2024-07-02 17:20:44.024 - [任务 41][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-58400236-6c82-48d0-9472-e2f2ec78d5ae 
[INFO ] 2024-07-02 17:20:44.027 - [任务 41][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-58400236-6c82-48d0-9472-e2f2ec78d5ae 
[INFO ] 2024-07-02 17:20:44.027 - [任务 41][POLICY] - Node POLICY[58400236-6c82-48d0-9472-e2f2ec78d5ae] schema data cleaned 
[INFO ] 2024-07-02 17:20:44.034 - [任务 41][POLICY] - Node POLICY[58400236-6c82-48d0-9472-e2f2ec78d5ae] monitor closed 
[INFO ] 2024-07-02 17:20:44.035 - [任务 41][POLICY] - Node POLICY[58400236-6c82-48d0-9472-e2f2ec78d5ae] close complete, cost 56 ms 
[INFO ] 2024-07-02 17:20:44.071 - [任务 41][POLICY] - Node POLICY[10bc8649-f221-4fda-b26d-28a41e061e00] running status set to false 
[INFO ] 2024-07-02 17:20:44.072 - [任务 41][POLICY] - PDK connector node stopped: HazelcastTargetPdkDataNode-10bc8649-f221-4fda-b26d-28a41e061e00 
[INFO ] 2024-07-02 17:20:44.072 - [任务 41][POLICY] - PDK connector node released: HazelcastTargetPdkDataNode-10bc8649-f221-4fda-b26d-28a41e061e00 
[INFO ] 2024-07-02 17:20:44.073 - [任务 41][POLICY] - Node POLICY[10bc8649-f221-4fda-b26d-28a41e061e00] schema data cleaned 
[INFO ] 2024-07-02 17:20:44.075 - [任务 41][POLICY] - Node POLICY[10bc8649-f221-4fda-b26d-28a41e061e00] monitor closed 
[INFO ] 2024-07-02 17:20:44.075 - [任务 41][POLICY] - Node POLICY[10bc8649-f221-4fda-b26d-28a41e061e00] close complete, cost 39 ms 
[INFO ] 2024-07-02 17:20:47.459 - [任务 41] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-02 17:20:47.462 - [任务 41] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@655f7e65 
[INFO ] 2024-07-02 17:20:47.463 - [任务 41] - Stop task milestones: 6683c3f0fa7caf4ccea13216(任务 41)  
[INFO ] 2024-07-02 17:20:47.596 - [任务 41] - Stopped task aspect(s) 
[INFO ] 2024-07-02 17:20:47.597 - [任务 41] - Snapshot order controller have been removed 
[INFO ] 2024-07-02 17:20:47.667 - [任务 41] - Remove memory task client succeed, task: 任务 41[6683c3f0fa7caf4ccea13216] 
[INFO ] 2024-07-02 17:20:47.670 - [任务 41] - Destroy memory task client cache succeed, task: 任务 41[6683c3f0fa7caf4ccea13216] 
