[INFO ] 2024-05-31 11:25:08.197 - [任务 1] - Start task milestones: 665940bef6745e7b9367481c(任务 1) 
[INFO ] 2024-05-31 11:25:08.198 - [任务 1] - Task initialization... 
[INFO ] 2024-05-31 11:25:08.198 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-31 11:25:08.198 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-31 11:25:08.686 - [任务 1][POCTest1] - Node POCTest1[a00a9531-c7f3-486a-8b4b-0469d539a0d1] start preload schema,table counts: 1 
[INFO ] 2024-05-31 11:25:08.690 - [任务 1][test1] - Node test1[61a2a4ab-a250-4e7b-87dd-1cfdd0a4b9cf] start preload schema,table counts: 1 
[INFO ] 2024-05-31 11:25:08.823 - [任务 1][test1] - Node test1[61a2a4ab-a250-4e7b-87dd-1cfdd0a4b9cf] preload schema finished, cost 130 ms 
[INFO ] 2024-05-31 11:25:08.823 - [任务 1][POCTest1] - Node POCTest1[a00a9531-c7f3-486a-8b4b-0469d539a0d1] preload schema finished, cost 133 ms 
[INFO ] 2024-05-31 11:25:10.258 - [任务 1][POCTest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-31 11:25:10.337 - [任务 1][test1] - Source node "test1" read batch size: 500 
[INFO ] 2024-05-31 11:25:10.338 - [任务 1][test1] - Source node "test1" event queue capacity: 1000 
[INFO ] 2024-05-31 11:25:10.338 - [任务 1][test1] - On the first run, the breakpoint will be initialized 
[WARN ] 2024-05-31 11:25:10.520 - [任务 1][test1] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-05-31 11:25:10.567 - [任务 1][test1] - new logical replication slot created, slotName:tapdata_cdc_7475b3bb_4b04_47ed_ad67_83654eaa573c 
[INFO ] 2024-05-31 11:25:10.567 - [任务 1][test1] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2024-05-31 11:25:14.389 - [任务 1][test1] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-31 11:25:14.423 - [任务 1][test1] - Initial sync started 
[INFO ] 2024-05-31 11:25:14.447 - [任务 1][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-05-31 11:25:14.448 - [任务 1][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-05-31 11:25:14.521 - [任务 1][test1] - Query table 'test1' counts: 0 
[INFO ] 2024-05-31 11:25:14.527 - [任务 1][test1] - Initial sync completed 
[INFO ] 2024-05-31 11:25:14.527 - [任务 1][test1] - Incremental sync starting... 
[INFO ] 2024-05-31 11:25:14.534 - [任务 1][test1] - Initial sync completed 
[INFO ] 2024-05-31 11:25:14.535 - [任务 1][test1] - Starting stream read, table list: [test1], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[WARN ] 2024-05-31 11:25:14.567 - [任务 1][test1] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2024-05-31 11:25:14.569 - [任务 1][test1] - Using an existing logical replication slot, slotName:tapdata_cdc_7475b3bb_4b04_47ed_ad67_83654eaa573c 
[INFO ] 2024-05-31 11:25:14.973 - [任务 1][test1] - Connector PostgreSQL incremental start succeed, tables: [test1], data change syncing 
[INFO ] 2024-05-31 12:16:57.443 - [任务 1] - Stop task milestones: 665940bef6745e7b9367481c(任务 1)  
[INFO ] 2024-05-31 12:16:57.773 - [任务 1][test1] - Node test1[61a2a4ab-a250-4e7b-87dd-1cfdd0a4b9cf] running status set to false 
[INFO ] 2024-05-31 12:16:58.231 - [任务 1][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-61a2a4ab-a250-4e7b-87dd-1cfdd0a4b9cf 
[INFO ] 2024-05-31 12:16:58.231 - [任务 1][test1] - PDK connector node released: HazelcastSourcePdkDataNode-61a2a4ab-a250-4e7b-87dd-1cfdd0a4b9cf 
[INFO ] 2024-05-31 12:16:58.235 - [任务 1][test1] - Node test1[61a2a4ab-a250-4e7b-87dd-1cfdd0a4b9cf] schema data cleaned 
[INFO ] 2024-05-31 12:16:58.237 - [任务 1][test1] - Node test1[61a2a4ab-a250-4e7b-87dd-1cfdd0a4b9cf] monitor closed 
[INFO ] 2024-05-31 12:16:58.242 - [任务 1][test1] - Node test1[61a2a4ab-a250-4e7b-87dd-1cfdd0a4b9cf] close complete, cost 480 ms 
[INFO ] 2024-05-31 12:16:58.247 - [任务 1][POCTest1] - Node POCTest1[a00a9531-c7f3-486a-8b4b-0469d539a0d1] running status set to false 
[INFO ] 2024-05-31 12:16:58.305 - [任务 1][POCTest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-a00a9531-c7f3-486a-8b4b-0469d539a0d1 
[INFO ] 2024-05-31 12:16:58.306 - [任务 1][POCTest1] - PDK connector node released: HazelcastTargetPdkDataNode-a00a9531-c7f3-486a-8b4b-0469d539a0d1 
[INFO ] 2024-05-31 12:16:58.306 - [任务 1][POCTest1] - Node POCTest1[a00a9531-c7f3-486a-8b4b-0469d539a0d1] schema data cleaned 
[INFO ] 2024-05-31 12:16:58.307 - [任务 1][POCTest1] - Node POCTest1[a00a9531-c7f3-486a-8b4b-0469d539a0d1] monitor closed 
[INFO ] 2024-05-31 12:16:58.521 - [任务 1][POCTest1] - Node POCTest1[a00a9531-c7f3-486a-8b4b-0469d539a0d1] close complete, cost 66 ms 
[INFO ] 2024-05-31 12:17:00.241 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-31 12:17:00.242 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-05-31 12:17:00.321 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-05-31 12:17:00.325 - [任务 1] - Remove memory task client succeed, task: 任务 1[665940bef6745e7b9367481c] 
[INFO ] 2024-05-31 12:17:00.325 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[665940bef6745e7b9367481c] 
