[INFO ] 2024-07-29 05:12:45.130 - [TestCkArray] - Task initialization... 
[INFO ] 2024-07-29 05:12:45.250 - [TestCkArray] - Start task milestones: 66a6b3e766c385580759c200(TestCkArray) 
[INFO ] 2024-07-29 05:12:45.250 - [TestCkArray] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-29 05:12:45.342 - [TestCkArray] - The engine receives TestCkArray task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-29 05:12:45.342 - [TestCkArray][my_array_table] - Node my_array_table[aaf8b351-5160-4316-88b6-35a9372deb53] start preload schema,table counts: 1 
[INFO ] 2024-07-29 05:12:45.342 - [TestCkArray][testArr] - Node testArr[75a6c63e-6621-4622-b55a-7df53a05aa34] start preload schema,table counts: 1 
[INFO ] 2024-07-29 05:12:45.342 - [TestCkArray][my_array_table] - Node my_array_table[aaf8b351-5160-4316-88b6-35a9372deb53] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 05:12:45.342 - [TestCkArray][testArr] - Node testArr[75a6c63e-6621-4622-b55a-7df53a05aa34] preload schema finished, cost 0 ms 
[INFO ] 2024-07-29 05:12:46.138 - [TestCkArray][testArr] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-29 05:12:46.232 - [TestCkArray][my_array_table] - Source node "my_array_table" read batch size: 100 
[INFO ] 2024-07-29 05:12:46.232 - [TestCkArray][my_array_table] - Source node "my_array_table" event queue capacity: 200 
[INFO ] 2024-07-29 05:12:46.232 - [TestCkArray][my_array_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-29 05:12:46.232 - [TestCkArray][my_array_table] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-29 05:12:46.279 - [TestCkArray][my_array_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-29 05:12:46.280 - [TestCkArray][my_array_table] - Initial sync started 
[INFO ] 2024-07-29 05:12:46.280 - [TestCkArray][my_array_table] - Starting batch read, table name: my_array_table, offset: null 
[INFO ] 2024-07-29 05:12:46.314 - [TestCkArray][my_array_table] - Table my_array_table is going to be initial synced 
[INFO ] 2024-07-29 05:12:46.316 - [TestCkArray][my_array_table] - Table [my_array_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-29 05:12:46.322 - [TestCkArray][my_array_table] - Query table 'my_array_table' counts: 3 
[INFO ] 2024-07-29 05:12:46.324 - [TestCkArray][my_array_table] - Initial sync completed 
[INFO ] 2024-07-29 05:12:46.853 - [TestCkArray][my_array_table] - Node my_array_table[aaf8b351-5160-4316-88b6-35a9372deb53] running status set to false 
[INFO ] 2024-07-29 05:12:46.858 - [TestCkArray][testArr] - Node testArr[75a6c63e-6621-4622-b55a-7df53a05aa34] running status set to false 
[INFO ] 2024-07-29 05:12:46.858 - [TestCkArray][my_array_table] - Clickhouse Optimize Table start, tables: [] 
[INFO ] 2024-07-29 05:12:46.916 - [TestCkArray][my_array_table] - Clickhouse Optimize Table end 
[INFO ] 2024-07-29 05:12:46.921 - [TestCkArray][testArr] - PDK connector node stopped: HazelcastTargetPdkDataNode-75a6c63e-6621-4622-b55a-7df53a05aa34 
[INFO ] 2024-07-29 05:12:46.921 - [TestCkArray][testArr] - PDK connector node released: HazelcastTargetPdkDataNode-75a6c63e-6621-4622-b55a-7df53a05aa34 
[INFO ] 2024-07-29 05:12:46.924 - [TestCkArray][testArr] - Node testArr[75a6c63e-6621-4622-b55a-7df53a05aa34] schema data cleaned 
[INFO ] 2024-07-29 05:12:46.924 - [TestCkArray][testArr] - Node testArr[75a6c63e-6621-4622-b55a-7df53a05aa34] monitor closed 
[INFO ] 2024-07-29 05:12:46.939 - [TestCkArray][testArr] - Node testArr[75a6c63e-6621-4622-b55a-7df53a05aa34] close complete, cost 93 ms 
[INFO ] 2024-07-29 05:12:46.939 - [TestCkArray][my_array_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-aaf8b351-5160-4316-88b6-35a9372deb53 
[INFO ] 2024-07-29 05:12:46.939 - [TestCkArray][my_array_table] - PDK connector node released: HazelcastSourcePdkDataNode-aaf8b351-5160-4316-88b6-35a9372deb53 
[INFO ] 2024-07-29 05:12:46.939 - [TestCkArray][my_array_table] - Node my_array_table[aaf8b351-5160-4316-88b6-35a9372deb53] schema data cleaned 
[INFO ] 2024-07-29 05:12:46.940 - [TestCkArray][my_array_table] - Node my_array_table[aaf8b351-5160-4316-88b6-35a9372deb53] monitor closed 
[INFO ] 2024-07-29 05:12:46.940 - [TestCkArray][my_array_table] - Node my_array_table[aaf8b351-5160-4316-88b6-35a9372deb53] close complete, cost 113 ms 
[INFO ] 2024-07-29 05:12:51.440 - [TestCkArray] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-29 05:12:51.561 - [TestCkArray] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@11116294 
[INFO ] 2024-07-29 05:12:51.562 - [TestCkArray] - Stop task milestones: 66a6b3e766c385580759c200(TestCkArray)  
[INFO ] 2024-07-29 05:12:51.576 - [TestCkArray] - Stopped task aspect(s) 
[INFO ] 2024-07-29 05:12:51.576 - [TestCkArray] - Snapshot order controller have been removed 
[INFO ] 2024-07-29 05:12:51.602 - [TestCkArray] - Remove memory task client succeed, task: TestCkArray[66a6b3e766c385580759c200] 
[INFO ] 2024-07-29 05:12:51.604 - [TestCkArray] - Destroy memory task client cache succeed, task: TestCkArray[66a6b3e766c385580759c200] 
