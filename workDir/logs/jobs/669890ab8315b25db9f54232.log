[INFO ] 2024-07-18 11:49:00.654 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537] - Start task milestones: 669890ab8315b25db9f54232(Heartbeat-qa_mongodb_repl_36230_1717403468657_3537) 
[INFO ] 2024-07-18 11:49:00.890 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 11:49:01.070 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537] - The engine receives Heartbeat-qa_mongodb_repl_36230_1717403468657_3537 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 11:49:01.165 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d2fafb75-6b78-42f6-91c1-e36ebaa47f69] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:49:01.165 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[f6143558-ddbf-4fb1-9cb3-6341610b19b6] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:49:01.166 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d2fafb75-6b78-42f6-91c1-e36ebaa47f69] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:49:01.166 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[f6143558-ddbf-4fb1-9cb3-6341610b19b6] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:49:01.398 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-18 11:49:01.398 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-18 11:49:01.398 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 11:49:01.399 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721274541398,"lastTimes":1721274541398,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-18 11:49:01.561 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-18 11:49:01.563 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-18 11:49:01.584 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-18 11:49:01.584 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:49:01.600 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-18 11:49:01.602 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1721274541398,"lastTimes":1721274541398,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-18 11:49:01.603 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-18 11:49:01.603 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 11:49:01.616 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[WARN ] 2024-07-18 11:49:01.687 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Index [{id=1}] already exists but options is inconsistent, will ignore creating this index, server error detail message: Command failed with error 85 (IndexOptionsConflict): 'Index with name: id_1 already exists with different options' on server 192.168.1.179:7002. The full response is {"operationTime": {"$timestamp": {"t": 1721274541, "i": 3}}, "ok": 0.0, "errmsg": "Index with name: id_1 already exists with different options", "code": 85, "codeName": "IndexOptionsConflict", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721274541, "i": 3}}, "signature": {"hash": {"$binary": {"base64": "XVdMm8+bhXCPZ7hCgqDgUORuh4Q=", "subType": "00"}}, "keyId": 7343467110548373506}}} 
[INFO ] 2024-07-18 12:33:13.289 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d2fafb75-6b78-42f6-91c1-e36ebaa47f69] running status set to false 
[INFO ] 2024-07-18 12:33:13.291 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-18 12:33:13.291 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-d2fafb75-6b78-42f6-91c1-e36ebaa47f69 
[INFO ] 2024-07-18 12:33:13.291 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-d2fafb75-6b78-42f6-91c1-e36ebaa47f69 
[INFO ] 2024-07-18 12:33:13.291 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d2fafb75-6b78-42f6-91c1-e36ebaa47f69] schema data cleaned 
[INFO ] 2024-07-18 12:33:13.291 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d2fafb75-6b78-42f6-91c1-e36ebaa47f69] monitor closed 
[INFO ] 2024-07-18 12:33:13.292 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[d2fafb75-6b78-42f6-91c1-e36ebaa47f69] close complete, cost 7 ms 
[INFO ] 2024-07-18 12:33:13.292 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[f6143558-ddbf-4fb1-9cb3-6341610b19b6] running status set to false 
[INFO ] 2024-07-18 12:33:13.307 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-f6143558-ddbf-4fb1-9cb3-6341610b19b6 
[INFO ] 2024-07-18 12:33:13.307 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-f6143558-ddbf-4fb1-9cb3-6341610b19b6 
[INFO ] 2024-07-18 12:33:13.307 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[f6143558-ddbf-4fb1-9cb3-6341610b19b6] schema data cleaned 
[INFO ] 2024-07-18 12:33:13.307 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[f6143558-ddbf-4fb1-9cb3-6341610b19b6] monitor closed 
[INFO ] 2024-07-18 12:33:13.511 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[f6143558-ddbf-4fb1-9cb3-6341610b19b6] close complete, cost 15 ms 
[INFO ] 2024-07-18 12:33:17.628 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 12:33:17.628 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6bcf2642 
[INFO ] 2024-07-18 12:33:17.741 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537] - Stop task milestones: 669890ab8315b25db9f54232(Heartbeat-qa_mongodb_repl_36230_1717403468657_3537)  
[INFO ] 2024-07-18 12:33:17.764 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537] - Stopped task aspect(s) 
[INFO ] 2024-07-18 12:33:17.764 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 12:33:17.810 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537] - Remove memory task client succeed, task: Heartbeat-qa_mongodb_repl_36230_1717403468657_3537[669890ab8315b25db9f54232] 
[INFO ] 2024-07-18 12:33:17.810 - [Heartbeat-qa_mongodb_repl_36230_1717403468657_3537] - Destroy memory task client cache succeed, task: Heartbeat-qa_mongodb_repl_36230_1717403468657_3537[669890ab8315b25db9f54232] 
