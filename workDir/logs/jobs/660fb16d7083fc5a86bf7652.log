[INFO ] 2024-04-05 16:52:37.366 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] start preload schema,table counts: 0 
[INFO ] 2024-04-05 16:52:37.367 - [任务 53(100)][ca293673-22e1-47ef-8a78-75281551d57b] - Node ca293673-22e1-47ef-8a78-75281551d57b[ca293673-22e1-47ef-8a78-75281551d57b] start preload schema,table counts: 0 
[INFO ] 2024-04-05 16:52:37.370 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] preload schema finished, cost 3 ms 
[INFO ] 2024-04-05 16:52:37.370 - [任务 53(100)][ca293673-22e1-47ef-8a78-75281551d57b] - Node ca293673-22e1-47ef-8a78-75281551d57b[ca293673-22e1-47ef-8a78-75281551d57b] preload schema finished, cost 1 ms 
[ERROR] 2024-04-05 16:52:37.370 - [任务 53(100)][ca293673-22e1-47ef-8a78-75281551d57b] - java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node <-- Error Message -->
java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node
	io.tapdata.flow.engine.V2.node.hazelcast.data.HazelcastSchemaTargetNode.doInit(HazelcastSchemaTargetNode.java:115)
	io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	...

<-- Full Stack Trace -->
java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:557)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:211)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node
	at io.tapdata.flow.engine.V2.node.hazelcast.data.HazelcastSchemaTargetNode.doInit(HazelcastSchemaTargetNode.java:115)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	... 12 more

[WARN ] 2024-04-05 16:52:37.370 - [任务 53(100)][增强JS] - The source could not build the executor, please check 
[INFO ] 2024-04-05 16:52:37.371 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] running status set to false 
[INFO ] 2024-04-05 16:52:37.371 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] schema data cleaned 
[INFO ] 2024-04-05 16:52:37.371 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] monitor closed 
[INFO ] 2024-04-05 16:52:37.371 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] close complete, cost 20 ms 
[INFO ] 2024-04-05 16:52:39.491 - [任务 53(100)][ca293673-22e1-47ef-8a78-75281551d57b] - Node ca293673-22e1-47ef-8a78-75281551d57b[ca293673-22e1-47ef-8a78-75281551d57b] running status set to false 
[INFO ] 2024-04-05 16:52:39.493 - [任务 53(100)][ca293673-22e1-47ef-8a78-75281551d57b] - Node ca293673-22e1-47ef-8a78-75281551d57b[ca293673-22e1-47ef-8a78-75281551d57b] schema data cleaned 
[INFO ] 2024-04-05 16:52:39.493 - [任务 53(100)][ca293673-22e1-47ef-8a78-75281551d57b] - Node ca293673-22e1-47ef-8a78-75281551d57b[ca293673-22e1-47ef-8a78-75281551d57b] monitor closed 
[INFO ] 2024-04-05 16:52:39.507 - [任务 53(100)][ca293673-22e1-47ef-8a78-75281551d57b] - Node ca293673-22e1-47ef-8a78-75281551d57b[ca293673-22e1-47ef-8a78-75281551d57b] close complete, cost 19 ms 
[INFO ] 2024-04-05 16:52:39.507 - [任务 53(100)] - load MigrateJsResultVos task 660fb16d7083fc5a86bf7652-ca293673-22e1-47ef-8a78-75281551d57b complete, cost 2708ms 
[INFO ] 2024-04-05 16:52:46.568 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] start preload schema,table counts: 1 
[INFO ] 2024-04-05 16:52:46.568 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] start preload schema,table counts: 1 
[INFO ] 2024-04-05 16:52:46.569 - [任务 53(100)][bab769d1-55c6-4b9e-a686-4617e1c1643f] - Node bab769d1-55c6-4b9e-a686-4617e1c1643f[bab769d1-55c6-4b9e-a686-4617e1c1643f] start preload schema,table counts: 0 
[INFO ] 2024-04-05 16:52:46.574 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] preload schema finished, cost 1 ms 
[INFO ] 2024-04-05 16:52:46.575 - [任务 53(100)][bab769d1-55c6-4b9e-a686-4617e1c1643f] - Node bab769d1-55c6-4b9e-a686-4617e1c1643f[bab769d1-55c6-4b9e-a686-4617e1c1643f] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 16:52:46.575 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 16:52:46.654 - [任务 53(100)][增强JS] - Init standardized JS engine... 
[ERROR] 2024-04-05 16:52:46.858 - [任务 53(100)][增强JS] - Incorrect JS code, syntax error found: function process(record){

	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@327ca59b error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){

	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@327ca59b error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:7)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){

	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@327ca59b error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:128)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:7)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-04-05 16:52:47.226 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] running status set to false 
[INFO ] 2024-04-05 16:52:47.261 - [任务 53(100)][testMysql] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-7eeb5ca2-0956-4afd-9438-dc4325816093 
[INFO ] 2024-04-05 16:52:47.261 - [任务 53(100)][testMysql] - PDK connector node released: HazelcastSampleSourcePdkDataNode-7eeb5ca2-0956-4afd-9438-dc4325816093 
[INFO ] 2024-04-05 16:52:47.261 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] schema data cleaned 
[INFO ] 2024-04-05 16:52:47.262 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] monitor closed 
[INFO ] 2024-04-05 16:52:47.406 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] close complete, cost 39 ms 
[INFO ] 2024-04-05 16:52:47.406 - [任务 53(100)][655941d4-9403-49fa-8ab4-258b41ae6aae] - Node 655941d4-9403-49fa-8ab4-258b41ae6aae[655941d4-9403-49fa-8ab4-258b41ae6aae] start preload schema,table counts: 0 
[INFO ] 2024-04-05 16:52:47.407 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] start preload schema,table counts: 1 
[INFO ] 2024-04-05 16:52:47.407 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] start preload schema,table counts: 1 
[INFO ] 2024-04-05 16:52:47.407 - [任务 53(100)][655941d4-9403-49fa-8ab4-258b41ae6aae] - Node 655941d4-9403-49fa-8ab4-258b41ae6aae[655941d4-9403-49fa-8ab4-258b41ae6aae] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 16:52:47.407 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] preload schema finished, cost 1 ms 
[INFO ] 2024-04-05 16:52:47.488 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 16:52:47.488 - [任务 53(100)][增强JS] - Init standardized JS engine... 
[ERROR] 2024-04-05 16:52:47.670 - [任务 53(100)][增强JS] - Incorrect JS code, syntax error found: function process(record){

	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@9e564c9 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){

	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@9e564c9 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:7)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){

	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@9e564c9 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:128)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:7)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-04-05 16:52:47.674 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] running status set to false 
[INFO ] 2024-04-05 16:52:47.714 - [任务 53(100)][testMysql] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-7eeb5ca2-0956-4afd-9438-dc4325816093 
[INFO ] 2024-04-05 16:52:47.714 - [任务 53(100)][testMysql] - PDK connector node released: HazelcastSampleSourcePdkDataNode-7eeb5ca2-0956-4afd-9438-dc4325816093 
[INFO ] 2024-04-05 16:52:47.715 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] schema data cleaned 
[INFO ] 2024-04-05 16:52:47.717 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] monitor closed 
[INFO ] 2024-04-05 16:52:47.718 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] close complete, cost 54 ms 
[INFO ] 2024-04-05 16:52:49.323 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] running status set to false 
[INFO ] 2024-04-05 16:52:49.324 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] schema data cleaned 
[INFO ] 2024-04-05 16:52:49.324 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] monitor closed 
[INFO ] 2024-04-05 16:52:49.325 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] close complete, cost 3 ms 
[INFO ] 2024-04-05 16:52:49.328 - [任务 53(100)][bab769d1-55c6-4b9e-a686-4617e1c1643f] - Node bab769d1-55c6-4b9e-a686-4617e1c1643f[bab769d1-55c6-4b9e-a686-4617e1c1643f] running status set to false 
[INFO ] 2024-04-05 16:52:49.330 - [任务 53(100)][bab769d1-55c6-4b9e-a686-4617e1c1643f] - Node bab769d1-55c6-4b9e-a686-4617e1c1643f[bab769d1-55c6-4b9e-a686-4617e1c1643f] schema data cleaned 
[INFO ] 2024-04-05 16:52:49.333 - [任务 53(100)][bab769d1-55c6-4b9e-a686-4617e1c1643f] - Node bab769d1-55c6-4b9e-a686-4617e1c1643f[bab769d1-55c6-4b9e-a686-4617e1c1643f] monitor closed 
[INFO ] 2024-04-05 16:52:49.333 - [任务 53(100)][bab769d1-55c6-4b9e-a686-4617e1c1643f] - Node bab769d1-55c6-4b9e-a686-4617e1c1643f[bab769d1-55c6-4b9e-a686-4617e1c1643f] close complete, cost 7 ms 
[INFO ] 2024-04-05 16:52:49.538 - [任务 53(100)] - load MigrateJsResultVos task 660fb16d7083fc5a86bf7652-bab769d1-55c6-4b9e-a686-4617e1c1643f complete, cost 2844ms 
[INFO ] 2024-04-05 16:52:50.046 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] running status set to false 
[INFO ] 2024-04-05 16:52:50.048 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] schema data cleaned 
[INFO ] 2024-04-05 16:52:50.049 - [任务 53(100)][655941d4-9403-49fa-8ab4-258b41ae6aae] - Node 655941d4-9403-49fa-8ab4-258b41ae6aae[655941d4-9403-49fa-8ab4-258b41ae6aae] running status set to false 
[INFO ] 2024-04-05 16:52:50.049 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] monitor closed 
[INFO ] 2024-04-05 16:52:50.051 - [任务 53(100)][655941d4-9403-49fa-8ab4-258b41ae6aae] - Node 655941d4-9403-49fa-8ab4-258b41ae6aae[655941d4-9403-49fa-8ab4-258b41ae6aae] schema data cleaned 
[INFO ] 2024-04-05 16:52:50.053 - [任务 53(100)][655941d4-9403-49fa-8ab4-258b41ae6aae] - Node 655941d4-9403-49fa-8ab4-258b41ae6aae[655941d4-9403-49fa-8ab4-258b41ae6aae] monitor closed 
[INFO ] 2024-04-05 16:52:50.054 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] close complete, cost 8 ms 
[INFO ] 2024-04-05 16:52:50.054 - [任务 53(100)][655941d4-9403-49fa-8ab4-258b41ae6aae] - Node 655941d4-9403-49fa-8ab4-258b41ae6aae[655941d4-9403-49fa-8ab4-258b41ae6aae] close complete, cost 4 ms 
[INFO ] 2024-04-05 16:52:50.256 - [任务 53(100)] - load MigrateJsResultVos task 660fb16d7083fc5a86bf7652-655941d4-9403-49fa-8ab4-258b41ae6aae complete, cost 2757ms 
[INFO ] 2024-04-05 17:21:07.972 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] start preload schema,table counts: 1 
[INFO ] 2024-04-05 17:21:07.975 - [任务 53(100)][b2a969c9-9c2f-4085-ad7c-486d9ff423f2] - Node b2a969c9-9c2f-4085-ad7c-486d9ff423f2[b2a969c9-9c2f-4085-ad7c-486d9ff423f2] start preload schema,table counts: 0 
[INFO ] 2024-04-05 17:21:07.975 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] start preload schema,table counts: 1 
[INFO ] 2024-04-05 17:21:07.975 - [任务 53(100)][b2a969c9-9c2f-4085-ad7c-486d9ff423f2] - Node b2a969c9-9c2f-4085-ad7c-486d9ff423f2[b2a969c9-9c2f-4085-ad7c-486d9ff423f2] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 17:21:07.975 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 17:21:07.975 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 17:21:08.070 - [任务 53(100)][增强JS] - Init standardized JS engine... 
[ERROR] 2024-04-05 17:21:08.268 - [任务 53(100)][增强JS] - Incorrect JS code, syntax error found: function process(record){

	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@42c350a1 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){

	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@42c350a1 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:7)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){

	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@42c350a1 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:128)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:7)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-04-05 17:21:08.269 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] running status set to false 
[INFO ] 2024-04-05 17:21:08.287 - [任务 53(100)][testMysql] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-7eeb5ca2-0956-4afd-9438-dc4325816093 
[INFO ] 2024-04-05 17:21:08.287 - [任务 53(100)][testMysql] - PDK connector node released: HazelcastSampleSourcePdkDataNode-7eeb5ca2-0956-4afd-9438-dc4325816093 
[INFO ] 2024-04-05 17:21:08.287 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] schema data cleaned 
[INFO ] 2024-04-05 17:21:08.287 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] monitor closed 
[INFO ] 2024-04-05 17:21:08.288 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] close complete, cost 20 ms 
[INFO ] 2024-04-05 17:21:10.654 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] running status set to false 
[INFO ] 2024-04-05 17:21:10.662 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] schema data cleaned 
[INFO ] 2024-04-05 17:21:10.663 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] monitor closed 
[INFO ] 2024-04-05 17:21:10.664 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] close complete, cost 2 ms 
[INFO ] 2024-04-05 17:21:10.664 - [任务 53(100)][b2a969c9-9c2f-4085-ad7c-486d9ff423f2] - Node b2a969c9-9c2f-4085-ad7c-486d9ff423f2[b2a969c9-9c2f-4085-ad7c-486d9ff423f2] running status set to false 
[INFO ] 2024-04-05 17:21:10.665 - [任务 53(100)][b2a969c9-9c2f-4085-ad7c-486d9ff423f2] - Node b2a969c9-9c2f-4085-ad7c-486d9ff423f2[b2a969c9-9c2f-4085-ad7c-486d9ff423f2] schema data cleaned 
[INFO ] 2024-04-05 17:21:10.665 - [任务 53(100)][b2a969c9-9c2f-4085-ad7c-486d9ff423f2] - Node b2a969c9-9c2f-4085-ad7c-486d9ff423f2[b2a969c9-9c2f-4085-ad7c-486d9ff423f2] monitor closed 
[INFO ] 2024-04-05 17:21:10.665 - [任务 53(100)][b2a969c9-9c2f-4085-ad7c-486d9ff423f2] - Node b2a969c9-9c2f-4085-ad7c-486d9ff423f2[b2a969c9-9c2f-4085-ad7c-486d9ff423f2] close complete, cost 4 ms 
[INFO ] 2024-04-05 17:21:10.665 - [任务 53(100)] - load MigrateJsResultVos task 660fb16d7083fc5a86bf7652-b2a969c9-9c2f-4085-ad7c-486d9ff423f2 complete, cost 2854ms 
[INFO ] 2024-04-05 17:21:51.504 - [任务 53(100)][183ae004-f3b3-48c7-a2bb-55459983d3aa] - Node 183ae004-f3b3-48c7-a2bb-55459983d3aa[183ae004-f3b3-48c7-a2bb-55459983d3aa] start preload schema,table counts: 0 
[INFO ] 2024-04-05 17:21:51.504 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] start preload schema,table counts: 1 
[INFO ] 2024-04-05 17:21:51.504 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] start preload schema,table counts: 1 
[INFO ] 2024-04-05 17:21:51.504 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 17:21:51.505 - [任务 53(100)][183ae004-f3b3-48c7-a2bb-55459983d3aa] - Node 183ae004-f3b3-48c7-a2bb-55459983d3aa[183ae004-f3b3-48c7-a2bb-55459983d3aa] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 17:21:51.505 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 17:21:51.520 - [任务 53(100)][增强JS] - Init standardized JS engine... 
[ERROR] 2024-04-05 17:21:51.726 - [任务 53(100)][增强JS] - Incorrect JS code, syntax error found: function process(record){

	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3107fd17 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){

	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3107fd17 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:7)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){

	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3107fd17 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:128)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:7)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-04-05 17:21:51.775 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] running status set to false 
[INFO ] 2024-04-05 17:21:51.798 - [任务 53(100)][testMysql] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-7eeb5ca2-0956-4afd-9438-dc4325816093 
[INFO ] 2024-04-05 17:21:51.798 - [任务 53(100)][testMysql] - PDK connector node released: HazelcastSampleSourcePdkDataNode-7eeb5ca2-0956-4afd-9438-dc4325816093 
[INFO ] 2024-04-05 17:21:51.799 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] schema data cleaned 
[INFO ] 2024-04-05 17:21:51.799 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] monitor closed 
[INFO ] 2024-04-05 17:21:51.801 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] close complete, cost 29 ms 
[INFO ] 2024-04-05 17:21:52.515 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] start preload schema,table counts: 1 
[INFO ] 2024-04-05 17:21:52.515 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] start preload schema,table counts: 1 
[INFO ] 2024-04-05 17:21:52.517 - [任务 53(100)][316bea4c-5367-4719-8f62-fdc179dcacd7] - Node 316bea4c-5367-4719-8f62-fdc179dcacd7[316bea4c-5367-4719-8f62-fdc179dcacd7] start preload schema,table counts: 0 
[INFO ] 2024-04-05 17:21:52.517 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 17:21:52.517 - [任务 53(100)][316bea4c-5367-4719-8f62-fdc179dcacd7] - Node 316bea4c-5367-4719-8f62-fdc179dcacd7[316bea4c-5367-4719-8f62-fdc179dcacd7] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 17:21:52.517 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 17:21:52.624 - [任务 53(100)][增强JS] - Init standardized JS engine... 
[ERROR] 2024-04-05 17:21:52.628 - [任务 53(100)][增强JS] - Incorrect JS code, syntax error found: function process(record){

	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@297526e8 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){

	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@297526e8 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:7)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){

	// Enter you code at here
	return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@297526e8 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:128)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:7)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-04-05 17:21:52.783 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] running status set to false 
[INFO ] 2024-04-05 17:21:52.783 - [任务 53(100)][testMysql] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-7eeb5ca2-0956-4afd-9438-dc4325816093 
[INFO ] 2024-04-05 17:21:52.783 - [任务 53(100)][testMysql] - PDK connector node released: HazelcastSampleSourcePdkDataNode-7eeb5ca2-0956-4afd-9438-dc4325816093 
[INFO ] 2024-04-05 17:21:52.783 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] schema data cleaned 
[INFO ] 2024-04-05 17:21:52.783 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] monitor closed 
[INFO ] 2024-04-05 17:21:52.984 - [任务 53(100)][testMysql] - Node testMysql[7eeb5ca2-0956-4afd-9438-dc4325816093] close complete, cost 17 ms 
[INFO ] 2024-04-05 17:21:54.112 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] running status set to false 
[INFO ] 2024-04-05 17:21:54.112 - [任务 53(100)][183ae004-f3b3-48c7-a2bb-55459983d3aa] - Node 183ae004-f3b3-48c7-a2bb-55459983d3aa[183ae004-f3b3-48c7-a2bb-55459983d3aa] running status set to false 
[INFO ] 2024-04-05 17:21:54.112 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] schema data cleaned 
[INFO ] 2024-04-05 17:21:54.113 - [任务 53(100)][183ae004-f3b3-48c7-a2bb-55459983d3aa] - Node 183ae004-f3b3-48c7-a2bb-55459983d3aa[183ae004-f3b3-48c7-a2bb-55459983d3aa] schema data cleaned 
[INFO ] 2024-04-05 17:21:54.115 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] monitor closed 
[INFO ] 2024-04-05 17:21:54.115 - [任务 53(100)][183ae004-f3b3-48c7-a2bb-55459983d3aa] - Node 183ae004-f3b3-48c7-a2bb-55459983d3aa[183ae004-f3b3-48c7-a2bb-55459983d3aa] monitor closed 
[INFO ] 2024-04-05 17:21:54.117 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] close complete, cost 4 ms 
[INFO ] 2024-04-05 17:21:54.117 - [任务 53(100)][183ae004-f3b3-48c7-a2bb-55459983d3aa] - Node 183ae004-f3b3-48c7-a2bb-55459983d3aa[183ae004-f3b3-48c7-a2bb-55459983d3aa] close complete, cost 4 ms 
[INFO ] 2024-04-05 17:21:54.122 - [任务 53(100)] - load MigrateJsResultVos task 660fb16d7083fc5a86bf7652-183ae004-f3b3-48c7-a2bb-55459983d3aa complete, cost 2648ms 
[INFO ] 2024-04-05 17:21:55.150 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] running status set to false 
[INFO ] 2024-04-05 17:21:55.150 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] schema data cleaned 
[INFO ] 2024-04-05 17:21:55.151 - [任务 53(100)][316bea4c-5367-4719-8f62-fdc179dcacd7] - Node 316bea4c-5367-4719-8f62-fdc179dcacd7[316bea4c-5367-4719-8f62-fdc179dcacd7] running status set to false 
[INFO ] 2024-04-05 17:21:55.151 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] monitor closed 
[INFO ] 2024-04-05 17:21:55.151 - [任务 53(100)][316bea4c-5367-4719-8f62-fdc179dcacd7] - Node 316bea4c-5367-4719-8f62-fdc179dcacd7[316bea4c-5367-4719-8f62-fdc179dcacd7] schema data cleaned 
[INFO ] 2024-04-05 17:21:55.151 - [任务 53(100)][增强JS] - Node 增强JS[643998e8-95f3-4636-b415-f5f3c936b403] close complete, cost 2 ms 
[INFO ] 2024-04-05 17:21:55.151 - [任务 53(100)][316bea4c-5367-4719-8f62-fdc179dcacd7] - Node 316bea4c-5367-4719-8f62-fdc179dcacd7[316bea4c-5367-4719-8f62-fdc179dcacd7] monitor closed 
[INFO ] 2024-04-05 17:21:55.152 - [任务 53(100)][316bea4c-5367-4719-8f62-fdc179dcacd7] - Node 316bea4c-5367-4719-8f62-fdc179dcacd7[316bea4c-5367-4719-8f62-fdc179dcacd7] close complete, cost 2 ms 
[INFO ] 2024-04-05 17:21:55.152 - [任务 53(100)] - load MigrateJsResultVos task 660fb16d7083fc5a86bf7652-316bea4c-5367-4719-8f62-fdc179dcacd7 complete, cost 2696ms 
[INFO ] 2024-04-05 19:09:27.913 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] start preload schema,table counts: 2 
[INFO ] 2024-04-05 19:09:27.915 - [任务 53(100)][TargetMysql] - Node TargetMysql[3cc4073e-d4de-4eb3-94c9-278a1e3308a5] start preload schema,table counts: 2 
[INFO ] 2024-04-05 19:09:27.916 - [任务 53(100)][e4578bd0-8ec3-4c8e-af8c-71e83b673304] - Node e4578bd0-8ec3-4c8e-af8c-71e83b673304[e4578bd0-8ec3-4c8e-af8c-71e83b673304] start preload schema,table counts: 0 
[INFO ] 2024-04-05 19:09:27.916 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] preload schema finished, cost 1 ms 
[INFO ] 2024-04-05 19:09:27.916 - [任务 53(100)][TargetMysql] - Node TargetMysql[3cc4073e-d4de-4eb3-94c9-278a1e3308a5] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 19:09:27.916 - [任务 53(100)][e4578bd0-8ec3-4c8e-af8c-71e83b673304] - Node e4578bd0-8ec3-4c8e-af8c-71e83b673304[e4578bd0-8ec3-4c8e-af8c-71e83b673304] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 19:09:28.751 - [任务 53(100)][TargetMysql] - Node TargetMysql[3cc4073e-d4de-4eb3-94c9-278a1e3308a5] running status set to false 
[INFO ] 2024-04-05 19:09:28.752 - [任务 53(100)][TargetMysql] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3cc4073e-d4de-4eb3-94c9-278a1e3308a5 
[INFO ] 2024-04-05 19:09:28.755 - [任务 53(100)][TargetMysql] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3cc4073e-d4de-4eb3-94c9-278a1e3308a5 
[INFO ] 2024-04-05 19:09:28.756 - [任务 53(100)][TargetMysql] - Node TargetMysql[3cc4073e-d4de-4eb3-94c9-278a1e3308a5] schema data cleaned 
[INFO ] 2024-04-05 19:09:28.769 - [任务 53(100)][TargetMysql] - Node TargetMysql[3cc4073e-d4de-4eb3-94c9-278a1e3308a5] monitor closed 
[INFO ] 2024-04-05 19:09:28.769 - [任务 53(100)][TargetMysql] - Node TargetMysql[3cc4073e-d4de-4eb3-94c9-278a1e3308a5] close complete, cost 27 ms 
[INFO ] 2024-04-05 19:09:29.015 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] running status set to false 
[INFO ] 2024-04-05 19:09:29.022 - [任务 53(100)][e4578bd0-8ec3-4c8e-af8c-71e83b673304] - Node e4578bd0-8ec3-4c8e-af8c-71e83b673304[e4578bd0-8ec3-4c8e-af8c-71e83b673304] running status set to false 
[INFO ] 2024-04-05 19:09:29.042 - [任务 53(100)][e4578bd0-8ec3-4c8e-af8c-71e83b673304] - Node e4578bd0-8ec3-4c8e-af8c-71e83b673304[e4578bd0-8ec3-4c8e-af8c-71e83b673304] schema data cleaned 
[INFO ] 2024-04-05 19:09:29.042 - [任务 53(100)][e4578bd0-8ec3-4c8e-af8c-71e83b673304] - Node e4578bd0-8ec3-4c8e-af8c-71e83b673304[e4578bd0-8ec3-4c8e-af8c-71e83b673304] monitor closed 
[INFO ] 2024-04-05 19:09:29.058 - [任务 53(100)][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-daca9347-e348-4709-a585-e1c0e3038c6c 
[INFO ] 2024-04-05 19:09:29.058 - [任务 53(100)][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-daca9347-e348-4709-a585-e1c0e3038c6c 
[INFO ] 2024-04-05 19:09:29.058 - [任务 53(100)][e4578bd0-8ec3-4c8e-af8c-71e83b673304] - Node e4578bd0-8ec3-4c8e-af8c-71e83b673304[e4578bd0-8ec3-4c8e-af8c-71e83b673304] close complete, cost 58 ms 
[INFO ] 2024-04-05 19:09:29.058 - [任务 53(100)][增强JS] - [ScriptExecutorsManager-660fb16d7083fc5a86bf7652-12ea0e72-afe5-406b-b4fb-a1e19af36bb9-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-04-05 19:09:29.077 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] schema data cleaned 
[INFO ] 2024-04-05 19:09:29.077 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] monitor closed 
[INFO ] 2024-04-05 19:09:29.104 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] close complete, cost 97 ms 
[INFO ] 2024-04-05 19:09:29.104 - [任务 53(100)] - load MigrateJsResultVos task 660fb16d7083fc5a86bf7652-e4578bd0-8ec3-4c8e-af8c-71e83b673304 complete, cost 1418ms 
[INFO ] 2024-04-05 19:09:37.486 - [任务 53(100)][56913e07-03a7-4833-b62b-7c0989eef26d] - Node 56913e07-03a7-4833-b62b-7c0989eef26d[56913e07-03a7-4833-b62b-7c0989eef26d] start preload schema,table counts: 0 
[INFO ] 2024-04-05 19:09:37.488 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] start preload schema,table counts: 2 
[INFO ] 2024-04-05 19:09:37.488 - [任务 53(100)][TargetMysql] - Node TargetMysql[3cc4073e-d4de-4eb3-94c9-278a1e3308a5] start preload schema,table counts: 2 
[INFO ] 2024-04-05 19:09:37.490 - [任务 53(100)][56913e07-03a7-4833-b62b-7c0989eef26d] - Node 56913e07-03a7-4833-b62b-7c0989eef26d[56913e07-03a7-4833-b62b-7c0989eef26d] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 19:09:37.490 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 19:09:37.692 - [任务 53(100)][TargetMysql] - Node TargetMysql[3cc4073e-d4de-4eb3-94c9-278a1e3308a5] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 19:09:37.705 - [任务 53(100)][TargetMysql] - Node TargetMysql[3cc4073e-d4de-4eb3-94c9-278a1e3308a5] running status set to false 
[INFO ] 2024-04-05 19:09:37.720 - [任务 53(100)][TargetMysql] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3cc4073e-d4de-4eb3-94c9-278a1e3308a5 
[INFO ] 2024-04-05 19:09:37.720 - [任务 53(100)][TargetMysql] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3cc4073e-d4de-4eb3-94c9-278a1e3308a5 
[INFO ] 2024-04-05 19:09:37.721 - [任务 53(100)][TargetMysql] - Node TargetMysql[3cc4073e-d4de-4eb3-94c9-278a1e3308a5] schema data cleaned 
[INFO ] 2024-04-05 19:09:37.722 - [任务 53(100)][TargetMysql] - Node TargetMysql[3cc4073e-d4de-4eb3-94c9-278a1e3308a5] monitor closed 
[INFO ] 2024-04-05 19:09:37.927 - [任务 53(100)][TargetMysql] - Node TargetMysql[3cc4073e-d4de-4eb3-94c9-278a1e3308a5] close complete, cost 20 ms 
[INFO ] 2024-04-05 19:09:37.929 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] running status set to false 
[INFO ] 2024-04-05 19:09:37.931 - [任务 53(100)][56913e07-03a7-4833-b62b-7c0989eef26d] - Node 56913e07-03a7-4833-b62b-7c0989eef26d[56913e07-03a7-4833-b62b-7c0989eef26d] running status set to false 
[INFO ] 2024-04-05 19:09:37.939 - [任务 53(100)][56913e07-03a7-4833-b62b-7c0989eef26d] - Node 56913e07-03a7-4833-b62b-7c0989eef26d[56913e07-03a7-4833-b62b-7c0989eef26d] schema data cleaned 
[INFO ] 2024-04-05 19:09:37.943 - [任务 53(100)][56913e07-03a7-4833-b62b-7c0989eef26d] - Node 56913e07-03a7-4833-b62b-7c0989eef26d[56913e07-03a7-4833-b62b-7c0989eef26d] monitor closed 
[INFO ] 2024-04-05 19:09:37.946 - [任务 53(100)][56913e07-03a7-4833-b62b-7c0989eef26d] - Node 56913e07-03a7-4833-b62b-7c0989eef26d[56913e07-03a7-4833-b62b-7c0989eef26d] close complete, cost 25 ms 
[INFO ] 2024-04-05 19:09:37.964 - [任务 53(100)][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-55484271-b73a-47c6-9170-0fc084c1db85 
[INFO ] 2024-04-05 19:09:37.964 - [任务 53(100)][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-55484271-b73a-47c6-9170-0fc084c1db85 
[INFO ] 2024-04-05 19:09:37.966 - [任务 53(100)][增强JS] - [ScriptExecutorsManager-660fb16d7083fc5a86bf7652-12ea0e72-afe5-406b-b4fb-a1e19af36bb9-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-04-05 19:09:37.966 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] schema data cleaned 
[INFO ] 2024-04-05 19:09:37.967 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] monitor closed 
[INFO ] 2024-04-05 19:09:37.967 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] close complete, cost 49 ms 
[INFO ] 2024-04-05 19:09:38.172 - [任务 53(100)] - load MigrateJsResultVos task 660fb16d7083fc5a86bf7652-56913e07-03a7-4833-b62b-7c0989eef26d complete, cost 532ms 
[INFO ] 2024-04-05 19:09:47.367 - [任务 53(100)][777cb382-4ebf-4d01-addc-d736baadbcc5] - Node 777cb382-4ebf-4d01-addc-d736baadbcc5[777cb382-4ebf-4d01-addc-d736baadbcc5] start preload schema,table counts: 0 
[INFO ] 2024-04-05 19:09:47.367 - [任务 53(100)][TargetMysql] - Node TargetMysql[3cc4073e-d4de-4eb3-94c9-278a1e3308a5] start preload schema,table counts: 2 
[INFO ] 2024-04-05 19:09:47.370 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] start preload schema,table counts: 2 
[INFO ] 2024-04-05 19:09:47.370 - [任务 53(100)][TargetMysql] - Node TargetMysql[3cc4073e-d4de-4eb3-94c9-278a1e3308a5] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 19:09:47.374 - [任务 53(100)][777cb382-4ebf-4d01-addc-d736baadbcc5] - Node 777cb382-4ebf-4d01-addc-d736baadbcc5[777cb382-4ebf-4d01-addc-d736baadbcc5] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 19:09:47.374 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 19:09:47.583 - [任务 53(100)][TargetMysql] - Node TargetMysql[3cc4073e-d4de-4eb3-94c9-278a1e3308a5] running status set to false 
[INFO ] 2024-04-05 19:09:47.593 - [任务 53(100)][TargetMysql] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-3cc4073e-d4de-4eb3-94c9-278a1e3308a5 
[INFO ] 2024-04-05 19:09:47.593 - [任务 53(100)][TargetMysql] - PDK connector node released: HazelcastSampleSourcePdkDataNode-3cc4073e-d4de-4eb3-94c9-278a1e3308a5 
[INFO ] 2024-04-05 19:09:47.594 - [任务 53(100)][TargetMysql] - Node TargetMysql[3cc4073e-d4de-4eb3-94c9-278a1e3308a5] schema data cleaned 
[INFO ] 2024-04-05 19:09:47.594 - [任务 53(100)][TargetMysql] - Node TargetMysql[3cc4073e-d4de-4eb3-94c9-278a1e3308a5] monitor closed 
[INFO ] 2024-04-05 19:09:47.805 - [任务 53(100)][TargetMysql] - Node TargetMysql[3cc4073e-d4de-4eb3-94c9-278a1e3308a5] close complete, cost 20 ms 
[INFO ] 2024-04-05 19:09:47.807 - [任务 53(100)][777cb382-4ebf-4d01-addc-d736baadbcc5] - Node 777cb382-4ebf-4d01-addc-d736baadbcc5[777cb382-4ebf-4d01-addc-d736baadbcc5] running status set to false 
[INFO ] 2024-04-05 19:09:47.807 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] running status set to false 
[INFO ] 2024-04-05 19:09:47.807 - [任务 53(100)][777cb382-4ebf-4d01-addc-d736baadbcc5] - Node 777cb382-4ebf-4d01-addc-d736baadbcc5[777cb382-4ebf-4d01-addc-d736baadbcc5] schema data cleaned 
[INFO ] 2024-04-05 19:09:47.818 - [任务 53(100)][777cb382-4ebf-4d01-addc-d736baadbcc5] - Node 777cb382-4ebf-4d01-addc-d736baadbcc5[777cb382-4ebf-4d01-addc-d736baadbcc5] monitor closed 
[INFO ] 2024-04-05 19:09:47.833 - [任务 53(100)][777cb382-4ebf-4d01-addc-d736baadbcc5] - Node 777cb382-4ebf-4d01-addc-d736baadbcc5[777cb382-4ebf-4d01-addc-d736baadbcc5] close complete, cost 27 ms 
[INFO ] 2024-04-05 19:09:47.833 - [任务 53(100)][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-63ff90d9-9633-453a-a1d4-9ff5e1a5f15a 
[INFO ] 2024-04-05 19:09:47.833 - [任务 53(100)][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-63ff90d9-9633-453a-a1d4-9ff5e1a5f15a 
[INFO ] 2024-04-05 19:09:47.834 - [任务 53(100)][增强JS] - [ScriptExecutorsManager-660fb16d7083fc5a86bf7652-12ea0e72-afe5-406b-b4fb-a1e19af36bb9-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-04-05 19:09:47.838 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] schema data cleaned 
[INFO ] 2024-04-05 19:09:47.840 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] monitor closed 
[INFO ] 2024-04-05 19:09:47.840 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] close complete, cost 52 ms 
[INFO ] 2024-04-05 19:09:48.047 - [任务 53(100)] - load MigrateJsResultVos task 660fb16d7083fc5a86bf7652-777cb382-4ebf-4d01-addc-d736baadbcc5 complete, cost 524ms 
[INFO ] 2024-04-05 19:10:00.707 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] start preload schema,table counts: 0 
[INFO ] 2024-04-05 19:10:00.708 - [任务 53(100)][7a8323b4-00e4-41c3-8ccb-7dc4b30b349e] - Node 7a8323b4-00e4-41c3-8ccb-7dc4b30b349e[7a8323b4-00e4-41c3-8ccb-7dc4b30b349e] start preload schema,table counts: 0 
[INFO ] 2024-04-05 19:10:00.711 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] preload schema finished, cost 0 ms 
[INFO ] 2024-04-05 19:10:00.712 - [任务 53(100)][7a8323b4-00e4-41c3-8ccb-7dc4b30b349e] - Node 7a8323b4-00e4-41c3-8ccb-7dc4b30b349e[7a8323b4-00e4-41c3-8ccb-7dc4b30b349e] preload schema finished, cost 0 ms 
[WARN ] 2024-04-05 19:10:00.751 - [任务 53(100)][增强JS] - The source could not build the executor, please check 
[INFO ] 2024-04-05 19:10:00.752 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] running status set to false 
[INFO ] 2024-04-05 19:10:00.755 - [任务 53(100)][7a8323b4-00e4-41c3-8ccb-7dc4b30b349e] - Node 7a8323b4-00e4-41c3-8ccb-7dc4b30b349e[7a8323b4-00e4-41c3-8ccb-7dc4b30b349e] running status set to false 
[INFO ] 2024-04-05 19:10:00.755 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] schema data cleaned 
[INFO ] 2024-04-05 19:10:00.757 - [任务 53(100)][7a8323b4-00e4-41c3-8ccb-7dc4b30b349e] - Node 7a8323b4-00e4-41c3-8ccb-7dc4b30b349e[7a8323b4-00e4-41c3-8ccb-7dc4b30b349e] schema data cleaned 
[INFO ] 2024-04-05 19:10:00.757 - [任务 53(100)][7a8323b4-00e4-41c3-8ccb-7dc4b30b349e] - Node 7a8323b4-00e4-41c3-8ccb-7dc4b30b349e[7a8323b4-00e4-41c3-8ccb-7dc4b30b349e] monitor closed 
[INFO ] 2024-04-05 19:10:00.757 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] monitor closed 
[INFO ] 2024-04-05 19:10:00.759 - [任务 53(100)][7a8323b4-00e4-41c3-8ccb-7dc4b30b349e] - Node 7a8323b4-00e4-41c3-8ccb-7dc4b30b349e[7a8323b4-00e4-41c3-8ccb-7dc4b30b349e] close complete, cost 8 ms 
[INFO ] 2024-04-05 19:10:00.763 - [任务 53(100)][增强JS] - Node 增强JS[12ea0e72-afe5-406b-b4fb-a1e19af36bb9] close complete, cost 10 ms 
[INFO ] 2024-04-05 19:10:00.763 - [任务 53(100)] - load MigrateJsResultVos task 660fb16d7083fc5a86bf7652-7a8323b4-00e4-41c3-8ccb-7dc4b30b349e complete, cost 88ms 
