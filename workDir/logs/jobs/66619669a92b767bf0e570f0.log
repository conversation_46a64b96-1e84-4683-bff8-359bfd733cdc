[INFO ] 2024-06-11 16:37:58.262 - [任务 4] - Task initialization... 
[INFO ] 2024-06-11 16:37:58.294 - [任务 4] - Start task milestones: 66619669a92b767bf0e570f0(任务 4) 
[INFO ] 2024-06-11 16:37:58.358 - [任务 4] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-11 16:37:58.539 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-11 16:37:58.908 - [任务 4][TESTLOG] - Node TESTLOG[828038af-a9c5-4e20-95d7-f1ae30f77abb] start preload schema,table counts: 1 
[INFO ] 2024-06-11 16:37:58.910 - [任务 4][TESTLOB] - Node TESTLOB[9d59fec1-b675-4c83-bf7b-e8d09e277838] start preload schema,table counts: 1 
[INFO ] 2024-06-11 16:38:01.834 - [任务 4][TESTLOB] - Node TESTLOB[9d59fec1-b675-4c83-bf7b-e8d09e277838] preload schema finished, cost 48 ms 
[INFO ] 2024-06-11 16:38:01.837 - [任务 4][TESTLOG] - Node TESTLOG[828038af-a9c5-4e20-95d7-f1ae30f77abb] preload schema finished, cost 50 ms 
[INFO ] 2024-06-11 16:38:31.100 - [任务 4][TESTLOG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-11 16:38:35.996 - [任务 4][TESTLOG] - The table TESTLOG has already exist. 
[INFO ] 2024-06-11 16:38:46.137 - [任务 4][TESTLOB] - Source node "TESTLOB" read batch size: 100 
[INFO ] 2024-06-11 16:38:46.140 - [任务 4][TESTLOB] - Source node "TESTLOB" event queue capacity: 200 
[INFO ] 2024-06-11 16:38:46.142 - [任务 4][TESTLOB] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-11 16:38:50.644 - [任务 4][TESTLOB] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":56127453,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-11 16:38:50.822 - [任务 4][TESTLOB] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-11 16:38:50.824 - [任务 4][TESTLOB] - Initial sync started 
[INFO ] 2024-06-11 16:38:50.826 - [任务 4][TESTLOB] - Starting batch read, table name: TESTLOB, offset: null 
[INFO ] 2024-06-11 16:38:50.827 - [任务 4][TESTLOB] - Table TESTLOB is going to be initial synced 
[INFO ] 2024-06-11 16:38:51.365 - [任务 4][TESTLOB] - Table [TESTLOB] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-11 16:38:51.370 - [任务 4][TESTLOB] - Query table 'TESTLOB' counts: 2 
[INFO ] 2024-06-11 16:38:51.371 - [任务 4][TESTLOB] - Initial sync completed 
[INFO ] 2024-06-11 16:38:51.373 - [任务 4][TESTLOB] - Incremental sync starting... 
[INFO ] 2024-06-11 16:38:51.374 - [任务 4][TESTLOB] - Initial sync completed 
[INFO ] 2024-06-11 16:38:51.376 - [任务 4][TESTLOB] - Starting stream read, table list: [TESTLOB], offset: {"sortString":null,"offsetValue":null,"lastScn":56127453,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-11 16:39:03.882 - [任务 4][TESTLOB] - total start mining scn: 56127453 
[INFO ] 2024-06-11 16:39:05.317 - [任务 4][TESTLOB] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-06-11 16:39:08.560 - [任务 4] - Stop task milestones: 66619669a92b767bf0e570f0(任务 4)  
[INFO ] 2024-06-11 16:39:08.888 - [任务 4][TESTLOB] - Node TESTLOB[9d59fec1-b675-4c83-bf7b-e8d09e277838] running status set to false 
[INFO ] 2024-06-11 16:39:08.904 - [任务 4][TESTLOB] - Log Miner is shutting down... 
[INFO ] 2024-06-11 16:39:08.906 - [任务 4][TESTLOB] - Log Miner has been closed! 
[ERROR] 2024-06-11 16:39:09.090 - [任务 4][TESTLOB] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:413)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-06-11 16:39:09.092 - [任务 4][TESTLOB] - PDK connector node stopped: HazelcastSourcePdkDataNode-9d59fec1-b675-4c83-bf7b-e8d09e277838 
[INFO ] 2024-06-11 16:39:09.094 - [任务 4][TESTLOB] - PDK connector node released: HazelcastSourcePdkDataNode-9d59fec1-b675-4c83-bf7b-e8d09e277838 
[INFO ] 2024-06-11 16:39:09.095 - [任务 4][TESTLOB] - Node TESTLOB[9d59fec1-b675-4c83-bf7b-e8d09e277838] schema data cleaned 
[INFO ] 2024-06-11 16:39:09.097 - [任务 4][TESTLOB] - Node TESTLOB[9d59fec1-b675-4c83-bf7b-e8d09e277838] monitor closed 
[INFO ] 2024-06-11 16:39:09.099 - [任务 4][TESTLOB] - Node TESTLOB[9d59fec1-b675-4c83-bf7b-e8d09e277838] close complete, cost 210 ms 
[INFO ] 2024-06-11 16:39:09.201 - [任务 4][TESTLOG] - Node TESTLOG[828038af-a9c5-4e20-95d7-f1ae30f77abb] running status set to false 
[INFO ] 2024-06-11 16:39:09.203 - [任务 4][TESTLOG] - PDK connector node stopped: HazelcastTargetPdkDataNode-828038af-a9c5-4e20-95d7-f1ae30f77abb 
[INFO ] 2024-06-11 16:39:09.204 - [任务 4][TESTLOG] - PDK connector node released: HazelcastTargetPdkDataNode-828038af-a9c5-4e20-95d7-f1ae30f77abb 
[INFO ] 2024-06-11 16:39:09.206 - [任务 4][TESTLOG] - Node TESTLOG[828038af-a9c5-4e20-95d7-f1ae30f77abb] schema data cleaned 
[INFO ] 2024-06-11 16:39:09.209 - [任务 4][TESTLOG] - Node TESTLOG[828038af-a9c5-4e20-95d7-f1ae30f77abb] monitor closed 
[INFO ] 2024-06-11 16:39:09.413 - [任务 4][TESTLOG] - Node TESTLOG[828038af-a9c5-4e20-95d7-f1ae30f77abb] close complete, cost 110 ms 
[INFO ] 2024-06-11 16:39:11.029 - [任务 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-11 16:39:11.031 - [任务 4] - Stopped task aspect(s) 
[INFO ] 2024-06-11 16:39:11.032 - [任务 4] - Snapshot order controller have been removed 
[INFO ] 2024-06-11 16:39:11.063 - [任务 4] - Remove memory task client succeed, task: 任务 4[66619669a92b767bf0e570f0] 
[INFO ] 2024-06-11 16:39:11.270 - [任务 4] - Destroy memory task client cache succeed, task: 任务 4[66619669a92b767bf0e570f0] 
