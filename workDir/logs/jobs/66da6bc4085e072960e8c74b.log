[INFO ] 2024-09-06 10:41:32.971 - [任务 13(100)][16fe0a75-ed97-4d16-a091-c1cb4eea8634] - Node 16fe0a75-ed97-4d16-a091-c1cb4eea8634[16fe0a75-ed97-4d16-a091-c1cb4eea8634] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:41:32.972 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:41:32.972 - [任务 13(100)][16fe0a75-ed97-4d16-a091-c1cb4eea8634] - Node 16fe0a75-ed97-4d16-a091-c1cb4eea8634[16fe0a75-ed97-4d16-a091-c1cb4eea8634] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:41:32.972 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:41:32.972 - [任务 13(100)][增强JS] - Node migrate_js_processor(增强JS: 7cebc975-e148-402f-87ec-58caeead6bea) enable batch process 
[INFO ] 2024-09-06 10:41:32.978 - [任务 13(100)][16fe0a75-ed97-4d16-a091-c1cb4eea8634] - Exception skipping - The current exception does not match the skip exception strategy, message: HazelcastSchemaTargetNode only allows one predecessor node 
[INFO ] 2024-09-06 10:41:32.978 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] running status set to false 
[INFO ] 2024-09-06 10:41:32.980 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] schema data cleaned 
[INFO ] 2024-09-06 10:41:32.980 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] monitor closed 
[INFO ] 2024-09-06 10:41:33.009 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] close complete, cost 4 ms 
[ERROR] 2024-09-06 10:41:33.012 - [任务 13(100)][16fe0a75-ed97-4d16-a091-c1cb4eea8634] - java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node <-- Error Message -->
java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node
	io.tapdata.flow.engine.V2.node.hazelcast.data.HazelcastSchemaTargetNode.doInit(HazelcastSchemaTargetNode.java:112)
	io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	...

<-- Full Stack Trace -->
java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:573)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:566)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:220)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node
	at io.tapdata.flow.engine.V2.node.hazelcast.data.HazelcastSchemaTargetNode.doInit(HazelcastSchemaTargetNode.java:112)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	... 12 more

[INFO ] 2024-09-06 10:41:35.130 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:41:35.130 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:41:35.130 - [任务 13(100)][4e63d148-99b1-4502-8947-1d3f4fb265b1] - Node 4e63d148-99b1-4502-8947-1d3f4fb265b1[4e63d148-99b1-4502-8947-1d3f4fb265b1] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:41:35.131 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:41:35.134 - [任务 13(100)][4e63d148-99b1-4502-8947-1d3f4fb265b1] - Node 4e63d148-99b1-4502-8947-1d3f4fb265b1[4e63d148-99b1-4502-8947-1d3f4fb265b1] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:41:35.134 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:41:35.134 - [任务 13(100)][增强JS] - Node migrate_js_processor(增强JS: 7cebc975-e148-402f-87ec-58caeead6bea) enable batch process 
[INFO ] 2024-09-06 10:41:35.546 - [任务 13(100)][16fe0a75-ed97-4d16-a091-c1cb4eea8634] - Node 16fe0a75-ed97-4d16-a091-c1cb4eea8634[16fe0a75-ed97-4d16-a091-c1cb4eea8634] running status set to false 
[INFO ] 2024-09-06 10:41:35.546 - [任务 13(100)][16fe0a75-ed97-4d16-a091-c1cb4eea8634] - Node 16fe0a75-ed97-4d16-a091-c1cb4eea8634[16fe0a75-ed97-4d16-a091-c1cb4eea8634] schema data cleaned 
[INFO ] 2024-09-06 10:41:35.546 - [任务 13(100)][16fe0a75-ed97-4d16-a091-c1cb4eea8634] - Node 16fe0a75-ed97-4d16-a091-c1cb4eea8634[16fe0a75-ed97-4d16-a091-c1cb4eea8634] monitor closed 
[INFO ] 2024-09-06 10:41:35.547 - [任务 13(100)][16fe0a75-ed97-4d16-a091-c1cb4eea8634] - Node 16fe0a75-ed97-4d16-a091-c1cb4eea8634[16fe0a75-ed97-4d16-a091-c1cb4eea8634] close complete, cost 6 ms 
[INFO ] 2024-09-06 10:41:35.552 - [任务 13(100)] - Closed task monitor(s)
null 
[INFO ] 2024-09-06 10:41:35.553 - [任务 13(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-06 10:41:35.758 - [任务 13(100)] - Stopped task aspect(s) 
[INFO ] 2024-09-06 10:41:36.443 - [任务 13(100)][c1197897-55c9-4d98-90fd-3aee5d4224b4] - Node c1197897-55c9-4d98-90fd-3aee5d4224b4[c1197897-55c9-4d98-90fd-3aee5d4224b4] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:41:36.443 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:41:36.448 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:41:36.449 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:41:36.450 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:41:36.450 - [任务 13(100)][c1197897-55c9-4d98-90fd-3aee5d4224b4] - Node c1197897-55c9-4d98-90fd-3aee5d4224b4[c1197897-55c9-4d98-90fd-3aee5d4224b4] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:41:36.596 - [任务 13(100)][增强JS] - Node migrate_js_processor(增强JS: 7cebc975-e148-402f-87ec-58caeead6bea) enable batch process 
[INFO ] 2024-09-06 10:41:36.597 - [任务 13(100)][Oracle] - Exception skipping - The current exception does not match the skip exception strategy, message: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[ERROR] 2024-09-06 10:41:36.617 - [任务 13(100)][Oracle] - start source runner failed: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b
	io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:573)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:191)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:61)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:207)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.initNode(HazelcastSampleSourcePdkDataNode.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:85)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b
	at io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:221)
	... 17 more

[WARN ] 2024-09-06 10:41:36.623 - [任务 13(100)][Oracle] - Source table is empty, trying to mock data 
[INFO ] 2024-09-06 10:41:36.829 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] running status set to false 
[INFO ] 2024-09-06 10:41:37.104 - [任务 13(100)][Oracle] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:41:37.104 - [任务 13(100)][Oracle] - PDK connector node released: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:41:37.105 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] schema data cleaned 
[INFO ] 2024-09-06 10:41:37.105 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] monitor closed 
[INFO ] 2024-09-06 10:41:37.105 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] close complete, cost 483 ms 
[INFO ] 2024-09-06 10:41:37.888 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] running status set to false 
[INFO ] 2024-09-06 10:41:37.888 - [任务 13(100)][4e63d148-99b1-4502-8947-1d3f4fb265b1] - Node 4e63d148-99b1-4502-8947-1d3f4fb265b1[4e63d148-99b1-4502-8947-1d3f4fb265b1] running status set to false 
[INFO ] 2024-09-06 10:41:37.888 - [任务 13(100)][4e63d148-99b1-4502-8947-1d3f4fb265b1] - Node 4e63d148-99b1-4502-8947-1d3f4fb265b1[4e63d148-99b1-4502-8947-1d3f4fb265b1] schema data cleaned 
[INFO ] 2024-09-06 10:41:37.888 - [任务 13(100)][4e63d148-99b1-4502-8947-1d3f4fb265b1] - Node 4e63d148-99b1-4502-8947-1d3f4fb265b1[4e63d148-99b1-4502-8947-1d3f4fb265b1] monitor closed 
[INFO ] 2024-09-06 10:41:37.888 - [任务 13(100)][4e63d148-99b1-4502-8947-1d3f4fb265b1] - Node 4e63d148-99b1-4502-8947-1d3f4fb265b1[4e63d148-99b1-4502-8947-1d3f4fb265b1] close complete, cost 2 ms 
[INFO ] 2024-09-06 10:41:38.098 - [任务 13(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Oracle-2710086a-d0cd-485f-a52c-a2264dcb95b8 
[INFO ] 2024-09-06 10:41:38.098 - [任务 13(100)][增强JS] - PDK connector node released: ScriptExecutor-Oracle-2710086a-d0cd-485f-a52c-a2264dcb95b8 
[INFO ] 2024-09-06 10:41:38.098 - [任务 13(100)][增强JS] - [ScriptExecutorsManager-66da6bc4085e072960e8c74b-7cebc975-e148-402f-87ec-58caeead6bea-66d688a8a40a37725da9ce03] schema data cleaned 
[INFO ] 2024-09-06 10:41:38.100 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] schema data cleaned 
[INFO ] 2024-09-06 10:41:38.100 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] monitor closed 
[INFO ] 2024-09-06 10:41:38.101 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] close complete, cost 224 ms 
[INFO ] 2024-09-06 10:41:38.106 - [任务 13(100)] - Closed task monitor(s)
null 
[INFO ] 2024-09-06 10:41:38.106 - [任务 13(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-06 10:41:38.307 - [任务 13(100)] - Stopped task aspect(s) 
[INFO ] 2024-09-06 10:41:39.158 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] running status set to false 
[INFO ] 2024-09-06 10:41:39.158 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] running status set to false 
[INFO ] 2024-09-06 10:41:39.158 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] schema data cleaned 
[INFO ] 2024-09-06 10:41:39.159 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] monitor closed 
[INFO ] 2024-09-06 10:41:39.160 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] close complete, cost 2 ms 
[INFO ] 2024-09-06 10:41:39.160 - [任务 13(100)][Oracle] - PDK connector node stopped: null 
[INFO ] 2024-09-06 10:41:39.160 - [任务 13(100)][Oracle] - PDK connector node released: null 
[INFO ] 2024-09-06 10:41:39.161 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] schema data cleaned 
[INFO ] 2024-09-06 10:41:39.161 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] monitor closed 
[INFO ] 2024-09-06 10:41:39.161 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] close complete, cost 9 ms 
[INFO ] 2024-09-06 10:41:39.165 - [任务 13(100)][c1197897-55c9-4d98-90fd-3aee5d4224b4] - Node c1197897-55c9-4d98-90fd-3aee5d4224b4[c1197897-55c9-4d98-90fd-3aee5d4224b4] running status set to false 
[INFO ] 2024-09-06 10:41:39.166 - [任务 13(100)][c1197897-55c9-4d98-90fd-3aee5d4224b4] - Node c1197897-55c9-4d98-90fd-3aee5d4224b4[c1197897-55c9-4d98-90fd-3aee5d4224b4] schema data cleaned 
[INFO ] 2024-09-06 10:41:39.166 - [任务 13(100)][c1197897-55c9-4d98-90fd-3aee5d4224b4] - Node c1197897-55c9-4d98-90fd-3aee5d4224b4[c1197897-55c9-4d98-90fd-3aee5d4224b4] monitor closed 
[INFO ] 2024-09-06 10:41:39.166 - [任务 13(100)][c1197897-55c9-4d98-90fd-3aee5d4224b4] - Node c1197897-55c9-4d98-90fd-3aee5d4224b4[c1197897-55c9-4d98-90fd-3aee5d4224b4] close complete, cost 1 ms 
[INFO ] 2024-09-06 10:41:39.167 - [任务 13(100)] - Closed task monitor(s)
null 
[INFO ] 2024-09-06 10:41:39.168 - [任务 13(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-06 10:41:39.168 - [任务 13(100)] - Stopped task aspect(s) 
[INFO ] 2024-09-06 10:41:39.258 - [任务 13(100)][60518e40-7c41-4c3f-917f-4c5f6a082587] - Node 60518e40-7c41-4c3f-917f-4c5f6a082587[60518e40-7c41-4c3f-917f-4c5f6a082587] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:41:39.258 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:41:39.258 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:41:39.259 - [任务 13(100)][60518e40-7c41-4c3f-917f-4c5f6a082587] - Node 60518e40-7c41-4c3f-917f-4c5f6a082587[60518e40-7c41-4c3f-917f-4c5f6a082587] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:41:39.259 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:41:39.259 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] preload schema finished, cost 1 ms 
[INFO ] 2024-09-06 10:41:39.260 - [任务 13(100)][增强JS] - Node migrate_js_processor(增强JS: 7cebc975-e148-402f-87ec-58caeead6bea) enable batch process 
[WARN ] 2024-09-06 10:41:40.214 - [任务 13(100)][Oracle] - Source table is empty, trying to mock data 
[INFO ] 2024-09-06 10:41:40.215 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] running status set to false 
[INFO ] 2024-09-06 10:41:40.537 - [任务 13(100)][Oracle] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:41:40.538 - [任务 13(100)][Oracle] - PDK connector node released: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:41:40.538 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] schema data cleaned 
[INFO ] 2024-09-06 10:41:40.538 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] monitor closed 
[INFO ] 2024-09-06 10:41:40.745 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] close complete, cost 327 ms 
[INFO ] 2024-09-06 10:41:40.751 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] running status set to false 
[INFO ] 2024-09-06 10:41:40.752 - [任务 13(100)][60518e40-7c41-4c3f-917f-4c5f6a082587] - Node 60518e40-7c41-4c3f-917f-4c5f6a082587[60518e40-7c41-4c3f-917f-4c5f6a082587] running status set to false 
[INFO ] 2024-09-06 10:41:40.752 - [任务 13(100)][60518e40-7c41-4c3f-917f-4c5f6a082587] - Node 60518e40-7c41-4c3f-917f-4c5f6a082587[60518e40-7c41-4c3f-917f-4c5f6a082587] schema data cleaned 
[INFO ] 2024-09-06 10:41:40.753 - [任务 13(100)][60518e40-7c41-4c3f-917f-4c5f6a082587] - Node 60518e40-7c41-4c3f-917f-4c5f6a082587[60518e40-7c41-4c3f-917f-4c5f6a082587] monitor closed 
[INFO ] 2024-09-06 10:41:40.753 - [任务 13(100)][60518e40-7c41-4c3f-917f-4c5f6a082587] - Node 60518e40-7c41-4c3f-917f-4c5f6a082587[60518e40-7c41-4c3f-917f-4c5f6a082587] close complete, cost 5 ms 
[INFO ] 2024-09-06 10:41:40.795 - [任务 13(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Oracle-eec2b99b-901f-408e-9cb8-ffd63aaa05f0 
[INFO ] 2024-09-06 10:41:40.796 - [任务 13(100)][增强JS] - PDK connector node released: ScriptExecutor-Oracle-eec2b99b-901f-408e-9cb8-ffd63aaa05f0 
[INFO ] 2024-09-06 10:41:40.796 - [任务 13(100)][增强JS] - [ScriptExecutorsManager-66da6bc4085e072960e8c74b-7cebc975-e148-402f-87ec-58caeead6bea-66d688a8a40a37725da9ce03] schema data cleaned 
[INFO ] 2024-09-06 10:41:40.796 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] schema data cleaned 
[INFO ] 2024-09-06 10:41:40.796 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] monitor closed 
[INFO ] 2024-09-06 10:41:40.796 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] close complete, cost 48 ms 
[INFO ] 2024-09-06 10:41:40.798 - [任务 13(100)] - Closed task monitor(s)
null 
[INFO ] 2024-09-06 10:41:40.798 - [任务 13(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-06 10:41:40.799 - [任务 13(100)] - Stopped task aspect(s) 
[INFO ] 2024-09-06 10:41:56.198 - [任务 13(100)][043170a0-147e-4391-ba4f-5ed449523a25] - Node 043170a0-147e-4391-ba4f-5ed449523a25[043170a0-147e-4391-ba4f-5ed449523a25] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:41:56.199 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:41:56.200 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:41:56.203 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:41:56.203 - [任务 13(100)][043170a0-147e-4391-ba4f-5ed449523a25] - Node 043170a0-147e-4391-ba4f-5ed449523a25[043170a0-147e-4391-ba4f-5ed449523a25] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:41:56.203 - [任务 13(100)][增强JS] - Node migrate_js_processor(增强JS: 7cebc975-e148-402f-87ec-58caeead6bea) enable batch process 
[INFO ] 2024-09-06 10:41:56.203 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:41:56.231 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:41:56.231 - [任务 13(100)][d9219fcd-1165-4c4e-955b-9b52141db9a3] - Node d9219fcd-1165-4c4e-955b-9b52141db9a3[d9219fcd-1165-4c4e-955b-9b52141db9a3] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:41:56.231 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:41:56.232 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:41:56.234 - [任务 13(100)][d9219fcd-1165-4c4e-955b-9b52141db9a3] - Node d9219fcd-1165-4c4e-955b-9b52141db9a3[d9219fcd-1165-4c4e-955b-9b52141db9a3] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:41:56.235 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:41:56.235 - [任务 13(100)][增强JS] - Node migrate_js_processor(增强JS: 7cebc975-e148-402f-87ec-58caeead6bea) enable batch process 
[INFO ] 2024-09-06 10:41:56.277 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:41:56.277 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:41:56.280 - [任务 13(100)][c308f6ea-d138-4cf8-9715-a99394337baf] - Node c308f6ea-d138-4cf8-9715-a99394337baf[c308f6ea-d138-4cf8-9715-a99394337baf] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:41:56.280 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:41:56.280 - [任务 13(100)][c308f6ea-d138-4cf8-9715-a99394337baf] - Node c308f6ea-d138-4cf8-9715-a99394337baf[c308f6ea-d138-4cf8-9715-a99394337baf] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:41:56.280 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:41:56.472 - [任务 13(100)][增强JS] - Node migrate_js_processor(增强JS: 7cebc975-e148-402f-87ec-58caeead6bea) enable batch process 
[INFO ] 2024-09-06 10:41:56.472 - [任务 13(100)][Oracle] - Exception skipping - The current exception does not match the skip exception strategy, message: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[ERROR] 2024-09-06 10:41:56.600 - [任务 13(100)][Oracle] - start source runner failed: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b
	io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:573)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:191)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:61)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:207)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.initNode(HazelcastSampleSourcePdkDataNode.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:85)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b
	at io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:221)
	... 17 more

[INFO ] 2024-09-06 10:41:56.600 - [任务 13(100)][Oracle] - Exception skipping - The current exception does not match the skip exception strategy, message: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[ERROR] 2024-09-06 10:41:56.804 - [任务 13(100)][Oracle] - start source runner failed: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b
	io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:573)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:191)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:61)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:207)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.initNode(HazelcastSampleSourcePdkDataNode.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:85)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b
	at io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:221)
	... 17 more

[WARN ] 2024-09-06 10:41:57.061 - [任务 13(100)][Oracle] - Source table is empty, trying to mock data 
[INFO ] 2024-09-06 10:41:57.266 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] running status set to false 
[INFO ] 2024-09-06 10:41:57.320 - [任务 13(100)][Oracle] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:41:57.320 - [任务 13(100)][Oracle] - PDK connector node released: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:41:57.320 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] schema data cleaned 
[INFO ] 2024-09-06 10:41:57.320 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] monitor closed 
[INFO ] 2024-09-06 10:41:57.525 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] close complete, cost 256 ms 
[INFO ] 2024-09-06 10:41:57.696 - [任务 13(100)][增强JS] - record:{TEST2=sample string, hdip_dml_typem=i, hdip_source_time=0, ID=1.0, NAME=sample string, TEST1=sample string} 
[INFO ] 2024-09-06 10:41:57.700 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] running status set to false 
[INFO ] 2024-09-06 10:41:57.701 - [任务 13(100)][043170a0-147e-4391-ba4f-5ed449523a25] - Node 043170a0-147e-4391-ba4f-5ed449523a25[043170a0-147e-4391-ba4f-5ed449523a25] running status set to false 
[INFO ] 2024-09-06 10:41:57.702 - [任务 13(100)][043170a0-147e-4391-ba4f-5ed449523a25] - Node 043170a0-147e-4391-ba4f-5ed449523a25[043170a0-147e-4391-ba4f-5ed449523a25] schema data cleaned 
[INFO ] 2024-09-06 10:41:57.702 - [任务 13(100)][043170a0-147e-4391-ba4f-5ed449523a25] - Node 043170a0-147e-4391-ba4f-5ed449523a25[043170a0-147e-4391-ba4f-5ed449523a25] monitor closed 
[INFO ] 2024-09-06 10:41:57.911 - [任务 13(100)][043170a0-147e-4391-ba4f-5ed449523a25] - Node 043170a0-147e-4391-ba4f-5ed449523a25[043170a0-147e-4391-ba4f-5ed449523a25] close complete, cost 6 ms 
[INFO ] 2024-09-06 10:41:57.947 - [任务 13(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Oracle-7e51bf83-8533-4adb-95ba-93667ecec7d8 
[INFO ] 2024-09-06 10:41:57.947 - [任务 13(100)][增强JS] - PDK connector node released: ScriptExecutor-Oracle-7e51bf83-8533-4adb-95ba-93667ecec7d8 
[INFO ] 2024-09-06 10:41:57.948 - [任务 13(100)][增强JS] - [ScriptExecutorsManager-66da6bc4085e072960e8c74b-7cebc975-e148-402f-87ec-58caeead6bea-66d688a8a40a37725da9ce03] schema data cleaned 
[INFO ] 2024-09-06 10:41:57.951 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] schema data cleaned 
[INFO ] 2024-09-06 10:41:57.952 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] monitor closed 
[INFO ] 2024-09-06 10:41:57.952 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] close complete, cost 259 ms 
[INFO ] 2024-09-06 10:41:57.959 - [任务 13(100)] - Closed task monitor(s)
null 
[INFO ] 2024-09-06 10:41:57.959 - [任务 13(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-06 10:41:57.960 - [任务 13(100)] - Stopped task aspect(s) 
[INFO ] 2024-09-06 10:41:59.019 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] running status set to false 
[INFO ] 2024-09-06 10:41:59.020 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] running status set to false 
[INFO ] 2024-09-06 10:41:59.021 - [任务 13(100)][c308f6ea-d138-4cf8-9715-a99394337baf] - Node c308f6ea-d138-4cf8-9715-a99394337baf[c308f6ea-d138-4cf8-9715-a99394337baf] running status set to false 
[INFO ] 2024-09-06 10:41:59.028 - [任务 13(100)][Oracle] - PDK connector node stopped: null 
[INFO ] 2024-09-06 10:41:59.028 - [任务 13(100)][c308f6ea-d138-4cf8-9715-a99394337baf] - Node c308f6ea-d138-4cf8-9715-a99394337baf[c308f6ea-d138-4cf8-9715-a99394337baf] schema data cleaned 
[INFO ] 2024-09-06 10:41:59.038 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] schema data cleaned 
[INFO ] 2024-09-06 10:41:59.038 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] monitor closed 
[INFO ] 2024-09-06 10:41:59.039 - [任务 13(100)][Oracle] - PDK connector node released: null 
[INFO ] 2024-09-06 10:41:59.046 - [任务 13(100)][c308f6ea-d138-4cf8-9715-a99394337baf] - Node c308f6ea-d138-4cf8-9715-a99394337baf[c308f6ea-d138-4cf8-9715-a99394337baf] monitor closed 
[INFO ] 2024-09-06 10:41:59.062 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] close complete, cost 26 ms 
[INFO ] 2024-09-06 10:41:59.063 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] schema data cleaned 
[INFO ] 2024-09-06 10:41:59.063 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] monitor closed 
[INFO ] 2024-09-06 10:41:59.063 - [任务 13(100)][c308f6ea-d138-4cf8-9715-a99394337baf] - Node c308f6ea-d138-4cf8-9715-a99394337baf[c308f6ea-d138-4cf8-9715-a99394337baf] close complete, cost 26 ms 
[INFO ] 2024-09-06 10:41:59.063 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] close complete, cost 48 ms 
[INFO ] 2024-09-06 10:41:59.063 - [任务 13(100)] - Closed task monitor(s)
null 
[INFO ] 2024-09-06 10:41:59.063 - [任务 13(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-06 10:41:59.063 - [任务 13(100)] - Stopped task aspect(s) 
[INFO ] 2024-09-06 10:41:59.142 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] running status set to false 
[INFO ] 2024-09-06 10:41:59.145 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] running status set to false 
[INFO ] 2024-09-06 10:41:59.145 - [任务 13(100)][d9219fcd-1165-4c4e-955b-9b52141db9a3] - Node d9219fcd-1165-4c4e-955b-9b52141db9a3[d9219fcd-1165-4c4e-955b-9b52141db9a3] running status set to false 
[INFO ] 2024-09-06 10:41:59.145 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] schema data cleaned 
[INFO ] 2024-09-06 10:41:59.145 - [任务 13(100)][d9219fcd-1165-4c4e-955b-9b52141db9a3] - Node d9219fcd-1165-4c4e-955b-9b52141db9a3[d9219fcd-1165-4c4e-955b-9b52141db9a3] schema data cleaned 
[INFO ] 2024-09-06 10:41:59.145 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] monitor closed 
[INFO ] 2024-09-06 10:41:59.145 - [任务 13(100)][d9219fcd-1165-4c4e-955b-9b52141db9a3] - Node d9219fcd-1165-4c4e-955b-9b52141db9a3[d9219fcd-1165-4c4e-955b-9b52141db9a3] monitor closed 
[INFO ] 2024-09-06 10:41:59.145 - [任务 13(100)][Oracle] - PDK connector node stopped: null 
[INFO ] 2024-09-06 10:41:59.145 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] close complete, cost 2 ms 
[INFO ] 2024-09-06 10:41:59.145 - [任务 13(100)][d9219fcd-1165-4c4e-955b-9b52141db9a3] - Node d9219fcd-1165-4c4e-955b-9b52141db9a3[d9219fcd-1165-4c4e-955b-9b52141db9a3] close complete, cost 2 ms 
[INFO ] 2024-09-06 10:41:59.145 - [任务 13(100)][Oracle] - PDK connector node released: null 
[INFO ] 2024-09-06 10:41:59.146 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] schema data cleaned 
[INFO ] 2024-09-06 10:41:59.146 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] monitor closed 
[INFO ] 2024-09-06 10:41:59.146 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] close complete, cost 9 ms 
[INFO ] 2024-09-06 10:41:59.146 - [任务 13(100)] - Closed task monitor(s)
null 
[INFO ] 2024-09-06 10:41:59.150 - [任务 13(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-06 10:41:59.150 - [任务 13(100)] - Stopped task aspect(s) 
[INFO ] 2024-09-06 10:42:13.099 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:42:13.099 - [任务 13(100)][b16eb90c-954f-445d-b7ae-6c61c84d933f] - Node b16eb90c-954f-445d-b7ae-6c61c84d933f[b16eb90c-954f-445d-b7ae-6c61c84d933f] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:42:13.099 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:42:13.099 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:42:13.099 - [任务 13(100)][b16eb90c-954f-445d-b7ae-6c61c84d933f] - Node b16eb90c-954f-445d-b7ae-6c61c84d933f[b16eb90c-954f-445d-b7ae-6c61c84d933f] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:42:13.099 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:42:13.300 - [任务 13(100)][增强JS] - Node migrate_js_processor(增强JS: 7cebc975-e148-402f-87ec-58caeead6bea) enable batch process 
[WARN ] 2024-09-06 10:42:13.952 - [任务 13(100)][Oracle] - Source table is empty, trying to mock data 
[INFO ] 2024-09-06 10:42:13.957 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] running status set to false 
[INFO ] 2024-09-06 10:42:14.569 - [任务 13(100)][Oracle] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:42:14.570 - [任务 13(100)][Oracle] - PDK connector node released: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:42:14.570 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] schema data cleaned 
[INFO ] 2024-09-06 10:42:14.570 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] monitor closed 
[INFO ] 2024-09-06 10:42:14.570 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] close complete, cost 617 ms 
[INFO ] 2024-09-06 10:42:14.909 - [任务 13(100)][增强JS] - record:{TEST2=sample string, hdip_dml_typem=i, hdip_source_time=0, ID=1.0, NAME=sample string, TEST1=sample string} 
[INFO ] 2024-09-06 10:42:14.911 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] running status set to false 
[INFO ] 2024-09-06 10:42:14.912 - [任务 13(100)][b16eb90c-954f-445d-b7ae-6c61c84d933f] - Node b16eb90c-954f-445d-b7ae-6c61c84d933f[b16eb90c-954f-445d-b7ae-6c61c84d933f] running status set to false 
[INFO ] 2024-09-06 10:42:14.912 - [任务 13(100)][b16eb90c-954f-445d-b7ae-6c61c84d933f] - Node b16eb90c-954f-445d-b7ae-6c61c84d933f[b16eb90c-954f-445d-b7ae-6c61c84d933f] schema data cleaned 
[INFO ] 2024-09-06 10:42:14.914 - [任务 13(100)][b16eb90c-954f-445d-b7ae-6c61c84d933f] - Node b16eb90c-954f-445d-b7ae-6c61c84d933f[b16eb90c-954f-445d-b7ae-6c61c84d933f] monitor closed 
[INFO ] 2024-09-06 10:42:14.914 - [任务 13(100)][b16eb90c-954f-445d-b7ae-6c61c84d933f] - Node b16eb90c-954f-445d-b7ae-6c61c84d933f[b16eb90c-954f-445d-b7ae-6c61c84d933f] close complete, cost 6 ms 
[INFO ] 2024-09-06 10:42:15.485 - [任务 13(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Oracle-7860e435-d06e-4dd4-bffb-b03ef8520c68 
[INFO ] 2024-09-06 10:42:15.485 - [任务 13(100)][增强JS] - PDK connector node released: ScriptExecutor-Oracle-7860e435-d06e-4dd4-bffb-b03ef8520c68 
[INFO ] 2024-09-06 10:42:15.486 - [任务 13(100)][增强JS] - [ScriptExecutorsManager-66da6bc4085e072960e8c74b-7cebc975-e148-402f-87ec-58caeead6bea-66d688a8a40a37725da9ce03] schema data cleaned 
[INFO ] 2024-09-06 10:42:15.488 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] schema data cleaned 
[INFO ] 2024-09-06 10:42:15.488 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] monitor closed 
[INFO ] 2024-09-06 10:42:15.489 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] close complete, cost 582 ms 
[INFO ] 2024-09-06 10:42:15.489 - [任务 13(100)] - Closed task monitor(s)
null 
[INFO ] 2024-09-06 10:42:15.489 - [任务 13(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-06 10:42:15.695 - [任务 13(100)] - Stopped task aspect(s) 
[INFO ] 2024-09-06 10:42:27.466 - [任务 13(100)][764217b0-33e5-4082-89cb-8feb47d3a04a] - Node 764217b0-33e5-4082-89cb-8feb47d3a04a[764217b0-33e5-4082-89cb-8feb47d3a04a] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:42:27.466 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:42:27.466 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:42:27.467 - [任务 13(100)][764217b0-33e5-4082-89cb-8feb47d3a04a] - Node 764217b0-33e5-4082-89cb-8feb47d3a04a[764217b0-33e5-4082-89cb-8feb47d3a04a] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:42:27.467 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:42:27.471 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:42:27.471 - [任务 13(100)][增强JS] - Node migrate_js_processor(增强JS: 7cebc975-e148-402f-87ec-58caeead6bea) enable batch process 
[WARN ] 2024-09-06 10:42:28.954 - [任务 13(100)][Oracle] - Source table is empty, trying to mock data 
[INFO ] 2024-09-06 10:42:29.160 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] running status set to false 
[INFO ] 2024-09-06 10:42:29.482 - [任务 13(100)][Oracle] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:42:29.482 - [任务 13(100)][Oracle] - PDK connector node released: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:42:29.482 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] schema data cleaned 
[INFO ] 2024-09-06 10:42:29.482 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] monitor closed 
[INFO ] 2024-09-06 10:42:29.484 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] close complete, cost 524 ms 
[INFO ] 2024-09-06 10:42:29.722 - [任务 13(100)][增强JS] - record:{TEST2=sample string, hdip_dml_typem=i, hdip_source_time=0, ID=1.0, NAME=sample string, TEST1=sample string} 
[INFO ] 2024-09-06 10:42:29.733 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] running status set to false 
[INFO ] 2024-09-06 10:42:29.733 - [任务 13(100)][764217b0-33e5-4082-89cb-8feb47d3a04a] - Node 764217b0-33e5-4082-89cb-8feb47d3a04a[764217b0-33e5-4082-89cb-8feb47d3a04a] running status set to false 
[INFO ] 2024-09-06 10:42:29.733 - [任务 13(100)][764217b0-33e5-4082-89cb-8feb47d3a04a] - Node 764217b0-33e5-4082-89cb-8feb47d3a04a[764217b0-33e5-4082-89cb-8feb47d3a04a] schema data cleaned 
[INFO ] 2024-09-06 10:42:29.734 - [任务 13(100)][764217b0-33e5-4082-89cb-8feb47d3a04a] - Node 764217b0-33e5-4082-89cb-8feb47d3a04a[764217b0-33e5-4082-89cb-8feb47d3a04a] monitor closed 
[INFO ] 2024-09-06 10:42:29.734 - [任务 13(100)][764217b0-33e5-4082-89cb-8feb47d3a04a] - Node 764217b0-33e5-4082-89cb-8feb47d3a04a[764217b0-33e5-4082-89cb-8feb47d3a04a] close complete, cost 2 ms 
[INFO ] 2024-09-06 10:42:30.208 - [任务 13(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Oracle-12534fb2-7533-4bbb-a7ce-627de995e3f5 
[INFO ] 2024-09-06 10:42:30.208 - [任务 13(100)][增强JS] - PDK connector node released: ScriptExecutor-Oracle-12534fb2-7533-4bbb-a7ce-627de995e3f5 
[INFO ] 2024-09-06 10:42:30.209 - [任务 13(100)][增强JS] - [ScriptExecutorsManager-66da6bc4085e072960e8c74b-7cebc975-e148-402f-87ec-58caeead6bea-66d688a8a40a37725da9ce03] schema data cleaned 
[INFO ] 2024-09-06 10:42:30.210 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] schema data cleaned 
[INFO ] 2024-09-06 10:42:30.210 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] monitor closed 
[INFO ] 2024-09-06 10:42:30.210 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] close complete, cost 485 ms 
[INFO ] 2024-09-06 10:42:30.212 - [任务 13(100)] - Closed task monitor(s)
null 
[INFO ] 2024-09-06 10:42:30.212 - [任务 13(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-06 10:42:30.212 - [任务 13(100)] - Stopped task aspect(s) 
[INFO ] 2024-09-06 10:42:31.961 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:42:31.963 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:42:31.963 - [任务 13(100)][31523042-1cbf-4af3-869a-d84209398c4b] - Node 31523042-1cbf-4af3-869a-d84209398c4b[31523042-1cbf-4af3-869a-d84209398c4b] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:42:31.963 - [任务 13(100)][31523042-1cbf-4af3-869a-d84209398c4b] - Node 31523042-1cbf-4af3-869a-d84209398c4b[31523042-1cbf-4af3-869a-d84209398c4b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:42:31.963 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:42:31.963 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] preload schema finished, cost 1 ms 
[INFO ] 2024-09-06 10:42:31.963 - [任务 13(100)][增强JS] - Node migrate_js_processor(增强JS: 7cebc975-e148-402f-87ec-58caeead6bea) enable batch process 
[WARN ] 2024-09-06 10:42:33.289 - [任务 13(100)][Oracle] - Source table is empty, trying to mock data 
[INFO ] 2024-09-06 10:42:33.289 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] running status set to false 
[INFO ] 2024-09-06 10:42:33.695 - [任务 13(100)][Oracle] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:42:33.695 - [任务 13(100)][Oracle] - PDK connector node released: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:42:33.695 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] schema data cleaned 
[INFO ] 2024-09-06 10:42:33.695 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] monitor closed 
[INFO ] 2024-09-06 10:42:33.898 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] close complete, cost 409 ms 
[INFO ] 2024-09-06 10:42:34.218 - [任务 13(100)][增强JS] - record:{TEST2=sample string, hdip_dml_typem=i, hdip_source_time=0, ID=1.0, NAME=sample string, TEST1=sample string} 
[INFO ] 2024-09-06 10:42:34.222 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] running status set to false 
[INFO ] 2024-09-06 10:42:34.228 - [任务 13(100)][31523042-1cbf-4af3-869a-d84209398c4b] - Node 31523042-1cbf-4af3-869a-d84209398c4b[31523042-1cbf-4af3-869a-d84209398c4b] running status set to false 
[INFO ] 2024-09-06 10:42:34.228 - [任务 13(100)][31523042-1cbf-4af3-869a-d84209398c4b] - Node 31523042-1cbf-4af3-869a-d84209398c4b[31523042-1cbf-4af3-869a-d84209398c4b] schema data cleaned 
[INFO ] 2024-09-06 10:42:34.228 - [任务 13(100)][31523042-1cbf-4af3-869a-d84209398c4b] - Node 31523042-1cbf-4af3-869a-d84209398c4b[31523042-1cbf-4af3-869a-d84209398c4b] monitor closed 
[INFO ] 2024-09-06 10:42:34.228 - [任务 13(100)][31523042-1cbf-4af3-869a-d84209398c4b] - Node 31523042-1cbf-4af3-869a-d84209398c4b[31523042-1cbf-4af3-869a-d84209398c4b] close complete, cost 3 ms 
[INFO ] 2024-09-06 10:42:34.474 - [任务 13(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Oracle-176c96cb-8f86-491b-8777-350466e1bd81 
[INFO ] 2024-09-06 10:42:34.477 - [任务 13(100)][增强JS] - PDK connector node released: ScriptExecutor-Oracle-176c96cb-8f86-491b-8777-350466e1bd81 
[INFO ] 2024-09-06 10:42:34.478 - [任务 13(100)][增强JS] - [ScriptExecutorsManager-66da6bc4085e072960e8c74b-7cebc975-e148-402f-87ec-58caeead6bea-66d688a8a40a37725da9ce03] schema data cleaned 
[INFO ] 2024-09-06 10:42:34.481 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] schema data cleaned 
[INFO ] 2024-09-06 10:42:34.481 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] monitor closed 
[INFO ] 2024-09-06 10:42:34.484 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] close complete, cost 264 ms 
[INFO ] 2024-09-06 10:42:34.485 - [任务 13(100)] - Closed task monitor(s)
null 
[INFO ] 2024-09-06 10:42:34.485 - [任务 13(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-06 10:42:34.486 - [任务 13(100)] - Stopped task aspect(s) 
[INFO ] 2024-09-06 10:42:38.239 - [任务 13(100)][e6ebbcd9-d81c-45d1-a0c1-41880d0bd712] - Node e6ebbcd9-d81c-45d1-a0c1-41880d0bd712[e6ebbcd9-d81c-45d1-a0c1-41880d0bd712] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:42:38.239 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:42:38.239 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:42:38.241 - [任务 13(100)][e6ebbcd9-d81c-45d1-a0c1-41880d0bd712] - Node e6ebbcd9-d81c-45d1-a0c1-41880d0bd712[e6ebbcd9-d81c-45d1-a0c1-41880d0bd712] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:42:38.241 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:42:38.241 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:42:38.447 - [任务 13(100)][增强JS] - Node migrate_js_processor(增强JS: 7cebc975-e148-402f-87ec-58caeead6bea) enable batch process 
[WARN ] 2024-09-06 10:42:39.145 - [任务 13(100)][Oracle] - Source table is empty, trying to mock data 
[INFO ] 2024-09-06 10:42:39.349 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] running status set to false 
[INFO ] 2024-09-06 10:42:39.426 - [任务 13(100)][Oracle] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:42:39.430 - [任务 13(100)][Oracle] - PDK connector node released: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:42:39.430 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] schema data cleaned 
[INFO ] 2024-09-06 10:42:39.431 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] monitor closed 
[INFO ] 2024-09-06 10:42:39.431 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] close complete, cost 282 ms 
[INFO ] 2024-09-06 10:42:39.703 - [任务 13(100)][增强JS] - record:{TEST2=sample string, hdip_dml_typem=i, hdip_source_time=0, ID=1.0, NAME=sample string, TEST1=sample string} 
[INFO ] 2024-09-06 10:42:39.711 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] running status set to false 
[INFO ] 2024-09-06 10:42:39.711 - [任务 13(100)][e6ebbcd9-d81c-45d1-a0c1-41880d0bd712] - Node e6ebbcd9-d81c-45d1-a0c1-41880d0bd712[e6ebbcd9-d81c-45d1-a0c1-41880d0bd712] running status set to false 
[INFO ] 2024-09-06 10:42:39.711 - [任务 13(100)][e6ebbcd9-d81c-45d1-a0c1-41880d0bd712] - Node e6ebbcd9-d81c-45d1-a0c1-41880d0bd712[e6ebbcd9-d81c-45d1-a0c1-41880d0bd712] schema data cleaned 
[INFO ] 2024-09-06 10:42:39.711 - [任务 13(100)][e6ebbcd9-d81c-45d1-a0c1-41880d0bd712] - Node e6ebbcd9-d81c-45d1-a0c1-41880d0bd712[e6ebbcd9-d81c-45d1-a0c1-41880d0bd712] monitor closed 
[INFO ] 2024-09-06 10:42:39.712 - [任务 13(100)][e6ebbcd9-d81c-45d1-a0c1-41880d0bd712] - Node e6ebbcd9-d81c-45d1-a0c1-41880d0bd712[e6ebbcd9-d81c-45d1-a0c1-41880d0bd712] close complete, cost 0 ms 
[INFO ] 2024-09-06 10:42:39.722 - [任务 13(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Oracle-57d049ee-c9e3-445c-bfd2-7319730dc863 
[INFO ] 2024-09-06 10:42:39.722 - [任务 13(100)][增强JS] - PDK connector node released: ScriptExecutor-Oracle-57d049ee-c9e3-445c-bfd2-7319730dc863 
[INFO ] 2024-09-06 10:42:39.722 - [任务 13(100)][增强JS] - [ScriptExecutorsManager-66da6bc4085e072960e8c74b-7cebc975-e148-402f-87ec-58caeead6bea-66d688a8a40a37725da9ce03] schema data cleaned 
[INFO ] 2024-09-06 10:42:39.723 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] schema data cleaned 
[INFO ] 2024-09-06 10:42:39.723 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] monitor closed 
[INFO ] 2024-09-06 10:42:39.725 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] close complete, cost 17 ms 
[INFO ] 2024-09-06 10:42:39.725 - [任务 13(100)] - Closed task monitor(s)
null 
[INFO ] 2024-09-06 10:42:39.725 - [任务 13(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-06 10:42:39.726 - [任务 13(100)] - Stopped task aspect(s) 
[INFO ] 2024-09-06 10:45:32.434 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:45:32.434 - [任务 13(100)][35e6cebd-0553-4645-a527-95fb3906beec] - Node 35e6cebd-0553-4645-a527-95fb3906beec[35e6cebd-0553-4645-a527-95fb3906beec] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:45:32.434 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:45:32.434 - [任务 13(100)][35e6cebd-0553-4645-a527-95fb3906beec] - Node 35e6cebd-0553-4645-a527-95fb3906beec[35e6cebd-0553-4645-a527-95fb3906beec] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:45:32.434 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:45:32.435 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:45:32.637 - [任务 13(100)][增强JS] - Node migrate_js_processor(增强JS: 7cebc975-e148-402f-87ec-58caeead6bea) enable batch process 
[INFO ] 2024-09-06 10:45:33.724 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] running status set to false 
[INFO ] 2024-09-06 10:45:33.946 - [任务 13(100)][Oracle] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:45:33.947 - [任务 13(100)][Oracle] - PDK connector node released: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:45:33.947 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] schema data cleaned 
[INFO ] 2024-09-06 10:45:33.947 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] monitor closed 
[INFO ] 2024-09-06 10:45:33.948 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] close complete, cost 223 ms 
[INFO ] 2024-09-06 10:45:34.055 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:45:34.056 - [任务 13(100)][76a29557-03e5-4453-8037-4a28472eefd9] - Node 76a29557-03e5-4453-8037-4a28472eefd9[76a29557-03e5-4453-8037-4a28472eefd9] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:45:34.056 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:45:34.056 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:45:34.056 - [任务 13(100)][76a29557-03e5-4453-8037-4a28472eefd9] - Node 76a29557-03e5-4453-8037-4a28472eefd9[76a29557-03e5-4453-8037-4a28472eefd9] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:45:34.056 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] preload schema finished, cost 1 ms 
[INFO ] 2024-09-06 10:45:34.264 - [任务 13(100)][增强JS] - Node migrate_js_processor(增强JS: 7cebc975-e148-402f-87ec-58caeead6bea) enable batch process 
[INFO ] 2024-09-06 10:45:34.428 - [任务 13(100)][增强JS] - record:{ID=1, NAME=name1, TEST1=test1, TEST2=test2, hdip_dml_typem=i, hdip_source_time=0} 
[INFO ] 2024-09-06 10:45:34.434 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] running status set to false 
[INFO ] 2024-09-06 10:45:34.434 - [任务 13(100)][35e6cebd-0553-4645-a527-95fb3906beec] - Node 35e6cebd-0553-4645-a527-95fb3906beec[35e6cebd-0553-4645-a527-95fb3906beec] running status set to false 
[INFO ] 2024-09-06 10:45:34.435 - [任务 13(100)][35e6cebd-0553-4645-a527-95fb3906beec] - Node 35e6cebd-0553-4645-a527-95fb3906beec[35e6cebd-0553-4645-a527-95fb3906beec] schema data cleaned 
[INFO ] 2024-09-06 10:45:34.435 - [任务 13(100)][35e6cebd-0553-4645-a527-95fb3906beec] - Node 35e6cebd-0553-4645-a527-95fb3906beec[35e6cebd-0553-4645-a527-95fb3906beec] monitor closed 
[INFO ] 2024-09-06 10:45:34.635 - [任务 13(100)][35e6cebd-0553-4645-a527-95fb3906beec] - Node 35e6cebd-0553-4645-a527-95fb3906beec[35e6cebd-0553-4645-a527-95fb3906beec] close complete, cost 0 ms 
[INFO ] 2024-09-06 10:45:34.738 - [任务 13(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Oracle-27e4ccc6-f0fb-4659-b75e-90cc117c373f 
[INFO ] 2024-09-06 10:45:34.738 - [任务 13(100)][增强JS] - PDK connector node released: ScriptExecutor-Oracle-27e4ccc6-f0fb-4659-b75e-90cc117c373f 
[INFO ] 2024-09-06 10:45:34.738 - [任务 13(100)][增强JS] - [ScriptExecutorsManager-66da6bc4085e072960e8c74b-7cebc975-e148-402f-87ec-58caeead6bea-66d688a8a40a37725da9ce03] schema data cleaned 
[INFO ] 2024-09-06 10:45:34.740 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] schema data cleaned 
[INFO ] 2024-09-06 10:45:34.740 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] monitor closed 
[INFO ] 2024-09-06 10:45:34.742 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] close complete, cost 314 ms 
[INFO ] 2024-09-06 10:45:34.742 - [任务 13(100)] - Closed task monitor(s)
null 
[INFO ] 2024-09-06 10:45:34.742 - [任务 13(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-06 10:45:34.803 - [任务 13(100)] - Stopped task aspect(s) 
[INFO ] 2024-09-06 10:45:34.803 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] running status set to false 
[INFO ] 2024-09-06 10:45:34.844 - [任务 13(100)][Oracle] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:45:34.845 - [任务 13(100)][Oracle] - PDK connector node released: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:45:34.845 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] schema data cleaned 
[INFO ] 2024-09-06 10:45:34.846 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] monitor closed 
[INFO ] 2024-09-06 10:45:35.052 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] close complete, cost 48 ms 
[INFO ] 2024-09-06 10:45:35.545 - [任务 13(100)][增强JS] - record:{ID=1, NAME=name1, TEST1=test1, TEST2=test2, hdip_dml_typem=i, hdip_source_time=0} 
[INFO ] 2024-09-06 10:45:35.550 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] running status set to false 
[INFO ] 2024-09-06 10:45:35.553 - [任务 13(100)][76a29557-03e5-4453-8037-4a28472eefd9] - Node 76a29557-03e5-4453-8037-4a28472eefd9[76a29557-03e5-4453-8037-4a28472eefd9] running status set to false 
[INFO ] 2024-09-06 10:45:35.553 - [任务 13(100)][76a29557-03e5-4453-8037-4a28472eefd9] - Node 76a29557-03e5-4453-8037-4a28472eefd9[76a29557-03e5-4453-8037-4a28472eefd9] schema data cleaned 
[INFO ] 2024-09-06 10:45:35.553 - [任务 13(100)][76a29557-03e5-4453-8037-4a28472eefd9] - Node 76a29557-03e5-4453-8037-4a28472eefd9[76a29557-03e5-4453-8037-4a28472eefd9] monitor closed 
[INFO ] 2024-09-06 10:45:35.758 - [任务 13(100)][76a29557-03e5-4453-8037-4a28472eefd9] - Node 76a29557-03e5-4453-8037-4a28472eefd9[76a29557-03e5-4453-8037-4a28472eefd9] close complete, cost 1 ms 
[INFO ] 2024-09-06 10:45:35.868 - [任务 13(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Oracle-188dd9ec-61ec-41cf-8561-e30de247276c 
[INFO ] 2024-09-06 10:45:35.869 - [任务 13(100)][增强JS] - PDK connector node released: ScriptExecutor-Oracle-188dd9ec-61ec-41cf-8561-e30de247276c 
[INFO ] 2024-09-06 10:45:35.869 - [任务 13(100)][增强JS] - [ScriptExecutorsManager-66da6bc4085e072960e8c74b-7cebc975-e148-402f-87ec-58caeead6bea-66d688a8a40a37725da9ce03] schema data cleaned 
[INFO ] 2024-09-06 10:45:35.870 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] schema data cleaned 
[INFO ] 2024-09-06 10:45:35.870 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] monitor closed 
[INFO ] 2024-09-06 10:45:35.872 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] close complete, cost 326 ms 
[INFO ] 2024-09-06 10:45:35.872 - [任务 13(100)] - Closed task monitor(s)
null 
[INFO ] 2024-09-06 10:45:35.873 - [任务 13(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-06 10:45:36.077 - [任务 13(100)] - Stopped task aspect(s) 
[INFO ] 2024-09-06 10:45:45.560 - [任务 13(100)][297fd813-dbf0-4eeb-905d-b01a006c233f] - Node 297fd813-dbf0-4eeb-905d-b01a006c233f[297fd813-dbf0-4eeb-905d-b01a006c233f] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:45:45.561 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:45:45.561 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:45:45.561 - [任务 13(100)][297fd813-dbf0-4eeb-905d-b01a006c233f] - Node 297fd813-dbf0-4eeb-905d-b01a006c233f[297fd813-dbf0-4eeb-905d-b01a006c233f] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:45:45.561 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:45:45.561 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:45:45.561 - [任务 13(100)][增强JS] - Node migrate_js_processor(增强JS: 7cebc975-e148-402f-87ec-58caeead6bea) enable batch process 
[INFO ] 2024-09-06 10:45:46.212 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] running status set to false 
[INFO ] 2024-09-06 10:45:46.398 - [任务 13(100)][Oracle] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:45:46.398 - [任务 13(100)][Oracle] - PDK connector node released: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:45:46.398 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] schema data cleaned 
[INFO ] 2024-09-06 10:45:46.398 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] monitor closed 
[INFO ] 2024-09-06 10:45:46.604 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] close complete, cost 204 ms 
[INFO ] 2024-09-06 10:45:46.879 - [任务 13(100)][增强JS] - record:{ID=1, NAME=name1, TEST1=test1, TEST2=test2, hdip_dml_typem=i, hdip_source_time=0} 
[INFO ] 2024-09-06 10:45:46.883 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] running status set to false 
[INFO ] 2024-09-06 10:45:46.883 - [任务 13(100)][297fd813-dbf0-4eeb-905d-b01a006c233f] - Node 297fd813-dbf0-4eeb-905d-b01a006c233f[297fd813-dbf0-4eeb-905d-b01a006c233f] running status set to false 
[INFO ] 2024-09-06 10:45:46.883 - [任务 13(100)][297fd813-dbf0-4eeb-905d-b01a006c233f] - Node 297fd813-dbf0-4eeb-905d-b01a006c233f[297fd813-dbf0-4eeb-905d-b01a006c233f] schema data cleaned 
[INFO ] 2024-09-06 10:45:46.884 - [任务 13(100)][297fd813-dbf0-4eeb-905d-b01a006c233f] - Node 297fd813-dbf0-4eeb-905d-b01a006c233f[297fd813-dbf0-4eeb-905d-b01a006c233f] monitor closed 
[INFO ] 2024-09-06 10:45:46.884 - [任务 13(100)][297fd813-dbf0-4eeb-905d-b01a006c233f] - Node 297fd813-dbf0-4eeb-905d-b01a006c233f[297fd813-dbf0-4eeb-905d-b01a006c233f] close complete, cost 0 ms 
[INFO ] 2024-09-06 10:45:46.910 - [任务 13(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Oracle-914c434a-4c6a-4f82-992d-ada2a4694b1c 
[INFO ] 2024-09-06 10:45:46.910 - [任务 13(100)][增强JS] - PDK connector node released: ScriptExecutor-Oracle-914c434a-4c6a-4f82-992d-ada2a4694b1c 
[INFO ] 2024-09-06 10:45:46.910 - [任务 13(100)][增强JS] - [ScriptExecutorsManager-66da6bc4085e072960e8c74b-7cebc975-e148-402f-87ec-58caeead6bea-66d688a8a40a37725da9ce03] schema data cleaned 
[INFO ] 2024-09-06 10:45:46.911 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] schema data cleaned 
[INFO ] 2024-09-06 10:45:46.911 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] monitor closed 
[INFO ] 2024-09-06 10:45:46.911 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] close complete, cost 30 ms 
[INFO ] 2024-09-06 10:45:46.912 - [任务 13(100)] - Closed task monitor(s)
null 
[INFO ] 2024-09-06 10:45:46.912 - [任务 13(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-06 10:45:47.116 - [任务 13(100)] - Stopped task aspect(s) 
[INFO ] 2024-09-06 10:45:47.882 - [任务 13(100)][cf91e6e3-1ae8-46a0-a4f4-af96bbc321f9] - Node cf91e6e3-1ae8-46a0-a4f4-af96bbc321f9[cf91e6e3-1ae8-46a0-a4f4-af96bbc321f9] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:45:47.882 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:45:47.882 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:45:47.882 - [任务 13(100)][cf91e6e3-1ae8-46a0-a4f4-af96bbc321f9] - Node cf91e6e3-1ae8-46a0-a4f4-af96bbc321f9[cf91e6e3-1ae8-46a0-a4f4-af96bbc321f9] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:45:47.882 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:45:47.883 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:45:48.085 - [任务 13(100)][增强JS] - Node migrate_js_processor(增强JS: 7cebc975-e148-402f-87ec-58caeead6bea) enable batch process 
[INFO ] 2024-09-06 10:45:48.491 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] running status set to false 
[INFO ] 2024-09-06 10:45:48.507 - [任务 13(100)][Oracle] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:45:48.507 - [任务 13(100)][Oracle] - PDK connector node released: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:45:48.507 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] schema data cleaned 
[INFO ] 2024-09-06 10:45:48.507 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] monitor closed 
[INFO ] 2024-09-06 10:45:48.709 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] close complete, cost 16 ms 
[INFO ] 2024-09-06 10:45:49.155 - [任务 13(100)][增强JS] - record:{ID=1, NAME=name1, TEST1=test1, TEST2=test2, hdip_dml_typem=i, hdip_source_time=0} 
[INFO ] 2024-09-06 10:45:49.158 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] running status set to false 
[INFO ] 2024-09-06 10:45:49.160 - [任务 13(100)][cf91e6e3-1ae8-46a0-a4f4-af96bbc321f9] - Node cf91e6e3-1ae8-46a0-a4f4-af96bbc321f9[cf91e6e3-1ae8-46a0-a4f4-af96bbc321f9] running status set to false 
[INFO ] 2024-09-06 10:45:49.160 - [任务 13(100)][cf91e6e3-1ae8-46a0-a4f4-af96bbc321f9] - Node cf91e6e3-1ae8-46a0-a4f4-af96bbc321f9[cf91e6e3-1ae8-46a0-a4f4-af96bbc321f9] schema data cleaned 
[INFO ] 2024-09-06 10:45:49.160 - [任务 13(100)][cf91e6e3-1ae8-46a0-a4f4-af96bbc321f9] - Node cf91e6e3-1ae8-46a0-a4f4-af96bbc321f9[cf91e6e3-1ae8-46a0-a4f4-af96bbc321f9] monitor closed 
[INFO ] 2024-09-06 10:45:49.160 - [任务 13(100)][cf91e6e3-1ae8-46a0-a4f4-af96bbc321f9] - Node cf91e6e3-1ae8-46a0-a4f4-af96bbc321f9[cf91e6e3-1ae8-46a0-a4f4-af96bbc321f9] close complete, cost 0 ms 
[INFO ] 2024-09-06 10:45:49.492 - [任务 13(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Oracle-19dfe1e1-0694-4cfc-93db-715efd3f6d01 
[INFO ] 2024-09-06 10:45:49.493 - [任务 13(100)][增强JS] - PDK connector node released: ScriptExecutor-Oracle-19dfe1e1-0694-4cfc-93db-715efd3f6d01 
[INFO ] 2024-09-06 10:45:49.493 - [任务 13(100)][增强JS] - [ScriptExecutorsManager-66da6bc4085e072960e8c74b-7cebc975-e148-402f-87ec-58caeead6bea-66d688a8a40a37725da9ce03] schema data cleaned 
[INFO ] 2024-09-06 10:45:49.495 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] schema data cleaned 
[INFO ] 2024-09-06 10:45:49.496 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] monitor closed 
[INFO ] 2024-09-06 10:45:49.497 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] close complete, cost 340 ms 
[INFO ] 2024-09-06 10:45:49.498 - [任务 13(100)] - Closed task monitor(s)
null 
[INFO ] 2024-09-06 10:45:49.498 - [任务 13(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-06 10:45:49.552 - [任务 13(100)] - Stopped task aspect(s) 
[INFO ] 2024-09-06 10:58:59.662 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:58:59.663 - [任务 13(100)][7842d5cb-5f6f-438f-9de5-c1f74b097b8d] - Node 7842d5cb-5f6f-438f-9de5-c1f74b097b8d[7842d5cb-5f6f-438f-9de5-c1f74b097b8d] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:58:59.663 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:58:59.663 - [任务 13(100)][7842d5cb-5f6f-438f-9de5-c1f74b097b8d] - Node 7842d5cb-5f6f-438f-9de5-c1f74b097b8d[7842d5cb-5f6f-438f-9de5-c1f74b097b8d] preload schema finished, cost 1 ms 
[INFO ] 2024-09-06 10:58:59.664 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] preload schema finished, cost 2 ms 
[INFO ] 2024-09-06 10:58:59.664 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] preload schema finished, cost 3 ms 
[INFO ] 2024-09-06 10:58:59.664 - [任务 13(100)][增强JS] - Node migrate_js_processor(增强JS: 7cebc975-e148-402f-87ec-58caeead6bea) enable batch process 
[INFO ] 2024-09-06 10:59:00.028 - [任务 13(100)][38b8441b-83b8-4a82-abca-ef076523c975] - Node 38b8441b-83b8-4a82-abca-ef076523c975[38b8441b-83b8-4a82-abca-ef076523c975] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:59:00.031 - [任务 13(100)][38b8441b-83b8-4a82-abca-ef076523c975] - Node 38b8441b-83b8-4a82-abca-ef076523c975[38b8441b-83b8-4a82-abca-ef076523c975] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:59:00.031 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:59:00.032 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:59:00.032 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:59:00.032 - [任务 13(100)][增强JS] - Node migrate_js_processor(增强JS: 7cebc975-e148-402f-87ec-58caeead6bea) enable batch process 
[INFO ] 2024-09-06 10:59:00.035 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:59:00.315 - [任务 13(100)][Oracle] - Exception skipping - The current exception does not match the skip exception strategy, message: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[ERROR] 2024-09-06 10:59:00.315 - [任务 13(100)][Oracle] - start source runner failed: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b
	io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:687)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:573)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:191)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:61)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: oracle-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:223)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:207)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.initNode(HazelcastSampleSourcePdkDataNode.java:66)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:85)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId oracle not found for associateId HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b
	at io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:221)
	... 17 more

[INFO ] 2024-09-06 10:59:00.919 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] running status set to false 
[INFO ] 2024-09-06 10:59:01.017 - [任务 13(100)][Oracle] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:59:01.017 - [任务 13(100)][Oracle] - PDK connector node released: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:59:01.017 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] schema data cleaned 
[INFO ] 2024-09-06 10:59:01.017 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] monitor closed 
[INFO ] 2024-09-06 10:59:01.220 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] close complete, cost 271 ms 
[INFO ] 2024-09-06 10:59:01.265 - [任务 13(100)][增强JS] - record:{ID=1, NAME=name1, TEST1=test1, TEST2=test2, hdip_dml_typem=i, hdip_source_time=0} 
[INFO ] 2024-09-06 10:59:01.265 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] running status set to false 
[INFO ] 2024-09-06 10:59:01.266 - [任务 13(100)][7842d5cb-5f6f-438f-9de5-c1f74b097b8d] - Node 7842d5cb-5f6f-438f-9de5-c1f74b097b8d[7842d5cb-5f6f-438f-9de5-c1f74b097b8d] running status set to false 
[INFO ] 2024-09-06 10:59:01.266 - [任务 13(100)][7842d5cb-5f6f-438f-9de5-c1f74b097b8d] - Node 7842d5cb-5f6f-438f-9de5-c1f74b097b8d[7842d5cb-5f6f-438f-9de5-c1f74b097b8d] schema data cleaned 
[INFO ] 2024-09-06 10:59:01.267 - [任务 13(100)][7842d5cb-5f6f-438f-9de5-c1f74b097b8d] - Node 7842d5cb-5f6f-438f-9de5-c1f74b097b8d[7842d5cb-5f6f-438f-9de5-c1f74b097b8d] monitor closed 
[INFO ] 2024-09-06 10:59:01.293 - [任务 13(100)][7842d5cb-5f6f-438f-9de5-c1f74b097b8d] - Node 7842d5cb-5f6f-438f-9de5-c1f74b097b8d[7842d5cb-5f6f-438f-9de5-c1f74b097b8d] close complete, cost 1 ms 
[INFO ] 2024-09-06 10:59:01.293 - [任务 13(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Oracle-5d4da87d-77bd-4eb7-b729-33283e0ad6d4 
[INFO ] 2024-09-06 10:59:01.293 - [任务 13(100)][增强JS] - PDK connector node released: ScriptExecutor-Oracle-5d4da87d-77bd-4eb7-b729-33283e0ad6d4 
[INFO ] 2024-09-06 10:59:01.294 - [任务 13(100)][增强JS] - [ScriptExecutorsManager-66da6bc4085e072960e8c74b-7cebc975-e148-402f-87ec-58caeead6bea-66d688a8a40a37725da9ce03] schema data cleaned 
[INFO ] 2024-09-06 10:59:01.296 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] schema data cleaned 
[INFO ] 2024-09-06 10:59:01.297 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] monitor closed 
[INFO ] 2024-09-06 10:59:01.297 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] close complete, cost 33 ms 
[INFO ] 2024-09-06 10:59:01.298 - [任务 13(100)] - Closed task monitor(s)
null 
[INFO ] 2024-09-06 10:59:01.298 - [任务 13(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-06 10:59:01.299 - [任务 13(100)] - Stopped task aspect(s) 
[INFO ] 2024-09-06 10:59:02.871 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] running status set to false 
[INFO ] 2024-09-06 10:59:02.872 - [任务 13(100)][38b8441b-83b8-4a82-abca-ef076523c975] - Node 38b8441b-83b8-4a82-abca-ef076523c975[38b8441b-83b8-4a82-abca-ef076523c975] running status set to false 
[INFO ] 2024-09-06 10:59:02.872 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] running status set to false 
[INFO ] 2024-09-06 10:59:02.872 - [任务 13(100)][38b8441b-83b8-4a82-abca-ef076523c975] - Node 38b8441b-83b8-4a82-abca-ef076523c975[38b8441b-83b8-4a82-abca-ef076523c975] schema data cleaned 
[INFO ] 2024-09-06 10:59:02.872 - [任务 13(100)][38b8441b-83b8-4a82-abca-ef076523c975] - Node 38b8441b-83b8-4a82-abca-ef076523c975[38b8441b-83b8-4a82-abca-ef076523c975] monitor closed 
[INFO ] 2024-09-06 10:59:02.872 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] schema data cleaned 
[INFO ] 2024-09-06 10:59:02.872 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] monitor closed 
[INFO ] 2024-09-06 10:59:02.872 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] close complete, cost 2 ms 
[INFO ] 2024-09-06 10:59:02.872 - [任务 13(100)][38b8441b-83b8-4a82-abca-ef076523c975] - Node 38b8441b-83b8-4a82-abca-ef076523c975[38b8441b-83b8-4a82-abca-ef076523c975] close complete, cost 2 ms 
[INFO ] 2024-09-06 10:59:02.873 - [任务 13(100)][Oracle] - PDK connector node stopped: null 
[INFO ] 2024-09-06 10:59:02.873 - [任务 13(100)][Oracle] - PDK connector node released: null 
[INFO ] 2024-09-06 10:59:02.873 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] schema data cleaned 
[INFO ] 2024-09-06 10:59:02.873 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] monitor closed 
[INFO ] 2024-09-06 10:59:02.874 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] close complete, cost 19 ms 
[INFO ] 2024-09-06 10:59:02.874 - [任务 13(100)] - Closed task monitor(s)
null 
[INFO ] 2024-09-06 10:59:02.874 - [任务 13(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-06 10:59:03.081 - [任务 13(100)] - Stopped task aspect(s) 
[INFO ] 2024-09-06 10:59:04.572 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:59:04.573 - [任务 13(100)][f0b0a2b3-98d4-4687-b8b3-5d6ad840fc53] - Node f0b0a2b3-98d4-4687-b8b3-5d6ad840fc53[f0b0a2b3-98d4-4687-b8b3-5d6ad840fc53] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:59:04.573 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:59:04.573 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:59:04.573 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:59:04.574 - [任务 13(100)][f0b0a2b3-98d4-4687-b8b3-5d6ad840fc53] - Node f0b0a2b3-98d4-4687-b8b3-5d6ad840fc53[f0b0a2b3-98d4-4687-b8b3-5d6ad840fc53] preload schema finished, cost 1 ms 
[INFO ] 2024-09-06 10:59:04.575 - [任务 13(100)][增强JS] - Node migrate_js_processor(增强JS: 7cebc975-e148-402f-87ec-58caeead6bea) enable batch process 
[INFO ] 2024-09-06 10:59:05.225 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] running status set to false 
[INFO ] 2024-09-06 10:59:05.225 - [任务 13(100)][Oracle] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:59:05.225 - [任务 13(100)][Oracle] - PDK connector node released: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:59:05.225 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] schema data cleaned 
[INFO ] 2024-09-06 10:59:05.226 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] monitor closed 
[INFO ] 2024-09-06 10:59:05.226 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] close complete, cost 22 ms 
[INFO ] 2024-09-06 10:59:05.765 - [任务 13(100)][增强JS] - record:{ID=1, NAME=name1, TEST1=test1, TEST2=test2, hdip_dml_typem=i, hdip_source_time=0} 
[INFO ] 2024-09-06 10:59:05.770 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] running status set to false 
[INFO ] 2024-09-06 10:59:05.770 - [任务 13(100)][f0b0a2b3-98d4-4687-b8b3-5d6ad840fc53] - Node f0b0a2b3-98d4-4687-b8b3-5d6ad840fc53[f0b0a2b3-98d4-4687-b8b3-5d6ad840fc53] running status set to false 
[INFO ] 2024-09-06 10:59:05.770 - [任务 13(100)][f0b0a2b3-98d4-4687-b8b3-5d6ad840fc53] - Node f0b0a2b3-98d4-4687-b8b3-5d6ad840fc53[f0b0a2b3-98d4-4687-b8b3-5d6ad840fc53] schema data cleaned 
[INFO ] 2024-09-06 10:59:05.770 - [任务 13(100)][f0b0a2b3-98d4-4687-b8b3-5d6ad840fc53] - Node f0b0a2b3-98d4-4687-b8b3-5d6ad840fc53[f0b0a2b3-98d4-4687-b8b3-5d6ad840fc53] monitor closed 
[INFO ] 2024-09-06 10:59:05.797 - [任务 13(100)][f0b0a2b3-98d4-4687-b8b3-5d6ad840fc53] - Node f0b0a2b3-98d4-4687-b8b3-5d6ad840fc53[f0b0a2b3-98d4-4687-b8b3-5d6ad840fc53] close complete, cost 0 ms 
[INFO ] 2024-09-06 10:59:05.797 - [任务 13(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Oracle-3bb06ae9-93ef-42f8-9430-b8a104905754 
[INFO ] 2024-09-06 10:59:05.797 - [任务 13(100)][增强JS] - PDK connector node released: ScriptExecutor-Oracle-3bb06ae9-93ef-42f8-9430-b8a104905754 
[INFO ] 2024-09-06 10:59:05.797 - [任务 13(100)][增强JS] - [ScriptExecutorsManager-66da6bc4085e072960e8c74b-7cebc975-e148-402f-87ec-58caeead6bea-66d688a8a40a37725da9ce03] schema data cleaned 
[INFO ] 2024-09-06 10:59:05.800 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] schema data cleaned 
[INFO ] 2024-09-06 10:59:05.800 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] monitor closed 
[INFO ] 2024-09-06 10:59:05.800 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] close complete, cost 33 ms 
[INFO ] 2024-09-06 10:59:05.801 - [任务 13(100)] - Closed task monitor(s)
null 
[INFO ] 2024-09-06 10:59:05.801 - [任务 13(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-06 10:59:05.801 - [任务 13(100)] - Stopped task aspect(s) 
[INFO ] 2024-09-06 10:59:06.806 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:59:06.806 - [任务 13(100)][bc3e7c4b-764f-4d22-b2a7-8853287a34b2] - Node bc3e7c4b-764f-4d22-b2a7-8853287a34b2[bc3e7c4b-764f-4d22-b2a7-8853287a34b2] start preload schema,table counts: 0 
[INFO ] 2024-09-06 10:59:06.806 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:59:06.806 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] start preload schema,table counts: 1 
[INFO ] 2024-09-06 10:59:06.807 - [任务 13(100)][bc3e7c4b-764f-4d22-b2a7-8853287a34b2] - Node bc3e7c4b-764f-4d22-b2a7-8853287a34b2[bc3e7c4b-764f-4d22-b2a7-8853287a34b2] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:59:06.807 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] preload schema finished, cost 0 ms 
[INFO ] 2024-09-06 10:59:06.807 - [任务 13(100)][增强JS] - Node migrate_js_processor(增强JS: 7cebc975-e148-402f-87ec-58caeead6bea) enable batch process 
[INFO ] 2024-09-06 10:59:07.360 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] running status set to false 
[INFO ] 2024-09-06 10:59:07.605 - [任务 13(100)][Oracle] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:59:07.606 - [任务 13(100)][Oracle] - PDK connector node released: HazelcastSampleSourcePdkDataNode-a99fe1df-6671-4ec4-b047-09ab7d14896b 
[INFO ] 2024-09-06 10:59:07.606 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] schema data cleaned 
[INFO ] 2024-09-06 10:59:07.606 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] monitor closed 
[INFO ] 2024-09-06 10:59:07.606 - [任务 13(100)][Oracle] - Node Oracle[a99fe1df-6671-4ec4-b047-09ab7d14896b] close complete, cost 247 ms 
[INFO ] 2024-09-06 10:59:08.072 - [任务 13(100)][增强JS] - record:{ID=1, NAME=name1, TEST1=test1, TEST2=test2, hdip_dml_typem=i, hdip_source_time=0} 
[INFO ] 2024-09-06 10:59:08.086 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] running status set to false 
[INFO ] 2024-09-06 10:59:08.086 - [任务 13(100)][bc3e7c4b-764f-4d22-b2a7-8853287a34b2] - Node bc3e7c4b-764f-4d22-b2a7-8853287a34b2[bc3e7c4b-764f-4d22-b2a7-8853287a34b2] running status set to false 
[INFO ] 2024-09-06 10:59:08.087 - [任务 13(100)][bc3e7c4b-764f-4d22-b2a7-8853287a34b2] - Node bc3e7c4b-764f-4d22-b2a7-8853287a34b2[bc3e7c4b-764f-4d22-b2a7-8853287a34b2] schema data cleaned 
[INFO ] 2024-09-06 10:59:08.087 - [任务 13(100)][bc3e7c4b-764f-4d22-b2a7-8853287a34b2] - Node bc3e7c4b-764f-4d22-b2a7-8853287a34b2[bc3e7c4b-764f-4d22-b2a7-8853287a34b2] monitor closed 
[INFO ] 2024-09-06 10:59:08.289 - [任务 13(100)][bc3e7c4b-764f-4d22-b2a7-8853287a34b2] - Node bc3e7c4b-764f-4d22-b2a7-8853287a34b2[bc3e7c4b-764f-4d22-b2a7-8853287a34b2] close complete, cost 1 ms 
[INFO ] 2024-09-06 10:59:08.407 - [任务 13(100)][增强JS] - PDK connector node stopped: ScriptExecutor-Oracle-c91d3bc9-8677-4abd-b595-858d12434a03 
[INFO ] 2024-09-06 10:59:08.407 - [任务 13(100)][增强JS] - PDK connector node released: ScriptExecutor-Oracle-c91d3bc9-8677-4abd-b595-858d12434a03 
[INFO ] 2024-09-06 10:59:08.412 - [任务 13(100)][增强JS] - [ScriptExecutorsManager-66da6bc4085e072960e8c74b-7cebc975-e148-402f-87ec-58caeead6bea-66d688a8a40a37725da9ce03] schema data cleaned 
[INFO ] 2024-09-06 10:59:08.412 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] schema data cleaned 
[INFO ] 2024-09-06 10:59:08.412 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] monitor closed 
[INFO ] 2024-09-06 10:59:08.413 - [任务 13(100)][增强JS] - Node 增强JS[7cebc975-e148-402f-87ec-58caeead6bea] close complete, cost 336 ms 
[INFO ] 2024-09-06 10:59:08.414 - [任务 13(100)] - Closed task monitor(s)
null 
[INFO ] 2024-09-06 10:59:08.415 - [任务 13(100)] - Closed task auto recovery instance
  null 
[INFO ] 2024-09-06 10:59:08.415 - [任务 13(100)] - Stopped task aspect(s) 
