[INFO ] 2024-07-08 14:50:52.547 - [测试共享挖掘Oracle-Mysql2] - Task initialization... 
[INFO ] 2024-07-08 14:50:52.557 - [测试共享挖掘Oracle-Mysql2] - Start task milestones: 668772c0ed8812650a8a3601(测试共享挖掘Oracle-Mysql2) 
[INFO ] 2024-07-08 14:50:53.563 - [测试共享挖掘Oracle-Mysql2] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-08 14:50:53.748 - [测试共享挖掘Oracle-Mysql2] - The engine receives 测试共享挖掘Oracle-Mysql2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-08 14:50:54.269 - [测试共享挖掘Oracle-Mysql2][POLICY] - Node POLICY[f1809cff-aad5-4493-9d9e-643851f12346] start preload schema,table counts: 1 
[INFO ] 2024-07-08 14:50:54.270 - [测试共享挖掘Oracle-Mysql2][POLICY] - Node POLICY[f1809cff-aad5-4493-9d9e-643851f12346] preload schema finished, cost 0 ms 
[INFO ] 2024-07-08 14:50:54.278 - [测试共享挖掘Oracle-Mysql2][POLICY2] - Node POLICY2[cb2a3545-fe37-4f13-91ab-01a7211233d2] start preload schema,table counts: 1 
[INFO ] 2024-07-08 14:50:54.279 - [测试共享挖掘Oracle-Mysql2][POLICY2] - Node POLICY2[cb2a3545-fe37-4f13-91ab-01a7211233d2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-08 14:50:55.404 - [测试共享挖掘Oracle-Mysql2][POLICY2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-08 14:50:56.653 - [测试共享挖掘Oracle-Mysql2][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-08 14:50:56.667 - [测试共享挖掘Oracle-Mysql2][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-08 14:50:56.670 - [测试共享挖掘Oracle-Mysql2][POLICY] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-08 14:50:56.676 - [测试共享挖掘Oracle-Mysql2][POLICY] - batch offset found: {},stream offset found: {"sequenceMap":{"POLICY":1,"_tapdata_heartbeat_table":12974},"streamOffset":{"sortString":null,"offsetValue":null,"lastScn":69114431,"pendingScn":69114436,"timestamp":1720176370000,"hexScn":null,"fno":0}} 
[INFO ] 2024-07-08 14:50:56.742 - [测试共享挖掘Oracle-Mysql2][POLICY] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-08 14:50:56.745 - [测试共享挖掘Oracle-Mysql2][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-08 14:50:56.746 - [测试共享挖掘Oracle-Mysql2][POLICY] - Initial sync completed 
[INFO ] 2024-07-08 14:50:56.808 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-08 14:50:56.810 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection TAPDATA2Oracle enable share cdc: true 
[INFO ] 2024-07-08 14:50:56.813 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试共享挖掘Oracle-Mysql2 enable share cdc: true 
[INFO ] 2024-07-08 14:50:56.842 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自TAPDATA2Oracle的共享挖掘任务 
[INFO ] 2024-07-08 14:50:56.858 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-08 14:50:56.870 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687708b66ab5ede8a35c768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6687678eed8812650a8a3044_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-825204492, shareCdcTaskId=6687708bed8812650a8a33e8, connectionId=6687678eed8812650a8a3044) 
[INFO ] 2024-07-08 14:50:56.879 - [测试共享挖掘Oracle-Mysql2][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2.POLICY_测试共享挖掘Oracle-Mysql2', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-825204492', head seq: 0, tail seq: 0 
[INFO ] 2024-07-08 14:50:56.888 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-08 14:50:56.888 - [测试共享挖掘Oracle-Mysql2][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-08 14:50:56.888 - [测试共享挖掘Oracle-Mysql2][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-08 14:50:56.889 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-08 14:50:56.891 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-08 14:50:56.902 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687708b66ab5ede8a35c768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6687678eed8812650a8a3044_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-825204492, shareCdcTaskId=6687708bed8812650a8a33e8, connectionId=6687678eed8812650a8a3044) 
[INFO ] 2024-07-08 14:50:56.906 - [测试共享挖掘Oracle-Mysql2][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2.POLICY_测试共享挖掘Oracle-Mysql2', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-825204492', head seq: 0, tail seq: 0 
[INFO ] 2024-07-08 14:50:56.906 - [测试共享挖掘Oracle-Mysql2][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2.POLICY_测试共享挖掘Oracle-Mysql2, external storage name: ExternalStorage_SHARE_CDC_-825204492 
[INFO ] 2024-07-08 14:50:56.911 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-08 14:50:56.915 - [测试共享挖掘Oracle-Mysql2][POLICY] - Connector Oracle incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-08 14:50:56.921 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 1 
[INFO ] 2024-07-08 14:50:56.931 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=1} 
[INFO ] 2024-07-08 14:59:54.627 - [测试共享挖掘Oracle-Mysql2][POLICY] - Node POLICY[f1809cff-aad5-4493-9d9e-643851f12346] running status set to false 
[INFO ] 2024-07-08 15:05:16.761 - [测试共享挖掘Oracle-Mysql2] - Task initialization... 
[INFO ] 2024-07-08 15:05:16.853 - [测试共享挖掘Oracle-Mysql2] - Start task milestones: 668772c0ed8812650a8a3601(测试共享挖掘Oracle-Mysql2) 
[INFO ] 2024-07-08 15:05:17.187 - [测试共享挖掘Oracle-Mysql2] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-08 15:05:17.251 - [测试共享挖掘Oracle-Mysql2] - The engine receives 测试共享挖掘Oracle-Mysql2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-08 15:05:17.531 - [测试共享挖掘Oracle-Mysql2][POLICY2] - Node POLICY2[cb2a3545-fe37-4f13-91ab-01a7211233d2] start preload schema,table counts: 1 
[INFO ] 2024-07-08 15:05:17.535 - [测试共享挖掘Oracle-Mysql2][POLICY] - Node POLICY[f1809cff-aad5-4493-9d9e-643851f12346] start preload schema,table counts: 1 
[INFO ] 2024-07-08 15:05:17.541 - [测试共享挖掘Oracle-Mysql2][POLICY2] - Node POLICY2[cb2a3545-fe37-4f13-91ab-01a7211233d2] preload schema finished, cost 0 ms 
[INFO ] 2024-07-08 15:05:17.541 - [测试共享挖掘Oracle-Mysql2][POLICY] - Node POLICY[f1809cff-aad5-4493-9d9e-643851f12346] preload schema finished, cost 0 ms 
[INFO ] 2024-07-08 15:05:22.111 - [测试共享挖掘Oracle-Mysql2][POLICY2] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-08 15:05:23.485 - [测试共享挖掘Oracle-Mysql2][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-08 15:05:23.486 - [测试共享挖掘Oracle-Mysql2][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-08 15:05:23.488 - [测试共享挖掘Oracle-Mysql2][POLICY] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-08 15:05:27.853 - [测试共享挖掘Oracle-Mysql2][POLICY] - batch offset found: {},stream offset found: {"sequenceMap":{"POLICY":1,"_tapdata_heartbeat_table":12974},"streamOffset":{"sortString":null,"offsetValue":null,"lastScn":69114431,"pendingScn":69114436,"timestamp":1720176370000,"hexScn":null,"fno":0}} 
[INFO ] 2024-07-08 15:05:28.036 - [测试共享挖掘Oracle-Mysql2][POLICY] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-08 15:05:28.038 - [测试共享挖掘Oracle-Mysql2][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-08 15:05:28.038 - [测试共享挖掘Oracle-Mysql2][POLICY] - Initial sync completed 
[INFO ] 2024-07-08 15:05:28.150 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-08 15:05:28.155 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection TAPDATA2Oracle enable share cdc: true 
[INFO ] 2024-07-08 15:05:28.155 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试共享挖掘Oracle-Mysql2 enable share cdc: true 
[INFO ] 2024-07-08 15:05:28.192 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自TAPDATA2Oracle的共享挖掘任务 
[INFO ] 2024-07-08 15:05:28.202 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-08 15:05:28.222 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687708b66ab5ede8a35c768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6687678eed8812650a8a3044_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-825204492, shareCdcTaskId=6687708bed8812650a8a33e8, connectionId=6687678eed8812650a8a3044) 
[INFO ] 2024-07-08 15:05:28.334 - [测试共享挖掘Oracle-Mysql2][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2.POLICY_测试共享挖掘Oracle-Mysql2', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-825204492', head seq: 0, tail seq: 0 
[INFO ] 2024-07-08 15:05:28.335 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-08 15:05:28.336 - [测试共享挖掘Oracle-Mysql2][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-08 15:05:28.336 - [测试共享挖掘Oracle-Mysql2][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-08 15:05:28.339 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-08 15:05:28.340 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-08 15:05:28.357 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687708b66ab5ede8a35c768, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6687678eed8812650a8a3044_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-825204492, shareCdcTaskId=6687708bed8812650a8a33e8, connectionId=6687678eed8812650a8a3044) 
[INFO ] 2024-07-08 15:05:28.363 - [测试共享挖掘Oracle-Mysql2][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2.POLICY_测试共享挖掘Oracle-Mysql2', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-825204492', head seq: 0, tail seq: 0 
[INFO ] 2024-07-08 15:05:28.363 - [测试共享挖掘Oracle-Mysql2][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自TAPDATA2Oracle的共享挖掘任务_C##TAPDATA2.POLICY_测试共享挖掘Oracle-Mysql2, external storage name: ExternalStorage_SHARE_CDC_-825204492 
[INFO ] 2024-07-08 15:05:28.366 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-08 15:05:28.369 - [测试共享挖掘Oracle-Mysql2][POLICY] - Connector Oracle incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-08 15:05:28.369 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 1 
[INFO ] 2024-07-08 15:05:28.571 - [测试共享挖掘Oracle-Mysql2][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=1} 
[INFO ] 2024-07-08 15:07:30.678 - [测试共享挖掘Oracle-Mysql2][POLICY] - Node POLICY[f1809cff-aad5-4493-9d9e-643851f12346] running status set to false 
[INFO ] 2024-07-08 15:07:30.678 - [测试共享挖掘Oracle-Mysql2][POLICY] - Incremental sync completed 
[INFO ] 2024-07-08 15:07:30.799 - [测试共享挖掘Oracle-Mysql2][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-f1809cff-aad5-4493-9d9e-643851f12346 
[INFO ] 2024-07-08 15:07:30.799 - [测试共享挖掘Oracle-Mysql2][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-f1809cff-aad5-4493-9d9e-643851f12346 
[INFO ] 2024-07-08 15:07:30.803 - [测试共享挖掘Oracle-Mysql2][POLICY] - Node POLICY[f1809cff-aad5-4493-9d9e-643851f12346] schema data cleaned 
[INFO ] 2024-07-08 15:07:30.803 - [测试共享挖掘Oracle-Mysql2][POLICY] - Node POLICY[f1809cff-aad5-4493-9d9e-643851f12346] monitor closed 
[INFO ] 2024-07-08 15:07:30.813 - [测试共享挖掘Oracle-Mysql2][POLICY] - Node POLICY[f1809cff-aad5-4493-9d9e-643851f12346] close complete, cost 147 ms 
[INFO ] 2024-07-08 15:07:30.814 - [测试共享挖掘Oracle-Mysql2][POLICY2] - Node POLICY2[cb2a3545-fe37-4f13-91ab-01a7211233d2] running status set to false 
[INFO ] 2024-07-08 15:07:30.832 - [测试共享挖掘Oracle-Mysql2][POLICY2] - PDK connector node stopped: HazelcastTargetPdkDataNode-cb2a3545-fe37-4f13-91ab-01a7211233d2 
[INFO ] 2024-07-08 15:07:30.833 - [测试共享挖掘Oracle-Mysql2][POLICY2] - PDK connector node released: HazelcastTargetPdkDataNode-cb2a3545-fe37-4f13-91ab-01a7211233d2 
[INFO ] 2024-07-08 15:07:30.833 - [测试共享挖掘Oracle-Mysql2][POLICY2] - Node POLICY2[cb2a3545-fe37-4f13-91ab-01a7211233d2] schema data cleaned 
[INFO ] 2024-07-08 15:07:30.834 - [测试共享挖掘Oracle-Mysql2][POLICY2] - Node POLICY2[cb2a3545-fe37-4f13-91ab-01a7211233d2] monitor closed 
[INFO ] 2024-07-08 15:07:31.043 - [测试共享挖掘Oracle-Mysql2][POLICY2] - Node POLICY2[cb2a3545-fe37-4f13-91ab-01a7211233d2] close complete, cost 22 ms 
[INFO ] 2024-07-08 15:07:35.439 - [测试共享挖掘Oracle-Mysql2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-08 15:07:35.439 - [测试共享挖掘Oracle-Mysql2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5c177c52 
[INFO ] 2024-07-08 15:07:35.566 - [测试共享挖掘Oracle-Mysql2] - Stop task milestones: 668772c0ed8812650a8a3601(测试共享挖掘Oracle-Mysql2)  
[INFO ] 2024-07-08 15:07:35.566 - [测试共享挖掘Oracle-Mysql2] - Stopped task aspect(s) 
[INFO ] 2024-07-08 15:07:35.599 - [测试共享挖掘Oracle-Mysql2] - Snapshot order controller have been removed 
[INFO ] 2024-07-08 15:07:35.600 - [测试共享挖掘Oracle-Mysql2] - Remove memory task client succeed, task: 测试共享挖掘Oracle-Mysql2[668772c0ed8812650a8a3601] 
[INFO ] 2024-07-08 15:07:35.600 - [测试共享挖掘Oracle-Mysql2] - Destroy memory task client cache succeed, task: 测试共享挖掘Oracle-Mysql2[668772c0ed8812650a8a3601] 
