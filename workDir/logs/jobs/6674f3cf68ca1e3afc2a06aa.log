[INFO ] 2024-06-21 11:30:49.045 - [任务 4] - Task initialization... 
[INFO ] 2024-06-21 11:30:49.048 - [任务 4] - Start task milestones: 6674f3cf68ca1e3afc2a06aa(任务 4) 
[INFO ] 2024-06-21 11:30:49.249 - [任务 4] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-21 11:30:49.415 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-21 11:30:49.416 - [任务 4][copyChildTable] - Node copyChildTable[912d41fe-ff7c-442f-9b84-4944f6e06dda] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:30:49.421 - [任务 4][ChildTable] - Node ChildTable[5fe0b50f-49b6-41ee-9f37-20ea5981c89a] start preload schema,table counts: 1 
[INFO ] 2024-06-21 11:30:49.422 - [任务 4][copyChildTable] - Node copyChildTable[912d41fe-ff7c-442f-9b84-4944f6e06dda] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:30:49.422 - [任务 4][ChildTable] - Node ChildTable[5fe0b50f-49b6-41ee-9f37-20ea5981c89a] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 11:30:50.438 - [任务 4][copyChildTable] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-21 11:30:50.439 - [任务 4][ChildTable] - Source node "ChildTable" read batch size: 100 
[INFO ] 2024-06-21 11:30:50.440 - [任务 4][ChildTable] - Source node "ChildTable" event queue capacity: 200 
[INFO ] 2024-06-21 11:30:50.476 - [任务 4][ChildTable] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-21 11:30:50.476 - [任务 4][ChildTable] - batch offset found: {},stream offset found: {"currentStartLSN":"000036A10000B2780029","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-06-21 11:30:50.530 - [任务 4][ChildTable] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-21 11:30:50.530 - [任务 4][ChildTable] - Initial sync started 
[INFO ] 2024-06-21 11:30:50.537 - [任务 4][ChildTable] - Starting batch read, table name: ChildTable, offset: null 
[INFO ] 2024-06-21 11:30:50.679 - [任务 4][ChildTable] - Table ChildTable is going to be initial synced 
[INFO ] 2024-06-21 11:30:50.679 - [任务 4][ChildTable] - Table [ChildTable] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-21 11:30:50.712 - [任务 4][ChildTable] - Query table 'ChildTable' counts: 3 
[INFO ] 2024-06-21 11:30:50.712 - [任务 4][ChildTable] - Initial sync completed 
[INFO ] 2024-06-21 11:30:50.714 - [任务 4][ChildTable] - Incremental sync starting... 
[INFO ] 2024-06-21 11:30:50.714 - [任务 4][ChildTable] - Initial sync completed 
[INFO ] 2024-06-21 11:30:50.832 - [任务 4][ChildTable] - Starting stream read, table list: [ChildTable], offset: {"currentStartLSN":"000036A10000B2780029","tablesOffset":{},"ddlOffset":null} 
[INFO ] 2024-06-21 11:30:50.832 - [任务 4][ChildTable] - opened cdc tables: [Category_test, Category, a_test, test001_dummy_test, dummy612, Product, TEST_LENGTH, table_name, IdentityServerIdentityResourceProperties, orderitem, Category1, testTable, Supplier, SupplierInfo, TEST_DDL_001, reimbursement_approval, _tapdata_heartbeat_table, my_table, MyTable, t_9_1_3, ParentTable, t_9_1_1, testdate, test8, test9, Category1_test, test7, orders] 
[INFO ] 2024-06-21 11:30:50.832 - [任务 4][ChildTable] - building CT table for table ChildTable 
[INFO ] 2024-06-21 11:30:51.839 - [任务 4][ChildTable] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-06-21 11:30:52.082 - [任务 4][ChildTable] - Connector SQL Server incremental start succeed, tables: [ChildTable], data change syncing 
[INFO ] 2024-06-21 14:19:19.687 - [任务 4] - Start task milestones: 6674f3cf68ca1e3afc2a06aa(任务 4) 
[INFO ] 2024-06-21 14:19:19.688 - [任务 4] - Task initialization... 
[INFO ] 2024-06-21 14:19:20.565 - [任务 4] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-21 14:19:20.778 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-21 14:19:21.481 - [任务 4][copyChildTable] - Node copyChildTable[912d41fe-ff7c-442f-9b84-4944f6e06dda] start preload schema,table counts: 1 
[INFO ] 2024-06-21 14:19:21.492 - [任务 4][copyChildTable] - Node copyChildTable[912d41fe-ff7c-442f-9b84-4944f6e06dda] preload schema finished, cost 2 ms 
[INFO ] 2024-06-21 14:19:21.499 - [任务 4][ChildTable] - Node ChildTable[5fe0b50f-49b6-41ee-9f37-20ea5981c89a] start preload schema,table counts: 1 
[INFO ] 2024-06-21 14:19:21.499 - [任务 4][ChildTable] - Node ChildTable[5fe0b50f-49b6-41ee-9f37-20ea5981c89a] preload schema finished, cost 1 ms 
[INFO ] 2024-06-21 14:19:22.857 - [任务 4][copyChildTable] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-21 14:19:22.858 - [任务 4][ChildTable] - Source node "ChildTable" read batch size: 100 
[INFO ] 2024-06-21 14:19:22.859 - [任务 4][ChildTable] - Source node "ChildTable" event queue capacity: 200 
[INFO ] 2024-06-21 14:19:22.877 - [任务 4][ChildTable] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-21 14:19:22.912 - [任务 4][ChildTable] - batch offset found: {"ChildTable":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"currentStartLSN":"000036A10000B2780029","tablesOffset":{"ChildTable":"000036A10000B2780029"},"ddlOffset":"AAA2oQAAsngAKQ=="} 
[INFO ] 2024-06-21 14:19:22.912 - [任务 4][ChildTable] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-21 14:19:22.980 - [任务 4][ChildTable] - Incremental sync starting... 
[INFO ] 2024-06-21 14:19:22.985 - [任务 4][ChildTable] - Initial sync completed 
[INFO ] 2024-06-21 14:19:22.989 - [任务 4][ChildTable] - Starting stream read, table list: [ChildTable], offset: {"currentStartLSN":"000036A10000B2780029","tablesOffset":{"ChildTable":"000036A10000B2780029"},"ddlOffset":"AAA2oQAAsngAKQ=="} 
[INFO ] 2024-06-21 14:19:23.324 - [任务 4][ChildTable] - opened cdc tables: [Category_test, Category, ChildTable, a_test, test001_dummy_test, dummy612, Product, TEST_LENGTH, table_name, IdentityServerIdentityResourceProperties, orderitem, Category1, testTable, Supplier, SupplierInfo, TEST_DDL_001, reimbursement_approval, _tapdata_heartbeat_table, my_table, MyTable, t_9_1_3, ParentTable, t_9_1_1, testdate, test8, test9, Category1_test, test7, orders] 
[INFO ] 2024-06-21 14:19:23.454 - [任务 4][ChildTable] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-06-21 14:19:23.454 - [任务 4][ChildTable] - Connector SQL Server incremental start succeed, tables: [ChildTable], data change syncing 
[INFO ] 2024-06-21 15:03:26.653 - [任务 4] - Start task milestones: 6674f3cf68ca1e3afc2a06aa(任务 4) 
[INFO ] 2024-06-21 15:03:26.656 - [任务 4] - Task initialization... 
[INFO ] 2024-06-21 15:03:27.485 - [任务 4] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-21 15:03:27.562 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-21 15:03:28.197 - [任务 4][ChildTable] - Node ChildTable[5fe0b50f-49b6-41ee-9f37-20ea5981c89a] start preload schema,table counts: 1 
[INFO ] 2024-06-21 15:03:28.198 - [任务 4][ChildTable] - Node ChildTable[5fe0b50f-49b6-41ee-9f37-20ea5981c89a] preload schema finished, cost 3 ms 
[INFO ] 2024-06-21 15:03:28.215 - [任务 4][copyChildTable] - Node copyChildTable[912d41fe-ff7c-442f-9b84-4944f6e06dda] start preload schema,table counts: 1 
[INFO ] 2024-06-21 15:03:28.216 - [任务 4][copyChildTable] - Node copyChildTable[912d41fe-ff7c-442f-9b84-4944f6e06dda] preload schema finished, cost 0 ms 
[INFO ] 2024-06-21 15:03:29.070 - [任务 4][ChildTable] - Source node "ChildTable" read batch size: 100 
[INFO ] 2024-06-21 15:03:29.073 - [任务 4][copyChildTable] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-21 15:03:29.085 - [任务 4][ChildTable] - Source node "ChildTable" event queue capacity: 200 
[INFO ] 2024-06-21 15:03:29.115 - [任务 4][ChildTable] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-06-21 15:03:29.138 - [任务 4][ChildTable] - batch offset found: {"ChildTable":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"currentStartLSN":"000036A10000B2780029","tablesOffset":{"ChildTable":"000036A10000B2780029"},"ddlOffset":"AAA2oQAAsngAKQ=="} 
[INFO ] 2024-06-21 15:03:29.142 - [任务 4][ChildTable] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-21 15:03:29.236 - [任务 4][ChildTable] - Incremental sync starting... 
[INFO ] 2024-06-21 15:03:29.244 - [任务 4][ChildTable] - Initial sync completed 
[INFO ] 2024-06-21 15:03:29.245 - [任务 4][ChildTable] - Starting stream read, table list: [ChildTable], offset: {"currentStartLSN":"000036A10000B2780029","tablesOffset":{"ChildTable":"000036A10000B2780029"},"ddlOffset":"AAA2oQAAsngAKQ=="} 
[INFO ] 2024-06-21 15:03:29.937 - [任务 4][ChildTable] - opened cdc tables: [Category_test, Category, ChildTable, a_test, test001_dummy_test, dummy612, Product, TEST_LENGTH, table_name, IdentityServerIdentityResourceProperties, orderitem, Category1, testTable, Supplier, SupplierInfo, TEST_DDL_001, reimbursement_approval, _tapdata_heartbeat_table, my_table, MyTable, t_9_1_3, ParentTable, t_9_1_1, testdate, test8, test9, Category1_test, test7, orders] 
[INFO ] 2024-06-21 15:03:30.089 - [任务 4][ChildTable] - Start to reading cdc table, fetch size: 1, interval: 500 ms 
[INFO ] 2024-06-21 15:03:30.090 - [任务 4][ChildTable] - Connector SQL Server incremental start succeed, tables: [ChildTable], data change syncing 
[INFO ] 2024-06-21 17:09:42.608 - [任务 4] - Stop task milestones: 6674f3cf68ca1e3afc2a06aa(任务 4)  
[INFO ] 2024-06-21 17:09:42.970 - [任务 4][ChildTable] - Node ChildTable[5fe0b50f-49b6-41ee-9f37-20ea5981c89a] running status set to false 
[INFO ] 2024-06-21 17:09:46.029 - [任务 4][ChildTable] - PDK connector node stopped: HazelcastSourcePdkDataNode-5fe0b50f-49b6-41ee-9f37-20ea5981c89a 
[INFO ] 2024-06-21 17:09:46.029 - [任务 4][ChildTable] - PDK connector node released: HazelcastSourcePdkDataNode-5fe0b50f-49b6-41ee-9f37-20ea5981c89a 
[INFO ] 2024-06-21 17:09:46.032 - [任务 4][ChildTable] - Node ChildTable[5fe0b50f-49b6-41ee-9f37-20ea5981c89a] schema data cleaned 
[INFO ] 2024-06-21 17:09:46.032 - [任务 4][ChildTable] - Node ChildTable[5fe0b50f-49b6-41ee-9f37-20ea5981c89a] monitor closed 
[INFO ] 2024-06-21 17:09:46.039 - [任务 4][ChildTable] - Node ChildTable[5fe0b50f-49b6-41ee-9f37-20ea5981c89a] close complete, cost 3072 ms 
[INFO ] 2024-06-21 17:09:46.040 - [任务 4][copyChildTable] - Node copyChildTable[912d41fe-ff7c-442f-9b84-4944f6e06dda] running status set to false 
[INFO ] 2024-06-21 17:09:46.053 - [任务 4][copyChildTable] - PDK connector node stopped: HazelcastTargetPdkDataNode-912d41fe-ff7c-442f-9b84-4944f6e06dda 
[INFO ] 2024-06-21 17:09:46.053 - [任务 4][copyChildTable] - PDK connector node released: HazelcastTargetPdkDataNode-912d41fe-ff7c-442f-9b84-4944f6e06dda 
[INFO ] 2024-06-21 17:09:46.054 - [任务 4][copyChildTable] - Node copyChildTable[912d41fe-ff7c-442f-9b84-4944f6e06dda] schema data cleaned 
[INFO ] 2024-06-21 17:09:46.056 - [任务 4][copyChildTable] - Node copyChildTable[912d41fe-ff7c-442f-9b84-4944f6e06dda] monitor closed 
[INFO ] 2024-06-21 17:09:46.057 - [任务 4][copyChildTable] - Node copyChildTable[912d41fe-ff7c-442f-9b84-4944f6e06dda] close complete, cost 16 ms 
[INFO ] 2024-06-21 17:09:48.720 - [任务 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-21 17:09:48.720 - [任务 4] - Stopped task aspect(s) 
[INFO ] 2024-06-21 17:09:48.810 - [任务 4] - Snapshot order controller have been removed 
[INFO ] 2024-06-21 17:09:48.814 - [任务 4] - Remove memory task client succeed, task: 任务 4[6674f3cf68ca1e3afc2a06aa] 
[INFO ] 2024-06-21 17:09:48.814 - [任务 4] - Destroy memory task client cache succeed, task: 任务 4[6674f3cf68ca1e3afc2a06aa] 
