[TRACE] 2025-02-20 22:00:59.600 - [PG~Sybase 验证左右边空格] - Task initialization... 
[TRACE] 2025-02-20 22:00:59.808 - [PG~Sybase 验证左右边空格] - Start task milestones: 67b73561875cdc50c184f1b3(PG~Sybase 验证左右边空格) 
[INFO ] 2025-02-20 22:00:59.942 - [PG~Sybase 验证左右边空格] - Loading table structure completed 
[TRACE] 2025-02-20 22:01:00.041 - [PG~Sybase 验证左右边空格] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-20 22:01:00.047 - [PG~Sybase 验证左右边空格] - The engine receives PG~Sybase 验证左右边空格 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-20 22:01:00.111 - [PG~Sybase 验证左右边空格] - Task started 
[TRACE] 2025-02-20 22:01:00.118 - [PG~Sybase 验证左右边空格][Sybase] - Node Sybase[ac08d03c-e621-419c-aa11-627070e973b2] start preload schema,table counts: 2 
[TRACE] 2025-02-20 22:01:00.118 - [PG~Sybase 验证左右边空格][表编辑] - Node 表编辑[2803ee69-dedd-48d5-a4dd-ad82c4a8b730] start preload schema,table counts: 2 
[TRACE] 2025-02-20 22:01:00.122 - [PG~Sybase 验证左右边空格][Pg] - Node Pg[90c10ddb-dd91-43c8-9b70-43752d96f65f] start preload schema,table counts: 2 
[TRACE] 2025-02-20 22:01:00.122 - [PG~Sybase 验证左右边空格][Sybase] - Node Sybase[ac08d03c-e621-419c-aa11-627070e973b2] preload schema finished, cost 1 ms 
[TRACE] 2025-02-20 22:01:00.122 - [PG~Sybase 验证左右边空格][表编辑] - Node 表编辑[2803ee69-dedd-48d5-a4dd-ad82c4a8b730] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:01:00.122 - [PG~Sybase 验证左右边空格][Pg] - Node Pg[90c10ddb-dd91-43c8-9b70-43752d96f65f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:01:00.122 - [PG~Sybase 验证左右边空格][表编辑] - Node table_rename_processor(表编辑: 2803ee69-dedd-48d5-a4dd-ad82c4a8b730) enable batch process 
[INFO ] 2025-02-20 22:01:00.122 - [PG~Sybase 验证左右边空格][Pg] - Enable partition table support for source database 
[ERROR] 2025-02-20 22:01:00.828 - [PG~Sybase 验证左右边空格][Sybase] - Sink connector(Sybase) initialization error: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Full Stack Trace -->
java.lang.RuntimeException: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	... 9 more
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:01:00.884 - [PG~Sybase 验证左右边空格][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer 
[ERROR] 2025-02-20 22:01:00.889 - [PG~Sybase 验证左右边空格][Sybase] - Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Error Message -->
Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer

<-- Simple Stack Trace -->
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	...

<-- Full Stack Trace -->
java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:01:01.099 - [PG~Sybase 验证左右边空格][Sybase] - Job suspend in error handle 
[INFO ] 2025-02-20 22:01:01.397 - [PG~Sybase 验证左右边空格][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-02-20 22:01:01.399 - [PG~Sybase 验证左右边空格][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-02-20 22:01:01.399 - [PG~Sybase 验证左右边空格][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-02-20 22:01:01.399 - [PG~Sybase 验证左右边空格][Pg] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-20 22:01:01.399 - [PG~Sybase 验证左右边空格][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-20 22:01:01.535 - [PG~Sybase 验证左右边空格][Pg] - Starting batch read from 2 tables 
[TRACE] 2025-02-20 22:01:01.558 - [PG~Sybase 验证左右边空格][Pg] - Initial sync started 
[INFO ] 2025-02-20 22:01:01.561 - [PG~Sybase 验证左右边空格][Pg] - Starting batch read from table: td_null_col 
[TRACE] 2025-02-20 22:01:01.561 - [PG~Sybase 验证左右边空格][Pg] - Table td_null_col is going to be initial synced 
[INFO ] 2025-02-20 22:01:01.632 - [PG~Sybase 验证左右边空格][Pg] - Starting batch read from table: td_test_varchar 
[TRACE] 2025-02-20 22:01:01.633 - [PG~Sybase 验证左右边空格][Pg] - Table td_test_varchar is going to be initial synced 
[TRACE] 2025-02-20 22:01:01.645 - [PG~Sybase 验证左右边空格][Pg] - Node Pg[90c10ddb-dd91-43c8-9b70-43752d96f65f] running status set to false 
[INFO ] 2025-02-20 22:01:01.648 - [PG~Sybase 验证左右边空格][Pg] - Retry operation null failed, total cost 14:01:01.644000 
[TRACE] 2025-02-20 22:01:01.665 - [PG~Sybase 验证左右边空格][Pg] - Initial sync completed 
[TRACE] 2025-02-20 22:01:01.665 - [PG~Sybase 验证左右边空格][Pg] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.InterruptedException 
[TRACE] 2025-02-20 22:01:01.672 - [PG~Sybase 验证左右边空格][Pg] - Query snapshot row size completed: Pg(90c10ddb-dd91-43c8-9b70-43752d96f65f) 
[ERROR] 2025-02-20 22:01:01.674 - [PG~Sybase 验证左右边空格][Pg] - java.lang.RuntimeException: java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.handleThrowable(HazelcastSourcePdkDataNode.java:445)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:414)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:356)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:269)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:490)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:412)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:219)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:134)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:498)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 13 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	... 17 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:146)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutHashSplit(CommonDbConnector.java:697)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:691)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:577)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 21 more
Caused by: java.lang.InterruptedException
	at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:944)
	at com.zaxxer.hikari.util.ConcurrentBag.borrow(ConcurrentBag.java:151)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:180)
	at com.zaxxer.hikari.pool.HikariPool.getConnection(HikariPool.java:162)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:128)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 26 more

[TRACE] 2025-02-20 22:01:01.762 - [PG~Sybase 验证左右边空格][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_90c10ddb-dd91-43c8-9b70-43752d96f65f_1740060060958 
[TRACE] 2025-02-20 22:01:01.762 - [PG~Sybase 验证左右边空格][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_90c10ddb-dd91-43c8-9b70-43752d96f65f_1740060060958 
[TRACE] 2025-02-20 22:01:01.763 - [PG~Sybase 验证左右边空格][Pg] - Node Pg[90c10ddb-dd91-43c8-9b70-43752d96f65f] schema data cleaned 
[TRACE] 2025-02-20 22:01:01.763 - [PG~Sybase 验证左右边空格][Pg] - Node Pg[90c10ddb-dd91-43c8-9b70-43752d96f65f] monitor closed 
[TRACE] 2025-02-20 22:01:01.767 - [PG~Sybase 验证左右边空格][Pg] - Node Pg[90c10ddb-dd91-43c8-9b70-43752d96f65f] close complete, cost 129 ms 
[TRACE] 2025-02-20 22:01:01.767 - [PG~Sybase 验证左右边空格][表编辑] - Node 表编辑[2803ee69-dedd-48d5-a4dd-ad82c4a8b730] running status set to false 
[TRACE] 2025-02-20 22:01:01.768 - [PG~Sybase 验证左右边空格][表编辑] - Node 表编辑[2803ee69-dedd-48d5-a4dd-ad82c4a8b730] schema data cleaned 
[TRACE] 2025-02-20 22:01:01.768 - [PG~Sybase 验证左右边空格][表编辑] - Node 表编辑[2803ee69-dedd-48d5-a4dd-ad82c4a8b730] monitor closed 
[TRACE] 2025-02-20 22:01:01.769 - [PG~Sybase 验证左右边空格][表编辑] - Node 表编辑[2803ee69-dedd-48d5-a4dd-ad82c4a8b730] close complete, cost 2 ms 
[TRACE] 2025-02-20 22:01:01.769 - [PG~Sybase 验证左右边空格][Sybase] - Node Sybase[ac08d03c-e621-419c-aa11-627070e973b2] running status set to false 
[TRACE] 2025-02-20 22:01:01.888 - [PG~Sybase 验证左右边空格][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_ac08d03c-e621-419c-aa11-627070e973b2_1740060060732 
[TRACE] 2025-02-20 22:01:01.888 - [PG~Sybase 验证左右边空格][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_ac08d03c-e621-419c-aa11-627070e973b2_1740060060732 
[TRACE] 2025-02-20 22:01:01.888 - [PG~Sybase 验证左右边空格][Sybase] - Node Sybase[ac08d03c-e621-419c-aa11-627070e973b2] schema data cleaned 
[TRACE] 2025-02-20 22:01:01.890 - [PG~Sybase 验证左右边空格][Sybase] - Node Sybase[ac08d03c-e621-419c-aa11-627070e973b2] monitor closed 
[TRACE] 2025-02-20 22:01:01.890 - [PG~Sybase 验证左右边空格][Sybase] - Node Sybase[ac08d03c-e621-419c-aa11-627070e973b2] close complete, cost 121 ms 
[INFO ] 2025-02-20 22:01:03.847 - [PG~Sybase 验证左右边空格] - Task [PG~Sybase 验证左右边空格] cannot retry, reason: Task retry service not start 
[TRACE] 2025-02-20 22:01:03.868 - [PG~Sybase 验证左右边空格] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-20 22:01:03.868 - [PG~Sybase 验证左右边空格] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4d9c9eb2 
[TRACE] 2025-02-20 22:01:04.021 - [PG~Sybase 验证左右边空格] - Stop task milestones: 67b73561875cdc50c184f1b3(PG~Sybase 验证左右边空格)  
[TRACE] 2025-02-20 22:01:04.022 - [PG~Sybase 验证左右边空格] - Stopped task aspect(s) 
[TRACE] 2025-02-20 22:01:04.022 - [PG~Sybase 验证左右边空格] - Snapshot order controller have been removed 
[INFO ] 2025-02-20 22:01:04.022 - [PG~Sybase 验证左右边空格] - Task stopped. 
[TRACE] 2025-02-20 22:01:04.037 - [PG~Sybase 验证左右边空格] - Remove memory task client succeed, task: PG~Sybase 验证左右边空格[67b73561875cdc50c184f1b3] 
[TRACE] 2025-02-20 22:01:04.039 - [PG~Sybase 验证左右边空格] - Destroy memory task client cache succeed, task: PG~Sybase 验证左右边空格[67b73561875cdc50c184f1b3] 
[TRACE] 2025-02-20 22:01:34.377 - [PG~Sybase 验证左右边空格] - Task initialization... 
[TRACE] 2025-02-20 22:01:34.492 - [PG~Sybase 验证左右边空格] - Start task milestones: 67b73561875cdc50c184f1b3(PG~Sybase 验证左右边空格) 
[INFO ] 2025-02-20 22:01:34.493 - [PG~Sybase 验证左右边空格] - Loading table structure completed 
[TRACE] 2025-02-20 22:01:34.567 - [PG~Sybase 验证左右边空格] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-20 22:01:34.567 - [PG~Sybase 验证左右边空格] - The engine receives PG~Sybase 验证左右边空格 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-20 22:01:34.613 - [PG~Sybase 验证左右边空格] - Task started 
[TRACE] 2025-02-20 22:01:34.613 - [PG~Sybase 验证左右边空格][Sybase] - Node Sybase[ac08d03c-e621-419c-aa11-627070e973b2] start preload schema,table counts: 2 
[TRACE] 2025-02-20 22:01:34.613 - [PG~Sybase 验证左右边空格][表编辑] - Node 表编辑[2803ee69-dedd-48d5-a4dd-ad82c4a8b730] start preload schema,table counts: 2 
[TRACE] 2025-02-20 22:01:34.613 - [PG~Sybase 验证左右边空格][Pg] - Node Pg[90c10ddb-dd91-43c8-9b70-43752d96f65f] start preload schema,table counts: 2 
[TRACE] 2025-02-20 22:01:34.613 - [PG~Sybase 验证左右边空格][Pg] - Node Pg[90c10ddb-dd91-43c8-9b70-43752d96f65f] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:01:34.614 - [PG~Sybase 验证左右边空格][Sybase] - Node Sybase[ac08d03c-e621-419c-aa11-627070e973b2] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:01:34.614 - [PG~Sybase 验证左右边空格][表编辑] - Node 表编辑[2803ee69-dedd-48d5-a4dd-ad82c4a8b730] preload schema finished, cost 0 ms 
[INFO ] 2025-02-20 22:01:34.614 - [PG~Sybase 验证左右边空格][Pg] - Enable partition table support for source database 
[TRACE] 2025-02-20 22:01:34.615 - [PG~Sybase 验证左右边空格][表编辑] - Node table_rename_processor(表编辑: 2803ee69-dedd-48d5-a4dd-ad82c4a8b730) enable batch process 
[ERROR] 2025-02-20 22:01:35.490 - [PG~Sybase 验证左右边空格][Sybase] - Sink connector(Sybase) initialization error: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Full Stack Trace -->
java.lang.RuntimeException: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	... 9 more
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:01:35.502 - [PG~Sybase 验证左右边空格][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer 
[ERROR] 2025-02-20 22:01:35.506 - [PG~Sybase 验证左右边空格][Sybase] - Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Error Message -->
Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer

<-- Simple Stack Trace -->
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	...

<-- Full Stack Trace -->
java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:01:35.712 - [PG~Sybase 验证左右边空格][Sybase] - Job suspend in error handle 
[INFO ] 2025-02-20 22:01:35.826 - [PG~Sybase 验证左右边空格][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-02-20 22:01:35.826 - [PG~Sybase 验证左右边空格][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-02-20 22:01:35.839 - [PG~Sybase 验证左右边空格][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-02-20 22:01:35.842 - [PG~Sybase 验证左右边空格][Pg] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-20 22:01:35.842 - [PG~Sybase 验证左右边空格][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-20 22:01:35.940 - [PG~Sybase 验证左右边空格][Pg] - Starting batch read from 2 tables 
[TRACE] 2025-02-20 22:01:35.941 - [PG~Sybase 验证左右边空格][Pg] - Initial sync started 
[INFO ] 2025-02-20 22:01:35.944 - [PG~Sybase 验证左右边空格][Pg] - Starting batch read from table: td_null_col 
[TRACE] 2025-02-20 22:01:35.944 - [PG~Sybase 验证左右边空格][Pg] - Table td_null_col is going to be initial synced 
[TRACE] 2025-02-20 22:01:36.052 - [PG~Sybase 验证左右边空格][Pg] - Node Pg[90c10ddb-dd91-43c8-9b70-43752d96f65f] running status set to false 
[TRACE] 2025-02-20 22:01:36.055 - [PG~Sybase 验证左右边空格][Pg] - Initial sync completed 
[INFO ] 2025-02-20 22:01:36.055 - [PG~Sybase 验证左右边空格][Pg] - Batch read completed. 
[INFO ] 2025-02-20 22:01:36.055 - [PG~Sybase 验证左右边空格][Pg] - Task run completed 
[TRACE] 2025-02-20 22:01:36.235 - [PG~Sybase 验证左右边空格][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_90c10ddb-dd91-43c8-9b70-43752d96f65f_1740060095289 
[TRACE] 2025-02-20 22:01:36.235 - [PG~Sybase 验证左右边空格][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_90c10ddb-dd91-43c8-9b70-43752d96f65f_1740060095289 
[TRACE] 2025-02-20 22:01:36.235 - [PG~Sybase 验证左右边空格][Pg] - Node Pg[90c10ddb-dd91-43c8-9b70-43752d96f65f] schema data cleaned 
[TRACE] 2025-02-20 22:01:36.235 - [PG~Sybase 验证左右边空格][Pg] - Node Pg[90c10ddb-dd91-43c8-9b70-43752d96f65f] monitor closed 
[TRACE] 2025-02-20 22:01:36.240 - [PG~Sybase 验证左右边空格][Pg] - Node Pg[90c10ddb-dd91-43c8-9b70-43752d96f65f] close complete, cost 216 ms 
[TRACE] 2025-02-20 22:01:36.241 - [PG~Sybase 验证左右边空格][表编辑] - Node 表编辑[2803ee69-dedd-48d5-a4dd-ad82c4a8b730] running status set to false 
[TRACE] 2025-02-20 22:01:36.241 - [PG~Sybase 验证左右边空格][表编辑] - Node 表编辑[2803ee69-dedd-48d5-a4dd-ad82c4a8b730] schema data cleaned 
[TRACE] 2025-02-20 22:01:36.241 - [PG~Sybase 验证左右边空格][表编辑] - Node 表编辑[2803ee69-dedd-48d5-a4dd-ad82c4a8b730] monitor closed 
[TRACE] 2025-02-20 22:01:36.241 - [PG~Sybase 验证左右边空格][表编辑] - Node 表编辑[2803ee69-dedd-48d5-a4dd-ad82c4a8b730] close complete, cost 1 ms 
[TRACE] 2025-02-20 22:01:36.241 - [PG~Sybase 验证左右边空格][Sybase] - Node Sybase[ac08d03c-e621-419c-aa11-627070e973b2] running status set to false 
[INFO ] 2025-02-20 22:01:36.258 - [PG~Sybase 验证左右边空格][Pg] - Retry operation null failed, total cost 14:01:36.254000 
[WARN ] 2025-02-20 22:01:36.376 - [PG~Sybase 验证左右边空格][Pg] - Query snapshot row size failed: java.lang.RuntimeException: io.tapdata.exception.NodeException: Count td_test_varchar failed: An I/O error occurred while sending to the backend.
java.util.concurrent.CompletionException: java.lang.RuntimeException: io.tapdata.exception.NodeException: Count td_test_varchar failed: An I/O error occurred while sending to the backend.
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:273)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:280)
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1643)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: io.tapdata.exception.NodeException: Count td_test_varchar failed: An I/O error occurred while sending to the backend.
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.doCountSynchronously(HazelcastSourcePdkBaseNode.java:1569)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$asyncCountTable$47(HazelcastSourcePdkBaseNode.java:1615)
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640)
	... 4 more
Caused by: io.tapdata.exception.NodeException: Count td_test_varchar failed: An I/O error occurred while sending to the backend.
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$doCountSynchronously$46(HazelcastSourcePdkBaseNode.java:1572)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 8 more
Caused by: io.tapdata.exception.NodeException: Count td_test_varchar failed: An I/O error occurred while sending to the backend.
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$null$45(HazelcastSourcePdkBaseNode.java:1590)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: org.postgresql.util.PSQLException: An I/O error occurred while sending to the backend.
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:382)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:329)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:315)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:291)
	at org.postgresql.jdbc.PgStatement.executeQuery(PgStatement.java:243)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.queryWithNext(JdbcContext.java:77)
	at io.tapdata.common.CommonDbConnector.batchCount(CommonDbConnector.java:385)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkBaseNode.lambda$null$45(HazelcastSourcePdkBaseNode.java:1580)
	... 17 more
Caused by: java.net.SocketException: Socket closed
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at org.postgresql.core.VisibleBufferedInputStream.readMore(VisibleBufferedInputStream.java:161)
	at org.postgresql.core.VisibleBufferedInputStream.ensureBytes(VisibleBufferedInputStream.java:128)
	at org.postgresql.core.VisibleBufferedInputStream.ensureBytes(VisibleBufferedInputStream.java:113)
	at org.postgresql.core.VisibleBufferedInputStream.read(VisibleBufferedInputStream.java:73)
	at org.postgresql.core.PGStream.receiveChar(PGStream.java:453)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2119)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	... 28 more
 
[TRACE] 2025-02-20 22:01:36.377 - [PG~Sybase 验证左右边空格][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_ac08d03c-e621-419c-aa11-627070e973b2_1740060095399 
[TRACE] 2025-02-20 22:01:36.377 - [PG~Sybase 验证左右边空格][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_ac08d03c-e621-419c-aa11-627070e973b2_1740060095399 
[TRACE] 2025-02-20 22:01:36.377 - [PG~Sybase 验证左右边空格][Sybase] - Node Sybase[ac08d03c-e621-419c-aa11-627070e973b2] schema data cleaned 
[TRACE] 2025-02-20 22:01:36.377 - [PG~Sybase 验证左右边空格][Sybase] - Node Sybase[ac08d03c-e621-419c-aa11-627070e973b2] monitor closed 
[TRACE] 2025-02-20 22:01:36.585 - [PG~Sybase 验证左右边空格][Sybase] - Node Sybase[ac08d03c-e621-419c-aa11-627070e973b2] close complete, cost 135 ms 
[INFO ] 2025-02-20 22:01:39.100 - [PG~Sybase 验证左右边空格] - Task [PG~Sybase 验证左右边空格] cannot retry, reason: Task retry service not start 
[TRACE] 2025-02-20 22:01:39.100 - [PG~Sybase 验证左右边空格] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-20 22:01:39.101 - [PG~Sybase 验证左右边空格] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3b8626a0 
[TRACE] 2025-02-20 22:01:39.254 - [PG~Sybase 验证左右边空格] - Stop task milestones: 67b73561875cdc50c184f1b3(PG~Sybase 验证左右边空格)  
[TRACE] 2025-02-20 22:01:39.257 - [PG~Sybase 验证左右边空格] - Stopped task aspect(s) 
[TRACE] 2025-02-20 22:01:39.258 - [PG~Sybase 验证左右边空格] - Snapshot order controller have been removed 
[INFO ] 2025-02-20 22:01:39.258 - [PG~Sybase 验证左右边空格] - Task stopped. 
[TRACE] 2025-02-20 22:01:39.278 - [PG~Sybase 验证左右边空格] - Remove memory task client succeed, task: PG~Sybase 验证左右边空格[67b73561875cdc50c184f1b3] 
[TRACE] 2025-02-20 22:01:39.279 - [PG~Sybase 验证左右边空格] - Destroy memory task client cache succeed, task: PG~Sybase 验证左右边空格[67b73561875cdc50c184f1b3] 
[TRACE] 2025-02-20 22:02:29.754 - [PG~Sybase 验证左右边空格] - Task initialization... 
[TRACE] 2025-02-20 22:02:29.754 - [PG~Sybase 验证左右边空格] - Start task milestones: 67b73561875cdc50c184f1b3(PG~Sybase 验证左右边空格) 
[INFO ] 2025-02-20 22:02:29.880 - [PG~Sybase 验证左右边空格] - Loading table structure completed 
[TRACE] 2025-02-20 22:02:29.880 - [PG~Sybase 验证左右边空格] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-20 22:02:29.926 - [PG~Sybase 验证左右边空格] - The engine receives PG~Sybase 验证左右边空格 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-20 22:02:29.926 - [PG~Sybase 验证左右边空格] - Task started 
[TRACE] 2025-02-20 22:02:29.950 - [PG~Sybase 验证左右边空格][表编辑] - Node 表编辑[2803ee69-dedd-48d5-a4dd-ad82c4a8b730] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:02:29.951 - [PG~Sybase 验证左右边空格][表编辑] - Node 表编辑[2803ee69-dedd-48d5-a4dd-ad82c4a8b730] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:02:29.951 - [PG~Sybase 验证左右边空格][表编辑] - Node table_rename_processor(表编辑: 2803ee69-dedd-48d5-a4dd-ad82c4a8b730) enable batch process 
[TRACE] 2025-02-20 22:02:29.951 - [PG~Sybase 验证左右边空格][Sybase] - Node Sybase[ac08d03c-e621-419c-aa11-627070e973b2] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:02:29.955 - [PG~Sybase 验证左右边空格][Sybase] - Node Sybase[ac08d03c-e621-419c-aa11-627070e973b2] preload schema finished, cost 0 ms 
[TRACE] 2025-02-20 22:02:29.957 - [PG~Sybase 验证左右边空格][Pg] - Node Pg[90c10ddb-dd91-43c8-9b70-43752d96f65f] start preload schema,table counts: 1 
[TRACE] 2025-02-20 22:02:29.957 - [PG~Sybase 验证左右边空格][Pg] - Node Pg[90c10ddb-dd91-43c8-9b70-43752d96f65f] preload schema finished, cost 0 ms 
[ERROR] 2025-02-20 22:02:30.621 - [PG~Sybase 验证左右边空格][Sybase] - Sink connector(Sybase) initialization error: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Full Stack Trace -->
java.lang.RuntimeException: Failed to init pdk connector, database type: sybase-io.tapdata-1.0-SNAPSHOT-public, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:206)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	... 9 more
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:02:30.625 - [PG~Sybase 验证左右边空格][Sybase] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer 
[ERROR] 2025-02-20 22:02:30.655 - [PG~Sybase 验证左右边空格][Sybase] - Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer <-- Error Message -->
Unknown PDK exception occur, java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer

<-- Simple Stack Trace -->
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	...

<-- Full Stack Trace -->
java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:188)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.connectorNodeInit(HazelcastPdkBaseNode.java:204)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createPdkAndInit(HazelcastTargetPdkBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$doInit$1(HazelcastTargetPdkBaseNode.java:188)
	at io.tapdata.pdk.core.async.ThreadPoolExecutorEx.lambda$submitSync$0(ThreadPoolExecutorEx.java:67)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.ClassCastException: java.lang.String cannot be cast to java.lang.Integer
	at io.tapdata.sybase.extend.SybaseConfig$$BeanMapByCGLIB$$8aae2764.put(<generated>)
	at net.sf.cglib.beans.BeanMap.put(BeanMap.java:172)
	at net.sf.cglib.beans.BeanMap.putAll(BeanMap.java:245)
	at io.tapdata.pdk.core.api.impl.BeanUtilsImpl.mapToBean(BeanUtilsImpl.java:33)
	at io.tapdata.common.CommonDbConfig.load(CommonDbConfig.java:119)
	at io.tapdata.sybase.extend.SybaseConfig.load(SybaseConfig.java:103)
	at io.tapdata.sybase.SybaseConnectorV2.lambda$onStart$0(SybaseConnectorV2.java:88)
	at io.tapdata.base.ConnectorBase.isConnectorStarted(ConnectorBase.java:340)
	at io.tapdata.sybase.SybaseConnectorV2.onStart(SybaseConnectorV2.java:82)
	at io.tapdata.base.ConnectorBase.init(ConnectorBase.java:285)
	at io.tapdata.pdk.core.api.ConnectorNode.connectorInit(ConnectorNode.java:99)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.lambda$connectorNodeInit$4(HazelcastPdkBaseNode.java:204)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more

[TRACE] 2025-02-20 22:02:30.656 - [PG~Sybase 验证左右边空格][Sybase] - Job suspend in error handle 
[INFO ] 2025-02-20 22:02:31.168 - [PG~Sybase 验证左右边空格][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-02-20 22:02:31.168 - [PG~Sybase 验证左右边空格][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-02-20 22:02:31.168 - [PG~Sybase 验证左右边空格][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-02-20 22:02:31.169 - [PG~Sybase 验证左右边空格][Pg] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-02-20 22:02:31.245 - [PG~Sybase 验证左右边空格][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-20 22:02:31.245 - [PG~Sybase 验证左右边空格][Pg] - Starting batch read from 1 tables 
[TRACE] 2025-02-20 22:02:31.268 - [PG~Sybase 验证左右边空格][Pg] - Initial sync started 
[INFO ] 2025-02-20 22:02:31.268 - [PG~Sybase 验证左右边空格][Pg] - Starting batch read from table: td_null_col 
[TRACE] 2025-02-20 22:02:31.313 - [PG~Sybase 验证左右边空格][Pg] - Table td_null_col is going to be initial synced 
[TRACE] 2025-02-20 22:02:31.314 - [PG~Sybase 验证左右边空格][Pg] - Query snapshot row size completed: Pg(90c10ddb-dd91-43c8-9b70-43752d96f65f) 
[TRACE] 2025-02-20 22:02:31.357 - [PG~Sybase 验证左右边空格][Pg] - Node Pg[90c10ddb-dd91-43c8-9b70-43752d96f65f] running status set to false 
[TRACE] 2025-02-20 22:02:31.359 - [PG~Sybase 验证左右边空格][Pg] - Initial sync completed 
[INFO ] 2025-02-20 22:02:31.360 - [PG~Sybase 验证左右边空格][Pg] - Batch read completed. 
[INFO ] 2025-02-20 22:02:31.360 - [PG~Sybase 验证左右边空格][Pg] - Task run completed 
[TRACE] 2025-02-20 22:02:31.503 - [PG~Sybase 验证左右边空格][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_90c10ddb-dd91-43c8-9b70-43752d96f65f_1740060150716 
[TRACE] 2025-02-20 22:02:31.503 - [PG~Sybase 验证左右边空格][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_90c10ddb-dd91-43c8-9b70-43752d96f65f_1740060150716 
[TRACE] 2025-02-20 22:02:31.503 - [PG~Sybase 验证左右边空格][Pg] - Node Pg[90c10ddb-dd91-43c8-9b70-43752d96f65f] schema data cleaned 
[TRACE] 2025-02-20 22:02:31.504 - [PG~Sybase 验证左右边空格][Pg] - Node Pg[90c10ddb-dd91-43c8-9b70-43752d96f65f] monitor closed 
[TRACE] 2025-02-20 22:02:31.507 - [PG~Sybase 验证左右边空格][Pg] - Node Pg[90c10ddb-dd91-43c8-9b70-43752d96f65f] close complete, cost 157 ms 
[TRACE] 2025-02-20 22:02:31.508 - [PG~Sybase 验证左右边空格][表编辑] - Node 表编辑[2803ee69-dedd-48d5-a4dd-ad82c4a8b730] running status set to false 
[TRACE] 2025-02-20 22:02:31.509 - [PG~Sybase 验证左右边空格][表编辑] - Node 表编辑[2803ee69-dedd-48d5-a4dd-ad82c4a8b730] schema data cleaned 
[TRACE] 2025-02-20 22:02:31.509 - [PG~Sybase 验证左右边空格][表编辑] - Node 表编辑[2803ee69-dedd-48d5-a4dd-ad82c4a8b730] monitor closed 
[TRACE] 2025-02-20 22:02:31.510 - [PG~Sybase 验证左右边空格][表编辑] - Node 表编辑[2803ee69-dedd-48d5-a4dd-ad82c4a8b730] close complete, cost 2 ms 
[TRACE] 2025-02-20 22:02:31.510 - [PG~Sybase 验证左右边空格][Sybase] - Node Sybase[ac08d03c-e621-419c-aa11-627070e973b2] running status set to false 
[TRACE] 2025-02-20 22:02:31.637 - [PG~Sybase 验证左右边空格][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_ac08d03c-e621-419c-aa11-627070e973b2_1740060150503 
[TRACE] 2025-02-20 22:02:31.637 - [PG~Sybase 验证左右边空格][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_ac08d03c-e621-419c-aa11-627070e973b2_1740060150503 
[TRACE] 2025-02-20 22:02:31.637 - [PG~Sybase 验证左右边空格][Sybase] - Node Sybase[ac08d03c-e621-419c-aa11-627070e973b2] schema data cleaned 
[TRACE] 2025-02-20 22:02:31.637 - [PG~Sybase 验证左右边空格][Sybase] - Node Sybase[ac08d03c-e621-419c-aa11-627070e973b2] monitor closed 
[TRACE] 2025-02-20 22:02:31.839 - [PG~Sybase 验证左右边空格][Sybase] - Node Sybase[ac08d03c-e621-419c-aa11-627070e973b2] close complete, cost 127 ms 
[INFO ] 2025-02-20 22:02:34.349 - [PG~Sybase 验证左右边空格] - Task [PG~Sybase 验证左右边空格] cannot retry, reason: Task retry service not start 
[TRACE] 2025-02-20 22:02:34.350 - [PG~Sybase 验证左右边空格] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-20 22:02:34.480 - [PG~Sybase 验证左右边空格] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@36ea03ed 
[TRACE] 2025-02-20 22:02:34.481 - [PG~Sybase 验证左右边空格] - Stop task milestones: 67b73561875cdc50c184f1b3(PG~Sybase 验证左右边空格)  
[TRACE] 2025-02-20 22:02:34.506 - [PG~Sybase 验证左右边空格] - Stopped task aspect(s) 
[TRACE] 2025-02-20 22:02:34.507 - [PG~Sybase 验证左右边空格] - Snapshot order controller have been removed 
[INFO ] 2025-02-20 22:02:34.507 - [PG~Sybase 验证左右边空格] - Task stopped. 
[TRACE] 2025-02-20 22:02:34.525 - [PG~Sybase 验证左右边空格] - Remove memory task client succeed, task: PG~Sybase 验证左右边空格[67b73561875cdc50c184f1b3] 
[TRACE] 2025-02-20 22:02:34.527 - [PG~Sybase 验证左右边空格] - Destroy memory task client cache succeed, task: PG~Sybase 验证左右边空格[67b73561875cdc50c184f1b3] 
