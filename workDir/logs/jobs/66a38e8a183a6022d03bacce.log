[INFO ] 2024-07-26 19:54:50.805 - [Heartbeat-source - Copy] - Start task milestones: 66a38e8a183a6022d03bacce(Heartbeat-source - Copy) 
[INFO ] 2024-07-26 19:54:50.980 - [Heartbeat-source - Copy] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:54:51.105 - [Heartbeat-source - Copy] - The engine receives Heartbeat-source - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 19:54:51.214 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[316826e1-9830-4d95-8d40-c7bc8493613b] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:54:51.216 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[316826e1-9830-4d95-8d40-c7bc8493613b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 19:54:51.263 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3d27fbdd-c6ec-4fc0-b60b-40b203070ea4] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:54:51.268 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3d27fbdd-c6ec-4fc0-b60b-40b203070ea4] preload schema finished, cost 0 ms 
[INFO ] 2024-07-26 19:54:52.547 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[WARN ] 2024-07-26 19:54:52.548 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Index [{id=1}] already exists but options is inconsistent, will ignore creating this index, server error detail message: Command failed with error 85 (IndexOptionsConflict): 'Index with name: id_1 already exists with different options' on server localhost:27017. The full response is {"operationTime": {"$timestamp": {"t": 1721994892, "i": 26}}, "ok": 0.0, "errmsg": "Index with name: id_1 already exists with different options", "code": 85, "codeName": "IndexOptionsConflict", "$clusterTime": {"clusterTime": {"$timestamp": {"t": 1721994892, "i": 26}}, "signature": {"hash": {"$binary": {"base64": "EIoDmkLz/+CMMy/446c3JQtleGk=", "subType": "00"}}, "keyId": 7376103549123428362}}} 
[INFO ] 2024-07-26 19:54:53.298 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-26 19:54:53.300 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-26 19:54:53.300 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-26 19:54:53.358 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721994893298,"lastTimes":1721994893298,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 19:54:53.380 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-26 19:54:53.393 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-26 19:54:53.407 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-26 19:54:53.407 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-26 19:54:53.410 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-26 19:54:53.410 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1721994893298,"lastTimes":1721994893298,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-26 19:54:53.415 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-26 19:54:53.415 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 19:55:18.552 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3d27fbdd-c6ec-4fc0-b60b-40b203070ea4] running status set to false 
[INFO ] 2024-07-26 19:55:18.554 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-26 19:55:18.566 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-3d27fbdd-c6ec-4fc0-b60b-40b203070ea4 
[INFO ] 2024-07-26 19:55:18.566 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-3d27fbdd-c6ec-4fc0-b60b-40b203070ea4 
[INFO ] 2024-07-26 19:55:18.567 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3d27fbdd-c6ec-4fc0-b60b-40b203070ea4] schema data cleaned 
[INFO ] 2024-07-26 19:55:18.567 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3d27fbdd-c6ec-4fc0-b60b-40b203070ea4] monitor closed 
[INFO ] 2024-07-26 19:55:18.569 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3d27fbdd-c6ec-4fc0-b60b-40b203070ea4] close complete, cost 57 ms 
[INFO ] 2024-07-26 19:55:18.569 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[316826e1-9830-4d95-8d40-c7bc8493613b] running status set to false 
[INFO ] 2024-07-26 19:55:18.589 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-316826e1-9830-4d95-8d40-c7bc8493613b 
[INFO ] 2024-07-26 19:55:18.589 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-316826e1-9830-4d95-8d40-c7bc8493613b 
[INFO ] 2024-07-26 19:55:18.589 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[316826e1-9830-4d95-8d40-c7bc8493613b] schema data cleaned 
[INFO ] 2024-07-26 19:55:18.589 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[316826e1-9830-4d95-8d40-c7bc8493613b] monitor closed 
[INFO ] 2024-07-26 19:55:18.790 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[316826e1-9830-4d95-8d40-c7bc8493613b] close complete, cost 20 ms 
[INFO ] 2024-07-26 19:55:22.487 - [Heartbeat-source - Copy] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-26 19:55:22.488 - [Heartbeat-source - Copy] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1193ce11 
[INFO ] 2024-07-26 19:55:22.488 - [Heartbeat-source - Copy] - Stop task milestones: 66a38e8a183a6022d03bacce(Heartbeat-source - Copy)  
[INFO ] 2024-07-26 19:55:22.601 - [Heartbeat-source - Copy] - Stopped task aspect(s) 
[INFO ] 2024-07-26 19:55:22.623 - [Heartbeat-source - Copy] - Snapshot order controller have been removed 
[INFO ] 2024-07-26 19:55:22.623 - [Heartbeat-source - Copy] - Remove memory task client succeed, task: Heartbeat-source - Copy[66a38e8a183a6022d03bacce] 
[INFO ] 2024-07-26 19:55:22.825 - [Heartbeat-source - Copy] - Destroy memory task client cache succeed, task: Heartbeat-source - Copy[66a38e8a183a6022d03bacce] 
[INFO ] 2024-07-26 19:58:49.490 - [Heartbeat-source - Copy] - Start task milestones: 66a38e8a183a6022d03bacce(Heartbeat-source - Copy) 
[INFO ] 2024-07-26 19:59:25.047 - [Heartbeat-source - Copy] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-26 19:59:25.083 - [Heartbeat-source - Copy] - The engine receives Heartbeat-source - Copy task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-26 19:59:25.136 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[316826e1-9830-4d95-8d40-c7bc8493613b] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:59:25.136 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3d27fbdd-c6ec-4fc0-b60b-40b203070ea4] start preload schema,table counts: 1 
[INFO ] 2024-07-26 19:59:25.137 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[316826e1-9830-4d95-8d40-c7bc8493613b] preload schema finished, cost 1 ms 
[INFO ] 2024-07-26 19:59:25.137 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3d27fbdd-c6ec-4fc0-b60b-40b203070ea4] preload schema finished, cost 1 ms 
[INFO ] 2024-07-26 19:59:25.341 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-26 19:59:25.363 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-26 19:59:25.363 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-26 19:59:25.372 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-26 19:59:25.372 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - batch offset found: {"_tapdata_heartbeat_table":{"batch_read_connector_offset":{"syncStage":"Initial","beginTimes":1721994893369,"lastTimes":1721994893396,"lastTN":1,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":1,"updateTotals":0,"deleteTotals":0}}},"batch_read_connector_status":"RUNNING"}},stream offset found: {"syncStage":"Incremental","beginTimes":1721994893298,"lastTimes":1721994918504,"lastTN":27,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":26,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-26 19:59:25.443 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1721994893298,"lastTimes":1721994918504,"lastTN":27,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":26,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-26 19:59:25.444 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-26 19:59:25.444 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-26 20:00:19.375 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3d27fbdd-c6ec-4fc0-b60b-40b203070ea4] running status set to false 
[INFO ] 2024-07-26 20:00:19.375 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Stop connector 
[WARN ] 2024-07-26 20:00:19.414 - [Heartbeat-source - Copy][_tapdata_heartbeat_table] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-3d27fbdd-c6ec-4fc0-b60b-40b203070ea4 
