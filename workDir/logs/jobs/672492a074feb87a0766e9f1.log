[INFO ] 2024-11-03 14:32:58.798 - [任务 11] - Task initialization... 
[INFO ] 2024-11-03 14:32:58.814 - [任务 11] - Start task milestones: 672492a074feb87a0766e9f1(任务 11) 
[INFO ] 2024-11-03 14:32:59.201 - [任务 11] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-11-03 14:32:59.422 - [任务 11] - The engine receives 任务 11 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-03 14:32:59.703 - [任务 11][Mysql3307] - Node Mysql3307[b35d25d0-7cba-47a5-a907-8911758b8420] start preload schema,table counts: 1 
[INFO ] 2024-11-03 14:32:59.706 - [任务 11][Mysql3306] - Node Mysql3306[e0f48e0b-7ad3-499a-8f19-4b41a8319d4e] start preload schema,table counts: 1 
[INFO ] 2024-11-03 14:32:59.707 - [任务 11][Mysql3306] - Node <PERSON>sql3306[e0f48e0b-7ad3-499a-8f19-4b41a8319d4e] preload schema finished, cost 0 ms 
[INFO ] 2024-11-03 14:32:59.709 - [任务 11][Mysql3307] - Node Mysql3307[b35d25d0-7cba-47a5-a907-8911758b8420] preload schema finished, cost 1 ms 
[INFO ] 2024-11-03 14:33:00.909 - [任务 11][Mysql3307] - Node(Mysql3307) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-11-03 14:33:00.919 - [任务 11][Mysql3306] - Source node "Mysql3306" read batch size: 100 
[INFO ] 2024-11-03 14:33:00.920 - [任务 11][Mysql3307] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-03 14:33:00.921 - [任务 11][Mysql3306] - Source node "Mysql3306" event queue capacity: 200 
[INFO ] 2024-11-03 14:33:00.924 - [任务 11][Mysql3306] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-03 14:33:00.950 - [任务 11][Mysql3306] - batch offset found: {},stream offset found: {"filename":"binlog.000037","position":18049,"gtidSet":""} 
[INFO ] 2024-11-03 14:33:00.950 - [任务 11][Mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-03 14:33:01.006 - [任务 11][Mysql3307] - Table "test.parent_table" exists, skip auto create table 
[INFO ] 2024-11-03 14:33:01.012 - [任务 11][Mysql3307] - The table parent_table has already exist. 
[INFO ] 2024-11-03 14:33:01.047 - [任务 11][Mysql3306] - Initial sync started 
[INFO ] 2024-11-03 14:33:01.051 - [任务 11][Mysql3306] - Query snapshot row size completed: Mysql3306(e0f48e0b-7ad3-499a-8f19-4b41a8319d4e) 
[INFO ] 2024-11-03 14:33:01.051 - [任务 11][Mysql3306] - Starting batch read, table name: parent_table 
[INFO ] 2024-11-03 14:33:01.104 - [任务 11][Mysql3306] - Table parent_table is going to be initial synced 
[INFO ] 2024-11-03 14:33:01.108 - [任务 11][Mysql3306] - Table [parent_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-03 14:33:01.108 - [任务 11][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-03 14:33:01.108 - [任务 11][Mysql3306] - Incremental sync starting... 
[INFO ] 2024-11-03 14:33:01.108 - [任务 11][Mysql3306] - Initial sync completed 
[INFO ] 2024-11-03 14:33:01.164 - [任务 11][Mysql3306] - Starting stream read, table list: [parent_table], offset: {"filename":"binlog.000037","position":18049,"gtidSet":""} 
[INFO ] 2024-11-03 14:33:01.167 - [任务 11][Mysql3307] - Exception skipping - The current exception does not match the skip exception strategy, message: Table name: parent_tableio.tapdata.entity.event.ddl.table.TapClearTableEvent@45acf31d: {"tableId":"parent_table","time":1730615581098,"type":205}
 
[INFO ] 2024-11-03 14:33:01.205 - [任务 11][Mysql3306] - Starting mysql cdc, server name: dfed2783-e0a8-4ef7-a39b-0ca2d435c07d 
[ERROR] 2024-11-03 14:33:01.206 - [任务 11][Mysql3307] - Table name: parent_tableio.tapdata.entity.event.ddl.table.TapClearTableEvent@45acf31d: {"tableId":"parent_table","time":1730615581098,"type":205}
 <-- Error Message -->
Table name: parent_tableio.tapdata.entity.event.ddl.table.TapClearTableEvent@45acf31d: {"tableId":"parent_table","time":1730615581098,"type":205}


<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Cannot truncate a table referenced in a foreign key constraint (`test`.`child_table`, CONSTRAINT `child_table_ibfk_1`)
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
Table name: parent_table
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.clearData(HazelcastTargetPdkDataNode.java:454)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:208)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:155)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:108)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Cannot truncate a table referenced in a foreign key constraint (`test`.`child_table`, CONSTRAINT `child_table_ibfk_1`)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$clearData$20(HazelcastTargetPdkDataNode.java:442)
	at java.util.Optional.ifPresent(Optional.java:159)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.clearData(HazelcastTargetPdkDataNode.java:440)
	... 19 more
Caused by: java.sql.SQLSyntaxErrorException: Cannot truncate a table referenced in a foreign key constraint (`test`.`child_table`, CONSTRAINT `child_table_ibfk_1`)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$19(HazelcastTargetPdkDataNode.java:447)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 23 more
Caused by: java.sql.SQLSyntaxErrorException: Cannot truncate a table referenced in a foreign key constraint (`test`.`child_table`, CONSTRAINT `child_table_ibfk_1`)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.execute(JdbcContext.java:155)
	at io.tapdata.common.CommonDbConnector.clearTable(CommonDbConnector.java:346)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$18(HazelcastTargetPdkDataNode.java:447)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 30 more

[INFO ] 2024-11-03 14:33:01.238 - [任务 11][Mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"dfed2783-e0a8-4ef7-a39b-0ca2d435c07d","offset":{"{\"server\":\"dfed2783-e0a8-4ef7-a39b-0ca2d435c07d\"}":"{\"file\":\"binlog.000037\",\"pos\":18049,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 7723094
  time.precision.mode: adaptive_time_microseconds
  database.server.name: dfed2783-e0a8-4ef7-a39b-0ca2d435c07d
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-dfed2783-e0a8-4ef7-a39b-0ca2d435c07d
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: dfed2783-e0a8-4ef7-a39b-0ca2d435c07d
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.parent_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-03 14:33:01.244 - [任务 11][Mysql3307] - Job suspend in error handle 
[INFO ] 2024-11-03 14:33:01.445 - [任务 11][Mysql3306] - Connector Mysql incremental start succeed, tables: [parent_table], data change syncing 
[INFO ] 2024-11-03 14:33:01.653 - [任务 11][Mysql3306] - Node Mysql3306[e0f48e0b-7ad3-499a-8f19-4b41a8319d4e] running status set to false 
[INFO ] 2024-11-03 14:33:02.037 - [任务 11][Mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-03 14:33:02.040 - [任务 11][Mysql3306] - Mysql binlog reader stopped 
[INFO ] 2024-11-03 14:33:02.047 - [任务 11][Mysql3306] - Incremental sync completed 
[INFO ] 2024-11-03 14:33:02.049 - [任务 11][Mysql3306] - PDK connector node stopped: HazelcastSourcePdkDataNode-e0f48e0b-7ad3-499a-8f19-4b41a8319d4e 
[INFO ] 2024-11-03 14:33:02.049 - [任务 11][Mysql3306] - PDK connector node released: HazelcastSourcePdkDataNode-e0f48e0b-7ad3-499a-8f19-4b41a8319d4e 
[INFO ] 2024-11-03 14:33:02.056 - [任务 11][Mysql3306] - Node Mysql3306[e0f48e0b-7ad3-499a-8f19-4b41a8319d4e] schema data cleaned 
[INFO ] 2024-11-03 14:33:02.056 - [任务 11][Mysql3306] - Node Mysql3306[e0f48e0b-7ad3-499a-8f19-4b41a8319d4e] monitor closed 
[INFO ] 2024-11-03 14:33:02.063 - [任务 11][Mysql3306] - Node Mysql3306[e0f48e0b-7ad3-499a-8f19-4b41a8319d4e] close complete, cost 437 ms 
[INFO ] 2024-11-03 14:33:02.063 - [任务 11][Mysql3307] - Node Mysql3307[b35d25d0-7cba-47a5-a907-8911758b8420] running status set to false 
[INFO ] 2024-11-03 14:33:02.076 - [任务 11][Mysql3307] - PDK connector node stopped: HazelcastTargetPdkDataNode-b35d25d0-7cba-47a5-a907-8911758b8420 
[INFO ] 2024-11-03 14:33:02.076 - [任务 11][Mysql3307] - PDK connector node released: HazelcastTargetPdkDataNode-b35d25d0-7cba-47a5-a907-8911758b8420 
[INFO ] 2024-11-03 14:33:02.077 - [任务 11][Mysql3307] - Node Mysql3307[b35d25d0-7cba-47a5-a907-8911758b8420] schema data cleaned 
[INFO ] 2024-11-03 14:33:02.077 - [任务 11][Mysql3307] - Node Mysql3307[b35d25d0-7cba-47a5-a907-8911758b8420] monitor closed 
[INFO ] 2024-11-03 14:33:02.288 - [任务 11][Mysql3307] - Node Mysql3307[b35d25d0-7cba-47a5-a907-8911758b8420] close complete, cost 16 ms 
[INFO ] 2024-11-03 14:33:02.582 - [任务 11] - Task [任务 11] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-03 14:33:02.583 - [任务 11] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-03 14:33:02.597 - [任务 11] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3204e241 
[INFO ] 2024-11-03 14:33:02.597 - [任务 11] - Stop task milestones: 672492a074feb87a0766e9f1(任务 11)  
[INFO ] 2024-11-03 14:33:02.747 - [任务 11] - Stopped task aspect(s) 
[INFO ] 2024-11-03 14:33:02.748 - [任务 11] - Snapshot order controller have been removed 
[INFO ] 2024-11-03 14:33:02.795 - [任务 11] - Remove memory task client succeed, task: 任务 11[672492a074feb87a0766e9f1] 
[INFO ] 2024-11-03 14:33:02.795 - [任务 11] - Destroy memory task client cache succeed, task: 任务 11[672492a074feb87a0766e9f1] 
