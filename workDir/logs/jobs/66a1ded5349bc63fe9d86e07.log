[INFO ] 2024-07-25 13:13:15.494 - [任务 29] - Task initialization... 
[INFO ] 2024-07-25 13:13:15.497 - [任务 29] - Start task milestones: 66a1ded5349bc63fe9d86e07(任务 29) 
[INFO ] 2024-07-25 13:13:15.705 - [任务 29] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-25 13:13:15.759 - [任务 29] - The engine receives 任务 29 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-25 13:13:15.809 - [任务 29][mockTable] - Node mockTable[b675ebc8-08a6-474a-8558-8a54ec4a572d] start preload schema,table counts: 1 
[INFO ] 2024-07-25 13:13:15.809 - [任务 29][dummy_test] - Node dummy_test[597cf5c2-a69a-41e6-a445-77eb253bdd3a] start preload schema,table counts: 1 
[INFO ] 2024-07-25 13:13:15.810 - [任务 29][mockTable] - Node mockTable[b675ebc8-08a6-474a-8558-8a54ec4a572d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 13:13:15.810 - [任务 29][dummy_test] - Node dummy_test[597cf5c2-a69a-41e6-a445-77eb253bdd3a] preload schema finished, cost 0 ms 
[INFO ] 2024-07-25 13:13:16.422 - [任务 29][mockTable] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-25 13:13:16.456 - [任务 29][dummy_test] - Source node "dummy_test" read batch size: 100 
[INFO ] 2024-07-25 13:13:16.456 - [任务 29][dummy_test] - Source node "dummy_test" event queue capacity: 200 
[INFO ] 2024-07-25 13:13:16.456 - [任务 29][dummy_test] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-25 13:13:16.516 - [任务 29][dummy_test] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721884396456,"lastTimes":1721884396456,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-25 13:13:16.520 - [任务 29][dummy_test] - Initial sync started 
[INFO ] 2024-07-25 13:13:16.520 - [任务 29][dummy_test] - Starting batch read, table name: dummy_test, offset: null 
[INFO ] 2024-07-25 13:13:16.520 - [任务 29][dummy_test] - Table dummy_test is going to be initial synced 
[INFO ] 2024-07-25 13:13:16.520 - [任务 29][dummy_test] - Start dummy_test batch read 
[INFO ] 2024-07-25 13:13:16.759 - [任务 29][dummy_test] - Query table 'dummy_test' counts: 10000 
[INFO ] 2024-07-25 13:13:17.626 - [任务 29][dummy_test] - Compile dummy_test batch read 
[INFO ] 2024-07-25 13:13:17.626 - [任务 29][dummy_test] - Table [dummy_test] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-25 13:13:17.629 - [任务 29][dummy_test] - Initial sync completed 
[INFO ] 2024-07-25 13:13:17.631 - [任务 29][dummy_test] - Incremental sync starting... 
[INFO ] 2024-07-25 13:13:17.631 - [任务 29][dummy_test] - Initial sync completed 
[INFO ] 2024-07-25 13:13:17.631 - [任务 29][dummy_test] - Starting stream read, table list: [dummy_test], offset: {"syncStage":null,"beginTimes":1721884396456,"lastTimes":1721884396456,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-25 13:13:17.640 - [任务 29][dummy_test] - Start [dummy_test] stream read 
[INFO ] 2024-07-25 13:13:17.643 - [任务 29][dummy_test] - Connector Dummy incremental start succeed, tables: [dummy_test], data change syncing 
[INFO ] 2024-07-25 13:13:30.690 - [任务 29][dummy_test] - Node dummy_test[597cf5c2-a69a-41e6-a445-77eb253bdd3a] running status set to false 
[INFO ] 2024-07-25 13:13:30.695 - [任务 29][dummy_test] - Incremental sync completed 
[INFO ] 2024-07-25 13:13:30.695 - [任务 29][dummy_test] - Stop connector 
[INFO ] 2024-07-25 13:13:30.699 - [任务 29][dummy_test] - PDK connector node stopped: HazelcastSourcePdkDataNode-597cf5c2-a69a-41e6-a445-77eb253bdd3a 
[INFO ] 2024-07-25 13:13:30.700 - [任务 29][dummy_test] - PDK connector node released: HazelcastSourcePdkDataNode-597cf5c2-a69a-41e6-a445-77eb253bdd3a 
[INFO ] 2024-07-25 13:13:30.700 - [任务 29][dummy_test] - Node dummy_test[597cf5c2-a69a-41e6-a445-77eb253bdd3a] schema data cleaned 
[INFO ] 2024-07-25 13:13:30.700 - [任务 29][dummy_test] - Node dummy_test[597cf5c2-a69a-41e6-a445-77eb253bdd3a] monitor closed 
[INFO ] 2024-07-25 13:13:30.701 - [任务 29][dummy_test] - Node dummy_test[597cf5c2-a69a-41e6-a445-77eb253bdd3a] close complete, cost 16 ms 
[INFO ] 2024-07-25 13:13:30.701 - [任务 29][mockTable] - Node mockTable[b675ebc8-08a6-474a-8558-8a54ec4a572d] running status set to false 
[INFO ] 2024-07-25 13:13:30.712 - [任务 29][mockTable] - PDK connector node stopped: HazelcastTargetPdkDataNode-b675ebc8-08a6-474a-8558-8a54ec4a572d 
[INFO ] 2024-07-25 13:13:30.712 - [任务 29][mockTable] - PDK connector node released: HazelcastTargetPdkDataNode-b675ebc8-08a6-474a-8558-8a54ec4a572d 
[INFO ] 2024-07-25 13:13:30.712 - [任务 29][mockTable] - Node mockTable[b675ebc8-08a6-474a-8558-8a54ec4a572d] schema data cleaned 
[INFO ] 2024-07-25 13:13:30.712 - [任务 29][mockTable] - Node mockTable[b675ebc8-08a6-474a-8558-8a54ec4a572d] monitor closed 
[INFO ] 2024-07-25 13:13:30.714 - [任务 29][mockTable] - Node mockTable[b675ebc8-08a6-474a-8558-8a54ec4a572d] close complete, cost 11 ms 
[INFO ] 2024-07-25 13:13:35.141 - [任务 29] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-25 13:13:35.146 - [任务 29] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1cf176fa 
[INFO ] 2024-07-25 13:13:35.296 - [任务 29] - Stop task milestones: 66a1ded5349bc63fe9d86e07(任务 29)  
[INFO ] 2024-07-25 13:13:35.296 - [任务 29] - Stopped task aspect(s) 
[INFO ] 2024-07-25 13:13:35.324 - [任务 29] - Snapshot order controller have been removed 
[INFO ] 2024-07-25 13:13:35.326 - [任务 29] - Remove memory task client succeed, task: 任务 29[66a1ded5349bc63fe9d86e07] 
[INFO ] 2024-07-25 13:13:35.326 - [任务 29] - Destroy memory task client cache succeed, task: 任务 29[66a1ded5349bc63fe9d86e07] 
