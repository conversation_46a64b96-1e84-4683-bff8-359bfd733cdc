[INFO ] 2024-07-05 12:14:17.428 - [来自SourceOracle的共享挖掘任务] - Start task milestones: 66877319ed8812650a8a3653(来自SourceOracle的共享挖掘任务) 
[INFO ] 2024-07-05 12:14:17.481 - [来自SourceOracle的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-05 12:14:17.548 - [来自SourceOracle的共享挖掘任务] - The engine receives 来自SourceOracle的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-05 12:14:17.548 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Node SourceOracle[764be68f0bd3492c94524480367a5605] start preload schema,table counts: 2 
[INFO ] 2024-07-05 12:14:17.548 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Node SourceOracle[764be68f0bd3492c94524480367a5605] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 12:14:17.572 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-05 12:14:17.572 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-05 12:14:17.573 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6686cea066ab5ede8a2a7756, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1986184445, shareCdcTaskId=6686cea04ef26b0b431bdbfe, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-05 12:14:17.573 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687731966ab5ede8a36d7ca, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5_CAR_POLICY, version=v2, tableName=CAR_POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1169691194, shareCdcTaskId=66877319ed8812650a8a3653, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-05 12:14:17.801 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1169691194', head seq: 0, tail seq: -1 
[INFO ] 2024-07-05 12:14:17.804 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-05 12:14:19.454 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Source node "SourceOracle" read batch size: 2000 
[INFO ] 2024-07-05 12:14:19.454 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Source node "SourceOracle" event queue capacity: 4000 
[INFO ] 2024-07-05 12:14:19.454 - [来自SourceOracle的共享挖掘任务][SourceOracle] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-05 12:14:19.898 - [来自SourceOracle的共享挖掘任务][SourceOracle] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":68920955,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 12:14:19.898 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-05 12:14:20.015 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Starting stream read, table list: [C##TAPDATA._tapdata_heartbeat_table, C##TAPDATA.CAR_POLICY], offset: {"sortString":null,"offsetValue":null,"lastScn":68920955,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 12:14:23.470 - [来自SourceOracle的共享挖掘任务][SourceOracle] - total start mining scn: 68920955 
[INFO ] 2024-07-05 12:14:24.842 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 12:27:04.059 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1169691194', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 13:27:04.176 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1169691194', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 14:27:04.256 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1169691194', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 14:29:51.294 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Node SourceOracle[764be68f0bd3492c94524480367a5605] running status set to false 
[INFO ] 2024-07-05 14:29:51.411 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Log Miner is shutting down... 
[INFO ] 2024-07-05 14:29:51.460 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Log Miner has been closed! 
[INFO ] 2024-07-05 14:29:51.531 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code null): java.io.InterruptedIOException: Socket read interrupted 
[WARN ] 2024-07-05 14:29:51.690 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkShareCDCNode-764be68f0bd3492c94524480367a5605 
[INFO ] 2024-07-05 14:29:51.719 - [来自SourceOracle的共享挖掘任务][SourceOracle] - PDK connector node released: HazelcastSourcePdkShareCDCNode-764be68f0bd3492c94524480367a5605 
[INFO ] 2024-07-05 17:28:14.065 - [来自SourceOracle的共享挖掘任务] - Start task milestones: 66877319ed8812650a8a3653(来自SourceOracle的共享挖掘任务) 
[INFO ] 2024-07-05 17:28:15.726 - [来自SourceOracle的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-05 17:28:16.035 - [来自SourceOracle的共享挖掘任务] - The engine receives 来自SourceOracle的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-05 17:28:17.299 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Node SourceOracle[764be68f0bd3492c94524480367a5605] start preload schema,table counts: 2 
[INFO ] 2024-07-05 17:28:17.327 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-05 17:28:17.328 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-05 17:28:17.329 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Node SourceOracle[764be68f0bd3492c94524480367a5605] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 17:28:17.861 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687731966ab5ede8a36d7ca, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5_CAR_POLICY, version=v2, tableName=CAR_POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1169691194, shareCdcTaskId=66877319ed8812650a8a3653, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-05 17:28:17.911 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6686cea066ab5ede8a2a7756, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1986184445, shareCdcTaskId=6686cea04ef26b0b431bdbfe, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-05 17:28:18.640 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA._tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1986184445', head seq: 0, tail seq: 56536 
[INFO ] 2024-07-05 17:28:18.642 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1169691194', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 17:28:18.645 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-05 17:28:39.768 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Source node "SourceOracle" read batch size: 2000 
[INFO ] 2024-07-05 17:28:39.769 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Source node "SourceOracle" event queue capacity: 4000 
[INFO ] 2024-07-05 17:28:39.820 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-05 17:28:40.096 - [来自SourceOracle的共享挖掘任务][SourceOracle] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":69008995,"pendingScn":69008996,"timestamp":1720160973000,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 17:28:40.096 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-05 17:28:40.291 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Starting stream read, table list: [C##TAPDATA._tapdata_heartbeat_table, C##TAPDATA.CAR_POLICY], offset: {"sortString":null,"offsetValue":null,"lastScn":69008995,"pendingScn":69008996,"timestamp":1720160973000,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 17:28:40.976 - [来自SourceOracle的共享挖掘任务][SourceOracle] - total start mining scn: 69008995 
[INFO ] 2024-07-05 17:28:42.186 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 18:28:18.532 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1169691194', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 18:28:18.548 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA._tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1986184445', head seq: 0, tail seq: 72945 
[INFO ] 2024-07-05 18:50:55.612 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code null): java.sql.SQLRecoverableException: 无法从套接字读取更多的数据 
[ERROR] 2024-07-05 18:50:55.637 - [来自SourceOracle的共享挖掘任务][SourceOracle] - java.sql.SQLRecoverableException: 无法从套接字读取更多的数据 <-- Error Message -->
java.sql.SQLRecoverableException: 无法从套接字读取更多的数据

<-- Simple Stack Trace -->
Caused by: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:855)
	oracle.jdbc.driver.T4CMAREngineNIO.getNBytes(T4CMAREngineNIO.java:636)
	oracle.jdbc.driver.T4CMAREngineNIO.unmarshalNBytes(T4CMAREngineNIO.java:605)
	oracle.jdbc.driver.DynamicByteArray.unmarshalBuffer(DynamicByteArray.java:304)
	oracle.jdbc.driver.DynamicByteArray.unmarshalCLR(DynamicByteArray.java:219)
	...

<-- Full Stack Trace -->
java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamReadMultiConnection(OracleConnector.java:424)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$25(HazelcastSourcePdkDataNode.java:701)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:612)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:855)
	at oracle.jdbc.driver.T4CMAREngineNIO.getNBytes(T4CMAREngineNIO.java:636)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalNBytes(T4CMAREngineNIO.java:605)
	at oracle.jdbc.driver.DynamicByteArray.unmarshalBuffer(DynamicByteArray.java:304)
	at oracle.jdbc.driver.DynamicByteArray.unmarshalCLR(DynamicByteArray.java:219)
	at oracle.jdbc.driver.T4CNumberAccessor.unmarshalBytes(T4CNumberAccessor.java:201)
	at oracle.jdbc.driver.T4CNumberAccessor.unmarshalOneRow(T4CNumberAccessor.java:183)
	at oracle.jdbc.driver.T4CTTIrxd.unmarshal(T4CTTIrxd.java:1652)
	at oracle.jdbc.driver.T4CTTIrxd.unmarshal(T4CTTIrxd.java:1369)
	at oracle.jdbc.driver.T4C8Oall.readRXD(T4C8Oall.java:935)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:683)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 19 more

[INFO ] 2024-07-05 18:50:55.649 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Job suspend in error handle 
[INFO ] 2024-07-05 18:50:55.853 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Node SourceOracle[764be68f0bd3492c94524480367a5605] running status set to false 
[INFO ] 2024-07-05 18:50:55.853 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Log Miner is shutting down... 
[INFO ] 2024-07-05 18:50:55.897 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Log Miner has been closed! 
[INFO ] 2024-07-05 18:50:55.897 - [来自SourceOracle的共享挖掘任务][SourceOracle] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-764be68f0bd3492c94524480367a5605 
[INFO ] 2024-07-05 18:50:55.897 - [来自SourceOracle的共享挖掘任务][SourceOracle] - PDK connector node released: HazelcastSourcePdkShareCDCNode-764be68f0bd3492c94524480367a5605 
[INFO ] 2024-07-05 18:50:55.898 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Node SourceOracle[764be68f0bd3492c94524480367a5605] schema data cleaned 
[INFO ] 2024-07-05 18:50:55.898 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Node SourceOracle[764be68f0bd3492c94524480367a5605] monitor closed 
[INFO ] 2024-07-05 18:50:55.898 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Node SourceOracle[764be68f0bd3492c94524480367a5605] close complete, cost 47 ms 
[INFO ] 2024-07-05 18:50:55.903 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[f659f7382b2148b5869b2b3ab202945a] running status set to false 
[INFO ] 2024-07-05 18:50:55.903 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-05 18:50:55.904 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-05 18:50:55.904 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[f659f7382b2148b5869b2b3ab202945a] schema data cleaned 
[INFO ] 2024-07-05 18:50:55.913 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[f659f7382b2148b5869b2b3ab202945a] monitor closed 
[INFO ] 2024-07-05 18:50:55.914 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[f659f7382b2148b5869b2b3ab202945a] close complete, cost 8 ms 
[INFO ] 2024-07-05 18:50:56.161 - [来自SourceOracle的共享挖掘任务] - Task [来自SourceOracle的共享挖掘任务] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-05 18:50:56.161 - [来自SourceOracle的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-05 18:50:56.162 - [来自SourceOracle的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@70bc0579 
[INFO ] 2024-07-05 18:50:56.163 - [来自SourceOracle的共享挖掘任务] - Stop task milestones: 66877319ed8812650a8a3653(来自SourceOracle的共享挖掘任务)  
[INFO ] 2024-07-05 18:50:56.276 - [来自SourceOracle的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-05 18:50:56.276 - [来自SourceOracle的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-05 18:50:56.325 - [来自SourceOracle的共享挖掘任务] - Remove memory task client succeed, task: 来自SourceOracle的共享挖掘任务[66877319ed8812650a8a3653] 
[INFO ] 2024-07-05 18:50:56.328 - [来自SourceOracle的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自SourceOracle的共享挖掘任务[66877319ed8812650a8a3653] 
[INFO ] 2024-07-05 19:28:18.907 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_POLICY', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1169691194', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 19:28:18.927 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA._tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1986184445', head seq: 0, tail seq: 75065 
