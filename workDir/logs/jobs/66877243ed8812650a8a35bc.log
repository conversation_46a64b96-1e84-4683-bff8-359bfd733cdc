[INFO ] 2024-07-08 14:50:52.511 - [测试共享挖掘Oracle-Mysql1] - Task initialization... 
[INFO ] 2024-07-08 14:50:52.546 - [测试共享挖掘Oracle-Mysql1] - Start task milestones: 66877243ed8812650a8a35bc(测试共享挖掘Oracle-Mysql1) 
[INFO ] 2024-07-08 14:50:53.563 - [测试共享挖掘Oracle-Mysql1] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-08 14:50:53.749 - [测试共享挖掘Oracle-Mysql1] - The engine receives 测试共享挖掘Oracle-Mysql1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-08 14:50:54.248 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Node CAR_POLICY[feebf8de-bf26-4159-988e-852cd41f1acf] start preload schema,table counts: 1 
[INFO ] 2024-07-08 14:50:54.257 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Node CAR_POLICY[feebf8de-bf26-4159-988e-852cd41f1acf] preload schema finished, cost 0 ms 
[INFO ] 2024-07-08 14:50:54.262 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Node CAR_POLICY[696a2d60-9147-48ad-84dd-4d956dd1966f] start preload schema,table counts: 1 
[INFO ] 2024-07-08 14:50:54.269 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Node CAR_POLICY[696a2d60-9147-48ad-84dd-4d956dd1966f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-08 14:50:55.407 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-08 14:50:56.615 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Source node "CAR_POLICY" read batch size: 100 
[INFO ] 2024-07-08 14:50:56.619 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Source node "CAR_POLICY" event queue capacity: 200 
[INFO ] 2024-07-08 14:50:56.621 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-08 14:50:56.670 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - batch offset found: {},stream offset found: {"sequenceMap":{"CAR_POLICY":1,"_tapdata_heartbeat_table":75066},"streamOffset":{"sortString":null,"offsetValue":null,"lastScn":69114331,"pendingScn":69114332,"timestamp":1720176359000,"hexScn":null,"fno":0}} 
[INFO ] 2024-07-08 14:50:56.670 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-08 14:50:56.743 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Incremental sync starting... 
[INFO ] 2024-07-08 14:50:56.746 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Initial sync completed 
[INFO ] 2024-07-08 14:50:56.810 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-08 14:50:56.810 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceOracle enable share cdc: true 
[INFO ] 2024-07-08 14:50:56.814 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试共享挖掘Oracle-Mysql1 enable share cdc: true 
[INFO ] 2024-07-08 14:50:56.842 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceOracle的共享挖掘任务 
[INFO ] 2024-07-08 14:50:56.858 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-08 14:50:56.872 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687731966ab5ede8a36d7ca, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5_CAR_POLICY, version=v2, tableName=CAR_POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1169691194, shareCdcTaskId=66877319ed8812650a8a3653, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-08 14:50:56.890 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_POLICY_测试共享挖掘Oracle-Mysql1', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1169691194', head seq: 0, tail seq: 0 
[INFO ] 2024-07-08 14:50:56.891 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-08 14:50:56.891 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-08 14:50:56.892 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-08 14:50:56.892 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-08 14:50:56.899 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-08 14:50:56.904 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687731966ab5ede8a36d7ca, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5_CAR_POLICY, version=v2, tableName=CAR_POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1169691194, shareCdcTaskId=66877319ed8812650a8a3653, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-08 14:50:56.912 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_POLICY_测试共享挖掘Oracle-Mysql1', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1169691194', head seq: 0, tail seq: 0 
[INFO ] 2024-07-08 14:50:56.915 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_POLICY_测试共享挖掘Oracle-Mysql1, external storage name: ExternalStorage_SHARE_CDC_-1169691194 
[INFO ] 2024-07-08 14:50:56.922 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR_POLICY] 
[INFO ] 2024-07-08 14:50:56.933 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Connector Oracle incremental start succeed, tables: [CAR_POLICY], data change syncing 
[INFO ] 2024-07-08 14:50:56.933 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Starting read 'CAR_POLICY' log, sequence: 1 
[INFO ] 2024-07-08 14:50:56.933 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Find by CAR_POLICY filter: {sequence=1} 
[INFO ] 2024-07-08 14:59:54.625 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Node CAR_POLICY[feebf8de-bf26-4159-988e-852cd41f1acf] running status set to false 
[INFO ] 2024-07-08 14:59:54.654 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Incremental sync completed 
[INFO ] 2024-07-08 15:05:16.762 - [测试共享挖掘Oracle-Mysql1] - Task initialization... 
[INFO ] 2024-07-08 15:05:16.857 - [测试共享挖掘Oracle-Mysql1] - Start task milestones: 66877243ed8812650a8a35bc(测试共享挖掘Oracle-Mysql1) 
[INFO ] 2024-07-08 15:05:17.187 - [测试共享挖掘Oracle-Mysql1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-08 15:05:17.251 - [测试共享挖掘Oracle-Mysql1] - The engine receives 测试共享挖掘Oracle-Mysql1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-08 15:05:17.532 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Node CAR_POLICY[feebf8de-bf26-4159-988e-852cd41f1acf] start preload schema,table counts: 1 
[INFO ] 2024-07-08 15:05:17.535 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Node CAR_POLICY[696a2d60-9147-48ad-84dd-4d956dd1966f] start preload schema,table counts: 1 
[INFO ] 2024-07-08 15:05:17.539 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Node CAR_POLICY[feebf8de-bf26-4159-988e-852cd41f1acf] preload schema finished, cost 0 ms 
[INFO ] 2024-07-08 15:05:17.542 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Node CAR_POLICY[696a2d60-9147-48ad-84dd-4d956dd1966f] preload schema finished, cost 0 ms 
[INFO ] 2024-07-08 15:05:17.937 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-08 15:05:23.485 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Source node "CAR_POLICY" read batch size: 100 
[INFO ] 2024-07-08 15:05:23.487 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Source node "CAR_POLICY" event queue capacity: 200 
[INFO ] 2024-07-08 15:05:23.488 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-08 15:05:23.511 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - batch offset found: {},stream offset found: {"sequenceMap":{"CAR_POLICY":1,"_tapdata_heartbeat_table":75066},"streamOffset":{"sortString":null,"offsetValue":null,"lastScn":69114331,"pendingScn":69114332,"timestamp":1720176359000,"hexScn":null,"fno":0}} 
[INFO ] 2024-07-08 15:05:27.900 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-08 15:05:28.038 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Incremental sync starting... 
[INFO ] 2024-07-08 15:05:28.038 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Initial sync completed 
[INFO ] 2024-07-08 15:05:28.153 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-08 15:05:28.155 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection SourceOracle enable share cdc: true 
[INFO ] 2024-07-08 15:05:28.159 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 测试共享挖掘Oracle-Mysql1 enable share cdc: true 
[INFO ] 2024-07-08 15:05:28.192 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自SourceOracle的共享挖掘任务 
[INFO ] 2024-07-08 15:05:28.202 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-08 15:05:28.221 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687731966ab5ede8a36d7ca, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5_CAR_POLICY, version=v2, tableName=CAR_POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1169691194, shareCdcTaskId=66877319ed8812650a8a3653, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-08 15:05:28.259 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_POLICY_测试共享挖掘Oracle-Mysql1', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1169691194', head seq: 0, tail seq: 0 
[INFO ] 2024-07-08 15:05:28.259 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-08 15:05:28.271 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-08 15:05:28.272 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-08 15:05:28.272 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-08 15:05:28.281 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-08 15:05:28.326 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6687731966ab5ede8a36d7ca, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5_CAR_POLICY, version=v2, tableName=CAR_POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1169691194, shareCdcTaskId=66877319ed8812650a8a3653, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-08 15:05:28.344 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_POLICY_测试共享挖掘Oracle-Mysql1', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1169691194', head seq: 0, tail seq: 0 
[INFO ] 2024-07-08 15:05:28.345 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_POLICY_测试共享挖掘Oracle-Mysql1, external storage name: ExternalStorage_SHARE_CDC_-1169691194 
[INFO ] 2024-07-08 15:05:28.352 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [CAR_POLICY] 
[INFO ] 2024-07-08 15:05:28.352 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Connector Oracle incremental start succeed, tables: [CAR_POLICY], data change syncing 
[INFO ] 2024-07-08 15:05:28.352 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Starting read 'CAR_POLICY' log, sequence: 1 
[INFO ] 2024-07-08 15:05:28.363 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - [Share CDC Task HZ Reader] - Find by CAR_POLICY filter: {sequence=1} 
[INFO ] 2024-07-08 15:07:35.633 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Node CAR_POLICY[feebf8de-bf26-4159-988e-852cd41f1acf] running status set to false 
[INFO ] 2024-07-08 15:07:35.838 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Incremental sync completed 
[INFO ] 2024-07-08 15:07:35.934 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-feebf8de-bf26-4159-988e-852cd41f1acf 
[INFO ] 2024-07-08 15:07:35.934 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-feebf8de-bf26-4159-988e-852cd41f1acf 
[INFO ] 2024-07-08 15:07:35.935 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Node CAR_POLICY[feebf8de-bf26-4159-988e-852cd41f1acf] schema data cleaned 
[INFO ] 2024-07-08 15:07:35.943 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Node CAR_POLICY[feebf8de-bf26-4159-988e-852cd41f1acf] monitor closed 
[INFO ] 2024-07-08 15:07:35.946 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Node CAR_POLICY[feebf8de-bf26-4159-988e-852cd41f1acf] close complete, cost 305 ms 
[INFO ] 2024-07-08 15:07:35.946 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Node CAR_POLICY[696a2d60-9147-48ad-84dd-4d956dd1966f] running status set to false 
[INFO ] 2024-07-08 15:07:35.964 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - PDK connector node stopped: HazelcastTargetPdkDataNode-696a2d60-9147-48ad-84dd-4d956dd1966f 
[INFO ] 2024-07-08 15:07:35.965 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - PDK connector node released: HazelcastTargetPdkDataNode-696a2d60-9147-48ad-84dd-4d956dd1966f 
[INFO ] 2024-07-08 15:07:35.966 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Node CAR_POLICY[696a2d60-9147-48ad-84dd-4d956dd1966f] schema data cleaned 
[INFO ] 2024-07-08 15:07:35.968 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Node CAR_POLICY[696a2d60-9147-48ad-84dd-4d956dd1966f] monitor closed 
[INFO ] 2024-07-08 15:07:35.968 - [测试共享挖掘Oracle-Mysql1][CAR_POLICY] - Node CAR_POLICY[696a2d60-9147-48ad-84dd-4d956dd1966f] close complete, cost 30 ms 
[INFO ] 2024-07-08 15:07:40.512 - [测试共享挖掘Oracle-Mysql1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-08 15:07:40.520 - [测试共享挖掘Oracle-Mysql1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6586cdd8 
[INFO ] 2024-07-08 15:07:40.638 - [测试共享挖掘Oracle-Mysql1] - Stop task milestones: 66877243ed8812650a8a35bc(测试共享挖掘Oracle-Mysql1)  
[INFO ] 2024-07-08 15:07:40.639 - [测试共享挖掘Oracle-Mysql1] - Stopped task aspect(s) 
[INFO ] 2024-07-08 15:07:40.663 - [测试共享挖掘Oracle-Mysql1] - Snapshot order controller have been removed 
[INFO ] 2024-07-08 15:07:40.664 - [测试共享挖掘Oracle-Mysql1] - Remove memory task client succeed, task: 测试共享挖掘Oracle-Mysql1[66877243ed8812650a8a35bc] 
[INFO ] 2024-07-08 15:07:40.665 - [测试共享挖掘Oracle-Mysql1] - Destroy memory task client cache succeed, task: 测试共享挖掘Oracle-Mysql1[66877243ed8812650a8a35bc] 
