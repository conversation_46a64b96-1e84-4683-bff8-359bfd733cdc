[INFO ] 2024-03-28 08:28:39.452 - [任务 27] - Start task milestones: 6603f7f08b5bca60f72dffee(任务 27) 
[INFO ] 2024-03-28 08:28:39.462 - [任务 27] - Task initialization... 
[INFO ] 2024-03-28 08:28:39.462 - [任务 27] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 08:28:39.463 - [任务 27] - The engine receives 任务 27 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 08:28:39.463 - [任务 27][CLAIM] - Node CLAIM[0aea3759-a946-4d27-8127-7d9d78dbf2f1] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:28:39.463 - [任务 27][test1] - Node test1[7e07b268-c655-4016-a830-37515ef03d2b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:28:39.483 - [任务 27][test1] - Node test1[7e07b268-c655-4016-a830-37515ef03d2b] preload schema finished, cost 21 ms 
[INFO ] 2024-03-28 08:28:39.484 - [任务 27][CLAIM] - Node CLAIM[0aea3759-a946-4d27-8127-7d9d78dbf2f1] preload schema finished, cost 30 ms 
[INFO ] 2024-03-28 08:28:40.817 - [任务 27][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-28 08:28:40.819 - [任务 27][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-28 08:28:40.819 - [任务 27][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 08:28:40.828 - [任务 27][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":145945561,"gtidSet":""} 
[INFO ] 2024-03-28 08:28:40.911 - [任务 27][CLAIM] - Initial sync started 
[INFO ] 2024-03-28 08:28:40.911 - [任务 27][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-28 08:28:40.990 - [任务 27][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-28 08:28:40.990 - [任务 27][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-28 08:28:40.990 - [任务 27][CLAIM] - Initial sync completed 
[ERROR] 2024-03-28 08:28:40.992 - [任务 27][CLAIM] - java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list' <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:253)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:147)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:175)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:308)
	... 10 more
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshot$13(HazelcastSourcePdkDataNode.java:316)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 12 more
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1140)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:94)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadV2(MysqlConnector.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:388)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 18 more

[INFO ] 2024-03-28 08:28:41.193 - [任务 27][CLAIM] - Job suspend in error handle 
[INFO ] 2024-03-28 08:28:42.411 - [任务 27] - Task [任务 27] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 08:28:47.431 - [任务 27] - Task [任务 27] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 08:28:47.765 - [任务 27][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 08:28:47.835 - [任务 27][CLAIM] - Node CLAIM[0aea3759-a946-4d27-8127-7d9d78dbf2f1] running status set to false 
[INFO ] 2024-03-28 08:28:47.835 - [任务 27][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-0aea3759-a946-4d27-8127-7d9d78dbf2f1 
[INFO ] 2024-03-28 08:28:47.835 - [任务 27][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-0aea3759-a946-4d27-8127-7d9d78dbf2f1 
[INFO ] 2024-03-28 08:28:47.836 - [任务 27][CLAIM] - Node CLAIM[0aea3759-a946-4d27-8127-7d9d78dbf2f1] schema data cleaned 
[INFO ] 2024-03-28 08:28:47.836 - [任务 27][CLAIM] - Node CLAIM[0aea3759-a946-4d27-8127-7d9d78dbf2f1] monitor closed 
[INFO ] 2024-03-28 08:28:47.850 - [任务 27][CLAIM] - Node CLAIM[0aea3759-a946-4d27-8127-7d9d78dbf2f1] close complete, cost 63 ms 
[INFO ] 2024-03-28 08:28:47.851 - [任务 27][test1] - Node test1[7e07b268-c655-4016-a830-37515ef03d2b] running status set to false 
[INFO ] 2024-03-28 08:28:47.985 - [任务 27][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-7e07b268-c655-4016-a830-37515ef03d2b 
[INFO ] 2024-03-28 08:28:47.986 - [任务 27][test1] - PDK connector node released: HazelcastTargetPdkDataNode-7e07b268-c655-4016-a830-37515ef03d2b 
[INFO ] 2024-03-28 08:28:47.986 - [任务 27][test1] - Node test1[7e07b268-c655-4016-a830-37515ef03d2b] schema data cleaned 
[INFO ] 2024-03-28 08:28:47.987 - [任务 27][test1] - Node test1[7e07b268-c655-4016-a830-37515ef03d2b] monitor closed 
[INFO ] 2024-03-28 08:28:48.196 - [任务 27][test1] - Node test1[7e07b268-c655-4016-a830-37515ef03d2b] close complete, cost 149 ms 
[INFO ] 2024-03-28 08:28:52.456 - [任务 27] - Task [任务 27] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 08:28:52.456 - [任务 27] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 08:28:52.481 - [任务 27] - Stop task milestones: 6603f7f08b5bca60f72dffee(任务 27)  
[INFO ] 2024-03-28 08:28:52.482 - [任务 27] - Stopped task aspect(s) 
[INFO ] 2024-03-28 08:28:52.482 - [任务 27] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 08:28:52.500 - [任务 27] - Remove memory task client succeed, task: 任务 27[6603f7f08b5bca60f72dffee] 
[INFO ] 2024-03-28 08:28:52.503 - [任务 27] - Destroy memory task client cache succeed, task: 任务 27[6603f7f08b5bca60f72dffee] 
[INFO ] 2024-03-28 08:29:30.331 - [任务 27] - Start task milestones: 6603f7f08b5bca60f72dffee(任务 27) 
[INFO ] 2024-03-28 08:29:30.334 - [任务 27] - Task initialization... 
[INFO ] 2024-03-28 08:29:30.513 - [任务 27] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 08:29:30.513 - [任务 27] - The engine receives 任务 27 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 08:29:30.600 - [任务 27][test1] - Node test1[7e07b268-c655-4016-a830-37515ef03d2b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:29:30.600 - [任务 27][CLAIM] - Node CLAIM[0aea3759-a946-4d27-8127-7d9d78dbf2f1] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:29:30.626 - [任务 27][CLAIM] - Node CLAIM[0aea3759-a946-4d27-8127-7d9d78dbf2f1] preload schema finished, cost 24 ms 
[INFO ] 2024-03-28 08:29:30.627 - [任务 27][test1] - Node test1[7e07b268-c655-4016-a830-37515ef03d2b] preload schema finished, cost 24 ms 
[INFO ] 2024-03-28 08:29:31.305 - [任务 27][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-28 08:29:31.305 - [任务 27][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-28 08:29:31.305 - [任务 27][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-28 08:29:31.307 - [任务 27][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":145945561,"gtidSet":""} 
[INFO ] 2024-03-28 08:29:31.342 - [任务 27][CLAIM] - Initial sync started 
[INFO ] 2024-03-28 08:29:31.342 - [任务 27][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-28 08:29:31.406 - [任务 27][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-28 08:29:31.406 - [任务 27][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-28 08:29:38.871 - [任务 27][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 08:29:39.181 - [任务 27][CLAIM] - Initial sync completed 
[INFO ] 2024-03-28 08:29:39.182 - [任务 27][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-28 08:29:39.182 - [任务 27][CLAIM] - Initial sync completed 
[INFO ] 2024-03-28 08:29:39.185 - [任务 27][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":145945561,"gtidSet":""} 
[INFO ] 2024-03-28 08:29:39.309 - [任务 27][CLAIM] - Starting mysql cdc, server name: fe5fd31c-8a27-494e-ae54-585a297c9f67 
[INFO ] 2024-03-28 08:29:39.312 - [任务 27][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2011522822
  time.precision.mode: adaptive_time_microseconds
  database.server.name: fe5fd31c-8a27-494e-ae54-585a297c9f67
  database.port: 3306
  threadName: Debezium-Mysql-Connector-fe5fd31c-8a27-494e-ae54-585a297c9f67
  database.hostname: 127.0.0.1
  database.password: ********
  name: fe5fd31c-8a27-494e-ae54-585a297c9f67
  pdk.offset.string: {"name":"fe5fd31c-8a27-494e-ae54-585a297c9f67","offset":{"{\"server\":\"fe5fd31c-8a27-494e-ae54-585a297c9f67\"}":"{\"file\":\"binlog.000020\",\"pos\":145945561,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-28 08:29:39.716 - [任务 27][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-28 08:30:20.636 - [任务 27][CLAIM] - Read DDL: alter table CLAIM add column name varchar(50), about to be packaged as some event(s) 
[INFO ] 2024-03-28 08:30:20.636 - [任务 27][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='fe5fd31c-8a27-494e-ae54-585a297c9f67', offset={{"server":"fe5fd31c-8a27-494e-ae54-585a297c9f67"}={"ts_sec":1711585820,"file":"binlog.000020","pos":145945818,"server_id":1}}} 
[INFO ] 2024-03-28 08:30:20.643 - [任务 27][CLAIM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@49c320e9: {"newFields":[{"autoInc":false,"dataType":"varchar(50)","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false}],"referenceTime":1711585820038,"tableId":"CLAIM","time":1711585820610,"type":209} 
[INFO ] 2024-03-28 08:30:58.702 - [任务 27][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_6603f7f08b5bca60f72dffee 
[INFO ] 2024-03-28 08:34:42.615 - [任务 27][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-28 08:40:04.982 - [任务 27][CLAIM] - Node CLAIM[0aea3759-a946-4d27-8127-7d9d78dbf2f1] running status set to false 
[INFO ] 2024-03-28 08:40:04.983 - [任务 27][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-28 08:40:05.004 - [任务 27][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-03-28 08:40:05.005 - [任务 27][CLAIM] - Incremental sync completed 
[WARN ] 2024-03-28 08:40:05.006 - [任务 27][CLAIM] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-0aea3759-a946-4d27-8127-7d9d78dbf2f1 
[INFO ] 2024-03-28 08:40:05.006 - [任务 27][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-0aea3759-a946-4d27-8127-7d9d78dbf2f1 
[INFO ] 2024-03-28 08:40:05.006 - [任务 27][CLAIM] - Node CLAIM[0aea3759-a946-4d27-8127-7d9d78dbf2f1] schema data cleaned 
[INFO ] 2024-03-28 08:40:05.008 - [任务 27][CLAIM] - Node CLAIM[0aea3759-a946-4d27-8127-7d9d78dbf2f1] monitor closed 
[INFO ] 2024-03-28 08:40:05.008 - [任务 27][CLAIM] - Node CLAIM[0aea3759-a946-4d27-8127-7d9d78dbf2f1] close complete, cost 158 ms 
[INFO ] 2024-03-28 08:40:05.028 - [任务 27][test1] - Node test1[7e07b268-c655-4016-a830-37515ef03d2b] running status set to false 
[WARN ] 2024-03-28 08:40:05.028 - [任务 27][test1] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastTargetPdkDataNode-7e07b268-c655-4016-a830-37515ef03d2b 
[INFO ] 2024-03-28 08:40:05.029 - [任务 27][test1] - PDK connector node released: HazelcastTargetPdkDataNode-7e07b268-c655-4016-a830-37515ef03d2b 
[INFO ] 2024-03-28 08:40:05.029 - [任务 27][test1] - Node test1[7e07b268-c655-4016-a830-37515ef03d2b] schema data cleaned 
[INFO ] 2024-03-28 08:40:05.029 - [任务 27][test1] - Node test1[7e07b268-c655-4016-a830-37515ef03d2b] monitor closed 
[INFO ] 2024-03-28 08:40:05.029 - [任务 27][test1] - Node test1[7e07b268-c655-4016-a830-37515ef03d2b] close complete, cost 20 ms 
[INFO ] 2024-03-28 08:47:22.330 - [任务 27] - Task initialization... 
[INFO ] 2024-03-28 08:47:22.330 - [任务 27] - Start task milestones: 6603f7f08b5bca60f72dffee(任务 27) 
[INFO ] 2024-03-28 08:47:22.555 - [任务 27] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-28 08:47:22.729 - [任务 27] - The engine receives 任务 27 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-28 08:47:22.980 - [任务 27][CLAIM] - Node CLAIM[0aea3759-a946-4d27-8127-7d9d78dbf2f1] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:47:22.981 - [任务 27][test1] - Node test1[7e07b268-c655-4016-a830-37515ef03d2b] start preload schema,table counts: 1 
[INFO ] 2024-03-28 08:47:23.000 - [任务 27][test1] - Node test1[7e07b268-c655-4016-a830-37515ef03d2b] preload schema finished, cost 123 ms 
[INFO ] 2024-03-28 08:47:23.000 - [任务 27][CLAIM] - Node CLAIM[0aea3759-a946-4d27-8127-7d9d78dbf2f1] preload schema finished, cost 125 ms 
[INFO ] 2024-03-28 08:47:23.345 - [任务 27][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-28 08:47:23.347 - [任务 27][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-28 08:47:23.375 - [任务 27][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-28 08:47:23.376 - [任务 27][CLAIM] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-28 08:47:23.469 - [任务 27][CLAIM] - batch offset found: {"CLAIM":{}},stream offset found: {"name":"fe5fd31c-8a27-494e-ae54-585a297c9f67","offset":{"{\"server\":\"fe5fd31c-8a27-494e-ae54-585a297c9f67\"}":"{\"ts_sec\":1711585820,\"file\":\"binlog.000020\",\"pos\":145945818,\"server_id\":1}"}} 
[INFO ] 2024-03-28 08:47:23.472 - [任务 27][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-28 08:47:23.474 - [任务 27][CLAIM] - Initial sync completed 
[INFO ] 2024-03-28 08:47:23.474 - [任务 27][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"name":"fe5fd31c-8a27-494e-ae54-585a297c9f67","offset":{"{\"server\":\"fe5fd31c-8a27-494e-ae54-585a297c9f67\"}":"{\"ts_sec\":1711585820,\"file\":\"binlog.000020\",\"pos\":145945818,\"server_id\":1}"}} 
[INFO ] 2024-03-28 08:47:23.570 - [任务 27][CLAIM] - Starting mysql cdc, server name: fe5fd31c-8a27-494e-ae54-585a297c9f67 
[INFO ] 2024-03-28 08:47:23.573 - [任务 27][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 313122311
  time.precision.mode: adaptive_time_microseconds
  database.server.name: fe5fd31c-8a27-494e-ae54-585a297c9f67
  database.port: 3306
  threadName: Debezium-Mysql-Connector-fe5fd31c-8a27-494e-ae54-585a297c9f67
  database.hostname: 127.0.0.1
  database.password: ********
  name: fe5fd31c-8a27-494e-ae54-585a297c9f67
  pdk.offset.string: {"name":"fe5fd31c-8a27-494e-ae54-585a297c9f67","offset":{"{\"server\":\"fe5fd31c-8a27-494e-ae54-585a297c9f67\"}":"{\"ts_sec\":1711585820,\"file\":\"binlog.000020\",\"pos\":145945818,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-28 08:47:24.517 - [任务 27][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-28 08:53:20.125 - [任务 27][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-03-28 08:53:20.132 - [任务 27][CLAIM] - Incremental sync completed 
[ERROR] 2024-03-28 08:53:20.133 - [任务 27][CLAIM] - java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms <-- Error Message -->
java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms

<-- Simple Stack Trace -->
Caused by: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
	com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:986)
	com.github.shyiko.mysql.binlog.BinaryLogClient$SpawnKeepAliveThread.run(BinaryLogClient.java:907)
	java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	java.util.concurrent.FutureTask.run(FutureTask.java)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
	at io.tapdata.common.exception.AbstractExceptionCollector.collectCdcConfigInvalid(AbstractExceptionCollector.java:63)
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:142)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:333)
	at io.tapdata.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:458)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.util.concurrent.TimeoutException: BinaryLogClient was unable to connect in 30000ms
	at com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:986)
	at com.github.shyiko.mysql.binlog.BinaryLogClient$SpawnKeepAliveThread.run(BinaryLogClient.java:907)
	... 6 more

[INFO ] 2024-03-28 08:53:20.345 - [任务 27][CLAIM] - Job suspend in error handle 
[INFO ] 2024-03-28 08:53:20.439 - [任务 27][CLAIM] - Node CLAIM[0aea3759-a946-4d27-8127-7d9d78dbf2f1] running status set to false 
[INFO ] 2024-03-28 08:53:20.439 - [任务 27][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-0aea3759-a946-4d27-8127-7d9d78dbf2f1 
[INFO ] 2024-03-28 08:53:20.440 - [任务 27][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-0aea3759-a946-4d27-8127-7d9d78dbf2f1 
[INFO ] 2024-03-28 08:53:20.443 - [任务 27][CLAIM] - Node CLAIM[0aea3759-a946-4d27-8127-7d9d78dbf2f1] schema data cleaned 
[INFO ] 2024-03-28 08:53:20.443 - [任务 27][CLAIM] - Node CLAIM[0aea3759-a946-4d27-8127-7d9d78dbf2f1] monitor closed 
[INFO ] 2024-03-28 08:53:20.443 - [任务 27][CLAIM] - Node CLAIM[0aea3759-a946-4d27-8127-7d9d78dbf2f1] close complete, cost 38 ms 
[INFO ] 2024-03-28 08:53:20.443 - [任务 27][test1] - Node test1[7e07b268-c655-4016-a830-37515ef03d2b] running status set to false 
[INFO ] 2024-03-28 08:53:20.491 - [任务 27][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-7e07b268-c655-4016-a830-37515ef03d2b 
[INFO ] 2024-03-28 08:53:20.496 - [任务 27][test1] - PDK connector node released: HazelcastTargetPdkDataNode-7e07b268-c655-4016-a830-37515ef03d2b 
[INFO ] 2024-03-28 08:53:20.497 - [任务 27][test1] - Node test1[7e07b268-c655-4016-a830-37515ef03d2b] schema data cleaned 
[INFO ] 2024-03-28 08:53:20.497 - [任务 27][test1] - Node test1[7e07b268-c655-4016-a830-37515ef03d2b] monitor closed 
[INFO ] 2024-03-28 08:53:20.500 - [任务 27][test1] - Node test1[7e07b268-c655-4016-a830-37515ef03d2b] close complete, cost 52 ms 
[INFO ] 2024-03-28 08:53:22.140 - [任务 27] - Task [任务 27] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-28 08:53:22.140 - [任务 27] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-28 08:53:22.162 - [任务 27] - Stop task milestones: 6603f7f08b5bca60f72dffee(任务 27)  
[INFO ] 2024-03-28 08:53:22.167 - [任务 27] - Stopped task aspect(s) 
[INFO ] 2024-03-28 08:53:22.167 - [任务 27] - Snapshot order controller have been removed 
[INFO ] 2024-03-28 08:53:22.184 - [任务 27] - Remove memory task client succeed, task: 任务 27[6603f7f08b5bca60f72dffee] 
[INFO ] 2024-03-28 08:53:22.185 - [任务 27] - Destroy memory task client cache succeed, task: 任务 27[6603f7f08b5bca60f72dffee] 
