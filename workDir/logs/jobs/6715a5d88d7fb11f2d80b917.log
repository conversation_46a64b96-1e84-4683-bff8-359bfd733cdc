[INFO ] 2024-10-21 08:56:13.967 - [任务 2] - Task initialization... 
[INFO ] 2024-10-21 08:56:13.970 - [任务 2] - Start task milestones: 6715a5d88d7fb11f2d80b917(任务 2) 
[INFO ] 2024-10-21 08:56:16.324 - [任务 2] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-10-21 08:56:16.534 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-21 08:56:16.875 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] start preload schema,table counts: 130 
[INFO ] 2024-10-21 08:56:16.878 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] start preload schema,table counts: 130 
[INFO ] 2024-10-21 08:56:16.878 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] preload schema finished, cost 1 ms 
[INFO ] 2024-10-21 08:56:16.879 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] preload schema finished, cost 1 ms 
[INFO ] 2024-10-21 08:56:17.681 - [任务 2][TargetMongo] - Node(TargetMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-21 08:56:17.682 - [任务 2][TargetMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-21 08:56:21.183 - [任务 2][TestMysql] - Source node "TestMysql" read batch size: 100 
[INFO ] 2024-10-21 08:56:21.186 - [任务 2][TestMysql] - Source node "TestMysql" event queue capacity: 200 
[INFO ] 2024-10-21 08:56:21.186 - [任务 2][TestMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-21 08:56:21.387 - [任务 2][TestMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":938080,"gtidSet":""} 
[INFO ] 2024-10-21 08:56:21.488 - [任务 2][TestMysql] - Initial sync started 
[INFO ] 2024-10-21 08:56:21.505 - [任务 2][TestMysql] - Starting batch read, table name: testAutoInspect 
[INFO ] 2024-10-21 08:56:21.521 - [任务 2][TestMysql] - Table testAutoInspect is going to be initial synced 
[INFO ] 2024-10-21 08:56:21.556 - [任务 2][TestMysql] - Query table 'testAutoInspect' counts: 4 
[INFO ] 2024-10-21 08:56:21.560 - [任务 2][TestMysql] - Table [testAutoInspect] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 08:56:21.564 - [任务 2][TestMysql] - Starting batch read, table name: Tes 
[INFO ] 2024-10-21 08:56:21.564 - [任务 2][TestMysql] - Table Tes is going to be initial synced 
[INFO ] 2024-10-21 08:56:21.773 - [任务 2][TestMysql] - Query table 'Tes' counts: 600 
[INFO ] 2024-10-21 08:56:31.956 - [任务 2][TestMysql] - Table [Tes] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 08:56:31.959 - [任务 2][TestMysql] - Starting batch read, table name: shippers 
[INFO ] 2024-10-21 08:56:31.959 - [任务 2][TestMysql] - Table shippers is going to be initial synced 
[INFO ] 2024-10-21 08:56:31.970 - [任务 2][TestMysql] - Table [shippers] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 08:56:31.971 - [任务 2][TestMysql] - Query table 'shippers' counts: 1 
[INFO ] 2024-10-21 08:56:31.972 - [任务 2][TestMysql] - Starting batch read, table name: TFloat 
[INFO ] 2024-10-21 08:56:31.972 - [任务 2][TestMysql] - Table TFloat is going to be initial synced 
[INFO ] 2024-10-21 08:56:31.977 - [任务 2][TestMysql] - Query table 'TFloat' counts: 1 
[INFO ] 2024-10-21 08:56:31.977 - [任务 2][TestMysql] - Table [TFloat] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 08:56:31.977 - [任务 2][TestMysql] - Starting batch read, table name: Test2 
[INFO ] 2024-10-21 08:56:31.978 - [任务 2][TestMysql] - Table Test2 is going to be initial synced 
[INFO ] 2024-10-21 08:56:32.186 - [任务 2][TestMysql] - Query table 'Test2' counts: 695 
[INFO ] 2024-10-21 08:56:32.848 - [任务 2][TestMysql] - Table [Test2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 08:56:32.852 - [任务 2][TestMysql] - Starting batch read, table name: testIndex 
[INFO ] 2024-10-21 08:56:32.866 - [任务 2][TestMysql] - Table testIndex is going to be initial synced 
[INFO ] 2024-10-21 08:56:32.873 - [任务 2][TestMysql] - Query table 'testIndex' counts: 2 
[INFO ] 2024-10-21 08:56:32.874 - [任务 2][TestMysql] - Table [testIndex] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 08:56:32.874 - [任务 2][TestMysql] - Starting batch read, table name: customer6 
[INFO ] 2024-10-21 08:56:32.874 - [任务 2][TestMysql] - Table customer6 is going to be initial synced 
[INFO ] 2024-10-21 08:56:33.075 - [任务 2][TestMysql] - Query table 'customer6' counts: 120000 
[INFO ] 2024-10-21 08:56:49.944 - [任务 2][TestMysql] - Table [customer6] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 08:56:49.953 - [任务 2][TestMysql] - Starting batch read, table name: customer7 
[INFO ] 2024-10-21 08:56:49.954 - [任务 2][TestMysql] - Table customer7 is going to be initial synced 
[INFO ] 2024-10-21 08:56:50.693 - [任务 2][TestMysql] - Query table 'customer7' counts: 2000001 
[INFO ] 2024-10-21 08:57:14.916 - [任务 2][TestMysql] - Table [customer7] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 08:57:14.925 - [任务 2][TestMysql] - Starting batch read, table name: customer4 
[INFO ] 2024-10-21 08:57:14.933 - [任务 2][TestMysql] - Table customer4 is going to be initial synced 
[INFO ] 2024-10-21 08:57:14.936 - [任务 2][TestMysql] - Query table 'customer4' counts: 0 
[INFO ] 2024-10-21 08:57:14.936 - [任务 2][TestMysql] - Table [customer4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 08:57:14.936 - [任务 2][TestMysql] - Starting batch read, table name: CUSTOMER 
[INFO ] 2024-10-21 08:57:14.936 - [任务 2][TestMysql] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-10-21 08:57:14.957 - [任务 2][TestMysql] - Query table 'CUSTOMER' counts: 600 
[INFO ] 2024-10-21 08:57:14.960 - [任务 2][TestMysql] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 08:57:14.960 - [任务 2][TestMysql] - Starting batch read, table name: customer5 
[INFO ] 2024-10-21 08:57:15.165 - [任务 2][TestMysql] - Table customer5 is going to be initial synced 
[INFO ] 2024-10-21 08:57:15.371 - [任务 2][TestMysql] - Query table 'customer5' counts: 1000000 
[INFO ] 2024-10-21 08:57:34.315 - [任务 2][TestMysql] - Table [customer5] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 08:57:34.326 - [任务 2][TestMysql] - Starting batch read, table name: customer2 
[INFO ] 2024-10-21 08:57:34.534 - [任务 2][TestMysql] - Table customer2 is going to be initial synced 
[INFO ] 2024-10-21 08:57:38.190 - [任务 2][TestMysql] - Query table 'customer2' counts: 20010000 
[INFO ] 2024-10-21 09:01:10.340 - [任务 2][TestMysql] - Table [customer2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:01:10.344 - [任务 2][TestMysql] - Starting batch read, table name: customer1 
[INFO ] 2024-10-21 09:01:10.351 - [任务 2][TestMysql] - Table customer1 is going to be initial synced 
[INFO ] 2024-10-21 09:01:10.351 - [任务 2][TestMysql] - Table [customer1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:01:10.353 - [任务 2][TestMysql] - Query table 'customer1' counts: 0 
[INFO ] 2024-10-21 09:01:10.353 - [任务 2][TestMysql] - Starting batch read, table name: Testabc 
[INFO ] 2024-10-21 09:01:10.368 - [任务 2][TestMysql] - Table Testabc is going to be initial synced 
[INFO ] 2024-10-21 09:01:10.368 - [任务 2][TestMysql] - Query table 'Testabc' counts: 676 
[INFO ] 2024-10-21 09:01:10.376 - [任务 2][TestMysql] - Table [Testabc] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:01:10.376 - [任务 2][TestMysql] - Starting batch read, table name: policyTest 
[INFO ] 2024-10-21 09:01:10.377 - [任务 2][TestMysql] - Table policyTest is going to be initial synced 
[INFO ] 2024-10-21 09:01:10.382 - [任务 2][TestMysql] - Query table 'policyTest' counts: 695 
[INFO ] 2024-10-21 09:01:16.444 - [任务 2][TestMysql] - Table [policyTest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:01:16.444 - [任务 2][TestMysql] - Starting batch read, table name: testReference 
[INFO ] 2024-10-21 09:01:16.457 - [任务 2][TestMysql] - Table testReference is going to be initial synced 
[INFO ] 2024-10-21 09:01:16.457 - [任务 2][TestMysql] - Table [testReference] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:01:16.461 - [任务 2][TestMysql] - Query table 'testReference' counts: 4 
[INFO ] 2024-10-21 09:01:16.464 - [任务 2][TestMysql] - Starting batch read, table name: TEST3 
[INFO ] 2024-10-21 09:01:16.466 - [任务 2][TestMysql] - Table TEST3 is going to be initial synced 
[INFO ] 2024-10-21 09:01:16.670 - [任务 2][TestMysql] - Query table 'TEST3' counts: 1053 
[INFO ] 2024-10-21 09:01:25.517 - [任务 2][TestMysql] - Table [TEST3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:01:25.518 - [任务 2][TestMysql] - Starting batch read, table name: TEST4 
[INFO ] 2024-10-21 09:01:25.520 - [任务 2][TestMysql] - Table TEST4 is going to be initial synced 
[INFO ] 2024-10-21 09:01:25.730 - [任务 2][TestMysql] - Query table 'TEST4' counts: 1053 
[INFO ] 2024-10-21 09:01:28.642 - [任务 2][TestMysql] - Table [TEST4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:01:28.643 - [任务 2][TestMysql] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-10-21 09:01:28.643 - [任务 2][TestMysql] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-10-21 09:01:28.650 - [任务 2][TestMysql] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:01:28.650 - [任务 2][TestMysql] - Query table 'BMSQL_ITEM' counts: 7 
[INFO ] 2024-10-21 09:01:28.650 - [任务 2][TestMysql] - Starting batch read, table name: TEST5 
[INFO ] 2024-10-21 09:01:28.650 - [任务 2][TestMysql] - Table TEST5 is going to be initial synced 
[INFO ] 2024-10-21 09:01:28.653 - [任务 2][TestMysql] - Table [TEST5] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:01:28.653 - [任务 2][TestMysql] - Query table 'TEST5' counts: 0 
[INFO ] 2024-10-21 09:01:28.657 - [任务 2][TestMysql] - Starting batch read, table name: TEST6 
[INFO ] 2024-10-21 09:01:28.657 - [任务 2][TestMysql] - Table TEST6 is going to be initial synced 
[INFO ] 2024-10-21 09:01:28.662 - [任务 2][TestMysql] - Table [TEST6] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:01:28.664 - [任务 2][TestMysql] - Query table 'TEST6' counts: 1 
[INFO ] 2024-10-21 09:01:28.664 - [任务 2][TestMysql] - Starting batch read, table name: TEST7 
[INFO ] 2024-10-21 09:01:28.665 - [任务 2][TestMysql] - Table TEST7 is going to be initial synced 
[INFO ] 2024-10-21 09:01:28.673 - [任务 2][TestMysql] - Table [TEST7] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:01:28.674 - [任务 2][TestMysql] - Query table 'TEST7' counts: 1 
[INFO ] 2024-10-21 09:01:28.675 - [任务 2][TestMysql] - Starting batch read, table name: CLAIMBACK 
[INFO ] 2024-10-21 09:01:28.675 - [任务 2][TestMysql] - Table CLAIMBACK is going to be initial synced 
[INFO ] 2024-10-21 09:01:28.677 - [任务 2][TestMysql] - Table [CLAIMBACK] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:01:28.677 - [任务 2][TestMysql] - Query table 'CLAIMBACK' counts: 0 
[INFO ] 2024-10-21 09:01:28.679 - [任务 2][TestMysql] - Starting batch read, table name: TESTPer 
[INFO ] 2024-10-21 09:01:28.679 - [任务 2][TestMysql] - Table TESTPer is going to be initial synced 
[INFO ] 2024-10-21 09:01:29.097 - [任务 2][TestMysql] - Query table 'TESTPer' counts: 5000000 
[WARN ] 2024-10-21 09:02:12.788 - [任务 2][TargetMongo] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.net.SocketException: Connection reset
	java.net.SocketInputStream.read(SocketInputStream.java:210)
	java.net.SocketInputStream.read(SocketInputStream.java:141)
	com.mongodb.internal.connection.SocketStream.read(SocketStream.java:113)
	com.mongodb.internal.connection.SocketStream.read(SocketStream.java:138)
	com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:735)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-21 09:02:12.791 - [任务 2][TargetMongo] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: java.net.SocketException: Connection reset
	java.net.SocketInputStream.read(SocketInputStream.java:210)
	java.net.SocketInputStream.read(SocketInputStream.java:141)
	com.mongodb.internal.connection.SocketStream.read(SocketStream.java:113)
	com.mongodb.internal.connection.SocketStream.read(SocketStream.java:138)
	com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:735)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-21 09:02:12.791 - [任务 2][TargetMongo] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: com.mongodb.MongoSocketReadException: Prematurely reached end of stream
	com.mongodb.internal.connection.SocketStream.read(SocketStream.java:115)
	com.mongodb.internal.connection.SocketStream.read(SocketStream.java:138)
	com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:735)
	com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:596)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:440)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-21 09:02:42.769 - [任务 2][TargetMongo] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=localhost:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.ConnectException: Connection refused (Connection refused)}}]
	com.mongodb.internal.connection.BaseCluster.getDescription(BaseCluster.java:184)
	com.mongodb.internal.connection.SingleServerCluster.getDescription(SingleServerCluster.java:46)
	com.mongodb.client.internal.MongoClientDelegate.getConnectedClusterDescription(MongoClientDelegate.java:143)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:100)
	com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:285)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-21 09:02:42.769 - [任务 2][TargetMongo] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=localhost:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.ConnectException: Connection refused (Connection refused)}}]
	com.mongodb.internal.connection.BaseCluster.getDescription(BaseCluster.java:184)
	com.mongodb.internal.connection.SingleServerCluster.getDescription(SingleServerCluster.java:46)
	com.mongodb.client.internal.MongoClientDelegate.getConnectedClusterDescription(MongoClientDelegate.java:143)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:100)
	com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:285)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-21 09:02:42.770 - [任务 2][TargetMongo] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=localhost:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.ConnectException: Connection refused (Connection refused)}}]
	com.mongodb.internal.connection.BaseCluster.getDescription(BaseCluster.java:184)
	com.mongodb.internal.connection.SingleServerCluster.getDescription(SingleServerCluster.java:46)
	com.mongodb.client.internal.MongoClientDelegate.getConnectedClusterDescription(MongoClientDelegate.java:143)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:100)
	com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:285)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-21 09:02:42.770 - [任务 2][TargetMongo] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=localhost:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.ConnectException: Connection refused (Connection refused)}}]
	com.mongodb.internal.connection.BaseCluster.getDescription(BaseCluster.java:184)
	com.mongodb.internal.connection.SingleServerCluster.getDescription(SingleServerCluster.java:46)
	com.mongodb.client.internal.MongoClientDelegate.getConnectedClusterDescription(MongoClientDelegate.java:143)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:100)
	com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:285)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-10-21 09:02:42.770 - [任务 2][TargetMongo] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: Caused by: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=localhost:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.ConnectException: Connection refused (Connection refused)}}]
	com.mongodb.internal.connection.BaseCluster.getDescription(BaseCluster.java:184)
	com.mongodb.internal.connection.SingleServerCluster.getDescription(SingleServerCluster.java:46)
	com.mongodb.client.internal.MongoClientDelegate.getConnectedClusterDescription(MongoClientDelegate.java:143)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:100)
	com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:285)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-10-21 09:02:55.904 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] running status set to false 
[INFO ] 2024-10-21 09:02:55.958 - [任务 2][TestMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-60d72b4c-433b-480e-bc78-a3ddec1f61a4 
[INFO ] 2024-10-21 09:02:55.960 - [任务 2][TestMysql] - PDK connector node released: HazelcastSourcePdkDataNode-60d72b4c-433b-480e-bc78-a3ddec1f61a4 
[INFO ] 2024-10-21 09:02:55.960 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] schema data cleaned 
[INFO ] 2024-10-21 09:02:55.961 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] monitor closed 
[INFO ] 2024-10-21 09:02:55.972 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] close complete, cost 70 ms 
[INFO ] 2024-10-21 09:02:55.972 - [任务 2][TestMysql] - Initial sync completed 
[INFO ] 2024-10-21 09:02:55.972 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] running status set to false 
[INFO ] 2024-10-21 09:02:55.979 - [任务 2][TestMysql] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 
[INFO ] 2024-10-21 09:02:56.009 - [任务 2][TargetMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-9f6e7558-5fc9-41e0-ab52-b9d30ca1b891 
[INFO ] 2024-10-21 09:02:56.010 - [任务 2][TargetMongo] - PDK connector node released: HazelcastTargetPdkDataNode-9f6e7558-5fc9-41e0-ab52-b9d30ca1b891 
[INFO ] 2024-10-21 09:02:56.010 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] schema data cleaned 
[INFO ] 2024-10-21 09:02:56.010 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] monitor closed 
[INFO ] 2024-10-21 09:02:56.028 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] close complete, cost 38 ms 
[ERROR] 2024-10-21 09:02:56.028 - [任务 2][TestMysql] - java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 <-- Error Message -->
java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **



<-- Simple Stack Trace -->
Caused by: java.io.IOException: Stream closed.
	java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	java.net.SocketInputStream.available(SocketInputStream.java:259)
	com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	java.io.FilterInputStream.read(FilterInputStream.java:133)
	com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.handleThrowable(HazelcastSourcePdkDataNode.java:354)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:323)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	... 10 more
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 13 more
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:213)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 21 more
Caused by: java.sql.SQLException: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed.

STACKTRACE:

java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:131)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1952)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	... 25 more
Caused by: java.io.IOException: Stream closed.
	at java.net.AbstractPlainSocketImpl.available(AbstractPlainSocketImpl.java:470)
	at java.net.SocketInputStream.available(SocketInputStream.java:259)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:199)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	... 28 more

[INFO ] 2024-10-21 09:03:00.912 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-21 09:03:00.912 - [任务 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1e1bbef4 
[INFO ] 2024-10-21 09:03:00.928 - [任务 2] - Stop task milestones: 6715a5d88d7fb11f2d80b917(任务 2)  
[INFO ] 2024-10-21 09:03:01.045 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-10-21 09:03:01.045 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-10-21 09:03:01.131 - [任务 2] - Remove memory task client succeed, task: 任务 2[6715a5d88d7fb11f2d80b917] 
[INFO ] 2024-10-21 09:03:01.131 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[6715a5d88d7fb11f2d80b917] 
[INFO ] 2024-10-21 09:03:26.052 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: TESTPer
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@424e35c: {"after":{"CREATED":1715860241000,"ID":2421600,"NAME":"insert"},"containsIllegalDate":false,"tableId":"TESTPer","time":1729472532385,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472181179, sourceSerialNo=null} 
[ERROR] 2024-10-21 09:03:26.079 - [任务 2][TargetMongo] - target write record(s) failed <-- Error Message -->
target write record(s) failed

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalStateException: state should be: open
	com.mongodb.assertions.Assertions.isTrue(Assertions.java:81)
	com.mongodb.internal.connection.BaseCluster.getDescription(BaseCluster.java:169)
	com.mongodb.internal.connection.SingleServerCluster.getDescription(SingleServerCluster.java:46)
	com.mongodb.client.internal.MongoClientDelegate.getConnectedClusterDescription(MongoClientDelegate.java:143)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:100)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: TESTPer
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:123)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: TESTPer
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:816)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$26(HazelcastTargetPdkDataNode.java:516)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:516)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:781)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:714)
	... 9 more
Caused by: Execute PDK method: TARGET_WRITE_RECORD, tableName: TESTPer
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:880)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$59(HazelcastTargetPdkDataNode.java:822)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 16 more
Caused by: java.lang.IllegalStateException: state should be: open
	at com.mongodb.assertions.Assertions.isTrue(Assertions.java:81)
	at com.mongodb.internal.connection.BaseCluster.getDescription(BaseCluster.java:169)
	at com.mongodb.internal.connection.SingleServerCluster.getDescription(SingleServerCluster.java:46)
	at com.mongodb.client.internal.MongoClientDelegate.getConnectedClusterDescription(MongoClientDelegate.java:143)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:100)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:285)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:201)
	at com.mongodb.client.internal.MongoCollectionImpl.executeBulkWrite(MongoCollectionImpl.java:447)
	at com.mongodb.client.internal.MongoCollectionImpl.bulkWrite(MongoCollectionImpl.java:428)
	at io.tapdata.mongodb.writer.MongodbWriter.writeRecord(MongodbWriter.java:119)
	at io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1314)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:871)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:867)
	... 25 more

[INFO ] 2024-10-21 09:03:26.079 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: TESTPer
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@dbc49da: {"after":{"CREATED":1715860241000,"ID":2423200,"NAME":"insert"},"containsIllegalDate":false,"tableId":"TESTPer","time":1729472532402,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472181179, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:03:26.082 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: TESTPer
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@5260c478: {"after":{"CREATED":1715860241000,"ID":2420500,"NAME":"insert"},"containsIllegalDate":false,"tableId":"TESTPer","time":1729472532374,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472181179, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:03:26.086 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: TESTPer
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@788638d9: {"after":{"CREATED":1715860241000,"ID":2423500,"NAME":"insert"},"containsIllegalDate":false,"tableId":"TESTPer","time":1729472532405,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472181179, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:03:26.093 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: TESTPer
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@77a81571: {"after":{"CREATED":1715860241000,"ID":2421400,"NAME":"insert"},"containsIllegalDate":false,"tableId":"TESTPer","time":1729472532383,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472181179, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:03:26.094 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: TESTPer
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@23933d04: {"after":{"CREATED":1715860241000,"ID":2421000,"NAME":"insert"},"containsIllegalDate":false,"tableId":"TESTPer","time":1729472532379,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472181179, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:03:26.095 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: TESTPer
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@7ecf4892: {"after":{"CREATED":1715860241000,"ID":2425400,"NAME":"insert"},"containsIllegalDate":false,"tableId":"TESTPer","time":1729472532424,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472181179, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:03:26.095 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: TESTPer
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@747fd6c9: {"after":{"CREATED":1715860241000,"ID":2424000,"NAME":"insert"},"containsIllegalDate":false,"tableId":"TESTPer","time":1729472532410,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472181179, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:04:06.259 - [任务 2] - Task initialization... 
[INFO ] 2024-10-21 09:04:06.270 - [任务 2] - Start task milestones: 6715a5d88d7fb11f2d80b917(任务 2) 
[INFO ] 2024-10-21 09:04:07.237 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-21 09:04:07.396 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-21 09:04:07.396 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] start preload schema,table counts: 130 
[INFO ] 2024-10-21 09:04:07.396 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] start preload schema,table counts: 130 
[INFO ] 2024-10-21 09:04:07.397 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] preload schema finished, cost 0 ms 
[INFO ] 2024-10-21 09:04:07.397 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] preload schema finished, cost 0 ms 
[INFO ] 2024-10-21 09:04:08.207 - [任务 2][TargetMongo] - Node(TargetMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-21 09:04:08.211 - [任务 2][TargetMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-21 09:04:08.413 - [任务 2][TestMysql] - Source node "TestMysql" read batch size: 100 
[INFO ] 2024-10-21 09:04:08.413 - [任务 2][TestMysql] - Source node "TestMysql" event queue capacity: 200 
[INFO ] 2024-10-21 09:04:08.424 - [任务 2][TestMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-21 09:04:08.424 - [任务 2][TestMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":938080,"gtidSet":""} 
[INFO ] 2024-10-21 09:04:08.762 - [任务 2][TestMysql] - Initial sync started 
[INFO ] 2024-10-21 09:04:08.762 - [任务 2][TestMysql] - Starting batch read, table name: testAutoInspect 
[INFO ] 2024-10-21 09:04:08.811 - [任务 2][TestMysql] - Table testAutoInspect is going to be initial synced 
[INFO ] 2024-10-21 09:04:08.812 - [任务 2][TestMysql] - Table [testAutoInspect] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:04:08.812 - [任务 2][TestMysql] - Query table 'testAutoInspect' counts: 4 
[INFO ] 2024-10-21 09:04:08.812 - [任务 2][TestMysql] - Starting batch read, table name: Tes 
[INFO ] 2024-10-21 09:04:08.813 - [任务 2][TestMysql] - Table Tes is going to be initial synced 
[INFO ] 2024-10-21 09:04:09.020 - [任务 2][TestMysql] - Query table 'Tes' counts: 600 
[INFO ] 2024-10-21 09:04:21.659 - [任务 2][TestMysql] - Table [Tes] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:04:21.663 - [任务 2][TestMysql] - Starting batch read, table name: shippers 
[INFO ] 2024-10-21 09:04:21.679 - [任务 2][TestMysql] - Table shippers is going to be initial synced 
[INFO ] 2024-10-21 09:04:21.680 - [任务 2][TestMysql] - Table [shippers] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:04:21.680 - [任务 2][TestMysql] - Query table 'shippers' counts: 1 
[INFO ] 2024-10-21 09:04:21.680 - [任务 2][TestMysql] - Starting batch read, table name: TFloat 
[INFO ] 2024-10-21 09:04:21.685 - [任务 2][TestMysql] - Table TFloat is going to be initial synced 
[INFO ] 2024-10-21 09:04:21.685 - [任务 2][TestMysql] - Table [TFloat] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:04:21.685 - [任务 2][TestMysql] - Query table 'TFloat' counts: 1 
[INFO ] 2024-10-21 09:04:21.685 - [任务 2][TestMysql] - Starting batch read, table name: Test2 
[INFO ] 2024-10-21 09:04:21.689 - [任务 2][TestMysql] - Table Test2 is going to be initial synced 
[INFO ] 2024-10-21 09:04:21.689 - [任务 2][TestMysql] - Query table 'Test2' counts: 695 
[INFO ] 2024-10-21 09:04:23.330 - [任务 2][TestMysql] - Table [Test2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:04:23.333 - [任务 2][TestMysql] - Starting batch read, table name: testIndex 
[INFO ] 2024-10-21 09:04:23.333 - [任务 2][TestMysql] - Table testIndex is going to be initial synced 
[INFO ] 2024-10-21 09:04:23.339 - [任务 2][TestMysql] - Table [testIndex] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:04:23.339 - [任务 2][TestMysql] - Query table 'testIndex' counts: 2 
[INFO ] 2024-10-21 09:04:23.339 - [任务 2][TestMysql] - Starting batch read, table name: customer6 
[INFO ] 2024-10-21 09:04:23.339 - [任务 2][TestMysql] - Table customer6 is going to be initial synced 
[INFO ] 2024-10-21 09:04:23.545 - [任务 2][TestMysql] - Query table 'customer6' counts: 120000 
[INFO ] 2024-10-21 09:04:39.917 - [任务 2][TestMysql] - Table [customer6] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:04:39.926 - [任务 2][TestMysql] - Starting batch read, table name: customer7 
[INFO ] 2024-10-21 09:04:39.930 - [任务 2][TestMysql] - Table customer7 is going to be initial synced 
[INFO ] 2024-10-21 09:04:40.131 - [任务 2][TestMysql] - Query table 'customer7' counts: 2000001 
[INFO ] 2024-10-21 09:05:04.232 - [任务 2][TestMysql] - Table [customer7] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:05:04.236 - [任务 2][TestMysql] - Starting batch read, table name: customer4 
[INFO ] 2024-10-21 09:05:04.236 - [任务 2][TestMysql] - Table customer4 is going to be initial synced 
[INFO ] 2024-10-21 09:05:04.266 - [任务 2][TestMysql] - Table [customer4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:05:04.271 - [任务 2][TestMysql] - Query table 'customer4' counts: 0 
[INFO ] 2024-10-21 09:05:04.272 - [任务 2][TestMysql] - Starting batch read, table name: CUSTOMER 
[INFO ] 2024-10-21 09:05:04.283 - [任务 2][TestMysql] - Table CUSTOMER is going to be initial synced 
[INFO ] 2024-10-21 09:05:04.283 - [任务 2][TestMysql] - Query table 'CUSTOMER' counts: 600 
[INFO ] 2024-10-21 09:05:04.297 - [任务 2][TestMysql] - Table [CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:05:04.297 - [任务 2][TestMysql] - Starting batch read, table name: customer5 
[INFO ] 2024-10-21 09:05:04.419 - [任务 2][TestMysql] - Table customer5 is going to be initial synced 
[INFO ] 2024-10-21 09:05:04.419 - [任务 2][TestMysql] - Query table 'customer5' counts: 1000000 
[INFO ] 2024-10-21 09:05:24.040 - [任务 2][TestMysql] - Table [customer5] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:05:24.042 - [任务 2][TestMysql] - Starting batch read, table name: customer2 
[INFO ] 2024-10-21 09:05:24.042 - [任务 2][TestMysql] - Table customer2 is going to be initial synced 
[INFO ] 2024-10-21 09:05:25.459 - [任务 2][TestMysql] - Query table 'customer2' counts: 20010000 
[INFO ] 2024-10-21 09:09:04.862 - [任务 2][TestMysql] - Table [customer2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:09:04.866 - [任务 2][TestMysql] - Starting batch read, table name: customer1 
[INFO ] 2024-10-21 09:09:04.866 - [任务 2][TestMysql] - Table customer1 is going to be initial synced 
[INFO ] 2024-10-21 09:09:04.883 - [任务 2][TestMysql] - Table [customer1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:09:04.884 - [任务 2][TestMysql] - Query table 'customer1' counts: 0 
[INFO ] 2024-10-21 09:09:04.884 - [任务 2][TestMysql] - Starting batch read, table name: Testabc 
[INFO ] 2024-10-21 09:09:04.884 - [任务 2][TestMysql] - Table Testabc is going to be initial synced 
[INFO ] 2024-10-21 09:09:04.899 - [任务 2][TestMysql] - Query table 'Testabc' counts: 676 
[INFO ] 2024-10-21 09:09:04.899 - [任务 2][TestMysql] - Table [Testabc] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:09:04.900 - [任务 2][TestMysql] - Starting batch read, table name: policyTest 
[INFO ] 2024-10-21 09:09:04.900 - [任务 2][TestMysql] - Table policyTest is going to be initial synced 
[INFO ] 2024-10-21 09:09:05.110 - [任务 2][TestMysql] - Query table 'policyTest' counts: 695 
[INFO ] 2024-10-21 09:09:10.909 - [任务 2][TestMysql] - Table [policyTest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:09:10.910 - [任务 2][TestMysql] - Starting batch read, table name: testReference 
[INFO ] 2024-10-21 09:09:10.918 - [任务 2][TestMysql] - Table testReference is going to be initial synced 
[INFO ] 2024-10-21 09:09:10.918 - [任务 2][TestMysql] - Table [testReference] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:09:10.919 - [任务 2][TestMysql] - Query table 'testReference' counts: 4 
[INFO ] 2024-10-21 09:09:10.919 - [任务 2][TestMysql] - Starting batch read, table name: TEST3 
[INFO ] 2024-10-21 09:09:10.928 - [任务 2][TestMysql] - Table TEST3 is going to be initial synced 
[INFO ] 2024-10-21 09:09:10.928 - [任务 2][TestMysql] - Query table 'TEST3' counts: 1053 
[INFO ] 2024-10-21 09:09:20.017 - [任务 2][TestMysql] - Table [TEST3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:09:20.018 - [任务 2][TestMysql] - Starting batch read, table name: TEST4 
[INFO ] 2024-10-21 09:09:20.035 - [任务 2][TestMysql] - Table TEST4 is going to be initial synced 
[INFO ] 2024-10-21 09:09:20.035 - [任务 2][TestMysql] - Query table 'TEST4' counts: 1053 
[INFO ] 2024-10-21 09:09:23.066 - [任务 2][TestMysql] - Table [TEST4] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:09:23.082 - [任务 2][TestMysql] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-10-21 09:09:23.082 - [任务 2][TestMysql] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-10-21 09:09:23.094 - [任务 2][TestMysql] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:09:23.094 - [任务 2][TestMysql] - Query table 'BMSQL_ITEM' counts: 7 
[INFO ] 2024-10-21 09:09:23.096 - [任务 2][TestMysql] - Starting batch read, table name: TEST5 
[INFO ] 2024-10-21 09:09:23.096 - [任务 2][TestMysql] - Table TEST5 is going to be initial synced 
[INFO ] 2024-10-21 09:09:23.100 - [任务 2][TestMysql] - Table [TEST5] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:09:23.101 - [任务 2][TestMysql] - Query table 'TEST5' counts: 0 
[INFO ] 2024-10-21 09:09:23.101 - [任务 2][TestMysql] - Starting batch read, table name: TEST6 
[INFO ] 2024-10-21 09:09:23.102 - [任务 2][TestMysql] - Table TEST6 is going to be initial synced 
[INFO ] 2024-10-21 09:09:23.106 - [任务 2][TestMysql] - Table [TEST6] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:09:23.106 - [任务 2][TestMysql] - Query table 'TEST6' counts: 1 
[INFO ] 2024-10-21 09:09:23.116 - [任务 2][TestMysql] - Starting batch read, table name: TEST7 
[INFO ] 2024-10-21 09:09:23.116 - [任务 2][TestMysql] - Table TEST7 is going to be initial synced 
[INFO ] 2024-10-21 09:09:23.125 - [任务 2][TestMysql] - Query table 'TEST7' counts: 1 
[INFO ] 2024-10-21 09:09:23.125 - [任务 2][TestMysql] - Table [TEST7] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:09:23.129 - [任务 2][TestMysql] - Starting batch read, table name: CLAIMBACK 
[INFO ] 2024-10-21 09:09:23.130 - [任务 2][TestMysql] - Table CLAIMBACK is going to be initial synced 
[INFO ] 2024-10-21 09:09:23.133 - [任务 2][TestMysql] - Query table 'CLAIMBACK' counts: 0 
[INFO ] 2024-10-21 09:09:23.134 - [任务 2][TestMysql] - Table [CLAIMBACK] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:09:23.138 - [任务 2][TestMysql] - Starting batch read, table name: TESTPer 
[INFO ] 2024-10-21 09:09:23.139 - [任务 2][TestMysql] - Table TESTPer is going to be initial synced 
[INFO ] 2024-10-21 09:09:23.541 - [任务 2][TestMysql] - Query table 'TESTPer' counts: 5000000 
[INFO ] 2024-10-21 09:10:14.450 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: com.mongodb.MongoSocketReadException: Prematurely reached end of stream
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@75a4cc90: {"after":{"CREATED":1715860247000,"ID":3110600,"NAME":"insert"},"containsIllegalDate":false,"tableId":"TESTPer","time":1729473013997,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472648414, sourceSerialNo=null} 
[ERROR] 2024-10-21 09:10:14.462 - [任务 2][TargetMongo] - target write record(s) failed <-- Error Message -->
target write record(s) failed

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoSocketReadException: Prematurely reached end of stream
	com.mongodb.internal.connection.SocketStream.read(SocketStream.java:115)
	com.mongodb.internal.connection.SocketStream.read(SocketStream.java:138)
	com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:735)
	com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:596)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:440)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: com.mongodb.MongoSocketReadException: Prematurely reached end of stream
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:123)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: com.mongodb.MongoSocketReadException: Prematurely reached end of stream
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:816)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$26(HazelcastTargetPdkDataNode.java:516)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:516)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:781)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:714)
	... 9 more
Caused by: com.mongodb.MongoSocketReadException: Prematurely reached end of stream
	at io.tapdata.mongodb.MongodbExceptionCollector.revealException(MongodbExceptionCollector.java:40)
	at io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1319)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:871)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:867)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$59(HazelcastTargetPdkDataNode.java:822)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 16 more
Caused by: com.mongodb.MongoSocketReadException: Prematurely reached end of stream
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:115)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:138)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:735)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessageWithAdditionalTimeout(InternalStreamConnection.java:596)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:440)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:365)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:114)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:643)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:73)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:204)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:122)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:87)
	at com.mongodb.internal.connection.DefaultServer$OperationCountTrackingConnection.command(DefaultServer.java:297)
	at com.mongodb.internal.operation.MixedBulkWriteOperation.executeCommand(MixedBulkWriteOperation.java:393)
	at com.mongodb.internal.operation.MixedBulkWriteOperation.executeBulkWriteBatch(MixedBulkWriteOperation.java:257)
	at com.mongodb.internal.operation.MixedBulkWriteOperation.lambda$execute$0(MixedBulkWriteOperation.java:198)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$0(OperationHelper.java:358)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.lambda$withSourceAndConnection$1(OperationHelper.java:357)
	at com.mongodb.internal.operation.OperationHelper.withSuppliedResource(OperationHelper.java:383)
	at com.mongodb.internal.operation.OperationHelper.withSourceAndConnection(OperationHelper.java:356)
	at com.mongodb.internal.operation.MixedBulkWriteOperation.lambda$execute$1(MixedBulkWriteOperation.java:181)
	at com.mongodb.internal.async.function.RetryingSyncSupplier.get(RetryingSyncSupplier.java:67)
	at com.mongodb.internal.operation.MixedBulkWriteOperation.execute(MixedBulkWriteOperation.java:202)
	at com.mongodb.internal.operation.MixedBulkWriteOperation.execute(MixedBulkWriteOperation.java:76)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:206)
	at com.mongodb.client.internal.MongoCollectionImpl.executeBulkWrite(MongoCollectionImpl.java:447)
	at com.mongodb.client.internal.MongoCollectionImpl.bulkWrite(MongoCollectionImpl.java:428)
	at io.tapdata.mongodb.writer.MongodbWriter.writeRecord(MongodbWriter.java:119)
	at io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1314)
	... 26 more

[INFO ] 2024-10-21 09:10:14.463 - [任务 2][TargetMongo] - Job suspend in error handle 
[INFO ] 2024-10-21 09:10:15.367 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] running status set to false 
[INFO ] 2024-10-21 09:10:15.395 - [任务 2][TestMysql] - Initial sync completed 
[INFO ] 2024-10-21 09:10:15.397 - [任务 2][TestMysql] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 
[INFO ] 2024-10-21 09:10:15.411 - [任务 2][TestMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-60d72b4c-433b-480e-bc78-a3ddec1f61a4 
[INFO ] 2024-10-21 09:10:15.413 - [任务 2][TestMysql] - PDK connector node released: HazelcastSourcePdkDataNode-60d72b4c-433b-480e-bc78-a3ddec1f61a4 
[INFO ] 2024-10-21 09:10:15.413 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] schema data cleaned 
[INFO ] 2024-10-21 09:10:15.413 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] monitor closed 
[ERROR] 2024-10-21 09:10:15.416 - [任务 2][TestMysql] - java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 <-- Error Message -->
java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **



<-- Simple Stack Trace -->
Caused by: java.io.IOException: Socket is closed.
	com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.handleThrowable(HazelcastSourcePdkDataNode.java:354)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:323)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	... 10 more
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 13 more
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `ID`, `NAME`, `CREATED` FROM `test`.`TESTPer`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:213)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 19 more
Caused by: java.sql.SQLException: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:131)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1952)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	... 23 more
Caused by: java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	... 26 more

[INFO ] 2024-10-21 09:10:15.417 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] close complete, cost 49 ms 
[INFO ] 2024-10-21 09:10:15.417 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] running status set to false 
[INFO ] 2024-10-21 09:10:15.435 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.InterruptedException
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@313dce3c: {"after":{"CREATED":1715860247000,"ID":3112500,"NAME":"insert"},"containsIllegalDate":false,"tableId":"TESTPer","time":1729473014016,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472648414, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:10:15.446 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.InterruptedException
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@6a124385: {"after":{"CREATED":1715860247000,"ID":3117100,"NAME":"insert"},"containsIllegalDate":false,"tableId":"TESTPer","time":1729473014063,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472648414, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:10:15.446 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.InterruptedException
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@1ff8ab7a: {"after":{"CREATED":1715860247000,"ID":3119300,"NAME":"insert"},"containsIllegalDate":false,"tableId":"TESTPer","time":1729473014085,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472648414, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:10:15.446 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.InterruptedException
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@74996797: {"after":{"CREATED":1715860247000,"ID":3118100,"NAME":"insert"},"containsIllegalDate":false,"tableId":"TESTPer","time":1729473014073,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472648414, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:10:15.456 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.InterruptedException
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@3613d687: {"after":{"CREATED":1715860247000,"ID":3113500,"NAME":"insert"},"containsIllegalDate":false,"tableId":"TESTPer","time":1729473014026,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472648414, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:10:15.456 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.InterruptedException
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@2f3b7992: {"after":{"CREATED":1715860247000,"ID":3115500,"NAME":"insert"},"containsIllegalDate":false,"tableId":"TESTPer","time":1729473014047,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472648414, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:10:15.459 - [任务 2][TargetMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-9f6e7558-5fc9-41e0-ab52-b9d30ca1b891 
[INFO ] 2024-10-21 09:10:15.459 - [任务 2][TargetMongo] - PDK connector node released: HazelcastTargetPdkDataNode-9f6e7558-5fc9-41e0-ab52-b9d30ca1b891 
[INFO ] 2024-10-21 09:10:15.459 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] schema data cleaned 
[INFO ] 2024-10-21 09:10:15.463 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] monitor closed 
[INFO ] 2024-10-21 09:10:15.464 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.InterruptedException
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@113f8786: {"after":{"CREATED":1715860247000,"ID":3118500,"NAME":"insert"},"containsIllegalDate":false,"tableId":"TESTPer","time":1729473014077,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472648414, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:10:15.672 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] close complete, cost 47 ms 
[INFO ] 2024-10-21 09:10:16.502 - [任务 2] - Task [任务 2] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-10-21 09:10:16.512 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-21 09:10:16.514 - [任务 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@30a2b147 
[INFO ] 2024-10-21 09:10:16.522 - [任务 2] - Stop task milestones: 6715a5d88d7fb11f2d80b917(任务 2)  
[INFO ] 2024-10-21 09:10:16.655 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-10-21 09:10:16.655 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-10-21 09:10:16.688 - [任务 2] - Remove memory task client succeed, task: 任务 2[6715a5d88d7fb11f2d80b917] 
[INFO ] 2024-10-21 09:10:16.688 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[6715a5d88d7fb11f2d80b917] 
[INFO ] 2024-10-21 09:11:32.372 - [任务 2] - Task initialization... 
[INFO ] 2024-10-21 09:11:32.373 - [任务 2] - Start task milestones: 6715a5d88d7fb11f2d80b917(任务 2) 
[INFO ] 2024-10-21 09:11:33.094 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-21 09:11:33.095 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-21 09:11:33.223 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] start preload schema,table counts: 130 
[INFO ] 2024-10-21 09:11:33.224 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] start preload schema,table counts: 130 
[INFO ] 2024-10-21 09:11:33.224 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] preload schema finished, cost 1 ms 
[INFO ] 2024-10-21 09:11:33.224 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] preload schema finished, cost 1 ms 
[INFO ] 2024-10-21 09:11:33.429 - [任务 2][TargetMongo] - Node(TargetMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-21 09:11:33.430 - [任务 2][TargetMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-21 09:11:33.582 - [任务 2][TestMysql] - Source node "TestMysql" read batch size: 100 
[INFO ] 2024-10-21 09:11:33.585 - [任务 2][TestMysql] - Source node "TestMysql" event queue capacity: 200 
[INFO ] 2024-10-21 09:11:33.586 - [任务 2][TestMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-21 09:11:33.789 - [任务 2][TestMysql] - batch offset found: {"testAutoInspect":{"batch_read_connector_status":"OVER"},"TEST3":{"batch_read_connector_status":"OVER"},"TEST4":{"batch_read_connector_status":"OVER"},"Tes":{"batch_read_connector_status":"OVER"},"BMSQL_ITEM":{"batch_read_connector_status":"RUNNING"},"TEST5":{"batch_read_connector_status":"OVER"},"TEST6":{"batch_read_connector_status":"OVER"},"TEST7":{"batch_read_connector_status":"RUNNING"},"CLAIMBACK":{"batch_read_connector_status":"OVER"},"shippers":{"batch_read_connector_status":"OVER"},"TESTPer":{"batch_read_connector_status":"RUNNING"},"TFloat":{"batch_read_connector_status":"OVER"},"Test2":{"batch_read_connector_status":"OVER"},"testIndex":{"batch_read_connector_status":"OVER"},"customer6":{"batch_read_connector_status":"OVER"},"customer7":{"batch_read_connector_status":"RUNNING"},"customer4":{"batch_read_connector_status":"OVER"},"CUSTOMER":{"batch_read_connector_status":"OVER"},"customer5":{"batch_read_connector_status":"OVER"},"customer2":{"batch_read_connector_status":"RUNNING"},"customer1":{"batch_read_connector_status":"OVER"},"Testabc":{"batch_read_connector_status":"OVER"},"policyTest":{"batch_read_connector_status":"OVER"},"testReference":{"batch_read_connector_status":"OVER"}},stream offset found: {"filename":"binlog.000036","position":938080,"gtidSet":""} 
[INFO ] 2024-10-21 09:11:33.903 - [任务 2][TestMysql] - Initial sync started 
[INFO ] 2024-10-21 09:11:33.905 - [任务 2][TestMysql] - Skip table [testAutoInspect] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-21 09:11:33.905 - [任务 2][TestMysql] - Skip table [Tes] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-21 09:11:33.905 - [任务 2][TestMysql] - Skip table [shippers] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-21 09:11:33.905 - [任务 2][TestMysql] - Skip table [TFloat] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-21 09:11:33.905 - [任务 2][TestMysql] - Skip table [Test2] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-21 09:11:33.905 - [任务 2][TestMysql] - Skip table [testIndex] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-21 09:11:33.906 - [任务 2][TestMysql] - Skip table [customer6] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-21 09:11:33.906 - [任务 2][TestMysql] - Starting batch read, table name: customer7 
[INFO ] 2024-10-21 09:11:34.135 - [任务 2][TestMysql] - Table customer7 is going to be initial synced 
[INFO ] 2024-10-21 09:11:34.342 - [任务 2][TestMysql] - Query table 'customer7' counts: 2000001 
[INFO ] 2024-10-21 09:11:34.475 - [任务 2][TargetMongo] - Table 'customer7' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:11:34.477 - [任务 2][TargetMongo] - Table 'customer7' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:11:34.477 - [任务 2][TargetMongo] - Table 'customer7' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:11:34.477 - [任务 2][TargetMongo] - Table 'customer7' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:11:34.477 - [任务 2][TargetMongo] - Table 'customer7' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:11:34.477 - [任务 2][TargetMongo] - Table 'customer7' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:11:34.477 - [任务 2][TargetMongo] - Table 'customer7' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:11:34.477 - [任务 2][TargetMongo] - Table 'customer7' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:11:34.570 - [任务 2][TargetMongo] - Table 'customer7' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-10-21 09:11:34.570 - [任务 2][TargetMongo] - Table 'customer7' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-10-21 09:11:34.574 - [任务 2][TargetMongo] - Table 'customer7' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-10-21 09:11:34.574 - [任务 2][TargetMongo] - Table 'customer7' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-10-21 09:11:34.574 - [任务 2][TargetMongo] - Table 'customer7' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-10-21 09:11:34.574 - [任务 2][TargetMongo] - Table 'customer7' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-10-21 09:11:34.575 - [任务 2][TargetMongo] - Table 'customer7' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 14 
[INFO ] 2024-10-21 09:11:34.576 - [任务 2][TargetMongo] - Table 'customer7' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 14 
[INFO ] 2024-10-21 09:11:34.656 - [任务 2][TargetMongo] - Table 'customer7' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-10-21 09:11:34.656 - [任务 2][TargetMongo] - Table 'customer7' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 16 
[INFO ] 2024-10-21 09:11:34.656 - [任务 2][TargetMongo] - Table 'customer7' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 16 
[INFO ] 2024-10-21 09:11:56.206 - [任务 2][TestMysql] - Table [customer7] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:11:56.211 - [任务 2][TestMysql] - Skip table [customer4] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-21 09:11:56.211 - [任务 2][TestMysql] - Skip table [CUSTOMER] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-21 09:11:56.211 - [任务 2][TestMysql] - Skip table [customer5] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-21 09:11:56.211 - [任务 2][TestMysql] - Starting batch read, table name: customer2 
[INFO ] 2024-10-21 09:11:56.211 - [任务 2][TestMysql] - Table customer2 is going to be initial synced 
[INFO ] 2024-10-21 09:11:57.634 - [任务 2][TestMysql] - Query table 'customer2' counts: 20010000 
[INFO ] 2024-10-21 09:11:59.539 - [任务 2][TargetMongo] - Table 'customer2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:11:59.539 - [任务 2][TargetMongo] - Table 'customer2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:11:59.540 - [任务 2][TargetMongo] - Table 'customer2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:11:59.540 - [任务 2][TargetMongo] - Table 'customer2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:11:59.540 - [任务 2][TargetMongo] - Table 'customer2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:11:59.540 - [任务 2][TargetMongo] - Table 'customer2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:11:59.540 - [任务 2][TargetMongo] - Table 'customer2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:11:59.540 - [任务 2][TargetMongo] - Table 'customer2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:11:59.643 - [任务 2][TargetMongo] - Table 'customer2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-10-21 09:11:59.648 - [任务 2][TargetMongo] - Table 'customer2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-10-21 09:11:59.648 - [任务 2][TargetMongo] - Table 'customer2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-10-21 09:11:59.651 - [任务 2][TargetMongo] - Table 'customer2' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-10-21 09:11:59.651 - [任务 2][TargetMongo] - Table 'customer2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 11 
[INFO ] 2024-10-21 09:11:59.651 - [任务 2][TargetMongo] - Table 'customer2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 11 
[INFO ] 2024-10-21 09:11:59.653 - [任务 2][TargetMongo] - Table 'customer2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 13 
[INFO ] 2024-10-21 09:11:59.654 - [任务 2][TargetMongo] - Table 'customer2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 13 
[INFO ] 2024-10-21 09:11:59.654 - [任务 2][TargetMongo] - Table 'customer2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 14 
[INFO ] 2024-10-21 09:11:59.856 - [任务 2][TargetMongo] - Table 'customer2' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 16 
[INFO ] 2024-10-21 09:15:37.816 - [任务 2][TestMysql] - Table [customer2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:15:37.820 - [任务 2][TestMysql] - Skip table [customer1] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-21 09:15:37.820 - [任务 2][TestMysql] - Skip table [Testabc] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-21 09:15:37.820 - [任务 2][TestMysql] - Skip table [policyTest] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-21 09:15:37.820 - [任务 2][TestMysql] - Skip table [testReference] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-21 09:15:37.820 - [任务 2][TestMysql] - Skip table [TEST3] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-21 09:15:37.820 - [任务 2][TestMysql] - Skip table [TEST4] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-21 09:15:37.820 - [任务 2][TestMysql] - Starting batch read, table name: BMSQL_ITEM 
[INFO ] 2024-10-21 09:15:37.831 - [任务 2][TestMysql] - Table BMSQL_ITEM is going to be initial synced 
[INFO ] 2024-10-21 09:15:37.831 - [任务 2][TestMysql] - Table [BMSQL_ITEM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:15:37.833 - [任务 2][TestMysql] - Query table 'BMSQL_ITEM' counts: 7 
[INFO ] 2024-10-21 09:15:37.833 - [任务 2][TestMysql] - Skip table [TEST5] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-21 09:15:37.833 - [任务 2][TestMysql] - Skip table [TEST6] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-21 09:15:37.833 - [任务 2][TestMysql] - Starting batch read, table name: TEST7 
[INFO ] 2024-10-21 09:15:37.834 - [任务 2][TestMysql] - Table TEST7 is going to be initial synced 
[INFO ] 2024-10-21 09:15:37.840 - [任务 2][TestMysql] - Table [TEST7] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:15:37.840 - [任务 2][TestMysql] - Query table 'TEST7' counts: 1 
[INFO ] 2024-10-21 09:15:37.841 - [任务 2][TestMysql] - Skip table [CLAIMBACK] in batch read, reason: last task, this table has been completed batch read 
[INFO ] 2024-10-21 09:15:37.841 - [任务 2][TestMysql] - Starting batch read, table name: TESTPer 
[INFO ] 2024-10-21 09:15:38.046 - [任务 2][TestMysql] - Table TESTPer is going to be initial synced 
[INFO ] 2024-10-21 09:15:38.247 - [任务 2][TestMysql] - Query table 'TESTPer' counts: 5000000 
[INFO ] 2024-10-21 09:15:43.987 - [任务 2][TargetMongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:15:43.993 - [任务 2][TargetMongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:15:43.993 - [任务 2][TargetMongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:15:44.002 - [任务 2][TargetMongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:15:44.003 - [任务 2][TargetMongo] - Table 'BMSQL_ITEM' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:15:47.040 - [任务 2][TargetMongo] - Table 'TEST7' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:15:47.309 - [任务 2][TargetMongo] - Table 'TESTPer' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:15:47.309 - [任务 2][TargetMongo] - Table 'TESTPer' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-10-21 09:15:47.312 - [任务 2][TargetMongo] - Table 'TESTPer' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-10-21 09:15:47.312 - [任务 2][TargetMongo] - Table 'TESTPer' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-10-21 09:15:47.313 - [任务 2][TargetMongo] - Table 'TESTPer' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-10-21 09:15:47.316 - [任务 2][TargetMongo] - Table 'TESTPer' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-10-21 09:15:47.316 - [任务 2][TargetMongo] - Table 'TESTPer' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-10-21 09:15:47.317 - [任务 2][TargetMongo] - Table 'TESTPer' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-10-21 09:15:47.397 - [任务 2][TargetMongo] - Table 'TESTPer' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-10-21 09:15:47.397 - [任务 2][TargetMongo] - Table 'TESTPer' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-10-21 09:15:47.399 - [任务 2][TargetMongo] - Table 'TESTPer' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-10-21 09:15:47.399 - [任务 2][TargetMongo] - Table 'TESTPer' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 11 
[INFO ] 2024-10-21 09:15:47.399 - [任务 2][TargetMongo] - Table 'TESTPer' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 11 
[INFO ] 2024-10-21 09:15:47.401 - [任务 2][TargetMongo] - Table 'TESTPer' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 13 
[INFO ] 2024-10-21 09:15:47.403 - [任务 2][TargetMongo] - Table 'TESTPer' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 14 
[INFO ] 2024-10-21 09:15:47.403 - [任务 2][TargetMongo] - Table 'TESTPer' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 15 
[INFO ] 2024-10-21 09:15:47.604 - [任务 2][TargetMongo] - Table 'TESTPer' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-10-21 09:16:41.434 - [任务 2][TestMysql] - Table [TESTPer] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:16:41.436 - [任务 2][TestMysql] - Starting batch read, table name: MDM_Targe_CLAIM_BACK 
[INFO ] 2024-10-21 09:16:41.436 - [任务 2][TestMysql] - Table MDM_Targe_CLAIM_BACK is going to be initial synced 
[INFO ] 2024-10-21 09:16:41.646 - [任务 2][TestMysql] - Query table 'MDM_Targe_CLAIM_BACK' counts: 1076 
[INFO ] 2024-10-21 09:16:44.604 - [任务 2][TestMysql] - Table [MDM_Targe_CLAIM_BACK] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:16:44.604 - [任务 2][TestMysql] - Starting batch read, table name: TESTDDL2 
[INFO ] 2024-10-21 09:16:44.605 - [任务 2][TestMysql] - Table TESTDDL2 is going to be initial synced 
[INFO ] 2024-10-21 09:16:44.613 - [任务 2][TestMysql] - Query table 'TESTDDL2' counts: 1 
[INFO ] 2024-10-21 09:16:44.614 - [任务 2][TestMysql] - Table [TESTDDL2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:16:44.614 - [任务 2][TestMysql] - Starting batch read, table name: TestPO 
[INFO ] 2024-10-21 09:16:44.614 - [任务 2][TestMysql] - Table TestPO is going to be initial synced 
[INFO ] 2024-10-21 09:16:44.631 - [任务 2][TestMysql] - Query table 'TestPO' counts: 600 
[INFO ] 2024-10-21 09:16:44.631 - [任务 2][TestMysql] - Table [TestPO] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:16:44.632 - [任务 2][TestMysql] - Starting batch read, table name: BMSQL_OORDER_BACK2 
[INFO ] 2024-10-21 09:16:44.632 - [任务 2][TestMysql] - Table BMSQL_OORDER_BACK2 is going to be initial synced 
[INFO ] 2024-10-21 09:16:44.648 - [任务 2][TestMysql] - Table [BMSQL_OORDER_BACK2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:16:44.648 - [任务 2][TestMysql] - Query table 'BMSQL_OORDER_BACK2' counts: 1 
[INFO ] 2024-10-21 09:16:44.649 - [任务 2][TestMysql] - Starting batch read, table name: BMSQL_OORDER_BACK 
[INFO ] 2024-10-21 09:16:44.649 - [任务 2][TestMysql] - Table BMSQL_OORDER_BACK is going to be initial synced 
[INFO ] 2024-10-21 09:16:44.656 - [任务 2][TestMysql] - Table [BMSQL_OORDER_BACK] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:16:44.656 - [任务 2][TestMysql] - Query table 'BMSQL_OORDER_BACK' counts: 1 
[INFO ] 2024-10-21 09:16:44.656 - [任务 2][TestMysql] - Starting batch read, table name: TestCustomer 
[INFO ] 2024-10-21 09:16:44.656 - [任务 2][TestMysql] - Table TestCustomer is going to be initial synced 
[INFO ] 2024-10-21 09:16:44.859 - [任务 2][TestMysql] - Query table 'TestCustomer' counts: 100000 
[INFO ] 2024-10-21 09:17:01.351 - [任务 2][TestMysql] - Table [TestCustomer] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:17:01.351 - [任务 2][TestMysql] - Starting batch read, table name: POLICY 
[INFO ] 2024-10-21 09:17:01.352 - [任务 2][TestMysql] - Table POLICY is going to be initial synced 
[INFO ] 2024-10-21 09:17:01.368 - [任务 2][TestMysql] - Query table 'POLICY' counts: 601 
[INFO ] 2024-10-21 09:17:01.368 - [任务 2][TestMysql] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:17:01.368 - [任务 2][TestMysql] - Starting batch read, table name: TESTE 
[INFO ] 2024-10-21 09:17:01.368 - [任务 2][TestMysql] - Table TESTE is going to be initial synced 
[INFO ] 2024-10-21 09:17:01.569 - [任务 2][TestMysql] - Query table 'TESTE' counts: 695 
[INFO ] 2024-10-21 09:17:04.383 - [任务 2][TestMysql] - Table [TESTE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:17:04.384 - [任务 2][TestMysql] - Starting batch read, table name: CLAIM2 
[INFO ] 2024-10-21 09:17:04.384 - [任务 2][TestMysql] - Table CLAIM2 is going to be initial synced 
[INFO ] 2024-10-21 09:17:04.586 - [任务 2][TestMysql] - Query table 'CLAIM2' counts: 695 
[INFO ] 2024-10-21 09:17:07.448 - [任务 2][TestMysql] - Table [CLAIM2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:17:07.449 - [任务 2][TestMysql] - Starting batch read, table name: CLAIM3 
[INFO ] 2024-10-21 09:17:07.449 - [任务 2][TestMysql] - Table CLAIM3 is going to be initial synced 
[INFO ] 2024-10-21 09:17:07.453 - [任务 2][TestMysql] - Table [CLAIM3] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:17:07.453 - [任务 2][TestMysql] - Query table 'CLAIM3' counts: 1 
[INFO ] 2024-10-21 09:17:07.454 - [任务 2][TestMysql] - Starting batch read, table name: CLAIM1 
[INFO ] 2024-10-21 09:17:07.454 - [任务 2][TestMysql] - Table CLAIM1 is going to be initial synced 
[INFO ] 2024-10-21 09:17:07.458 - [任务 2][TestMysql] - Table [CLAIM1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:17:07.458 - [任务 2][TestMysql] - Query table 'CLAIM1' counts: 0 
[INFO ] 2024-10-21 09:17:07.458 - [任务 2][TestMysql] - Starting batch read, table name: customerTest 
[INFO ] 2024-10-21 09:17:07.458 - [任务 2][TestMysql] - Table customerTest is going to be initial synced 
[INFO ] 2024-10-21 09:17:07.466 - [任务 2][TestMysql] - Query table 'customerTest' counts: 100 
[INFO ] 2024-10-21 09:17:07.466 - [任务 2][TestMysql] - Table [customerTest] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:17:07.466 - [任务 2][TestMysql] - Starting batch read, table name: orders 
[INFO ] 2024-10-21 09:17:07.481 - [任务 2][TestMysql] - Table orders is going to be initial synced 
[INFO ] 2024-10-21 09:17:07.481 - [任务 2][TestMysql] - Table [orders] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:17:07.481 - [任务 2][TestMysql] - Query table 'orders' counts: 1 
[INFO ] 2024-10-21 09:17:07.482 - [任务 2][TestMysql] - Starting batch read, table name: t1 
[INFO ] 2024-10-21 09:17:07.482 - [任务 2][TestMysql] - Table t1 is going to be initial synced 
[INFO ] 2024-10-21 09:17:07.488 - [任务 2][TestMysql] - Table [t1] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:17:07.488 - [任务 2][TestMysql] - Query table 't1' counts: 0 
[INFO ] 2024-10-21 09:17:07.489 - [任务 2][TestMysql] - Starting batch read, table name: t2 
[INFO ] 2024-10-21 09:17:07.489 - [任务 2][TestMysql] - Table t2 is going to be initial synced 
[INFO ] 2024-10-21 09:17:07.690 - [任务 2][TestMysql] - Query table 't2' counts: 1296000 
[INFO ] 2024-10-21 09:18:03.331 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=localhost:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.ConnectException: Connection refused (Connection refused)}}]
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@2e3c31: {"after":{"dt":1725850799000,"k1":"Hd","num":56917,"k2":"MN","id":374901},"containsIllegalDate":false,"tableId":"t2","time":1729473452796,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472648414, sourceSerialNo=null} 
[ERROR] 2024-10-21 09:18:03.343 - [任务 2][TargetMongo] - target write record(s) failed <-- Error Message -->
target write record(s) failed

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=localhost:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.ConnectException: Connection refused (Connection refused)}}]
	com.mongodb.internal.connection.BaseCluster.getDescription(BaseCluster.java:184)
	com.mongodb.internal.connection.SingleServerCluster.getDescription(SingleServerCluster.java:46)
	com.mongodb.client.internal.MongoClientDelegate.getConnectedClusterDescription(MongoClientDelegate.java:143)
	com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:100)
	com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:285)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=localhost:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.ConnectException: Connection refused (Connection refused)}}]
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:123)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=localhost:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.ConnectException: Connection refused (Connection refused)}}]
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:816)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$26(HazelcastTargetPdkDataNode.java:516)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:516)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:781)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:714)
	... 9 more
Caused by: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=localhost:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.ConnectException: Connection refused (Connection refused)}}]
	at io.tapdata.mongodb.MongodbExceptionCollector.collectTerminateByServer(MongodbExceptionCollector.java:51)
	at io.tapdata.mongodb.MongodbExceptionCollector.throwWriteExIfNeed(MongodbExceptionCollector.java:29)
	at io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1318)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:871)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:867)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$59(HazelcastTargetPdkDataNode.java:822)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 16 more
Caused by: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=localhost:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.ConnectException: Connection refused (Connection refused)}}]
	at com.mongodb.internal.connection.BaseCluster.getDescription(BaseCluster.java:184)
	at com.mongodb.internal.connection.SingleServerCluster.getDescription(SingleServerCluster.java:46)
	at com.mongodb.client.internal.MongoClientDelegate.getConnectedClusterDescription(MongoClientDelegate.java:143)
	at com.mongodb.client.internal.MongoClientDelegate.createClientSession(MongoClientDelegate.java:100)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.getClientSession(MongoClientDelegate.java:285)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:201)
	at com.mongodb.client.internal.MongoCollectionImpl.executeBulkWrite(MongoCollectionImpl.java:447)
	at com.mongodb.client.internal.MongoCollectionImpl.bulkWrite(MongoCollectionImpl.java:428)
	at io.tapdata.mongodb.writer.MongodbWriter.writeRecord(MongodbWriter.java:119)
	at io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1314)
	... 26 more

[INFO ] 2024-10-21 09:18:03.357 - [任务 2][TargetMongo] - Job suspend in error handle 
[INFO ] 2024-10-21 09:18:03.358 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=localhost:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.ConnectException: Connection refused (Connection refused)}}]
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@2a95d1bc: {"after":{"dt":1725850799000,"k1":"Hd","num":56917,"k2":"MN","id":374701},"containsIllegalDate":false,"tableId":"t2","time":1729473452794,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472648414, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:18:03.366 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=localhost:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.ConnectException: Connection refused (Connection refused)}}]
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@3819f003: {"after":{"dt":1725850799000,"k1":"Hd","num":56917,"k2":"MN","id":379001},"containsIllegalDate":false,"tableId":"t2","time":1729473452840,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472648414, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:18:03.367 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=localhost:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.ConnectException: Connection refused (Connection refused)}}]
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@56c185a: {"after":{"dt":1725850799000,"k1":"Hd","num":56917,"k2":"MN","id":377401},"containsIllegalDate":false,"tableId":"t2","time":1729473452823,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472648414, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:18:03.367 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=localhost:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.ConnectException: Connection refused (Connection refused)}}]
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@55f27e59: {"after":{"dt":1725850799000,"k1":"Hd","num":56917,"k2":"MN","id":375101},"containsIllegalDate":false,"tableId":"t2","time":1729473452798,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472648414, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:18:03.368 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=localhost:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.ConnectException: Connection refused (Connection refused)}}]
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@6ef1b3fd: {"after":{"dt":1725850799000,"k1":"Hd","num":56917,"k2":"MN","id":377001},"containsIllegalDate":false,"tableId":"t2","time":1729473452818,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472648414, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:18:03.368 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=localhost:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.ConnectException: Connection refused (Connection refused)}}]
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@656cf2da: {"after":{"dt":1725850799000,"k1":"Hd","num":56917,"k2":"MN","id":378601},"containsIllegalDate":false,"tableId":"t2","time":1729473452836,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472648414, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:18:03.572 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: com.mongodb.MongoTimeoutException: Timed out after 30000 ms while waiting to connect. Client view of cluster state is {type=UNKNOWN, servers=[{address=localhost:27018, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketOpenException: Exception opening socket}, caused by {java.net.ConnectException: Connection refused (Connection refused)}}]
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@5cf97fbe: {"after":{"dt":1725850799000,"k1":"Hd","num":56917,"k2":"MN","id":376101},"containsIllegalDate":false,"tableId":"t2","time":1729473452809,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472648414, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:18:04.299 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] running status set to false 
[INFO ] 2024-10-21 09:18:04.299 - [任务 2][TestMysql] - Initial sync completed 
[INFO ] 2024-10-21 09:18:04.310 - [任务 2][TestMysql] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t2`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed

STACKTRACE:

java.io.IOException: Stream closed
	at com.mysql.cj.protocol.ReadAheadInputStream.checkClosed(ReadAheadInputStream.java:231)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:170)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 
[INFO ] 2024-10-21 09:18:04.311 - [任务 2][TestMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-60d72b4c-433b-480e-bc78-a3ddec1f61a4 
[INFO ] 2024-10-21 09:18:04.311 - [任务 2][TestMysql] - PDK connector node released: HazelcastSourcePdkDataNode-60d72b4c-433b-480e-bc78-a3ddec1f61a4 
[INFO ] 2024-10-21 09:18:04.312 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] schema data cleaned 
[INFO ] 2024-10-21 09:18:04.322 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] monitor closed 
[INFO ] 2024-10-21 09:18:04.328 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] close complete, cost 49 ms 
[ERROR] 2024-10-21 09:18:04.328 - [任务 2][TestMysql] - java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t2`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed

STACKTRACE:

java.io.IOException: Stream closed
	at com.mysql.cj.protocol.ReadAheadInputStream.checkClosed(ReadAheadInputStream.java:231)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:170)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 <-- Error Message -->
java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t2`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed

STACKTRACE:

java.io.IOException: Stream closed
	at com.mysql.cj.protocol.ReadAheadInputStream.checkClosed(ReadAheadInputStream.java:231)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:170)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **



<-- Simple Stack Trace -->
Caused by: java.io.IOException: Stream closed
	com.mysql.cj.protocol.ReadAheadInputStream.checkClosed(ReadAheadInputStream.java:231)
	com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:170)
	java.io.FilterInputStream.read(FilterInputStream.java:133)
	com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t2`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed

STACKTRACE:

java.io.IOException: Stream closed
	at com.mysql.cj.protocol.ReadAheadInputStream.checkClosed(ReadAheadInputStream.java:231)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:170)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.handleThrowable(HazelcastSourcePdkDataNode.java:354)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:323)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t2`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed

STACKTRACE:

java.io.IOException: Stream closed
	at com.mysql.cj.protocol.ReadAheadInputStream.checkClosed(ReadAheadInputStream.java:231)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:170)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	... 10 more
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t2`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed

STACKTRACE:

java.io.IOException: Stream closed
	at com.mysql.cj.protocol.ReadAheadInputStream.checkClosed(ReadAheadInputStream.java:231)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:170)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 13 more
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t2`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed

STACKTRACE:

java.io.IOException: Stream closed
	at com.mysql.cj.protocol.ReadAheadInputStream.checkClosed(ReadAheadInputStream.java:231)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:170)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:213)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 19 more
Caused by: java.sql.SQLException: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed

STACKTRACE:

java.io.IOException: Stream closed
	at com.mysql.cj.protocol.ReadAheadInputStream.checkClosed(ReadAheadInputStream.java:231)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:170)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:483)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$12(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:321)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:264)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:265)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:181)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:131)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1952)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	... 23 more
Caused by: java.io.IOException: Stream closed
	at com.mysql.cj.protocol.ReadAheadInputStream.checkClosed(ReadAheadInputStream.java:231)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:170)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	... 26 more

[INFO ] 2024-10-21 09:18:04.329 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] running status set to false 
[INFO ] 2024-10-21 09:18:04.347 - [任务 2][TargetMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-9f6e7558-5fc9-41e0-ab52-b9d30ca1b891 
[INFO ] 2024-10-21 09:18:04.347 - [任务 2][TargetMongo] - PDK connector node released: HazelcastTargetPdkDataNode-9f6e7558-5fc9-41e0-ab52-b9d30ca1b891 
[INFO ] 2024-10-21 09:18:04.347 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] schema data cleaned 
[INFO ] 2024-10-21 09:18:04.347 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] monitor closed 
[INFO ] 2024-10-21 09:18:04.348 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] close complete, cost 20 ms 
[INFO ] 2024-10-21 09:18:07.102 - [任务 2] - Task [任务 2] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-10-21 09:18:07.114 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-21 09:18:07.115 - [任务 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@57fa9d1a 
[INFO ] 2024-10-21 09:18:07.121 - [任务 2] - Stop task milestones: 6715a5d88d7fb11f2d80b917(任务 2)  
[INFO ] 2024-10-21 09:18:07.257 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-10-21 09:18:07.257 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-10-21 09:18:07.287 - [任务 2] - Remove memory task client succeed, task: 任务 2[6715a5d88d7fb11f2d80b917] 
[INFO ] 2024-10-21 09:18:07.288 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[6715a5d88d7fb11f2d80b917] 
[INFO ] 2024-10-21 09:51:02.126 - [任务 2] - Task initialization... 
[INFO ] 2024-10-21 09:51:02.127 - [任务 2] - Start task milestones: 6715a5d88d7fb11f2d80b917(任务 2) 
[INFO ] 2024-10-21 09:51:02.861 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-21 09:51:02.861 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-21 09:51:02.998 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] start preload schema,table counts: 130 
[INFO ] 2024-10-21 09:51:02.998 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] start preload schema,table counts: 130 
[INFO ] 2024-10-21 09:51:02.998 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] preload schema finished, cost 0 ms 
[INFO ] 2024-10-21 09:51:02.999 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] preload schema finished, cost 0 ms 
[INFO ] 2024-10-21 09:51:03.395 - [任务 2][TestMysql] - Source node "TestMysql" read batch size: 100 
[INFO ] 2024-10-21 09:51:03.395 - [任务 2][TestMysql] - Source node "TestMysql" event queue capacity: 200 
[INFO ] 2024-10-21 09:51:03.398 - [任务 2][TestMysql] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-21 09:51:03.599 - [任务 2][TestMysql] - batch offset found: {"BMSQL_ITEM":{"batch_read_connector_status":"OVER"},"TEST7":{"batch_read_connector_status":"OVER"},"TESTPer":{"batch_read_connector_status":"OVER"},"MDM_Targe_CLAIM_BACK":{"batch_read_connector_status":"OVER"},"TESTDDL2":{"batch_read_connector_status":"OVER"},"TestPO":{"batch_read_connector_status":"OVER"},"BMSQL_OORDER_BACK2":{"batch_read_connector_status":"OVER"},"BMSQL_OORDER_BACK":{"batch_read_connector_status":"OVER"},"TestCustomer":{"batch_read_connector_status":"OVER"},"POLICY":{"batch_read_connector_status":"OVER"},"TESTE":{"batch_read_connector_status":"OVER"},"CLAIM2":{"batch_read_connector_status":"OVER"},"CLAIM3":{"batch_read_connector_status":"RUNNING"},"customer7":{"batch_read_connector_status":"OVER"},"CLAIM1":{"batch_read_connector_status":"OVER"},"customer2":{"batch_read_connector_status":"OVER"},"customerTest":{"batch_read_connector_status":"OVER"},"orders":{"batch_read_connector_status":"RUNNING"},"t1":{"batch_read_connector_status":"OVER"},"t2":{"batch_read_connector_status":"RUNNING"}},stream offset found: {"filename":"binlog.000036","position":938080,"gtidSet":""} 
[INFO ] 2024-10-21 09:51:03.670 - [任务 2][TestMysql] - Initial sync started 
[INFO ] 2024-10-21 09:51:03.676 - [任务 2][TestMysql] - Starting batch read, table name: testAutoInspect 
[INFO ] 2024-10-21 09:51:03.677 - [任务 2][TestMysql] - Table testAutoInspect is going to be initial synced 
[INFO ] 2024-10-21 09:51:03.702 - [任务 2][TestMysql] - Table [testAutoInspect] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:51:03.702 - [任务 2][TestMysql] - Query table 'testAutoInspect' counts: 4 
[INFO ] 2024-10-21 09:51:03.702 - [任务 2][TestMysql] - Starting batch read, table name: Tes 
[INFO ] 2024-10-21 09:51:03.703 - [任务 2][TestMysql] - Table Tes is going to be initial synced 
[INFO ] 2024-10-21 09:51:03.724 - [任务 2][TestMysql] - Query table 'Tes' counts: 600 
[INFO ] 2024-10-21 09:51:33.335 - [任务 2][TargetMongo] - Node(TargetMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-10-21 09:51:33.335 - [任务 2][TargetMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-21 09:51:33.373 - [任务 2][TestMysql] - Table [Tes] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:51:33.375 - [任务 2][TestMysql] - Starting batch read, table name: shippers 
[INFO ] 2024-10-21 09:51:33.375 - [任务 2][TestMysql] - Table shippers is going to be initial synced 
[INFO ] 2024-10-21 09:51:33.394 - [任务 2][TestMysql] - Table [shippers] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:51:33.394 - [任务 2][TestMysql] - Query table 'shippers' counts: 1 
[INFO ] 2024-10-21 09:51:33.394 - [任务 2][TestMysql] - Starting batch read, table name: TFloat 
[INFO ] 2024-10-21 09:51:33.394 - [任务 2][TestMysql] - Table TFloat is going to be initial synced 
[INFO ] 2024-10-21 09:51:33.397 - [任务 2][TestMysql] - Table [TFloat] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:51:33.397 - [任务 2][TestMysql] - Query table 'TFloat' counts: 1 
[INFO ] 2024-10-21 09:51:33.398 - [任务 2][TestMysql] - Starting batch read, table name: Test2 
[INFO ] 2024-10-21 09:51:33.398 - [任务 2][TestMysql] - Table Test2 is going to be initial synced 
[INFO ] 2024-10-21 09:51:33.601 - [任务 2][TestMysql] - Query table 'Test2' counts: 695 
[INFO ] 2024-10-21 09:51:53.514 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] running status set to false 
[INFO ] 2024-10-21 09:51:53.521 - [任务 2][TestMysql] - Table [Test2] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-21 09:51:53.521 - [任务 2][TestMysql] - Initial sync completed 
[INFO ] 2024-10-21 09:51:53.530 - [任务 2][TestMysql] - Incremental sync starting... 
[INFO ] 2024-10-21 09:51:53.530 - [任务 2][TestMysql] - Incremental sync completed 
[INFO ] 2024-10-21 09:51:53.539 - [任务 2][TestMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-60d72b4c-433b-480e-bc78-a3ddec1f61a4 
[INFO ] 2024-10-21 09:51:53.539 - [任务 2][TestMysql] - PDK connector node released: HazelcastSourcePdkDataNode-60d72b4c-433b-480e-bc78-a3ddec1f61a4 
[INFO ] 2024-10-21 09:51:53.539 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] schema data cleaned 
[INFO ] 2024-10-21 09:51:53.539 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] monitor closed 
[INFO ] 2024-10-21 09:51:53.541 - [任务 2][TestMysql] - Node TestMysql[60d72b4c-433b-480e-bc78-a3ddec1f61a4] close complete, cost 43 ms 
[INFO ] 2024-10-21 09:51:53.542 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] running status set to false 
[INFO ] 2024-10-21 09:51:53.551 - [任务 2][TargetMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-9f6e7558-5fc9-41e0-ab52-b9d30ca1b891 
[INFO ] 2024-10-21 09:51:53.551 - [任务 2][TargetMongo] - PDK connector node released: HazelcastTargetPdkDataNode-9f6e7558-5fc9-41e0-ab52-b9d30ca1b891 
[INFO ] 2024-10-21 09:51:53.551 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] schema data cleaned 
[INFO ] 2024-10-21 09:51:53.552 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] monitor closed 
[INFO ] 2024-10-21 09:51:53.553 - [任务 2][TargetMongo] - Node TargetMongo[9f6e7558-5fc9-41e0-ab52-b9d30ca1b891] close complete, cost 10 ms 
[INFO ] 2024-10-21 09:51:53.555 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: testAutoInspect 
[ERROR] 2024-10-21 09:51:53.573 - [任务 2][TargetMongo] - target write record(s) failed <-- Error Message -->
target write record(s) failed

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	io.tapdata.mongodb.writer.MongodbWriter.<init>(MongodbWriter.java:62)
	io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1284)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:871)
	io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:867)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: testAutoInspect
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.processPartitionEvents(PartitionConcurrentProcessor.java:191)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.partitionConsumer(PartitionConcurrentProcessor.java:166)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.concurrent.PartitionConcurrentProcessor.lambda$start$3(PartitionConcurrentProcessor.java:123)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: testAutoInspect
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:816)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$26(HazelcastTargetPdkDataNode.java:516)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:516)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:781)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:714)
	... 9 more
Caused by: Execute PDK method: TARGET_WRITE_RECORD, tableName: testAutoInspect
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:880)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$59(HazelcastTargetPdkDataNode.java:822)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 16 more
Caused by: java.lang.NullPointerException
	at io.tapdata.mongodb.writer.MongodbWriter.<init>(MongodbWriter.java:62)
	at io.tapdata.mongodb.MongodbConnector.writeRecord(MongodbConnector.java:1284)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$57(HazelcastTargetPdkDataNode.java:871)
	at io.tapdata.flow.engine.V2.policy.PDkNodeInsertRecordPolicyService.writeRecordWithPolicyControl(PDkNodeInsertRecordPolicyService.java:72)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$58(HazelcastTargetPdkDataNode.java:867)
	... 23 more

[INFO ] 2024-10-21 09:51:53.574 - [任务 2][TargetMongo] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: Execute PDK method: TARGET_WRITE_RECORD, tableName: testAutoInspect
 - TapdataEvent{syncStage=INITIAL_SYNC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@2c5b6eb7: {"after":{"name":"bb","id":4},"containsIllegalDate":false,"tableId":"testAutoInspect","time":1729475463688,"type":300}, nodeIds=[60d72b4c-433b-480e-bc78-a3ddec1f61a4], sourceTime=1729472648414, sourceSerialNo=null} 
[INFO ] 2024-10-21 09:51:54.638 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-21 09:51:54.638 - [任务 2] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@75f680c6 
[INFO ] 2024-10-21 09:51:54.643 - [任务 2] - Stop task milestones: 6715a5d88d7fb11f2d80b917(任务 2)  
[INFO ] 2024-10-21 09:51:54.769 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-10-21 09:51:54.769 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-10-21 09:51:54.802 - [任务 2] - Remove memory task client succeed, task: 任务 2[6715a5d88d7fb11f2d80b917] 
[INFO ] 2024-10-21 09:51:54.802 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[6715a5d88d7fb11f2d80b917] 
