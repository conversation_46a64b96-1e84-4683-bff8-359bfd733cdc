[INFO ] 2024-05-14 07:12:36.653 - [任务 6] - Task initialization... 
[INFO ] 2024-05-14 07:12:36.657 - [任务 6] - Start task milestones: 66430e68d96c7030f5d248de(任务 6) 
[INFO ] 2024-05-14 07:12:36.709 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-05-14 07:12:36.863 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-05-14 07:12:36.864 - [任务 6][OracleTest3] - Node OracleTest3[25b3b260-7489-4599-810a-affe5bfecc15] start preload schema,table counts: 1 
[INFO ] 2024-05-14 07:12:36.864 - [任务 6][TestDate2] - Node TestDate2[e20c39b5-4eeb-422e-af46-97b8aaed0527] start preload schema,table counts: 1 
[INFO ] 2024-05-14 07:12:36.898 - [任务 6][OracleTest3] - Node OracleTest3[25b3b260-7489-4599-810a-affe5bfecc15] preload schema finished, cost 28 ms 
[INFO ] 2024-05-14 07:12:36.899 - [任务 6][TestDate2] - Node TestDate2[e20c39b5-4eeb-422e-af46-97b8aaed0527] preload schema finished, cost 33 ms 
[INFO ] 2024-05-14 07:12:37.784 - [任务 6][TestDate2] - Source node "TestDate2" read batch size: 100 
[INFO ] 2024-05-14 07:12:37.785 - [任务 6][TestDate2] - Source node "TestDate2" event queue capacity: 200 
[INFO ] 2024-05-14 07:12:37.785 - [任务 6][TestDate2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-05-14 07:12:37.962 - [任务 6][TestDate2] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":721166135,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-05-14 07:12:37.963 - [任务 6][TestDate2] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-05-14 07:12:38.012 - [任务 6][TestDate2] - Initial sync started 
[INFO ] 2024-05-14 07:12:38.015 - [任务 6][TestDate2] - Starting batch read, table name: TestDate2, offset: null 
[INFO ] 2024-05-14 07:12:38.015 - [任务 6][TestDate2] - Table TestDate2 is going to be initial synced 
[INFO ] 2024-05-14 07:12:38.091 - [任务 6][TestDate2] - Query table 'TestDate2' counts: 10 
[INFO ] 2024-05-14 07:12:38.092 - [任务 6][TestDate2] - Initial sync completed 
[INFO ] 2024-05-14 07:12:38.092 - [任务 6][TestDate2] - Incremental sync starting... 
[INFO ] 2024-05-14 07:12:38.092 - [任务 6][TestDate2] - Initial sync completed 
[INFO ] 2024-05-14 07:12:38.229 - [任务 6][TestDate2] - Starting stream read, table list: [TestDate2], offset: {"sortString":null,"offsetValue":null,"lastScn":721166135,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-05-14 07:12:38.229 - [任务 6][TestDate2] - Checking whether archived log exists... 
[INFO ] 2024-05-14 07:12:38.431 - [任务 6][TestDate2] - building new log file... 
[INFO ] 2024-05-14 07:12:42.263 - [任务 6][TestDate2] - Redo Log Miner is starting... 
[INFO ] 2024-05-14 07:12:43.274 - [任务 6][OracleTest3] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-05-14 07:13:30.622 - [任务 6][TestDate2] - Redo Log Miner has been started... 
[INFO ] 2024-05-14 07:13:30.622 - [任务 6][TestDate2] - Connector Oracle incremental start succeed, tables: [TestDate2], data change syncing 
[INFO ] 2024-05-14 08:06:44.905 - [任务 6][TestDate2] - Log Miner is shutting down... 
[INFO ] 2024-05-14 08:06:44.906 - [任务 6][TestDate2] - Log Miner has been closed! 
[INFO ] 2024-05-14 08:06:45.084 - [任务 6][TestDate2] - Checking whether archived log exists... 
[INFO ] 2024-05-14 08:06:45.183 - [任务 6][TestDate2] - building new log file... 
[INFO ] 2024-05-14 08:06:54.364 - [任务 6][TestDate2] - Redo Log Miner is starting... 
[INFO ] 2024-05-14 08:08:15.618 - [任务 6][TestDate2] - Redo Log Miner has been started... 
[INFO ] 2024-05-14 08:08:36.197 - [任务 6][TestDate2] - [Auto Retry] Method (source_stream_read) retry succeed 
[WARN ] 2024-05-14 09:42:57.281 - [任务 6][TestDate2] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLRecoverableException: 无法从套接字读取更多的数据
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-05-14 09:45:37.368 - [任务 6][TestDate2] - Log Miner is shutting down... 
[INFO ] 2024-05-14 09:45:37.369 - [任务 6][TestDate2] - Log Miner has been closed! 
[INFO ] 2024-05-14 09:45:47.395 - [任务 6][TestDate2] - Checking whether archived log exists... 
[INFO ] 2024-05-14 09:45:47.518 - [任务 6][TestDate2] - building new log file... 
[INFO ] 2024-05-14 09:45:57.526 - [任务 6][TestDate2] - Redo Log Miner is starting... 
[INFO ] 2024-05-14 09:47:16.743 - [任务 6][TestDate2] - Redo Log Miner has been started... 
[INFO ] 2024-05-14 09:47:39.296 - [任务 6][TestDate2] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-05-14 10:40:14.596 - [任务 6] - Stop task milestones: 66430e68d96c7030f5d248de(任务 6)  
[INFO ] 2024-05-14 10:40:14.597 - [任务 6][TestDate2] - Node TestDate2[e20c39b5-4eeb-422e-af46-97b8aaed0527] running status set to false 
[INFO ] 2024-05-14 10:40:14.604 - [任务 6][TestDate2] - Log Miner is shutting down... 
[INFO ] 2024-05-14 10:40:14.610 - [任务 6][TestDate2] - Auto redo oracle log miner result set closed 
[INFO ] 2024-05-14 10:40:14.610 - [任务 6][TestDate2] - Log Miner has been closed! 
[INFO ] 2024-05-14 10:40:14.644 - [任务 6][TestDate2] - PDK connector node stopped: HazelcastSourcePdkDataNode-e20c39b5-4eeb-422e-af46-97b8aaed0527 
[INFO ] 2024-05-14 10:40:14.645 - [任务 6][TestDate2] - PDK connector node released: HazelcastSourcePdkDataNode-e20c39b5-4eeb-422e-af46-97b8aaed0527 
[INFO ] 2024-05-14 10:40:14.645 - [任务 6][TestDate2] - Node TestDate2[e20c39b5-4eeb-422e-af46-97b8aaed0527] schema data cleaned 
[INFO ] 2024-05-14 10:40:14.645 - [任务 6][TestDate2] - Node TestDate2[e20c39b5-4eeb-422e-af46-97b8aaed0527] monitor closed 
[INFO ] 2024-05-14 10:40:14.646 - [任务 6][TestDate2] - Node TestDate2[e20c39b5-4eeb-422e-af46-97b8aaed0527] close complete, cost 52 ms 
[INFO ] 2024-05-14 10:40:14.675 - [任务 6][OracleTest3] - Node OracleTest3[25b3b260-7489-4599-810a-affe5bfecc15] running status set to false 
[INFO ] 2024-05-14 10:40:14.675 - [任务 6][OracleTest3] - PDK connector node stopped: HazelcastTargetPdkDataNode-25b3b260-7489-4599-810a-affe5bfecc15 
[INFO ] 2024-05-14 10:40:14.675 - [任务 6][OracleTest3] - PDK connector node released: HazelcastTargetPdkDataNode-25b3b260-7489-4599-810a-affe5bfecc15 
[INFO ] 2024-05-14 10:40:14.676 - [任务 6][OracleTest3] - Node OracleTest3[25b3b260-7489-4599-810a-affe5bfecc15] schema data cleaned 
[INFO ] 2024-05-14 10:40:14.676 - [任务 6][OracleTest3] - Node OracleTest3[25b3b260-7489-4599-810a-affe5bfecc15] monitor closed 
[INFO ] 2024-05-14 10:40:14.676 - [任务 6][OracleTest3] - Node OracleTest3[25b3b260-7489-4599-810a-affe5bfecc15] close complete, cost 29 ms 
[INFO ] 2024-05-14 10:40:15.522 - [任务 6] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-05-14 10:40:15.522 - [任务 6] - Stopped task aspect(s) 
[INFO ] 2024-05-14 10:40:15.544 - [任务 6] - Snapshot order controller have been removed 
[INFO ] 2024-05-14 10:40:15.547 - [任务 6] - Remove memory task client succeed, task: 任务 6[66430e68d96c7030f5d248de] 
[INFO ] 2024-05-14 10:40:15.547 - [任务 6] - Destroy memory task client cache succeed, task: 任务 6[66430e68d96c7030f5d248de] 
