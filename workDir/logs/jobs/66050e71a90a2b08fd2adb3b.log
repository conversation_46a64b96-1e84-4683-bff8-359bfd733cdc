[INFO ] 2024-03-28 14:48:49.005 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:48:49.007 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:48:49.008 - [orders(100)][944d409c-7a2b-4daf-9610-ebef047b1a41] - Node 944d409c-7a2b-4daf-9610-ebef047b1a41[944d409c-7a2b-4daf-9610-ebef047b1a41] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:48:49.011 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:48:49.011 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:48:49.012 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:48:49.012 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:48:49.012 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:48:49.012 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:48:49.012 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:48:49.012 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:48:49.013 - [orders(100)][944d409c-7a2b-4daf-9610-ebef047b1a41] - Node 944d409c-7a2b-4daf-9610-ebef047b1a41[944d409c-7a2b-4daf-9610-ebef047b1a41] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:48:49.013 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:48:49.035 - [orders(100)][d58a662f-61f1-4f12-8493-f8f2af7f80a4] - Node d58a662f-61f1-4f12-8493-f8f2af7f80a4[d58a662f-61f1-4f12-8493-f8f2af7f80a4] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:48:49.037 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:48:49.037 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:48:49.037 - [orders(100)][d58a662f-61f1-4f12-8493-f8f2af7f80a4] - Node d58a662f-61f1-4f12-8493-f8f2af7f80a4[d58a662f-61f1-4f12-8493-f8f2af7f80a4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:48:49.037 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:48:49.046 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:48:49.047 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:48:49.307 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 14:48:49.307 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 14:48:49.503 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@31b824fe error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@31b824fe error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@31b824fe error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-28 14:48:49.505 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@51658967 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@51658967 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@51658967 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 14:48:49.754 - [orders(100)][Order Detail] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:48:49.754 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] running status set to false 
[INFO ] 2024-03-28 14:48:49.772 - [orders(100)][Order Detail] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:48:49.772 - [orders(100)][Order Detail] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:48:49.773 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] schema data cleaned 
[INFO ] 2024-03-28 14:48:49.773 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] monitor closed 
[INFO ] 2024-03-28 14:48:49.793 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] close complete, cost 20 ms 
[INFO ] 2024-03-28 14:48:49.794 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] running status set to false 
[INFO ] 2024-03-28 14:48:49.936 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] running status set to false 
[WARN ] 2024-03-28 14:48:49.938 - [orders(100)][Order Detail] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:48:49.938 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] running status set to false 
[INFO ] 2024-03-28 14:48:49.953 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] running status set to false 
[INFO ] 2024-03-28 14:48:49.954 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] running status set to false 
[INFO ] 2024-03-28 14:48:49.962 - [orders(100)][Order Detail] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:48:49.963 - [orders(100)][Order Detail] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:48:49.963 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] schema data cleaned 
[INFO ] 2024-03-28 14:48:49.963 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] monitor closed 
[INFO ] 2024-03-28 14:48:49.992 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] close complete, cost 33 ms 
[INFO ] 2024-03-28 14:48:49.993 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] schema data cleaned 
[INFO ] 2024-03-28 14:48:49.993 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] schema data cleaned 
[INFO ] 2024-03-28 14:48:49.993 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] monitor closed 
[INFO ] 2024-03-28 14:48:49.993 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] monitor closed 
[INFO ] 2024-03-28 14:48:49.993 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] close complete, cost 193 ms 
[INFO ] 2024-03-28 14:48:49.993 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] close complete, cost 199 ms 
[INFO ] 2024-03-28 14:48:50.017 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] schema data cleaned 
[INFO ] 2024-03-28 14:48:50.019 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] schema data cleaned 
[INFO ] 2024-03-28 14:48:50.019 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] monitor closed 
[INFO ] 2024-03-28 14:48:50.019 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] monitor closed 
[INFO ] 2024-03-28 14:48:50.020 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] close complete, cost 81 ms 
[INFO ] 2024-03-28 14:48:50.020 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] close complete, cost 76 ms 
[INFO ] 2024-03-28 14:48:52.063 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] running status set to false 
[INFO ] 2024-03-28 14:48:52.065 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] running status set to false 
[INFO ] 2024-03-28 14:48:52.068 - [orders(100)][d58a662f-61f1-4f12-8493-f8f2af7f80a4] - Node d58a662f-61f1-4f12-8493-f8f2af7f80a4[d58a662f-61f1-4f12-8493-f8f2af7f80a4] running status set to false 
[INFO ] 2024-03-28 14:48:52.083 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] schema data cleaned 
[INFO ] 2024-03-28 14:48:52.085 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] schema data cleaned 
[INFO ] 2024-03-28 14:48:52.086 - [orders(100)][d58a662f-61f1-4f12-8493-f8f2af7f80a4] - Node d58a662f-61f1-4f12-8493-f8f2af7f80a4[d58a662f-61f1-4f12-8493-f8f2af7f80a4] schema data cleaned 
[INFO ] 2024-03-28 14:48:52.088 - [orders(100)][944d409c-7a2b-4daf-9610-ebef047b1a41] - Node 944d409c-7a2b-4daf-9610-ebef047b1a41[944d409c-7a2b-4daf-9610-ebef047b1a41] running status set to false 
[INFO ] 2024-03-28 14:48:52.098 - [orders(100)][944d409c-7a2b-4daf-9610-ebef047b1a41] - Node 944d409c-7a2b-4daf-9610-ebef047b1a41[944d409c-7a2b-4daf-9610-ebef047b1a41] schema data cleaned 
[INFO ] 2024-03-28 14:48:52.100 - [orders(100)][d58a662f-61f1-4f12-8493-f8f2af7f80a4] - Node d58a662f-61f1-4f12-8493-f8f2af7f80a4[d58a662f-61f1-4f12-8493-f8f2af7f80a4] monitor closed 
[INFO ] 2024-03-28 14:48:52.100 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] monitor closed 
[INFO ] 2024-03-28 14:48:52.100 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] monitor closed 
[INFO ] 2024-03-28 14:48:52.101 - [orders(100)][944d409c-7a2b-4daf-9610-ebef047b1a41] - Node 944d409c-7a2b-4daf-9610-ebef047b1a41[944d409c-7a2b-4daf-9610-ebef047b1a41] monitor closed 
[INFO ] 2024-03-28 14:48:52.101 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] close complete, cost 11 ms 
[INFO ] 2024-03-28 14:48:52.101 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] close complete, cost 11 ms 
[INFO ] 2024-03-28 14:48:52.101 - [orders(100)][d58a662f-61f1-4f12-8493-f8f2af7f80a4] - Node d58a662f-61f1-4f12-8493-f8f2af7f80a4[d58a662f-61f1-4f12-8493-f8f2af7f80a4] close complete, cost 34 ms 
[INFO ] 2024-03-28 14:48:52.101 - [orders(100)][944d409c-7a2b-4daf-9610-ebef047b1a41] - Node 944d409c-7a2b-4daf-9610-ebef047b1a41[944d409c-7a2b-4daf-9610-ebef047b1a41] close complete, cost 28 ms 
[INFO ] 2024-03-28 14:48:52.101 - [orders(100)] - load tapTable task 66050e71a90a2b08fd2adb3b-944d409c-7a2b-4daf-9610-ebef047b1a41 complete, cost 3271ms 
[INFO ] 2024-03-28 14:48:52.101 - [orders(100)] - load tapTable task 66050e71a90a2b08fd2adb3b-d58a662f-61f1-4f12-8493-f8f2af7f80a4 complete, cost 3270ms 
[INFO ] 2024-03-28 14:48:57.201 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:48:57.201 - [orders(100)][78b5ea25-720e-4e8d-ac12-b1eb22eec59d] - Node 78b5ea25-720e-4e8d-ac12-b1eb22eec59d[78b5ea25-720e-4e8d-ac12-b1eb22eec59d] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:48:57.202 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:48:57.202 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:48:57.202 - [orders(100)][78b5ea25-720e-4e8d-ac12-b1eb22eec59d] - Node 78b5ea25-720e-4e8d-ac12-b1eb22eec59d[78b5ea25-720e-4e8d-ac12-b1eb22eec59d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:48:57.202 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:48:57.202 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:48:57.204 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:48:57.205 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:48:57.205 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:48:57.242 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 14:48:57.413 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5da7baee error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5da7baee error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5da7baee error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 14:48:57.414 - [orders(100)][Order Detail] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:48:57.435 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] running status set to false 
[INFO ] 2024-03-28 14:48:57.436 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] running status set to false 
[INFO ] 2024-03-28 14:48:57.436 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] running status set to false 
[INFO ] 2024-03-28 14:48:57.436 - [orders(100)][Order Detail] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:48:57.436 - [orders(100)][Order Detail] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:48:57.436 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] schema data cleaned 
[INFO ] 2024-03-28 14:48:57.436 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] monitor closed 
[INFO ] 2024-03-28 14:48:57.437 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] close complete, cost 20 ms 
[INFO ] 2024-03-28 14:48:57.532 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] schema data cleaned 
[INFO ] 2024-03-28 14:48:57.534 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] schema data cleaned 
[INFO ] 2024-03-28 14:48:57.534 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] monitor closed 
[INFO ] 2024-03-28 14:48:57.534 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] monitor closed 
[INFO ] 2024-03-28 14:48:57.534 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] close complete, cost 109 ms 
[INFO ] 2024-03-28 14:48:57.534 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] close complete, cost 109 ms 
[INFO ] 2024-03-28 14:48:59.876 - [orders(100)][78b5ea25-720e-4e8d-ac12-b1eb22eec59d] - Node 78b5ea25-720e-4e8d-ac12-b1eb22eec59d[78b5ea25-720e-4e8d-ac12-b1eb22eec59d] running status set to false 
[INFO ] 2024-03-28 14:48:59.878 - [orders(100)][78b5ea25-720e-4e8d-ac12-b1eb22eec59d] - Node 78b5ea25-720e-4e8d-ac12-b1eb22eec59d[78b5ea25-720e-4e8d-ac12-b1eb22eec59d] schema data cleaned 
[INFO ] 2024-03-28 14:48:59.879 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] running status set to false 
[INFO ] 2024-03-28 14:48:59.882 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] schema data cleaned 
[INFO ] 2024-03-28 14:48:59.884 - [orders(100)][78b5ea25-720e-4e8d-ac12-b1eb22eec59d] - Node 78b5ea25-720e-4e8d-ac12-b1eb22eec59d[78b5ea25-720e-4e8d-ac12-b1eb22eec59d] monitor closed 
[INFO ] 2024-03-28 14:48:59.884 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] monitor closed 
[INFO ] 2024-03-28 14:48:59.885 - [orders(100)][78b5ea25-720e-4e8d-ac12-b1eb22eec59d] - Node 78b5ea25-720e-4e8d-ac12-b1eb22eec59d[78b5ea25-720e-4e8d-ac12-b1eb22eec59d] close complete, cost 51 ms 
[INFO ] 2024-03-28 14:48:59.889 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] close complete, cost 52 ms 
[INFO ] 2024-03-28 14:48:59.889 - [orders(100)] - load tapTable task 66050e71a90a2b08fd2adb3b-78b5ea25-720e-4e8d-ac12-b1eb22eec59d complete, cost 2737ms 
[INFO ] 2024-03-28 14:49:01.393 - [orders(100)][4e6ff3ac-eb4f-48e8-a271-b49581e93a9a] - Node 4e6ff3ac-eb4f-48e8-a271-b49581e93a9a[4e6ff3ac-eb4f-48e8-a271-b49581e93a9a] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:49:01.393 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:01.396 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:01.396 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:01.396 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:01.402 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 14:49:01.402 - [orders(100)][4e6ff3ac-eb4f-48e8-a271-b49581e93a9a] - Node 4e6ff3ac-eb4f-48e8-a271-b49581e93a9a[4e6ff3ac-eb4f-48e8-a271-b49581e93a9a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:01.403 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:01.403 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:01.451 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:01.451 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 14:49:01.632 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7ba29af6 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7ba29af6 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7ba29af6 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 14:49:01.653 - [orders(100)][Order Detail] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:49:01.670 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] running status set to false 
[INFO ] 2024-03-28 14:49:01.670 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] running status set to false 
[INFO ] 2024-03-28 14:49:01.670 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] running status set to false 
[INFO ] 2024-03-28 14:49:01.671 - [orders(100)][Order Detail] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:49:01.671 - [orders(100)][Order Detail] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:49:01.671 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] schema data cleaned 
[INFO ] 2024-03-28 14:49:01.671 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] monitor closed 
[INFO ] 2024-03-28 14:49:01.671 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] close complete, cost 34 ms 
[INFO ] 2024-03-28 14:49:01.739 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] schema data cleaned 
[INFO ] 2024-03-28 14:49:01.740 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] schema data cleaned 
[INFO ] 2024-03-28 14:49:01.740 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] monitor closed 
[INFO ] 2024-03-28 14:49:01.740 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] monitor closed 
[INFO ] 2024-03-28 14:49:01.741 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] close complete, cost 96 ms 
[INFO ] 2024-03-28 14:49:01.741 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] close complete, cost 106 ms 
[INFO ] 2024-03-28 14:49:04.046 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] running status set to false 
[INFO ] 2024-03-28 14:49:04.054 - [orders(100)][4e6ff3ac-eb4f-48e8-a271-b49581e93a9a] - Node 4e6ff3ac-eb4f-48e8-a271-b49581e93a9a[4e6ff3ac-eb4f-48e8-a271-b49581e93a9a] running status set to false 
[INFO ] 2024-03-28 14:49:04.054 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] schema data cleaned 
[INFO ] 2024-03-28 14:49:04.054 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] monitor closed 
[INFO ] 2024-03-28 14:49:04.054 - [orders(100)][4e6ff3ac-eb4f-48e8-a271-b49581e93a9a] - Node 4e6ff3ac-eb4f-48e8-a271-b49581e93a9a[4e6ff3ac-eb4f-48e8-a271-b49581e93a9a] schema data cleaned 
[INFO ] 2024-03-28 14:49:04.054 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] close complete, cost 17 ms 
[INFO ] 2024-03-28 14:49:04.055 - [orders(100)][4e6ff3ac-eb4f-48e8-a271-b49581e93a9a] - Node 4e6ff3ac-eb4f-48e8-a271-b49581e93a9a[4e6ff3ac-eb4f-48e8-a271-b49581e93a9a] monitor closed 
[INFO ] 2024-03-28 14:49:04.056 - [orders(100)][4e6ff3ac-eb4f-48e8-a271-b49581e93a9a] - Node 4e6ff3ac-eb4f-48e8-a271-b49581e93a9a[4e6ff3ac-eb4f-48e8-a271-b49581e93a9a] close complete, cost 33 ms 
[INFO ] 2024-03-28 14:49:04.263 - [orders(100)] - load tapTable task 66050e71a90a2b08fd2adb3b-4e6ff3ac-eb4f-48e8-a271-b49581e93a9a complete, cost 2736ms 
[INFO ] 2024-03-28 14:49:05.516 - [orders(100)][20169ee2-f2d4-4076-ae57-d3cc1322dd15] - Node 20169ee2-f2d4-4076-ae57-d3cc1322dd15[20169ee2-f2d4-4076-ae57-d3cc1322dd15] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:49:05.516 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:05.518 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:05.518 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:05.518 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:05.519 - [orders(100)][20169ee2-f2d4-4076-ae57-d3cc1322dd15] - Node 20169ee2-f2d4-4076-ae57-d3cc1322dd15[20169ee2-f2d4-4076-ae57-d3cc1322dd15] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:05.519 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 14:49:05.519 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:05.519 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:05.519 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:05.597 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 14:49:05.769 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@320bda91 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@320bda91 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@320bda91 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 14:49:05.769 - [orders(100)][Order Detail] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:49:05.784 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] running status set to false 
[INFO ] 2024-03-28 14:49:05.785 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] running status set to false 
[INFO ] 2024-03-28 14:49:05.785 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] running status set to false 
[INFO ] 2024-03-28 14:49:05.789 - [orders(100)][Order Detail] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:49:05.791 - [orders(100)][Order Detail] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:49:05.791 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] schema data cleaned 
[INFO ] 2024-03-28 14:49:05.792 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] monitor closed 
[INFO ] 2024-03-28 14:49:05.792 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] close complete, cost 19 ms 
[INFO ] 2024-03-28 14:49:05.846 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] schema data cleaned 
[INFO ] 2024-03-28 14:49:05.846 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] schema data cleaned 
[INFO ] 2024-03-28 14:49:05.846 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] monitor closed 
[INFO ] 2024-03-28 14:49:05.846 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] monitor closed 
[INFO ] 2024-03-28 14:49:05.846 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] close complete, cost 68 ms 
[INFO ] 2024-03-28 14:49:05.846 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] close complete, cost 64 ms 
[INFO ] 2024-03-28 14:49:08.178 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] running status set to false 
[INFO ] 2024-03-28 14:49:08.178 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] schema data cleaned 
[INFO ] 2024-03-28 14:49:08.178 - [orders(100)][20169ee2-f2d4-4076-ae57-d3cc1322dd15] - Node 20169ee2-f2d4-4076-ae57-d3cc1322dd15[20169ee2-f2d4-4076-ae57-d3cc1322dd15] running status set to false 
[INFO ] 2024-03-28 14:49:08.178 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] monitor closed 
[INFO ] 2024-03-28 14:49:08.178 - [orders(100)][20169ee2-f2d4-4076-ae57-d3cc1322dd15] - Node 20169ee2-f2d4-4076-ae57-d3cc1322dd15[20169ee2-f2d4-4076-ae57-d3cc1322dd15] schema data cleaned 
[INFO ] 2024-03-28 14:49:08.188 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] close complete, cost 4 ms 
[INFO ] 2024-03-28 14:49:08.188 - [orders(100)][20169ee2-f2d4-4076-ae57-d3cc1322dd15] - Node 20169ee2-f2d4-4076-ae57-d3cc1322dd15[20169ee2-f2d4-4076-ae57-d3cc1322dd15] monitor closed 
[INFO ] 2024-03-28 14:49:08.188 - [orders(100)][20169ee2-f2d4-4076-ae57-d3cc1322dd15] - Node 20169ee2-f2d4-4076-ae57-d3cc1322dd15[20169ee2-f2d4-4076-ae57-d3cc1322dd15] close complete, cost 2 ms 
[INFO ] 2024-03-28 14:49:08.190 - [orders(100)] - load tapTable task 66050e71a90a2b08fd2adb3b-20169ee2-f2d4-4076-ae57-d3cc1322dd15 complete, cost 2783ms 
[INFO ] 2024-03-28 14:49:15.917 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:15.918 - [orders(100)][7e052420-c8fc-4496-a20f-157d7b3b2b67] - Node 7e052420-c8fc-4496-a20f-157d7b3b2b67[7e052420-c8fc-4496-a20f-157d7b3b2b67] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:49:15.918 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:15.918 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:15.918 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:15.918 - [orders(100)][7e052420-c8fc-4496-a20f-157d7b3b2b67] - Node 7e052420-c8fc-4496-a20f-157d7b3b2b67[7e052420-c8fc-4496-a20f-157d7b3b2b67] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:15.918 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 14:49:15.918 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 14:49:15.918 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 14:49:15.919 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 14:49:15.965 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:15.965 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:15.965 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:15.965 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:15.966 - [orders(100)][e5e3a924-0f53-4887-b677-0d21ec359f98] - Node e5e3a924-0f53-4887-b677-0d21ec359f98[e5e3a924-0f53-4887-b677-0d21ec359f98] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:49:15.966 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:15.966 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:15.966 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:15.966 - [orders(100)][e5e3a924-0f53-4887-b677-0d21ec359f98] - Node e5e3a924-0f53-4887-b677-0d21ec359f98[e5e3a924-0f53-4887-b677-0d21ec359f98] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:15.966 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:15.966 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:15.966 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:15.971 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:15.974 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:15.975 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:15.975 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:15.975 - [orders(100)][6a1e9b0e-8a34-4bf8-a93e-685c033497cb] - Node 6a1e9b0e-8a34-4bf8-a93e-685c033497cb[6a1e9b0e-8a34-4bf8-a93e-685c033497cb] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:49:15.975 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:15.975 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:15.988 - [orders(100)][6a1e9b0e-8a34-4bf8-a93e-685c033497cb] - Node 6a1e9b0e-8a34-4bf8-a93e-685c033497cb[6a1e9b0e-8a34-4bf8-a93e-685c033497cb] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:15.988 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 14:49:15.988 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 14:49:15.988 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 14:49:16.039 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@239da07e error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@239da07e error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@239da07e error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-28 14:49:16.040 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@298893c6 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@298893c6 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@298893c6 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-28 14:49:16.040 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@79fdd501 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@79fdd501 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@79fdd501 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 14:49:16.161 - [orders(100)][Order Detail] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:49:16.177 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] running status set to false 
[INFO ] 2024-03-28 14:49:16.177 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] running status set to false 
[INFO ] 2024-03-28 14:49:16.178 - [orders(100)][Order Detail] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:49:16.178 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] running status set to false 
[INFO ] 2024-03-28 14:49:16.178 - [orders(100)][Order Detail] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:49:16.178 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] schema data cleaned 
[INFO ] 2024-03-28 14:49:16.178 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] monitor closed 
[INFO ] 2024-03-28 14:49:16.237 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] close complete, cost 15 ms 
[INFO ] 2024-03-28 14:49:16.237 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] schema data cleaned 
[INFO ] 2024-03-28 14:49:16.237 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] schema data cleaned 
[INFO ] 2024-03-28 14:49:16.237 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] monitor closed 
[INFO ] 2024-03-28 14:49:16.237 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] monitor closed 
[INFO ] 2024-03-28 14:49:16.238 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] close complete, cost 59 ms 
[INFO ] 2024-03-28 14:49:16.238 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] close complete, cost 63 ms 
[WARN ] 2024-03-28 14:49:16.308 - [orders(100)][Order Detail] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:49:16.308 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] running status set to false 
[INFO ] 2024-03-28 14:49:16.320 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] running status set to false 
[INFO ] 2024-03-28 14:49:16.320 - [orders(100)][Order Detail] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:49:16.321 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] running status set to false 
[INFO ] 2024-03-28 14:49:16.321 - [orders(100)][Order Detail] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:49:16.322 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] schema data cleaned 
[INFO ] 2024-03-28 14:49:16.322 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] monitor closed 
[INFO ] 2024-03-28 14:49:16.411 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] close complete, cost 16 ms 
[INFO ] 2024-03-28 14:49:16.412 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] schema data cleaned 
[INFO ] 2024-03-28 14:49:16.413 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] schema data cleaned 
[INFO ] 2024-03-28 14:49:16.413 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] monitor closed 
[INFO ] 2024-03-28 14:49:16.413 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] monitor closed 
[INFO ] 2024-03-28 14:49:16.414 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] close complete, cost 96 ms 
[INFO ] 2024-03-28 14:49:16.414 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] close complete, cost 94 ms 
[WARN ] 2024-03-28 14:49:16.501 - [orders(100)][Order Detail] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:49:16.526 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] running status set to false 
[INFO ] 2024-03-28 14:49:16.526 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] running status set to false 
[INFO ] 2024-03-28 14:49:16.526 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] running status set to false 
[INFO ] 2024-03-28 14:49:16.526 - [orders(100)][Order Detail] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:49:16.526 - [orders(100)][Order Detail] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:49:16.527 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] schema data cleaned 
[INFO ] 2024-03-28 14:49:16.527 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] monitor closed 
[INFO ] 2024-03-28 14:49:16.581 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] close complete, cost 25 ms 
[INFO ] 2024-03-28 14:49:16.581 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] schema data cleaned 
[INFO ] 2024-03-28 14:49:16.581 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] schema data cleaned 
[INFO ] 2024-03-28 14:49:16.581 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] monitor closed 
[INFO ] 2024-03-28 14:49:16.582 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] monitor closed 
[INFO ] 2024-03-28 14:49:16.582 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] close complete, cost 70 ms 
[INFO ] 2024-03-28 14:49:16.582 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] close complete, cost 59 ms 
[INFO ] 2024-03-28 14:49:18.571 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] running status set to false 
[INFO ] 2024-03-28 14:49:18.572 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] running status set to false 
[INFO ] 2024-03-28 14:49:18.572 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] running status set to false 
[INFO ] 2024-03-28 14:49:18.572 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] schema data cleaned 
[INFO ] 2024-03-28 14:49:18.573 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] schema data cleaned 
[INFO ] 2024-03-28 14:49:18.573 - [orders(100)][6a1e9b0e-8a34-4bf8-a93e-685c033497cb] - Node 6a1e9b0e-8a34-4bf8-a93e-685c033497cb[6a1e9b0e-8a34-4bf8-a93e-685c033497cb] running status set to false 
[INFO ] 2024-03-28 14:49:18.573 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] schema data cleaned 
[INFO ] 2024-03-28 14:49:18.573 - [orders(100)][e5e3a924-0f53-4887-b677-0d21ec359f98] - Node e5e3a924-0f53-4887-b677-0d21ec359f98[e5e3a924-0f53-4887-b677-0d21ec359f98] running status set to false 
[INFO ] 2024-03-28 14:49:18.574 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] monitor closed 
[INFO ] 2024-03-28 14:49:18.574 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] monitor closed 
[INFO ] 2024-03-28 14:49:18.575 - [orders(100)][7e052420-c8fc-4496-a20f-157d7b3b2b67] - Node 7e052420-c8fc-4496-a20f-157d7b3b2b67[7e052420-c8fc-4496-a20f-157d7b3b2b67] running status set to false 
[INFO ] 2024-03-28 14:49:18.575 - [orders(100)][e5e3a924-0f53-4887-b677-0d21ec359f98] - Node e5e3a924-0f53-4887-b677-0d21ec359f98[e5e3a924-0f53-4887-b677-0d21ec359f98] schema data cleaned 
[INFO ] 2024-03-28 14:49:18.575 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] monitor closed 
[INFO ] 2024-03-28 14:49:18.575 - [orders(100)][6a1e9b0e-8a34-4bf8-a93e-685c033497cb] - Node 6a1e9b0e-8a34-4bf8-a93e-685c033497cb[6a1e9b0e-8a34-4bf8-a93e-685c033497cb] schema data cleaned 
[INFO ] 2024-03-28 14:49:18.575 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] close complete, cost 6 ms 
[INFO ] 2024-03-28 14:49:18.575 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] close complete, cost 7 ms 
[INFO ] 2024-03-28 14:49:18.575 - [orders(100)][7e052420-c8fc-4496-a20f-157d7b3b2b67] - Node 7e052420-c8fc-4496-a20f-157d7b3b2b67[7e052420-c8fc-4496-a20f-157d7b3b2b67] schema data cleaned 
[INFO ] 2024-03-28 14:49:18.575 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] close complete, cost 6 ms 
[INFO ] 2024-03-28 14:49:18.576 - [orders(100)][e5e3a924-0f53-4887-b677-0d21ec359f98] - Node e5e3a924-0f53-4887-b677-0d21ec359f98[e5e3a924-0f53-4887-b677-0d21ec359f98] monitor closed 
[INFO ] 2024-03-28 14:49:18.576 - [orders(100)][7e052420-c8fc-4496-a20f-157d7b3b2b67] - Node 7e052420-c8fc-4496-a20f-157d7b3b2b67[7e052420-c8fc-4496-a20f-157d7b3b2b67] monitor closed 
[INFO ] 2024-03-28 14:49:18.576 - [orders(100)][6a1e9b0e-8a34-4bf8-a93e-685c033497cb] - Node 6a1e9b0e-8a34-4bf8-a93e-685c033497cb[6a1e9b0e-8a34-4bf8-a93e-685c033497cb] monitor closed 
[INFO ] 2024-03-28 14:49:18.576 - [orders(100)][e5e3a924-0f53-4887-b677-0d21ec359f98] - Node e5e3a924-0f53-4887-b677-0d21ec359f98[e5e3a924-0f53-4887-b677-0d21ec359f98] close complete, cost 3 ms 
[INFO ] 2024-03-28 14:49:18.579 - [orders(100)][6a1e9b0e-8a34-4bf8-a93e-685c033497cb] - Node 6a1e9b0e-8a34-4bf8-a93e-685c033497cb[6a1e9b0e-8a34-4bf8-a93e-685c033497cb] close complete, cost 3 ms 
[INFO ] 2024-03-28 14:49:18.579 - [orders(100)][7e052420-c8fc-4496-a20f-157d7b3b2b67] - Node 7e052420-c8fc-4496-a20f-157d7b3b2b67[7e052420-c8fc-4496-a20f-157d7b3b2b67] close complete, cost 3 ms 
[INFO ] 2024-03-28 14:49:18.579 - [orders(100)] - load tapTable task 66050e71a90a2b08fd2adb3b-e5e3a924-0f53-4887-b677-0d21ec359f98 complete, cost 2673ms 
[INFO ] 2024-03-28 14:49:18.579 - [orders(100)] - load tapTable task 66050e71a90a2b08fd2adb3b-6a1e9b0e-8a34-4bf8-a93e-685c033497cb complete, cost 2654ms 
[INFO ] 2024-03-28 14:49:18.580 - [orders(100)] - load tapTable task 66050e71a90a2b08fd2adb3b-7e052420-c8fc-4496-a20f-157d7b3b2b67 complete, cost 2814ms 
[INFO ] 2024-03-28 14:49:18.727 - [orders(100)][a66373af-a710-4bbd-8d4b-b0a6fde0942d] - Node a66373af-a710-4bbd-8d4b-b0a6fde0942d[a66373af-a710-4bbd-8d4b-b0a6fde0942d] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:49:18.727 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:18.727 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:18.727 - [orders(100)][a66373af-a710-4bbd-8d4b-b0a6fde0942d] - Node a66373af-a710-4bbd-8d4b-b0a6fde0942d[a66373af-a710-4bbd-8d4b-b0a6fde0942d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:18.727 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:18.728 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:18.728 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:18.728 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:18.728 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:18.728 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:18.798 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 14:49:18.798 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7701fbb4 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7701fbb4 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7701fbb4 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 14:49:18.963 - [orders(100)][Order Detail] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:49:18.968 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] running status set to false 
[INFO ] 2024-03-28 14:49:18.971 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] running status set to false 
[INFO ] 2024-03-28 14:49:18.972 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] running status set to false 
[INFO ] 2024-03-28 14:49:18.984 - [orders(100)][Order Detail] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:49:18.984 - [orders(100)][Order Detail] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:49:18.984 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] schema data cleaned 
[INFO ] 2024-03-28 14:49:18.984 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] monitor closed 
[INFO ] 2024-03-28 14:49:18.984 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] close complete, cost 30 ms 
[INFO ] 2024-03-28 14:49:19.022 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] schema data cleaned 
[INFO ] 2024-03-28 14:49:19.022 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] schema data cleaned 
[INFO ] 2024-03-28 14:49:19.022 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] monitor closed 
[INFO ] 2024-03-28 14:49:19.022 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] monitor closed 
[INFO ] 2024-03-28 14:49:19.022 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] close complete, cost 64 ms 
[INFO ] 2024-03-28 14:49:19.022 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] close complete, cost 66 ms 
[INFO ] 2024-03-28 14:49:21.337 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] running status set to false 
[INFO ] 2024-03-28 14:49:21.337 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] schema data cleaned 
[INFO ] 2024-03-28 14:49:21.342 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] monitor closed 
[INFO ] 2024-03-28 14:49:21.342 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] close complete, cost 6 ms 
[INFO ] 2024-03-28 14:49:21.343 - [orders(100)][a66373af-a710-4bbd-8d4b-b0a6fde0942d] - Node a66373af-a710-4bbd-8d4b-b0a6fde0942d[a66373af-a710-4bbd-8d4b-b0a6fde0942d] running status set to false 
[INFO ] 2024-03-28 14:49:21.343 - [orders(100)][a66373af-a710-4bbd-8d4b-b0a6fde0942d] - Node a66373af-a710-4bbd-8d4b-b0a6fde0942d[a66373af-a710-4bbd-8d4b-b0a6fde0942d] schema data cleaned 
[INFO ] 2024-03-28 14:49:21.343 - [orders(100)][a66373af-a710-4bbd-8d4b-b0a6fde0942d] - Node a66373af-a710-4bbd-8d4b-b0a6fde0942d[a66373af-a710-4bbd-8d4b-b0a6fde0942d] monitor closed 
[INFO ] 2024-03-28 14:49:21.344 - [orders(100)][a66373af-a710-4bbd-8d4b-b0a6fde0942d] - Node a66373af-a710-4bbd-8d4b-b0a6fde0942d[a66373af-a710-4bbd-8d4b-b0a6fde0942d] close complete, cost 2 ms 
[INFO ] 2024-03-28 14:49:21.545 - [orders(100)] - load tapTable task 66050e71a90a2b08fd2adb3b-a66373af-a710-4bbd-8d4b-b0a6fde0942d complete, cost 2668ms 
[INFO ] 2024-03-28 14:49:23.434 - [orders(100)][5c4c7a40-9c07-4dc0-ac65-0b29fd9daa69] - Node 5c4c7a40-9c07-4dc0-ac65-0b29fd9daa69[5c4c7a40-9c07-4dc0-ac65-0b29fd9daa69] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:49:23.438 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:23.441 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:23.441 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:23.442 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:23.442 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:23.442 - [orders(100)][5c4c7a40-9c07-4dc0-ac65-0b29fd9daa69] - Node 5c4c7a40-9c07-4dc0-ac65-0b29fd9daa69[5c4c7a40-9c07-4dc0-ac65-0b29fd9daa69] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:23.442 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:23.442 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:23.443 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:23.496 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 14:49:23.498 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@37393a30 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@37393a30 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@37393a30 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 14:49:23.605 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:23.605 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:23.605 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:23.606 - [orders(100)][eb00ba8d-d0e0-428e-96af-51c42a3d6f24] - Node eb00ba8d-d0e0-428e-96af-51c42a3d6f24[eb00ba8d-d0e0-428e-96af-51c42a3d6f24] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:49:23.606 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:49:23.606 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:23.606 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:23.606 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:23.606 - [orders(100)][eb00ba8d-d0e0-428e-96af-51c42a3d6f24] - Node eb00ba8d-d0e0-428e-96af-51c42a3d6f24[eb00ba8d-d0e0-428e-96af-51c42a3d6f24] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:23.606 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:49:23.643 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 14:49:23.684 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@63190f1f error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@63190f1f error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@63190f1f error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 14:49:23.688 - [orders(100)][Order Detail] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:49:23.688 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] running status set to false 
[INFO ] 2024-03-28 14:49:23.703 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] running status set to false 
[INFO ] 2024-03-28 14:49:23.703 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] running status set to false 
[INFO ] 2024-03-28 14:49:23.703 - [orders(100)][Order Detail] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:49:23.705 - [orders(100)][Order Detail] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:49:23.705 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] schema data cleaned 
[INFO ] 2024-03-28 14:49:23.705 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] monitor closed 
[INFO ] 2024-03-28 14:49:23.706 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] close complete, cost 18 ms 
[INFO ] 2024-03-28 14:49:23.766 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] schema data cleaned 
[INFO ] 2024-03-28 14:49:23.766 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] schema data cleaned 
[INFO ] 2024-03-28 14:49:23.766 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] monitor closed 
[INFO ] 2024-03-28 14:49:23.766 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] monitor closed 
[INFO ] 2024-03-28 14:49:23.766 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] close complete, cost 69 ms 
[INFO ] 2024-03-28 14:49:23.832 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] close complete, cost 68 ms 
[WARN ] 2024-03-28 14:49:23.832 - [orders(100)][Order Detail] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:49:23.849 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] running status set to false 
[INFO ] 2024-03-28 14:49:23.849 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] running status set to false 
[INFO ] 2024-03-28 14:49:23.849 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] running status set to false 
[INFO ] 2024-03-28 14:49:23.849 - [orders(100)][Order Detail] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:49:23.849 - [orders(100)][Order Detail] - PDK connector node released: HazelcastSampleSourcePdkDataNode-fa68c865-6ef9-4b31-8c2b-beb4d9e30e99 
[INFO ] 2024-03-28 14:49:23.850 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] schema data cleaned 
[INFO ] 2024-03-28 14:49:23.851 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] monitor closed 
[INFO ] 2024-03-28 14:49:23.939 - [orders(100)][Order Detail] - Node Order Detail[fa68c865-6ef9-4b31-8c2b-beb4d9e30e99] close complete, cost 16 ms 
[INFO ] 2024-03-28 14:49:23.942 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] schema data cleaned 
[INFO ] 2024-03-28 14:49:23.942 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] schema data cleaned 
[INFO ] 2024-03-28 14:49:23.942 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] monitor closed 
[INFO ] 2024-03-28 14:49:23.943 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] monitor closed 
[INFO ] 2024-03-28 14:49:23.943 - [orders(100)][Delete Order Details] - Node Delete Order Details[1c4ceec6-1f27-464d-9680-60245ac2d816] close complete, cost 106 ms 
[INFO ] 2024-03-28 14:49:23.943 - [orders(100)][Rename Order Details] - Node Rename Order Details[0d2f5191-188a-430e-b694-29e91805fd08] close complete, cost 97 ms 
[INFO ] 2024-03-28 14:49:26.048 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] running status set to false 
[INFO ] 2024-03-28 14:49:26.049 - [orders(100)][5c4c7a40-9c07-4dc0-ac65-0b29fd9daa69] - Node 5c4c7a40-9c07-4dc0-ac65-0b29fd9daa69[5c4c7a40-9c07-4dc0-ac65-0b29fd9daa69] running status set to false 
[INFO ] 2024-03-28 14:49:26.049 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] schema data cleaned 
[INFO ] 2024-03-28 14:49:26.049 - [orders(100)][5c4c7a40-9c07-4dc0-ac65-0b29fd9daa69] - Node 5c4c7a40-9c07-4dc0-ac65-0b29fd9daa69[5c4c7a40-9c07-4dc0-ac65-0b29fd9daa69] schema data cleaned 
[INFO ] 2024-03-28 14:49:26.049 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] monitor closed 
[INFO ] 2024-03-28 14:49:26.050 - [orders(100)][5c4c7a40-9c07-4dc0-ac65-0b29fd9daa69] - Node 5c4c7a40-9c07-4dc0-ac65-0b29fd9daa69[5c4c7a40-9c07-4dc0-ac65-0b29fd9daa69] monitor closed 
[INFO ] 2024-03-28 14:49:26.050 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] close complete, cost 15 ms 
[INFO ] 2024-03-28 14:49:26.050 - [orders(100)][5c4c7a40-9c07-4dc0-ac65-0b29fd9daa69] - Node 5c4c7a40-9c07-4dc0-ac65-0b29fd9daa69[5c4c7a40-9c07-4dc0-ac65-0b29fd9daa69] close complete, cost 11 ms 
[INFO ] 2024-03-28 14:49:26.185 - [orders(100)] - load tapTable task 66050e71a90a2b08fd2adb3b-5c4c7a40-9c07-4dc0-ac65-0b29fd9daa69 complete, cost 2664ms 
[INFO ] 2024-03-28 14:49:26.187 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] running status set to false 
[INFO ] 2024-03-28 14:49:26.188 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] schema data cleaned 
[INFO ] 2024-03-28 14:49:26.188 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] monitor closed 
[INFO ] 2024-03-28 14:49:26.188 - [orders(100)][Order Details] - Node Order Details[01ec4e11-f6b8-4d48-bdfb-d00f0d995d8a] close complete, cost 1 ms 
[INFO ] 2024-03-28 14:49:26.188 - [orders(100)][eb00ba8d-d0e0-428e-96af-51c42a3d6f24] - Node eb00ba8d-d0e0-428e-96af-51c42a3d6f24[eb00ba8d-d0e0-428e-96af-51c42a3d6f24] running status set to false 
[INFO ] 2024-03-28 14:49:26.188 - [orders(100)][eb00ba8d-d0e0-428e-96af-51c42a3d6f24] - Node eb00ba8d-d0e0-428e-96af-51c42a3d6f24[eb00ba8d-d0e0-428e-96af-51c42a3d6f24] schema data cleaned 
[INFO ] 2024-03-28 14:49:26.188 - [orders(100)][eb00ba8d-d0e0-428e-96af-51c42a3d6f24] - Node eb00ba8d-d0e0-428e-96af-51c42a3d6f24[eb00ba8d-d0e0-428e-96af-51c42a3d6f24] monitor closed 
[INFO ] 2024-03-28 14:49:26.188 - [orders(100)][eb00ba8d-d0e0-428e-96af-51c42a3d6f24] - Node eb00ba8d-d0e0-428e-96af-51c42a3d6f24[eb00ba8d-d0e0-428e-96af-51c42a3d6f24] close complete, cost 0 ms 
[INFO ] 2024-03-28 14:49:26.189 - [orders(100)] - load tapTable task 66050e71a90a2b08fd2adb3b-eb00ba8d-d0e0-428e-96af-51c42a3d6f24 complete, cost 2681ms 
