[INFO ] 2024-07-24 15:20:47.513 - [Heartbeat-sa184mssql] - Start task milestones: 66a0ab4ff604e81d788d0427(Heartbeat-sa184mssql) 
[INFO ] 2024-07-24 15:20:47.651 - [Heartbeat-sa184mssql] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-24 15:20:47.712 - [Heartbeat-sa184mssql] - The engine receives Heartbeat-sa184mssql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-24 15:20:47.810 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[25a076a0-ad42-4275-bab9-34ca05b9c3f1] start preload schema,table counts: 1 
[INFO ] 2024-07-24 15:20:47.810 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3932e05c-d471-4d01-9282-1ac845a9d402] start preload schema,table counts: 1 
[INFO ] 2024-07-24 15:20:47.810 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[25a076a0-ad42-4275-bab9-34ca05b9c3f1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 15:20:47.810 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3932e05c-d471-4d01-9282-1ac845a9d402] preload schema finished, cost 0 ms 
[INFO ] 2024-07-24 15:20:49.340 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-24 15:20:49.341 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-24 15:20:49.341 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-24 15:20:49.342 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":null,"beginTimes":1721805649335,"lastTimes":1721805649335,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-24 15:20:49.342 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-24 15:20:49.404 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Starting batch read, table name: _tapdata_heartbeat_table, offset: null 
[INFO ] 2024-07-24 15:20:49.410 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Start _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-24 15:20:49.479 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Query table '_tapdata_heartbeat_table' counts: 1 
[INFO ] 2024-07-24 15:20:49.483 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Compile _tapdata_heartbeat_table batch read 
[INFO ] 2024-07-24 15:20:49.487 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Table [_tapdata_heartbeat_table] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-24 15:20:49.487 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":null,"beginTimes":1721805649335,"lastTimes":1721805649335,"lastTN":0,"tableStats":{}} 
[INFO ] 2024-07-24 15:20:49.487 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-24 15:20:49.487 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-24 15:20:49.736 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-24 19:12:34.912 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.net.SocketException: Connection reset
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@1bd9321b: {"after":{"id":"66a0aafef604e81d788d03d5","ts":"2024-07-24T11:01:39.582"},"containsIllegalDate":false,"referenceTime":1721818899582,"tableId":"_tapdata_heartbeat_table","time":1721818899582,"type":300}, nodeIds=[3932e05c-d471-4d01-9282-1ac845a9d402], sourceTime=1721818899582, sourceSerialNo=null} 
[ERROR] 2024-07-24 19:12:34.915 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - java.lang.RuntimeException: java.net.SocketException: Connection reset
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@1bd9321b: {"after":{"id":"66a0aafef604e81d788d03d5","ts":"2024-07-24T11:01:39.582"},"containsIllegalDate":false,"referenceTime":1721818899582,"tableId":"_tapdata_heartbeat_table","time":1721818899582,"type":300}, nodeIds=[3932e05c-d471-4d01-9282-1ac845a9d402], sourceTime=1721818899582, sourceSerialNo=null} <-- Error Message -->
java.lang.RuntimeException: java.net.SocketException: Connection reset
 - TapdataEvent{syncStage=CDC, tapEvent=io.tapdata.entity.event.dml.TapInsertRecordEvent@1bd9321b: {"after":{"id":"66a0aafef604e81d788d03d5","ts":"2024-07-24T11:01:39.582"},"containsIllegalDate":false,"referenceTime":1721818899582,"tableId":"_tapdata_heartbeat_table","time":1721818899582,"type":300}, nodeIds=[3932e05c-d471-4d01-9282-1ac845a9d402], sourceTime=1721818899582, sourceSerialNo=null}

<-- Simple Stack Trace -->
Caused by: java.net.SocketException: Connection reset
	java.net.SocketInputStream.read(SocketInputStream.java:210)
	java.net.SocketInputStream.read(SocketInputStream.java:141)
	com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2058)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.Prelogin(SQLServerConnection.java:3133)
	com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:2966)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.net.SocketException: Connection reset
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:633)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.splitDDL2NewBatch(HazelcastTargetPdkBaseNode.java:602)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.cdcProcessEvents(HazelcastTargetPdkBaseNode.java:583)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$8(HazelcastTargetPdkBaseNode.java:532)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:495)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:501)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:547)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.net.SocketException: Connection reset
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:799)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$25(HazelcastTargetPdkDataNode.java:499)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:499)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTapEvents(HazelcastTargetPdkBaseNode.java:688)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:625)
	... 12 more
Caused by: java.net.SocketException: Connection reset
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.connector.mssql.MssqlJdbcRunner.getConnection(MssqlJdbcRunner.java:33)
	at io.tapdata.common.RecordWriter.<init>(RecordWriter.java:37)
	at io.tapdata.connector.mssql.MssqlRecordWriter.<init>(MssqlRecordWriter.java:15)
	at io.tapdata.connector.mssql.MssqlConnector.writeRecord(MssqlConnector.java:320)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$55(HazelcastTargetPdkDataNode.java:853)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$56(HazelcastTargetPdkDataNode.java:805)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 19 more
Caused by: java.net.SocketException: Connection reset
	at java.net.SocketInputStream.read(SocketInputStream.java:210)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.microsoft.sqlserver.jdbc.TDSChannel.read(IOBuffer.java:2058)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.Prelogin(SQLServerConnection.java:3133)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectHelper(SQLServerConnection.java:2966)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.login(SQLServerConnection.java:2628)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connectInternal(SQLServerConnection.java:2471)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.connect(SQLServerConnection.java:1470)
	at com.microsoft.sqlserver.jdbc.SQLServerDriver.connect(SQLServerDriver.java:915)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.access$100(HikariPool.java:71)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:726)
	at com.zaxxer.hikari.pool.HikariPool$PoolEntryCreator.call(HikariPool.java:712)
	... 5 more

[INFO ] 2024-07-24 19:12:34.915 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Job suspend in error handle 
[INFO ] 2024-07-24 19:12:35.318 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3932e05c-d471-4d01-9282-1ac845a9d402] running status set to false 
[INFO ] 2024-07-24 19:12:35.319 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-24 19:12:35.321 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-3932e05c-d471-4d01-9282-1ac845a9d402 
[INFO ] 2024-07-24 19:12:35.321 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-3932e05c-d471-4d01-9282-1ac845a9d402 
[INFO ] 2024-07-24 19:12:35.321 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3932e05c-d471-4d01-9282-1ac845a9d402] schema data cleaned 
[INFO ] 2024-07-24 19:12:35.325 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3932e05c-d471-4d01-9282-1ac845a9d402] monitor closed 
[INFO ] 2024-07-24 19:12:35.325 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3932e05c-d471-4d01-9282-1ac845a9d402] close complete, cost 8 ms 
[INFO ] 2024-07-24 19:12:35.325 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[25a076a0-ad42-4275-bab9-34ca05b9c3f1] running status set to false 
[INFO ] 2024-07-24 19:12:39.808 - [Heartbeat-sa184mssql] - Task [Heartbeat-sa184mssql] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-24 19:12:44.826 - [Heartbeat-sa184mssql] - Task [Heartbeat-sa184mssql] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-24 19:12:49.838 - [Heartbeat-sa184mssql] - Task [Heartbeat-sa184mssql] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-24 19:12:54.848 - [Heartbeat-sa184mssql] - Task [Heartbeat-sa184mssql] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-24 19:29:22.227 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-25a076a0-ad42-4275-bab9-34ca05b9c3f1 
[INFO ] 2024-07-24 19:29:22.228 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-25a076a0-ad42-4275-bab9-34ca05b9c3f1 
[INFO ] 2024-07-24 19:29:22.228 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[25a076a0-ad42-4275-bab9-34ca05b9c3f1] schema data cleaned 
[INFO ] 2024-07-24 19:29:22.229 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[25a076a0-ad42-4275-bab9-34ca05b9c3f1] monitor closed 
[INFO ] 2024-07-24 19:29:22.232 - [Heartbeat-sa184mssql][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[25a076a0-ad42-4275-bab9-34ca05b9c3f1] close complete, cost 22488 ms 
[INFO ] 2024-07-24 19:29:24.286 - [Heartbeat-sa184mssql] - Task [Heartbeat-sa184mssql] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-24 19:29:24.287 - [Heartbeat-sa184mssql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-24 19:29:24.287 - [Heartbeat-sa184mssql] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@c699954 
[INFO ] 2024-07-24 19:29:24.394 - [Heartbeat-sa184mssql] - Stop task milestones: 66a0ab4ff604e81d788d0427(Heartbeat-sa184mssql)  
[INFO ] 2024-07-24 19:29:24.402 - [Heartbeat-sa184mssql] - Stopped task aspect(s) 
[INFO ] 2024-07-24 19:29:24.402 - [Heartbeat-sa184mssql] - Snapshot order controller have been removed 
[INFO ] 2024-07-24 19:29:24.426 - [Heartbeat-sa184mssql] - Remove memory task client succeed, task: Heartbeat-sa184mssql[66a0ab4ff604e81d788d0427] 
[INFO ] 2024-07-24 19:29:24.437 - [Heartbeat-sa184mssql] - Destroy memory task client cache succeed, task: Heartbeat-sa184mssql[66a0ab4ff604e81d788d0427] 
