[INFO ] 2024-07-05 00:32:32.927 - [来自SourceOracle的共享挖掘任务] - Start task milestones: 6686cea04ef26b0b431bdbfe(来自SourceOracle的共享挖掘任务) 
[INFO ] 2024-07-05 00:32:33.172 - [来自SourceOracle的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-05 00:32:33.232 - [来自SourceOracle的共享挖掘任务] - The engine receives 来自SourceOracle的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-05 00:32:33.287 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Node SourceOracle[0733ce7e56114cdf9a55cf1f0dff9c64] start preload schema,table counts: 2 
[INFO ] 2024-07-05 00:32:33.287 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Node SourceOracle[0733ce7e56114cdf9a55cf1f0dff9c64] preload schema finished, cost 1 ms 
[INFO ] 2024-07-05 00:32:33.294 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-05 00:32:33.295 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-05 00:32:33.314 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6686cea066ab5ede8a2a7755, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5_CAR_CLAIM_009, version=v2, tableName=CAR_CLAIM_009, externalStorageTableName=ExternalStorage_SHARE_CDC_-1275224446, shareCdcTaskId=6686cea04ef26b0b431bdbfe, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-05 00:32:33.314 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6686cea066ab5ede8a2a7756, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1986184445, shareCdcTaskId=6686cea04ef26b0b431bdbfe, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-05 00:32:33.570 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_CLAIM_009', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1275224446', head seq: 0, tail seq: -1 
[INFO ] 2024-07-05 00:32:34.147 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA._tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1986184445', head seq: 0, tail seq: -1 
[INFO ] 2024-07-05 00:32:34.350 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-05 00:32:35.114 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Source node "SourceOracle" read batch size: 2000 
[INFO ] 2024-07-05 00:32:35.119 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Source node "SourceOracle" event queue capacity: 4000 
[INFO ] 2024-07-05 00:32:35.119 - [来自SourceOracle的共享挖掘任务][SourceOracle] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-05 00:32:35.415 - [来自SourceOracle的共享挖掘任务][SourceOracle] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":68576937,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 00:32:35.415 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-05 00:32:35.468 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Starting stream read, table list: [C##TAPDATA.CAR_CLAIM_009, C##TAPDATA._tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":68576937,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 00:32:35.723 - [来自SourceOracle的共享挖掘任务][SourceOracle] - total start mining scn: 68576937 
[INFO ] 2024-07-05 00:32:36.876 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 00:54:58.314 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_CLAIM_009', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1275224446', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 00:54:58.314 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA._tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1986184445', head seq: 0, tail seq: 1335 
[INFO ] 2024-07-05 01:54:58.347 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_CLAIM_009', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1275224446', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 01:54:58.348 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA._tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1986184445', head seq: 0, tail seq: 4915 
[INFO ] 2024-07-05 02:04:02.403 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Node SourceOracle[0733ce7e56114cdf9a55cf1f0dff9c64] running status set to false 
[INFO ] 2024-07-05 11:27:00.473 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Log Miner is shutting down... 
[INFO ] 2024-07-05 11:27:00.475 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Log Miner has been closed! 
[INFO ] 2024-07-05 11:27:00.564 - [来自SourceOracle的共享挖掘任务] - Start task milestones: 6686cea04ef26b0b431bdbfe(来自SourceOracle的共享挖掘任务) 
[INFO ] 2024-07-05 11:27:01.900 - [来自SourceOracle的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-05 11:27:01.970 - [来自SourceOracle的共享挖掘任务] - The engine receives 来自SourceOracle的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-05 11:27:03.306 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Node SourceOracle[0733ce7e56114cdf9a55cf1f0dff9c64] start preload schema,table counts: 2 
[INFO ] 2024-07-05 11:27:03.307 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Node SourceOracle[0733ce7e56114cdf9a55cf1f0dff9c64] preload schema finished, cost 0 ms 
[INFO ] 2024-07-05 11:27:03.322 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-05 11:27:03.355 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-05 11:27:03.806 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6686cea066ab5ede8a2a7755, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5_CAR_CLAIM_009, version=v2, tableName=CAR_CLAIM_009, externalStorageTableName=ExternalStorage_SHARE_CDC_-1275224446, shareCdcTaskId=6686cea04ef26b0b431bdbfe, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-05 11:27:03.809 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6686cea066ab5ede8a2a7756, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6686ccd14ef26b0b431bdad5__tapdata_heartbeat_table, version=v2, tableName=_tapdata_heartbeat_table, externalStorageTableName=ExternalStorage_SHARE_CDC_-1986184445, shareCdcTaskId=6686cea04ef26b0b431bdbfe, connectionId=6686ccd14ef26b0b431bdad5) 
[INFO ] 2024-07-05 11:27:03.986 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA._tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1986184445', head seq: 0, tail seq: 5459 
[INFO ] 2024-07-05 11:27:04.200 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_CLAIM_009', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1275224446', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 11:27:04.201 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatavdevcopy?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-05 11:27:05.534 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Source node "SourceOracle" read batch size: 2000 
[INFO ] 2024-07-05 11:27:05.534 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Source node "SourceOracle" event queue capacity: 4000 
[INFO ] 2024-07-05 11:27:05.538 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-05 11:27:05.576 - [来自SourceOracle的共享挖掘任务][SourceOracle] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":68614128,"pendingScn":68614129,"timestamp":1720116236000,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 11:27:05.576 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-05 11:27:05.788 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Starting stream read, table list: [C##TAPDATA.CAR_CLAIM_009, C##TAPDATA._tapdata_heartbeat_table], offset: {"sortString":null,"offsetValue":null,"lastScn":68614128,"pendingScn":68614129,"timestamp":1720116236000,"hexScn":null,"fno":0} 
[INFO ] 2024-07-05 11:27:10.452 - [来自SourceOracle的共享挖掘任务][SourceOracle] - total start mining scn: 68614128 
[INFO ] 2024-07-05 11:27:11.901 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_07_05/o1_mf_1_1411_m8fwpvr1_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 11:27:32.270 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_07_05/o1_mf_1_1412_m8fwqslv_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 11:27:50.953 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_07_05/o1_mf_1_1413_m8fwrnq7_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 11:28:08.643 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_07_05/o1_mf_1_1414_m8fwshw7_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 11:28:27.042 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_07_05/o1_mf_1_1415_m8fwtpvh_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 11:28:44.448 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_07_05/o1_mf_1_1416_m8fx7hbv_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 11:29:03.071 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_07_05/o1_mf_1_1417_m8fx95lh_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 11:29:23.781 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_07_05/o1_mf_1_1418_m8fxc4mm_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 11:29:44.955 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_07_05/o1_mf_1_1419_m8fxf3oh_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 11:30:04.183 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_07_05/o1_mf_1_1420_m8fztw0y_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 11:30:25.326 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 11:47:42.013 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_07_05/o1_mf_1_1421_m8gv6tz3_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 11:47:53.930 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo03.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 11:48:58.381 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_07_05/o1_mf_1_1422_m8gv990z_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 11:48:59.573 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 11:56:03.205 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/fast_recovery_area/ORCL/archivelog/2024_07_05/o1_mf_1_1423_m8gvpknp_.arc',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 11:56:04.588 - [来自SourceOracle的共享挖掘任务][SourceOracle] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo02.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-07-05 12:07:53.869 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Node SourceOracle[0733ce7e56114cdf9a55cf1f0dff9c64] running status set to false 
[INFO ] 2024-07-05 12:07:53.966 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Log Miner is shutting down... 
[INFO ] 2024-07-05 12:07:53.966 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Log Miner has been closed! 
[INFO ] 2024-07-05 12:07:54.046 - [来自SourceOracle的共享挖掘任务][SourceOracle] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-0733ce7e56114cdf9a55cf1f0dff9c64 
[INFO ] 2024-07-05 12:07:54.046 - [来自SourceOracle的共享挖掘任务][SourceOracle] - PDK connector node released: HazelcastSourcePdkShareCDCNode-0733ce7e56114cdf9a55cf1f0dff9c64 
[INFO ] 2024-07-05 12:07:54.046 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Node SourceOracle[0733ce7e56114cdf9a55cf1f0dff9c64] schema data cleaned 
[INFO ] 2024-07-05 12:07:54.048 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Node SourceOracle[0733ce7e56114cdf9a55cf1f0dff9c64] monitor closed 
[INFO ] 2024-07-05 12:07:54.049 - [来自SourceOracle的共享挖掘任务][SourceOracle] - Node SourceOracle[0733ce7e56114cdf9a55cf1f0dff9c64] close complete, cost 198 ms 
[INFO ] 2024-07-05 12:07:54.049 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad008095a9014b1884ea7f60587d767c] running status set to false 
[INFO ] 2024-07-05 12:07:54.069 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-05 12:07:54.071 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-05 12:07:54.071 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad008095a9014b1884ea7f60587d767c] schema data cleaned 
[INFO ] 2024-07-05 12:07:54.071 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad008095a9014b1884ea7f60587d767c] monitor closed 
[INFO ] 2024-07-05 12:07:54.276 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[ad008095a9014b1884ea7f60587d767c] close complete, cost 22 ms 
[INFO ] 2024-07-05 12:07:54.461 - [来自SourceOracle的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-05 12:07:54.461 - [来自SourceOracle的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@778ee9ef 
[INFO ] 2024-07-05 12:07:54.462 - [来自SourceOracle的共享挖掘任务] - Stop task milestones: 6686cea04ef26b0b431bdbfe(来自SourceOracle的共享挖掘任务)  
[INFO ] 2024-07-05 12:07:54.586 - [来自SourceOracle的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-05 12:07:54.586 - [来自SourceOracle的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-05 12:07:54.610 - [来自SourceOracle的共享挖掘任务] - Remove memory task client succeed, task: 来自SourceOracle的共享挖掘任务[6686cea04ef26b0b431bdbfe] 
[INFO ] 2024-07-05 12:07:54.611 - [来自SourceOracle的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自SourceOracle的共享挖掘任务[6686cea04ef26b0b431bdbfe] 
[INFO ] 2024-07-05 12:14:17.577 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA._tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1986184445', head seq: 0, tail seq: 40345 
[INFO ] 2024-07-05 12:27:04.057 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA.CAR_CLAIM_009', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1275224446', head seq: 0, tail seq: 0 
[INFO ] 2024-07-05 12:27:04.058 - [来自SourceOracle的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自SourceOracle的共享挖掘任务_C##TAPDATA._tapdata_heartbeat_table', name space: 'tapdatavdevcopy.ExternalStorage_SHARE_CDC_-1986184445', head seq: 0, tail seq: 41867 
