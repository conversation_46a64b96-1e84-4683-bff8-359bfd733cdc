[INFO ] 2024-03-31 20:57:28.390 - [任务 34] - Start task milestones: 66095bcee940ce2abb839dac(任务 34) 
[INFO ] 2024-03-31 20:57:28.390 - [任务 34] - Task initialization... 
[INFO ] 2024-03-31 20:57:28.508 - [任务 34] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-03-31 20:57:28.508 - [任务 34] - The engine receives 任务 34 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-31 20:57:28.572 - [任务 34][test7] - Node test7[e3f4e690-d3b3-4208-8db5-5468b1ab0a8e] start preload schema,table counts: 1 
[INFO ] 2024-03-31 20:57:28.572 - [任务 34][test1] - Node test1[61335d8b-261b-4e7d-916a-f6774c58550b] start preload schema,table counts: 1 
[INFO ] 2024-03-31 20:57:28.646 - [任务 34][增强JS] - Node 增强JS[765c5b9b-68fb-4f33-9b9b-d616f67034d9] start preload schema,table counts: 1 
[INFO ] 2024-03-31 20:57:28.646 - [任务 34][test1] - Node test1[61335d8b-261b-4e7d-916a-f6774c58550b] preload schema finished, cost 73 ms 
[INFO ] 2024-03-31 20:57:28.646 - [任务 34][增强JS] - Node 增强JS[765c5b9b-68fb-4f33-9b9b-d616f67034d9] preload schema finished, cost 72 ms 
[INFO ] 2024-03-31 20:57:28.646 - [任务 34][test7] - Node test7[e3f4e690-d3b3-4208-8db5-5468b1ab0a8e] preload schema finished, cost 86 ms 
[INFO ] 2024-03-31 20:57:29.025 - [任务 34][test1] - Source node "test1" read batch size: 100 
[INFO ] 2024-03-31 20:57:29.025 - [任务 34][test1] - Source node "test1" event queue capacity: 200 
[INFO ] 2024-03-31 20:57:29.025 - [任务 34][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-31 20:57:29.033 - [任务 34][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-31 20:57:29.160 - [任务 34][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-31 20:57:29.161 - [任务 34][test1] - Initial sync started 
[INFO ] 2024-03-31 20:57:29.161 - [任务 34][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-31 20:57:29.165 - [任务 34][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-31 20:57:29.466 - [任务 34][test7] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-31 21:00:51.659 - [任务 34][增强JS] - Alter table in memory, qualified name: PN_765c5b9b-68fb-4f33-9b9b-d616f67034d9 
[INFO ] 2024-03-31 21:00:51.742 - [任务 34][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-31 21:00:51.950 - [任务 34][test7] - Table id: test7, field name: name1
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@3102dda5: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name1","nullable":true,"partitionKey":false,"pos":8,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711889850326,"tableId":"test7","time":1711889850326,"type":209} <-- Error Message -->
Table id: test7, field name: name1
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@3102dda5: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name1","nullable":true,"partitionKey":false,"pos":8,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711889850326,"tableId":"test7","time":1711889850326,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test7, field name: name1
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@3102dda5: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name1","nullable":true,"partitionKey":false,"pos":8,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711889850326,"tableId":"test7","time":1711889850326,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test7, field name: name1
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:445)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-31 21:00:52.152 - [任务 34][test7] - Job suspend in error handle 
[INFO ] 2024-03-31 21:00:52.894 - [任务 34][test1] - Node test1[61335d8b-261b-4e7d-916a-f6774c58550b] running status set to false 
[INFO ] 2024-03-31 21:00:52.911 - [任务 34][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-61335d8b-261b-4e7d-916a-f6774c58550b 
[INFO ] 2024-03-31 21:00:52.911 - [任务 34][test1] - PDK connector node released: HazelcastSourcePdkDataNode-61335d8b-261b-4e7d-916a-f6774c58550b 
[INFO ] 2024-03-31 21:00:52.913 - [任务 34][test1] - Node test1[61335d8b-261b-4e7d-916a-f6774c58550b] schema data cleaned 
[INFO ] 2024-03-31 21:00:52.921 - [任务 34][test1] - Node test1[61335d8b-261b-4e7d-916a-f6774c58550b] monitor closed 
[INFO ] 2024-03-31 21:00:52.921 - [任务 34][test1] - Initial sync completed 
[INFO ] 2024-03-31 21:00:52.922 - [任务 34][test1] - Node test1[61335d8b-261b-4e7d-916a-f6774c58550b] close complete, cost 28 ms 
[INFO ] 2024-03-31 21:00:52.922 - [任务 34][test1] - Incremental sync starting... 
[INFO ] 2024-03-31 21:00:52.923 - [任务 34][增强JS] - Node 增强JS[765c5b9b-68fb-4f33-9b9b-d616f67034d9] running status set to false 
[INFO ] 2024-03-31 21:00:52.928 - [任务 34][test1] - Incremental sync completed 
[INFO ] 2024-03-31 21:00:52.945 - [任务 34][增强JS] - PDK connector node stopped: ScriptExecutor-Test-a3636fed-3c5a-4fea-ba6b-904e79432669 
[INFO ] 2024-03-31 21:00:52.946 - [任务 34][增强JS] - PDK connector node released: ScriptExecutor-Test-a3636fed-3c5a-4fea-ba6b-904e79432669 
[INFO ] 2024-03-31 21:00:52.946 - [任务 34][增强JS] - [ScriptExecutorsManager-66095bcee940ce2abb839dac-765c5b9b-68fb-4f33-9b9b-d616f67034d9-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-31 21:00:52.954 - [任务 34][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-6a842833-1ea4-406f-9f49-3ff63b5a5675 
[INFO ] 2024-03-31 21:00:52.957 - [任务 34][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-6a842833-1ea4-406f-9f49-3ff63b5a5675 
[INFO ] 2024-03-31 21:00:52.961 - [任务 34][增强JS] - [ScriptExecutorsManager-66095bcee940ce2abb839dac-765c5b9b-68fb-4f33-9b9b-d616f67034d9-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-31 21:00:52.961 - [任务 34][增强JS] - Node 增强JS[765c5b9b-68fb-4f33-9b9b-d616f67034d9] schema data cleaned 
[INFO ] 2024-03-31 21:00:52.961 - [任务 34][增强JS] - Node 增强JS[765c5b9b-68fb-4f33-9b9b-d616f67034d9] monitor closed 
[INFO ] 2024-03-31 21:00:52.961 - [任务 34][增强JS] - Node 增强JS[765c5b9b-68fb-4f33-9b9b-d616f67034d9] close complete, cost 38 ms 
[INFO ] 2024-03-31 21:00:52.961 - [任务 34][test7] - Node test7[e3f4e690-d3b3-4208-8db5-5468b1ab0a8e] running status set to false 
[INFO ] 2024-03-31 21:00:53.004 - [任务 34][test7] - PDK connector node stopped: HazelcastTargetPdkDataNode-e3f4e690-d3b3-4208-8db5-5468b1ab0a8e 
[INFO ] 2024-03-31 21:00:53.006 - [任务 34][test7] - PDK connector node released: HazelcastTargetPdkDataNode-e3f4e690-d3b3-4208-8db5-5468b1ab0a8e 
[INFO ] 2024-03-31 21:00:53.006 - [任务 34][test7] - Node test7[e3f4e690-d3b3-4208-8db5-5468b1ab0a8e] schema data cleaned 
[INFO ] 2024-03-31 21:00:53.006 - [任务 34][test7] - Node test7[e3f4e690-d3b3-4208-8db5-5468b1ab0a8e] monitor closed 
[INFO ] 2024-03-31 21:00:53.215 - [任务 34][test7] - Node test7[e3f4e690-d3b3-4208-8db5-5468b1ab0a8e] close complete, cost 45 ms 
[INFO ] 2024-03-31 21:00:56.267 - [任务 34] - Task [任务 34] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-31 21:00:56.308 - [任务 34] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-31 21:00:56.308 - [任务 34] - Stop task milestones: 66095bcee940ce2abb839dac(任务 34)  
[INFO ] 2024-03-31 21:00:56.325 - [任务 34] - Stopped task aspect(s) 
[INFO ] 2024-03-31 21:00:56.328 - [任务 34] - Snapshot order controller have been removed 
[INFO ] 2024-03-31 21:00:56.360 - [任务 34] - Remove memory task client succeed, task: 任务 34[66095bcee940ce2abb839dac] 
[INFO ] 2024-03-31 21:00:56.365 - [任务 34] - Destroy memory task client cache succeed, task: 任务 34[66095bcee940ce2abb839dac] 
[INFO ] 2024-03-31 21:05:09.534 - [任务 34] - Start task milestones: 66095bcee940ce2abb839dac(任务 34) 
[INFO ] 2024-03-31 21:05:09.534 - [任务 34] - Task initialization... 
[INFO ] 2024-03-31 21:05:09.623 - [任务 34] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-31 21:05:09.624 - [任务 34] - The engine receives 任务 34 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-31 21:05:09.705 - [任务 34][增强JS] - Node 增强JS[765c5b9b-68fb-4f33-9b9b-d616f67034d9] start preload schema,table counts: 1 
[INFO ] 2024-03-31 21:05:09.706 - [任务 34][test7] - Node test7[e3f4e690-d3b3-4208-8db5-5468b1ab0a8e] start preload schema,table counts: 1 
[INFO ] 2024-03-31 21:05:09.706 - [任务 34][test1] - Node test1[61335d8b-261b-4e7d-916a-f6774c58550b] start preload schema,table counts: 1 
[INFO ] 2024-03-31 21:05:09.707 - [任务 34][test7] - Node test7[e3f4e690-d3b3-4208-8db5-5468b1ab0a8e] preload schema finished, cost 21 ms 
[INFO ] 2024-03-31 21:05:09.707 - [任务 34][增强JS] - Node 增强JS[765c5b9b-68fb-4f33-9b9b-d616f67034d9] preload schema finished, cost 21 ms 
[INFO ] 2024-03-31 21:05:09.708 - [任务 34][test1] - Node test1[61335d8b-261b-4e7d-916a-f6774c58550b] preload schema finished, cost 24 ms 
[INFO ] 2024-03-31 21:05:10.030 - [任务 34][test1] - Source node "test1" read batch size: 100 
[INFO ] 2024-03-31 21:05:10.031 - [任务 34][test1] - Source node "test1" event queue capacity: 200 
[INFO ] 2024-03-31 21:05:10.040 - [任务 34][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-31 21:05:10.040 - [任务 34][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-31 21:05:10.086 - [任务 34][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-31 21:05:10.086 - [任务 34][test1] - Initial sync started 
[INFO ] 2024-03-31 21:05:10.087 - [任务 34][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-31 21:05:10.087 - [任务 34][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-31 21:05:10.430 - [任务 34][test7] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-31 21:05:10.458 - [任务 34][test7] - Table "test.test7" exists, skip auto create table 
[INFO ] 2024-03-31 21:05:10.458 - [任务 34][test7] - The table test7 has already exist. 
[INFO ] 2024-03-31 21:35:56.302 - [任务 34][增强JS] - Alter table in memory, qualified name: PN_765c5b9b-68fb-4f33-9b9b-d616f67034d9 
[INFO ] 2024-03-31 21:35:56.505 - [任务 34][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-31 21:35:56.665 - [任务 34][test7] - Table id: test7, field name: name1
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@fe23855: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name1","nullable":true,"partitionKey":false,"pos":8,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711890310674,"tableId":"test7","time":1711890310674,"type":209} <-- Error Message -->
Table id: test7, field name: name1
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@fe23855: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name1","nullable":true,"partitionKey":false,"pos":8,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711890310674,"tableId":"test7","time":1711890310674,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test7, field name: name1
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@fe23855: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name1","nullable":true,"partitionKey":false,"pos":8,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711890310674,"tableId":"test7","time":1711890310674,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test7, field name: name1
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:445)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-31 21:35:56.665 - [任务 34][test7] - Job suspend in error handle 
[WARN ] 2024-03-31 21:35:56.749 - [任务 34] - Save milestone failed: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/update": Broken pipe (Write failed); nested exception is java.net.SocketException: Broken pipe (Write failed). 
[INFO ] 2024-03-31 21:35:57.721 - [任务 34][test1] - Node test1[61335d8b-261b-4e7d-916a-f6774c58550b] running status set to false 
[INFO ] 2024-03-31 21:35:57.721 - [任务 34][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-61335d8b-261b-4e7d-916a-f6774c58550b 
[INFO ] 2024-03-31 21:35:57.721 - [任务 34][test1] - PDK connector node released: HazelcastSourcePdkDataNode-61335d8b-261b-4e7d-916a-f6774c58550b 
[INFO ] 2024-03-31 21:35:57.721 - [任务 34][test1] - Node test1[61335d8b-261b-4e7d-916a-f6774c58550b] schema data cleaned 
[INFO ] 2024-03-31 21:35:57.722 - [任务 34][test1] - Node test1[61335d8b-261b-4e7d-916a-f6774c58550b] monitor closed 
[INFO ] 2024-03-31 21:35:57.728 - [任务 34][test1] - Node test1[61335d8b-261b-4e7d-916a-f6774c58550b] close complete, cost 61 ms 
[INFO ] 2024-03-31 21:35:57.729 - [任务 34][增强JS] - Node 增强JS[765c5b9b-68fb-4f33-9b9b-d616f67034d9] running status set to false 
[INFO ] 2024-03-31 21:35:57.730 - [任务 34][test1] - Initial sync completed 
[INFO ] 2024-03-31 21:35:57.733 - [任务 34][test1] - Incremental sync starting... 
[INFO ] 2024-03-31 21:35:57.733 - [任务 34][test1] - Incremental sync completed 
[INFO ] 2024-03-31 21:35:57.742 - [任务 34][增强JS] - PDK connector node stopped: ScriptExecutor-Test-244695df-e8ca-46e7-a7f0-4401009a5d05 
[INFO ] 2024-03-31 21:35:57.749 - [任务 34][增强JS] - PDK connector node released: ScriptExecutor-Test-244695df-e8ca-46e7-a7f0-4401009a5d05 
[INFO ] 2024-03-31 21:35:57.749 - [任务 34][增强JS] - [ScriptExecutorsManager-66095bcee940ce2abb839dac-765c5b9b-68fb-4f33-9b9b-d616f67034d9-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-31 21:35:57.760 - [任务 34][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-a46a474f-ae83-44c2-b372-77bcdf019914 
[INFO ] 2024-03-31 21:35:57.760 - [任务 34][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-a46a474f-ae83-44c2-b372-77bcdf019914 
[INFO ] 2024-03-31 21:35:57.760 - [任务 34][增强JS] - [ScriptExecutorsManager-66095bcee940ce2abb839dac-765c5b9b-68fb-4f33-9b9b-d616f67034d9-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-31 21:35:57.772 - [任务 34][增强JS] - Node 增强JS[765c5b9b-68fb-4f33-9b9b-d616f67034d9] schema data cleaned 
[INFO ] 2024-03-31 21:35:57.772 - [任务 34][增强JS] - Node 增强JS[765c5b9b-68fb-4f33-9b9b-d616f67034d9] monitor closed 
[INFO ] 2024-03-31 21:35:57.772 - [任务 34][增强JS] - Node 增强JS[765c5b9b-68fb-4f33-9b9b-d616f67034d9] close complete, cost 47 ms 
[INFO ] 2024-03-31 21:35:57.772 - [任务 34][test7] - Node test7[e3f4e690-d3b3-4208-8db5-5468b1ab0a8e] running status set to false 
[INFO ] 2024-03-31 21:35:57.791 - [任务 34][test7] - PDK connector node stopped: HazelcastTargetPdkDataNode-e3f4e690-d3b3-4208-8db5-5468b1ab0a8e 
[INFO ] 2024-03-31 21:35:57.791 - [任务 34][test7] - PDK connector node released: HazelcastTargetPdkDataNode-e3f4e690-d3b3-4208-8db5-5468b1ab0a8e 
[INFO ] 2024-03-31 21:35:57.791 - [任务 34][test7] - Node test7[e3f4e690-d3b3-4208-8db5-5468b1ab0a8e] schema data cleaned 
[INFO ] 2024-03-31 21:35:57.791 - [任务 34][test7] - Node test7[e3f4e690-d3b3-4208-8db5-5468b1ab0a8e] monitor closed 
[INFO ] 2024-03-31 21:35:57.993 - [任务 34][test7] - Node test7[e3f4e690-d3b3-4208-8db5-5468b1ab0a8e] close complete, cost 23 ms 
[INFO ] 2024-03-31 21:36:01.457 - [任务 34] - Task [任务 34] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-31 21:36:01.460 - [任务 34] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-31 21:36:01.474 - [任务 34] - Stop task milestones: 66095bcee940ce2abb839dac(任务 34)  
[INFO ] 2024-03-31 21:36:01.477 - [任务 34] - Stopped task aspect(s) 
[INFO ] 2024-03-31 21:36:01.482 - [任务 34] - Snapshot order controller have been removed 
[INFO ] 2024-03-31 21:36:01.502 - [任务 34] - Remove memory task client succeed, task: 任务 34[66095bcee940ce2abb839dac] 
[INFO ] 2024-03-31 21:36:01.505 - [任务 34] - Destroy memory task client cache succeed, task: 任务 34[66095bcee940ce2abb839dac] 
[INFO ] 2024-03-31 21:41:32.899 - [任务 34] - Task initialization... 
[INFO ] 2024-03-31 21:41:32.912 - [任务 34] - Start task milestones: 66095bcee940ce2abb839dac(任务 34) 
[INFO ] 2024-03-31 21:41:33.009 - [任务 34] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-31 21:41:33.010 - [任务 34] - The engine receives 任务 34 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-31 21:41:33.179 - [任务 34][增强JS] - Node 增强JS[765c5b9b-68fb-4f33-9b9b-d616f67034d9] start preload schema,table counts: 1 
[INFO ] 2024-03-31 21:41:33.180 - [任务 34][test7] - Node test7[e3f4e690-d3b3-4208-8db5-5468b1ab0a8e] start preload schema,table counts: 1 
[INFO ] 2024-03-31 21:41:33.180 - [任务 34][test1] - Node test1[61335d8b-261b-4e7d-916a-f6774c58550b] start preload schema,table counts: 1 
[INFO ] 2024-03-31 21:41:33.350 - [任务 34][test7] - Node test7[e3f4e690-d3b3-4208-8db5-5468b1ab0a8e] preload schema finished, cost 149 ms 
[INFO ] 2024-03-31 21:41:33.360 - [任务 34][test1] - Node test1[61335d8b-261b-4e7d-916a-f6774c58550b] preload schema finished, cost 148 ms 
[INFO ] 2024-03-31 21:41:33.361 - [任务 34][增强JS] - Node 增强JS[765c5b9b-68fb-4f33-9b9b-d616f67034d9] preload schema finished, cost 149 ms 
[INFO ] 2024-03-31 21:41:33.898 - [任务 34][test1] - Source node "test1" read batch size: 100 
[INFO ] 2024-03-31 21:41:33.899 - [任务 34][test1] - Source node "test1" event queue capacity: 200 
[INFO ] 2024-03-31 21:41:33.899 - [任务 34][test1] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-31 21:41:34.108 - [任务 34][test1] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-03-31 21:41:34.118 - [任务 34][test1] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-03-31 21:41:34.124 - [任务 34][test1] - Initial sync started 
[INFO ] 2024-03-31 21:41:34.126 - [任务 34][test1] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-03-31 21:41:34.292 - [任务 34][test1] - Table test1 is going to be initial synced 
[INFO ] 2024-03-31 21:41:34.293 - [任务 34][test7] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-31 21:41:34.322 - [任务 34][test7] - Table "test.test7" exists, skip auto create table 
[INFO ] 2024-03-31 21:41:34.323 - [任务 34][test7] - The table test7 has already exist. 
[INFO ] 2024-03-31 22:04:14.524 - [任务 34][增强JS] - Alter table in memory, qualified name: PN_765c5b9b-68fb-4f33-9b9b-d616f67034d9 
[INFO ] 2024-03-31 22:04:14.747 - [任务 34][增强JS] - Alter table schema transform finished 
[ERROR] 2024-03-31 22:04:14.894 - [任务 34][test7] - Table id: test7, field name: name1
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@e91972f: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name1","nullable":true,"partitionKey":false,"pos":8,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711892495050,"tableId":"test7","time":1711892495050,"type":209} <-- Error Message -->
Table id: test7, field name: name1
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@e91972f: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name1","nullable":true,"partitionKey":false,"pos":8,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711892495050,"tableId":"test7","time":1711892495050,"type":209}

<-- Simple Stack Trace -->
Caused by: io.tapdata.error.TapEventException: Table id: test7, field name: name1
 - io.tapdata.entity.event.ddl.table.TapNewFieldEvent@e91972f: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"name1","nullable":true,"partitionKey":false,"pos":8,"primaryKey":false,"tapType":{"bytes":200,"defaultValue":200,"type":10}}],"referenceTime":1711892495050,"tableId":"test7","time":1711892495050,"type":209}
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	...

<-- Full Stack Trace -->
Table id: test7, field name: name1
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.executeNewFieldFunction(HazelcastTargetPdkDataNode.java:415)
	at io.tapdata.entity.simplify.pretty.ClassHandlers.handle(ClassHandlers.java:28)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeDDL(HazelcastTargetPdkDataNode.java:394)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$20(HazelcastTargetPdkDataNode.java:380)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapRecordEvents(HazelcastTargetPdkBaseNode.java:1336)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:367)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:445)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-31 22:04:14.894 - [任务 34][test7] - Job suspend in error handle 
[WARN ] 2024-03-31 22:04:14.975 - [任务 34] - Save milestone failed: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/update": Broken pipe (Write failed); nested exception is java.net.SocketException: Broken pipe (Write failed). 
[WARN ] 2024-03-31 22:04:14.975 - [任务 34][test7] - Save to snapshot failed, collection: Task/syncProgress/66095bcee940ce2abb839dac, object: {61335d8b-261b-4e7d-916a-f6774c58550b,e3f4e690-d3b3-4208-8db5-5468b1ab0a8e=SyncProgress{eventSerialNo=3, syncStage='INITIAL_SYNC', batchOffset='{test1=[]}', streamOffset='[]'}}, errors: Failed to call rest api, msg I/O error on POST request for "http://127.0.0.1:3000/api/Task/syncProgress/66095bcee940ce2abb839dac": Broken pipe (Write failed); nested exception is java.net.SocketException: Broken pipe (Write failed). 
[INFO ] 2024-03-31 22:04:15.860 - [任务 34][test1] - Node test1[61335d8b-261b-4e7d-916a-f6774c58550b] running status set to false 
[INFO ] 2024-03-31 22:04:15.860 - [任务 34][test1] - PDK connector node stopped: HazelcastSourcePdkDataNode-61335d8b-261b-4e7d-916a-f6774c58550b 
[INFO ] 2024-03-31 22:04:15.862 - [任务 34][test1] - PDK connector node released: HazelcastSourcePdkDataNode-61335d8b-261b-4e7d-916a-f6774c58550b 
[INFO ] 2024-03-31 22:04:15.862 - [任务 34][test1] - Node test1[61335d8b-261b-4e7d-916a-f6774c58550b] schema data cleaned 
[INFO ] 2024-03-31 22:04:15.874 - [任务 34][test1] - Node test1[61335d8b-261b-4e7d-916a-f6774c58550b] monitor closed 
[INFO ] 2024-03-31 22:04:15.878 - [任务 34][test1] - Node test1[61335d8b-261b-4e7d-916a-f6774c58550b] close complete, cost 35 ms 
[INFO ] 2024-03-31 22:04:15.885 - [任务 34][增强JS] - Node 增强JS[765c5b9b-68fb-4f33-9b9b-d616f67034d9] running status set to false 
[INFO ] 2024-03-31 22:04:15.887 - [任务 34][增强JS] - PDK connector node stopped: ScriptExecutor-Test-894e383c-139f-4093-a54c-0362cd771191 
[INFO ] 2024-03-31 22:04:15.888 - [任务 34][test1] - Initial sync completed 
[INFO ] 2024-03-31 22:04:15.888 - [任务 34][增强JS] - PDK connector node released: ScriptExecutor-Test-894e383c-139f-4093-a54c-0362cd771191 
[INFO ] 2024-03-31 22:04:15.890 - [任务 34][增强JS] - [ScriptExecutorsManager-66095bcee940ce2abb839dac-765c5b9b-68fb-4f33-9b9b-d616f67034d9-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-31 22:04:15.890 - [任务 34][test1] - Incremental sync starting... 
[INFO ] 2024-03-31 22:04:15.941 - [任务 34][test1] - Incremental sync completed 
[INFO ] 2024-03-31 22:04:15.941 - [任务 34][增强JS] - PDK connector node stopped: ScriptExecutor-TargetMysql-298fd60d-714e-4dd6-ac39-bdcab5b51ce1 
[INFO ] 2024-03-31 22:04:15.942 - [任务 34][增强JS] - PDK connector node released: ScriptExecutor-TargetMysql-298fd60d-714e-4dd6-ac39-bdcab5b51ce1 
[INFO ] 2024-03-31 22:04:15.942 - [任务 34][增强JS] - [ScriptExecutorsManager-66095bcee940ce2abb839dac-765c5b9b-68fb-4f33-9b9b-d616f67034d9-6603c0c38b5bca60f72df4fb] schema data cleaned 
[INFO ] 2024-03-31 22:04:15.948 - [任务 34][增强JS] - Node 增强JS[765c5b9b-68fb-4f33-9b9b-d616f67034d9] schema data cleaned 
[INFO ] 2024-03-31 22:04:15.952 - [任务 34][增强JS] - Node 增强JS[765c5b9b-68fb-4f33-9b9b-d616f67034d9] monitor closed 
[INFO ] 2024-03-31 22:04:15.956 - [任务 34][增强JS] - Node 增强JS[765c5b9b-68fb-4f33-9b9b-d616f67034d9] close complete, cost 81 ms 
[INFO ] 2024-03-31 22:04:15.956 - [任务 34][test7] - Node test7[e3f4e690-d3b3-4208-8db5-5468b1ab0a8e] running status set to false 
[INFO ] 2024-03-31 22:04:16.009 - [任务 34][test7] - PDK connector node stopped: HazelcastTargetPdkDataNode-e3f4e690-d3b3-4208-8db5-5468b1ab0a8e 
[INFO ] 2024-03-31 22:04:16.009 - [任务 34][test7] - PDK connector node released: HazelcastTargetPdkDataNode-e3f4e690-d3b3-4208-8db5-5468b1ab0a8e 
[INFO ] 2024-03-31 22:04:16.009 - [任务 34][test7] - Node test7[e3f4e690-d3b3-4208-8db5-5468b1ab0a8e] schema data cleaned 
[INFO ] 2024-03-31 22:04:16.010 - [任务 34][test7] - Node test7[e3f4e690-d3b3-4208-8db5-5468b1ab0a8e] monitor closed 
[INFO ] 2024-03-31 22:04:16.216 - [任务 34][test7] - Node test7[e3f4e690-d3b3-4208-8db5-5468b1ab0a8e] close complete, cost 55 ms 
[INFO ] 2024-03-31 22:04:19.350 - [任务 34] - Task [任务 34] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-31 22:04:19.351 - [任务 34] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-31 22:04:19.379 - [任务 34] - Stop task milestones: 66095bcee940ce2abb839dac(任务 34)  
[INFO ] 2024-03-31 22:04:19.382 - [任务 34] - Stopped task aspect(s) 
[INFO ] 2024-03-31 22:04:19.382 - [任务 34] - Snapshot order controller have been removed 
[INFO ] 2024-03-31 22:04:19.420 - [任务 34] - Remove memory task client succeed, task: 任务 34[66095bcee940ce2abb839dac] 
[INFO ] 2024-03-31 22:04:19.421 - [任务 34] - Destroy memory task client cache succeed, task: 任务 34[66095bcee940ce2abb839dac] 
