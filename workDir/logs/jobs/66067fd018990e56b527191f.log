[INFO ] 2024-03-29 16:58:16.853 - [employees_import_import_import_import_import_import_import_import_import] - Task initialization... 
[INFO ] 2024-03-29 16:58:16.877 - [employees_import_import_import_import_import_import_import_import_import] - Start task milestones: 66067fd018990e56b527191f(employees_import_import_import_import_import_import_import_import_import) 
[INFO ] 2024-03-29 16:58:16.877 - [employees_import_import_import_import_import_import_import_import_import] - No<PERSON> performs snapshot read by order list: [ null ] -> [ null,null ] -> [ null ] -> [ null ] 
[INFO ] 2024-03-29 16:58:17.141 - [employees_import_import_import_import_import_import_import_import_import] - The engine receives employees_import_import_import_import_import_import_import_import_import task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 16:58:17.282 - [employees_import_import_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[de078065-5e92-40c6-981b-edf4b4be8369] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:58:17.282 - [employees_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[2c7147ac-41f4-4c68-8de7-26fb77babf3f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:58:17.282 - [employees_import_import_import_import_import_import_import_import_import][employees] - Node employees[5269b34a-9767-4508-8269-718fd4aeaa68] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:58:17.283 - [employees_import_import_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[195a8b60-f852-42cf-987e-dea71f712fba] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:58:17.296 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[0dd88320-55aa-4e78-a8e0-df90a59b2578] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:58:17.296 - [employees_import_import_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[a14d7f67-027f-4796-9f47-7611cabe00bb] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:58:17.297 - [employees_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[7b450f38-0b92-45f2-940e-ffdd1c5bb40c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:58:17.297 - [employees_import_import_import_import_import_import_import_import_import][Territories] - Node Territories[d80f826f-5d4d-44d8-b85b-ecdb92cff606] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:58:17.297 - [employees_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[58697add-3efa-414b-a969-cbd4acd5fdc4] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:58:17.297 - [employees_import_import_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[6d650da8-4f9b-4138-9552-29b43309de9c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:58:17.310 - [employees_import_import_import_import_import_import_import_import_import][merge] - Node merge[9b3040aa-0938-4bac-b7cd-1f3c40624d8c] start preload schema,table counts: 6 
[INFO ] 2024-03-29 16:58:17.310 - [employees_import_import_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[001c6302-7e40-40b3-8b9d-8634b43e108e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:58:17.334 - [employees_import_import_import_import_import_import_import_import_import][Region] - Node Region[18eb6013-71c8-4aa4-ac91-c909d4e54fca] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:58:17.334 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[fd2d0c44-7913-47b2-b214-c324717fe9fa] start preload schema,table counts: 1 
[INFO ] 2024-03-29 16:58:17.375 - [employees_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[58697add-3efa-414b-a969-cbd4acd5fdc4] preload schema finished, cost 79 ms 
[INFO ] 2024-03-29 16:58:17.375 - [employees_import_import_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[001c6302-7e40-40b3-8b9d-8634b43e108e] preload schema finished, cost 65 ms 
[INFO ] 2024-03-29 16:58:17.375 - [employees_import_import_import_import_import_import_import_import_import][Region] - Node Region[18eb6013-71c8-4aa4-ac91-c909d4e54fca] preload schema finished, cost 63 ms 
[INFO ] 2024-03-29 16:58:17.375 - [employees_import_import_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[195a8b60-f852-42cf-987e-dea71f712fba] preload schema finished, cost 93 ms 
[INFO ] 2024-03-29 16:58:17.375 - [employees_import_import_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[a14d7f67-027f-4796-9f47-7611cabe00bb] preload schema finished, cost 79 ms 
[INFO ] 2024-03-29 16:58:17.376 - [employees_import_import_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[6d650da8-4f9b-4138-9552-29b43309de9c] preload schema finished, cost 78 ms 
[INFO ] 2024-03-29 16:58:17.381 - [employees_import_import_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[de078065-5e92-40c6-981b-edf4b4be8369] preload schema finished, cost 96 ms 
[INFO ] 2024-03-29 16:58:17.381 - [employees_import_import_import_import_import_import_import_import_import][Territories] - Node Territories[d80f826f-5d4d-44d8-b85b-ecdb92cff606] preload schema finished, cost 84 ms 
[INFO ] 2024-03-29 16:58:17.381 - [employees_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[2c7147ac-41f4-4c68-8de7-26fb77babf3f] preload schema finished, cost 100 ms 
[INFO ] 2024-03-29 16:58:17.381 - [employees_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[7b450f38-0b92-45f2-940e-ffdd1c5bb40c] preload schema finished, cost 84 ms 
[INFO ] 2024-03-29 16:58:17.381 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[0dd88320-55aa-4e78-a8e0-df90a59b2578] preload schema finished, cost 84 ms 
[INFO ] 2024-03-29 16:58:17.409 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[fd2d0c44-7913-47b2-b214-c324717fe9fa] preload schema finished, cost 75 ms 
[INFO ] 2024-03-29 16:58:17.580 - [employees_import_import_import_import_import_import_import_import_import][employees] - Node employees[5269b34a-9767-4508-8269-718fd4aeaa68] preload schema finished, cost 127 ms 
[INFO ] 2024-03-29 16:58:17.584 - [employees_import_import_import_import_import_import_import_import_import][merge] - Node merge[9b3040aa-0938-4bac-b7cd-1f3c40624d8c] preload schema finished, cost 270 ms 
[INFO ] 2024-03-29 16:58:17.584 - [employees_import_import_import_import_import_import_import_import_import][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 16:58:17.584 - [employees_import_import_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  Territories(6d650da8-4f9b-4138-9552-29b43309de9c)
    ->Region(de078065-5e92-40c6-981b-edf4b4be8369)
} 
[INFO ] 2024-03-29 16:58:17.584 - [employees_import_import_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  EmployeeTerritories(195a8b60-f852-42cf-987e-dea71f712fba)
    ->Territories(6d650da8-4f9b-4138-9552-29b43309de9c)
} 
[INFO ] 2024-03-29 16:58:17.584 - [employees_import_import_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  Employees(7b450f38-0b92-45f2-940e-ffdd1c5bb40c)
    ->Employees(2c7147ac-41f4-4c68-8de7-26fb77babf3f)
    ->EmployeeTerritories(195a8b60-f852-42cf-987e-dea71f712fba)
} 
[INFO ] 2024-03-29 16:58:18.671 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" read batch size: 500 
[INFO ] 2024-03-29 16:58:18.675 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" event queue capacity: 1000 
[INFO ] 2024-03-29 16:58:18.675 - [employees_import_import_import_import_import_import_import_import_import][Employees] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 16:58:18.808 - [employees_import_import_import_import_import_import_import_import_import][Employees] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 16:58:18.808 - [employees_import_import_import_import_import_import_import_import_import][Region] - Source node "Region" read batch size: 500 
[INFO ] 2024-03-29 16:58:18.808 - [employees_import_import_import_import_import_import_import_import_import][Region] - Source node "Region" event queue capacity: 1000 
[INFO ] 2024-03-29 16:58:18.849 - [employees_import_import_import_import_import_import_import_import_import][Region] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 16:58:18.850 - [employees_import_import_import_import_import_import_import_import_import][Region] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 16:58:18.850 - [employees_import_import_import_import_import_import_import_import_import] - Node[Employees] is waiting for running 
[INFO ] 2024-03-29 16:58:18.912 - [employees_import_import_import_import_import_import_import_import_import] - Node[Region] is waiting for running 
[INFO ] 2024-03-29 16:58:18.915 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" read batch size: 500 
[INFO ] 2024-03-29 16:58:18.915 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" event queue capacity: 1000 
[INFO ] 2024-03-29 16:58:18.915 - [employees_import_import_import_import_import_import_import_import_import][Employees] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 16:58:18.960 - [employees_import_import_import_import_import_import_import_import_import][Employees] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 16:58:18.961 - [employees_import_import_import_import_import_import_import_import_import][employees] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 16:58:18.990 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Initial sync started 
[INFO ] 2024-03-29 16:58:19.004 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Starting batch read, table name: Employees, offset: null 
[INFO ] 2024-03-29 16:58:19.011 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Table Employees is going to be initial synced 
[INFO ] 2024-03-29 16:58:19.058 - [employees_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Region_de078065-5e92-40c6-981b-edf4b4be8369__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 16:58:19.069 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Query table 'Employees' counts: 0 
[INFO ] 2024-03-29 16:58:19.069 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 16:58:19.475 - [employees_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Employees_2c7147ac-41f4-4c68-8de7-26fb77babf3f__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 16:58:19.483 - [employees_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Source node "EmployeeTerritories" read batch size: 500 
[INFO ] 2024-03-29 16:58:19.485 - [employees_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Source node "EmployeeTerritories" event queue capacity: 1000 
[INFO ] 2024-03-29 16:58:19.485 - [employees_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 16:58:19.557 - [employees_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 16:58:19.558 - [employees_import_import_import_import_import_import_import_import_import] - Node[EmployeeTerritories] is waiting for running 
[INFO ] 2024-03-29 16:58:19.723 - [employees_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_EmployeeTerritories_195a8b60-f852-42cf-987e-dea71f712fba__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 16:58:19.723 - [employees_import_import_import_import_import_import_import_import_import][Territories] - Source node "Territories" read batch size: 500 
[INFO ] 2024-03-29 16:58:19.723 - [employees_import_import_import_import_import_import_import_import_import][Territories] - Source node "Territories" event queue capacity: 1000 
[INFO ] 2024-03-29 16:58:19.723 - [employees_import_import_import_import_import_import_import_import_import][Territories] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 16:58:19.725 - [employees_import_import_import_import_import_import_import_import_import][Territories] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 16:58:19.778 - [employees_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Territories_6d650da8-4f9b-4138-9552-29b43309de9c__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 16:58:19.778 - [employees_import_import_import_import_import_import_import_import_import] - Node[Territories] is waiting for running 
[INFO ] 2024-03-29 16:58:19.809 - [employees_import_import_import_import_import_import_import_import_import][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 16:58:19.809 - [employees_import_import_import_import_import_import_import_import_import][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 16:58:20.978 - [employees_import_import_import_import_import_import_import_import_import] - Node[Employees] finish, notify next layer to run 
[INFO ] 2024-03-29 16:58:20.978 - [employees_import_import_import_import_import_import_import_import_import] - Next layer have been notified: [null,null] 
[INFO ] 2024-03-29 16:58:20.979 - [employees_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Initial sync started 
[INFO ] 2024-03-29 16:58:20.979 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Initial sync started 
[INFO ] 2024-03-29 16:58:20.980 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Starting batch read, table name: Employees, offset: null 
[INFO ] 2024-03-29 16:58:20.980 - [employees_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Starting batch read, table name: EmployeeTerritories, offset: null 
[INFO ] 2024-03-29 16:58:21.039 - [employees_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Table EmployeeTerritories is going to be initial synced 
[INFO ] 2024-03-29 16:58:21.040 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Table Employees is going to be initial synced 
[INFO ] 2024-03-29 16:58:21.075 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Query table 'Employees' counts: 0 
[INFO ] 2024-03-29 16:58:21.076 - [employees_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Query table 'EmployeeTerritories' counts: 1 
[INFO ] 2024-03-29 16:58:21.076 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 16:58:21.076 - [employees_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Initial sync completed 
[ERROR] 2024-03-29 16:58:21.604 - [employees_import_import_import_import_import_import_import_import_import][merge] - - Map name: HazelcastMergeNode_EmployeeTerritories_195a8b60-f852-42cf-987e-dea71f712fba__TPORIG
- Join key: employeeId
- Data: {TerritoryID=01581, EmployeeID=1} <-- Error Message -->
- Map name: HazelcastMergeNode_EmployeeTerritories_195a8b60-f852-42cf-987e-dea71f712fba__TPORIG
- Join key: employeeId
- Data: {TerritoryID=01581, EmployeeID=1}

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.TapCodeException: - Map name: HazelcastMergeNode_EmployeeTerritories_195a8b60-f852-42cf-987e-dea71f712fba__TPORIG
- Join key: employeeId
- Data: {TerritoryID=01581, EmployeeID=1}
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getJoinValueKeyBySource(HazelcastMergeNode.java:1303)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.upsertCache(HazelcastMergeNode.java:1190)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.handleCacheByOp(HazelcastMergeNode.java:1135)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.cache(HazelcastMergeNode.java:1116)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doBatchCache(HazelcastMergeNode.java:387)
	...

<-- Full Stack Trace -->
- Map name: HazelcastMergeNode_EmployeeTerritories_195a8b60-f852-42cf-987e-dea71f712fba__TPORIG
- Join key: employeeId
- Data: {TerritoryID=01581, EmployeeID=1}
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.getJoinValueKeyBySource(HazelcastMergeNode.java:1303)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.upsertCache(HazelcastMergeNode.java:1190)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.handleCacheByOp(HazelcastMergeNode.java:1135)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.cache(HazelcastMergeNode.java:1116)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.doBatchCache(HazelcastMergeNode.java:387)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastMergeNode.tryProcess(HazelcastMergeNode.java:286)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:136)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:124)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$0(HazelcastProcessorBaseNode.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode$EventBatchProcessor.lambda$new$1(HazelcastProcessorBaseNode.java:405)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-03-29 16:58:21.814 - [employees_import_import_import_import_import_import_import_import_import][merge] - Job suspend in error handle 
[INFO ] 2024-03-29 16:58:22.095 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[fd2d0c44-7913-47b2-b214-c324717fe9fa] running status set to false 
[INFO ] 2024-03-29 16:58:22.095 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Incremental sync starting... 
[INFO ] 2024-03-29 16:58:22.095 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Incremental sync completed 
[INFO ] 2024-03-29 16:58:22.127 - [employees_import_import_import_import_import_import_import_import_import][Employees] - PDK connector node stopped: HazelcastSourcePdkDataNode-fd2d0c44-7913-47b2-b214-c324717fe9fa 
[INFO ] 2024-03-29 16:58:22.127 - [employees_import_import_import_import_import_import_import_import_import][Employees] - PDK connector node released: HazelcastSourcePdkDataNode-fd2d0c44-7913-47b2-b214-c324717fe9fa 
[INFO ] 2024-03-29 16:58:22.127 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[fd2d0c44-7913-47b2-b214-c324717fe9fa] schema data cleaned 
[INFO ] 2024-03-29 16:58:22.129 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[fd2d0c44-7913-47b2-b214-c324717fe9fa] monitor closed 
[INFO ] 2024-03-29 16:58:22.129 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[fd2d0c44-7913-47b2-b214-c324717fe9fa] close complete, cost 37 ms 
[INFO ] 2024-03-29 16:58:22.134 - [employees_import_import_import_import_import_import_import_import_import][Territories] - Node Territories[d80f826f-5d4d-44d8-b85b-ecdb92cff606] running status set to false 
[INFO ] 2024-03-29 16:58:22.134 - [employees_import_import_import_import_import_import_import_import_import][Territories] - Initial sync started 
[INFO ] 2024-03-29 16:58:22.139 - [employees_import_import_import_import_import_import_import_import_import][Territories] - Initial sync completed 
[INFO ] 2024-03-29 16:58:22.139 - [employees_import_import_import_import_import_import_import_import_import][Territories] - Incremental sync starting... 
[INFO ] 2024-03-29 16:58:22.141 - [employees_import_import_import_import_import_import_import_import_import][Territories] - Incremental sync completed 
[INFO ] 2024-03-29 16:58:22.141 - [employees_import_import_import_import_import_import_import_import_import][Territories] - PDK connector node stopped: HazelcastSourcePdkDataNode-d80f826f-5d4d-44d8-b85b-ecdb92cff606 
[INFO ] 2024-03-29 16:58:22.141 - [employees_import_import_import_import_import_import_import_import_import][Territories] - PDK connector node released: HazelcastSourcePdkDataNode-d80f826f-5d4d-44d8-b85b-ecdb92cff606 
[INFO ] 2024-03-29 16:58:22.141 - [employees_import_import_import_import_import_import_import_import_import][Territories] - Node Territories[d80f826f-5d4d-44d8-b85b-ecdb92cff606] schema data cleaned 
[INFO ] 2024-03-29 16:58:22.141 - [employees_import_import_import_import_import_import_import_import_import][Territories] - Node Territories[d80f826f-5d4d-44d8-b85b-ecdb92cff606] monitor closed 
[INFO ] 2024-03-29 16:58:22.142 - [employees_import_import_import_import_import_import_import_import_import][Territories] - Node Territories[d80f826f-5d4d-44d8-b85b-ecdb92cff606] close complete, cost 12 ms 
[INFO ] 2024-03-29 16:58:22.230 - [employees_import_import_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[001c6302-7e40-40b3-8b9d-8634b43e108e] running status set to false 
[INFO ] 2024-03-29 16:58:22.231 - [employees_import_import_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[001c6302-7e40-40b3-8b9d-8634b43e108e] schema data cleaned 
[INFO ] 2024-03-29 16:58:22.231 - [employees_import_import_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[001c6302-7e40-40b3-8b9d-8634b43e108e] monitor closed 
[INFO ] 2024-03-29 16:58:22.231 - [employees_import_import_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[001c6302-7e40-40b3-8b9d-8634b43e108e] close complete, cost 88 ms 
[INFO ] 2024-03-29 16:58:22.231 - [employees_import_import_import_import_import_import_import_import_import][Region] - Node Region[18eb6013-71c8-4aa4-ac91-c909d4e54fca] running status set to false 
[INFO ] 2024-03-29 16:58:22.232 - [employees_import_import_import_import_import_import_import_import_import][Region] - Initial sync started 
[INFO ] 2024-03-29 16:58:22.232 - [employees_import_import_import_import_import_import_import_import_import][Region] - Initial sync completed 
[INFO ] 2024-03-29 16:58:22.233 - [employees_import_import_import_import_import_import_import_import_import][Region] - Incremental sync starting... 
[INFO ] 2024-03-29 16:58:22.233 - [employees_import_import_import_import_import_import_import_import_import][Region] - Incremental sync completed 
[INFO ] 2024-03-29 16:58:22.239 - [employees_import_import_import_import_import_import_import_import_import][Region] - PDK connector node stopped: HazelcastSourcePdkDataNode-18eb6013-71c8-4aa4-ac91-c909d4e54fca 
[INFO ] 2024-03-29 16:58:22.239 - [employees_import_import_import_import_import_import_import_import_import][Region] - PDK connector node released: HazelcastSourcePdkDataNode-18eb6013-71c8-4aa4-ac91-c909d4e54fca 
[INFO ] 2024-03-29 16:58:22.239 - [employees_import_import_import_import_import_import_import_import_import][Region] - Node Region[18eb6013-71c8-4aa4-ac91-c909d4e54fca] schema data cleaned 
[INFO ] 2024-03-29 16:58:22.239 - [employees_import_import_import_import_import_import_import_import_import][Region] - Node Region[18eb6013-71c8-4aa4-ac91-c909d4e54fca] monitor closed 
[INFO ] 2024-03-29 16:58:22.240 - [employees_import_import_import_import_import_import_import_import_import][Region] - Node Region[18eb6013-71c8-4aa4-ac91-c909d4e54fca] close complete, cost 8 ms 
[INFO ] 2024-03-29 16:58:22.284 - [employees_import_import_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[a14d7f67-027f-4796-9f47-7611cabe00bb] running status set to false 
[INFO ] 2024-03-29 16:58:22.284 - [employees_import_import_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[a14d7f67-027f-4796-9f47-7611cabe00bb] schema data cleaned 
[INFO ] 2024-03-29 16:58:22.284 - [employees_import_import_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[a14d7f67-027f-4796-9f47-7611cabe00bb] monitor closed 
[INFO ] 2024-03-29 16:58:22.285 - [employees_import_import_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[a14d7f67-027f-4796-9f47-7611cabe00bb] close complete, cost 45 ms 
[INFO ] 2024-03-29 16:58:22.285 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[0dd88320-55aa-4e78-a8e0-df90a59b2578] running status set to false 
[INFO ] 2024-03-29 16:58:22.286 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Incremental sync starting... 
[INFO ] 2024-03-29 16:58:22.286 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Incremental sync completed 
[INFO ] 2024-03-29 16:58:22.293 - [employees_import_import_import_import_import_import_import_import_import][Employees] - PDK connector node stopped: HazelcastSourcePdkDataNode-0dd88320-55aa-4e78-a8e0-df90a59b2578 
[INFO ] 2024-03-29 16:58:22.293 - [employees_import_import_import_import_import_import_import_import_import][Employees] - PDK connector node released: HazelcastSourcePdkDataNode-0dd88320-55aa-4e78-a8e0-df90a59b2578 
[INFO ] 2024-03-29 16:58:22.293 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[0dd88320-55aa-4e78-a8e0-df90a59b2578] schema data cleaned 
[INFO ] 2024-03-29 16:58:22.293 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[0dd88320-55aa-4e78-a8e0-df90a59b2578] monitor closed 
[INFO ] 2024-03-29 16:58:22.294 - [employees_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[0dd88320-55aa-4e78-a8e0-df90a59b2578] close complete, cost 8 ms 
[INFO ] 2024-03-29 16:58:22.294 - [employees_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[2c7147ac-41f4-4c68-8de7-26fb77babf3f] running status set to false 
[INFO ] 2024-03-29 16:58:22.335 - [employees_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[2c7147ac-41f4-4c68-8de7-26fb77babf3f] schema data cleaned 
[INFO ] 2024-03-29 16:58:22.335 - [employees_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[2c7147ac-41f4-4c68-8de7-26fb77babf3f] monitor closed 
[INFO ] 2024-03-29 16:58:22.336 - [employees_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[2c7147ac-41f4-4c68-8de7-26fb77babf3f] close complete, cost 42 ms 
[INFO ] 2024-03-29 16:58:22.336 - [employees_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[58697add-3efa-414b-a969-cbd4acd5fdc4] running status set to false 
[INFO ] 2024-03-29 16:58:22.337 - [employees_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Incremental sync starting... 
[INFO ] 2024-03-29 16:58:22.337 - [employees_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Incremental sync completed 
[INFO ] 2024-03-29 16:58:22.344 - [employees_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - PDK connector node stopped: HazelcastSourcePdkDataNode-58697add-3efa-414b-a969-cbd4acd5fdc4 
[INFO ] 2024-03-29 16:58:22.344 - [employees_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - PDK connector node released: HazelcastSourcePdkDataNode-58697add-3efa-414b-a969-cbd4acd5fdc4 
[INFO ] 2024-03-29 16:58:22.344 - [employees_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[58697add-3efa-414b-a969-cbd4acd5fdc4] schema data cleaned 
[INFO ] 2024-03-29 16:58:22.344 - [employees_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[58697add-3efa-414b-a969-cbd4acd5fdc4] monitor closed 
[INFO ] 2024-03-29 16:58:22.344 - [employees_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[58697add-3efa-414b-a969-cbd4acd5fdc4] close complete, cost 7 ms 
[INFO ] 2024-03-29 16:58:22.344 - [employees_import_import_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[195a8b60-f852-42cf-987e-dea71f712fba] running status set to false 
[INFO ] 2024-03-29 16:58:22.381 - [employees_import_import_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[195a8b60-f852-42cf-987e-dea71f712fba] schema data cleaned 
[INFO ] 2024-03-29 16:58:22.381 - [employees_import_import_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[195a8b60-f852-42cf-987e-dea71f712fba] monitor closed 
[INFO ] 2024-03-29 16:58:22.382 - [employees_import_import_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[195a8b60-f852-42cf-987e-dea71f712fba] close complete, cost 37 ms 
[INFO ] 2024-03-29 16:58:22.382 - [employees_import_import_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[6d650da8-4f9b-4138-9552-29b43309de9c] running status set to false 
[INFO ] 2024-03-29 16:58:22.420 - [employees_import_import_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[6d650da8-4f9b-4138-9552-29b43309de9c] schema data cleaned 
[INFO ] 2024-03-29 16:58:22.420 - [employees_import_import_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[6d650da8-4f9b-4138-9552-29b43309de9c] monitor closed 
[INFO ] 2024-03-29 16:58:22.420 - [employees_import_import_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[6d650da8-4f9b-4138-9552-29b43309de9c] close complete, cost 38 ms 
[INFO ] 2024-03-29 16:58:22.420 - [employees_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[7b450f38-0b92-45f2-940e-ffdd1c5bb40c] running status set to false 
[INFO ] 2024-03-29 16:58:22.460 - [employees_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[7b450f38-0b92-45f2-940e-ffdd1c5bb40c] schema data cleaned 
[INFO ] 2024-03-29 16:58:22.460 - [employees_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[7b450f38-0b92-45f2-940e-ffdd1c5bb40c] monitor closed 
[INFO ] 2024-03-29 16:58:22.461 - [employees_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[7b450f38-0b92-45f2-940e-ffdd1c5bb40c] close complete, cost 40 ms 
[INFO ] 2024-03-29 16:58:22.461 - [employees_import_import_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[de078065-5e92-40c6-981b-edf4b4be8369] running status set to false 
[INFO ] 2024-03-29 16:58:22.501 - [employees_import_import_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[de078065-5e92-40c6-981b-edf4b4be8369] schema data cleaned 
[INFO ] 2024-03-29 16:58:22.501 - [employees_import_import_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[de078065-5e92-40c6-981b-edf4b4be8369] monitor closed 
[INFO ] 2024-03-29 16:58:22.501 - [employees_import_import_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[de078065-5e92-40c6-981b-edf4b4be8369] close complete, cost 40 ms 
[INFO ] 2024-03-29 16:58:22.502 - [employees_import_import_import_import_import_import_import_import_import][merge] - Node merge[9b3040aa-0938-4bac-b7cd-1f3c40624d8c] running status set to false 
[INFO ] 2024-03-29 16:58:22.502 - [employees_import_import_import_import_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Employees_2c7147ac-41f4-4c68-8de7-26fb77babf3f__TPORIG 
[INFO ] 2024-03-29 16:58:22.502 - [employees_import_import_import_import_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Region_de078065-5e92-40c6-981b-edf4b4be8369__TPORIG 
[INFO ] 2024-03-29 16:58:22.506 - [employees_import_import_import_import_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Territories_6d650da8-4f9b-4138-9552-29b43309de9c__TPORIG 
[INFO ] 2024-03-29 16:58:22.506 - [employees_import_import_import_import_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_EmployeeTerritories_195a8b60-f852-42cf-987e-dea71f712fba__TPORIG 
[INFO ] 2024-03-29 16:58:22.506 - [employees_import_import_import_import_import_import_import_import_import][merge] - Node merge[9b3040aa-0938-4bac-b7cd-1f3c40624d8c] schema data cleaned 
[INFO ] 2024-03-29 16:58:22.506 - [employees_import_import_import_import_import_import_import_import_import][merge] - Node merge[9b3040aa-0938-4bac-b7cd-1f3c40624d8c] monitor closed 
[INFO ] 2024-03-29 16:58:22.506 - [employees_import_import_import_import_import_import_import_import_import][merge] - Node merge[9b3040aa-0938-4bac-b7cd-1f3c40624d8c] close complete, cost 4 ms 
[INFO ] 2024-03-29 16:58:22.506 - [employees_import_import_import_import_import_import_import_import_import][employees] - Node employees[5269b34a-9767-4508-8269-718fd4aeaa68] running status set to false 
[INFO ] 2024-03-29 16:58:22.514 - [employees_import_import_import_import_import_import_import_import_import][employees] - PDK connector node stopped: HazelcastTargetPdkDataNode-5269b34a-9767-4508-8269-718fd4aeaa68 
[INFO ] 2024-03-29 16:58:22.514 - [employees_import_import_import_import_import_import_import_import_import][employees] - PDK connector node released: HazelcastTargetPdkDataNode-5269b34a-9767-4508-8269-718fd4aeaa68 
[INFO ] 2024-03-29 16:58:22.515 - [employees_import_import_import_import_import_import_import_import_import][employees] - Node employees[5269b34a-9767-4508-8269-718fd4aeaa68] schema data cleaned 
[INFO ] 2024-03-29 16:58:22.515 - [employees_import_import_import_import_import_import_import_import_import][employees] - Node employees[5269b34a-9767-4508-8269-718fd4aeaa68] monitor closed 
[INFO ] 2024-03-29 16:58:22.716 - [employees_import_import_import_import_import_import_import_import_import][employees] - Node employees[5269b34a-9767-4508-8269-718fd4aeaa68] close complete, cost 8 ms 
[INFO ] 2024-03-29 16:58:24.239 - [employees_import_import_import_import_import_import_import_import_import] - Task [employees_import_import_import_import_import_import_import_import_import] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-03-29 16:58:24.239 - [employees_import_import_import_import_import_import_import_import_import] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-29 16:58:24.270 - [employees_import_import_import_import_import_import_import_import_import] - Stop task milestones: 66067fd018990e56b527191f(employees_import_import_import_import_import_import_import_import_import)  
[INFO ] 2024-03-29 16:58:24.273 - [employees_import_import_import_import_import_import_import_import_import] - Stopped task aspect(s) 
[INFO ] 2024-03-29 16:58:24.273 - [employees_import_import_import_import_import_import_import_import_import] - Snapshot order controller have been removed 
[INFO ] 2024-03-29 16:58:24.291 - [employees_import_import_import_import_import_import_import_import_import] - Remove memory task client succeed, task: employees_import_import_import_import_import_import_import_import_import[66067fd018990e56b527191f] 
[INFO ] 2024-03-29 16:58:24.291 - [employees_import_import_import_import_import_import_import_import_import] - Destroy memory task client cache succeed, task: employees_import_import_import_import_import_import_import_import_import[66067fd018990e56b527191f] 
