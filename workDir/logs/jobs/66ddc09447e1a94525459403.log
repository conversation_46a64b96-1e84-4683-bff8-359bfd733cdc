[INFO ] 2024-09-20 13:44:09.225 - [测试主从-mysql] - Start task milestones: 66ddc09447e1a94525459403(测试主从-mysql) 
[INFO ] 2024-09-20 13:44:09.244 - [测试主从-mysql] - Task initialization... 
[INFO ] 2024-09-20 13:44:09.644 - [测试主从-mysql] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-09-20 13:44:09.776 - [测试主从-mysql] - The engine receives 测试主从-mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-09-20 13:44:09.776 - [测试主从-mysql][Mysql3306] - Node Mysql3306[ec972fde-447a-45be-ab60-7cd1fff816ad] start preload schema,table counts: 1 
[INFO ] 2024-09-20 13:44:09.776 - [测试主从-mysql][Mysql] - Node Mysql[786b9abd-e678-4ef7-b249-a0c63bb7bf64] start preload schema,table counts: 1 
[INFO ] 2024-09-20 13:44:09.777 - [测试主从-mysql][Mysql3306] - Node Mysql3306[ec972fde-447a-45be-ab60-7cd1fff816ad] preload schema finished, cost 0 ms 
[INFO ] 2024-09-20 13:44:20.100 - [测试主从-mysql][Mysql] - Node Mysql[786b9abd-e678-4ef7-b249-a0c63bb7bf64] preload schema finished, cost 0 ms 
[INFO ] 2024-09-20 13:44:27.338 - [测试主从-mysql][Mysql3306] - Node(Mysql3306) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-09-20 13:44:27.339 - [测试主从-mysql][Mysql3306] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-09-20 13:44:27.708 - [测试主从-mysql][Mysql] - Source node "Mysql" read batch size: 100 
[INFO ] 2024-09-20 13:44:27.708 - [测试主从-mysql][Mysql] - Source node "Mysql" event queue capacity: 200 
[INFO ] 2024-09-20 13:44:27.708 - [测试主从-mysql][Mysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-09-20 13:44:27.808 - [测试主从-mysql][Mysql] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000109","position":16611061,"gtidSet":""} 
[INFO ] 2024-09-20 13:44:27.809 - [测试主从-mysql][Mysql] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-09-20 13:44:27.901 - [测试主从-mysql][Mysql] - Initial sync started 
[INFO ] 2024-09-20 13:44:27.928 - [测试主从-mysql][Mysql] - Starting batch read, table name: t1, offset: null 
[INFO ] 2024-09-20 13:44:27.928 - [测试主从-mysql][Mysql] - Table t1 is going to be initial synced 
[WARN ] 2024-09-20 13:44:28.130 - [测试主从-mysql][Mysql] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'dt' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-20 13:44:28.440 - [测试主从-mysql][Mysql] - Query table 't1' counts: 3507635 
[WARN ] 2024-09-20 13:46:32.398 - [测试主从-mysql][Mysql] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'dt' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:47:32.449 - [测试主从-mysql][Mysql] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'dt' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:48:49.239 - [测试主从-mysql][Mysql] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'dt' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:49:49.255 - [测试主从-mysql][Mysql] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'dt' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:50:49.451 - [测试主从-mysql][Mysql] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'dt' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:51:49.571 - [测试主从-mysql][Mysql] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'dt' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:52:49.706 - [测试主从-mysql][Mysql] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'dt' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 8 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:53:49.993 - [测试主从-mysql][Mysql] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'dt' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 7 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:54:49.945 - [测试主从-mysql][Mysql] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'dt' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 6 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:55:50.282 - [测试主从-mysql][Mysql] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'dt' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 5 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:56:50.253 - [测试主从-mysql][Mysql] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'dt' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 4 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:57:50.556 - [测试主从-mysql][Mysql] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'dt' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 3 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:58:55.716 - [测试主从-mysql][Mysql] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'dt' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 2 time(s)
 - Period 60 second(s) 
[WARN ] 2024-09-20 13:59:55.855 - [测试主从-mysql][Mysql] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'dt' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...
 - Remaining retry 1 time(s)
 - Period 60 second(s) 
[INFO ] 2024-09-20 14:00:56.030 - [测试主从-mysql][Mysql] - Initial sync completed 
[INFO ] 2024-09-20 14:00:56.046 - [测试主从-mysql][Mysql] - Exception skipping - The current exception does not match the skip exception strategy, message: java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t1`, code: 42S22(1054), error: Unknown column 'dt' in 'field list' 
[ERROR] 2024-09-20 14:00:56.057 - [测试主从-mysql][Mysql] - java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t1`, code: 42S22(1054), error: Unknown column 'dt' in 'field list' <-- Error Message -->
java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t1`, code: 42S22(1054), error: Unknown column 'dt' in 'field list'

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'dt' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t1`, code: 42S22(1054), error: Unknown column 'dt' in 'field list'
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:440)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:256)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:147)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:257)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:178)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t1`, code: 42S22(1054), error: Unknown column 'dt' in 'field list'
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:340)
	... 10 more
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t1`, code: 42S22(1054), error: Unknown column 'dt' in 'field list'
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:69)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshot$12(HazelcastSourcePdkDataNode.java:348)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 12 more
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `id`, `num`, `k1`, `k2`, `dt` FROM `test`.`t1`, code: 42S22(1054), error: Unknown column 'dt' in 'field list'
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:213)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:610)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:424)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 20 more
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'dt' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1200)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:206)
	... 24 more

[INFO ] 2024-09-20 14:00:56.057 - [测试主从-mysql][Mysql] - Job suspend in error handle 
[INFO ] 2024-09-20 14:00:56.601 - [测试主从-mysql][Mysql] - Node Mysql[786b9abd-e678-4ef7-b249-a0c63bb7bf64] running status set to false 
[INFO ] 2024-09-20 14:00:56.601 - [测试主从-mysql][Mysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-786b9abd-e678-4ef7-b249-a0c63bb7bf64 
[INFO ] 2024-09-20 14:00:56.601 - [测试主从-mysql][Mysql] - PDK connector node released: HazelcastSourcePdkDataNode-786b9abd-e678-4ef7-b249-a0c63bb7bf64 
[INFO ] 2024-09-20 14:00:56.601 - [测试主从-mysql][Mysql] - Node Mysql[786b9abd-e678-4ef7-b249-a0c63bb7bf64] schema data cleaned 
[INFO ] 2024-09-20 14:00:56.601 - [测试主从-mysql][Mysql] - Node Mysql[786b9abd-e678-4ef7-b249-a0c63bb7bf64] monitor closed 
[INFO ] 2024-09-20 14:00:56.604 - [测试主从-mysql][Mysql] - Node Mysql[786b9abd-e678-4ef7-b249-a0c63bb7bf64] close complete, cost 66 ms 
[INFO ] 2024-09-20 14:00:56.619 - [测试主从-mysql][Mysql3306] - Node Mysql3306[ec972fde-447a-45be-ab60-7cd1fff816ad] running status set to false 
[INFO ] 2024-09-20 14:00:56.619 - [测试主从-mysql][Mysql3306] - PDK connector node stopped: HazelcastTargetPdkDataNode-ec972fde-447a-45be-ab60-7cd1fff816ad 
[INFO ] 2024-09-20 14:00:56.619 - [测试主从-mysql][Mysql3306] - PDK connector node released: HazelcastTargetPdkDataNode-ec972fde-447a-45be-ab60-7cd1fff816ad 
[INFO ] 2024-09-20 14:00:56.619 - [测试主从-mysql][Mysql3306] - Node Mysql3306[ec972fde-447a-45be-ab60-7cd1fff816ad] schema data cleaned 
[INFO ] 2024-09-20 14:00:56.620 - [测试主从-mysql][Mysql3306] - Node Mysql3306[ec972fde-447a-45be-ab60-7cd1fff816ad] monitor closed 
[INFO ] 2024-09-20 14:00:56.824 - [测试主从-mysql][Mysql3306] - Node Mysql3306[ec972fde-447a-45be-ab60-7cd1fff816ad] close complete, cost 15 ms 
[INFO ] 2024-09-20 14:00:57.997 - [测试主从-mysql] - Task [测试主从-mysql] cannot retry, reason: Sync progress is empty 
[INFO ] 2024-09-20 14:00:58.004 - [测试主从-mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-09-20 14:00:58.004 - [测试主从-mysql] - Stop task milestones: 66ddc09447e1a94525459403(测试主从-mysql)  
[INFO ] 2024-09-20 14:00:58.024 - [测试主从-mysql] - Stopped task aspect(s) 
[INFO ] 2024-09-20 14:00:58.024 - [测试主从-mysql] - Snapshot order controller have been removed 
[INFO ] 2024-09-20 14:00:58.038 - [测试主从-mysql] - Remove memory task client succeed, task: 测试主从-mysql[66ddc09447e1a94525459403] 
[INFO ] 2024-09-20 14:00:58.039 - [测试主从-mysql] - Destroy memory task client cache succeed, task: 测试主从-mysql[66ddc09447e1a94525459403] 
