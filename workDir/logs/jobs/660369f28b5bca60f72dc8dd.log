[INFO ] 2024-03-27 08:38:12.801 - [任务 13] - Start task milestones: 660369f28b5bca60f72dc8dd(任务 13) 
[INFO ] 2024-03-27 08:38:12.802 - [任务 13] - Task initialization... 
[INFO ] 2024-03-27 08:38:12.803 - [任务 13] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 08:38:12.803 - [任务 13] - The engine receives 任务 13 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 08:38:12.803 - [任务 13][CLAIM] - Node CLAIM[bc0fda17-24a0-4b05-b76e-b464725c9d13] start preload schema,table counts: 1 
[INFO ] 2024-03-27 08:38:12.804 - [任务 13][test1] - Node test1[ea45930b-418a-42ff-a99b-08c927ea17<PERSON>] start preload schema,table counts: 1 
[INFO ] 2024-03-27 08:38:12.846 - [任务 13][test1] - Node test1[ea45930b-418a-42ff-a99b-08c927ea17fa] preload schema finished, cost 48 ms 
[INFO ] 2024-03-27 08:38:12.847 - [任务 13][CLAIM] - Node CLAIM[bc0fda17-24a0-4b05-b76e-b464725c9d13] preload schema finished, cost 54 ms 
[INFO ] 2024-03-27 08:38:15.432 - [任务 13][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 08:38:15.436 - [任务 13][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 08:38:15.439 - [任务 13][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-27 08:38:15.442 - [任务 13][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":144627947,"gtidSet":""} 
[INFO ] 2024-03-27 08:38:15.525 - [任务 13][CLAIM] - Initial sync started 
[INFO ] 2024-03-27 08:38:15.530 - [任务 13][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-27 08:38:15.537 - [任务 13][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-27 08:38:15.609 - [任务 13][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-27 08:38:20.617 - [任务 13][test1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 08:38:21.183 - [任务 13][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 08:38:21.187 - [任务 13][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-27 08:38:21.188 - [任务 13][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 08:38:21.194 - [任务 13][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":144627947,"gtidSet":""} 
[INFO ] 2024-03-27 08:38:21.298 - [任务 13][CLAIM] - Starting mysql cdc, server name: 458b51db-681e-43e7-a5f8-a01449a83337 
[INFO ] 2024-03-27 08:38:21.344 - [任务 13][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1414164077
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 458b51db-681e-43e7-a5f8-a01449a83337
  database.port: 3306
  threadName: Debezium-Mysql-Connector-458b51db-681e-43e7-a5f8-a01449a83337
  database.hostname: 127.0.0.1
  database.password: ********
  name: 458b51db-681e-43e7-a5f8-a01449a83337
  pdk.offset.string: {"name":"458b51db-681e-43e7-a5f8-a01449a83337","offset":{"{\"server\":\"458b51db-681e-43e7-a5f8-a01449a83337\"}":"{\"file\":\"binlog.000020\",\"pos\":144627947,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 08:38:21.965 - [任务 13][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 08:39:09.999 - [任务 13][CLAIM] - Read DDL: alter table CLAIM drop column `name2`, about to be packaged as some event(s) 
[INFO ] 2024-03-27 08:39:09.999 - [任务 13][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapDropFieldEvent
  - Offset: MysqlStreamOffset{name='458b51db-681e-43e7-a5f8-a01449a83337', offset={{"server":"458b51db-681e-43e7-a5f8-a01449a83337"}={"ts_sec":1711499949,"file":"binlog.000020","pos":144628194,"server_id":1}}} 
[INFO ] 2024-03-27 08:39:09.999 - [任务 13][CLAIM] - Source node received an ddl event: TapDropFieldEvent{tableId='CLAIM', fieldName='name2'} 
[INFO ] 2024-03-27 08:39:10.077 - [任务 13][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_660369f28b5bca60f72dc8dd 
[INFO ] 2024-03-27 08:39:10.399 - [任务 13][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 08:42:49.185 - [任务 13][CLAIM] - Read DDL: alter table CLAIM RENAME COLUMN `name3` to `name1`, about to be packaged as some event(s) 
[INFO ] 2024-03-27 08:42:49.187 - [任务 13][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldNameEvent
  - Offset: MysqlStreamOffset{name='458b51db-681e-43e7-a5f8-a01449a83337', offset={{"server":"458b51db-681e-43e7-a5f8-a01449a83337"}={"ts_sec":1711500108,"file":"binlog.000020","pos":144628456,"server_id":1}}} 
[INFO ] 2024-03-27 08:42:49.187 - [任务 13][CLAIM] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='CLAIM', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@457917d3} 
[INFO ] 2024-03-27 08:42:49.187 - [任务 13][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_660369f28b5bca60f72dc8dd 
[INFO ] 2024-03-27 08:42:49.187 - [任务 13][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 08:46:34.385 - [任务 13][CLAIM] - Read DDL: alter table CLAIM add column age int, about to be packaged as some event(s) 
[INFO ] 2024-03-27 08:46:34.389 - [任务 13][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='458b51db-681e-43e7-a5f8-a01449a83337', offset={{"server":"458b51db-681e-43e7-a5f8-a01449a83337"}={"ts_sec":1711500333,"file":"binlog.000020","pos":144628702,"server_id":1}}} 
[INFO ] 2024-03-27 08:46:34.390 - [任务 13][CLAIM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@6707c6c1: {"newFields":[{"autoInc":false,"dataType":"int","name":"age","nullable":true,"partitionKey":false,"pos":11,"primaryKey":false}],"referenceTime":1711500333667,"tableId":"CLAIM","time":1711500333953,"type":209} 
[INFO ] 2024-03-27 08:46:34.390 - [任务 13][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_660369f28b5bca60f72dc8dd 
[INFO ] 2024-03-27 08:46:34.391 - [任务 13][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 08:49:09.258 - [任务 13][CLAIM] - Read DDL: alter table CLAIM modify column `name1` varchar(70) default 'a', about to be packaged as some event(s) 
[INFO ] 2024-03-27 08:49:09.265 - [任务 13][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='458b51db-681e-43e7-a5f8-a01449a83337', offset={{"server":"458b51db-681e-43e7-a5f8-a01449a83337"}={"ts_sec":1711500488,"file":"binlog.000020","pos":144628977,"server_id":1}}} 
[INFO ] 2024-03-27 08:49:09.266 - [任务 13][CLAIM] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='CLAIM', fieldName='name1', dataTypeChange=io.tapdata.entity.event.ddl.entity.ValueChange@72c13053, checkChange=null, constraintChange=null, nullableChange=null, commentChange=null, defaultChange=io.tapdata.entity.event.ddl.entity.ValueChange@602139ef, primaryChange=null} 
[INFO ] 2024-03-27 08:49:09.266 - [任务 13][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_660369f28b5bca60f72dc8dd 
[INFO ] 2024-03-27 08:49:09.267 - [任务 13][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 08:51:38.214 - [任务 13][CLAIM] - Read DDL: alter table CLAIM modify column `name1` varchar(70) default 'a' null, about to be packaged as some event(s) 
[INFO ] 2024-03-27 08:51:38.216 - [任务 13][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='458b51db-681e-43e7-a5f8-a01449a83337', offset={{"server":"458b51db-681e-43e7-a5f8-a01449a83337"}={"ts_sec":1711500637,"file":"binlog.000020","pos":144629257,"server_id":1}}} 
[INFO ] 2024-03-27 08:51:38.217 - [任务 13][CLAIM] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='CLAIM', fieldName='name1', dataTypeChange=io.tapdata.entity.event.ddl.entity.ValueChange@388c9c26, checkChange=null, constraintChange=null, nullableChange=io.tapdata.entity.event.ddl.entity.ValueChange@341c86e7, commentChange=null, defaultChange=io.tapdata.entity.event.ddl.entity.ValueChange@76b51ba6, primaryChange=null} 
[INFO ] 2024-03-27 08:51:38.217 - [任务 13][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_660369f28b5bca60f72dc8dd 
[INFO ] 2024-03-27 08:51:38.217 - [任务 13][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 08:52:41.818 - [任务 13][CLAIM] - Read DDL: alter table CLAIM modify column `name1` varchar(70) default 'a' null unique, about to be packaged as some event(s) 
[INFO ] 2024-03-27 08:52:41.819 - [任务 13][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='458b51db-681e-43e7-a5f8-a01449a83337', offset={{"server":"458b51db-681e-43e7-a5f8-a01449a83337"}={"ts_sec":1711500754,"file":"binlog.000020","pos":144629544,"server_id":1}}} 
[INFO ] 2024-03-27 08:52:41.819 - [任务 13][CLAIM] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='CLAIM', fieldName='name1', dataTypeChange=io.tapdata.entity.event.ddl.entity.ValueChange@3fda4d14, checkChange=null, constraintChange=null, nullableChange=io.tapdata.entity.event.ddl.entity.ValueChange@273c3c78, commentChange=null, defaultChange=io.tapdata.entity.event.ddl.entity.ValueChange@6aa291f0, primaryChange=null} 
[INFO ] 2024-03-27 08:52:41.820 - [任务 13][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_660369f28b5bca60f72dc8dd 
[INFO ] 2024-03-27 08:52:41.820 - [任务 13][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 08:57:18.670 - [任务 13][CLAIM] - Read DDL: alter table CLAIM modify column `name1` varchar(70) default 'a' null check ( `name1`<>'b' ), about to be packaged as some event(s) 
[INFO ] 2024-03-27 08:57:18.672 - [任务 13][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='458b51db-681e-43e7-a5f8-a01449a83337', offset={{"server":"458b51db-681e-43e7-a5f8-a01449a83337"}={"ts_sec":1711500883,"file":"binlog.000020","pos":144629847,"server_id":1}}} 
[INFO ] 2024-03-27 08:57:18.672 - [任务 13][CLAIM] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='CLAIM', fieldName='name1', dataTypeChange=io.tapdata.entity.event.ddl.entity.ValueChange@e8e022b, checkChange=null, constraintChange=null, nullableChange=io.tapdata.entity.event.ddl.entity.ValueChange@53da7b9, commentChange=null, defaultChange=io.tapdata.entity.event.ddl.entity.ValueChange@4f4d9199, primaryChange=null} 
[INFO ] 2024-03-27 08:57:18.673 - [任务 13][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_660369f28b5bca60f72dc8dd 
[INFO ] 2024-03-27 08:57:18.673 - [任务 13][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 09:00:10.363 - [任务 13][CLAIM] - Read DDL: alter table CLAIM add column id int, about to be packaged as some event(s) 
[INFO ] 2024-03-27 09:00:10.365 - [任务 13][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='458b51db-681e-43e7-a5f8-a01449a83337', offset={{"server":"458b51db-681e-43e7-a5f8-a01449a83337"}={"ts_sec":1711501149,"file":"binlog.000020","pos":144630362,"server_id":1}}} 
[INFO ] 2024-03-27 09:00:10.365 - [任务 13][CLAIM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@7360f824: {"newFields":[{"autoInc":false,"dataType":"int","name":"id","nullable":true,"partitionKey":false,"pos":12,"primaryKey":false}],"referenceTime":1711501149598,"tableId":"CLAIM","time":1711501149947,"type":209} 
[INFO ] 2024-03-27 09:00:10.365 - [任务 13][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_660369f28b5bca60f72dc8dd 
[INFO ] 2024-03-27 09:00:10.366 - [任务 13][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 09:02:39.639 - [任务 13][CLAIM] - Mysql binlog reader stopped 
[WARN ] 2024-03-27 09:02:39.642 - [任务 13][CLAIM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: java.lang.RuntimeException: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>

 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-03-27 09:02:39.648 - [任务 13][CLAIM] - Starting mysql cdc, server name: 458b51db-681e-43e7-a5f8-a01449a83337 
[INFO ] 2024-03-27 09:02:39.653 - [任务 13][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1066179852
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 458b51db-681e-43e7-a5f8-a01449a83337
  database.port: 3306
  threadName: Debezium-Mysql-Connector-458b51db-681e-43e7-a5f8-a01449a83337
  database.hostname: 127.0.0.1
  database.password: ********
  name: 458b51db-681e-43e7-a5f8-a01449a83337
  pdk.offset.string: {"name":"458b51db-681e-43e7-a5f8-a01449a83337","offset":{"{\"server\":\"458b51db-681e-43e7-a5f8-a01449a83337\"}":"{\"ts_sec\":1711501149,\"file\":\"binlog.000020\",\"pos\":144630362,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 09:02:39.751 - [任务 13][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 09:02:40.354 - [任务 13][CLAIM] - Mysql binlog reader stopped 
[WARN ] 2024-03-27 09:02:40.357 - [任务 13][CLAIM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: java.lang.RuntimeException: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>

 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-03-27 09:03:40.422 - [任务 13][CLAIM] - Starting mysql cdc, server name: 458b51db-681e-43e7-a5f8-a01449a83337 
[INFO ] 2024-03-27 09:03:40.428 - [任务 13][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1673535754
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 458b51db-681e-43e7-a5f8-a01449a83337
  database.port: 3306
  threadName: Debezium-Mysql-Connector-458b51db-681e-43e7-a5f8-a01449a83337
  database.hostname: 127.0.0.1
  database.password: ********
  name: 458b51db-681e-43e7-a5f8-a01449a83337
  pdk.offset.string: {"name":"458b51db-681e-43e7-a5f8-a01449a83337","offset":{"{\"server\":\"458b51db-681e-43e7-a5f8-a01449a83337\"}":"{\"ts_sec\":1711501149,\"file\":\"binlog.000020\",\"pos\":144630362,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 09:03:40.513 - [任务 13][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 09:03:41.078 - [任务 13][CLAIM] - Mysql binlog reader stopped 
[WARN ] 2024-03-27 09:03:41.081 - [任务 13][CLAIM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: java.lang.RuntimeException: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>

 - Remaining retry 13 time(s)
 - Period 60 second(s) 
[INFO ] 2024-03-27 09:04:41.170 - [任务 13][CLAIM] - Starting mysql cdc, server name: 458b51db-681e-43e7-a5f8-a01449a83337 
[INFO ] 2024-03-27 09:04:41.174 - [任务 13][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1465365511
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 458b51db-681e-43e7-a5f8-a01449a83337
  database.port: 3306
  threadName: Debezium-Mysql-Connector-458b51db-681e-43e7-a5f8-a01449a83337
  database.hostname: 127.0.0.1
  database.password: ********
  name: 458b51db-681e-43e7-a5f8-a01449a83337
  pdk.offset.string: {"name":"458b51db-681e-43e7-a5f8-a01449a83337","offset":{"{\"server\":\"458b51db-681e-43e7-a5f8-a01449a83337\"}":"{\"ts_sec\":1711501149,\"file\":\"binlog.000020\",\"pos\":144630362,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 09:04:41.245 - [任务 13][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 09:04:41.820 - [任务 13][CLAIM] - Mysql binlog reader stopped 
[WARN ] 2024-03-27 09:04:41.821 - [任务 13][CLAIM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: java.lang.RuntimeException: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>

 - Remaining retry 12 time(s)
 - Period 60 second(s) 
[INFO ] 2024-03-27 09:05:41.871 - [任务 13][CLAIM] - Starting mysql cdc, server name: 458b51db-681e-43e7-a5f8-a01449a83337 
[INFO ] 2024-03-27 09:05:41.876 - [任务 13][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1358008144
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 458b51db-681e-43e7-a5f8-a01449a83337
  database.port: 3306
  threadName: Debezium-Mysql-Connector-458b51db-681e-43e7-a5f8-a01449a83337
  database.hostname: 127.0.0.1
  database.password: ********
  name: 458b51db-681e-43e7-a5f8-a01449a83337
  pdk.offset.string: {"name":"458b51db-681e-43e7-a5f8-a01449a83337","offset":{"{\"server\":\"458b51db-681e-43e7-a5f8-a01449a83337\"}":"{\"ts_sec\":1711501149,\"file\":\"binlog.000020\",\"pos\":144630362,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 09:05:41.935 - [任务 13][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 09:05:42.476 - [任务 13][CLAIM] - Mysql binlog reader stopped 
[WARN ] 2024-03-27 09:05:42.477 - [任务 13][CLAIM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: java.lang.RuntimeException: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>

 - Remaining retry 11 time(s)
 - Period 60 second(s) 
[INFO ] 2024-03-27 09:06:42.542 - [任务 13][CLAIM] - Starting mysql cdc, server name: 458b51db-681e-43e7-a5f8-a01449a83337 
[INFO ] 2024-03-27 09:06:42.549 - [任务 13][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1239876221
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 458b51db-681e-43e7-a5f8-a01449a83337
  database.port: 3306
  threadName: Debezium-Mysql-Connector-458b51db-681e-43e7-a5f8-a01449a83337
  database.hostname: 127.0.0.1
  database.password: ********
  name: 458b51db-681e-43e7-a5f8-a01449a83337
  pdk.offset.string: {"name":"458b51db-681e-43e7-a5f8-a01449a83337","offset":{"{\"server\":\"458b51db-681e-43e7-a5f8-a01449a83337\"}":"{\"ts_sec\":1711501149,\"file\":\"binlog.000020\",\"pos\":144630362,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 09:06:42.611 - [任务 13][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 09:06:43.182 - [任务 13][CLAIM] - Mysql binlog reader stopped 
[WARN ] 2024-03-27 09:06:43.184 - [任务 13][CLAIM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: java.lang.RuntimeException: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>

 - Remaining retry 10 time(s)
 - Period 60 second(s) 
[INFO ] 2024-03-27 09:07:43.243 - [任务 13][CLAIM] - Starting mysql cdc, server name: 458b51db-681e-43e7-a5f8-a01449a83337 
[INFO ] 2024-03-27 09:07:43.246 - [任务 13][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1129733810
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 458b51db-681e-43e7-a5f8-a01449a83337
  database.port: 3306
  threadName: Debezium-Mysql-Connector-458b51db-681e-43e7-a5f8-a01449a83337
  database.hostname: 127.0.0.1
  database.password: ********
  name: 458b51db-681e-43e7-a5f8-a01449a83337
  pdk.offset.string: {"name":"458b51db-681e-43e7-a5f8-a01449a83337","offset":{"{\"server\":\"458b51db-681e-43e7-a5f8-a01449a83337\"}":"{\"ts_sec\":1711501149,\"file\":\"binlog.000020\",\"pos\":144630362,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 09:07:43.316 - [任务 13][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 09:07:43.876 - [任务 13][CLAIM] - Mysql binlog reader stopped 
[WARN ] 2024-03-27 09:07:43.878 - [任务 13][CLAIM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: java.lang.RuntimeException: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>

 - Remaining retry 9 time(s)
 - Period 60 second(s) 
[INFO ] 2024-03-27 09:08:43.940 - [任务 13][CLAIM] - Starting mysql cdc, server name: 458b51db-681e-43e7-a5f8-a01449a83337 
[INFO ] 2024-03-27 09:08:43.943 - [任务 13][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 274707994
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 458b51db-681e-43e7-a5f8-a01449a83337
  database.port: 3306
  threadName: Debezium-Mysql-Connector-458b51db-681e-43e7-a5f8-a01449a83337
  database.hostname: 127.0.0.1
  database.password: ********
  name: 458b51db-681e-43e7-a5f8-a01449a83337
  pdk.offset.string: {"name":"458b51db-681e-43e7-a5f8-a01449a83337","offset":{"{\"server\":\"458b51db-681e-43e7-a5f8-a01449a83337\"}":"{\"ts_sec\":1711501149,\"file\":\"binlog.000020\",\"pos\":144630362,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 09:08:44.003 - [任务 13][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 09:08:44.584 - [任务 13][CLAIM] - Mysql binlog reader stopped 
[WARN ] 2024-03-27 09:08:44.591 - [任务 13][CLAIM] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: java.lang.RuntimeException: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>

 - Remaining retry 8 time(s)
 - Period 60 second(s) 
[INFO ] 2024-03-27 09:09:06.684 - [任务 13] - Stop task milestones: 660369f28b5bca60f72dc8dd(任务 13)  
[INFO ] 2024-03-27 09:09:06.895 - [任务 13][CLAIM] - Node CLAIM[bc0fda17-24a0-4b05-b76e-b464725c9d13] running status set to false 
[INFO ] 2024-03-27 09:09:06.952 - [任务 13][CLAIM] - Starting mysql cdc, server name: 458b51db-681e-43e7-a5f8-a01449a83337 
[INFO ] 2024-03-27 09:09:06.955 - [任务 13][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-bc0fda17-24a0-4b05-b76e-b464725c9d13 
[INFO ] 2024-03-27 09:09:06.956 - [任务 13][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-bc0fda17-24a0-4b05-b76e-b464725c9d13 
[INFO ] 2024-03-27 09:09:06.956 - [任务 13][CLAIM] - Node CLAIM[bc0fda17-24a0-4b05-b76e-b464725c9d13] schema data cleaned 
[INFO ] 2024-03-27 09:09:06.956 - [任务 13][CLAIM] - Node CLAIM[bc0fda17-24a0-4b05-b76e-b464725c9d13] monitor closed 
[INFO ] 2024-03-27 09:09:06.956 - [任务 13][CLAIM] - Node CLAIM[bc0fda17-24a0-4b05-b76e-b464725c9d13] close complete, cost 63 ms 
[INFO ] 2024-03-27 09:09:06.956 - [任务 13][test1] - Node test1[ea45930b-418a-42ff-a99b-08c927ea17fa] running status set to false 
[INFO ] 2024-03-27 09:09:06.983 - [任务 13][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2046709561
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 458b51db-681e-43e7-a5f8-a01449a83337
  database.port: 3306
  threadName: Debezium-Mysql-Connector-458b51db-681e-43e7-a5f8-a01449a83337
  database.hostname: 127.0.0.1
  database.password: ********
  name: 458b51db-681e-43e7-a5f8-a01449a83337
  pdk.offset.string: {"name":"458b51db-681e-43e7-a5f8-a01449a83337","offset":{"{\"server\":\"458b51db-681e-43e7-a5f8-a01449a83337\"}":"{\"ts_sec\":1711501149,\"file\":\"binlog.000020\",\"pos\":144630362,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 09:09:06.990 - [任务 13][test1] - PDK connector node stopped: HazelcastTargetPdkDataNode-ea45930b-418a-42ff-a99b-08c927ea17fa 
[INFO ] 2024-03-27 09:09:06.990 - [任务 13][test1] - PDK connector node released: HazelcastTargetPdkDataNode-ea45930b-418a-42ff-a99b-08c927ea17fa 
[INFO ] 2024-03-27 09:09:06.990 - [任务 13][test1] - Node test1[ea45930b-418a-42ff-a99b-08c927ea17fa] schema data cleaned 
[INFO ] 2024-03-27 09:09:06.990 - [任务 13][test1] - Node test1[ea45930b-418a-42ff-a99b-08c927ea17fa] monitor closed 
[INFO ] 2024-03-27 09:09:06.990 - [任务 13][test1] - Node test1[ea45930b-418a-42ff-a99b-08c927ea17fa] close complete, cost 38 ms 
[INFO ] 2024-03-27 09:09:07.057 - [任务 13][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 09:09:07.614 - [任务 13][CLAIM] - Mysql binlog reader stopped 
[ERROR] 2024-03-27 09:09:30.515 - [任务 13][CLAIM] - java.lang.RuntimeException: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>
 <-- Error Message -->
java.lang.RuntimeException: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>


<-- Simple Stack Trace -->
Caused by: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>

	net.sf.jsqlparser.parser.CCJSqlParser.generateParseException(CCJSqlParser.java:31468)
	net.sf.jsqlparser.parser.CCJSqlParser.jj_consume_token(CCJSqlParser.java:31301)
	net.sf.jsqlparser.parser.CCJSqlParser.AlterExpression(CCJSqlParser.java:17496)
	net.sf.jsqlparser.parser.CCJSqlParser.AlterTable(CCJSqlParser.java:17947)
	net.sf.jsqlparser.parser.CCJSqlParser.SingleStatement(CCJSqlParser.java:267)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>

	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:66)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>

	at io.tapdata.common.exception.AbstractExceptionCollector.collectCdcConfigInvalid(AbstractExceptionCollector.java:63)
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:142)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:333)
	at io.tapdata.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:458)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 18 more
Caused by: net.sf.jsqlparser.parser.ParseException: Encountered unexpected token: "check" "CHECK"
    at line 1, column 23.

Was expecting one of:

    "("
    "ACTION"
    "ACTIVE"
    "ALGORITHM"
    "ARCHIVE"
    "ARRAY"
    "AT"
    "BYTE"
    "CASCADE"
    "CASE"
    "CAST"
    "CHANGE"
    "CHAR"
    "CHARACTER"
    "CHECKPOINT"
    "COLUMN"
    "COLUMNS"
    "COMMENT"
    "COMMIT"
    "CONSTRAINT"
    "COSTS"
    "CREATE"
    "CYCLE"
    "DBA_RECYCLEBIN"
    "DEFAULT"
    "DESC"
    "DESCRIBE"
    "DISABLE"
    "DISCONNECT"
    "DIV"
    "DO"
    "DUMP"
    "DUPLICATE"
    "ENABLE"
    "END"
    "EXCLUDE"
    "EXTRACT"
    "FALSE"
    "FILTER"
    "FIRST"
    "FLUSH"
    "FN"
    "FOLLOWING"
    "FOREIGN"
    "FORMAT"
    "FULLTEXT"
    "GROUP"
    "HISTORY"
    "INDEX"
    "INSERT"
    "INTERVAL"
    "ISNULL"
    "JSON"
    "KEY"
    "LAST"
    "LEADING"
    "LINK"
    "LOCAL"
    "LOG"
    "MATERIALIZED"
    "NO"
    "NOLOCK"
    "NULLS"
    "OF"
    "ON"
    "OPEN"
    "ORDER"
    "OVER"
    "PARALLEL"
    "PARTITION"
    "PATH"
    "PERCENT"
    "PRECISION"
    "PRIMARY"
    "PRIOR"
    "QUERY"
    "QUIESCE"
    "RANGE"
    "READ"
    "RECYCLEBIN"
    "REGISTER"
    "REPLACE"
    "RESTRICTED"
    "RESUME"
    "ROW"
    "ROWS"
    "SCHEMA"
    "SEPARATOR"
    "SEQUENCE"
    "SESSION"
    "SHUTDOWN"
    "SIBLINGS"
    "SIGNED"
    "SIZE"
    "SKIP"
    "START"
    "SUSPEND"
    "SWITCH"
    "SYNONYM"
    "SYSTEM"
    "TABLE"
    "TABLES"
    "TABLESPACE"
    "TEMP"
    "TEMPORARY"
    "TIMEOUT"
    "TO"
    "TOP"
    "TRUE"
    "TRUNCATE"
    "TRY_CAST"
    "TYPE"
    "UNIQUE"
    "UNQIESCE"
    "UNSIGNED"
    "USER"
    "VALIDATE"
    "VALUE"
    "VALUES"
    "VIEW"
    "XML"
    "ZONE"
    <K_DATETIMELITERAL>
    <K_DATE_LITERAL>
    <K_NEXTVAL>
    <K_STRING_FUNCTION_NAME>
    <S_IDENTIFIER>
    <S_QUOTED_IDENTIFIER>

	at net.sf.jsqlparser.parser.CCJSqlParser.generateParseException(CCJSqlParser.java:31468)
	at net.sf.jsqlparser.parser.CCJSqlParser.jj_consume_token(CCJSqlParser.java:31301)
	at net.sf.jsqlparser.parser.CCJSqlParser.AlterExpression(CCJSqlParser.java:17496)
	at net.sf.jsqlparser.parser.CCJSqlParser.AlterTable(CCJSqlParser.java:17947)
	at net.sf.jsqlparser.parser.CCJSqlParser.SingleStatement(CCJSqlParser.java:267)
	at net.sf.jsqlparser.parser.CCJSqlParser.Statement(CCJSqlParser.java:153)
	at net.sf.jsqlparser.parser.CCJSqlParserUtil.parseStatement(CCJSqlParserUtil.java:188)
	at net.sf.jsqlparser.parser.CCJSqlParserUtil.parse(CCJSqlParserUtil.java:63)
	at net.sf.jsqlparser.parser.CCJSqlParserUtil.parse(CCJSqlParserUtil.java:38)
	at io.tapdata.common.ddl.parser.CCJSqlParser.parse(CCJSqlParser.java:18)
	at io.tapdata.common.ddl.parser.CCJSqlParser.parse(CCJSqlParser.java:12)
	at io.tapdata.common.ddl.DDLFactory.ddlToTapDDLEvent(DDLFactory.java:76)
	at io.tapdata.common.ddl.DDLFactory.ddlToTapDDLEvent(DDLFactory.java:58)
	at io.tapdata.connector.mysql.MysqlReader.wrapDDL(MysqlReader.java:556)
	at io.tapdata.connector.mysql.MysqlReader.consumeRecords(MysqlReader.java:434)
	at io.debezium.embedded.EmbeddedEngine.run(EmbeddedEngine.java:821)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:328)
	... 21 more

[INFO ] 2024-03-27 09:09:30.554 - [任务 13] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 09:09:30.555 - [任务 13] - Stopped task aspect(s) 
[INFO ] 2024-03-27 09:09:30.555 - [任务 13] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 09:09:30.584 - [任务 13] - Remove memory task client succeed, task: 任务 13[660369f28b5bca60f72dc8dd] 
[INFO ] 2024-03-27 09:09:30.590 - [任务 13] - Destroy memory task client cache succeed, task: 任务 13[660369f28b5bca60f72dc8dd] 
