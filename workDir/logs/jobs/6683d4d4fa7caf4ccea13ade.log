[INFO ] 2024-07-04 14:28:23.679 - [Heartbeat-SourceMongo] - Start task milestones: 6683d4d4fa7caf4ccea13ade(Heartbeat-SourceMongo) 
[INFO ] 2024-07-04 14:28:23.856 - [Heartbeat-SourceMongo] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-04 14:28:23.857 - [Heartbeat-SourceMongo] - The engine receives Heartbeat-SourceMongo task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-04 14:28:23.974 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3fb1b881-32e4-438e-8e4e-a1aaccd4a904] start preload schema,table counts: 1 
[INFO ] 2024-07-04 14:28:23.975 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[14a630fc-7f2f-47d1-a6aa-0505715733a0] start preload schema,table counts: 1 
[INFO ] 2024-07-04 14:28:23.975 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3fb1b881-32e4-438e-8e4e-a1aaccd4a904] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 14:28:23.979 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[14a630fc-7f2f-47d1-a6aa-0505715733a0] preload schema finished, cost 0 ms 
[INFO ] 2024-07-04 14:28:24.277 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-04 14:28:24.284 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-04 14:28:24.286 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-04 14:28:24.291 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1719915734694,"lastTimes":1719994376379,"lastTN":2477,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":29415,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-04 14:28:24.291 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-04 14:28:24.344 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1719915734694,"lastTimes":1719994376379,"lastTN":2477,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":29415,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-04 14:28:24.345 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-04 14:28:24.349 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-04 14:33:11.349 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3fb1b881-32e4-438e-8e4e-a1aaccd4a904] running status set to false 
[INFO ] 2024-07-04 14:33:11.369 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-04 14:33:11.389 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-3fb1b881-32e4-438e-8e4e-a1aaccd4a904 
[INFO ] 2024-07-04 14:33:11.390 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-3fb1b881-32e4-438e-8e4e-a1aaccd4a904 
[INFO ] 2024-07-04 14:33:11.391 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3fb1b881-32e4-438e-8e4e-a1aaccd4a904] schema data cleaned 
[INFO ] 2024-07-04 14:33:11.407 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3fb1b881-32e4-438e-8e4e-a1aaccd4a904] monitor closed 
[INFO ] 2024-07-04 14:33:11.408 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3fb1b881-32e4-438e-8e4e-a1aaccd4a904] close complete, cost 95 ms 
[INFO ] 2024-07-04 14:33:11.408 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[14a630fc-7f2f-47d1-a6aa-0505715733a0] running status set to false 
[INFO ] 2024-07-04 14:33:11.453 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-14a630fc-7f2f-47d1-a6aa-0505715733a0 
[INFO ] 2024-07-04 14:33:11.453 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-14a630fc-7f2f-47d1-a6aa-0505715733a0 
[INFO ] 2024-07-04 14:33:11.454 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[14a630fc-7f2f-47d1-a6aa-0505715733a0] schema data cleaned 
[INFO ] 2024-07-04 14:33:11.454 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[14a630fc-7f2f-47d1-a6aa-0505715733a0] monitor closed 
[INFO ] 2024-07-04 14:33:11.455 - [Heartbeat-SourceMongo][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[14a630fc-7f2f-47d1-a6aa-0505715733a0] close complete, cost 47 ms 
[INFO ] 2024-07-04 14:33:12.640 - [Heartbeat-SourceMongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-04 14:33:12.640 - [Heartbeat-SourceMongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7383a040 
[INFO ] 2024-07-04 14:33:12.640 - [Heartbeat-SourceMongo] - Stop task milestones: 6683d4d4fa7caf4ccea13ade(Heartbeat-SourceMongo)  
[INFO ] 2024-07-04 14:33:12.762 - [Heartbeat-SourceMongo] - Stopped task aspect(s) 
[INFO ] 2024-07-04 14:33:12.763 - [Heartbeat-SourceMongo] - Snapshot order controller have been removed 
[INFO ] 2024-07-04 14:33:12.795 - [Heartbeat-SourceMongo] - Remove memory task client succeed, task: Heartbeat-SourceMongo[6683d4d4fa7caf4ccea13ade] 
[INFO ] 2024-07-04 14:33:12.795 - [Heartbeat-SourceMongo] - Destroy memory task client cache succeed, task: Heartbeat-SourceMongo[6683d4d4fa7caf4ccea13ade] 
