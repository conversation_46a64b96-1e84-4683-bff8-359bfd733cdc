[INFO ] 2024-06-04 15:48:36.640 - [任务 3] - Task initialization... 
[INFO ] 2024-06-04 15:48:36.733 - [任务 3] - Start task milestones: 665ec637005bc048083d7392(任务 3) 
[INFO ] 2024-06-04 15:48:36.736 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-04 15:48:36.945 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-04 15:48:36.969 - [任务 3][TestClaim] - Node TestClaim[ce56eafe-86b0-4514-9335-3e41be37d51d] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:48:36.974 - [任务 3][CLAIM] - Node CLAIM[6ad44fa7-642f-41a8-8dce-cff26194bb0d] start preload schema,table counts: 1 
[INFO ] 2024-06-04 15:48:37.176 - [任务 3][CLAIM] - Node CLAIM[6ad44fa7-642f-41a8-8dce-cff26194bb0d] preload schema finished, cost 158 ms 
[INFO ] 2024-06-04 15:48:37.177 - [任务 3][TestClaim] - Node TestClaim[ce56eafe-86b0-4514-9335-3e41be37d51d] preload schema finished, cost 159 ms 
[INFO ] 2024-06-04 15:48:37.899 - [任务 3][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-06-04 15:48:37.900 - [任务 3][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-06-04 15:48:37.900 - [任务 3][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-04 15:48:38.109 - [任务 3][CLAIM] - batch offset found: {},stream offset found: {"cdcOffset":1717487317,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-04 15:48:38.173 - [任务 3][TestClaim] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-04 15:48:38.173 - [任务 3][CLAIM] - Initial sync started 
[INFO ] 2024-06-04 15:48:38.174 - [任务 3][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-04 15:48:49.675 - [任务 3][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-04 15:48:49.852 - [任务 3][CLAIM] - Query table 'CLAIM' counts: 1094 
[INFO ] 2024-06-04 15:48:50.568 - [任务 3][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-04 15:48:50.571 - [任务 3][CLAIM] - Initial sync completed 
[INFO ] 2024-06-04 15:48:50.571 - [任务 3][CLAIM] - Incremental sync starting... 
[INFO ] 2024-06-04 15:48:50.572 - [任务 3][CLAIM] - Initial sync completed 
[INFO ] 2024-06-04 15:48:50.613 - [任务 3][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"cdcOffset":1717487317,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-06-04 15:48:50.614 - [任务 3][CLAIM] - Connector MongoDB incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-04 15:49:57.211 - [任务 3] - Stop task milestones: 665ec637005bc048083d7392(任务 3)  
[INFO ] 2024-06-04 15:49:57.506 - [任务 3][CLAIM] - Node CLAIM[6ad44fa7-642f-41a8-8dce-cff26194bb0d] running status set to false 
[INFO ] 2024-06-04 15:49:57.546 - [任务 3][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-6ad44fa7-642f-41a8-8dce-cff26194bb0d 
[INFO ] 2024-06-04 15:49:57.546 - [任务 3][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-6ad44fa7-642f-41a8-8dce-cff26194bb0d 
[INFO ] 2024-06-04 15:49:57.546 - [任务 3][CLAIM] - Node CLAIM[6ad44fa7-642f-41a8-8dce-cff26194bb0d] schema data cleaned 
[INFO ] 2024-06-04 15:50:03.961 - [任务 3][CLAIM] - Node CLAIM[6ad44fa7-642f-41a8-8dce-cff26194bb0d] monitor closed 
[INFO ] 2024-06-04 15:50:04.055 - [任务 3][CLAIM] - Node CLAIM[6ad44fa7-642f-41a8-8dce-cff26194bb0d] close complete, cost 6441 ms 
[INFO ] 2024-06-04 15:50:04.055 - [任务 3][TestClaim] - Node TestClaim[ce56eafe-86b0-4514-9335-3e41be37d51d] running status set to false 
[INFO ] 2024-06-04 15:50:04.081 - [任务 3][TestClaim] - PDK connector node stopped: HazelcastTargetPdkDataNode-ce56eafe-86b0-4514-9335-3e41be37d51d 
[INFO ] 2024-06-04 15:50:04.082 - [任务 3][TestClaim] - PDK connector node released: HazelcastTargetPdkDataNode-ce56eafe-86b0-4514-9335-3e41be37d51d 
[INFO ] 2024-06-04 15:50:04.082 - [任务 3][TestClaim] - Node TestClaim[ce56eafe-86b0-4514-9335-3e41be37d51d] schema data cleaned 
[INFO ] 2024-06-04 15:50:04.082 - [任务 3][TestClaim] - Node TestClaim[ce56eafe-86b0-4514-9335-3e41be37d51d] monitor closed 
[INFO ] 2024-06-04 15:50:04.083 - [任务 3][TestClaim] - Node TestClaim[ce56eafe-86b0-4514-9335-3e41be37d51d] close complete, cost 43 ms 
[INFO ] 2024-06-04 15:50:09.071 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-04 15:50:09.072 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-06-04 15:50:09.072 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-06-04 15:50:09.099 - [任务 3] - Remove memory task client succeed, task: 任务 3[665ec637005bc048083d7392] 
[INFO ] 2024-06-04 15:50:09.099 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[665ec637005bc048083d7392] 
[INFO ] 2024-06-04 16:00:03.883 - [任务 3] - Task initialization... 
[INFO ] 2024-06-04 16:00:03.884 - [任务 3] - Start task milestones: 665ec637005bc048083d7392(任务 3) 
[INFO ] 2024-06-04 16:00:03.891 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-04 16:00:03.954 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-04 16:00:03.954 - [任务 3][TEST_001] - Node TEST_001[708bc2b2-85df-4b5f-abb8-d6e964bcbe4c] start preload schema,table counts: 1 
[INFO ] 2024-06-04 16:00:03.954 - [任务 3][TestClaim] - Node TestClaim[ce56eafe-86b0-4514-9335-3e41be37d51d] start preload schema,table counts: 1 
[INFO ] 2024-06-04 16:00:03.979 - [任务 3][TestClaim] - Node TestClaim[ce56eafe-86b0-4514-9335-3e41be37d51d] preload schema finished, cost 25 ms 
[INFO ] 2024-06-04 16:00:03.979 - [任务 3][TEST_001] - Node TEST_001[708bc2b2-85df-4b5f-abb8-d6e964bcbe4c] preload schema finished, cost 25 ms 
[INFO ] 2024-06-04 16:00:05.012 - [任务 3][TEST_001] - Source node "TEST_001" read batch size: 100 
[INFO ] 2024-06-04 16:00:05.013 - [任务 3][TEST_001] - Source node "TEST_001" event queue capacity: 200 
[INFO ] 2024-06-04 16:00:05.014 - [任务 3][TEST_001] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-04 16:00:05.047 - [任务 3][TestClaim] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-04 16:00:14.906 - [任务 3][TEST_001] - new logical replication slot created, slotName:tapdata_cdc_9f34471c_6cd2_4fd3_9818_8ba42860e4ca 
[INFO ] 2024-06-04 16:00:14.925 - [任务 3][TEST_001] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2024-06-04 16:00:14.925 - [任务 3][TEST_001] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-04 16:00:15.033 - [任务 3][TEST_001] - Initial sync started 
[INFO ] 2024-06-04 16:00:15.033 - [任务 3][TEST_001] - Starting batch read, table name: TEST_001, offset: null 
[INFO ] 2024-06-04 16:00:17.072 - [任务 3][TEST_001] - Table TEST_001 is going to be initial synced 
[INFO ] 2024-06-04 16:00:17.143 - [任务 3][TEST_001] - Table [TEST_001] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-04 16:00:17.144 - [任务 3][TEST_001] - Query table 'TEST_001' counts: 4 
[INFO ] 2024-06-04 16:00:17.144 - [任务 3][TEST_001] - Initial sync completed 
[INFO ] 2024-06-04 16:00:17.144 - [任务 3][TEST_001] - Incremental sync starting... 
[INFO ] 2024-06-04 16:00:17.144 - [任务 3][TEST_001] - Initial sync completed 
[INFO ] 2024-06-04 16:00:17.147 - [任务 3][TEST_001] - Starting stream read, table list: [TEST_001], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2024-06-04 16:00:17.358 - [任务 3][TEST_001] - Using an existing logical replication slot, slotName:tapdata_cdc_9f34471c_6cd2_4fd3_9818_8ba42860e4ca 
[INFO ] 2024-06-04 16:00:17.711 - [任务 3][TEST_001] - Connector PostgreSQL incremental start succeed, tables: [TEST_001], data change syncing 
[INFO ] 2024-06-04 16:01:00.670 - [任务 3] - Stop task milestones: 665ec637005bc048083d7392(任务 3)  
[INFO ] 2024-06-04 16:01:00.871 - [任务 3][TEST_001] - Node TEST_001[708bc2b2-85df-4b5f-abb8-d6e964bcbe4c] running status set to false 
[INFO ] 2024-06-04 16:01:00.908 - [任务 3][TEST_001] - PDK connector node stopped: HazelcastSourcePdkDataNode-708bc2b2-85df-4b5f-abb8-d6e964bcbe4c 
[INFO ] 2024-06-04 16:01:00.908 - [任务 3][TEST_001] - PDK connector node released: HazelcastSourcePdkDataNode-708bc2b2-85df-4b5f-abb8-d6e964bcbe4c 
[INFO ] 2024-06-04 16:01:00.908 - [任务 3][TEST_001] - Node TEST_001[708bc2b2-85df-4b5f-abb8-d6e964bcbe4c] schema data cleaned 
[INFO ] 2024-06-04 16:01:00.908 - [任务 3][TEST_001] - Node TEST_001[708bc2b2-85df-4b5f-abb8-d6e964bcbe4c] monitor closed 
[INFO ] 2024-06-04 16:01:00.909 - [任务 3][TEST_001] - Node TEST_001[708bc2b2-85df-4b5f-abb8-d6e964bcbe4c] close complete, cost 187 ms 
[INFO ] 2024-06-04 16:01:00.930 - [任务 3][TestClaim] - Node TestClaim[ce56eafe-86b0-4514-9335-3e41be37d51d] running status set to false 
[INFO ] 2024-06-04 16:01:00.931 - [任务 3][TestClaim] - PDK connector node stopped: HazelcastTargetPdkDataNode-ce56eafe-86b0-4514-9335-3e41be37d51d 
[INFO ] 2024-06-04 16:01:00.931 - [任务 3][TestClaim] - PDK connector node released: HazelcastTargetPdkDataNode-ce56eafe-86b0-4514-9335-3e41be37d51d 
[INFO ] 2024-06-04 16:01:00.931 - [任务 3][TestClaim] - Node TestClaim[ce56eafe-86b0-4514-9335-3e41be37d51d] schema data cleaned 
[INFO ] 2024-06-04 16:01:00.931 - [任务 3][TestClaim] - Node TestClaim[ce56eafe-86b0-4514-9335-3e41be37d51d] monitor closed 
[INFO ] 2024-06-04 16:01:00.931 - [任务 3][TestClaim] - Node TestClaim[ce56eafe-86b0-4514-9335-3e41be37d51d] close complete, cost 22 ms 
[INFO ] 2024-06-04 16:01:05.182 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-04 16:01:05.182 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-06-04 16:01:05.182 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-06-04 16:01:05.200 - [任务 3] - Remove memory task client succeed, task: 任务 3[665ec637005bc048083d7392] 
[INFO ] 2024-06-04 16:01:05.202 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[665ec637005bc048083d7392] 
[INFO ] 2024-06-04 16:01:13.789 - [任务 3] - Task initialization... 
[INFO ] 2024-06-04 16:01:13.790 - [任务 3] - Start task milestones: 665ec637005bc048083d7392(任务 3) 
[INFO ] 2024-06-04 16:01:13.811 - [任务 3] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-04 16:01:17.256 - [任务 3] - The engine receives 任务 3 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-04 16:01:17.398 - [任务 3][TestClaim] - Node TestClaim[ce56eafe-86b0-4514-9335-3e41be37d51d] start preload schema,table counts: 1 
[INFO ] 2024-06-04 16:01:17.401 - [任务 3][TEST_001] - Node TEST_001[708bc2b2-85df-4b5f-abb8-d6e964bcbe4c] start preload schema,table counts: 1 
[INFO ] 2024-06-04 16:01:17.401 - [任务 3][TEST_001] - Node TEST_001[708bc2b2-85df-4b5f-abb8-d6e964bcbe4c] preload schema finished, cost 48 ms 
[INFO ] 2024-06-04 16:01:17.401 - [任务 3][TestClaim] - Node TestClaim[ce56eafe-86b0-4514-9335-3e41be37d51d] preload schema finished, cost 48 ms 
[INFO ] 2024-06-04 16:01:18.366 - [任务 3][TEST_001] - Source node "TEST_001" read batch size: 100 
[INFO ] 2024-06-04 16:01:18.366 - [任务 3][TEST_001] - Source node "TEST_001" event queue capacity: 200 
[INFO ] 2024-06-04 16:01:18.366 - [任务 3][TEST_001] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-04 16:01:18.384 - [任务 3][TestClaim] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-04 16:01:27.390 - [任务 3][TEST_001] - new logical replication slot created, slotName:tapdata_cdc_4ff31681_d0a7_47be_bfcd_0f4cdab122fc 
[INFO ] 2024-06-04 16:01:27.453 - [任务 3][TEST_001] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2024-06-04 16:01:27.457 - [任务 3][TEST_001] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-04 16:01:27.507 - [任务 3][TEST_001] - Initial sync started 
[INFO ] 2024-06-04 16:01:27.507 - [任务 3][TEST_001] - Starting batch read, table name: TEST_001, offset: null 
[INFO ] 2024-06-04 16:02:03.761 - [任务 3][TEST_001] - Table TEST_001 is going to be initial synced 
[INFO ] 2024-06-04 16:02:03.886 - [任务 3][TEST_001] - Table [TEST_001] has been completed batch read, will skip batch read on the next run 
[WARN ] 2024-06-04 16:02:03.898 - [任务 3][TEST_001] - [Auto Retry] Method (source_batch_count) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLTransientConnectionException: HikariPool-62 - Connection is not available, request timed out after 36210ms.
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-06-04 16:02:49.237 - [任务 3] - Stop task milestones: 665ec637005bc048083d7392(任务 3)  
[INFO ] 2024-06-04 16:02:49.564 - [任务 3][TEST_001] - Node TEST_001[708bc2b2-85df-4b5f-abb8-d6e964bcbe4c] running status set to false 
[INFO ] 2024-06-04 16:02:49.566 - [任务 3][TEST_001] - PDK connector node stopped: HazelcastSourcePdkDataNode-708bc2b2-85df-4b5f-abb8-d6e964bcbe4c 
[INFO ] 2024-06-04 16:02:49.566 - [任务 3][TEST_001] - PDK connector node released: HazelcastSourcePdkDataNode-708bc2b2-85df-4b5f-abb8-d6e964bcbe4c 
[INFO ] 2024-06-04 16:02:49.566 - [任务 3][TEST_001] - Node TEST_001[708bc2b2-85df-4b5f-abb8-d6e964bcbe4c] schema data cleaned 
[INFO ] 2024-06-04 16:02:49.566 - [任务 3][TEST_001] - Node TEST_001[708bc2b2-85df-4b5f-abb8-d6e964bcbe4c] monitor closed 
[INFO ] 2024-06-04 16:02:49.567 - [任务 3][TEST_001] - Node TEST_001[708bc2b2-85df-4b5f-abb8-d6e964bcbe4c] close complete, cost 43 ms 
[INFO ] 2024-06-04 16:02:49.568 - [任务 3][TEST_001] - Cancel query 'TEST_001' snapshot row size with task stopped. 
[INFO ] 2024-06-04 16:02:49.569 - [任务 3][TestClaim] - Node TestClaim[ce56eafe-86b0-4514-9335-3e41be37d51d] running status set to false 
[INFO ] 2024-06-04 16:02:49.585 - [任务 3][TestClaim] - PDK connector node stopped: HazelcastTargetPdkDataNode-ce56eafe-86b0-4514-9335-3e41be37d51d 
[INFO ] 2024-06-04 16:02:49.585 - [任务 3][TestClaim] - PDK connector node released: HazelcastTargetPdkDataNode-ce56eafe-86b0-4514-9335-3e41be37d51d 
[INFO ] 2024-06-04 16:02:49.585 - [任务 3][TestClaim] - Node TestClaim[ce56eafe-86b0-4514-9335-3e41be37d51d] schema data cleaned 
[INFO ] 2024-06-04 16:02:49.585 - [任务 3][TestClaim] - Node TestClaim[ce56eafe-86b0-4514-9335-3e41be37d51d] monitor closed 
[INFO ] 2024-06-04 16:02:49.586 - [任务 3][TestClaim] - Node TestClaim[ce56eafe-86b0-4514-9335-3e41be37d51d] close complete, cost 18 ms 
[INFO ] 2024-06-04 16:02:53.825 - [任务 3] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-04 16:02:53.825 - [任务 3] - Stopped task aspect(s) 
[INFO ] 2024-06-04 16:02:53.825 - [任务 3] - Snapshot order controller have been removed 
[INFO ] 2024-06-04 16:02:53.865 - [任务 3] - Remove memory task client succeed, task: 任务 3[665ec637005bc048083d7392] 
[INFO ] 2024-06-04 16:02:53.867 - [任务 3] - Destroy memory task client cache succeed, task: 任务 3[665ec637005bc048083d7392] 
