[INFO ] 2024-10-23 15:26:41.345 - [CDC log cache task from mysql3306] - Start task milestones: 670cd822d363a43f4ca774b3(CDC log cache task from mysql3306) 
[INFO ] 2024-10-23 15:26:41.678 - [CDC log cache task from mysql3306] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-10-23 15:26:41.880 - [CDC log cache task from mysql3306] - The engine receives CDC log cache task from mysql3306 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-23 15:26:42.408 - [CDC log cache task from mysql3306][mysql3306] - Node mysql3306[d3b0838fa0e64a33907a2e6c38778941] start preload schema,table counts: 1 
[INFO ] 2024-10-23 15:26:42.411 - [CDC log cache task from mysql3306][MongoDB External Storage] - Node(MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-10-23 15:26:42.413 - [CDC log cache task from mysql3306][mysql3306] - Node mysql3306[d3b0838fa0e64a33907a2e6c38778941] preload schema finished, cost 1 ms 
[INFO ] 2024-10-23 15:26:42.416 - [CDC log cache task from mysql3306][MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-10-23 15:26:42.632 - [CDC log cache task from mysql3306][MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=670cd822ce59bbba5747f598, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=670754025fe35676bdffab25_BMSQL_ITEM, version=v2, tableName=BMSQL_ITEM, externalStorageTableName=ExternalStorage_SHARE_CDC_794615392, shareCdcTaskId=670cd822d363a43f4ca774b3, connectionId=670754025fe35676bdffab25) 
[INFO ] 2024-10-23 15:26:42.696 - [CDC log cache task from mysql3306][MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdv13-c?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-10-23 15:26:45.277 - [CDC log cache task from mysql3306][mysql3306] - Source node "mysql3306" read batch size: 100 
[INFO ] 2024-10-23 15:26:45.279 - [CDC log cache task from mysql3306][mysql3306] - Source node "mysql3306" event queue capacity: 200 
[INFO ] 2024-10-23 15:26:45.304 - [CDC log cache task from mysql3306][mysql3306] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-10-23 15:26:45.304 - [CDC log cache task from mysql3306][mysql3306] - batch offset found: {},stream offset found: {"name":"fd9af975-f470-40c2-8540-b8cce62edb28","offset":{"{\"server\":\"fd9af975-f470-40c2-8540-b8cce62edb28\"}":"{\"file\":\"binlog.000036\",\"pos\":938080,\"server_id\":1}"}} 
[INFO ] 2024-10-23 15:26:45.435 - [CDC log cache task from mysql3306][mysql3306] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-23 15:26:45.436 - [CDC log cache task from mysql3306][mysql3306] - Starting stream read, table list: [BMSQL_ITEM], offset: {"name":"fd9af975-f470-40c2-8540-b8cce62edb28","offset":{"{\"server\":\"fd9af975-f470-40c2-8540-b8cce62edb28\"}":"{\"file\":\"binlog.000036\",\"pos\":938080,\"server_id\":1}"}} 
[INFO ] 2024-10-23 15:26:45.505 - [CDC log cache task from mysql3306][mysql3306] - Starting mysql cdc, server name: fd9af975-f470-40c2-8540-b8cce62edb28 
[INFO ] 2024-10-23 15:26:45.506 - [CDC log cache task from mysql3306][mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"fd9af975-f470-40c2-8540-b8cce62edb28","offset":{"{\"server\":\"fd9af975-f470-40c2-8540-b8cce62edb28\"}":"{\"file\":\"binlog.000036\",\"pos\":938080,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1187639693
  time.precision.mode: adaptive_time_microseconds
  database.server.name: fd9af975-f470-40c2-8540-b8cce62edb28
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-fd9af975-f470-40c2-8540-b8cce62edb28
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: fd9af975-f470-40c2-8540-b8cce62edb28
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-23 15:26:46.351 - [CDC log cache task from mysql3306][mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-10-23 16:29:11.767 - [CDC log cache task from mysql3306][mysql3306] - Mysql binlog reader stopped 
[WARN ] 2024-10-23 16:29:11.770 - [CDC log cache task from mysql3306][mysql3306] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.lang.IllegalStateException: BinaryLogClient is already connected
	com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:566)
	com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	java.lang.Thread.run(Thread.java:750)
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-10-23 16:30:11.901 - [CDC log cache task from mysql3306][mysql3306] - Starting mysql cdc, server name: fd9af975-f470-40c2-8540-b8cce62edb28 
[INFO ] 2024-10-23 16:30:12.012 - [CDC log cache task from mysql3306][mysql3306] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"fd9af975-f470-40c2-8540-b8cce62edb28","offset":{"{\"server\":\"fd9af975-f470-40c2-8540-b8cce62edb28\"}":"{\"ts_sec\":1729503051,\"file\":\"binlog.000036\",\"pos\":946528,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1314359726
  time.precision.mode: adaptive_time_microseconds
  database.server.name: fd9af975-f470-40c2-8540-b8cce62edb28
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-fd9af975-f470-40c2-8540-b8cce62edb28
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: fd9af975-f470-40c2-8540-b8cce62edb28
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_ITEM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-23 16:30:12.013 - [CDC log cache task from mysql3306][mysql3306] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-10-23 16:30:12.217 - [CDC log cache task from mysql3306][mysql3306] - Connector Mysql incremental start succeed, tables: [BMSQL_ITEM], data change syncing 
[INFO ] 2024-10-23 17:26:55.630 - [CDC log cache task from mysql3306][mysql3306] - Node mysql3306[d3b0838fa0e64a33907a2e6c38778941] running status set to false 
[INFO ] 2024-10-23 17:26:55.682 - [CDC log cache task from mysql3306][mysql3306] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
