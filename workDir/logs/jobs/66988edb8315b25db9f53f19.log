[INFO ] 2024-07-18 11:41:26.432 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046] - Task initialization... 
[INFO ] 2024-07-18 11:41:26.636 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046] - Start task milestones: 66988edb8315b25db9f53f19(t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046) 
[INFO ] 2024-07-18 11:41:26.731 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 11:41:27.088 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046] - The engine receives t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 11:41:27.089 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][类型修改] - Node 类型修改[9fb57b3a-852f-4426-ab3b-74ff4b1189a4] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:41:27.089 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[5739b6d5-0d76-488b-a9f5-cb79dea7f267] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:41:27.089 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[41a75ce9-1ffd-45d5-9819-cf56839c1423] start preload schema,table counts: 1 
[INFO ] 2024-07-18 11:41:27.089 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][类型修改] - Node 类型修改[9fb57b3a-852f-4426-ab3b-74ff4b1189a4] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:41:27.089 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[41a75ce9-1ffd-45d5-9819-cf56839c1423] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:41:27.089 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[5739b6d5-0d76-488b-a9f5-cb79dea7f267] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 11:41:27.475 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mongodb_cluster_27017_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 11:41:27.845 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Source node "qa_mysql_repl_33306_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 11:41:27.845 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Source node "qa_mysql_repl_33306_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 11:41:27.845 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 11:41:28.002 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000304","position":479355070,"gtidSet":""} 
[INFO ] 2024-07-18 11:41:28.168 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 11:41:28.169 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Starting batch read, table name: t_bigint_20, offset: null 
[INFO ] 2024-07-18 11:41:28.170 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Table t_bigint_20 is going to be initial synced 
[INFO ] 2024-07-18 11:41:28.235 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Table [t_bigint_20] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 11:41:28.236 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Query table 't_bigint_20' counts: 5 
[INFO ] 2024-07-18 11:41:28.236 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:41:28.236 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 11:41:28.237 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 11:41:28.237 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Starting stream read, table list: [t_bigint_20, _tapdata_heartbeat_table], offset: {"filename":"mysql-bin.000304","position":479355070,"gtidSet":""} 
[INFO ] 2024-07-18 11:41:28.345 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Starting mysql cdc, server name: b41f943a-6d92-4e00-998e-a863171a8b4e 
[INFO ] 2024-07-18 11:41:28.345 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 827461407
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b41f943a-6d92-4e00-998e-a863171a8b4e
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b41f943a-6d92-4e00-998e-a863171a8b4e
  database.hostname: *************
  database.password: ********
  name: b41f943a-6d92-4e00-998e-a863171a8b4e
  pdk.offset.string: {"name":"b41f943a-6d92-4e00-998e-a863171a8b4e","offset":{"{\"server\":\"b41f943a-6d92-4e00-998e-a863171a8b4e\"}":"{\"file\":\"mysql-bin.000304\",\"pos\":479355070,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: t0.t_bigint_20,t0._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: t0
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-18 11:41:28.552 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Connector Mysql incremental start succeed, tables: [t_bigint_20, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 11:41:48.560 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[41a75ce9-1ffd-45d5-9819-cf56839c1423] running status set to false 
[INFO ] 2024-07-18 11:41:48.560 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-18 11:41:48.576 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Mysql binlog reader stopped 
[INFO ] 2024-07-18 11:41:48.576 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 11:41:48.595 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-41a75ce9-1ffd-45d5-9819-cf56839c1423 
[INFO ] 2024-07-18 11:41:48.596 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-41a75ce9-1ffd-45d5-9819-cf56839c1423 
[INFO ] 2024-07-18 11:41:48.596 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[41a75ce9-1ffd-45d5-9819-cf56839c1423] schema data cleaned 
[INFO ] 2024-07-18 11:41:48.596 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[41a75ce9-1ffd-45d5-9819-cf56839c1423] monitor closed 
[INFO ] 2024-07-18 11:41:48.598 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[41a75ce9-1ffd-45d5-9819-cf56839c1423] close complete, cost 131 ms 
[INFO ] 2024-07-18 11:41:48.599 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][类型修改] - Node 类型修改[9fb57b3a-852f-4426-ab3b-74ff4b1189a4] running status set to false 
[INFO ] 2024-07-18 11:41:48.800 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][类型修改] - Node 类型修改[9fb57b3a-852f-4426-ab3b-74ff4b1189a4] schema data cleaned 
[INFO ] 2024-07-18 11:41:48.801 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][类型修改] - Node 类型修改[9fb57b3a-852f-4426-ab3b-74ff4b1189a4] monitor closed 
[INFO ] 2024-07-18 11:41:48.802 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][类型修改] - Node 类型修改[9fb57b3a-852f-4426-ab3b-74ff4b1189a4] close complete, cost 203 ms 
[INFO ] 2024-07-18 11:41:48.849 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[5739b6d5-0d76-488b-a9f5-cb79dea7f267] running status set to false 
[INFO ] 2024-07-18 11:41:48.851 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mongodb_cluster_27017_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-5739b6d5-0d76-488b-a9f5-cb79dea7f267 
[INFO ] 2024-07-18 11:41:48.851 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mongodb_cluster_27017_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-5739b6d5-0d76-488b-a9f5-cb79dea7f267 
[INFO ] 2024-07-18 11:41:48.851 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[5739b6d5-0d76-488b-a9f5-cb79dea7f267] schema data cleaned 
[INFO ] 2024-07-18 11:41:48.851 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[5739b6d5-0d76-488b-a9f5-cb79dea7f267] monitor closed 
[INFO ] 2024-07-18 11:41:49.052 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046][qa_mongodb_cluster_27017_1717403468657_3537] - Node qa_mongodb_cluster_27017_1717403468657_3537[5739b6d5-0d76-488b-a9f5-cb79dea7f267] close complete, cost 50 ms 
[INFO ] 2024-07-18 11:41:49.934 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 11:41:49.934 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4deea78a 
[INFO ] 2024-07-18 11:41:50.050 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046] - Stop task milestones: 66988edb8315b25db9f53f19(t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046)  
[INFO ] 2024-07-18 11:41:50.089 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046] - Stopped task aspect(s) 
[INFO ] 2024-07-18 11:41:50.089 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 11:41:50.171 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046] - Remove memory task client succeed, task: t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046[66988edb8315b25db9f53f19] 
[INFO ] 2024-07-18 11:41:50.172 - [t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046] - Destroy memory task client cache succeed, task: t_3.2255-mysql -> mongodb, bigint(20) unsigned type_1717403468657_3537-1721274046[66988edb8315b25db9f53f19] 
