[INFO ] 2024-03-28 19:10:23.027 - [orders(100)][0de4d993-5d5c-487c-a258-024b1a3db7c5] - Node 0de4d993-5d5c-487c-a258-024b1a3db7c5[0de4d993-5d5c-487c-a258-024b1a3db7c5] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:10:23.028 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:23.029 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:23.029 - [orders(100)][0de4d993-5d5c-487c-a258-024b1a3db7c5] - Node 0de4d993-5d5c-487c-a258-024b1a3db7c5[0de4d993-5d5c-487c-a258-024b1a3db7c5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:23.031 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:23.033 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:23.035 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:10:23.037 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1e29e1eb error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1e29e1eb error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1e29e1eb error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 19:10:23.573 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:10:23.617 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:10:23.617 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:23.619 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:23.619 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:10:23.624 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:10:23.624 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 41 ms 
[INFO ] 2024-03-28 19:10:25.615 - [orders(100)][0de4d993-5d5c-487c-a258-024b1a3db7c5] - Node 0de4d993-5d5c-487c-a258-024b1a3db7c5[0de4d993-5d5c-487c-a258-024b1a3db7c5] running status set to false 
[INFO ] 2024-03-28 19:10:25.622 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:10:25.623 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:10:25.623 - [orders(100)][0de4d993-5d5c-487c-a258-024b1a3db7c5] - Node 0de4d993-5d5c-487c-a258-024b1a3db7c5[0de4d993-5d5c-487c-a258-024b1a3db7c5] schema data cleaned 
[INFO ] 2024-03-28 19:10:25.624 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:10:25.631 - [orders(100)][0de4d993-5d5c-487c-a258-024b1a3db7c5] - Node 0de4d993-5d5c-487c-a258-024b1a3db7c5[0de4d993-5d5c-487c-a258-024b1a3db7c5] monitor closed 
[INFO ] 2024-03-28 19:10:25.633 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 40 ms 
[INFO ] 2024-03-28 19:10:25.633 - [orders(100)][0de4d993-5d5c-487c-a258-024b1a3db7c5] - Node 0de4d993-5d5c-487c-a258-024b1a3db7c5[0de4d993-5d5c-487c-a258-024b1a3db7c5] close complete, cost 41 ms 
[INFO ] 2024-03-28 19:10:25.712 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-0de4d993-5d5c-487c-a258-024b1a3db7c5 complete, cost 3652ms 
[INFO ] 2024-03-28 19:10:43.745 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:43.748 - [orders(100)][e435dbb3-9b46-4b70-8fe7-af95e2064ee7] - Node e435dbb3-9b46-4b70-8fe7-af95e2064ee7[e435dbb3-9b46-4b70-8fe7-af95e2064ee7] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:10:43.748 - [orders(100)][611d80c6-32d8-4eea-94ff-3e1d5b07731e] - Node 611d80c6-32d8-4eea-94ff-3e1d5b07731e[611d80c6-32d8-4eea-94ff-3e1d5b07731e] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:10:43.750 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:43.750 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:43.750 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:43.751 - [orders(100)][611d80c6-32d8-4eea-94ff-3e1d5b07731e] - Node 611d80c6-32d8-4eea-94ff-3e1d5b07731e[611d80c6-32d8-4eea-94ff-3e1d5b07731e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:43.752 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:43.752 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:43.752 - [orders(100)][e435dbb3-9b46-4b70-8fe7-af95e2064ee7] - Node e435dbb3-9b46-4b70-8fe7-af95e2064ee7[e435dbb3-9b46-4b70-8fe7-af95e2064ee7] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:43.752 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:43.752 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:43.893 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 19:10:43.893 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:10:43.894 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3357c648 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3357c648 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3357c648 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-28 19:10:43.896 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@76857d0a error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@76857d0a error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@76857d0a error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 19:10:44.016 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:10:44.036 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:10:44.036 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:44.036 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:44.037 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:10:44.037 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:10:44.038 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 18 ms 
[WARN ] 2024-03-28 19:10:44.244 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:10:44.246 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:10:44.257 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:44.257 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:44.259 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:10:44.260 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:10:44.260 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 19 ms 
[INFO ] 2024-03-28 19:10:44.878 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:44.881 - [orders(100)][98eac9e6-662d-4cae-96c1-cd98c873053f] - Node 98eac9e6-662d-4cae-96c1-cd98c873053f[98eac9e6-662d-4cae-96c1-cd98c873053f] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:10:44.881 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:44.881 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:44.881 - [orders(100)][98eac9e6-662d-4cae-96c1-cd98c873053f] - Node 98eac9e6-662d-4cae-96c1-cd98c873053f[98eac9e6-662d-4cae-96c1-cd98c873053f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:44.881 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:44.989 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 19:10:44.993 - [orders(100)][fc12fa29-c578-4a47-9e06-30c640b1557d] - Node fc12fa29-c578-4a47-9e06-30c640b1557d[fc12fa29-c578-4a47-9e06-30c640b1557d] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:10:44.993 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:44.993 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:44.993 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:44.993 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:44.994 - [orders(100)][fc12fa29-c578-4a47-9e06-30c640b1557d] - Node fc12fa29-c578-4a47-9e06-30c640b1557d[fc12fa29-c578-4a47-9e06-30c640b1557d] preload schema finished, cost 0 ms 
[ERROR] 2024-03-28 19:10:44.994 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6638b524 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6638b524 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6638b524 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 19:10:45.043 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:10:45.044 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4ece90cf error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4ece90cf error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4ece90cf error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 19:10:45.062 - [orders(100)][9f0596ee-220c-4f9b-9218-d0a04fb8c54e] - Node 9f0596ee-220c-4f9b-9218-d0a04fb8c54e[9f0596ee-220c-4f9b-9218-d0a04fb8c54e] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:10:45.062 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:45.062 - [orders(100)][9f0596ee-220c-4f9b-9218-d0a04fb8c54e] - Node 9f0596ee-220c-4f9b-9218-d0a04fb8c54e[9f0596ee-220c-4f9b-9218-d0a04fb8c54e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:45.063 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:45.065 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:45.065 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:45.108 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:10:45.108 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4bea0ade error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4bea0ade error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4bea0ade error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 19:10:45.108 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:10:45.119 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:10:45.131 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:45.132 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:45.133 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:10:45.133 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:10:45.320 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 21 ms 
[WARN ] 2024-03-28 19:10:45.321 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:10:45.322 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:10:45.332 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:45.332 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:45.332 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:10:45.333 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:10:45.334 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 12 ms 
[WARN ] 2024-03-28 19:10:45.474 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:10:45.474 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:10:45.482 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:45.482 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:45.483 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:10:45.484 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:10:45.688 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 10 ms 
[INFO ] 2024-03-28 19:10:46.203 - [orders(100)][849f6713-b01f-4c71-b058-7af0566d581d] - Node 849f6713-b01f-4c71-b058-7af0566d581d[849f6713-b01f-4c71-b058-7af0566d581d] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:10:46.203 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:46.203 - [orders(100)][849f6713-b01f-4c71-b058-7af0566d581d] - Node 849f6713-b01f-4c71-b058-7af0566d581d[849f6713-b01f-4c71-b058-7af0566d581d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:46.203 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:46.203 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 19:10:46.203 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 19:10:46.253 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:10:46.417 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@35dcded error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@35dcded error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@35dcded error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 19:10:46.417 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:10:46.417 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:10:46.417 - [orders(100)][e435dbb3-9b46-4b70-8fe7-af95e2064ee7] - Node e435dbb3-9b46-4b70-8fe7-af95e2064ee7[e435dbb3-9b46-4b70-8fe7-af95e2064ee7] running status set to false 
[INFO ] 2024-03-28 19:10:46.417 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:10:46.417 - [orders(100)][e435dbb3-9b46-4b70-8fe7-af95e2064ee7] - Node e435dbb3-9b46-4b70-8fe7-af95e2064ee7[e435dbb3-9b46-4b70-8fe7-af95e2064ee7] schema data cleaned 
[INFO ] 2024-03-28 19:10:46.418 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 1 ms 
[INFO ] 2024-03-28 19:10:46.418 - [orders(100)][e435dbb3-9b46-4b70-8fe7-af95e2064ee7] - Node e435dbb3-9b46-4b70-8fe7-af95e2064ee7[e435dbb3-9b46-4b70-8fe7-af95e2064ee7] monitor closed 
[INFO ] 2024-03-28 19:10:46.418 - [orders(100)][e435dbb3-9b46-4b70-8fe7-af95e2064ee7] - Node e435dbb3-9b46-4b70-8fe7-af95e2064ee7[e435dbb3-9b46-4b70-8fe7-af95e2064ee7] close complete, cost 1 ms 
[INFO ] 2024-03-28 19:10:46.426 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-e435dbb3-9b46-4b70-8fe7-af95e2064ee7 complete, cost 2768ms 
[INFO ] 2024-03-28 19:10:46.426 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:10:46.426 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:10:46.427 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:10:46.427 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 1 ms 
[INFO ] 2024-03-28 19:10:46.433 - [orders(100)][611d80c6-32d8-4eea-94ff-3e1d5b07731e] - Node 611d80c6-32d8-4eea-94ff-3e1d5b07731e[611d80c6-32d8-4eea-94ff-3e1d5b07731e] running status set to false 
[INFO ] 2024-03-28 19:10:46.435 - [orders(100)][611d80c6-32d8-4eea-94ff-3e1d5b07731e] - Node 611d80c6-32d8-4eea-94ff-3e1d5b07731e[611d80c6-32d8-4eea-94ff-3e1d5b07731e] schema data cleaned 
[INFO ] 2024-03-28 19:10:46.435 - [orders(100)][611d80c6-32d8-4eea-94ff-3e1d5b07731e] - Node 611d80c6-32d8-4eea-94ff-3e1d5b07731e[611d80c6-32d8-4eea-94ff-3e1d5b07731e] monitor closed 
[INFO ] 2024-03-28 19:10:46.436 - [orders(100)][611d80c6-32d8-4eea-94ff-3e1d5b07731e] - Node 611d80c6-32d8-4eea-94ff-3e1d5b07731e[611d80c6-32d8-4eea-94ff-3e1d5b07731e] close complete, cost 6 ms 
[INFO ] 2024-03-28 19:10:46.436 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-611d80c6-32d8-4eea-94ff-3e1d5b07731e complete, cost 2822ms 
[WARN ] 2024-03-28 19:10:46.445 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:10:46.446 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:10:46.468 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:46.468 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:46.468 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:10:46.471 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:10:46.676 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 27 ms 
[INFO ] 2024-03-28 19:10:47.524 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:10:47.526 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:10:47.527 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:10:47.527 - [orders(100)][98eac9e6-662d-4cae-96c1-cd98c873053f] - Node 98eac9e6-662d-4cae-96c1-cd98c873053f[98eac9e6-662d-4cae-96c1-cd98c873053f] running status set to false 
[INFO ] 2024-03-28 19:10:47.528 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 5 ms 
[INFO ] 2024-03-28 19:10:47.528 - [orders(100)][98eac9e6-662d-4cae-96c1-cd98c873053f] - Node 98eac9e6-662d-4cae-96c1-cd98c873053f[98eac9e6-662d-4cae-96c1-cd98c873053f] schema data cleaned 
[INFO ] 2024-03-28 19:10:47.535 - [orders(100)][98eac9e6-662d-4cae-96c1-cd98c873053f] - Node 98eac9e6-662d-4cae-96c1-cd98c873053f[98eac9e6-662d-4cae-96c1-cd98c873053f] monitor closed 
[INFO ] 2024-03-28 19:10:47.535 - [orders(100)][98eac9e6-662d-4cae-96c1-cd98c873053f] - Node 98eac9e6-662d-4cae-96c1-cd98c873053f[98eac9e6-662d-4cae-96c1-cd98c873053f] close complete, cost 4 ms 
[INFO ] 2024-03-28 19:10:47.574 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-98eac9e6-662d-4cae-96c1-cd98c873053f complete, cost 2709ms 
[INFO ] 2024-03-28 19:10:47.575 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:10:47.575 - [orders(100)][fc12fa29-c578-4a47-9e06-30c640b1557d] - Node fc12fa29-c578-4a47-9e06-30c640b1557d[fc12fa29-c578-4a47-9e06-30c640b1557d] running status set to false 
[INFO ] 2024-03-28 19:10:47.577 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:10:47.577 - [orders(100)][fc12fa29-c578-4a47-9e06-30c640b1557d] - Node fc12fa29-c578-4a47-9e06-30c640b1557d[fc12fa29-c578-4a47-9e06-30c640b1557d] schema data cleaned 
[INFO ] 2024-03-28 19:10:47.578 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:10:47.580 - [orders(100)][fc12fa29-c578-4a47-9e06-30c640b1557d] - Node fc12fa29-c578-4a47-9e06-30c640b1557d[fc12fa29-c578-4a47-9e06-30c640b1557d] monitor closed 
[INFO ] 2024-03-28 19:10:47.580 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 9 ms 
[INFO ] 2024-03-28 19:10:47.583 - [orders(100)][fc12fa29-c578-4a47-9e06-30c640b1557d] - Node fc12fa29-c578-4a47-9e06-30c640b1557d[fc12fa29-c578-4a47-9e06-30c640b1557d] close complete, cost 8 ms 
[INFO ] 2024-03-28 19:10:47.583 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-fc12fa29-c578-4a47-9e06-30c640b1557d complete, cost 2645ms 
[INFO ] 2024-03-28 19:10:47.629 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:10:47.629 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:10:47.630 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:10:47.630 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 1 ms 
[INFO ] 2024-03-28 19:10:47.633 - [orders(100)][9f0596ee-220c-4f9b-9218-d0a04fb8c54e] - Node 9f0596ee-220c-4f9b-9218-d0a04fb8c54e[9f0596ee-220c-4f9b-9218-d0a04fb8c54e] running status set to false 
[INFO ] 2024-03-28 19:10:47.633 - [orders(100)][9f0596ee-220c-4f9b-9218-d0a04fb8c54e] - Node 9f0596ee-220c-4f9b-9218-d0a04fb8c54e[9f0596ee-220c-4f9b-9218-d0a04fb8c54e] schema data cleaned 
[INFO ] 2024-03-28 19:10:47.633 - [orders(100)][9f0596ee-220c-4f9b-9218-d0a04fb8c54e] - Node 9f0596ee-220c-4f9b-9218-d0a04fb8c54e[9f0596ee-220c-4f9b-9218-d0a04fb8c54e] monitor closed 
[INFO ] 2024-03-28 19:10:47.633 - [orders(100)][9f0596ee-220c-4f9b-9218-d0a04fb8c54e] - Node 9f0596ee-220c-4f9b-9218-d0a04fb8c54e[9f0596ee-220c-4f9b-9218-d0a04fb8c54e] close complete, cost 0 ms 
[INFO ] 2024-03-28 19:10:47.838 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-9f0596ee-220c-4f9b-9218-d0a04fb8c54e complete, cost 2609ms 
[INFO ] 2024-03-28 19:10:48.825 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:10:48.826 - [orders(100)][849f6713-b01f-4c71-b058-7af0566d581d] - Node 849f6713-b01f-4c71-b058-7af0566d581d[849f6713-b01f-4c71-b058-7af0566d581d] running status set to false 
[INFO ] 2024-03-28 19:10:48.826 - [orders(100)][849f6713-b01f-4c71-b058-7af0566d581d] - Node 849f6713-b01f-4c71-b058-7af0566d581d[849f6713-b01f-4c71-b058-7af0566d581d] schema data cleaned 
[INFO ] 2024-03-28 19:10:48.826 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:10:48.826 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:10:48.827 - [orders(100)][849f6713-b01f-4c71-b058-7af0566d581d] - Node 849f6713-b01f-4c71-b058-7af0566d581d[849f6713-b01f-4c71-b058-7af0566d581d] monitor closed 
[INFO ] 2024-03-28 19:10:48.829 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 7 ms 
[INFO ] 2024-03-28 19:10:48.829 - [orders(100)][849f6713-b01f-4c71-b058-7af0566d581d] - Node 849f6713-b01f-4c71-b058-7af0566d581d[849f6713-b01f-4c71-b058-7af0566d581d] close complete, cost 6 ms 
[INFO ] 2024-03-28 19:10:49.033 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-849f6713-b01f-4c71-b058-7af0566d581d complete, cost 2731ms 
[INFO ] 2024-03-28 19:10:50.744 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:50.744 - [orders(100)][e0898aa3-8d02-4f21-9932-bb100ce3ca4b] - Node e0898aa3-8d02-4f21-9932-bb100ce3ca4b[e0898aa3-8d02-4f21-9932-bb100ce3ca4b] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:10:50.744 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:50.745 - [orders(100)][e0898aa3-8d02-4f21-9932-bb100ce3ca4b] - Node e0898aa3-8d02-4f21-9932-bb100ce3ca4b[e0898aa3-8d02-4f21-9932-bb100ce3ca4b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:50.746 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:50.746 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:50.766 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:10:50.838 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1a1eeaeb error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1a1eeaeb error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1a1eeaeb error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 19:10:50.838 - [orders(100)][e5288419-1dec-4c81-9c2b-a110a6f94dbf] - Node e5288419-1dec-4c81-9c2b-a110a6f94dbf[e5288419-1dec-4c81-9c2b-a110a6f94dbf] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:10:50.838 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:50.839 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:50.839 - [orders(100)][e5288419-1dec-4c81-9c2b-a110a6f94dbf] - Node e5288419-1dec-4c81-9c2b-a110a6f94dbf[e5288419-1dec-4c81-9c2b-a110a6f94dbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:50.839 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:50.839 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:50.874 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:10:50.874 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7b3e80cf error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7b3e80cf error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7b3e80cf error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 19:10:50.946 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:10:50.946 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:10:50.958 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:50.958 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:50.959 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:10:50.960 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:10:50.960 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 14 ms 
[WARN ] 2024-03-28 19:10:51.136 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:10:51.136 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:10:51.144 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:51.145 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:51.145 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:10:51.146 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:10:51.146 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 12 ms 
[INFO ] 2024-03-28 19:10:53.345 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:10:53.348 - [orders(100)][e0898aa3-8d02-4f21-9932-bb100ce3ca4b] - Node e0898aa3-8d02-4f21-9932-bb100ce3ca4b[e0898aa3-8d02-4f21-9932-bb100ce3ca4b] running status set to false 
[INFO ] 2024-03-28 19:10:53.348 - [orders(100)][e0898aa3-8d02-4f21-9932-bb100ce3ca4b] - Node e0898aa3-8d02-4f21-9932-bb100ce3ca4b[e0898aa3-8d02-4f21-9932-bb100ce3ca4b] schema data cleaned 
[INFO ] 2024-03-28 19:10:53.349 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:10:53.349 - [orders(100)][e0898aa3-8d02-4f21-9932-bb100ce3ca4b] - Node e0898aa3-8d02-4f21-9932-bb100ce3ca4b[e0898aa3-8d02-4f21-9932-bb100ce3ca4b] monitor closed 
[INFO ] 2024-03-28 19:10:53.354 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:10:53.354 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 26 ms 
[INFO ] 2024-03-28 19:10:53.356 - [orders(100)][e0898aa3-8d02-4f21-9932-bb100ce3ca4b] - Node e0898aa3-8d02-4f21-9932-bb100ce3ca4b[e0898aa3-8d02-4f21-9932-bb100ce3ca4b] close complete, cost 18 ms 
[INFO ] 2024-03-28 19:10:53.356 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-e0898aa3-8d02-4f21-9932-bb100ce3ca4b complete, cost 2650ms 
[INFO ] 2024-03-28 19:10:53.420 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:10:53.420 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:10:53.420 - [orders(100)][e5288419-1dec-4c81-9c2b-a110a6f94dbf] - Node e5288419-1dec-4c81-9c2b-a110a6f94dbf[e5288419-1dec-4c81-9c2b-a110a6f94dbf] running status set to false 
[INFO ] 2024-03-28 19:10:53.428 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:10:53.429 - [orders(100)][e5288419-1dec-4c81-9c2b-a110a6f94dbf] - Node e5288419-1dec-4c81-9c2b-a110a6f94dbf[e5288419-1dec-4c81-9c2b-a110a6f94dbf] schema data cleaned 
[INFO ] 2024-03-28 19:10:53.470 - [orders(100)][e5288419-1dec-4c81-9c2b-a110a6f94dbf] - Node e5288419-1dec-4c81-9c2b-a110a6f94dbf[e5288419-1dec-4c81-9c2b-a110a6f94dbf] monitor closed 
[INFO ] 2024-03-28 19:10:53.473 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 18 ms 
[INFO ] 2024-03-28 19:10:53.475 - [orders(100)][e5288419-1dec-4c81-9c2b-a110a6f94dbf] - Node e5288419-1dec-4c81-9c2b-a110a6f94dbf[e5288419-1dec-4c81-9c2b-a110a6f94dbf] close complete, cost 25 ms 
[INFO ] 2024-03-28 19:10:53.475 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-e5288419-1dec-4c81-9c2b-a110a6f94dbf complete, cost 2643ms 
[INFO ] 2024-03-28 19:10:54.776 - [orders(100)][b8ae1b3e-79e0-4259-ae27-6f9536b6332b] - Node b8ae1b3e-79e0-4259-ae27-6f9536b6332b[b8ae1b3e-79e0-4259-ae27-6f9536b6332b] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:10:54.776 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:54.776 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:54.776 - [orders(100)][b8ae1b3e-79e0-4259-ae27-6f9536b6332b] - Node b8ae1b3e-79e0-4259-ae27-6f9536b6332b[b8ae1b3e-79e0-4259-ae27-6f9536b6332b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:54.776 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:54.813 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:54.813 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:10:54.990 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@37ab98c5 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@37ab98c5 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@37ab98c5 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 19:10:54.994 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:10:54.994 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:10:55.004 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:55.005 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:55.006 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:10:55.006 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:10:55.007 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 14 ms 
[INFO ] 2024-03-28 19:10:57.381 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:10:57.386 - [orders(100)][b8ae1b3e-79e0-4259-ae27-6f9536b6332b] - Node b8ae1b3e-79e0-4259-ae27-6f9536b6332b[b8ae1b3e-79e0-4259-ae27-6f9536b6332b] running status set to false 
[INFO ] 2024-03-28 19:10:57.386 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:10:57.386 - [orders(100)][b8ae1b3e-79e0-4259-ae27-6f9536b6332b] - Node b8ae1b3e-79e0-4259-ae27-6f9536b6332b[b8ae1b3e-79e0-4259-ae27-6f9536b6332b] schema data cleaned 
[INFO ] 2024-03-28 19:10:57.386 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:10:57.386 - [orders(100)][b8ae1b3e-79e0-4259-ae27-6f9536b6332b] - Node b8ae1b3e-79e0-4259-ae27-6f9536b6332b[b8ae1b3e-79e0-4259-ae27-6f9536b6332b] monitor closed 
[INFO ] 2024-03-28 19:10:57.386 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 9 ms 
[INFO ] 2024-03-28 19:10:57.391 - [orders(100)][b8ae1b3e-79e0-4259-ae27-6f9536b6332b] - Node b8ae1b3e-79e0-4259-ae27-6f9536b6332b[b8ae1b3e-79e0-4259-ae27-6f9536b6332b] close complete, cost 4 ms 
[INFO ] 2024-03-28 19:10:57.391 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-b8ae1b3e-79e0-4259-ae27-6f9536b6332b complete, cost 2654ms 
[INFO ] 2024-03-28 19:10:58.298 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:58.299 - [orders(100)][0dbf63b1-7a4d-4ca1-a6bf-adbdcf8c779b] - Node 0dbf63b1-7a4d-4ca1-a6bf-adbdcf8c779b[0dbf63b1-7a4d-4ca1-a6bf-adbdcf8c779b] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:10:58.299 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:10:58.299 - [orders(100)][0dbf63b1-7a4d-4ca1-a6bf-adbdcf8c779b] - Node 0dbf63b1-7a4d-4ca1-a6bf-adbdcf8c779b[0dbf63b1-7a4d-4ca1-a6bf-adbdcf8c779b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:58.299 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:58.353 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:10:58.353 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:10:58.538 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7931ab47 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7931ab47 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7931ab47 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 19:10:58.541 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:10:58.541 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:10:58.553 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:58.554 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:10:58.554 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:10:58.554 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:10:58.554 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 14 ms 
[INFO ] 2024-03-28 19:11:00.942 - [orders(100)][0dbf63b1-7a4d-4ca1-a6bf-adbdcf8c779b] - Node 0dbf63b1-7a4d-4ca1-a6bf-adbdcf8c779b[0dbf63b1-7a4d-4ca1-a6bf-adbdcf8c779b] running status set to false 
[INFO ] 2024-03-28 19:11:00.946 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:11:00.946 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:11:00.947 - [orders(100)][0dbf63b1-7a4d-4ca1-a6bf-adbdcf8c779b] - Node 0dbf63b1-7a4d-4ca1-a6bf-adbdcf8c779b[0dbf63b1-7a4d-4ca1-a6bf-adbdcf8c779b] schema data cleaned 
[INFO ] 2024-03-28 19:11:00.947 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:11:00.947 - [orders(100)][0dbf63b1-7a4d-4ca1-a6bf-adbdcf8c779b] - Node 0dbf63b1-7a4d-4ca1-a6bf-adbdcf8c779b[0dbf63b1-7a4d-4ca1-a6bf-adbdcf8c779b] monitor closed 
[INFO ] 2024-03-28 19:11:00.947 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 44 ms 
[INFO ] 2024-03-28 19:11:00.948 - [orders(100)][0dbf63b1-7a4d-4ca1-a6bf-adbdcf8c779b] - Node 0dbf63b1-7a4d-4ca1-a6bf-adbdcf8c779b[0dbf63b1-7a4d-4ca1-a6bf-adbdcf8c779b] close complete, cost 44 ms 
[INFO ] 2024-03-28 19:11:00.950 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-0dbf63b1-7a4d-4ca1-a6bf-adbdcf8c779b complete, cost 2714ms 
[INFO ] 2024-03-28 19:11:03.080 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:03.080 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:03.080 - [orders(100)][61ec9d05-af04-4c3d-bce3-14952b5e42f1] - Node 61ec9d05-af04-4c3d-bce3-14952b5e42f1[61ec9d05-af04-4c3d-bce3-14952b5e42f1] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:11:03.080 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:03.080 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:03.081 - [orders(100)][61ec9d05-af04-4c3d-bce3-14952b5e42f1] - Node 61ec9d05-af04-4c3d-bce3-14952b5e42f1[61ec9d05-af04-4c3d-bce3-14952b5e42f1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:03.136 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 19:11:03.143 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:03.143 - [orders(100)][609c48e8-0970-4719-8298-4b73d9e9de8e] - Node 609c48e8-0970-4719-8298-4b73d9e9de8e[609c48e8-0970-4719-8298-4b73d9e9de8e] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:11:03.143 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:03.143 - [orders(100)][609c48e8-0970-4719-8298-4b73d9e9de8e] - Node 609c48e8-0970-4719-8298-4b73d9e9de8e[609c48e8-0970-4719-8298-4b73d9e9de8e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:03.143 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:03.144 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:03.158 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:11:03.178 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@78253adb error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@78253adb error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@78253adb error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-28 19:11:03.179 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@360f8d20 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@360f8d20 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@360f8d20 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 19:11:03.271 - [orders(100)][5b36ede9-93b1-4e84-84ac-57fe0266ca43] - Node 5b36ede9-93b1-4e84-84ac-57fe0266ca43[5b36ede9-93b1-4e84-84ac-57fe0266ca43] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:11:03.271 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:03.271 - [orders(100)][5b36ede9-93b1-4e84-84ac-57fe0266ca43] - Node 5b36ede9-93b1-4e84-84ac-57fe0266ca43[5b36ede9-93b1-4e84-84ac-57fe0266ca43] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:03.271 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:03.272 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:03.289 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:03.289 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:11:03.320 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@49782bdd error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@49782bdd error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@49782bdd error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 19:11:03.325 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:11:03.325 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:11:03.334 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:03.334 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:03.334 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:11:03.334 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:11:03.507 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 11 ms 
[WARN ] 2024-03-28 19:11:03.510 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:11:03.530 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:11:03.530 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:03.530 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:03.531 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:11:03.532 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:11:03.532 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 23 ms 
[WARN ] 2024-03-28 19:11:03.701 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:11:03.701 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:11:03.714 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:03.714 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:03.714 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:11:03.714 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:11:03.915 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 15 ms 
[INFO ] 2024-03-28 19:11:05.730 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:11:05.730 - [orders(100)][61ec9d05-af04-4c3d-bce3-14952b5e42f1] - Node 61ec9d05-af04-4c3d-bce3-14952b5e42f1[61ec9d05-af04-4c3d-bce3-14952b5e42f1] running status set to false 
[INFO ] 2024-03-28 19:11:05.730 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:11:05.730 - [orders(100)][609c48e8-0970-4719-8298-4b73d9e9de8e] - Node 609c48e8-0970-4719-8298-4b73d9e9de8e[609c48e8-0970-4719-8298-4b73d9e9de8e] running status set to false 
[INFO ] 2024-03-28 19:11:05.730 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:11:05.730 - [orders(100)][61ec9d05-af04-4c3d-bce3-14952b5e42f1] - Node 61ec9d05-af04-4c3d-bce3-14952b5e42f1[61ec9d05-af04-4c3d-bce3-14952b5e42f1] schema data cleaned 
[INFO ] 2024-03-28 19:11:05.730 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:11:05.730 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:11:05.730 - [orders(100)][61ec9d05-af04-4c3d-bce3-14952b5e42f1] - Node 61ec9d05-af04-4c3d-bce3-14952b5e42f1[61ec9d05-af04-4c3d-bce3-14952b5e42f1] monitor closed 
[INFO ] 2024-03-28 19:11:05.731 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 14 ms 
[INFO ] 2024-03-28 19:11:05.731 - [orders(100)][61ec9d05-af04-4c3d-bce3-14952b5e42f1] - Node 61ec9d05-af04-4c3d-bce3-14952b5e42f1[61ec9d05-af04-4c3d-bce3-14952b5e42f1] close complete, cost 14 ms 
[INFO ] 2024-03-28 19:11:05.731 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:11:05.731 - [orders(100)][609c48e8-0970-4719-8298-4b73d9e9de8e] - Node 609c48e8-0970-4719-8298-4b73d9e9de8e[609c48e8-0970-4719-8298-4b73d9e9de8e] schema data cleaned 
[INFO ] 2024-03-28 19:11:05.731 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 16 ms 
[INFO ] 2024-03-28 19:11:05.731 - [orders(100)][609c48e8-0970-4719-8298-4b73d9e9de8e] - Node 609c48e8-0970-4719-8298-4b73d9e9de8e[609c48e8-0970-4719-8298-4b73d9e9de8e] monitor closed 
[INFO ] 2024-03-28 19:11:05.740 - [orders(100)][609c48e8-0970-4719-8298-4b73d9e9de8e] - Node 609c48e8-0970-4719-8298-4b73d9e9de8e[609c48e8-0970-4719-8298-4b73d9e9de8e] close complete, cost 11 ms 
[INFO ] 2024-03-28 19:11:05.747 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-61ec9d05-af04-4c3d-bce3-14952b5e42f1 complete, cost 2698ms 
[INFO ] 2024-03-28 19:11:05.748 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-609c48e8-0970-4719-8298-4b73d9e9de8e complete, cost 2635ms 
[INFO ] 2024-03-28 19:11:05.839 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:11:05.839 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:11:05.839 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:11:05.840 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 0 ms 
[INFO ] 2024-03-28 19:11:05.845 - [orders(100)][5b36ede9-93b1-4e84-84ac-57fe0266ca43] - Node 5b36ede9-93b1-4e84-84ac-57fe0266ca43[5b36ede9-93b1-4e84-84ac-57fe0266ca43] running status set to false 
[INFO ] 2024-03-28 19:11:05.845 - [orders(100)][5b36ede9-93b1-4e84-84ac-57fe0266ca43] - Node 5b36ede9-93b1-4e84-84ac-57fe0266ca43[5b36ede9-93b1-4e84-84ac-57fe0266ca43] schema data cleaned 
[INFO ] 2024-03-28 19:11:05.847 - [orders(100)][5b36ede9-93b1-4e84-84ac-57fe0266ca43] - Node 5b36ede9-93b1-4e84-84ac-57fe0266ca43[5b36ede9-93b1-4e84-84ac-57fe0266ca43] monitor closed 
[INFO ] 2024-03-28 19:11:05.847 - [orders(100)][5b36ede9-93b1-4e84-84ac-57fe0266ca43] - Node 5b36ede9-93b1-4e84-84ac-57fe0266ca43[5b36ede9-93b1-4e84-84ac-57fe0266ca43] close complete, cost 2 ms 
[INFO ] 2024-03-28 19:11:06.049 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-5b36ede9-93b1-4e84-84ac-57fe0266ca43 complete, cost 2612ms 
[INFO ] 2024-03-28 19:11:09.632 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:09.635 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:09.638 - [orders(100)][ed622946-1b29-43e3-adde-0f33954ca55b] - Node ed622946-1b29-43e3-adde-0f33954ca55b[ed622946-1b29-43e3-adde-0f33954ca55b] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:11:09.638 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:09.645 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:09.645 - [orders(100)][ed622946-1b29-43e3-adde-0f33954ca55b] - Node ed622946-1b29-43e3-adde-0f33954ca55b[ed622946-1b29-43e3-adde-0f33954ca55b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:09.713 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:11:09.713 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@14fb3d77 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@14fb3d77 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@14fb3d77 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 19:11:09.863 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:11:09.864 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:11:09.871 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:09.872 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:09.872 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:11:09.872 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:11:09.872 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 9 ms 
[INFO ] 2024-03-28 19:11:10.716 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:10.717 - [orders(100)][c754513d-cbda-4249-88e9-e70e78ae25f1] - Node c754513d-cbda-4249-88e9-e70e78ae25f1[c754513d-cbda-4249-88e9-e70e78ae25f1] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:11:10.717 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:10.718 - [orders(100)][c754513d-cbda-4249-88e9-e70e78ae25f1] - Node c754513d-cbda-4249-88e9-e70e78ae25f1[c754513d-cbda-4249-88e9-e70e78ae25f1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:10.718 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:10.718 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:10.785 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:11:10.974 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@963de69 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@963de69 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@963de69 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 19:11:10.977 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:11:10.978 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:11:10.987 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:10.987 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:10.987 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:11:10.987 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:11:11.193 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 10 ms 
[INFO ] 2024-03-28 19:11:11.845 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:11.845 - [orders(100)][47ff9822-4d6e-4db2-bfef-266e82907bf2] - Node 47ff9822-4d6e-4db2-bfef-266e82907bf2[47ff9822-4d6e-4db2-bfef-266e82907bf2] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:11:11.845 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:11.845 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:11.845 - [orders(100)][47ff9822-4d6e-4db2-bfef-266e82907bf2] - Node 47ff9822-4d6e-4db2-bfef-266e82907bf2[47ff9822-4d6e-4db2-bfef-266e82907bf2] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:11.845 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:11.860 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:11:12.058 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3373e7ca error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3373e7ca error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3373e7ca error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 19:11:12.064 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:11:12.064 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:11:12.072 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:12.073 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:12.073 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:11:12.073 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:11:12.077 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 11 ms 
[INFO ] 2024-03-28 19:11:12.260 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:11:12.261 - [orders(100)][ed622946-1b29-43e3-adde-0f33954ca55b] - Node ed622946-1b29-43e3-adde-0f33954ca55b[ed622946-1b29-43e3-adde-0f33954ca55b] running status set to false 
[INFO ] 2024-03-28 19:11:12.261 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:11:12.261 - [orders(100)][ed622946-1b29-43e3-adde-0f33954ca55b] - Node ed622946-1b29-43e3-adde-0f33954ca55b[ed622946-1b29-43e3-adde-0f33954ca55b] schema data cleaned 
[INFO ] 2024-03-28 19:11:12.261 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:11:12.261 - [orders(100)][ed622946-1b29-43e3-adde-0f33954ca55b] - Node ed622946-1b29-43e3-adde-0f33954ca55b[ed622946-1b29-43e3-adde-0f33954ca55b] monitor closed 
[INFO ] 2024-03-28 19:11:12.261 - [orders(100)][ed622946-1b29-43e3-adde-0f33954ca55b] - Node ed622946-1b29-43e3-adde-0f33954ca55b[ed622946-1b29-43e3-adde-0f33954ca55b] close complete, cost 6 ms 
[INFO ] 2024-03-28 19:11:12.261 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 6 ms 
[INFO ] 2024-03-28 19:11:12.265 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-ed622946-1b29-43e3-adde-0f33954ca55b complete, cost 2687ms 
[INFO ] 2024-03-28 19:11:13.400 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:11:13.403 - [orders(100)][c754513d-cbda-4249-88e9-e70e78ae25f1] - Node c754513d-cbda-4249-88e9-e70e78ae25f1[c754513d-cbda-4249-88e9-e70e78ae25f1] running status set to false 
[INFO ] 2024-03-28 19:11:13.404 - [orders(100)][c754513d-cbda-4249-88e9-e70e78ae25f1] - Node c754513d-cbda-4249-88e9-e70e78ae25f1[c754513d-cbda-4249-88e9-e70e78ae25f1] schema data cleaned 
[INFO ] 2024-03-28 19:11:13.404 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:11:13.404 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:11:13.404 - [orders(100)][c754513d-cbda-4249-88e9-e70e78ae25f1] - Node c754513d-cbda-4249-88e9-e70e78ae25f1[c754513d-cbda-4249-88e9-e70e78ae25f1] monitor closed 
[INFO ] 2024-03-28 19:11:13.404 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 37 ms 
[INFO ] 2024-03-28 19:11:13.404 - [orders(100)][c754513d-cbda-4249-88e9-e70e78ae25f1] - Node c754513d-cbda-4249-88e9-e70e78ae25f1[c754513d-cbda-4249-88e9-e70e78ae25f1] close complete, cost 37 ms 
[INFO ] 2024-03-28 19:11:13.404 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-c754513d-cbda-4249-88e9-e70e78ae25f1 complete, cost 2768ms 
[INFO ] 2024-03-28 19:11:14.420 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:11:14.428 - [orders(100)][47ff9822-4d6e-4db2-bfef-266e82907bf2] - Node 47ff9822-4d6e-4db2-bfef-266e82907bf2[47ff9822-4d6e-4db2-bfef-266e82907bf2] running status set to false 
[INFO ] 2024-03-28 19:11:14.428 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:11:14.428 - [orders(100)][47ff9822-4d6e-4db2-bfef-266e82907bf2] - Node 47ff9822-4d6e-4db2-bfef-266e82907bf2[47ff9822-4d6e-4db2-bfef-266e82907bf2] schema data cleaned 
[INFO ] 2024-03-28 19:11:14.428 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:11:14.428 - [orders(100)][47ff9822-4d6e-4db2-bfef-266e82907bf2] - Node 47ff9822-4d6e-4db2-bfef-266e82907bf2[47ff9822-4d6e-4db2-bfef-266e82907bf2] monitor closed 
[INFO ] 2024-03-28 19:11:14.429 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 14 ms 
[INFO ] 2024-03-28 19:11:14.429 - [orders(100)][47ff9822-4d6e-4db2-bfef-266e82907bf2] - Node 47ff9822-4d6e-4db2-bfef-266e82907bf2[47ff9822-4d6e-4db2-bfef-266e82907bf2] close complete, cost 5 ms 
[INFO ] 2024-03-28 19:11:14.429 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-47ff9822-4d6e-4db2-bfef-266e82907bf2 complete, cost 2619ms 
[INFO ] 2024-03-28 19:11:17.481 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:17.481 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:17.481 - [orders(100)][1a87d05d-0458-4b17-ad02-ac92fbf8bba7] - Node 1a87d05d-0458-4b17-ad02-ac92fbf8bba7[1a87d05d-0458-4b17-ad02-ac92fbf8bba7] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:11:17.481 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:17.481 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:17.481 - [orders(100)][1a87d05d-0458-4b17-ad02-ac92fbf8bba7] - Node 1a87d05d-0458-4b17-ad02-ac92fbf8bba7[1a87d05d-0458-4b17-ad02-ac92fbf8bba7] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:17.547 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:11:17.710 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1871ebdc error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1871ebdc error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1871ebdc error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 19:11:17.711 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:11:17.712 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:11:17.720 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:17.720 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:17.720 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:11:17.720 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:11:17.921 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 10 ms 
[INFO ] 2024-03-28 19:11:19.513 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:19.514 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:19.514 - [orders(100)][49510169-223e-4b30-a7cb-c3bc3488ed61] - Node 49510169-223e-4b30-a7cb-c3bc3488ed61[49510169-223e-4b30-a7cb-c3bc3488ed61] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:11:19.515 - [orders(100)][49510169-223e-4b30-a7cb-c3bc3488ed61] - Node 49510169-223e-4b30-a7cb-c3bc3488ed61[49510169-223e-4b30-a7cb-c3bc3488ed61] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:19.515 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:19.515 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:19.594 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:11:19.595 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@47170a9b error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@47170a9b error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@47170a9b error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 19:11:19.761 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:11:19.761 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:11:19.770 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:19.770 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:19.770 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:11:19.770 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:11:19.770 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 10 ms 
[INFO ] 2024-03-28 19:11:20.110 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:11:20.110 - [orders(100)][1a87d05d-0458-4b17-ad02-ac92fbf8bba7] - Node 1a87d05d-0458-4b17-ad02-ac92fbf8bba7[1a87d05d-0458-4b17-ad02-ac92fbf8bba7] running status set to false 
[INFO ] 2024-03-28 19:11:20.110 - [orders(100)][1a87d05d-0458-4b17-ad02-ac92fbf8bba7] - Node 1a87d05d-0458-4b17-ad02-ac92fbf8bba7[1a87d05d-0458-4b17-ad02-ac92fbf8bba7] schema data cleaned 
[INFO ] 2024-03-28 19:11:20.110 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:11:20.110 - [orders(100)][1a87d05d-0458-4b17-ad02-ac92fbf8bba7] - Node 1a87d05d-0458-4b17-ad02-ac92fbf8bba7[1a87d05d-0458-4b17-ad02-ac92fbf8bba7] monitor closed 
[INFO ] 2024-03-28 19:11:20.111 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:11:20.121 - [orders(100)][1a87d05d-0458-4b17-ad02-ac92fbf8bba7] - Node 1a87d05d-0458-4b17-ad02-ac92fbf8bba7[1a87d05d-0458-4b17-ad02-ac92fbf8bba7] close complete, cost 6 ms 
[INFO ] 2024-03-28 19:11:20.121 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 6 ms 
[INFO ] 2024-03-28 19:11:20.327 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-1a87d05d-0458-4b17-ad02-ac92fbf8bba7 complete, cost 2695ms 
[INFO ] 2024-03-28 19:11:22.142 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:11:22.142 - [orders(100)][49510169-223e-4b30-a7cb-c3bc3488ed61] - Node 49510169-223e-4b30-a7cb-c3bc3488ed61[49510169-223e-4b30-a7cb-c3bc3488ed61] running status set to false 
[INFO ] 2024-03-28 19:11:22.143 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:11:22.143 - [orders(100)][49510169-223e-4b30-a7cb-c3bc3488ed61] - Node 49510169-223e-4b30-a7cb-c3bc3488ed61[49510169-223e-4b30-a7cb-c3bc3488ed61] schema data cleaned 
[INFO ] 2024-03-28 19:11:22.143 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:11:22.143 - [orders(100)][49510169-223e-4b30-a7cb-c3bc3488ed61] - Node 49510169-223e-4b30-a7cb-c3bc3488ed61[49510169-223e-4b30-a7cb-c3bc3488ed61] monitor closed 
[INFO ] 2024-03-28 19:11:22.144 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 3 ms 
[INFO ] 2024-03-28 19:11:22.145 - [orders(100)][49510169-223e-4b30-a7cb-c3bc3488ed61] - Node 49510169-223e-4b30-a7cb-c3bc3488ed61[49510169-223e-4b30-a7cb-c3bc3488ed61] close complete, cost 2 ms 
[INFO ] 2024-03-28 19:11:22.346 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-49510169-223e-4b30-a7cb-c3bc3488ed61 complete, cost 2694ms 
[INFO ] 2024-03-28 19:11:36.261 - [orders(100)][be6154d0-8c5f-4de2-885f-e2108ac6a760] - Node be6154d0-8c5f-4de2-885f-e2108ac6a760[be6154d0-8c5f-4de2-885f-e2108ac6a760] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:11:36.262 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:36.263 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:36.265 - [orders(100)][be6154d0-8c5f-4de2-885f-e2108ac6a760] - Node be6154d0-8c5f-4de2-885f-e2108ac6a760[be6154d0-8c5f-4de2-885f-e2108ac6a760] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:36.265 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:36.265 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:36.342 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:11:36.342 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@12180eea error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@12180eea error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@12180eea error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 19:11:36.491 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:11:36.492 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:11:36.501 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:36.501 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:36.501 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:11:36.501 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:11:36.501 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 11 ms 
[INFO ] 2024-03-28 19:11:38.915 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:11:38.921 - [orders(100)][be6154d0-8c5f-4de2-885f-e2108ac6a760] - Node be6154d0-8c5f-4de2-885f-e2108ac6a760[be6154d0-8c5f-4de2-885f-e2108ac6a760] running status set to false 
[INFO ] 2024-03-28 19:11:38.921 - [orders(100)][be6154d0-8c5f-4de2-885f-e2108ac6a760] - Node be6154d0-8c5f-4de2-885f-e2108ac6a760[be6154d0-8c5f-4de2-885f-e2108ac6a760] schema data cleaned 
[INFO ] 2024-03-28 19:11:38.921 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:11:38.922 - [orders(100)][be6154d0-8c5f-4de2-885f-e2108ac6a760] - Node be6154d0-8c5f-4de2-885f-e2108ac6a760[be6154d0-8c5f-4de2-885f-e2108ac6a760] monitor closed 
[INFO ] 2024-03-28 19:11:38.922 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:11:38.922 - [orders(100)][be6154d0-8c5f-4de2-885f-e2108ac6a760] - Node be6154d0-8c5f-4de2-885f-e2108ac6a760[be6154d0-8c5f-4de2-885f-e2108ac6a760] close complete, cost 34 ms 
[INFO ] 2024-03-28 19:11:38.922 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 38 ms 
[INFO ] 2024-03-28 19:11:39.133 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-be6154d0-8c5f-4de2-885f-e2108ac6a760 complete, cost 2725ms 
[INFO ] 2024-03-28 19:11:43.577 - [orders(100)][3ef8949f-f331-4655-9b1c-199756362368] - Node 3ef8949f-f331-4655-9b1c-199756362368[3ef8949f-f331-4655-9b1c-199756362368] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:11:43.577 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:43.577 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:11:43.577 - [orders(100)][3ef8949f-f331-4655-9b1c-199756362368] - Node 3ef8949f-f331-4655-9b1c-199756362368[3ef8949f-f331-4655-9b1c-199756362368] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:43.577 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:43.577 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:11:43.633 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:11:43.809 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@479ee1e5 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@479ee1e5 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@479ee1e5 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 19:11:43.814 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:11:43.816 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:11:43.826 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:43.826 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:11:43.826 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:11:43.826 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:11:44.027 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 14 ms 
[INFO ] 2024-03-28 19:11:46.201 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:11:46.202 - [orders(100)][3ef8949f-f331-4655-9b1c-199756362368] - Node 3ef8949f-f331-4655-9b1c-199756362368[3ef8949f-f331-4655-9b1c-199756362368] running status set to false 
[INFO ] 2024-03-28 19:11:46.202 - [orders(100)][3ef8949f-f331-4655-9b1c-199756362368] - Node 3ef8949f-f331-4655-9b1c-199756362368[3ef8949f-f331-4655-9b1c-199756362368] schema data cleaned 
[INFO ] 2024-03-28 19:11:46.202 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:11:46.202 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:11:46.202 - [orders(100)][3ef8949f-f331-4655-9b1c-199756362368] - Node 3ef8949f-f331-4655-9b1c-199756362368[3ef8949f-f331-4655-9b1c-199756362368] monitor closed 
[INFO ] 2024-03-28 19:11:46.206 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 10 ms 
[INFO ] 2024-03-28 19:11:46.206 - [orders(100)][3ef8949f-f331-4655-9b1c-199756362368] - Node 3ef8949f-f331-4655-9b1c-199756362368[3ef8949f-f331-4655-9b1c-199756362368] close complete, cost 6 ms 
[INFO ] 2024-03-28 19:11:46.206 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-3ef8949f-f331-4655-9b1c-199756362368 complete, cost 2692ms 
[INFO ] 2024-03-28 19:12:02.659 - [orders(100)][a40a396c-b9d0-4ee0-88bf-d56a1f0bf808] - Node a40a396c-b9d0-4ee0-88bf-d56a1f0bf808[a40a396c-b9d0-4ee0-88bf-d56a1f0bf808] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:12:02.660 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:12:02.660 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:12:02.660 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 19:12:02.661 - [orders(100)][a40a396c-b9d0-4ee0-88bf-d56a1f0bf808] - Node a40a396c-b9d0-4ee0-88bf-d56a1f0bf808[a40a396c-b9d0-4ee0-88bf-d56a1f0bf808] preload schema finished, cost 2 ms 
[INFO ] 2024-03-28 19:12:02.661 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 19:12:02.710 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:12:02.884 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@af47752 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@af47752 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@af47752 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 19:12:02.884 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:12:02.888 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:12:02.896 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:12:02.896 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:12:02.896 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:12:02.896 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:12:03.096 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 10 ms 
[INFO ] 2024-03-28 19:12:05.317 - [orders(100)][a40a396c-b9d0-4ee0-88bf-d56a1f0bf808] - Node a40a396c-b9d0-4ee0-88bf-d56a1f0bf808[a40a396c-b9d0-4ee0-88bf-d56a1f0bf808] running status set to false 
[INFO ] 2024-03-28 19:12:05.322 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:12:05.322 - [orders(100)][a40a396c-b9d0-4ee0-88bf-d56a1f0bf808] - Node a40a396c-b9d0-4ee0-88bf-d56a1f0bf808[a40a396c-b9d0-4ee0-88bf-d56a1f0bf808] schema data cleaned 
[INFO ] 2024-03-28 19:12:05.322 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:12:05.323 - [orders(100)][a40a396c-b9d0-4ee0-88bf-d56a1f0bf808] - Node a40a396c-b9d0-4ee0-88bf-d56a1f0bf808[a40a396c-b9d0-4ee0-88bf-d56a1f0bf808] monitor closed 
[INFO ] 2024-03-28 19:12:05.323 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:12:05.323 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 39 ms 
[INFO ] 2024-03-28 19:12:05.323 - [orders(100)][a40a396c-b9d0-4ee0-88bf-d56a1f0bf808] - Node a40a396c-b9d0-4ee0-88bf-d56a1f0bf808[a40a396c-b9d0-4ee0-88bf-d56a1f0bf808] close complete, cost 38 ms 
[INFO ] 2024-03-28 19:12:05.527 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-a40a396c-b9d0-4ee0-88bf-d56a1f0bf808 complete, cost 2745ms 
[INFO ] 2024-03-28 19:12:07.324 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:12:07.324 - [orders(100)][e71ecabc-a849-4c73-b3df-07d3df29e4a4] - Node e71ecabc-a849-4c73-b3df-07d3df29e4a4[e71ecabc-a849-4c73-b3df-07d3df29e4a4] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:12:07.324 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:12:07.324 - [orders(100)][e71ecabc-a849-4c73-b3df-07d3df29e4a4] - Node e71ecabc-a849-4c73-b3df-07d3df29e4a4[e71ecabc-a849-4c73-b3df-07d3df29e4a4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:12:07.325 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:12:07.325 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:12:07.373 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:12:07.537 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@15cbf3a3 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@15cbf3a3 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@15cbf3a3 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 19:12:07.541 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:12:07.556 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:12:07.556 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:12:07.557 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:12:07.557 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:12:07.557 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:12:07.758 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 18 ms 
[INFO ] 2024-03-28 19:12:09.980 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:12:09.989 - [orders(100)][e71ecabc-a849-4c73-b3df-07d3df29e4a4] - Node e71ecabc-a849-4c73-b3df-07d3df29e4a4[e71ecabc-a849-4c73-b3df-07d3df29e4a4] running status set to false 
[INFO ] 2024-03-28 19:12:09.989 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:12:09.989 - [orders(100)][e71ecabc-a849-4c73-b3df-07d3df29e4a4] - Node e71ecabc-a849-4c73-b3df-07d3df29e4a4[e71ecabc-a849-4c73-b3df-07d3df29e4a4] schema data cleaned 
[INFO ] 2024-03-28 19:12:09.989 - [orders(100)][e71ecabc-a849-4c73-b3df-07d3df29e4a4] - Node e71ecabc-a849-4c73-b3df-07d3df29e4a4[e71ecabc-a849-4c73-b3df-07d3df29e4a4] monitor closed 
[INFO ] 2024-03-28 19:12:09.989 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:12:09.990 - [orders(100)][e71ecabc-a849-4c73-b3df-07d3df29e4a4] - Node e71ecabc-a849-4c73-b3df-07d3df29e4a4[e71ecabc-a849-4c73-b3df-07d3df29e4a4] close complete, cost 12 ms 
[INFO ] 2024-03-28 19:12:09.990 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 16 ms 
[INFO ] 2024-03-28 19:12:10.199 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-e71ecabc-a849-4c73-b3df-07d3df29e4a4 complete, cost 2727ms 
[INFO ] 2024-03-28 19:12:13.542 - [orders(100)][55e76401-a600-48e0-af40-0640df1302f9] - Node 55e76401-a600-48e0-af40-0640df1302f9[55e76401-a600-48e0-af40-0640df1302f9] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:12:13.542 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:12:13.542 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:12:13.542 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:12:13.542 - [orders(100)][55e76401-a600-48e0-af40-0640df1302f9] - Node 55e76401-a600-48e0-af40-0640df1302f9[55e76401-a600-48e0-af40-0640df1302f9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:12:13.542 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:12:13.593 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:12:13.771 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5faad1a9 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5faad1a9 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5faad1a9 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 19:12:13.775 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:12:13.775 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:12:13.783 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:12:13.783 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:12:13.783 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:12:13.783 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:12:13.784 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 10 ms 
[INFO ] 2024-03-28 19:12:16.171 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:12:16.173 - [orders(100)][55e76401-a600-48e0-af40-0640df1302f9] - Node 55e76401-a600-48e0-af40-0640df1302f9[55e76401-a600-48e0-af40-0640df1302f9] running status set to false 
[INFO ] 2024-03-28 19:12:16.173 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:12:16.175 - [orders(100)][55e76401-a600-48e0-af40-0640df1302f9] - Node 55e76401-a600-48e0-af40-0640df1302f9[55e76401-a600-48e0-af40-0640df1302f9] schema data cleaned 
[INFO ] 2024-03-28 19:12:16.175 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:12:16.175 - [orders(100)][55e76401-a600-48e0-af40-0640df1302f9] - Node 55e76401-a600-48e0-af40-0640df1302f9[55e76401-a600-48e0-af40-0640df1302f9] monitor closed 
[INFO ] 2024-03-28 19:12:16.176 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 9 ms 
[INFO ] 2024-03-28 19:12:16.176 - [orders(100)][55e76401-a600-48e0-af40-0640df1302f9] - Node 55e76401-a600-48e0-af40-0640df1302f9[55e76401-a600-48e0-af40-0640df1302f9] close complete, cost 10 ms 
[INFO ] 2024-03-28 19:12:16.387 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-55e76401-a600-48e0-af40-0640df1302f9 complete, cost 2697ms 
[INFO ] 2024-03-28 19:24:29.276 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:24:29.277 - [orders(100)][dc372a60-cf48-4521-a360-e0969fdf88f1] - Node dc372a60-cf48-4521-a360-e0969fdf88f1[dc372a60-cf48-4521-a360-e0969fdf88f1] start preload schema,table counts: 0 
[INFO ] 2024-03-28 19:24:29.277 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] start preload schema,table counts: 1 
[INFO ] 2024-03-28 19:24:29.277 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 19:24:29.277 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] preload schema finished, cost 3 ms 
[INFO ] 2024-03-28 19:24:29.277 - [orders(100)][dc372a60-cf48-4521-a360-e0969fdf88f1] - Node dc372a60-cf48-4521-a360-e0969fdf88f1[dc372a60-cf48-4521-a360-e0969fdf88f1] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 19:24:29.400 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 19:24:29.404 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7f0a6cf2 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7f0a6cf2 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7f0a6cf2 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 19:24:29.549 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 19:24:29.549 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] running status set to false 
[INFO ] 2024-03-28 19:24:29.559 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:24:29.559 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-165499d4-8571-4ff0-8378-74055eacd207 
[INFO ] 2024-03-28 19:24:29.559 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] schema data cleaned 
[INFO ] 2024-03-28 19:24:29.559 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] monitor closed 
[INFO ] 2024-03-28 19:24:29.761 - [orders(100)][Order Details] - Node Order Details[165499d4-8571-4ff0-8378-74055eacd207] close complete, cost 11 ms 
[INFO ] 2024-03-28 19:24:31.931 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] running status set to false 
[INFO ] 2024-03-28 19:24:31.931 - [orders(100)][dc372a60-cf48-4521-a360-e0969fdf88f1] - Node dc372a60-cf48-4521-a360-e0969fdf88f1[dc372a60-cf48-4521-a360-e0969fdf88f1] running status set to false 
[INFO ] 2024-03-28 19:24:31.931 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] schema data cleaned 
[INFO ] 2024-03-28 19:24:31.931 - [orders(100)][dc372a60-cf48-4521-a360-e0969fdf88f1] - Node dc372a60-cf48-4521-a360-e0969fdf88f1[dc372a60-cf48-4521-a360-e0969fdf88f1] schema data cleaned 
[INFO ] 2024-03-28 19:24:31.932 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] monitor closed 
[INFO ] 2024-03-28 19:24:31.932 - [orders(100)][dc372a60-cf48-4521-a360-e0969fdf88f1] - Node dc372a60-cf48-4521-a360-e0969fdf88f1[dc372a60-cf48-4521-a360-e0969fdf88f1] monitor closed 
[INFO ] 2024-03-28 19:24:31.932 - [orders(100)][Order Details] - Node Order Details[6974e28f-4f06-4275-9836-591767c54fbf] close complete, cost 6 ms 
[INFO ] 2024-03-28 19:24:31.932 - [orders(100)][dc372a60-cf48-4521-a360-e0969fdf88f1] - Node dc372a60-cf48-4521-a360-e0969fdf88f1[dc372a60-cf48-4521-a360-e0969fdf88f1] close complete, cost 1 ms 
[INFO ] 2024-03-28 19:24:32.135 - [orders(100)] - load tapTable task 6605501c97124471315d5a75-dc372a60-cf48-4521-a360-e0969fdf88f1 complete, cost 3018ms 
