[INFO ] 2024-07-11 02:06:30.407 - [任务 51] - Start task milestones: 668ba3681465df7a0893fa5d(任务 51) 
[INFO ] 2024-07-11 02:06:30.891 - [任务 51] - Task initialization... 
[INFO ] 2024-07-11 02:06:33.252 - [任务 51] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-11 02:06:33.819 - [任务 51] - The engine receives 任务 51 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-11 02:11:27.926 - [任务 51] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-11 02:11:27.928 - [任务 51] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1d712b5e 
[INFO ] 2024-07-11 02:11:27.928 - [任务 51] - Stop task milestones: 668ba3681465df7a0893fa5d(任务 51)  
[INFO ] 2024-07-11 02:11:28.048 - [任务 51] - Stopped task aspect(s) 
[INFO ] 2024-07-11 02:11:28.048 - [任务 51] - Snapshot order controller have been removed 
[INFO ] 2024-07-11 02:11:28.070 - [任务 51] - Remove memory task client succeed, task: 任务 51[668ba3681465df7a0893fa5d] 
[INFO ] 2024-07-11 02:11:28.280 - [任务 51] - Destroy memory task client cache succeed, task: 任务 51[668ba3681465df7a0893fa5d] 
[INFO ] 2024-07-11 02:13:40.999 - [任务 51] - Task initialization... 
[INFO ] 2024-07-11 02:13:41.205 - [任务 51] - Start task milestones: 668ba3681465df7a0893fa5d(任务 51) 
[INFO ] 2024-07-11 02:13:42.310 - [任务 51] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-11 02:13:42.310 - [任务 51] - The engine receives 任务 51 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-11 02:13:59.731 - [任务 51] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-11 02:13:59.855 - [任务 51] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4bc8bf1d 
[INFO ] 2024-07-11 02:13:59.856 - [任务 51] - Stop task milestones: 668ba3681465df7a0893fa5d(任务 51)  
[INFO ] 2024-07-11 02:13:59.877 - [任务 51] - Stopped task aspect(s) 
[INFO ] 2024-07-11 02:13:59.878 - [任务 51] - Snapshot order controller have been removed 
[INFO ] 2024-07-11 02:13:59.926 - [任务 51] - Remove memory task client succeed, task: 任务 51[668ba3681465df7a0893fa5d] 
[INFO ] 2024-07-11 02:13:59.927 - [任务 51] - Destroy memory task client cache succeed, task: 任务 51[668ba3681465df7a0893fa5d] 
[INFO ] 2024-07-11 02:15:19.898 - [任务 51] - Task initialization... 
[INFO ] 2024-07-11 02:15:20.105 - [任务 51] - Start task milestones: 668ba3681465df7a0893fa5d(任务 51) 
[INFO ] 2024-07-11 02:15:21.302 - [任务 51] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-11 02:15:21.302 - [任务 51] - The engine receives 任务 51 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-11 02:15:24.052 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] start preload schema,table counts: 1 
[INFO ] 2024-07-11 02:15:24.054 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] start preload schema,table counts: 1 
[INFO ] 2024-07-11 02:15:24.054 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] preload schema finished, cost 0 ms 
[INFO ] 2024-07-11 02:15:28.531 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] preload schema finished, cost 1 ms 
[INFO ] 2024-07-11 02:15:29.833 - [任务 51][SouceMysql - Copy] - Source node "SouceMysql - Copy" read batch size: 100 
[INFO ] 2024-07-11 02:15:29.835 - [任务 51][SouceMysql - Copy] - Source node "SouceMysql - Copy" event queue capacity: 200 
[INFO ] 2024-07-11 02:15:29.841 - [任务 51][TargetMysql] - Node(TargetMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-11 02:15:29.844 - [任务 51][SouceMysql - Copy] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-11 02:15:29.849 - [任务 51][TargetMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-11 02:15:29.858 - [任务 51][SouceMysql - Copy] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":92396103,"gtidSet":""} 
[INFO ] 2024-07-11 02:15:29.918 - [任务 51][SouceMysql - Copy] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-11 02:15:29.923 - [任务 51][TargetMysql] - Table "test.POLICY" exists, skip auto create table 
[INFO ] 2024-07-11 02:15:29.927 - [任务 51][TargetMysql] - The table POLICY has already exist. 
[INFO ] 2024-07-11 02:15:29.957 - [任务 51][SouceMysql - Copy] - Initial sync started 
[INFO ] 2024-07-11 02:15:29.958 - [任务 51][SouceMysql - Copy] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-11 02:15:30.012 - [任务 51][SouceMysql - Copy] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-11 02:15:30.012 - [任务 51][SouceMysql - Copy] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-11 02:15:30.196 - [任务 51][SouceMysql - Copy] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-11 02:15:30.200 - [任务 51][SouceMysql - Copy] - Initial sync completed 
[INFO ] 2024-07-11 02:15:30.200 - [任务 51][SouceMysql - Copy] - Incremental sync starting... 
[INFO ] 2024-07-11 02:15:30.211 - [任务 51][SouceMysql - Copy] - Initial sync completed 
[INFO ] 2024-07-11 02:15:30.212 - [任务 51][SouceMysql - Copy] - Starting stream read, table list: [POLICY], offset: {"filename":"binlog.000032","position":92396103,"gtidSet":""} 
[INFO ] 2024-07-11 02:15:30.300 - [任务 51][SouceMysql - Copy] - Starting mysql cdc, server name: b8f416c6-049e-4c41-9032-9711836d23df 
[INFO ] 2024-07-11 02:15:30.300 - [任务 51][SouceMysql - Copy] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 462207356
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b8f416c6-049e-4c41-9032-9711836d23df
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b8f416c6-049e-4c41-9032-9711836d23df
  database.hostname: localhost
  database.password: ********
  name: b8f416c6-049e-4c41-9032-9711836d23df
  pdk.offset.string: {"name":"b8f416c6-049e-4c41-9032-9711836d23df","offset":{"{\"server\":\"b8f416c6-049e-4c41-9032-9711836d23df\"}":"{\"file\":\"binlog.000032\",\"pos\":92396103,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test2.POLICY
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test2
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-11 02:15:30.493 - [任务 51][SouceMysql - Copy] - Connector Mysql incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-11 02:24:09.088 - [任务 51][SouceMysql - Copy] - Mysql binlog reader stopped 
[INFO ] 2024-07-11 02:24:09.088 - [任务 51][SouceMysql - Copy] - Incremental sync completed 
[INFO ] 2024-07-11 02:24:09.100 - [任务 51][SouceMysql - Copy] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-11 02:24:09.100 - [任务 51][SouceMysql - Copy] - java.lang.RuntimeException: java.net.SocketException: Socket closed <-- Error Message -->
java.lang.RuntimeException: java.net.SocketException: Socket closed

<-- Simple Stack Trace -->
Caused by: java.net.SocketException: Socket closed
	java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:118)
	java.net.SocketOutputStream.write(SocketOutputStream.java:143)
	com.github.shyiko.mysql.binlog.io.ByteArrayOutputStream.write(ByteArrayOutputStream.java:83)
	com.github.shyiko.mysql.binlog.network.protocol.PacketChannel.write(PacketChannel.java:88)
	com.github.shyiko.mysql.binlog.network.Authenticator.authenticate(Authenticator.java:69)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.net.SocketException: Socket closed
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.net.SocketException: Socket closed
	at io.tapdata.common.exception.AbstractExceptionCollector.collectCdcConfigInvalid(AbstractExceptionCollector.java:63)
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:153)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:360)
	at io.tapdata.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:602)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.net.SocketException: Socket closed
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:118)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:143)
	at com.github.shyiko.mysql.binlog.io.ByteArrayOutputStream.write(ByteArrayOutputStream.java:83)
	at com.github.shyiko.mysql.binlog.network.protocol.PacketChannel.write(PacketChannel.java:88)
	at com.github.shyiko.mysql.binlog.network.Authenticator.authenticate(Authenticator.java:69)
	at com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:591)
	at com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	... 1 more

[INFO ] 2024-07-11 02:24:09.179 - [任务 51][SouceMysql - Copy] - Job suspend in error handle 
[INFO ] 2024-07-11 02:24:09.290 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] running status set to false 
[INFO ] 2024-07-11 02:24:09.308 - [任务 51][SouceMysql - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode-59b7c2a4-b4d8-43f3-a06a-846b2fd34632 
[INFO ] 2024-07-11 02:24:09.308 - [任务 51][SouceMysql - Copy] - PDK connector node released: HazelcastSourcePdkDataNode-59b7c2a4-b4d8-43f3-a06a-846b2fd34632 
[INFO ] 2024-07-11 02:24:09.309 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] schema data cleaned 
[INFO ] 2024-07-11 02:24:09.309 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] monitor closed 
[INFO ] 2024-07-11 02:24:09.313 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] close complete, cost 51 ms 
[INFO ] 2024-07-11 02:24:09.349 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] running status set to false 
[INFO ] 2024-07-11 02:24:09.372 - [任务 51][TargetMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-03647ff8-af0e-4d14-ae45-7c7eb19f63a4 
[INFO ] 2024-07-11 02:24:09.372 - [任务 51][TargetMysql] - PDK connector node released: HazelcastTargetPdkDataNode-03647ff8-af0e-4d14-ae45-7c7eb19f63a4 
[INFO ] 2024-07-11 02:24:09.373 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] schema data cleaned 
[INFO ] 2024-07-11 02:24:09.374 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] monitor closed 
[INFO ] 2024-07-11 02:24:09.382 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] close complete, cost 63 ms 
[INFO ] 2024-07-11 02:24:13.642 - [任务 51] - Task [任务 51] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-11 02:24:13.642 - [任务 51] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-11 02:24:13.642 - [任务 51] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@597c6c86 
[INFO ] 2024-07-11 02:24:13.753 - [任务 51] - Stop task milestones: 668ba3681465df7a0893fa5d(任务 51)  
[INFO ] 2024-07-11 02:24:13.762 - [任务 51] - Stopped task aspect(s) 
[INFO ] 2024-07-11 02:24:13.762 - [任务 51] - Snapshot order controller have been removed 
[INFO ] 2024-07-11 02:24:13.775 - [任务 51] - Remove memory task client succeed, task: 任务 51[668ba3681465df7a0893fa5d] 
[INFO ] 2024-07-11 02:24:13.777 - [任务 51] - Destroy memory task client cache succeed, task: 任务 51[668ba3681465df7a0893fa5d] 
[INFO ] 2024-07-11 14:49:08.546 - [任务 51] - Start task milestones: 668ba3681465df7a0893fa5d(任务 51) 
[INFO ] 2024-07-11 14:49:08.588 - [任务 51] - Task initialization... 
[INFO ] 2024-07-11 14:49:08.785 - [任务 51] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-11 14:49:08.787 - [任务 51] - The engine receives 任务 51 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-11 14:49:08.853 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] start preload schema,table counts: 1 
[INFO ] 2024-07-11 14:49:08.853 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] start preload schema,table counts: 1 
[INFO ] 2024-07-11 14:49:08.856 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] preload schema finished, cost 0 ms 
[INFO ] 2024-07-11 14:49:08.861 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] preload schema finished, cost 0 ms 
[INFO ] 2024-07-11 14:49:09.300 - [任务 51][TargetMysql] - Node(TargetMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-11 14:49:09.300 - [任务 51][TargetMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-11 14:49:09.428 - [任务 51][SouceMysql - Copy] - Source node "SouceMysql - Copy" read batch size: 100 
[INFO ] 2024-07-11 14:49:09.428 - [任务 51][SouceMysql - Copy] - Source node "SouceMysql - Copy" event queue capacity: 200 
[INFO ] 2024-07-11 14:49:09.439 - [任务 51][SouceMysql - Copy] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-11 14:49:09.441 - [任务 51][SouceMysql - Copy] - batch offset found: {"POLICY":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"name":"b8f416c6-049e-4c41-9032-9711836d23df","offset":{"{\"server\":\"b8f416c6-049e-4c41-9032-9711836d23df\"}":"{\"ts_sec\":1720635331,\"file\":\"binlog.000032\",\"pos\":92396103,\"server_id\":1}"}} 
[INFO ] 2024-07-11 14:49:09.441 - [任务 51][SouceMysql - Copy] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-11 14:49:09.481 - [任务 51][SouceMysql - Copy] - Incremental sync starting... 
[INFO ] 2024-07-11 14:49:09.481 - [任务 51][SouceMysql - Copy] - Initial sync completed 
[INFO ] 2024-07-11 14:49:09.502 - [任务 51][SouceMysql - Copy] - Starting stream read, table list: [POLICY], offset: {"name":"b8f416c6-049e-4c41-9032-9711836d23df","offset":{"{\"server\":\"b8f416c6-049e-4c41-9032-9711836d23df\"}":"{\"ts_sec\":1720635331,\"file\":\"binlog.000032\",\"pos\":92396103,\"server_id\":1}"}} 
[INFO ] 2024-07-11 14:49:09.505 - [任务 51][SouceMysql - Copy] - Starting mysql cdc, server name: b8f416c6-049e-4c41-9032-9711836d23df 
[INFO ] 2024-07-11 14:49:09.555 - [任务 51][SouceMysql - Copy] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1687801673
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b8f416c6-049e-4c41-9032-9711836d23df
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b8f416c6-049e-4c41-9032-9711836d23df
  database.hostname: localhost
  database.password: ********
  name: b8f416c6-049e-4c41-9032-9711836d23df
  pdk.offset.string: {"name":"b8f416c6-049e-4c41-9032-9711836d23df","offset":{"{\"server\":\"b8f416c6-049e-4c41-9032-9711836d23df\"}":"{\"ts_sec\":1720635331,\"file\":\"binlog.000032\",\"pos\":92396103,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test2.POLICY
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test2
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-11 14:49:09.555 - [任务 51][SouceMysql - Copy] - Connector Mysql incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-11 14:54:46.588 - [任务 51][SouceMysql - Copy] - Mysql binlog reader stopped 
[INFO ] 2024-07-11 14:54:46.600 - [任务 51][SouceMysql - Copy] - Incremental sync completed 
[INFO ] 2024-07-11 14:54:46.601 - [任务 51][SouceMysql - Copy] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-11 14:54:46.657 - [任务 51][SouceMysql - Copy] - java.lang.RuntimeException: java.net.SocketException: Socket closed <-- Error Message -->
java.lang.RuntimeException: java.net.SocketException: Socket closed

<-- Simple Stack Trace -->
Caused by: java.net.SocketException: Socket closed
	java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:118)
	java.net.SocketOutputStream.write(SocketOutputStream.java:143)
	com.github.shyiko.mysql.binlog.io.ByteArrayOutputStream.write(ByteArrayOutputStream.java:83)
	com.github.shyiko.mysql.binlog.network.protocol.PacketChannel.write(PacketChannel.java:88)
	com.github.shyiko.mysql.binlog.network.Authenticator.authenticate(Authenticator.java:69)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.net.SocketException: Socket closed
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.net.SocketException: Socket closed
	at io.tapdata.common.exception.AbstractExceptionCollector.collectCdcConfigInvalid(AbstractExceptionCollector.java:63)
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:153)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:360)
	at io.tapdata.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:602)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.net.SocketException: Socket closed
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:118)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:143)
	at com.github.shyiko.mysql.binlog.io.ByteArrayOutputStream.write(ByteArrayOutputStream.java:83)
	at com.github.shyiko.mysql.binlog.network.protocol.PacketChannel.write(PacketChannel.java:88)
	at com.github.shyiko.mysql.binlog.network.Authenticator.authenticate(Authenticator.java:69)
	at com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:591)
	at com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	... 1 more

[INFO ] 2024-07-11 14:54:46.657 - [任务 51][SouceMysql - Copy] - Job suspend in error handle 
[INFO ] 2024-07-11 14:54:47.011 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] running status set to false 
[INFO ] 2024-07-11 14:54:47.011 - [任务 51][SouceMysql - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode-59b7c2a4-b4d8-43f3-a06a-846b2fd34632 
[INFO ] 2024-07-11 14:54:47.011 - [任务 51][SouceMysql - Copy] - PDK connector node released: HazelcastSourcePdkDataNode-59b7c2a4-b4d8-43f3-a06a-846b2fd34632 
[INFO ] 2024-07-11 14:54:47.011 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] schema data cleaned 
[INFO ] 2024-07-11 14:54:47.013 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] monitor closed 
[INFO ] 2024-07-11 14:54:47.017 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] close complete, cost 23 ms 
[INFO ] 2024-07-11 14:54:47.017 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] running status set to false 
[INFO ] 2024-07-11 14:54:47.066 - [任务 51][TargetMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-03647ff8-af0e-4d14-ae45-7c7eb19f63a4 
[INFO ] 2024-07-11 14:54:47.066 - [任务 51][TargetMysql] - PDK connector node released: HazelcastTargetPdkDataNode-03647ff8-af0e-4d14-ae45-7c7eb19f63a4 
[INFO ] 2024-07-11 14:54:47.066 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] schema data cleaned 
[INFO ] 2024-07-11 14:54:47.066 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] monitor closed 
[INFO ] 2024-07-11 14:54:47.066 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] close complete, cost 49 ms 
[INFO ] 2024-07-11 14:54:51.441 - [任务 51] - Task [任务 51] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-11 14:54:51.442 - [任务 51] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-11 14:54:51.451 - [任务 51] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@493e7344 
[INFO ] 2024-07-11 14:54:51.452 - [任务 51] - Stop task milestones: 668ba3681465df7a0893fa5d(任务 51)  
[INFO ] 2024-07-11 14:54:51.584 - [任务 51] - Stopped task aspect(s) 
[INFO ] 2024-07-11 14:54:51.584 - [任务 51] - Snapshot order controller have been removed 
[INFO ] 2024-07-11 14:54:51.612 - [任务 51] - Remove memory task client succeed, task: 任务 51[668ba3681465df7a0893fa5d] 
[INFO ] 2024-07-11 14:54:51.618 - [任务 51] - Destroy memory task client cache succeed, task: 任务 51[668ba3681465df7a0893fa5d] 
[INFO ] 2024-07-11 14:55:14.146 - [任务 51] - Start task milestones: 668ba3681465df7a0893fa5d(任务 51) 
[INFO ] 2024-07-11 14:55:14.161 - [任务 51] - Task initialization... 
[INFO ] 2024-07-11 14:55:14.263 - [任务 51] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-11 14:55:14.263 - [任务 51] - The engine receives 任务 51 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-11 14:55:14.291 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] start preload schema,table counts: 1 
[INFO ] 2024-07-11 14:55:14.291 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] start preload schema,table counts: 1 
[INFO ] 2024-07-11 14:55:14.291 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] preload schema finished, cost 0 ms 
[INFO ] 2024-07-11 14:55:14.291 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] preload schema finished, cost 0 ms 
[INFO ] 2024-07-11 14:55:15.110 - [任务 51][SouceMysql - Copy] - Source node "SouceMysql - Copy" read batch size: 100 
[INFO ] 2024-07-11 14:55:15.112 - [任务 51][SouceMysql - Copy] - Source node "SouceMysql - Copy" event queue capacity: 200 
[INFO ] 2024-07-11 14:55:15.114 - [任务 51][SouceMysql - Copy] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-11 14:55:15.136 - [任务 51][SouceMysql - Copy] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":92396103,"gtidSet":""} 
[INFO ] 2024-07-11 14:55:15.201 - [任务 51][SouceMysql - Copy] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-11 14:55:15.201 - [任务 51][SouceMysql - Copy] - Initial sync started 
[INFO ] 2024-07-11 14:55:15.214 - [任务 51][SouceMysql - Copy] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-11 14:55:15.214 - [任务 51][SouceMysql - Copy] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-11 14:55:15.347 - [任务 51][SouceMysql - Copy] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-11 14:55:15.347 - [任务 51][TargetMysql] - Node(TargetMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-11 14:55:15.347 - [任务 51][TargetMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-11 14:55:15.437 - [任务 51][TargetMysql] - Table "test.POLICY" exists, skip auto create table 
[INFO ] 2024-07-11 14:55:15.646 - [任务 51][TargetMysql] - The table POLICY has already exist. 
[INFO ] 2024-07-11 14:55:15.677 - [任务 51][SouceMysql - Copy] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-11 14:55:15.678 - [任务 51][SouceMysql - Copy] - Initial sync completed 
[INFO ] 2024-07-11 14:55:15.678 - [任务 51][SouceMysql - Copy] - Incremental sync starting... 
[INFO ] 2024-07-11 14:55:15.678 - [任务 51][SouceMysql - Copy] - Initial sync completed 
[INFO ] 2024-07-11 14:55:15.681 - [任务 51][SouceMysql - Copy] - Starting stream read, table list: [POLICY], offset: {"filename":"binlog.000032","position":92396103,"gtidSet":""} 
[INFO ] 2024-07-11 14:55:15.728 - [任务 51][SouceMysql - Copy] - Starting mysql cdc, server name: 1de17fa7-a707-4bdc-84ea-f947bf62dc7c 
[INFO ] 2024-07-11 14:55:15.776 - [任务 51][SouceMysql - Copy] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1752265357
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 1de17fa7-a707-4bdc-84ea-f947bf62dc7c
  database.port: 3306
  threadName: Debezium-Mysql-Connector-1de17fa7-a707-4bdc-84ea-f947bf62dc7c
  database.hostname: localhost
  database.password: ********
  name: 1de17fa7-a707-4bdc-84ea-f947bf62dc7c
  pdk.offset.string: {"name":"1de17fa7-a707-4bdc-84ea-f947bf62dc7c","offset":{"{\"server\":\"1de17fa7-a707-4bdc-84ea-f947bf62dc7c\"}":"{\"file\":\"binlog.000032\",\"pos\":92396103,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test2.POLICY
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test2
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-11 14:55:15.777 - [任务 51][SouceMysql - Copy] - Connector Mysql incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-11 14:57:14.458 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] running status set to false 
[INFO ] 2024-07-11 14:57:14.458 - [任务 51][SouceMysql - Copy] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-11 14:57:14.472 - [任务 51][SouceMysql - Copy] - Mysql binlog reader stopped 
[INFO ] 2024-07-11 14:57:14.473 - [任务 51][SouceMysql - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode-59b7c2a4-b4d8-43f3-a06a-846b2fd34632 
[INFO ] 2024-07-11 14:57:14.473 - [任务 51][SouceMysql - Copy] - PDK connector node released: HazelcastSourcePdkDataNode-59b7c2a4-b4d8-43f3-a06a-846b2fd34632 
[INFO ] 2024-07-11 14:57:14.473 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] schema data cleaned 
[INFO ] 2024-07-11 14:57:14.474 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] monitor closed 
[INFO ] 2024-07-11 14:57:14.476 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] close complete, cost 54 ms 
[INFO ] 2024-07-11 14:57:14.476 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] running status set to false 
[INFO ] 2024-07-11 14:57:14.500 - [任务 51][TargetMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-03647ff8-af0e-4d14-ae45-7c7eb19f63a4 
[INFO ] 2024-07-11 14:57:14.500 - [任务 51][TargetMysql] - PDK connector node released: HazelcastTargetPdkDataNode-03647ff8-af0e-4d14-ae45-7c7eb19f63a4 
[INFO ] 2024-07-11 14:57:14.501 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] schema data cleaned 
[INFO ] 2024-07-11 14:57:14.501 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] monitor closed 
[INFO ] 2024-07-11 14:57:14.506 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] close complete, cost 25 ms 
[INFO ] 2024-07-11 14:57:46.651 - [任务 51] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-11 14:57:46.652 - [任务 51] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@46c3db38 
[INFO ] 2024-07-11 14:57:46.782 - [任务 51] - Stop task milestones: 668ba3681465df7a0893fa5d(任务 51)  
[INFO ] 2024-07-11 14:57:46.915 - [任务 51] - Remove memory task client succeed, task: 任务 51[668ba3681465df7a0893fa5d] 
[INFO ] 2024-07-11 14:57:46.915 - [任务 51] - Destroy memory task client cache succeed, task: 任务 51[668ba3681465df7a0893fa5d] 
[INFO ] 2024-07-11 15:03:50.889 - [任务 51] - Task initialization... 
[INFO ] 2024-07-11 15:03:50.907 - [任务 51] - Start task milestones: 668ba3681465df7a0893fa5d(任务 51) 
[INFO ] 2024-07-11 15:03:52.819 - [任务 51] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-11 15:03:52.979 - [任务 51] - The engine receives 任务 51 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-11 15:03:53.862 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] start preload schema,table counts: 1 
[INFO ] 2024-07-11 15:03:53.863 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] preload schema finished, cost 2 ms 
[INFO ] 2024-07-11 15:03:53.869 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] start preload schema,table counts: 1 
[INFO ] 2024-07-11 15:03:53.870 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] preload schema finished, cost 0 ms 
[INFO ] 2024-07-11 15:03:59.088 - [任务 51][TargetMysql] - Node(TargetMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-07-11 15:03:59.089 - [任务 51][TargetMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-11 15:03:59.616 - [任务 51][SouceMysql - Copy] - Source node "SouceMysql - Copy" read batch size: 100 
[INFO ] 2024-07-11 15:03:59.617 - [任务 51][SouceMysql - Copy] - Source node "SouceMysql - Copy" event queue capacity: 200 
[INFO ] 2024-07-11 15:03:59.623 - [任务 51][SouceMysql - Copy] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-11 15:03:59.623 - [任务 51][SouceMysql - Copy] - batch offset found: {"POLICY":{"batch_read_connector_offset":{},"batch_read_connector_status":"RUNNING"}},stream offset found: {"name":"1de17fa7-a707-4bdc-84ea-f947bf62dc7c","offset":{"{\"server\":\"1de17fa7-a707-4bdc-84ea-f947bf62dc7c\"}":"{\"ts_sec\":1720680915,\"file\":\"binlog.000032\",\"pos\":92396103,\"server_id\":1}"}} 
[INFO ] 2024-07-11 15:03:59.665 - [任务 51][SouceMysql - Copy] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-11 15:03:59.666 - [任务 51][SouceMysql - Copy] - Incremental sync starting... 
[INFO ] 2024-07-11 15:03:59.666 - [任务 51][SouceMysql - Copy] - Initial sync completed 
[INFO ] 2024-07-11 15:03:59.667 - [任务 51][SouceMysql - Copy] - Starting stream read, table list: [POLICY], offset: {"name":"1de17fa7-a707-4bdc-84ea-f947bf62dc7c","offset":{"{\"server\":\"1de17fa7-a707-4bdc-84ea-f947bf62dc7c\"}":"{\"ts_sec\":1720680915,\"file\":\"binlog.000032\",\"pos\":92396103,\"server_id\":1}"}} 
[INFO ] 2024-07-11 15:03:59.683 - [任务 51][SouceMysql - Copy] - Starting mysql cdc, server name: 1de17fa7-a707-4bdc-84ea-f947bf62dc7c 
[INFO ] 2024-07-11 15:03:59.683 - [任务 51][SouceMysql - Copy] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 286750847
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 1de17fa7-a707-4bdc-84ea-f947bf62dc7c
  database.port: 3306
  threadName: Debezium-Mysql-Connector-1de17fa7-a707-4bdc-84ea-f947bf62dc7c
  database.hostname: localhost
  database.password: ********
  name: 1de17fa7-a707-4bdc-84ea-f947bf62dc7c
  pdk.offset.string: {"name":"1de17fa7-a707-4bdc-84ea-f947bf62dc7c","offset":{"{\"server\":\"1de17fa7-a707-4bdc-84ea-f947bf62dc7c\"}":"{\"ts_sec\":1720680915,\"file\":\"binlog.000032\",\"pos\":92396103,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test2.POLICY
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test2
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-11 15:04:00.679 - [任务 51][SouceMysql - Copy] - Connector Mysql incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-11 15:09:28.675 - [任务 51][SouceMysql - Copy] - Mysql binlog reader stopped 
[INFO ] 2024-07-11 15:09:28.689 - [任务 51][SouceMysql - Copy] - Incremental sync completed 
[INFO ] 2024-07-11 15:09:28.692 - [任务 51][SouceMysql - Copy] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown exception occur when operate table: unknown 
[ERROR] 2024-07-11 15:09:28.725 - [任务 51][SouceMysql - Copy] - java.lang.RuntimeException: java.io.EOFException <-- Error Message -->
java.lang.RuntimeException: java.io.EOFException

<-- Simple Stack Trace -->
Caused by: java.io.EOFException: null
	com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:580)
	com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	java.lang.Thread.run(Thread.java:750)

<-- Full Stack Trace -->
java.lang.RuntimeException: java.io.EOFException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.io.EOFException
	at io.tapdata.common.exception.AbstractExceptionCollector.collectCdcConfigInvalid(AbstractExceptionCollector.java:63)
	at io.tapdata.connector.mysql.MysqlExceptionCollector.collectCdcConfigInvalid(MysqlExceptionCollector.java:153)
	at io.tapdata.connector.mysql.MysqlReader.readBinlog(MysqlReader.java:360)
	at io.tapdata.connector.mysql.MysqlConnector.streamRead(MysqlConnector.java:602)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 16 more
Caused by: java.io.EOFException
	at com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:580)
	at com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	... 1 more

[INFO ] 2024-07-11 15:09:28.734 - [任务 51][SouceMysql - Copy] - Job suspend in error handle 
[INFO ] 2024-07-11 15:09:29.171 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] running status set to false 
[INFO ] 2024-07-11 15:09:29.171 - [任务 51][SouceMysql - Copy] - PDK connector node stopped: HazelcastSourcePdkDataNode-59b7c2a4-b4d8-43f3-a06a-846b2fd34632 
[INFO ] 2024-07-11 15:09:29.172 - [任务 51][SouceMysql - Copy] - PDK connector node released: HazelcastSourcePdkDataNode-59b7c2a4-b4d8-43f3-a06a-846b2fd34632 
[INFO ] 2024-07-11 15:09:29.172 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] schema data cleaned 
[INFO ] 2024-07-11 15:09:29.173 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] monitor closed 
[INFO ] 2024-07-11 15:09:29.174 - [任务 51][SouceMysql - Copy] - Node SouceMysql - Copy[59b7c2a4-b4d8-43f3-a06a-846b2fd34632] close complete, cost 29 ms 
[INFO ] 2024-07-11 15:09:29.175 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] running status set to false 
[INFO ] 2024-07-11 15:09:29.203 - [任务 51][TargetMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-03647ff8-af0e-4d14-ae45-7c7eb19f63a4 
[INFO ] 2024-07-11 15:09:29.203 - [任务 51][TargetMysql] - PDK connector node released: HazelcastTargetPdkDataNode-03647ff8-af0e-4d14-ae45-7c7eb19f63a4 
[INFO ] 2024-07-11 15:09:29.203 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] schema data cleaned 
[INFO ] 2024-07-11 15:09:29.203 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] monitor closed 
[INFO ] 2024-07-11 15:09:29.409 - [任务 51][TargetMysql] - Node TargetMysql[03647ff8-af0e-4d14-ae45-7c7eb19f63a4] close complete, cost 31 ms 
[INFO ] 2024-07-11 15:09:33.022 - [任务 51] - Task [任务 51] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-07-11 15:09:33.022 - [任务 51] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-11 15:09:33.038 - [任务 51] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@755546a9 
[INFO ] 2024-07-11 15:09:33.039 - [任务 51] - Stop task milestones: 668ba3681465df7a0893fa5d(任务 51)  
[INFO ] 2024-07-11 15:09:33.202 - [任务 51] - Stopped task aspect(s) 
[INFO ] 2024-07-11 15:09:33.202 - [任务 51] - Snapshot order controller have been removed 
[INFO ] 2024-07-11 15:09:33.217 - [任务 51] - Remove memory task client succeed, task: 任务 51[668ba3681465df7a0893fa5d] 
[INFO ] 2024-07-11 15:09:33.218 - [任务 51] - Destroy memory task client cache succeed, task: 任务 51[668ba3681465df7a0893fa5d] 
