[INFO ] 2024-03-29 15:43:11.217 - [orders_import_import_import_import_import_import] - Task initialization... 
[INFO ] 2024-03-29 15:43:11.217 - [orders_import_import_import_import_import_import] - Start task milestones: 66066c578c01ee6e137e6e02(orders_import_import_import_import_import_import) 
[INFO ] 2024-03-29 15:43:11.422 - [orders_import_import_import_import_import_import] - <PERSON><PERSON> performs snapshot read by order list: [ null ] -> [ null ] -> [ null ] 
[INFO ] 2024-03-29 15:43:11.494 - [orders_import_import_import_import_import_import] - The engine receives orders_import_import_import_import_import_import task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 15:43:11.683 - [orders_import_import_import_import_import_import][Rename Products] - Node Rename Products[85c129f6-6dcd-4eb3-ad1a-416c476a236f] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:43:11.683 - [orders_import_import_import_import_import_import][merge] - Node merge[9f8b4924-5221-418e-b665-abe451d7a421] start preload schema,table counts: 4 
[INFO ] 2024-03-29 15:43:11.683 - [orders_import_import_import_import_import_import][Delete Products] - Node Delete Products[df0f11c1-7921-4d81-a35a-4a8145c45659] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:43:11.683 - [orders_import_import_import_import_import_import][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:43:11.683 - [orders_import_import_import_import_import_import][Delete Order Details] - Node Delete Order Details[ef69f9f2-dc4e-4833-82e5-2c731e382e00] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:43:11.684 - [orders_import_import_import_import_import_import][Rename Orders] - Node Rename Orders[b488c0ec-0488-4d09-84ed-5c4b2d534b2b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:43:11.723 - [orders_import_import_import_import_import_import][Orders] - Node Orders[097159d7-0f75-47be-b2ee-50e878961f3c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:43:11.725 - [orders_import_import_import_import_import_import][Products] - Node Products[88018517-425a-47e5-a7ee-38347859dd14] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:43:11.725 - [orders_import_import_import_import_import_import][orders] - Node orders[dc2e200b-c5e6-4b29-8db3-7fa7c307a955] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:43:11.725 - [orders_import_import_import_import_import_import][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:43:11.725 - [orders_import_import_import_import_import_import][Rename Order Details] - Node Rename Order Details[bd6602d0-4aa1-43b2-aa03-f5c7ad1a26d7] start preload schema,table counts: 1 
[INFO ] 2024-03-29 15:43:11.851 - [orders_import_import_import_import_import_import][Order Details] - Node Order Details[33bb84e8-3fe2-4ced-8eb4-65a3067e9447] preload schema finished, cost 152 ms 
[INFO ] 2024-03-29 15:43:11.854 - [orders_import_import_import_import_import_import][Delete Order Details] - Node Delete Order Details[ef69f9f2-dc4e-4833-82e5-2c731e382e00] preload schema finished, cost 152 ms 
[INFO ] 2024-03-29 15:43:11.854 - [orders_import_import_import_import_import_import][Order Details] - Node Order Details[f69f5315-82c0-4828-8999-d7e0f2942273] preload schema finished, cost 113 ms 
[INFO ] 2024-03-29 15:43:11.854 - [orders_import_import_import_import_import_import][Delete Products] - Node Delete Products[df0f11c1-7921-4d81-a35a-4a8145c45659] preload schema finished, cost 163 ms 
[INFO ] 2024-03-29 15:43:11.854 - [orders_import_import_import_import_import_import][Rename Order Details] - Node Rename Order Details[bd6602d0-4aa1-43b2-aa03-f5c7ad1a26d7] preload schema finished, cost 116 ms 
[INFO ] 2024-03-29 15:43:11.854 - [orders_import_import_import_import_import_import][Products] - Node Products[88018517-425a-47e5-a7ee-38347859dd14] preload schema finished, cost 126 ms 
[INFO ] 2024-03-29 15:43:11.854 - [orders_import_import_import_import_import_import][Rename Products] - Node Rename Products[85c129f6-6dcd-4eb3-ad1a-416c476a236f] preload schema finished, cost 165 ms 
[INFO ] 2024-03-29 15:43:11.906 - [orders_import_import_import_import_import_import][Rename Orders] - Node Rename Orders[b488c0ec-0488-4d09-84ed-5c4b2d534b2b] preload schema finished, cost 215 ms 
[INFO ] 2024-03-29 15:43:11.906 - [orders_import_import_import_import_import_import][Orders] - Node Orders[097159d7-0f75-47be-b2ee-50e878961f3c] preload schema finished, cost 214 ms 
[INFO ] 2024-03-29 15:43:11.906 - [orders_import_import_import_import_import_import][orders] - Node orders[dc2e200b-c5e6-4b29-8db3-7fa7c307a955] preload schema finished, cost 181 ms 
[INFO ] 2024-03-29 15:43:11.997 - [orders_import_import_import_import_import_import][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 15:43:12.054 - [orders_import_import_import_import_import_import][merge] - Node merge[9f8b4924-5221-418e-b665-abe451d7a421] preload schema finished, cost 360 ms 
[INFO ] 2024-03-29 15:43:12.055 - [orders_import_import_import_import_import_import][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 15:43:12.055 - [orders_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  Order Details(ef69f9f2-dc4e-4833-82e5-2c731e382e00)
    ->Products(df0f11c1-7921-4d81-a35a-4a8145c45659)
} 
[INFO ] 2024-03-29 15:43:12.055 - [orders_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  Orders(b488c0ec-0488-4d09-84ed-5c4b2d534b2b)
    ->Order Details(ef69f9f2-dc4e-4833-82e5-2c731e382e00)
} 
[INFO ] 2024-03-29 15:43:12.280 - [orders_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Products_df0f11c1-7921-4d81-a35a-4a8145c45659__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 15:43:12.913 - [orders_import_import_import_import_import_import][Order Details] - Source node "Order Details" read batch size: 500 
[INFO ] 2024-03-29 15:43:12.916 - [orders_import_import_import_import_import_import][Order Details] - Source node "Order Details" event queue capacity: 1000 
[INFO ] 2024-03-29 15:43:12.916 - [orders_import_import_import_import_import_import][Order Details] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 15:43:13.126 - [orders_import_import_import_import_import_import][Order Details] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 15:43:13.146 - [orders_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Order Details_ef69f9f2-dc4e-4833-82e5-2c731e382e00__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 15:43:13.184 - [orders_import_import_import_import_import_import] - Node[Order Details] is waiting for running 
[INFO ] 2024-03-29 15:43:13.200 - [orders_import_import_import_import_import_import][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 15:43:13.202 - [orders_import_import_import_import_import_import][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 15:43:13.252 - [orders_import_import_import_import_import_import][Orders] - Source node "Orders" read batch size: 500 
[INFO ] 2024-03-29 15:43:13.252 - [orders_import_import_import_import_import_import][Orders] - Source node "Orders" event queue capacity: 1000 
[INFO ] 2024-03-29 15:43:13.252 - [orders_import_import_import_import_import_import][Orders] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 15:43:13.365 - [orders_import_import_import_import_import_import][Orders] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 15:43:13.368 - [orders_import_import_import_import_import_import][Orders] - Initial sync started 
[INFO ] 2024-03-29 15:43:13.368 - [orders_import_import_import_import_import_import][Orders] - Starting batch read, table name: Orders, offset: null 
[INFO ] 2024-03-29 15:43:13.384 - [orders_import_import_import_import_import_import][Orders] - Table Orders is going to be initial synced 
[INFO ] 2024-03-29 15:43:13.464 - [orders_import_import_import_import_import_import][orders] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-03-29 15:43:13.476 - [orders_import_import_import_import_import_import][Orders] - Query table 'Orders' counts: 1 
[INFO ] 2024-03-29 15:43:13.476 - [orders_import_import_import_import_import_import][Orders] - Initial sync completed 
[INFO ] 2024-03-29 15:43:13.742 - [orders_import_import_import_import_import_import][Products] - Source node "Products" read batch size: 500 
[INFO ] 2024-03-29 15:43:13.742 - [orders_import_import_import_import_import_import][Products] - Source node "Products" event queue capacity: 1000 
[INFO ] 2024-03-29 15:43:13.743 - [orders_import_import_import_import_import_import][Products] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 15:43:13.747 - [orders_import_import_import_import_import_import][Products] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 15:43:13.951 - [orders_import_import_import_import_import_import] - Node[Products] is waiting for running 
[INFO ] 2024-03-29 15:43:14.526 - [orders_import_import_import_import_import_import] - Node[Orders] finish, notify next layer to run 
[INFO ] 2024-03-29 15:43:14.528 - [orders_import_import_import_import_import_import][Order Details] - Initial sync started 
[INFO ] 2024-03-29 15:43:14.528 - [orders_import_import_import_import_import_import] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 15:43:14.528 - [orders_import_import_import_import_import_import][Order Details] - Starting batch read, table name: Order Details, offset: null 
[INFO ] 2024-03-29 15:43:14.557 - [orders_import_import_import_import_import_import][Order Details] - Table Order Details is going to be initial synced 
[INFO ] 2024-03-29 15:43:14.558 - [orders_import_import_import_import_import_import][Order Details] - Query table 'Order Details' counts: 1 
[INFO ] 2024-03-29 15:43:14.558 - [orders_import_import_import_import_import_import][Order Details] - Initial sync completed 
[INFO ] 2024-03-29 15:43:18.945 - [orders_import_import_import_import_import_import] - Node[Order Details] finish, notify next layer to run 
[INFO ] 2024-03-29 15:43:18.974 - [orders_import_import_import_import_import_import][Products] - Initial sync started 
[INFO ] 2024-03-29 15:43:18.975 - [orders_import_import_import_import_import_import] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 15:43:18.975 - [orders_import_import_import_import_import_import][Products] - Starting batch read, table name: Products, offset: null 
[INFO ] 2024-03-29 15:43:19.068 - [orders_import_import_import_import_import_import][Products] - Table Products is going to be initial synced 
[INFO ] 2024-03-29 15:43:19.068 - [orders_import_import_import_import_import_import][Products] - Query table 'Products' counts: 1 
[INFO ] 2024-03-29 15:43:19.273 - [orders_import_import_import_import_import_import][Products] - Initial sync completed 
[INFO ] 2024-03-29 15:43:20.928 - [orders_import_import_import_import_import_import][Orders] - Incremental sync starting... 
[INFO ] 2024-03-29 15:43:20.929 - [orders_import_import_import_import_import_import][Order Details] - Incremental sync starting... 
[INFO ] 2024-03-29 15:43:20.929 - [orders_import_import_import_import_import_import][Order Details] - Initial sync completed 
[INFO ] 2024-03-29 15:43:20.943 - [orders_import_import_import_import_import_import][Orders] - Initial sync completed 
[INFO ] 2024-03-29 15:43:20.952 - [orders_import_import_import_import_import_import][Orders] - Starting stream read, table list: [Orders], offset: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 15:43:20.952 - [orders_import_import_import_import_import_import][Order Details] - Starting stream read, table list: [Order Details], offset: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 15:43:21.023 - [orders_import_import_import_import_import_import][Orders] - Starting mysql cdc, server name: 1a8ba478-a6e4-4b0a-9389-18307ee290f9 
[INFO ] 2024-03-29 15:43:21.024 - [orders_import_import_import_import_import_import][Order Details] - Starting mysql cdc, server name: fab25a2e-077e-4142-a18d-eea01d8fd52b 
[INFO ] 2024-03-29 15:43:21.083 - [orders_import_import_import_import_import_import][Products] - Incremental sync starting... 
[INFO ] 2024-03-29 15:43:21.084 - [orders_import_import_import_import_import_import][Products] - Initial sync completed 
[INFO ] 2024-03-29 15:43:21.085 - [orders_import_import_import_import_import_import][Products] - Starting stream read, table list: [Products], offset: {"filename":"binlog.000008","position":6369457,"gtidSet":""} 
[INFO ] 2024-03-29 15:43:21.092 - [orders_import_import_import_import_import_import][Orders] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 326041919
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 1a8ba478-a6e4-4b0a-9389-18307ee290f9
  database.port: 3307
  threadName: Debezium-Mysql-Connector-1a8ba478-a6e4-4b0a-9389-18307ee290f9
  database.hostname: 127.0.0.1
  database.password: ********
  name: 1a8ba478-a6e4-4b0a-9389-18307ee290f9
  pdk.offset.string: {"name":"1a8ba478-a6e4-4b0a-9389-18307ee290f9","offset":{"{\"server\":\"1a8ba478-a6e4-4b0a-9389-18307ee290f9\"}":"{\"file\":\"binlog.000008\",\"pos\":6369457,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Orders
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 15:43:21.093 - [orders_import_import_import_import_import_import][Order Details] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1848070720
  time.precision.mode: adaptive_time_microseconds
  database.server.name: fab25a2e-077e-4142-a18d-eea01d8fd52b
  database.port: 3307
  threadName: Debezium-Mysql-Connector-fab25a2e-077e-4142-a18d-eea01d8fd52b
  database.hostname: 127.0.0.1
  database.password: ********
  name: fab25a2e-077e-4142-a18d-eea01d8fd52b
  pdk.offset.string: {"name":"fab25a2e-077e-4142-a18d-eea01d8fd52b","offset":{"{\"server\":\"fab25a2e-077e-4142-a18d-eea01d8fd52b\"}":"{\"file\":\"binlog.000008\",\"pos\":6369457,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Order Details
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 15:43:21.145 - [orders_import_import_import_import_import_import][Products] - Starting mysql cdc, server name: 959ffce7-b34a-452d-a118-59529d7b1231 
[INFO ] 2024-03-29 15:43:21.147 - [orders_import_import_import_import_import_import][Products] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 837796608
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 959ffce7-b34a-452d-a118-59529d7b1231
  database.port: 3307
  threadName: Debezium-Mysql-Connector-959ffce7-b34a-452d-a118-59529d7b1231
  database.hostname: 127.0.0.1
  database.password: ********
  name: 959ffce7-b34a-452d-a118-59529d7b1231
  pdk.offset.string: {"name":"959ffce7-b34a-452d-a118-59529d7b1231","offset":{"{\"server\":\"959ffce7-b34a-452d-a118-59529d7b1231\"}":"{\"file\":\"binlog.000008\",\"pos\":6369457,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Products
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 15:43:21.551 - [orders_import_import_import_import_import_import][Orders] - Connector Mysql incremental start succeed, tables: [Orders], data change syncing 
[INFO ] 2024-03-29 15:43:21.552 - [orders_import_import_import_import_import_import][Order Details] - Connector Mysql incremental start succeed, tables: [Order Details], data change syncing 
[INFO ] 2024-03-29 15:43:21.552 - [orders_import_import_import_import_import_import][Products] - Connector Mysql incremental start succeed, tables: [Products], data change syncing 
