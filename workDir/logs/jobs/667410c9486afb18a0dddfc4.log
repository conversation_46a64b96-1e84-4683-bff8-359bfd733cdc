[INFO ] 2024-06-20 19:25:50.846 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:25:50.847 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:25:50.847 - [任务 76(100)][5cb1a002-1ccd-4813-a724-e95af9e891ed] - Node 5cb1a002-1ccd-4813-a724-e95af9e891ed[5cb1a002-1ccd-4813-a724-e95af9e891ed] start preload schema,table counts: 0 
[INFO ] 2024-06-20 19:25:50.847 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] preload schema finished, cost 3 ms 
[INFO ] 2024-06-20 19:25:50.848 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:25:50.848 - [任务 76(100)][5cb1a002-1ccd-4813-a724-e95af9e891ed] - Node 5cb1a002-1ccd-4813-a724-e95af9e891ed[5cb1a002-1ccd-4813-a724-e95af9e891ed] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:25:51.007 - [任务 76(100)][testReference] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 
[ERROR] 2024-06-20 19:25:51.012 - [任务 76(100)][testReference] - start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 <-- Error Message -->
start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882750, "i" : 6 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882750, "i" : 6 } }, "signature" : { "hash" : { "$binary" : "2TDvQrDad2Gc4Hhbvvqn3VCGQz0=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:91)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882750, "i" : 6 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882750, "i" : 6 } }, "signature" : { "hash" : { "$binary" : "2TDvQrDad2Gc4Hhbvvqn3VCGQz0=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 19 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882750, "i" : 6 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882750, "i" : 6 } }, "signature" : { "hash" : { "$binary" : "2TDvQrDad2Gc4Hhbvvqn3VCGQz0=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 24 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882750, "i" : 6 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882750, "i" : 6 } }, "signature" : { "hash" : { "$binary" : "2TDvQrDad2Gc4Hhbvvqn3VCGQz0=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 25 more

[INFO ] 2024-06-20 19:25:51.211 - [任务 76(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 
[ERROR] 2024-06-20 19:25:51.211 - [任务 76(100)][增强JS] - Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 <-- Error Message -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882750, "i" : 6 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882750, "i" : 6 } }, "signature" : { "hash" : { "$binary" : "2TDvQrDad2Gc4Hhbvvqn3VCGQz0=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:55)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:143)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:110)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.create(ScriptExecutorsManager.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getDefaultScriptExecutor(HazelcastJavaScriptProcessorNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:162)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882750, "i" : 6 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882750, "i" : 6 } }, "signature" : { "hash" : { "$binary" : "2TDvQrDad2Gc4Hhbvvqn3VCGQz0=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 20 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882750, "i" : 6 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882750, "i" : 6 } }, "signature" : { "hash" : { "$binary" : "2TDvQrDad2Gc4Hhbvvqn3VCGQz0=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 25 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882750, "i" : 6 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882750, "i" : 6 } }, "signature" : { "hash" : { "$binary" : "2TDvQrDad2Gc4Hhbvvqn3VCGQz0=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 26 more

[INFO ] 2024-06-20 19:25:53.723 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] running status set to false 
[INFO ] 2024-06-20 19:25:53.724 - [任务 76(100)][testReference] - PDK connector node stopped: null 
[INFO ] 2024-06-20 19:25:53.730 - [任务 76(100)][testReference] - PDK connector node released: null 
[INFO ] 2024-06-20 19:25:53.730 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] schema data cleaned 
[INFO ] 2024-06-20 19:25:53.744 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] monitor closed 
[INFO ] 2024-06-20 19:25:53.745 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] close complete, cost 21 ms 
[INFO ] 2024-06-20 19:25:53.758 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] running status set to false 
[INFO ] 2024-06-20 19:25:53.759 - [任务 76(100)][5cb1a002-1ccd-4813-a724-e95af9e891ed] - Node 5cb1a002-1ccd-4813-a724-e95af9e891ed[5cb1a002-1ccd-4813-a724-e95af9e891ed] running status set to false 
[INFO ] 2024-06-20 19:25:53.768 - [任务 76(100)][5cb1a002-1ccd-4813-a724-e95af9e891ed] - Node 5cb1a002-1ccd-4813-a724-e95af9e891ed[5cb1a002-1ccd-4813-a724-e95af9e891ed] schema data cleaned 
[INFO ] 2024-06-20 19:25:53.770 - [任务 76(100)][5cb1a002-1ccd-4813-a724-e95af9e891ed] - Node 5cb1a002-1ccd-4813-a724-e95af9e891ed[5cb1a002-1ccd-4813-a724-e95af9e891ed] monitor closed 
[INFO ] 2024-06-20 19:25:53.770 - [任务 76(100)][5cb1a002-1ccd-4813-a724-e95af9e891ed] - Node 5cb1a002-1ccd-4813-a724-e95af9e891ed[5cb1a002-1ccd-4813-a724-e95af9e891ed] close complete, cost 12 ms 
[INFO ] 2024-06-20 19:25:53.780 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] schema data cleaned 
[INFO ] 2024-06-20 19:25:53.780 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] monitor closed 
[INFO ] 2024-06-20 19:25:53.786 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] close complete, cost 31 ms 
[INFO ] 2024-06-20 19:25:58.396 - [任务 76(100)][a997e16a-6185-4075-a84b-4de9ca403033] - Node a997e16a-6185-4075-a84b-4de9ca403033[a997e16a-6185-4075-a84b-4de9ca403033] start preload schema,table counts: 0 
[INFO ] 2024-06-20 19:25:58.397 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:25:58.403 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:25:58.411 - [任务 76(100)][a997e16a-6185-4075-a84b-4de9ca403033] - Node a997e16a-6185-4075-a84b-4de9ca403033[a997e16a-6185-4075-a84b-4de9ca403033] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:25:58.412 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:25:58.412 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:25:58.432 - [任务 76(100)][testReference] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 
[ERROR] 2024-06-20 19:25:58.530 - [任务 76(100)][testReference] - start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 <-- Error Message -->
start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882758, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882758, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "1xKAXOK4I5LlpA9LeIxH1siuu3g=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:91)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882758, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882758, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "1xKAXOK4I5LlpA9LeIxH1siuu3g=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 19 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882758, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882758, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "1xKAXOK4I5LlpA9LeIxH1siuu3g=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 24 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882758, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882758, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "1xKAXOK4I5LlpA9LeIxH1siuu3g=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 25 more

[INFO ] 2024-06-20 19:25:58.531 - [任务 76(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 
[ERROR] 2024-06-20 19:25:58.737 - [任务 76(100)][增强JS] - Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 <-- Error Message -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882758, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882758, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "1xKAXOK4I5LlpA9LeIxH1siuu3g=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:55)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:143)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:110)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.create(ScriptExecutorsManager.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getDefaultScriptExecutor(HazelcastJavaScriptProcessorNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:162)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882758, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882758, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "1xKAXOK4I5LlpA9LeIxH1siuu3g=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 20 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882758, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882758, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "1xKAXOK4I5LlpA9LeIxH1siuu3g=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 25 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882758, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882758, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "1xKAXOK4I5LlpA9LeIxH1siuu3g=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 26 more

[INFO ] 2024-06-20 19:26:01.009 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] running status set to false 
[INFO ] 2024-06-20 19:26:01.009 - [任务 76(100)][testReference] - PDK connector node stopped: null 
[INFO ] 2024-06-20 19:26:01.015 - [任务 76(100)][testReference] - PDK connector node released: null 
[INFO ] 2024-06-20 19:26:01.016 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] schema data cleaned 
[INFO ] 2024-06-20 19:26:01.016 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] monitor closed 
[INFO ] 2024-06-20 19:26:01.018 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] close complete, cost 39 ms 
[INFO ] 2024-06-20 19:26:01.096 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] running status set to false 
[INFO ] 2024-06-20 19:26:01.096 - [任务 76(100)][a997e16a-6185-4075-a84b-4de9ca403033] - Node a997e16a-6185-4075-a84b-4de9ca403033[a997e16a-6185-4075-a84b-4de9ca403033] running status set to false 
[INFO ] 2024-06-20 19:26:01.098 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] schema data cleaned 
[INFO ] 2024-06-20 19:26:01.099 - [任务 76(100)][a997e16a-6185-4075-a84b-4de9ca403033] - Node a997e16a-6185-4075-a84b-4de9ca403033[a997e16a-6185-4075-a84b-4de9ca403033] schema data cleaned 
[INFO ] 2024-06-20 19:26:01.110 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] monitor closed 
[INFO ] 2024-06-20 19:26:01.111 - [任务 76(100)][a997e16a-6185-4075-a84b-4de9ca403033] - Node a997e16a-6185-4075-a84b-4de9ca403033[a997e16a-6185-4075-a84b-4de9ca403033] monitor closed 
[INFO ] 2024-06-20 19:26:01.111 - [任务 76(100)][fa180372-e696-4e81-b9b0-5ae7950d21d4] - Node fa180372-e696-4e81-b9b0-5ae7950d21d4[fa180372-e696-4e81-b9b0-5ae7950d21d4] start preload schema,table counts: 0 
[INFO ] 2024-06-20 19:26:01.112 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:26:01.114 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] close complete, cost 24 ms 
[INFO ] 2024-06-20 19:26:01.115 - [任务 76(100)][a997e16a-6185-4075-a84b-4de9ca403033] - Node a997e16a-6185-4075-a84b-4de9ca403033[a997e16a-6185-4075-a84b-4de9ca403033] close complete, cost 22 ms 
[INFO ] 2024-06-20 19:26:01.117 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:26:01.118 - [任务 76(100)][fa180372-e696-4e81-b9b0-5ae7950d21d4] - Node fa180372-e696-4e81-b9b0-5ae7950d21d4[fa180372-e696-4e81-b9b0-5ae7950d21d4] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:26:01.119 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:26:01.130 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:26:01.175 - [任务 76(100)][testReference] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 
[ERROR] 2024-06-20 19:26:01.175 - [任务 76(100)][testReference] - start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 <-- Error Message -->
start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882760, "i" : 12 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882760, "i" : 12 } }, "signature" : { "hash" : { "$binary" : "/0lO6jXRaXR/9r5+RAYeywKraSI=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:91)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882760, "i" : 12 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882760, "i" : 12 } }, "signature" : { "hash" : { "$binary" : "/0lO6jXRaXR/9r5+RAYeywKraSI=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 19 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882760, "i" : 12 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882760, "i" : 12 } }, "signature" : { "hash" : { "$binary" : "/0lO6jXRaXR/9r5+RAYeywKraSI=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 24 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882760, "i" : 12 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882760, "i" : 12 } }, "signature" : { "hash" : { "$binary" : "/0lO6jXRaXR/9r5+RAYeywKraSI=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 25 more

[INFO ] 2024-06-20 19:26:01.262 - [任务 76(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 
[ERROR] 2024-06-20 19:26:01.264 - [任务 76(100)][增强JS] - Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 <-- Error Message -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882760, "i" : 12 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882760, "i" : 12 } }, "signature" : { "hash" : { "$binary" : "/0lO6jXRaXR/9r5+RAYeywKraSI=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:55)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:143)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:110)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.create(ScriptExecutorsManager.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getDefaultScriptExecutor(HazelcastJavaScriptProcessorNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:162)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882760, "i" : 12 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882760, "i" : 12 } }, "signature" : { "hash" : { "$binary" : "/0lO6jXRaXR/9r5+RAYeywKraSI=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 20 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882760, "i" : 12 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882760, "i" : 12 } }, "signature" : { "hash" : { "$binary" : "/0lO6jXRaXR/9r5+RAYeywKraSI=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 25 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882760, "i" : 12 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882760, "i" : 12 } }, "signature" : { "hash" : { "$binary" : "/0lO6jXRaXR/9r5+RAYeywKraSI=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 26 more

[INFO ] 2024-06-20 19:26:03.742 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] running status set to false 
[INFO ] 2024-06-20 19:26:03.744 - [任务 76(100)][testReference] - PDK connector node stopped: null 
[INFO ] 2024-06-20 19:26:03.745 - [任务 76(100)][testReference] - PDK connector node released: null 
[INFO ] 2024-06-20 19:26:03.748 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] schema data cleaned 
[INFO ] 2024-06-20 19:26:03.748 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] monitor closed 
[INFO ] 2024-06-20 19:26:03.798 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] close complete, cost 21 ms 
[INFO ] 2024-06-20 19:26:03.799 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] running status set to false 
[INFO ] 2024-06-20 19:26:03.814 - [任务 76(100)][fa180372-e696-4e81-b9b0-5ae7950d21d4] - Node fa180372-e696-4e81-b9b0-5ae7950d21d4[fa180372-e696-4e81-b9b0-5ae7950d21d4] running status set to false 
[INFO ] 2024-06-20 19:26:03.815 - [任务 76(100)][fa180372-e696-4e81-b9b0-5ae7950d21d4] - Node fa180372-e696-4e81-b9b0-5ae7950d21d4[fa180372-e696-4e81-b9b0-5ae7950d21d4] schema data cleaned 
[INFO ] 2024-06-20 19:26:03.823 - [任务 76(100)][fa180372-e696-4e81-b9b0-5ae7950d21d4] - Node fa180372-e696-4e81-b9b0-5ae7950d21d4[fa180372-e696-4e81-b9b0-5ae7950d21d4] monitor closed 
[INFO ] 2024-06-20 19:26:03.823 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] schema data cleaned 
[INFO ] 2024-06-20 19:26:03.824 - [任务 76(100)][fa180372-e696-4e81-b9b0-5ae7950d21d4] - Node fa180372-e696-4e81-b9b0-5ae7950d21d4[fa180372-e696-4e81-b9b0-5ae7950d21d4] close complete, cost 17 ms 
[INFO ] 2024-06-20 19:26:03.824 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] monitor closed 
[INFO ] 2024-06-20 19:26:03.825 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] close complete, cost 22 ms 
[INFO ] 2024-06-20 19:26:05.591 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:26:05.591 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:26:05.592 - [任务 76(100)][7e9c97fd-33da-4ba9-aff1-53a0b19b0b0f] - Node 7e9c97fd-33da-4ba9-aff1-53a0b19b0b0f[7e9c97fd-33da-4ba9-aff1-53a0b19b0b0f] start preload schema,table counts: 0 
[INFO ] 2024-06-20 19:26:05.592 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:26:05.592 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:26:05.592 - [任务 76(100)][7e9c97fd-33da-4ba9-aff1-53a0b19b0b0f] - Node 7e9c97fd-33da-4ba9-aff1-53a0b19b0b0f[7e9c97fd-33da-4ba9-aff1-53a0b19b0b0f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:26:05.622 - [任务 76(100)][testReference] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 
[ERROR] 2024-06-20 19:26:05.624 - [任务 76(100)][testReference] - start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 <-- Error Message -->
start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882765, "i" : 8 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882765, "i" : 8 } }, "signature" : { "hash" : { "$binary" : "dqpISAp8IdxjeJVneXwy95X1lKA=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:91)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882765, "i" : 8 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882765, "i" : 8 } }, "signature" : { "hash" : { "$binary" : "dqpISAp8IdxjeJVneXwy95X1lKA=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 19 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882765, "i" : 8 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882765, "i" : 8 } }, "signature" : { "hash" : { "$binary" : "dqpISAp8IdxjeJVneXwy95X1lKA=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 24 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882765, "i" : 8 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882765, "i" : 8 } }, "signature" : { "hash" : { "$binary" : "dqpISAp8IdxjeJVneXwy95X1lKA=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 25 more

[INFO ] 2024-06-20 19:26:05.669 - [任务 76(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 
[ERROR] 2024-06-20 19:26:05.669 - [任务 76(100)][增强JS] - Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 <-- Error Message -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882765, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882765, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "dqpISAp8IdxjeJVneXwy95X1lKA=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:55)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:143)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:110)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.create(ScriptExecutorsManager.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getDefaultScriptExecutor(HazelcastJavaScriptProcessorNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:162)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882765, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882765, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "dqpISAp8IdxjeJVneXwy95X1lKA=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 20 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882765, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882765, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "dqpISAp8IdxjeJVneXwy95X1lKA=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 25 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882765, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882765, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "dqpISAp8IdxjeJVneXwy95X1lKA=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 26 more

[INFO ] 2024-06-20 19:26:08.169 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] running status set to false 
[INFO ] 2024-06-20 19:26:08.170 - [任务 76(100)][testReference] - PDK connector node stopped: null 
[INFO ] 2024-06-20 19:26:08.171 - [任务 76(100)][testReference] - PDK connector node released: null 
[INFO ] 2024-06-20 19:26:08.174 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] schema data cleaned 
[INFO ] 2024-06-20 19:26:08.174 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] monitor closed 
[INFO ] 2024-06-20 19:26:08.212 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] close complete, cost 11 ms 
[INFO ] 2024-06-20 19:26:08.214 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] running status set to false 
[INFO ] 2024-06-20 19:26:08.222 - [任务 76(100)][7e9c97fd-33da-4ba9-aff1-53a0b19b0b0f] - Node 7e9c97fd-33da-4ba9-aff1-53a0b19b0b0f[7e9c97fd-33da-4ba9-aff1-53a0b19b0b0f] running status set to false 
[INFO ] 2024-06-20 19:26:08.223 - [任务 76(100)][7e9c97fd-33da-4ba9-aff1-53a0b19b0b0f] - Node 7e9c97fd-33da-4ba9-aff1-53a0b19b0b0f[7e9c97fd-33da-4ba9-aff1-53a0b19b0b0f] schema data cleaned 
[INFO ] 2024-06-20 19:26:08.223 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] schema data cleaned 
[INFO ] 2024-06-20 19:26:08.223 - [任务 76(100)][7e9c97fd-33da-4ba9-aff1-53a0b19b0b0f] - Node 7e9c97fd-33da-4ba9-aff1-53a0b19b0b0f[7e9c97fd-33da-4ba9-aff1-53a0b19b0b0f] monitor closed 
[INFO ] 2024-06-20 19:26:08.223 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] monitor closed 
[INFO ] 2024-06-20 19:26:08.223 - [任务 76(100)][7e9c97fd-33da-4ba9-aff1-53a0b19b0b0f] - Node 7e9c97fd-33da-4ba9-aff1-53a0b19b0b0f[7e9c97fd-33da-4ba9-aff1-53a0b19b0b0f] close complete, cost 17 ms 
[INFO ] 2024-06-20 19:26:08.227 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] close complete, cost 18 ms 
[INFO ] 2024-06-20 19:26:10.953 - [任务 76(100)][d2e0efbd-4117-428d-8c09-28089955936f] - Node d2e0efbd-4117-428d-8c09-28089955936f[d2e0efbd-4117-428d-8c09-28089955936f] start preload schema,table counts: 0 
[INFO ] 2024-06-20 19:26:10.953 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:26:10.954 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:26:10.954 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:26:10.955 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:26:10.956 - [任务 76(100)][d2e0efbd-4117-428d-8c09-28089955936f] - Node d2e0efbd-4117-428d-8c09-28089955936f[d2e0efbd-4117-428d-8c09-28089955936f] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:26:10.995 - [任务 76(100)][testReference] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 
[ERROR] 2024-06-20 19:26:10.996 - [任务 76(100)][testReference] - start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 <-- Error Message -->
start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882770, "i" : 16 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882770, "i" : 16 } }, "signature" : { "hash" : { "$binary" : "czfMH450+xGlVNkGHECsJ5utTVY=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:91)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882770, "i" : 16 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882770, "i" : 16 } }, "signature" : { "hash" : { "$binary" : "czfMH450+xGlVNkGHECsJ5utTVY=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 19 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882770, "i" : 16 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882770, "i" : 16 } }, "signature" : { "hash" : { "$binary" : "czfMH450+xGlVNkGHECsJ5utTVY=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 24 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882770, "i" : 16 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882770, "i" : 16 } }, "signature" : { "hash" : { "$binary" : "czfMH450+xGlVNkGHECsJ5utTVY=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 25 more

[INFO ] 2024-06-20 19:26:11.051 - [任务 76(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 
[ERROR] 2024-06-20 19:26:11.055 - [任务 76(100)][增强JS] - Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 <-- Error Message -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882770, "i" : 16 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882770, "i" : 16 } }, "signature" : { "hash" : { "$binary" : "czfMH450+xGlVNkGHECsJ5utTVY=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:55)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:143)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:110)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.create(ScriptExecutorsManager.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getDefaultScriptExecutor(HazelcastJavaScriptProcessorNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:162)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882770, "i" : 16 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882770, "i" : 16 } }, "signature" : { "hash" : { "$binary" : "czfMH450+xGlVNkGHECsJ5utTVY=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 20 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882770, "i" : 16 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882770, "i" : 16 } }, "signature" : { "hash" : { "$binary" : "czfMH450+xGlVNkGHECsJ5utTVY=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 25 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882770, "i" : 16 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882770, "i" : 16 } }, "signature" : { "hash" : { "$binary" : "czfMH450+xGlVNkGHECsJ5utTVY=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 26 more

[INFO ] 2024-06-20 19:26:13.552 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] running status set to false 
[INFO ] 2024-06-20 19:26:13.555 - [任务 76(100)][testReference] - PDK connector node stopped: null 
[INFO ] 2024-06-20 19:26:13.556 - [任务 76(100)][testReference] - PDK connector node released: null 
[INFO ] 2024-06-20 19:26:13.556 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] schema data cleaned 
[INFO ] 2024-06-20 19:26:13.557 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] monitor closed 
[INFO ] 2024-06-20 19:26:13.594 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] close complete, cost 22 ms 
[INFO ] 2024-06-20 19:26:13.595 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] running status set to false 
[INFO ] 2024-06-20 19:26:13.595 - [任务 76(100)][d2e0efbd-4117-428d-8c09-28089955936f] - Node d2e0efbd-4117-428d-8c09-28089955936f[d2e0efbd-4117-428d-8c09-28089955936f] running status set to false 
[INFO ] 2024-06-20 19:26:13.595 - [任务 76(100)][d2e0efbd-4117-428d-8c09-28089955936f] - Node d2e0efbd-4117-428d-8c09-28089955936f[d2e0efbd-4117-428d-8c09-28089955936f] schema data cleaned 
[INFO ] 2024-06-20 19:26:13.595 - [任务 76(100)][d2e0efbd-4117-428d-8c09-28089955936f] - Node d2e0efbd-4117-428d-8c09-28089955936f[d2e0efbd-4117-428d-8c09-28089955936f] monitor closed 
[INFO ] 2024-06-20 19:26:13.595 - [任务 76(100)][d2e0efbd-4117-428d-8c09-28089955936f] - Node d2e0efbd-4117-428d-8c09-28089955936f[d2e0efbd-4117-428d-8c09-28089955936f] close complete, cost 10 ms 
[INFO ] 2024-06-20 19:26:13.602 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] schema data cleaned 
[INFO ] 2024-06-20 19:26:13.602 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] monitor closed 
[INFO ] 2024-06-20 19:26:13.602 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] close complete, cost 20 ms 
[INFO ] 2024-06-20 19:26:18.678 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:26:18.678 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:26:18.680 - [任务 76(100)][5573161a-4c6b-4b3a-ac59-7e1c164ce34a] - Node 5573161a-4c6b-4b3a-ac59-7e1c164ce34a[5573161a-4c6b-4b3a-ac59-7e1c164ce34a] start preload schema,table counts: 0 
[INFO ] 2024-06-20 19:26:18.680 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:26:18.682 - [任务 76(100)][5573161a-4c6b-4b3a-ac59-7e1c164ce34a] - Node 5573161a-4c6b-4b3a-ac59-7e1c164ce34a[5573161a-4c6b-4b3a-ac59-7e1c164ce34a] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:26:18.683 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:26:18.708 - [任务 76(100)][testReference] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 
[ERROR] 2024-06-20 19:26:18.757 - [任务 76(100)][testReference] - start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 <-- Error Message -->
start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882778, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882778, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "K5p/2CcaYDdeRNFyV8bBFmCDYDk=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:91)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882778, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882778, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "K5p/2CcaYDdeRNFyV8bBFmCDYDk=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 19 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882778, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882778, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "K5p/2CcaYDdeRNFyV8bBFmCDYDk=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 24 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882778, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882778, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "K5p/2CcaYDdeRNFyV8bBFmCDYDk=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 25 more

[INFO ] 2024-06-20 19:26:18.758 - [任务 76(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 
[ERROR] 2024-06-20 19:26:18.958 - [任务 76(100)][增强JS] - Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 <-- Error Message -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882778, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882778, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "K5p/2CcaYDdeRNFyV8bBFmCDYDk=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:55)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:143)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:110)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.create(ScriptExecutorsManager.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getDefaultScriptExecutor(HazelcastJavaScriptProcessorNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:162)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882778, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882778, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "K5p/2CcaYDdeRNFyV8bBFmCDYDk=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 20 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882778, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882778, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "K5p/2CcaYDdeRNFyV8bBFmCDYDk=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 25 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882778, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882778, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "K5p/2CcaYDdeRNFyV8bBFmCDYDk=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 26 more

[INFO ] 2024-06-20 19:26:21.257 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] running status set to false 
[INFO ] 2024-06-20 19:26:21.261 - [任务 76(100)][testReference] - PDK connector node stopped: null 
[INFO ] 2024-06-20 19:26:21.261 - [任务 76(100)][testReference] - PDK connector node released: null 
[INFO ] 2024-06-20 19:26:21.261 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] schema data cleaned 
[INFO ] 2024-06-20 19:26:21.300 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] monitor closed 
[INFO ] 2024-06-20 19:26:21.301 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] close complete, cost 8 ms 
[INFO ] 2024-06-20 19:26:21.301 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] running status set to false 
[INFO ] 2024-06-20 19:26:21.301 - [任务 76(100)][5573161a-4c6b-4b3a-ac59-7e1c164ce34a] - Node 5573161a-4c6b-4b3a-ac59-7e1c164ce34a[5573161a-4c6b-4b3a-ac59-7e1c164ce34a] running status set to false 
[INFO ] 2024-06-20 19:26:21.304 - [任务 76(100)][5573161a-4c6b-4b3a-ac59-7e1c164ce34a] - Node 5573161a-4c6b-4b3a-ac59-7e1c164ce34a[5573161a-4c6b-4b3a-ac59-7e1c164ce34a] schema data cleaned 
[INFO ] 2024-06-20 19:26:21.306 - [任务 76(100)][5573161a-4c6b-4b3a-ac59-7e1c164ce34a] - Node 5573161a-4c6b-4b3a-ac59-7e1c164ce34a[5573161a-4c6b-4b3a-ac59-7e1c164ce34a] monitor closed 
[INFO ] 2024-06-20 19:26:21.307 - [任务 76(100)][5573161a-4c6b-4b3a-ac59-7e1c164ce34a] - Node 5573161a-4c6b-4b3a-ac59-7e1c164ce34a[5573161a-4c6b-4b3a-ac59-7e1c164ce34a] close complete, cost 9 ms 
[INFO ] 2024-06-20 19:26:21.307 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] schema data cleaned 
[INFO ] 2024-06-20 19:26:21.309 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] monitor closed 
[INFO ] 2024-06-20 19:26:21.309 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] close complete, cost 13 ms 
[INFO ] 2024-06-20 19:26:24.246 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:26:24.246 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:26:24.247 - [任务 76(100)][260b4e4d-2d13-43d9-8f44-74902c8fa154] - Node 260b4e4d-2d13-43d9-8f44-74902c8fa154[260b4e4d-2d13-43d9-8f44-74902c8fa154] start preload schema,table counts: 0 
[INFO ] 2024-06-20 19:26:24.247 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:26:24.247 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:26:24.250 - [任务 76(100)][260b4e4d-2d13-43d9-8f44-74902c8fa154] - Node 260b4e4d-2d13-43d9-8f44-74902c8fa154[260b4e4d-2d13-43d9-8f44-74902c8fa154] preload schema finished, cost 1 ms 
[INFO ] 2024-06-20 19:26:24.291 - [任务 76(100)][testReference] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 
[ERROR] 2024-06-20 19:26:24.292 - [任务 76(100)][testReference] - start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 <-- Error Message -->
start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882784, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882784, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "PrVREoEpYWkBwiSWj5B9wm5jg3w=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:91)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882784, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882784, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "PrVREoEpYWkBwiSWj5B9wm5jg3w=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 19 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882784, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882784, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "PrVREoEpYWkBwiSWj5B9wm5jg3w=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 24 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882784, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882784, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "PrVREoEpYWkBwiSWj5B9wm5jg3w=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 25 more

[INFO ] 2024-06-20 19:26:24.395 - [任务 76(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 
[ERROR] 2024-06-20 19:26:24.399 - [任务 76(100)][增强JS] - Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 <-- Error Message -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882784, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882784, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "PrVREoEpYWkBwiSWj5B9wm5jg3w=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:55)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:143)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:110)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.create(ScriptExecutorsManager.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getDefaultScriptExecutor(HazelcastJavaScriptProcessorNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:162)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882784, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882784, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "PrVREoEpYWkBwiSWj5B9wm5jg3w=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 20 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882784, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882784, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "PrVREoEpYWkBwiSWj5B9wm5jg3w=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 25 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882784, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882784, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "PrVREoEpYWkBwiSWj5B9wm5jg3w=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 26 more

[INFO ] 2024-06-20 19:26:24.932 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:26:24.933 - [任务 76(100)][75294f6a-c0eb-40db-b422-4aef870ca2d4] - Node 75294f6a-c0eb-40db-b422-4aef870ca2d4[75294f6a-c0eb-40db-b422-4aef870ca2d4] start preload schema,table counts: 0 
[INFO ] 2024-06-20 19:26:24.933 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:26:24.933 - [任务 76(100)][75294f6a-c0eb-40db-b422-4aef870ca2d4] - Node 75294f6a-c0eb-40db-b422-4aef870ca2d4[75294f6a-c0eb-40db-b422-4aef870ca2d4] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:26:24.933 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:26:24.933 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:26:24.990 - [任务 76(100)][testReference] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 
[ERROR] 2024-06-20 19:26:24.990 - [任务 76(100)][testReference] - start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 <-- Error Message -->
start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882784, "i" : 17 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882784, "i" : 17 } }, "signature" : { "hash" : { "$binary" : "PrVREoEpYWkBwiSWj5B9wm5jg3w=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:91)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882784, "i" : 17 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882784, "i" : 17 } }, "signature" : { "hash" : { "$binary" : "PrVREoEpYWkBwiSWj5B9wm5jg3w=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 19 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882784, "i" : 17 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882784, "i" : 17 } }, "signature" : { "hash" : { "$binary" : "PrVREoEpYWkBwiSWj5B9wm5jg3w=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 24 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882784, "i" : 17 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882784, "i" : 17 } }, "signature" : { "hash" : { "$binary" : "PrVREoEpYWkBwiSWj5B9wm5jg3w=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 25 more

[INFO ] 2024-06-20 19:26:25.031 - [任务 76(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 
[ERROR] 2024-06-20 19:26:25.031 - [任务 76(100)][增强JS] - Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 <-- Error Message -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882784, "i" : 17 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882784, "i" : 17 } }, "signature" : { "hash" : { "$binary" : "PrVREoEpYWkBwiSWj5B9wm5jg3w=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:55)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:143)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:110)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.create(ScriptExecutorsManager.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getDefaultScriptExecutor(HazelcastJavaScriptProcessorNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:162)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882784, "i" : 17 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882784, "i" : 17 } }, "signature" : { "hash" : { "$binary" : "PrVREoEpYWkBwiSWj5B9wm5jg3w=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 20 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882784, "i" : 17 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882784, "i" : 17 } }, "signature" : { "hash" : { "$binary" : "PrVREoEpYWkBwiSWj5B9wm5jg3w=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 25 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882784, "i" : 17 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882784, "i" : 17 } }, "signature" : { "hash" : { "$binary" : "PrVREoEpYWkBwiSWj5B9wm5jg3w=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 26 more

[INFO ] 2024-06-20 19:26:26.831 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] running status set to false 
[INFO ] 2024-06-20 19:26:26.838 - [任务 76(100)][testReference] - PDK connector node stopped: null 
[INFO ] 2024-06-20 19:26:26.839 - [任务 76(100)][testReference] - PDK connector node released: null 
[INFO ] 2024-06-20 19:26:26.842 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] schema data cleaned 
[INFO ] 2024-06-20 19:26:26.842 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] monitor closed 
[INFO ] 2024-06-20 19:26:26.936 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] close complete, cost 21 ms 
[INFO ] 2024-06-20 19:26:26.936 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] running status set to false 
[INFO ] 2024-06-20 19:26:26.945 - [任务 76(100)][260b4e4d-2d13-43d9-8f44-74902c8fa154] - Node 260b4e4d-2d13-43d9-8f44-74902c8fa154[260b4e4d-2d13-43d9-8f44-74902c8fa154] running status set to false 
[INFO ] 2024-06-20 19:26:26.955 - [任务 76(100)][260b4e4d-2d13-43d9-8f44-74902c8fa154] - Node 260b4e4d-2d13-43d9-8f44-74902c8fa154[260b4e4d-2d13-43d9-8f44-74902c8fa154] schema data cleaned 
[INFO ] 2024-06-20 19:26:26.955 - [任务 76(100)][260b4e4d-2d13-43d9-8f44-74902c8fa154] - Node 260b4e4d-2d13-43d9-8f44-74902c8fa154[260b4e4d-2d13-43d9-8f44-74902c8fa154] monitor closed 
[INFO ] 2024-06-20 19:26:26.956 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] schema data cleaned 
[INFO ] 2024-06-20 19:26:26.956 - [任务 76(100)][260b4e4d-2d13-43d9-8f44-74902c8fa154] - Node 260b4e4d-2d13-43d9-8f44-74902c8fa154[260b4e4d-2d13-43d9-8f44-74902c8fa154] close complete, cost 27 ms 
[INFO ] 2024-06-20 19:26:26.959 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] monitor closed 
[INFO ] 2024-06-20 19:26:26.960 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] close complete, cost 30 ms 
[INFO ] 2024-06-20 19:26:27.535 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] running status set to false 
[INFO ] 2024-06-20 19:26:27.540 - [任务 76(100)][testReference] - PDK connector node stopped: null 
[INFO ] 2024-06-20 19:26:27.541 - [任务 76(100)][testReference] - PDK connector node released: null 
[INFO ] 2024-06-20 19:26:27.541 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] schema data cleaned 
[INFO ] 2024-06-20 19:26:27.552 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] monitor closed 
[INFO ] 2024-06-20 19:26:27.553 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] close complete, cost 23 ms 
[INFO ] 2024-06-20 19:26:27.566 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] running status set to false 
[INFO ] 2024-06-20 19:26:27.566 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] schema data cleaned 
[INFO ] 2024-06-20 19:26:27.566 - [任务 76(100)][75294f6a-c0eb-40db-b422-4aef870ca2d4] - Node 75294f6a-c0eb-40db-b422-4aef870ca2d4[75294f6a-c0eb-40db-b422-4aef870ca2d4] running status set to false 
[INFO ] 2024-06-20 19:26:27.566 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] monitor closed 
[INFO ] 2024-06-20 19:26:27.578 - [任务 76(100)][75294f6a-c0eb-40db-b422-4aef870ca2d4] - Node 75294f6a-c0eb-40db-b422-4aef870ca2d4[75294f6a-c0eb-40db-b422-4aef870ca2d4] schema data cleaned 
[INFO ] 2024-06-20 19:26:27.582 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] close complete, cost 6 ms 
[INFO ] 2024-06-20 19:26:27.582 - [任务 76(100)][75294f6a-c0eb-40db-b422-4aef870ca2d4] - Node 75294f6a-c0eb-40db-b422-4aef870ca2d4[75294f6a-c0eb-40db-b422-4aef870ca2d4] monitor closed 
[INFO ] 2024-06-20 19:26:27.582 - [任务 76(100)][75294f6a-c0eb-40db-b422-4aef870ca2d4] - Node 75294f6a-c0eb-40db-b422-4aef870ca2d4[75294f6a-c0eb-40db-b422-4aef870ca2d4] close complete, cost 3 ms 
[INFO ] 2024-06-20 19:27:09.167 - [任务 76(100)][bde37c6a-4802-4aef-9f81-25293a4751b8] - Node bde37c6a-4802-4aef-9f81-25293a4751b8[bde37c6a-4802-4aef-9f81-25293a4751b8] start preload schema,table counts: 0 
[INFO ] 2024-06-20 19:27:09.167 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:27:09.169 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:27:09.174 - [任务 76(100)][bde37c6a-4802-4aef-9f81-25293a4751b8] - Node bde37c6a-4802-4aef-9f81-25293a4751b8[bde37c6a-4802-4aef-9f81-25293a4751b8] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:27:09.174 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] preload schema finished, cost 1 ms 
[INFO ] 2024-06-20 19:27:09.175 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:27:09.204 - [任务 76(100)][testReference] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 
[ERROR] 2024-06-20 19:27:09.271 - [任务 76(100)][testReference] - start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 <-- Error Message -->
start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882829, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882829, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "utFexaMWE9ysc3jxxcdM4OhsTl8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:91)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882829, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882829, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "utFexaMWE9ysc3jxxcdM4OhsTl8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 19 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882829, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882829, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "utFexaMWE9ysc3jxxcdM4OhsTl8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 24 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882829, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882829, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "utFexaMWE9ysc3jxxcdM4OhsTl8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 25 more

[INFO ] 2024-06-20 19:27:09.272 - [任务 76(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 
[ERROR] 2024-06-20 19:27:09.476 - [任务 76(100)][增强JS] - Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 <-- Error Message -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882829, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882829, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "utFexaMWE9ysc3jxxcdM4OhsTl8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:55)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:143)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:110)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.create(ScriptExecutorsManager.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getDefaultScriptExecutor(HazelcastJavaScriptProcessorNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:162)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882829, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882829, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "utFexaMWE9ysc3jxxcdM4OhsTl8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 20 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882829, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882829, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "utFexaMWE9ysc3jxxcdM4OhsTl8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 25 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882829, "i" : 5 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882829, "i" : 5 } }, "signature" : { "hash" : { "$binary" : "utFexaMWE9ysc3jxxcdM4OhsTl8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 26 more

[INFO ] 2024-06-20 19:27:11.773 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] running status set to false 
[INFO ] 2024-06-20 19:27:11.774 - [任务 76(100)][testReference] - PDK connector node stopped: null 
[INFO ] 2024-06-20 19:27:11.775 - [任务 76(100)][testReference] - PDK connector node released: null 
[INFO ] 2024-06-20 19:27:11.775 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] schema data cleaned 
[INFO ] 2024-06-20 19:27:11.777 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] monitor closed 
[INFO ] 2024-06-20 19:27:11.779 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] close complete, cost 15 ms 
[INFO ] 2024-06-20 19:27:11.829 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] running status set to false 
[INFO ] 2024-06-20 19:27:11.833 - [任务 76(100)][bde37c6a-4802-4aef-9f81-25293a4751b8] - Node bde37c6a-4802-4aef-9f81-25293a4751b8[bde37c6a-4802-4aef-9f81-25293a4751b8] running status set to false 
[INFO ] 2024-06-20 19:27:11.833 - [任务 76(100)][bde37c6a-4802-4aef-9f81-25293a4751b8] - Node bde37c6a-4802-4aef-9f81-25293a4751b8[bde37c6a-4802-4aef-9f81-25293a4751b8] schema data cleaned 
[INFO ] 2024-06-20 19:27:11.833 - [任务 76(100)][bde37c6a-4802-4aef-9f81-25293a4751b8] - Node bde37c6a-4802-4aef-9f81-25293a4751b8[bde37c6a-4802-4aef-9f81-25293a4751b8] monitor closed 
[INFO ] 2024-06-20 19:27:11.834 - [任务 76(100)][bde37c6a-4802-4aef-9f81-25293a4751b8] - Node bde37c6a-4802-4aef-9f81-25293a4751b8[bde37c6a-4802-4aef-9f81-25293a4751b8] close complete, cost 11 ms 
[INFO ] 2024-06-20 19:27:11.840 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] schema data cleaned 
[INFO ] 2024-06-20 19:27:11.840 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] monitor closed 
[INFO ] 2024-06-20 19:27:11.841 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] close complete, cost 21 ms 
[INFO ] 2024-06-20 19:27:16.654 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:27:16.654 - [任务 76(100)][e65d92c5-1c09-4409-8a0a-64226f7240f7] - Node e65d92c5-1c09-4409-8a0a-64226f7240f7[e65d92c5-1c09-4409-8a0a-64226f7240f7] start preload schema,table counts: 0 
[INFO ] 2024-06-20 19:27:16.655 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:27:16.655 - [任务 76(100)][e65d92c5-1c09-4409-8a0a-64226f7240f7] - Node e65d92c5-1c09-4409-8a0a-64226f7240f7[e65d92c5-1c09-4409-8a0a-64226f7240f7] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:27:16.656 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:27:16.656 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:27:16.730 - [任务 76(100)][testReference] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 
[ERROR] 2024-06-20 19:27:16.731 - [任务 76(100)][testReference] - start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 <-- Error Message -->
start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "v07vIHLelQ96B1ihB6qZF8C0CEg=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:91)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "v07vIHLelQ96B1ihB6qZF8C0CEg=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 19 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "v07vIHLelQ96B1ihB6qZF8C0CEg=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 24 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "v07vIHLelQ96B1ihB6qZF8C0CEg=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 25 more

[INFO ] 2024-06-20 19:27:16.744 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:27:16.744 - [任务 76(100)][8226a4eb-5ce2-4f55-92eb-fa6fae085cec] - Node 8226a4eb-5ce2-4f55-92eb-fa6fae085cec[8226a4eb-5ce2-4f55-92eb-fa6fae085cec] start preload schema,table counts: 0 
[INFO ] 2024-06-20 19:27:16.749 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:27:16.765 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:27:16.765 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:27:16.767 - [任务 76(100)][8226a4eb-5ce2-4f55-92eb-fa6fae085cec] - Node 8226a4eb-5ce2-4f55-92eb-fa6fae085cec[8226a4eb-5ce2-4f55-92eb-fa6fae085cec] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:27:16.767 - [任务 76(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 
[ERROR] 2024-06-20 19:27:16.772 - [任务 76(100)][增强JS] - Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 <-- Error Message -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "v07vIHLelQ96B1ihB6qZF8C0CEg=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:55)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:143)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:110)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.create(ScriptExecutorsManager.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getDefaultScriptExecutor(HazelcastJavaScriptProcessorNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:162)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "v07vIHLelQ96B1ihB6qZF8C0CEg=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 20 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "v07vIHLelQ96B1ihB6qZF8C0CEg=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 25 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "v07vIHLelQ96B1ihB6qZF8C0CEg=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 26 more

[INFO ] 2024-06-20 19:27:16.796 - [任务 76(100)][testReference] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 
[ERROR] 2024-06-20 19:27:16.796 - [任务 76(100)][testReference] - start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 <-- Error Message -->
start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "v07vIHLelQ96B1ihB6qZF8C0CEg=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:91)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "v07vIHLelQ96B1ihB6qZF8C0CEg=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 19 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "v07vIHLelQ96B1ihB6qZF8C0CEg=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 24 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "v07vIHLelQ96B1ihB6qZF8C0CEg=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 25 more

[INFO ] 2024-06-20 19:27:16.834 - [任务 76(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 
[ERROR] 2024-06-20 19:27:16.835 - [任务 76(100)][增强JS] - Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 <-- Error Message -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "v07vIHLelQ96B1ihB6qZF8C0CEg=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:55)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:143)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:110)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.create(ScriptExecutorsManager.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getDefaultScriptExecutor(HazelcastJavaScriptProcessorNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:162)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "v07vIHLelQ96B1ihB6qZF8C0CEg=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 20 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "v07vIHLelQ96B1ihB6qZF8C0CEg=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 25 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882836, "i" : 10 } }, "signature" : { "hash" : { "$binary" : "v07vIHLelQ96B1ihB6qZF8C0CEg=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 26 more

[INFO ] 2024-06-20 19:27:19.310 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] running status set to false 
[INFO ] 2024-06-20 19:27:19.315 - [任务 76(100)][testReference] - PDK connector node stopped: null 
[INFO ] 2024-06-20 19:27:19.315 - [任务 76(100)][testReference] - PDK connector node released: null 
[INFO ] 2024-06-20 19:27:19.317 - [任务 76(100)][e65d92c5-1c09-4409-8a0a-64226f7240f7] - Node e65d92c5-1c09-4409-8a0a-64226f7240f7[e65d92c5-1c09-4409-8a0a-64226f7240f7] running status set to false 
[INFO ] 2024-06-20 19:27:19.319 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] schema data cleaned 
[INFO ] 2024-06-20 19:27:19.320 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] running status set to false 
[INFO ] 2024-06-20 19:27:19.324 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] monitor closed 
[INFO ] 2024-06-20 19:27:19.324 - [任务 76(100)][e65d92c5-1c09-4409-8a0a-64226f7240f7] - Node e65d92c5-1c09-4409-8a0a-64226f7240f7[e65d92c5-1c09-4409-8a0a-64226f7240f7] schema data cleaned 
[INFO ] 2024-06-20 19:27:19.327 - [任务 76(100)][e65d92c5-1c09-4409-8a0a-64226f7240f7] - Node e65d92c5-1c09-4409-8a0a-64226f7240f7[e65d92c5-1c09-4409-8a0a-64226f7240f7] monitor closed 
[INFO ] 2024-06-20 19:27:19.328 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] close complete, cost 54 ms 
[INFO ] 2024-06-20 19:27:19.346 - [任务 76(100)][e65d92c5-1c09-4409-8a0a-64226f7240f7] - Node e65d92c5-1c09-4409-8a0a-64226f7240f7[e65d92c5-1c09-4409-8a0a-64226f7240f7] close complete, cost 19 ms 
[INFO ] 2024-06-20 19:27:19.347 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] running status set to false 
[INFO ] 2024-06-20 19:27:19.358 - [任务 76(100)][testReference] - PDK connector node stopped: null 
[INFO ] 2024-06-20 19:27:19.358 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] schema data cleaned 
[INFO ] 2024-06-20 19:27:19.358 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] monitor closed 
[INFO ] 2024-06-20 19:27:19.358 - [任务 76(100)][testReference] - PDK connector node released: null 
[INFO ] 2024-06-20 19:27:19.358 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] close complete, cost 45 ms 
[INFO ] 2024-06-20 19:27:19.358 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] schema data cleaned 
[INFO ] 2024-06-20 19:27:19.358 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] monitor closed 
[INFO ] 2024-06-20 19:27:19.359 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] close complete, cost 20 ms 
[INFO ] 2024-06-20 19:27:19.363 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] running status set to false 
[INFO ] 2024-06-20 19:27:19.365 - [任务 76(100)][8226a4eb-5ce2-4f55-92eb-fa6fae085cec] - Node 8226a4eb-5ce2-4f55-92eb-fa6fae085cec[8226a4eb-5ce2-4f55-92eb-fa6fae085cec] running status set to false 
[INFO ] 2024-06-20 19:27:19.365 - [任务 76(100)][8226a4eb-5ce2-4f55-92eb-fa6fae085cec] - Node 8226a4eb-5ce2-4f55-92eb-fa6fae085cec[8226a4eb-5ce2-4f55-92eb-fa6fae085cec] schema data cleaned 
[INFO ] 2024-06-20 19:27:19.365 - [任务 76(100)][8226a4eb-5ce2-4f55-92eb-fa6fae085cec] - Node 8226a4eb-5ce2-4f55-92eb-fa6fae085cec[8226a4eb-5ce2-4f55-92eb-fa6fae085cec] monitor closed 
[INFO ] 2024-06-20 19:27:19.371 - [任务 76(100)][8226a4eb-5ce2-4f55-92eb-fa6fae085cec] - Node 8226a4eb-5ce2-4f55-92eb-fa6fae085cec[8226a4eb-5ce2-4f55-92eb-fa6fae085cec] close complete, cost 0 ms 
[INFO ] 2024-06-20 19:27:19.371 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] schema data cleaned 
[INFO ] 2024-06-20 19:27:19.371 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] monitor closed 
[INFO ] 2024-06-20 19:27:19.371 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] close complete, cost 8 ms 
[INFO ] 2024-06-20 19:27:24.048 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:27:24.048 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:27:24.048 - [任务 76(100)][e9b1f77f-e3d6-4812-9425-29f50e132a99] - Node e9b1f77f-e3d6-4812-9425-29f50e132a99[e9b1f77f-e3d6-4812-9425-29f50e132a99] start preload schema,table counts: 0 
[INFO ] 2024-06-20 19:27:24.049 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:27:24.050 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:27:24.050 - [任务 76(100)][e9b1f77f-e3d6-4812-9425-29f50e132a99] - Node e9b1f77f-e3d6-4812-9425-29f50e132a99[e9b1f77f-e3d6-4812-9425-29f50e132a99] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:27:24.078 - [任务 76(100)][testReference] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 
[ERROR] 2024-06-20 19:27:24.079 - [任务 76(100)][testReference] - start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 <-- Error Message -->
start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882843, "i" : 11 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882843, "i" : 11 } }, "signature" : { "hash" : { "$binary" : "QUEMm3lTYznVwy4FvDkiEt5v6Nc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:91)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882843, "i" : 11 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882843, "i" : 11 } }, "signature" : { "hash" : { "$binary" : "QUEMm3lTYznVwy4FvDkiEt5v6Nc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 19 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882843, "i" : 11 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882843, "i" : 11 } }, "signature" : { "hash" : { "$binary" : "QUEMm3lTYznVwy4FvDkiEt5v6Nc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 24 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882843, "i" : 11 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882843, "i" : 11 } }, "signature" : { "hash" : { "$binary" : "QUEMm3lTYznVwy4FvDkiEt5v6Nc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 25 more

[INFO ] 2024-06-20 19:27:24.132 - [任务 76(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 
[ERROR] 2024-06-20 19:27:24.132 - [任务 76(100)][增强JS] - Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 <-- Error Message -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882843, "i" : 11 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882843, "i" : 11 } }, "signature" : { "hash" : { "$binary" : "QUEMm3lTYznVwy4FvDkiEt5v6Nc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:55)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:143)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:110)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.create(ScriptExecutorsManager.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getDefaultScriptExecutor(HazelcastJavaScriptProcessorNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:162)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882843, "i" : 11 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882843, "i" : 11 } }, "signature" : { "hash" : { "$binary" : "QUEMm3lTYznVwy4FvDkiEt5v6Nc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 20 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882843, "i" : 11 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882843, "i" : 11 } }, "signature" : { "hash" : { "$binary" : "QUEMm3lTYznVwy4FvDkiEt5v6Nc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 25 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882843, "i" : 11 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882843, "i" : 11 } }, "signature" : { "hash" : { "$binary" : "QUEMm3lTYznVwy4FvDkiEt5v6Nc=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 26 more

[INFO ] 2024-06-20 19:27:26.632 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] running status set to false 
[INFO ] 2024-06-20 19:27:26.633 - [任务 76(100)][testReference] - PDK connector node stopped: null 
[INFO ] 2024-06-20 19:27:26.633 - [任务 76(100)][testReference] - PDK connector node released: null 
[INFO ] 2024-06-20 19:27:26.633 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] schema data cleaned 
[INFO ] 2024-06-20 19:27:26.633 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] monitor closed 
[INFO ] 2024-06-20 19:27:26.665 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] close complete, cost 10 ms 
[INFO ] 2024-06-20 19:27:26.666 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] running status set to false 
[INFO ] 2024-06-20 19:27:26.668 - [任务 76(100)][e9b1f77f-e3d6-4812-9425-29f50e132a99] - Node e9b1f77f-e3d6-4812-9425-29f50e132a99[e9b1f77f-e3d6-4812-9425-29f50e132a99] running status set to false 
[INFO ] 2024-06-20 19:27:26.668 - [任务 76(100)][e9b1f77f-e3d6-4812-9425-29f50e132a99] - Node e9b1f77f-e3d6-4812-9425-29f50e132a99[e9b1f77f-e3d6-4812-9425-29f50e132a99] schema data cleaned 
[INFO ] 2024-06-20 19:27:26.668 - [任务 76(100)][e9b1f77f-e3d6-4812-9425-29f50e132a99] - Node e9b1f77f-e3d6-4812-9425-29f50e132a99[e9b1f77f-e3d6-4812-9425-29f50e132a99] monitor closed 
[INFO ] 2024-06-20 19:27:26.669 - [任务 76(100)][e9b1f77f-e3d6-4812-9425-29f50e132a99] - Node e9b1f77f-e3d6-4812-9425-29f50e132a99[e9b1f77f-e3d6-4812-9425-29f50e132a99] close complete, cost 1 ms 
[INFO ] 2024-06-20 19:27:26.678 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] schema data cleaned 
[INFO ] 2024-06-20 19:27:26.678 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] monitor closed 
[INFO ] 2024-06-20 19:27:26.679 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] close complete, cost 15 ms 
[INFO ] 2024-06-20 19:27:29.769 - [任务 76(100)][e01f71bc-184b-496f-8f41-ce015dfc6d88] - Node e01f71bc-184b-496f-8f41-ce015dfc6d88[e01f71bc-184b-496f-8f41-ce015dfc6d88] start preload schema,table counts: 0 
[INFO ] 2024-06-20 19:27:29.769 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:27:29.769 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] start preload schema,table counts: 1 
[INFO ] 2024-06-20 19:27:29.769 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:27:29.769 - [任务 76(100)][e01f71bc-184b-496f-8f41-ce015dfc6d88] - Node e01f71bc-184b-496f-8f41-ce015dfc6d88[e01f71bc-184b-496f-8f41-ce015dfc6d88] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:27:29.769 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] preload schema finished, cost 0 ms 
[INFO ] 2024-06-20 19:27:29.805 - [任务 76(100)][testReference] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 
[ERROR] 2024-06-20 19:27:29.867 - [任务 76(100)][testReference] - start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429 <-- Error Message -->
start source runner failed: Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882849, "i" : 8 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882849, "i" : 8 } }, "signature" : { "hash" : { "$binary" : "WYJiwsK37yougJk6f9wCJlSU5y8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_c642b77f-5bb2-4b01-bb7d-9014ad2ab429
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:66)
	at io.tapdata.flow.engine.V2.entity.PdkStateMapEx.<init>(PdkStateMapEx.java:18)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:195)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:91)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882849, "i" : 8 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882849, "i" : 8 } }, "signature" : { "hash" : { "$binary" : "WYJiwsK37yougJk6f9wCJlSU5y8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 19 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882849, "i" : 8 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882849, "i" : 8 } }, "signature" : { "hash" : { "$binary" : "WYJiwsK37yougJk6f9wCJlSU5y8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 24 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882849, "i" : 8 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882849, "i" : 8 } }, "signature" : { "hash" : { "$binary" : "WYJiwsK37yougJk6f9wCJlSU5y8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 25 more

[INFO ] 2024-06-20 19:27:29.867 - [任务 76(100)][增强JS] - Exception skipping - The current exception does not match the skip exception strategy, message: Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 
[ERROR] 2024-06-20 19:27:30.071 - [任务 76(100)][增强JS] - Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7 <-- Error Message -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7

<-- Simple Stack Trace -->
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882849, "i" : 8 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882849, "i" : 8 } }, "signature" : { "hash" : { "$binary" : "WYJiwsK37yougJk6f9wCJlSU5y8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	...

<-- Full Stack Trace -->
Map name: PdkStateMap_ScriptExecutorsManager-667410c9486afb18a0dddfc4-7a8c11b7-c9a2-4c47-9aae-85b357e718c8-65af576e143bf05eeea6a5a7
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:93)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initConstructMap(PdkStateMap.java:77)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.<init>(PdkStateMap.java:55)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:143)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager$ScriptExecutor.<init>(ScriptExecutorsManager.java:110)
	at io.tapdata.flow.engine.V2.script.ScriptExecutorsManager.create(ScriptExecutorsManager.java:98)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.getDefaultScriptExecutor(HazelcastJavaScriptProcessorNode.java:184)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:162)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: [Hazelcast IMDG Persistence] - Init hazelcast IMap persistence failed. com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882849, "i" : 8 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882849, "i" : 8 } }, "signature" : { "hash" : { "$binary" : "WYJiwsK37yougJk6f9wCJlSU5y8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:64)
	at io.tapdata.construct.constructImpl.ConstructIMap.<init>(ConstructIMap.java:39)
	at io.tapdata.construct.constructImpl.DocumentIMap.<init>(DocumentIMap.java:23)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMap(PdkStateMap.java:184)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initDocumentIMapV2(PdkStateMap.java:180)
	at io.tapdata.flow.engine.V2.entity.PdkStateMap.initNodeStateMap(PdkStateMap.java:88)
	... 20 more
Caused by: java.lang.RuntimeException: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882849, "i" : 8 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882849, "i" : 8 } }, "signature" : { "hash" : { "$binary" : "WYJiwsK37yougJk6f9wCJlSU5y8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:120)
	at io.tapdata.flow.engine.V2.util.ExternalStorageUtil.initHZMapStorage(ExternalStorageUtil.java:61)
	... 25 more
Caused by: com.mongodb.MongoCommandException: Command failed with error 13 (Unauthorized): 'command createIndexes requires authentication' on server localhost:27017. The full response is { "operationTime" : { "$timestamp" : { "t" : 1718882849, "i" : 8 } }, "ok" : 0.0, "errmsg" : "command createIndexes requires authentication", "code" : 13, "codeName" : "Unauthorized", "$clusterTime" : { "clusterTime" : { "$timestamp" : { "t" : 1718882849, "i" : 8 } }, "signature" : { "hash" : { "$binary" : "WYJiwsK37yougJk6f9wCJlSU5y8=", "$type" : "00" }, "keyId" : { "$numberLong" : "7329804974198620162" } } } }
	at com.mongodb.internal.connection.ProtocolHelper.getCommandFailureException(ProtocolHelper.java:179)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:299)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:255)
	at com.mongodb.internal.connection.UsageTrackingInternalConnection.sendAndReceive(UsageTrackingInternalConnection.java:99)
	at com.mongodb.internal.connection.DefaultConnectionPool$PooledConnection.sendAndReceive(DefaultConnectionPool.java:444)
	at com.mongodb.internal.connection.CommandProtocolImpl.execute(CommandProtocolImpl.java:72)
	at com.mongodb.internal.connection.DefaultServer$DefaultServerProtocolExecutor.execute(DefaultServer.java:200)
	at com.mongodb.internal.connection.DefaultServerConnection.executeProtocol(DefaultServerConnection.java:269)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:131)
	at com.mongodb.internal.connection.DefaultServerConnection.command(DefaultServerConnection.java:123)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:242)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:233)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:170)
	at com.mongodb.operation.CommandOperationHelper.executeWrappedCommandProtocol(CommandOperationHelper.java:163)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:175)
	at com.mongodb.operation.CreateIndexesOperation$1.call(CreateIndexesOperation.java:170)
	at com.mongodb.operation.OperationHelper.withConnectionSource(OperationHelper.java:453)
	at com.mongodb.operation.OperationHelper.withConnection(OperationHelper.java:415)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:170)
	at com.mongodb.operation.CreateIndexesOperation.execute(CreateIndexesOperation.java:70)
	at com.mongodb.client.internal.MongoClientDelegate$DelegateOperationExecutor.execute(MongoClientDelegate.java:193)
	at com.mongodb.client.internal.MongoCollectionImpl.executeCreateIndexes(MongoCollectionImpl.java:805)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:788)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndexes(MongoCollectionImpl.java:783)
	at com.mongodb.client.internal.MongoCollectionImpl.createIndex(MongoCollectionImpl.java:768)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.createIndex(MongoDBIMap.java:47)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:39)
	at com.hazelcast.persistence.store.impl.MongoDBIMap.doInit(MongoDBIMap.java:18)
	at com.hazelcast.persistence.PersistenceStorage.initStore(PersistenceStorage.java:260)
	at com.hazelcast.persistence.PersistenceStorage.initMapStoreConfig(PersistenceStorage.java:110)
	... 26 more

[INFO ] 2024-06-20 19:27:32.341 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] running status set to false 
[INFO ] 2024-06-20 19:27:32.342 - [任务 76(100)][testReference] - PDK connector node stopped: null 
[INFO ] 2024-06-20 19:27:32.343 - [任务 76(100)][testReference] - PDK connector node released: null 
[INFO ] 2024-06-20 19:27:32.343 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] schema data cleaned 
[INFO ] 2024-06-20 19:27:32.343 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] monitor closed 
[INFO ] 2024-06-20 19:27:32.401 - [任务 76(100)][testReference] - Node testReference[c642b77f-5bb2-4b01-bb7d-9014ad2ab429] close complete, cost 3 ms 
[INFO ] 2024-06-20 19:27:32.401 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] running status set to false 
[INFO ] 2024-06-20 19:27:32.402 - [任务 76(100)][e01f71bc-184b-496f-8f41-ce015dfc6d88] - Node e01f71bc-184b-496f-8f41-ce015dfc6d88[e01f71bc-184b-496f-8f41-ce015dfc6d88] running status set to false 
[INFO ] 2024-06-20 19:27:32.402 - [任务 76(100)][e01f71bc-184b-496f-8f41-ce015dfc6d88] - Node e01f71bc-184b-496f-8f41-ce015dfc6d88[e01f71bc-184b-496f-8f41-ce015dfc6d88] schema data cleaned 
[INFO ] 2024-06-20 19:27:32.403 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] schema data cleaned 
[INFO ] 2024-06-20 19:27:32.403 - [任务 76(100)][e01f71bc-184b-496f-8f41-ce015dfc6d88] - Node e01f71bc-184b-496f-8f41-ce015dfc6d88[e01f71bc-184b-496f-8f41-ce015dfc6d88] monitor closed 
[INFO ] 2024-06-20 19:27:32.409 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] monitor closed 
[INFO ] 2024-06-20 19:27:32.410 - [任务 76(100)][e01f71bc-184b-496f-8f41-ce015dfc6d88] - Node e01f71bc-184b-496f-8f41-ce015dfc6d88[e01f71bc-184b-496f-8f41-ce015dfc6d88] close complete, cost 1 ms 
[INFO ] 2024-06-20 19:27:32.410 - [任务 76(100)][增强JS] - Node 增强JS[7a8c11b7-c9a2-4c47-9aae-85b357e718c8] close complete, cost 3 ms 
