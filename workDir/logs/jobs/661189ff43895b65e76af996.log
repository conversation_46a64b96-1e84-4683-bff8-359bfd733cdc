[INFO ] 2024-04-07 01:46:03.352 - [CLAIM-Kafka] - Task initialization... 
[INFO ] 2024-04-07 01:46:03.354 - [CLAIM-Kafka] - Start task milestones: 661189ff43895b65e76af996(CLAIM-Kafka) 
[INFO ] 2024-04-07 01:46:03.355 - [CLAIM-Kafka] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-04-07 01:46:03.355 - [CLAIM-Kafka] - The engine receives CLAIM-Kafka task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 01:46:03.355 - [CLAIM-Kafka][CLAIMKAFKA] - Node CLAIMKAFKA[ef29bae5-4aa1-4d7b-bf99-1b6bffb21866] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:46:03.355 - [CLAIM-Kafka][CLAIM] - Node CLAIM[2f2bd638-7e1b-4b5e-9ac2-bcbe97db3e7a] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:46:03.355 - [CLAIM-Kafka][CLAIM] - Node CLAIM[2f2bd638-7e1b-4b5e-9ac2-bcbe97db3e7a] preload schema finished, cost 17 ms 
[INFO ] 2024-04-07 01:46:03.355 - [CLAIM-Kafka][CLAIMKAFKA] - Node CLAIMKAFKA[ef29bae5-4aa1-4d7b-bf99-1b6bffb21866] preload schema finished, cost 17 ms 
[INFO ] 2024-04-07 01:46:04.112 - [CLAIM-Kafka][CLAIMKAFKA] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 01:46:04.113 - [CLAIM-Kafka][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-07 01:46:04.113 - [CLAIM-Kafka][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-07 01:46:04.113 - [CLAIM-Kafka][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 01:46:04.186 - [CLAIM-Kafka][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000021","position":3357987,"gtidSet":""} 
[INFO ] 2024-04-07 01:46:04.187 - [CLAIM-Kafka][CLAIM] - Initial sync started 
[INFO ] 2024-04-07 01:46:04.187 - [CLAIM-Kafka][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-04-07 01:46:04.252 - [CLAIM-Kafka][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-04-07 01:46:04.253 - [CLAIM-Kafka][CLAIM] - Query table 'CLAIM' counts: 1078 
[INFO ] 2024-04-07 01:46:04.468 - [CLAIM-Kafka][CLAIM] - Initial sync completed 
[INFO ] 2024-04-07 01:46:04.468 - [CLAIM-Kafka][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-07 01:46:04.470 - [CLAIM-Kafka][CLAIM] - Initial sync completed 
[INFO ] 2024-04-07 01:46:04.516 - [CLAIM-Kafka][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000021","position":3357987,"gtidSet":""} 
[INFO ] 2024-04-07 01:46:04.516 - [CLAIM-Kafka][CLAIM] - Starting mysql cdc, server name: 5b3f4902-931b-46fe-9492-12faf76a3d9c 
[INFO ] 2024-04-07 01:46:04.557 - [CLAIM-Kafka][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 866084065
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 5b3f4902-931b-46fe-9492-12faf76a3d9c
  database.port: 3306
  threadName: Debezium-Mysql-Connector-5b3f4902-931b-46fe-9492-12faf76a3d9c
  database.hostname: localhost
  database.password: ********
  name: 5b3f4902-931b-46fe-9492-12faf76a3d9c
  pdk.offset.string: {"name":"5b3f4902-931b-46fe-9492-12faf76a3d9c","offset":{"{\"server\":\"5b3f4902-931b-46fe-9492-12faf76a3d9c\"}":"{\"file\":\"binlog.000021\",\"pos\":3357987,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-07 01:46:04.557 - [CLAIM-Kafka][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-04-07 01:47:15.577 - [CLAIM-Kafka][CLAIM] - Read DDL: alter table CLAIM modify name varchar(70), about to be packaged as some event(s) 
[INFO ] 2024-04-07 01:47:15.578 - [CLAIM-Kafka][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='5b3f4902-931b-46fe-9492-12faf76a3d9c', offset={{"server":"5b3f4902-931b-46fe-9492-12faf76a3d9c"}={"ts_sec":1712425635,"file":"binlog.000021","pos":3358240,"server_id":1}}} 
[INFO ] 2024-04-07 01:47:15.578 - [CLAIM-Kafka][CLAIM] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='CLAIM', fieldName='name', dataTypeChange=io.tapdata.entity.event.ddl.entity.ValueChange@28e521ae, checkChange=null, constraintChange=null, nullableChange=null, commentChange=null, defaultChange=null, primaryChange=null} 
[INFO ] 2024-04-07 01:47:15.586 - [CLAIM-Kafka][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_661147b205642634b1daa0d2_661189ff43895b65e76af996 
[INFO ] 2024-04-07 01:47:15.787 - [CLAIM-Kafka][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-04-07 01:58:39.803 - [CLAIM-Kafka][CLAIM] - Node CLAIM[2f2bd638-7e1b-4b5e-9ac2-bcbe97db3e7a] running status set to false 
[INFO ] 2024-04-07 01:58:39.887 - [CLAIM-Kafka] - Start task milestones: 661189ff43895b65e76af996(CLAIM-Kafka) 
[INFO ] 2024-04-07 01:58:39.888 - [CLAIM-Kafka] - Task initialization... 
[INFO ] 2024-04-07 01:58:39.888 - [CLAIM-Kafka] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 01:58:39.990 - [CLAIM-Kafka] - The engine receives CLAIM-Kafka task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 01:58:40.325 - [CLAIM-Kafka][CLAIMKAFKA] - Node CLAIMKAFKA[ef29bae5-4aa1-4d7b-bf99-1b6bffb21866] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:58:40.326 - [CLAIM-Kafka][CLAIM] - Node CLAIM[2f2bd638-7e1b-4b5e-9ac2-bcbe97db3e7a] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:58:40.396 - [CLAIM-Kafka][CLAIMKAFKA] - Node CLAIMKAFKA[ef29bae5-4aa1-4d7b-bf99-1b6bffb21866] preload schema finished, cost 68 ms 
[INFO ] 2024-04-07 01:58:40.398 - [CLAIM-Kafka][CLAIM] - Node CLAIM[2f2bd638-7e1b-4b5e-9ac2-bcbe97db3e7a] preload schema finished, cost 67 ms 
[INFO ] 2024-04-07 01:58:41.415 - [CLAIM-Kafka][CLAIMKAFKA] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 01:58:41.796 - [CLAIM-Kafka][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-07 01:58:41.798 - [CLAIM-Kafka][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-07 01:58:41.800 - [CLAIM-Kafka][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 01:58:41.811 - [CLAIM-Kafka][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000021","position":3358240,"gtidSet":""} 
[INFO ] 2024-04-07 01:58:41.941 - [CLAIM-Kafka][CLAIM] - Initial sync started 
[INFO ] 2024-04-07 01:58:41.941 - [CLAIM-Kafka][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-04-07 01:58:41.957 - [CLAIM-Kafka][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-04-07 01:58:42.008 - [CLAIM-Kafka][CLAIM] - Query table 'CLAIM' counts: 1078 
[INFO ] 2024-04-07 01:58:42.593 - [CLAIM-Kafka][CLAIM] - Initial sync completed 
[INFO ] 2024-04-07 01:58:42.595 - [CLAIM-Kafka][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-07 01:58:42.596 - [CLAIM-Kafka][CLAIM] - Initial sync completed 
[INFO ] 2024-04-07 01:58:42.675 - [CLAIM-Kafka][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000021","position":3358240,"gtidSet":""} 
[INFO ] 2024-04-07 01:58:42.677 - [CLAIM-Kafka][CLAIM] - Starting mysql cdc, server name: 8e6a4508-f879-4caf-82dd-13ce60cce335 
[INFO ] 2024-04-07 01:58:42.879 - [CLAIM-Kafka][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1389565204
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 8e6a4508-f879-4caf-82dd-13ce60cce335
  database.port: 3306
  threadName: Debezium-Mysql-Connector-8e6a4508-f879-4caf-82dd-13ce60cce335
  database.hostname: localhost
  database.password: ********
  name: 8e6a4508-f879-4caf-82dd-13ce60cce335
  pdk.offset.string: {"name":"8e6a4508-f879-4caf-82dd-13ce60cce335","offset":{"{\"server\":\"8e6a4508-f879-4caf-82dd-13ce60cce335\"}":"{\"file\":\"binlog.000021\",\"pos\":3358240,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-07 01:58:42.948 - [CLAIM-Kafka][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-04-07 01:59:13.004 - [CLAIM-Kafka][CLAIM] - Read DDL: alter table CLAIM modify name varchar(80), about to be packaged as some event(s) 
[INFO ] 2024-04-07 01:59:13.010 - [CLAIM-Kafka][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldAttributesEvent
  - Offset: MysqlStreamOffset{name='8e6a4508-f879-4caf-82dd-13ce60cce335', offset={{"server":"8e6a4508-f879-4caf-82dd-13ce60cce335"}={"ts_sec":1712426352,"file":"binlog.000021","pos":3358493,"server_id":1}}} 
[INFO ] 2024-04-07 01:59:13.010 - [CLAIM-Kafka][CLAIM] - Source node received an ddl event: TapAlterFieldAttributesEvent{tableId='CLAIM', fieldName='name', dataTypeChange=io.tapdata.entity.event.ddl.entity.ValueChange@17f44093, checkChange=null, constraintChange=null, nullableChange=null, commentChange=null, defaultChange=null, primaryChange=null} 
[INFO ] 2024-04-07 01:59:13.214 - [CLAIM-Kafka][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_661147b205642634b1daa0d2_661189ff43895b65e76af996 
[INFO ] 2024-04-07 01:59:13.558 - [CLAIM-Kafka][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-04-07 02:01:16.550 - [CLAIM-Kafka] - Stop task milestones: 661189ff43895b65e76af996(CLAIM-Kafka)  
[INFO ] 2024-04-07 02:01:16.861 - [CLAIM-Kafka][CLAIM] - Node CLAIM[2f2bd638-7e1b-4b5e-9ac2-bcbe97db3e7a] running status set to false 
[INFO ] 2024-04-07 02:01:16.863 - [CLAIM-Kafka][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-04-07 02:01:16.863 - [CLAIM-Kafka][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-04-07 02:01:16.872 - [CLAIM-Kafka][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-2f2bd638-7e1b-4b5e-9ac2-bcbe97db3e7a 
[INFO ] 2024-04-07 02:01:16.873 - [CLAIM-Kafka][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-2f2bd638-7e1b-4b5e-9ac2-bcbe97db3e7a 
[INFO ] 2024-04-07 02:01:16.873 - [CLAIM-Kafka][CLAIM] - Node CLAIM[2f2bd638-7e1b-4b5e-9ac2-bcbe97db3e7a] schema data cleaned 
[INFO ] 2024-04-07 02:01:16.874 - [CLAIM-Kafka][CLAIM] - Node CLAIM[2f2bd638-7e1b-4b5e-9ac2-bcbe97db3e7a] monitor closed 
[INFO ] 2024-04-07 02:01:16.874 - [CLAIM-Kafka][CLAIM] - Node CLAIM[2f2bd638-7e1b-4b5e-9ac2-bcbe97db3e7a] close complete, cost 115 ms 
[INFO ] 2024-04-07 02:01:16.908 - [CLAIM-Kafka][CLAIMKAFKA] - Node CLAIMKAFKA[ef29bae5-4aa1-4d7b-bf99-1b6bffb21866] running status set to false 
[INFO ] 2024-04-07 02:01:16.908 - [CLAIM-Kafka][CLAIMKAFKA] - PDK connector node stopped: HazelcastTargetPdkDataNode-ef29bae5-4aa1-4d7b-bf99-1b6bffb21866 
[INFO ] 2024-04-07 02:01:16.908 - [CLAIM-Kafka][CLAIMKAFKA] - PDK connector node released: HazelcastTargetPdkDataNode-ef29bae5-4aa1-4d7b-bf99-1b6bffb21866 
[INFO ] 2024-04-07 02:01:16.908 - [CLAIM-Kafka][CLAIMKAFKA] - Node CLAIMKAFKA[ef29bae5-4aa1-4d7b-bf99-1b6bffb21866] schema data cleaned 
[INFO ] 2024-04-07 02:01:16.908 - [CLAIM-Kafka][CLAIMKAFKA] - Node CLAIMKAFKA[ef29bae5-4aa1-4d7b-bf99-1b6bffb21866] monitor closed 
[INFO ] 2024-04-07 02:01:16.909 - [CLAIM-Kafka][CLAIMKAFKA] - Node CLAIMKAFKA[ef29bae5-4aa1-4d7b-bf99-1b6bffb21866] close complete, cost 34 ms 
[INFO ] 2024-04-07 02:01:18.238 - [CLAIM-Kafka] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 02:01:18.241 - [CLAIM-Kafka] - Stopped task aspect(s) 
[INFO ] 2024-04-07 02:01:18.241 - [CLAIM-Kafka] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 02:01:18.266 - [CLAIM-Kafka] - Remove memory task client succeed, task: CLAIM-Kafka[661189ff43895b65e76af996] 
[INFO ] 2024-04-07 02:01:18.268 - [CLAIM-Kafka] - Destroy memory task client cache succeed, task: CLAIM-Kafka[661189ff43895b65e76af996] 
