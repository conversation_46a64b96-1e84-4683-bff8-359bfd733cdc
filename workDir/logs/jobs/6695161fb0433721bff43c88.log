[INFO ] 2024-07-15 20:30:06.566 - [任务 1] - Start task milestones: 6695161fb0433721bff43c88(任务 1) 
[INFO ] 2024-07-15 20:30:06.567 - [任务 1] - Task initialization... 
[INFO ] 2024-07-15 20:30:06.904 - [任务 1] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 20:30:07.047 - [任务 1] - The engine receives 任务 1 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 20:30:07.048 - [任务 1][TESTPOLICY] - Node TESTPOLICY[35cc7faa-8620-471e-ac16-3671a070b5eb] start preload schema,table counts: 1 
[INFO ] 2024-07-15 20:30:07.048 - [任务 1][TESTPOLICY] - Node TESTPOLICY[35cc7faa-8620-471e-ac16-3671a070b5eb] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 20:30:07.048 - [任务 1][POLICY] - Node POLICY[1c7ce4b7-79bb-48b9-8094-20065eae52d6] start preload schema,table counts: 1 
[INFO ] 2024-07-15 20:30:07.251 - [任务 1][POLICY] - Node POLICY[1c7ce4b7-79bb-48b9-8094-20065eae52d6] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 20:30:07.990 - [任务 1][POLICY] - Source node "POLICY" read batch size: 100 
[INFO ] 2024-07-15 20:30:07.991 - [任务 1][POLICY] - Source node "POLICY" event queue capacity: 200 
[INFO ] 2024-07-15 20:30:07.991 - [任务 1][POLICY] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 20:30:07.992 - [任务 1][TESTPOLICY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-07-15 20:30:08.103 - [任务 1][TESTPOLICY] - Table "test.TESTPOLICY" exists, skip auto create table 
[INFO ] 2024-07-15 20:30:08.103 - [任务 1][TESTPOLICY] - The table TESTPOLICY has already exist. 
[INFO ] 2024-07-15 20:30:08.167 - [任务 1][POLICY] - batch offset found: {},stream offset found: {"cdcOffset":1721046607,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 20:30:08.230 - [任务 1][POLICY] - Initial sync started 
[INFO ] 2024-07-15 20:30:08.231 - [任务 1][POLICY] - Starting batch read, table name: POLICY, offset: null 
[INFO ] 2024-07-15 20:30:08.261 - [任务 1][POLICY] - Table POLICY is going to be initial synced 
[INFO ] 2024-07-15 20:30:08.261 - [任务 1][POLICY] - Query table 'POLICY' counts: 695 
[INFO ] 2024-07-15 20:30:08.338 - [任务 1][POLICY] - Table [POLICY] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-15 20:30:08.340 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 20:30:08.342 - [任务 1][POLICY] - Incremental sync starting... 
[INFO ] 2024-07-15 20:30:08.344 - [任务 1][POLICY] - Initial sync completed 
[INFO ] 2024-07-15 20:30:08.395 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Initializing share cdc reader... 
[INFO ] 2024-07-15 20:30:08.395 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 1 - Check connection Mongo enable share cdc: true 
[INFO ] 2024-07-15 20:30:08.423 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 2 - Check task 任务 1 enable share cdc: true 
[INFO ] 2024-07-15 20:30:08.423 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 3 - Found log collector task: 来自Mongo的共享挖掘任务 
[INFO ] 2024-07-15 20:30:08.445 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Will use external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav391?authSource=admin', table='null', ttlDay=0] 
[INFO ] 2024-07-15 20:30:08.446 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695163a66ab5ede8a96025c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669515e9b0433721bff43c66_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_759076299, shareCdcTaskId=6695163ab0433721bff43cc3, connectionId=669515e9b0433721bff43c66) 
[INFO ] 2024-07-15 20:30:08.483 - [任务 1][POLICY] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav391.ExternalStorage_SHARE_CDC_759076299', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 20:30:08.484 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Step 5 - Init read thread pool completed 
[INFO ] 2024-07-15 20:30:08.486 - [任务 1][POLICY] - Init share cdc reader completed 
[INFO ] 2024-07-15 20:30:08.487 - [任务 1][POLICY] - Starting incremental sync, read from share log storage... 
[INFO ] 2024-07-15 20:30:08.490 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting listen share log storage... 
[INFO ] 2024-07-15 20:30:08.490 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Read table count: 1, partition size: 1, read thread number: 1 
[INFO ] 2024-07-15 20:30:08.500 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Found share cdc table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695163a66ab5ede8a96025c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669515e9b0433721bff43c66_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_759076299, shareCdcTaskId=6695163ab0433721bff43cc3, connectionId=669515e9b0433721bff43c66) 
[INFO ] 2024-07-15 20:30:08.500 - [任务 1][POLICY] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY_任务 1', name space: 'tapdatav391.ExternalStorage_SHARE_CDC_759076299', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 20:30:08.500 - [任务 1][POLICY] - [[Share CDC Task HZ Reader] - ] Successfully obtained construct, name: SHARE_CDC_来自Mongo的共享挖掘任务_POLICY_任务 1, external storage name: ExternalStorage_SHARE_CDC_759076299 
[INFO ] 2024-07-15 20:30:08.505 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read log from hazelcast construct, tables: [POLICY] 
[INFO ] 2024-07-15 20:30:08.510 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find sequence in construct(POLICY) by timestamp(2024-07-15T12:30:07.989Z): 1 
[INFO ] 2024-07-15 20:30:08.511 - [任务 1][POLICY] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-15 20:30:08.513 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Starting read 'POLICY' log, sequence: 1 
[INFO ] 2024-07-15 20:30:08.714 - [任务 1][POLICY] - [Share CDC Task HZ Reader] - Find by POLICY filter: {sequence=1} 
[INFO ] 2024-07-15 20:30:55.350 - [任务 1][POLICY] - Node POLICY[1c7ce4b7-79bb-48b9-8094-20065eae52d6] running status set to false 
[INFO ] 2024-07-15 20:30:55.350 - [任务 1][POLICY] - Incremental sync completed 
[INFO ] 2024-07-15 20:30:55.382 - [任务 1][POLICY] - PDK connector node stopped: HazelcastSourcePdkDataNode-1c7ce4b7-79bb-48b9-8094-20065eae52d6 
[INFO ] 2024-07-15 20:30:55.382 - [任务 1][POLICY] - PDK connector node released: HazelcastSourcePdkDataNode-1c7ce4b7-79bb-48b9-8094-20065eae52d6 
[INFO ] 2024-07-15 20:30:55.386 - [任务 1][POLICY] - Node POLICY[1c7ce4b7-79bb-48b9-8094-20065eae52d6] schema data cleaned 
[INFO ] 2024-07-15 20:30:55.386 - [任务 1][POLICY] - Node POLICY[1c7ce4b7-79bb-48b9-8094-20065eae52d6] monitor closed 
[INFO ] 2024-07-15 20:30:55.390 - [任务 1][POLICY] - Node POLICY[1c7ce4b7-79bb-48b9-8094-20065eae52d6] close complete, cost 58 ms 
[INFO ] 2024-07-15 20:30:55.415 - [任务 1][TESTPOLICY] - Node TESTPOLICY[35cc7faa-8620-471e-ac16-3671a070b5eb] running status set to false 
[INFO ] 2024-07-15 20:30:55.415 - [任务 1][TESTPOLICY] - PDK connector node stopped: HazelcastTargetPdkDataNode-35cc7faa-8620-471e-ac16-3671a070b5eb 
[INFO ] 2024-07-15 20:30:55.418 - [任务 1][TESTPOLICY] - PDK connector node released: HazelcastTargetPdkDataNode-35cc7faa-8620-471e-ac16-3671a070b5eb 
[INFO ] 2024-07-15 20:30:55.419 - [任务 1][TESTPOLICY] - Node TESTPOLICY[35cc7faa-8620-471e-ac16-3671a070b5eb] schema data cleaned 
[INFO ] 2024-07-15 20:30:55.419 - [任务 1][TESTPOLICY] - Node TESTPOLICY[35cc7faa-8620-471e-ac16-3671a070b5eb] monitor closed 
[INFO ] 2024-07-15 20:30:55.622 - [任务 1][TESTPOLICY] - Node TESTPOLICY[35cc7faa-8620-471e-ac16-3671a070b5eb] close complete, cost 27 ms 
[INFO ] 2024-07-15 20:31:00.032 - [任务 1] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 20:31:00.034 - [任务 1] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7c2e36f3 
[INFO ] 2024-07-15 20:31:00.162 - [任务 1] - Stop task milestones: 6695161fb0433721bff43c88(任务 1)  
[INFO ] 2024-07-15 20:31:00.162 - [任务 1] - Stopped task aspect(s) 
[INFO ] 2024-07-15 20:31:00.163 - [任务 1] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 20:31:00.230 - [任务 1] - Remove memory task client succeed, task: 任务 1[6695161fb0433721bff43c88] 
[INFO ] 2024-07-15 20:31:00.230 - [任务 1] - Destroy memory task client cache succeed, task: 任务 1[6695161fb0433721bff43c88] 
