[INFO ] 2024-03-29 17:31:57.094 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Task initialization... 
[INFO ] 2024-03-29 17:31:57.100 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Start task milestones: 66068a6a5c94df48bc70bd3a(employees_import_import_import_import_import_import_import_import_import_import_import) 
[INFO ] 2024-03-29 17:31:57.286 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Node performs snapshot read by order list: [ null ] -> [ null,null ] -> [ null ] -> [ null ] 
[INFO ] 2024-03-29 17:31:57.909 - [employees_import_import_import_import_import_import_import_import_import_import_import] - The engine receives employees_import_import_import_import_import_import_import_import_import_import_import task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 17:31:58.005 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[7f57f82c-db71-4b08-99d8-5620afdc209d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:31:58.006 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[34c16d5c-d315-458f-8287-c4529e01f1d9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:31:58.006 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[2da87501-4d24-4b11-b021-35bc8f75c87c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:31:58.006 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[ac99608c-4577-43fc-ba71-fc3627861a91] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:31:58.006 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[a28548a8-a70b-4734-9b7f-85e92c5fc2a8] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:31:58.006 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Node Territories[19e59acd-9841-4cf7-bc4e-bd33067139b1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:31:58.006 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[1ba8c468-3477-42e7-a5b9-c035659bb9dd] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:31:58.007 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[ac4ad9fa-ce9d-4d89-9be7-8b9b4dec9391] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:31:58.007 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Node merge[2c656bdd-fb5f-4f66-887f-f9460a80e1be] start preload schema,table counts: 6 
[INFO ] 2024-03-29 17:31:58.025 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[837c950f-4961-4f24-b40c-5563d769bc9a] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:31:58.025 - [employees_import_import_import_import_import_import_import_import_import_import_import][employees] - Node employees[01b1de5b-e930-411b-8dc3-13d83a4b52b1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:31:58.081 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[87a637c7-b7e5-4ed5-84a1-347dd6817644] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:31:58.084 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Node Region[9d9ab82d-01be-4a50-b612-01a66f44b2e1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:31:58.084 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[c91421bd-beb5-4d0a-9dc0-17ce6755a98a] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:31:58.084 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[a666fdf2-1847-4a31-b556-150e6cb647a5] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:31:58.281 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Node Region[9d9ab82d-01be-4a50-b612-01a66f44b2e1] preload schema finished, cost 183 ms 
[INFO ] 2024-03-29 17:31:58.285 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[34c16d5c-d315-458f-8287-c4529e01f1d9] preload schema finished, cost 264 ms 
[INFO ] 2024-03-29 17:31:58.322 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Node Territories[19e59acd-9841-4cf7-bc4e-bd33067139b1] preload schema finished, cost 259 ms 
[INFO ] 2024-03-29 17:31:58.323 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[a666fdf2-1847-4a31-b556-150e6cb647a5] preload schema finished, cost 179 ms 
[INFO ] 2024-03-29 17:31:58.323 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[ac99608c-4577-43fc-ba71-fc3627861a91] preload schema finished, cost 261 ms 
[INFO ] 2024-03-29 17:31:58.324 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[2da87501-4d24-4b11-b021-35bc8f75c87c] preload schema finished, cost 261 ms 
[INFO ] 2024-03-29 17:31:58.337 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[ac4ad9fa-ce9d-4d89-9be7-8b9b4dec9391] preload schema finished, cost 259 ms 
[INFO ] 2024-03-29 17:31:58.337 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[87a637c7-b7e5-4ed5-84a1-347dd6817644] preload schema finished, cost 187 ms 
[INFO ] 2024-03-29 17:31:58.340 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[7f57f82c-db71-4b08-99d8-5620afdc209d] preload schema finished, cost 321 ms 
[INFO ] 2024-03-29 17:31:58.340 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[c91421bd-beb5-4d0a-9dc0-17ce6755a98a] preload schema finished, cost 235 ms 
[INFO ] 2024-03-29 17:31:58.341 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[1ba8c468-3477-42e7-a5b9-c035659bb9dd] preload schema finished, cost 315 ms 
[INFO ] 2024-03-29 17:31:58.345 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[a28548a8-a70b-4734-9b7f-85e92c5fc2a8] preload schema finished, cost 315 ms 
[INFO ] 2024-03-29 17:31:58.345 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[837c950f-4961-4f24-b40c-5563d769bc9a] preload schema finished, cost 235 ms 
[INFO ] 2024-03-29 17:31:58.546 - [employees_import_import_import_import_import_import_import_import_import_import_import][employees] - Node employees[01b1de5b-e930-411b-8dc3-13d83a4b52b1] preload schema finished, cost 317 ms 
[INFO ] 2024-03-29 17:31:58.644 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Node merge[2c656bdd-fb5f-4f66-887f-f9460a80e1be] preload schema finished, cost 635 ms 
[INFO ] 2024-03-29 17:31:58.650 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 17:31:58.650 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  Territories(a666fdf2-1847-4a31-b556-150e6cb647a5)
    ->Region(837c950f-4961-4f24-b40c-5563d769bc9a)
} 
[INFO ] 2024-03-29 17:31:58.651 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  EmployeeTerritories(ac99608c-4577-43fc-ba71-fc3627861a91)
    ->Territories(a666fdf2-1847-4a31-b556-150e6cb647a5)
} 
[INFO ] 2024-03-29 17:31:58.653 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  Employees(7f57f82c-db71-4b08-99d8-5620afdc209d)
    ->Employees(c91421bd-beb5-4d0a-9dc0-17ce6755a98a)
    ->EmployeeTerritories(ac99608c-4577-43fc-ba71-fc3627861a91)
} 
[INFO ] 2024-03-29 17:31:59.786 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" read batch size: 500 
[INFO ] 2024-03-29 17:31:59.788 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" event queue capacity: 1000 
[INFO ] 2024-03-29 17:31:59.788 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 17:31:59.929 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:31:59.930 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" read batch size: 500 
[INFO ] 2024-03-29 17:31:59.930 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" event queue capacity: 1000 
[INFO ] 2024-03-29 17:31:59.930 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 17:31:59.936 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:32:00.102 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Initial sync started 
[INFO ] 2024-03-29 17:32:00.104 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Node[Employees] is waiting for running 
[INFO ] 2024-03-29 17:32:00.104 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting batch read, table name: Employees, offset: null 
[INFO ] 2024-03-29 17:32:00.143 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Table Employees is going to be initial synced 
[INFO ] 2024-03-29 17:32:00.144 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Source node "Region" read batch size: 500 
[INFO ] 2024-03-29 17:32:00.145 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Source node "Region" event queue capacity: 1000 
[INFO ] 2024-03-29 17:32:00.145 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 17:32:00.176 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:32:00.191 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Query table 'Employees' counts: 0 
[INFO ] 2024-03-29 17:32:00.192 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 17:32:00.211 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Employees_c91421bd-beb5-4d0a-9dc0-17ce6755a98a__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 17:32:00.341 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Node[Region] is waiting for running 
[INFO ] 2024-03-29 17:32:00.344 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Source node "Territories" read batch size: 500 
[INFO ] 2024-03-29 17:32:00.344 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Source node "Territories" event queue capacity: 1000 
[INFO ] 2024-03-29 17:32:00.344 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 17:32:00.345 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:32:00.502 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Node[Territories] is waiting for running 
[INFO ] 2024-03-29 17:32:00.502 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_EmployeeTerritories_ac99608c-4577-43fc-ba71-fc3627861a91__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 17:32:00.896 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Territories_a666fdf2-1847-4a31-b556-150e6cb647a5__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 17:32:00.916 - [employees_import_import_import_import_import_import_import_import_import_import_import][employees] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 17:32:00.939 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Source node "EmployeeTerritories" read batch size: 500 
[INFO ] 2024-03-29 17:32:00.947 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Source node "EmployeeTerritories" event queue capacity: 1000 
[INFO ] 2024-03-29 17:32:00.947 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 17:32:00.947 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:32:01.052 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Region_837c950f-4961-4f24-b40c-5563d769bc9a__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 17:32:01.053 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Node[EmployeeTerritories] is waiting for running 
[INFO ] 2024-03-29 17:32:01.068 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 17:32:01.071 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 17:32:01.913 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Node[Employees] finish, notify next layer to run 
[INFO ] 2024-03-29 17:32:01.914 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Initial sync started 
[INFO ] 2024-03-29 17:32:01.915 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Initial sync started 
[INFO ] 2024-03-29 17:32:01.915 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Next layer have been notified: [null,null] 
[INFO ] 2024-03-29 17:32:01.929 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Starting batch read, table name: EmployeeTerritories, offset: null 
[INFO ] 2024-03-29 17:32:01.940 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting batch read, table name: Employees, offset: null 
[INFO ] 2024-03-29 17:32:01.956 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Table EmployeeTerritories is going to be initial synced 
[INFO ] 2024-03-29 17:32:01.969 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Table Employees is going to be initial synced 
[INFO ] 2024-03-29 17:32:02.005 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Query table 'EmployeeTerritories' counts: 1 
[INFO ] 2024-03-29 17:32:02.017 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Query table 'Employees' counts: 0 
[INFO ] 2024-03-29 17:32:02.017 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 17:32:02.230 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Initial sync completed 
[INFO ] 2024-03-29 17:32:03.067 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Node[EmployeeTerritories] finish, notify next layer to run 
[INFO ] 2024-03-29 17:32:03.073 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 17:32:03.073 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Initial sync started 
[INFO ] 2024-03-29 17:32:03.074 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Starting batch read, table name: Territories, offset: null 
[INFO ] 2024-03-29 17:32:03.129 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Table Territories is going to be initial synced 
[INFO ] 2024-03-29 17:32:03.130 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Query table 'Territories' counts: 1 
[INFO ] 2024-03-29 17:32:03.130 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Initial sync completed 
[INFO ] 2024-03-29 17:32:04.140 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Node[Territories] finish, notify next layer to run 
[INFO ] 2024-03-29 17:32:04.164 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 17:32:04.166 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Initial sync started 
[INFO ] 2024-03-29 17:32:04.166 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Starting batch read, table name: Region, offset: null 
[INFO ] 2024-03-29 17:32:04.167 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Table Region is going to be initial synced 
[INFO ] 2024-03-29 17:32:04.198 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Query table 'Region' counts: 1 
[INFO ] 2024-03-29 17:32:04.198 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Initial sync completed 
[INFO ] 2024-03-29 17:32:06.051 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Incremental sync starting... 
[INFO ] 2024-03-29 17:32:06.086 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 17:32:06.091 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting stream read, table list: [Employees], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:32:06.091 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Incremental sync starting... 
[INFO ] 2024-03-29 17:32:06.091 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Initial sync completed 
[INFO ] 2024-03-29 17:32:06.091 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Starting stream read, table list: [EmployeeTerritories], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:32:06.116 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting mysql cdc, server name: cd3a3ac3-8253-4945-8369-c609a70212b5 
[INFO ] 2024-03-29 17:32:06.116 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Starting mysql cdc, server name: 14e80da5-804f-46e6-9c5c-81124a7bf21d 
[INFO ] 2024-03-29 17:32:06.134 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Incremental sync starting... 
[INFO ] 2024-03-29 17:32:06.134 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Initial sync completed 
[INFO ] 2024-03-29 17:32:06.186 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Starting stream read, table list: [Territories], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:32:06.187 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Starting mysql cdc, server name: c7ded3cb-031e-4b0c-901d-cf57d36ab72a 
[INFO ] 2024-03-29 17:32:06.200 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 141029797
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 14e80da5-804f-46e6-9c5c-81124a7bf21d
  database.port: 3307
  threadName: Debezium-Mysql-Connector-14e80da5-804f-46e6-9c5c-81124a7bf21d
  database.hostname: 127.0.0.1
  database.password: ********
  name: 14e80da5-804f-46e6-9c5c-81124a7bf21d
  pdk.offset.string: {"name":"14e80da5-804f-46e6-9c5c-81124a7bf21d","offset":{"{\"server\":\"14e80da5-804f-46e6-9c5c-81124a7bf21d\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.EmployeeTerritories
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 17:32:06.201 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1084083869
  time.precision.mode: adaptive_time_microseconds
  database.server.name: c7ded3cb-031e-4b0c-901d-cf57d36ab72a
  database.port: 3307
  threadName: Debezium-Mysql-Connector-c7ded3cb-031e-4b0c-901d-cf57d36ab72a
  database.hostname: 127.0.0.1
  database.password: ********
  name: c7ded3cb-031e-4b0c-901d-cf57d36ab72a
  pdk.offset.string: {"name":"c7ded3cb-031e-4b0c-901d-cf57d36ab72a","offset":{"{\"server\":\"c7ded3cb-031e-4b0c-901d-cf57d36ab72a\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Territories
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 17:32:06.201 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 544418537
  time.precision.mode: adaptive_time_microseconds
  database.server.name: cd3a3ac3-8253-4945-8369-c609a70212b5
  database.port: 3307
  threadName: Debezium-Mysql-Connector-cd3a3ac3-8253-4945-8369-c609a70212b5
  database.hostname: 127.0.0.1
  database.password: ********
  name: cd3a3ac3-8253-4945-8369-c609a70212b5
  pdk.offset.string: {"name":"cd3a3ac3-8253-4945-8369-c609a70212b5","offset":{"{\"server\":\"cd3a3ac3-8253-4945-8369-c609a70212b5\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Employees
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 17:32:06.208 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Incremental sync starting... 
[INFO ] 2024-03-29 17:32:06.208 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Initial sync completed 
[INFO ] 2024-03-29 17:32:06.210 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Starting stream read, table list: [Region], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:32:06.223 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Incremental sync starting... 
[INFO ] 2024-03-29 17:32:06.223 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 17:32:06.256 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting stream read, table list: [Employees], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:32:06.258 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting mysql cdc, server name: b3be3fc4-c60f-45b9-adaa-e77bc5b33627 
[INFO ] 2024-03-29 17:32:06.258 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Starting mysql cdc, server name: 2531a6bb-0fb4-430c-bca9-acb7f48522ca 
[INFO ] 2024-03-29 17:32:06.268 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1527794848
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b3be3fc4-c60f-45b9-adaa-e77bc5b33627
  database.port: 3307
  threadName: Debezium-Mysql-Connector-b3be3fc4-c60f-45b9-adaa-e77bc5b33627
  database.hostname: 127.0.0.1
  database.password: ********
  name: b3be3fc4-c60f-45b9-adaa-e77bc5b33627
  pdk.offset.string: {"name":"b3be3fc4-c60f-45b9-adaa-e77bc5b33627","offset":{"{\"server\":\"b3be3fc4-c60f-45b9-adaa-e77bc5b33627\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Employees
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 17:32:06.269 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1336374359
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 2531a6bb-0fb4-430c-bca9-acb7f48522ca
  database.port: 3307
  threadName: Debezium-Mysql-Connector-2531a6bb-0fb4-430c-bca9-acb7f48522ca
  database.hostname: 127.0.0.1
  database.password: ********
  name: 2531a6bb-0fb4-430c-bca9-acb7f48522ca
  pdk.offset.string: {"name":"2531a6bb-0fb4-430c-bca9-acb7f48522ca","offset":{"{\"server\":\"2531a6bb-0fb4-430c-bca9-acb7f48522ca\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Region
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 17:32:06.638 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Connector Mysql incremental start succeed, tables: [Employees], data change syncing 
[INFO ] 2024-03-29 17:32:06.638 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Connector Mysql incremental start succeed, tables: [Employees], data change syncing 
[INFO ] 2024-03-29 17:32:06.638 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Connector Mysql incremental start succeed, tables: [Region], data change syncing 
[INFO ] 2024-03-29 17:32:06.638 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Connector Mysql incremental start succeed, tables: [EmployeeTerritories], data change syncing 
[INFO ] 2024-03-29 17:32:06.638 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Connector Mysql incremental start succeed, tables: [Territories], data change syncing 
[INFO ] 2024-03-29 18:09:47.243 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Task initialization... 
[INFO ] 2024-03-29 18:09:47.247 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Start task milestones: 66068a6a5c94df48bc70bd3a(employees_import_import_import_import_import_import_import_import_import_import_import) 
[INFO ] 2024-03-29 18:09:47.402 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Node performs snapshot read by order list: [ null ] -> [ null,null ] -> [ null ] -> [ null ] 
[INFO ] 2024-03-29 18:09:48.236 - [employees_import_import_import_import_import_import_import_import_import_import_import] - The engine receives employees_import_import_import_import_import_import_import_import_import_import_import task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 18:09:49.568 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Node Territories[19e59acd-9841-4cf7-bc4e-bd33067139b1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:09:49.569 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[7f57f82c-db71-4b08-99d8-5620afdc209d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:09:49.569 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[a666fdf2-1847-4a31-b556-150e6cb647a5] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:09:49.569 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[ac99608c-4577-43fc-ba71-fc3627861a91] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:09:49.569 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[2da87501-4d24-4b11-b021-35bc8f75c87c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:09:49.570 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[a28548a8-a70b-4734-9b7f-85e92c5fc2a8] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:09:49.649 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[ac4ad9fa-ce9d-4d89-9be7-8b9b4dec9391] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:09:49.650 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[837c950f-4961-4f24-b40c-5563d769bc9a] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:09:49.652 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[c91421bd-beb5-4d0a-9dc0-17ce6755a98a] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:09:49.652 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[34c16d5c-d315-458f-8287-c4529e01f1d9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:09:49.652 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[87a637c7-b7e5-4ed5-84a1-347dd6817644] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:09:49.653 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Node Region[9d9ab82d-01be-4a50-b612-01a66f44b2e1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:09:49.653 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[1ba8c468-3477-42e7-a5b9-c035659bb9dd] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:09:49.654 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Node merge[2c656bdd-fb5f-4f66-887f-f9460a80e1be] start preload schema,table counts: 6 
[INFO ] 2024-03-29 18:09:49.665 - [employees_import_import_import_import_import_import_import_import_import_import_import][employees] - Node employees[01b1de5b-e930-411b-8dc3-13d83a4b52b1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:09:50.369 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[ac4ad9fa-ce9d-4d89-9be7-8b9b4dec9391] preload schema finished, cost 633 ms 
[INFO ] 2024-03-29 18:09:50.389 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[837c950f-4961-4f24-b40c-5563d769bc9a] preload schema finished, cost 634 ms 
[INFO ] 2024-03-29 18:09:50.402 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[c91421bd-beb5-4d0a-9dc0-17ce6755a98a] preload schema finished, cost 689 ms 
[INFO ] 2024-03-29 18:09:50.422 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Node Territories[19e59acd-9841-4cf7-bc4e-bd33067139b1] preload schema finished, cost 733 ms 
[INFO ] 2024-03-29 18:09:50.426 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[1ba8c468-3477-42e7-a5b9-c035659bb9dd] preload schema finished, cost 686 ms 
[INFO ] 2024-03-29 18:09:50.428 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[7f57f82c-db71-4b08-99d8-5620afdc209d] preload schema finished, cost 773 ms 
[INFO ] 2024-03-29 18:09:50.433 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[a28548a8-a70b-4734-9b7f-85e92c5fc2a8] preload schema finished, cost 772 ms 
[INFO ] 2024-03-29 18:09:50.446 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Node Region[9d9ab82d-01be-4a50-b612-01a66f44b2e1] preload schema finished, cost 629 ms 
[INFO ] 2024-03-29 18:09:50.450 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[34c16d5c-d315-458f-8287-c4529e01f1d9] preload schema finished, cost 633 ms 
[INFO ] 2024-03-29 18:09:50.450 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[2da87501-4d24-4b11-b021-35bc8f75c87c] preload schema finished, cost 719 ms 
[INFO ] 2024-03-29 18:09:50.450 - [employees_import_import_import_import_import_import_import_import_import_import_import][employees] - Node employees[01b1de5b-e930-411b-8dc3-13d83a4b52b1] preload schema finished, cost 691 ms 
[INFO ] 2024-03-29 18:09:50.450 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[87a637c7-b7e5-4ed5-84a1-347dd6817644] preload schema finished, cost 633 ms 
[INFO ] 2024-03-29 18:09:50.451 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[ac99608c-4577-43fc-ba71-fc3627861a91] preload schema finished, cost 718 ms 
[INFO ] 2024-03-29 18:09:50.455 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[a666fdf2-1847-4a31-b556-150e6cb647a5] preload schema finished, cost 716 ms 
[INFO ] 2024-03-29 18:09:50.640 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Node merge[2c656bdd-fb5f-4f66-887f-f9460a80e1be] preload schema finished, cost 980 ms 
[INFO ] 2024-03-29 18:09:50.641 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 18:09:50.650 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  Territories(a666fdf2-1847-4a31-b556-150e6cb647a5)
    ->Region(837c950f-4961-4f24-b40c-5563d769bc9a)
} 
[INFO ] 2024-03-29 18:09:50.656 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  EmployeeTerritories(ac99608c-4577-43fc-ba71-fc3627861a91)
    ->Territories(a666fdf2-1847-4a31-b556-150e6cb647a5)
} 
[INFO ] 2024-03-29 18:09:50.658 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  Employees(7f57f82c-db71-4b08-99d8-5620afdc209d)
    ->Employees(c91421bd-beb5-4d0a-9dc0-17ce6755a98a)
    ->EmployeeTerritories(ac99608c-4577-43fc-ba71-fc3627861a91)
} 
[INFO ] 2024-03-29 18:09:53.506 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Employees_c91421bd-beb5-4d0a-9dc0-17ce6755a98a__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:09:53.722 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_EmployeeTerritories_ac99608c-4577-43fc-ba71-fc3627861a91__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:09:54.138 - [employees_import_import_import_import_import_import_import_import_import_import_import][employees] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 18:09:54.227 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Source node "Region" read batch size: 500 
[INFO ] 2024-03-29 18:09:54.228 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" read batch size: 500 
[INFO ] 2024-03-29 18:09:54.229 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Source node "EmployeeTerritories" read batch size: 500 
[INFO ] 2024-03-29 18:09:54.244 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Source node "Region" event queue capacity: 1000 
[INFO ] 2024-03-29 18:09:54.252 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Source node "EmployeeTerritories" event queue capacity: 1000 
[INFO ] 2024-03-29 18:09:54.255 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" event queue capacity: 1000 
[INFO ] 2024-03-29 18:09:54.255 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-29 18:09:54.255 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-29 18:09:54.255 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-29 18:09:54.280 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - batch offset found: {},stream offset found: {"name":"cd3a3ac3-8253-4945-8369-c609a70212b5","offset":{"{\"server\":\"cd3a3ac3-8253-4945-8369-c609a70212b5\"}":"{\"ts_sec\":1711704727,\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:09:54.281 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - batch offset found: {"Region":{}},stream offset found: {"name":"2531a6bb-0fb4-430c-bca9-acb7f48522ca","offset":{"{\"server\":\"2531a6bb-0fb4-430c-bca9-acb7f48522ca\"}":"{\"ts_sec\":1711704727,\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:09:54.282 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - batch offset found: {"EmployeeTerritories":{}},stream offset found: {"name":"14e80da5-804f-46e6-9c5c-81124a7bf21d","offset":{"{\"server\":\"14e80da5-804f-46e6-9c5c-81124a7bf21d\"}":"{\"ts_sec\":1711704727,\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:09:54.604 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Source node "Territories" read batch size: 500 
[INFO ] 2024-03-29 18:09:54.619 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Source node "Territories" event queue capacity: 1000 
[INFO ] 2024-03-29 18:09:54.620 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-29 18:09:54.788 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - batch offset found: {"Territories":{}},stream offset found: {"name":"c7ded3cb-031e-4b0c-901d-cf57d36ab72a","offset":{"{\"server\":\"c7ded3cb-031e-4b0c-901d-cf57d36ab72a\"}":"{\"ts_sec\":1711704727,\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:09:54.788 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" read batch size: 500 
[INFO ] 2024-03-29 18:09:54.789 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" event queue capacity: 1000 
[INFO ] 2024-03-29 18:09:54.789 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-29 18:09:54.820 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - batch offset found: {},stream offset found: {"name":"b3be3fc4-c60f-45b9-adaa-e77bc5b33627","offset":{"{\"server\":\"b3be3fc4-c60f-45b9-adaa-e77bc5b33627\"}":"{\"ts_sec\":1711704727,\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:09:54.824 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Incremental sync starting... 
[INFO ] 2024-03-29 18:09:54.824 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Incremental sync starting... 
[INFO ] 2024-03-29 18:09:54.824 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Incremental sync starting... 
[INFO ] 2024-03-29 18:09:54.824 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 18:09:54.824 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Incremental sync starting... 
[INFO ] 2024-03-29 18:09:54.827 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Initial sync completed 
[INFO ] 2024-03-29 18:09:54.875 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Initial sync completed 
[INFO ] 2024-03-29 18:09:54.915 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Initial sync completed 
[INFO ] 2024-03-29 18:09:54.932 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting stream read, table list: [Employees], offset: {"name":"cd3a3ac3-8253-4945-8369-c609a70212b5","offset":{"{\"server\":\"cd3a3ac3-8253-4945-8369-c609a70212b5\"}":"{\"ts_sec\":1711704727,\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:09:54.934 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Starting stream read, table list: [Territories], offset: {"name":"c7ded3cb-031e-4b0c-901d-cf57d36ab72a","offset":{"{\"server\":\"c7ded3cb-031e-4b0c-901d-cf57d36ab72a\"}":"{\"ts_sec\":1711704727,\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:09:54.941 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Starting stream read, table list: [Region], offset: {"name":"2531a6bb-0fb4-430c-bca9-acb7f48522ca","offset":{"{\"server\":\"2531a6bb-0fb4-430c-bca9-acb7f48522ca\"}":"{\"ts_sec\":1711704727,\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:09:54.949 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Starting stream read, table list: [EmployeeTerritories], offset: {"name":"14e80da5-804f-46e6-9c5c-81124a7bf21d","offset":{"{\"server\":\"14e80da5-804f-46e6-9c5c-81124a7bf21d\"}":"{\"ts_sec\":1711704727,\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:09:54.969 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting mysql cdc, server name: cd3a3ac3-8253-4945-8369-c609a70212b5 
[INFO ] 2024-03-29 18:09:54.972 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Starting mysql cdc, server name: 2531a6bb-0fb4-430c-bca9-acb7f48522ca 
[INFO ] 2024-03-29 18:09:54.972 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Starting mysql cdc, server name: 14e80da5-804f-46e6-9c5c-81124a7bf21d 
[INFO ] 2024-03-29 18:09:54.973 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Starting mysql cdc, server name: c7ded3cb-031e-4b0c-901d-cf57d36ab72a 
[INFO ] 2024-03-29 18:09:54.990 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Incremental sync starting... 
[INFO ] 2024-03-29 18:09:54.994 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 18:09:54.995 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting stream read, table list: [Employees], offset: {"name":"b3be3fc4-c60f-45b9-adaa-e77bc5b33627","offset":{"{\"server\":\"b3be3fc4-c60f-45b9-adaa-e77bc5b33627\"}":"{\"ts_sec\":1711704727,\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:09:55.077 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting mysql cdc, server name: b3be3fc4-c60f-45b9-adaa-e77bc5b33627 
[INFO ] 2024-03-29 18:09:55.091 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1108654062
  time.precision.mode: adaptive_time_microseconds
  database.server.name: cd3a3ac3-8253-4945-8369-c609a70212b5
  database.port: 3307
  threadName: Debezium-Mysql-Connector-cd3a3ac3-8253-4945-8369-c609a70212b5
  database.hostname: 127.0.0.1
  database.password: ********
  name: cd3a3ac3-8253-4945-8369-c609a70212b5
  pdk.offset.string: {"name":"cd3a3ac3-8253-4945-8369-c609a70212b5","offset":{"{\"server\":\"cd3a3ac3-8253-4945-8369-c609a70212b5\"}":"{\"ts_sec\":1711704727,\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Employees
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:09:55.092 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 327277403
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b3be3fc4-c60f-45b9-adaa-e77bc5b33627
  database.port: 3307
  threadName: Debezium-Mysql-Connector-b3be3fc4-c60f-45b9-adaa-e77bc5b33627
  database.hostname: 127.0.0.1
  database.password: ********
  name: b3be3fc4-c60f-45b9-adaa-e77bc5b33627
  pdk.offset.string: {"name":"b3be3fc4-c60f-45b9-adaa-e77bc5b33627","offset":{"{\"server\":\"b3be3fc4-c60f-45b9-adaa-e77bc5b33627\"}":"{\"ts_sec\":1711704727,\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Employees
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:09:55.092 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1936113387
  time.precision.mode: adaptive_time_microseconds
  database.server.name: c7ded3cb-031e-4b0c-901d-cf57d36ab72a
  database.port: 3307
  threadName: Debezium-Mysql-Connector-c7ded3cb-031e-4b0c-901d-cf57d36ab72a
  database.hostname: 127.0.0.1
  database.password: ********
  name: c7ded3cb-031e-4b0c-901d-cf57d36ab72a
  pdk.offset.string: {"name":"c7ded3cb-031e-4b0c-901d-cf57d36ab72a","offset":{"{\"server\":\"c7ded3cb-031e-4b0c-901d-cf57d36ab72a\"}":"{\"ts_sec\":1711704727,\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Territories
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:09:55.092 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1999608044
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 2531a6bb-0fb4-430c-bca9-acb7f48522ca
  database.port: 3307
  threadName: Debezium-Mysql-Connector-2531a6bb-0fb4-430c-bca9-acb7f48522ca
  database.hostname: 127.0.0.1
  database.password: ********
  name: 2531a6bb-0fb4-430c-bca9-acb7f48522ca
  pdk.offset.string: {"name":"2531a6bb-0fb4-430c-bca9-acb7f48522ca","offset":{"{\"server\":\"2531a6bb-0fb4-430c-bca9-acb7f48522ca\"}":"{\"ts_sec\":1711704727,\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Region
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:09:55.093 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 154665141
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 14e80da5-804f-46e6-9c5c-81124a7bf21d
  database.port: 3307
  threadName: Debezium-Mysql-Connector-14e80da5-804f-46e6-9c5c-81124a7bf21d
  database.hostname: 127.0.0.1
  database.password: ********
  name: 14e80da5-804f-46e6-9c5c-81124a7bf21d
  pdk.offset.string: {"name":"14e80da5-804f-46e6-9c5c-81124a7bf21d","offset":{"{\"server\":\"14e80da5-804f-46e6-9c5c-81124a7bf21d\"}":"{\"ts_sec\":1711704727,\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.EmployeeTerritories
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:09:56.378 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Connector Mysql incremental start succeed, tables: [EmployeeTerritories], data change syncing 
[INFO ] 2024-03-29 18:09:56.384 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Connector Mysql incremental start succeed, tables: [Region], data change syncing 
[INFO ] 2024-03-29 18:09:56.395 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Connector Mysql incremental start succeed, tables: [Territories], data change syncing 
[INFO ] 2024-03-29 18:09:56.405 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Connector Mysql incremental start succeed, tables: [Employees], data change syncing 
[INFO ] 2024-03-29 18:09:56.406 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Connector Mysql incremental start succeed, tables: [Employees], data change syncing 
[INFO ] 2024-03-29 18:09:58.635 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Territories_a666fdf2-1847-4a31-b556-150e6cb647a5__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:10:03.729 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Region_837c950f-4961-4f24-b40c-5563d769bc9a__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:10:03.805 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 18:10:04.011 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 18:11:58.310 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[a28548a8-a70b-4734-9b7f-85e92c5fc2a8] running status set to false 
[INFO ] 2024-03-29 18:20:03.839 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Start task milestones: 66068a6a5c94df48bc70bd3a(employees_import_import_import_import_import_import_import_import_import_import_import) 
[INFO ] 2024-03-29 18:20:03.843 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Task initialization... 
[INFO ] 2024-03-29 18:20:04.048 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Node performs snapshot read by order list: [ null ] -> [ null,null ] -> [ null ] -> [ null ] 
[INFO ] 2024-03-29 18:20:04.426 - [employees_import_import_import_import_import_import_import_import_import_import_import] - The engine receives employees_import_import_import_import_import_import_import_import_import_import_import task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 18:20:04.803 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[a666fdf2-1847-4a31-b556-150e6cb647a5] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:20:04.803 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[2da87501-4d24-4b11-b021-35bc8f75c87c] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:20:04.803 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[1ba8c468-3477-42e7-a5b9-c035659bb9dd] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:20:04.803 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[7f57f82c-db71-4b08-99d8-5620afdc209d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:20:04.803 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[ac4ad9fa-ce9d-4d89-9be7-8b9b4dec9391] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:20:04.803 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[a28548a8-a70b-4734-9b7f-85e92c5fc2a8] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:20:04.803 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[ac99608c-4577-43fc-ba71-fc3627861a91] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:20:04.857 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[87a637c7-b7e5-4ed5-84a1-347dd6817644] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:20:04.857 - [employees_import_import_import_import_import_import_import_import_import_import_import][employees] - Node employees[01b1de5b-e930-411b-8dc3-13d83a4b52b1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:20:04.857 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[c91421bd-beb5-4d0a-9dc0-17ce6755a98a] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:20:04.857 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Node merge[2c656bdd-fb5f-4f66-887f-f9460a80e1be] start preload schema,table counts: 6 
[INFO ] 2024-03-29 18:20:04.857 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Node Region[9d9ab82d-01be-4a50-b612-01a66f44b2e1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:20:04.857 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Node Territories[19e59acd-9841-4cf7-bc4e-bd33067139b1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:20:04.857 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[837c950f-4961-4f24-b40c-5563d769bc9a] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:20:04.865 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[34c16d5c-d315-458f-8287-c4529e01f1d9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:20:05.020 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[ac4ad9fa-ce9d-4d89-9be7-8b9b4dec9391] preload schema finished, cost 196 ms 
[INFO ] 2024-03-29 18:20:05.024 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[837c950f-4961-4f24-b40c-5563d769bc9a] preload schema finished, cost 144 ms 
[INFO ] 2024-03-29 18:20:05.024 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[34c16d5c-d315-458f-8287-c4529e01f1d9] preload schema finished, cost 139 ms 
[INFO ] 2024-03-29 18:20:05.024 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[87a637c7-b7e5-4ed5-84a1-347dd6817644] preload schema finished, cost 144 ms 
[INFO ] 2024-03-29 18:20:05.024 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[2da87501-4d24-4b11-b021-35bc8f75c87c] preload schema finished, cost 197 ms 
[INFO ] 2024-03-29 18:20:05.024 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Node Region[9d9ab82d-01be-4a50-b612-01a66f44b2e1] preload schema finished, cost 142 ms 
[INFO ] 2024-03-29 18:20:05.024 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[ac99608c-4577-43fc-ba71-fc3627861a91] preload schema finished, cost 196 ms 
[INFO ] 2024-03-29 18:20:05.024 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[a666fdf2-1847-4a31-b556-150e6cb647a5] preload schema finished, cost 207 ms 
[INFO ] 2024-03-29 18:20:05.024 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Node Territories[19e59acd-9841-4cf7-bc4e-bd33067139b1] preload schema finished, cost 154 ms 
[INFO ] 2024-03-29 18:20:05.034 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[1ba8c468-3477-42e7-a5b9-c035659bb9dd] preload schema finished, cost 231 ms 
[INFO ] 2024-03-29 18:20:05.034 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[7f57f82c-db71-4b08-99d8-5620afdc209d] preload schema finished, cost 230 ms 
[INFO ] 2024-03-29 18:20:05.034 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[c91421bd-beb5-4d0a-9dc0-17ce6755a98a] preload schema finished, cost 178 ms 
[INFO ] 2024-03-29 18:20:05.040 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[a28548a8-a70b-4734-9b7f-85e92c5fc2a8] preload schema finished, cost 233 ms 
[INFO ] 2024-03-29 18:20:05.040 - [employees_import_import_import_import_import_import_import_import_import_import_import][employees] - Node employees[01b1de5b-e930-411b-8dc3-13d83a4b52b1] preload schema finished, cost 181 ms 
[INFO ] 2024-03-29 18:20:05.275 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Node merge[2c656bdd-fb5f-4f66-887f-f9460a80e1be] preload schema finished, cost 415 ms 
[INFO ] 2024-03-29 18:20:05.275 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 18:20:05.275 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  Territories(a666fdf2-1847-4a31-b556-150e6cb647a5)
    ->Region(837c950f-4961-4f24-b40c-5563d769bc9a)
} 
[INFO ] 2024-03-29 18:20:05.276 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  EmployeeTerritories(ac99608c-4577-43fc-ba71-fc3627861a91)
    ->Territories(a666fdf2-1847-4a31-b556-150e6cb647a5)
} 
[INFO ] 2024-03-29 18:20:05.276 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  Employees(7f57f82c-db71-4b08-99d8-5620afdc209d)
    ->Employees(c91421bd-beb5-4d0a-9dc0-17ce6755a98a)
    ->EmployeeTerritories(ac99608c-4577-43fc-ba71-fc3627861a91)
} 
[INFO ] 2024-03-29 18:20:05.309 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Employees_c91421bd-beb5-4d0a-9dc0-17ce6755a98a__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:20:05.309 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_EmployeeTerritories_ac99608c-4577-43fc-ba71-fc3627861a91__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:20:05.312 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Territories_a666fdf2-1847-4a31-b556-150e6cb647a5__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:20:05.312 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Region_837c950f-4961-4f24-b40c-5563d769bc9a__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:20:05.389 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Source node "EmployeeTerritories" read batch size: 500 
[INFO ] 2024-03-29 18:20:05.389 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Source node "EmployeeTerritories" event queue capacity: 1000 
[INFO ] 2024-03-29 18:20:05.394 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 18:20:05.394 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 18:20:05.406 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-29 18:20:05.406 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - batch offset found: {},stream offset found: {"name":"14e80da5-804f-46e6-9c5c-81124a7bf21d","offset":{"{\"server\":\"14e80da5-804f-46e6-9c5c-81124a7bf21d\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:20:06.105 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" read batch size: 500 
[INFO ] 2024-03-29 18:20:06.112 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" event queue capacity: 1000 
[INFO ] 2024-03-29 18:20:06.112 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-29 18:20:06.112 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - batch offset found: {},stream offset found: {"name":"cd3a3ac3-8253-4945-8369-c609a70212b5","offset":{"{\"server\":\"cd3a3ac3-8253-4945-8369-c609a70212b5\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:20:06.118 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Incremental sync starting... 
[INFO ] 2024-03-29 18:20:06.119 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Initial sync completed 
[INFO ] 2024-03-29 18:20:06.143 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Starting stream read, table list: [EmployeeTerritories], offset: {"name":"14e80da5-804f-46e6-9c5c-81124a7bf21d","offset":{"{\"server\":\"14e80da5-804f-46e6-9c5c-81124a7bf21d\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:20:06.172 - [employees_import_import_import_import_import_import_import_import_import_import_import][employees] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 18:20:06.174 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Source node "Region" read batch size: 500 
[INFO ] 2024-03-29 18:20:06.174 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Source node "Region" event queue capacity: 1000 
[INFO ] 2024-03-29 18:20:06.174 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-29 18:20:06.212 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - batch offset found: {},stream offset found: {"name":"2531a6bb-0fb4-430c-bca9-acb7f48522ca","offset":{"{\"server\":\"2531a6bb-0fb4-430c-bca9-acb7f48522ca\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:20:06.216 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Starting mysql cdc, server name: 14e80da5-804f-46e6-9c5c-81124a7bf21d 
[INFO ] 2024-03-29 18:20:06.234 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Incremental sync starting... 
[INFO ] 2024-03-29 18:20:06.235 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 18:20:06.257 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting stream read, table list: [Employees], offset: {"name":"cd3a3ac3-8253-4945-8369-c609a70212b5","offset":{"{\"server\":\"cd3a3ac3-8253-4945-8369-c609a70212b5\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:20:06.259 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting mysql cdc, server name: cd3a3ac3-8253-4945-8369-c609a70212b5 
[INFO ] 2024-03-29 18:20:06.287 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Incremental sync starting... 
[INFO ] 2024-03-29 18:20:06.288 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Initial sync completed 
[INFO ] 2024-03-29 18:20:06.288 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Starting stream read, table list: [Region], offset: {"name":"2531a6bb-0fb4-430c-bca9-acb7f48522ca","offset":{"{\"server\":\"2531a6bb-0fb4-430c-bca9-acb7f48522ca\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:20:06.299 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 584325231
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 14e80da5-804f-46e6-9c5c-81124a7bf21d
  database.port: 3307
  threadName: Debezium-Mysql-Connector-14e80da5-804f-46e6-9c5c-81124a7bf21d
  database.hostname: 127.0.0.1
  database.password: ********
  name: 14e80da5-804f-46e6-9c5c-81124a7bf21d
  pdk.offset.string: {"name":"14e80da5-804f-46e6-9c5c-81124a7bf21d","offset":{"{\"server\":\"14e80da5-804f-46e6-9c5c-81124a7bf21d\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.EmployeeTerritories
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:20:06.299 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 512684956
  time.precision.mode: adaptive_time_microseconds
  database.server.name: cd3a3ac3-8253-4945-8369-c609a70212b5
  database.port: 3307
  threadName: Debezium-Mysql-Connector-cd3a3ac3-8253-4945-8369-c609a70212b5
  database.hostname: 127.0.0.1
  database.password: ********
  name: cd3a3ac3-8253-4945-8369-c609a70212b5
  pdk.offset.string: {"name":"cd3a3ac3-8253-4945-8369-c609a70212b5","offset":{"{\"server\":\"cd3a3ac3-8253-4945-8369-c609a70212b5\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Employees
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:20:06.324 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Starting mysql cdc, server name: 2531a6bb-0fb4-430c-bca9-acb7f48522ca 
[INFO ] 2024-03-29 18:20:06.372 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2053509119
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 2531a6bb-0fb4-430c-bca9-acb7f48522ca
  database.port: 3307
  threadName: Debezium-Mysql-Connector-2531a6bb-0fb4-430c-bca9-acb7f48522ca
  database.hostname: 127.0.0.1
  database.password: ********
  name: 2531a6bb-0fb4-430c-bca9-acb7f48522ca
  pdk.offset.string: {"name":"2531a6bb-0fb4-430c-bca9-acb7f48522ca","offset":{"{\"server\":\"2531a6bb-0fb4-430c-bca9-acb7f48522ca\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Region
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:20:06.373 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" read batch size: 500 
[INFO ] 2024-03-29 18:20:06.373 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" event queue capacity: 1000 
[INFO ] 2024-03-29 18:20:06.373 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-29 18:20:06.377 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - batch offset found: {},stream offset found: {"name":"b3be3fc4-c60f-45b9-adaa-e77bc5b33627","offset":{"{\"server\":\"b3be3fc4-c60f-45b9-adaa-e77bc5b33627\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:20:06.526 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Incremental sync starting... 
[INFO ] 2024-03-29 18:20:06.526 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 18:20:06.537 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting stream read, table list: [Employees], offset: {"name":"b3be3fc4-c60f-45b9-adaa-e77bc5b33627","offset":{"{\"server\":\"b3be3fc4-c60f-45b9-adaa-e77bc5b33627\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:20:06.596 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting mysql cdc, server name: b3be3fc4-c60f-45b9-adaa-e77bc5b33627 
[INFO ] 2024-03-29 18:20:06.602 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Source node "Territories" read batch size: 500 
[INFO ] 2024-03-29 18:20:06.602 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Source node "Territories" event queue capacity: 1000 
[INFO ] 2024-03-29 18:20:06.604 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-03-29 18:20:06.604 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1561154017
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b3be3fc4-c60f-45b9-adaa-e77bc5b33627
  database.port: 3307
  threadName: Debezium-Mysql-Connector-b3be3fc4-c60f-45b9-adaa-e77bc5b33627
  database.hostname: 127.0.0.1
  database.password: ********
  name: b3be3fc4-c60f-45b9-adaa-e77bc5b33627
  pdk.offset.string: {"name":"b3be3fc4-c60f-45b9-adaa-e77bc5b33627","offset":{"{\"server\":\"b3be3fc4-c60f-45b9-adaa-e77bc5b33627\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Employees
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:20:06.604 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - batch offset found: {},stream offset found: {"name":"c7ded3cb-031e-4b0c-901d-cf57d36ab72a","offset":{"{\"server\":\"c7ded3cb-031e-4b0c-901d-cf57d36ab72a\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:20:06.718 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Incremental sync starting... 
[INFO ] 2024-03-29 18:20:06.718 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Initial sync completed 
[INFO ] 2024-03-29 18:20:06.736 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Starting stream read, table list: [Territories], offset: {"name":"c7ded3cb-031e-4b0c-901d-cf57d36ab72a","offset":{"{\"server\":\"c7ded3cb-031e-4b0c-901d-cf57d36ab72a\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}} 
[INFO ] 2024-03-29 18:20:06.736 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Starting mysql cdc, server name: c7ded3cb-031e-4b0c-901d-cf57d36ab72a 
[INFO ] 2024-03-29 18:20:06.740 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1065482338
  time.precision.mode: adaptive_time_microseconds
  database.server.name: c7ded3cb-031e-4b0c-901d-cf57d36ab72a
  database.port: 3307
  threadName: Debezium-Mysql-Connector-c7ded3cb-031e-4b0c-901d-cf57d36ab72a
  database.hostname: 127.0.0.1
  database.password: ********
  name: c7ded3cb-031e-4b0c-901d-cf57d36ab72a
  pdk.offset.string: {"name":"c7ded3cb-031e-4b0c-901d-cf57d36ab72a","offset":{"{\"server\":\"c7ded3cb-031e-4b0c-901d-cf57d36ab72a\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":1}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Territories
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:20:07.512 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Connector Mysql incremental start succeed, tables: [Employees], data change syncing 
[INFO ] 2024-03-29 18:20:07.512 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Connector Mysql incremental start succeed, tables: [EmployeeTerritories], data change syncing 
[INFO ] 2024-03-29 18:20:07.512 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Connector Mysql incremental start succeed, tables: [Territories], data change syncing 
[INFO ] 2024-03-29 18:20:07.512 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Connector Mysql incremental start succeed, tables: [Employees], data change syncing 
[INFO ] 2024-03-29 18:20:07.513 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Connector Mysql incremental start succeed, tables: [Region], data change syncing 
[INFO ] 2024-03-29 18:36:47.814 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Stop task milestones: 66068a6a5c94df48bc70bd3a(employees_import_import_import_import_import_import_import_import_import_import_import)  
[INFO ] 2024-03-29 18:36:48.212 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[1ba8c468-3477-42e7-a5b9-c035659bb9dd] running status set to false 
[INFO ] 2024-03-29 18:36:48.291 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:36:48.291 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:36:48.303 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - PDK connector node stopped: HazelcastSourcePdkDataNode-1ba8c468-3477-42e7-a5b9-c035659bb9dd 
[INFO ] 2024-03-29 18:36:48.306 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - PDK connector node released: HazelcastSourcePdkDataNode-1ba8c468-3477-42e7-a5b9-c035659bb9dd 
[INFO ] 2024-03-29 18:36:48.306 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[1ba8c468-3477-42e7-a5b9-c035659bb9dd] schema data cleaned 
[INFO ] 2024-03-29 18:36:48.306 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[1ba8c468-3477-42e7-a5b9-c035659bb9dd] monitor closed 
[INFO ] 2024-03-29 18:36:48.306 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[1ba8c468-3477-42e7-a5b9-c035659bb9dd] close complete, cost 93 ms 
[INFO ] 2024-03-29 18:36:48.306 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[34c16d5c-d315-458f-8287-c4529e01f1d9] running status set to false 
[INFO ] 2024-03-29 18:36:48.413 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:36:48.416 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - PDK connector node stopped: HazelcastSourcePdkDataNode-34c16d5c-d315-458f-8287-c4529e01f1d9 
[INFO ] 2024-03-29 18:36:48.416 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - PDK connector node released: HazelcastSourcePdkDataNode-34c16d5c-d315-458f-8287-c4529e01f1d9 
[INFO ] 2024-03-29 18:36:48.416 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[34c16d5c-d315-458f-8287-c4529e01f1d9] schema data cleaned 
[INFO ] 2024-03-29 18:36:48.416 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[34c16d5c-d315-458f-8287-c4529e01f1d9] monitor closed 
[INFO ] 2024-03-29 18:36:48.416 - [employees_import_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[34c16d5c-d315-458f-8287-c4529e01f1d9] close complete, cost 109 ms 
[INFO ] 2024-03-29 18:36:48.416 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Node Region[9d9ab82d-01be-4a50-b612-01a66f44b2e1] running status set to false 
[INFO ] 2024-03-29 18:36:48.508 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:36:48.508 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:36:48.523 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - PDK connector node stopped: HazelcastSourcePdkDataNode-9d9ab82d-01be-4a50-b612-01a66f44b2e1 
[INFO ] 2024-03-29 18:36:48.526 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - PDK connector node released: HazelcastSourcePdkDataNode-9d9ab82d-01be-4a50-b612-01a66f44b2e1 
[INFO ] 2024-03-29 18:36:48.526 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Node Region[9d9ab82d-01be-4a50-b612-01a66f44b2e1] schema data cleaned 
[INFO ] 2024-03-29 18:36:48.526 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Node Region[9d9ab82d-01be-4a50-b612-01a66f44b2e1] monitor closed 
[INFO ] 2024-03-29 18:36:48.526 - [employees_import_import_import_import_import_import_import_import_import_import_import][Region] - Node Region[9d9ab82d-01be-4a50-b612-01a66f44b2e1] close complete, cost 108 ms 
[INFO ] 2024-03-29 18:36:48.526 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Node Territories[19e59acd-9841-4cf7-bc4e-bd33067139b1] running status set to false 
[INFO ] 2024-03-29 18:36:48.638 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:36:48.638 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:36:48.643 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - PDK connector node stopped: HazelcastSourcePdkDataNode-19e59acd-9841-4cf7-bc4e-bd33067139b1 
[INFO ] 2024-03-29 18:36:48.644 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - PDK connector node released: HazelcastSourcePdkDataNode-19e59acd-9841-4cf7-bc4e-bd33067139b1 
[INFO ] 2024-03-29 18:36:48.644 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Node Territories[19e59acd-9841-4cf7-bc4e-bd33067139b1] schema data cleaned 
[INFO ] 2024-03-29 18:36:48.644 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Node Territories[19e59acd-9841-4cf7-bc4e-bd33067139b1] monitor closed 
[INFO ] 2024-03-29 18:36:48.644 - [employees_import_import_import_import_import_import_import_import_import_import_import][Territories] - Node Territories[19e59acd-9841-4cf7-bc4e-bd33067139b1] close complete, cost 120 ms 
[INFO ] 2024-03-29 18:36:48.741 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[2da87501-4d24-4b11-b021-35bc8f75c87c] running status set to false 
[INFO ] 2024-03-29 18:36:48.744 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[2da87501-4d24-4b11-b021-35bc8f75c87c] schema data cleaned 
[INFO ] 2024-03-29 18:36:48.744 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[2da87501-4d24-4b11-b021-35bc8f75c87c] monitor closed 
[INFO ] 2024-03-29 18:36:48.744 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[2da87501-4d24-4b11-b021-35bc8f75c87c] close complete, cost 97 ms 
[INFO ] 2024-03-29 18:36:48.744 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[ac99608c-4577-43fc-ba71-fc3627861a91] running status set to false 
[INFO ] 2024-03-29 18:36:48.785 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[ac99608c-4577-43fc-ba71-fc3627861a91] schema data cleaned 
[INFO ] 2024-03-29 18:36:48.788 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[ac99608c-4577-43fc-ba71-fc3627861a91] monitor closed 
[INFO ] 2024-03-29 18:36:48.788 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[ac99608c-4577-43fc-ba71-fc3627861a91] close complete, cost 43 ms 
[INFO ] 2024-03-29 18:36:48.788 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[a28548a8-a70b-4734-9b7f-85e92c5fc2a8] running status set to false 
[INFO ] 2024-03-29 18:36:48.853 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:36:48.853 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:36:48.855 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - PDK connector node stopped: HazelcastSourcePdkDataNode-a28548a8-a70b-4734-9b7f-85e92c5fc2a8 
[INFO ] 2024-03-29 18:36:48.855 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - PDK connector node released: HazelcastSourcePdkDataNode-a28548a8-a70b-4734-9b7f-85e92c5fc2a8 
[INFO ] 2024-03-29 18:36:48.855 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[a28548a8-a70b-4734-9b7f-85e92c5fc2a8] schema data cleaned 
[INFO ] 2024-03-29 18:36:48.855 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[a28548a8-a70b-4734-9b7f-85e92c5fc2a8] monitor closed 
[INFO ] 2024-03-29 18:36:48.856 - [employees_import_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[a28548a8-a70b-4734-9b7f-85e92c5fc2a8] close complete, cost 70 ms 
[INFO ] 2024-03-29 18:36:48.856 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[87a637c7-b7e5-4ed5-84a1-347dd6817644] running status set to false 
[INFO ] 2024-03-29 18:36:48.910 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[87a637c7-b7e5-4ed5-84a1-347dd6817644] schema data cleaned 
[INFO ] 2024-03-29 18:36:48.912 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[87a637c7-b7e5-4ed5-84a1-347dd6817644] monitor closed 
[INFO ] 2024-03-29 18:36:48.912 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[87a637c7-b7e5-4ed5-84a1-347dd6817644] close complete, cost 54 ms 
[INFO ] 2024-03-29 18:36:48.912 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[837c950f-4961-4f24-b40c-5563d769bc9a] running status set to false 
[INFO ] 2024-03-29 18:36:48.975 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[837c950f-4961-4f24-b40c-5563d769bc9a] schema data cleaned 
[INFO ] 2024-03-29 18:36:48.976 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[837c950f-4961-4f24-b40c-5563d769bc9a] monitor closed 
[INFO ] 2024-03-29 18:36:48.976 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[837c950f-4961-4f24-b40c-5563d769bc9a] close complete, cost 60 ms 
[INFO ] 2024-03-29 18:36:48.976 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[7f57f82c-db71-4b08-99d8-5620afdc209d] running status set to false 
[INFO ] 2024-03-29 18:36:49.016 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[7f57f82c-db71-4b08-99d8-5620afdc209d] schema data cleaned 
[INFO ] 2024-03-29 18:36:49.017 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[7f57f82c-db71-4b08-99d8-5620afdc209d] monitor closed 
[INFO ] 2024-03-29 18:36:49.017 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[7f57f82c-db71-4b08-99d8-5620afdc209d] close complete, cost 44 ms 
[INFO ] 2024-03-29 18:36:49.017 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[c91421bd-beb5-4d0a-9dc0-17ce6755a98a] running status set to false 
[INFO ] 2024-03-29 18:36:49.059 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[c91421bd-beb5-4d0a-9dc0-17ce6755a98a] schema data cleaned 
[INFO ] 2024-03-29 18:36:49.063 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[c91421bd-beb5-4d0a-9dc0-17ce6755a98a] monitor closed 
[INFO ] 2024-03-29 18:36:49.063 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[c91421bd-beb5-4d0a-9dc0-17ce6755a98a] close complete, cost 43 ms 
[INFO ] 2024-03-29 18:36:49.063 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[ac4ad9fa-ce9d-4d89-9be7-8b9b4dec9391] running status set to false 
[INFO ] 2024-03-29 18:36:49.100 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[ac4ad9fa-ce9d-4d89-9be7-8b9b4dec9391] schema data cleaned 
[INFO ] 2024-03-29 18:36:49.100 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[ac4ad9fa-ce9d-4d89-9be7-8b9b4dec9391] monitor closed 
[INFO ] 2024-03-29 18:36:49.100 - [employees_import_import_import_import_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[ac4ad9fa-ce9d-4d89-9be7-8b9b4dec9391] close complete, cost 39 ms 
[INFO ] 2024-03-29 18:36:49.100 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[a666fdf2-1847-4a31-b556-150e6cb647a5] running status set to false 
[INFO ] 2024-03-29 18:36:49.143 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[a666fdf2-1847-4a31-b556-150e6cb647a5] schema data cleaned 
[INFO ] 2024-03-29 18:36:49.147 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[a666fdf2-1847-4a31-b556-150e6cb647a5] monitor closed 
[INFO ] 2024-03-29 18:36:49.147 - [employees_import_import_import_import_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[a666fdf2-1847-4a31-b556-150e6cb647a5] close complete, cost 43 ms 
[INFO ] 2024-03-29 18:36:49.151 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Node merge[2c656bdd-fb5f-4f66-887f-f9460a80e1be] running status set to false 
[INFO ] 2024-03-29 18:36:49.151 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Employees_c91421bd-beb5-4d0a-9dc0-17ce6755a98a__TPORIG 
[INFO ] 2024-03-29 18:36:49.151 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_EmployeeTerritories_ac99608c-4577-43fc-ba71-fc3627861a91__TPORIG 
[INFO ] 2024-03-29 18:36:49.151 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Territories_a666fdf2-1847-4a31-b556-150e6cb647a5__TPORIG 
[INFO ] 2024-03-29 18:36:49.151 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Destroy merge cache resource: HazelcastMergeNode_Region_837c950f-4961-4f24-b40c-5563d769bc9a__TPORIG 
[INFO ] 2024-03-29 18:36:49.151 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Node merge[2c656bdd-fb5f-4f66-887f-f9460a80e1be] schema data cleaned 
[INFO ] 2024-03-29 18:36:49.151 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Node merge[2c656bdd-fb5f-4f66-887f-f9460a80e1be] monitor closed 
[INFO ] 2024-03-29 18:36:49.151 - [employees_import_import_import_import_import_import_import_import_import_import_import][merge] - Node merge[2c656bdd-fb5f-4f66-887f-f9460a80e1be] close complete, cost 2 ms 
[INFO ] 2024-03-29 18:36:49.151 - [employees_import_import_import_import_import_import_import_import_import_import_import][employees] - Node employees[01b1de5b-e930-411b-8dc3-13d83a4b52b1] running status set to false 
[INFO ] 2024-03-29 18:36:49.170 - [employees_import_import_import_import_import_import_import_import_import_import_import][employees] - PDK connector node stopped: HazelcastTargetPdkDataNode-01b1de5b-e930-411b-8dc3-13d83a4b52b1 
[INFO ] 2024-03-29 18:36:49.170 - [employees_import_import_import_import_import_import_import_import_import_import_import][employees] - PDK connector node released: HazelcastTargetPdkDataNode-01b1de5b-e930-411b-8dc3-13d83a4b52b1 
[INFO ] 2024-03-29 18:36:49.170 - [employees_import_import_import_import_import_import_import_import_import_import_import][employees] - Node employees[01b1de5b-e930-411b-8dc3-13d83a4b52b1] schema data cleaned 
[INFO ] 2024-03-29 18:36:49.170 - [employees_import_import_import_import_import_import_import_import_import_import_import][employees] - Node employees[01b1de5b-e930-411b-8dc3-13d83a4b52b1] monitor closed 
[INFO ] 2024-03-29 18:36:49.170 - [employees_import_import_import_import_import_import_import_import_import_import_import][employees] - Node employees[01b1de5b-e930-411b-8dc3-13d83a4b52b1] close complete, cost 23 ms 
[INFO ] 2024-03-29 18:36:53.960 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-29 18:36:53.960 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Stopped task aspect(s) 
[INFO ] 2024-03-29 18:36:53.960 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Snapshot order controller have been removed 
[INFO ] 2024-03-29 18:36:53.996 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Remove memory task client succeed, task: employees_import_import_import_import_import_import_import_import_import_import_import[66068a6a5c94df48bc70bd3a] 
[INFO ] 2024-03-29 18:36:53.999 - [employees_import_import_import_import_import_import_import_import_import_import_import] - Destroy memory task client cache succeed, task: employees_import_import_import_import_import_import_import_import_import_import_import[66068a6a5c94df48bc70bd3a] 
