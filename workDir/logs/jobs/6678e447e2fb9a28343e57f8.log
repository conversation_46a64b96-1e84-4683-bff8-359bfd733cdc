[INFO ] 2024-06-30 13:29:25.780 - [任务 15] - Task initialization... 
[INFO ] 2024-06-30 13:29:25.997 - [任务 15] - Start task milestones: 6678e447e2fb9a28343e57f8(任务 15) 
[INFO ] 2024-06-30 13:29:26.095 - [任务 15] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-30 13:29:26.205 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-30 13:29:26.206 - [任务 15][testCLAIM] - Node testCLAIM[ae0aa594-251c-47e7-ba56-1ba15c250733] start preload schema,table counts: 1 
[INFO ] 2024-06-30 13:29:26.206 - [任务 15][CLAIM] - Node CLAIM[9f0ab189-030f-444b-b485-d1bcfa6e28b5] start preload schema,table counts: 1 
[INFO ] 2024-06-30 13:29:26.206 - [任务 15][testCLAIM] - Node testCLAIM[ae0aa594-251c-47e7-ba56-1ba15c250733] preload schema finished, cost 0 ms 
[INFO ] 2024-06-30 13:29:26.206 - [任务 15][CLAIM] - Node CLAIM[9f0ab189-030f-444b-b485-d1bcfa6e28b5] preload schema finished, cost 0 ms 
[INFO ] 2024-06-30 13:29:27.013 - [任务 15][testCLAIM] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-30 13:29:27.074 - [任务 15][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-06-30 13:29:27.075 - [任务 15][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-06-30 13:29:27.075 - [任务 15][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-30 13:29:27.098 - [任务 15][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000032","position":157,"gtidSet":""} 
[INFO ] 2024-06-30 13:29:27.158 - [任务 15][CLAIM] - Initial sync started 
[INFO ] 2024-06-30 13:29:27.159 - [任务 15][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-30 13:29:27.197 - [任务 15][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-30 13:29:27.197 - [任务 15][CLAIM] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-06-30 13:29:27.605 - [任务 15][CLAIM] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-30 13:29:27.605 - [任务 15][CLAIM] - Initial sync completed 
[INFO ] 2024-06-30 13:29:27.605 - [任务 15][CLAIM] - Incremental sync starting... 
[INFO ] 2024-06-30 13:29:27.605 - [任务 15][CLAIM] - Initial sync completed 
[INFO ] 2024-06-30 13:29:27.636 - [任务 15][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000032","position":157,"gtidSet":""} 
[INFO ] 2024-06-30 13:29:27.636 - [任务 15][CLAIM] - Starting mysql cdc, server name: fb82ed19-2891-45f8-8b85-609ff68f5144 
[INFO ] 2024-06-30 13:29:27.680 - [任务 15][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2112611654
  time.precision.mode: adaptive_time_microseconds
  database.server.name: fb82ed19-2891-45f8-8b85-609ff68f5144
  database.port: 3306
  threadName: Debezium-Mysql-Connector-fb82ed19-2891-45f8-8b85-609ff68f5144
  database.hostname: localhost
  database.password: ********
  name: fb82ed19-2891-45f8-8b85-609ff68f5144
  pdk.offset.string: {"name":"fb82ed19-2891-45f8-8b85-609ff68f5144","offset":{"{\"server\":\"fb82ed19-2891-45f8-8b85-609ff68f5144\"}":"{\"file\":\"binlog.000032\",\"pos\":157,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-30 13:29:27.680 - [任务 15][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-30 15:37:55.165 - [任务 15][CLAIM] - Node CLAIM[9f0ab189-030f-444b-b485-d1bcfa6e28b5] running status set to false 
[INFO ] 2024-06-30 15:37:55.268 - [任务 15] - Stop task milestones: 6678e447e2fb9a28343e57f8(任务 15)  
[INFO ] 2024-06-30 15:37:55.268 - [任务 15][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-06-30 15:37:55.268 - [任务 15][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-06-30 15:37:55.277 - [任务 15][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-9f0ab189-030f-444b-b485-d1bcfa6e28b5 
[INFO ] 2024-06-30 15:37:55.277 - [任务 15][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-9f0ab189-030f-444b-b485-d1bcfa6e28b5 
[INFO ] 2024-06-30 15:37:55.278 - [任务 15][CLAIM] - Node CLAIM[9f0ab189-030f-444b-b485-d1bcfa6e28b5] schema data cleaned 
[INFO ] 2024-06-30 15:37:55.278 - [任务 15][CLAIM] - Node CLAIM[9f0ab189-030f-444b-b485-d1bcfa6e28b5] monitor closed 
[INFO ] 2024-06-30 15:37:55.279 - [任务 15][CLAIM] - Node CLAIM[9f0ab189-030f-444b-b485-d1bcfa6e28b5] close complete, cost 124 ms 
[INFO ] 2024-06-30 15:37:55.279 - [任务 15][testCLAIM] - Node testCLAIM[ae0aa594-251c-47e7-ba56-1ba15c250733] running status set to false 
[INFO ] 2024-06-30 15:37:55.306 - [任务 15][testCLAIM] - PDK connector node stopped: HazelcastTargetPdkDataNode-ae0aa594-251c-47e7-ba56-1ba15c250733 
[INFO ] 2024-06-30 15:37:55.307 - [任务 15][testCLAIM] - PDK connector node released: HazelcastTargetPdkDataNode-ae0aa594-251c-47e7-ba56-1ba15c250733 
[INFO ] 2024-06-30 15:37:55.307 - [任务 15][testCLAIM] - Node testCLAIM[ae0aa594-251c-47e7-ba56-1ba15c250733] schema data cleaned 
[INFO ] 2024-06-30 15:37:55.307 - [任务 15][testCLAIM] - Node testCLAIM[ae0aa594-251c-47e7-ba56-1ba15c250733] monitor closed 
[INFO ] 2024-06-30 15:37:55.307 - [任务 15][testCLAIM] - Node testCLAIM[ae0aa594-251c-47e7-ba56-1ba15c250733] close complete, cost 28 ms 
[INFO ] 2024-06-30 15:37:57.258 - [任务 15] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-30 15:37:57.260 - [任务 15] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5ad6c25e 
[INFO ] 2024-06-30 15:37:57.260 - [任务 15] - Stopped task aspect(s) 
[INFO ] 2024-06-30 15:37:57.260 - [任务 15] - Snapshot order controller have been removed 
[INFO ] 2024-06-30 15:37:57.288 - [任务 15] - Remove memory task client succeed, task: 任务 15[6678e447e2fb9a28343e57f8] 
[INFO ] 2024-06-30 15:37:57.290 - [任务 15] - Destroy memory task client cache succeed, task: 任务 15[6678e447e2fb9a28343e57f8] 
