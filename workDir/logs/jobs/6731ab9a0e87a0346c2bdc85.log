[INFO ] 2024-11-11 15:00:54.245 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-90e0b2d3-b890-4da6-9737-d13c2f56ea58 complete, cost 1363ms 
[INFO ] 2024-11-11 15:00:56.681 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-42ffe034-02d6-4b8d-ab1d-c6c4c57a5698 complete, cost 954ms 
[INFO ] 2024-11-11 15:01:04.887 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-6052b016-8413-4d2e-adef-3abf3990730d complete, cost 879ms 
[INFO ] 2024-11-11 15:03:22.839 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-21b86035-2815-4fd0-bd31-7b74186f2fd6 complete, cost 384ms 
[INFO ] 2024-11-11 15:03:23.312 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-8d4fd553-d6d1-43ce-9e41-ce9ed08e121c complete, cost 523ms 
[INFO ] 2024-11-11 15:03:27.298 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-cbb50f86-1934-433d-ac33-7495ecd79312 complete, cost 346ms 
[INFO ] 2024-11-11 15:03:29.103 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-61f44b2a-5adb-4b06-aaf3-cadb71861578 complete, cost 499ms 
[INFO ] 2024-11-11 15:06:56.573 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-238afada-ac49-4ebe-87ad-30a685a6722a complete, cost 408ms 
[INFO ] 2024-11-11 15:07:06.924 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-91dd9969-99b9-489f-bff3-b3a9407c5b5f complete, cost 370ms 
[INFO ] 2024-11-11 15:07:09.348 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-841f9b04-9b1f-48fe-bf03-ec948d74ca07 complete, cost 3113ms 
[INFO ] 2024-11-11 15:07:17.700 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-d980d39f-4bd6-426d-b0c9-485146b4489b complete, cost 357ms 
[INFO ] 2024-11-11 15:07:22.038 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-1534d8c4-a40d-43a7-9369-0d8b25ded91b complete, cost 479ms 
[INFO ] 2024-11-11 15:07:23.842 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-e7d478cc-fe30-44b1-8be3-ce30075f55e2 complete, cost 500ms 
[INFO ] 2024-11-11 15:07:24.383 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-781f9730-50e0-4577-95ad-0411da7e2242 complete, cost 446ms 
[INFO ] 2024-11-11 15:07:24.558 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-a0d18483-70c0-45a1-8477-13bf64e684a9 complete, cost 474ms 
[INFO ] 2024-11-11 15:07:24.760 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-856a3773-9c72-4ff0-9e1e-8c9efd9c34b8 complete, cost 883ms 
[INFO ] 2024-11-11 15:07:26.297 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-3dee7c83-500d-4301-950d-a68bcfa9d7cc complete, cost 375ms 
[INFO ] 2024-11-11 15:07:27.777 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-455f24d6-c295-4c6b-91f1-d13c59bae544 complete, cost 480ms 
[INFO ] 2024-11-11 15:07:28.065 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-4a073478-4097-4ec5-a52a-56f6b1f26b2c complete, cost 433ms 
[INFO ] 2024-11-11 15:07:28.272 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-d3fad1cc-96b9-420f-a6c2-e98f2560470f complete, cost 323ms 
[INFO ] 2024-11-11 15:07:29.952 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-b8868b64-1f9d-441e-97a6-099bf95fbec5 complete, cost 369ms 
[INFO ] 2024-11-11 15:08:37.178 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-6ba8540b-18cd-4923-909e-f8dd07c0355b complete, cost 51351ms 
[INFO ] 2024-11-11 15:08:43.183 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-be2abaac-9d25-4cc7-91ec-d79a0e232a44 complete, cost 327ms 
[INFO ] 2024-11-11 15:08:43.342 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-29b76650-5ccf-4dfb-8292-a3685d0bc4bd complete, cost 607ms 
[INFO ] 2024-11-11 15:10:20.625 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-d71f627e-04c0-4b80-a3de-8c4ae8602ff5 complete, cost 361ms 
[INFO ] 2024-11-11 15:10:24.158 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-c29f17d4-1709-4fb5-a5ac-577fec821b91 complete, cost 400ms 
[INFO ] 2024-11-11 15:10:32.046 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-47a42705-c560-4412-8cde-c94497a56b80 complete, cost 380ms 
[INFO ] 2024-11-11 15:10:34.580 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-4f650dad-a1f4-4895-8856-e6310bc0cc44 complete, cost 424ms 
[INFO ] 2024-11-11 15:10:36.205 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-3dd7d6fc-fd60-4162-95b0-3421977f1d69 complete, cost 387ms 
[INFO ] 2024-11-11 15:10:36.710 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-3a86cfb0-5917-458b-a127-162b315570ac complete, cost 458ms 
[INFO ] 2024-11-11 15:10:36.862 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-b77c059c-b068-4f60-b92a-57975f4f4102 complete, cost 510ms 
[INFO ] 2024-11-11 15:10:40.710 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-95208593-bdd6-407f-9ff7-ba5e39ee2f79 complete, cost 346ms 
[INFO ] 2024-11-11 15:10:42.542 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-673ee54c-6983-4829-98db-2eb7f1ad8938 complete, cost 467ms 
[INFO ] 2024-11-11 15:10:42.708 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-7f69f5ad-4695-40b6-9594-145257d49d2a complete, cost 289ms 
[INFO ] 2024-11-11 15:10:43.357 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-a2f41609-09e4-40e8-aba4-910dd9d7bff9 complete, cost 470ms 
[INFO ] 2024-11-11 15:10:44.281 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-d1bc8e74-06c3-438d-89e5-99751d198175 complete, cost 386ms 
[INFO ] 2024-11-11 15:10:44.477 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-e723c8a5-da82-497a-b4a6-7cdcdebdf0ab complete, cost 359ms 
[INFO ] 2024-11-11 15:10:46.247 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-8cf6fca0-cdcd-48ed-88ca-4475ce947793 complete, cost 363ms 
[INFO ] 2024-11-11 15:10:48.080 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-b1d7f0fe-3f5c-4a15-bf49-31f237b219b6 complete, cost 431ms 
[INFO ] 2024-11-11 15:10:51.029 - [任务 26] - Start task milestones: 6731ab9a0e87a0346c2bdc85(任务 26) 
[INFO ] 2024-11-11 15:10:51.030 - [任务 26] - Task initialization... 
[INFO ] 2024-11-11 15:10:51.660 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-73f339d3-1712-4cdc-b484-64675532b69d complete, cost 546ms 
[INFO ] 2024-11-11 15:10:51.777 - [任务 26] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-11 15:10:51.778 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-11 15:10:51.828 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:10:51.828 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:10:51.828 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:10:51.828 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:10:51.828 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:10:51.828 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:10:51.828 - [任务 26][test] - Node custom_processor(test: def953bb-d619-4f6a-8664-4e852594e818) enable batch process 
[INFO ] 2024-11-11 15:10:52.134 - [任务 26][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" read batch size: 100 
[INFO ] 2024-11-11 15:10:52.137 - [任务 26][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" event queue capacity: 200 
[INFO ] 2024-11-11 15:10:52.137 - [任务 26][BMSQL_CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-11 15:10:52.137 - [任务 26][BMSQL_CUSTOMER] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":15077,"gtidSet":""} 
[INFO ] 2024-11-11 15:10:52.137 - [任务 26][BMSQL_CUSTOMER] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-11 15:10:52.220 - [任务 26][BMSQL_CUSTOMER] - Initial sync started 
[INFO ] 2024-11-11 15:10:52.220 - [任务 26][BMSQL_CUSTOMER] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-11-11 15:10:52.220 - [任务 26][BMSQL_CUSTOMER] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-11-11 15:10:52.232 - [任务 26][BMSQL_CUSTOMER] - Query snapshot row size completed: BMSQL_CUSTOMER(1d3acc9c-2d86-4ede-8ec9-201621d8a251) 
[INFO ] 2024-11-11 15:10:52.640 - [任务 26][testCustom] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-11 15:10:54.038 - [任务 26][BMSQL_CUSTOMER] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-11 15:10:54.047 - [任务 26][BMSQL_CUSTOMER] - Initial sync completed 
[INFO ] 2024-11-11 15:10:54.047 - [任务 26][BMSQL_CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-11-11 15:10:54.047 - [任务 26][BMSQL_CUSTOMER] - Initial sync completed 
[INFO ] 2024-11-11 15:10:54.047 - [任务 26][BMSQL_CUSTOMER] - Starting stream read, table list: [BMSQL_CUSTOMER], offset: {"filename":"binlog.000038","position":15077,"gtidSet":""} 
[INFO ] 2024-11-11 15:10:54.114 - [任务 26][BMSQL_CUSTOMER] - Starting mysql cdc, server name: 04e5990a-86bf-4508-9d0d-42a3ff5cb036 
[INFO ] 2024-11-11 15:10:54.117 - [任务 26][BMSQL_CUSTOMER] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"04e5990a-86bf-4508-9d0d-42a3ff5cb036","offset":{"{\"server\":\"04e5990a-86bf-4508-9d0d-42a3ff5cb036\"}":"{\"file\":\"binlog.000038\",\"pos\":15077,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1476578138
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 04e5990a-86bf-4508-9d0d-42a3ff5cb036
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-04e5990a-86bf-4508-9d0d-42a3ff5cb036
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 04e5990a-86bf-4508-9d0d-42a3ff5cb036
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_CUSTOMER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-11 15:10:54.319 - [任务 26][BMSQL_CUSTOMER] - Connector Mysql incremental start succeed, tables: [BMSQL_CUSTOMER], data change syncing 
[INFO ] 2024-11-11 15:12:06.452 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] running status set to false 
[INFO ] 2024-11-11 15:12:06.471 - [任务 26][BMSQL_CUSTOMER] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-11 15:12:06.471 - [任务 26][BMSQL_CUSTOMER] - Mysql binlog reader stopped 
[INFO ] 2024-11-11 15:12:06.487 - [任务 26][BMSQL_CUSTOMER] - Incremental sync completed 
[INFO ] 2024-11-11 15:12:06.488 - [任务 26][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode_1d3acc9c-2d86-4ede-8ec9-201621d8a251_1731309051956 
[INFO ] 2024-11-11 15:12:06.488 - [任务 26][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode_1d3acc9c-2d86-4ede-8ec9-201621d8a251_1731309051956 
[INFO ] 2024-11-11 15:12:06.488 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] schema data cleaned 
[INFO ] 2024-11-11 15:12:06.489 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] monitor closed 
[INFO ] 2024-11-11 15:12:06.490 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] close complete, cost 54 ms 
[INFO ] 2024-11-11 15:12:06.506 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] running status set to false 
[INFO ] 2024-11-11 15:12:06.506 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] schema data cleaned 
[INFO ] 2024-11-11 15:12:06.506 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] monitor closed 
[INFO ] 2024-11-11 15:12:06.513 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] close complete, cost 22 ms 
[INFO ] 2024-11-11 15:12:06.529 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] running status set to false 
[INFO ] 2024-11-11 15:12:06.529 - [任务 26][testCustom] - PDK connector node stopped: HazelcastTargetPdkDataNode_4d2736f0-c8e7-4e23-afa1-b58cde1de30b_1731309052328 
[INFO ] 2024-11-11 15:12:06.530 - [任务 26][testCustom] - PDK connector node released: HazelcastTargetPdkDataNode_4d2736f0-c8e7-4e23-afa1-b58cde1de30b_1731309052328 
[INFO ] 2024-11-11 15:12:06.530 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] schema data cleaned 
[INFO ] 2024-11-11 15:12:06.530 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] monitor closed 
[INFO ] 2024-11-11 15:12:06.734 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] close complete, cost 16 ms 
[INFO ] 2024-11-11 15:12:07.134 - [任务 26] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-11 15:12:07.134 - [任务 26] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@107cb0eb 
[INFO ] 2024-11-11 15:12:07.244 - [任务 26] - Stop task milestones: 6731ab9a0e87a0346c2bdc85(任务 26)  
[INFO ] 2024-11-11 15:12:07.339 - [任务 26] - Stopped task aspect(s) 
[INFO ] 2024-11-11 15:12:07.360 - [任务 26] - Snapshot order controller have been removed 
[INFO ] 2024-11-11 15:12:07.363 - [任务 26] - Remove memory task client succeed, task: 任务 26[6731ab9a0e87a0346c2bdc85] 
[INFO ] 2024-11-11 15:12:07.363 - [任务 26] - Destroy memory task client cache succeed, task: 任务 26[6731ab9a0e87a0346c2bdc85] 
[INFO ] 2024-11-11 15:12:15.540 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-57d3b2a2-fa2b-400a-90b1-ebe07546719a complete, cost 4930ms 
[INFO ] 2024-11-11 15:12:56.969 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-534e082c-3b15-47ce-81ad-6a21c30a3254 complete, cost 27979ms 
[INFO ] 2024-11-11 15:12:59.222 - [任务 26] - Start task milestones: 6731ab9a0e87a0346c2bdc85(任务 26) 
[INFO ] 2024-11-11 15:12:59.430 - [任务 26] - Task initialization... 
[INFO ] 2024-11-11 15:12:59.938 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-abdba7b0-2c90-48d3-82f7-1e784e9ca413 complete, cost 490ms 
[INFO ] 2024-11-11 15:12:59.999 - [任务 26] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-11 15:13:00.099 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-11 15:13:00.099 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:13:00.099 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:13:00.099 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:13:00.099 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:13:00.099 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:13:00.099 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:13:00.099 - [任务 26][test] - Node custom_processor(test: def953bb-d619-4f6a-8664-4e852594e818) enable batch process 
[INFO ] 2024-11-11 15:13:00.637 - [任务 26][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" read batch size: 100 
[INFO ] 2024-11-11 15:13:00.637 - [任务 26][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" event queue capacity: 200 
[INFO ] 2024-11-11 15:13:00.637 - [任务 26][BMSQL_CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-11 15:13:00.638 - [任务 26][BMSQL_CUSTOMER] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":15077,"gtidSet":""} 
[INFO ] 2024-11-11 15:13:00.750 - [任务 26][BMSQL_CUSTOMER] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-11 15:13:00.751 - [任务 26][BMSQL_CUSTOMER] - Initial sync started 
[INFO ] 2024-11-11 15:13:00.751 - [任务 26][BMSQL_CUSTOMER] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-11-11 15:13:00.751 - [任务 26][BMSQL_CUSTOMER] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-11-11 15:13:00.761 - [任务 26][BMSQL_CUSTOMER] - Query snapshot row size completed: BMSQL_CUSTOMER(1d3acc9c-2d86-4ede-8ec9-201621d8a251) 
[INFO ] 2024-11-11 15:13:00.870 - [任务 26][testCustom] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-11 15:13:00.871 - [任务 26][testCustom] - Table "test.testCustom" exists, skip auto create table 
[INFO ] 2024-11-11 15:13:00.871 - [任务 26][testCustom] - The table testCustom has already exist. 
[INFO ] 2024-11-11 15:13:01.016 - [任务 26][testCustom] - Table 'testCustom' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2024-11-11 15:13:01.016 - [任务 26][testCustom] - Table 'testCustom' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 1 
[INFO ] 2024-11-11 15:13:01.035 - [任务 26][testCustom] - Table 'testCustom' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 2 
[INFO ] 2024-11-11 15:13:01.035 - [任务 26][testCustom] - Table 'testCustom' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 3 
[INFO ] 2024-11-11 15:13:01.052 - [任务 26][testCustom] - Table 'testCustom' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 4 
[INFO ] 2024-11-11 15:13:01.053 - [任务 26][testCustom] - Table 'testCustom' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 5 
[INFO ] 2024-11-11 15:13:01.072 - [任务 26][testCustom] - Table 'testCustom' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 6 
[INFO ] 2024-11-11 15:13:01.072 - [任务 26][testCustom] - Table 'testCustom' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 7 
[INFO ] 2024-11-11 15:13:01.096 - [任务 26][testCustom] - Table 'testCustom' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 8 
[INFO ] 2024-11-11 15:13:01.096 - [任务 26][testCustom] - Table 'testCustom' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 9 
[INFO ] 2024-11-11 15:13:01.110 - [任务 26][testCustom] - Table 'testCustom' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 10 
[INFO ] 2024-11-11 15:13:01.110 - [任务 26][testCustom] - Table 'testCustom' has more than 10 continuous duplicate key errors, all subsequent data insert policy are switched to update_on_exists 
[INFO ] 2024-11-11 15:13:01.724 - [任务 26][BMSQL_CUSTOMER] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-11 15:13:01.724 - [任务 26][BMSQL_CUSTOMER] - Initial sync completed 
[INFO ] 2024-11-11 15:13:01.724 - [任务 26][BMSQL_CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-11-11 15:13:01.724 - [任务 26][BMSQL_CUSTOMER] - Initial sync completed 
[INFO ] 2024-11-11 15:13:01.739 - [任务 26][BMSQL_CUSTOMER] - Starting stream read, table list: [BMSQL_CUSTOMER], offset: {"filename":"binlog.000038","position":15077,"gtidSet":""} 
[INFO ] 2024-11-11 15:13:01.741 - [任务 26][BMSQL_CUSTOMER] - Starting mysql cdc, server name: b3502384-3681-431a-8e0f-77b98f151be7 
[INFO ] 2024-11-11 15:13:01.741 - [任务 26][BMSQL_CUSTOMER] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"b3502384-3681-431a-8e0f-77b98f151be7","offset":{"{\"server\":\"b3502384-3681-431a-8e0f-77b98f151be7\"}":"{\"file\":\"binlog.000038\",\"pos\":15077,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1255004969
  time.precision.mode: adaptive_time_microseconds
  database.server.name: b3502384-3681-431a-8e0f-77b98f151be7
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-b3502384-3681-431a-8e0f-77b98f151be7
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: b3502384-3681-431a-8e0f-77b98f151be7
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_CUSTOMER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-11 15:13:01.942 - [任务 26][BMSQL_CUSTOMER] - Connector Mysql incremental start succeed, tables: [BMSQL_CUSTOMER], data change syncing 
[INFO ] 2024-11-11 15:13:25.834 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] running status set to false 
[INFO ] 2024-11-11 15:13:25.834 - [任务 26][BMSQL_CUSTOMER] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-11 15:13:25.857 - [任务 26][BMSQL_CUSTOMER] - Mysql binlog reader stopped 
[INFO ] 2024-11-11 15:13:25.857 - [任务 26][BMSQL_CUSTOMER] - Incremental sync completed 
[INFO ] 2024-11-11 15:13:25.857 - [任务 26][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode_1d3acc9c-2d86-4ede-8ec9-201621d8a251_1731309180392 
[INFO ] 2024-11-11 15:13:25.857 - [任务 26][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode_1d3acc9c-2d86-4ede-8ec9-201621d8a251_1731309180392 
[INFO ] 2024-11-11 15:13:25.857 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] schema data cleaned 
[INFO ] 2024-11-11 15:13:25.858 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] monitor closed 
[INFO ] 2024-11-11 15:13:25.859 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] close complete, cost 73 ms 
[INFO ] 2024-11-11 15:13:25.859 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] running status set to false 
[INFO ] 2024-11-11 15:13:25.861 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] schema data cleaned 
[INFO ] 2024-11-11 15:13:25.861 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] monitor closed 
[INFO ] 2024-11-11 15:13:25.861 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] close complete, cost 2 ms 
[INFO ] 2024-11-11 15:13:25.877 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] running status set to false 
[INFO ] 2024-11-11 15:13:25.878 - [任务 26][testCustom] - PDK connector node stopped: HazelcastTargetPdkDataNode_4d2736f0-c8e7-4e23-afa1-b58cde1de30b_1731309180553 
[INFO ] 2024-11-11 15:13:25.878 - [任务 26][testCustom] - PDK connector node released: HazelcastTargetPdkDataNode_4d2736f0-c8e7-4e23-afa1-b58cde1de30b_1731309180553 
[INFO ] 2024-11-11 15:13:25.878 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] schema data cleaned 
[INFO ] 2024-11-11 15:13:25.878 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] monitor closed 
[INFO ] 2024-11-11 15:13:25.879 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] close complete, cost 16 ms 
[INFO ] 2024-11-11 15:13:26.896 - [任务 26] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-11 15:13:26.897 - [任务 26] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@31fd1094 
[INFO ] 2024-11-11 15:13:27.024 - [任务 26] - Stop task milestones: 6731ab9a0e87a0346c2bdc85(任务 26)  
[INFO ] 2024-11-11 15:13:27.024 - [任务 26] - Stopped task aspect(s) 
[INFO ] 2024-11-11 15:13:27.047 - [任务 26] - Snapshot order controller have been removed 
[INFO ] 2024-11-11 15:13:27.047 - [任务 26] - Remove memory task client succeed, task: 任务 26[6731ab9a0e87a0346c2bdc85] 
[INFO ] 2024-11-11 15:13:27.047 - [任务 26] - Destroy memory task client cache succeed, task: 任务 26[6731ab9a0e87a0346c2bdc85] 
[INFO ] 2024-11-11 15:13:34.578 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-07637f7c-8088-4a86-818b-9eb1efcfefae complete, cost 2968ms 
[INFO ] 2024-11-11 15:13:39.096 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-602c6f38-494d-45b7-a0ec-594c2471d833 complete, cost 2963ms 
[INFO ] 2024-11-11 15:13:44.733 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-8bbe753f-b16a-4b6c-84af-4c32637496fa complete, cost 3302ms 
[INFO ] 2024-11-11 15:13:47.567 - [任务 26] - Start task milestones: 6731ab9a0e87a0346c2bdc85(任务 26) 
[INFO ] 2024-11-11 15:13:47.717 - [任务 26] - Task initialization... 
[INFO ] 2024-11-11 15:13:50.647 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-a76ddd8b-2b87-4734-b1c4-601db2f930aa complete, cost 3007ms 
[INFO ] 2024-11-11 15:13:50.751 - [任务 26] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-11 15:13:50.829 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-11 15:13:50.829 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:13:50.829 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:13:50.829 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:13:50.829 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:13:50.829 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:13:50.829 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] preload schema finished, cost 1 ms 
[INFO ] 2024-11-11 15:13:50.829 - [任务 26][test] - Node custom_processor(test: def953bb-d619-4f6a-8664-4e852594e818) enable batch process 
[INFO ] 2024-11-11 15:13:51.314 - [任务 26][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" read batch size: 100 
[INFO ] 2024-11-11 15:13:51.316 - [任务 26][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" event queue capacity: 200 
[INFO ] 2024-11-11 15:13:51.316 - [任务 26][BMSQL_CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-11 15:13:51.317 - [任务 26][BMSQL_CUSTOMER] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":15077,"gtidSet":""} 
[INFO ] 2024-11-11 15:13:51.317 - [任务 26][BMSQL_CUSTOMER] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-11 15:13:51.399 - [任务 26][BMSQL_CUSTOMER] - Initial sync started 
[INFO ] 2024-11-11 15:13:51.399 - [任务 26][BMSQL_CUSTOMER] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-11-11 15:13:51.411 - [任务 26][BMSQL_CUSTOMER] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-11-11 15:13:51.411 - [任务 26][BMSQL_CUSTOMER] - Query snapshot row size completed: BMSQL_CUSTOMER(1d3acc9c-2d86-4ede-8ec9-201621d8a251) 
[INFO ] 2024-11-11 15:13:51.497 - [任务 26][test] - Exception skipping - The current exception does not match the skip exception strategy, message: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property 'get' of undefined 
[ERROR] 2024-11-11 15:13:51.500 - [任务 26][test] - java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property 'get' of undefined <-- Error Message -->
java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property 'get' of undefined

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property 'get' of undefined
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.execute(HazelcastCustomProcessor.java:151)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.tryProcess(HazelcastCustomProcessor.java:114)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:392)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$batchProcess$9(HazelcastProcessorBaseNode.java:194)
	io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property 'get' of undefined
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:735)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:617)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:610)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$1(HazelcastProcessorBaseNode.java:141)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode$EventBatchProcessor.lambda$new$0(HazelcastProcessorBaseNode.java:474)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property 'get' of undefined
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.execute(HazelcastCustomProcessor.java:151)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.tryProcess(HazelcastCustomProcessor.java:114)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:392)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$batchProcess$9(HazelcastProcessorBaseNode.java:194)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:190)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$1(HazelcastProcessorBaseNode.java:135)
	... 7 more

[INFO ] 2024-11-11 15:13:51.512 - [任务 26][test] - Job suspend in error handle 
[INFO ] 2024-11-11 15:13:51.513 - [任务 26][testCustom] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-11 15:13:51.688 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] running status set to false 
[INFO ] 2024-11-11 15:13:51.688 - [任务 26][BMSQL_CUSTOMER] - Initial sync completed 
[INFO ] 2024-11-11 15:13:51.697 - [任务 26][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode_1d3acc9c-2d86-4ede-8ec9-201621d8a251_1731309231124 
[INFO ] 2024-11-11 15:13:51.697 - [任务 26][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode_1d3acc9c-2d86-4ede-8ec9-201621d8a251_1731309231124 
[INFO ] 2024-11-11 15:13:51.697 - [任务 26][BMSQL_CUSTOMER] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.lang.NullPointerException message given: null

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.lang.NullPointerException

STACKTRACE:

java.lang.NullPointerException
	at java.lang.System.arraycopy(Native Method)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:161)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 
[INFO ] 2024-11-11 15:13:51.697 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] schema data cleaned 
[INFO ] 2024-11-11 15:13:51.697 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] monitor closed 
[INFO ] 2024-11-11 15:13:51.702 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] close complete, cost 63 ms 
[INFO ] 2024-11-11 15:13:51.707 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] running status set to false 
[INFO ] 2024-11-11 15:13:51.707 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] schema data cleaned 
[INFO ] 2024-11-11 15:13:51.707 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] monitor closed 
[INFO ] 2024-11-11 15:13:51.707 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] close complete, cost 4 ms 
[INFO ] 2024-11-11 15:13:51.707 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] running status set to false 
[ERROR] 2024-11-11 15:13:51.723 - [任务 26][BMSQL_CUSTOMER] - Unknown PDK exception occur, java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.lang.NullPointerException message given: null

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.lang.NullPointerException

STACKTRACE:

java.lang.NullPointerException
	at java.lang.System.arraycopy(Native Method)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:161)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 <-- Error Message -->
Unknown PDK exception occur, java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.lang.NullPointerException message given: null

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.lang.NullPointerException

STACKTRACE:

java.lang.NullPointerException
	at java.lang.System.arraycopy(Native Method)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:161)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **



<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	java.lang.System.arraycopy(Native Method)
	com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:161)
	com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	java.io.FilterInputStream.read(FilterInputStream.java:133)
	com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	...

<-- Full Stack Trace -->
java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.lang.NullPointerException message given: null

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.lang.NullPointerException

STACKTRACE:

java.lang.NullPointerException
	at java.lang.System.arraycopy(Native Method)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:161)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.lang.NullPointerException message given: null

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.lang.NullPointerException

STACKTRACE:

java.lang.NullPointerException
	at java.lang.System.arraycopy(Native Method)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:161)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:213)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 19 more
Caused by: java.sql.SQLException: Error retrieving record: Unexpected Exception: java.lang.NullPointerException message given: null

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.lang.NullPointerException

STACKTRACE:

java.lang.NullPointerException
	at java.lang.System.arraycopy(Native Method)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:161)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:131)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1952)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	... 23 more
Caused by: java.lang.NullPointerException
	at java.lang.System.arraycopy(Native Method)
	at com.mysql.cj.protocol.ReadAheadInputStream.readFromUnderlyingStreamIfNecessary(ReadAheadInputStream.java:161)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:180)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessageLocal(SimplePacketReader.java:137)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:102)
	at com.mysql.cj.protocol.a.SimplePacketReader.readMessage(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:62)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readMessage(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:66)
	at com.mysql.cj.protocol.a.MultiPacketReader.readMessage(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:75)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	... 26 more

[INFO ] 2024-11-11 15:13:51.724 - [任务 26][testCustom] - PDK connector node stopped: HazelcastTargetPdkDataNode_4d2736f0-c8e7-4e23-afa1-b58cde1de30b_1731309231276 
[INFO ] 2024-11-11 15:13:51.724 - [任务 26][testCustom] - PDK connector node released: HazelcastTargetPdkDataNode_4d2736f0-c8e7-4e23-afa1-b58cde1de30b_1731309231276 
[INFO ] 2024-11-11 15:13:51.724 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] schema data cleaned 
[INFO ] 2024-11-11 15:13:51.724 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] monitor closed 
[INFO ] 2024-11-11 15:13:51.729 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] close complete, cost 15 ms 
[INFO ] 2024-11-11 15:13:52.087 - [任务 26] - Task [任务 26] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-11 15:13:52.087 - [任务 26] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-11 15:13:52.087 - [任务 26] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@64dbf7a5 
[INFO ] 2024-11-11 15:13:52.203 - [任务 26] - Stop task milestones: 6731ab9a0e87a0346c2bdc85(任务 26)  
[INFO ] 2024-11-11 15:13:52.218 - [任务 26] - Stopped task aspect(s) 
[INFO ] 2024-11-11 15:13:52.218 - [任务 26] - Snapshot order controller have been removed 
[INFO ] 2024-11-11 15:13:52.234 - [任务 26] - Remove memory task client succeed, task: 任务 26[6731ab9a0e87a0346c2bdc85] 
[INFO ] 2024-11-11 15:13:52.236 - [任务 26] - Destroy memory task client cache succeed, task: 任务 26[6731ab9a0e87a0346c2bdc85] 
[INFO ] 2024-11-11 15:14:05.816 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-d9bc01c5-35b2-4200-aca6-e62690ea0390 complete, cost 2929ms 
[INFO ] 2024-11-11 15:15:18.503 - [任务 26] - Start task milestones: 6731ab9a0e87a0346c2bdc85(任务 26) 
[INFO ] 2024-11-11 15:15:18.712 - [任务 26] - Task initialization... 
[INFO ] 2024-11-11 15:15:21.974 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-e6aae2af-29c0-4b59-b5a4-28d0a437058f complete, cost 3303ms 
[INFO ] 2024-11-11 15:15:22.066 - [任务 26] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-11 15:15:22.066 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-11 15:15:22.097 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:15:22.097 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:15:22.097 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:15:22.097 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:15:22.097 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:15:22.097 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:15:22.097 - [任务 26][test] - Node custom_processor(test: def953bb-d619-4f6a-8664-4e852594e818) enable batch process 
[INFO ] 2024-11-11 15:15:22.638 - [任务 26][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" read batch size: 100 
[INFO ] 2024-11-11 15:15:22.639 - [任务 26][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" event queue capacity: 200 
[INFO ] 2024-11-11 15:15:22.639 - [任务 26][BMSQL_CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-11 15:15:22.642 - [任务 26][BMSQL_CUSTOMER] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":15077,"gtidSet":""} 
[INFO ] 2024-11-11 15:15:22.642 - [任务 26][BMSQL_CUSTOMER] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-11 15:15:22.737 - [任务 26][BMSQL_CUSTOMER] - Initial sync started 
[INFO ] 2024-11-11 15:15:22.737 - [任务 26][BMSQL_CUSTOMER] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-11-11 15:15:22.737 - [任务 26][BMSQL_CUSTOMER] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-11-11 15:15:22.743 - [任务 26][BMSQL_CUSTOMER] - Query snapshot row size completed: BMSQL_CUSTOMER(1d3acc9c-2d86-4ede-8ec9-201621d8a251) 
[INFO ] 2024-11-11 15:15:22.832 - [任务 26][test] - Exception skipping - The current exception does not match the skip exception strategy, message: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property '0' of undefined 
[ERROR] 2024-11-11 15:15:22.833 - [任务 26][test] - java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property '0' of undefined <-- Error Message -->
java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property '0' of undefined

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property '0' of undefined
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.execute(HazelcastCustomProcessor.java:151)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.tryProcess(HazelcastCustomProcessor.java:114)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:392)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$batchProcess$9(HazelcastProcessorBaseNode.java:194)
	io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property '0' of undefined
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:735)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:617)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:610)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$1(HazelcastProcessorBaseNode.java:141)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode$EventBatchProcessor.lambda$new$0(HazelcastProcessorBaseNode.java:474)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property '0' of undefined
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.execute(HazelcastCustomProcessor.java:151)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.tryProcess(HazelcastCustomProcessor.java:114)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:392)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$batchProcess$9(HazelcastProcessorBaseNode.java:194)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:190)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$1(HazelcastProcessorBaseNode.java:135)
	... 7 more

[INFO ] 2024-11-11 15:15:22.843 - [任务 26][testCustom] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-11 15:15:22.843 - [任务 26][test] - Job suspend in error handle 
[INFO ] 2024-11-11 15:15:23.831 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] running status set to false 
[INFO ] 2024-11-11 15:15:23.857 - [任务 26][BMSQL_CUSTOMER] - Initial sync completed 
[INFO ] 2024-11-11 15:15:23.869 - [任务 26][BMSQL_CUSTOMER] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 
[INFO ] 2024-11-11 15:15:23.870 - [任务 26][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode_1d3acc9c-2d86-4ede-8ec9-201621d8a251_1731309322398 
[INFO ] 2024-11-11 15:15:23.870 - [任务 26][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode_1d3acc9c-2d86-4ede-8ec9-201621d8a251_1731309322398 
[ERROR] 2024-11-11 15:15:23.870 - [任务 26][BMSQL_CUSTOMER] - Unknown PDK exception occur, java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 <-- Error Message -->
Unknown PDK exception occur, java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **



<-- Simple Stack Trace -->
Caused by: java.io.IOException: Socket is closed.
	com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	...

<-- Full Stack Trace -->
java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:213)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 19 more
Caused by: java.sql.SQLException: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:131)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1952)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	... 23 more
Caused by: java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	... 26 more

[INFO ] 2024-11-11 15:15:23.871 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] schema data cleaned 
[INFO ] 2024-11-11 15:15:23.871 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] monitor closed 
[INFO ] 2024-11-11 15:15:23.873 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] close complete, cost 42 ms 
[INFO ] 2024-11-11 15:15:23.873 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] running status set to false 
[INFO ] 2024-11-11 15:15:23.881 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] schema data cleaned 
[INFO ] 2024-11-11 15:15:23.881 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] monitor closed 
[INFO ] 2024-11-11 15:15:23.882 - [任务 26][test] - Node test[def953bb-d619-4f6a-8664-4e852594e818] close complete, cost 8 ms 
[INFO ] 2024-11-11 15:15:23.882 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] running status set to false 
[INFO ] 2024-11-11 15:15:23.894 - [任务 26][testCustom] - PDK connector node stopped: HazelcastTargetPdkDataNode_4d2736f0-c8e7-4e23-afa1-b58cde1de30b_1731309322567 
[INFO ] 2024-11-11 15:15:23.894 - [任务 26][testCustom] - PDK connector node released: HazelcastTargetPdkDataNode_4d2736f0-c8e7-4e23-afa1-b58cde1de30b_1731309322567 
[INFO ] 2024-11-11 15:15:23.895 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] schema data cleaned 
[INFO ] 2024-11-11 15:15:23.895 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] monitor closed 
[INFO ] 2024-11-11 15:15:24.097 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] close complete, cost 12 ms 
[INFO ] 2024-11-11 15:15:27.314 - [任务 26] - Task [任务 26] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-11 15:15:27.331 - [任务 26] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-11 15:15:27.332 - [任务 26] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@2c70fa2c 
[INFO ] 2024-11-11 15:15:27.443 - [任务 26] - Stop task milestones: 6731ab9a0e87a0346c2bdc85(任务 26)  
[INFO ] 2024-11-11 15:15:27.466 - [任务 26] - Stopped task aspect(s) 
[INFO ] 2024-11-11 15:15:27.466 - [任务 26] - Snapshot order controller have been removed 
[INFO ] 2024-11-11 15:15:27.481 - [任务 26] - Remove memory task client succeed, task: 任务 26[6731ab9a0e87a0346c2bdc85] 
[INFO ] 2024-11-11 15:15:27.483 - [任务 26] - Destroy memory task client cache succeed, task: 任务 26[6731ab9a0e87a0346c2bdc85] 
[INFO ] 2024-11-11 15:15:37.741 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-23c46a34-31c1-4d0f-83d8-7f0c78d493d8 complete, cost 2987ms 
[INFO ] 2024-11-11 15:17:14.952 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-bf26fa15-6d8f-4993-a270-7aff3c704faa complete, cost 3026ms 
[INFO ] 2024-11-11 15:17:15.912 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-7434cbbe-2e35-4760-8789-26f0955e8a48 complete, cost 2942ms 
[INFO ] 2024-11-11 15:17:37.316 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-5831204c-0b3e-4107-a27b-9ed259545fa8 complete, cost 3241ms 
[INFO ] 2024-11-11 15:18:04.567 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-f78335b9-4bde-4cfe-a7b3-0a4a0e59d6a2 complete, cost 2991ms 
[INFO ] 2024-11-11 15:18:14.556 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-d6465199-8013-4a40-a207-d3aaca258460 complete, cost 2880ms 
[INFO ] 2024-11-11 15:18:18.548 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-22438666-f5f1-471c-af64-79cfa1b59be4 complete, cost 3010ms 
[INFO ] 2024-11-11 15:18:18.998 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-eb493d45-238d-440f-9f75-c27800e54591 complete, cost 2897ms 
[INFO ] 2024-11-11 15:18:25.718 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-5852c539-ff01-4c97-b54f-7f724615e4b3 complete, cost 2910ms 
[INFO ] 2024-11-11 15:18:55.481 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-77ba76d3-81fb-43eb-af76-567eefa261f6 complete, cost 2989ms 
[INFO ] 2024-11-11 15:18:56.647 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-c698ab7a-9689-415d-8f63-acbdab0afdcf complete, cost 2854ms 
[INFO ] 2024-11-11 15:18:57.377 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-2791d86c-e709-4518-a750-6e5b3a65032d complete, cost 3026ms 
[INFO ] 2024-11-11 15:19:02.224 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-a274e68e-6aa0-4657-8a9b-969a1c1b9659 complete, cost 2882ms 
[INFO ] 2024-11-11 15:19:05.093 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-9d3fa18b-2f75-4cee-8c88-929b93eec518 complete, cost 3028ms 
[INFO ] 2024-11-11 15:19:05.220 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-2079c07b-11f1-48ff-b4d2-e3bfb8e40971 complete, cost 2846ms 
[INFO ] 2024-11-11 15:19:13.529 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-0716da79-c4e5-424c-ad2c-7977cc553373 complete, cost 3002ms 
[INFO ] 2024-11-11 15:19:19.921 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-2c00d89b-cf91-4a8c-82dc-315c813c1bb7 complete, cost 3264ms 
[INFO ] 2024-11-11 15:19:22.892 - [任务 26] - Start task milestones: 6731ab9a0e87a0346c2bdc85(任务 26) 
[INFO ] 2024-11-11 15:19:23.085 - [任务 26] - Task initialization... 
[INFO ] 2024-11-11 15:19:26.042 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-37d290f7-6384-4b85-87d6-4dada36e6f8f complete, cost 3037ms 
[INFO ] 2024-11-11 15:19:26.119 - [任务 26] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-11 15:19:26.196 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-11 15:19:26.196 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:19:26.196 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:19:26.196 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:19:26.196 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:19:26.196 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:19:26.196 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] preload schema finished, cost 1 ms 
[INFO ] 2024-11-11 15:19:26.196 - [任务 26][test] - Node custom_processor(test: e4de84d0-fcec-4a0b-8e7a-d1b22693378d) enable batch process 
[INFO ] 2024-11-11 15:19:26.659 - [任务 26][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" read batch size: 100 
[INFO ] 2024-11-11 15:19:26.659 - [任务 26][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" event queue capacity: 200 
[INFO ] 2024-11-11 15:19:26.659 - [任务 26][BMSQL_CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-11 15:19:26.662 - [任务 26][BMSQL_CUSTOMER] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":15077,"gtidSet":""} 
[INFO ] 2024-11-11 15:19:26.662 - [任务 26][BMSQL_CUSTOMER] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-11 15:19:26.752 - [任务 26][BMSQL_CUSTOMER] - Initial sync started 
[INFO ] 2024-11-11 15:19:26.752 - [任务 26][BMSQL_CUSTOMER] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-11-11 15:19:26.752 - [任务 26][BMSQL_CUSTOMER] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-11-11 15:19:26.770 - [任务 26][BMSQL_CUSTOMER] - Query snapshot row size completed: BMSQL_CUSTOMER(1d3acc9c-2d86-4ede-8ec9-201621d8a251) 
[INFO ] 2024-11-11 15:19:26.867 - [任务 26][test] - Exception skipping - The current exception does not match the skip exception strategy, message: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: ReferenceError: recordValue is not defined 
[ERROR] 2024-11-11 15:19:26.868 - [任务 26][test] - java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: ReferenceError: recordValue is not defined <-- Error Message -->
java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: ReferenceError: recordValue is not defined

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: ReferenceError: recordValue is not defined
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.execute(HazelcastCustomProcessor.java:151)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.tryProcess(HazelcastCustomProcessor.java:114)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:392)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$batchProcess$9(HazelcastProcessorBaseNode.java:194)
	io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: ReferenceError: recordValue is not defined
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:735)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:617)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:610)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$1(HazelcastProcessorBaseNode.java:141)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode$EventBatchProcessor.lambda$new$0(HazelcastProcessorBaseNode.java:474)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: ReferenceError: recordValue is not defined
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.execute(HazelcastCustomProcessor.java:151)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.tryProcess(HazelcastCustomProcessor.java:114)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:392)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$batchProcess$9(HazelcastProcessorBaseNode.java:194)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:190)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$1(HazelcastProcessorBaseNode.java:135)
	... 7 more

[INFO ] 2024-11-11 15:19:26.883 - [任务 26][test] - Job suspend in error handle 
[INFO ] 2024-11-11 15:19:26.883 - [任务 26][testCustom] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-11 15:19:27.869 - [任务 26] - Task [任务 26] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-11 15:19:27.870 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] running status set to false 
[INFO ] 2024-11-11 15:19:27.902 - [任务 26][BMSQL_CUSTOMER] - Initial sync completed 
[INFO ] 2024-11-11 15:19:27.903 - [任务 26][BMSQL_CUSTOMER] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed

STACKTRACE:

java.io.IOException: Stream closed
	at com.mysql.cj.protocol.ReadAheadInputStream.checkClosed(ReadAheadInputStream.java:231)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:170)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 
[INFO ] 2024-11-11 15:19:27.907 - [任务 26][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode_1d3acc9c-2d86-4ede-8ec9-201621d8a251_1731309566488 
[INFO ] 2024-11-11 15:19:27.907 - [任务 26][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode_1d3acc9c-2d86-4ede-8ec9-201621d8a251_1731309566488 
[INFO ] 2024-11-11 15:19:27.907 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] schema data cleaned 
[INFO ] 2024-11-11 15:19:27.908 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] monitor closed 
[INFO ] 2024-11-11 15:19:27.912 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] close complete, cost 43 ms 
[INFO ] 2024-11-11 15:19:27.915 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] running status set to false 
[ERROR] 2024-11-11 15:19:27.915 - [任务 26][BMSQL_CUSTOMER] - Unknown PDK exception occur, java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed

STACKTRACE:

java.io.IOException: Stream closed
	at com.mysql.cj.protocol.ReadAheadInputStream.checkClosed(ReadAheadInputStream.java:231)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:170)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 <-- Error Message -->
Unknown PDK exception occur, java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed

STACKTRACE:

java.io.IOException: Stream closed
	at com.mysql.cj.protocol.ReadAheadInputStream.checkClosed(ReadAheadInputStream.java:231)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:170)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **



<-- Simple Stack Trace -->
Caused by: java.io.IOException: Stream closed
	com.mysql.cj.protocol.ReadAheadInputStream.checkClosed(ReadAheadInputStream.java:231)
	com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:170)
	java.io.FilterInputStream.read(FilterInputStream.java:133)
	com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	...

<-- Full Stack Trace -->
java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed

STACKTRACE:

java.io.IOException: Stream closed
	at com.mysql.cj.protocol.ReadAheadInputStream.checkClosed(ReadAheadInputStream.java:231)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:170)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed

STACKTRACE:

java.io.IOException: Stream closed
	at com.mysql.cj.protocol.ReadAheadInputStream.checkClosed(ReadAheadInputStream.java:231)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:170)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:213)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 19 more
Caused by: java.sql.SQLException: Error retrieving record: Unexpected Exception: java.io.IOException message given: Stream closed

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Stream closed

STACKTRACE:

java.io.IOException: Stream closed
	at com.mysql.cj.protocol.ReadAheadInputStream.checkClosed(ReadAheadInputStream.java:231)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:170)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:131)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1952)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	... 23 more
Caused by: java.io.IOException: Stream closed
	at com.mysql.cj.protocol.ReadAheadInputStream.checkClosed(ReadAheadInputStream.java:231)
	at com.mysql.cj.protocol.ReadAheadInputStream.read(ReadAheadInputStream.java:170)
	at java.io.FilterInputStream.read(FilterInputStream.java:133)
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:64)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	... 26 more

[INFO ] 2024-11-11 15:19:27.923 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] schema data cleaned 
[INFO ] 2024-11-11 15:19:27.923 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] monitor closed 
[INFO ] 2024-11-11 15:19:27.924 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] close complete, cost 11 ms 
[INFO ] 2024-11-11 15:19:27.924 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] running status set to false 
[INFO ] 2024-11-11 15:19:27.936 - [任务 26][testCustom] - PDK connector node stopped: HazelcastTargetPdkDataNode_4d2736f0-c8e7-4e23-afa1-b58cde1de30b_1731309566646 
[INFO ] 2024-11-11 15:19:27.936 - [任务 26][testCustom] - PDK connector node released: HazelcastTargetPdkDataNode_4d2736f0-c8e7-4e23-afa1-b58cde1de30b_1731309566646 
[INFO ] 2024-11-11 15:19:27.937 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] schema data cleaned 
[INFO ] 2024-11-11 15:19:27.937 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] monitor closed 
[INFO ] 2024-11-11 15:19:27.937 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] close complete, cost 13 ms 
[INFO ] 2024-11-11 15:19:32.739 - [任务 26] - Task [任务 26] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-11 15:19:32.753 - [任务 26] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-11 15:19:32.753 - [任务 26] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1af6b25f 
[INFO ] 2024-11-11 15:19:32.905 - [任务 26] - Stop task milestones: 6731ab9a0e87a0346c2bdc85(任务 26)  
[INFO ] 2024-11-11 15:19:32.905 - [任务 26] - Stopped task aspect(s) 
[INFO ] 2024-11-11 15:19:32.905 - [任务 26] - Snapshot order controller have been removed 
[INFO ] 2024-11-11 15:19:32.930 - [任务 26] - Remove memory task client succeed, task: 任务 26[6731ab9a0e87a0346c2bdc85] 
[INFO ] 2024-11-11 15:19:32.932 - [任务 26] - Destroy memory task client cache succeed, task: 任务 26[6731ab9a0e87a0346c2bdc85] 
[INFO ] 2024-11-11 15:24:39.907 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-c94c5390-6111-4215-ad1e-cc06d863f34d complete, cost 3286ms 
[INFO ] 2024-11-11 15:24:45.914 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-f3a71a21-cda5-4b8d-a68c-e3285b039961 complete, cost 3040ms 
[INFO ] 2024-11-11 15:24:49.528 - [任务 26] - Start task milestones: 6731ab9a0e87a0346c2bdc85(任务 26) 
[INFO ] 2024-11-11 15:24:49.528 - [任务 26] - Task initialization... 
[INFO ] 2024-11-11 15:24:52.683 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-5cac4b18-e612-4da9-9263-423a77ddb72e complete, cost 3101ms 
[INFO ] 2024-11-11 15:24:52.773 - [任务 26] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-11 15:24:52.924 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-11 15:24:52.925 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:24:52.925 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:24:52.925 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:24:52.925 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:24:52.925 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:24:52.926 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] preload schema finished, cost 1 ms 
[INFO ] 2024-11-11 15:24:52.926 - [任务 26][test] - Node custom_processor(test: e4de84d0-fcec-4a0b-8e7a-d1b22693378d) enable batch process 
[INFO ] 2024-11-11 15:24:53.436 - [任务 26][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" read batch size: 100 
[INFO ] 2024-11-11 15:24:53.436 - [任务 26][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" event queue capacity: 200 
[INFO ] 2024-11-11 15:24:53.436 - [任务 26][BMSQL_CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-11 15:24:53.439 - [任务 26][BMSQL_CUSTOMER] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":15077,"gtidSet":""} 
[INFO ] 2024-11-11 15:24:53.439 - [任务 26][BMSQL_CUSTOMER] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-11 15:24:53.526 - [任务 26][BMSQL_CUSTOMER] - Initial sync started 
[INFO ] 2024-11-11 15:24:53.526 - [任务 26][BMSQL_CUSTOMER] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-11-11 15:24:53.526 - [任务 26][BMSQL_CUSTOMER] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-11-11 15:24:53.534 - [任务 26][BMSQL_CUSTOMER] - Query snapshot row size completed: BMSQL_CUSTOMER(1d3acc9c-2d86-4ede-8ec9-201621d8a251) 
[INFO ] 2024-11-11 15:24:53.593 - [任务 26][test] - Exception skipping - The current exception does not match the skip exception strategy, message: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property '0' of undefined 
[ERROR] 2024-11-11 15:24:53.597 - [任务 26][test] - java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property '0' of undefined <-- Error Message -->
java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property '0' of undefined

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property '0' of undefined
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.execute(HazelcastCustomProcessor.java:151)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.tryProcess(HazelcastCustomProcessor.java:114)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:392)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$batchProcess$9(HazelcastProcessorBaseNode.java:194)
	io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property '0' of undefined
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:735)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:617)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:610)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$1(HazelcastProcessorBaseNode.java:141)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode$EventBatchProcessor.lambda$new$0(HazelcastProcessorBaseNode.java:474)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property '0' of undefined
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.execute(HazelcastCustomProcessor.java:151)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.tryProcess(HazelcastCustomProcessor.java:114)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:392)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$batchProcess$9(HazelcastProcessorBaseNode.java:194)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:190)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$1(HazelcastProcessorBaseNode.java:135)
	... 7 more

[INFO ] 2024-11-11 15:24:53.612 - [任务 26][test] - Job suspend in error handle 
[INFO ] 2024-11-11 15:24:53.612 - [任务 26][testCustom] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-11 15:24:53.785 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] running status set to false 
[INFO ] 2024-11-11 15:24:53.785 - [任务 26][BMSQL_CUSTOMER] - Initial sync completed 
[INFO ] 2024-11-11 15:24:53.802 - [任务 26][BMSQL_CUSTOMER] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 
[INFO ] 2024-11-11 15:24:53.803 - [任务 26][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode_1d3acc9c-2d86-4ede-8ec9-201621d8a251_1731309893246 
[INFO ] 2024-11-11 15:24:53.803 - [任务 26][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode_1d3acc9c-2d86-4ede-8ec9-201621d8a251_1731309893246 
[INFO ] 2024-11-11 15:24:53.803 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] schema data cleaned 
[INFO ] 2024-11-11 15:24:53.803 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] monitor closed 
[INFO ] 2024-11-11 15:24:53.805 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] close complete, cost 61 ms 
[INFO ] 2024-11-11 15:24:53.810 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] running status set to false 
[INFO ] 2024-11-11 15:24:53.810 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] schema data cleaned 
[INFO ] 2024-11-11 15:24:53.810 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] monitor closed 
[INFO ] 2024-11-11 15:24:53.810 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] close complete, cost 4 ms 
[INFO ] 2024-11-11 15:24:53.830 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] running status set to false 
[ERROR] 2024-11-11 15:24:53.831 - [任务 26][BMSQL_CUSTOMER] - Unknown PDK exception occur, java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **

 <-- Error Message -->
Unknown PDK exception occur, java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **



<-- Simple Stack Trace -->
Caused by: java.io.IOException: Socket is closed.
	com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	...

<-- Full Stack Trace -->
java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.Exception: Execute steaming query failed, sql: SELECT `C_W_ID`, `C_D_ID`, `C_ID`, `C_DISCOUNT`, `C_CREDIT`, `C_LAST`, `C_FIRST`, `C_CREDIT_LIM`, `C_BALANCE`, `C_YTD_PAYMENT`, `C_PAYMENT_CNT`, `C_DELIVERY_CNT`, `C_STREET_1`, `C_STREET_2`, `C_CITY`, `C_STATE`, `C_ZIP`, `C_PHONE`, `C_SINCE`, `C_MIDDLE`, `C_DATA`, `NEWFIELD` FROM `test`.`BMSQL_CUSTOMER`, code: S1000(0), error: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:213)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 19 more
Caused by: java.sql.SQLException: Error retrieving record: Unexpected Exception: java.io.IOException message given: Socket is closed.

Nested Stack Trace:


** BEGIN NESTED EXCEPTION ** 

java.io.IOException
MESSAGE: Socket is closed.

STACKTRACE:

java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadWithoutHashSplit(MysqlConnector.java:645)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:570)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:494)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:165)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:486)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:407)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:148)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:351)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:267)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)


** END NESTED EXCEPTION **


	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:130)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:131)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1952)
	at com.mysql.cj.jdbc.result.ResultSetImpl.close(ResultSetImpl.java:564)
	at com.zaxxer.hikari.pool.HikariProxyResultSet.close(HikariProxyResultSet.java)
	at io.tapdata.connector.mysql.MysqlJdbcContextV2.queryWithStream(MysqlJdbcContextV2.java:211)
	... 23 more
Caused by: java.io.IOException: Socket is closed.
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:73)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:52)
	at com.mysql.cj.protocol.a.TimeTrackingPacketReader.readHeader(TimeTrackingPacketReader.java:41)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:54)
	at com.mysql.cj.protocol.a.MultiPacketReader.readHeader(MultiPacketReader.java:44)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:71)
	at com.mysql.cj.protocol.a.ResultsetRowReader.read(ResultsetRowReader.java:42)
	at com.mysql.cj.protocol.a.NativeProtocol.read(NativeProtocol.java:1648)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.next(ResultsetRowsStreaming.java:194)
	at com.mysql.cj.protocol.a.result.ResultsetRowsStreaming.close(ResultsetRowsStreaming.java:116)
	at com.mysql.cj.jdbc.result.ResultSetImpl.realClose(ResultSetImpl.java:1950)
	... 26 more

[INFO ] 2024-11-11 15:24:53.841 - [任务 26][testCustom] - PDK connector node stopped: HazelcastTargetPdkDataNode_4d2736f0-c8e7-4e23-afa1-b58cde1de30b_1731309893394 
[INFO ] 2024-11-11 15:24:53.843 - [任务 26][testCustom] - PDK connector node released: HazelcastTargetPdkDataNode_4d2736f0-c8e7-4e23-afa1-b58cde1de30b_1731309893394 
[INFO ] 2024-11-11 15:24:53.847 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] schema data cleaned 
[INFO ] 2024-11-11 15:24:53.847 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] monitor closed 
[INFO ] 2024-11-11 15:24:54.049 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] close complete, cost 37 ms 
[INFO ] 2024-11-11 15:24:58.218 - [任务 26] - Task [任务 26] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-11 15:24:58.228 - [任务 26] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-11 15:24:58.229 - [任务 26] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@3106ee9c 
[INFO ] 2024-11-11 15:24:58.344 - [任务 26] - Stop task milestones: 6731ab9a0e87a0346c2bdc85(任务 26)  
[INFO ] 2024-11-11 15:24:58.368 - [任务 26] - Stopped task aspect(s) 
[INFO ] 2024-11-11 15:24:58.368 - [任务 26] - Snapshot order controller have been removed 
[INFO ] 2024-11-11 15:24:58.388 - [任务 26] - Remove memory task client succeed, task: 任务 26[6731ab9a0e87a0346c2bdc85] 
[INFO ] 2024-11-11 15:24:58.391 - [任务 26] - Destroy memory task client cache succeed, task: 任务 26[6731ab9a0e87a0346c2bdc85] 
[INFO ] 2024-11-11 15:29:53.763 - [任务 26] - Start task milestones: 6731ab9a0e87a0346c2bdc85(任务 26) 
[INFO ] 2024-11-11 15:29:53.926 - [任务 26] - Task initialization... 
[INFO ] 2024-11-11 15:29:57.246 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-a569d6f8-f354-4551-b46a-1911ef9bb9ff complete, cost 3370ms 
[INFO ] 2024-11-11 15:29:57.362 - [任务 26] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-11 15:29:57.488 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-11 15:29:57.488 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:29:57.488 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:29:57.488 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:29:57.488 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:29:57.488 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] preload schema finished, cost 1 ms 
[INFO ] 2024-11-11 15:29:57.488 - [任务 26][test] - Node custom_processor(test: e4de84d0-fcec-4a0b-8e7a-d1b22693378d) enable batch process 
[INFO ] 2024-11-11 15:29:57.488 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] preload schema finished, cost 1 ms 
[INFO ] 2024-11-11 15:29:57.837 - [任务 26][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" read batch size: 100 
[INFO ] 2024-11-11 15:29:57.837 - [任务 26][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" event queue capacity: 200 
[INFO ] 2024-11-11 15:29:57.837 - [任务 26][BMSQL_CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-11 15:29:57.839 - [任务 26][BMSQL_CUSTOMER] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":15077,"gtidSet":""} 
[INFO ] 2024-11-11 15:29:57.843 - [任务 26][BMSQL_CUSTOMER] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-11 15:29:57.943 - [任务 26][BMSQL_CUSTOMER] - Initial sync started 
[INFO ] 2024-11-11 15:29:57.943 - [任务 26][BMSQL_CUSTOMER] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-11-11 15:29:57.943 - [任务 26][BMSQL_CUSTOMER] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-11-11 15:29:57.963 - [任务 26][BMSQL_CUSTOMER] - Query snapshot row size completed: BMSQL_CUSTOMER(1d3acc9c-2d86-4ede-8ec9-201621d8a251) 
[INFO ] 2024-11-11 15:29:58.035 - [任务 26][test] - Exception skipping - The current exception does not match the skip exception strategy, message: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property '0' of null 
[ERROR] 2024-11-11 15:29:58.038 - [任务 26][test] - java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property '0' of null <-- Error Message -->
java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property '0' of null

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property '0' of null
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.execute(HazelcastCustomProcessor.java:151)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.tryProcess(HazelcastCustomProcessor.java:114)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:392)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$batchProcess$9(HazelcastProcessorBaseNode.java:194)
	io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property '0' of null
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:735)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:617)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:610)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$1(HazelcastProcessorBaseNode.java:141)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode$EventBatchProcessor.lambda$new$0(HazelcastProcessorBaseNode.java:474)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: Cannot read property '0' of null
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.execute(HazelcastCustomProcessor.java:151)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.tryProcess(HazelcastCustomProcessor.java:114)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:392)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$batchProcess$9(HazelcastProcessorBaseNode.java:194)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:190)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$1(HazelcastProcessorBaseNode.java:135)
	... 7 more

[INFO ] 2024-11-11 15:29:58.188 - [任务 26][test] - Job suspend in error handle 
[INFO ] 2024-11-11 15:29:58.189 - [任务 26][testCustom] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-11 15:29:58.302 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] running status set to false 
[INFO ] 2024-11-11 15:29:58.325 - [任务 26][BMSQL_CUSTOMER] - Initial sync completed 
[INFO ] 2024-11-11 15:29:58.325 - [任务 26][BMSQL_CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-11-11 15:29:58.325 - [任务 26][BMSQL_CUSTOMER] - Incremental sync completed 
[INFO ] 2024-11-11 15:29:58.335 - [任务 26][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode_1d3acc9c-2d86-4ede-8ec9-201621d8a251_1731310197621 
[INFO ] 2024-11-11 15:29:58.335 - [任务 26][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode_1d3acc9c-2d86-4ede-8ec9-201621d8a251_1731310197621 
[INFO ] 2024-11-11 15:29:58.335 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] schema data cleaned 
[INFO ] 2024-11-11 15:29:58.336 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] monitor closed 
[INFO ] 2024-11-11 15:29:58.338 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] close complete, cost 39 ms 
[INFO ] 2024-11-11 15:29:58.338 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] running status set to false 
[INFO ] 2024-11-11 15:29:58.343 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] schema data cleaned 
[INFO ] 2024-11-11 15:29:58.343 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] monitor closed 
[INFO ] 2024-11-11 15:29:58.344 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] close complete, cost 5 ms 
[INFO ] 2024-11-11 15:29:58.344 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] running status set to false 
[INFO ] 2024-11-11 15:29:58.356 - [任务 26][testCustom] - PDK connector node stopped: HazelcastTargetPdkDataNode_4d2736f0-c8e7-4e23-afa1-b58cde1de30b_1731310197986 
[INFO ] 2024-11-11 15:29:58.357 - [任务 26][testCustom] - PDK connector node released: HazelcastTargetPdkDataNode_4d2736f0-c8e7-4e23-afa1-b58cde1de30b_1731310197986 
[INFO ] 2024-11-11 15:29:58.357 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] schema data cleaned 
[INFO ] 2024-11-11 15:29:58.357 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] monitor closed 
[INFO ] 2024-11-11 15:29:58.357 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] close complete, cost 12 ms 
[INFO ] 2024-11-11 15:29:58.672 - [任务 26] - Task [任务 26] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-11 15:29:58.672 - [任务 26] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-11 15:29:58.672 - [任务 26] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5ec2564f 
[INFO ] 2024-11-11 15:29:58.781 - [任务 26] - Stop task milestones: 6731ab9a0e87a0346c2bdc85(任务 26)  
[INFO ] 2024-11-11 15:29:58.819 - [任务 26] - Stopped task aspect(s) 
[INFO ] 2024-11-11 15:29:58.819 - [任务 26] - Snapshot order controller have been removed 
[INFO ] 2024-11-11 15:29:58.843 - [任务 26] - Remove memory task client succeed, task: 任务 26[6731ab9a0e87a0346c2bdc85] 
[INFO ] 2024-11-11 15:29:58.844 - [任务 26] - Destroy memory task client cache succeed, task: 任务 26[6731ab9a0e87a0346c2bdc85] 
[INFO ] 2024-11-11 15:31:51.762 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-97c322d2-cb01-4d34-b836-89a2a99448a6 complete, cost 86037ms 
[INFO ] 2024-11-11 15:32:18.108 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-e644efdb-8415-4f39-bd31-31c6033d7b00 complete, cost 798ms 
[INFO ] 2024-11-11 15:32:19.456 - [任务 26] - Start task milestones: 6731ab9a0e87a0346c2bdc85(任务 26) 
[INFO ] 2024-11-11 15:32:19.456 - [任务 26] - Task initialization... 
[INFO ] 2024-11-11 15:32:20.216 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-df784677-f9a0-4c0f-9d4c-b9257bd34780 complete, cost 662ms 
[INFO ] 2024-11-11 15:32:20.284 - [任务 26] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-11 15:32:20.406 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-11 15:32:20.406 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:32:20.406 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:32:20.406 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:32:20.406 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:32:20.406 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:32:20.407 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:32:20.407 - [任务 26][test] - Node custom_processor(test: e4de84d0-fcec-4a0b-8e7a-d1b22693378d) enable batch process 
[INFO ] 2024-11-11 15:32:20.903 - [任务 26][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" read batch size: 100 
[INFO ] 2024-11-11 15:32:20.904 - [任务 26][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" event queue capacity: 200 
[INFO ] 2024-11-11 15:32:20.904 - [任务 26][BMSQL_CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-11 15:32:20.908 - [任务 26][BMSQL_CUSTOMER] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":15077,"gtidSet":""} 
[INFO ] 2024-11-11 15:32:20.910 - [任务 26][BMSQL_CUSTOMER] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-11 15:32:21.035 - [任务 26][BMSQL_CUSTOMER] - Initial sync started 
[INFO ] 2024-11-11 15:32:21.036 - [任务 26][BMSQL_CUSTOMER] - Query snapshot row size completed: BMSQL_CUSTOMER(1d3acc9c-2d86-4ede-8ec9-201621d8a251) 
[INFO ] 2024-11-11 15:32:21.036 - [任务 26][BMSQL_CUSTOMER] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-11-11 15:32:21.036 - [任务 26][BMSQL_CUSTOMER] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-11-11 15:32:21.264 - [任务 26][testCustom] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-11 15:32:21.968 - [任务 26][BMSQL_CUSTOMER] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-11-11 15:32:21.968 - [任务 26][BMSQL_CUSTOMER] - Initial sync completed 
[INFO ] 2024-11-11 15:32:21.968 - [任务 26][BMSQL_CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-11-11 15:32:21.968 - [任务 26][BMSQL_CUSTOMER] - Initial sync completed 
[INFO ] 2024-11-11 15:32:21.982 - [任务 26][BMSQL_CUSTOMER] - Starting stream read, table list: [BMSQL_CUSTOMER], offset: {"filename":"binlog.000038","position":15077,"gtidSet":""} 
[INFO ] 2024-11-11 15:32:21.988 - [任务 26][BMSQL_CUSTOMER] - Starting mysql cdc, server name: aef7316f-027f-40b2-95d8-c850cc98d816 
[INFO ] 2024-11-11 15:32:21.988 - [任务 26][BMSQL_CUSTOMER] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"aef7316f-027f-40b2-95d8-c850cc98d816","offset":{"{\"server\":\"aef7316f-027f-40b2-95d8-c850cc98d816\"}":"{\"file\":\"binlog.000038\",\"pos\":15077,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 55509833
  time.precision.mode: adaptive_time_microseconds
  database.server.name: aef7316f-027f-40b2-95d8-c850cc98d816
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-aef7316f-027f-40b2-95d8-c850cc98d816
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: aef7316f-027f-40b2-95d8-c850cc98d816
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.BMSQL_CUSTOMER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-11 15:32:22.194 - [任务 26][BMSQL_CUSTOMER] - Connector Mysql incremental start succeed, tables: [BMSQL_CUSTOMER], data change syncing 
[INFO ] 2024-11-11 15:32:57.848 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] running status set to false 
[INFO ] 2024-11-11 15:32:57.935 - [任务 26][BMSQL_CUSTOMER] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-11 15:32:57.936 - [任务 26][BMSQL_CUSTOMER] - Mysql binlog reader stopped 
[INFO ] 2024-11-11 15:32:57.950 - [任务 26][BMSQL_CUSTOMER] - Incremental sync completed 
[INFO ] 2024-11-11 15:32:57.951 - [任务 26][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode_1d3acc9c-2d86-4ede-8ec9-201621d8a251_1731310340650 
[INFO ] 2024-11-11 15:32:57.951 - [任务 26][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode_1d3acc9c-2d86-4ede-8ec9-201621d8a251_1731310340650 
[INFO ] 2024-11-11 15:32:57.951 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] schema data cleaned 
[INFO ] 2024-11-11 15:32:57.953 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] monitor closed 
[INFO ] 2024-11-11 15:32:57.953 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] close complete, cost 128 ms 
[INFO ] 2024-11-11 15:32:57.954 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] running status set to false 
[INFO ] 2024-11-11 15:32:57.954 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] schema data cleaned 
[INFO ] 2024-11-11 15:32:57.954 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] monitor closed 
[INFO ] 2024-11-11 15:32:57.955 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] close complete, cost 1 ms 
[INFO ] 2024-11-11 15:32:57.973 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] running status set to false 
[INFO ] 2024-11-11 15:32:57.973 - [任务 26][testCustom] - PDK connector node stopped: HazelcastTargetPdkDataNode_4d2736f0-c8e7-4e23-afa1-b58cde1de30b_1731310341042 
[INFO ] 2024-11-11 15:32:57.974 - [任务 26][testCustom] - PDK connector node released: HazelcastTargetPdkDataNode_4d2736f0-c8e7-4e23-afa1-b58cde1de30b_1731310341042 
[INFO ] 2024-11-11 15:32:57.974 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] schema data cleaned 
[INFO ] 2024-11-11 15:32:57.974 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] monitor closed 
[INFO ] 2024-11-11 15:32:57.975 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] close complete, cost 19 ms 
[INFO ] 2024-11-11 15:32:59.127 - [任务 26] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-11 15:32:59.129 - [任务 26] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@46047864 
[INFO ] 2024-11-11 15:32:59.237 - [任务 26] - Stop task milestones: 6731ab9a0e87a0346c2bdc85(任务 26)  
[INFO ] 2024-11-11 15:32:59.259 - [任务 26] - Stopped task aspect(s) 
[INFO ] 2024-11-11 15:32:59.259 - [任务 26] - Snapshot order controller have been removed 
[INFO ] 2024-11-11 15:32:59.283 - [任务 26] - Remove memory task client succeed, task: 任务 26[6731ab9a0e87a0346c2bdc85] 
[INFO ] 2024-11-11 15:32:59.286 - [任务 26] - Destroy memory task client cache succeed, task: 任务 26[6731ab9a0e87a0346c2bdc85] 
[INFO ] 2024-11-11 15:33:01.559 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-db4cd1d7-32b7-47b7-bab9-91afc1f9f17e complete, cost 375ms 
[INFO ] 2024-11-11 15:33:16.367 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-2172afd7-7b0e-4c4c-bd55-f6d5e9cde191 complete, cost 401ms 
[INFO ] 2024-11-11 15:33:17.008 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-129bf853-49f0-44d3-ae57-49a815719ab5 complete, cost 380ms 
[INFO ] 2024-11-11 15:33:17.574 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-aa21cb70-1a44-479e-8eb1-6c4a94f6c1ec complete, cost 1169ms 
[INFO ] 2024-11-11 15:33:17.723 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-59ed2b4b-3058-4be9-b486-39f5313b5331 complete, cost 685ms 
[INFO ] 2024-11-11 15:37:41.150 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-7ba069e2-314c-4886-ac3d-0ef526438b25 complete, cost 2990ms 
[INFO ] 2024-11-11 15:37:42.588 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-dc9f63a0-1eb1-4ee5-b3dd-376be7666118 complete, cost 2931ms 
[INFO ] 2024-11-11 15:37:47.324 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-e403f001-e57d-4e8a-aa4b-4f501f04e365 complete, cost 2903ms 
[INFO ] 2024-11-11 15:37:52.487 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-5951d0c4-c91c-4e06-86a6-02169968b5c7 complete, cost 3004ms 
[INFO ] 2024-11-11 15:37:58.953 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-4aaebb70-2b66-44f6-a9e6-2ba0294eed4b complete, cost 3294ms 
[INFO ] 2024-11-11 15:38:01.813 - [任务 26] - Start task milestones: 6731ab9a0e87a0346c2bdc85(任务 26) 
[INFO ] 2024-11-11 15:38:02.021 - [任务 26] - Task initialization... 
[INFO ] 2024-11-11 15:38:05.095 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-06dc6cc7-943e-42c2-85f7-65f6625e9915 complete, cost 3170ms 
[INFO ] 2024-11-11 15:38:05.231 - [任务 26] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-11 15:38:05.231 - [任务 26] - The engine receives 任务 26 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-11 15:38:05.273 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:38:05.273 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:38:05.273 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] start preload schema,table counts: 1 
[INFO ] 2024-11-11 15:38:05.273 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] preload schema finished, cost 1 ms 
[INFO ] 2024-11-11 15:38:05.273 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] preload schema finished, cost 1 ms 
[INFO ] 2024-11-11 15:38:05.273 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] preload schema finished, cost 0 ms 
[INFO ] 2024-11-11 15:38:05.476 - [任务 26][test] - Node custom_processor(test: e4de84d0-fcec-4a0b-8e7a-d1b22693378d) enable batch process 
[INFO ] 2024-11-11 15:38:05.539 - [任务 26][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" read batch size: 100 
[INFO ] 2024-11-11 15:38:05.539 - [任务 26][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" event queue capacity: 200 
[INFO ] 2024-11-11 15:38:05.543 - [任务 26][BMSQL_CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-11-11 15:38:05.543 - [任务 26][BMSQL_CUSTOMER] - batch offset found: {},stream offset found: {"filename":"binlog.000038","position":15077,"gtidSet":""} 
[INFO ] 2024-11-11 15:38:05.637 - [任务 26][BMSQL_CUSTOMER] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-11 15:38:05.645 - [任务 26][BMSQL_CUSTOMER] - Initial sync started 
[INFO ] 2024-11-11 15:38:05.649 - [任务 26][BMSQL_CUSTOMER] - Starting batch read, table name: BMSQL_CUSTOMER 
[INFO ] 2024-11-11 15:38:05.649 - [任务 26][BMSQL_CUSTOMER] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-11-11 15:38:05.649 - [任务 26][BMSQL_CUSTOMER] - Query snapshot row size completed: BMSQL_CUSTOMER(1d3acc9c-2d86-4ede-8ec9-201621d8a251) 
[INFO ] 2024-11-11 15:38:05.730 - [任务 26][test] - Exception skipping - The current exception does not match the skip exception strategy, message: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (json2Map) on com.tapdata.constant.JSONUtil failed due to: Cannot convert '0.2750'(language: Java, type: java.math.BigDecimal) to Java type 'java.lang.String': Invalid or lossy primitive coercion. 
[ERROR] 2024-11-11 15:38:05.731 - [任务 26][test] - java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (json2Map) on com.tapdata.constant.JSONUtil failed due to: Cannot convert '0.2750'(language: Java, type: java.math.BigDecimal) to Java type 'java.lang.String': Invalid or lossy primitive coercion. <-- Error Message -->
java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (json2Map) on com.tapdata.constant.JSONUtil failed due to: Cannot convert '0.2750'(language: Java, type: java.math.BigDecimal) to Java type 'java.lang.String': Invalid or lossy primitive coercion.

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (json2Map) on com.tapdata.constant.JSONUtil failed due to: Cannot convert '0.2750'(language: Java, type: java.math.BigDecimal) to Java type 'java.lang.String': Invalid or lossy primitive coercion.
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.execute(HazelcastCustomProcessor.java:151)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.tryProcess(HazelcastCustomProcessor.java:114)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:392)
	io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$batchProcess$9(HazelcastProcessorBaseNode.java:194)
	io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (json2Map) on com.tapdata.constant.JSONUtil failed due to: Cannot convert '0.2750'(language: Java, type: java.math.BigDecimal) to Java type 'java.lang.String': Invalid or lossy primitive coercion.
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:735)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:617)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:610)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$1(HazelcastProcessorBaseNode.java:141)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode$EventBatchProcessor.lambda$new$0(HazelcastProcessorBaseNode.java:474)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Execute script error, record: {C_LAST=BARBARBAR, NEWFIELD=null, C_CREDIT=GC, C_YTD_PAYMENT=10.00, C_STATE=CK, C_W_ID=1, C_ID=1, C_CITY=JWt8Ac, C_DATA=uXZiz, C_BALANCE=-10.00, C_FIRST=vfOQ1, C_SINCE=DateTime nano 849000000 seconds 1729693444 timeZone null, C_DISCOUNT=0.2750, C_CREDIT_LIM=50000.00, C_STREET_1=niDmLi3, C_PHONE=****************, C_STREET_2=eCwbdjUXdp, C_PAYMENT_CNT=1, C_DELIVERY_CNT=1, C_ZIP=584811111, C_MIDDLE=OE, C_D_ID=1}, error: org.graalvm.polyglot.PolyglotException: TypeError: invokeMember (json2Map) on com.tapdata.constant.JSONUtil failed due to: Cannot convert '0.2750'(language: Java, type: java.math.BigDecimal) to Java type 'java.lang.String': Invalid or lossy primitive coercion.
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.execute(HazelcastCustomProcessor.java:151)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastCustomProcessor.tryProcess(HazelcastCustomProcessor.java:114)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.tryProcess(HazelcastProcessorBaseNode.java:392)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$batchProcess$9(HazelcastProcessorBaseNode.java:194)
	at io.tapdata.aspect.utils.AspectUtils.executeProcessorFuncAspect(AspectUtils.java:102)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.batchProcess(HazelcastProcessorBaseNode.java:190)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastProcessorBaseNode.lambda$initBatchProcessorIfNeed$1(HazelcastProcessorBaseNode.java:135)
	... 7 more

[INFO ] 2024-11-11 15:38:05.938 - [任务 26][test] - Job suspend in error handle 
[INFO ] 2024-11-11 15:38:06.138 - [任务 26][testCustom] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-11-11 15:38:06.150 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] running status set to false 
[INFO ] 2024-11-11 15:38:06.166 - [任务 26][BMSQL_CUSTOMER] - Initial sync completed 
[INFO ] 2024-11-11 15:38:06.166 - [任务 26][BMSQL_CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-11-11 15:38:06.166 - [任务 26][BMSQL_CUSTOMER] - Incremental sync completed 
[INFO ] 2024-11-11 15:38:06.184 - [任务 26][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode_1d3acc9c-2d86-4ede-8ec9-201621d8a251_1731310685356 
[INFO ] 2024-11-11 15:38:06.184 - [任务 26][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode_1d3acc9c-2d86-4ede-8ec9-201621d8a251_1731310685356 
[INFO ] 2024-11-11 15:38:06.184 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] schema data cleaned 
[INFO ] 2024-11-11 15:38:06.190 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] monitor closed 
[INFO ] 2024-11-11 15:38:06.190 - [任务 26][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[1d3acc9c-2d86-4ede-8ec9-201621d8a251] close complete, cost 65 ms 
[INFO ] 2024-11-11 15:38:06.201 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] running status set to false 
[INFO ] 2024-11-11 15:38:06.201 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] schema data cleaned 
[INFO ] 2024-11-11 15:38:06.202 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] monitor closed 
[INFO ] 2024-11-11 15:38:06.202 - [任务 26][test] - Node test[e4de84d0-fcec-4a0b-8e7a-d1b22693378d] close complete, cost 11 ms 
[INFO ] 2024-11-11 15:38:06.202 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] running status set to false 
[INFO ] 2024-11-11 15:38:06.219 - [任务 26][testCustom] - PDK connector node stopped: HazelcastTargetPdkDataNode_4d2736f0-c8e7-4e23-afa1-b58cde1de30b_1731310685765 
[INFO ] 2024-11-11 15:38:06.219 - [任务 26][testCustom] - PDK connector node released: HazelcastTargetPdkDataNode_4d2736f0-c8e7-4e23-afa1-b58cde1de30b_1731310685765 
[INFO ] 2024-11-11 15:38:06.219 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] schema data cleaned 
[INFO ] 2024-11-11 15:38:06.220 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] monitor closed 
[INFO ] 2024-11-11 15:38:06.220 - [任务 26][testCustom] - Node testCustom[4d2736f0-c8e7-4e23-afa1-b58cde1de30b] close complete, cost 17 ms 
[INFO ] 2024-11-11 15:38:09.562 - [任务 26] - Task [任务 26] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-11-11 15:38:09.577 - [任务 26] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-11 15:38:09.577 - [任务 26] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@1088ff1 
[INFO ] 2024-11-11 15:38:09.716 - [任务 26] - Stop task milestones: 6731ab9a0e87a0346c2bdc85(任务 26)  
[INFO ] 2024-11-11 15:38:09.716 - [任务 26] - Stopped task aspect(s) 
[INFO ] 2024-11-11 15:38:09.716 - [任务 26] - Snapshot order controller have been removed 
[INFO ] 2024-11-11 15:38:09.742 - [任务 26] - Remove memory task client succeed, task: 任务 26[6731ab9a0e87a0346c2bdc85] 
[INFO ] 2024-11-11 15:38:09.745 - [任务 26] - Destroy memory task client cache succeed, task: 任务 26[6731ab9a0e87a0346c2bdc85] 
[INFO ] 2024-11-11 15:38:43.553 - [任务 26] - load tapTable task 6731ab9a0e87a0346c2bdc84-61fd1d92-c3b1-4727-8573-cccaf4093f9d complete, cost 2916ms 
