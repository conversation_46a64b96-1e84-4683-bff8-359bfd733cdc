[INFO ] 2024-07-17 19:56:35.516 - [来自source的共享挖掘任务] - Start task milestones: 6697b1733ea63301f61948f2(来自source的共享挖掘任务) 
[INFO ] 2024-07-17 19:56:35.542 - [来自source的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-17 19:56:35.635 - [来自source的共享挖掘任务] - The engine receives 来自source的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-17 19:56:35.636 - [来自source的共享挖掘任务][source] - Node source[d2a233bd3a4f4a549be7b14664f13d13] start preload schema,table counts: 1 
[INFO ] 2024-07-17 19:56:35.637 - [来自source的共享挖掘任务][source] - Node source[d2a233bd3a4f4a549be7b14664f13d13] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 19:56:35.664 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-17 19:56:35.692 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-17 19:56:35.702 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6697b17366ab5ede8acc6011, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697b1493ea63301f61948aa_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-510503781, shareCdcTaskId=6697b1733ea63301f61948f2, connectionId=6697b1493ea63301f61948aa) 
[INFO ] 2024-07-17 19:56:35.748 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav393?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-17 19:56:36.380 - [来自source的共享挖掘任务][source] - Source node "source" read batch size: 2000 
[INFO ] 2024-07-17 19:56:36.382 - [来自source的共享挖掘任务][source] - Source node "source" event queue capacity: 4000 
[INFO ] 2024-07-17 19:56:36.382 - [来自source的共享挖掘任务][source] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-17 19:56:36.553 - [来自source的共享挖掘任务][source] - batch offset found: {},stream offset found: {"cdcOffset":1721217396,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 19:56:36.557 - [来自source的共享挖掘任务][source] - Starting stream read, table list: [POLICY], offset: {"cdcOffset":1721217396,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 19:56:36.764 - [来自source的共享挖掘任务][source] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-17 20:03:08.422 - [来自source的共享挖掘任务][source] - Node source[d2a233bd3a4f4a549be7b14664f13d13] running status set to false 
[INFO ] 2024-07-17 20:03:08.423 - [来自source的共享挖掘任务][source] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-d2a233bd3a4f4a549be7b14664f13d13 
[INFO ] 2024-07-17 20:03:08.423 - [来自source的共享挖掘任务][source] - PDK connector node released: HazelcastSourcePdkShareCDCNode-d2a233bd3a4f4a549be7b14664f13d13 
[INFO ] 2024-07-17 20:03:08.423 - [来自source的共享挖掘任务][source] - Node source[d2a233bd3a4f4a549be7b14664f13d13] schema data cleaned 
[INFO ] 2024-07-17 20:03:08.424 - [来自source的共享挖掘任务][source] - Node source[d2a233bd3a4f4a549be7b14664f13d13] monitor closed 
[INFO ] 2024-07-17 20:03:08.428 - [来自source的共享挖掘任务][source] - Node source[d2a233bd3a4f4a549be7b14664f13d13] close complete, cost 54 ms 
[INFO ] 2024-07-17 20:03:08.429 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[fc1a8eaf493d49e68a03a27a9de6b020] running status set to false 
[INFO ] 2024-07-17 20:03:08.441 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-17 20:03:08.442 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-17 20:03:08.443 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[fc1a8eaf493d49e68a03a27a9de6b020] schema data cleaned 
[INFO ] 2024-07-17 20:03:08.443 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[fc1a8eaf493d49e68a03a27a9de6b020] monitor closed 
[INFO ] 2024-07-17 20:03:08.648 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[fc1a8eaf493d49e68a03a27a9de6b020] close complete, cost 15 ms 
[INFO ] 2024-07-17 20:03:13.158 - [来自source的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 20:03:13.159 - [来自source的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@70da40bb 
[INFO ] 2024-07-17 20:03:13.160 - [来自source的共享挖掘任务] - Stop task milestones: 6697b1733ea63301f61948f2(来自source的共享挖掘任务)  
[INFO ] 2024-07-17 20:03:13.278 - [来自source的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-17 20:03:13.278 - [来自source的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-17 20:03:13.297 - [来自source的共享挖掘任务] - Remove memory task client succeed, task: 来自source的共享挖掘任务[6697b1733ea63301f61948f2] 
[INFO ] 2024-07-17 20:03:13.299 - [来自source的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自source的共享挖掘任务[6697b1733ea63301f61948f2] 
