[INFO ] 2024-06-24 20:19:04.515 - [任务 22] - Task initialization... 
[INFO ] 2024-06-24 20:19:04.722 - [任务 22] - Start task milestones: 6679621a657c031fd5d6618b(任务 22) 
[INFO ] 2024-06-24 20:19:06.485 - [任务 22] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-06-24 20:19:06.627 - [任务 22] - The engine receives 任务 22 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-24 20:19:07.267 - [任务 22][SourceMongo] - Node SourceMongo[5ec63df2-c9ed-4b2e-b8c2-d0bc03c92f43] start preload schema,table counts: 1 
[INFO ] 2024-06-24 20:19:07.269 - [任务 22][表编辑] - Node 表编辑[69eced05-b43e-402a-8fa4-83d6061584ed] start preload schema,table counts: 1 
[INFO ] 2024-06-24 20:19:07.271 - [任务 22][SourceMongo] - Node SourceMongo[5ec63df2-c9ed-4b2e-b8c2-d0bc03c92f43] preload schema finished, cost 3 ms 
[INFO ] 2024-06-24 20:19:07.271 - [任务 22][表编辑] - Node 表编辑[69eced05-b43e-402a-8fa4-83d6061584ed] preload schema finished, cost 1 ms 
[INFO ] 2024-06-24 20:19:07.279 - [任务 22][SouceMysql] - Node SouceMysql[695ae4b2-52d8-4388-b188-4c21af8b2f7b] start preload schema,table counts: 1 
[INFO ] 2024-06-24 20:19:07.489 - [任务 22][SouceMysql] - Node SouceMysql[695ae4b2-52d8-4388-b188-4c21af8b2f7b] preload schema finished, cost 0 ms 
[INFO ] 2024-06-24 20:19:08.341 - [任务 22][SourceMongo] - Node(SourceMongo) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-06-24 20:19:08.341 - [任务 22][SourceMongo] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-24 20:19:08.506 - [任务 22][SouceMysql] - Source node "SouceMysql" read batch size: 100 
[INFO ] 2024-06-24 20:19:08.508 - [任务 22][SouceMysql] - Source node "SouceMysql" event queue capacity: 200 
[INFO ] 2024-06-24 20:19:08.513 - [任务 22][SouceMysql] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-24 20:19:08.525 - [任务 22][SouceMysql] - batch offset found: {},stream offset found: {"filename":"binlog.000031","position":1058090632,"gtidSet":""} 
[INFO ] 2024-06-24 20:19:08.629 - [任务 22][SouceMysql] - Initial sync started 
[INFO ] 2024-06-24 20:19:08.636 - [任务 22][SouceMysql] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-06-24 20:19:08.640 - [任务 22][SouceMysql] - Table CLAIM is going to be initial synced 
[INFO ] 2024-06-24 20:19:24.823 - [任务 22][SouceMysql] - Query table 'CLAIM' counts: 1130 
[INFO ] 2024-06-24 20:19:25.219 - [任务 22][SouceMysql] - Table [CLAIM] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-24 20:19:25.230 - [任务 22][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-24 20:19:25.230 - [任务 22][SouceMysql] - Incremental sync starting... 
[INFO ] 2024-06-24 20:19:25.239 - [任务 22][SouceMysql] - Initial sync completed 
[INFO ] 2024-06-24 20:19:25.244 - [任务 22][SouceMysql] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000031","position":1058090632,"gtidSet":""} 
[INFO ] 2024-06-24 20:19:25.368 - [任务 22][SouceMysql] - Starting mysql cdc, server name: 6d1f636b-d6d6-49c0-9966-7f150cd91a16 
[INFO ] 2024-06-24 20:19:25.370 - [任务 22][SouceMysql] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1282917662
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 6d1f636b-d6d6-49c0-9966-7f150cd91a16
  database.port: 3306
  threadName: Debezium-Mysql-Connector-6d1f636b-d6d6-49c0-9966-7f150cd91a16
  database.hostname: localhost
  database.password: ********
  name: 6d1f636b-d6d6-49c0-9966-7f150cd91a16
  pdk.offset.string: {"name":"6d1f636b-d6d6-49c0-9966-7f150cd91a16","offset":{"{\"server\":\"6d1f636b-d6d6-49c0-9966-7f150cd91a16\"}":"{\"file\":\"binlog.000031\",\"pos\":1058090632,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-06-24 20:19:25.568 - [任务 22][SouceMysql] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-06-24 20:19:59.226 - [任务 22] - Stop task milestones: 6679621a657c031fd5d6618b(任务 22)  
[INFO ] 2024-06-24 20:19:59.623 - [任务 22][SouceMysql] - Node SouceMysql[695ae4b2-52d8-4388-b188-4c21af8b2f7b] running status set to false 
[INFO ] 2024-06-24 20:19:59.711 - [任务 22][SouceMysql] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-06-24 20:19:59.711 - [任务 22][SouceMysql] - Mysql binlog reader stopped 
[INFO ] 2024-06-24 20:19:59.713 - [任务 22][SouceMysql] - PDK connector node stopped: HazelcastSourcePdkDataNode-695ae4b2-52d8-4388-b188-4c21af8b2f7b 
[INFO ] 2024-06-24 20:19:59.714 - [任务 22][SouceMysql] - PDK connector node released: HazelcastSourcePdkDataNode-695ae4b2-52d8-4388-b188-4c21af8b2f7b 
[INFO ] 2024-06-24 20:19:59.715 - [任务 22][SouceMysql] - Node SouceMysql[695ae4b2-52d8-4388-b188-4c21af8b2f7b] schema data cleaned 
[INFO ] 2024-06-24 20:19:59.715 - [任务 22][SouceMysql] - Node SouceMysql[695ae4b2-52d8-4388-b188-4c21af8b2f7b] monitor closed 
[INFO ] 2024-06-24 20:19:59.723 - [任务 22][SouceMysql] - Node SouceMysql[695ae4b2-52d8-4388-b188-4c21af8b2f7b] close complete, cost 97 ms 
[INFO ] 2024-06-24 20:19:59.723 - [任务 22][表编辑] - Node 表编辑[69eced05-b43e-402a-8fa4-83d6061584ed] running status set to false 
[INFO ] 2024-06-24 20:19:59.724 - [任务 22][表编辑] - Node 表编辑[69eced05-b43e-402a-8fa4-83d6061584ed] schema data cleaned 
[INFO ] 2024-06-24 20:19:59.727 - [任务 22][表编辑] - Node 表编辑[69eced05-b43e-402a-8fa4-83d6061584ed] monitor closed 
[INFO ] 2024-06-24 20:19:59.731 - [任务 22][表编辑] - Node 表编辑[69eced05-b43e-402a-8fa4-83d6061584ed] close complete, cost 4 ms 
[INFO ] 2024-06-24 20:19:59.731 - [任务 22][SourceMongo] - Node SourceMongo[5ec63df2-c9ed-4b2e-b8c2-d0bc03c92f43] running status set to false 
[INFO ] 2024-06-24 20:19:59.765 - [任务 22][SourceMongo] - PDK connector node stopped: HazelcastTargetPdkDataNode-5ec63df2-c9ed-4b2e-b8c2-d0bc03c92f43 
[INFO ] 2024-06-24 20:19:59.768 - [任务 22][SourceMongo] - PDK connector node released: HazelcastTargetPdkDataNode-5ec63df2-c9ed-4b2e-b8c2-d0bc03c92f43 
[INFO ] 2024-06-24 20:19:59.768 - [任务 22][SourceMongo] - Node SourceMongo[5ec63df2-c9ed-4b2e-b8c2-d0bc03c92f43] schema data cleaned 
[INFO ] 2024-06-24 20:19:59.768 - [任务 22][SourceMongo] - Node SourceMongo[5ec63df2-c9ed-4b2e-b8c2-d0bc03c92f43] monitor closed 
[INFO ] 2024-06-24 20:19:59.770 - [任务 22][SourceMongo] - Node SourceMongo[5ec63df2-c9ed-4b2e-b8c2-d0bc03c92f43] close complete, cost 38 ms 
[INFO ] 2024-06-24 20:20:04.820 - [任务 22] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-24 20:20:04.820 - [任务 22] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@45bc110f 
[INFO ] 2024-06-24 20:20:04.823 - [任务 22] - Stopped task aspect(s) 
[INFO ] 2024-06-24 20:20:04.823 - [任务 22] - Snapshot order controller have been removed 
[INFO ] 2024-06-24 20:20:04.878 - [任务 22] - Remove memory task client succeed, task: 任务 22[6679621a657c031fd5d6618b] 
[INFO ] 2024-06-24 20:20:04.879 - [任务 22] - Destroy memory task client cache succeed, task: 任务 22[6679621a657c031fd5d6618b] 
