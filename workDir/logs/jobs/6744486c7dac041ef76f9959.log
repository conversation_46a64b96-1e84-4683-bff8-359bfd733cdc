[INFO ] 2024-11-26 08:41:49.031 - [CDC log cache task from AutoTestMySQL] - Start task milestones: 6744486c7dac041ef76f9959(CDC log cache task from AutoTestMySQL) 
[INFO ] 2024-11-26 08:41:51.058 - [CDC log cache task from AutoTestMySQL] - No<PERSON> performs snapshot read asynchronously 
[INFO ] 2024-11-26 08:41:51.208 - [CDC log cache task from AutoTestMySQL] - The engine receives CDC log cache task from AutoTestMySQL task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-26 08:41:52.855 - [CDC log cache task from AutoTestMySQL][Mongo27018] - Node(Mongo27018) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-11-26 08:41:52.865 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Node AutoTestMySQL[038d29e49c3d467b959f4c341b61898b] start preload schema,table counts: 8 
[INFO ] 2024-11-26 08:41:52.866 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Node AutoTestMySQL[038d29e49c3d467b959f4c341b61898b] preload schema finished, cost 0 ms 
[INFO ] 2024-11-26 08:41:52.866 - [CDC log cache task from AutoTestMySQL][Mongo27018] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-11-26 08:41:53.179 - [CDC log cache task from AutoTestMySQL][Mongo27018] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67444bb84150dbb0e2481f4b, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67403b949f37fc7feebe0ab7_ReplicationTableEditor_tableEditor, version=v2, tableName=ReplicationTableEditor_tableEditor, externalStorageTableName=ExternalStorage_SHARE_CDC_-1426218403, shareCdcTaskId=6744486c7dac041ef76f9959, connectionId=67403b949f37fc7feebe0ab7) 
[INFO ] 2024-11-26 08:41:53.180 - [CDC log cache task from AutoTestMySQL][Mongo27018] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6744486f4150dbb0e2477444, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67403b949f37fc7feebe0ab7_ReplEnhancedJSTable, version=v2, tableName=ReplEnhancedJSTable, externalStorageTableName=ExternalStorage_SHARE_CDC_-1759887794, shareCdcTaskId=6744486c7dac041ef76f9959, connectionId=67403b949f37fc7feebe0ab7) 
[INFO ] 2024-11-26 08:41:53.180 - [CDC log cache task from AutoTestMySQL][Mongo27018] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67444bb84150dbb0e2481f4d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67403b949f37fc7feebe0ab7_ReplicationTableEditor, version=v2, tableName=ReplicationTableEditor, externalStorageTableName=ExternalStorage_SHARE_CDC_1638246081, shareCdcTaskId=6744486c7dac041ef76f9959, connectionId=67403b949f37fc7feebe0ab7) 
[INFO ] 2024-11-26 08:41:53.269 - [CDC log cache task from AutoTestMySQL][Mongo27018] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67444b114150dbb0e247fa2d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67403b949f37fc7feebe0ab7_ReplFilterTypeTable, version=v2, tableName=ReplFilterTypeTable, externalStorageTableName=ExternalStorage_SHARE_CDC_-1913600357, shareCdcTaskId=6744486c7dac041ef76f9959, connectionId=67403b949f37fc7feebe0ab7) 
[INFO ] 2024-11-26 08:41:53.355 - [CDC log cache task from AutoTestMySQL][Mongo27018] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6744486f4150dbb0e2477445, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67403b949f37fc7feebe0ab7_R07FilterTypeTable, version=v2, tableName=R07FilterTypeTable, externalStorageTableName=ExternalStorage_SHARE_CDC_1202192693, shareCdcTaskId=6744486c7dac041ef76f9959, connectionId=67403b949f37fc7feebe0ab7) 
[INFO ] 2024-11-26 08:41:53.368 - [CDC log cache task from AutoTestMySQL][Mongo27018] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67444bb84150dbb0e2481f4e, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67403b949f37fc7feebe0ab7_ReplicationNoProcessor, version=v2, tableName=ReplicationNoProcessor, externalStorageTableName=ExternalStorage_SHARE_CDC_-2029570121, shareCdcTaskId=6744486c7dac041ef76f9959, connectionId=67403b949f37fc7feebe0ab7) 
[INFO ] 2024-11-26 08:41:53.369 - [CDC log cache task from AutoTestMySQL][Mongo27018] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67444bb84150dbb0e2481f4c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67403b949f37fc7feebe0ab7_ReplicationFieldEditor, version=v2, tableName=ReplicationFieldEditor, externalStorageTableName=ExternalStorage_SHARE_CDC_1733720813, shareCdcTaskId=6744486c7dac041ef76f9959, connectionId=67403b949f37fc7feebe0ab7) 
[INFO ] 2024-11-26 08:41:53.817 - [CDC log cache task from AutoTestMySQL][Mongo27018] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67444b114150dbb0e247fa2e, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67403b949f37fc7feebe0ab7_ReplStandardJSTable, version=v2, tableName=ReplStandardJSTable, externalStorageTableName=ExternalStorage_SHARE_CDC_2025112871, shareCdcTaskId=6744486c7dac041ef76f9959, connectionId=67403b949f37fc7feebe0ab7) 
[INFO ] 2024-11-26 08:41:53.847 - [CDC log cache task from AutoTestMySQL][Mongo27018] - Init log data storage finished, config: ExternalStorageDto[name='Mongo27018', type='mongodb', uri='mongodb://localhost:27018/ExternalStorage', table='null', ttlDay=3] 
[INFO ] 2024-11-26 08:41:55.826 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Source node "AutoTestMySQL" read batch size: 100 
[INFO ] 2024-11-26 08:41:55.826 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Source node "AutoTestMySQL" event queue capacity: 200 
[INFO ] 2024-11-26 08:41:55.828 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-11-26 08:41:55.833 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - batch offset found: {},stream offset found: {"name":"e79dd01e-8b83-4409-9009-c22d5becb5e7","offset":{"{\"server\":\"e79dd01e-8b83-4409-9009-c22d5becb5e7\"}":"{\"ts_sec\":1732532822,\"file\":\"mysql-bin.000217\",\"pos\":872344097,\"row\":1,\"server_id\":1121,\"event\":2}"}} 
[INFO ] 2024-11-26 08:41:55.833 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-26 08:41:55.931 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Starting stream read, table list: [ReplicationTableEditor_tableEditor, ReplicationFieldEditor, ReplEnhancedJSTable, ReplStandardJSTable, ReplicationTableEditor, ReplFilterTypeTable, R07FilterTypeTable, ReplicationNoProcessor, _tapdata_heartbeat_table], offset: {"name":"e79dd01e-8b83-4409-9009-c22d5becb5e7","offset":{"{\"server\":\"e79dd01e-8b83-4409-9009-c22d5becb5e7\"}":"{\"ts_sec\":1732532822,\"file\":\"mysql-bin.000217\",\"pos\":872344097,\"row\":1,\"server_id\":1121,\"event\":2}"}} 
[INFO ] 2024-11-26 08:41:55.959 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Starting mysql cdc, server name: e79dd01e-8b83-4409-9009-c22d5becb5e7 
[INFO ] 2024-11-26 08:41:55.999 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1764420898
  time.precision.mode: adaptive_time_microseconds
  database.server.name: e79dd01e-8b83-4409-9009-c22d5becb5e7
  database.port: 3306
  threadName: Debezium-Mysql-Connector-e79dd01e-8b83-4409-9009-c22d5becb5e7
  enable.time.adjuster: false
  database.hostname: *************
  database.password: ********
  name: e79dd01e-8b83-4409-9009-c22d5becb5e7
  pdk.offset.string: {"name":"e79dd01e-8b83-4409-9009-c22d5becb5e7","offset":{"{\"server\":\"e79dd01e-8b83-4409-9009-c22d5becb5e7\"}":"{\"ts_sec\":1732532822,\"file\":\"mysql-bin.000217\",\"pos\":872344097,\"row\":1,\"server_id\":1121,\"event\":2}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: autoTest.ReplicationTableEditor_tableEditor,autoTest.ReplicationFieldEditor,autoTest.ReplEnhancedJSTable,autoTest.ReplStandardJSTable,autoTest.ReplicationTableEditor,autoTest.ReplFilterTypeTable,autoTest.R07FilterTypeTable,autoTest.ReplicationNoProcessor,autoTest._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: autoTest
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-26 08:41:57.715 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Connector Mysql incremental start succeed, tables: [ReplicationTableEditor_tableEditor, ReplicationFieldEditor, ReplEnhancedJSTable, ReplStandardJSTable, ReplicationTableEditor, ReplFilterTypeTable, R07FilterTypeTable, ReplicationNoProcessor, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-11-26 09:41:42.187 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Mysql binlog reader stopped 
[WARN ] 2024-11-26 09:41:42.188 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.io.EOFException: null
	com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:580)
	com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	java.lang.Thread.run(Thread.java:750)
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-26 10:10:24.899 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Starting mysql cdc, server name: e79dd01e-8b83-4409-9009-c22d5becb5e7 
[INFO ] 2024-11-26 10:10:25.114 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 728112060
  time.precision.mode: adaptive_time_microseconds
  database.server.name: e79dd01e-8b83-4409-9009-c22d5becb5e7
  database.port: 3306
  threadName: Debezium-Mysql-Connector-e79dd01e-8b83-4409-9009-c22d5becb5e7
  enable.time.adjuster: false
  database.hostname: *************
  database.password: ********
  name: e79dd01e-8b83-4409-9009-c22d5becb5e7
  pdk.offset.string: {"name":"e79dd01e-8b83-4409-9009-c22d5becb5e7","offset":{"{\"server\":\"e79dd01e-8b83-4409-9009-c22d5becb5e7\"}":"{\"ts_sec\":1732583259,\"file\":\"mysql-bin.000217\",\"pos\":904354251,\"row\":1,\"server_id\":1121,\"event\":2}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: autoTest.ReplicationTableEditor_tableEditor,autoTest.ReplicationFieldEditor,autoTest.ReplEnhancedJSTable,autoTest.ReplStandardJSTable,autoTest.ReplicationTableEditor,autoTest.ReplFilterTypeTable,autoTest.R07FilterTypeTable,autoTest.ReplicationNoProcessor,autoTest._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: autoTest
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-26 10:10:25.201 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-11-26 10:10:25.201 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Connector Mysql incremental start succeed, tables: [ReplicationTableEditor_tableEditor, ReplicationFieldEditor, ReplEnhancedJSTable, ReplStandardJSTable, ReplicationTableEditor, ReplFilterTypeTable, R07FilterTypeTable, ReplicationNoProcessor, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-11-26 11:06:48.684 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Mysql binlog reader stopped 
[WARN ] 2024-11-26 11:06:48.686 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - [Auto Retry] Method (source_stream_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: java.lang.IllegalStateException: BinaryLogClient is already connected
	com.github.shyiko.mysql.binlog.BinaryLogClient.connect(BinaryLogClient.java:566)
	com.github.shyiko.mysql.binlog.BinaryLogClient$6.run(BinaryLogClient.java:959)
	java.lang.Thread.run(Thread.java:750)
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-11-26 11:07:48.801 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Starting mysql cdc, server name: e79dd01e-8b83-4409-9009-c22d5becb5e7 
[INFO ] 2024-11-26 11:07:49.010 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 343190666
  time.precision.mode: adaptive_time_microseconds
  database.server.name: e79dd01e-8b83-4409-9009-c22d5becb5e7
  database.port: 3306
  threadName: Debezium-Mysql-Connector-e79dd01e-8b83-4409-9009-c22d5becb5e7
  enable.time.adjuster: false
  database.hostname: *************
  database.password: ********
  name: e79dd01e-8b83-4409-9009-c22d5becb5e7
  pdk.offset.string: {"name":"e79dd01e-8b83-4409-9009-c22d5becb5e7","offset":{"{\"server\":\"e79dd01e-8b83-4409-9009-c22d5becb5e7\"}":"{\"ts_sec\":1732590156,\"file\":\"mysql-bin.000217\",\"pos\":909879853,\"row\":1,\"server_id\":1121,\"event\":2}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: autoTest.ReplicationTableEditor_tableEditor,autoTest.ReplicationFieldEditor,autoTest.ReplEnhancedJSTable,autoTest.ReplStandardJSTable,autoTest.ReplicationTableEditor,autoTest.ReplFilterTypeTable,autoTest.R07FilterTypeTable,autoTest.ReplicationNoProcessor,autoTest._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: autoTest
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-26 11:07:49.253 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - [Auto Retry] Method (source_stream_read) retry succeed 
[INFO ] 2024-11-26 11:07:49.254 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Connector Mysql incremental start succeed, tables: [ReplicationTableEditor_tableEditor, ReplicationFieldEditor, ReplEnhancedJSTable, ReplStandardJSTable, ReplicationTableEditor, ReplFilterTypeTable, R07FilterTypeTable, ReplicationNoProcessor, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-11-26 14:49:31.878 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Node AutoTestMySQL[038d29e49c3d467b959f4c341b61898b] running status set to false 
[WARN ] 2024-11-26 14:49:32.155 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkShareCDCNode_038d29e49c3d467b959f4c341b61898b_1732581713848 
[INFO ] 2024-11-26 14:49:32.156 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - PDK connector node released: HazelcastSourcePdkShareCDCNode_038d29e49c3d467b959f4c341b61898b_1732581713848 
[INFO ] 2024-11-26 16:47:17.908 - [CDC log cache task from AutoTestMySQL] - Start task milestones: 6744486c7dac041ef76f9959(CDC log cache task from AutoTestMySQL) 
[INFO ] 2024-11-26 16:47:20.249 - [CDC log cache task from AutoTestMySQL] - Node performs snapshot read asynchronously 
[INFO ] 2024-11-26 16:47:20.585 - [CDC log cache task from AutoTestMySQL] - The engine receives CDC log cache task from AutoTestMySQL task data from TM and will continue to run tasks by jet 
[INFO ] 2024-11-26 16:47:21.956 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Node AutoTestMySQL[038d29e49c3d467b959f4c341b61898b] start preload schema,table counts: 8 
[INFO ] 2024-11-26 16:47:21.958 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Node AutoTestMySQL[038d29e49c3d467b959f4c341b61898b] preload schema finished, cost 0 ms 
[INFO ] 2024-11-26 16:47:22.169 - [CDC log cache task from AutoTestMySQL][Mongo27018] - Node(Mongo27018) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-11-26 16:47:22.187 - [CDC log cache task from AutoTestMySQL][Mongo27018] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-11-26 16:47:22.519 - [CDC log cache task from AutoTestMySQL][Mongo27018] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67444bb84150dbb0e2481f4e, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67403b949f37fc7feebe0ab7_ReplicationNoProcessor, version=v2, tableName=ReplicationNoProcessor, externalStorageTableName=ExternalStorage_SHARE_CDC_-2029570121, shareCdcTaskId=6744486c7dac041ef76f9959, connectionId=67403b949f37fc7feebe0ab7) 
[INFO ] 2024-11-26 16:47:22.539 - [CDC log cache task from AutoTestMySQL][Mongo27018] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6744486f4150dbb0e2477444, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67403b949f37fc7feebe0ab7_ReplEnhancedJSTable, version=v2, tableName=ReplEnhancedJSTable, externalStorageTableName=ExternalStorage_SHARE_CDC_-1759887794, shareCdcTaskId=6744486c7dac041ef76f9959, connectionId=67403b949f37fc7feebe0ab7) 
[INFO ] 2024-11-26 16:47:22.546 - [CDC log cache task from AutoTestMySQL][Mongo27018] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6744486f4150dbb0e2477445, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67403b949f37fc7feebe0ab7_R07FilterTypeTable, version=v2, tableName=R07FilterTypeTable, externalStorageTableName=ExternalStorage_SHARE_CDC_1202192693, shareCdcTaskId=6744486c7dac041ef76f9959, connectionId=67403b949f37fc7feebe0ab7) 
[INFO ] 2024-11-26 16:47:22.551 - [CDC log cache task from AutoTestMySQL][Mongo27018] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67444bb84150dbb0e2481f4d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67403b949f37fc7feebe0ab7_ReplicationTableEditor, version=v2, tableName=ReplicationTableEditor, externalStorageTableName=ExternalStorage_SHARE_CDC_1638246081, shareCdcTaskId=6744486c7dac041ef76f9959, connectionId=67403b949f37fc7feebe0ab7) 
[INFO ] 2024-11-26 16:47:22.551 - [CDC log cache task from AutoTestMySQL][Mongo27018] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67444bb84150dbb0e2481f4b, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67403b949f37fc7feebe0ab7_ReplicationTableEditor_tableEditor, version=v2, tableName=ReplicationTableEditor_tableEditor, externalStorageTableName=ExternalStorage_SHARE_CDC_-1426218403, shareCdcTaskId=6744486c7dac041ef76f9959, connectionId=67403b949f37fc7feebe0ab7) 
[INFO ] 2024-11-26 16:47:22.849 - [CDC log cache task from AutoTestMySQL][Mongo27018] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67444bb84150dbb0e2481f4c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67403b949f37fc7feebe0ab7_ReplicationFieldEditor, version=v2, tableName=ReplicationFieldEditor, externalStorageTableName=ExternalStorage_SHARE_CDC_1733720813, shareCdcTaskId=6744486c7dac041ef76f9959, connectionId=67403b949f37fc7feebe0ab7) 
[INFO ] 2024-11-26 16:47:22.912 - [CDC log cache task from AutoTestMySQL][Mongo27018] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67444b114150dbb0e247fa2d, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67403b949f37fc7feebe0ab7_ReplFilterTypeTable, version=v2, tableName=ReplFilterTypeTable, externalStorageTableName=ExternalStorage_SHARE_CDC_-1913600357, shareCdcTaskId=6744486c7dac041ef76f9959, connectionId=67403b949f37fc7feebe0ab7) 
[INFO ] 2024-11-26 16:47:23.444 - [CDC log cache task from AutoTestMySQL][Mongo27018] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=67444b114150dbb0e247fa2e, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=67403b949f37fc7feebe0ab7_ReplStandardJSTable, version=v2, tableName=ReplStandardJSTable, externalStorageTableName=ExternalStorage_SHARE_CDC_2025112871, shareCdcTaskId=6744486c7dac041ef76f9959, connectionId=67403b949f37fc7feebe0ab7) 
[INFO ] 2024-11-26 16:47:23.659 - [CDC log cache task from AutoTestMySQL][Mongo27018] - Init log data storage finished, config: ExternalStorageDto[name='Mongo27018', type='mongodb', uri='mongodb://localhost:27018/ExternalStorage', table='null', ttlDay=3] 
[INFO ] 2024-11-26 16:47:26.848 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Source node "AutoTestMySQL" read batch size: 100 
[INFO ] 2024-11-26 16:47:26.875 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Source node "AutoTestMySQL" event queue capacity: 200 
[INFO ] 2024-11-26 16:47:26.875 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-11-26 16:47:26.876 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - batch offset found: {},stream offset found: {"name":"e79dd01e-8b83-4409-9009-c22d5becb5e7","offset":{"{\"server\":\"e79dd01e-8b83-4409-9009-c22d5becb5e7\"}":"{\"ts_sec\":1732603768,\"file\":\"mysql-bin.000217\",\"pos\":923164216,\"row\":1,\"server_id\":1121,\"event\":2}"}} 
[INFO ] 2024-11-26 16:47:26.886 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-11-26 16:47:27.053 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Starting stream read, table list: [ReplicationTableEditor_tableEditor, ReplicationFieldEditor, ReplEnhancedJSTable, ReplStandardJSTable, ReplicationTableEditor, ReplFilterTypeTable, R07FilterTypeTable, ReplicationNoProcessor, _tapdata_heartbeat_table], offset: {"name":"e79dd01e-8b83-4409-9009-c22d5becb5e7","offset":{"{\"server\":\"e79dd01e-8b83-4409-9009-c22d5becb5e7\"}":"{\"ts_sec\":1732603768,\"file\":\"mysql-bin.000217\",\"pos\":923164216,\"row\":1,\"server_id\":1121,\"event\":2}"}} 
[INFO ] 2024-11-26 16:47:27.053 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Starting mysql cdc, server name: e79dd01e-8b83-4409-9009-c22d5becb5e7 
[INFO ] 2024-11-26 16:47:27.143 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 229886586
  time.precision.mode: adaptive_time_microseconds
  database.server.name: e79dd01e-8b83-4409-9009-c22d5becb5e7
  database.port: 3306
  threadName: Debezium-Mysql-Connector-e79dd01e-8b83-4409-9009-c22d5becb5e7
  enable.time.adjuster: false
  database.hostname: *************
  database.password: ********
  name: e79dd01e-8b83-4409-9009-c22d5becb5e7
  pdk.offset.string: {"name":"e79dd01e-8b83-4409-9009-c22d5becb5e7","offset":{"{\"server\":\"e79dd01e-8b83-4409-9009-c22d5becb5e7\"}":"{\"ts_sec\":1732603768,\"file\":\"mysql-bin.000217\",\"pos\":923164216,\"row\":1,\"server_id\":1121,\"event\":2}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: autoTest.ReplicationTableEditor_tableEditor,autoTest.ReplicationFieldEditor,autoTest.ReplEnhancedJSTable,autoTest.ReplStandardJSTable,autoTest.ReplicationTableEditor,autoTest.ReplFilterTypeTable,autoTest.R07FilterTypeTable,autoTest.ReplicationNoProcessor,autoTest._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: autoTest
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-11-26 16:47:28.536 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Connector Mysql incremental start succeed, tables: [ReplicationTableEditor_tableEditor, ReplicationFieldEditor, ReplEnhancedJSTable, ReplStandardJSTable, ReplicationTableEditor, ReplFilterTypeTable, R07FilterTypeTable, ReplicationNoProcessor, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-11-26 16:59:15.618 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Node AutoTestMySQL[038d29e49c3d467b959f4c341b61898b] running status set to false 
[INFO ] 2024-11-26 16:59:15.618 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-11-26 16:59:15.618 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Mysql binlog reader stopped 
[INFO ] 2024-11-26 16:59:15.644 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode_038d29e49c3d467b959f4c341b61898b_1732610843398 
[INFO ] 2024-11-26 16:59:15.645 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - PDK connector node released: HazelcastSourcePdkShareCDCNode_038d29e49c3d467b959f4c341b61898b_1732610843398 
[INFO ] 2024-11-26 16:59:15.645 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Node AutoTestMySQL[038d29e49c3d467b959f4c341b61898b] schema data cleaned 
[INFO ] 2024-11-26 16:59:15.645 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Node AutoTestMySQL[038d29e49c3d467b959f4c341b61898b] monitor closed 
[INFO ] 2024-11-26 16:59:15.646 - [CDC log cache task from AutoTestMySQL][AutoTestMySQL] - Node AutoTestMySQL[038d29e49c3d467b959f4c341b61898b] close complete, cost 116 ms 
[INFO ] 2024-11-26 16:59:15.658 - [CDC log cache task from AutoTestMySQL][Mongo27018] - Node Mongo27018[f7d1c9914aa14677ab6056dd8ada3318] running status set to false 
[INFO ] 2024-11-26 16:59:15.665 - [CDC log cache task from AutoTestMySQL][Mongo27018] - PDK connector node stopped: null 
[INFO ] 2024-11-26 16:59:15.666 - [CDC log cache task from AutoTestMySQL][Mongo27018] - PDK connector node released: null 
[INFO ] 2024-11-26 16:59:15.666 - [CDC log cache task from AutoTestMySQL][Mongo27018] - Node Mongo27018[f7d1c9914aa14677ab6056dd8ada3318] schema data cleaned 
[INFO ] 2024-11-26 16:59:15.666 - [CDC log cache task from AutoTestMySQL][Mongo27018] - Node Mongo27018[f7d1c9914aa14677ab6056dd8ada3318] monitor closed 
[INFO ] 2024-11-26 16:59:15.666 - [CDC log cache task from AutoTestMySQL][Mongo27018] - Node Mongo27018[f7d1c9914aa14677ab6056dd8ada3318] close complete, cost 12 ms 
[INFO ] 2024-11-26 16:59:17.237 - [CDC log cache task from AutoTestMySQL] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-11-26 16:59:17.238 - [CDC log cache task from AutoTestMySQL] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5991a297 
[INFO ] 2024-11-26 16:59:17.238 - [CDC log cache task from AutoTestMySQL] - Stop task milestones: 6744486c7dac041ef76f9959(CDC log cache task from AutoTestMySQL)  
[INFO ] 2024-11-26 16:59:17.357 - [CDC log cache task from AutoTestMySQL] - Stopped task aspect(s) 
[INFO ] 2024-11-26 16:59:17.359 - [CDC log cache task from AutoTestMySQL] - Snapshot order controller have been removed 
[INFO ] 2024-11-26 16:59:17.369 - [CDC log cache task from AutoTestMySQL] - Remove memory task client succeed, task: CDC log cache task from AutoTestMySQL[6744486c7dac041ef76f9959] 
[INFO ] 2024-11-26 16:59:17.371 - [CDC log cache task from AutoTestMySQL] - Destroy memory task client cache succeed, task: CDC log cache task from AutoTestMySQL[6744486c7dac041ef76f9959] 
