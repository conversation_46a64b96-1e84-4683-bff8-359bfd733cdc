[INFO ] 2024-04-06 22:11:17.840 - [任务 4] - Start task milestones: 66114a83bf72be48c786c179(任务 4) 
[INFO ] 2024-04-06 22:11:17.840 - [任务 4] - Task initialization... 
[INFO ] 2024-04-06 22:11:17.840 - [任务 4] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-04-06 22:11:17.841 - [任务 4] - The engine receives 任务 4 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-06 22:11:17.844 - [任务 4][TestMysql] - Node TestMysql[b823aed5-c296-4d8a-898a-5f0c7c133c9b] start preload schema,table counts: 1 
[INFO ] 2024-04-06 22:11:17.844 - [任务 4][TestKafka] - Node TestKafka[2be2b553-c8f3-4e19-8433-fd232f55df32] start preload schema,table counts: 1 
[INFO ] 2024-04-06 22:11:17.883 - [任务 4][TestKafka] - Node TestKafka[2be2b553-c8f3-4e19-8433-fd232f55df32] preload schema finished, cost 35 ms 
[INFO ] 2024-04-06 22:11:17.883 - [任务 4][TestMysql] - Node TestMysql[b823aed5-c296-4d8a-898a-5f0c7c133c9b] preload schema finished, cost 38 ms 
[INFO ] 2024-04-06 22:11:18.888 - [任务 4][TestMysql] - Node(TestMysql) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[INFO ] 2024-04-06 22:11:18.889 - [任务 4][TestMysql] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-06 22:11:18.914 - [任务 4][TestMysql] - Table "test.test1" exists, skip auto create table 
[INFO ] 2024-04-06 22:11:18.915 - [任务 4][TestMysql] - The table test1 has already exist. 
[INFO ] 2024-04-06 22:11:19.055 - [任务 4][TestKafka] - Source node "TestKafka" read batch size: 100 
[INFO ] 2024-04-06 22:11:19.056 - [任务 4][TestKafka] - Source node "TestKafka" event queue capacity: 200 
[INFO ] 2024-04-06 22:11:19.057 - [任务 4][TestKafka] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-06 22:11:19.060 - [任务 4][TestKafka] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-04-06 22:11:19.162 - [任务 4][TestKafka] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-04-06 22:11:19.162 - [任务 4][TestKafka] - Initial sync started 
[INFO ] 2024-04-06 22:11:19.162 - [任务 4][TestKafka] - Starting batch read, table name: test1, offset: null 
[INFO ] 2024-04-06 22:11:19.175 - [任务 4][TestKafka] - Table test1 is going to be initial synced 
[INFO ] 2024-04-06 22:11:25.497 - [任务 4][TestKafka] - Initial sync completed 
[INFO ] 2024-04-06 22:11:25.502 - [任务 4][TestKafka] - Incremental sync starting... 
[INFO ] 2024-04-06 22:11:25.502 - [任务 4][TestKafka] - Initial sync completed 
[INFO ] 2024-04-06 22:11:25.706 - [任务 4][TestKafka] - Starting stream read, table list: [test1], offset: [] 
[INFO ] 2024-04-06 22:11:47.614 - [任务 4] - Stop task milestones: 66114a83bf72be48c786c179(任务 4)  
[INFO ] 2024-04-06 22:11:47.978 - [任务 4][TestKafka] - Node TestKafka[2be2b553-c8f3-4e19-8433-fd232f55df32] running status set to false 
[ERROR] 2024-04-06 22:11:47.980 - [任务 4][TestKafka] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.utils.RetryUtils.wrapAndThrowError(RetryUtils.java:206)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:128)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:66)
	... 14 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:326)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 18 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:690)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:324)
	... 20 more

[INFO ] 2024-04-06 22:11:47.981 - [任务 4][TestKafka] - PDK connector node stopped: HazelcastSourcePdkDataNode-2be2b553-c8f3-4e19-8433-fd232f55df32 
[INFO ] 2024-04-06 22:11:47.981 - [任务 4][TestKafka] - PDK connector node released: HazelcastSourcePdkDataNode-2be2b553-c8f3-4e19-8433-fd232f55df32 
[INFO ] 2024-04-06 22:11:47.982 - [任务 4][TestKafka] - Node TestKafka[2be2b553-c8f3-4e19-8433-fd232f55df32] schema data cleaned 
[INFO ] 2024-04-06 22:11:47.988 - [任务 4][TestKafka] - Node TestKafka[2be2b553-c8f3-4e19-8433-fd232f55df32] monitor closed 
[INFO ] 2024-04-06 22:11:47.989 - [任务 4][TestKafka] - Node TestKafka[2be2b553-c8f3-4e19-8433-fd232f55df32] close complete, cost 77 ms 
[INFO ] 2024-04-06 22:11:47.989 - [任务 4][TestMysql] - Node TestMysql[b823aed5-c296-4d8a-898a-5f0c7c133c9b] running status set to false 
[INFO ] 2024-04-06 22:11:48.027 - [任务 4][TestMysql] - PDK connector node stopped: HazelcastTargetPdkDataNode-b823aed5-c296-4d8a-898a-5f0c7c133c9b 
[INFO ] 2024-04-06 22:11:48.027 - [任务 4][TestMysql] - PDK connector node released: HazelcastTargetPdkDataNode-b823aed5-c296-4d8a-898a-5f0c7c133c9b 
[INFO ] 2024-04-06 22:11:48.031 - [任务 4][TestMysql] - Node TestMysql[b823aed5-c296-4d8a-898a-5f0c7c133c9b] schema data cleaned 
[INFO ] 2024-04-06 22:11:48.031 - [任务 4][TestMysql] - Node TestMysql[b823aed5-c296-4d8a-898a-5f0c7c133c9b] monitor closed 
[INFO ] 2024-04-06 22:11:48.232 - [任务 4][TestMysql] - Node TestMysql[b823aed5-c296-4d8a-898a-5f0c7c133c9b] close complete, cost 43 ms 
[INFO ] 2024-04-06 22:11:51.574 - [任务 4] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-06 22:11:51.578 - [任务 4] - Stopped task aspect(s) 
[INFO ] 2024-04-06 22:11:51.579 - [任务 4] - Snapshot order controller have been removed 
[INFO ] 2024-04-06 22:11:51.620 - [任务 4] - Remove memory task client succeed, task: 任务 4[66114a83bf72be48c786c179] 
[INFO ] 2024-04-06 22:11:51.620 - [任务 4] - Destroy memory task client cache succeed, task: 任务 4[66114a83bf72be48c786c179] 
