[INFO ] 2024-04-07 00:10:19.153 - [任务 6] - Task initialization... 
[INFO ] 2024-04-07 00:10:19.211 - [任务 6] - Start task milestones: 6611738e7cd6743386dcd955(任务 6) 
[INFO ] 2024-04-07 00:10:19.212 - [任务 6] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-04-07 00:10:19.297 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 00:10:19.399 - [任务 6][CLAIM] - Node CLAIM[112c857b-14bf-466c-9563-be554ace7150] start preload schema,table counts: 1 
[INFO ] 2024-04-07 00:10:19.403 - [任务 6][KafkaTest1] - Node KafkaTest1[28d6d9e4-2949-4b6e-901b-35a7cd7815bf] start preload schema,table counts: 1 
[INFO ] 2024-04-07 00:10:19.490 - [任务 6][KafkaTest1] - Node KafkaTest1[28d6d9e4-2949-4b6e-901b-35a7cd7815bf] preload schema finished, cost 82 ms 
[INFO ] 2024-04-07 00:10:19.490 - [任务 6][CLAIM] - Node CLAIM[112c857b-14bf-466c-9563-be554ace7150] preload schema finished, cost 87 ms 
[INFO ] 2024-04-07 00:10:20.378 - [任务 6][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-07 00:10:20.378 - [任务 6][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-07 00:10:20.384 - [任务 6][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 00:10:20.384 - [任务 6][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000021","position":2665,"gtidSet":""} 
[INFO ] 2024-04-07 00:10:20.495 - [任务 6][CLAIM] - Initial sync started 
[INFO ] 2024-04-07 00:10:20.499 - [任务 6][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-04-07 00:10:20.551 - [任务 6][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-04-07 00:10:20.552 - [任务 6][CLAIM] - Query table 'CLAIM' counts: 1078 
[INFO ] 2024-04-07 00:10:22.951 - [任务 6][KafkaTest1] - Write batch size: 100, max wait ms per batch: 500 
[ERROR] 2024-04-07 00:11:23.072 - [任务 6][KafkaTest1] - java.lang.RuntimeException: fetch topic list error <-- Error Message -->
java.lang.RuntimeException: fetch topic list error

<-- Simple Stack Trace -->
Caused by: org.apache.kafka.common.errors.TimeoutException: Timed out waiting for a node assignment. Call: listTopics

<-- Full Stack Trace -->
java.lang.RuntimeException: fetch topic list error
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:125)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:109)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:103)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:80)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$createTable$6(HazelcastTargetPdkBaseNode.java:250)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.createTable(HazelcastTargetPdkBaseNode.java:245)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:200)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:146)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:134)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:100)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: fetch topic list error
	at io.tapdata.connector.kafka.admin.DefaultAdmin.listTopics(DefaultAdmin.java:38)
	at io.tapdata.connector.kafka.KafkaConnector.createTableV2(KafkaConnector.java:174)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$null$5(HazelcastTargetPdkBaseNode.java:252)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 29 more
Caused by: java.util.concurrent.ExecutionException: org.apache.kafka.common.errors.TimeoutException: Timed out waiting for a node assignment. Call: listTopics
	at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
	at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1908)
	at org.apache.kafka.common.internals.KafkaFutureImpl.get(KafkaFutureImpl.java:165)
	at io.tapdata.connector.kafka.admin.DefaultAdmin.listTopics(DefaultAdmin.java:36)
	... 32 more
Caused by: org.apache.kafka.common.errors.TimeoutException: Timed out waiting for a node assignment. Call: listTopics

[INFO ] 2024-04-07 00:11:23.073 - [任务 6][KafkaTest1] - Job suspend in error handle 
[INFO ] 2024-04-07 00:11:23.115 - [任务 6][CLAIM] - Node CLAIM[112c857b-14bf-466c-9563-be554ace7150] running status set to false 
[INFO ] 2024-04-07 00:11:23.118 - [任务 6][CLAIM] - Initial sync completed 
[INFO ] 2024-04-07 00:11:23.118 - [任务 6][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-07 00:11:23.118 - [任务 6][CLAIM] - Incremental sync completed 
[INFO ] 2024-04-07 00:11:23.130 - [任务 6][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-112c857b-14bf-466c-9563-be554ace7150 
[INFO ] 2024-04-07 00:11:23.130 - [任务 6][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-112c857b-14bf-466c-9563-be554ace7150 
[INFO ] 2024-04-07 00:11:23.132 - [任务 6][CLAIM] - Node CLAIM[112c857b-14bf-466c-9563-be554ace7150] schema data cleaned 
[INFO ] 2024-04-07 00:11:23.132 - [任务 6][CLAIM] - Node CLAIM[112c857b-14bf-466c-9563-be554ace7150] monitor closed 
[INFO ] 2024-04-07 00:11:23.137 - [任务 6][CLAIM] - Node CLAIM[112c857b-14bf-466c-9563-be554ace7150] close complete, cost 57 ms 
[INFO ] 2024-04-07 00:11:23.144 - [任务 6][KafkaTest1] - Node KafkaTest1[28d6d9e4-2949-4b6e-901b-35a7cd7815bf] running status set to false 
[INFO ] 2024-04-07 00:11:23.172 - [任务 6][KafkaTest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-28d6d9e4-2949-4b6e-901b-35a7cd7815bf 
[INFO ] 2024-04-07 00:11:23.172 - [任务 6][KafkaTest1] - PDK connector node released: HazelcastTargetPdkDataNode-28d6d9e4-2949-4b6e-901b-35a7cd7815bf 
[INFO ] 2024-04-07 00:11:23.172 - [任务 6][KafkaTest1] - Node KafkaTest1[28d6d9e4-2949-4b6e-901b-35a7cd7815bf] schema data cleaned 
[INFO ] 2024-04-07 00:11:23.172 - [任务 6][KafkaTest1] - Node KafkaTest1[28d6d9e4-2949-4b6e-901b-35a7cd7815bf] monitor closed 
[INFO ] 2024-04-07 00:11:23.381 - [任务 6][KafkaTest1] - Node KafkaTest1[28d6d9e4-2949-4b6e-901b-35a7cd7815bf] close complete, cost 36 ms 
[INFO ] 2024-04-07 00:11:23.651 - [任务 6] - Task [任务 6] cannot retry, reason: Task retry service not start 
[INFO ] 2024-04-07 00:11:23.652 - [任务 6] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 00:11:23.671 - [任务 6] - Stop task milestones: 6611738e7cd6743386dcd955(任务 6)  
[INFO ] 2024-04-07 00:11:23.671 - [任务 6] - Stopped task aspect(s) 
[INFO ] 2024-04-07 00:11:23.671 - [任务 6] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 00:11:23.695 - [任务 6] - Remove memory task client succeed, task: 任务 6[6611738e7cd6743386dcd955] 
[INFO ] 2024-04-07 00:11:23.696 - [任务 6] - Destroy memory task client cache succeed, task: 任务 6[6611738e7cd6743386dcd955] 
[INFO ] 2024-04-07 00:12:10.542 - [任务 6] - Task initialization... 
[INFO ] 2024-04-07 00:12:10.549 - [任务 6] - Start task milestones: 6611738e7cd6743386dcd955(任务 6) 
[INFO ] 2024-04-07 00:12:10.617 - [任务 6] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 00:12:10.618 - [任务 6] - The engine receives 任务 6 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 00:12:10.687 - [任务 6][CLAIM] - Node CLAIM[112c857b-14bf-466c-9563-be554ace7150] start preload schema,table counts: 1 
[INFO ] 2024-04-07 00:12:10.690 - [任务 6][KafkaTest1] - Node KafkaTest1[28d6d9e4-2949-4b6e-901b-35a7cd7815bf] start preload schema,table counts: 1 
[INFO ] 2024-04-07 00:12:10.717 - [任务 6][CLAIM] - Node CLAIM[112c857b-14bf-466c-9563-be554ace7150] preload schema finished, cost 30 ms 
[INFO ] 2024-04-07 00:12:10.718 - [任务 6][KafkaTest1] - Node KafkaTest1[28d6d9e4-2949-4b6e-901b-35a7cd7815bf] preload schema finished, cost 30 ms 
[INFO ] 2024-04-07 00:12:11.660 - [任务 6][KafkaTest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 00:12:11.698 - [任务 6][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-04-07 00:12:11.698 - [任务 6][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-04-07 00:12:11.700 - [任务 6][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 00:12:11.707 - [任务 6][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000021","position":2665,"gtidSet":""} 
[INFO ] 2024-04-07 00:12:11.768 - [任务 6][CLAIM] - Initial sync started 
[INFO ] 2024-04-07 00:12:11.770 - [任务 6][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-04-07 00:12:11.776 - [任务 6][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-04-07 00:12:11.988 - [任务 6][CLAIM] - Query table 'CLAIM' counts: 1078 
[INFO ] 2024-04-07 00:12:12.471 - [任务 6][CLAIM] - Initial sync completed 
[INFO ] 2024-04-07 00:12:12.474 - [任务 6][CLAIM] - Incremental sync starting... 
[INFO ] 2024-04-07 00:12:12.486 - [任务 6][CLAIM] - Initial sync completed 
[INFO ] 2024-04-07 00:12:12.500 - [任务 6][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000021","position":2665,"gtidSet":""} 
[INFO ] 2024-04-07 00:12:12.611 - [任务 6][CLAIM] - Starting mysql cdc, server name: 20bd72c7-320a-46c1-a32a-7f453150dfc1 
[INFO ] 2024-04-07 00:12:12.612 - [任务 6][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 430400979
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 20bd72c7-320a-46c1-a32a-7f453150dfc1
  database.port: 3306
  threadName: Debezium-Mysql-Connector-20bd72c7-320a-46c1-a32a-7f453150dfc1
  database.hostname: localhost
  database.password: ********
  name: 20bd72c7-320a-46c1-a32a-7f453150dfc1
  pdk.offset.string: {"name":"20bd72c7-320a-46c1-a32a-7f453150dfc1","offset":{"{\"server\":\"20bd72c7-320a-46c1-a32a-7f453150dfc1\"}":"{\"file\":\"binlog.000021\",\"pos\":2665,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-04-07 00:12:13.018 - [任务 6][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-04-07 00:13:44.968 - [任务 6][CLAIM] - Read DDL: alter table CLAIM add column name varchar(50), about to be packaged as some event(s) 
[INFO ] 2024-04-07 00:13:44.970 - [任务 6][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='20bd72c7-320a-46c1-a32a-7f453150dfc1', offset={{"server":"20bd72c7-320a-46c1-a32a-7f453150dfc1"}={"ts_sec":1712420024,"file":"binlog.000021","pos":2922,"server_id":1}}} 
[INFO ] 2024-04-07 00:13:45.005 - [任务 6][CLAIM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@613f2050: {"newFields":[{"autoInc":false,"dataType":"varchar(50)","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false}],"referenceTime":1712420024808,"tableId":"CLAIM","time":1712420024934,"type":209} 
[INFO ] 2024-04-07 00:13:45.211 - [任务 6][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_661147b205642634b1daa0d2_6611738e7cd6743386dcd955 
[INFO ] 2024-04-07 00:13:48.483 - [任务 6][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-04-07 00:16:03.614 - [任务 6] - Stop task milestones: 6611738e7cd6743386dcd955(任务 6)  
[INFO ] 2024-04-07 00:16:03.660 - [任务 6][CLAIM] - Node CLAIM[112c857b-14bf-466c-9563-be554ace7150] running status set to false 
[INFO ] 2024-04-07 00:16:03.664 - [任务 6][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-04-07 00:16:03.665 - [任务 6][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-04-07 00:16:03.674 - [任务 6][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-112c857b-14bf-466c-9563-be554ace7150 
[INFO ] 2024-04-07 00:16:03.675 - [任务 6][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-112c857b-14bf-466c-9563-be554ace7150 
[INFO ] 2024-04-07 00:16:03.675 - [任务 6][CLAIM] - Node CLAIM[112c857b-14bf-466c-9563-be554ace7150] schema data cleaned 
[INFO ] 2024-04-07 00:16:03.676 - [任务 6][CLAIM] - Node CLAIM[112c857b-14bf-466c-9563-be554ace7150] monitor closed 
[INFO ] 2024-04-07 00:16:03.676 - [任务 6][CLAIM] - Node CLAIM[112c857b-14bf-466c-9563-be554ace7150] close complete, cost 53 ms 
[INFO ] 2024-04-07 00:16:03.704 - [任务 6][KafkaTest1] - Node KafkaTest1[28d6d9e4-2949-4b6e-901b-35a7cd7815bf] running status set to false 
[INFO ] 2024-04-07 00:16:03.704 - [任务 6][KafkaTest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-28d6d9e4-2949-4b6e-901b-35a7cd7815bf 
[INFO ] 2024-04-07 00:16:03.704 - [任务 6][KafkaTest1] - PDK connector node released: HazelcastTargetPdkDataNode-28d6d9e4-2949-4b6e-901b-35a7cd7815bf 
[INFO ] 2024-04-07 00:16:03.704 - [任务 6][KafkaTest1] - Node KafkaTest1[28d6d9e4-2949-4b6e-901b-35a7cd7815bf] schema data cleaned 
[INFO ] 2024-04-07 00:16:03.705 - [任务 6][KafkaTest1] - Node KafkaTest1[28d6d9e4-2949-4b6e-901b-35a7cd7815bf] monitor closed 
[INFO ] 2024-04-07 00:16:03.920 - [任务 6][KafkaTest1] - Node KafkaTest1[28d6d9e4-2949-4b6e-901b-35a7cd7815bf] close complete, cost 28 ms 
[INFO ] 2024-04-07 00:16:03.934 - [任务 6] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 00:16:03.934 - [任务 6] - Stopped task aspect(s) 
[INFO ] 2024-04-07 00:16:03.978 - [任务 6] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 00:16:03.978 - [任务 6] - Remove memory task client succeed, task: 任务 6[6611738e7cd6743386dcd955] 
[INFO ] 2024-04-07 00:16:03.978 - [任务 6] - Destroy memory task client cache succeed, task: 任务 6[6611738e7cd6743386dcd955] 
