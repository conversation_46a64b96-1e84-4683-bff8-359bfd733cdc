[INFO ] 2024-03-29 17:10:34.386 - [employees_import_import_import_import_import_import_import_import_import_import] - Task initialization... 
[INFO ] 2024-03-29 17:10:34.390 - [employees_import_import_import_import_import_import_import_import_import_import] - Start task milestones: 6606856604c06403f62f8730(employees_import_import_import_import_import_import_import_import_import_import) 
[INFO ] 2024-03-29 17:10:34.554 - [employees_import_import_import_import_import_import_import_import_import_import] - Node performs snapshot read by order list: [ null ] -> [ null,null ] -> [ null ] -> [ null ] 
[INFO ] 2024-03-29 17:10:35.264 - [employees_import_import_import_import_import_import_import_import_import_import] - The engine receives employees_import_import_import_import_import_import_import_import_import_import task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 17:10:35.509 - [employees_import_import_import_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[ea988ddd-f009-4d48-b437-aadce91fbb76] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:10:35.510 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[d4136557-ce79-44ff-8184-42fe04873ee0] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:10:35.541 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[0a5924ea-0136-4997-9423-a5d6e343e277] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:10:35.544 - [employees_import_import_import_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[dd1a142b-2341-4d15-aa79-512e52858b10] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:10:35.544 - [employees_import_import_import_import_import_import_import_import_import_import][Territories] - Node Territories[e991930f-4f50-422e-a179-c2a933b6d8aa] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:10:35.545 - [employees_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[c615f294-00f2-4834-abf9-e32394a1bacc] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:10:35.545 - [employees_import_import_import_import_import_import_import_import_import_import][Region] - Node Region[2cbb173e-c9be-4472-9205-3d7265b7391d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:10:35.556 - [employees_import_import_import_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[6dacd9e7-83f5-4d89-8b57-cfb0f6c0dcbb] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:10:35.556 - [employees_import_import_import_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[2eedca06-b379-4c0e-8ef1-51d6a5343c3b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:10:35.573 - [employees_import_import_import_import_import_import_import_import_import_import][merge] - Node merge[79d65452-36d5-4b9b-8c63-44b2f21b243b] start preload schema,table counts: 6 
[INFO ] 2024-03-29 17:10:35.573 - [employees_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[1365dfdf-6ce1-473d-9548-4d538b4632a2] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:10:35.573 - [employees_import_import_import_import_import_import_import_import_import_import][employees] - Node employees[918af5f8-7f0e-4445-b110-67c5ca91727b] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:10:35.587 - [employees_import_import_import_import_import_import_import_import_import_import][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[178cdc1c-2bc2-4dbc-a8b3-edcfc73a4202] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:10:35.596 - [employees_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[fb5d2aee-c8cb-4676-8ce5-df05406e39e3] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:10:35.597 - [employees_import_import_import_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[9be8fd9a-ee83-4da0-92ec-f4c8fe87c715] start preload schema,table counts: 1 
[INFO ] 2024-03-29 17:10:35.821 - [employees_import_import_import_import_import_import_import_import_import_import][Rename Territories] - Node Rename Territories[dd1a142b-2341-4d15-aa79-512e52858b10] preload schema finished, cost 245 ms 
[INFO ] 2024-03-29 17:10:35.826 - [employees_import_import_import_import_import_import_import_import_import_import][Delete Territories] - Node Delete Territories[9be8fd9a-ee83-4da0-92ec-f4c8fe87c715] preload schema finished, cost 191 ms 
[INFO ] 2024-03-29 17:10:35.826 - [employees_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[fb5d2aee-c8cb-4676-8ce5-df05406e39e3] preload schema finished, cost 232 ms 
[INFO ] 2024-03-29 17:10:35.828 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[d4136557-ce79-44ff-8184-42fe04873ee0] preload schema finished, cost 287 ms 
[INFO ] 2024-03-29 17:10:35.828 - [employees_import_import_import_import_import_import_import_import_import_import][Region] - Node Region[2cbb173e-c9be-4472-9205-3d7265b7391d] preload schema finished, cost 240 ms 
[INFO ] 2024-03-29 17:10:35.829 - [employees_import_import_import_import_import_import_import_import_import_import][employees] - Node employees[918af5f8-7f0e-4445-b110-67c5ca91727b] preload schema finished, cost 243 ms 
[INFO ] 2024-03-29 17:10:35.829 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Node Employees[0a5924ea-0136-4997-9423-a5d6e343e277] preload schema finished, cost 283 ms 
[INFO ] 2024-03-29 17:10:35.830 - [employees_import_import_import_import_import_import_import_import_import_import][Rename Employees] - Node Rename Employees[c615f294-00f2-4834-abf9-e32394a1bacc] preload schema finished, cost 252 ms 
[INFO ] 2024-03-29 17:10:35.830 - [employees_import_import_import_import_import_import_import_import_import_import][Rename Region] - Node Rename Region[ea988ddd-f009-4d48-b437-aadce91fbb76] preload schema finished, cost 285 ms 
[INFO ] 2024-03-29 17:10:35.830 - [employees_import_import_import_import_import_import_import_import_import_import][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[178cdc1c-2bc2-4dbc-a8b3-edcfc73a4202] preload schema finished, cost 204 ms 
[INFO ] 2024-03-29 17:10:35.831 - [employees_import_import_import_import_import_import_import_import_import_import][Delete Region] - Node Delete Region[2eedca06-b379-4c0e-8ef1-51d6a5343c3b] preload schema finished, cost 229 ms 
[INFO ] 2024-03-29 17:10:35.831 - [employees_import_import_import_import_import_import_import_import_import_import][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[6dacd9e7-83f5-4d89-8b57-cfb0f6c0dcbb] preload schema finished, cost 231 ms 
[INFO ] 2024-03-29 17:10:35.832 - [employees_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Node EmployeeTerritories[1365dfdf-6ce1-473d-9548-4d538b4632a2] preload schema finished, cost 212 ms 
[INFO ] 2024-03-29 17:10:35.832 - [employees_import_import_import_import_import_import_import_import_import_import][Territories] - Node Territories[e991930f-4f50-422e-a179-c2a933b6d8aa] preload schema finished, cost 244 ms 
[INFO ] 2024-03-29 17:10:36.141 - [employees_import_import_import_import_import_import_import_import_import_import][merge] - Node merge[79d65452-36d5-4b9b-8c63-44b2f21b243b] preload schema finished, cost 564 ms 
[INFO ] 2024-03-29 17:10:36.147 - [employees_import_import_import_import_import_import_import_import_import_import][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 17:10:36.152 - [employees_import_import_import_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  Territories(9be8fd9a-ee83-4da0-92ec-f4c8fe87c715)
    ->Region(2eedca06-b379-4c0e-8ef1-51d6a5343c3b)
} 
[INFO ] 2024-03-29 17:10:36.154 - [employees_import_import_import_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  EmployeeTerritories(6dacd9e7-83f5-4d89-8b57-cfb0f6c0dcbb)
    ->Territories(9be8fd9a-ee83-4da0-92ec-f4c8fe87c715)
} 
[INFO ] 2024-03-29 17:10:36.156 - [employees_import_import_import_import_import_import_import_import_import_import][merge] - 
Merge lookup relation{
  Employees(fb5d2aee-c8cb-4676-8ce5-df05406e39e3)
    ->Employees(c615f294-00f2-4834-abf9-e32394a1bacc)
    ->EmployeeTerritories(6dacd9e7-83f5-4d89-8b57-cfb0f6c0dcbb)
} 
[INFO ] 2024-03-29 17:10:37.203 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" read batch size: 500 
[INFO ] 2024-03-29 17:10:37.206 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" event queue capacity: 1000 
[INFO ] 2024-03-29 17:10:37.206 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 17:10:37.223 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:10:37.330 - [employees_import_import_import_import_import_import_import_import_import_import][Territories] - Source node "Territories" read batch size: 500 
[INFO ] 2024-03-29 17:10:37.331 - [employees_import_import_import_import_import_import_import_import_import_import][Territories] - Source node "Territories" event queue capacity: 1000 
[INFO ] 2024-03-29 17:10:37.333 - [employees_import_import_import_import_import_import_import_import_import_import][Territories] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 17:10:37.458 - [employees_import_import_import_import_import_import_import_import_import_import][Territories] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:10:37.463 - [employees_import_import_import_import_import_import_import_import_import_import] - Node[Territories] is waiting for running 
[INFO ] 2024-03-29 17:10:37.463 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Initial sync started 
[INFO ] 2024-03-29 17:10:37.463 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Starting batch read, table name: Employees, offset: null 
[INFO ] 2024-03-29 17:10:37.546 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Table Employees is going to be initial synced 
[INFO ] 2024-03-29 17:10:37.546 - [employees_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Source node "EmployeeTerritories" read batch size: 500 
[INFO ] 2024-03-29 17:10:37.546 - [employees_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Source node "EmployeeTerritories" event queue capacity: 1000 
[INFO ] 2024-03-29 17:10:37.546 - [employees_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 17:10:37.555 - [employees_import_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Territories_9be8fd9a-ee83-4da0-92ec-f4c8fe87c715__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 17:10:37.556 - [employees_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:10:37.564 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Query table 'Employees' counts: 0 
[INFO ] 2024-03-29 17:10:37.574 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 17:10:37.695 - [employees_import_import_import_import_import_import_import_import_import_import] - Node[EmployeeTerritories] is waiting for running 
[INFO ] 2024-03-29 17:10:37.695 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" read batch size: 500 
[INFO ] 2024-03-29 17:10:37.696 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Source node "Employees" event queue capacity: 1000 
[INFO ] 2024-03-29 17:10:37.696 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 17:10:37.799 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:10:37.799 - [employees_import_import_import_import_import_import_import_import_import_import] - Node[Employees] is waiting for running 
[INFO ] 2024-03-29 17:10:38.004 - [employees_import_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Region_2eedca06-b379-4c0e-8ef1-51d6a5343c3b__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 17:10:38.132 - [employees_import_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_Employees_c615f294-00f2-4834-abf9-e32394a1bacc__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 17:10:38.284 - [employees_import_import_import_import_import_import_import_import_import_import][employees] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 17:10:38.285 - [employees_import_import_import_import_import_import_import_import_import_import][Region] - Source node "Region" read batch size: 500 
[INFO ] 2024-03-29 17:10:38.285 - [employees_import_import_import_import_import_import_import_import_import_import][Region] - Source node "Region" event queue capacity: 1000 
[INFO ] 2024-03-29 17:10:38.286 - [employees_import_import_import_import_import_import_import_import_import_import][Region] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 17:10:38.295 - [employees_import_import_import_import_import_import_import_import_import_import][merge] - Create merge cache imap name: HazelcastMergeNode_EmployeeTerritories_6dacd9e7-83f5-4d89-8b57-cfb0f6c0dcbb__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 17:10:38.295 - [employees_import_import_import_import_import_import_import_import_import_import][Region] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:10:38.375 - [employees_import_import_import_import_import_import_import_import_import_import][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 17:10:38.375 - [employees_import_import_import_import_import_import_import_import_import_import] - Node[Region] is waiting for running 
[INFO ] 2024-03-29 17:10:38.579 - [employees_import_import_import_import_import_import_import_import_import_import][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 17:10:39.236 - [employees_import_import_import_import_import_import_import_import_import_import] - Node[Employees] finish, notify next layer to run 
[INFO ] 2024-03-29 17:10:39.236 - [employees_import_import_import_import_import_import_import_import_import_import] - Next layer have been notified: [null,null] 
[INFO ] 2024-03-29 17:10:39.239 - [employees_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Initial sync started 
[INFO ] 2024-03-29 17:10:39.242 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Initial sync started 
[INFO ] 2024-03-29 17:10:39.257 - [employees_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Starting batch read, table name: EmployeeTerritories, offset: null 
[INFO ] 2024-03-29 17:10:39.272 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Starting batch read, table name: Employees, offset: null 
[INFO ] 2024-03-29 17:10:39.274 - [employees_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Table EmployeeTerritories is going to be initial synced 
[INFO ] 2024-03-29 17:10:39.277 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Table Employees is going to be initial synced 
[INFO ] 2024-03-29 17:10:39.304 - [employees_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Query table 'EmployeeTerritories' counts: 1 
[INFO ] 2024-03-29 17:10:39.305 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Query table 'Employees' counts: 0 
[INFO ] 2024-03-29 17:10:39.305 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 17:10:39.515 - [employees_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Initial sync completed 
[INFO ] 2024-03-29 17:10:40.615 - [employees_import_import_import_import_import_import_import_import_import_import] - Node[EmployeeTerritories] finish, notify next layer to run 
[INFO ] 2024-03-29 17:10:40.639 - [employees_import_import_import_import_import_import_import_import_import_import] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 17:10:40.641 - [employees_import_import_import_import_import_import_import_import_import_import][Territories] - Initial sync started 
[INFO ] 2024-03-29 17:10:40.642 - [employees_import_import_import_import_import_import_import_import_import_import][Territories] - Starting batch read, table name: Territories, offset: null 
[INFO ] 2024-03-29 17:10:40.642 - [employees_import_import_import_import_import_import_import_import_import_import][Territories] - Table Territories is going to be initial synced 
[INFO ] 2024-03-29 17:10:40.676 - [employees_import_import_import_import_import_import_import_import_import_import][Territories] - Query table 'Territories' counts: 1 
[INFO ] 2024-03-29 17:10:40.677 - [employees_import_import_import_import_import_import_import_import_import_import][Territories] - Initial sync completed 
[INFO ] 2024-03-29 17:10:41.702 - [employees_import_import_import_import_import_import_import_import_import_import] - Node[Territories] finish, notify next layer to run 
[INFO ] 2024-03-29 17:10:41.705 - [employees_import_import_import_import_import_import_import_import_import_import] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 17:10:41.709 - [employees_import_import_import_import_import_import_import_import_import_import][Region] - Initial sync started 
[INFO ] 2024-03-29 17:10:41.720 - [employees_import_import_import_import_import_import_import_import_import_import][Region] - Starting batch read, table name: Region, offset: null 
[INFO ] 2024-03-29 17:10:41.721 - [employees_import_import_import_import_import_import_import_import_import_import][Region] - Table Region is going to be initial synced 
[INFO ] 2024-03-29 17:10:41.741 - [employees_import_import_import_import_import_import_import_import_import_import][Region] - Query table 'Region' counts: 1 
[INFO ] 2024-03-29 17:10:41.741 - [employees_import_import_import_import_import_import_import_import_import_import][Region] - Initial sync completed 
[INFO ] 2024-03-29 17:10:43.367 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Incremental sync starting... 
[INFO ] 2024-03-29 17:10:43.371 - [employees_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Incremental sync starting... 
[INFO ] 2024-03-29 17:10:43.378 - [employees_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Initial sync completed 
[INFO ] 2024-03-29 17:10:43.406 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 17:10:43.408 - [employees_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Starting stream read, table list: [EmployeeTerritories], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:10:43.408 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Starting stream read, table list: [Employees], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:10:43.449 - [employees_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Starting mysql cdc, server name: 2cd26f27-4cf9-48be-8e3c-1e6dd055ce8e 
[INFO ] 2024-03-29 17:10:43.449 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Starting mysql cdc, server name: 9282328a-23ed-4147-9f31-6a1e0bde8570 
[INFO ] 2024-03-29 17:10:43.500 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1823838650
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 9282328a-23ed-4147-9f31-6a1e0bde8570
  database.port: 3307
  threadName: Debezium-Mysql-Connector-9282328a-23ed-4147-9f31-6a1e0bde8570
  database.hostname: 127.0.0.1
  database.password: ********
  name: 9282328a-23ed-4147-9f31-6a1e0bde8570
  pdk.offset.string: {"name":"9282328a-23ed-4147-9f31-6a1e0bde8570","offset":{"{\"server\":\"9282328a-23ed-4147-9f31-6a1e0bde8570\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Employees
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 17:10:43.500 - [employees_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1504815574
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 2cd26f27-4cf9-48be-8e3c-1e6dd055ce8e
  database.port: 3307
  threadName: Debezium-Mysql-Connector-2cd26f27-4cf9-48be-8e3c-1e6dd055ce8e
  database.hostname: 127.0.0.1
  database.password: ********
  name: 2cd26f27-4cf9-48be-8e3c-1e6dd055ce8e
  pdk.offset.string: {"name":"2cd26f27-4cf9-48be-8e3c-1e6dd055ce8e","offset":{"{\"server\":\"2cd26f27-4cf9-48be-8e3c-1e6dd055ce8e\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.EmployeeTerritories
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 17:10:43.584 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Incremental sync starting... 
[INFO ] 2024-03-29 17:10:43.585 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Initial sync completed 
[INFO ] 2024-03-29 17:10:43.624 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Starting stream read, table list: [Employees], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:10:43.625 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Starting mysql cdc, server name: 6309d443-0289-4b50-942f-4c1d149e46fd 
[INFO ] 2024-03-29 17:10:43.691 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 274624671
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 6309d443-0289-4b50-942f-4c1d149e46fd
  database.port: 3307
  threadName: Debezium-Mysql-Connector-6309d443-0289-4b50-942f-4c1d149e46fd
  database.hostname: 127.0.0.1
  database.password: ********
  name: 6309d443-0289-4b50-942f-4c1d149e46fd
  pdk.offset.string: {"name":"6309d443-0289-4b50-942f-4c1d149e46fd","offset":{"{\"server\":\"6309d443-0289-4b50-942f-4c1d149e46fd\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Employees
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 17:10:43.691 - [employees_import_import_import_import_import_import_import_import_import_import][Territories] - Incremental sync starting... 
[INFO ] 2024-03-29 17:10:43.691 - [employees_import_import_import_import_import_import_import_import_import_import][Territories] - Initial sync completed 
[INFO ] 2024-03-29 17:10:43.692 - [employees_import_import_import_import_import_import_import_import_import_import][Territories] - Starting stream read, table list: [Territories], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:10:43.745 - [employees_import_import_import_import_import_import_import_import_import_import][Territories] - Starting mysql cdc, server name: 20f156a6-7e53-4172-9b62-5e41acf288b5 
[INFO ] 2024-03-29 17:10:43.748 - [employees_import_import_import_import_import_import_import_import_import_import][Region] - Incremental sync starting... 
[INFO ] 2024-03-29 17:10:43.753 - [employees_import_import_import_import_import_import_import_import_import_import][Territories] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1643507005
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 20f156a6-7e53-4172-9b62-5e41acf288b5
  database.port: 3307
  threadName: Debezium-Mysql-Connector-20f156a6-7e53-4172-9b62-5e41acf288b5
  database.hostname: 127.0.0.1
  database.password: ********
  name: 20f156a6-7e53-4172-9b62-5e41acf288b5
  pdk.offset.string: {"name":"20f156a6-7e53-4172-9b62-5e41acf288b5","offset":{"{\"server\":\"20f156a6-7e53-4172-9b62-5e41acf288b5\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Territories
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 17:10:43.761 - [employees_import_import_import_import_import_import_import_import_import_import][Region] - Initial sync completed 
[INFO ] 2024-03-29 17:10:43.763 - [employees_import_import_import_import_import_import_import_import_import_import][Region] - Starting stream read, table list: [Region], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 17:10:43.810 - [employees_import_import_import_import_import_import_import_import_import_import][Region] - Starting mysql cdc, server name: d03e6a86-17cf-4d98-94da-d30f2fe11221 
[INFO ] 2024-03-29 17:10:43.810 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Connector Mysql incremental start succeed, tables: [Employees], data change syncing 
[INFO ] 2024-03-29 17:10:43.810 - [employees_import_import_import_import_import_import_import_import_import_import][EmployeeTerritories] - Connector Mysql incremental start succeed, tables: [EmployeeTerritories], data change syncing 
[INFO ] 2024-03-29 17:10:43.810 - [employees_import_import_import_import_import_import_import_import_import_import][Employees] - Connector Mysql incremental start succeed, tables: [Employees], data change syncing 
[INFO ] 2024-03-29 17:10:43.822 - [employees_import_import_import_import_import_import_import_import_import_import][Region] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2103789173
  time.precision.mode: adaptive_time_microseconds
  database.server.name: d03e6a86-17cf-4d98-94da-d30f2fe11221
  database.port: 3307
  threadName: Debezium-Mysql-Connector-d03e6a86-17cf-4d98-94da-d30f2fe11221
  database.hostname: 127.0.0.1
  database.password: ********
  name: d03e6a86-17cf-4d98-94da-d30f2fe11221
  pdk.offset.string: {"name":"d03e6a86-17cf-4d98-94da-d30f2fe11221","offset":{"{\"server\":\"d03e6a86-17cf-4d98-94da-d30f2fe11221\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Region
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 17:10:43.950 - [employees_import_import_import_import_import_import_import_import_import_import][Territories] - Connector Mysql incremental start succeed, tables: [Territories], data change syncing 
[INFO ] 2024-03-29 17:10:43.951 - [employees_import_import_import_import_import_import_import_import_import_import][Region] - Connector Mysql incremental start succeed, tables: [Region], data change syncing 
