[TRACE] 2025-02-18 16:50:25.721 - [任务 9] - Task initialization... 
[TRACE] 2025-02-18 16:50:25.722 - [任务 9] - Start task milestones: 67b4472ccc3e3c6b7743d6ac(任务 9) 
[INFO ] 2025-02-18 16:50:25.852 - [任务 9] - Loading table structure completed 
[TRACE] 2025-02-18 16:50:25.852 - [任务 9] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-02-18 16:50:25.903 - [任务 9] - The engine receives 任务 9 task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-18 16:50:25.903 - [任务 9] - Task started 
[TRACE] 2025-02-18 16:50:25.928 - [任务 9][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] start preload schema,table counts: 1 
[TRACE] 2025-02-18 16:50:25.928 - [任务 9][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] start preload schema,table counts: 1 
[TRACE] 2025-02-18 16:50:25.931 - [任务 9][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] preload schema finished, cost 0 ms 
[TRACE] 2025-02-18 16:50:25.932 - [任务 9][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] preload schema finished, cost 0 ms 
[INFO ] 2025-02-18 16:50:26.553 - [任务 9][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-02-18 16:50:26.557 - [任务 9][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-02-18 16:50:26.560 - [任务 9][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-02-18 16:50:26.561 - [任务 9][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-18 16:50:26.561 - [任务 9][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-02-18 16:50:26.658 - [任务 9][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-02-18 16:50:26.658 - [任务 9][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[TRACE] 2025-02-18 16:50:26.658 - [任务 9][Sybase] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-18 16:50:26.757 - [任务 9][Sybase] - Starting batch read from 1 tables 
[TRACE] 2025-02-18 16:50:26.757 - [任务 9][Sybase] - Initial sync started 
[INFO ] 2025-02-18 16:50:26.758 - [任务 9][Sybase] - Starting batch read from table: td_empty_string 
[TRACE] 2025-02-18 16:50:26.758 - [任务 9][Sybase] - Table td_empty_string is going to be initial synced 
[TRACE] 2025-02-18 16:50:26.840 - [任务 9][Sybase] - Query snapshot row size completed: Sybase(21df3819-d4c2-4e2d-94bf-05732f3cc289) 
[INFO ] 2025-02-18 16:50:26.844 - [任务 9][Sybase] - Table td_empty_string has been completed batch read 
[TRACE] 2025-02-18 16:50:26.844 - [任务 9][Sybase] - Initial sync completed 
[INFO ] 2025-02-18 16:50:26.844 - [任务 9][Sybase] - Batch read completed. 
[TRACE] 2025-02-18 16:50:26.844 - [任务 9][Sybase] - Incremental sync starting... 
[TRACE] 2025-02-18 16:50:26.845 - [任务 9][Sybase] - Initial sync completed 
[TRACE] 2025-02-18 16:50:26.845 - [任务 9][Sybase] - Starting stream read, table list: [td_empty_string], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-02-18 16:50:26.845 - [任务 9][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-02-18 16:50:26.860 - [任务 9][PG] - Sink connector(PG) initialization completed 
[TRACE] 2025-02-18 16:50:26.860 - [任务 9][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-18 16:50:26.860 - [任务 9][PG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-18 16:50:27.011 - [任务 9][PG] - Apply table structure to target database 
[INFO ] 2025-02-18 16:50:27.011 - [任务 9][Sybase] - startRid: 368331, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 16:50:27.011 - [任务 9][Sybase] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 16:50:27.011 - [任务 9][Sybase] - sybase offset in database is: startRid: 368331, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-02-18 16:50:27.012 - [任务 9][Sybase] - we will use offset in database, how ever, this is safe: startRid: 368331, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 16:50:27.027 - [任务 9][Sybase] - sybase cdc work with mode v1: auto rescan 
[INFO ] 2025-02-18 16:50:27.215 - [任务 9][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-02-18 16:50:27.215 - [任务 9][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-02-18 16:50:27.231 - [任务 9][Sybase] - opened cdc for tables: {dbo=[td_empty_string]} 
[INFO ] 2025-02-18 16:50:27.231 - [任务 9][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-02-18 16:50:27.436 - [任务 9][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-02-18 16:50:27.511 - [任务 9][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-02-18 16:50:27.511 - [任务 9][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-02-18 16:50:27.512 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:50:30.518 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:50:30.641 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:50:33.647 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:50:33.853 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:50:36.795 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:50:37.000 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:50:39.971 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:50:40.177 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:50:43.163 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:50:43.370 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:50:46.336 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:50:46.543 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:50:49.510 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:50:49.715 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:50:52.685 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:50:52.685 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:50:55.694 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:50:55.873 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:50:58.981 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:50:59.387 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:51:02.308 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:51:02.513 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:51:05.329 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:51:05.538 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:51:08.544 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:51:08.750 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:51:11.709 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:51:11.915 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:51:14.779 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:51:14.981 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:51:17.955 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:51:18.159 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:51:21.104 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:51:21.307 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:51:24.217 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:51:24.325 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:51:27.435 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:51:27.435 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:51:30.533 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:51:30.533 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:51:33.544 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:51:33.661 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:51:36.665 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:51:36.743 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:51:39.754 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:51:39.865 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:51:42.858 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:51:42.993 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368331, rowId: 0 
[INFO ] 2025-02-18 16:51:46.001 - [任务 9][Sybase] - rebuild statement with 368331, 0 
[INFO ] 2025-02-18 16:51:46.187 - [任务 9][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 16:51:46.187 - [任务 9][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 16:51:46.390 - [任务 9][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368332, rowId: 94 
[TRACE] 2025-02-18 16:51:48.906 - [任务 9][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] running status set to false 
[INFO ] 2025-02-18 16:51:48.928 - [任务 9][Sybase] - Log Miner is shutting down... 
[INFO ] 2025-02-18 16:51:48.930 - [任务 9][Sybase] - rebuild statement with 368332, 94 
[TRACE] 2025-02-18 16:51:49.285 - [任务 9][Sybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_21df3819-d4c2-4e2d-94bf-05732f3cc289_1739868626448 
[TRACE] 2025-02-18 16:51:49.285 - [任务 9][Sybase] - PDK connector node released: HazelcastSourcePdkDataNode_21df3819-d4c2-4e2d-94bf-05732f3cc289_1739868626448 
[TRACE] 2025-02-18 16:51:49.285 - [任务 9][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] schema data cleaned 
[TRACE] 2025-02-18 16:51:49.289 - [任务 9][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] monitor closed 
[TRACE] 2025-02-18 16:51:49.289 - [任务 9][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] close complete, cost 384 ms 
[TRACE] 2025-02-18 16:51:49.312 - [任务 9][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] running status set to false 
[TRACE] 2025-02-18 16:51:49.312 - [任务 9][Sybase] - Incremental sync completed 
[TRACE] 2025-02-18 16:51:49.321 - [任务 9][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode_4bf82ce7-6fc2-4db5-b06c-430fb61ba005_1739868626589 
[TRACE] 2025-02-18 16:51:49.322 - [任务 9][PG] - PDK connector node released: HazelcastTargetPdkDataNode_4bf82ce7-6fc2-4db5-b06c-430fb61ba005_1739868626589 
[TRACE] 2025-02-18 16:51:49.322 - [任务 9][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] schema data cleaned 
[TRACE] 2025-02-18 16:51:49.325 - [任务 9][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] monitor closed 
[TRACE] 2025-02-18 16:51:49.326 - [任务 9][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] close complete, cost 35 ms 
[TRACE] 2025-02-18 16:51:50.504 - [任务 9] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-18 16:51:50.506 - [任务 9] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@15461856 
[TRACE] 2025-02-18 16:51:50.509 - [任务 9] - Stop task milestones: 67b4472ccc3e3c6b7743d6ac(任务 9)  
[TRACE] 2025-02-18 16:51:50.644 - [任务 9] - Stopped task aspect(s) 
[TRACE] 2025-02-18 16:51:50.644 - [任务 9] - Snapshot order controller have been removed 
[INFO ] 2025-02-18 16:51:50.645 - [任务 9] - Task stopped. 
[TRACE] 2025-02-18 16:51:50.689 - [任务 9] - Remove memory task client succeed, task: 任务 9[67b4472ccc3e3c6b7743d6ac] 
[TRACE] 2025-02-18 16:51:50.689 - [任务 9] - Destroy memory task client cache succeed, task: 任务 9[67b4472ccc3e3c6b7743d6ac] 
[TRACE] 2025-02-18 18:34:50.138 - [验证Sybase 空字符串到PG] - Task initialization... 
[TRACE] 2025-02-18 18:34:50.141 - [验证Sybase 空字符串到PG] - Start task milestones: 67b4472ccc3e3c6b7743d6ac(验证Sybase 空字符串到PG) 
[INFO ] 2025-02-18 18:34:50.259 - [验证Sybase 空字符串到PG] - Loading table structure completed 
[TRACE] 2025-02-18 18:34:50.259 - [验证Sybase 空字符串到PG] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-18 18:34:50.314 - [验证Sybase 空字符串到PG] - The engine receives 验证Sybase 空字符串到PG task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-18 18:34:50.314 - [验证Sybase 空字符串到PG] - Task started 
[TRACE] 2025-02-18 18:34:50.369 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] start preload schema,table counts: 1 
[TRACE] 2025-02-18 18:34:50.369 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] start preload schema,table counts: 1 
[TRACE] 2025-02-18 18:34:50.369 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] preload schema finished, cost 0 ms 
[TRACE] 2025-02-18 18:34:50.369 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] preload schema finished, cost 0 ms 
[INFO ] 2025-02-18 18:34:51.178 - [验证Sybase 空字符串到PG][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-02-18 18:34:51.180 - [验证Sybase 空字符串到PG][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-02-18 18:34:51.180 - [验证Sybase 空字符串到PG][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-02-18 18:34:51.180 - [验证Sybase 空字符串到PG][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-18 18:34:51.180 - [验证Sybase 空字符串到PG][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-02-18 18:34:51.293 - [验证Sybase 空字符串到PG][Sybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-02-18 18:34:51.293 - [验证Sybase 空字符串到PG][PG] - Sink connector(PG) initialization completed 
[TRACE] 2025-02-18 18:34:51.293 - [验证Sybase 空字符串到PG][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-18 18:34:51.293 - [验证Sybase 空字符串到PG][PG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-18 18:34:51.419 - [验证Sybase 空字符串到PG][PG] - Apply table structure to target database 
[INFO ] 2025-02-18 18:34:51.420 - [验证Sybase 空字符串到PG][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[TRACE] 2025-02-18 18:34:51.420 - [验证Sybase 空字符串到PG][Sybase] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-18 18:34:51.461 - [验证Sybase 空字符串到PG][Sybase] - Starting batch read from 1 tables 
[TRACE] 2025-02-18 18:34:51.461 - [验证Sybase 空字符串到PG][Sybase] - Initial sync started 
[INFO ] 2025-02-18 18:34:51.467 - [验证Sybase 空字符串到PG][Sybase] - Starting batch read from table: td_empty_string 
[TRACE] 2025-02-18 18:34:51.468 - [验证Sybase 空字符串到PG][Sybase] - Table td_empty_string is going to be initial synced 
[TRACE] 2025-02-18 18:34:51.554 - [验证Sybase 空字符串到PG][Sybase] - Query snapshot row size completed: Sybase(21df3819-d4c2-4e2d-94bf-05732f3cc289) 
[INFO ] 2025-02-18 18:34:51.554 - [验证Sybase 空字符串到PG][Sybase] - Table td_empty_string has been completed batch read 
[TRACE] 2025-02-18 18:34:51.554 - [验证Sybase 空字符串到PG][Sybase] - Initial sync completed 
[INFO ] 2025-02-18 18:34:51.554 - [验证Sybase 空字符串到PG][Sybase] - Batch read completed. 
[TRACE] 2025-02-18 18:34:51.555 - [验证Sybase 空字符串到PG][Sybase] - Incremental sync starting... 
[TRACE] 2025-02-18 18:34:51.555 - [验证Sybase 空字符串到PG][Sybase] - Initial sync completed 
[TRACE] 2025-02-18 18:34:51.555 - [验证Sybase 空字符串到PG][Sybase] - Starting stream read, table list: [td_empty_string], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-02-18 18:34:51.555 - [验证Sybase 空字符串到PG][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-02-18 18:34:51.609 - [验证Sybase 空字符串到PG][Sybase] - startRid: 368336, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 18:34:51.609 - [验证Sybase 空字符串到PG][Sybase] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 18:34:51.609 - [验证Sybase 空字符串到PG][Sybase] - sybase offset in database is: startRid: 368336, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-02-18 18:34:51.609 - [验证Sybase 空字符串到PG][Sybase] - we will use offset in database, how ever, this is safe: startRid: 368336, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 18:34:51.738 - [验证Sybase 空字符串到PG][Sybase] - sybase cdc work with mode v1: auto rescan 
[INFO ] 2025-02-18 18:34:51.738 - [验证Sybase 空字符串到PG][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-02-18 18:34:51.754 - [验证Sybase 空字符串到PG][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-02-18 18:34:51.754 - [验证Sybase 空字符串到PG][Sybase] - opened cdc for tables: {dbo=[td_empty_string]} 
[INFO ] 2025-02-18 18:34:51.819 - [验证Sybase 空字符串到PG][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-02-18 18:34:51.819 - [验证Sybase 空字符串到PG][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-02-18 18:34:51.994 - [验证Sybase 空字符串到PG][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-02-18 18:34:51.995 - [验证Sybase 空字符串到PG][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-02-18 18:34:51.995 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368336, rowId: 0 
[INFO ] 2025-02-18 18:34:55.005 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368336, 0 
[INFO ] 2025-02-18 18:34:55.179 - [验证Sybase 空字符串到PG][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 18:34:55.179 - [验证Sybase 空字符串到PG][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 18:34:55.385 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368336, rowId: 24 
[INFO ] 2025-02-18 18:34:58.249 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368336, 24 
[INFO ] 2025-02-18 18:34:58.456 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368336, rowId: 24 
[INFO ] 2025-02-18 18:35:01.363 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368336, 24 
[TRACE] 2025-02-18 18:35:01.367 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] running status set to false 
[INFO ] 2025-02-18 18:35:01.368 - [验证Sybase 空字符串到PG][Sybase] - Log Miner is shutting down... 
[TRACE] 2025-02-18 18:35:01.469 - [验证Sybase 空字符串到PG][Sybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_21df3819-d4c2-4e2d-94bf-05732f3cc289_1739874891109 
[TRACE] 2025-02-18 18:35:01.469 - [验证Sybase 空字符串到PG][Sybase] - PDK connector node released: HazelcastSourcePdkDataNode_21df3819-d4c2-4e2d-94bf-05732f3cc289_1739874891109 
[TRACE] 2025-02-18 18:35:01.469 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] schema data cleaned 
[TRACE] 2025-02-18 18:35:01.470 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] monitor closed 
[TRACE] 2025-02-18 18:35:01.470 - [验证Sybase 空字符串到PG][Sybase] - Incremental sync completed 
[TRACE] 2025-02-18 18:35:01.471 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] close complete, cost 111 ms 
[TRACE] 2025-02-18 18:35:01.471 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] running status set to false 
[TRACE] 2025-02-18 18:35:01.501 - [验证Sybase 空字符串到PG][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode_4bf82ce7-6fc2-4db5-b06c-430fb61ba005_1739874891002 
[TRACE] 2025-02-18 18:35:01.502 - [验证Sybase 空字符串到PG][PG] - PDK connector node released: HazelcastTargetPdkDataNode_4bf82ce7-6fc2-4db5-b06c-430fb61ba005_1739874891002 
[TRACE] 2025-02-18 18:35:01.502 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] schema data cleaned 
[TRACE] 2025-02-18 18:35:01.502 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] monitor closed 
[TRACE] 2025-02-18 18:35:01.502 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] close complete, cost 31 ms 
[TRACE] 2025-02-18 18:35:03.981 - [验证Sybase 空字符串到PG] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-18 18:35:03.981 - [验证Sybase 空字符串到PG] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@69533fd7 
[TRACE] 2025-02-18 18:35:04.121 - [验证Sybase 空字符串到PG] - Stop task milestones: 67b4472ccc3e3c6b7743d6ac(验证Sybase 空字符串到PG)  
[TRACE] 2025-02-18 18:35:04.122 - [验证Sybase 空字符串到PG] - Stopped task aspect(s) 
[TRACE] 2025-02-18 18:35:04.122 - [验证Sybase 空字符串到PG] - Snapshot order controller have been removed 
[INFO ] 2025-02-18 18:35:04.122 - [验证Sybase 空字符串到PG] - Task stopped. 
[TRACE] 2025-02-18 18:35:04.144 - [验证Sybase 空字符串到PG] - Remove memory task client succeed, task: 验证Sybase 空字符串到PG[67b4472ccc3e3c6b7743d6ac] 
[TRACE] 2025-02-18 18:35:04.147 - [验证Sybase 空字符串到PG] - Destroy memory task client cache succeed, task: 验证Sybase 空字符串到PG[67b4472ccc3e3c6b7743d6ac] 
[TRACE] 2025-02-18 18:42:10.669 - [验证Sybase 空字符串到PG] - Task initialization... 
[TRACE] 2025-02-18 18:42:10.670 - [验证Sybase 空字符串到PG] - Start task milestones: 67b4472ccc3e3c6b7743d6ac(验证Sybase 空字符串到PG) 
[INFO ] 2025-02-18 18:42:10.791 - [验证Sybase 空字符串到PG] - Loading table structure completed 
[TRACE] 2025-02-18 18:42:10.791 - [验证Sybase 空字符串到PG] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-18 18:42:10.848 - [验证Sybase 空字符串到PG] - The engine receives 验证Sybase 空字符串到PG task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-18 18:42:10.848 - [验证Sybase 空字符串到PG] - Task started 
[TRACE] 2025-02-18 18:42:10.877 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] start preload schema,table counts: 1 
[TRACE] 2025-02-18 18:42:10.877 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] start preload schema,table counts: 1 
[TRACE] 2025-02-18 18:42:10.879 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] preload schema finished, cost 1 ms 
[TRACE] 2025-02-18 18:42:10.879 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] preload schema finished, cost 0 ms 
[INFO ] 2025-02-18 18:42:11.701 - [验证Sybase 空字符串到PG][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-02-18 18:42:11.701 - [验证Sybase 空字符串到PG][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-02-18 18:42:11.702 - [验证Sybase 空字符串到PG][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-02-18 18:42:11.702 - [验证Sybase 空字符串到PG][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-18 18:42:11.703 - [验证Sybase 空字符串到PG][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-02-18 18:42:11.784 - [验证Sybase 空字符串到PG][Sybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-02-18 18:42:11.812 - [验证Sybase 空字符串到PG][PG] - Sink connector(PG) initialization completed 
[TRACE] 2025-02-18 18:42:11.812 - [验证Sybase 空字符串到PG][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-18 18:42:11.812 - [验证Sybase 空字符串到PG][PG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-18 18:42:11.847 - [验证Sybase 空字符串到PG][PG] - Apply table structure to target database 
[WARN ] 2025-02-18 18:42:11.848 - [验证Sybase 空字符串到PG][PG] - Table td_null_col not exists, skip drop 
[INFO ] 2025-02-18 18:42:11.938 - [验证Sybase 空字符串到PG][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[TRACE] 2025-02-18 18:42:11.938 - [验证Sybase 空字符串到PG][Sybase] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-18 18:42:11.989 - [验证Sybase 空字符串到PG][Sybase] - Starting batch read from 1 tables 
[TRACE] 2025-02-18 18:42:12.005 - [验证Sybase 空字符串到PG][Sybase] - Initial sync started 
[INFO ] 2025-02-18 18:42:12.005 - [验证Sybase 空字符串到PG][Sybase] - Starting batch read from table: td_null_col 
[TRACE] 2025-02-18 18:42:12.005 - [验证Sybase 空字符串到PG][Sybase] - Table td_null_col is going to be initial synced 
[TRACE] 2025-02-18 18:42:12.118 - [验证Sybase 空字符串到PG][Sybase] - Query snapshot row size completed: Sybase(21df3819-d4c2-4e2d-94bf-05732f3cc289) 
[INFO ] 2025-02-18 18:42:12.118 - [验证Sybase 空字符串到PG][Sybase] - Table td_null_col has been completed batch read 
[TRACE] 2025-02-18 18:42:12.118 - [验证Sybase 空字符串到PG][Sybase] - Initial sync completed 
[INFO ] 2025-02-18 18:42:12.118 - [验证Sybase 空字符串到PG][Sybase] - Batch read completed. 
[TRACE] 2025-02-18 18:42:12.118 - [验证Sybase 空字符串到PG][Sybase] - Incremental sync starting... 
[TRACE] 2025-02-18 18:42:12.118 - [验证Sybase 空字符串到PG][Sybase] - Initial sync completed 
[TRACE] 2025-02-18 18:42:12.119 - [验证Sybase 空字符串到PG][Sybase] - Starting stream read, table list: [td_null_col], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-02-18 18:42:12.174 - [验证Sybase 空字符串到PG][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-02-18 18:42:12.174 - [验证Sybase 空字符串到PG][Sybase] - startRid: 368339, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 18:42:12.174 - [验证Sybase 空字符串到PG][Sybase] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 18:42:12.175 - [验证Sybase 空字符串到PG][Sybase] - sybase offset in database is: startRid: 368339, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-02-18 18:42:12.184 - [验证Sybase 空字符串到PG][Sybase] - we will use offset in database, how ever, this is safe: startRid: 368339, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 18:42:12.186 - [验证Sybase 空字符串到PG][Sybase] - sybase cdc work with mode v1: auto rescan 
[INFO ] 2025-02-18 18:42:12.307 - [验证Sybase 空字符串到PG][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-02-18 18:42:12.308 - [验证Sybase 空字符串到PG][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-02-18 18:42:12.330 - [验证Sybase 空字符串到PG][Sybase] - opened cdc for tables: {dbo=[td_null_col]} 
[INFO ] 2025-02-18 18:42:12.330 - [验证Sybase 空字符串到PG][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-02-18 18:42:12.530 - [验证Sybase 空字符串到PG][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-02-18 18:42:12.564 - [验证Sybase 空字符串到PG][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-02-18 18:42:12.564 - [验证Sybase 空字符串到PG][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-02-18 18:42:12.564 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368339, rowId: 0 
[INFO ] 2025-02-18 18:42:15.574 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368339, 0 
[INFO ] 2025-02-18 18:42:15.684 - [验证Sybase 空字符串到PG][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 18:42:15.732 - [验证Sybase 空字符串到PG][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 18:42:15.962 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368339, rowId: 108 
[INFO ] 2025-02-18 18:42:18.780 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368339, 108 
[INFO ] 2025-02-18 18:42:18.924 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368339, rowId: 108 
[INFO ] 2025-02-18 18:42:21.927 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368339, 108 
[INFO ] 2025-02-18 18:42:22.109 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368339, rowId: 108 
[INFO ] 2025-02-18 18:42:25.177 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368339, 108 
[INFO ] 2025-02-18 18:42:25.380 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368339, rowId: 108 
[INFO ] 2025-02-18 18:42:28.377 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368339, 108 
[INFO ] 2025-02-18 18:42:28.377 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368339, rowId: 108 
[INFO ] 2025-02-18 18:42:31.378 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368339, 108 
[INFO ] 2025-02-18 18:42:31.583 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368339, rowId: 108 
[INFO ] 2025-02-18 18:42:34.632 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368339, 108 
[INFO ] 2025-02-18 18:42:34.839 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368339, rowId: 108 
[INFO ] 2025-02-18 18:42:37.670 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368339, 108 
[INFO ] 2025-02-18 18:42:37.818 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368339, rowId: 108 
[INFO ] 2025-02-18 18:42:40.825 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368339, 108 
[INFO ] 2025-02-18 18:42:41.015 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368339, rowId: 108 
[INFO ] 2025-02-18 18:42:44.023 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368339, 108 
[INFO ] 2025-02-18 18:42:44.229 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368339, rowId: 108 
[INFO ] 2025-02-18 18:42:47.296 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368339, 108 
[INFO ] 2025-02-18 18:42:47.500 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368339, rowId: 108 
[INFO ] 2025-02-18 18:42:50.326 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368339, 108 
[INFO ] 2025-02-18 18:42:50.939 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368339, rowId: 108 
[INFO ] 2025-02-18 18:42:53.799 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368339, 108 
[INFO ] 2025-02-18 18:42:53.965 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368339, rowId: 108 
[INFO ] 2025-02-18 18:42:56.966 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368339, 108 
[INFO ] 2025-02-18 18:42:57.155 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368339, rowId: 108 
[TRACE] 2025-02-18 18:43:00.144 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] running status set to false 
[INFO ] 2025-02-18 18:43:00.158 - [验证Sybase 空字符串到PG][Sybase] - Log Miner is shutting down... 
[INFO ] 2025-02-18 18:43:00.158 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368339, 108 
[TRACE] 2025-02-18 18:43:00.260 - [验证Sybase 空字符串到PG][Sybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_21df3819-d4c2-4e2d-94bf-05732f3cc289_1739875331608 
[TRACE] 2025-02-18 18:43:00.260 - [验证Sybase 空字符串到PG][Sybase] - Incremental sync completed 
[TRACE] 2025-02-18 18:43:00.260 - [验证Sybase 空字符串到PG][Sybase] - PDK connector node released: HazelcastSourcePdkDataNode_21df3819-d4c2-4e2d-94bf-05732f3cc289_1739875331608 
[TRACE] 2025-02-18 18:43:00.261 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] schema data cleaned 
[TRACE] 2025-02-18 18:43:00.261 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] monitor closed 
[TRACE] 2025-02-18 18:43:00.261 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] close complete, cost 158 ms 
[TRACE] 2025-02-18 18:43:00.262 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] running status set to false 
[TRACE] 2025-02-18 18:43:00.284 - [验证Sybase 空字符串到PG][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode_4bf82ce7-6fc2-4db5-b06c-430fb61ba005_1739875331535 
[TRACE] 2025-02-18 18:43:00.284 - [验证Sybase 空字符串到PG][PG] - PDK connector node released: HazelcastTargetPdkDataNode_4bf82ce7-6fc2-4db5-b06c-430fb61ba005_1739875331535 
[TRACE] 2025-02-18 18:43:00.285 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] schema data cleaned 
[TRACE] 2025-02-18 18:43:00.285 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] monitor closed 
[TRACE] 2025-02-18 18:43:00.285 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] close complete, cost 23 ms 
[TRACE] 2025-02-18 18:43:04.519 - [验证Sybase 空字符串到PG] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-18 18:43:04.523 - [验证Sybase 空字符串到PG] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@ef49546 
[TRACE] 2025-02-18 18:43:04.523 - [验证Sybase 空字符串到PG] - Stop task milestones: 67b4472ccc3e3c6b7743d6ac(验证Sybase 空字符串到PG)  
[TRACE] 2025-02-18 18:43:04.675 - [验证Sybase 空字符串到PG] - Stopped task aspect(s) 
[TRACE] 2025-02-18 18:43:04.676 - [验证Sybase 空字符串到PG] - Snapshot order controller have been removed 
[INFO ] 2025-02-18 18:43:04.676 - [验证Sybase 空字符串到PG] - Task stopped. 
[TRACE] 2025-02-18 18:43:04.708 - [验证Sybase 空字符串到PG] - Remove memory task client succeed, task: 验证Sybase 空字符串到PG[67b4472ccc3e3c6b7743d6ac] 
[TRACE] 2025-02-18 18:43:04.708 - [验证Sybase 空字符串到PG] - Destroy memory task client cache succeed, task: 验证Sybase 空字符串到PG[67b4472ccc3e3c6b7743d6ac] 
[TRACE] 2025-02-18 18:59:19.711 - [验证Sybase 空字符串到PG] - Task initialization... 
[TRACE] 2025-02-18 18:59:19.711 - [验证Sybase 空字符串到PG] - Start task milestones: 67b4472ccc3e3c6b7743d6ac(验证Sybase 空字符串到PG) 
[INFO ] 2025-02-18 18:59:19.966 - [验证Sybase 空字符串到PG] - Loading table structure completed 
[TRACE] 2025-02-18 18:59:19.966 - [验证Sybase 空字符串到PG] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-18 18:59:20.037 - [验证Sybase 空字符串到PG] - The engine receives 验证Sybase 空字符串到PG task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-18 18:59:20.038 - [验证Sybase 空字符串到PG] - Task started 
[TRACE] 2025-02-18 18:59:20.087 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] start preload schema,table counts: 1 
[TRACE] 2025-02-18 18:59:20.087 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] start preload schema,table counts: 1 
[TRACE] 2025-02-18 18:59:20.087 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] preload schema finished, cost 0 ms 
[TRACE] 2025-02-18 18:59:20.088 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] preload schema finished, cost 0 ms 
[INFO ] 2025-02-18 18:59:20.754 - [验证Sybase 空字符串到PG][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-02-18 18:59:20.757 - [验证Sybase 空字符串到PG][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-02-18 18:59:20.757 - [验证Sybase 空字符串到PG][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-02-18 18:59:20.757 - [验证Sybase 空字符串到PG][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-18 18:59:20.762 - [验证Sybase 空字符串到PG][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-02-18 18:59:20.967 - [验证Sybase 空字符串到PG][Sybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-02-18 18:59:21.053 - [验证Sybase 空字符串到PG][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[TRACE] 2025-02-18 18:59:21.053 - [验证Sybase 空字符串到PG][Sybase] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-18 18:59:21.080 - [验证Sybase 空字符串到PG][PG] - Sink connector(PG) initialization completed 
[TRACE] 2025-02-18 18:59:21.080 - [验证Sybase 空字符串到PG][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-18 18:59:21.080 - [验证Sybase 空字符串到PG][PG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-18 18:59:21.112 - [验证Sybase 空字符串到PG][PG] - Apply table structure to target database 
[INFO ] 2025-02-18 18:59:21.112 - [验证Sybase 空字符串到PG][Sybase] - Starting batch read from 1 tables 
[TRACE] 2025-02-18 18:59:21.130 - [验证Sybase 空字符串到PG][Sybase] - Initial sync started 
[INFO ] 2025-02-18 18:59:21.130 - [验证Sybase 空字符串到PG][Sybase] - Starting batch read from table: td_null_col 
[TRACE] 2025-02-18 18:59:21.228 - [验证Sybase 空字符串到PG][Sybase] - Table td_null_col is going to be initial synced 
[TRACE] 2025-02-18 18:59:21.229 - [验证Sybase 空字符串到PG][Sybase] - Query snapshot row size completed: Sybase(21df3819-d4c2-4e2d-94bf-05732f3cc289) 
[INFO ] 2025-02-18 18:59:21.229 - [验证Sybase 空字符串到PG][Sybase] - Table td_null_col has been completed batch read 
[TRACE] 2025-02-18 18:59:21.229 - [验证Sybase 空字符串到PG][Sybase] - Initial sync completed 
[INFO ] 2025-02-18 18:59:21.229 - [验证Sybase 空字符串到PG][Sybase] - Batch read completed. 
[TRACE] 2025-02-18 18:59:21.230 - [验证Sybase 空字符串到PG][Sybase] - Incremental sync starting... 
[TRACE] 2025-02-18 18:59:21.230 - [验证Sybase 空字符串到PG][Sybase] - Initial sync completed 
[TRACE] 2025-02-18 18:59:21.230 - [验证Sybase 空字符串到PG][Sybase] - Starting stream read, table list: [td_null_col], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-02-18 18:59:21.233 - [验证Sybase 空字符串到PG][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-02-18 18:59:21.309 - [验证Sybase 空字符串到PG][Sybase] - startRid: 368341, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 18:59:21.309 - [验证Sybase 空字符串到PG][Sybase] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 18:59:21.309 - [验证Sybase 空字符串到PG][Sybase] - sybase offset in database is: startRid: 368341, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-02-18 18:59:21.309 - [验证Sybase 空字符串到PG][Sybase] - we will use offset in database, how ever, this is safe: startRid: 368341, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 18:59:21.476 - [验证Sybase 空字符串到PG][Sybase] - sybase cdc work with mode v1: auto rescan 
[INFO ] 2025-02-18 18:59:21.476 - [验证Sybase 空字符串到PG][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-02-18 18:59:21.496 - [验证Sybase 空字符串到PG][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-02-18 18:59:21.496 - [验证Sybase 空字符串到PG][Sybase] - opened cdc for tables: {dbo=[td_null_col]} 
[INFO ] 2025-02-18 18:59:21.585 - [验证Sybase 空字符串到PG][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-02-18 18:59:21.586 - [验证Sybase 空字符串到PG][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-02-18 18:59:21.799 - [验证Sybase 空字符串到PG][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-02-18 18:59:21.799 - [验证Sybase 空字符串到PG][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-02-18 18:59:21.799 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 0 
[INFO ] 2025-02-18 18:59:24.805 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 0 
[INFO ] 2025-02-18 18:59:24.876 - [验证Sybase 空字符串到PG][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 18:59:24.876 - [验证Sybase 空字符串到PG][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 18:59:25.082 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[TRACE] 2025-02-18 18:59:25.459 - [验证Sybase 空字符串到PG][PG] - Table 'td_null_col' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2025-02-18 18:59:28.031 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 18:59:28.031 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 18:59:31.037 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 18:59:31.136 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 18:59:34.156 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 18:59:34.356 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 18:59:37.344 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 18:59:37.344 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 18:59:40.350 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 18:59:40.495 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 18:59:43.496 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 18:59:43.596 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 18:59:46.601 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 18:59:46.804 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 18:59:49.792 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 18:59:49.792 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 18:59:52.797 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 18:59:52.998 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 18:59:55.994 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 18:59:55.994 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 18:59:58.999 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 18:59:59.200 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:00:02.243 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:00:02.243 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:00:05.245 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:00:05.339 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:00:08.340 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:00:08.468 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:00:11.474 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:00:11.605 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:00:14.630 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:00:14.835 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:00:17.861 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:00:18.061 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:00:20.873 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:00:20.973 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:00:23.976 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:00:24.184 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:00:27.170 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:00:27.171 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:00:30.172 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:00:30.373 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:00:33.354 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:00:33.355 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:00:36.355 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:00:36.558 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:00:39.555 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:00:39.555 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:00:42.556 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:00:42.758 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:00:45.772 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:00:45.772 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:00:48.772 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:00:48.973 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:00:52.007 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:00:52.208 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:00:55.035 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:00:55.240 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:00:58.293 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:00:58.498 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:01:01.304 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:01:01.405 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:01:04.408 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:01:05.019 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:01:08.018 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:01:08.018 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:01:11.024 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:01:11.167 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:01:14.198 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:01:14.402 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:01:17.371 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:01:17.372 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:01:20.377 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:01:20.524 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:01:23.562 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:01:23.764 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:01:26.751 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:01:26.751 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:01:29.753 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:01:29.956 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:01:32.965 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:01:32.965 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:01:35.969 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:01:36.175 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:01:39.210 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:01:39.411 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:01:42.241 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:01:42.447 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:01:45.447 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:01:45.448 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:01:48.453 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:01:48.654 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:01:51.629 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:01:51.629 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:01:54.633 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:01:54.834 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:01:57.837 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:01:57.837 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:02:00.843 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:02:00.980 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:02:03.981 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:02:04.095 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:02:07.098 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:02:07.299 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:02:10.324 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:02:10.525 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:02:13.355 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:02:13.458 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:02:16.460 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:02:16.660 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:02:19.687 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:02:19.688 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:02:22.688 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:02:22.891 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:02:25.893 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:02:25.893 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:02:28.896 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:02:29.100 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:02:32.136 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:02:32.339 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:02:35.157 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:02:35.323 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:02:38.327 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:02:38.463 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:02:41.508 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:02:41.713 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:02:44.711 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:02:44.711 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:02:47.714 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:02:47.915 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:02:50.950 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:02:51.153 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:02:53.984 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:02:54.149 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:02:57.155 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:02:57.285 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:03:00.317 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:03:00.522 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:03:03.552 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:03:03.753 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:03:06.578 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:03:06.882 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:03:09.887 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:03:10.076 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:03:13.077 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:03:13.280 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:03:16.304 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:03:16.505 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:03:19.545 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:03:19.704 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:03:22.710 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:03:22.911 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:03:25.944 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:03:26.554 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:03:29.572 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:03:29.572 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:03:32.575 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:03:32.761 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:03:35.768 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:03:35.971 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:03:38.997 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:03:39.198 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:03:42.191 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:03:42.191 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:03:45.192 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:03:45.394 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:03:48.431 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:03:48.487 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:03:51.519 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:03:51.721 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:03:54.753 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:03:54.954 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:03:57.767 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:03:57.892 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:04:00.897 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:04:01.042 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:04:04.085 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:04:04.286 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:04:07.303 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:04:07.303 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:04:10.303 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:04:10.506 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:04:13.544 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:04:13.745 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:04:16.556 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:04:16.716 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:04:19.721 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:04:19.853 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:04:22.859 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:04:23.064 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:04:26.097 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:04:26.298 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:04:29.267 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:04:29.267 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:04:32.272 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:04:32.474 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:04:35.505 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:04:35.707 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:04:38.532 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:04:38.645 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:04:41.647 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:04:41.851 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:04:44.882 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:04:44.882 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 77 
[INFO ] 2025-02-18 19:04:47.884 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 77 
[INFO ] 2025-02-18 19:04:48.032 - [验证Sybase 空字符串到PG][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 19:04:48.038 - [验证Sybase 空字符串到PG][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 19:04:48.039 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 80 
[INFO ] 2025-02-18 19:04:51.044 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 80 
[INFO ] 2025-02-18 19:04:51.170 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 80 
[INFO ] 2025-02-18 19:04:54.171 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 80 
[INFO ] 2025-02-18 19:04:54.305 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 80 
[INFO ] 2025-02-18 19:04:57.311 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 80 
[INFO ] 2025-02-18 19:04:57.478 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 80 
[INFO ] 2025-02-18 19:05:00.483 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 80 
[INFO ] 2025-02-18 19:05:00.611 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 80 
[INFO ] 2025-02-18 19:05:03.659 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 80 
[INFO ] 2025-02-18 19:05:03.859 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 80 
[INFO ] 2025-02-18 19:05:06.897 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 80 
[INFO ] 2025-02-18 19:05:07.098 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 80 
[INFO ] 2025-02-18 19:05:09.927 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 80 
[INFO ] 2025-02-18 19:05:10.030 - [验证Sybase 空字符串到PG][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 19:05:10.030 - [验证Sybase 空字符串到PG][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 19:05:10.034 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:05:13.055 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:05:13.256 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:05:16.286 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:05:16.286 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:05:19.286 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:05:19.425 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:05:22.443 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:05:22.643 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:05:25.675 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:05:25.876 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[TRACE] 2025-02-18 19:05:28.426 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] running status set to false 
[INFO ] 2025-02-18 19:05:28.428 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:05:28.428 - [验证Sybase 空字符串到PG][Sybase] - Log Miner is shutting down... 
[TRACE] 2025-02-18 19:05:28.579 - [验证Sybase 空字符串到PG][Sybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_21df3819-d4c2-4e2d-94bf-05732f3cc289_1739876360685 
[TRACE] 2025-02-18 19:05:28.580 - [验证Sybase 空字符串到PG][Sybase] - Incremental sync completed 
[TRACE] 2025-02-18 19:05:28.580 - [验证Sybase 空字符串到PG][Sybase] - PDK connector node released: HazelcastSourcePdkDataNode_21df3819-d4c2-4e2d-94bf-05732f3cc289_1739876360685 
[TRACE] 2025-02-18 19:05:28.580 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] schema data cleaned 
[TRACE] 2025-02-18 19:05:28.580 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] monitor closed 
[TRACE] 2025-02-18 19:05:28.581 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] close complete, cost 158 ms 
[TRACE] 2025-02-18 19:05:28.581 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] running status set to false 
[TRACE] 2025-02-18 19:05:28.587 - [验证Sybase 空字符串到PG][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode_4bf82ce7-6fc2-4db5-b06c-430fb61ba005_1739876360819 
[TRACE] 2025-02-18 19:05:28.588 - [验证Sybase 空字符串到PG][PG] - PDK connector node released: HazelcastTargetPdkDataNode_4bf82ce7-6fc2-4db5-b06c-430fb61ba005_1739876360819 
[TRACE] 2025-02-18 19:05:28.588 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] schema data cleaned 
[TRACE] 2025-02-18 19:05:28.588 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] monitor closed 
[TRACE] 2025-02-18 19:05:28.588 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] close complete, cost 6 ms 
[TRACE] 2025-02-18 19:05:31.424 - [验证Sybase 空字符串到PG] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-18 19:05:31.424 - [验证Sybase 空字符串到PG] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@533c5293 
[TRACE] 2025-02-18 19:05:31.426 - [验证Sybase 空字符串到PG] - Stop task milestones: 67b4472ccc3e3c6b7743d6ac(验证Sybase 空字符串到PG)  
[TRACE] 2025-02-18 19:05:31.549 - [验证Sybase 空字符串到PG] - Stopped task aspect(s) 
[TRACE] 2025-02-18 19:05:31.549 - [验证Sybase 空字符串到PG] - Snapshot order controller have been removed 
[INFO ] 2025-02-18 19:05:31.550 - [验证Sybase 空字符串到PG] - Task stopped. 
[TRACE] 2025-02-18 19:05:31.567 - [验证Sybase 空字符串到PG] - Remove memory task client succeed, task: 验证Sybase 空字符串到PG[67b4472ccc3e3c6b7743d6ac] 
[TRACE] 2025-02-18 19:05:31.570 - [验证Sybase 空字符串到PG] - Destroy memory task client cache succeed, task: 验证Sybase 空字符串到PG[67b4472ccc3e3c6b7743d6ac] 
[TRACE] 2025-02-18 19:05:37.187 - [验证Sybase 空字符串到PG] - Task initialization... 
[TRACE] 2025-02-18 19:05:37.191 - [验证Sybase 空字符串到PG] - Start task milestones: 67b4472ccc3e3c6b7743d6ac(验证Sybase 空字符串到PG) 
[INFO ] 2025-02-18 19:05:37.286 - [验证Sybase 空字符串到PG] - Loading table structure completed 
[TRACE] 2025-02-18 19:05:37.286 - [验证Sybase 空字符串到PG] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-18 19:05:37.336 - [验证Sybase 空字符串到PG] - The engine receives 验证Sybase 空字符串到PG task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-18 19:05:37.337 - [验证Sybase 空字符串到PG] - Task started 
[TRACE] 2025-02-18 19:05:37.362 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] start preload schema,table counts: 1 
[TRACE] 2025-02-18 19:05:37.362 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] start preload schema,table counts: 1 
[TRACE] 2025-02-18 19:05:37.362 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] preload schema finished, cost 0 ms 
[TRACE] 2025-02-18 19:05:37.362 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] preload schema finished, cost 0 ms 
[INFO ] 2025-02-18 19:05:38.033 - [验证Sybase 空字符串到PG][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-02-18 19:05:38.037 - [验证Sybase 空字符串到PG][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-02-18 19:05:38.037 - [验证Sybase 空字符串到PG][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-02-18 19:05:38.037 - [验证Sybase 空字符串到PG][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-18 19:05:38.037 - [验证Sybase 空字符串到PG][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-02-18 19:05:38.241 - [验证Sybase 空字符串到PG][Sybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-02-18 19:05:38.284 - [验证Sybase 空字符串到PG][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[TRACE] 2025-02-18 19:05:38.284 - [验证Sybase 空字符串到PG][Sybase] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-18 19:05:38.335 - [验证Sybase 空字符串到PG][PG] - Sink connector(PG) initialization completed 
[TRACE] 2025-02-18 19:05:38.336 - [验证Sybase 空字符串到PG][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-18 19:05:38.336 - [验证Sybase 空字符串到PG][PG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-18 19:05:38.337 - [验证Sybase 空字符串到PG][PG] - Apply table structure to target database 
[INFO ] 2025-02-18 19:05:38.384 - [验证Sybase 空字符串到PG][Sybase] - Starting batch read from 1 tables 
[TRACE] 2025-02-18 19:05:38.385 - [验证Sybase 空字符串到PG][Sybase] - Initial sync started 
[INFO ] 2025-02-18 19:05:38.385 - [验证Sybase 空字符串到PG][Sybase] - Starting batch read from table: td_null_col 
[TRACE] 2025-02-18 19:05:38.468 - [验证Sybase 空字符串到PG][Sybase] - Table td_null_col is going to be initial synced 
[TRACE] 2025-02-18 19:05:38.468 - [验证Sybase 空字符串到PG][Sybase] - Query snapshot row size completed: Sybase(21df3819-d4c2-4e2d-94bf-05732f3cc289) 
[INFO ] 2025-02-18 19:05:38.479 - [验证Sybase 空字符串到PG][Sybase] - Table td_null_col has been completed batch read 
[TRACE] 2025-02-18 19:05:38.479 - [验证Sybase 空字符串到PG][Sybase] - Initial sync completed 
[INFO ] 2025-02-18 19:05:38.479 - [验证Sybase 空字符串到PG][Sybase] - Batch read completed. 
[TRACE] 2025-02-18 19:05:38.479 - [验证Sybase 空字符串到PG][Sybase] - Incremental sync starting... 
[TRACE] 2025-02-18 19:05:38.479 - [验证Sybase 空字符串到PG][Sybase] - Initial sync completed 
[TRACE] 2025-02-18 19:05:38.480 - [验证Sybase 空字符串到PG][Sybase] - Starting stream read, table list: [td_null_col], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-02-18 19:05:38.480 - [验证Sybase 空字符串到PG][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-02-18 19:05:38.538 - [验证Sybase 空字符串到PG][Sybase] - startRid: 368341, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 19:05:38.538 - [验证Sybase 空字符串到PG][Sybase] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 19:05:38.539 - [验证Sybase 空字符串到PG][Sybase] - sybase offset in database is: startRid: 368341, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-02-18 19:05:38.539 - [验证Sybase 空字符串到PG][Sybase] - we will use offset in database, how ever, this is safe: startRid: 368341, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 19:05:38.561 - [验证Sybase 空字符串到PG][Sybase] - sybase cdc work with mode v1: auto rescan 
[INFO ] 2025-02-18 19:05:38.719 - [验证Sybase 空字符串到PG][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-02-18 19:05:38.720 - [验证Sybase 空字符串到PG][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-02-18 19:05:38.733 - [验证Sybase 空字符串到PG][Sybase] - opened cdc for tables: {dbo=[td_null_col]} 
[INFO ] 2025-02-18 19:05:38.733 - [验证Sybase 空字符串到PG][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-02-18 19:05:38.938 - [验证Sybase 空字符串到PG][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-02-18 19:05:38.952 - [验证Sybase 空字符串到PG][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-02-18 19:05:38.952 - [验证Sybase 空字符串到PG][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-02-18 19:05:38.952 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 0 
[INFO ] 2025-02-18 19:05:41.991 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 0 
[INFO ] 2025-02-18 19:05:42.094 - [验证Sybase 空字符串到PG][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 19:05:42.094 - [验证Sybase 空字符串到PG][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 19:05:42.102 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[TRACE] 2025-02-18 19:05:42.710 - [验证Sybase 空字符串到PG][PG] - Table 'td_null_col' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2025-02-18 19:05:45.129 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:05:45.330 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:05:48.355 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:05:48.355 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:05:51.356 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:05:51.561 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:05:54.568 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:05:54.568 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:05:57.573 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:05:57.778 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:06:00.810 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:06:01.015 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:06:03.836 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:06:04.040 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:06:07.065 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:06:07.065 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:06:10.071 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:06:10.271 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:06:13.265 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:06:13.265 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[TRACE] 2025-02-18 19:06:15.171 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] running status set to false 
[INFO ] 2025-02-18 19:06:15.189 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:06:15.194 - [验证Sybase 空字符串到PG][Sybase] - Log Miner is shutting down... 
[TRACE] 2025-02-18 19:06:15.300 - [验证Sybase 空字符串到PG][Sybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_21df3819-d4c2-4e2d-94bf-05732f3cc289_1739876737953 
[TRACE] 2025-02-18 19:06:15.300 - [验证Sybase 空字符串到PG][Sybase] - PDK connector node released: HazelcastSourcePdkDataNode_21df3819-d4c2-4e2d-94bf-05732f3cc289_1739876737953 
[TRACE] 2025-02-18 19:06:15.300 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] schema data cleaned 
[TRACE] 2025-02-18 19:06:15.300 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] monitor closed 
[TRACE] 2025-02-18 19:06:15.301 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] close complete, cost 132 ms 
[TRACE] 2025-02-18 19:06:15.302 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] running status set to false 
[TRACE] 2025-02-18 19:06:15.328 - [验证Sybase 空字符串到PG][Sybase] - Incremental sync completed 
[TRACE] 2025-02-18 19:06:15.329 - [验证Sybase 空字符串到PG][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode_4bf82ce7-6fc2-4db5-b06c-430fb61ba005_1739876738086 
[TRACE] 2025-02-18 19:06:15.329 - [验证Sybase 空字符串到PG][PG] - PDK connector node released: HazelcastTargetPdkDataNode_4bf82ce7-6fc2-4db5-b06c-430fb61ba005_1739876738086 
[TRACE] 2025-02-18 19:06:15.329 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] schema data cleaned 
[TRACE] 2025-02-18 19:06:15.329 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] monitor closed 
[TRACE] 2025-02-18 19:06:15.533 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] close complete, cost 28 ms 
[TRACE] 2025-02-18 19:06:16.616 - [验证Sybase 空字符串到PG] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-18 19:06:16.617 - [验证Sybase 空字符串到PG] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@68338513 
[TRACE] 2025-02-18 19:06:16.747 - [验证Sybase 空字符串到PG] - Stop task milestones: 67b4472ccc3e3c6b7743d6ac(验证Sybase 空字符串到PG)  
[TRACE] 2025-02-18 19:06:16.747 - [验证Sybase 空字符串到PG] - Stopped task aspect(s) 
[TRACE] 2025-02-18 19:06:16.747 - [验证Sybase 空字符串到PG] - Snapshot order controller have been removed 
[INFO ] 2025-02-18 19:06:16.747 - [验证Sybase 空字符串到PG] - Task stopped. 
[TRACE] 2025-02-18 19:06:16.767 - [验证Sybase 空字符串到PG] - Remove memory task client succeed, task: 验证Sybase 空字符串到PG[67b4472ccc3e3c6b7743d6ac] 
[TRACE] 2025-02-18 19:06:16.767 - [验证Sybase 空字符串到PG] - Destroy memory task client cache succeed, task: 验证Sybase 空字符串到PG[67b4472ccc3e3c6b7743d6ac] 
[TRACE] 2025-02-18 19:06:39.402 - [验证Sybase 空字符串到PG] - Task initialization... 
[TRACE] 2025-02-18 19:06:39.406 - [验证Sybase 空字符串到PG] - Start task milestones: 67b4472ccc3e3c6b7743d6ac(验证Sybase 空字符串到PG) 
[INFO ] 2025-02-18 19:06:39.530 - [验证Sybase 空字符串到PG] - Loading table structure completed 
[TRACE] 2025-02-18 19:06:39.531 - [验证Sybase 空字符串到PG] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-18 19:06:39.600 - [验证Sybase 空字符串到PG] - The engine receives 验证Sybase 空字符串到PG task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-18 19:06:39.601 - [验证Sybase 空字符串到PG] - Task started 
[TRACE] 2025-02-18 19:06:39.642 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] start preload schema,table counts: 1 
[TRACE] 2025-02-18 19:06:39.642 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] start preload schema,table counts: 1 
[TRACE] 2025-02-18 19:06:39.643 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] preload schema finished, cost 1 ms 
[TRACE] 2025-02-18 19:06:39.643 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] preload schema finished, cost 0 ms 
[INFO ] 2025-02-18 19:06:40.318 - [验证Sybase 空字符串到PG][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-02-18 19:06:40.320 - [验证Sybase 空字符串到PG][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-02-18 19:06:40.320 - [验证Sybase 空字符串到PG][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-02-18 19:06:40.321 - [验证Sybase 空字符串到PG][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-18 19:06:40.321 - [验证Sybase 空字符串到PG][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-02-18 19:06:40.525 - [验证Sybase 空字符串到PG][Sybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-02-18 19:06:40.564 - [验证Sybase 空字符串到PG][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[TRACE] 2025-02-18 19:06:40.564 - [验证Sybase 空字符串到PG][Sybase] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-18 19:06:40.589 - [验证Sybase 空字符串到PG][PG] - Sink connector(PG) initialization completed 
[TRACE] 2025-02-18 19:06:40.589 - [验证Sybase 空字符串到PG][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-18 19:06:40.589 - [验证Sybase 空字符串到PG][PG] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-02-18 19:06:40.623 - [验证Sybase 空字符串到PG][PG] - Apply table structure to target database 
[INFO ] 2025-02-18 19:06:40.623 - [验证Sybase 空字符串到PG][Sybase] - Starting batch read from 1 tables 
[TRACE] 2025-02-18 19:06:40.637 - [验证Sybase 空字符串到PG][Sybase] - Initial sync started 
[INFO ] 2025-02-18 19:06:40.637 - [验证Sybase 空字符串到PG][Sybase] - Starting batch read from table: td_null_col 
[TRACE] 2025-02-18 19:06:40.637 - [验证Sybase 空字符串到PG][Sybase] - Table td_null_col is going to be initial synced 
[TRACE] 2025-02-18 19:06:40.737 - [验证Sybase 空字符串到PG][Sybase] - Query snapshot row size completed: Sybase(21df3819-d4c2-4e2d-94bf-05732f3cc289) 
[INFO ] 2025-02-18 19:06:40.739 - [验证Sybase 空字符串到PG][Sybase] - Table td_null_col has been completed batch read 
[TRACE] 2025-02-18 19:06:40.739 - [验证Sybase 空字符串到PG][Sybase] - Initial sync completed 
[INFO ] 2025-02-18 19:06:40.739 - [验证Sybase 空字符串到PG][Sybase] - Batch read completed. 
[TRACE] 2025-02-18 19:06:40.739 - [验证Sybase 空字符串到PG][Sybase] - Incremental sync starting... 
[TRACE] 2025-02-18 19:06:40.739 - [验证Sybase 空字符串到PG][Sybase] - Initial sync completed 
[TRACE] 2025-02-18 19:06:40.739 - [验证Sybase 空字符串到PG][Sybase] - Starting stream read, table list: [td_null_col], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-02-18 19:06:40.739 - [验证Sybase 空字符串到PG][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-02-18 19:06:40.810 - [验证Sybase 空字符串到PG][Sybase] - startRid: 368341, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 19:06:40.810 - [验证Sybase 空字符串到PG][Sybase] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 19:06:40.810 - [验证Sybase 空字符串到PG][Sybase] - sybase offset in database is: startRid: 368341, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-02-18 19:06:40.810 - [验证Sybase 空字符串到PG][Sybase] - we will use offset in database, how ever, this is safe: startRid: 368341, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 19:06:40.818 - [验证Sybase 空字符串到PG][Sybase] - sybase cdc work with mode v1: auto rescan 
[INFO ] 2025-02-18 19:06:41.180 - [验证Sybase 空字符串到PG][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-02-18 19:06:41.180 - [验证Sybase 空字符串到PG][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-02-18 19:06:41.200 - [验证Sybase 空字符串到PG][Sybase] - opened cdc for tables: {dbo=[td_null_col]} 
[INFO ] 2025-02-18 19:06:41.201 - [验证Sybase 空字符串到PG][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-02-18 19:06:41.392 - [验证Sybase 空字符串到PG][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-02-18 19:06:41.392 - [验证Sybase 空字符串到PG][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-02-18 19:06:41.392 - [验证Sybase 空字符串到PG][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-02-18 19:06:41.392 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 0 
[INFO ] 2025-02-18 19:06:44.398 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 0 
[INFO ] 2025-02-18 19:06:44.485 - [验证Sybase 空字符串到PG][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 19:06:44.485 - [验证Sybase 空字符串到PG][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 19:06:44.685 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[TRACE] 2025-02-18 19:06:44.724 - [验证Sybase 空字符串到PG][PG] - Table 'td_null_col' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2025-02-18 19:06:47.547 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:06:47.752 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[TRACE] 2025-02-18 19:06:48.865 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] running status set to false 
[INFO ] 2025-02-18 19:06:48.865 - [验证Sybase 空字符串到PG][Sybase] - Log Miner is shutting down... 
[INFO ] 2025-02-18 19:06:48.865 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[TRACE] 2025-02-18 19:06:48.972 - [验证Sybase 空字符串到PG][Sybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_21df3819-d4c2-4e2d-94bf-05732f3cc289_1739876800227 
[TRACE] 2025-02-18 19:06:48.972 - [验证Sybase 空字符串到PG][Sybase] - PDK connector node released: HazelcastSourcePdkDataNode_21df3819-d4c2-4e2d-94bf-05732f3cc289_1739876800227 
[TRACE] 2025-02-18 19:06:48.973 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] schema data cleaned 
[TRACE] 2025-02-18 19:06:48.973 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] monitor closed 
[TRACE] 2025-02-18 19:06:48.973 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] close complete, cost 110 ms 
[TRACE] 2025-02-18 19:06:48.973 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] running status set to false 
[TRACE] 2025-02-18 19:06:48.991 - [验证Sybase 空字符串到PG][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode_4bf82ce7-6fc2-4db5-b06c-430fb61ba005_1739876800363 
[TRACE] 2025-02-18 19:06:48.991 - [验证Sybase 空字符串到PG][PG] - PDK connector node released: HazelcastTargetPdkDataNode_4bf82ce7-6fc2-4db5-b06c-430fb61ba005_1739876800363 
[TRACE] 2025-02-18 19:06:48.991 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] schema data cleaned 
[TRACE] 2025-02-18 19:06:48.991 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] monitor closed 
[TRACE] 2025-02-18 19:06:49.198 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] close complete, cost 18 ms 
[TRACE] 2025-02-18 19:06:51.807 - [验证Sybase 空字符串到PG] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-18 19:06:51.808 - [验证Sybase 空字符串到PG] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@ccba76c 
[TRACE] 2025-02-18 19:06:51.949 - [验证Sybase 空字符串到PG] - Stop task milestones: 67b4472ccc3e3c6b7743d6ac(验证Sybase 空字符串到PG)  
[TRACE] 2025-02-18 19:06:51.949 - [验证Sybase 空字符串到PG] - Stopped task aspect(s) 
[TRACE] 2025-02-18 19:06:51.949 - [验证Sybase 空字符串到PG] - Snapshot order controller have been removed 
[INFO ] 2025-02-18 19:06:51.949 - [验证Sybase 空字符串到PG] - Task stopped. 
[TRACE] 2025-02-18 19:06:51.970 - [验证Sybase 空字符串到PG] - Remove memory task client succeed, task: 验证Sybase 空字符串到PG[67b4472ccc3e3c6b7743d6ac] 
[TRACE] 2025-02-18 19:06:51.973 - [验证Sybase 空字符串到PG] - Destroy memory task client cache succeed, task: 验证Sybase 空字符串到PG[67b4472ccc3e3c6b7743d6ac] 
[TRACE] 2025-02-18 19:07:04.165 - [验证Sybase 空字符串到PG] - Task initialization... 
[TRACE] 2025-02-18 19:07:04.167 - [验证Sybase 空字符串到PG] - Start task milestones: 67b4472ccc3e3c6b7743d6ac(验证Sybase 空字符串到PG) 
[INFO ] 2025-02-18 19:07:04.289 - [验证Sybase 空字符串到PG] - Loading table structure completed 
[TRACE] 2025-02-18 19:07:04.289 - [验证Sybase 空字符串到PG] - Node performs snapshot read asynchronously 
[TRACE] 2025-02-18 19:07:04.337 - [验证Sybase 空字符串到PG] - The engine receives 验证Sybase 空字符串到PG task data from TM and will continue to run tasks by jet 
[INFO ] 2025-02-18 19:07:04.337 - [验证Sybase 空字符串到PG] - Task started 
[TRACE] 2025-02-18 19:07:04.367 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] start preload schema,table counts: 2 
[TRACE] 2025-02-18 19:07:04.367 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] start preload schema,table counts: 2 
[TRACE] 2025-02-18 19:07:04.367 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] preload schema finished, cost 0 ms 
[TRACE] 2025-02-18 19:07:04.367 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] preload schema finished, cost 0 ms 
[INFO ] 2025-02-18 19:07:05.020 - [验证Sybase 空字符串到PG][Sybase] - Source connector(Sybase) initialization completed 
[TRACE] 2025-02-18 19:07:05.023 - [验证Sybase 空字符串到PG][Sybase] - Source node "Sybase" read batch size: 100 
[TRACE] 2025-02-18 19:07:05.026 - [验证Sybase 空字符串到PG][Sybase] - Source node "Sybase" event queue capacity: 200 
[TRACE] 2025-02-18 19:07:05.026 - [验证Sybase 空字符串到PG][Sybase] - On the first run, the breakpoint will be initialized 
[INFO ] 2025-02-18 19:07:05.026 - [验证Sybase 空字符串到PG][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-02-18 19:07:05.221 - [验证Sybase 空字符串到PG][Sybase] - logs holder not exists, execute ltm valid to make sure ltm is valid 
[INFO ] 2025-02-18 19:07:05.222 - [验证Sybase 空字符串到PG][Sybase] - Use existing stream offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[TRACE] 2025-02-18 19:07:05.222 - [验证Sybase 空字符串到PG][Sybase] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-02-18 19:07:05.286 - [验证Sybase 空字符串到PG][Sybase] - Starting batch read from 2 tables 
[INFO ] 2025-02-18 19:07:05.286 - [验证Sybase 空字符串到PG][PG] - Sink connector(PG) initialization completed 
[TRACE] 2025-02-18 19:07:05.286 - [验证Sybase 空字符串到PG][PG] - Node(PG) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-02-18 19:07:05.286 - [验证Sybase 空字符串到PG][PG] - Write batch size: 100, max wait ms per batch: 500 
[TRACE] 2025-02-18 19:07:05.286 - [验证Sybase 空字符串到PG][Sybase] - Initial sync started 
[INFO ] 2025-02-18 19:07:05.295 - [验证Sybase 空字符串到PG][Sybase] - Starting batch read from table: td_null_col 
[INFO ] 2025-02-18 19:07:05.296 - [验证Sybase 空字符串到PG][PG] - Apply table structure to target database 
[TRACE] 2025-02-18 19:07:05.300 - [验证Sybase 空字符串到PG][Sybase] - Table td_null_col is going to be initial synced 
[INFO ] 2025-02-18 19:07:05.394 - [验证Sybase 空字符串到PG][Sybase] - Table td_null_col has been completed batch read 
[INFO ] 2025-02-18 19:07:05.394 - [验证Sybase 空字符串到PG][Sybase] - Starting batch read from table: td_empty_string 
[TRACE] 2025-02-18 19:07:05.395 - [验证Sybase 空字符串到PG][Sybase] - Table td_empty_string is going to be initial synced 
[TRACE] 2025-02-18 19:07:05.467 - [验证Sybase 空字符串到PG][Sybase] - Query snapshot row size completed: Sybase(21df3819-d4c2-4e2d-94bf-05732f3cc289) 
[INFO ] 2025-02-18 19:07:05.478 - [验证Sybase 空字符串到PG][Sybase] - Table td_empty_string has been completed batch read 
[TRACE] 2025-02-18 19:07:05.478 - [验证Sybase 空字符串到PG][Sybase] - Initial sync completed 
[TRACE] 2025-02-18 19:07:05.480 - [验证Sybase 空字符串到PG][Sybase] - Skip table [td_empty_string] in batch read, reason: last task, this table has been completed batch read 
[TRACE] 2025-02-18 19:07:05.480 - [验证Sybase 空字符串到PG][Sybase] - Initial sync completed 
[INFO ] 2025-02-18 19:07:05.480 - [验证Sybase 空字符串到PG][Sybase] - Batch read completed. 
[TRACE] 2025-02-18 19:07:05.481 - [验证Sybase 空字符串到PG][Sybase] - Incremental sync starting... 
[TRACE] 2025-02-18 19:07:05.482 - [验证Sybase 空字符串到PG][Sybase] - Initial sync completed 
[TRACE] 2025-02-18 19:07:05.482 - [验证Sybase 空字符串到PG][Sybase] - Starting stream read, table list: [td_null_col, td_empty_string], offset: {"startRid":0,"rowId":0,"h":0,"l":0} 
[INFO ] 2025-02-18 19:07:05.482 - [验证Sybase 空字符串到PG][Sybase] - Starting incremental sync using database log parser 
[INFO ] 2025-02-18 19:07:05.568 - [验证Sybase 空字符串到PG][Sybase] - startRid: 368341, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 19:07:05.570 - [验证Sybase 空字符串到PG][Sybase] - startRid: 0, rowId: 0, h: 0, l: 0 
[INFO ] 2025-02-18 19:07:05.570 - [验证Sybase 空字符串到PG][Sybase] - sybase offset in database is: startRid: 368341, rowId: 0, h: 0, l: 0, in tapdata is: startRid: 0, rowId: 0, h: 0, l: 0, database is bigger, will use it 
[INFO ] 2025-02-18 19:07:05.570 - [验证Sybase 空字符串到PG][Sybase] - we will use offset in database, how ever, this is safe: startRid: 368341, rowId: 0, h: 0, l: 0 
[TRACE] 2025-02-18 19:07:05.587 - [验证Sybase 空字符串到PG][Sybase] - Query snapshot row size completed: Sybase(21df3819-d4c2-4e2d-94bf-05732f3cc289) 
[INFO ] 2025-02-18 19:07:05.591 - [验证Sybase 空字符串到PG][Sybase] - sybase cdc work with mode v1: auto rescan 
[INFO ] 2025-02-18 19:07:05.713 - [验证Sybase 空字符串到PG][Sybase] - sp_config_rep_agent disabled, database: lisTest 
[INFO ] 2025-02-18 19:07:05.714 - [验证Sybase 空字符串到PG][Sybase] - turned off automatic log cleaning for sybase 
[INFO ] 2025-02-18 19:07:05.732 - [验证Sybase 空字符串到PG][Sybase] - opened cdc for tables: {dbo=[td_null_col, td_empty_string]} 
[INFO ] 2025-02-18 19:07:05.732 - [验证Sybase 空字符串到PG][Sybase] - check logs holder sql is: select * from master..syslogshold where name='$replication_truncation_point'
and dbid in (select dbid from master..sysdatabases where name='lisTest') 
[INFO ] 2025-02-18 19:07:05.902 - [验证Sybase 空字符串到PG][Sybase] - logs holder exists, will skip valid operation 
[INFO ] 2025-02-18 19:07:05.902 - [验证Sybase 空字符串到PG][Sybase] - trans timestamp offset: 28800000 
[INFO ] 2025-02-18 19:07:05.902 - [验证Sybase 空字符串到PG][Sybase] - sybase cdc debug log is disabled 
[INFO ] 2025-02-18 19:07:05.902 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 0 
[INFO ] 2025-02-18 19:07:08.940 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 0 
[INFO ] 2025-02-18 19:07:08.998 - [验证Sybase 空字符串到PG][Sybase] - uncommit trans size: 0 
[INFO ] 2025-02-18 19:07:08.998 - [验证Sybase 空字符串到PG][Sybase] - uncommit trans: {} 
[INFO ] 2025-02-18 19:07:09.007 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[TRACE] 2025-02-18 19:07:09.614 - [验证Sybase 空字符串到PG][PG] - Table 'td_null_col' has duplicate key error, switch the insert policy to update_on_exists and retry writing, continuous error time: 0 
[INFO ] 2025-02-18 19:07:12.012 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:07:12.112 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:07:15.143 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:07:15.348 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:07:18.331 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:07:18.332 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:07:21.332 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:07:21.538 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:07:24.549 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:07:24.549 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:07:27.554 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:07:27.755 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:07:30.770 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:07:30.770 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:07:33.771 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:07:33.973 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:07:37.014 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:07:37.014 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:07:40.018 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:07:40.160 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:07:43.161 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:07:43.364 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:07:46.402 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:07:46.607 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:07:49.564 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:07:49.565 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:07:52.566 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:07:52.767 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:07:55.808 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:07:55.809 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:07:58.814 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:07:59.016 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:08:02.000 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:08:02.000 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:08:05.005 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:08:05.210 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:08:08.236 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:08:08.236 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:08:11.242 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:08:11.379 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:08:14.416 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:08:14.617 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:08:17.577 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:08:17.578 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:08:20.583 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:08:20.783 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:08:23.778 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:08:23.778 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:08:26.785 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:08:26.987 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:08:29.993 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:08:29.993 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:08:32.993 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:08:33.198 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:08:36.230 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:08:36.230 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:08:39.233 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:08:39.435 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:08:42.448 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:08:42.448 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:08:45.453 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:08:45.654 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:08:48.655 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:08:48.655 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:08:51.660 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:08:51.862 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:08:54.857 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:08:54.857 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:08:57.862 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:08:58.067 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:09:01.050 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:09:01.050 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:09:04.056 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:09:04.256 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:09:07.285 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:09:07.491 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:09:10.311 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:09:10.427 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:09:13.433 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:09:13.638 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:09:16.643 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:09:16.644 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:09:19.649 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:09:19.852 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:09:22.874 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:09:22.874 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:09:25.880 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:09:26.084 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:09:29.094 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:09:29.095 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:09:32.100 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:09:32.301 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:09:35.310 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:09:35.310 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:09:38.317 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:09:38.522 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:09:41.570 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:09:41.776 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:09:44.612 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:09:44.813 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:09:47.789 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:09:47.789 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:09:50.794 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:09:51.198 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:09:54.037 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:09:54.242 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:09:57.204 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:09:57.204 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:10:00.209 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:10:00.414 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:10:03.421 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:10:03.422 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:10:06.428 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:10:06.628 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:10:09.620 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:10:09.621 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:10:12.626 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:10:12.828 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:10:15.818 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:10:15.818 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:10:18.819 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:10:19.022 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:10:22.005 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:10:22.005 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:10:25.010 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:10:25.212 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:10:28.195 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:10:28.195 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:10:31.200 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:10:31.405 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:10:34.391 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:10:34.391 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:10:37.396 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:10:37.599 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:10:40.590 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:10:40.590 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:10:43.596 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:10:43.798 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:10:46.779 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:10:46.779 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:10:49.785 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:10:49.986 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[INFO ] 2025-02-18 19:10:52.988 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[INFO ] 2025-02-18 19:10:52.988 - [验证Sybase 空字符串到PG][Sybase] - normal rescan, will sleep 3s, and scan from startRid: 368341, rowId: 83 
[TRACE] 2025-02-18 19:10:54.922 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] running status set to false 
[INFO ] 2025-02-18 19:10:54.933 - [验证Sybase 空字符串到PG][Sybase] - Log Miner is shutting down... 
[INFO ] 2025-02-18 19:10:54.953 - [验证Sybase 空字符串到PG][Sybase] - rebuild statement with 368341, 83 
[TRACE] 2025-02-18 19:10:55.037 - [验证Sybase 空字符串到PG][Sybase] - PDK connector node stopped: HazelcastSourcePdkDataNode_21df3819-d4c2-4e2d-94bf-05732f3cc289_1739876824944 
[TRACE] 2025-02-18 19:10:55.037 - [验证Sybase 空字符串到PG][Sybase] - PDK connector node released: HazelcastSourcePdkDataNode_21df3819-d4c2-4e2d-94bf-05732f3cc289_1739876824944 
[TRACE] 2025-02-18 19:10:55.037 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] schema data cleaned 
[TRACE] 2025-02-18 19:10:55.037 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] monitor closed 
[TRACE] 2025-02-18 19:10:55.038 - [验证Sybase 空字符串到PG][Sybase] - Node Sybase[21df3819-d4c2-4e2d-94bf-05732f3cc289] close complete, cost 122 ms 
[TRACE] 2025-02-18 19:10:55.038 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] running status set to false 
[TRACE] 2025-02-18 19:10:55.051 - [验证Sybase 空字符串到PG][Sybase] - Incremental sync completed 
[TRACE] 2025-02-18 19:10:55.051 - [验证Sybase 空字符串到PG][PG] - PDK connector node stopped: HazelcastTargetPdkDataNode_4bf82ce7-6fc2-4db5-b06c-430fb61ba005_1739876825081 
[TRACE] 2025-02-18 19:10:55.051 - [验证Sybase 空字符串到PG][PG] - PDK connector node released: HazelcastTargetPdkDataNode_4bf82ce7-6fc2-4db5-b06c-430fb61ba005_1739876825081 
[TRACE] 2025-02-18 19:10:55.052 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] schema data cleaned 
[TRACE] 2025-02-18 19:10:55.052 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] monitor closed 
[TRACE] 2025-02-18 19:10:55.256 - [验证Sybase 空字符串到PG][PG] - Node PG[4bf82ce7-6fc2-4db5-b06c-430fb61ba005] close complete, cost 13 ms 
[TRACE] 2025-02-18 19:10:57.170 - [验证Sybase 空字符串到PG] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-02-18 19:10:57.170 - [验证Sybase 空字符串到PG] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@abd98a4 
[TRACE] 2025-02-18 19:10:57.171 - [验证Sybase 空字符串到PG] - Stop task milestones: 67b4472ccc3e3c6b7743d6ac(验证Sybase 空字符串到PG)  
[TRACE] 2025-02-18 19:10:57.304 - [验证Sybase 空字符串到PG] - Stopped task aspect(s) 
[TRACE] 2025-02-18 19:10:57.304 - [验证Sybase 空字符串到PG] - Snapshot order controller have been removed 
[INFO ] 2025-02-18 19:10:57.304 - [验证Sybase 空字符串到PG] - Task stopped. 
[TRACE] 2025-02-18 19:10:57.332 - [验证Sybase 空字符串到PG] - Remove memory task client succeed, task: 验证Sybase 空字符串到PG[67b4472ccc3e3c6b7743d6ac] 
[TRACE] 2025-02-18 19:10:57.336 - [验证Sybase 空字符串到PG] - Destroy memory task client cache succeed, task: 验证Sybase 空字符串到PG[67b4472ccc3e3c6b7743d6ac] 
