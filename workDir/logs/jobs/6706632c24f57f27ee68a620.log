[INFO ] 2024-10-10 15:52:22.923 - [模拟创建索引失败] - Start task milestones: 6706632c24f57f27ee68a620(模拟创建索引失败) 
[INFO ] 2024-10-10 15:52:23.115 - [模拟创建索引失败] - Task initialization... 
[INFO ] 2024-10-10 15:52:23.115 - [模拟创建索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-10 15:52:23.215 - [模拟创建索引失败] - The engine receives 模拟创建索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-10 15:52:23.215 - [模拟创建索引失败][testIndex] - Node testIndex[52e25dfe-285b-4de6-8194-755955a5a11b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 15:52:23.215 - [模拟创建索引失败][testIndex] - Node testIndex[0b3b10cd-3264-4ea9-83f5-2a1c9519be1e] start preload schema,table counts: 1 
[INFO ] 2024-10-10 15:52:23.215 - [模拟创建索引失败][testIndex] - Node testIndex[0b3b10cd-3264-4ea9-83f5-2a1c9519be1e] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 15:52:23.215 - [模拟创建索引失败][testIndex] - Node testIndex[52e25dfe-285b-4de6-8194-755955a5a11b] preload schema finished, cost 1 ms 
[INFO ] 2024-10-10 15:52:29.377 - [模拟创建索引失败][testIndex] - Source node "testIndex" read batch size: 100 
[INFO ] 2024-10-10 15:52:29.378 - [模拟创建索引失败][testIndex] - Source node "testIndex" event queue capacity: 200 
[INFO ] 2024-10-10 15:52:29.378 - [模拟创建索引失败][testIndex] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-10 15:52:29.389 - [模拟创建索引失败][testIndex] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":2457,"gtidSet":""} 
[INFO ] 2024-10-10 15:52:29.389 - [模拟创建索引失败][testIndex] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-10 15:52:29.436 - [模拟创建索引失败][testIndex] - Initial sync started 
[INFO ] 2024-10-10 15:52:29.436 - [模拟创建索引失败][testIndex] - Starting batch read, table name: testIndex 
[INFO ] 2024-10-10 15:52:29.470 - [模拟创建索引失败][testIndex] - Table testIndex is going to be initial synced 
[INFO ] 2024-10-10 15:52:29.473 - [模拟创建索引失败][testIndex] - Table [testIndex] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-10 15:52:29.481 - [模拟创建索引失败][testIndex] - Query table 'testIndex' counts: 0 
[INFO ] 2024-10-10 15:52:29.482 - [模拟创建索引失败][testIndex] - Initial sync completed 
[INFO ] 2024-10-10 15:52:29.482 - [模拟创建索引失败][testIndex] - Incremental sync starting... 
[INFO ] 2024-10-10 15:52:29.482 - [模拟创建索引失败][testIndex] - Initial sync completed 
[INFO ] 2024-10-10 15:52:29.485 - [模拟创建索引失败][testIndex] - Starting stream read, table list: [testIndex], offset: {"filename":"binlog.000036","position":2457,"gtidSet":""} 
[INFO ] 2024-10-10 15:52:29.589 - [模拟创建索引失败][testIndex] - Starting mysql cdc, server name: 367e28c6-8c55-4f09-b08a-3cbbd8aa2d68 
[INFO ] 2024-10-10 15:52:29.590 - [模拟创建索引失败][testIndex] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"367e28c6-8c55-4f09-b08a-3cbbd8aa2d68","offset":{"{\"server\":\"367e28c6-8c55-4f09-b08a-3cbbd8aa2d68\"}":"{\"file\":\"binlog.000036\",\"pos\":2457,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 229878959
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 367e28c6-8c55-4f09-b08a-3cbbd8aa2d68
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-367e28c6-8c55-4f09-b08a-3cbbd8aa2d68
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 367e28c6-8c55-4f09-b08a-3cbbd8aa2d68
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.testIndex
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-10 15:52:29.996 - [模拟创建索引失败][testIndex] - Connector Mysql incremental start succeed, tables: [testIndex], data change syncing 
[INFO ] 2024-10-10 15:52:34.240 - [模拟创建索引失败][testIndex] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-10 15:52:34.242 - [模拟创建索引失败][testIndex] - Exception skipping - The current exception does not match the skip exception strategy, message: Unknown PDK exception occur, when operate table: testIndex, java.sql.SQLSyntaxErrorException: Key column 'O_W_ID' doesn't exist in table 
[ERROR] 2024-10-10 15:52:34.274 - [模拟创建索引失败][testIndex] - Unknown PDK exception occur, when operate table: testIndex, java.sql.SQLSyntaxErrorException: Key column 'O_W_ID' doesn't exist in table <-- Error Message -->
Unknown PDK exception occur, when operate table: testIndex, java.sql.SQLSyntaxErrorException: Key column 'O_W_ID' doesn't exist in table

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Key column 'O_W_ID' doesn't exist in table
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	...

<-- Full Stack Trace -->
java.sql.SQLSyntaxErrorException: Key column 'O_W_ID' doesn't exist in table
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$5(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:124)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:108)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:76)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$createTargetIndex$8(HazelcastTargetPdkDataNode.java:277)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTargetIndex(HazelcastTargetPdkDataNode.java:272)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:207)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:153)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:141)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:106)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: Key column 'O_W_ID' doesn't exist in table
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:763)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:648)
	at com.zaxxer.hikari.pool.ProxyStatement.execute(ProxyStatement.java:94)
	at com.zaxxer.hikari.pool.HikariProxyStatement.execute(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.batchExecute(JdbcContext.java:166)
	at io.tapdata.common.CommonDbConnector.createIndex(CommonDbConnector.java:404)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$7(HazelcastTargetPdkDataNode.java:279)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 28 more

[INFO ] 2024-10-10 15:52:34.278 - [模拟创建索引失败][testIndex] - Job suspend in error handle 
[INFO ] 2024-10-10 15:52:34.563 - [模拟创建索引失败][testIndex] - Node testIndex[52e25dfe-285b-4de6-8194-755955a5a11b] running status set to false 
[INFO ] 2024-10-10 15:52:34.567 - [模拟创建索引失败][testIndex] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-10 15:52:34.567 - [模拟创建索引失败][testIndex] - Mysql binlog reader stopped 
[INFO ] 2024-10-10 15:52:34.587 - [模拟创建索引失败][testIndex] - Incremental sync completed 
[INFO ] 2024-10-10 15:52:34.588 - [模拟创建索引失败][testIndex] - PDK connector node stopped: HazelcastSourcePdkDataNode-52e25dfe-285b-4de6-8194-755955a5a11b 
[INFO ] 2024-10-10 15:52:34.588 - [模拟创建索引失败][testIndex] - PDK connector node released: HazelcastSourcePdkDataNode-52e25dfe-285b-4de6-8194-755955a5a11b 
[INFO ] 2024-10-10 15:52:34.588 - [模拟创建索引失败][testIndex] - Node testIndex[52e25dfe-285b-4de6-8194-755955a5a11b] schema data cleaned 
[INFO ] 2024-10-10 15:52:34.588 - [模拟创建索引失败][testIndex] - Node testIndex[52e25dfe-285b-4de6-8194-755955a5a11b] monitor closed 
[INFO ] 2024-10-10 15:52:34.594 - [模拟创建索引失败][testIndex] - Node testIndex[52e25dfe-285b-4de6-8194-755955a5a11b] close complete, cost 106 ms 
[INFO ] 2024-10-10 15:52:34.598 - [模拟创建索引失败][testIndex] - Node testIndex[0b3b10cd-3264-4ea9-83f5-2a1c9519be1e] running status set to false 
[INFO ] 2024-10-10 15:52:34.602 - [模拟创建索引失败][testIndex] - PDK connector node stopped: HazelcastTargetPdkDataNode-0b3b10cd-3264-4ea9-83f5-2a1c9519be1e 
[INFO ] 2024-10-10 15:52:34.602 - [模拟创建索引失败][testIndex] - PDK connector node released: HazelcastTargetPdkDataNode-0b3b10cd-3264-4ea9-83f5-2a1c9519be1e 
[INFO ] 2024-10-10 15:52:34.602 - [模拟创建索引失败][testIndex] - Node testIndex[0b3b10cd-3264-4ea9-83f5-2a1c9519be1e] schema data cleaned 
[INFO ] 2024-10-10 15:52:34.602 - [模拟创建索引失败][testIndex] - Node testIndex[0b3b10cd-3264-4ea9-83f5-2a1c9519be1e] monitor closed 
[INFO ] 2024-10-10 15:52:34.805 - [模拟创建索引失败][testIndex] - Node testIndex[0b3b10cd-3264-4ea9-83f5-2a1c9519be1e] close complete, cost 8 ms 
[INFO ] 2024-10-10 15:52:37.038 - [模拟创建索引失败] - Task [模拟创建索引失败] cannot retry, reason: Task retry service not start 
[INFO ] 2024-10-10 15:52:37.049 - [模拟创建索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-10 15:52:37.050 - [模拟创建索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@163b98f 
[INFO ] 2024-10-10 15:52:37.168 - [模拟创建索引失败] - Stop task milestones: 6706632c24f57f27ee68a620(模拟创建索引失败)  
[INFO ] 2024-10-10 15:52:37.192 - [模拟创建索引失败] - Stopped task aspect(s) 
[INFO ] 2024-10-10 15:52:37.195 - [模拟创建索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-10-10 15:52:37.209 - [模拟创建索引失败] - Remove memory task client succeed, task: 模拟创建索引失败[6706632c24f57f27ee68a620] 
[INFO ] 2024-10-10 15:52:37.211 - [模拟创建索引失败] - Destroy memory task client cache succeed, task: 模拟创建索引失败[6706632c24f57f27ee68a620] 
[INFO ] 2024-10-10 15:56:03.227 - [模拟创建索引失败] - Start task milestones: 6706632c24f57f27ee68a620(模拟创建索引失败) 
[INFO ] 2024-10-10 15:56:03.310 - [模拟创建索引失败] - Task initialization... 
[INFO ] 2024-10-10 15:56:03.311 - [模拟创建索引失败] - Node performs snapshot read asynchronously 
[INFO ] 2024-10-10 15:56:03.409 - [模拟创建索引失败] - The engine receives 模拟创建索引失败 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-10-10 15:56:03.419 - [模拟创建索引失败][testIndex] - Node testIndex[52e25dfe-285b-4de6-8194-755955a5a11b] start preload schema,table counts: 1 
[INFO ] 2024-10-10 15:56:03.419 - [模拟创建索引失败][testIndex] - Node testIndex1[0b3b10cd-3264-4ea9-83f5-2a1c9519be1e] start preload schema,table counts: 1 
[INFO ] 2024-10-10 15:56:03.419 - [模拟创建索引失败][testIndex] - Node testIndex1[0b3b10cd-3264-4ea9-83f5-2a1c9519be1e] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 15:56:03.420 - [模拟创建索引失败][testIndex] - Node testIndex[52e25dfe-285b-4de6-8194-755955a5a11b] preload schema finished, cost 0 ms 
[INFO ] 2024-10-10 15:56:09.744 - [模拟创建索引失败][testIndex] - Source node "testIndex" read batch size: 100 
[INFO ] 2024-10-10 15:56:09.746 - [模拟创建索引失败][testIndex] - Source node "testIndex" event queue capacity: 200 
[INFO ] 2024-10-10 15:56:09.746 - [模拟创建索引失败][testIndex] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-10-10 15:56:09.757 - [模拟创建索引失败][testIndex] - batch offset found: {},stream offset found: {"filename":"binlog.000036","position":2457,"gtidSet":""} 
[INFO ] 2024-10-10 15:56:09.757 - [模拟创建索引失败][testIndex] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-10-10 15:56:09.805 - [模拟创建索引失败][testIndex] - Initial sync started 
[INFO ] 2024-10-10 15:56:09.814 - [模拟创建索引失败][testIndex] - Starting batch read, table name: testIndex 
[INFO ] 2024-10-10 15:56:09.814 - [模拟创建索引失败][testIndex] - Table testIndex is going to be initial synced 
[INFO ] 2024-10-10 15:56:09.853 - [模拟创建索引失败][testIndex] - Table [testIndex] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-10-10 15:56:09.853 - [模拟创建索引失败][testIndex] - Query table 'testIndex' counts: 0 
[INFO ] 2024-10-10 15:56:09.854 - [模拟创建索引失败][testIndex] - Initial sync completed 
[INFO ] 2024-10-10 15:56:09.854 - [模拟创建索引失败][testIndex] - Incremental sync starting... 
[INFO ] 2024-10-10 15:56:09.854 - [模拟创建索引失败][testIndex] - Initial sync completed 
[INFO ] 2024-10-10 15:56:09.858 - [模拟创建索引失败][testIndex] - Starting stream read, table list: [testIndex], offset: {"filename":"binlog.000036","position":2457,"gtidSet":""} 
[INFO ] 2024-10-10 15:56:09.938 - [模拟创建索引失败][testIndex] - Starting mysql cdc, server name: 45d7def5-5099-4563-81d6-e3737e02e8de 
[INFO ] 2024-10-10 15:56:09.939 - [模拟创建索引失败][testIndex] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  max.queue.size: 800
  pdk.offset.string: {"name":"45d7def5-5099-4563-81d6-e3737e02e8de","offset":{"{\"server\":\"45d7def5-5099-4563-81d6-e3737e02e8de\"}":"{\"file\":\"binlog.000036\",\"pos\":2457,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.captured.tables.ddl: true
  converters: geometry
  database.user: root
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 492773819
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 45d7def5-5099-4563-81d6-e3737e02e8de
  geometry.schema.name: io.debezium.mysql.type.Geometry
  database.port: 3306
  threadName: Debezium-Mysql-Connector-45d7def5-5099-4563-81d6-e3737e02e8de
  enable.time.adjuster: false
  geometry.type: io.tapdata.connector.mysql.GeometryConverter
  database.hostname: localhost
  database.password: ********
  name: 45d7def5-5099-4563-81d6-e3737e02e8de
  database.history.store.only.monitored.tables.ddl: true
  table.include.list: test.testIndex
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-10-10 15:56:10.342 - [模拟创建索引失败][testIndex] - Connector Mysql incremental start succeed, tables: [testIndex], data change syncing 
[INFO ] 2024-10-10 15:56:14.361 - [模拟创建索引失败][testIndex] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-10-10 15:56:14.475 - [模拟创建索引失败][testIndex] - Exception skipping - The current exception does not match the skip exception strategy, message: Table name: testIndex1 
[ERROR] 2024-10-10 15:56:14.497 - [模拟创建索引失败][testIndex] - Table name: testIndex1 <-- Error Message -->
Table name: testIndex1

<-- Simple Stack Trace -->
Caused by: java.lang.NullPointerException: null
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:340)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:209)
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:153)
	io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	...

<-- Full Stack Trace -->
Table name: testIndex1
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:378)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.createTable(HazelcastTargetPdkDataNode.java:209)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$initTargetDB$2(HazelcastTargetPdkDataNode.java:153)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:146)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.initTargetDB(HazelcastTargetPdkDataNode.java:141)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.doInit(HazelcastTargetPdkDataNode.java:106)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:215)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.NullPointerException
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.syncIndex(HazelcastTargetPdkDataNode.java:340)
	... 19 more

[INFO ] 2024-10-10 15:56:14.498 - [模拟创建索引失败][testIndex] - Job suspend in error handle 
[INFO ] 2024-10-10 15:56:14.855 - [模拟创建索引失败][testIndex] - Node testIndex[52e25dfe-285b-4de6-8194-755955a5a11b] running status set to false 
[INFO ] 2024-10-10 15:56:14.960 - [模拟创建索引失败][testIndex] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-10-10 15:56:14.969 - [模拟创建索引失败][testIndex] - Mysql binlog reader stopped 
[INFO ] 2024-10-10 15:56:14.970 - [模拟创建索引失败][testIndex] - Incremental sync completed 
[INFO ] 2024-10-10 15:56:14.994 - [模拟创建索引失败][testIndex] - PDK connector node stopped: HazelcastSourcePdkDataNode-52e25dfe-285b-4de6-8194-755955a5a11b 
[INFO ] 2024-10-10 15:56:14.994 - [模拟创建索引失败][testIndex] - PDK connector node released: HazelcastSourcePdkDataNode-52e25dfe-285b-4de6-8194-755955a5a11b 
[INFO ] 2024-10-10 15:56:14.994 - [模拟创建索引失败][testIndex] - Node testIndex[52e25dfe-285b-4de6-8194-755955a5a11b] schema data cleaned 
[INFO ] 2024-10-10 15:56:14.994 - [模拟创建索引失败][testIndex] - Node testIndex[52e25dfe-285b-4de6-8194-755955a5a11b] monitor closed 
[INFO ] 2024-10-10 15:56:14.997 - [模拟创建索引失败][testIndex] - Node testIndex[52e25dfe-285b-4de6-8194-755955a5a11b] close complete, cost 149 ms 
[INFO ] 2024-10-10 15:56:14.997 - [模拟创建索引失败][testIndex] - Node testIndex1[0b3b10cd-3264-4ea9-83f5-2a1c9519be1e] running status set to false 
[INFO ] 2024-10-10 15:56:15.009 - [模拟创建索引失败][testIndex] - PDK connector node stopped: HazelcastTargetPdkDataNode-0b3b10cd-3264-4ea9-83f5-2a1c9519be1e 
[INFO ] 2024-10-10 15:56:15.009 - [模拟创建索引失败][testIndex] - PDK connector node released: HazelcastTargetPdkDataNode-0b3b10cd-3264-4ea9-83f5-2a1c9519be1e 
[INFO ] 2024-10-10 15:56:15.009 - [模拟创建索引失败][testIndex] - Node testIndex1[0b3b10cd-3264-4ea9-83f5-2a1c9519be1e] schema data cleaned 
[INFO ] 2024-10-10 15:56:15.009 - [模拟创建索引失败][testIndex] - Node testIndex1[0b3b10cd-3264-4ea9-83f5-2a1c9519be1e] monitor closed 
[INFO ] 2024-10-10 15:56:15.010 - [模拟创建索引失败][testIndex] - Node testIndex1[0b3b10cd-3264-4ea9-83f5-2a1c9519be1e] close complete, cost 12 ms 
[INFO ] 2024-10-10 15:56:17.377 - [模拟创建索引失败] - Task [模拟创建索引失败] cannot retry, reason: Task retry service not start 
[INFO ] 2024-10-10 15:56:17.392 - [模拟创建索引失败] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-10-10 15:56:17.393 - [模拟创建索引失败] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@f4c8540 
[INFO ] 2024-10-10 15:56:17.549 - [模拟创建索引失败] - Stop task milestones: 6706632c24f57f27ee68a620(模拟创建索引失败)  
[INFO ] 2024-10-10 15:56:17.549 - [模拟创建索引失败] - Stopped task aspect(s) 
[INFO ] 2024-10-10 15:56:17.568 - [模拟创建索引失败] - Snapshot order controller have been removed 
[INFO ] 2024-10-10 15:56:17.570 - [模拟创建索引失败] - Remove memory task client succeed, task: 模拟创建索引失败[6706632c24f57f27ee68a620] 
[INFO ] 2024-10-10 15:56:17.570 - [模拟创建索引失败] - Destroy memory task client cache succeed, task: 模拟创建索引失败[6706632c24f57f27ee68a620] 
