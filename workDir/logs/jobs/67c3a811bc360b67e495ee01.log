[TRACE] 2025-03-02 12:09:27.807 - [Pg~SybaseTimestamp] - Task initialization... 
[TRACE] 2025-03-02 12:09:27.808 - [Pg~SybaseTimestamp] - Start task milestones: 67c3a811bc360b67e495ee01(Pg~SybaseTimestamp) 
[INFO ] 2025-03-02 12:09:27.982 - [Pg~SybaseTimestamp] - Loading table structure completed 
[TRACE] 2025-03-02 12:09:27.982 - [Pg~SybaseTimestamp] - <PERSON><PERSON> performs snapshot read asynchronously 
[TRACE] 2025-03-02 12:09:28.042 - [Pg~SybaseTimestamp] - The engine receives Pg~SybaseTimestamp task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-02 12:09:28.042 - [Pg~SybaseTimestamp] - Task started 
[TRACE] 2025-03-02 12:09:28.069 - [Pg~SybaseTimestamp][Sybase] - Node Sybase[8a485737-d2ed-4385-873c-b7a24d454b30] start preload schema,table counts: 3 
[TRACE] 2025-03-02 12:09:28.070 - [Pg~SybaseTimestamp][Pg] - Node Pg[7843ae3f-0ec6-4efa-b225-4a41063995bd] start preload schema,table counts: 3 
[TRACE] 2025-03-02 12:09:28.070 - [Pg~SybaseTimestamp][Pg] - Node Pg[7843ae3f-0ec6-4efa-b225-4a41063995bd] preload schema finished, cost 0 ms 
[TRACE] 2025-03-02 12:09:28.070 - [Pg~SybaseTimestamp][Sybase] - Node Sybase[8a485737-d2ed-4385-873c-b7a24d454b30] preload schema finished, cost 0 ms 
[INFO ] 2025-03-02 12:09:28.070 - [Pg~SybaseTimestamp][Pg] - Enable partition table support for source database 
[INFO ] 2025-03-02 12:09:28.750 - [Pg~SybaseTimestamp][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-03-02 12:09:28.751 - [Pg~SybaseTimestamp][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-02 12:09:28.751 - [Pg~SybaseTimestamp][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-02 12:09:28.958 - [Pg~SybaseTimestamp][Sybase] - Apply table structure to target database 
[TRACE] 2025-03-02 12:09:29.159 - [Pg~SybaseTimestamp][Sybase] - The table testTimeStampWithIndex has already exist. 
[INFO ] 2025-03-02 12:09:29.418 - [Pg~SybaseTimestamp][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-03-02 12:09:29.418 - [Pg~SybaseTimestamp][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-03-02 12:09:29.418 - [Pg~SybaseTimestamp][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-03-02 12:09:29.418 - [Pg~SybaseTimestamp][Pg] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-03-02 12:09:29.873 - [Pg~SybaseTimestamp][Sybase] - Create index failed Column name '_no_pk_hash' does not exist in target table.
, please execute it manually [create index  IDX_pWithIndexc091010443ee  on  lisTest.dbo.testTimeStampWithIndex ( _no_pk_hash  asc)] 
[TRACE] 2025-03-02 12:09:30.001 - [Pg~SybaseTimestamp][Sybase] - The table testTimeStampWithNoIndex has already exist. 
[WARN ] 2025-03-02 12:09:30.147 - [Pg~SybaseTimestamp][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 12:09:30.233 - [Pg~SybaseTimestamp][Pg] - new logical replication slot created, slotName:tapdata_cdc_da968e09_0443_4a8a_af7f_96d9fdba0157 
[INFO ] 2025-03-02 12:09:30.235 - [Pg~SybaseTimestamp][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-03-02 12:09:30.235 - [Pg~SybaseTimestamp][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-03-02 12:09:30.364 - [Pg~SybaseTimestamp][Pg] - Starting batch read from 3 tables 
[TRACE] 2025-03-02 12:09:30.364 - [Pg~SybaseTimestamp][Pg] - Initial sync started 
[INFO ] 2025-03-02 12:09:30.365 - [Pg~SybaseTimestamp][Pg] - Starting batch read from table: testTimeStampWithIndex 
[TRACE] 2025-03-02 12:09:30.365 - [Pg~SybaseTimestamp][Pg] - Table testTimeStampWithIndex is going to be initial synced 
[WARN ] 2025-03-02 12:09:30.566 - [Pg~SybaseTimestamp][Pg] - [Auto Retry] Method (source_batch_read) encountered an error, triggering auto retry.
 - Error code: 18001, message: Caused by: org.postgresql.util.PSQLException: ERROR: column "_no_pk_hash" does not exist
  位置：36
	org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2675)
	org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2365)
	org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	...
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2025-03-02 12:09:30.609 - [Pg~SybaseTimestamp][Pg] - Retry operation SOURCE_BATCH_READ, retry times 1/15, first retry time 2025-03-02 12:09:30, next retry time 2025-03-02 12:10:30 
[TRACE] 2025-03-02 12:09:30.609 - [Pg~SybaseTimestamp][Pg] - Query snapshot row size completed: Pg(7843ae3f-0ec6-4efa-b225-4a41063995bd) 
[WARN ] 2025-03-02 12:09:30.815 - [Pg~SybaseTimestamp][Sybase] - Create index failed Column name '_no_pk_hash' does not exist in target table.
, please execute it manually [create index  IDX_ithNoIndex452c4abdecfa  on  lisTest.dbo.testTimeStampWithNoIndex ( _no_pk_hash  asc)] 
[TRACE] 2025-03-02 12:09:31.223 - [Pg~SybaseTimestamp][Sybase] - The table testTimeStamp has already exist. 
[TRACE] 2025-03-02 12:10:03.895 - [Pg~SybaseTimestamp][Pg] - Node Pg[7843ae3f-0ec6-4efa-b225-4a41063995bd] running status set to false 
[TRACE] 2025-03-02 12:10:03.926 - [Pg~SybaseTimestamp][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_7843ae3f-0ec6-4efa-b225-4a41063995bd_1740888568584 
[TRACE] 2025-03-02 12:10:03.926 - [Pg~SybaseTimestamp][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_7843ae3f-0ec6-4efa-b225-4a41063995bd_1740888568584 
[TRACE] 2025-03-02 12:10:03.926 - [Pg~SybaseTimestamp][Pg] - Node Pg[7843ae3f-0ec6-4efa-b225-4a41063995bd] schema data cleaned 
[INFO ] 2025-03-02 12:10:03.926 - [Pg~SybaseTimestamp][Pg] - Retry operation SOURCE_BATCH_READ failed, total cost 00:00:33.406000 
[TRACE] 2025-03-02 12:10:03.926 - [Pg~SybaseTimestamp][Pg] - Node Pg[7843ae3f-0ec6-4efa-b225-4a41063995bd] monitor closed 
[TRACE] 2025-03-02 12:10:03.930 - [Pg~SybaseTimestamp][Pg] - Node Pg[7843ae3f-0ec6-4efa-b225-4a41063995bd] close complete, cost 40 ms 
[TRACE] 2025-03-02 12:10:03.930 - [Pg~SybaseTimestamp][Sybase] - Node Sybase[8a485737-d2ed-4385-873c-b7a24d454b30] running status set to false 
[TRACE] 2025-03-02 12:10:03.954 - [Pg~SybaseTimestamp][Pg] - Initial sync completed 
[TRACE] 2025-03-02 12:10:03.956 - [Pg~SybaseTimestamp][Pg] - Exception skipping - The current exception does not match the skip exception strategy, message: PDK retry exception (Server Error Code null): when operate table: unknown, java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-37) has been closed. 
[ERROR] 2025-03-02 12:10:04.163 - [Pg~SybaseTimestamp][Pg] - PDK retry exception (Server Error Code null): when operate table: unknown, java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-37) has been closed. <-- Error Message -->
PDK retry exception (Server Error Code null): when operate table: unknown, java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-37) has been closed.

<-- Simple Stack Trace -->
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-37) has been closed.
	com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	io.tapdata.common.CommonDbConnector.batchReadWithoutHashSplit(CommonDbConnector.java:706)
	io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:700)
	...

<-- Full Stack Trace -->
java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-37) has been closed.
	at io.tapdata.connector.postgres.exception.PostgresExceptionCollector.revealException(PostgresExceptionCollector.java:151)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:51)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:89)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutHashSplit(CommonDbConnector.java:706)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:700)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:577)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:71)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotInvoke$13(HazelcastSourcePdkDataNode.java:498)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:163)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotInvoke(HazelcastSourcePdkDataNode.java:490)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:412)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$3(HazelcastSourcePdkDataNode.java:355)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:149)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:356)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:269)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLException: HikariDataSource HikariDataSource (HikariPool-37) has been closed.
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:96)
	at io.tapdata.common.JdbcContext.getConnection(JdbcContext.java:48)
	... 26 more

[TRACE] 2025-03-02 12:10:04.360 - [Pg~SybaseTimestamp][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_8a485737-d2ed-4385-873c-b7a24d454b30_1740888568671 
[TRACE] 2025-03-02 12:10:04.361 - [Pg~SybaseTimestamp][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_8a485737-d2ed-4385-873c-b7a24d454b30_1740888568671 
[TRACE] 2025-03-02 12:10:04.361 - [Pg~SybaseTimestamp][Sybase] - Node Sybase[8a485737-d2ed-4385-873c-b7a24d454b30] schema data cleaned 
[TRACE] 2025-03-02 12:10:04.363 - [Pg~SybaseTimestamp][Sybase] - Node Sybase[8a485737-d2ed-4385-873c-b7a24d454b30] monitor closed 
[TRACE] 2025-03-02 12:10:04.363 - [Pg~SybaseTimestamp][Sybase] - Node Sybase[8a485737-d2ed-4385-873c-b7a24d454b30] close complete, cost 433 ms 
[TRACE] 2025-03-02 12:10:06.325 - [Pg~SybaseTimestamp] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-02 12:10:06.325 - [Pg~SybaseTimestamp] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@6356367e 
[TRACE] 2025-03-02 12:10:06.327 - [Pg~SybaseTimestamp] - Stop task milestones: 67c3a811bc360b67e495ee01(Pg~SybaseTimestamp)  
[TRACE] 2025-03-02 12:10:06.470 - [Pg~SybaseTimestamp] - Stopped task aspect(s) 
[TRACE] 2025-03-02 12:10:06.471 - [Pg~SybaseTimestamp] - Snapshot order controller have been removed 
[INFO ] 2025-03-02 12:10:06.471 - [Pg~SybaseTimestamp] - Task stopped. 
[TRACE] 2025-03-02 12:10:06.495 - [Pg~SybaseTimestamp] - Remove memory task client succeed, task: Pg~SybaseTimestamp[67c3a811bc360b67e495ee01] 
[TRACE] 2025-03-02 12:10:06.495 - [Pg~SybaseTimestamp] - Destroy memory task client cache succeed, task: Pg~SybaseTimestamp[67c3a811bc360b67e495ee01] 
[TRACE] 2025-03-02 12:11:33.568 - [Pg~SybaseTimestamp] - Task initialization... 
[TRACE] 2025-03-02 12:11:33.685 - [Pg~SybaseTimestamp] - Start task milestones: 67c3a811bc360b67e495ee01(Pg~SybaseTimestamp) 
[INFO ] 2025-03-02 12:11:33.685 - [Pg~SybaseTimestamp] - Loading table structure completed 
[TRACE] 2025-03-02 12:11:33.786 - [Pg~SybaseTimestamp] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-02 12:11:33.861 - [Pg~SybaseTimestamp] - The engine receives Pg~SybaseTimestamp task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-02 12:11:33.862 - [Pg~SybaseTimestamp] - Task started 
[TRACE] 2025-03-02 12:11:33.913 - [Pg~SybaseTimestamp][Pg] - Node Pg[7843ae3f-0ec6-4efa-b225-4a41063995bd] start preload schema,table counts: 3 
[TRACE] 2025-03-02 12:11:33.913 - [Pg~SybaseTimestamp][Sybase] - Node Sybase[8a485737-d2ed-4385-873c-b7a24d454b30] start preload schema,table counts: 3 
[TRACE] 2025-03-02 12:11:33.913 - [Pg~SybaseTimestamp][Pg] - Node Pg[7843ae3f-0ec6-4efa-b225-4a41063995bd] preload schema finished, cost 0 ms 
[TRACE] 2025-03-02 12:11:33.913 - [Pg~SybaseTimestamp][Sybase] - Node Sybase[8a485737-d2ed-4385-873c-b7a24d454b30] preload schema finished, cost 0 ms 
[INFO ] 2025-03-02 12:11:33.913 - [Pg~SybaseTimestamp][Pg] - Enable partition table support for source database 
[INFO ] 2025-03-02 12:11:34.511 - [Pg~SybaseTimestamp][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-03-02 12:11:34.519 - [Pg~SybaseTimestamp][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-02 12:11:34.519 - [Pg~SybaseTimestamp][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-02 12:11:34.521 - [Pg~SybaseTimestamp][Sybase] - Apply table structure to target database 
[TRACE] 2025-03-02 12:11:34.996 - [Pg~SybaseTimestamp][Sybase] - The table testTimeStampWithIndex has already exist. 
[INFO ] 2025-03-02 12:11:35.063 - [Pg~SybaseTimestamp][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-03-02 12:11:35.064 - [Pg~SybaseTimestamp][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-03-02 12:11:35.064 - [Pg~SybaseTimestamp][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-03-02 12:11:35.065 - [Pg~SybaseTimestamp][Pg] - On the first run, the breakpoint will be initialized 
[WARN ] 2025-03-02 12:11:35.659 - [Pg~SybaseTimestamp][Sybase] - Create index failed Column name '_no_pk_hash' does not exist in target table.
, please execute it manually [create index  IDX_pWithIndex09d11bd7dc2e  on  lisTest.dbo.testTimeStampWithIndex ( _no_pk_hash  asc)] 
[TRACE] 2025-03-02 12:11:35.915 - [Pg~SybaseTimestamp][Sybase] - The table testTimeStampWithNoIndex has already exist. 
[WARN ] 2025-03-02 12:11:36.299 - [Pg~SybaseTimestamp][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 12:11:36.364 - [Pg~SybaseTimestamp][Pg] - new logical replication slot created, slotName:tapdata_cdc_1909da4a_3b45_4c72_aa73_31eb0c1d35c5 
[INFO ] 2025-03-02 12:11:36.366 - [Pg~SybaseTimestamp][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-03-02 12:11:36.366 - [Pg~SybaseTimestamp][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2025-03-02 12:11:36.447 - [Pg~SybaseTimestamp][Pg] - Starting batch read from 3 tables 
[TRACE] 2025-03-02 12:11:36.448 - [Pg~SybaseTimestamp][Pg] - Initial sync started 
[INFO ] 2025-03-02 12:11:36.449 - [Pg~SybaseTimestamp][Pg] - Starting batch read from table: testTimeStampWithIndex 
[TRACE] 2025-03-02 12:11:36.449 - [Pg~SybaseTimestamp][Pg] - Table testTimeStampWithIndex is going to be initial synced 
[INFO ] 2025-03-02 12:11:36.565 - [Pg~SybaseTimestamp][Pg] - Table testTimeStampWithIndex has been completed batch read 
[INFO ] 2025-03-02 12:11:36.566 - [Pg~SybaseTimestamp][Pg] - Starting batch read from table: testTimeStampWithNoIndex 
[TRACE] 2025-03-02 12:11:36.566 - [Pg~SybaseTimestamp][Pg] - Table testTimeStampWithNoIndex is going to be initial synced 
[INFO ] 2025-03-02 12:11:36.665 - [Pg~SybaseTimestamp][Pg] - Table testTimeStampWithNoIndex has been completed batch read 
[INFO ] 2025-03-02 12:11:36.665 - [Pg~SybaseTimestamp][Pg] - Starting batch read from table: testTimeStamp 
[TRACE] 2025-03-02 12:11:36.665 - [Pg~SybaseTimestamp][Pg] - Table testTimeStamp is going to be initial synced 
[WARN ] 2025-03-02 12:11:36.729 - [Pg~SybaseTimestamp][Sybase] - Create index failed Column name '_no_pk_hash' does not exist in target table.
, please execute it manually [create index  IDX_ithNoIndex021292472fb1  on  lisTest.dbo.testTimeStampWithNoIndex ( _no_pk_hash  asc)] 
[TRACE] 2025-03-02 12:11:36.729 - [Pg~SybaseTimestamp][Pg] - Query snapshot row size completed: Pg(7843ae3f-0ec6-4efa-b225-4a41063995bd) 
[INFO ] 2025-03-02 12:11:36.782 - [Pg~SybaseTimestamp][Pg] - Table testTimeStamp has been completed batch read 
[TRACE] 2025-03-02 12:11:36.783 - [Pg~SybaseTimestamp][Pg] - Initial sync completed 
[INFO ] 2025-03-02 12:11:36.783 - [Pg~SybaseTimestamp][Pg] - Batch read completed. 
[TRACE] 2025-03-02 12:11:36.785 - [Pg~SybaseTimestamp][Pg] - Incremental sync starting... 
[TRACE] 2025-03-02 12:11:36.787 - [Pg~SybaseTimestamp][Pg] - Initial sync completed 
[TRACE] 2025-03-02 12:11:36.788 - [Pg~SybaseTimestamp][Pg] - Starting stream read, table list: [testTimeStampWithIndex, testTimeStampWithNoIndex, testTimeStamp], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-03-02 12:11:36.788 - [Pg~SybaseTimestamp][Pg] - Starting incremental sync using database log parser 
[WARN ] 2025-03-02 12:11:36.844 - [Pg~SybaseTimestamp][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 12:11:36.845 - [Pg~SybaseTimestamp][Pg] - Using an existing logical replication slot, slotName:tapdata_cdc_1909da4a_3b45_4c72_aa73_31eb0c1d35c5 
[TRACE] 2025-03-02 12:11:37.250 - [Pg~SybaseTimestamp][Sybase] - The table testTimeStamp has already exist. 
[TRACE] 2025-03-02 12:11:38.812 - [Pg~SybaseTimestamp][Pg] - Connector PostgreSQL incremental start succeed, tables: [testTimeStampWithIndex, testTimeStampWithNoIndex, testTimeStamp], data change syncing 
[TRACE] 2025-03-02 12:11:39.236 - [Pg~SybaseTimestamp][Sybase] - Process after table "testTimeStamp" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-02 12:11:39.236 - [Pg~SybaseTimestamp][Sybase] - Process after table "testTimeStampWithIndex" initial sync finished, cost: 1 ms 
[TRACE] 2025-03-02 12:11:39.237 - [Pg~SybaseTimestamp][Sybase] - Process after table "testTimeStampWithNoIndex" initial sync finished, cost: 1 ms 
[INFO ] 2025-03-02 12:11:39.441 - [Pg~SybaseTimestamp][Sybase] - Process after all table(s) initial sync are finished，table number: 3 
[TRACE] 2025-03-02 12:24:37.755 - [Pg~SybaseTimestamp][Pg] - Node Pg[7843ae3f-0ec6-4efa-b225-4a41063995bd] running status set to false 
[TRACE] 2025-03-02 12:26:42.570 - [Pg~SybaseTimestamp] - Task initialization... 
[TRACE] 2025-03-02 12:26:42.777 - [Pg~SybaseTimestamp] - Start task milestones: 67c3a811bc360b67e495ee01(Pg~SybaseTimestamp) 
[INFO ] 2025-03-02 12:26:43.521 - [Pg~SybaseTimestamp] - Loading table structure completed 
[TRACE] 2025-03-02 12:26:43.522 - [Pg~SybaseTimestamp] - Node performs snapshot read asynchronously 
[TRACE] 2025-03-02 12:26:43.727 - [Pg~SybaseTimestamp] - The engine receives Pg~SybaseTimestamp task data from TM and will continue to run tasks by jet 
[INFO ] 2025-03-02 12:26:43.799 - [Pg~SybaseTimestamp] - Task started 
[TRACE] 2025-03-02 12:26:43.914 - [Pg~SybaseTimestamp][Pg] - Node Pg[7843ae3f-0ec6-4efa-b225-4a41063995bd] start preload schema,table counts: 3 
[TRACE] 2025-03-02 12:26:43.915 - [Pg~SybaseTimestamp][Sybase] - Node Sybase[a57d80fb-fe4b-4d77-acef-209c206b4423] start preload schema,table counts: 3 
[TRACE] 2025-03-02 12:26:43.915 - [Pg~SybaseTimestamp][Sybase] - Node Sybase[a57d80fb-fe4b-4d77-acef-209c206b4423] preload schema finished, cost 0 ms 
[TRACE] 2025-03-02 12:26:43.922 - [Pg~SybaseTimestamp][Pg] - Node Pg[7843ae3f-0ec6-4efa-b225-4a41063995bd] preload schema finished, cost 1 ms 
[INFO ] 2025-03-02 12:26:43.923 - [Pg~SybaseTimestamp][Pg] - Enable partition table support for source database 
[INFO ] 2025-03-02 12:26:44.869 - [Pg~SybaseTimestamp][Sybase] - Sink connector(Sybase) initialization completed 
[TRACE] 2025-03-02 12:26:44.872 - [Pg~SybaseTimestamp][Sybase] - Node(Sybase) exactly once write is disabled, reason: Node type DatabaseNode nonsupport exactly once write 
[TRACE] 2025-03-02 12:26:44.890 - [Pg~SybaseTimestamp][Sybase] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2025-03-02 12:26:45.093 - [Pg~SybaseTimestamp][Sybase] - Apply table structure to target database 
[INFO ] 2025-03-02 12:26:45.455 - [Pg~SybaseTimestamp][Pg] - Source connector(Pg) initialization completed 
[TRACE] 2025-03-02 12:26:45.457 - [Pg~SybaseTimestamp][Pg] - Source node "Pg" read batch size: 100 
[TRACE] 2025-03-02 12:26:45.458 - [Pg~SybaseTimestamp][Pg] - Source node "Pg" event queue capacity: 200 
[TRACE] 2025-03-02 12:26:45.458 - [Pg~SybaseTimestamp][Pg] - On the first run, the breakpoint will be initialized 
[TRACE] 2025-03-02 12:26:45.646 - [Pg~SybaseTimestamp][Sybase] - The table testTimeStampWithIndex has already exist. 
[TRACE] 2025-03-02 12:26:45.978 - [Pg~SybaseTimestamp][Sybase] - Table: testTimeStampWithIndex already exists Index: TapIndex indexFields: [TapIndexField name id fieldAsc true indexType null; TapIndexField name testTimeStamp fieldAsc true indexType null; ] and will no longer create index 
[WARN ] 2025-03-02 12:26:46.362 - [Pg~SybaseTimestamp][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 12:26:46.377 - [Pg~SybaseTimestamp][Pg] - new logical replication slot created, slotName:tapdata_cdc_15b3886b_9611_4694_b91f_d8f964274273 
[INFO ] 2025-03-02 12:26:46.406 - [Pg~SybaseTimestamp][Pg] - Use existing stream offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[TRACE] 2025-03-02 12:26:46.423 - [Pg~SybaseTimestamp][Pg] - Before the event is output to the target from source, it will automatically block field changes 
[TRACE] 2025-03-02 12:26:46.424 - [Pg~SybaseTimestamp][Sybase] - The table testTimeStampWithNoIndex has already exist. 
[INFO ] 2025-03-02 12:26:46.524 - [Pg~SybaseTimestamp][Pg] - Starting batch read from 3 tables 
[TRACE] 2025-03-02 12:26:46.526 - [Pg~SybaseTimestamp][Pg] - Initial sync started 
[INFO ] 2025-03-02 12:26:46.527 - [Pg~SybaseTimestamp][Pg] - Starting batch read from table: testTimeStampWithIndex 
[TRACE] 2025-03-02 12:26:46.528 - [Pg~SybaseTimestamp][Pg] - Table testTimeStampWithIndex is going to be initial synced 
[INFO ] 2025-03-02 12:26:46.663 - [Pg~SybaseTimestamp][Pg] - Table testTimeStampWithIndex has been completed batch read 
[INFO ] 2025-03-02 12:26:46.664 - [Pg~SybaseTimestamp][Pg] - Starting batch read from table: testTimeStampWithNoIndex 
[TRACE] 2025-03-02 12:26:46.665 - [Pg~SybaseTimestamp][Pg] - Table testTimeStampWithNoIndex is going to be initial synced 
[INFO ] 2025-03-02 12:26:46.781 - [Pg~SybaseTimestamp][Pg] - Table testTimeStampWithNoIndex has been completed batch read 
[INFO ] 2025-03-02 12:26:46.782 - [Pg~SybaseTimestamp][Pg] - Starting batch read from table: testTimeStamp 
[TRACE] 2025-03-02 12:26:46.783 - [Pg~SybaseTimestamp][Pg] - Table testTimeStamp is going to be initial synced 
[TRACE] 2025-03-02 12:26:46.888 - [Pg~SybaseTimestamp][Pg] - Query snapshot row size completed: Pg(7843ae3f-0ec6-4efa-b225-4a41063995bd) 
[INFO ] 2025-03-02 12:26:46.892 - [Pg~SybaseTimestamp][Pg] - Table testTimeStamp has been completed batch read 
[TRACE] 2025-03-02 12:26:46.893 - [Pg~SybaseTimestamp][Pg] - Initial sync completed 
[INFO ] 2025-03-02 12:26:46.893 - [Pg~SybaseTimestamp][Pg] - Batch read completed. 
[TRACE] 2025-03-02 12:26:46.900 - [Pg~SybaseTimestamp][Pg] - Incremental sync starting... 
[TRACE] 2025-03-02 12:26:46.908 - [Pg~SybaseTimestamp][Pg] - Initial sync completed 
[TRACE] 2025-03-02 12:26:46.909 - [Pg~SybaseTimestamp][Pg] - Starting stream read, table list: [testTimeStampWithIndex, testTimeStampWithNoIndex, testTimeStamp], offset: {"sortString":null,"offsetValue":null,"sourceOffset":null} 
[INFO ] 2025-03-02 12:26:46.911 - [Pg~SybaseTimestamp][Pg] - Starting incremental sync using database log parser 
[WARN ] 2025-03-02 12:26:46.996 - [Pg~SybaseTimestamp][Pg] - The pgoutput plugin may cause before of data loss, if you need, please use another plugin instead, such as wal2json 
[INFO ] 2025-03-02 12:26:46.997 - [Pg~SybaseTimestamp][Pg] - Using an existing logical replication slot, slotName:tapdata_cdc_15b3886b_9611_4694_b91f_d8f964274273 
[WARN ] 2025-03-02 12:26:47.402 - [Pg~SybaseTimestamp][Sybase] - Create index failed Column name '_no_pk_hash' does not exist in target table.
, please execute it manually [create index  IDX_ithNoIndex021292472fb1  on  lisTest.dbo.testTimeStampWithNoIndex ( _no_pk_hash  asc)] 
[TRACE] 2025-03-02 12:26:47.608 - [Pg~SybaseTimestamp][Sybase] - The table testTimeStamp has already exist. 
[TRACE] 2025-03-02 12:26:48.760 - [Pg~SybaseTimestamp][Pg] - Connector PostgreSQL incremental start succeed, tables: [testTimeStampWithIndex, testTimeStampWithNoIndex, testTimeStamp], data change syncing 
[TRACE] 2025-03-02 12:26:49.686 - [Pg~SybaseTimestamp][Sybase] - Process after table "testTimeStampWithIndex" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-02 12:26:49.689 - [Pg~SybaseTimestamp][Sybase] - Process after table "testTimeStampWithNoIndex" initial sync finished, cost: 0 ms 
[TRACE] 2025-03-02 12:26:49.690 - [Pg~SybaseTimestamp][Sybase] - Process after table "testTimeStamp" initial sync finished, cost: 0 ms 
[INFO ] 2025-03-02 12:26:49.892 - [Pg~SybaseTimestamp][Sybase] - Process after all table(s) initial sync are finished，table number: 3 
[TRACE] 2025-03-02 12:27:33.715 - [Pg~SybaseTimestamp][Pg] - Node Pg[7843ae3f-0ec6-4efa-b225-4a41063995bd] running status set to false 
[TRACE] 2025-03-02 12:27:34.149 - [Pg~SybaseTimestamp][Pg] - Incremental sync completed 
[TRACE] 2025-03-02 12:27:34.150 - [Pg~SybaseTimestamp][Pg] - PDK connector node stopped: HazelcastSourcePdkDataNode_7843ae3f-0ec6-4efa-b225-4a41063995bd_1740889604876 
[TRACE] 2025-03-02 12:27:34.153 - [Pg~SybaseTimestamp][Pg] - PDK connector node released: HazelcastSourcePdkDataNode_7843ae3f-0ec6-4efa-b225-4a41063995bd_1740889604876 
[TRACE] 2025-03-02 12:27:34.154 - [Pg~SybaseTimestamp][Pg] - Node Pg[7843ae3f-0ec6-4efa-b225-4a41063995bd] schema data cleaned 
[TRACE] 2025-03-02 12:27:34.170 - [Pg~SybaseTimestamp][Pg] - Node Pg[7843ae3f-0ec6-4efa-b225-4a41063995bd] monitor closed 
[TRACE] 2025-03-02 12:27:34.171 - [Pg~SybaseTimestamp][Pg] - Node Pg[7843ae3f-0ec6-4efa-b225-4a41063995bd] close complete, cost 505 ms 
[TRACE] 2025-03-02 12:27:34.171 - [Pg~SybaseTimestamp][Sybase] - Node Sybase[a57d80fb-fe4b-4d77-acef-209c206b4423] running status set to false 
[TRACE] 2025-03-02 12:27:35.496 - [Pg~SybaseTimestamp][Sybase] - PDK connector node stopped: HazelcastTargetPdkDataNode_a57d80fb-fe4b-4d77-acef-209c206b4423_1740889604636 
[TRACE] 2025-03-02 12:27:35.498 - [Pg~SybaseTimestamp][Sybase] - PDK connector node released: HazelcastTargetPdkDataNode_a57d80fb-fe4b-4d77-acef-209c206b4423_1740889604636 
[TRACE] 2025-03-02 12:27:35.499 - [Pg~SybaseTimestamp][Sybase] - Node Sybase[a57d80fb-fe4b-4d77-acef-209c206b4423] schema data cleaned 
[TRACE] 2025-03-02 12:27:35.499 - [Pg~SybaseTimestamp][Sybase] - Node Sybase[a57d80fb-fe4b-4d77-acef-209c206b4423] monitor closed 
[TRACE] 2025-03-02 12:27:35.708 - [Pg~SybaseTimestamp][Sybase] - Node Sybase[a57d80fb-fe4b-4d77-acef-209c206b4423] close complete, cost 1330 ms 
[TRACE] 2025-03-02 12:27:37.647 - [Pg~SybaseTimestamp] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[TRACE] 2025-03-02 12:27:37.773 - [Pg~SybaseTimestamp] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4cae44a9 
[TRACE] 2025-03-02 12:27:37.773 - [Pg~SybaseTimestamp] - Stop task milestones: 67c3a811bc360b67e495ee01(Pg~SybaseTimestamp)  
[TRACE] 2025-03-02 12:27:37.785 - [Pg~SybaseTimestamp] - Stopped task aspect(s) 
[TRACE] 2025-03-02 12:27:37.785 - [Pg~SybaseTimestamp] - Snapshot order controller have been removed 
[INFO ] 2025-03-02 12:27:37.839 - [Pg~SybaseTimestamp] - Task stopped. 
[TRACE] 2025-03-02 12:27:37.840 - [Pg~SybaseTimestamp] - Remove memory task client succeed, task: Pg~SybaseTimestamp[67c3a811bc360b67e495ee01] 
[TRACE] 2025-03-02 12:27:37.840 - [Pg~SybaseTimestamp] - Destroy memory task client cache succeed, task: Pg~SybaseTimestamp[67c3a811bc360b67e495ee01] 
