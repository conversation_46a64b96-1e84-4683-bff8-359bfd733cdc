[INFO ] 2024-03-29 18:40:54.848 - [employees] - Start task milestones: 66069a1b0aba7657dd9a0e9e(employees) 
[INFO ] 2024-03-29 18:40:54.882 - [employees] - Task initialization... 
[INFO ] 2024-03-29 18:40:54.882 - [employees] - <PERSON>de performs snapshot read by order list: [ null ] -> [ null,null ] -> [ null ] -> [ null ] 
[INFO ] 2024-03-29 18:40:55.699 - [employees] - The engine receives employees task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-29 18:40:55.755 - [employees][merge] - Node merge[a42f918c-c487-432a-8243-9ddfe06952ef] start preload schema,table counts: 6 
[INFO ] 2024-03-29 18:40:55.756 - [employees][employees] - Node employees[d155cbf2-a46b-4651-a1ea-56ef75315823] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:55.756 - [employees][Delete Region] - Node Delete Region[e986aeda-5842-4c4b-9dfc-dd8bd98cbc89] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:55.756 - [employees][Rename Employees] - Node Rename Employees[ed66468d-a930-4efb-854e-2fbf28b035ad] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:55.756 - [employees][Territories] - Node Territories[c3efe132-89ed-4405-9838-7bb638a5f377] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:55.756 - [employees][EmployeeTerritories] - Node EmployeeTerritories[891d6b29-8973-44c8-ad1f-40c3ff034ba9] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:55.781 - [employees][Rename Region] - Node Rename Region[6df9ca45-9a39-4f1e-81a8-a67f6f9ec9cb] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:55.781 - [employees][Delete Territories] - Node Delete Territories[ef2f0ae0-3aad-496f-8f6c-6010e69f9520] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:55.781 - [employees][Rename Employees] - Node Rename Employees[18a6a58b-24cc-477b-a60a-f1b3628dc615] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:55.781 - [employees][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[7b57ca71-d592-44c4-a6b6-2e7c40be2da3] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:55.782 - [employees][Region] - Node Region[c377bc4a-8967-4502-84bc-cb5e51865cd3] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:55.801 - [employees][Employees] - Node Employees[728efed7-b0a5-4284-9398-0c3699cd8ea1] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:55.801 - [employees][Rename Territories] - Node Rename Territories[09c3dde2-9758-4a11-8982-55fc9dd92be7] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:55.801 - [employees][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[15d4a0e4-7b18-41da-bc4e-e305a90e5406] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:55.801 - [employees][Employees] - Node Employees[0417b1c8-30ed-4510-83e7-5c4f9f4f354d] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:40:55.865 - [employees][Rename Territories] - Node Rename Territories[09c3dde2-9758-4a11-8982-55fc9dd92be7] preload schema finished, cost 50 ms 
[INFO ] 2024-03-29 18:40:55.867 - [employees][Rename Region] - Node Rename Region[6df9ca45-9a39-4f1e-81a8-a67f6f9ec9cb] preload schema finished, cost 70 ms 
[INFO ] 2024-03-29 18:40:55.869 - [employees][EmployeeTerritories] - Node EmployeeTerritories[891d6b29-8973-44c8-ad1f-40c3ff034ba9] preload schema finished, cost 97 ms 
[INFO ] 2024-03-29 18:40:55.869 - [employees][Region] - Node Region[c377bc4a-8967-4502-84bc-cb5e51865cd3] preload schema finished, cost 54 ms 
[INFO ] 2024-03-29 18:40:55.869 - [employees][Territories] - Node Territories[c3efe132-89ed-4405-9838-7bb638a5f377] preload schema finished, cost 95 ms 
[INFO ] 2024-03-29 18:40:55.869 - [employees][Employees] - Node Employees[0417b1c8-30ed-4510-83e7-5c4f9f4f354d] preload schema finished, cost 61 ms 
[INFO ] 2024-03-29 18:40:55.869 - [employees][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[7b57ca71-d592-44c4-a6b6-2e7c40be2da3] preload schema finished, cost 71 ms 
[INFO ] 2024-03-29 18:40:55.869 - [employees][Delete Region] - Node Delete Region[e986aeda-5842-4c4b-9dfc-dd8bd98cbc89] preload schema finished, cost 99 ms 
[INFO ] 2024-03-29 18:40:55.869 - [employees][Employees] - Node Employees[728efed7-b0a5-4284-9398-0c3699cd8ea1] preload schema finished, cost 68 ms 
[INFO ] 2024-03-29 18:40:56.038 - [employees][Delete Territories] - Node Delete Territories[ef2f0ae0-3aad-496f-8f6c-6010e69f9520] preload schema finished, cost 226 ms 
[INFO ] 2024-03-29 18:40:56.055 - [employees][Rename Employees] - Node Rename Employees[18a6a58b-24cc-477b-a60a-f1b3628dc615] preload schema finished, cost 226 ms 
[INFO ] 2024-03-29 18:40:56.055 - [employees][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[15d4a0e4-7b18-41da-bc4e-e305a90e5406] preload schema finished, cost 208 ms 
[INFO ] 2024-03-29 18:40:56.055 - [employees][Rename Employees] - Node Rename Employees[ed66468d-a930-4efb-854e-2fbf28b035ad] preload schema finished, cost 253 ms 
[INFO ] 2024-03-29 18:40:56.055 - [employees][employees] - Node employees[d155cbf2-a46b-4651-a1ea-56ef75315823] preload schema finished, cost 264 ms 
[INFO ] 2024-03-29 18:40:56.300 - [employees][merge] - Node merge[a42f918c-c487-432a-8243-9ddfe06952ef] preload schema finished, cost 535 ms 
[INFO ] 2024-03-29 18:40:56.302 - [employees][merge] - Node %s(%s) enable initial batch 
[INFO ] 2024-03-29 18:40:56.302 - [employees][merge] - 
Merge lookup relation{
  Territories(ef2f0ae0-3aad-496f-8f6c-6010e69f9520)
    ->Region(e986aeda-5842-4c4b-9dfc-dd8bd98cbc89)
} 
[INFO ] 2024-03-29 18:40:56.302 - [employees][merge] - 
Merge lookup relation{
  EmployeeTerritories(7b57ca71-d592-44c4-a6b6-2e7c40be2da3)
    ->Territories(ef2f0ae0-3aad-496f-8f6c-6010e69f9520)
} 
[INFO ] 2024-03-29 18:40:56.303 - [employees][merge] - 
Merge lookup relation{
  Employees(18a6a58b-24cc-477b-a60a-f1b3628dc615)
    ->Employees(ed66468d-a930-4efb-854e-2fbf28b035ad)
    ->EmployeeTerritories(7b57ca71-d592-44c4-a6b6-2e7c40be2da3)
} 
[INFO ] 2024-03-29 18:40:57.760 - [employees][Territories] - Source node "Territories" read batch size: 500 
[INFO ] 2024-03-29 18:40:57.763 - [employees][Territories] - Source node "Territories" event queue capacity: 1000 
[INFO ] 2024-03-29 18:40:57.764 - [employees][Territories] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:40:57.764 - [employees][Territories] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:40:57.908 - [employees] - Node[Territories] is waiting for running 
[INFO ] 2024-03-29 18:40:57.909 - [employees][Region] - Source node "Region" read batch size: 500 
[INFO ] 2024-03-29 18:40:57.909 - [employees][Region] - Source node "Region" event queue capacity: 1000 
[INFO ] 2024-03-29 18:40:57.909 - [employees][Region] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:40:57.972 - [employees][Region] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:40:57.972 - [employees] - Node[Region] is waiting for running 
[INFO ] 2024-03-29 18:40:58.099 - [employees][EmployeeTerritories] - Source node "EmployeeTerritories" read batch size: 500 
[INFO ] 2024-03-29 18:40:58.100 - [employees][EmployeeTerritories] - Source node "EmployeeTerritories" event queue capacity: 1000 
[INFO ] 2024-03-29 18:40:58.100 - [employees][EmployeeTerritories] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:40:58.159 - [employees][EmployeeTerritories] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:40:58.171 - [employees] - Node[EmployeeTerritories] is waiting for running 
[INFO ] 2024-03-29 18:40:58.204 - [employees][employees] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-29 18:40:58.204 - [employees][merge] - Create merge cache imap name: HazelcastMergeNode_Territories_ef2f0ae0-3aad-496f-8f6c-6010e69f9520__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:40:58.609 - [employees][merge] - Create merge cache imap name: HazelcastMergeNode_Region_e986aeda-5842-4c4b-9dfc-dd8bd98cbc89__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:40:58.630 - [employees][Employees] - Source node "Employees" read batch size: 500 
[INFO ] 2024-03-29 18:40:58.632 - [employees][Employees] - Source node "Employees" event queue capacity: 1000 
[INFO ] 2024-03-29 18:40:58.632 - [employees][Employees] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:40:58.726 - [employees][Employees] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:40:58.738 - [employees] - Node[Employees] is waiting for running 
[INFO ] 2024-03-29 18:40:58.943 - [employees][merge] - Create merge cache imap name: HazelcastMergeNode_Employees_ed66468d-a930-4efb-854e-2fbf28b035ad__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:40:58.963 - [employees][Employees] - Source node "Employees" read batch size: 500 
[INFO ] 2024-03-29 18:40:58.963 - [employees][Employees] - Source node "Employees" event queue capacity: 1000 
[INFO ] 2024-03-29 18:40:58.964 - [employees][Employees] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-29 18:40:58.966 - [employees][Employees] - batch offset found: {},stream offset found: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:40:59.027 - [employees][merge] - Create merge cache imap name: HazelcastMergeNode_EmployeeTerritories_7b57ca71-d592-44c4-a6b6-2e7c40be2da3__TPORIG, external storage: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://localhost:27017/tapdatavdevkafka', table='null', ttlDay=0] 
[INFO ] 2024-03-29 18:40:59.027 - [employees][merge] - Merge table processor lookup thread num: 8 
[INFO ] 2024-03-29 18:40:59.027 - [employees][merge] - Merge table processor handle update join key thread num: 4 
[INFO ] 2024-03-29 18:40:59.031 - [employees][Employees] - Initial sync started 
[INFO ] 2024-03-29 18:40:59.033 - [employees][Employees] - Starting batch read, table name: Employees, offset: null 
[INFO ] 2024-03-29 18:40:59.033 - [employees][Employees] - Table Employees is going to be initial synced 
[INFO ] 2024-03-29 18:40:59.075 - [employees][Employees] - Query table 'Employees' counts: 0 
[INFO ] 2024-03-29 18:40:59.075 - [employees][Employees] - Initial sync completed 
[INFO ] 2024-03-29 18:40:59.707 - [employees] - Node[Employees] finish, notify next layer to run 
[INFO ] 2024-03-29 18:40:59.707 - [employees] - Next layer have been notified: [null,null] 
[INFO ] 2024-03-29 18:40:59.707 - [employees][Employees] - Initial sync started 
[INFO ] 2024-03-29 18:40:59.708 - [employees][EmployeeTerritories] - Initial sync started 
[INFO ] 2024-03-29 18:40:59.708 - [employees][Employees] - Starting batch read, table name: Employees, offset: null 
[INFO ] 2024-03-29 18:40:59.708 - [employees][EmployeeTerritories] - Starting batch read, table name: EmployeeTerritories, offset: null 
[INFO ] 2024-03-29 18:40:59.708 - [employees][Employees] - Table Employees is going to be initial synced 
[INFO ] 2024-03-29 18:40:59.728 - [employees][EmployeeTerritories] - Table EmployeeTerritories is going to be initial synced 
[INFO ] 2024-03-29 18:40:59.753 - [employees][Employees] - Query table 'Employees' counts: 0 
[INFO ] 2024-03-29 18:40:59.756 - [employees][Employees] - Initial sync completed 
[INFO ] 2024-03-29 18:40:59.756 - [employees][EmployeeTerritories] - Query table 'EmployeeTerritories' counts: 1 
[INFO ] 2024-03-29 18:40:59.756 - [employees][EmployeeTerritories] - Initial sync completed 
[INFO ] 2024-03-29 18:41:00.817 - [employees] - Node[EmployeeTerritories] finish, notify next layer to run 
[INFO ] 2024-03-29 18:41:00.817 - [employees] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 18:41:00.817 - [employees][Territories] - Initial sync started 
[INFO ] 2024-03-29 18:41:00.839 - [employees][Territories] - Starting batch read, table name: Territories, offset: null 
[INFO ] 2024-03-29 18:41:00.842 - [employees][Territories] - Table Territories is going to be initial synced 
[INFO ] 2024-03-29 18:41:00.862 - [employees][Territories] - Query table 'Territories' counts: 1 
[INFO ] 2024-03-29 18:41:00.862 - [employees][Territories] - Initial sync completed 
[INFO ] 2024-03-29 18:41:01.898 - [employees] - Node[Territories] finish, notify next layer to run 
[INFO ] 2024-03-29 18:41:01.905 - [employees] - Next layer have been notified: [null] 
[INFO ] 2024-03-29 18:41:01.905 - [employees][Region] - Initial sync started 
[INFO ] 2024-03-29 18:41:01.905 - [employees][Region] - Starting batch read, table name: Region, offset: null 
[INFO ] 2024-03-29 18:41:01.905 - [employees][Region] - Table Region is going to be initial synced 
[INFO ] 2024-03-29 18:41:01.951 - [employees][Region] - Query table 'Region' counts: 1 
[INFO ] 2024-03-29 18:41:01.951 - [employees][Region] - Initial sync completed 
[INFO ] 2024-03-29 18:41:02.766 - [employees][EmployeeTerritories] - Incremental sync starting... 
[INFO ] 2024-03-29 18:41:02.766 - [employees][EmployeeTerritories] - Initial sync completed 
[INFO ] 2024-03-29 18:41:02.767 - [employees][Employees] - Incremental sync starting... 
[INFO ] 2024-03-29 18:41:02.767 - [employees][EmployeeTerritories] - Starting stream read, table list: [EmployeeTerritories], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:41:02.767 - [employees][Employees] - Initial sync completed 
[INFO ] 2024-03-29 18:41:02.767 - [employees][Employees] - Starting stream read, table list: [Employees], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:41:02.816 - [employees][Employees] - Starting mysql cdc, server name: 7d379f53-6c23-4b6b-af23-e12779fcf21b 
[INFO ] 2024-03-29 18:41:02.816 - [employees][EmployeeTerritories] - Starting mysql cdc, server name: 3b4bd85e-91bf-401f-af9b-39f4d042ed08 
[INFO ] 2024-03-29 18:41:02.819 - [employees][EmployeeTerritories] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1333190585
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 3b4bd85e-91bf-401f-af9b-39f4d042ed08
  database.port: 3307
  threadName: Debezium-Mysql-Connector-3b4bd85e-91bf-401f-af9b-39f4d042ed08
  database.hostname: 127.0.0.1
  database.password: ********
  name: 3b4bd85e-91bf-401f-af9b-39f4d042ed08
  pdk.offset.string: {"name":"3b4bd85e-91bf-401f-af9b-39f4d042ed08","offset":{"{\"server\":\"3b4bd85e-91bf-401f-af9b-39f4d042ed08\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.EmployeeTerritories
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:41:02.819 - [employees][Employees] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 349916644
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 7d379f53-6c23-4b6b-af23-e12779fcf21b
  database.port: 3307
  threadName: Debezium-Mysql-Connector-7d379f53-6c23-4b6b-af23-e12779fcf21b
  database.hostname: 127.0.0.1
  database.password: ********
  name: 7d379f53-6c23-4b6b-af23-e12779fcf21b
  pdk.offset.string: {"name":"7d379f53-6c23-4b6b-af23-e12779fcf21b","offset":{"{\"server\":\"7d379f53-6c23-4b6b-af23-e12779fcf21b\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Employees
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:41:02.874 - [employees][Employees] - Connector Mysql incremental start succeed, tables: [Employees], data change syncing 
[INFO ] 2024-03-29 18:41:02.874 - [employees][EmployeeTerritories] - Connector Mysql incremental start succeed, tables: [EmployeeTerritories], data change syncing 
[INFO ] 2024-03-29 18:41:02.874 - [employees][Territories] - Incremental sync starting... 
[INFO ] 2024-03-29 18:41:02.874 - [employees][Territories] - Initial sync completed 
[INFO ] 2024-03-29 18:41:02.908 - [employees][Territories] - Starting stream read, table list: [Territories], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:41:02.908 - [employees][Territories] - Starting mysql cdc, server name: 12589956-830d-4d0b-9b30-010b5104e872 
[INFO ] 2024-03-29 18:41:02.959 - [employees][Territories] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 701583275
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 12589956-830d-4d0b-9b30-010b5104e872
  database.port: 3307
  threadName: Debezium-Mysql-Connector-12589956-830d-4d0b-9b30-010b5104e872
  database.hostname: 127.0.0.1
  database.password: ********
  name: 12589956-830d-4d0b-9b30-010b5104e872
  pdk.offset.string: {"name":"12589956-830d-4d0b-9b30-010b5104e872","offset":{"{\"server\":\"12589956-830d-4d0b-9b30-010b5104e872\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Territories
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:41:02.959 - [employees][Territories] - Connector Mysql incremental start succeed, tables: [Territories], data change syncing 
[INFO ] 2024-03-29 18:41:02.960 - [employees][Region] - Incremental sync starting... 
[INFO ] 2024-03-29 18:41:02.960 - [employees][Region] - Initial sync completed 
[INFO ] 2024-03-29 18:41:02.960 - [employees][Region] - Starting stream read, table list: [Region], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:41:03.042 - [employees][Region] - Starting mysql cdc, server name: 4c571568-b277-4e10-bde2-21606e5dbc95 
[INFO ] 2024-03-29 18:41:03.043 - [employees][Region] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1854483195
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 4c571568-b277-4e10-bde2-21606e5dbc95
  database.port: 3307
  threadName: Debezium-Mysql-Connector-4c571568-b277-4e10-bde2-21606e5dbc95
  database.hostname: 127.0.0.1
  database.password: ********
  name: 4c571568-b277-4e10-bde2-21606e5dbc95
  pdk.offset.string: {"name":"4c571568-b277-4e10-bde2-21606e5dbc95","offset":{"{\"server\":\"4c571568-b277-4e10-bde2-21606e5dbc95\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Region
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:41:03.148 - [employees][Employees] - Incremental sync starting... 
[INFO ] 2024-03-29 18:41:03.149 - [employees][Employees] - Initial sync completed 
[INFO ] 2024-03-29 18:41:03.150 - [employees][Employees] - Starting stream read, table list: [Employees], offset: {"filename":"binlog.000008","position":6374446,"gtidSet":""} 
[INFO ] 2024-03-29 18:41:03.176 - [employees][Region] - Connector Mysql incremental start succeed, tables: [Region], data change syncing 
[INFO ] 2024-03-29 18:41:03.239 - [employees][Employees] - Starting mysql cdc, server name: 0adf25c9-6dd5-4a31-8a40-f008ea47d933 
[INFO ] 2024-03-29 18:41:03.288 - [employees][Employees] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1203412695
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 0adf25c9-6dd5-4a31-8a40-f008ea47d933
  database.port: 3307
  threadName: Debezium-Mysql-Connector-0adf25c9-6dd5-4a31-8a40-f008ea47d933
  database.hostname: 127.0.0.1
  database.password: ********
  name: 0adf25c9-6dd5-4a31-8a40-f008ea47d933
  pdk.offset.string: {"name":"0adf25c9-6dd5-4a31-8a40-f008ea47d933","offset":{"{\"server\":\"0adf25c9-6dd5-4a31-8a40-f008ea47d933\"}":"{\"file\":\"binlog.000008\",\"pos\":6374446,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.Employees
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-29 18:41:03.293 - [employees][Employees] - Connector Mysql incremental start succeed, tables: [Employees], data change syncing 
[INFO ] 2024-03-29 18:53:18.018 - [employees] - Stop task milestones: 66069a1b0aba7657dd9a0e9e(employees)  
[INFO ] 2024-03-29 18:53:18.221 - [employees][EmployeeTerritories] - Node EmployeeTerritories[891d6b29-8973-44c8-ad1f-40c3ff034ba9] running status set to false 
[INFO ] 2024-03-29 18:53:18.339 - [employees][EmployeeTerritories] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:53:18.354 - [employees][EmployeeTerritories] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:53:18.354 - [employees][EmployeeTerritories] - PDK connector node stopped: HazelcastSourcePdkDataNode-891d6b29-8973-44c8-ad1f-40c3ff034ba9 
[INFO ] 2024-03-29 18:53:18.354 - [employees][EmployeeTerritories] - PDK connector node released: HazelcastSourcePdkDataNode-891d6b29-8973-44c8-ad1f-40c3ff034ba9 
[INFO ] 2024-03-29 18:53:18.354 - [employees][EmployeeTerritories] - Node EmployeeTerritories[891d6b29-8973-44c8-ad1f-40c3ff034ba9] schema data cleaned 
[INFO ] 2024-03-29 18:53:18.355 - [employees][EmployeeTerritories] - Node EmployeeTerritories[891d6b29-8973-44c8-ad1f-40c3ff034ba9] monitor closed 
[INFO ] 2024-03-29 18:53:18.355 - [employees][EmployeeTerritories] - Node EmployeeTerritories[891d6b29-8973-44c8-ad1f-40c3ff034ba9] close complete, cost 141 ms 
[INFO ] 2024-03-29 18:53:18.451 - [employees][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[15d4a0e4-7b18-41da-bc4e-e305a90e5406] running status set to false 
[INFO ] 2024-03-29 18:53:18.451 - [employees][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[15d4a0e4-7b18-41da-bc4e-e305a90e5406] schema data cleaned 
[INFO ] 2024-03-29 18:53:18.451 - [employees][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[15d4a0e4-7b18-41da-bc4e-e305a90e5406] monitor closed 
[INFO ] 2024-03-29 18:53:18.451 - [employees][Rename EmployeeTerritories] - Node Rename EmployeeTerritories[15d4a0e4-7b18-41da-bc4e-e305a90e5406] close complete, cost 95 ms 
[INFO ] 2024-03-29 18:53:18.495 - [employees][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[7b57ca71-d592-44c4-a6b6-2e7c40be2da3] running status set to false 
[INFO ] 2024-03-29 18:53:18.495 - [employees][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[7b57ca71-d592-44c4-a6b6-2e7c40be2da3] schema data cleaned 
[INFO ] 2024-03-29 18:53:18.495 - [employees][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[7b57ca71-d592-44c4-a6b6-2e7c40be2da3] monitor closed 
[INFO ] 2024-03-29 18:53:18.495 - [employees][Delete EmployeeTerritories] - Node Delete EmployeeTerritories[7b57ca71-d592-44c4-a6b6-2e7c40be2da3] close complete, cost 43 ms 
[INFO ] 2024-03-29 18:53:18.567 - [employees][Territories] - Node Territories[c3efe132-89ed-4405-9838-7bb638a5f377] running status set to false 
[INFO ] 2024-03-29 18:53:18.567 - [employees][Territories] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:53:18.567 - [employees][Territories] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:53:18.576 - [employees][Territories] - PDK connector node stopped: HazelcastSourcePdkDataNode-c3efe132-89ed-4405-9838-7bb638a5f377 
[INFO ] 2024-03-29 18:53:18.576 - [employees][Territories] - PDK connector node released: HazelcastSourcePdkDataNode-c3efe132-89ed-4405-9838-7bb638a5f377 
[INFO ] 2024-03-29 18:53:18.576 - [employees][Territories] - Node Territories[c3efe132-89ed-4405-9838-7bb638a5f377] schema data cleaned 
[INFO ] 2024-03-29 18:53:18.576 - [employees][Territories] - Node Territories[c3efe132-89ed-4405-9838-7bb638a5f377] monitor closed 
[INFO ] 2024-03-29 18:53:18.576 - [employees][Territories] - Node Territories[c3efe132-89ed-4405-9838-7bb638a5f377] close complete, cost 81 ms 
[INFO ] 2024-03-29 18:53:18.576 - [employees][Rename Territories] - Node Rename Territories[09c3dde2-9758-4a11-8982-55fc9dd92be7] running status set to false 
[INFO ] 2024-03-29 18:53:18.620 - [employees][Rename Territories] - Node Rename Territories[09c3dde2-9758-4a11-8982-55fc9dd92be7] schema data cleaned 
[INFO ] 2024-03-29 18:53:18.620 - [employees][Rename Territories] - Node Rename Territories[09c3dde2-9758-4a11-8982-55fc9dd92be7] monitor closed 
[INFO ] 2024-03-29 18:53:18.620 - [employees][Rename Territories] - Node Rename Territories[09c3dde2-9758-4a11-8982-55fc9dd92be7] close complete, cost 44 ms 
[INFO ] 2024-03-29 18:53:18.621 - [employees][Delete Territories] - Node Delete Territories[ef2f0ae0-3aad-496f-8f6c-6010e69f9520] running status set to false 
[INFO ] 2024-03-29 18:53:18.659 - [employees][Delete Territories] - Node Delete Territories[ef2f0ae0-3aad-496f-8f6c-6010e69f9520] schema data cleaned 
[INFO ] 2024-03-29 18:53:18.660 - [employees][Delete Territories] - Node Delete Territories[ef2f0ae0-3aad-496f-8f6c-6010e69f9520] monitor closed 
[INFO ] 2024-03-29 18:53:18.660 - [employees][Delete Territories] - Node Delete Territories[ef2f0ae0-3aad-496f-8f6c-6010e69f9520] close complete, cost 38 ms 
[INFO ] 2024-03-29 18:53:18.660 - [employees][Employees] - Node Employees[0417b1c8-30ed-4510-83e7-5c4f9f4f354d] running status set to false 
[INFO ] 2024-03-29 18:53:18.719 - [employees][Employees] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:53:18.719 - [employees][Employees] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:53:18.726 - [employees][Employees] - PDK connector node stopped: HazelcastSourcePdkDataNode-0417b1c8-30ed-4510-83e7-5c4f9f4f354d 
[INFO ] 2024-03-29 18:53:18.726 - [employees][Employees] - PDK connector node released: HazelcastSourcePdkDataNode-0417b1c8-30ed-4510-83e7-5c4f9f4f354d 
[INFO ] 2024-03-29 18:53:18.726 - [employees][Employees] - Node Employees[0417b1c8-30ed-4510-83e7-5c4f9f4f354d] schema data cleaned 
[INFO ] 2024-03-29 18:53:18.726 - [employees][Employees] - Node Employees[0417b1c8-30ed-4510-83e7-5c4f9f4f354d] monitor closed 
[INFO ] 2024-03-29 18:53:18.727 - [employees][Employees] - Node Employees[0417b1c8-30ed-4510-83e7-5c4f9f4f354d] close complete, cost 66 ms 
[INFO ] 2024-03-29 18:53:18.727 - [employees][Rename Employees] - Node Rename Employees[ed66468d-a930-4efb-854e-2fbf28b035ad] running status set to false 
[INFO ] 2024-03-29 18:53:18.770 - [employees][Rename Employees] - Node Rename Employees[ed66468d-a930-4efb-854e-2fbf28b035ad] schema data cleaned 
[INFO ] 2024-03-29 18:53:18.770 - [employees][Rename Employees] - Node Rename Employees[ed66468d-a930-4efb-854e-2fbf28b035ad] monitor closed 
[INFO ] 2024-03-29 18:53:18.770 - [employees][Rename Employees] - Node Rename Employees[ed66468d-a930-4efb-854e-2fbf28b035ad] close complete, cost 43 ms 
[INFO ] 2024-03-29 18:53:18.770 - [employees][Employees] - Node Employees[728efed7-b0a5-4284-9398-0c3699cd8ea1] running status set to false 
[INFO ] 2024-03-29 18:53:18.845 - [employees][Employees] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:53:18.845 - [employees][Employees] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:53:18.852 - [employees][Employees] - PDK connector node stopped: HazelcastSourcePdkDataNode-728efed7-b0a5-4284-9398-0c3699cd8ea1 
[INFO ] 2024-03-29 18:53:18.852 - [employees][Employees] - PDK connector node released: HazelcastSourcePdkDataNode-728efed7-b0a5-4284-9398-0c3699cd8ea1 
[INFO ] 2024-03-29 18:53:18.852 - [employees][Employees] - Node Employees[728efed7-b0a5-4284-9398-0c3699cd8ea1] schema data cleaned 
[INFO ] 2024-03-29 18:53:18.853 - [employees][Employees] - Node Employees[728efed7-b0a5-4284-9398-0c3699cd8ea1] monitor closed 
[INFO ] 2024-03-29 18:53:18.853 - [employees][Employees] - Node Employees[728efed7-b0a5-4284-9398-0c3699cd8ea1] close complete, cost 82 ms 
[INFO ] 2024-03-29 18:53:18.893 - [employees][Rename Employees] - Node Rename Employees[18a6a58b-24cc-477b-a60a-f1b3628dc615] running status set to false 
[INFO ] 2024-03-29 18:53:18.893 - [employees][Rename Employees] - Node Rename Employees[18a6a58b-24cc-477b-a60a-f1b3628dc615] schema data cleaned 
[INFO ] 2024-03-29 18:53:18.893 - [employees][Rename Employees] - Node Rename Employees[18a6a58b-24cc-477b-a60a-f1b3628dc615] monitor closed 
[INFO ] 2024-03-29 18:53:18.893 - [employees][Rename Employees] - Node Rename Employees[18a6a58b-24cc-477b-a60a-f1b3628dc615] close complete, cost 40 ms 
[INFO ] 2024-03-29 18:53:18.948 - [employees][Region] - Node Region[c377bc4a-8967-4502-84bc-cb5e51865cd3] running status set to false 
[INFO ] 2024-03-29 18:53:18.948 - [employees][Region] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-29 18:53:18.948 - [employees][Region] - Mysql binlog reader stopped 
[INFO ] 2024-03-29 18:53:18.956 - [employees][Region] - PDK connector node stopped: HazelcastSourcePdkDataNode-c377bc4a-8967-4502-84bc-cb5e51865cd3 
[INFO ] 2024-03-29 18:53:18.956 - [employees][Region] - PDK connector node released: HazelcastSourcePdkDataNode-c377bc4a-8967-4502-84bc-cb5e51865cd3 
[INFO ] 2024-03-29 18:53:18.956 - [employees][Region] - Node Region[c377bc4a-8967-4502-84bc-cb5e51865cd3] schema data cleaned 
[INFO ] 2024-03-29 18:53:18.957 - [employees][Region] - Node Region[c377bc4a-8967-4502-84bc-cb5e51865cd3] monitor closed 
[INFO ] 2024-03-29 18:53:18.957 - [employees][Region] - Node Region[c377bc4a-8967-4502-84bc-cb5e51865cd3] close complete, cost 62 ms 
[INFO ] 2024-03-29 18:53:19.020 - [employees][Rename Region] - Node Rename Region[6df9ca45-9a39-4f1e-81a8-a67f6f9ec9cb] running status set to false 
[INFO ] 2024-03-29 18:53:19.020 - [employees][Rename Region] - Node Rename Region[6df9ca45-9a39-4f1e-81a8-a67f6f9ec9cb] schema data cleaned 
[INFO ] 2024-03-29 18:53:19.020 - [employees][Rename Region] - Node Rename Region[6df9ca45-9a39-4f1e-81a8-a67f6f9ec9cb] monitor closed 
[INFO ] 2024-03-29 18:53:19.021 - [employees][Rename Region] - Node Rename Region[6df9ca45-9a39-4f1e-81a8-a67f6f9ec9cb] close complete, cost 63 ms 
[INFO ] 2024-03-29 18:53:19.087 - [employees][Delete Region] - Node Delete Region[e986aeda-5842-4c4b-9dfc-dd8bd98cbc89] running status set to false 
[INFO ] 2024-03-29 18:53:19.098 - [employees][Delete Region] - Node Delete Region[e986aeda-5842-4c4b-9dfc-dd8bd98cbc89] schema data cleaned 
[INFO ] 2024-03-29 18:53:19.098 - [employees][Delete Region] - Node Delete Region[e986aeda-5842-4c4b-9dfc-dd8bd98cbc89] monitor closed 
[INFO ] 2024-03-29 18:53:19.098 - [employees][Delete Region] - Node Delete Region[e986aeda-5842-4c4b-9dfc-dd8bd98cbc89] close complete, cost 64 ms 
[INFO ] 2024-03-29 18:53:19.098 - [employees][merge] - Node merge[a42f918c-c487-432a-8243-9ddfe06952ef] running status set to false 
[INFO ] 2024-03-29 18:53:19.104 - [employees][merge] - Destroy merge cache resource: HazelcastMergeNode_Region_e986aeda-5842-4c4b-9dfc-dd8bd98cbc89__TPORIG 
[INFO ] 2024-03-29 18:53:19.119 - [employees][merge] - Destroy merge cache resource: HazelcastMergeNode_EmployeeTerritories_7b57ca71-d592-44c4-a6b6-2e7c40be2da3__TPORIG 
[INFO ] 2024-03-29 18:53:19.120 - [employees][merge] - Destroy merge cache resource: HazelcastMergeNode_Employees_ed66468d-a930-4efb-854e-2fbf28b035ad__TPORIG 
[INFO ] 2024-03-29 18:53:19.141 - [employees][merge] - Destroy merge cache resource: HazelcastMergeNode_Territories_ef2f0ae0-3aad-496f-8f6c-6010e69f9520__TPORIG 
[INFO ] 2024-03-29 18:53:19.141 - [employees][merge] - Node merge[a42f918c-c487-432a-8243-9ddfe06952ef] schema data cleaned 
[INFO ] 2024-03-29 18:53:19.142 - [employees][merge] - Node merge[a42f918c-c487-432a-8243-9ddfe06952ef] monitor closed 
[INFO ] 2024-03-29 18:53:19.142 - [employees][merge] - Node merge[a42f918c-c487-432a-8243-9ddfe06952ef] close complete, cost 56 ms 
[INFO ] 2024-03-29 18:53:19.193 - [employees][employees] - Node employees[d155cbf2-a46b-4651-a1ea-56ef75315823] running status set to false 
[INFO ] 2024-03-29 18:53:19.193 - [employees][employees] - PDK connector node stopped: HazelcastTargetPdkDataNode-d155cbf2-a46b-4651-a1ea-56ef75315823 
[INFO ] 2024-03-29 18:53:19.193 - [employees][employees] - PDK connector node released: HazelcastTargetPdkDataNode-d155cbf2-a46b-4651-a1ea-56ef75315823 
[INFO ] 2024-03-29 18:53:19.193 - [employees][employees] - Node employees[d155cbf2-a46b-4651-a1ea-56ef75315823] schema data cleaned 
[INFO ] 2024-03-29 18:53:19.193 - [employees][employees] - Node employees[d155cbf2-a46b-4651-a1ea-56ef75315823] monitor closed 
[INFO ] 2024-03-29 18:53:19.193 - [employees][employees] - Node employees[d155cbf2-a46b-4651-a1ea-56ef75315823] close complete, cost 51 ms 
[INFO ] 2024-03-29 18:53:19.956 - [employees] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-29 18:53:19.958 - [employees] - Stopped task aspect(s) 
[INFO ] 2024-03-29 18:53:19.958 - [employees] - Snapshot order controller have been removed 
[INFO ] 2024-03-29 18:53:20.047 - [employees] - Remove memory task client succeed, task: employees[66069a1b0aba7657dd9a0e9e] 
[INFO ] 2024-03-29 18:53:20.047 - [employees] - Destroy memory task client cache succeed, task: employees[66069a1b0aba7657dd9a0e9e] 
