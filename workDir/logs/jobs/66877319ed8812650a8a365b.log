[INFO ] 2024-07-10 12:12:55.360 - [Heartbeat-Source<PERSON><PERSON><PERSON>] - Start task milestones: 66877319ed8812650a8a365b(Heartbeat-SourceOracle) 
[INFO ] 2024-07-10 12:12:56.700 - [Heartbeat-SourceOracle] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-10 12:12:57.172 - [Heartbeat-SourceOracle] - The engine receives Heartbeat-SourceOracle task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-10 12:12:57.674 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3e2b631c-bc88-4844-8b58-8904dd4a00b7] start preload schema,table counts: 1 
[INFO ] 2024-07-10 12:12:57.685 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3e2b631c-bc88-4844-8b58-8904dd4a00b7] preload schema finished, cost 1 ms 
[INFO ] 2024-07-10 12:12:57.685 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7778e2e8-9e7c-4bdf-9f79-71205a9add2b] start preload schema,table counts: 1 
[INFO ] 2024-07-10 12:12:57.877 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7778e2e8-9e7c-4bdf-9f79-71205a9add2b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-10 12:12:58.665 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-10 12:12:58.668 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-10 12:12:58.679 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-10 12:12:58.725 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1720152858674,"lastTimes":1720525643289,"lastTN":135,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":19042,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-10 12:12:58.838 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1720152858674,"lastTimes":1720525643289,"lastTN":135,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":19042,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-10 12:12:58.846 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-10 12:12:58.854 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-10 12:12:59.665 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-10 13:25:48.671 - [Heartbeat-SourceOracle] - Start task milestones: 66877319ed8812650a8a365b(Heartbeat-SourceOracle) 
[INFO ] 2024-07-10 13:25:49.515 - [Heartbeat-SourceOracle] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-10 13:25:49.582 - [Heartbeat-SourceOracle] - The engine receives Heartbeat-SourceOracle task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-10 13:25:50.318 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3e2b631c-bc88-4844-8b58-8904dd4a00b7] start preload schema,table counts: 1 
[INFO ] 2024-07-10 13:25:50.322 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7778e2e8-9e7c-4bdf-9f79-71205a9add2b] start preload schema,table counts: 1 
[INFO ] 2024-07-10 13:25:50.324 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3e2b631c-bc88-4844-8b58-8904dd4a00b7] preload schema finished, cost 1 ms 
[INFO ] 2024-07-10 13:25:50.334 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7778e2e8-9e7c-4bdf-9f79-71205a9add2b] preload schema finished, cost 0 ms 
[INFO ] 2024-07-10 13:25:51.017 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" read batch size: 500 
[INFO ] 2024-07-10 13:25:51.024 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Source node "_tapdata_heartbeat_table" event queue capacity: 1000 
[INFO ] 2024-07-10 13:25:51.039 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-07-10 13:25:51.068 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - batch offset found: {},stream offset found: {"syncStage":"Incremental","beginTimes":1720152858674,"lastTimes":1720588836862,"lastTN":4033,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":23075,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-10 13:25:51.278 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Starting stream read, table list: [_tapdata_heartbeat_table], offset: {"syncStage":"Incremental","beginTimes":1720152858674,"lastTimes":1720588836862,"lastTN":4033,"tableStats":{"_tapdata_heartbeat_table":{"insertTotals":23075,"updateTotals":0,"deleteTotals":0}}} 
[INFO ] 2024-07-10 13:25:51.315 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Start [_tapdata_heartbeat_table] stream read 
[INFO ] 2024-07-10 13:25:51.317 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Connector Dummy incremental start succeed, tables: [_tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-10 13:25:51.500 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Write batch size: 1000, max wait ms per batch: 1000 
[WARN ] 2024-07-10 13:48:26.945 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: null, message: PDK retry exception (Server Error Code null): java.sql.SQLTransientConnectionException: HikariPool-5 - Connection is not available, request timed out after 378884ms.
 - Remaining retry 30 time(s)
 - Period 30 second(s) 
[INFO ] 2024-07-10 13:48:57.658 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - [Auto Retry] Method (target_write_record) retry succeed 
[INFO ] 2024-07-10 13:49:27.688 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7778e2e8-9e7c-4bdf-9f79-71205a9add2b] running status set to false 
[INFO ] 2024-07-10 13:49:27.725 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Stop connector 
[INFO ] 2024-07-10 13:49:27.726 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastSourcePdkDataNode-7778e2e8-9e7c-4bdf-9f79-71205a9add2b 
[INFO ] 2024-07-10 13:49:27.728 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - PDK connector node released: HazelcastSourcePdkDataNode-7778e2e8-9e7c-4bdf-9f79-71205a9add2b 
[INFO ] 2024-07-10 13:49:27.728 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7778e2e8-9e7c-4bdf-9f79-71205a9add2b] schema data cleaned 
[INFO ] 2024-07-10 13:49:27.732 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7778e2e8-9e7c-4bdf-9f79-71205a9add2b] monitor closed 
[INFO ] 2024-07-10 13:49:27.732 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[7778e2e8-9e7c-4bdf-9f79-71205a9add2b] close complete, cost 47 ms 
[INFO ] 2024-07-10 13:49:27.787 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3e2b631c-bc88-4844-8b58-8904dd4a00b7] running status set to false 
[INFO ] 2024-07-10 13:49:27.787 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - PDK connector node stopped: HazelcastTargetPdkDataNode-3e2b631c-bc88-4844-8b58-8904dd4a00b7 
[INFO ] 2024-07-10 13:49:27.787 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - PDK connector node released: HazelcastTargetPdkDataNode-3e2b631c-bc88-4844-8b58-8904dd4a00b7 
[INFO ] 2024-07-10 13:49:27.787 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3e2b631c-bc88-4844-8b58-8904dd4a00b7] schema data cleaned 
[INFO ] 2024-07-10 13:49:27.787 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3e2b631c-bc88-4844-8b58-8904dd4a00b7] monitor closed 
[INFO ] 2024-07-10 13:49:27.788 - [Heartbeat-SourceOracle][_tapdata_heartbeat_table] - Node _tapdata_heartbeat_table[3e2b631c-bc88-4844-8b58-8904dd4a00b7] close complete, cost 55 ms 
[INFO ] 2024-07-10 13:49:29.435 - [Heartbeat-SourceOracle] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-10 13:49:29.435 - [Heartbeat-SourceOracle] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@5d8cb0b 
[INFO ] 2024-07-10 13:49:29.557 - [Heartbeat-SourceOracle] - Stop task milestones: 66877319ed8812650a8a365b(Heartbeat-SourceOracle)  
[INFO ] 2024-07-10 13:49:29.557 - [Heartbeat-SourceOracle] - Stopped task aspect(s) 
[INFO ] 2024-07-10 13:49:29.644 - [Heartbeat-SourceOracle] - Snapshot order controller have been removed 
[INFO ] 2024-07-10 13:49:29.645 - [Heartbeat-SourceOracle] - Remove memory task client succeed, task: Heartbeat-SourceOracle[66877319ed8812650a8a365b] 
[INFO ] 2024-07-10 13:49:29.847 - [Heartbeat-SourceOracle] - Destroy memory task client cache succeed, task: Heartbeat-SourceOracle[66877319ed8812650a8a365b] 
