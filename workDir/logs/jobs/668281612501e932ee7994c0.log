[INFO ] 2024-07-01 18:14:00.146 - [任务 6(100)][增强JS] - Node 增强JS[ff62e971-2ffb-46e1-86d5-c070d8fe1a46] start preload schema,table counts: 0 
[INFO ] 2024-07-01 18:14:00.147 - [任务 6(100)][34bac711-cf51-47e6-9c89-579fed300ed7] - Node 34bac711-cf51-47e6-9c89-579fed300ed7[34bac711-cf51-47e6-9c89-579fed300ed7] start preload schema,table counts: 0 
[INFO ] 2024-07-01 18:14:00.150 - [任务 6(100)][34bac711-cf51-47e6-9c89-579fed300ed7] - Node 34bac711-cf51-47e6-9c89-579fed300ed7[34bac711-cf51-47e6-9c89-579fed300ed7] preload schema finished, cost 1 ms 
[INFO ] 2024-07-01 18:14:00.150 - [任务 6(100)][增强JS] - Node 增强JS[ff62e971-2ffb-46e1-86d5-c070d8fe1a46] preload schema finished, cost 1 ms 
[INFO ] 2024-07-01 18:14:00.194 - [任务 6(100)][34bac711-cf51-47e6-9c89-579fed300ed7] - Exception skipping - The current exception does not match the skip exception strategy, message: HazelcastSchemaTargetNode only allows one predecessor node 
[ERROR] 2024-07-01 18:14:00.195 - [任务 6(100)][34bac711-cf51-47e6-9c89-579fed300ed7] - java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node <-- Error Message -->
java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node

<-- Simple Stack Trace -->
Caused by: java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node
	io.tapdata.flow.engine.V2.node.hazelcast.data.HazelcastSchemaTargetNode.doInit(HazelcastSchemaTargetNode.java:112)
	io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	...

<-- Full Stack Trace -->
java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:689)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:575)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:568)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:222)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.IllegalArgumentException: HazelcastSchemaTargetNode only allows one predecessor node
	at io.tapdata.flow.engine.V2.node.hazelcast.data.HazelcastSchemaTargetNode.doInit(HazelcastSchemaTargetNode.java:112)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:217)
	... 12 more

[WARN ] 2024-07-01 18:14:00.285 - [任务 6(100)][增强JS] - The source could not build the executor, please check 
[INFO ] 2024-07-01 18:14:00.286 - [任务 6(100)][增强JS] - Node 增强JS[ff62e971-2ffb-46e1-86d5-c070d8fe1a46] running status set to false 
[INFO ] 2024-07-01 18:14:00.307 - [任务 6(100)][增强JS] - Node 增强JS[ff62e971-2ffb-46e1-86d5-c070d8fe1a46] schema data cleaned 
[INFO ] 2024-07-01 18:14:00.307 - [任务 6(100)][增强JS] - Node 增强JS[ff62e971-2ffb-46e1-86d5-c070d8fe1a46] monitor closed 
[INFO ] 2024-07-01 18:14:00.307 - [任务 6(100)][增强JS] - Node 增强JS[ff62e971-2ffb-46e1-86d5-c070d8fe1a46] close complete, cost 22 ms 
[INFO ] 2024-07-01 18:14:02.783 - [任务 6(100)][34bac711-cf51-47e6-9c89-579fed300ed7] - Node 34bac711-cf51-47e6-9c89-579fed300ed7[34bac711-cf51-47e6-9c89-579fed300ed7] running status set to false 
[INFO ] 2024-07-01 18:14:02.789 - [任务 6(100)][34bac711-cf51-47e6-9c89-579fed300ed7] - Node 34bac711-cf51-47e6-9c89-579fed300ed7[34bac711-cf51-47e6-9c89-579fed300ed7] schema data cleaned 
[INFO ] 2024-07-01 18:14:02.792 - [任务 6(100)][34bac711-cf51-47e6-9c89-579fed300ed7] - Node 34bac711-cf51-47e6-9c89-579fed300ed7[34bac711-cf51-47e6-9c89-579fed300ed7] monitor closed 
[INFO ] 2024-07-01 18:14:02.793 - [任务 6(100)][34bac711-cf51-47e6-9c89-579fed300ed7] - Node 34bac711-cf51-47e6-9c89-579fed300ed7[34bac711-cf51-47e6-9c89-579fed300ed7] close complete, cost 39 ms 
