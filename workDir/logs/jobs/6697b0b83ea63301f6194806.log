[INFO ] 2024-07-17 19:53:29.511 - [来自source的共享挖掘任务] - Start task milestones: 6697b0b83ea63301f6194806(来自source的共享挖掘任务) 
[INFO ] 2024-07-17 19:53:29.956 - [来自source的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-17 19:53:29.956 - [来自source的共享挖掘任务] - The engine receives 来自source的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-17 19:53:30.642 - [来自source的共享挖掘任务][source] - Node source[515fabb8159448f58649aeb82142c81b] start preload schema,table counts: 1 
[INFO ] 2024-07-17 19:53:30.652 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-17 19:53:30.652 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-17 19:53:30.653 - [来自source的共享挖掘任务][source] - Node source[515fabb8159448f58649aeb82142c81b] preload schema finished, cost 2 ms 
[INFO ] 2024-07-17 19:53:30.860 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6697b0b866ab5ede8acc2dd2, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=6697b0733ea63301f61947a0_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_-1377990793, shareCdcTaskId=6697b0b83ea63301f6194806, connectionId=6697b0733ea63301f61947a0) 
[INFO ] 2024-07-17 19:53:31.165 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自source的共享挖掘任务_POLICY', name space: 'tapdatav393.ExternalStorage_SHARE_CDC_-1377990793', head seq: 0, tail seq: -1 
[INFO ] 2024-07-17 19:53:31.371 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav393?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-17 19:53:32.181 - [来自source的共享挖掘任务][source] - Source node "source" read batch size: 2000 
[INFO ] 2024-07-17 19:53:32.184 - [来自source的共享挖掘任务][source] - Source node "source" event queue capacity: 4000 
[INFO ] 2024-07-17 19:53:32.186 - [来自source的共享挖掘任务][source] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-17 19:53:32.320 - [来自source的共享挖掘任务][source] - batch offset found: {},stream offset found: {"cdcOffset":1721217212,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 19:53:32.434 - [来自source的共享挖掘任务][source] - Starting stream read, table list: [POLICY], offset: {"cdcOffset":1721217212,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-17 19:53:32.508 - [来自source的共享挖掘任务][source] - Connector MongoDB incremental start succeed, tables: [POLICY], data change syncing 
[INFO ] 2024-07-17 19:54:54.799 - [来自source的共享挖掘任务][source] - Node source[515fabb8159448f58649aeb82142c81b] running status set to false 
[INFO ] 2024-07-17 19:54:54.802 - [来自source的共享挖掘任务][source] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-515fabb8159448f58649aeb82142c81b 
[INFO ] 2024-07-17 19:54:54.802 - [来自source的共享挖掘任务][source] - PDK connector node released: HazelcastSourcePdkShareCDCNode-515fabb8159448f58649aeb82142c81b 
[INFO ] 2024-07-17 19:54:54.802 - [来自source的共享挖掘任务][source] - Node source[515fabb8159448f58649aeb82142c81b] schema data cleaned 
[INFO ] 2024-07-17 19:54:54.803 - [来自source的共享挖掘任务][source] - Node source[515fabb8159448f58649aeb82142c81b] monitor closed 
[INFO ] 2024-07-17 19:54:54.804 - [来自source的共享挖掘任务][source] - Node source[515fabb8159448f58649aeb82142c81b] close complete, cost 55 ms 
[INFO ] 2024-07-17 19:54:54.804 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[2be8c666e9504ff18574770b43b70eb4] running status set to false 
[INFO ] 2024-07-17 19:54:54.813 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-17 19:54:54.814 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-17 19:54:54.814 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[2be8c666e9504ff18574770b43b70eb4] schema data cleaned 
[INFO ] 2024-07-17 19:54:54.814 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[2be8c666e9504ff18574770b43b70eb4] monitor closed 
[INFO ] 2024-07-17 19:54:54.815 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[2be8c666e9504ff18574770b43b70eb4] close complete, cost 11 ms 
[INFO ] 2024-07-17 19:54:56.769 - [来自source的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 19:54:56.775 - [来自source的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@69f0844b 
[INFO ] 2024-07-17 19:54:56.776 - [来自source的共享挖掘任务] - Stop task milestones: 6697b0b83ea63301f6194806(来自source的共享挖掘任务)  
[INFO ] 2024-07-17 19:54:56.903 - [来自source的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-17 19:54:56.903 - [来自source的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-17 19:54:56.930 - [来自source的共享挖掘任务] - Remove memory task client succeed, task: 来自source的共享挖掘任务[6697b0b83ea63301f6194806] 
[INFO ] 2024-07-17 19:54:56.931 - [来自source的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自source的共享挖掘任务[6697b0b83ea63301f6194806] 
[INFO ] 2024-07-17 19:56:35.707 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自source的共享挖掘任务_POLICY', name space: 'tapdatav393.ExternalStorage_SHARE_CDC_-1377990793', head seq: 0, tail seq: 1 
[INFO ] 2024-07-17 20:05:49.859 - [来自source的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自source的共享挖掘任务_POLICY', name space: 'tapdatav393.ExternalStorage_SHARE_CDC_-1377990793', head seq: 0, tail seq: 3 
