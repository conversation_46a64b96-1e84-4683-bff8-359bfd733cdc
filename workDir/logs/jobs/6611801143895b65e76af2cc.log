[INFO ] 2024-04-07 01:09:38.938 - [Ka<PERSON>ka-Mysq<PERSON>] - Task initialization... 
[INFO ] 2024-04-07 01:09:38.938 - [<PERSON><PERSON>ka-Mysql] - Start task milestones: 6611801143895b65e76af2cc(Kafka-Mysql) 
[INFO ] 2024-04-07 01:09:38.938 - [Kafka-Mysql] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-04-07 01:09:38.938 - [Kafka-Mysql] - The engine receives Kafka-Mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 01:09:38.938 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:09:38.938 - [<PERSON><PERSON>ka-Mysql][KafkaTest2] - Node <PERSON>fkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:09:38.938 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] preload schema finished, cost 14 ms 
[INFO ] 2024-04-07 01:09:38.938 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] preload schema finished, cost 13 ms 
[INFO ] 2024-04-07 01:09:39.504 - [Kafka-Mysql][targetTest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 01:09:39.762 - [Kafka-Mysql][KafkaTest2] - Source node "KafkaTest2" read batch size: 100 
[INFO ] 2024-04-07 01:09:39.762 - [Kafka-Mysql][KafkaTest2] - Source node "KafkaTest2" event queue capacity: 200 
[INFO ] 2024-04-07 01:09:39.763 - [Kafka-Mysql][KafkaTest2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 01:09:39.810 - [Kafka-Mysql][KafkaTest2] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-04-07 01:09:39.810 - [Kafka-Mysql][KafkaTest2] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-04-07 01:09:39.810 - [Kafka-Mysql][KafkaTest2] - Initial sync started 
[INFO ] 2024-04-07 01:09:39.810 - [Kafka-Mysql][KafkaTest2] - Starting batch read, table name: KafkaTest2, offset: null 
[INFO ] 2024-04-07 01:09:39.976 - [Kafka-Mysql][KafkaTest2] - Table KafkaTest2 is going to be initial synced 
[WARN ] 2024-04-07 01:09:39.978 - [Kafka-Mysql][targetTest1] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: targetTest1
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[INFO ] 2024-04-07 01:09:52.309 - [Kafka-Mysql] - Stop task milestones: 6611801143895b65e76af2cc(Kafka-Mysql)  
[INFO ] 2024-04-07 01:09:52.989 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] running status set to false 
[INFO ] 2024-04-07 01:09:52.989 - [Kafka-Mysql][KafkaTest2] - PDK connector node stopped: HazelcastSourcePdkDataNode-fed1ffa2-a4bc-485e-8d97-7ae536da5d45 
[INFO ] 2024-04-07 01:09:52.991 - [Kafka-Mysql][KafkaTest2] - PDK connector node released: HazelcastSourcePdkDataNode-fed1ffa2-a4bc-485e-8d97-7ae536da5d45 
[INFO ] 2024-04-07 01:09:52.991 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] schema data cleaned 
[INFO ] 2024-04-07 01:09:52.993 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] monitor closed 
[INFO ] 2024-04-07 01:09:52.993 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] close complete, cost 41 ms 
[INFO ] 2024-04-07 01:09:53.031 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] running status set to false 
[INFO ] 2024-04-07 01:09:53.032 - [Kafka-Mysql][targetTest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-85c3c7de-72b3-445c-b3d8-2ee9d483271e 
[INFO ] 2024-04-07 01:09:53.032 - [Kafka-Mysql][targetTest1] - PDK connector node released: HazelcastTargetPdkDataNode-85c3c7de-72b3-445c-b3d8-2ee9d483271e 
[INFO ] 2024-04-07 01:09:53.034 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] schema data cleaned 
[INFO ] 2024-04-07 01:09:53.037 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] monitor closed 
[INFO ] 2024-04-07 01:09:53.037 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] close complete, cost 43 ms 
[INFO ] 2024-04-07 01:09:53.239 - [Kafka-Mysql][targetTest1] - [Auto Retry] Method (target_write_record) retry succeed 
[INFO ] 2024-04-07 01:09:53.426 - [Kafka-Mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 01:09:53.429 - [Kafka-Mysql] - Stopped task aspect(s) 
[INFO ] 2024-04-07 01:09:53.430 - [Kafka-Mysql] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 01:09:53.471 - [Kafka-Mysql] - Remove memory task client succeed, task: Kafka-Mysql[6611801143895b65e76af2cc] 
[INFO ] 2024-04-07 01:09:53.471 - [Kafka-Mysql] - Destroy memory task client cache succeed, task: Kafka-Mysql[6611801143895b65e76af2cc] 
[INFO ] 2024-04-07 01:10:12.935 - [Kafka-Mysql] - Task initialization... 
[INFO ] 2024-04-07 01:10:12.977 - [Kafka-Mysql] - Start task milestones: 6611801143895b65e76af2cc(Kafka-Mysql) 
[INFO ] 2024-04-07 01:10:12.978 - [Kafka-Mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 01:10:13.130 - [Kafka-Mysql] - The engine receives Kafka-Mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 01:10:13.130 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:10:13.164 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:10:13.170 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] preload schema finished, cost 28 ms 
[INFO ] 2024-04-07 01:10:13.174 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] preload schema finished, cost 30 ms 
[INFO ] 2024-04-07 01:10:13.517 - [Kafka-Mysql][targetTest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 01:10:13.546 - [Kafka-Mysql][targetTest1] - Sync progress not exists, will run task as first time 
[INFO ] 2024-04-07 01:10:13.547 - [Kafka-Mysql][targetTest1] - Table "test.targetTest1" exists, skip auto create table 
[INFO ] 2024-04-07 01:10:13.547 - [Kafka-Mysql][targetTest1] - The table targetTest1 has already exist. 
[INFO ] 2024-04-07 01:10:13.621 - [Kafka-Mysql][KafkaTest2] - Source node "KafkaTest2" read batch size: 100 
[INFO ] 2024-04-07 01:10:13.622 - [Kafka-Mysql][KafkaTest2] - Source node "KafkaTest2" event queue capacity: 200 
[INFO ] 2024-04-07 01:10:13.622 - [Kafka-Mysql][KafkaTest2] - Sync progress not exists, will run task as first time 
[INFO ] 2024-04-07 01:10:13.622 - [Kafka-Mysql][KafkaTest2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 01:10:13.624 - [Kafka-Mysql][KafkaTest2] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-04-07 01:10:13.693 - [Kafka-Mysql][KafkaTest2] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-04-07 01:10:13.693 - [Kafka-Mysql][KafkaTest2] - Initial sync started 
[INFO ] 2024-04-07 01:10:13.693 - [Kafka-Mysql][KafkaTest2] - Starting batch read, table name: KafkaTest2, offset: null 
[INFO ] 2024-04-07 01:10:13.694 - [Kafka-Mysql][KafkaTest2] - Table KafkaTest2 is going to be initial synced 
[WARN ] 2024-04-07 01:10:13.894 - [Kafka-Mysql][targetTest1] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: targetTest1
 - Remaining retry 15 time(s)
 - Period 60 second(s) 
[WARN ] 2024-04-07 01:11:14.126 - [Kafka-Mysql][targetTest1] - [Auto Retry] Method (target_write_record) encountered an error, triggering auto retry.
 - Error code: 15019, message: Execute PDK method: TARGET_WRITE_RECORD, tableName: targetTest1
 - Remaining retry 14 time(s)
 - Period 60 second(s) 
[INFO ] 2024-04-07 01:11:50.556 - [Kafka-Mysql] - Stop task milestones: 6611801143895b65e76af2cc(Kafka-Mysql)  
[INFO ] 2024-04-07 01:11:52.116 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] running status set to false 
[INFO ] 2024-04-07 01:11:52.149 - [Kafka-Mysql][KafkaTest2] - PDK connector node stopped: HazelcastSourcePdkDataNode-fed1ffa2-a4bc-485e-8d97-7ae536da5d45 
[INFO ] 2024-04-07 01:11:52.149 - [Kafka-Mysql][KafkaTest2] - PDK connector node released: HazelcastSourcePdkDataNode-fed1ffa2-a4bc-485e-8d97-7ae536da5d45 
[INFO ] 2024-04-07 01:11:52.150 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] schema data cleaned 
[INFO ] 2024-04-07 01:11:52.150 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] monitor closed 
[INFO ] 2024-04-07 01:11:52.151 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] close complete, cost 39 ms 
[INFO ] 2024-04-07 01:11:52.151 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] running status set to false 
[INFO ] 2024-04-07 01:11:52.190 - [Kafka-Mysql][targetTest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-85c3c7de-72b3-445c-b3d8-2ee9d483271e 
[INFO ] 2024-04-07 01:11:52.191 - [Kafka-Mysql][targetTest1] - PDK connector node released: HazelcastTargetPdkDataNode-85c3c7de-72b3-445c-b3d8-2ee9d483271e 
[INFO ] 2024-04-07 01:11:52.191 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] schema data cleaned 
[INFO ] 2024-04-07 01:11:52.191 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] monitor closed 
[INFO ] 2024-04-07 01:11:52.245 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] close complete, cost 40 ms 
[ERROR] 2024-04-07 01:11:52.246 - [Kafka-Mysql][targetTest1] - io.tapdata.exception.NodeException: Node is stopped, need to exit write_record <-- Error Message -->
io.tapdata.exception.NodeException: Node is stopped, need to exit write_record

<-- Simple Stack Trace -->
Caused by: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$47(HazelcastTargetPdkDataNode.java:662)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	...

<-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:66)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:152)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$48(HazelcastTargetPdkDataNode.java:657)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:651)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$17(HazelcastTargetPdkDataNode.java:360)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:360)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:445)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.tapdata.exception.NodeException: Node is stopped, need to exit write_record
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$47(HazelcastTargetPdkDataNode.java:662)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 25 more

[INFO ] 2024-04-07 01:11:53.621 - [Kafka-Mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 01:11:53.621 - [Kafka-Mysql] - Stopped task aspect(s) 
[INFO ] 2024-04-07 01:11:53.622 - [Kafka-Mysql] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 01:11:53.664 - [Kafka-Mysql] - Remove memory task client succeed, task: Kafka-Mysql[6611801143895b65e76af2cc] 
[INFO ] 2024-04-07 01:11:53.665 - [Kafka-Mysql] - Destroy memory task client cache succeed, task: Kafka-Mysql[6611801143895b65e76af2cc] 
[INFO ] 2024-04-07 01:13:20.192 - [Kafka-Mysql] - Task initialization... 
[INFO ] 2024-04-07 01:13:20.223 - [Kafka-Mysql] - Start task milestones: 6611801143895b65e76af2cc(Kafka-Mysql) 
[INFO ] 2024-04-07 01:13:20.223 - [Kafka-Mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 01:13:20.285 - [Kafka-Mysql] - The engine receives Kafka-Mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 01:13:20.340 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:13:20.340 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:13:20.365 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] preload schema finished, cost 24 ms 
[INFO ] 2024-04-07 01:13:20.365 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] preload schema finished, cost 23 ms 
[INFO ] 2024-04-07 01:13:21.360 - [Kafka-Mysql][KafkaTest2] - Source node "KafkaTest2" read batch size: 100 
[INFO ] 2024-04-07 01:13:21.360 - [Kafka-Mysql][KafkaTest2] - Source node "KafkaTest2" event queue capacity: 200 
[INFO ] 2024-04-07 01:13:21.361 - [Kafka-Mysql][KafkaTest2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 01:13:21.361 - [Kafka-Mysql][KafkaTest2] - batch offset found: {},stream offset found: [] 
[INFO ] 2024-04-07 01:13:21.395 - [Kafka-Mysql][targetTest1] - Write batch size: 100, max wait ms per batch: 500 
[WARN ] 2024-04-07 01:13:21.447 - [Kafka-Mysql][KafkaTest2] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-04-07 01:13:21.447 - [Kafka-Mysql][KafkaTest2] - Initial sync started 
[INFO ] 2024-04-07 01:13:21.447 - [Kafka-Mysql][KafkaTest2] - Starting batch read, table name: KafkaTest2, offset: null 
[INFO ] 2024-04-07 01:13:21.447 - [Kafka-Mysql][KafkaTest2] - Table KafkaTest2 is going to be initial synced 
[ERROR] 2024-04-07 01:13:21.616 - [Kafka-Mysql][targetTest1] - Execute PDK method: TARGET_WRITE_RECORD, tableName: targetTest1 <-- Error Message -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: targetTest1

<-- Simple Stack Trace -->
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Out of range value for column 'created' at row 1
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	com.mysql.cj.jdbc.ServerPreparedStatement.serverExecute(ServerPreparedStatement.java:555)
	com.mysql.cj.jdbc.ServerPreparedStatement.executeInternal(ServerPreparedStatement.java:339)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	...

<-- Full Stack Trace -->
Execute PDK method: TARGET_WRITE_RECORD, tableName: targetTest1
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$47(HazelcastTargetPdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$writeRecord$48(HazelcastTargetPdkDataNode.java:657)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.writeRecord(HazelcastTargetPdkDataNode.java:651)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$processEvents$17(HazelcastTargetPdkDataNode.java:360)
	at java.util.HashMap.forEach(HashMap.java:1290)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.processEvents(HazelcastTargetPdkDataNode.java:360)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.handleTapdataEvents(HazelcastTargetPdkBaseNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.initialProcessEvents(HazelcastTargetPdkBaseNode.java:523)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.lambda$processTargetEvents$7(HazelcastTargetPdkBaseNode.java:479)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.dispatchTapdataEvents(HazelcastTargetPdkBaseNode.java:445)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.processTargetEvents(HazelcastTargetPdkBaseNode.java:451)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkBaseNode.queueConsume(HazelcastTargetPdkBaseNode.java:497)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Insert data failed: Data truncation: Out of range value for column 'created' at row 1
 Sql: HikariProxyPreparedStatement@623372166 wrapping com.mysql.cj.jdbc.ServerPreparedStatement[9]: INSERT INTO `test`.`targetTest1`(`created`,`id`,`title`) values(1712422918172,'00004a78-e236-4340-ba98-a4f46831d856','6iGbHl33') ON DUPLICATE KEY UPDATE `created`=values(`created`), `id`=values(`id`), `title`=values(`title`)
	at io.tapdata.connector.mysql.writer.MysqlJdbcOneByOneWriter.lambda$doInsert$0(MysqlJdbcOneByOneWriter.java:155)
	at io.tapdata.connector.mysql.util.ExceptionWrapper.wrap(ExceptionWrapper.java:124)
	at io.tapdata.connector.mysql.writer.MysqlJdbcOneByOneWriter.doInsert(MysqlJdbcOneByOneWriter.java:153)
	at io.tapdata.connector.mysql.writer.MysqlJdbcOneByOneWriter.doInsertOne(MysqlJdbcOneByOneWriter.java:126)
	at io.tapdata.connector.mysql.writer.MysqlJdbcOneByOneWriter.write(MysqlJdbcOneByOneWriter.java:63)
	at io.tapdata.connector.mysql.writer.MysqlSqlBatchWriter.doOneByOne(MysqlSqlBatchWriter.java:109)
	at io.tapdata.connector.mysql.writer.MysqlSqlBatchWriter.lambda$write$0(MysqlSqlBatchWriter.java:66)
	at io.tapdata.connector.mysql.writer.MysqlWriter.dispatch(MysqlWriter.java:148)
	at io.tapdata.connector.mysql.writer.MysqlSqlBatchWriter.write(MysqlSqlBatchWriter.java:57)
	at io.tapdata.connector.mysql.MysqlConnector.writeRecord(MysqlConnector.java:350)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastTargetPdkDataNode.lambda$null$47(HazelcastTargetPdkDataNode.java:705)
	... 24 more
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Out of range value for column 'created' at row 1
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ServerPreparedStatement.serverExecute(ServerPreparedStatement.java:555)
	at com.mysql.cj.jdbc.ServerPreparedStatement.executeInternal(ServerPreparedStatement.java:339)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1061)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdateInternal(ClientPreparedStatement.java:1009)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeLargeUpdate(ClientPreparedStatement.java:1320)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeUpdate(ClientPreparedStatement.java:994)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at io.tapdata.connector.mysql.writer.MysqlJdbcOneByOneWriter.doInsert(MysqlJdbcOneByOneWriter.java:151)
	... 32 more

[INFO ] 2024-04-07 01:13:21.616 - [Kafka-Mysql][targetTest1] - Job suspend in error handle 
[INFO ] 2024-04-07 01:13:22.593 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] running status set to false 
[INFO ] 2024-04-07 01:13:22.610 - [Kafka-Mysql][KafkaTest2] - PDK connector node stopped: HazelcastSourcePdkDataNode-fed1ffa2-a4bc-485e-8d97-7ae536da5d45 
[INFO ] 2024-04-07 01:13:22.610 - [Kafka-Mysql][KafkaTest2] - PDK connector node released: HazelcastSourcePdkDataNode-fed1ffa2-a4bc-485e-8d97-7ae536da5d45 
[INFO ] 2024-04-07 01:13:22.610 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] schema data cleaned 
[INFO ] 2024-04-07 01:13:22.611 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] monitor closed 
[INFO ] 2024-04-07 01:13:22.612 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] close complete, cost 24 ms 
[INFO ] 2024-04-07 01:13:22.612 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] running status set to false 
[INFO ] 2024-04-07 01:13:22.636 - [Kafka-Mysql][targetTest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-85c3c7de-72b3-445c-b3d8-2ee9d483271e 
[INFO ] 2024-04-07 01:13:22.636 - [Kafka-Mysql][targetTest1] - PDK connector node released: HazelcastTargetPdkDataNode-85c3c7de-72b3-445c-b3d8-2ee9d483271e 
[INFO ] 2024-04-07 01:13:22.636 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] schema data cleaned 
[INFO ] 2024-04-07 01:13:22.636 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] monitor closed 
[INFO ] 2024-04-07 01:13:22.654 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] close complete, cost 24 ms 
[INFO ] 2024-04-07 01:13:22.654 - [Kafka-Mysql][KafkaTest2] - Initial sync completed 
[INFO ] 2024-04-07 01:13:22.658 - [Kafka-Mysql][KafkaTest2] - Incremental sync starting... 
[INFO ] 2024-04-07 01:13:22.658 - [Kafka-Mysql][KafkaTest2] - Incremental sync completed 
[INFO ] 2024-04-07 01:13:23.787 - [Kafka-Mysql] - Task [Kafka-Mysql] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-04-07 01:13:23.788 - [Kafka-Mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 01:13:23.817 - [Kafka-Mysql] - Stop task milestones: 6611801143895b65e76af2cc(Kafka-Mysql)  
[INFO ] 2024-04-07 01:13:23.817 - [Kafka-Mysql] - Stopped task aspect(s) 
[INFO ] 2024-04-07 01:13:23.817 - [Kafka-Mysql] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 01:13:23.847 - [Kafka-Mysql] - Remove memory task client succeed, task: Kafka-Mysql[6611801143895b65e76af2cc] 
[INFO ] 2024-04-07 01:13:23.849 - [Kafka-Mysql] - Destroy memory task client cache succeed, task: Kafka-Mysql[6611801143895b65e76af2cc] 
[INFO ] 2024-04-07 01:17:29.338 - [Kafka-Mysql] - Task initialization... 
[INFO ] 2024-04-07 01:17:29.469 - [Kafka-Mysql] - Start task milestones: 6611801143895b65e76af2cc(Kafka-Mysql) 
[INFO ] 2024-04-07 01:17:29.470 - [Kafka-Mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 01:17:29.783 - [Kafka-Mysql] - The engine receives Kafka-Mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 01:17:29.783 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:17:29.783 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:17:29.818 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] preload schema finished, cost 34 ms 
[INFO ] 2024-04-07 01:17:29.818 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] preload schema finished, cost 34 ms 
[INFO ] 2024-04-07 01:17:30.634 - [Kafka-Mysql][KafkaTest2] - Source node "KafkaTest2" read batch size: 100 
[INFO ] 2024-04-07 01:17:30.635 - [Kafka-Mysql][KafkaTest2] - Source node "KafkaTest2" event queue capacity: 200 
[INFO ] 2024-04-07 01:17:30.636 - [Kafka-Mysql][KafkaTest2] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-04-07 01:17:30.636 - [Kafka-Mysql][KafkaTest2] - batch offset found: {},stream offset found: [] 
[WARN ] 2024-04-07 01:17:30.717 - [Kafka-Mysql][KafkaTest2] - PDK node does not support table batch count: kafka-io.tapdata-1.0-SNAPSHOT-public 
[INFO ] 2024-04-07 01:17:30.717 - [Kafka-Mysql][KafkaTest2] - Initial sync started 
[INFO ] 2024-04-07 01:17:30.717 - [Kafka-Mysql][KafkaTest2] - Starting batch read, table name: KafkaTest2, offset: null 
[INFO ] 2024-04-07 01:17:30.720 - [Kafka-Mysql][KafkaTest2] - Table KafkaTest2 is going to be initial synced 
[INFO ] 2024-04-07 01:17:30.720 - [Kafka-Mysql][targetTest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 01:17:38.600 - [Kafka-Mysql][KafkaTest2] - Initial sync completed 
[INFO ] 2024-04-07 01:17:38.600 - [Kafka-Mysql][KafkaTest2] - Incremental sync starting... 
[INFO ] 2024-04-07 01:17:38.600 - [Kafka-Mysql][KafkaTest2] - Initial sync completed 
[INFO ] 2024-04-07 01:17:38.601 - [Kafka-Mysql][KafkaTest2] - Starting stream read, table list: [KafkaTest2], offset: [] 
[INFO ] 2024-04-07 01:19:05.032 - [Kafka-Mysql][KafkaTest2] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@22c9d6d1: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"firstName","nullable":true,"partitionKey":false,"primaryKey":false}],"referenceTime":1712423945019,"tableId":"KafkaTest2","time":1712423945019,"type":209} 
[INFO ] 2024-04-07 01:19:05.032 - [Kafka-Mysql][KafkaTest2] - Alter table in memory, qualified name: T_kafka_io_tapdata_1_0-SNAPSHOT_KafkaTest2_661147dd05642634b1daa0dd_6611801143895b65e76af2cc 
[INFO ] 2024-04-07 01:19:05.232 - [Kafka-Mysql][KafkaTest2] - Alter table schema transform finished 
[INFO ] 2024-04-07 01:22:46.515 - [Kafka-Mysql][KafkaTest2] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='KafkaTest2', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@287f021a} 
[INFO ] 2024-04-07 01:22:46.566 - [Kafka-Mysql][KafkaTest2] - Alter table in memory, qualified name: T_kafka_io_tapdata_1_0-SNAPSHOT_KafkaTest2_661147dd05642634b1daa0dd_6611801143895b65e76af2cc 
[INFO ] 2024-04-07 01:22:46.566 - [Kafka-Mysql][KafkaTest2] - Alter table schema transform finished 
[INFO ] 2024-04-07 01:23:08.972 - [Kafka-Mysql][KafkaTest2] - Source node received an ddl event: TapDropFieldEvent{tableId='KafkaTest2', fieldName='lastName'} 
[INFO ] 2024-04-07 01:23:09.006 - [Kafka-Mysql][KafkaTest2] - Alter table in memory, qualified name: T_kafka_io_tapdata_1_0-SNAPSHOT_KafkaTest2_661147dd05642634b1daa0dd_6611801143895b65e76af2cc 
[INFO ] 2024-04-07 01:23:09.009 - [Kafka-Mysql][KafkaTest2] - Alter table schema transform finished 
[INFO ] 2024-04-07 01:23:41.212 - [Kafka-Mysql] - Stop task milestones: 6611801143895b65e76af2cc(Kafka-Mysql)  
[INFO ] 2024-04-07 01:23:41.510 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] running status set to false 
[ERROR] 2024-04-07 01:23:41.528 - [Kafka-Mysql][KafkaTest2] - java.lang.InterruptedException <-- Full Stack Trace -->
java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:220)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.lang.InterruptedException
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	... 6 more
Caused by: java.lang.InterruptedException
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 10 more
Caused by: java.lang.InterruptedException
	at io.tapdata.connector.kafka.KafkaExceptionCollector.collectTerminateByServer(KafkaExceptionCollector.java:26)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:326)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 16 more
Caused by: java.lang.InterruptedException
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.maybeThrowInterruptException(ConsumerNetworkClient.java:520)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:281)
	at org.apache.kafka.clients.consumer.internals.ConsumerNetworkClient.poll(ConsumerNetworkClient.java:236)
	at org.apache.kafka.clients.consumer.KafkaConsumer.pollForFetches(KafkaConsumer.java:1297)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1238)
	at org.apache.kafka.clients.consumer.KafkaConsumer.poll(KafkaConsumer.java:1211)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:690)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:324)
	... 18 more

[INFO ] 2024-04-07 01:23:41.528 - [Kafka-Mysql][KafkaTest2] - PDK connector node stopped: HazelcastSourcePdkDataNode-fed1ffa2-a4bc-485e-8d97-7ae536da5d45 
[INFO ] 2024-04-07 01:23:41.529 - [Kafka-Mysql][KafkaTest2] - PDK connector node released: HazelcastSourcePdkDataNode-fed1ffa2-a4bc-485e-8d97-7ae536da5d45 
[INFO ] 2024-04-07 01:23:41.529 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] schema data cleaned 
[INFO ] 2024-04-07 01:23:41.530 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] monitor closed 
[INFO ] 2024-04-07 01:23:41.530 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] close complete, cost 20 ms 
[INFO ] 2024-04-07 01:23:41.550 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] running status set to false 
[INFO ] 2024-04-07 01:23:41.550 - [Kafka-Mysql][targetTest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-85c3c7de-72b3-445c-b3d8-2ee9d483271e 
[INFO ] 2024-04-07 01:23:41.550 - [Kafka-Mysql][targetTest1] - PDK connector node released: HazelcastTargetPdkDataNode-85c3c7de-72b3-445c-b3d8-2ee9d483271e 
[INFO ] 2024-04-07 01:23:41.550 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] schema data cleaned 
[INFO ] 2024-04-07 01:23:41.551 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] monitor closed 
[INFO ] 2024-04-07 01:23:41.756 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] close complete, cost 20 ms 
[INFO ] 2024-04-07 01:23:44.459 - [Kafka-Mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 01:23:44.460 - [Kafka-Mysql] - Stopped task aspect(s) 
[INFO ] 2024-04-07 01:23:44.460 - [Kafka-Mysql] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 01:23:44.491 - [Kafka-Mysql] - Remove memory task client succeed, task: Kafka-Mysql[6611801143895b65e76af2cc] 
[INFO ] 2024-04-07 01:23:44.492 - [Kafka-Mysql] - Destroy memory task client cache succeed, task: Kafka-Mysql[6611801143895b65e76af2cc] 
[INFO ] 2024-04-07 01:37:51.415 - [Kafka-Mysql] - Task initialization... 
[INFO ] 2024-04-07 01:37:51.456 - [Kafka-Mysql] - Start task milestones: 6611801143895b65e76af2cc(Kafka-Mysql) 
[INFO ] 2024-04-07 01:37:51.456 - [Kafka-Mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 01:37:51.556 - [Kafka-Mysql] - The engine receives Kafka-Mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 01:37:51.556 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:37:51.556 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:37:51.573 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] preload schema finished, cost 17 ms 
[INFO ] 2024-04-07 01:37:51.573 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] preload schema finished, cost 17 ms 
[INFO ] 2024-04-07 01:37:51.949 - [Kafka-Mysql][targetTest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 01:37:52.023 - [Kafka-Mysql][KafkaTest2] - Source node "KafkaTest2" read batch size: 100 
[INFO ] 2024-04-07 01:37:52.023 - [Kafka-Mysql][KafkaTest2] - Source node "KafkaTest2" event queue capacity: 200 
[INFO ] 2024-04-07 01:37:52.023 - [Kafka-Mysql][KafkaTest2] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-04-07 01:37:52.076 - [Kafka-Mysql][KafkaTest2] - batch offset found: {"KafkaTest2":[]},stream offset found: [] 
[INFO ] 2024-04-07 01:37:52.076 - [Kafka-Mysql][KafkaTest2] - Incremental sync starting... 
[INFO ] 2024-04-07 01:37:52.076 - [Kafka-Mysql][KafkaTest2] - Initial sync completed 
[INFO ] 2024-04-07 01:37:52.081 - [Kafka-Mysql][KafkaTest2] - Starting stream read, table list: [KafkaTest2], offset: [] 
[INFO ] 2024-04-07 01:37:54.532 - [Kafka-Mysql][KafkaTest2] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@32c0f93d: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"firstName","nullable":true,"partitionKey":false,"primaryKey":false}],"referenceTime":1712425074519,"tableId":"KafkaTest2","time":1712425074519,"type":209} 
[INFO ] 2024-04-07 01:37:54.568 - [Kafka-Mysql][KafkaTest2] - Alter table in memory, qualified name: T_kafka_io_tapdata_1_0-SNAPSHOT_KafkaTest2_661147dd05642634b1daa0dd_6611801143895b65e76af2cc 
[INFO ] 2024-04-07 01:37:54.568 - [Kafka-Mysql][KafkaTest2] - Alter table schema transform finished 
[INFO ] 2024-04-07 01:37:57.221 - [Kafka-Mysql][KafkaTest2] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='KafkaTest2', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@f65f622} 
[INFO ] 2024-04-07 01:37:57.221 - [Kafka-Mysql][KafkaTest2] - Alter table in memory, qualified name: T_kafka_io_tapdata_1_0-SNAPSHOT_KafkaTest2_661147dd05642634b1daa0dd_6611801143895b65e76af2cc 
[INFO ] 2024-04-07 01:37:57.424 - [Kafka-Mysql][KafkaTest2] - Alter table schema transform finished 
[INFO ] 2024-04-07 01:37:57.491 - [Kafka-Mysql][KafkaTest2] - Source node received an ddl event: TapDropFieldEvent{tableId='KafkaTest2', fieldName='lastName'} 
[INFO ] 2024-04-07 01:37:57.513 - [Kafka-Mysql][KafkaTest2] - Alter table in memory, qualified name: T_kafka_io_tapdata_1_0-SNAPSHOT_KafkaTest2_661147dd05642634b1daa0dd_6611801143895b65e76af2cc 
[INFO ] 2024-04-07 01:37:57.513 - [Kafka-Mysql][KafkaTest2] - Alter table schema transform finished 
[INFO ] 2024-04-07 01:37:58.294 - [Kafka-Mysql][KafkaTest2] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@6abbefe6: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"firstName","nullable":true,"partitionKey":false,"primaryKey":false}],"referenceTime":1712425078291,"tableId":"KafkaTest2","time":1712425078291,"type":209} 
[INFO ] 2024-04-07 01:37:58.294 - [Kafka-Mysql][KafkaTest2] - Alter table in memory, qualified name: T_kafka_io_tapdata_1_0-SNAPSHOT_KafkaTest2_661147dd05642634b1daa0dd_6611801143895b65e76af2cc 
[INFO ] 2024-04-07 01:37:58.314 - [Kafka-Mysql][KafkaTest2] - Alter table schema transform finished 
[ERROR] 2024-04-07 01:37:58.314 - [Kafka-Mysql][KafkaTest2] - java.lang.RuntimeException: event is null <-- Error Message -->
java.lang.RuntimeException: event is null

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: event is null
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:44)
	io.tapdata.entity.logger.Log.error(Log.java:43)
	io.tapdata.connector.kafka.KafkaService.makeCustomMessage(KafkaService.java:717)
	io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:696)
	io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:324)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: event is null
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:46)
	at io.tapdata.entity.logger.Log.error(Log.java:43)
	at io.tapdata.connector.kafka.KafkaService.makeCustomMessage(KafkaService.java:717)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:696)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:324)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: event is null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:44)
	... 22 more

[INFO ] 2024-04-07 01:37:58.332 - [Kafka-Mysql][KafkaTest2] - Job suspend in error handle 
[INFO ] 2024-04-07 01:37:58.333 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] running status set to false 
[INFO ] 2024-04-07 01:37:58.333 - [Kafka-Mysql][KafkaTest2] - Incremental sync completed 
[INFO ] 2024-04-07 01:37:58.350 - [Kafka-Mysql][KafkaTest2] - PDK connector node stopped: HazelcastSourcePdkDataNode-fed1ffa2-a4bc-485e-8d97-7ae536da5d45 
[INFO ] 2024-04-07 01:37:58.351 - [Kafka-Mysql][KafkaTest2] - PDK connector node released: HazelcastSourcePdkDataNode-fed1ffa2-a4bc-485e-8d97-7ae536da5d45 
[INFO ] 2024-04-07 01:37:58.351 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] schema data cleaned 
[INFO ] 2024-04-07 01:37:58.352 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] monitor closed 
[INFO ] 2024-04-07 01:37:58.354 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] close complete, cost 22 ms 
[INFO ] 2024-04-07 01:37:58.354 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] running status set to false 
[INFO ] 2024-04-07 01:37:58.424 - [Kafka-Mysql][targetTest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-85c3c7de-72b3-445c-b3d8-2ee9d483271e 
[INFO ] 2024-04-07 01:37:58.424 - [Kafka-Mysql][targetTest1] - PDK connector node released: HazelcastTargetPdkDataNode-85c3c7de-72b3-445c-b3d8-2ee9d483271e 
[INFO ] 2024-04-07 01:37:58.425 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] schema data cleaned 
[INFO ] 2024-04-07 01:37:58.425 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] monitor closed 
[INFO ] 2024-04-07 01:37:58.630 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] close complete, cost 73 ms 
[INFO ] 2024-04-07 01:38:00.435 - [Kafka-Mysql] - Task [Kafka-Mysql] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-04-07 01:38:00.459 - [Kafka-Mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 01:38:00.463 - [Kafka-Mysql] - Stop task milestones: 6611801143895b65e76af2cc(Kafka-Mysql)  
[INFO ] 2024-04-07 01:38:00.476 - [Kafka-Mysql] - Stopped task aspect(s) 
[INFO ] 2024-04-07 01:38:00.477 - [Kafka-Mysql] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 01:38:00.499 - [Kafka-Mysql] - Remove memory task client succeed, task: Kafka-Mysql[6611801143895b65e76af2cc] 
[INFO ] 2024-04-07 01:38:00.504 - [Kafka-Mysql] - Destroy memory task client cache succeed, task: Kafka-Mysql[6611801143895b65e76af2cc] 
[INFO ] 2024-04-07 01:38:23.001 - [Kafka-Mysql] - Task initialization... 
[INFO ] 2024-04-07 01:38:23.001 - [Kafka-Mysql] - Start task milestones: 6611801143895b65e76af2cc(Kafka-Mysql) 
[INFO ] 2024-04-07 01:38:23.070 - [Kafka-Mysql] - Node performs snapshot read asynchronously 
[INFO ] 2024-04-07 01:38:23.070 - [Kafka-Mysql] - The engine receives Kafka-Mysql task data from TM and will continue to run tasks by jet 
[INFO ] 2024-04-07 01:38:23.109 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:38:23.109 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] start preload schema,table counts: 1 
[INFO ] 2024-04-07 01:38:23.118 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] preload schema finished, cost 7 ms 
[INFO ] 2024-04-07 01:38:23.118 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] preload schema finished, cost 7 ms 
[INFO ] 2024-04-07 01:38:23.525 - [Kafka-Mysql][targetTest1] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-04-07 01:38:23.606 - [Kafka-Mysql][KafkaTest2] - Source node "KafkaTest2" read batch size: 100 
[INFO ] 2024-04-07 01:38:23.606 - [Kafka-Mysql][KafkaTest2] - Source node "KafkaTest2" event queue capacity: 200 
[INFO ] 2024-04-07 01:38:23.607 - [Kafka-Mysql][KafkaTest2] - Found exists breakpoint, will decode batch/stream offset 
[INFO ] 2024-04-07 01:38:23.608 - [Kafka-Mysql][KafkaTest2] - batch offset found: {},stream offset found: [] 
[INFO ] 2024-04-07 01:38:23.669 - [Kafka-Mysql][KafkaTest2] - Incremental sync starting... 
[INFO ] 2024-04-07 01:38:23.669 - [Kafka-Mysql][KafkaTest2] - Initial sync completed 
[INFO ] 2024-04-07 01:38:23.875 - [Kafka-Mysql][KafkaTest2] - Starting stream read, table list: [KafkaTest2], offset: [] 
[INFO ] 2024-04-07 01:38:34.146 - [Kafka-Mysql][KafkaTest2] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@4bbdc88e: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"firstName","nullable":true,"partitionKey":false,"primaryKey":false}],"referenceTime":1712425114089,"tableId":"KafkaTest2","time":1712425114089,"type":209} 
[INFO ] 2024-04-07 01:38:36.598 - [Kafka-Mysql][KafkaTest2] - Alter table in memory, qualified name: T_kafka_io_tapdata_1_0-SNAPSHOT_KafkaTest2_661147dd05642634b1daa0dd_6611801143895b65e76af2cc 
[INFO ] 2024-04-07 01:38:38.457 - [Kafka-Mysql][KafkaTest2] - Alter table schema transform finished 
[INFO ] 2024-04-07 01:38:56.661 - [Kafka-Mysql][KafkaTest2] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='KafkaTest2', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@634c5e9e} 
[INFO ] 2024-04-07 01:39:00.806 - [Kafka-Mysql][KafkaTest2] - Alter table in memory, qualified name: T_kafka_io_tapdata_1_0-SNAPSHOT_KafkaTest2_661147dd05642634b1daa0dd_6611801143895b65e76af2cc 
[INFO ] 2024-04-07 01:39:01.013 - [Kafka-Mysql][KafkaTest2] - Alter table schema transform finished 
[INFO ] 2024-04-07 01:39:11.588 - [Kafka-Mysql][KafkaTest2] - Source node received an ddl event: TapDropFieldEvent{tableId='KafkaTest2', fieldName='lastName'} 
[INFO ] 2024-04-07 01:39:11.653 - [Kafka-Mysql][KafkaTest2] - Alter table in memory, qualified name: T_kafka_io_tapdata_1_0-SNAPSHOT_KafkaTest2_661147dd05642634b1daa0dd_6611801143895b65e76af2cc 
[INFO ] 2024-04-07 01:39:11.653 - [Kafka-Mysql][KafkaTest2] - Alter table schema transform finished 
[INFO ] 2024-04-07 01:39:26.490 - [Kafka-Mysql][KafkaTest2] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@647ffde3: {"newFields":[{"autoInc":false,"dataType":"STRING","name":"firstName","nullable":true,"partitionKey":false,"primaryKey":false}],"referenceTime":1712425166372,"tableId":"KafkaTest2","time":1712425166372,"type":209} 
[INFO ] 2024-04-07 01:39:26.494 - [Kafka-Mysql][KafkaTest2] - Alter table in memory, qualified name: T_kafka_io_tapdata_1_0-SNAPSHOT_KafkaTest2_661147dd05642634b1daa0dd_6611801143895b65e76af2cc 
[INFO ] 2024-04-07 01:39:26.581 - [Kafka-Mysql][KafkaTest2] - Alter table schema transform finished 
[ERROR] 2024-04-07 01:39:26.582 - [Kafka-Mysql][KafkaTest2] - java.lang.RuntimeException: event is null <-- Error Message -->
java.lang.RuntimeException: event is null

<-- Simple Stack Trace -->
Caused by: java.lang.RuntimeException: event is null
	io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:44)
	io.tapdata.entity.logger.Log.error(Log.java:43)
	io.tapdata.connector.kafka.KafkaService.makeCustomMessage(KafkaService.java:717)
	io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:696)
	io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:324)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: event is null
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:46)
	at io.tapdata.entity.logger.Log.error(Log.java:43)
	at io.tapdata.connector.kafka.KafkaService.makeCustomMessage(KafkaService.java:717)
	at io.tapdata.connector.kafka.KafkaService.streamConsume(KafkaService.java:696)
	at io.tapdata.connector.kafka.KafkaConnector.streamRead(KafkaConnector.java:324)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:711)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:732)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:722)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:614)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:202)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: event is null
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.StopTaskOnErrorLog.error(StopTaskOnErrorLog.java:44)
	... 22 more

[INFO ] 2024-04-07 01:39:26.597 - [Kafka-Mysql][KafkaTest2] - Job suspend in error handle 
[INFO ] 2024-04-07 01:39:26.597 - [Kafka-Mysql][KafkaTest2] - Incremental sync completed 
[INFO ] 2024-04-07 01:39:27.125 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] running status set to false 
[INFO ] 2024-04-07 01:39:27.125 - [Kafka-Mysql][KafkaTest2] - PDK connector node stopped: HazelcastSourcePdkDataNode-fed1ffa2-a4bc-485e-8d97-7ae536da5d45 
[INFO ] 2024-04-07 01:39:27.125 - [Kafka-Mysql][KafkaTest2] - PDK connector node released: HazelcastSourcePdkDataNode-fed1ffa2-a4bc-485e-8d97-7ae536da5d45 
[INFO ] 2024-04-07 01:39:27.125 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] schema data cleaned 
[INFO ] 2024-04-07 01:39:27.126 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] monitor closed 
[INFO ] 2024-04-07 01:39:27.132 - [Kafka-Mysql][KafkaTest2] - Node KafkaTest2[fed1ffa2-a4bc-485e-8d97-7ae536da5d45] close complete, cost 50 ms 
[INFO ] 2024-04-07 01:39:27.132 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] running status set to false 
[INFO ] 2024-04-07 01:39:27.164 - [Kafka-Mysql][targetTest1] - PDK connector node stopped: HazelcastTargetPdkDataNode-85c3c7de-72b3-445c-b3d8-2ee9d483271e 
[INFO ] 2024-04-07 01:39:27.164 - [Kafka-Mysql][targetTest1] - PDK connector node released: HazelcastTargetPdkDataNode-85c3c7de-72b3-445c-b3d8-2ee9d483271e 
[INFO ] 2024-04-07 01:39:27.164 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] schema data cleaned 
[INFO ] 2024-04-07 01:39:27.164 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] monitor closed 
[INFO ] 2024-04-07 01:39:27.164 - [Kafka-Mysql][targetTest1] - Node targetTest1[85c3c7de-72b3-445c-b3d8-2ee9d483271e] close complete, cost 33 ms 
[INFO ] 2024-04-07 01:39:31.460 - [Kafka-Mysql] - Task [Kafka-Mysql] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-04-07 01:39:31.475 - [Kafka-Mysql] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-04-07 01:39:31.475 - [Kafka-Mysql] - Stop task milestones: 6611801143895b65e76af2cc(Kafka-Mysql)  
[INFO ] 2024-04-07 01:39:31.490 - [Kafka-Mysql] - Stopped task aspect(s) 
[INFO ] 2024-04-07 01:39:31.491 - [Kafka-Mysql] - Snapshot order controller have been removed 
[INFO ] 2024-04-07 01:39:31.518 - [Kafka-Mysql] - Remove memory task client succeed, task: Kafka-Mysql[6611801143895b65e76af2cc] 
[INFO ] 2024-04-07 01:39:31.520 - [Kafka-Mysql] - Destroy memory task client cache succeed, task: Kafka-Mysql[6611801143895b65e76af2cc] 
