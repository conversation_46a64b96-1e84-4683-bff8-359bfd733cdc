[INFO ] 2024-07-15 20:30:01.601 - [Heartbeat-<PERSON><PERSON>] - Start task milestones: 66951648b0433721bff43cd1(Heartbeat-Mon<PERSON>) 
[INFO ] 2024-07-15 20:30:02.160 - [Heartbeat-<PERSON><PERSON>] - <PERSON><PERSON> performs snapshot read asynchronously 
[INFO ] 2024-07-15 20:30:02.160 - [Heartbeat-<PERSON><PERSON>] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 20:30:02.278 - [Heartbeat-Mongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@4ce23209 
[INFO ] 2024-07-15 20:30:02.278 - [Heartbeat-<PERSON><PERSON>] - Stop task milestones: 66951648b0433721bff43cd1(Heartbeat-Mongo)  
[INFO ] 2024-07-15 20:30:02.296 - [Heartbeat-Mongo] - Stopped task aspect(s) 
[INFO ] 2024-07-15 20:30:02.297 - [Heartbeat-<PERSON><PERSON>] - Snapshot order controller have been removed 
[ERROR] 2024-07-15 20:30:02.505 - [Heartbeat-<PERSON><PERSON>] - Node [id 3956df3a-5869-421e-b901-c63e9e371871, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id 3956df3a-5869-421e-b901-c63e9e371871, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-15 20:30:06.523 - [Heartbeat-Mongo] - Start task milestones: 66951648b0433721bff43cd1(Heartbeat-Mongo) 
[INFO ] 2024-07-15 20:30:06.602 - [Heartbeat-Mongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 20:30:06.602 - [Heartbeat-Mongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 20:30:06.607 - [Heartbeat-Mongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@108ce68f 
[INFO ] 2024-07-15 20:30:06.719 - [Heartbeat-Mongo] - Stop task milestones: 66951648b0433721bff43cd1(Heartbeat-Mongo)  
[INFO ] 2024-07-15 20:30:06.769 - [Heartbeat-Mongo] - Stopped task aspect(s) 
[INFO ] 2024-07-15 20:30:06.770 - [Heartbeat-Mongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-15 20:30:06.903 - [Heartbeat-Mongo] - Node [id 3956df3a-5869-421e-b901-c63e9e371871, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id 3956df3a-5869-421e-b901-c63e9e371871, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

[INFO ] 2024-07-15 20:30:45.156 - [Heartbeat-Mongo] - Start task milestones: 66951648b0433721bff43cd1(Heartbeat-Mongo) 
[INFO ] 2024-07-15 20:30:45.157 - [Heartbeat-Mongo] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 20:30:45.159 - [Heartbeat-Mongo] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 20:30:45.166 - [Heartbeat-Mongo] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@53c70c5b 
[INFO ] 2024-07-15 20:30:45.282 - [Heartbeat-Mongo] - Stop task milestones: 66951648b0433721bff43cd1(Heartbeat-Mongo)  
[INFO ] 2024-07-15 20:30:45.285 - [Heartbeat-Mongo] - Stopped task aspect(s) 
[INFO ] 2024-07-15 20:30:45.285 - [Heartbeat-Mongo] - Snapshot order controller have been removed 
[ERROR] 2024-07-15 20:30:45.489 - [Heartbeat-Mongo] - Node [id 3956df3a-5869-421e-b901-c63e9e371871, name _tapdata_heartbeat_table] schema cannot be empty <-- Full Stack Trace -->
io.tapdata.exception.NodeException: Node [id 3956df3a-5869-421e-b901-c63e9e371871, name _tapdata_heartbeat_table] schema cannot be empty
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.task2HazelcastDAG(HazelcastTaskService.java:332)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startJetJob(HazelcastTaskService.java:199)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:186)
	at io.tapdata.flow.engine.V2.task.impl.HazelcastTaskService.startTask(HazelcastTaskService.java:123)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.startTask(TapdataTaskScheduler.java:344)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$null$3(TapdataTaskScheduler.java:201)
	at io.tapdata.flow.engine.V2.util.SingleLockWithKey.tryRun(SingleLockWithKey.java:93)
	at io.tapdata.flow.engine.V2.schedule.TapdataTaskScheduler.lambda$getHandleTaskOperationRunnable$5(TapdataTaskScheduler.java:201)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)

