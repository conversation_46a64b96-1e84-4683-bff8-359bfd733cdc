[INFO ] 2024-07-15 20:30:01.594 - [来自Mongo的共享挖掘任务] - Start task milestones: 6695163ab0433721bff43cc3(来自Mongo的共享挖掘任务) 
[INFO ] 2024-07-15 20:30:01.906 - [来自Mongo的共享挖掘任务] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-15 20:30:02.114 - [来自Mongo的共享挖掘任务] - The engine receives 来自Mongo的共享挖掘任务 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-15 20:30:02.514 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[d040e2d257df4138aa5786c3c4cb762d] start preload schema,table counts: 1 
[INFO ] 2024-07-15 20:30:02.514 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node(Tapdata MongoDB External Storage) exactly once write is disabled, reason: Node type HazelCastImdgNode nonsupport exactly once write 
[INFO ] 2024-07-15 20:30:02.516 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[d040e2d257df4138aa5786c3c4cb762d] preload schema finished, cost 0 ms 
[INFO ] 2024-07-15 20:30:02.565 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-15 20:30:02.576 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [TapTargetShareCDCNode] Found table mapping: ShareCdcTableMappingDto(super=BaseDto(id=6695163a66ab5ede8a96025c, customId=null, createAt=null, lastUpdAt=null, userId=62bc5008d4958d013d97c7a6, lastUpdBy=null, createUser=null, permissionActions=null), sign=669515e9b0433721bff43c66_POLICY, version=v2, tableName=POLICY, externalStorageTableName=ExternalStorage_SHARE_CDC_759076299, shareCdcTaskId=6695163ab0433721bff43cc3, connectionId=669515e9b0433721bff43c66) 
[INFO ] 2024-07-15 20:30:02.963 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav391.ExternalStorage_SHARE_CDC_759076299', head seq: 0, tail seq: -1 
[INFO ] 2024-07-15 20:30:02.964 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Init log data storage finished, config: ExternalStorageDto[name='Tapdata MongoDB External Storage', type='mongodb', uri='mongodb://wim:******@localhost:27017/tapdatav391?authSource=admin', table='null', ttlDay=3] 
[INFO ] 2024-07-15 20:30:03.850 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" read batch size: 2000 
[INFO ] 2024-07-15 20:30:03.852 - [来自Mongo的共享挖掘任务][Mongo] - Source node "Mongo" event queue capacity: 4000 
[INFO ] 2024-07-15 20:30:03.853 - [来自Mongo的共享挖掘任务][Mongo] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-15 20:30:04.018 - [来自Mongo的共享挖掘任务][Mongo] - batch offset found: {},stream offset found: {"cdcOffset":1721046603,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 20:30:04.084 - [来自Mongo的共享挖掘任务][Mongo] - Starting stream read, table list: [POLICY, _tapdata_heartbeat_table], offset: {"cdcOffset":1721046603,"opLogOffset":null,"mongo_cdc_offset_flag":true} 
[INFO ] 2024-07-15 20:30:04.084 - [来自Mongo的共享挖掘任务][Mongo] - Connector MongoDB incremental start succeed, tables: [POLICY, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-15 20:31:12.796 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[d040e2d257df4138aa5786c3c4cb762d] running status set to false 
[INFO ] 2024-07-15 20:31:12.798 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node stopped: HazelcastSourcePdkShareCDCNode-d040e2d257df4138aa5786c3c4cb762d 
[INFO ] 2024-07-15 20:31:12.798 - [来自Mongo的共享挖掘任务][Mongo] - PDK connector node released: HazelcastSourcePdkShareCDCNode-d040e2d257df4138aa5786c3c4cb762d 
[INFO ] 2024-07-15 20:31:12.807 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[d040e2d257df4138aa5786c3c4cb762d] schema data cleaned 
[INFO ] 2024-07-15 20:31:12.807 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[d040e2d257df4138aa5786c3c4cb762d] monitor closed 
[INFO ] 2024-07-15 20:31:12.807 - [来自Mongo的共享挖掘任务][Mongo] - Node Mongo[d040e2d257df4138aa5786c3c4cb762d] close complete, cost 47 ms 
[INFO ] 2024-07-15 20:31:12.807 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[e0f4c0d06078446498ee2faae5919478] running status set to false 
[INFO ] 2024-07-15 20:31:12.820 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node stopped: null 
[INFO ] 2024-07-15 20:31:12.822 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - PDK connector node released: null 
[INFO ] 2024-07-15 20:31:12.822 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[e0f4c0d06078446498ee2faae5919478] schema data cleaned 
[INFO ] 2024-07-15 20:31:12.822 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[e0f4c0d06078446498ee2faae5919478] monitor closed 
[INFO ] 2024-07-15 20:31:13.029 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - Node Tapdata MongoDB External Storage[e0f4c0d06078446498ee2faae5919478] close complete, cost 20 ms 
[INFO ] 2024-07-15 20:31:15.254 - [来自Mongo的共享挖掘任务] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-15 20:31:15.254 - [来自Mongo的共享挖掘任务] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@7ddecc88 
[INFO ] 2024-07-15 20:31:15.372 - [来自Mongo的共享挖掘任务] - Stop task milestones: 6695163ab0433721bff43cc3(来自Mongo的共享挖掘任务)  
[INFO ] 2024-07-15 20:31:15.403 - [来自Mongo的共享挖掘任务] - Stopped task aspect(s) 
[INFO ] 2024-07-15 20:31:15.403 - [来自Mongo的共享挖掘任务] - Snapshot order controller have been removed 
[INFO ] 2024-07-15 20:31:15.428 - [来自Mongo的共享挖掘任务] - Remove memory task client succeed, task: 来自Mongo的共享挖掘任务[6695163ab0433721bff43cc3] 
[INFO ] 2024-07-15 20:31:15.429 - [来自Mongo的共享挖掘任务] - Destroy memory task client cache succeed, task: 来自Mongo的共享挖掘任务[6695163ab0433721bff43cc3] 
[INFO ] 2024-07-15 20:32:02.110 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav391.ExternalStorage_SHARE_CDC_759076299', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 20:33:03.457 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav391.ExternalStorage_SHARE_CDC_759076299', head seq: 0, tail seq: 0 
[INFO ] 2024-07-15 20:43:54.304 - [来自Mongo的共享挖掘任务][Tapdata MongoDB External Storage] - [MongoDBRingBuffer] Light init finished, ringbuffer name: 'SHARE_CDC_来自Mongo的共享挖掘任务_POLICY', name space: 'tapdatav391.ExternalStorage_SHARE_CDC_759076299', head seq: 0, tail seq: 0 
