[INFO ] 2024-03-27 10:02:39.410 - [任务 15] - Start task milestones: 66037b898b5bca60f72dcd90(任务 15) 
[INFO ] 2024-03-27 10:02:39.431 - [任务 15] - Task initialization... 
[INFO ] 2024-03-27 10:02:39.447 - [任务 15] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 10:02:39.593 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 10:02:39.712 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:02:39.718 - [任务 15][增强JS] - Node 增强JS[be443b8a-d020-45f6-b494-7a324bab97a2] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:02:39.719 - [任务 15][test4] - Node test4[09107715-aee5-4190-9a68-ae3bd5d4d4a7] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:02:39.759 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] preload schema finished, cost 45 ms 
[INFO ] 2024-03-27 10:02:39.759 - [任务 15][增强JS] - Node 增强JS[be443b8a-d020-45f6-b494-7a324bab97a2] preload schema finished, cost 44 ms 
[INFO ] 2024-03-27 10:02:39.760 - [任务 15][test4] - Node test4[09107715-aee5-4190-9a68-ae3bd5d4d4a7] preload schema finished, cost 45 ms 
[INFO ] 2024-03-27 10:02:40.329 - [任务 15][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 10:02:40.330 - [任务 15][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 10:02:40.330 - [任务 15][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-27 10:02:40.330 - [任务 15][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":144635805,"gtidSet":""} 
[INFO ] 2024-03-27 10:02:40.380 - [任务 15][CLAIM] - Initial sync started 
[INFO ] 2024-03-27 10:02:40.380 - [任务 15][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-27 10:02:40.385 - [任务 15][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-27 10:02:40.409 - [任务 15][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-27 10:02:40.416 - [任务 15][CLAIM] - Initial sync completed 
[ERROR] 2024-03-27 10:02:47.058 - [任务 15][CLAIM] - java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list' <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:406)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:253)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:147)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:254)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:175)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:137)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:308)
	... 10 more
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:187)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:154)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:96)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshot$13(HazelcastSourcePdkDataNode.java:316)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 12 more
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'name' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1140)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:94)
	at io.tapdata.connector.mysql.MysqlConnector.batchReadV2(MysqlConnector.java:398)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$12(HazelcastSourcePdkDataNode.java:388)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:165)
	... 18 more

[INFO ] 2024-03-27 10:02:47.060 - [任务 15][CLAIM] - Job suspend in error handle 
[INFO ] 2024-03-27 10:02:51.621 - [任务 15] - Stop task milestones: 66037b898b5bca60f72dcd90(任务 15)  
[INFO ] 2024-03-27 10:02:53.692 - [任务 15][test4] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 10:03:00.226 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] running status set to false 
[INFO ] 2024-03-27 10:03:00.238 - [任务 15][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-e70086ae-3f99-4591-a7f7-89e716e56a7f 
[INFO ] 2024-03-27 10:03:00.239 - [任务 15][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-e70086ae-3f99-4591-a7f7-89e716e56a7f 
[INFO ] 2024-03-27 10:03:00.239 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] schema data cleaned 
[INFO ] 2024-03-27 10:03:00.239 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] monitor closed 
[INFO ] 2024-03-27 10:03:00.240 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] close complete, cost 41 ms 
[INFO ] 2024-03-27 10:03:00.240 - [任务 15][增强JS] - Node 增强JS[be443b8a-d020-45f6-b494-7a324bab97a2] running status set to false 
[INFO ] 2024-03-27 10:03:00.254 - [任务 15][增强JS] - PDK connector node stopped: ScriptExecutor-testMysql-9c6f3845-b475-4094-8d5d-39fd74a23c8d 
[INFO ] 2024-03-27 10:03:00.254 - [任务 15][增强JS] - PDK connector node released: ScriptExecutor-testMysql-9c6f3845-b475-4094-8d5d-39fd74a23c8d 
[INFO ] 2024-03-27 10:03:00.255 - [任务 15][增强JS] - [ScriptExecutorsManager-66037b898b5bca60f72dcd90-be443b8a-d020-45f6-b494-7a324bab97a2-65fd534767def503a78ea02d] schema data cleaned 
[INFO ] 2024-03-27 10:03:00.279 - [任务 15][增强JS] - PDK connector node stopped: ScriptExecutor-Test-3645ea1d-5714-4fcd-9045-bb5831c398d8 
[INFO ] 2024-03-27 10:03:00.279 - [任务 15][增强JS] - PDK connector node released: ScriptExecutor-Test-3645ea1d-5714-4fcd-9045-bb5831c398d8 
[INFO ] 2024-03-27 10:03:00.280 - [任务 15][增强JS] - [ScriptExecutorsManager-66037b898b5bca60f72dcd90-be443b8a-d020-45f6-b494-7a324bab97a2-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 10:03:00.292 - [任务 15][增强JS] - Node 增强JS[be443b8a-d020-45f6-b494-7a324bab97a2] schema data cleaned 
[INFO ] 2024-03-27 10:03:00.293 - [任务 15][增强JS] - Node 增强JS[be443b8a-d020-45f6-b494-7a324bab97a2] monitor closed 
[INFO ] 2024-03-27 10:03:00.293 - [任务 15][增强JS] - Node 增强JS[be443b8a-d020-45f6-b494-7a324bab97a2] close complete, cost 52 ms 
[INFO ] 2024-03-27 10:03:00.293 - [任务 15][test4] - Node test4[09107715-aee5-4190-9a68-ae3bd5d4d4a7] running status set to false 
[INFO ] 2024-03-27 10:03:00.303 - [任务 15][test4] - PDK connector node stopped: HazelcastTargetPdkDataNode-09107715-aee5-4190-9a68-ae3bd5d4d4a7 
[INFO ] 2024-03-27 10:03:00.303 - [任务 15][test4] - PDK connector node released: HazelcastTargetPdkDataNode-09107715-aee5-4190-9a68-ae3bd5d4d4a7 
[INFO ] 2024-03-27 10:03:00.303 - [任务 15][test4] - Node test4[09107715-aee5-4190-9a68-ae3bd5d4d4a7] schema data cleaned 
[INFO ] 2024-03-27 10:03:00.303 - [任务 15][test4] - Node test4[09107715-aee5-4190-9a68-ae3bd5d4d4a7] monitor closed 
[INFO ] 2024-03-27 10:03:00.303 - [任务 15][test4] - Node test4[09107715-aee5-4190-9a68-ae3bd5d4d4a7] close complete, cost 10 ms 
[INFO ] 2024-03-27 10:03:02.079 - [任务 15] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 10:03:02.081 - [任务 15] - Stopped task aspect(s) 
[INFO ] 2024-03-27 10:03:02.081 - [任务 15] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 10:03:02.107 - [任务 15] - Remove memory task client succeed, task: 任务 15[66037b898b5bca60f72dcd90] 
[INFO ] 2024-03-27 10:03:02.110 - [任务 15] - Destroy memory task client cache succeed, task: 任务 15[66037b898b5bca60f72dcd90] 
[INFO ] 2024-03-27 10:03:27.172 - [任务 15] - Start task milestones: 66037b898b5bca60f72dcd90(任务 15) 
[INFO ] 2024-03-27 10:03:27.181 - [任务 15] - Task initialization... 
[INFO ] 2024-03-27 10:03:27.186 - [任务 15] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 10:03:27.232 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 10:03:27.277 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:03:27.278 - [任务 15][增强JS] - Node 增强JS[be443b8a-d020-45f6-b494-7a324bab97a2] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:03:27.278 - [任务 15][test4] - Node test4[09107715-aee5-4190-9a68-ae3bd5d4d4a7] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:03:27.302 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] preload schema finished, cost 25 ms 
[INFO ] 2024-03-27 10:03:27.303 - [任务 15][增强JS] - Node 增强JS[be443b8a-d020-45f6-b494-7a324bab97a2] preload schema finished, cost 24 ms 
[INFO ] 2024-03-27 10:03:27.312 - [任务 15][test4] - Node test4[09107715-aee5-4190-9a68-ae3bd5d4d4a7] preload schema finished, cost 34 ms 
[INFO ] 2024-03-27 10:03:27.883 - [任务 15][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 10:03:27.885 - [任务 15][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 10:03:27.885 - [任务 15][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-27 10:03:27.886 - [任务 15][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":144635805,"gtidSet":""} 
[INFO ] 2024-03-27 10:03:27.931 - [任务 15][CLAIM] - Initial sync started 
[INFO ] 2024-03-27 10:03:27.931 - [任务 15][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-27 10:03:27.939 - [任务 15][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-27 10:03:27.964 - [任务 15][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-27 10:03:34.678 - [任务 15][test4] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 10:03:41.262 - [任务 15][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 10:03:41.267 - [任务 15][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-27 10:03:41.267 - [任务 15][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 10:03:41.268 - [任务 15][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":144635805,"gtidSet":""} 
[INFO ] 2024-03-27 10:03:41.298 - [任务 15][CLAIM] - Starting mysql cdc, server name: e3e014d1-9f46-4d14-bbb7-bbb794f313fb 
[INFO ] 2024-03-27 10:03:41.302 - [任务 15][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 2093204315
  time.precision.mode: adaptive_time_microseconds
  database.server.name: e3e014d1-9f46-4d14-bbb7-bbb794f313fb
  database.port: 3306
  threadName: Debezium-Mysql-Connector-e3e014d1-9f46-4d14-bbb7-bbb794f313fb
  database.hostname: 127.0.0.1
  database.password: ********
  name: e3e014d1-9f46-4d14-bbb7-bbb794f313fb
  pdk.offset.string: {"name":"e3e014d1-9f46-4d14-bbb7-bbb794f313fb","offset":{"{\"server\":\"e3e014d1-9f46-4d14-bbb7-bbb794f313fb\"}":"{\"file\":\"binlog.000020\",\"pos\":144635805,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 10:03:41.357 - [任务 15][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 10:04:46.880 - [任务 15][CLAIM] - Read DDL: alter table CLAIM add column name varchar(50), about to be packaged as some event(s) 
[INFO ] 2024-03-27 10:04:46.881 - [任务 15][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapNewFieldEvent
  - Offset: MysqlStreamOffset{name='e3e014d1-9f46-4d14-bbb7-bbb794f313fb', offset={{"server":"e3e014d1-9f46-4d14-bbb7-bbb794f313fb"}={"ts_sec":1711505086,"file":"binlog.000020","pos":144636062,"server_id":1}}} 
[INFO ] 2024-03-27 10:04:46.881 - [任务 15][CLAIM] - Source node received an ddl event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@4f948b8: {"newFields":[{"autoInc":false,"dataType":"varchar(50)","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false}],"referenceTime":1711505086650,"tableId":"CLAIM","time":1711505086859,"type":209} 
[WARN ] 2024-03-27 10:04:46.882 - [任务 15][CLAIM] - DDL events are filtered
 - Event: io.tapdata.entity.event.ddl.table.TapNewFieldEvent@4f948b8: {"newFields":[{"autoInc":false,"dataType":"varchar(50)","name":"name","nullable":true,"partitionKey":false,"pos":7,"primaryKey":false}],"referenceTime":1711505086650,"tableId":"CLAIM","time":1711505086859,"type":209}
 - Filter: {} 
[INFO ] 2024-03-27 10:05:07.141 - [任务 15] - Stop task milestones: 66037b898b5bca60f72dcd90(任务 15)  
[INFO ] 2024-03-27 10:05:07.197 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] running status set to false 
[INFO ] 2024-03-27 10:05:07.289 - [任务 15][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-27 10:05:07.289 - [任务 15][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-03-27 10:05:07.293 - [任务 15][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-e70086ae-3f99-4591-a7f7-89e716e56a7f 
[INFO ] 2024-03-27 10:05:07.293 - [任务 15][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-e70086ae-3f99-4591-a7f7-89e716e56a7f 
[INFO ] 2024-03-27 10:05:07.293 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] schema data cleaned 
[INFO ] 2024-03-27 10:05:07.293 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] monitor closed 
[INFO ] 2024-03-27 10:05:07.296 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] close complete, cost 99 ms 
[INFO ] 2024-03-27 10:05:07.296 - [任务 15][增强JS] - Node 增强JS[be443b8a-d020-45f6-b494-7a324bab97a2] running status set to false 
[INFO ] 2024-03-27 10:05:07.300 - [任务 15][增强JS] - PDK connector node stopped: ScriptExecutor-testMysql-d23587f2-a375-4a94-8554-8a1dfb6929af 
[INFO ] 2024-03-27 10:05:07.300 - [任务 15][增强JS] - PDK connector node released: ScriptExecutor-testMysql-d23587f2-a375-4a94-8554-8a1dfb6929af 
[INFO ] 2024-03-27 10:05:07.300 - [任务 15][增强JS] - [ScriptExecutorsManager-66037b898b5bca60f72dcd90-be443b8a-d020-45f6-b494-7a324bab97a2-65fd534767def503a78ea02d] schema data cleaned 
[INFO ] 2024-03-27 10:05:07.310 - [任务 15][增强JS] - PDK connector node stopped: ScriptExecutor-Test-450105ed-cc2c-4776-b65d-2d162636a608 
[INFO ] 2024-03-27 10:05:07.310 - [任务 15][增强JS] - PDK connector node released: ScriptExecutor-Test-450105ed-cc2c-4776-b65d-2d162636a608 
[INFO ] 2024-03-27 10:05:07.310 - [任务 15][增强JS] - [ScriptExecutorsManager-66037b898b5bca60f72dcd90-be443b8a-d020-45f6-b494-7a324bab97a2-65fd52aa67def503a78ea020] schema data cleaned 
[INFO ] 2024-03-27 10:05:07.320 - [任务 15][增强JS] - Node 增强JS[be443b8a-d020-45f6-b494-7a324bab97a2] schema data cleaned 
[INFO ] 2024-03-27 10:05:07.320 - [任务 15][增强JS] - Node 增强JS[be443b8a-d020-45f6-b494-7a324bab97a2] monitor closed 
[INFO ] 2024-03-27 10:05:07.320 - [任务 15][增强JS] - Node 增强JS[be443b8a-d020-45f6-b494-7a324bab97a2] close complete, cost 27 ms 
[INFO ] 2024-03-27 10:05:07.320 - [任务 15][test4] - Node test4[09107715-aee5-4190-9a68-ae3bd5d4d4a7] running status set to false 
[INFO ] 2024-03-27 10:05:07.333 - [任务 15][test4] - PDK connector node stopped: HazelcastTargetPdkDataNode-09107715-aee5-4190-9a68-ae3bd5d4d4a7 
[INFO ] 2024-03-27 10:05:07.333 - [任务 15][test4] - PDK connector node released: HazelcastTargetPdkDataNode-09107715-aee5-4190-9a68-ae3bd5d4d4a7 
[INFO ] 2024-03-27 10:05:07.333 - [任务 15][test4] - Node test4[09107715-aee5-4190-9a68-ae3bd5d4d4a7] schema data cleaned 
[INFO ] 2024-03-27 10:05:07.333 - [任务 15][test4] - Node test4[09107715-aee5-4190-9a68-ae3bd5d4d4a7] monitor closed 
[INFO ] 2024-03-27 10:05:07.333 - [任务 15][test4] - Node test4[09107715-aee5-4190-9a68-ae3bd5d4d4a7] close complete, cost 12 ms 
[INFO ] 2024-03-27 10:05:12.232 - [任务 15] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 10:05:12.233 - [任务 15] - Stopped task aspect(s) 
[INFO ] 2024-03-27 10:05:12.233 - [任务 15] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 10:05:12.267 - [任务 15] - Remove memory task client succeed, task: 任务 15[66037b898b5bca60f72dcd90] 
[INFO ] 2024-03-27 10:05:12.268 - [任务 15] - Destroy memory task client cache succeed, task: 任务 15[66037b898b5bca60f72dcd90] 
[INFO ] 2024-03-27 10:07:26.991 - [任务 15] - Start task milestones: 66037b898b5bca60f72dcd90(任务 15) 
[INFO ] 2024-03-27 10:07:27.000 - [任务 15] - Task initialization... 
[INFO ] 2024-03-27 10:07:27.007 - [任务 15] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 10:07:27.051 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 10:07:27.086 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:07:27.087 - [任务 15][test4] - Node test4[09107715-aee5-4190-9a68-ae3bd5d4d4a7] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:07:27.101 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] preload schema finished, cost 14 ms 
[INFO ] 2024-03-27 10:07:27.102 - [任务 15][test4] - Node test4[09107715-aee5-4190-9a68-ae3bd5d4d4a7] preload schema finished, cost 13 ms 
[INFO ] 2024-03-27 10:07:27.493 - [任务 15][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 10:07:27.496 - [任务 15][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 10:07:27.496 - [任务 15][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-27 10:07:27.497 - [任务 15][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":144636062,"gtidSet":""} 
[INFO ] 2024-03-27 10:07:27.576 - [任务 15][CLAIM] - Initial sync started 
[INFO ] 2024-03-27 10:07:27.577 - [任务 15][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-27 10:07:27.584 - [任务 15][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-27 10:07:27.611 - [任务 15][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-27 10:07:34.388 - [任务 15][test4] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 10:07:34.625 - [任务 15][test4] - The table test4 has already exist. 
[INFO ] 2024-03-27 10:07:34.775 - [任务 15][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 10:07:34.776 - [任务 15][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-27 10:07:34.776 - [任务 15][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 10:07:34.776 - [任务 15][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":144636062,"gtidSet":""} 
[INFO ] 2024-03-27 10:07:34.829 - [任务 15][CLAIM] - Starting mysql cdc, server name: f49f4ff4-2696-4517-9c7c-689937b37623 
[INFO ] 2024-03-27 10:07:34.836 - [任务 15][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 1833410140
  time.precision.mode: adaptive_time_microseconds
  database.server.name: f49f4ff4-2696-4517-9c7c-689937b37623
  database.port: 3306
  threadName: Debezium-Mysql-Connector-f49f4ff4-2696-4517-9c7c-689937b37623
  database.hostname: 127.0.0.1
  database.password: ********
  name: f49f4ff4-2696-4517-9c7c-689937b37623
  pdk.offset.string: {"name":"f49f4ff4-2696-4517-9c7c-689937b37623","offset":{"{\"server\":\"f49f4ff4-2696-4517-9c7c-689937b37623\"}":"{\"file\":\"binlog.000020\",\"pos\":144636062,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 10:07:34.881 - [任务 15][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 10:10:42.825 - [任务 15] - Stop task milestones: 66037b898b5bca60f72dcd90(任务 15)  
[INFO ] 2024-03-27 10:10:43.144 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] running status set to false 
[INFO ] 2024-03-27 10:10:43.258 - [任务 15][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-27 10:10:43.259 - [任务 15][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-03-27 10:10:43.263 - [任务 15][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-e70086ae-3f99-4591-a7f7-89e716e56a7f 
[INFO ] 2024-03-27 10:10:43.265 - [任务 15][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-e70086ae-3f99-4591-a7f7-89e716e56a7f 
[INFO ] 2024-03-27 10:10:43.265 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] schema data cleaned 
[INFO ] 2024-03-27 10:10:43.267 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] monitor closed 
[INFO ] 2024-03-27 10:10:43.267 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] close complete, cost 129 ms 
[INFO ] 2024-03-27 10:10:43.267 - [任务 15][test4] - Node test4[09107715-aee5-4190-9a68-ae3bd5d4d4a7] running status set to false 
[INFO ] 2024-03-27 10:10:43.281 - [任务 15][test4] - PDK connector node stopped: HazelcastTargetPdkDataNode-09107715-aee5-4190-9a68-ae3bd5d4d4a7 
[INFO ] 2024-03-27 10:10:43.281 - [任务 15][test4] - PDK connector node released: HazelcastTargetPdkDataNode-09107715-aee5-4190-9a68-ae3bd5d4d4a7 
[INFO ] 2024-03-27 10:10:43.281 - [任务 15][test4] - Node test4[09107715-aee5-4190-9a68-ae3bd5d4d4a7] schema data cleaned 
[INFO ] 2024-03-27 10:10:43.282 - [任务 15][test4] - Node test4[09107715-aee5-4190-9a68-ae3bd5d4d4a7] monitor closed 
[INFO ] 2024-03-27 10:10:43.282 - [任务 15][test4] - Node test4[09107715-aee5-4190-9a68-ae3bd5d4d4a7] close complete, cost 14 ms 
[INFO ] 2024-03-27 10:10:47.546 - [任务 15] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 10:10:47.546 - [任务 15] - Stopped task aspect(s) 
[INFO ] 2024-03-27 10:10:47.547 - [任务 15] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 10:10:47.583 - [任务 15] - Remove memory task client succeed, task: 任务 15[66037b898b5bca60f72dcd90] 
[INFO ] 2024-03-27 10:10:47.583 - [任务 15] - Destroy memory task client cache succeed, task: 任务 15[66037b898b5bca60f72dcd90] 
[INFO ] 2024-03-27 10:11:19.211 - [任务 15] - Start task milestones: 66037b898b5bca60f72dcd90(任务 15) 
[INFO ] 2024-03-27 10:11:19.219 - [任务 15] - Task initialization... 
[INFO ] 2024-03-27 10:11:19.226 - [任务 15] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 10:11:19.276 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 10:11:19.312 - [任务 15][test4] - Node test5[09107715-aee5-4190-9a68-ae3bd5d4d4a7] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:11:19.312 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:11:19.341 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] preload schema finished, cost 29 ms 
[INFO ] 2024-03-27 10:11:19.341 - [任务 15][test4] - Node test5[09107715-aee5-4190-9a68-ae3bd5d4d4a7] preload schema finished, cost 29 ms 
[INFO ] 2024-03-27 10:11:20.110 - [任务 15][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 10:11:20.110 - [任务 15][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 10:11:20.110 - [任务 15][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-27 10:11:20.111 - [任务 15][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":144636062,"gtidSet":""} 
[INFO ] 2024-03-27 10:11:20.145 - [任务 15][CLAIM] - Initial sync started 
[INFO ] 2024-03-27 10:11:20.146 - [任务 15][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-27 10:11:20.153 - [任务 15][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-27 10:11:20.168 - [任务 15][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-27 10:11:26.815 - [任务 15][test4] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 10:11:27.673 - [任务 15][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 10:11:27.690 - [任务 15][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-27 10:11:27.692 - [任务 15][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 10:11:27.692 - [任务 15][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":144636062,"gtidSet":""} 
[INFO ] 2024-03-27 10:11:27.753 - [任务 15][CLAIM] - Starting mysql cdc, server name: 3922df45-5c3f-4765-b382-2ee14442f788 
[INFO ] 2024-03-27 10:11:27.767 - [任务 15][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 468248253
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 3922df45-5c3f-4765-b382-2ee14442f788
  database.port: 3306
  threadName: Debezium-Mysql-Connector-3922df45-5c3f-4765-b382-2ee14442f788
  database.hostname: 127.0.0.1
  database.password: ********
  name: 3922df45-5c3f-4765-b382-2ee14442f788
  pdk.offset.string: {"name":"3922df45-5c3f-4765-b382-2ee14442f788","offset":{"{\"server\":\"3922df45-5c3f-4765-b382-2ee14442f788\"}":"{\"file\":\"binlog.000020\",\"pos\":144636062,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 10:11:27.830 - [任务 15][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 10:24:58.260 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] running status set to false 
[INFO ] 2024-03-27 10:24:58.269 - [任务 15][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-27 10:24:58.270 - [任务 15][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-03-27 10:24:58.271 - [任务 15][CLAIM] - Incremental sync completed 
[WARN ] 2024-03-27 10:24:58.274 - [任务 15][CLAIM] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastSourcePdkDataNode-e70086ae-3f99-4591-a7f7-89e716e56a7f 
[INFO ] 2024-03-27 10:24:58.275 - [任务 15][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-e70086ae-3f99-4591-a7f7-89e716e56a7f 
[INFO ] 2024-03-27 10:24:58.275 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] schema data cleaned 
[INFO ] 2024-03-27 10:24:58.275 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] monitor closed 
[INFO ] 2024-03-27 10:24:58.276 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] close complete, cost 20 ms 
[INFO ] 2024-03-27 10:24:58.276 - [任务 15][test4] - Node test5[09107715-aee5-4190-9a68-ae3bd5d4d4a7] running status set to false 
[WARN ] 2024-03-27 10:24:58.297 - [任务 15][test4] - Stop PDK connector node failed: Hazelcast instance is not active! | Associate id: HazelcastTargetPdkDataNode-09107715-aee5-4190-9a68-ae3bd5d4d4a7 
[INFO ] 2024-03-27 10:24:58.298 - [任务 15][test4] - PDK connector node released: HazelcastTargetPdkDataNode-09107715-aee5-4190-9a68-ae3bd5d4d4a7 
[INFO ] 2024-03-27 10:24:58.298 - [任务 15][test4] - Node test5[09107715-aee5-4190-9a68-ae3bd5d4d4a7] schema data cleaned 
[INFO ] 2024-03-27 10:24:58.298 - [任务 15][test4] - Node test5[09107715-aee5-4190-9a68-ae3bd5d4d4a7] monitor closed 
[INFO ] 2024-03-27 10:24:58.298 - [任务 15][test4] - Node test5[09107715-aee5-4190-9a68-ae3bd5d4d4a7] close complete, cost 22 ms 
[INFO ] 2024-03-27 10:27:13.929 - [任务 15] - Start task milestones: 66037b898b5bca60f72dcd90(任务 15) 
[INFO ] 2024-03-27 10:27:13.929 - [任务 15] - Task initialization... 
[INFO ] 2024-03-27 10:27:13.986 - [任务 15] - Node performs snapshot read asynchronously 
[INFO ] 2024-03-27 10:27:14.133 - [任务 15] - The engine receives 任务 15 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-03-27 10:27:14.285 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:27:14.287 - [任务 15][test6] - Node test6[09107715-aee5-4190-9a68-ae3bd5d4d4a7] start preload schema,table counts: 1 
[INFO ] 2024-03-27 10:27:14.334 - [任务 15][test6] - Node test6[09107715-aee5-4190-9a68-ae3bd5d4d4a7] preload schema finished, cost 49 ms 
[INFO ] 2024-03-27 10:27:14.336 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] preload schema finished, cost 52 ms 
[INFO ] 2024-03-27 10:27:14.559 - [任务 15][CLAIM] - Source node "CLAIM" read batch size: 100 
[INFO ] 2024-03-27 10:27:14.559 - [任务 15][CLAIM] - Source node "CLAIM" event queue capacity: 200 
[INFO ] 2024-03-27 10:27:14.559 - [任务 15][CLAIM] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-03-27 10:27:14.570 - [任务 15][CLAIM] - batch offset found: {},stream offset found: {"filename":"binlog.000020","position":144636062,"gtidSet":""} 
[INFO ] 2024-03-27 10:27:14.638 - [任务 15][CLAIM] - Initial sync started 
[INFO ] 2024-03-27 10:27:14.649 - [任务 15][CLAIM] - Starting batch read, table name: CLAIM, offset: null 
[INFO ] 2024-03-27 10:27:14.651 - [任务 15][CLAIM] - Table CLAIM is going to be initial synced 
[INFO ] 2024-03-27 10:27:14.711 - [任务 15][CLAIM] - Query table 'CLAIM' counts: 1079 
[INFO ] 2024-03-27 10:27:15.139 - [任务 15][test6] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-03-27 10:27:15.619 - [任务 15][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 10:27:15.625 - [任务 15][CLAIM] - Incremental sync starting... 
[INFO ] 2024-03-27 10:27:15.627 - [任务 15][CLAIM] - Initial sync completed 
[INFO ] 2024-03-27 10:27:15.630 - [任务 15][CLAIM] - Starting stream read, table list: [CLAIM], offset: {"filename":"binlog.000020","position":144636062,"gtidSet":""} 
[INFO ] 2024-03-27 10:27:15.698 - [任务 15][CLAIM] - Starting mysql cdc, server name: 4a7a5361-4571-471d-a23b-563b53c83712 
[INFO ] 2024-03-27 10:27:15.742 - [任务 15][CLAIM] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 501076368
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 4a7a5361-4571-471d-a23b-563b53c83712
  database.port: 3306
  threadName: Debezium-Mysql-Connector-4a7a5361-4571-471d-a23b-563b53c83712
  database.hostname: 127.0.0.1
  database.password: ********
  name: 4a7a5361-4571-471d-a23b-563b53c83712
  pdk.offset.string: {"name":"4a7a5361-4571-471d-a23b-563b53c83712","offset":{"{\"server\":\"4a7a5361-4571-471d-a23b-563b53c83712\"}":"{\"file\":\"binlog.000020\",\"pos\":144636062,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.CLAIM
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-03-27 10:27:15.945 - [任务 15][CLAIM] - Connector Mysql incremental start succeed, tables: [CLAIM], data change syncing 
[INFO ] 2024-03-27 10:29:50.000 - [任务 15][CLAIM] - Read DDL: alter table CLAIM rename column `name` to `name1`, about to be packaged as some event(s) 
[INFO ] 2024-03-27 10:29:50.001 - [任务 15][CLAIM] - DDL event  - Table: CLAIM
  - Event type: TapAlterFieldNameEvent
  - Offset: MysqlStreamOffset{name='4a7a5361-4571-471d-a23b-563b53c83712', offset={{"server":"4a7a5361-4571-471d-a23b-563b53c83712"}={"ts_sec":1711506589,"file":"binlog.000020","pos":144636962,"server_id":1}}} 
[INFO ] 2024-03-27 10:29:50.002 - [任务 15][CLAIM] - Source node received an ddl event: TapAlterFieldNameEvent{tableId='CLAIM', nameChange=io.tapdata.entity.event.ddl.entity.ValueChange@e1ff5e8} 
[INFO ] 2024-03-27 10:29:50.010 - [任务 15][CLAIM] - Alter table in memory, qualified name: T_mysql_io_tapdata_1_0-SNAPSHOT_CLAIM_65fd534767def503a78ea02d_66037b898b5bca60f72dcd90 
[INFO ] 2024-03-27 10:29:50.075 - [任务 15][CLAIM] - Alter table schema transform finished 
[INFO ] 2024-03-27 10:54:08.197 - [任务 15] - Stop task milestones: 66037b898b5bca60f72dcd90(任务 15)  
[INFO ] 2024-03-27 10:54:08.497 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] running status set to false 
[INFO ] 2024-03-27 10:54:08.536 - [任务 15][CLAIM] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-03-27 10:54:08.538 - [任务 15][CLAIM] - Mysql binlog reader stopped 
[INFO ] 2024-03-27 10:54:08.545 - [任务 15][CLAIM] - PDK connector node stopped: HazelcastSourcePdkDataNode-e70086ae-3f99-4591-a7f7-89e716e56a7f 
[INFO ] 2024-03-27 10:54:08.547 - [任务 15][CLAIM] - PDK connector node released: HazelcastSourcePdkDataNode-e70086ae-3f99-4591-a7f7-89e716e56a7f 
[INFO ] 2024-03-27 10:54:08.547 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] schema data cleaned 
[INFO ] 2024-03-27 10:54:08.547 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] monitor closed 
[INFO ] 2024-03-27 10:54:08.547 - [任务 15][CLAIM] - Node CLAIM[e70086ae-3f99-4591-a7f7-89e716e56a7f] close complete, cost 62 ms 
[INFO ] 2024-03-27 10:54:08.547 - [任务 15][test6] - Node test6[09107715-aee5-4190-9a68-ae3bd5d4d4a7] running status set to false 
[INFO ] 2024-03-27 10:54:08.563 - [任务 15][test6] - PDK connector node stopped: HazelcastTargetPdkDataNode-09107715-aee5-4190-9a68-ae3bd5d4d4a7 
[INFO ] 2024-03-27 10:54:08.565 - [任务 15][test6] - PDK connector node released: HazelcastTargetPdkDataNode-09107715-aee5-4190-9a68-ae3bd5d4d4a7 
[INFO ] 2024-03-27 10:54:08.565 - [任务 15][test6] - Node test6[09107715-aee5-4190-9a68-ae3bd5d4d4a7] schema data cleaned 
[INFO ] 2024-03-27 10:54:08.565 - [任务 15][test6] - Node test6[09107715-aee5-4190-9a68-ae3bd5d4d4a7] monitor closed 
[INFO ] 2024-03-27 10:54:08.565 - [任务 15][test6] - Node test6[09107715-aee5-4190-9a68-ae3bd5d4d4a7] close complete, cost 17 ms 
[INFO ] 2024-03-27 10:54:08.680 - [任务 15] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-03-27 10:54:08.680 - [任务 15] - Stopped task aspect(s) 
[INFO ] 2024-03-27 10:54:08.680 - [任务 15] - Snapshot order controller have been removed 
[INFO ] 2024-03-27 10:54:08.718 - [任务 15] - Remove memory task client succeed, task: 任务 15[66037b898b5bca60f72dcd90] 
[INFO ] 2024-03-27 10:54:08.718 - [任务 15] - Destroy memory task client cache succeed, task: 任务 15[66037b898b5bca60f72dcd90] 
