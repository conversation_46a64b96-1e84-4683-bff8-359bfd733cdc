[INFO ] 2024-07-17 19:09:46.541 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575] - Start task milestones: 6697a66fb92eda1a86f52545(t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575) 
[INFO ] 2024-07-17 19:09:46.733 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575] - Task initialization... 
[INFO ] 2024-07-17 19:09:46.733 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-07-17 19:09:46.863 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575] - The engine receives t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-17 19:09:46.899 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[5cda2b9c-deb7-4068-a02a-4e49569539f1] start preload schema,table counts: 1 
[INFO ] 2024-07-17 19:09:46.899 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[a2a2299e-b8fd-46ca-9eef-cc5eada67c25] start preload schema,table counts: 1 
[INFO ] 2024-07-17 19:09:46.899 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[5cda2b9c-deb7-4068-a02a-4e49569539f1] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 19:09:46.899 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[a2a2299e-b8fd-46ca-9eef-cc5eada67c25] preload schema finished, cost 0 ms 
[INFO ] 2024-07-17 19:09:47.190 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-17 19:09:47.190 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Source node "qa_mock_1000w_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-17 19:09:47.190 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Source node "qa_mock_1000w_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-17 19:09:47.190 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-17 19:09:47.190 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - batch offset found: {},stream offset not found. 
[INFO ] 2024-07-17 19:09:47.341 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-17 19:09:47.343 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Starting batch read, table name: mock_1000w, offset: null 
[INFO ] 2024-07-17 19:09:47.343 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Table mock_1000w is going to be initial synced 
[INFO ] 2024-07-17 19:09:47.366 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Start mock_1000w batch read 
[INFO ] 2024-07-17 19:09:47.366 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Query table 'mock_1000w' counts: 10000000 
[INFO ] 2024-07-17 19:10:16.798 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Compile mock_1000w batch read 
[INFO ] 2024-07-17 19:10:16.798 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Table [mock_1000w] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-17 19:10:17.002 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-17 19:10:17.828 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[a2a2299e-b8fd-46ca-9eef-cc5eada67c25] running status set to false 
[INFO ] 2024-07-17 19:10:17.828 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[5cda2b9c-deb7-4068-a02a-4e49569539f1] running status set to false 
[INFO ] 2024-07-17 19:10:17.828 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Stop connector 
[INFO ] 2024-07-17 19:10:17.846 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Stop connector: first 1721214587346 19ms, last 1721214616790 8ms, counts: 10000000/29452ms, min: 2, max: 27, QPS: 344827/s 
[INFO ] 2024-07-17 19:10:17.846 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-a2a2299e-b8fd-46ca-9eef-cc5eada67c25 
[INFO ] 2024-07-17 19:10:17.848 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-a2a2299e-b8fd-46ca-9eef-cc5eada67c25 
[INFO ] 2024-07-17 19:10:17.848 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-5cda2b9c-deb7-4068-a02a-4e49569539f1 
[INFO ] 2024-07-17 19:10:17.848 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-5cda2b9c-deb7-4068-a02a-4e49569539f1 
[INFO ] 2024-07-17 19:10:17.848 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[5cda2b9c-deb7-4068-a02a-4e49569539f1] schema data cleaned 
[INFO ] 2024-07-17 19:10:17.848 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[5cda2b9c-deb7-4068-a02a-4e49569539f1] monitor closed 
[INFO ] 2024-07-17 19:10:17.849 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[a2a2299e-b8fd-46ca-9eef-cc5eada67c25] schema data cleaned 
[INFO ] 2024-07-17 19:10:17.849 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[a2a2299e-b8fd-46ca-9eef-cc5eada67c25] monitor closed 
[INFO ] 2024-07-17 19:10:17.850 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[5cda2b9c-deb7-4068-a02a-4e49569539f1] close complete, cost 24 ms 
[INFO ] 2024-07-17 19:10:17.850 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575][qa_mock_1000w_1717403468657_3537] - Node qa_mock_1000w_1717403468657_3537[a2a2299e-b8fd-46ca-9eef-cc5eada67c25] close complete, cost 25 ms 
[INFO ] 2024-07-17 19:10:19.642 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-17 19:10:19.644 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@b03960a 
[INFO ] 2024-07-17 19:10:19.802 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575] - Stop task milestones: 6697a66fb92eda1a86f52545(t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575)  
[INFO ] 2024-07-17 19:10:19.802 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575] - Stopped task aspect(s) 
[INFO ] 2024-07-17 19:10:19.802 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575] - Snapshot order controller have been removed 
[INFO ] 2024-07-17 19:10:19.853 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575] - Remove memory task client succeed, task: t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575[6697a66fb92eda1a86f52545] 
[INFO ] 2024-07-17 19:10:19.853 - [t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575] - Destroy memory task client cache succeed, task: t_1.1-mock_to_mock_20fields_1717403468657_3537-1721214575[6697a66fb92eda1a86f52545] 
