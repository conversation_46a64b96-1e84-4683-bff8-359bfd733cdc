[INFO ] 2024-07-18 10:50:16.242 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002] - Task initialization... 
[INFO ] 2024-07-18 10:50:16.267 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002] - Start task milestones: 669882db8315b25db9f538e9(t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002) 
[INFO ] 2024-07-18 10:50:16.634 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002] - Node performs snapshot read asynchronously 
[INFO ] 2024-07-18 10:50:16.911 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002] - The engine receives t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-07-18 10:50:17.012 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[a4079023-50eb-4fab-8924-a3849b921445] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:50:17.012 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[8c5c57f6-70f4-4c1b-8326-44eb507dd3bc] start preload schema,table counts: 1 
[INFO ] 2024-07-18 10:50:17.012 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[a4079023-50eb-4fab-8924-a3849b921445] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 10:50:17.012 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[8c5c57f6-70f4-4c1b-8326-44eb507dd3bc] preload schema finished, cost 0 ms 
[INFO ] 2024-07-18 10:50:18.169 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Write batch size: 1000, max wait ms per batch: 1000 
[INFO ] 2024-07-18 10:50:18.297 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Source node "qa_mysql_repl_33306_1717403468657_3537" read batch size: 500 
[INFO ] 2024-07-18 10:50:18.304 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Source node "qa_mysql_repl_33306_1717403468657_3537" event queue capacity: 1000 
[INFO ] 2024-07-18 10:50:18.313 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-07-18 10:50:18.510 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - batch offset found: {},stream offset found: {"filename":"mysql-bin.000303","position":804546402,"gtidSet":""} 
[INFO ] 2024-07-18 10:50:18.511 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-07-18 10:50:18.798 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Initial sync started 
[INFO ] 2024-07-18 10:50:18.798 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Starting batch read, table name: source_100w, offset: null 
[INFO ] 2024-07-18 10:50:18.799 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Table source_100w is going to be initial synced 
[INFO ] 2024-07-18 10:50:20.112 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Query table 'source_100w' counts: 1012928 
[INFO ] 2024-07-18 10:51:58.899 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Table [source_100w] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-07-18 10:51:58.899 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 10:51:58.899 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Incremental sync starting... 
[INFO ] 2024-07-18 10:51:58.900 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Initial sync completed 
[INFO ] 2024-07-18 10:51:58.900 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Starting stream read, table list: [source_100w, _tapdata_heartbeat_table], offset: {"filename":"mysql-bin.000303","position":804546402,"gtidSet":""} 
[INFO ] 2024-07-18 10:51:59.105 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Starting mysql cdc, server name: 05184987-5806-4c7b-b3b9-ada71632d1f7 
[INFO ] 2024-07-18 10:51:59.309 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 228888667
  time.precision.mode: adaptive_time_microseconds
  database.server.name: 05184987-5806-4c7b-b3b9-ada71632d1f7
  database.port: 3306
  threadName: Debezium-Mysql-Connector-05184987-5806-4c7b-b3b9-ada71632d1f7
  database.hostname: *************
  database.password: ********
  name: 05184987-5806-4c7b-b3b9-ada71632d1f7
  pdk.offset.string: {"name":"05184987-5806-4c7b-b3b9-ada71632d1f7","offset":{"{\"server\":\"05184987-5806-4c7b-b3b9-ada71632d1f7\"}":"{\"file\":\"mysql-bin.000303\",\"pos\":804546402,\"server_id\":\"1121\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: t0.source_100w,t0._tapdata_heartbeat_table
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: t0
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-07-18 10:51:59.510 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Connector Mysql incremental start succeed, tables: [source_100w, _tapdata_heartbeat_table], data change syncing 
[INFO ] 2024-07-18 10:53:02.535 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[a4079023-50eb-4fab-8924-a3849b921445] running status set to false 
[INFO ] 2024-07-18 10:53:02.637 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-07-18 10:53:02.643 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Mysql binlog reader stopped 
[INFO ] 2024-07-18 10:53:02.644 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Incremental sync completed 
[INFO ] 2024-07-18 10:53:02.655 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node stopped: HazelcastSourcePdkDataNode-a4079023-50eb-4fab-8924-a3849b921445 
[INFO ] 2024-07-18 10:53:02.656 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node released: HazelcastSourcePdkDataNode-a4079023-50eb-4fab-8924-a3849b921445 
[INFO ] 2024-07-18 10:53:02.656 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[a4079023-50eb-4fab-8924-a3849b921445] schema data cleaned 
[INFO ] 2024-07-18 10:53:02.656 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[a4079023-50eb-4fab-8924-a3849b921445] monitor closed 
[INFO ] 2024-07-18 10:53:02.660 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[a4079023-50eb-4fab-8924-a3849b921445] close complete, cost 149 ms 
[INFO ] 2024-07-18 10:53:02.818 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[8c5c57f6-70f4-4c1b-8326-44eb507dd3bc] running status set to false 
[INFO ] 2024-07-18 10:53:02.821 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node stopped: HazelcastTargetPdkDataNode-8c5c57f6-70f4-4c1b-8326-44eb507dd3bc 
[INFO ] 2024-07-18 10:53:02.821 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - PDK connector node released: HazelcastTargetPdkDataNode-8c5c57f6-70f4-4c1b-8326-44eb507dd3bc 
[INFO ] 2024-07-18 10:53:02.821 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[8c5c57f6-70f4-4c1b-8326-44eb507dd3bc] schema data cleaned 
[INFO ] 2024-07-18 10:53:02.821 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[8c5c57f6-70f4-4c1b-8326-44eb507dd3bc] monitor closed 
[INFO ] 2024-07-18 10:53:03.023 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002][qa_mysql_repl_33306_1717403468657_3537] - Node qa_mysql_repl_33306_1717403468657_3537[8c5c57f6-70f4-4c1b-8326-44eb507dd3bc] close complete, cost 162 ms 
[INFO ] 2024-07-18 10:53:05.068 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-07-18 10:53:05.200 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002] - Closed task auto recovery instance
  io.tapdata.inspect.AutoRecovery@781d718d 
[INFO ] 2024-07-18 10:53:05.202 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002] - Stop task milestones: 669882db8315b25db9f538e9(t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002)  
[INFO ] 2024-07-18 10:53:05.258 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002] - Stopped task aspect(s) 
[INFO ] 2024-07-18 10:53:05.258 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002] - Snapshot order controller have been removed 
[INFO ] 2024-07-18 10:53:05.342 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002] - Remove memory task client succeed, task: t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002[669882db8315b25db9f538e9] 
[INFO ] 2024-07-18 10:53:05.342 - [t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002] - Destroy memory task client cache succeed, task: t_2.2-2-mysql_to_mysql_full/realtime_1717403468657_3537-1721271002[669882db8315b25db9f538e9] 
