[INFO ] 2024-06-06 08:11:53.485 - [LongRaw] - Start task milestones: 665fe41f0ea2021850b59d4a(LongRaw) 
[INFO ] 2024-06-06 08:11:53.503 - [LongRaw] - Task initialization... 
[INFO ] 2024-06-06 08:11:53.598 - [LongRaw] - Node performs snapshot read asynchronously 
[INFO ] 2024-06-06 08:11:53.601 - [LongRaw] - The engine receives LongRaw task data from TM and will continue to run tasks by jet 
[INFO ] 2024-06-06 08:11:53.698 - [LongRaw][IMAGETABLE] - Node IMAGETABLE[eeaedb3c-63c0-4b09-ab4e-e4fc451b1947] start preload schema,table counts: 1 
[INFO ] 2024-06-06 08:11:53.700 - [LongRaw][IMAGETABLECOPY] - Node IMAGETABLECOPY[8b773173-63eb-4014-86f9-4ef39a9fa3d1] start preload schema,table counts: 1 
[INFO ] 2024-06-06 08:11:53.701 - [LongRaw][IMAGETABLE] - Node IMAGETABLE[eeaedb3c-63c0-4b09-ab4e-e4fc451b1947] preload schema finished, cost 24 ms 
[INFO ] 2024-06-06 08:11:53.703 - [LongRaw][IMAGETABLECOPY] - Node IMAGETABLECOPY[8b773173-63eb-4014-86f9-4ef39a9fa3d1] preload schema finished, cost 31 ms 
[INFO ] 2024-06-06 08:11:54.920 - [LongRaw][IMAGETABLECOPY] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-06-06 08:11:54.928 - [LongRaw][IMAGETABLE] - Source node "IMAGETABLE" read batch size: 100 
[INFO ] 2024-06-06 08:11:54.928 - [LongRaw][IMAGETABLE] - Source node "IMAGETABLE" event queue capacity: 200 
[INFO ] 2024-06-06 08:11:54.928 - [LongRaw][IMAGETABLE] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-06-06 08:11:55.214 - [LongRaw][IMAGETABLECOPY] - The table IMAGETABLECOPY has already exist. 
[INFO ] 2024-06-06 08:11:55.214 - [LongRaw][IMAGETABLE] - batch offset found: {},stream offset found: {"sortString":null,"offsetValue":null,"lastScn":54052939,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-06 08:11:55.214 - [LongRaw][IMAGETABLE] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-06-06 08:11:55.284 - [LongRaw][IMAGETABLE] - Initial sync started 
[INFO ] 2024-06-06 08:11:55.285 - [LongRaw][IMAGETABLE] - Starting batch read, table name: IMAGETABLE, offset: null 
[INFO ] 2024-06-06 08:11:55.368 - [LongRaw][IMAGETABLE] - Table IMAGETABLE is going to be initial synced 
[INFO ] 2024-06-06 08:11:55.368 - [LongRaw][IMAGETABLE] - Query table 'IMAGETABLE' counts: 2 
[INFO ] 2024-06-06 08:11:55.405 - [LongRaw][IMAGETABLE] - Table [IMAGETABLE] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-06-06 08:11:55.405 - [LongRaw][IMAGETABLE] - Initial sync completed 
[INFO ] 2024-06-06 08:11:55.406 - [LongRaw][IMAGETABLE] - Incremental sync starting... 
[INFO ] 2024-06-06 08:11:55.406 - [LongRaw][IMAGETABLE] - Initial sync completed 
[INFO ] 2024-06-06 08:11:55.544 - [LongRaw][IMAGETABLE] - Starting stream read, table list: [IMAGETABLE], offset: {"sortString":null,"offsetValue":null,"lastScn":54052939,"pendingScn":null,"timestamp":null,"hexScn":null,"fno":0} 
[INFO ] 2024-06-06 08:11:55.544 - [LongRaw][IMAGETABLE] - total start mining scn: 54052939 
[INFO ] 2024-06-06 08:11:56.659 - [LongRaw][IMAGETABLE] - 【single miner】add log miner sql: BEGIN SYS.dbms_logmnr.add_logfile(logfilename=>'/u01/app/oracle/oradata/ORCL/redo01.log',options=>SYS.dbms_logmnr.NEW);END; 
[INFO ] 2024-06-06 08:12:07.724 - [LongRaw] - Stop task milestones: 665fe41f0ea2021850b59d4a(LongRaw)  
[INFO ] 2024-06-06 08:12:08.125 - [LongRaw][IMAGETABLE] - Node IMAGETABLE[eeaedb3c-63c0-4b09-ab4e-e4fc451b1947] running status set to false 
[INFO ] 2024-06-06 08:12:08.170 - [LongRaw][IMAGETABLE] - Log Miner is shutting down... 
[INFO ] 2024-06-06 08:12:08.171 - [LongRaw][IMAGETABLE] - Log Miner has been closed! 
[ERROR] 2024-06-06 08:12:08.224 - [LongRaw][IMAGETABLE] - java.io.InterruptedIOException: Socket read interrupted <-- Error Message -->
java.io.InterruptedIOException: Socket read interrupted

<-- Simple Stack Trace -->
Caused by: java.io.InterruptedIOException: Socket read interrupted
	oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	...

<-- Full Stack Trace -->
java.io.InterruptedIOException: Socket read interrupted
	at io.tapdata.common.exception.AbstractExceptionCollector.revealException(AbstractExceptionCollector.java:69)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:104)
	at io.tapdata.connector.oracle.OracleConnector.streamRead(OracleConnector.java:413)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$31(HazelcastSourcePdkDataNode.java:739)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$null$10(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$11(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.utils.RetryUtils.autoRetry(RetryUtils.java:68)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:151)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doNormalCDC$33(HazelcastSourcePdkDataNode.java:760)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doNormalCDC(HazelcastSourcePdkDataNode.java:750)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doCdc(HazelcastSourcePdkDataNode.java:639)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:205)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.io.InterruptedIOException: Socket read interrupted
	at oracle.net.nt.TimeoutSocketChannel.handleInterrupt(TimeoutSocketChannel.java:549)
	at oracle.net.nt.TimeoutSocketChannel.read(TimeoutSocketChannel.java:420)
	at oracle.net.ns.NSProtocolNIO.doSocketRead(NSProtocolNIO.java:1119)
	at oracle.net.ns.NIOPacket.readHeader(NIOPacket.java:267)
	at oracle.net.ns.NIOPacket.readPacketFromSocketChannel(NIOPacket.java:199)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:141)
	at oracle.net.ns.NIOPacket.readFromSocketChannel(NIOPacket.java:114)
	at oracle.net.ns.NIONSDataChannel.readDataFromSocketChannel(NIONSDataChannel.java:98)
	at oracle.jdbc.driver.T4CMAREngineNIO.prepareForUnmarshall(T4CMAREngineNIO.java:834)
	at oracle.jdbc.driver.T4CMAREngineNIO.unmarshalUB1(T4CMAREngineNIO.java:487)
	at oracle.jdbc.driver.T4CTTIfun.receive(T4CTTIfun.java:622)
	at oracle.jdbc.driver.T4CTTIfun.doRPC(T4CTTIfun.java:299)
	at oracle.jdbc.driver.T4C8Oall.doOALL(T4C8Oall.java:498)
	at oracle.jdbc.driver.T4CStatement.doOall8(T4CStatement.java:112)
	at oracle.jdbc.driver.T4CStatement.executeForDescribe(T4CStatement.java:896)
	at oracle.jdbc.driver.OracleStatement.prepareDefineBufferAndExecute(OracleStatement.java:1172)
	at oracle.jdbc.driver.OracleStatement.executeMaybeDescribe(OracleStatement.java:1101)
	at oracle.jdbc.driver.OracleStatement.executeSQLSelect(OracleStatement.java:1426)
	at oracle.jdbc.driver.OracleStatement.doExecuteWithTimeout(OracleStatement.java:1309)
	at oracle.jdbc.driver.OracleStatement.executeQuery(OracleStatement.java:1713)
	at oracle.jdbc.driver.OracleStatementWrapper.executeQuery(OracleStatementWrapper.java:394)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.connector.oracle.cdc.logminer.SingleOracleLogMiner.startMiner(SingleOracleLogMiner.java:89)
	at io.tapdata.connector.oracle.cdc.OracleCdcRunner.startCdcRunner(OracleCdcRunner.java:99)
	... 21 more

[INFO ] 2024-06-06 08:12:08.227 - [LongRaw][IMAGETABLE] - PDK connector node stopped: HazelcastSourcePdkDataNode-eeaedb3c-63c0-4b09-ab4e-e4fc451b1947 
[INFO ] 2024-06-06 08:12:08.229 - [LongRaw][IMAGETABLE] - PDK connector node released: HazelcastSourcePdkDataNode-eeaedb3c-63c0-4b09-ab4e-e4fc451b1947 
[INFO ] 2024-06-06 08:12:08.229 - [LongRaw][IMAGETABLE] - Node IMAGETABLE[eeaedb3c-63c0-4b09-ab4e-e4fc451b1947] schema data cleaned 
[INFO ] 2024-06-06 08:12:08.229 - [LongRaw][IMAGETABLE] - Node IMAGETABLE[eeaedb3c-63c0-4b09-ab4e-e4fc451b1947] monitor closed 
[INFO ] 2024-06-06 08:12:08.229 - [LongRaw][IMAGETABLE] - Node IMAGETABLE[eeaedb3c-63c0-4b09-ab4e-e4fc451b1947] close complete, cost 118 ms 
[INFO ] 2024-06-06 08:12:08.229 - [LongRaw][IMAGETABLECOPY] - Node IMAGETABLECOPY[8b773173-63eb-4014-86f9-4ef39a9fa3d1] running status set to false 
[INFO ] 2024-06-06 08:12:08.296 - [LongRaw][IMAGETABLECOPY] - PDK connector node stopped: HazelcastTargetPdkDataNode-8b773173-63eb-4014-86f9-4ef39a9fa3d1 
[INFO ] 2024-06-06 08:12:08.296 - [LongRaw][IMAGETABLECOPY] - PDK connector node released: HazelcastTargetPdkDataNode-8b773173-63eb-4014-86f9-4ef39a9fa3d1 
[INFO ] 2024-06-06 08:12:08.296 - [LongRaw][IMAGETABLECOPY] - Node IMAGETABLECOPY[8b773173-63eb-4014-86f9-4ef39a9fa3d1] schema data cleaned 
[INFO ] 2024-06-06 08:12:08.296 - [LongRaw][IMAGETABLECOPY] - Node IMAGETABLECOPY[8b773173-63eb-4014-86f9-4ef39a9fa3d1] monitor closed 
[INFO ] 2024-06-06 08:12:08.297 - [LongRaw][IMAGETABLECOPY] - Node IMAGETABLECOPY[8b773173-63eb-4014-86f9-4ef39a9fa3d1] close complete, cost 67 ms 
[INFO ] 2024-06-06 08:12:09.060 - [LongRaw] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-06-06 08:12:09.061 - [LongRaw] - Stopped task aspect(s) 
[INFO ] 2024-06-06 08:12:09.061 - [LongRaw] - Snapshot order controller have been removed 
[INFO ] 2024-06-06 08:12:09.092 - [LongRaw] - Remove memory task client succeed, task: LongRaw[665fe41f0ea2021850b59d4a] 
[INFO ] 2024-06-06 08:12:09.297 - [LongRaw] - Destroy memory task client cache succeed, task: LongRaw[665fe41f0ea2021850b59d4a] 
