[INFO ] 2024-03-29 18:38:24.529 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:24.531 - [orders(100)][f690bfee-7575-4277-ba26-f356f6ea5047] - Node f690bfee-7575-4277-ba26-f356f6ea5047[f690bfee-7575-4277-ba26-f356f6ea5047] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:38:24.532 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:24.532 - [orders(100)][f690bfee-7575-4277-ba26-f356f6ea5047] - Node f690bfee-7575-4277-ba26-f356f6ea5047[f690bfee-7575-4277-ba26-f356f6ea5047] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:24.533 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:24.534 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:24.938 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:38:25.214 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@59cbe57e error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@59cbe57e error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@59cbe57e error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:38:26.254 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] running status set to false 
[INFO ] 2024-03-29 18:38:26.304 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:26.305 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:26.307 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] schema data cleaned 
[INFO ] 2024-03-29 18:38:26.309 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] monitor closed 
[INFO ] 2024-03-29 18:38:26.316 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] close complete, cost 79 ms 
[INFO ] 2024-03-29 18:38:27.753 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] running status set to false 
[INFO ] 2024-03-29 18:38:27.754 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] schema data cleaned 
[INFO ] 2024-03-29 18:38:27.754 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] monitor closed 
[INFO ] 2024-03-29 18:38:27.754 - [orders(100)][f690bfee-7575-4277-ba26-f356f6ea5047] - Node f690bfee-7575-4277-ba26-f356f6ea5047[f690bfee-7575-4277-ba26-f356f6ea5047] running status set to false 
[INFO ] 2024-03-29 18:38:27.754 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] close complete, cost 7 ms 
[INFO ] 2024-03-29 18:38:27.755 - [orders(100)][f690bfee-7575-4277-ba26-f356f6ea5047] - Node f690bfee-7575-4277-ba26-f356f6ea5047[f690bfee-7575-4277-ba26-f356f6ea5047] schema data cleaned 
[INFO ] 2024-03-29 18:38:27.755 - [orders(100)][f690bfee-7575-4277-ba26-f356f6ea5047] - Node f690bfee-7575-4277-ba26-f356f6ea5047[f690bfee-7575-4277-ba26-f356f6ea5047] monitor closed 
[INFO ] 2024-03-29 18:38:27.756 - [orders(100)][f690bfee-7575-4277-ba26-f356f6ea5047] - Node f690bfee-7575-4277-ba26-f356f6ea5047[f690bfee-7575-4277-ba26-f356f6ea5047] close complete, cost 4 ms 
[INFO ] 2024-03-29 18:38:27.757 - [orders(100)] - load tapTable task 66069a1b0aba7657dd9a0e9b-f690bfee-7575-4277-ba26-f356f6ea5047 complete, cost 4578ms 
[INFO ] 2024-03-29 18:38:36.956 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:36.962 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:36.970 - [orders(100)][6a0dbf0b-b043-45b7-b73f-49e90b9dd565] - Node 6a0dbf0b-b043-45b7-b73f-49e90b9dd565[6a0dbf0b-b043-45b7-b73f-49e90b9dd565] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:38:36.986 - [orders(100)][73092499-bf4b-4776-95e2-fe8d8cc44d8e] - Node 73092499-bf4b-4776-95e2-fe8d8cc44d8e[73092499-bf4b-4776-95e2-fe8d8cc44d8e] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:38:36.987 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:36.987 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:36.987 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:36.994 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:37.008 - [orders(100)][72e94879-0b71-400c-a7b1-929e1f1eb9e6] - Node 72e94879-0b71-400c-a7b1-929e1f1eb9e6[72e94879-0b71-400c-a7b1-929e1f1eb9e6] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:38:37.008 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:37.009 - [orders(100)][6a0dbf0b-b043-45b7-b73f-49e90b9dd565] - Node 6a0dbf0b-b043-45b7-b73f-49e90b9dd565[6a0dbf0b-b043-45b7-b73f-49e90b9dd565] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:37.009 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:37.009 - [orders(100)][72e94879-0b71-400c-a7b1-929e1f1eb9e6] - Node 72e94879-0b71-400c-a7b1-929e1f1eb9e6[72e94879-0b71-400c-a7b1-929e1f1eb9e6] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:37.025 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:37.039 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:37.039 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:37.044 - [orders(100)][73092499-bf4b-4776-95e2-fe8d8cc44d8e] - Node 73092499-bf4b-4776-95e2-fe8d8cc44d8e[73092499-bf4b-4776-95e2-fe8d8cc44d8e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:37.044 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:37.079 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:38:37.093 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:38:37.093 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:38:37.180 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4f1ca153 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4f1ca153 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4f1ca153 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-29 18:38:37.181 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@45ed883c error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@45ed883c error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@45ed883c error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-29 18:38:37.181 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@612d4e0d error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@612d4e0d error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@612d4e0d error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:38:37.283 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] running status set to false 
[INFO ] 2024-03-29 18:38:37.298 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:37.299 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:37.299 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] schema data cleaned 
[INFO ] 2024-03-29 18:38:37.300 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] monitor closed 
[INFO ] 2024-03-29 18:38:37.501 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] close complete, cost 32 ms 
[INFO ] 2024-03-29 18:38:37.504 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] running status set to false 
[INFO ] 2024-03-29 18:38:37.511 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:37.525 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:37.528 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] schema data cleaned 
[INFO ] 2024-03-29 18:38:37.528 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] monitor closed 
[INFO ] 2024-03-29 18:38:37.532 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] close complete, cost 50 ms 
[INFO ] 2024-03-29 18:38:37.650 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] running status set to false 
[INFO ] 2024-03-29 18:38:37.651 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:37.660 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:37.662 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] schema data cleaned 
[INFO ] 2024-03-29 18:38:37.662 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] monitor closed 
[INFO ] 2024-03-29 18:38:37.663 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] close complete, cost 34 ms 
[INFO ] 2024-03-29 18:38:37.733 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:37.735 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:37.735 - [orders(100)][17aa9fe1-66cb-4eb4-97c2-a9b426edd112] - Node 17aa9fe1-66cb-4eb4-97c2-a9b426edd112[17aa9fe1-66cb-4eb4-97c2-a9b426edd112] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:38:37.735 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:37.735 - [orders(100)][a8b6663a-d64e-482e-90d7-59a8569414af] - Node a8b6663a-d64e-482e-90d7-59a8569414af[a8b6663a-d64e-482e-90d7-59a8569414af] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:38:37.735 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:37.735 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:37.738 - [orders(100)][17aa9fe1-66cb-4eb4-97c2-a9b426edd112] - Node 17aa9fe1-66cb-4eb4-97c2-a9b426edd112[17aa9fe1-66cb-4eb4-97c2-a9b426edd112] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:37.743 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:37.744 - [orders(100)][a8b6663a-d64e-482e-90d7-59a8569414af] - Node a8b6663a-d64e-482e-90d7-59a8569414af[a8b6663a-d64e-482e-90d7-59a8569414af] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:37.744 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:37.744 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:37.808 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:38:37.809 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:38:37.842 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:37.843 - [orders(100)][dc046465-94a8-4dbd-9032-f8c2fa846293] - Node dc046465-94a8-4dbd-9032-f8c2fa846293[dc046465-94a8-4dbd-9032-f8c2fa846293] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:38:37.846 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:37.846 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:37.850 - [orders(100)][dc046465-94a8-4dbd-9032-f8c2fa846293] - Node dc046465-94a8-4dbd-9032-f8c2fa846293[dc046465-94a8-4dbd-9032-f8c2fa846293] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:37.850 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 18:38:37.866 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@e8c4644 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@e8c4644 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@e8c4644 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:38:37.867 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:38:37.867 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3e0b3ca8 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3e0b3ca8 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3e0b3ca8 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:38:37.868 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:37.868 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:37.869 - [orders(100)][e0e9268f-c807-407c-9174-5d370e0ea950] - Node e0e9268f-c807-407c-9174-5d370e0ea950[e0e9268f-c807-407c-9174-5d370e0ea950] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:38:37.869 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:37.869 - [orders(100)][e0e9268f-c807-407c-9174-5d370e0ea950] - Node e0e9268f-c807-407c-9174-5d370e0ea950[e0e9268f-c807-407c-9174-5d370e0ea950] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:37.869 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] preload schema finished, cost 0 ms 
[ERROR] 2024-03-29 18:38:37.888 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@566894d9 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@566894d9 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@566894d9 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:38:37.889 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:38:38.062 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4a205302 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4a205302 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@4a205302 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:38:38.065 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] running status set to false 
[INFO ] 2024-03-29 18:38:38.133 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:38.133 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:38.133 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] schema data cleaned 
[INFO ] 2024-03-29 18:38:38.133 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] monitor closed 
[INFO ] 2024-03-29 18:38:38.134 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] close complete, cost 92 ms 
[INFO ] 2024-03-29 18:38:38.287 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] running status set to false 
[INFO ] 2024-03-29 18:38:38.289 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:38.292 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:38.292 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] schema data cleaned 
[INFO ] 2024-03-29 18:38:38.293 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] monitor closed 
[INFO ] 2024-03-29 18:38:38.298 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] close complete, cost 76 ms 
[INFO ] 2024-03-29 18:38:38.489 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] running status set to false 
[INFO ] 2024-03-29 18:38:38.489 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:38.492 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:38.492 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] schema data cleaned 
[INFO ] 2024-03-29 18:38:38.495 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] monitor closed 
[INFO ] 2024-03-29 18:38:38.496 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] close complete, cost 54 ms 
[INFO ] 2024-03-29 18:38:38.630 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] running status set to false 
[INFO ] 2024-03-29 18:38:38.631 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:38.631 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:38.639 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] schema data cleaned 
[INFO ] 2024-03-29 18:38:38.644 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] monitor closed 
[INFO ] 2024-03-29 18:38:38.646 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:38.646 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:38.646 - [orders(100)][59261e8d-591a-49a8-803a-92455e29c522] - Node 59261e8d-591a-49a8-803a-92455e29c522[59261e8d-591a-49a8-803a-92455e29c522] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:38:38.646 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:38.646 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:38.654 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] close complete, cost 36 ms 
[INFO ] 2024-03-29 18:38:38.655 - [orders(100)][59261e8d-591a-49a8-803a-92455e29c522] - Node 59261e8d-591a-49a8-803a-92455e29c522[59261e8d-591a-49a8-803a-92455e29c522] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:38.732 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:38:38.733 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@32ef581b error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@32ef581b error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@32ef581b error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:38:38.825 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:38.825 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:38.825 - [orders(100)][37105c42-ab8e-44f8-ae5f-65c2e79eedd7] - Node 37105c42-ab8e-44f8-ae5f-65c2e79eedd7[37105c42-ab8e-44f8-ae5f-65c2e79eedd7] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:38:38.826 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:38.826 - [orders(100)][37105c42-ab8e-44f8-ae5f-65c2e79eedd7] - Node 37105c42-ab8e-44f8-ae5f-65c2e79eedd7[37105c42-ab8e-44f8-ae5f-65c2e79eedd7] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:38.851 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:38:38.851 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:38:38.861 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:38.861 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:38.864 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:38.865 - [orders(100)][1521c8bc-e40f-4f5c-931f-04d43ca3b410] - Node 1521c8bc-e40f-4f5c-931f-04d43ca3b410[1521c8bc-e40f-4f5c-931f-04d43ca3b410] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:38:38.865 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:38.866 - [orders(100)][1521c8bc-e40f-4f5c-931f-04d43ca3b410] - Node 1521c8bc-e40f-4f5c-931f-04d43ca3b410[1521c8bc-e40f-4f5c-931f-04d43ca3b410] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:38.888 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:38:38.970 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@70b228ff error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@70b228ff error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@70b228ff error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:38:38.972 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] running status set to false 
[ERROR] 2024-03-29 18:38:38.981 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1c8d47a error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1c8d47a error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1c8d47a error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:38:38.982 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:38.982 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:38.982 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] schema data cleaned 
[INFO ] 2024-03-29 18:38:38.982 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] monitor closed 
[INFO ] 2024-03-29 18:38:38.983 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] close complete, cost 25 ms 
[INFO ] 2024-03-29 18:38:39.156 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] running status set to false 
[INFO ] 2024-03-29 18:38:39.157 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:39.157 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:39.157 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] schema data cleaned 
[INFO ] 2024-03-29 18:38:39.158 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] monitor closed 
[INFO ] 2024-03-29 18:38:39.323 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] close complete, cost 28 ms 
[INFO ] 2024-03-29 18:38:39.325 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] running status set to false 
[INFO ] 2024-03-29 18:38:39.327 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:39.327 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:39.328 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] schema data cleaned 
[INFO ] 2024-03-29 18:38:39.328 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] monitor closed 
[INFO ] 2024-03-29 18:38:39.533 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] close complete, cost 18 ms 
[INFO ] 2024-03-29 18:38:39.711 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] running status set to false 
[INFO ] 2024-03-29 18:38:39.711 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] schema data cleaned 
[INFO ] 2024-03-29 18:38:39.712 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] running status set to false 
[INFO ] 2024-03-29 18:38:39.712 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] monitor closed 
[INFO ] 2024-03-29 18:38:39.712 - [orders(100)][73092499-bf4b-4776-95e2-fe8d8cc44d8e] - Node 73092499-bf4b-4776-95e2-fe8d8cc44d8e[73092499-bf4b-4776-95e2-fe8d8cc44d8e] running status set to false 
[INFO ] 2024-03-29 18:38:39.713 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] schema data cleaned 
[INFO ] 2024-03-29 18:38:39.713 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] close complete, cost 2 ms 
[INFO ] 2024-03-29 18:38:39.715 - [orders(100)][73092499-bf4b-4776-95e2-fe8d8cc44d8e] - Node 73092499-bf4b-4776-95e2-fe8d8cc44d8e[73092499-bf4b-4776-95e2-fe8d8cc44d8e] schema data cleaned 
[INFO ] 2024-03-29 18:38:39.715 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] monitor closed 
[INFO ] 2024-03-29 18:38:39.718 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] running status set to false 
[INFO ] 2024-03-29 18:38:39.718 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] close complete, cost 3 ms 
[INFO ] 2024-03-29 18:38:39.721 - [orders(100)][73092499-bf4b-4776-95e2-fe8d8cc44d8e] - Node 73092499-bf4b-4776-95e2-fe8d8cc44d8e[73092499-bf4b-4776-95e2-fe8d8cc44d8e] monitor closed 
[INFO ] 2024-03-29 18:38:39.721 - [orders(100)][72e94879-0b71-400c-a7b1-929e1f1eb9e6] - Node 72e94879-0b71-400c-a7b1-929e1f1eb9e6[72e94879-0b71-400c-a7b1-929e1f1eb9e6] running status set to false 
[INFO ] 2024-03-29 18:38:39.721 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] schema data cleaned 
[INFO ] 2024-03-29 18:38:39.721 - [orders(100)][73092499-bf4b-4776-95e2-fe8d8cc44d8e] - Node 73092499-bf4b-4776-95e2-fe8d8cc44d8e[73092499-bf4b-4776-95e2-fe8d8cc44d8e] close complete, cost 9 ms 
[INFO ] 2024-03-29 18:38:39.722 - [orders(100)][72e94879-0b71-400c-a7b1-929e1f1eb9e6] - Node 72e94879-0b71-400c-a7b1-929e1f1eb9e6[72e94879-0b71-400c-a7b1-929e1f1eb9e6] schema data cleaned 
[INFO ] 2024-03-29 18:38:39.722 - [orders(100)][6a0dbf0b-b043-45b7-b73f-49e90b9dd565] - Node 6a0dbf0b-b043-45b7-b73f-49e90b9dd565[6a0dbf0b-b043-45b7-b73f-49e90b9dd565] running status set to false 
[INFO ] 2024-03-29 18:38:39.723 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] monitor closed 
[INFO ] 2024-03-29 18:38:39.723 - [orders(100)][72e94879-0b71-400c-a7b1-929e1f1eb9e6] - Node 72e94879-0b71-400c-a7b1-929e1f1eb9e6[72e94879-0b71-400c-a7b1-929e1f1eb9e6] monitor closed 
[INFO ] 2024-03-29 18:38:39.723 - [orders(100)][6a0dbf0b-b043-45b7-b73f-49e90b9dd565] - Node 6a0dbf0b-b043-45b7-b73f-49e90b9dd565[6a0dbf0b-b043-45b7-b73f-49e90b9dd565] schema data cleaned 
[INFO ] 2024-03-29 18:38:39.723 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] close complete, cost 11 ms 
[INFO ] 2024-03-29 18:38:39.723 - [orders(100)] - load tapTable task 66069a1b0aba7657dd9a0e9b-73092499-bf4b-4776-95e2-fe8d8cc44d8e complete, cost 2897ms 
[INFO ] 2024-03-29 18:38:39.723 - [orders(100)][72e94879-0b71-400c-a7b1-929e1f1eb9e6] - Node 72e94879-0b71-400c-a7b1-929e1f1eb9e6[72e94879-0b71-400c-a7b1-929e1f1eb9e6] close complete, cost 8 ms 
[INFO ] 2024-03-29 18:38:39.723 - [orders(100)][6a0dbf0b-b043-45b7-b73f-49e90b9dd565] - Node 6a0dbf0b-b043-45b7-b73f-49e90b9dd565[6a0dbf0b-b043-45b7-b73f-49e90b9dd565] monitor closed 
[INFO ] 2024-03-29 18:38:39.723 - [orders(100)][6a0dbf0b-b043-45b7-b73f-49e90b9dd565] - Node 6a0dbf0b-b043-45b7-b73f-49e90b9dd565[6a0dbf0b-b043-45b7-b73f-49e90b9dd565] close complete, cost 10 ms 
[INFO ] 2024-03-29 18:38:39.728 - [orders(100)] - load tapTable task 66069a1b0aba7657dd9a0e9b-72e94879-0b71-400c-a7b1-929e1f1eb9e6 complete, cost 2843ms 
[INFO ] 2024-03-29 18:38:39.758 - [orders(100)] - load tapTable task 66069a1b0aba7657dd9a0e9b-6a0dbf0b-b043-45b7-b73f-49e90b9dd565 complete, cost 2883ms 
[INFO ] 2024-03-29 18:38:40.089 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:40.089 - [orders(100)][2ee356c9-e13e-4298-936a-0c77daa6e8f6] - Node 2ee356c9-e13e-4298-936a-0c77daa6e8f6[2ee356c9-e13e-4298-936a-0c77daa6e8f6] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:38:40.091 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:40.091 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:40.091 - [orders(100)][2ee356c9-e13e-4298-936a-0c77daa6e8f6] - Node 2ee356c9-e13e-4298-936a-0c77daa6e8f6[2ee356c9-e13e-4298-936a-0c77daa6e8f6] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:38:40.091 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] preload schema finished, cost 2 ms 
[INFO ] 2024-03-29 18:38:40.176 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:38:40.176 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@203ad74c error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@203ad74c error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@203ad74c error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:38:40.179 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:40.179 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:40.179 - [orders(100)][1b927cd1-a3ea-4c95-aa59-a51cba0c141a] - Node 1b927cd1-a3ea-4c95-aa59-a51cba0c141a[1b927cd1-a3ea-4c95-aa59-a51cba0c141a] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:38:40.179 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:40.180 - [orders(100)][1b927cd1-a3ea-4c95-aa59-a51cba0c141a] - Node 1b927cd1-a3ea-4c95-aa59-a51cba0c141a[1b927cd1-a3ea-4c95-aa59-a51cba0c141a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:40.180 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:40.221 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:38:40.221 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@489ea897 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@489ea897 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@489ea897 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:38:40.300 - [orders(100)][bb81beda-f413-40a7-845f-69c4ba9cda6f] - Node bb81beda-f413-40a7-845f-69c4ba9cda6f[bb81beda-f413-40a7-845f-69c4ba9cda6f] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:38:40.300 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:40.300 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:40.300 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:40.301 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:40.301 - [orders(100)][bb81beda-f413-40a7-845f-69c4ba9cda6f] - Node bb81beda-f413-40a7-845f-69c4ba9cda6f[bb81beda-f413-40a7-845f-69c4ba9cda6f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:40.328 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-29 18:38:40.328 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] running status set to false 
[INFO ] 2024-03-29 18:38:40.348 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:40.348 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:40.348 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] schema data cleaned 
[INFO ] 2024-03-29 18:38:40.348 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] monitor closed 
[INFO ] 2024-03-29 18:38:40.348 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] close complete, cost 22 ms 
[ERROR] 2024-03-29 18:38:40.382 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@61d6f3bc error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@61d6f3bc error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@61d6f3bc error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:38:40.394 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] running status set to false 
[INFO ] 2024-03-29 18:38:40.394 - [orders(100)][17aa9fe1-66cb-4eb4-97c2-a9b426edd112] - Node 17aa9fe1-66cb-4eb4-97c2-a9b426edd112[17aa9fe1-66cb-4eb4-97c2-a9b426edd112] running status set to false 
[INFO ] 2024-03-29 18:38:40.395 - [orders(100)][17aa9fe1-66cb-4eb4-97c2-a9b426edd112] - Node 17aa9fe1-66cb-4eb4-97c2-a9b426edd112[17aa9fe1-66cb-4eb4-97c2-a9b426edd112] schema data cleaned 
[INFO ] 2024-03-29 18:38:40.395 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] schema data cleaned 
[INFO ] 2024-03-29 18:38:40.395 - [orders(100)][17aa9fe1-66cb-4eb4-97c2-a9b426edd112] - Node 17aa9fe1-66cb-4eb4-97c2-a9b426edd112[17aa9fe1-66cb-4eb4-97c2-a9b426edd112] monitor closed 
[INFO ] 2024-03-29 18:38:40.396 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] monitor closed 
[INFO ] 2024-03-29 18:38:40.396 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] close complete, cost 3 ms 
[INFO ] 2024-03-29 18:38:40.397 - [orders(100)][17aa9fe1-66cb-4eb4-97c2-a9b426edd112] - Node 17aa9fe1-66cb-4eb4-97c2-a9b426edd112[17aa9fe1-66cb-4eb4-97c2-a9b426edd112] close complete, cost 3 ms 
[INFO ] 2024-03-29 18:38:40.397 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] running status set to false 
[INFO ] 2024-03-29 18:38:40.398 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] schema data cleaned 
[INFO ] 2024-03-29 18:38:40.398 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] monitor closed 
[INFO ] 2024-03-29 18:38:40.398 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:38:40.403 - [orders(100)] - load tapTable task 66069a1b0aba7657dd9a0e9b-17aa9fe1-66cb-4eb4-97c2-a9b426edd112 complete, cost 2786ms 
[INFO ] 2024-03-29 18:38:40.403 - [orders(100)][a8b6663a-d64e-482e-90d7-59a8569414af] - Node a8b6663a-d64e-482e-90d7-59a8569414af[a8b6663a-d64e-482e-90d7-59a8569414af] running status set to false 
[INFO ] 2024-03-29 18:38:40.403 - [orders(100)][a8b6663a-d64e-482e-90d7-59a8569414af] - Node a8b6663a-d64e-482e-90d7-59a8569414af[a8b6663a-d64e-482e-90d7-59a8569414af] schema data cleaned 
[INFO ] 2024-03-29 18:38:40.403 - [orders(100)][a8b6663a-d64e-482e-90d7-59a8569414af] - Node a8b6663a-d64e-482e-90d7-59a8569414af[a8b6663a-d64e-482e-90d7-59a8569414af] monitor closed 
[INFO ] 2024-03-29 18:38:40.404 - [orders(100)][a8b6663a-d64e-482e-90d7-59a8569414af] - Node a8b6663a-d64e-482e-90d7-59a8569414af[a8b6663a-d64e-482e-90d7-59a8569414af] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:38:40.404 - [orders(100)] - load tapTable task 66069a1b0aba7657dd9a0e9b-a8b6663a-d64e-482e-90d7-59a8569414af complete, cost 2790ms 
[INFO ] 2024-03-29 18:38:40.405 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] running status set to false 
[INFO ] 2024-03-29 18:38:40.405 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] schema data cleaned 
[INFO ] 2024-03-29 18:38:40.405 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] monitor closed 
[INFO ] 2024-03-29 18:38:40.405 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:38:40.411 - [orders(100)][dc046465-94a8-4dbd-9032-f8c2fa846293] - Node dc046465-94a8-4dbd-9032-f8c2fa846293[dc046465-94a8-4dbd-9032-f8c2fa846293] running status set to false 
[INFO ] 2024-03-29 18:38:40.411 - [orders(100)][dc046465-94a8-4dbd-9032-f8c2fa846293] - Node dc046465-94a8-4dbd-9032-f8c2fa846293[dc046465-94a8-4dbd-9032-f8c2fa846293] schema data cleaned 
[INFO ] 2024-03-29 18:38:40.411 - [orders(100)][dc046465-94a8-4dbd-9032-f8c2fa846293] - Node dc046465-94a8-4dbd-9032-f8c2fa846293[dc046465-94a8-4dbd-9032-f8c2fa846293] monitor closed 
[INFO ] 2024-03-29 18:38:40.411 - [orders(100)][dc046465-94a8-4dbd-9032-f8c2fa846293] - Node dc046465-94a8-4dbd-9032-f8c2fa846293[dc046465-94a8-4dbd-9032-f8c2fa846293] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:38:40.439 - [orders(100)] - load tapTable task 66069a1b0aba7657dd9a0e9b-dc046465-94a8-4dbd-9032-f8c2fa846293 complete, cost 2658ms 
[INFO ] 2024-03-29 18:38:40.439 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] running status set to false 
[INFO ] 2024-03-29 18:38:40.439 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] schema data cleaned 
[INFO ] 2024-03-29 18:38:40.439 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] monitor closed 
[INFO ] 2024-03-29 18:38:40.443 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] close complete, cost 4 ms 
[INFO ] 2024-03-29 18:38:40.444 - [orders(100)][e0e9268f-c807-407c-9174-5d370e0ea950] - Node e0e9268f-c807-407c-9174-5d370e0ea950[e0e9268f-c807-407c-9174-5d370e0ea950] running status set to false 
[INFO ] 2024-03-29 18:38:40.444 - [orders(100)][e0e9268f-c807-407c-9174-5d370e0ea950] - Node e0e9268f-c807-407c-9174-5d370e0ea950[e0e9268f-c807-407c-9174-5d370e0ea950] schema data cleaned 
[INFO ] 2024-03-29 18:38:40.444 - [orders(100)][e0e9268f-c807-407c-9174-5d370e0ea950] - Node e0e9268f-c807-407c-9174-5d370e0ea950[e0e9268f-c807-407c-9174-5d370e0ea950] monitor closed 
[INFO ] 2024-03-29 18:38:40.444 - [orders(100)][e0e9268f-c807-407c-9174-5d370e0ea950] - Node e0e9268f-c807-407c-9174-5d370e0ea950[e0e9268f-c807-407c-9174-5d370e0ea950] close complete, cost 2 ms 
[INFO ] 2024-03-29 18:38:40.547 - [orders(100)] - load tapTable task 66069a1b0aba7657dd9a0e9b-e0e9268f-c807-407c-9174-5d370e0ea950 complete, cost 2634ms 
[ERROR] 2024-03-29 18:38:40.547 - [orders(100)][Order Details] - start source runner failed: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e <-- Error Message -->
start source runner failed: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e

<-- Simple Stack Trace -->
Caused by: io.tapdata.entity.error.CoreException: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e
	io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.wrapTapCodeException(HazelcastBaseNode.java:649)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.errorHandle(HazelcastBaseNode.java:561)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:187)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.doInit(HazelcastSampleSourcePdkDataNode.java:60)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: Failed to create pdk connector node, database type: mysql-io.tapdata-1.0-SNAPSHOT-public, message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:232)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastPdkBaseNode.createPdkConnectorNode(HazelcastPdkBaseNode.java:202)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSampleSourcePdkDataNode.startSourceRunner(HazelcastSampleSourcePdkDataNode.java:92)
	... 14 more
Caused by: code: 50001 | message: Connector TapNodeId mysql not found for associateId HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e
	at io.tapdata.pdk.core.tapnode.TapNodeClassFactory.createTapConnector(TapNodeClassFactory.java:55)
	at io.tapdata.pdk.core.connector.TapConnector.createTapConnector(TapConnector.java:102)
	at io.tapdata.pdk.core.connector.TapConnectorManager.createConnectorInstance(TapConnectorManager.java:71)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:389)
	at io.tapdata.pdk.core.api.PDKIntegration$ConnectorBuilderEx.build(PDKIntegration.java:386)
	at io.tapdata.flow.engine.V2.util.PdkUtil.createNode(PdkUtil.java:230)
	... 16 more

[INFO ] 2024-03-29 18:38:40.593 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] running status set to false 
[INFO ] 2024-03-29 18:38:40.593 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:40.593 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:40.593 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] schema data cleaned 
[INFO ] 2024-03-29 18:38:40.593 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] monitor closed 
[INFO ] 2024-03-29 18:38:40.802 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] close complete, cost 20 ms 
[INFO ] 2024-03-29 18:38:41.252 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] running status set to false 
[INFO ] 2024-03-29 18:38:41.252 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] schema data cleaned 
[INFO ] 2024-03-29 18:38:41.252 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] monitor closed 
[INFO ] 2024-03-29 18:38:41.253 - [orders(100)][59261e8d-591a-49a8-803a-92455e29c522] - Node 59261e8d-591a-49a8-803a-92455e29c522[59261e8d-591a-49a8-803a-92455e29c522] running status set to false 
[INFO ] 2024-03-29 18:38:41.253 - [orders(100)][59261e8d-591a-49a8-803a-92455e29c522] - Node 59261e8d-591a-49a8-803a-92455e29c522[59261e8d-591a-49a8-803a-92455e29c522] schema data cleaned 
[INFO ] 2024-03-29 18:38:41.253 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] close complete, cost 6 ms 
[INFO ] 2024-03-29 18:38:41.253 - [orders(100)][59261e8d-591a-49a8-803a-92455e29c522] - Node 59261e8d-591a-49a8-803a-92455e29c522[59261e8d-591a-49a8-803a-92455e29c522] monitor closed 
[INFO ] 2024-03-29 18:38:41.255 - [orders(100)][59261e8d-591a-49a8-803a-92455e29c522] - Node 59261e8d-591a-49a8-803a-92455e29c522[59261e8d-591a-49a8-803a-92455e29c522] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:38:41.255 - [orders(100)] - load tapTable task 66069a1b0aba7657dd9a0e9b-59261e8d-591a-49a8-803a-92455e29c522 complete, cost 2898ms 
[INFO ] 2024-03-29 18:38:41.469 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] running status set to false 
[INFO ] 2024-03-29 18:38:41.470 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] schema data cleaned 
[INFO ] 2024-03-29 18:38:41.470 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] monitor closed 
[INFO ] 2024-03-29 18:38:41.470 - [orders(100)][37105c42-ab8e-44f8-ae5f-65c2e79eedd7] - Node 37105c42-ab8e-44f8-ae5f-65c2e79eedd7[37105c42-ab8e-44f8-ae5f-65c2e79eedd7] running status set to false 
[INFO ] 2024-03-29 18:38:41.470 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:38:41.470 - [orders(100)][37105c42-ab8e-44f8-ae5f-65c2e79eedd7] - Node 37105c42-ab8e-44f8-ae5f-65c2e79eedd7[37105c42-ab8e-44f8-ae5f-65c2e79eedd7] schema data cleaned 
[INFO ] 2024-03-29 18:38:41.471 - [orders(100)][37105c42-ab8e-44f8-ae5f-65c2e79eedd7] - Node 37105c42-ab8e-44f8-ae5f-65c2e79eedd7[37105c42-ab8e-44f8-ae5f-65c2e79eedd7] monitor closed 
[INFO ] 2024-03-29 18:38:41.472 - [orders(100)][37105c42-ab8e-44f8-ae5f-65c2e79eedd7] - Node 37105c42-ab8e-44f8-ae5f-65c2e79eedd7[37105c42-ab8e-44f8-ae5f-65c2e79eedd7] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:38:41.472 - [orders(100)] - load tapTable task 66069a1b0aba7657dd9a0e9b-37105c42-ab8e-44f8-ae5f-65c2e79eedd7 complete, cost 2816ms 
[INFO ] 2024-03-29 18:38:41.501 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] running status set to false 
[INFO ] 2024-03-29 18:38:41.501 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] schema data cleaned 
[INFO ] 2024-03-29 18:38:41.501 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] monitor closed 
[INFO ] 2024-03-29 18:38:41.501 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:38:41.502 - [orders(100)][1521c8bc-e40f-4f5c-931f-04d43ca3b410] - Node 1521c8bc-e40f-4f5c-931f-04d43ca3b410[1521c8bc-e40f-4f5c-931f-04d43ca3b410] running status set to false 
[INFO ] 2024-03-29 18:38:41.502 - [orders(100)][1521c8bc-e40f-4f5c-931f-04d43ca3b410] - Node 1521c8bc-e40f-4f5c-931f-04d43ca3b410[1521c8bc-e40f-4f5c-931f-04d43ca3b410] schema data cleaned 
[INFO ] 2024-03-29 18:38:41.502 - [orders(100)][1521c8bc-e40f-4f5c-931f-04d43ca3b410] - Node 1521c8bc-e40f-4f5c-931f-04d43ca3b410[1521c8bc-e40f-4f5c-931f-04d43ca3b410] monitor closed 
[INFO ] 2024-03-29 18:38:41.503 - [orders(100)][1521c8bc-e40f-4f5c-931f-04d43ca3b410] - Node 1521c8bc-e40f-4f5c-931f-04d43ca3b410[1521c8bc-e40f-4f5c-931f-04d43ca3b410] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:38:41.706 - [orders(100)] - load tapTable task 66069a1b0aba7657dd9a0e9b-1521c8bc-e40f-4f5c-931f-04d43ca3b410 complete, cost 2793ms 
[INFO ] 2024-03-29 18:38:42.115 - [orders(100)][58bd545c-e34f-4a67-99a3-024088962129] - Node 58bd545c-e34f-4a67-99a3-024088962129[58bd545c-e34f-4a67-99a3-024088962129] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:38:42.115 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:42.115 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:42.118 - [orders(100)][58bd545c-e34f-4a67-99a3-024088962129] - Node 58bd545c-e34f-4a67-99a3-024088962129[58bd545c-e34f-4a67-99a3-024088962129] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:42.118 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:42.118 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:42.199 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:38:42.199 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3d5d8df2 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3d5d8df2 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3d5d8df2 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:38:42.362 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] running status set to false 
[INFO ] 2024-03-29 18:38:42.362 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:42.363 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:42.363 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] schema data cleaned 
[INFO ] 2024-03-29 18:38:42.363 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] monitor closed 
[INFO ] 2024-03-29 18:38:42.364 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] close complete, cost 19 ms 
[INFO ] 2024-03-29 18:38:42.699 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] running status set to false 
[INFO ] 2024-03-29 18:38:42.700 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] schema data cleaned 
[INFO ] 2024-03-29 18:38:42.700 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] monitor closed 
[INFO ] 2024-03-29 18:38:42.704 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:38:42.704 - [orders(100)][2ee356c9-e13e-4298-936a-0c77daa6e8f6] - Node 2ee356c9-e13e-4298-936a-0c77daa6e8f6[2ee356c9-e13e-4298-936a-0c77daa6e8f6] running status set to false 
[INFO ] 2024-03-29 18:38:42.705 - [orders(100)][2ee356c9-e13e-4298-936a-0c77daa6e8f6] - Node 2ee356c9-e13e-4298-936a-0c77daa6e8f6[2ee356c9-e13e-4298-936a-0c77daa6e8f6] schema data cleaned 
[INFO ] 2024-03-29 18:38:42.705 - [orders(100)][2ee356c9-e13e-4298-936a-0c77daa6e8f6] - Node 2ee356c9-e13e-4298-936a-0c77daa6e8f6[2ee356c9-e13e-4298-936a-0c77daa6e8f6] monitor closed 
[INFO ] 2024-03-29 18:38:42.706 - [orders(100)][2ee356c9-e13e-4298-936a-0c77daa6e8f6] - Node 2ee356c9-e13e-4298-936a-0c77daa6e8f6[2ee356c9-e13e-4298-936a-0c77daa6e8f6] close complete, cost 1 ms 
[INFO ] 2024-03-29 18:38:42.707 - [orders(100)] - load tapTable task 66069a1b0aba7657dd9a0e9b-2ee356c9-e13e-4298-936a-0c77daa6e8f6 complete, cost 2681ms 
[INFO ] 2024-03-29 18:38:42.749 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] running status set to false 
[INFO ] 2024-03-29 18:38:42.749 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] schema data cleaned 
[INFO ] 2024-03-29 18:38:42.752 - [orders(100)][1b927cd1-a3ea-4c95-aa59-a51cba0c141a] - Node 1b927cd1-a3ea-4c95-aa59-a51cba0c141a[1b927cd1-a3ea-4c95-aa59-a51cba0c141a] running status set to false 
[INFO ] 2024-03-29 18:38:42.753 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] monitor closed 
[INFO ] 2024-03-29 18:38:42.761 - [orders(100)][1b927cd1-a3ea-4c95-aa59-a51cba0c141a] - Node 1b927cd1-a3ea-4c95-aa59-a51cba0c141a[1b927cd1-a3ea-4c95-aa59-a51cba0c141a] schema data cleaned 
[INFO ] 2024-03-29 18:38:42.763 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] close complete, cost 12 ms 
[INFO ] 2024-03-29 18:38:42.764 - [orders(100)][1b927cd1-a3ea-4c95-aa59-a51cba0c141a] - Node 1b927cd1-a3ea-4c95-aa59-a51cba0c141a[1b927cd1-a3ea-4c95-aa59-a51cba0c141a] monitor closed 
[INFO ] 2024-03-29 18:38:42.764 - [orders(100)][1b927cd1-a3ea-4c95-aa59-a51cba0c141a] - Node 1b927cd1-a3ea-4c95-aa59-a51cba0c141a[1b927cd1-a3ea-4c95-aa59-a51cba0c141a] close complete, cost 16 ms 
[INFO ] 2024-03-29 18:38:42.969 - [orders(100)] - load tapTable task 66069a1b0aba7657dd9a0e9b-1b927cd1-a3ea-4c95-aa59-a51cba0c141a complete, cost 2637ms 
[INFO ] 2024-03-29 18:38:43.080 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] running status set to false 
[INFO ] 2024-03-29 18:38:43.081 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] running status set to false 
[INFO ] 2024-03-29 18:38:43.081 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] schema data cleaned 
[INFO ] 2024-03-29 18:38:43.081 - [orders(100)][bb81beda-f413-40a7-845f-69c4ba9cda6f] - Node bb81beda-f413-40a7-845f-69c4ba9cda6f[bb81beda-f413-40a7-845f-69c4ba9cda6f] running status set to false 
[INFO ] 2024-03-29 18:38:43.081 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] monitor closed 
[INFO ] 2024-03-29 18:38:43.081 - [orders(100)][bb81beda-f413-40a7-845f-69c4ba9cda6f] - Node bb81beda-f413-40a7-845f-69c4ba9cda6f[bb81beda-f413-40a7-845f-69c4ba9cda6f] schema data cleaned 
[INFO ] 2024-03-29 18:38:43.081 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] close complete, cost 3 ms 
[INFO ] 2024-03-29 18:38:43.081 - [orders(100)][bb81beda-f413-40a7-845f-69c4ba9cda6f] - Node bb81beda-f413-40a7-845f-69c4ba9cda6f[bb81beda-f413-40a7-845f-69c4ba9cda6f] monitor closed 
[INFO ] 2024-03-29 18:38:43.081 - [orders(100)][bb81beda-f413-40a7-845f-69c4ba9cda6f] - Node bb81beda-f413-40a7-845f-69c4ba9cda6f[bb81beda-f413-40a7-845f-69c4ba9cda6f] close complete, cost 2 ms 
[INFO ] 2024-03-29 18:38:43.081 - [orders(100)][Order Details] - PDK connector node stopped: null 
[INFO ] 2024-03-29 18:38:43.083 - [orders(100)][Order Details] - PDK connector node released: null 
[INFO ] 2024-03-29 18:38:43.084 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] schema data cleaned 
[INFO ] 2024-03-29 18:38:43.084 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] monitor closed 
[INFO ] 2024-03-29 18:38:43.086 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] close complete, cost 8 ms 
[INFO ] 2024-03-29 18:38:43.086 - [orders(100)] - load tapTable task 66069a1b0aba7657dd9a0e9b-bb81beda-f413-40a7-845f-69c4ba9cda6f complete, cost 2837ms 
[INFO ] 2024-03-29 18:38:44.733 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] running status set to false 
[INFO ] 2024-03-29 18:38:44.735 - [orders(100)][58bd545c-e34f-4a67-99a3-024088962129] - Node 58bd545c-e34f-4a67-99a3-024088962129[58bd545c-e34f-4a67-99a3-024088962129] running status set to false 
[INFO ] 2024-03-29 18:38:44.735 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] schema data cleaned 
[INFO ] 2024-03-29 18:38:44.736 - [orders(100)][58bd545c-e34f-4a67-99a3-024088962129] - Node 58bd545c-e34f-4a67-99a3-024088962129[58bd545c-e34f-4a67-99a3-024088962129] schema data cleaned 
[INFO ] 2024-03-29 18:38:44.736 - [orders(100)][58bd545c-e34f-4a67-99a3-024088962129] - Node 58bd545c-e34f-4a67-99a3-024088962129[58bd545c-e34f-4a67-99a3-024088962129] monitor closed 
[INFO ] 2024-03-29 18:38:44.736 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] monitor closed 
[INFO ] 2024-03-29 18:38:44.736 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] close complete, cost 8 ms 
[INFO ] 2024-03-29 18:38:44.738 - [orders(100)][58bd545c-e34f-4a67-99a3-024088962129] - Node 58bd545c-e34f-4a67-99a3-024088962129[58bd545c-e34f-4a67-99a3-024088962129] close complete, cost 4 ms 
[INFO ] 2024-03-29 18:38:44.738 - [orders(100)] - load tapTable task 66069a1b0aba7657dd9a0e9b-58bd545c-e34f-4a67-99a3-024088962129 complete, cost 2673ms 
[INFO ] 2024-03-29 18:38:46.821 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:46.822 - [orders(100)][5eae48c6-0eb0-4245-8f03-e43c68bb933f] - Node 5eae48c6-0eb0-4245-8f03-e43c68bb933f[5eae48c6-0eb0-4245-8f03-e43c68bb933f] start preload schema,table counts: 0 
[INFO ] 2024-03-29 18:38:46.822 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] start preload schema,table counts: 1 
[INFO ] 2024-03-29 18:38:46.822 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] preload schema finished, cost 1 ms 
[INFO ] 2024-03-29 18:38:46.822 - [orders(100)][5eae48c6-0eb0-4245-8f03-e43c68bb933f] - Node 5eae48c6-0eb0-4245-8f03-e43c68bb933f[5eae48c6-0eb0-4245-8f03-e43c68bb933f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:46.872 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] preload schema finished, cost 0 ms 
[INFO ] 2024-03-29 18:38:46.872 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-29 18:38:47.052 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@68437778 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@68437778 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@68437778 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:129)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:485)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-29 18:38:47.059 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] running status set to false 
[INFO ] 2024-03-29 18:38:47.064 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:47.065 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-bf29f76b-60e3-4904-98a9-cd192e06a90e 
[INFO ] 2024-03-29 18:38:47.065 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] schema data cleaned 
[INFO ] 2024-03-29 18:38:47.065 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] monitor closed 
[INFO ] 2024-03-29 18:38:47.065 - [orders(100)][Order Details] - Node Order Details[bf29f76b-60e3-4904-98a9-cd192e06a90e] close complete, cost 14 ms 
[INFO ] 2024-03-29 18:38:49.418 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] running status set to false 
[INFO ] 2024-03-29 18:38:49.418 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] schema data cleaned 
[INFO ] 2024-03-29 18:38:49.418 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] monitor closed 
[INFO ] 2024-03-29 18:38:49.418 - [orders(100)][Order Details] - Node Order Details[ee6beffa-b287-422e-9f45-6c32fe6f6c74] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:38:49.423 - [orders(100)][5eae48c6-0eb0-4245-8f03-e43c68bb933f] - Node 5eae48c6-0eb0-4245-8f03-e43c68bb933f[5eae48c6-0eb0-4245-8f03-e43c68bb933f] running status set to false 
[INFO ] 2024-03-29 18:38:49.423 - [orders(100)][5eae48c6-0eb0-4245-8f03-e43c68bb933f] - Node 5eae48c6-0eb0-4245-8f03-e43c68bb933f[5eae48c6-0eb0-4245-8f03-e43c68bb933f] schema data cleaned 
[INFO ] 2024-03-29 18:38:49.424 - [orders(100)][5eae48c6-0eb0-4245-8f03-e43c68bb933f] - Node 5eae48c6-0eb0-4245-8f03-e43c68bb933f[5eae48c6-0eb0-4245-8f03-e43c68bb933f] monitor closed 
[INFO ] 2024-03-29 18:38:49.424 - [orders(100)][5eae48c6-0eb0-4245-8f03-e43c68bb933f] - Node 5eae48c6-0eb0-4245-8f03-e43c68bb933f[5eae48c6-0eb0-4245-8f03-e43c68bb933f] close complete, cost 0 ms 
[INFO ] 2024-03-29 18:38:49.425 - [orders(100)] - load tapTable task 66069a1b0aba7657dd9a0e9b-5eae48c6-0eb0-4245-8f03-e43c68bb933f complete, cost 2670ms 
