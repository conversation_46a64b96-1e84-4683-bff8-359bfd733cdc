[INFO ] 2024-03-28 14:51:02.609 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:02.610 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:02.610 - [orders(100)][6a4d224d-622d-4bd6-b938-98da4a079922] - Node 6a4d224d-622d-4bd6-b938-98da4a079922[6a4d224d-622d-4bd6-b938-98da4a079922] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:51:02.614 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:02.615 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:02.616 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:02.616 - [orders(100)][6a4d224d-622d-4bd6-b938-98da4a079922] - Node 6a4d224d-622d-4bd6-b938-98da4a079922[6a4d224d-622d-4bd6-b938-98da4a079922] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:02.616 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:02.616 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:02.668 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:02.670 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 14:51:02.871 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@39112c01 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@39112c01 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@39112c01 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 14:51:03.286 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:51:03.287 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 14:51:03.299 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 14:51:03.299 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 14:51:03.305 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:03.305 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:03.305 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 14:51:03.306 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 14:51:03.306 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 19 ms 
[INFO ] 2024-03-28 14:51:03.382 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 14:51:03.382 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 14:51:03.382 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 14:51:03.382 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 14:51:03.383 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 83 ms 
[INFO ] 2024-03-28 14:51:03.383 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 87 ms 
[INFO ] 2024-03-28 14:51:03.680 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:03.680 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:03.680 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:03.681 - [orders(100)][f995139d-a384-44b5-8220-916bf8ff9669] - Node f995139d-a384-44b5-8220-916bf8ff9669[f995139d-a384-44b5-8220-916bf8ff9669] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:51:03.681 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:03.681 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 2 ms 
[INFO ] 2024-03-28 14:51:03.682 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 2 ms 
[INFO ] 2024-03-28 14:51:03.682 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 2 ms 
[INFO ] 2024-03-28 14:51:03.682 - [orders(100)][f995139d-a384-44b5-8220-916bf8ff9669] - Node f995139d-a384-44b5-8220-916bf8ff9669[f995139d-a384-44b5-8220-916bf8ff9669] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:03.747 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:03.748 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 14:51:03.948 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2ba52382 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2ba52382 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2ba52382 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 14:51:03.973 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:51:03.974 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 14:51:03.987 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 14:51:03.987 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 14:51:03.998 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:04.004 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:04.005 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 14:51:04.005 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 14:51:04.005 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 40 ms 
[INFO ] 2024-03-28 14:51:04.057 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 14:51:04.057 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 14:51:04.057 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 14:51:04.057 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 14:51:04.057 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 87 ms 
[INFO ] 2024-03-28 14:51:04.057 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 86 ms 
[INFO ] 2024-03-28 14:51:04.172 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:04.172 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:04.172 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:04.172 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:04.173 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:04.173 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:04.173 - [orders(100)][0d500337-d7ee-4e9a-9892-0a0a09cf27fb] - Node 0d500337-d7ee-4e9a-9892-0a0a09cf27fb[0d500337-d7ee-4e9a-9892-0a0a09cf27fb] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:51:04.173 - [orders(100)][0d500337-d7ee-4e9a-9892-0a0a09cf27fb] - Node 0d500337-d7ee-4e9a-9892-0a0a09cf27fb[0d500337-d7ee-4e9a-9892-0a0a09cf27fb] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:04.173 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:04.173 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:04.188 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 14:51:04.294 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@58307f8a error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@58307f8a error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@58307f8a error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 14:51:04.294 - [orders(100)][4509a762-6380-47ff-a830-43b61e1a0269] - Node 4509a762-6380-47ff-a830-43b61e1a0269[4509a762-6380-47ff-a830-43b61e1a0269] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:51:04.294 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:04.294 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:04.294 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:04.294 - [orders(100)][4509a762-6380-47ff-a830-43b61e1a0269] - Node 4509a762-6380-47ff-a830-43b61e1a0269[4509a762-6380-47ff-a830-43b61e1a0269] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:04.294 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:04.294 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:04.294 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:04.294 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:04.317 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:04.317 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 14:51:04.404 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1b05da7f error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1b05da7f error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1b05da7f error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 14:51:04.412 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:51:04.412 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 14:51:04.430 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 14:51:04.430 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:04.431 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 14:51:04.431 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:04.432 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 14:51:04.432 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 14:51:04.513 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 24 ms 
[INFO ] 2024-03-28 14:51:04.513 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 14:51:04.513 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 14:51:04.514 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 14:51:04.514 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 14:51:04.514 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 100 ms 
[INFO ] 2024-03-28 14:51:04.514 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 95 ms 
[WARN ] 2024-03-28 14:51:04.585 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:51:04.585 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 14:51:04.600 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 14:51:04.601 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 14:51:04.601 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:04.602 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:04.602 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 14:51:04.602 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 14:51:04.678 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 19 ms 
[INFO ] 2024-03-28 14:51:04.680 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 14:51:04.680 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 14:51:04.680 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 14:51:04.680 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 14:51:04.681 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 75 ms 
[INFO ] 2024-03-28 14:51:04.681 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 74 ms 
[INFO ] 2024-03-28 14:51:05.252 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 14:51:05.253 - [orders(100)][6a4d224d-622d-4bd6-b938-98da4a079922] - Node 6a4d224d-622d-4bd6-b938-98da4a079922[6a4d224d-622d-4bd6-b938-98da4a079922] running status set to false 
[INFO ] 2024-03-28 14:51:05.253 - [orders(100)][6a4d224d-622d-4bd6-b938-98da4a079922] - Node 6a4d224d-622d-4bd6-b938-98da4a079922[6a4d224d-622d-4bd6-b938-98da4a079922] schema data cleaned 
[INFO ] 2024-03-28 14:51:05.253 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 14:51:05.255 - [orders(100)][6a4d224d-622d-4bd6-b938-98da4a079922] - Node 6a4d224d-622d-4bd6-b938-98da4a079922[6a4d224d-622d-4bd6-b938-98da4a079922] monitor closed 
[INFO ] 2024-03-28 14:51:05.255 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 14:51:05.255 - [orders(100)][6a4d224d-622d-4bd6-b938-98da4a079922] - Node 6a4d224d-622d-4bd6-b938-98da4a079922[6a4d224d-622d-4bd6-b938-98da4a079922] close complete, cost 7 ms 
[INFO ] 2024-03-28 14:51:05.258 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 12 ms 
[INFO ] 2024-03-28 14:51:05.258 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-6a4d224d-622d-4bd6-b938-98da4a079922 complete, cost 2718ms 
[INFO ] 2024-03-28 14:51:06.348 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 14:51:06.355 - [orders(100)][f995139d-a384-44b5-8220-916bf8ff9669] - Node f995139d-a384-44b5-8220-916bf8ff9669[f995139d-a384-44b5-8220-916bf8ff9669] running status set to false 
[INFO ] 2024-03-28 14:51:06.355 - [orders(100)][f995139d-a384-44b5-8220-916bf8ff9669] - Node f995139d-a384-44b5-8220-916bf8ff9669[f995139d-a384-44b5-8220-916bf8ff9669] schema data cleaned 
[INFO ] 2024-03-28 14:51:06.356 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 14:51:06.356 - [orders(100)][f995139d-a384-44b5-8220-916bf8ff9669] - Node f995139d-a384-44b5-8220-916bf8ff9669[f995139d-a384-44b5-8220-916bf8ff9669] monitor closed 
[INFO ] 2024-03-28 14:51:06.356 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 14:51:06.357 - [orders(100)][f995139d-a384-44b5-8220-916bf8ff9669] - Node f995139d-a384-44b5-8220-916bf8ff9669[f995139d-a384-44b5-8220-916bf8ff9669] close complete, cost 13 ms 
[INFO ] 2024-03-28 14:51:06.358 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 18 ms 
[INFO ] 2024-03-28 14:51:06.358 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-f995139d-a384-44b5-8220-916bf8ff9669 complete, cost 2786ms 
[INFO ] 2024-03-28 14:51:06.767 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 14:51:06.773 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 14:51:06.773 - [orders(100)][0d500337-d7ee-4e9a-9892-0a0a09cf27fb] - Node 0d500337-d7ee-4e9a-9892-0a0a09cf27fb[0d500337-d7ee-4e9a-9892-0a0a09cf27fb] running status set to false 
[INFO ] 2024-03-28 14:51:06.773 - [orders(100)][0d500337-d7ee-4e9a-9892-0a0a09cf27fb] - Node 0d500337-d7ee-4e9a-9892-0a0a09cf27fb[0d500337-d7ee-4e9a-9892-0a0a09cf27fb] schema data cleaned 
[INFO ] 2024-03-28 14:51:06.774 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 14:51:06.774 - [orders(100)][0d500337-d7ee-4e9a-9892-0a0a09cf27fb] - Node 0d500337-d7ee-4e9a-9892-0a0a09cf27fb[0d500337-d7ee-4e9a-9892-0a0a09cf27fb] monitor closed 
[INFO ] 2024-03-28 14:51:06.775 - [orders(100)][0d500337-d7ee-4e9a-9892-0a0a09cf27fb] - Node 0d500337-d7ee-4e9a-9892-0a0a09cf27fb[0d500337-d7ee-4e9a-9892-0a0a09cf27fb] close complete, cost 7 ms 
[INFO ] 2024-03-28 14:51:06.775 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 11 ms 
[INFO ] 2024-03-28 14:51:06.865 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-0d500337-d7ee-4e9a-9892-0a0a09cf27fb complete, cost 2645ms 
[INFO ] 2024-03-28 14:51:06.865 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 14:51:06.865 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 14:51:06.865 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 14:51:06.866 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 1 ms 
[INFO ] 2024-03-28 14:51:06.866 - [orders(100)][4509a762-6380-47ff-a830-43b61e1a0269] - Node 4509a762-6380-47ff-a830-43b61e1a0269[4509a762-6380-47ff-a830-43b61e1a0269] running status set to false 
[INFO ] 2024-03-28 14:51:06.866 - [orders(100)][4509a762-6380-47ff-a830-43b61e1a0269] - Node 4509a762-6380-47ff-a830-43b61e1a0269[4509a762-6380-47ff-a830-43b61e1a0269] schema data cleaned 
[INFO ] 2024-03-28 14:51:06.867 - [orders(100)][4509a762-6380-47ff-a830-43b61e1a0269] - Node 4509a762-6380-47ff-a830-43b61e1a0269[4509a762-6380-47ff-a830-43b61e1a0269] monitor closed 
[INFO ] 2024-03-28 14:51:06.867 - [orders(100)][4509a762-6380-47ff-a830-43b61e1a0269] - Node 4509a762-6380-47ff-a830-43b61e1a0269[4509a762-6380-47ff-a830-43b61e1a0269] close complete, cost 0 ms 
[INFO ] 2024-03-28 14:51:07.070 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-4509a762-6380-47ff-a830-43b61e1a0269 complete, cost 2614ms 
[INFO ] 2024-03-28 14:51:08.768 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:08.769 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:08.769 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:08.769 - [orders(100)][a5238c63-ffce-416e-a6c4-42778080eac0] - Node a5238c63-ffce-416e-a6c4-42778080eac0[a5238c63-ffce-416e-a6c4-42778080eac0] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:51:08.769 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:08.769 - [orders(100)][a5238c63-ffce-416e-a6c4-42778080eac0] - Node a5238c63-ffce-416e-a6c4-42778080eac0[a5238c63-ffce-416e-a6c4-42778080eac0] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:08.769 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 14:51:08.769 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:08.769 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 14:51:08.769 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 14:51:08.813 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 14:51:08.993 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6dbb7a1f error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6dbb7a1f error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6dbb7a1f error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 14:51:08.998 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:51:08.998 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 14:51:09.008 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 14:51:09.011 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 14:51:09.011 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:09.012 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:09.012 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 14:51:09.012 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 14:51:09.012 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 16 ms 
[INFO ] 2024-03-28 14:51:09.058 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 14:51:09.058 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 14:51:09.058 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 14:51:09.058 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 50 ms 
[INFO ] 2024-03-28 14:51:09.059 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 14:51:09.059 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 57 ms 
[INFO ] 2024-03-28 14:51:09.313 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:09.313 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:09.313 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:09.313 - [orders(100)][8b0c1fbf-ba04-4ef6-88be-eae4bfce2036] - Node 8b0c1fbf-ba04-4ef6-88be-eae4bfce2036[8b0c1fbf-ba04-4ef6-88be-eae4bfce2036] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:51:09.313 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:09.314 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:09.314 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:09.314 - [orders(100)][8b0c1fbf-ba04-4ef6-88be-eae4bfce2036] - Node 8b0c1fbf-ba04-4ef6-88be-eae4bfce2036[8b0c1fbf-ba04-4ef6-88be-eae4bfce2036] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:09.314 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:09.314 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:09.337 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 14:51:09.337 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@17cce056 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@17cce056 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@17cce056 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 14:51:09.499 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:51:09.499 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 14:51:09.510 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 14:51:09.510 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 14:51:09.513 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:09.513 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:09.513 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 14:51:09.513 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 14:51:09.586 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 15 ms 
[INFO ] 2024-03-28 14:51:09.587 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 14:51:09.587 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 14:51:09.587 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 14:51:09.587 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 14:51:09.587 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 78 ms 
[INFO ] 2024-03-28 14:51:09.587 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 85 ms 
[INFO ] 2024-03-28 14:51:10.283 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:10.286 - [orders(100)][6969be6b-eb51-45f3-a8bd-0775edad068c] - Node 6969be6b-eb51-45f3-a8bd-0775edad068c[6969be6b-eb51-45f3-a8bd-0775edad068c] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:51:10.286 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:10.286 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:10.286 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:10.286 - [orders(100)][6969be6b-eb51-45f3-a8bd-0775edad068c] - Node 6969be6b-eb51-45f3-a8bd-0775edad068c[6969be6b-eb51-45f3-a8bd-0775edad068c] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:10.286 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:10.286 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:10.286 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:10.286 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:10.339 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 14:51:10.541 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@dbfa4d6 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@dbfa4d6 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@dbfa4d6 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 14:51:10.548 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:51:10.548 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 14:51:10.564 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 14:51:10.564 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:10.565 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:10.565 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 14:51:10.565 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 14:51:10.566 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 14:51:10.567 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 18 ms 
[INFO ] 2024-03-28 14:51:10.661 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 14:51:10.662 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 14:51:10.662 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 14:51:10.662 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 14:51:10.662 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 103 ms 
[INFO ] 2024-03-28 14:51:10.662 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 97 ms 
[INFO ] 2024-03-28 14:51:11.372 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 14:51:11.372 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 14:51:11.372 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 14:51:11.372 - [orders(100)][a5238c63-ffce-416e-a6c4-42778080eac0] - Node a5238c63-ffce-416e-a6c4-42778080eac0[a5238c63-ffce-416e-a6c4-42778080eac0] running status set to false 
[INFO ] 2024-03-28 14:51:11.374 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 3 ms 
[INFO ] 2024-03-28 14:51:11.374 - [orders(100)][a5238c63-ffce-416e-a6c4-42778080eac0] - Node a5238c63-ffce-416e-a6c4-42778080eac0[a5238c63-ffce-416e-a6c4-42778080eac0] schema data cleaned 
[INFO ] 2024-03-28 14:51:11.374 - [orders(100)][a5238c63-ffce-416e-a6c4-42778080eac0] - Node a5238c63-ffce-416e-a6c4-42778080eac0[a5238c63-ffce-416e-a6c4-42778080eac0] monitor closed 
[INFO ] 2024-03-28 14:51:11.375 - [orders(100)][a5238c63-ffce-416e-a6c4-42778080eac0] - Node a5238c63-ffce-416e-a6c4-42778080eac0[a5238c63-ffce-416e-a6c4-42778080eac0] close complete, cost 2 ms 
[INFO ] 2024-03-28 14:51:11.378 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-a5238c63-ffce-416e-a6c4-42778080eac0 complete, cost 2681ms 
[INFO ] 2024-03-28 14:51:11.877 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 14:51:11.879 - [orders(100)][8b0c1fbf-ba04-4ef6-88be-eae4bfce2036] - Node 8b0c1fbf-ba04-4ef6-88be-eae4bfce2036[8b0c1fbf-ba04-4ef6-88be-eae4bfce2036] running status set to false 
[INFO ] 2024-03-28 14:51:11.879 - [orders(100)][8b0c1fbf-ba04-4ef6-88be-eae4bfce2036] - Node 8b0c1fbf-ba04-4ef6-88be-eae4bfce2036[8b0c1fbf-ba04-4ef6-88be-eae4bfce2036] schema data cleaned 
[INFO ] 2024-03-28 14:51:11.879 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 14:51:11.880 - [orders(100)][8b0c1fbf-ba04-4ef6-88be-eae4bfce2036] - Node 8b0c1fbf-ba04-4ef6-88be-eae4bfce2036[8b0c1fbf-ba04-4ef6-88be-eae4bfce2036] monitor closed 
[INFO ] 2024-03-28 14:51:11.880 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 14:51:11.884 - [orders(100)][8b0c1fbf-ba04-4ef6-88be-eae4bfce2036] - Node 8b0c1fbf-ba04-4ef6-88be-eae4bfce2036[8b0c1fbf-ba04-4ef6-88be-eae4bfce2036] close complete, cost 8 ms 
[INFO ] 2024-03-28 14:51:11.905 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 13 ms 
[INFO ] 2024-03-28 14:51:11.906 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-8b0c1fbf-ba04-4ef6-88be-eae4bfce2036 complete, cost 2624ms 
[INFO ] 2024-03-28 14:51:12.616 - [orders(100)][a4c3ee28-bc93-4f5a-a6cb-0c9b9521e712] - Node a4c3ee28-bc93-4f5a-a6cb-0c9b9521e712[a4c3ee28-bc93-4f5a-a6cb-0c9b9521e712] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:51:12.616 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:12.616 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:12.616 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:12.617 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:12.617 - [orders(100)][a4c3ee28-bc93-4f5a-a6cb-0c9b9521e712] - Node a4c3ee28-bc93-4f5a-a6cb-0c9b9521e712[a4c3ee28-bc93-4f5a-a6cb-0c9b9521e712] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:12.617 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 14:51:12.617 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 14:51:12.617 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 14:51:12.617 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:12.687 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 14:51:12.710 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:12.710 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:12.710 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:12.711 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:12.711 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:12.711 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:12.711 - [orders(100)][a425d389-9989-4bbd-8413-bd96521487dc] - Node a425d389-9989-4bbd-8413-bd96521487dc[a425d389-9989-4bbd-8413-bd96521487dc] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:51:12.712 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:12.712 - [orders(100)][a425d389-9989-4bbd-8413-bd96521487dc] - Node a425d389-9989-4bbd-8413-bd96521487dc[a425d389-9989-4bbd-8413-bd96521487dc] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:12.712 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[ERROR] 2024-03-28 14:51:12.713 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@af2b7d6 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@af2b7d6 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@af2b7d6 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 14:51:12.757 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 14:51:12.757 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:12.757 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:12.757 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:12.757 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:12.766 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:12.766 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:51:12.774 - [orders(100)][6d1a3b66-2ec2-4a30-94e6-8b59bbb8cbeb] - Node 6d1a3b66-2ec2-4a30-94e6-8b59bbb8cbeb[6d1a3b66-2ec2-4a30-94e6-8b59bbb8cbeb] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:51:12.775 - [orders(100)][6d1a3b66-2ec2-4a30-94e6-8b59bbb8cbeb] - Node 6d1a3b66-2ec2-4a30-94e6-8b59bbb8cbeb[6d1a3b66-2ec2-4a30-94e6-8b59bbb8cbeb] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:12.776 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[ERROR] 2024-03-28 14:51:12.776 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@204c066b error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@204c066b error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@204c066b error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 14:51:12.776 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:51:12.809 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 14:51:12.809 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3918fdad error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3918fdad error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@3918fdad error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 14:51:12.880 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:51:12.881 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 14:51:12.901 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 14:51:12.902 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:12.906 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 14:51:12.910 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:12.910 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 14:51:12.910 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 14:51:12.910 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 22 ms 
[INFO ] 2024-03-28 14:51:12.927 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 14:51:12.930 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 14:51:12.930 - [orders(100)][6969be6b-eb51-45f3-a8bd-0775edad068c] - Node 6969be6b-eb51-45f3-a8bd-0775edad068c[6969be6b-eb51-45f3-a8bd-0775edad068c] running status set to false 
[INFO ] 2024-03-28 14:51:12.930 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 14:51:12.930 - [orders(100)][6969be6b-eb51-45f3-a8bd-0775edad068c] - Node 6969be6b-eb51-45f3-a8bd-0775edad068c[6969be6b-eb51-45f3-a8bd-0775edad068c] schema data cleaned 
[INFO ] 2024-03-28 14:51:12.930 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 2 ms 
[INFO ] 2024-03-28 14:51:12.930 - [orders(100)][6969be6b-eb51-45f3-a8bd-0775edad068c] - Node 6969be6b-eb51-45f3-a8bd-0775edad068c[6969be6b-eb51-45f3-a8bd-0775edad068c] monitor closed 
[INFO ] 2024-03-28 14:51:12.934 - [orders(100)][6969be6b-eb51-45f3-a8bd-0775edad068c] - Node 6969be6b-eb51-45f3-a8bd-0775edad068c[6969be6b-eb51-45f3-a8bd-0775edad068c] close complete, cost 0 ms 
[INFO ] 2024-03-28 14:51:12.934 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-6969be6b-eb51-45f3-a8bd-0775edad068c complete, cost 2754ms 
[INFO ] 2024-03-28 14:51:13.007 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 14:51:13.010 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 14:51:13.010 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 14:51:13.010 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 14:51:13.010 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 105 ms 
[INFO ] 2024-03-28 14:51:13.010 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 113 ms 
[WARN ] 2024-03-28 14:51:13.085 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:51:13.086 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 14:51:13.086 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 14:51:13.086 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 14:51:13.109 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:13.110 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:13.110 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 14:51:13.110 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 14:51:13.111 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 38 ms 
[INFO ] 2024-03-28 14:51:13.176 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 14:51:13.178 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 14:51:13.178 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 14:51:13.178 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 14:51:13.178 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 98 ms 
[INFO ] 2024-03-28 14:51:13.178 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 94 ms 
[WARN ] 2024-03-28 14:51:13.231 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:51:13.243 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 14:51:13.244 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 14:51:13.244 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:13.244 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:51:13.244 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 14:51:13.244 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 14:51:13.244 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 12 ms 
[INFO ] 2024-03-28 14:51:13.300 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 14:51:13.300 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 14:51:13.300 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 14:51:13.300 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 14:51:13.300 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 14:51:13.300 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 59 ms 
[INFO ] 2024-03-28 14:51:13.300 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 54 ms 
[INFO ] 2024-03-28 14:51:15.254 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 14:51:15.255 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 14:51:15.255 - [orders(100)][a4c3ee28-bc93-4f5a-a6cb-0c9b9521e712] - Node a4c3ee28-bc93-4f5a-a6cb-0c9b9521e712[a4c3ee28-bc93-4f5a-a6cb-0c9b9521e712] running status set to false 
[INFO ] 2024-03-28 14:51:15.255 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 14:51:15.255 - [orders(100)][a4c3ee28-bc93-4f5a-a6cb-0c9b9521e712] - Node a4c3ee28-bc93-4f5a-a6cb-0c9b9521e712[a4c3ee28-bc93-4f5a-a6cb-0c9b9521e712] schema data cleaned 
[INFO ] 2024-03-28 14:51:15.255 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 3 ms 
[INFO ] 2024-03-28 14:51:15.255 - [orders(100)][a4c3ee28-bc93-4f5a-a6cb-0c9b9521e712] - Node a4c3ee28-bc93-4f5a-a6cb-0c9b9521e712[a4c3ee28-bc93-4f5a-a6cb-0c9b9521e712] monitor closed 
[INFO ] 2024-03-28 14:51:15.255 - [orders(100)][a4c3ee28-bc93-4f5a-a6cb-0c9b9521e712] - Node a4c3ee28-bc93-4f5a-a6cb-0c9b9521e712[a4c3ee28-bc93-4f5a-a6cb-0c9b9521e712] close complete, cost 2 ms 
[INFO ] 2024-03-28 14:51:15.301 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-a4c3ee28-bc93-4f5a-a6cb-0c9b9521e712 complete, cost 2706ms 
[INFO ] 2024-03-28 14:51:15.301 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 14:51:15.301 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 14:51:15.301 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 14:51:15.301 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 1 ms 
[INFO ] 2024-03-28 14:51:15.304 - [orders(100)][a425d389-9989-4bbd-8413-bd96521487dc] - Node a425d389-9989-4bbd-8413-bd96521487dc[a425d389-9989-4bbd-8413-bd96521487dc] running status set to false 
[INFO ] 2024-03-28 14:51:15.304 - [orders(100)][a425d389-9989-4bbd-8413-bd96521487dc] - Node a425d389-9989-4bbd-8413-bd96521487dc[a425d389-9989-4bbd-8413-bd96521487dc] schema data cleaned 
[INFO ] 2024-03-28 14:51:15.304 - [orders(100)][a425d389-9989-4bbd-8413-bd96521487dc] - Node a425d389-9989-4bbd-8413-bd96521487dc[a425d389-9989-4bbd-8413-bd96521487dc] monitor closed 
[INFO ] 2024-03-28 14:51:15.306 - [orders(100)][a425d389-9989-4bbd-8413-bd96521487dc] - Node a425d389-9989-4bbd-8413-bd96521487dc[a425d389-9989-4bbd-8413-bd96521487dc] close complete, cost 0 ms 
[INFO ] 2024-03-28 14:51:15.306 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-a425d389-9989-4bbd-8413-bd96521487dc complete, cost 2639ms 
[INFO ] 2024-03-28 14:51:15.325 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 14:51:15.325 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 14:51:15.325 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 14:51:15.325 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 0 ms 
[INFO ] 2024-03-28 14:51:15.329 - [orders(100)][6d1a3b66-2ec2-4a30-94e6-8b59bbb8cbeb] - Node 6d1a3b66-2ec2-4a30-94e6-8b59bbb8cbeb[6d1a3b66-2ec2-4a30-94e6-8b59bbb8cbeb] running status set to false 
[INFO ] 2024-03-28 14:51:15.329 - [orders(100)][6d1a3b66-2ec2-4a30-94e6-8b59bbb8cbeb] - Node 6d1a3b66-2ec2-4a30-94e6-8b59bbb8cbeb[6d1a3b66-2ec2-4a30-94e6-8b59bbb8cbeb] schema data cleaned 
[INFO ] 2024-03-28 14:51:15.329 - [orders(100)][6d1a3b66-2ec2-4a30-94e6-8b59bbb8cbeb] - Node 6d1a3b66-2ec2-4a30-94e6-8b59bbb8cbeb[6d1a3b66-2ec2-4a30-94e6-8b59bbb8cbeb] monitor closed 
[INFO ] 2024-03-28 14:51:15.330 - [orders(100)][6d1a3b66-2ec2-4a30-94e6-8b59bbb8cbeb] - Node 6d1a3b66-2ec2-4a30-94e6-8b59bbb8cbeb[6d1a3b66-2ec2-4a30-94e6-8b59bbb8cbeb] close complete, cost 0 ms 
[INFO ] 2024-03-28 14:51:15.330 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-6d1a3b66-2ec2-4a30-94e6-8b59bbb8cbeb complete, cost 2610ms 
[INFO ] 2024-03-28 14:57:43.758 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:57:43.758 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:57:43.798 - [orders(100)][76d1a3f1-02e9-4a71-8a61-47e5f7f7d46f] - Node 76d1a3f1-02e9-4a71-8a61-47e5f7f7d46f[76d1a3f1-02e9-4a71-8a61-47e5f7f7d46f] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:57:43.798 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:57:43.798 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:57:43.798 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:57:43.798 - [orders(100)][76d1a3f1-02e9-4a71-8a61-47e5f7f7d46f] - Node 76d1a3f1-02e9-4a71-8a61-47e5f7f7d46f[76d1a3f1-02e9-4a71-8a61-47e5f7f7d46f] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:57:43.798 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 3 ms 
[INFO ] 2024-03-28 14:57:43.798 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:57:43.798 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:57:43.858 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 14:57:43.883 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@246550e5 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@246550e5 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@246550e5 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 14:57:44.038 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:57:44.061 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 14:57:44.061 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 14:57:44.061 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 14:57:44.061 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:57:44.062 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:57:44.062 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 14:57:44.062 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 14:57:44.062 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 22 ms 
[INFO ] 2024-03-28 14:57:44.130 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 14:57:44.130 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 14:57:44.130 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 14:57:44.130 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 14:57:44.130 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 89 ms 
[INFO ] 2024-03-28 14:57:44.130 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 74 ms 
[INFO ] 2024-03-28 14:57:46.438 - [orders(100)][76d1a3f1-02e9-4a71-8a61-47e5f7f7d46f] - Node 76d1a3f1-02e9-4a71-8a61-47e5f7f7d46f[76d1a3f1-02e9-4a71-8a61-47e5f7f7d46f] running status set to false 
[INFO ] 2024-03-28 14:57:46.439 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 14:57:46.439 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 14:57:46.439 - [orders(100)][76d1a3f1-02e9-4a71-8a61-47e5f7f7d46f] - Node 76d1a3f1-02e9-4a71-8a61-47e5f7f7d46f[76d1a3f1-02e9-4a71-8a61-47e5f7f7d46f] schema data cleaned 
[INFO ] 2024-03-28 14:57:46.439 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 14:57:46.439 - [orders(100)][76d1a3f1-02e9-4a71-8a61-47e5f7f7d46f] - Node 76d1a3f1-02e9-4a71-8a61-47e5f7f7d46f[76d1a3f1-02e9-4a71-8a61-47e5f7f7d46f] monitor closed 
[INFO ] 2024-03-28 14:57:46.442 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 17 ms 
[INFO ] 2024-03-28 14:57:46.449 - [orders(100)][76d1a3f1-02e9-4a71-8a61-47e5f7f7d46f] - Node 76d1a3f1-02e9-4a71-8a61-47e5f7f7d46f[76d1a3f1-02e9-4a71-8a61-47e5f7f7d46f] close complete, cost 17 ms 
[INFO ] 2024-03-28 14:57:46.656 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-76d1a3f1-02e9-4a71-8a61-47e5f7f7d46f complete, cost 2769ms 
[INFO ] 2024-03-28 14:57:49.525 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:57:49.525 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:57:49.530 - [orders(100)][318bbe1f-e5df-4862-bfc2-d604ef08105b] - Node 318bbe1f-e5df-4862-bfc2-d604ef08105b[318bbe1f-e5df-4862-bfc2-d604ef08105b] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:57:49.531 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:57:49.531 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:57:49.532 - [orders(100)][318bbe1f-e5df-4862-bfc2-d604ef08105b] - Node 318bbe1f-e5df-4862-bfc2-d604ef08105b[318bbe1f-e5df-4862-bfc2-d604ef08105b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:57:49.532 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:57:49.532 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:57:49.532 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:57:49.532 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:57:49.595 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:57:49.595 - [orders(100)][1d054032-5412-4f59-8fe8-deac04905cb5] - Node 1d054032-5412-4f59-8fe8-deac04905cb5[1d054032-5412-4f59-8fe8-deac04905cb5] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:57:49.595 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:57:49.602 - [orders(100)][1d054032-5412-4f59-8fe8-deac04905cb5] - Node 1d054032-5412-4f59-8fe8-deac04905cb5[1d054032-5412-4f59-8fe8-deac04905cb5] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:57:49.602 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:57:49.602 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:57:49.602 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:57:49.602 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:57:49.602 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:57:49.602 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:57:49.641 - [orders(100)][Order Details] - Init standardized JS engine... 
[INFO ] 2024-03-28 14:57:49.641 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 14:57:49.668 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@55369aa5 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@55369aa5 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@55369aa5 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[ERROR] 2024-03-28 14:57:49.668 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@621c31c8 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@621c31c8 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@621c31c8 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 14:57:49.726 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:57:49.727 - [orders(100)][4e764cc2-5b4b-4454-998d-58c00ec2bdbd] - Node 4e764cc2-5b4b-4454-998d-58c00ec2bdbd[4e764cc2-5b4b-4454-998d-58c00ec2bdbd] start preload schema,table counts: 0 
[INFO ] 2024-03-28 14:57:49.727 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:57:49.727 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:57:49.727 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 14:57:49.727 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:57:49.727 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:57:49.727 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:57:49.727 - [orders(100)][4e764cc2-5b4b-4454-998d-58c00ec2bdbd] - Node 4e764cc2-5b4b-4454-998d-58c00ec2bdbd[4e764cc2-5b4b-4454-998d-58c00ec2bdbd] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:57:49.727 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 14:57:49.742 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 14:57:49.818 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7b21f5bc error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7b21f5bc error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7b21f5bc error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 14:57:49.818 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:57:49.826 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 14:57:49.826 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 14:57:49.826 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 14:57:49.849 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:57:49.849 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:57:49.849 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 14:57:49.849 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 14:57:49.915 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 28 ms 
[INFO ] 2024-03-28 14:57:49.915 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 14:57:49.915 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 14:57:49.915 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 14:57:49.915 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 14:57:49.915 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 92 ms 
[INFO ] 2024-03-28 14:57:49.915 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 89 ms 
[WARN ] 2024-03-28 14:57:49.977 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:57:49.977 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 14:57:49.992 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 14:57:49.992 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:57:49.992 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:57:49.993 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 14:57:49.993 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 14:57:49.994 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 17 ms 
[INFO ] 2024-03-28 14:57:49.994 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 14:57:50.083 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 14:57:50.084 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 14:57:50.084 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 14:57:50.084 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 14:57:50.084 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 104 ms 
[INFO ] 2024-03-28 14:57:50.084 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 90 ms 
[WARN ] 2024-03-28 14:57:50.163 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 14:57:50.163 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 14:57:50.165 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 14:57:50.165 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 14:57:50.185 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:57:50.185 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 14:57:50.186 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 14:57:50.186 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 14:57:50.186 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 27 ms 
[INFO ] 2024-03-28 14:57:50.269 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 14:57:50.270 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 14:57:50.270 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 14:57:50.277 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 14:57:50.278 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 105 ms 
[INFO ] 2024-03-28 14:57:50.278 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 106 ms 
[INFO ] 2024-03-28 14:57:52.214 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 14:57:52.215 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 14:57:52.215 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 14:57:52.215 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 14:57:52.222 - [orders(100)][318bbe1f-e5df-4862-bfc2-d604ef08105b] - Node 318bbe1f-e5df-4862-bfc2-d604ef08105b[318bbe1f-e5df-4862-bfc2-d604ef08105b] running status set to false 
[INFO ] 2024-03-28 14:57:52.223 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 14:57:52.223 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 14:57:52.223 - [orders(100)][318bbe1f-e5df-4862-bfc2-d604ef08105b] - Node 318bbe1f-e5df-4862-bfc2-d604ef08105b[318bbe1f-e5df-4862-bfc2-d604ef08105b] schema data cleaned 
[INFO ] 2024-03-28 14:57:52.223 - [orders(100)][1d054032-5412-4f59-8fe8-deac04905cb5] - Node 1d054032-5412-4f59-8fe8-deac04905cb5[1d054032-5412-4f59-8fe8-deac04905cb5] running status set to false 
[INFO ] 2024-03-28 14:57:52.223 - [orders(100)][318bbe1f-e5df-4862-bfc2-d604ef08105b] - Node 318bbe1f-e5df-4862-bfc2-d604ef08105b[318bbe1f-e5df-4862-bfc2-d604ef08105b] monitor closed 
[INFO ] 2024-03-28 14:57:52.223 - [orders(100)][1d054032-5412-4f59-8fe8-deac04905cb5] - Node 1d054032-5412-4f59-8fe8-deac04905cb5[1d054032-5412-4f59-8fe8-deac04905cb5] schema data cleaned 
[INFO ] 2024-03-28 14:57:52.223 - [orders(100)][1d054032-5412-4f59-8fe8-deac04905cb5] - Node 1d054032-5412-4f59-8fe8-deac04905cb5[1d054032-5412-4f59-8fe8-deac04905cb5] monitor closed 
[INFO ] 2024-03-28 14:57:52.225 - [orders(100)][318bbe1f-e5df-4862-bfc2-d604ef08105b] - Node 318bbe1f-e5df-4862-bfc2-d604ef08105b[318bbe1f-e5df-4862-bfc2-d604ef08105b] close complete, cost 7 ms 
[INFO ] 2024-03-28 14:57:52.225 - [orders(100)][1d054032-5412-4f59-8fe8-deac04905cb5] - Node 1d054032-5412-4f59-8fe8-deac04905cb5[1d054032-5412-4f59-8fe8-deac04905cb5] close complete, cost 10 ms 
[INFO ] 2024-03-28 14:57:52.225 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 11 ms 
[INFO ] 2024-03-28 14:57:52.225 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 11 ms 
[INFO ] 2024-03-28 14:57:52.228 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-318bbe1f-e5df-4862-bfc2-d604ef08105b complete, cost 2788ms 
[INFO ] 2024-03-28 14:57:52.229 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-1d054032-5412-4f59-8fe8-deac04905cb5 complete, cost 2685ms 
[INFO ] 2024-03-28 14:57:52.301 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 14:57:52.301 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 14:57:52.301 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 14:57:52.301 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 0 ms 
[INFO ] 2024-03-28 14:57:52.303 - [orders(100)][4e764cc2-5b4b-4454-998d-58c00ec2bdbd] - Node 4e764cc2-5b4b-4454-998d-58c00ec2bdbd[4e764cc2-5b4b-4454-998d-58c00ec2bdbd] running status set to false 
[INFO ] 2024-03-28 14:57:52.303 - [orders(100)][4e764cc2-5b4b-4454-998d-58c00ec2bdbd] - Node 4e764cc2-5b4b-4454-998d-58c00ec2bdbd[4e764cc2-5b4b-4454-998d-58c00ec2bdbd] schema data cleaned 
[INFO ] 2024-03-28 14:57:52.303 - [orders(100)][4e764cc2-5b4b-4454-998d-58c00ec2bdbd] - Node 4e764cc2-5b4b-4454-998d-58c00ec2bdbd[4e764cc2-5b4b-4454-998d-58c00ec2bdbd] monitor closed 
[INFO ] 2024-03-28 14:57:52.303 - [orders(100)][4e764cc2-5b4b-4454-998d-58c00ec2bdbd] - Node 4e764cc2-5b4b-4454-998d-58c00ec2bdbd[4e764cc2-5b4b-4454-998d-58c00ec2bdbd] close complete, cost 0 ms 
[INFO ] 2024-03-28 14:57:52.303 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-4e764cc2-5b4b-4454-998d-58c00ec2bdbd complete, cost 2632ms 
[INFO ] 2024-03-28 15:01:27.833 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:27.836 - [orders(100)][70c12249-3e42-4156-94c1-270b7e47437b] - Node 70c12249-3e42-4156-94c1-270b7e47437b[70c12249-3e42-4156-94c1-270b7e47437b] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:01:27.836 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:27.836 - [orders(100)][70c12249-3e42-4156-94c1-270b7e47437b] - Node 70c12249-3e42-4156-94c1-270b7e47437b[70c12249-3e42-4156-94c1-270b7e47437b] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:27.836 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:27.836 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:27.836 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 3 ms 
[INFO ] 2024-03-28 15:01:27.845 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 15:01:27.846 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:27.846 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:27.910 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:01:28.078 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2171b2d0 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2171b2d0 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2171b2d0 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:01:28.078 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:01:28.099 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:01:28.101 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:01:28.103 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:01:28.103 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:01:28.103 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:01:28.103 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:01:28.103 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:01:28.104 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 23 ms 
[INFO ] 2024-03-28 15:01:28.164 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:01:28.164 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:01:28.164 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:01:28.164 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:01:28.164 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 69 ms 
[INFO ] 2024-03-28 15:01:28.164 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 61 ms 
[INFO ] 2024-03-28 15:01:30.465 - [orders(100)][70c12249-3e42-4156-94c1-270b7e47437b] - Node 70c12249-3e42-4156-94c1-270b7e47437b[70c12249-3e42-4156-94c1-270b7e47437b] running status set to false 
[INFO ] 2024-03-28 15:01:30.466 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:01:30.466 - [orders(100)][70c12249-3e42-4156-94c1-270b7e47437b] - Node 70c12249-3e42-4156-94c1-270b7e47437b[70c12249-3e42-4156-94c1-270b7e47437b] schema data cleaned 
[INFO ] 2024-03-28 15:01:30.466 - [orders(100)][70c12249-3e42-4156-94c1-270b7e47437b] - Node 70c12249-3e42-4156-94c1-270b7e47437b[70c12249-3e42-4156-94c1-270b7e47437b] monitor closed 
[INFO ] 2024-03-28 15:01:30.466 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:01:30.466 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:01:30.466 - [orders(100)][70c12249-3e42-4156-94c1-270b7e47437b] - Node 70c12249-3e42-4156-94c1-270b7e47437b[70c12249-3e42-4156-94c1-270b7e47437b] close complete, cost 3 ms 
[INFO ] 2024-03-28 15:01:30.466 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 4 ms 
[INFO ] 2024-03-28 15:01:30.671 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-70c12249-3e42-4156-94c1-270b7e47437b complete, cost 2720ms 
[INFO ] 2024-03-28 15:01:34.328 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:34.328 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:34.328 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:34.328 - [orders(100)][9386c800-7599-4299-9a79-ace990a2342a] - Node 9386c800-7599-4299-9a79-ace990a2342a[9386c800-7599-4299-9a79-ace990a2342a] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:01:34.328 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:34.329 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:34.329 - [orders(100)][9386c800-7599-4299-9a79-ace990a2342a] - Node 9386c800-7599-4299-9a79-ace990a2342a[9386c800-7599-4299-9a79-ace990a2342a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:34.329 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 15:01:34.329 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:34.329 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 15:01:34.394 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:01:34.580 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5effe9d5 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5effe9d5 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5effe9d5 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:01:34.580 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:01:34.587 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:01:34.587 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:01:34.602 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:01:34.603 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:01:34.603 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:01:34.603 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:01:34.603 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:01:34.604 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 20 ms 
[INFO ] 2024-03-28 15:01:34.658 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:01:34.658 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:01:34.658 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:01:34.658 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:01:34.658 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 60 ms 
[INFO ] 2024-03-28 15:01:34.658 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 72 ms 
[INFO ] 2024-03-28 15:01:36.939 - [orders(100)][9386c800-7599-4299-9a79-ace990a2342a] - Node 9386c800-7599-4299-9a79-ace990a2342a[9386c800-7599-4299-9a79-ace990a2342a] running status set to false 
[INFO ] 2024-03-28 15:01:36.939 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:01:36.940 - [orders(100)][9386c800-7599-4299-9a79-ace990a2342a] - Node 9386c800-7599-4299-9a79-ace990a2342a[9386c800-7599-4299-9a79-ace990a2342a] schema data cleaned 
[INFO ] 2024-03-28 15:01:36.940 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:01:36.940 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:01:36.940 - [orders(100)][9386c800-7599-4299-9a79-ace990a2342a] - Node 9386c800-7599-4299-9a79-ace990a2342a[9386c800-7599-4299-9a79-ace990a2342a] monitor closed 
[INFO ] 2024-03-28 15:01:36.940 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 3 ms 
[INFO ] 2024-03-28 15:01:36.940 - [orders(100)][9386c800-7599-4299-9a79-ace990a2342a] - Node 9386c800-7599-4299-9a79-ace990a2342a[9386c800-7599-4299-9a79-ace990a2342a] close complete, cost 3 ms 
[INFO ] 2024-03-28 15:01:36.942 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-9386c800-7599-4299-9a79-ace990a2342a complete, cost 2685ms 
[INFO ] 2024-03-28 15:01:38.853 - [orders(100)][9a1bba35-b6e6-479c-94d7-eae1e3b08af1] - Node 9a1bba35-b6e6-479c-94d7-eae1e3b08af1[9a1bba35-b6e6-479c-94d7-eae1e3b08af1] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:01:38.854 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:38.854 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:38.854 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:38.854 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:38.854 - [orders(100)][9a1bba35-b6e6-479c-94d7-eae1e3b08af1] - Node 9a1bba35-b6e6-479c-94d7-eae1e3b08af1[9a1bba35-b6e6-479c-94d7-eae1e3b08af1] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:38.854 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:38.854 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:38.854 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:38.854 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:38.862 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:01:38.942 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5ce55d73 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5ce55d73 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5ce55d73 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 15:01:38.943 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:38.943 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:38.943 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:38.943 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:38.943 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:38.943 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:38.943 - [orders(100)][e4c5dcec-069e-43e3-9577-d58aaa6420d9] - Node e4c5dcec-069e-43e3-9577-d58aaa6420d9[e4c5dcec-069e-43e3-9577-d58aaa6420d9] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:01:38.943 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:38.943 - [orders(100)][e4c5dcec-069e-43e3-9577-d58aaa6420d9] - Node e4c5dcec-069e-43e3-9577-d58aaa6420d9[e4c5dcec-069e-43e3-9577-d58aaa6420d9] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:38.943 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:38.950 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:01:39.036 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5424db1 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5424db1 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5424db1 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:01:39.044 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:01:39.044 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:01:39.044 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:01:39.044 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:01:39.060 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:01:39.060 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:01:39.061 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:01:39.061 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:01:39.122 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 22 ms 
[INFO ] 2024-03-28 15:01:39.122 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:01:39.122 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:01:39.123 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:01:39.123 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:01:39.123 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 80 ms 
[INFO ] 2024-03-28 15:01:39.123 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 78 ms 
[WARN ] 2024-03-28 15:01:39.228 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:01:39.232 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:01:39.234 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:01:39.236 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:01:39.236 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:01:39.237 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:01:39.237 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:01:39.237 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:01:39.237 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 19 ms 
[INFO ] 2024-03-28 15:01:39.302 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:01:39.303 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:01:39.303 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:01:39.303 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:01:39.303 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 67 ms 
[INFO ] 2024-03-28 15:01:39.303 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 80 ms 
[INFO ] 2024-03-28 15:01:41.453 - [orders(100)][9a1bba35-b6e6-479c-94d7-eae1e3b08af1] - Node 9a1bba35-b6e6-479c-94d7-eae1e3b08af1[9a1bba35-b6e6-479c-94d7-eae1e3b08af1] running status set to false 
[INFO ] 2024-03-28 15:01:41.457 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:01:41.457 - [orders(100)][9a1bba35-b6e6-479c-94d7-eae1e3b08af1] - Node 9a1bba35-b6e6-479c-94d7-eae1e3b08af1[9a1bba35-b6e6-479c-94d7-eae1e3b08af1] schema data cleaned 
[INFO ] 2024-03-28 15:01:41.457 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:01:41.457 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:01:41.458 - [orders(100)][9a1bba35-b6e6-479c-94d7-eae1e3b08af1] - Node 9a1bba35-b6e6-479c-94d7-eae1e3b08af1[9a1bba35-b6e6-479c-94d7-eae1e3b08af1] monitor closed 
[INFO ] 2024-03-28 15:01:41.458 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 30 ms 
[INFO ] 2024-03-28 15:01:41.460 - [orders(100)][9a1bba35-b6e6-479c-94d7-eae1e3b08af1] - Node 9a1bba35-b6e6-479c-94d7-eae1e3b08af1[9a1bba35-b6e6-479c-94d7-eae1e3b08af1] close complete, cost 29 ms 
[INFO ] 2024-03-28 15:01:41.461 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-9a1bba35-b6e6-479c-94d7-eae1e3b08af1 complete, cost 2656ms 
[INFO ] 2024-03-28 15:01:41.496 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:01:41.496 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:01:41.496 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:01:41.502 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 0 ms 
[INFO ] 2024-03-28 15:01:41.502 - [orders(100)][e4c5dcec-069e-43e3-9577-d58aaa6420d9] - Node e4c5dcec-069e-43e3-9577-d58aaa6420d9[e4c5dcec-069e-43e3-9577-d58aaa6420d9] running status set to false 
[INFO ] 2024-03-28 15:01:41.502 - [orders(100)][e4c5dcec-069e-43e3-9577-d58aaa6420d9] - Node e4c5dcec-069e-43e3-9577-d58aaa6420d9[e4c5dcec-069e-43e3-9577-d58aaa6420d9] schema data cleaned 
[INFO ] 2024-03-28 15:01:41.502 - [orders(100)][e4c5dcec-069e-43e3-9577-d58aaa6420d9] - Node e4c5dcec-069e-43e3-9577-d58aaa6420d9[e4c5dcec-069e-43e3-9577-d58aaa6420d9] monitor closed 
[INFO ] 2024-03-28 15:01:41.502 - [orders(100)][e4c5dcec-069e-43e3-9577-d58aaa6420d9] - Node e4c5dcec-069e-43e3-9577-d58aaa6420d9[e4c5dcec-069e-43e3-9577-d58aaa6420d9] close complete, cost 0 ms 
[INFO ] 2024-03-28 15:01:41.502 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-e4c5dcec-069e-43e3-9577-d58aaa6420d9 complete, cost 2591ms 
[INFO ] 2024-03-28 15:01:47.931 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:47.931 - [orders(100)][b9751d51-2933-4038-88d5-6849fbf90194] - Node b9751d51-2933-4038-88d5-6849fbf90194[b9751d51-2933-4038-88d5-6849fbf90194] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:01:47.931 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:47.931 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:47.932 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:47.932 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:47.932 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:47.932 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:47.932 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:47.932 - [orders(100)][b9751d51-2933-4038-88d5-6849fbf90194] - Node b9751d51-2933-4038-88d5-6849fbf90194[b9751d51-2933-4038-88d5-6849fbf90194] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:47.995 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:01:47.995 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2633ab96 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2633ab96 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2633ab96 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:01:48.150 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:01:48.150 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:01:48.160 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:01:48.160 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:01:48.161 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:01:48.161 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:01:48.161 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:01:48.161 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:01:48.161 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 12 ms 
[INFO ] 2024-03-28 15:01:48.247 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:01:48.248 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:01:48.248 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:01:48.248 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:01:48.248 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 89 ms 
[INFO ] 2024-03-28 15:01:48.248 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 85 ms 
[INFO ] 2024-03-28 15:01:50.534 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:01:50.535 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:01:50.536 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:01:50.536 - [orders(100)][b9751d51-2933-4038-88d5-6849fbf90194] - Node b9751d51-2933-4038-88d5-6849fbf90194[b9751d51-2933-4038-88d5-6849fbf90194] running status set to false 
[INFO ] 2024-03-28 15:01:50.537 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 6 ms 
[INFO ] 2024-03-28 15:01:50.537 - [orders(100)][b9751d51-2933-4038-88d5-6849fbf90194] - Node b9751d51-2933-4038-88d5-6849fbf90194[b9751d51-2933-4038-88d5-6849fbf90194] schema data cleaned 
[INFO ] 2024-03-28 15:01:50.537 - [orders(100)][b9751d51-2933-4038-88d5-6849fbf90194] - Node b9751d51-2933-4038-88d5-6849fbf90194[b9751d51-2933-4038-88d5-6849fbf90194] monitor closed 
[INFO ] 2024-03-28 15:01:50.538 - [orders(100)][b9751d51-2933-4038-88d5-6849fbf90194] - Node b9751d51-2933-4038-88d5-6849fbf90194[b9751d51-2933-4038-88d5-6849fbf90194] close complete, cost 2 ms 
[INFO ] 2024-03-28 15:01:50.744 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-b9751d51-2933-4038-88d5-6849fbf90194 complete, cost 2718ms 
[INFO ] 2024-03-28 15:01:53.545 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:53.546 - [orders(100)][c0effd25-b4b4-4eeb-b705-1acaa6d262ca] - Node c0effd25-b4b4-4eeb-b705-1acaa6d262ca[c0effd25-b4b4-4eeb-b705-1acaa6d262ca] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:01:53.546 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:53.546 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:53.547 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:01:53.547 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:53.547 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 15:01:53.547 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 15:01:53.547 - [orders(100)][c0effd25-b4b4-4eeb-b705-1acaa6d262ca] - Node c0effd25-b4b4-4eeb-b705-1acaa6d262ca[c0effd25-b4b4-4eeb-b705-1acaa6d262ca] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:01:53.547 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 15:01:53.632 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:01:53.634 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@14f80b10 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@14f80b10 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@14f80b10 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:01:53.799 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:01:53.799 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:01:53.813 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:01:53.813 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:01:53.813 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:01:53.813 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:01:53.813 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:01:53.813 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:01:53.860 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 15 ms 
[INFO ] 2024-03-28 15:01:53.861 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:01:53.861 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:01:53.861 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:01:53.861 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:01:53.861 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 51 ms 
[INFO ] 2024-03-28 15:01:53.861 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 59 ms 
[INFO ] 2024-03-28 15:01:56.171 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:01:56.173 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:01:56.174 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:01:56.174 - [orders(100)][c0effd25-b4b4-4eeb-b705-1acaa6d262ca] - Node c0effd25-b4b4-4eeb-b705-1acaa6d262ca[c0effd25-b4b4-4eeb-b705-1acaa6d262ca] running status set to false 
[INFO ] 2024-03-28 15:01:56.174 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 6 ms 
[INFO ] 2024-03-28 15:01:56.176 - [orders(100)][c0effd25-b4b4-4eeb-b705-1acaa6d262ca] - Node c0effd25-b4b4-4eeb-b705-1acaa6d262ca[c0effd25-b4b4-4eeb-b705-1acaa6d262ca] schema data cleaned 
[INFO ] 2024-03-28 15:01:56.177 - [orders(100)][c0effd25-b4b4-4eeb-b705-1acaa6d262ca] - Node c0effd25-b4b4-4eeb-b705-1acaa6d262ca[c0effd25-b4b4-4eeb-b705-1acaa6d262ca] monitor closed 
[INFO ] 2024-03-28 15:01:56.181 - [orders(100)][c0effd25-b4b4-4eeb-b705-1acaa6d262ca] - Node c0effd25-b4b4-4eeb-b705-1acaa6d262ca[c0effd25-b4b4-4eeb-b705-1acaa6d262ca] close complete, cost 4 ms 
[INFO ] 2024-03-28 15:01:56.181 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-c0effd25-b4b4-4eeb-b705-1acaa6d262ca complete, cost 2696ms 
[INFO ] 2024-03-28 15:02:06.543 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:02:06.544 - [orders(100)][33ea187e-df0d-4a41-8fd6-191320ebec2a] - Node 33ea187e-df0d-4a41-8fd6-191320ebec2a[33ea187e-df0d-4a41-8fd6-191320ebec2a] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:02:06.544 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:02:06.545 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:02:06.546 - [orders(100)][33ea187e-df0d-4a41-8fd6-191320ebec2a] - Node 33ea187e-df0d-4a41-8fd6-191320ebec2a[33ea187e-df0d-4a41-8fd6-191320ebec2a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:02:06.546 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:02:06.552 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 2 ms 
[INFO ] 2024-03-28 15:02:06.553 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 15:02:06.553 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:02:06.553 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:02:06.623 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:02:06.624 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6360753b error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6360753b error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6360753b error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:02:06.803 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:02:06.803 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:02:06.818 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:02:06.818 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:02:06.821 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:02:06.821 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:02:06.822 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:02:06.822 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:02:06.876 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 20 ms 
[INFO ] 2024-03-28 15:02:06.876 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:02:06.876 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:02:06.876 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:02:06.877 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:02:06.877 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 65 ms 
[INFO ] 2024-03-28 15:02:06.877 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 60 ms 
[INFO ] 2024-03-28 15:02:09.164 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:02:09.165 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:02:09.171 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:02:09.171 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 7 ms 
[INFO ] 2024-03-28 15:02:09.171 - [orders(100)][33ea187e-df0d-4a41-8fd6-191320ebec2a] - Node 33ea187e-df0d-4a41-8fd6-191320ebec2a[33ea187e-df0d-4a41-8fd6-191320ebec2a] running status set to false 
[INFO ] 2024-03-28 15:02:09.171 - [orders(100)][33ea187e-df0d-4a41-8fd6-191320ebec2a] - Node 33ea187e-df0d-4a41-8fd6-191320ebec2a[33ea187e-df0d-4a41-8fd6-191320ebec2a] schema data cleaned 
[INFO ] 2024-03-28 15:02:09.172 - [orders(100)][33ea187e-df0d-4a41-8fd6-191320ebec2a] - Node 33ea187e-df0d-4a41-8fd6-191320ebec2a[33ea187e-df0d-4a41-8fd6-191320ebec2a] monitor closed 
[INFO ] 2024-03-28 15:02:09.172 - [orders(100)][33ea187e-df0d-4a41-8fd6-191320ebec2a] - Node 33ea187e-df0d-4a41-8fd6-191320ebec2a[33ea187e-df0d-4a41-8fd6-191320ebec2a] close complete, cost 2 ms 
[INFO ] 2024-03-28 15:02:09.375 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-33ea187e-df0d-4a41-8fd6-191320ebec2a complete, cost 2740ms 
[INFO ] 2024-03-28 15:06:09.628 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:09.633 - [orders(100)][71797db5-c343-4930-bb32-fd53452bf727] - Node 71797db5-c343-4930-bb32-fd53452bf727[71797db5-c343-4930-bb32-fd53452bf727] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:06:09.633 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:09.633 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:09.633 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 15:06:09.633 - [orders(100)][71797db5-c343-4930-bb32-fd53452bf727] - Node 71797db5-c343-4930-bb32-fd53452bf727[71797db5-c343-4930-bb32-fd53452bf727] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:09.633 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:09.633 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:09.633 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:09.633 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:09.727 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:06:09.927 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5bbaf089 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5bbaf089 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@5bbaf089 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:06:09.927 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:06:09.955 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:06:09.955 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:06:09.963 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:06:09.964 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:09.964 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:09.964 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:06:09.964 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:06:09.965 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 33 ms 
[INFO ] 2024-03-28 15:06:10.029 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:06:10.029 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:06:10.029 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:06:10.029 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:06:10.029 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 76 ms 
[INFO ] 2024-03-28 15:06:10.029 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 72 ms 
[INFO ] 2024-03-28 15:06:12.285 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:06:12.286 - [orders(100)][71797db5-c343-4930-bb32-fd53452bf727] - Node 71797db5-c343-4930-bb32-fd53452bf727[71797db5-c343-4930-bb32-fd53452bf727] running status set to false 
[INFO ] 2024-03-28 15:06:12.286 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:06:12.287 - [orders(100)][71797db5-c343-4930-bb32-fd53452bf727] - Node 71797db5-c343-4930-bb32-fd53452bf727[71797db5-c343-4930-bb32-fd53452bf727] schema data cleaned 
[INFO ] 2024-03-28 15:06:12.287 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:06:12.287 - [orders(100)][71797db5-c343-4930-bb32-fd53452bf727] - Node 71797db5-c343-4930-bb32-fd53452bf727[71797db5-c343-4930-bb32-fd53452bf727] monitor closed 
[INFO ] 2024-03-28 15:06:12.287 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 4 ms 
[INFO ] 2024-03-28 15:06:12.287 - [orders(100)][71797db5-c343-4930-bb32-fd53452bf727] - Node 71797db5-c343-4930-bb32-fd53452bf727[71797db5-c343-4930-bb32-fd53452bf727] close complete, cost 2 ms 
[INFO ] 2024-03-28 15:06:16.402 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-71797db5-c343-4930-bb32-fd53452bf727 complete, cost 2834ms 
[INFO ] 2024-03-28 15:06:28.599 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:28.599 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:28.599 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:28.601 - [orders(100)][1e1eb39b-13c4-4595-bd49-03fc4387fef6] - Node 1e1eb39b-13c4-4595-bd49-03fc4387fef6[1e1eb39b-13c4-4595-bd49-03fc4387fef6] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:06:28.601 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:28.601 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:28.601 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:28.601 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:28.601 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 15:06:28.601 - [orders(100)][1e1eb39b-13c4-4595-bd49-03fc4387fef6] - Node 1e1eb39b-13c4-4595-bd49-03fc4387fef6[1e1eb39b-13c4-4595-bd49-03fc4387fef6] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:28.670 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:06:28.723 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@534fc885 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@534fc885 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@534fc885 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 15:06:28.724 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:28.724 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:28.724 - [orders(100)][2eb8ccbd-42a9-4ec1-a735-66878432b142] - Node 2eb8ccbd-42a9-4ec1-a735-66878432b142[2eb8ccbd-42a9-4ec1-a735-66878432b142] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:06:28.724 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:28.724 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:28.724 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:28.724 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:28.724 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:28.724 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:28.724 - [orders(100)][2eb8ccbd-42a9-4ec1-a735-66878432b142] - Node 2eb8ccbd-42a9-4ec1-a735-66878432b142[2eb8ccbd-42a9-4ec1-a735-66878432b142] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:28.733 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:06:28.833 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6d4b26e3 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6d4b26e3 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@6d4b26e3 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:06:28.833 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:06:28.854 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:06:28.854 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:06:28.854 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:06:28.854 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:28.855 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:28.855 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:06:28.855 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:06:28.909 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 19 ms 
[INFO ] 2024-03-28 15:06:28.909 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:06:28.909 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:06:28.909 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:06:28.909 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:06:28.909 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 61 ms 
[INFO ] 2024-03-28 15:06:28.909 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 58 ms 
[WARN ] 2024-03-28 15:06:28.990 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:06:28.991 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:06:29.000 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:06:29.002 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:29.002 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:29.002 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:06:29.002 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:06:29.002 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 10 ms 
[INFO ] 2024-03-28 15:06:29.002 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:06:29.041 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:06:29.041 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:06:29.041 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:06:29.041 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:06:29.041 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 42 ms 
[INFO ] 2024-03-28 15:06:29.041 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 40 ms 
[INFO ] 2024-03-28 15:06:30.888 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:30.888 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:30.889 - [orders(100)][cc2ae182-5552-4a4a-9ef1-170c1c5f10c4] - Node cc2ae182-5552-4a4a-9ef1-170c1c5f10c4[cc2ae182-5552-4a4a-9ef1-170c1c5f10c4] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:06:30.889 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:30.889 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:30.889 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:30.889 - [orders(100)][cc2ae182-5552-4a4a-9ef1-170c1c5f10c4] - Node cc2ae182-5552-4a4a-9ef1-170c1c5f10c4[cc2ae182-5552-4a4a-9ef1-170c1c5f10c4] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:30.890 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:30.891 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:30.891 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:30.938 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:06:31.150 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@29e258f4 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@29e258f4 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@29e258f4 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:06:31.150 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:06:31.167 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:06:31.167 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:06:31.169 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:06:31.169 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:31.170 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:31.170 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:06:31.170 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:06:31.225 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 16 ms 
[INFO ] 2024-03-28 15:06:31.225 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:06:31.225 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:06:31.225 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:06:31.225 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:06:31.225 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 60 ms 
[INFO ] 2024-03-28 15:06:31.225 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 57 ms 
[INFO ] 2024-03-28 15:06:31.226 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:06:31.226 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:06:31.226 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:06:31.226 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 0 ms 
[INFO ] 2024-03-28 15:06:31.226 - [orders(100)][1e1eb39b-13c4-4595-bd49-03fc4387fef6] - Node 1e1eb39b-13c4-4595-bd49-03fc4387fef6[1e1eb39b-13c4-4595-bd49-03fc4387fef6] running status set to false 
[INFO ] 2024-03-28 15:06:31.226 - [orders(100)][1e1eb39b-13c4-4595-bd49-03fc4387fef6] - Node 1e1eb39b-13c4-4595-bd49-03fc4387fef6[1e1eb39b-13c4-4595-bd49-03fc4387fef6] schema data cleaned 
[INFO ] 2024-03-28 15:06:31.226 - [orders(100)][1e1eb39b-13c4-4595-bd49-03fc4387fef6] - Node 1e1eb39b-13c4-4595-bd49-03fc4387fef6[1e1eb39b-13c4-4595-bd49-03fc4387fef6] monitor closed 
[INFO ] 2024-03-28 15:06:31.226 - [orders(100)][1e1eb39b-13c4-4595-bd49-03fc4387fef6] - Node 1e1eb39b-13c4-4595-bd49-03fc4387fef6[1e1eb39b-13c4-4595-bd49-03fc4387fef6] close complete, cost 0 ms 
[INFO ] 2024-03-28 15:06:31.265 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-1e1eb39b-13c4-4595-bd49-03fc4387fef6 complete, cost 2703ms 
[INFO ] 2024-03-28 15:06:31.265 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:06:31.265 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:06:31.265 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:06:31.265 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 0 ms 
[INFO ] 2024-03-28 15:06:31.271 - [orders(100)][2eb8ccbd-42a9-4ec1-a735-66878432b142] - Node 2eb8ccbd-42a9-4ec1-a735-66878432b142[2eb8ccbd-42a9-4ec1-a735-66878432b142] running status set to false 
[INFO ] 2024-03-28 15:06:31.271 - [orders(100)][2eb8ccbd-42a9-4ec1-a735-66878432b142] - Node 2eb8ccbd-42a9-4ec1-a735-66878432b142[2eb8ccbd-42a9-4ec1-a735-66878432b142] schema data cleaned 
[INFO ] 2024-03-28 15:06:31.271 - [orders(100)][2eb8ccbd-42a9-4ec1-a735-66878432b142] - Node 2eb8ccbd-42a9-4ec1-a735-66878432b142[2eb8ccbd-42a9-4ec1-a735-66878432b142] monitor closed 
[INFO ] 2024-03-28 15:06:31.271 - [orders(100)][2eb8ccbd-42a9-4ec1-a735-66878432b142] - Node 2eb8ccbd-42a9-4ec1-a735-66878432b142[2eb8ccbd-42a9-4ec1-a735-66878432b142] close complete, cost 0 ms 
[INFO ] 2024-03-28 15:06:31.474 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-2eb8ccbd-42a9-4ec1-a735-66878432b142 complete, cost 2584ms 
[INFO ] 2024-03-28 15:06:32.447 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:32.448 - [orders(100)][d37b72a0-3936-44b0-b673-b8c95b664edd] - Node d37b72a0-3936-44b0-b673-b8c95b664edd[d37b72a0-3936-44b0-b673-b8c95b664edd] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:06:32.448 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:32.448 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:32.448 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:32.448 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:32.448 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:32.448 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:32.448 - [orders(100)][d37b72a0-3936-44b0-b673-b8c95b664edd] - Node d37b72a0-3936-44b0-b673-b8c95b664edd[d37b72a0-3936-44b0-b673-b8c95b664edd] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:32.448 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:32.511 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:06:32.695 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7e1fa4c6 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7e1fa4c6 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@7e1fa4c6 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:06:32.696 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:06:32.696 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:06:32.723 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:06:32.723 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:06:32.736 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:32.737 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:32.737 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:06:32.737 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:06:32.800 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 42 ms 
[INFO ] 2024-03-28 15:06:32.801 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:06:32.801 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:06:32.801 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:06:32.801 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:06:32.801 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 85 ms 
[INFO ] 2024-03-28 15:06:33.006 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 88 ms 
[INFO ] 2024-03-28 15:06:33.491 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:06:33.491 - [orders(100)][cc2ae182-5552-4a4a-9ef1-170c1c5f10c4] - Node cc2ae182-5552-4a4a-9ef1-170c1c5f10c4[cc2ae182-5552-4a4a-9ef1-170c1c5f10c4] running status set to false 
[INFO ] 2024-03-28 15:06:33.492 - [orders(100)][cc2ae182-5552-4a4a-9ef1-170c1c5f10c4] - Node cc2ae182-5552-4a4a-9ef1-170c1c5f10c4[cc2ae182-5552-4a4a-9ef1-170c1c5f10c4] schema data cleaned 
[INFO ] 2024-03-28 15:06:33.492 - [orders(100)][cc2ae182-5552-4a4a-9ef1-170c1c5f10c4] - Node cc2ae182-5552-4a4a-9ef1-170c1c5f10c4[cc2ae182-5552-4a4a-9ef1-170c1c5f10c4] monitor closed 
[INFO ] 2024-03-28 15:06:33.492 - [orders(100)][cc2ae182-5552-4a4a-9ef1-170c1c5f10c4] - Node cc2ae182-5552-4a4a-9ef1-170c1c5f10c4[cc2ae182-5552-4a4a-9ef1-170c1c5f10c4] close complete, cost 3 ms 
[INFO ] 2024-03-28 15:06:33.492 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:06:33.494 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:06:33.494 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 5 ms 
[INFO ] 2024-03-28 15:06:33.697 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-cc2ae182-5552-4a4a-9ef1-170c1c5f10c4 complete, cost 2703ms 
[INFO ] 2024-03-28 15:06:34.435 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:34.435 - [orders(100)][1d46a5ec-8c55-4f5f-b32f-86662d3d2e68] - Node 1d46a5ec-8c55-4f5f-b32f-86662d3d2e68[1d46a5ec-8c55-4f5f-b32f-86662d3d2e68] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:06:34.435 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:34.435 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:34.435 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:34.435 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:34.436 - [orders(100)][1d46a5ec-8c55-4f5f-b32f-86662d3d2e68] - Node 1d46a5ec-8c55-4f5f-b32f-86662d3d2e68[1d46a5ec-8c55-4f5f-b32f-86662d3d2e68] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:34.436 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:34.436 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:34.436 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:34.511 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:06:34.511 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@72d014e2 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@72d014e2 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@72d014e2 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:06:34.680 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:06:34.681 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:06:34.693 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:06:34.693 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:34.693 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:34.693 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:06:34.694 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:06:34.694 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 14 ms 
[INFO ] 2024-03-28 15:06:34.694 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:06:34.737 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:06:34.737 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:06:34.737 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:06:34.737 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:06:34.737 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 42 ms 
[INFO ] 2024-03-28 15:06:34.737 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 46 ms 
[INFO ] 2024-03-28 15:06:35.077 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:06:35.077 - [orders(100)][d37b72a0-3936-44b0-b673-b8c95b664edd] - Node d37b72a0-3936-44b0-b673-b8c95b664edd[d37b72a0-3936-44b0-b673-b8c95b664edd] running status set to false 
[INFO ] 2024-03-28 15:06:35.077 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:06:35.078 - [orders(100)][d37b72a0-3936-44b0-b673-b8c95b664edd] - Node d37b72a0-3936-44b0-b673-b8c95b664edd[d37b72a0-3936-44b0-b673-b8c95b664edd] schema data cleaned 
[INFO ] 2024-03-28 15:06:35.078 - [orders(100)][d37b72a0-3936-44b0-b673-b8c95b664edd] - Node d37b72a0-3936-44b0-b673-b8c95b664edd[d37b72a0-3936-44b0-b673-b8c95b664edd] monitor closed 
[INFO ] 2024-03-28 15:06:35.078 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:06:35.078 - [orders(100)][d37b72a0-3936-44b0-b673-b8c95b664edd] - Node d37b72a0-3936-44b0-b673-b8c95b664edd[d37b72a0-3936-44b0-b673-b8c95b664edd] close complete, cost 1 ms 
[INFO ] 2024-03-28 15:06:35.078 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 2 ms 
[INFO ] 2024-03-28 15:06:35.080 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-d37b72a0-3936-44b0-b673-b8c95b664edd complete, cost 2698ms 
[INFO ] 2024-03-28 15:06:35.635 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:35.635 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:35.635 - [orders(100)][2225972a-a9d8-4f67-b40d-796cb38af123] - Node 2225972a-a9d8-4f67-b40d-796cb38af123[2225972a-a9d8-4f67-b40d-796cb38af123] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:06:35.635 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:35.635 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:35.635 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:35.635 - [orders(100)][2225972a-a9d8-4f67-b40d-796cb38af123] - Node 2225972a-a9d8-4f67-b40d-796cb38af123[2225972a-a9d8-4f67-b40d-796cb38af123] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:35.635 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:35.636 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:35.636 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:35.664 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:06:35.664 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1ae98d8 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1ae98d8 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1ae98d8 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:06:35.838 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:06:35.838 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:06:35.838 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:06:35.838 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:06:35.850 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:35.850 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:35.851 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:06:35.851 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:06:35.926 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 18 ms 
[INFO ] 2024-03-28 15:06:35.927 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:06:35.928 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:06:35.928 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:06:35.928 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:06:35.928 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 90 ms 
[INFO ] 2024-03-28 15:06:35.928 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 89 ms 
[INFO ] 2024-03-28 15:06:37.034 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:06:37.034 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:06:37.034 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:06:37.035 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 1 ms 
[INFO ] 2024-03-28 15:06:37.035 - [orders(100)][1d46a5ec-8c55-4f5f-b32f-86662d3d2e68] - Node 1d46a5ec-8c55-4f5f-b32f-86662d3d2e68[1d46a5ec-8c55-4f5f-b32f-86662d3d2e68] running status set to false 
[INFO ] 2024-03-28 15:06:37.035 - [orders(100)][1d46a5ec-8c55-4f5f-b32f-86662d3d2e68] - Node 1d46a5ec-8c55-4f5f-b32f-86662d3d2e68[1d46a5ec-8c55-4f5f-b32f-86662d3d2e68] schema data cleaned 
[INFO ] 2024-03-28 15:06:37.036 - [orders(100)][1d46a5ec-8c55-4f5f-b32f-86662d3d2e68] - Node 1d46a5ec-8c55-4f5f-b32f-86662d3d2e68[1d46a5ec-8c55-4f5f-b32f-86662d3d2e68] monitor closed 
[INFO ] 2024-03-28 15:06:37.036 - [orders(100)][1d46a5ec-8c55-4f5f-b32f-86662d3d2e68] - Node 1d46a5ec-8c55-4f5f-b32f-86662d3d2e68[1d46a5ec-8c55-4f5f-b32f-86662d3d2e68] close complete, cost 1 ms 
[INFO ] 2024-03-28 15:06:37.242 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-1d46a5ec-8c55-4f5f-b32f-86662d3d2e68 complete, cost 2664ms 
[INFO ] 2024-03-28 15:06:38.238 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:06:38.241 - [orders(100)][2225972a-a9d8-4f67-b40d-796cb38af123] - Node 2225972a-a9d8-4f67-b40d-796cb38af123[2225972a-a9d8-4f67-b40d-796cb38af123] running status set to false 
[INFO ] 2024-03-28 15:06:38.241 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:06:38.241 - [orders(100)][2225972a-a9d8-4f67-b40d-796cb38af123] - Node 2225972a-a9d8-4f67-b40d-796cb38af123[2225972a-a9d8-4f67-b40d-796cb38af123] schema data cleaned 
[INFO ] 2024-03-28 15:06:38.241 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:06:38.241 - [orders(100)][2225972a-a9d8-4f67-b40d-796cb38af123] - Node 2225972a-a9d8-4f67-b40d-796cb38af123[2225972a-a9d8-4f67-b40d-796cb38af123] monitor closed 
[INFO ] 2024-03-28 15:06:38.244 - [orders(100)][2225972a-a9d8-4f67-b40d-796cb38af123] - Node 2225972a-a9d8-4f67-b40d-796cb38af123[2225972a-a9d8-4f67-b40d-796cb38af123] close complete, cost 24 ms 
[INFO ] 2024-03-28 15:06:38.244 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 30 ms 
[INFO ] 2024-03-28 15:06:38.449 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-2225972a-a9d8-4f67-b40d-796cb38af123 complete, cost 2643ms 
[INFO ] 2024-03-28 15:06:47.202 - [orders(100)][7c87172e-3ace-49d7-8b06-60da8ff34645] - Node 7c87172e-3ace-49d7-8b06-60da8ff34645[7c87172e-3ace-49d7-8b06-60da8ff34645] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:06:47.202 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:47.202 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:47.202 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:47.202 - [orders(100)][7c87172e-3ace-49d7-8b06-60da8ff34645] - Node 7c87172e-3ace-49d7-8b06-60da8ff34645[7c87172e-3ace-49d7-8b06-60da8ff34645] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:47.202 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:47.202 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:47.202 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:47.202 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:47.202 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:47.267 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:06:47.299 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@9045f5a error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@9045f5a error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@9045f5a error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[INFO ] 2024-03-28 15:06:47.300 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:47.300 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:47.300 - [orders(100)][84040f2a-c2d2-461d-9ac9-d27b492f0e33] - Node 84040f2a-c2d2-461d-9ac9-d27b492f0e33[84040f2a-c2d2-461d-9ac9-d27b492f0e33] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:06:47.300 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:47.300 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:47.300 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:47.300 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:47.300 - [orders(100)][84040f2a-c2d2-461d-9ac9-d27b492f0e33] - Node 84040f2a-c2d2-461d-9ac9-d27b492f0e33[84040f2a-c2d2-461d-9ac9-d27b492f0e33] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:47.300 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:47.300 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:47.311 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:06:47.459 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2589bbd8 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2589bbd8 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@2589bbd8 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:06:47.459 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:06:47.464 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:06:47.464 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:06:47.481 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:06:47.482 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:47.484 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:47.484 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:06:47.484 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:06:47.546 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 23 ms 
[INFO ] 2024-03-28 15:06:47.546 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:06:47.546 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:06:47.546 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:06:47.546 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:06:47.547 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 83 ms 
[INFO ] 2024-03-28 15:06:47.547 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 70 ms 
[WARN ] 2024-03-28 15:06:47.620 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:06:47.620 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:06:47.636 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:06:47.636 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:06:47.636 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:47.636 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:47.637 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:06:47.637 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:06:47.711 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 19 ms 
[INFO ] 2024-03-28 15:06:47.714 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:06:47.714 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:06:47.714 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:06:47.714 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:06:47.714 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 87 ms 
[INFO ] 2024-03-28 15:06:47.715 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 76 ms 
[INFO ] 2024-03-28 15:06:48.609 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:48.609 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:48.609 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:48.609 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:48.612 - [orders(100)][f753f3fc-d94f-4807-aa50-fc13cc4a08c6] - Node f753f3fc-d94f-4807-aa50-fc13cc4a08c6[f753f3fc-d94f-4807-aa50-fc13cc4a08c6] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:06:48.612 - [orders(100)][f753f3fc-d94f-4807-aa50-fc13cc4a08c6] - Node f753f3fc-d94f-4807-aa50-fc13cc4a08c6[f753f3fc-d94f-4807-aa50-fc13cc4a08c6] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:48.612 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 2 ms 
[INFO ] 2024-03-28 15:06:48.612 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:48.612 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 3 ms 
[INFO ] 2024-03-28 15:06:48.613 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 3 ms 
[INFO ] 2024-03-28 15:06:48.679 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:06:48.875 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1c99ddce error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1c99ddce error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@1c99ddce error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:06:48.875 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:06:48.895 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:06:48.895 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:06:48.895 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:06:48.895 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:48.895 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:48.895 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:06:48.895 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:06:48.941 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 17 ms 
[INFO ] 2024-03-28 15:06:48.941 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:06:48.941 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:06:48.941 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:06:48.941 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:06:48.941 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 51 ms 
[INFO ] 2024-03-28 15:06:49.142 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 49 ms 
[INFO ] 2024-03-28 15:06:49.812 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:06:49.813 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:06:49.813 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:06:49.815 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 2 ms 
[INFO ] 2024-03-28 15:06:49.815 - [orders(100)][7c87172e-3ace-49d7-8b06-60da8ff34645] - Node 7c87172e-3ace-49d7-8b06-60da8ff34645[7c87172e-3ace-49d7-8b06-60da8ff34645] running status set to false 
[INFO ] 2024-03-28 15:06:49.815 - [orders(100)][7c87172e-3ace-49d7-8b06-60da8ff34645] - Node 7c87172e-3ace-49d7-8b06-60da8ff34645[7c87172e-3ace-49d7-8b06-60da8ff34645] schema data cleaned 
[INFO ] 2024-03-28 15:06:49.816 - [orders(100)][7c87172e-3ace-49d7-8b06-60da8ff34645] - Node 7c87172e-3ace-49d7-8b06-60da8ff34645[7c87172e-3ace-49d7-8b06-60da8ff34645] monitor closed 
[INFO ] 2024-03-28 15:06:49.821 - [orders(100)][7c87172e-3ace-49d7-8b06-60da8ff34645] - Node 7c87172e-3ace-49d7-8b06-60da8ff34645[7c87172e-3ace-49d7-8b06-60da8ff34645] close complete, cost 1 ms 
[INFO ] 2024-03-28 15:06:49.821 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-7c87172e-3ace-49d7-8b06-60da8ff34645 complete, cost 2676ms 
[INFO ] 2024-03-28 15:06:49.849 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:06:49.849 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:06:49.849 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:06:49.852 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 1 ms 
[INFO ] 2024-03-28 15:06:49.852 - [orders(100)][84040f2a-c2d2-461d-9ac9-d27b492f0e33] - Node 84040f2a-c2d2-461d-9ac9-d27b492f0e33[84040f2a-c2d2-461d-9ac9-d27b492f0e33] running status set to false 
[INFO ] 2024-03-28 15:06:49.852 - [orders(100)][84040f2a-c2d2-461d-9ac9-d27b492f0e33] - Node 84040f2a-c2d2-461d-9ac9-d27b492f0e33[84040f2a-c2d2-461d-9ac9-d27b492f0e33] schema data cleaned 
[INFO ] 2024-03-28 15:06:49.853 - [orders(100)][84040f2a-c2d2-461d-9ac9-d27b492f0e33] - Node 84040f2a-c2d2-461d-9ac9-d27b492f0e33[84040f2a-c2d2-461d-9ac9-d27b492f0e33] monitor closed 
[INFO ] 2024-03-28 15:06:49.853 - [orders(100)][84040f2a-c2d2-461d-9ac9-d27b492f0e33] - Node 84040f2a-c2d2-461d-9ac9-d27b492f0e33[84040f2a-c2d2-461d-9ac9-d27b492f0e33] close complete, cost 1 ms 
[INFO ] 2024-03-28 15:06:50.056 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-84040f2a-c2d2-461d-9ac9-d27b492f0e33 complete, cost 2582ms 
[INFO ] 2024-03-28 15:06:50.351 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:50.351 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:50.351 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:50.352 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:50.352 - [orders(100)][9bacf676-f2b4-43f2-8d33-951347800c9a] - Node 9bacf676-f2b4-43f2-8d33-951347800c9a[9bacf676-f2b4-43f2-8d33-951347800c9a] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:06:50.352 - [orders(100)][9bacf676-f2b4-43f2-8d33-951347800c9a] - Node 9bacf676-f2b4-43f2-8d33-951347800c9a[9bacf676-f2b4-43f2-8d33-951347800c9a] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:50.352 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:50.352 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:50.352 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:50.352 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:50.381 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:06:50.381 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@20b3db75 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@20b3db75 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@20b3db75 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:06:50.552 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:06:50.562 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:06:50.569 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:06:50.569 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:50.570 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:06:50.570 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:50.570 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:06:50.570 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:06:50.649 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 28 ms 
[INFO ] 2024-03-28 15:06:50.649 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:06:50.649 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:06:50.649 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:06:50.649 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:06:50.649 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 88 ms 
[INFO ] 2024-03-28 15:06:50.649 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 80 ms 
[INFO ] 2024-03-28 15:06:51.240 - [orders(100)][f753f3fc-d94f-4807-aa50-fc13cc4a08c6] - Node f753f3fc-d94f-4807-aa50-fc13cc4a08c6[f753f3fc-d94f-4807-aa50-fc13cc4a08c6] running status set to false 
[INFO ] 2024-03-28 15:06:51.240 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:06:51.241 - [orders(100)][f753f3fc-d94f-4807-aa50-fc13cc4a08c6] - Node f753f3fc-d94f-4807-aa50-fc13cc4a08c6[f753f3fc-d94f-4807-aa50-fc13cc4a08c6] schema data cleaned 
[INFO ] 2024-03-28 15:06:51.241 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:06:51.241 - [orders(100)][f753f3fc-d94f-4807-aa50-fc13cc4a08c6] - Node f753f3fc-d94f-4807-aa50-fc13cc4a08c6[f753f3fc-d94f-4807-aa50-fc13cc4a08c6] monitor closed 
[INFO ] 2024-03-28 15:06:51.241 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:06:51.243 - [orders(100)][f753f3fc-d94f-4807-aa50-fc13cc4a08c6] - Node f753f3fc-d94f-4807-aa50-fc13cc4a08c6[f753f3fc-d94f-4807-aa50-fc13cc4a08c6] close complete, cost 2 ms 
[INFO ] 2024-03-28 15:06:51.243 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 3 ms 
[INFO ] 2024-03-28 15:06:51.449 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-f753f3fc-d94f-4807-aa50-fc13cc4a08c6 complete, cost 2711ms 
[INFO ] 2024-03-28 15:06:52.455 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:52.455 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:52.457 - [orders(100)][60aaf829-1822-42b6-a594-dc1017b85e46] - Node 60aaf829-1822-42b6-a594-dc1017b85e46[60aaf829-1822-42b6-a594-dc1017b85e46] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:06:52.457 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:52.457 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:52.457 - [orders(100)][60aaf829-1822-42b6-a594-dc1017b85e46] - Node 60aaf829-1822-42b6-a594-dc1017b85e46[60aaf829-1822-42b6-a594-dc1017b85e46] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:52.457 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:52.457 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:52.457 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:52.457 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 1 ms 
[INFO ] 2024-03-28 15:06:52.547 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:06:52.547 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@68c934a3 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@68c934a3 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@68c934a3 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:06:52.707 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:06:52.707 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:06:52.707 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:06:52.724 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:06:52.724 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:52.724 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:52.724 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:06:52.724 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:06:52.724 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 23 ms 
[INFO ] 2024-03-28 15:06:52.772 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:06:52.772 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:06:52.772 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:06:52.773 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:06:52.773 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 67 ms 
[INFO ] 2024-03-28 15:06:52.773 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 55 ms 
[INFO ] 2024-03-28 15:06:52.916 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:06:52.916 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:06:52.916 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:06:52.917 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 1 ms 
[INFO ] 2024-03-28 15:06:52.919 - [orders(100)][9bacf676-f2b4-43f2-8d33-951347800c9a] - Node 9bacf676-f2b4-43f2-8d33-951347800c9a[9bacf676-f2b4-43f2-8d33-951347800c9a] running status set to false 
[INFO ] 2024-03-28 15:06:52.919 - [orders(100)][9bacf676-f2b4-43f2-8d33-951347800c9a] - Node 9bacf676-f2b4-43f2-8d33-951347800c9a[9bacf676-f2b4-43f2-8d33-951347800c9a] schema data cleaned 
[INFO ] 2024-03-28 15:06:52.919 - [orders(100)][9bacf676-f2b4-43f2-8d33-951347800c9a] - Node 9bacf676-f2b4-43f2-8d33-951347800c9a[9bacf676-f2b4-43f2-8d33-951347800c9a] monitor closed 
[INFO ] 2024-03-28 15:06:52.920 - [orders(100)][9bacf676-f2b4-43f2-8d33-951347800c9a] - Node 9bacf676-f2b4-43f2-8d33-951347800c9a[9bacf676-f2b4-43f2-8d33-951347800c9a] close complete, cost 0 ms 
[INFO ] 2024-03-28 15:06:53.056 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-9bacf676-f2b4-43f2-8d33-951347800c9a complete, cost 2608ms 
[INFO ] 2024-03-28 15:06:53.056 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:53.056 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:53.056 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:53.056 - [orders(100)][eab1c922-3d96-49c0-a5a3-4a8c857d76ad] - Node eab1c922-3d96-49c0-a5a3-4a8c857d76ad[eab1c922-3d96-49c0-a5a3-4a8c857d76ad] start preload schema,table counts: 0 
[INFO ] 2024-03-28 15:06:53.056 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] start preload schema,table counts: 1 
[INFO ] 2024-03-28 15:06:53.056 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:53.056 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:53.056 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:53.056 - [orders(100)][eab1c922-3d96-49c0-a5a3-4a8c857d76ad] - Node eab1c922-3d96-49c0-a5a3-4a8c857d76ad[eab1c922-3d96-49c0-a5a3-4a8c857d76ad] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:53.070 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] preload schema finished, cost 0 ms 
[INFO ] 2024-03-28 15:06:53.070 - [orders(100)][Order Details] - Init standardized JS engine... 
[ERROR] 2024-03-28 15:06:53.238 - [orders(100)][Order Details] - Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@33da27c4 error,please check your javascript code <-- Error Message -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@33da27c4 error,please check your javascript code

<-- Simple Stack Trace -->
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	<js>.:program(<eval>:5)
	org.graalvm.polyglot.Context.eval(Context.java:379)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	...

<-- Full Stack Trace -->
Incorrect JS code, syntax error found: function process(record){    record["subtotal"] = (record["UnitPrice"] * record["Quantity"]) - record["Discount"];
    return record;
}
var DateUtil = Java.type("com.tapdata.constant.DateUtil");
var UUIDGenerator = Java.type("com.tapdata.constant.UUIDGenerator");
var idGen = Java.type("com.tapdata.constant.UUIDGenerator");
var HashMap = Java.type("java.util.HashMap");
var LinkedHashMap = Java.type("java.util.LinkedHashMap");
var ArrayList = Java.type("java.util.ArrayList");
var uuid = UUIDGenerator.uuid;
var JSONUtil = Java.type('com.tapdata.constant.JSONUtil');
var HanLPUtil = Java.type("com.tapdata.constant.HanLPUtil");
var split_chinese = HanLPUtil.hanLPParticiple;
var util = Java.type("com.tapdata.processor.util.Util");
var MD5Util = Java.type("com.tapdata.constant.MD5Util");
var MD5 = function(str){return MD5Util.crypt(str, true);};
var Collections = Java.type("java.util.Collections");
var MapUtils = Java.type("com.tapdata.constant.MapUtil");
var sleep = function(ms){
var Thread = Java.type("java.lang.Thread");
Thread.sleep(ms);
}
var networkUtil = Java.type("com.tapdata.constant.NetworkUtil");
var rest = Java.type("com.tapdata.processor.util.CustomRest");
var httpUtil = Java.type("cn.hutool.http.HttpUtil");
var tcp = Java.type("com.tapdata.processor.util.CustomTcp");
var mongo = Java.type("com.tapdata.processor.util.CustomMongodb");
,script eval io.tapdata.script.factory.script.TapRunScriptEngine@33da27c4 error,please check your javascript code
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:489)
	at io.tapdata.flow.engine.V2.node.hazelcast.processor.HazelcastJavaScriptProcessorNode.doInit(HazelcastJavaScriptProcessorNode.java:144)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.init(HazelcastBaseNode.java:206)
	at com.hazelcast.jet.core.AbstractProcessor.init(AbstractProcessor.java:82)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.lambda$init$2f647568$1(ProcessorTasklet.java:279)
	at com.hazelcast.jet.function.RunnableEx.run(RunnableEx.java:31)
	at com.hazelcast.jet.impl.util.Util.doWithClassLoader(Util.java:532)
	at com.hazelcast.jet.impl.execution.ProcessorTasklet.init(ProcessorTasklet.java:279)
	at com.hazelcast.jet.impl.execution.TaskletExecutionService$BlockingWorker.run(TaskletExecutionService.java:311)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:83)
	at io.tapdata.script.factory.script.TapRunScriptEngine.eval(TapRunScriptEngine.java:105)
	at com.tapdata.processor.ScriptUtil.getScriptStandardizationEngine(ScriptUtil.java:487)
	... 14 more
Caused by: javax.script.ScriptException: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.toScriptException(GraalJSScriptEngine.java:483)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:460)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:426)
	at javax.script.AbstractScriptEngine.eval(AbstractScriptEngine.java:264)
	at io.tapdata.script.factory.script.TapRunScriptEngine.lambda$eval$3(TapRunScriptEngine.java:105)
	at io.tapdata.script.factory.script.TapRunScriptEngine.applyClassLoaderContext(TapRunScriptEngine.java:81)
	... 16 more
Caused by: org.graalvm.polyglot.PolyglotException: TypeError: Access to host class com.tapdata.constant.DateUtil is not allowed or does not exist.
	at <js>.:program(<eval>:5)
	at org.graalvm.polyglot.Context.eval(Context.java:379)
	at com.oracle.truffle.js.scriptengine.GraalJSScriptEngine.eval(GraalJSScriptEngine.java:458)
	... 20 more

[WARN ] 2024-03-28 15:06:53.245 - [orders(100)][Order Details] - Source table is empty, trying to mock data 
[INFO ] 2024-03-28 15:06:53.245 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] running status set to false 
[INFO ] 2024-03-28 15:06:53.257 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] running status set to false 
[INFO ] 2024-03-28 15:06:53.258 - [orders(100)][Order Details] - PDK connector node stopped: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:53.258 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] running status set to false 
[INFO ] 2024-03-28 15:06:53.258 - [orders(100)][Order Details] - PDK connector node released: HazelcastSampleSourcePdkDataNode-c618a243-0bea-4333-bcb2-fe72aed7e168 
[INFO ] 2024-03-28 15:06:53.258 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] schema data cleaned 
[INFO ] 2024-03-28 15:06:53.259 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] monitor closed 
[INFO ] 2024-03-28 15:06:53.259 - [orders(100)][Order Details] - Node Order Details[c618a243-0bea-4333-bcb2-fe72aed7e168] close complete, cost 17 ms 
[INFO ] 2024-03-28 15:06:53.302 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] schema data cleaned 
[INFO ] 2024-03-28 15:06:53.302 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] schema data cleaned 
[INFO ] 2024-03-28 15:06:53.302 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] monitor closed 
[INFO ] 2024-03-28 15:06:53.303 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] monitor closed 
[INFO ] 2024-03-28 15:06:53.303 - [orders(100)][Delete Order Details] - Node Delete Order Details[9e7cf1fd-8d6b-42ea-9a7a-eec2d9182054] close complete, cost 57 ms 
[INFO ] 2024-03-28 15:06:53.303 - [orders(100)][Rename Order Details] - Node Rename Order Details[e74e35b3-5618-474a-8074-fa7610cf3d1d] close complete, cost 44 ms 
[INFO ] 2024-03-28 15:06:55.086 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:06:55.087 - [orders(100)][60aaf829-1822-42b6-a594-dc1017b85e46] - Node 60aaf829-1822-42b6-a594-dc1017b85e46[60aaf829-1822-42b6-a594-dc1017b85e46] running status set to false 
[INFO ] 2024-03-28 15:06:55.087 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:06:55.087 - [orders(100)][60aaf829-1822-42b6-a594-dc1017b85e46] - Node 60aaf829-1822-42b6-a594-dc1017b85e46[60aaf829-1822-42b6-a594-dc1017b85e46] schema data cleaned 
[INFO ] 2024-03-28 15:06:55.087 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:06:55.087 - [orders(100)][60aaf829-1822-42b6-a594-dc1017b85e46] - Node 60aaf829-1822-42b6-a594-dc1017b85e46[60aaf829-1822-42b6-a594-dc1017b85e46] monitor closed 
[INFO ] 2024-03-28 15:06:55.087 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 6 ms 
[INFO ] 2024-03-28 15:06:55.090 - [orders(100)][60aaf829-1822-42b6-a594-dc1017b85e46] - Node 60aaf829-1822-42b6-a594-dc1017b85e46[60aaf829-1822-42b6-a594-dc1017b85e46] close complete, cost 2 ms 
[INFO ] 2024-03-28 15:06:55.090 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-60aaf829-1822-42b6-a594-dc1017b85e46 complete, cost 2688ms 
[INFO ] 2024-03-28 15:06:55.611 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] running status set to false 
[INFO ] 2024-03-28 15:06:55.614 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] schema data cleaned 
[INFO ] 2024-03-28 15:06:55.614 - [orders(100)][eab1c922-3d96-49c0-a5a3-4a8c857d76ad] - Node eab1c922-3d96-49c0-a5a3-4a8c857d76ad[eab1c922-3d96-49c0-a5a3-4a8c857d76ad] running status set to false 
[INFO ] 2024-03-28 15:06:55.614 - [orders(100)][eab1c922-3d96-49c0-a5a3-4a8c857d76ad] - Node eab1c922-3d96-49c0-a5a3-4a8c857d76ad[eab1c922-3d96-49c0-a5a3-4a8c857d76ad] schema data cleaned 
[INFO ] 2024-03-28 15:06:55.614 - [orders(100)][eab1c922-3d96-49c0-a5a3-4a8c857d76ad] - Node eab1c922-3d96-49c0-a5a3-4a8c857d76ad[eab1c922-3d96-49c0-a5a3-4a8c857d76ad] monitor closed 
[INFO ] 2024-03-28 15:06:55.614 - [orders(100)][eab1c922-3d96-49c0-a5a3-4a8c857d76ad] - Node eab1c922-3d96-49c0-a5a3-4a8c857d76ad[eab1c922-3d96-49c0-a5a3-4a8c857d76ad] close complete, cost 0 ms 
[INFO ] 2024-03-28 15:06:55.614 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] monitor closed 
[INFO ] 2024-03-28 15:06:55.616 - [orders(100)][Order Details] - Node Order Details[c2743dff-22d1-470d-ada0-a8a8199d2c37] close complete, cost 7 ms 
[INFO ] 2024-03-28 15:06:55.616 - [orders(100)] - load tapTable task 66051315a90a2b08fd2add84-eab1c922-3d96-49c0-a5a3-4a8c857d76ad complete, cost 2596ms 
