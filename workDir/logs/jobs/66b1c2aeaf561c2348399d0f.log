[INFO ] 2024-08-06 14:32:08.143 - [任务 2] - Task initialization... 
[INFO ] 2024-08-06 14:32:08.144 - [任务 2] - Start task milestones: 66b1c2aeaf561c2348399d0f(任务 2) 
[INFO ] 2024-08-06 14:32:08.265 - [任务 2] - <PERSON>de performs snapshot read asynchronously 
[INFO ] 2024-08-06 14:32:08.266 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-06 14:32:08.880 - [任务 2][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[9885d5f4-69c6-40d7-be06-9f0aa76dd1fc] start preload schema,table counts: 1 
[INFO ] 2024-08-06 14:32:08.881 - [任务 2][TestCustomer] - Node TestCustomer[a917436f-56d9-40d8-914f-608eec0dcf9a] start preload schema,table counts: 1 
[INFO ] 2024-08-06 14:32:08.948 - [任务 2][TestCustomer] - Node TestCustomer[a917436f-56d9-40d8-914f-608eec0dcf9a] preload schema finished, cost 66 ms 
[INFO ] 2024-08-06 14:32:08.950 - [任务 2][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[9885d5f4-69c6-40d7-be06-9f0aa76dd1fc] preload schema finished, cost 70 ms 
[INFO ] 2024-08-06 14:32:10.134 - [任务 2][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" read batch size: 100 
[INFO ] 2024-08-06 14:32:10.137 - [任务 2][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" event queue capacity: 200 
[INFO ] 2024-08-06 14:32:10.138 - [任务 2][BMSQL_CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-06 14:32:10.158 - [任务 2][BMSQL_CUSTOMER] - batch offset found: {},stream offset found: {"filename":"binlog.000027","position":34670360,"gtidSet":""} 
[INFO ] 2024-08-06 14:32:10.257 - [任务 2][BMSQL_CUSTOMER] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-06 14:32:10.257 - [任务 2][TestCustomer] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-06 14:32:10.361 - [任务 2][BMSQL_CUSTOMER] - Initial sync started 
[INFO ] 2024-08-06 14:32:10.365 - [任务 2][BMSQL_CUSTOMER] - Starting batch read, table name: BMSQL_CUSTOMER, offset: null 
[INFO ] 2024-08-06 14:32:10.497 - [任务 2][BMSQL_CUSTOMER] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-08-06 14:32:10.500 - [任务 2][BMSQL_CUSTOMER] - Query table 'BMSQL_CUSTOMER' counts: 100000 
[INFO ] 2024-08-06 14:32:10.501 - [任务 2][BMSQL_CUSTOMER] - Initial sync completed 
[ERROR] 2024-08-06 14:32:10.536 - [任务 2][BMSQL_CUSTOMER] - java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column '_id' in 'field list' <-- Error Message -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column '_id' in 'field list'

<-- Simple Stack Trace -->
Caused by: java.sql.SQLSyntaxErrorException: Unknown column '_id' in 'field list'
	com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	...

<-- Full Stack Trace -->
java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column '_id' in 'field list'
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:440)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshotWithControl$2(HazelcastSourcePdkDataNode.java:256)
	at io.tapdata.flow.engine.V2.node.hazelcast.controller.SnapshotOrderController.runWithControl(SnapshotOrderController.java:147)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshotWithControl(HazelcastSourcePdkDataNode.java:257)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.startSourceRunner(HazelcastSourcePdkDataNode.java:178)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.RuntimeException: java.sql.SQLSyntaxErrorException: Unknown column '_id' in 'field list'
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:73)
	at io.tapdata.flow.engine.V2.node.hazelcast.HazelcastBaseNode.executeDataFuncAspect(HazelcastBaseNode.java:143)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.doSnapshot(HazelcastSourcePdkDataNode.java:340)
	... 10 more
Caused by: java.sql.SQLSyntaxErrorException: Unknown column '_id' in 'field list'
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:186)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.lambda$invokePDKMethod$12(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.api.Node.applyClassLoaderContext(Node.java:27)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethod(PDKInvocationMonitor.java:153)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invoke(PDKInvocationMonitor.java:95)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$doSnapshot$12(HazelcastSourcePdkDataNode.java:348)
	at io.tapdata.aspect.utils.AspectUtils.executeDataFuncAspect(AspectUtils.java:67)
	... 12 more
Caused by: java.sql.SQLSyntaxErrorException: Unknown column '_id' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.mysql.cj.jdbc.StatementImpl.createResultSetUsingServerFetch(StatementImpl.java:565)
	at com.mysql.cj.jdbc.StatementImpl.executeQuery(StatementImpl.java:1140)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:110)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at io.tapdata.common.JdbcContext.query(JdbcContext.java:94)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutHashSplit(CommonDbConnector.java:568)
	at io.tapdata.common.CommonDbConnector.batchReadWithoutOffset(CommonDbConnector.java:562)
	at io.tapdata.flow.engine.V2.node.hazelcast.data.pdk.HazelcastSourcePdkDataNode.lambda$null$11(HazelcastSourcePdkDataNode.java:421)
	at io.tapdata.pdk.core.monitor.PDKInvocationMonitor.invokePDKMethodPrivate(PDKInvocationMonitor.java:164)
	... 18 more

[INFO ] 2024-08-06 14:32:10.739 - [任务 2][BMSQL_CUSTOMER] - Job suspend in error handle 
[INFO ] 2024-08-06 14:32:11.029 - [任务 2][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[9885d5f4-69c6-40d7-be06-9f0aa76dd1fc] running status set to false 
[INFO ] 2024-08-06 14:32:11.098 - [任务 2] - Task [任务 2] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-08-06 14:32:11.100 - [任务 2][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode-9885d5f4-69c6-40d7-be06-9f0aa76dd1fc 
[INFO ] 2024-08-06 14:32:11.104 - [任务 2][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode-9885d5f4-69c6-40d7-be06-9f0aa76dd1fc 
[INFO ] 2024-08-06 14:32:11.105 - [任务 2][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[9885d5f4-69c6-40d7-be06-9f0aa76dd1fc] schema data cleaned 
[INFO ] 2024-08-06 14:32:11.121 - [任务 2][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[9885d5f4-69c6-40d7-be06-9f0aa76dd1fc] monitor closed 
[INFO ] 2024-08-06 14:32:11.125 - [任务 2][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[9885d5f4-69c6-40d7-be06-9f0aa76dd1fc] close complete, cost 91 ms 
[INFO ] 2024-08-06 14:32:11.125 - [任务 2][TestCustomer] - Node TestCustomer[a917436f-56d9-40d8-914f-608eec0dcf9a] running status set to false 
[INFO ] 2024-08-06 14:32:11.138 - [任务 2][TestCustomer] - PDK connector node stopped: HazelcastTargetPdkDataNode-a917436f-56d9-40d8-914f-608eec0dcf9a 
[INFO ] 2024-08-06 14:32:11.138 - [任务 2][TestCustomer] - PDK connector node released: HazelcastTargetPdkDataNode-a917436f-56d9-40d8-914f-608eec0dcf9a 
[INFO ] 2024-08-06 14:32:11.138 - [任务 2][TestCustomer] - Node TestCustomer[a917436f-56d9-40d8-914f-608eec0dcf9a] schema data cleaned 
[INFO ] 2024-08-06 14:32:11.139 - [任务 2][TestCustomer] - Node TestCustomer[a917436f-56d9-40d8-914f-608eec0dcf9a] monitor closed 
[INFO ] 2024-08-06 14:32:11.140 - [任务 2][TestCustomer] - Node TestCustomer[a917436f-56d9-40d8-914f-608eec0dcf9a] close complete, cost 18 ms 
[INFO ] 2024-08-06 14:32:16.098 - [任务 2] - Task [任务 2] cannot retry, reason: Max retry duration set to 0 
[INFO ] 2024-08-06 14:32:16.173 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-06 14:32:16.174 - [任务 2] - Stop task milestones: 66b1c2aeaf561c2348399d0f(任务 2)  
[INFO ] 2024-08-06 14:32:16.205 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-08-06 14:32:16.206 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-08-06 14:32:16.253 - [任务 2] - Remove memory task client succeed, task: 任务 2[66b1c2aeaf561c2348399d0f] 
[INFO ] 2024-08-06 14:32:16.254 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[66b1c2aeaf561c2348399d0f] 
[INFO ] 2024-08-06 14:32:39.140 - [任务 2] - Task initialization... 
[INFO ] 2024-08-06 14:32:39.157 - [任务 2] - Start task milestones: 66b1c2aeaf561c2348399d0f(任务 2) 
[INFO ] 2024-08-06 14:32:39.157 - [任务 2] - Node performs snapshot read asynchronously 
[INFO ] 2024-08-06 14:32:39.279 - [任务 2] - The engine receives 任务 2 task data from TM and will continue to run tasks by jet 
[INFO ] 2024-08-06 14:32:39.280 - [任务 2][TestCustomer] - Node TestCustomer[a917436f-56d9-40d8-914f-608eec0dcf9a] start preload schema,table counts: 1 
[INFO ] 2024-08-06 14:32:39.307 - [任务 2][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[9885d5f4-69c6-40d7-be06-9f0aa76dd1fc] start preload schema,table counts: 1 
[INFO ] 2024-08-06 14:32:39.307 - [任务 2][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[9885d5f4-69c6-40d7-be06-9f0aa76dd1fc] preload schema finished, cost 19 ms 
[INFO ] 2024-08-06 14:32:39.307 - [任务 2][TestCustomer] - Node TestCustomer[a917436f-56d9-40d8-914f-608eec0dcf9a] preload schema finished, cost 23 ms 
[INFO ] 2024-08-06 14:32:40.220 - [任务 2][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" read batch size: 100 
[INFO ] 2024-08-06 14:32:40.224 - [任务 2][BMSQL_CUSTOMER] - Source node "BMSQL_CUSTOMER" event queue capacity: 200 
[INFO ] 2024-08-06 14:32:40.224 - [任务 2][BMSQL_CUSTOMER] - On the first run, the breakpoint will be initialized 
[INFO ] 2024-08-06 14:32:40.238 - [任务 2][BMSQL_CUSTOMER] - batch offset found: {},stream offset found: {"filename":"binlog.000027","position":34670360,"gtidSet":""} 
[INFO ] 2024-08-06 14:32:40.238 - [任务 2][BMSQL_CUSTOMER] - Before the event is output to the target from source, it will automatically block field changes 
[INFO ] 2024-08-06 14:32:40.291 - [任务 2][BMSQL_CUSTOMER] - Initial sync started 
[INFO ] 2024-08-06 14:32:40.291 - [任务 2][BMSQL_CUSTOMER] - Starting batch read, table name: BMSQL_CUSTOMER, offset: null 
[INFO ] 2024-08-06 14:32:40.308 - [任务 2][BMSQL_CUSTOMER] - Table BMSQL_CUSTOMER is going to be initial synced 
[INFO ] 2024-08-06 14:32:40.385 - [任务 2][BMSQL_CUSTOMER] - Query table 'BMSQL_CUSTOMER' counts: 100000 
[INFO ] 2024-08-06 14:32:40.386 - [任务 2][TestCustomer] - Write batch size: 100, max wait ms per batch: 500 
[INFO ] 2024-08-06 14:32:45.864 - [任务 2][BMSQL_CUSTOMER] - Table [BMSQL_CUSTOMER] has been completed batch read, will skip batch read on the next run 
[INFO ] 2024-08-06 14:32:45.864 - [任务 2][BMSQL_CUSTOMER] - Initial sync completed 
[INFO ] 2024-08-06 14:32:45.868 - [任务 2][BMSQL_CUSTOMER] - Incremental sync starting... 
[INFO ] 2024-08-06 14:32:45.869 - [任务 2][BMSQL_CUSTOMER] - Initial sync completed 
[INFO ] 2024-08-06 14:32:45.903 - [任务 2][BMSQL_CUSTOMER] - Starting stream read, table list: [BMSQL_CUSTOMER], offset: {"filename":"binlog.000027","position":34670360,"gtidSet":""} 
[INFO ] 2024-08-06 14:32:45.903 - [任务 2][BMSQL_CUSTOMER] - Starting mysql cdc, server name: e16ca8b7-9f06-40e0-a866-70472a919aa3 
[INFO ] 2024-08-06 14:32:46.098 - [任务 2][BMSQL_CUSTOMER] - Starting binlog reader with config {
  snapshot.locking.mode: none
  connector.class: io.debezium.connector.mysql.MySqlConnector
  database.user: root
  max.queue.size: 800
  offset.storage: io.tapdata.connector.mysql.PdkPersistenceOffsetBackingStore
  database.server.id: 362581063
  time.precision.mode: adaptive_time_microseconds
  database.server.name: e16ca8b7-9f06-40e0-a866-70472a919aa3
  database.port: 3307
  threadName: Debezium-Mysql-Connector-e16ca8b7-9f06-40e0-a866-70472a919aa3
  database.hostname: localhost
  database.password: ********
  name: e16ca8b7-9f06-40e0-a866-70472a919aa3
  pdk.offset.string: {"name":"e16ca8b7-9f06-40e0-a866-70472a919aa3","offset":{"{\"server\":\"e16ca8b7-9f06-40e0-a866-70472a919aa3\"}":"{\"file\":\"binlog.000027\",\"pos\":34670360,\"server_id\":\"1\"}"}}
  database.history.skip.unparseable.ddl: true
  database.history.store.only.monitored.tables.ddl: true
  database.history.store.only.captured.tables.ddl: true
  table.include.list: test.BMSQL_CUSTOMER
  max.batch.size: 100
  snapshot.mode: schema_only_recovery
  database.include.list: test
  database.history: io.tapdata.connector.mysql.StateMapHistoryBackingStore
} 
[INFO ] 2024-08-06 14:32:46.098 - [任务 2][BMSQL_CUSTOMER] - Connector Mysql incremental start succeed, tables: [BMSQL_CUSTOMER], data change syncing 
[INFO ] 2024-08-06 14:33:01.259 - [任务 2] - Stop task milestones: 66b1c2aeaf561c2348399d0f(任务 2)  
[INFO ] 2024-08-06 14:33:01.461 - [任务 2][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[9885d5f4-69c6-40d7-be06-9f0aa76dd1fc] running status set to false 
[INFO ] 2024-08-06 14:33:01.462 - [任务 2][BMSQL_CUSTOMER] - CDC engine stopped: Connector 'io.debezium.connector.mysql.MySqlConnector' completed normally. 
[INFO ] 2024-08-06 14:33:01.462 - [任务 2][BMSQL_CUSTOMER] - Mysql binlog reader stopped 
[INFO ] 2024-08-06 14:33:01.474 - [任务 2][BMSQL_CUSTOMER] - PDK connector node stopped: HazelcastSourcePdkDataNode-9885d5f4-69c6-40d7-be06-9f0aa76dd1fc 
[INFO ] 2024-08-06 14:33:01.474 - [任务 2][BMSQL_CUSTOMER] - PDK connector node released: HazelcastSourcePdkDataNode-9885d5f4-69c6-40d7-be06-9f0aa76dd1fc 
[INFO ] 2024-08-06 14:33:01.475 - [任务 2][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[9885d5f4-69c6-40d7-be06-9f0aa76dd1fc] schema data cleaned 
[INFO ] 2024-08-06 14:33:01.475 - [任务 2][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[9885d5f4-69c6-40d7-be06-9f0aa76dd1fc] monitor closed 
[INFO ] 2024-08-06 14:33:01.476 - [任务 2][BMSQL_CUSTOMER] - Node BMSQL_CUSTOMER[9885d5f4-69c6-40d7-be06-9f0aa76dd1fc] close complete, cost 81 ms 
[INFO ] 2024-08-06 14:33:01.476 - [任务 2][TestCustomer] - Node TestCustomer[a917436f-56d9-40d8-914f-608eec0dcf9a] running status set to false 
[INFO ] 2024-08-06 14:33:01.499 - [任务 2][TestCustomer] - PDK connector node stopped: HazelcastTargetPdkDataNode-a917436f-56d9-40d8-914f-608eec0dcf9a 
[INFO ] 2024-08-06 14:33:01.499 - [任务 2][TestCustomer] - PDK connector node released: HazelcastTargetPdkDataNode-a917436f-56d9-40d8-914f-608eec0dcf9a 
[INFO ] 2024-08-06 14:33:01.499 - [任务 2][TestCustomer] - Node TestCustomer[a917436f-56d9-40d8-914f-608eec0dcf9a] schema data cleaned 
[INFO ] 2024-08-06 14:33:01.500 - [任务 2][TestCustomer] - Node TestCustomer[a917436f-56d9-40d8-914f-608eec0dcf9a] monitor closed 
[INFO ] 2024-08-06 14:33:01.711 - [任务 2][TestCustomer] - Node TestCustomer[a917436f-56d9-40d8-914f-608eec0dcf9a] close complete, cost 23 ms 
[INFO ] 2024-08-06 14:33:06.324 - [任务 2] - Closed task monitor(s)
Monitors: 
  - Report task ping time monitor 
[INFO ] 2024-08-06 14:33:06.329 - [任务 2] - Stopped task aspect(s) 
[INFO ] 2024-08-06 14:33:06.329 - [任务 2] - Snapshot order controller have been removed 
[INFO ] 2024-08-06 14:33:06.374 - [任务 2] - Remove memory task client succeed, task: 任务 2[66b1c2aeaf561c2348399d0f] 
[INFO ] 2024-08-06 14:33:06.375 - [任务 2] - Destroy memory task client cache succeed, task: 任务 2[66b1c2aeaf561c2348399d0f] 
