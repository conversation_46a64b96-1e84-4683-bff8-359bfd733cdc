{"id": "dag1", "nodes": [{"connectionConfig": {}, "table": {"name": "empty-table1", "id": "empty-table1"}, "id": "s1", "pdkId": "emptySource", "group": "io.tapdata.connector.empty", "type": "Source", "version": "1.1-SNAPSHOT"}, {"connectionConfig": {}, "table": {"name": "empty-table2", "id": "empty-table2"}, "id": "t2", "pdkId": "emptySource", "group": "io.tapdata.connector.empty", "type": "Target", "version": "1.1-SNAPSHOT"}], "dag": [["s1", "t2"]], "jobOptions": {"queueSize": 100, "queueBatchSize": 100}}