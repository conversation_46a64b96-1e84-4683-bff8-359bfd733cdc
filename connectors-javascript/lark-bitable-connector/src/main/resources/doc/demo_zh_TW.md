### 寫在前面
如果您感興趣的話，不妨前往飛書多元表格提供的API文檔文檔，詳細了解全部內容：

- OpenAPI文檔：[https://open.feishu.cn/document/server-docs/docs/bitable-v1/bitable-overview](https://open.feishu.cn/document/server-docs/docs/bitable-v1/bitable-overview)

當然您也可以瀏覽以下內容，快速上手飛書多元表格數據源的配置流程。

---

### 1.屬性說明

App ID：在飛書自建一個應用，建立完成後得到應用的App ID資訊。 獲取管道參見：https://open.feishu.cn/document/home/<USER>/overview

App Secret：在飛書自建一個應用，建立完成後得到應用的App Secret資訊。 獲取管道參見：https://open.feishu.cn/document/home/<USER>/overview

App Token：一篇多元表格可以理解成是一個應用（app），標記該應用的唯一標識叫app_ token。 獲取管道參見：https://open.feishu.cn/document/server-docs/docs/bitable-v1/notification

Table ID：每篇多元表格是由多個資料表（table）組成的，標記該資料表的唯一標識叫table_ id。 獲取管道參見：https://open.feishu.cn/document/server-docs/docs/bitable-v1/notification

---
