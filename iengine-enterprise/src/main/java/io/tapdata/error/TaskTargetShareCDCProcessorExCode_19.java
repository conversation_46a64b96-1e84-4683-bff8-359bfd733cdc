package io.tapdata.error;

import io.tapdata.exception.TapExClass;
import io.tapdata.exception.TapExCode;

/**
 * <AUTHOR>
 * @Description
 * @create 2023-03-16 19:20
 **/
@TapExClass(code = 19, module = "Target Share CDC Processor", prefix = "TSCP", describe = "Write share cdc log content")
public interface TaskTargetShareCDCProcessorExCode_19 {
    @TapExCode
    String UNKNOWN_ERROR = "19001";
    @TapExCode(
            describe = "The shared mining task will read the incremental events from the source side and cache them in the external database for use by the copy/transform task that starts the shared mining. The shared mining task failed to write incremental events to the external storage database",
            describeCN = "共享挖掘任务会将从源端读取出增量事件并缓存在外存数据库中，给开启共享挖掘的复制/转换任务使用。将增量事件写入外存数据库失败",
            dynamicDescription = "The configured external cache database is unable to provide normal service\n" +
                    "The configured external storage database type is :{}, the external storage name is :{}, the source table name of the incremental event is :{}, and the size of the batch writing is :{}\"",
            dynamicDescriptionCN = "配置的外存缓存数据库无法正常提供服务\n" +
                    "配置的外存数据库类型为:{}，外存名称为:{}，增量事件的源表名为:{}，此次批量写入的大小为:{}",
            solution = "To check whether the external storage cache database is normal, you can click the sub-menu \"external storage Management\" in the parent menu \"System Management\" in the left menu bar of the system to enter the external storage management interface. The external memory configuration corresponding to the shared mining task is found, and the connection test is carried out to see if it is available. If it is not available, the test failure information is used to troubleshoate. Restart the shared mining task after the connection test passes",
            solutionCN = "检查外存缓存数据库是否正常，可以点击系统左侧菜单栏中的父菜单“系统管理”中的子菜单“外存管理”进入外存管理界面。找到共享挖掘任务对应的外存配置，进行连接测试看是否可用，如不可用则需要根据测试失败信息进行排查。待连接测试通过后重新启动共享挖掘任务"
    )
    String INSERT_MANY_INTO_RINGBUFFER_FAILED = "19002";
    @TapExCode(
            describe = "Before the incremental event is written to the external database by shared mining, it fails to convert the LogContent of the incremental event into the Document that the external database can write",
            describeCN = "共享挖掘将增量事件写入外存数据库前，将增量事件内容LogContent对象转为外存数据库可以写入的对象Document时失败",
            dynamicDescription = "The LogContent of the incremental event for a failed conversion is {}",
            dynamicDescriptionCN = "转换失败的增量事件内容为：{}"
    )
    String CONVERT_LOG_CONTENT_TO_DOCUMENT_FAILED = "19003";

    @TapExCode(
            describe = "Before the shared mining starts the task, the engine generates a start tag to write to the external database. The conversion of the startTimeSign object to the Document object written to the disk database before writing to the disk database failed",
            describeCN = "在共享挖掘启动任务之前，引擎会生成一个开始标记写到外存数据库中。在写入外存数据库前，将开始标记startTimeSign对象转换为外存数据库写入的Document对象时失败",
            dynamicDescription = "The startTimeSign is {}",
            dynamicDescriptionCN = "开始标记的内容为:{}"
    )
    String CONVERT_START_TIME_SIGN_OBJ_TO_DOCUMENT_FAILED = "19004";

    @TapExCode(
            describe = "Before the shared mining starts the task, the engine will generate a start tag to write to the table of the cache external database, and the start tag will fail to write to the cache external database",
            describeCN = "在共享挖掘启动任务之前，引擎会生成一个开始标记写到缓存外存数据库的表中，将开始标记写到缓存外存数据库失败",
            dynamicDescription = "The configured external cache database is unable to provide normal service\n" +
                    "The start tag of shared mining failed to write to the cache table in the external database. The startTimeSign is {}",
            dynamicDescriptionCN = "配置的外存缓存数据库无法正常提供服务\n" +
                    "配置的外存数据库类型为:{}，外存名称为:{},开始标记的内容为:{}",
            solution = "To check whether the external storage cache database is normal, you can click the sub-menu \"external storage Management\" in the parent menu \"System Management\" in the left menu bar of the system to enter the external storage management interface. The external memory configuration corresponding to the shared mining task is found, and the connection test is carried out to see if it is available. If it is not available, the test failure information is used to troubleshoate. Restart the shared mining task after the connection test passes",
            solutionCN = "检查外存缓存数据库是否正常，可以点击系统左侧菜单栏中的父菜单“系统管理”中的子菜单“外存管理”进入外存管理界面。找到共享挖掘任务对应的外存配置，进行连接测试看是否可用，如不可用则需要根据测试失败信息进行排查。待连接测试通过后重新启动共享挖掘任务"

    )
    String WRITE_START_TIME_SIGN_FAILED = "19005";

    @TapExCode(
            describe = "The shared mining task will read the incremental events from the source side and cache them in the external database for use by the copy/transform task that starts the shared mining. Shared mining failed to write the log contents of a single incremental event to the external database",
            describeCN = "共享挖掘任务会将从源端读取出增量事件并缓存在外存数据库中，给开启共享挖掘的复制/转换任务使用。共享挖掘将单条增量事件的日志内容写入外存数据库失败",
            dynamicDescription = "The configured external cache database is unable to provide normal service\n"+
                    "The configured external storage database type is :{}, the external storage name is :{}, the incremental event source table name is :{}, and the LogContent of the incremental event is :{}",
            dynamicDescriptionCN = "配置的外存缓存数据库无法正常提供服务\n" +
                    "配置的外存数据库类型为:{}，外存名称为:{},增量事件源表名为:{},增量事件的内容为:{}",
            solution = "To check whether the external storage cache database is normal, you can click the sub-menu \"external storage Management\" in the parent menu \"System Management\" in the left menu bar of the system to enter the external storage management interface. The external memory configuration corresponding to the shared mining task is found, and the connection test is carried out to see if it is available. If it is not available, the test failure information is used to troubleshoate. Restart the shared mining task after the connection test passes",
            solutionCN = "检查外存缓存数据库是否正常，可以点击系统左侧菜单栏中的父菜单“系统管理”中的子菜单“外存管理”进入外存管理界面。找到共享挖掘任务对应的外存配置，进行连接测试看是否可用，如不可用则需要根据测试失败信息进行排查。待连接测试通过后重新启动共享挖掘任务"
    )
    String WRITE_ONE_SHARE_LOG_FAILED = "19006";
}
