package com.tapdata.tm.inspect.service;

import com.tapdata.tm.Unit4Util;
import com.tapdata.tm.base.dto.Where;
import com.tapdata.tm.base.exception.BizException;
import com.tapdata.tm.commons.task.dto.TaskDto;
import com.tapdata.tm.commons.util.JsonUtil;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.ds.service.impl.DataSourceService;
import com.tapdata.tm.inspect.bean.Stats;
import com.tapdata.tm.inspect.bean.Task;
import com.tapdata.tm.inspect.constant.InspectResultEnum;
import com.tapdata.tm.inspect.constant.InspectStatusEnum;
import com.tapdata.tm.inspect.dto.InspectDto;
import com.tapdata.tm.inspect.dto.InspectResultDto;
import com.tapdata.tm.message.constant.Level;
import com.tapdata.tm.message.constant.MsgTypeEnum;
import com.tapdata.tm.message.service.MessageService;
import com.tapdata.tm.messagequeue.dto.MessageQueueDto;
import com.tapdata.tm.messagequeue.service.MessageQueueService;
import com.tapdata.tm.permissions.DataPermissionHelper;
import com.tapdata.tm.permissions.constants.DataPermissionActionEnums;
import com.tapdata.tm.permissions.constants.DataPermissionDataTypeEnums;
import com.tapdata.tm.permissions.constants.DataPermissionMenuEnums;
import com.tapdata.tm.task.service.TaskService;
import com.tapdata.tm.userLog.constant.Modular;
import com.tapdata.tm.userLog.constant.Operation;
import com.tapdata.tm.userLog.service.UserLogService;
import com.tapdata.tm.utils.MessageUtil;
import com.tapdata.tm.worker.service.WorkerService;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.slf4j.Logger;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doCallRealMethod;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class InspectTaskServiceImplTest {
    Logger log;
    UserDetail userDetail;
    InspectTaskServiceImpl impl;
    private MessageService messageService;
    private UserLogService userLogService;
    private InspectDetailsService inspectDetailsService;
    private InspectResultService inspectResultService;
    private MessageQueueService messageQueueService;
    private WorkerService workerService;
    private TaskService taskService;
    private DataSourceService dataSourceService;
    @BeforeEach
    void setUp() {
        log = mock(Logger.class);
        userDetail = mock(UserDetail.class);
        impl = mock(InspectTaskServiceImpl.class);
        messageService = mock(MessageService.class);
        userLogService = mock(UserLogService.class);
        inspectDetailsService = mock(InspectDetailsService.class);
        inspectResultService = mock(InspectResultService.class);
        messageQueueService = mock(MessageQueueService.class);
        workerService = mock(WorkerService.class);
        taskService = mock(TaskService.class);
        dataSourceService = mock(DataSourceService.class);
        doCallRealMethod().when(impl).setDataSourceService(dataSourceService);
        doCallRealMethod().when(impl).setMessageService(messageService);
        doCallRealMethod().when(impl).setUserLogService(userLogService);
        doCallRealMethod().when(impl).setInspectDetailsService(inspectDetailsService);
        doCallRealMethod().when(impl).setInspectResultService(inspectResultService);
        doCallRealMethod().when(impl).setMessageQueueService(messageQueueService);
        doCallRealMethod().when(impl).setTaskService(taskService);
        doCallRealMethod().when(impl).setWorkerService(workerService);
        impl.setDataSourceService(dataSourceService);
        impl.setMessageService(messageService);
        impl.setUserLogService(userLogService);
        impl.setInspectDetailsService(inspectDetailsService);
        impl.setInspectResultService(inspectResultService);
        impl.setMessageQueueService(messageQueueService);
        impl.setTaskService(taskService);
        impl.setWorkerService(workerService);
        Unit4Util.mockSlf4jLog(impl, log);
    }

    @Test
    void beforeSave() {
        InspectDto dto = new InspectDto();
        doCallRealMethod().when(impl).beforeSave(dto, userDetail);
        Assertions.assertDoesNotThrow(() -> impl.beforeSave(dto, userDetail));
    }

    @Test
    void inspectTaskRun() {
        InspectDto retDto = new InspectDto();
        Where where = mock(Where.class);
        InspectDto updateDto = new InspectDto();
        doNothing().when(log).info("Inspect task execute running: {}", updateDto);
        when((impl.executeInspect(where, updateDto, userDetail))).thenReturn(retDto);
        when(userDetail.getUserId()).thenReturn("id");
        ObjectId id = new ObjectId();
        retDto.setId(id);
        retDto.setName("name");
        doNothing().when(userLogService).addUserLog(Modular.INSPECT, Operation.START, "id", id.toString(), "name");

        when(impl.inspectTaskRun(where, updateDto, userDetail)).thenCallRealMethod();
        Assertions.assertEquals(retDto, impl.inspectTaskRun(where, updateDto, userDetail));
        verify(impl).executeInspect(where, updateDto, userDetail);
        verify(userLogService).addUserLog(Modular.INSPECT, Operation.START, "id", id.toString(), "name");
    }

    @Test
    void inspectTaskStop() {
        InspectDto retDto = new InspectDto();
        String id1 = new ObjectId().toHexString();
        InspectDto updateDto = new InspectDto();
        doNothing().when(log).info("Inspect task execute stopping: {}", updateDto);
        when(impl.findById(any(ObjectId.class))).thenReturn(retDto);
        doNothing().when(impl).stopInspectTask(retDto);

        when(userDetail.getUserId()).thenReturn("id");
        ObjectId id = new ObjectId();
        retDto.setId(id);
        retDto.setName("name");
        doNothing().when(userLogService).addUserLog(Modular.INSPECT, Operation.STOP, "id", id.toString(), "name");

        when(impl.inspectTaskStop(id1, updateDto, userDetail)).thenCallRealMethod();
        Assertions.assertEquals(updateDto, impl.inspectTaskStop(id1, updateDto, userDetail));
        verify(impl).findById(any(ObjectId.class));
        verify(userLogService).addUserLog(Modular.INSPECT, Operation.STOP, "id", id.toString(), "name");
    }

    @Test
    void inspectTaskError() {
        String id1 = new ObjectId().toHexString();
        InspectDto updateDto = new InspectDto();
        updateDto.setName("name");
        doNothing().when(log).info("Inspect task execute error: {}", updateDto);
        when(userDetail.getUserId()).thenReturn("id");
        doNothing().when(messageService).addInspect("name", id1, MsgTypeEnum.INSPECT_ERROR, Level.ERROR, userDetail);
        when(impl.inspectTaskError(id1, updateDto, userDetail)).thenCallRealMethod();
        Assertions.assertEquals(updateDto, impl.inspectTaskError(id1, updateDto, userDetail));
        verify(messageService).addInspect("name", id1, MsgTypeEnum.INSPECT_ERROR, Level.ERROR, userDetail);
    }

    @Nested
    class inspectTaskDoneTest {
        String id1;
        InspectDto updateDto;
        ObjectId id;
        @BeforeEach
        void init() {
            id1 = new ObjectId().toHexString();
            updateDto = new InspectDto();
            updateDto.setName("name");
            updateDto.setResult(InspectResultEnum.FAILED.getValue());
            doNothing().when(log).info("Inspect task execute error: {}", updateDto);
            when(userDetail.getUserId()).thenReturn("id");
            id = new ObjectId();
            doNothing().when(messageService).addInspect("name", id1, MsgTypeEnum.INSPECT_VALUE, Level.ERROR, userDetail);
            when(impl.inspectTaskDone(id1, updateDto, userDetail)).thenCallRealMethod();
        }
        @Test
        void inspectTaskDone() {
            Assertions.assertEquals(updateDto, impl.inspectTaskDone(id1, updateDto, userDetail));
            verify(messageService).addInspect("name", id1, MsgTypeEnum.INSPECT_VALUE, Level.ERROR, userDetail);
        }
        @Test
        void testIsFAILED() {
            updateDto.setResult(InspectResultEnum.PASSED.getValue());
            Assertions.assertEquals(updateDto, impl.inspectTaskDone(id1, updateDto, userDetail));
            verify(messageService, times(0)).addInspect("name", id1, MsgTypeEnum.INSPECT_VALUE, Level.ERROR, userDetail);
        }
    }

    @Nested
    class ExecuteInspectTest {
        Where where;
        InspectDto updateDto;
        String id;
        InspectDto inspectDto;

        List<String> taskIds;
        InspectResultDto inspectResult;
        String inspectResultId;
        @BeforeEach
        void init() {
            inspectResultId = new ObjectId().toHexString();
            updateDto = new InspectDto();
            inspectResult = new InspectResultDto();
            taskIds = new ArrayList<>();
            id = new ObjectId().toHexString();
            where = mock(Where.class);

            inspectDto = new InspectDto();
            inspectDto.setStatus(InspectStatusEnum.SCHEDULING.name());
            inspectDto.setAgentId("agent-id");

            when(where.get("id")).thenReturn(id);
            when(where.put(anyString(), any(ObjectId.class))).thenReturn(new ObjectId());

            when(impl.findById(any(ObjectId.class), any(UserDetail.class))).thenReturn(inspectDto);
            doAnswer(a -> {
                inspectDto.setAgentId("agent-id");
                return null;
            }).when(workerService).scheduleTaskToEngine(inspectDto, userDetail);

            inspectDto.setInspectResultId(null);
            inspectDto.setTaskIds(taskIds);
            when(inspectResultService.findById(any(ObjectId.class), any(UserDetail.class))).thenReturn(inspectResult);
            doNothing().when(inspectDetailsService).deleteAll(any(Query.class), any(UserDetail.class));
            doNothing().when(log).info(anyString(), anyString(), anyString(), anyString());

            when(impl.updateByWhere(where, inspectDto, userDetail)).thenReturn(0L);
            when(impl.startInspectTask(inspectDto, "agent-id")).thenReturn("");

            when(impl.executeInspect(where, updateDto, userDetail)).thenCallRealMethod();
        }

        void verifyMock(String errorCode, Object... param) {
            Assertions.assertThrows(BizException.class, () -> {
                try(MockedStatic<MessageUtil> mu = mockStatic(MessageUtil.class)) {
                    mu.when(() -> MessageUtil.getMessage(errorCode, param)).thenReturn("message");
                    mu.when(() -> MessageUtil.getMessage(errorCode)).thenReturn("message");
                    impl.executeInspect(where, updateDto, userDetail);
                } catch (BizException e) {
                    Assertions.assertEquals(errorCode, e.getErrorCode());
                    throw e;
                }
            });
        }
        @Test
        void testNormal() {
            Assertions.assertDoesNotThrow(() -> impl.executeInspect(where, updateDto, userDetail));
            verify(where).get("id");
            verify(impl).findById(any(ObjectId.class), any(UserDetail.class));
            verify(workerService).scheduleTaskToEngine(inspectDto, userDetail);
            verify(where).put(anyString(), any(ObjectId.class));
            verify(impl).updateByWhere(where, inspectDto, userDetail);
            verify(inspectResultService, times(0)).findById(any(ObjectId.class), any(UserDetail.class));
            verify(inspectDetailsService, times(0)).deleteAll(any(Query.class), any(UserDetail.class));
            verify(log, times(0)).info(anyString(), anyString(), anyString(), anyString());
            verify(impl).startInspectTask(inspectDto, "agent-id");
        }

        @Test
        void testCanNotFindById() {
            when(impl.findById(any(ObjectId.class), any(UserDetail.class))).thenReturn(null);
            verifyMock("Inspect.Start.Failed", "Inspect task not exists: " + id);
            verify(where).get("id");
            verify(impl).findById(any(ObjectId.class), any(UserDetail.class));
            verify(workerService, times(0)).scheduleTaskToEngine(inspectDto, userDetail);
            verify(where, times(0)).put(anyString(), any(ObjectId.class));
            verify(impl, times(0)).updateByWhere(where, inspectDto, userDetail);
            verify(inspectResultService, times(0)).findById(any(ObjectId.class), any(UserDetail.class));
            verify(inspectDetailsService, times(0)).deleteAll(any(Query.class), any(UserDetail.class));
            verify(log, times(0)).info(anyString(), anyString(), anyString(), anyString());
            verify(impl, times(0)).startInspectTask(inspectDto, "agent-id");
        }

        @Test
        void testIsRUNNING() {
            inspectDto.setStatus(InspectStatusEnum.RUNNING.getValue());
            verifyMock("Inspect.Start.Failed", "Running tasks cannot be restarted");
            verify(where).get("id");
            verify(impl).findById(any(ObjectId.class), any(UserDetail.class));
            verify(workerService, times(0)).scheduleTaskToEngine(inspectDto, userDetail);
            verify(where, times(0)).put(anyString(), any(ObjectId.class));
            verify(impl, times(0)).updateByWhere(where, inspectDto, userDetail);
            verify(inspectResultService, times(0)).findById(any(ObjectId.class), any(UserDetail.class));
            verify(inspectDetailsService, times(0)).deleteAll(any(Query.class), any(UserDetail.class));
            verify(log, times(0)).info(anyString(), anyString(), anyString(), anyString());
            verify(impl, times(0)).startInspectTask(inspectDto, "agent-id");
        }

        @Test
        void testFirstCheckIdNotEmpty() {
            updateDto.setByFirstCheckId("id");
            Assertions.assertDoesNotThrow(() -> impl.executeInspect(where, updateDto, userDetail));
            verify(where).get("id");
            verify(impl).findById(any(ObjectId.class), any(UserDetail.class));
            verify(workerService).scheduleTaskToEngine(inspectDto, userDetail);
            verify(where).put(anyString(), any(ObjectId.class));
            verify(impl).updateByWhere(where, inspectDto, userDetail);
            verify(inspectResultService, times(0)).findById(any(ObjectId.class), any(UserDetail.class));
            verify(inspectDetailsService, times(0)).deleteAll(any(Query.class), any(UserDetail.class));
            verify(log, times(0)).info(anyString(), anyString(), anyString(), anyString());
            verify(impl).startInspectTask(inspectDto, "agent-id");
        }

        @Test
        void testAgentIdIsEmpty() {
            doAnswer(a -> {
                inspectDto.setAgentId(null);
                return null;
            }).when(workerService).scheduleTaskToEngine(inspectDto, userDetail);
            verifyMock("Inspect.ProcessId.NotFound");
            verify(where).get("id");
            verify(impl).findById(any(ObjectId.class), any(UserDetail.class));
            verify(workerService, times(1)).scheduleTaskToEngine(inspectDto, userDetail);
            verify(where, times(0)).put(anyString(), any(ObjectId.class));
            verify(impl, times(0)).updateByWhere(where, inspectDto, userDetail);
            verify(inspectResultService, times(0)).findById(any(ObjectId.class), any(UserDetail.class));
            verify(inspectDetailsService, times(0)).deleteAll(any(Query.class), any(UserDetail.class));
            verify(log, times(0)).info(anyString(), anyString(), anyString(), anyString());
            verify(impl, times(0)).startInspectTask(inspectDto, "agent-id");
        }

        @Test
        void testInspectResultIdNotEmpty() {
            updateDto.setInspectResultId(inspectResultId);
            Assertions.assertDoesNotThrow(() -> impl.executeInspect(where, updateDto, userDetail));
            verify(where).get("id");
            verify(impl).findById(any(ObjectId.class), any(UserDetail.class));
            verify(workerService).scheduleTaskToEngine(inspectDto, userDetail);
            verify(where).put(anyString(), any(ObjectId.class));
            verify(impl).updateByWhere(where, inspectDto, userDetail);
            verify(inspectResultService, times(1)).findById(any(ObjectId.class), any(UserDetail.class));
            verify(inspectDetailsService, times(1)).deleteAll(any(Query.class), any(UserDetail.class));
            verify(log, times(1)).info(anyString(), anyString(), anyString(), anyString());
            verify(impl).startInspectTask(inspectDto, "agent-id");
        }
        @Test
        void testInspectResultIdNotEmpty1() {
            when(inspectResultService.findById(any(ObjectId.class), any(UserDetail.class))).thenReturn(null);
            updateDto.setInspectResultId(inspectResultId);
            Assertions.assertDoesNotThrow(() -> impl.executeInspect(where, updateDto, userDetail));
            verify(where).get("id");
            verify(impl).findById(any(ObjectId.class), any(UserDetail.class));
            verify(workerService).scheduleTaskToEngine(inspectDto, userDetail);
            verify(where).put(anyString(), any(ObjectId.class));
            verify(impl).updateByWhere(where, inspectDto, userDetail);
            verify(inspectResultService, times(1)).findById(any(ObjectId.class), any(UserDetail.class));
            verify(inspectDetailsService, times(1)).deleteAll(any(Query.class), any(UserDetail.class));
            verify(log, times(1)).info(anyString(), anyString(), anyString(), anyString());
            verify(impl).startInspectTask(inspectDto, "agent-id");
        }

        @Test
        void testInspectResultIdNotEmpty2() {
            List<Stats> stats = new ArrayList<>();
            stats.add(null);
            Stats s1 = new Stats();
            s1.setResult("failed");
            s1.setTaskId("taskId");
            stats.add(s1);
            Stats s2 = new Stats();
            s2.setResult("succeed");
            s2.setTaskId("taskId");
            stats.add(s2);
            List<Task> ts = new ArrayList<>();
            Task t1 = new Task();
            t1.setTaskId("taskId");
            ts.add(t1);
            ts.add(null);
            Task t2 = new Task();
            t2.setTaskId("taskId2");
            ts.add(t2);
            inspectDto.setTasks(ts);
            updateDto.setInspectResultId(inspectResultId);
            inspectResult.setStats(stats);
            Assertions.assertDoesNotThrow(() -> impl.executeInspect(where, updateDto, userDetail));
            verify(where).get("id");
            verify(impl).findById(any(ObjectId.class), any(UserDetail.class));
            verify(workerService).scheduleTaskToEngine(inspectDto, userDetail);
            verify(where).put(anyString(), any(ObjectId.class));
            verify(impl).updateByWhere(where, inspectDto, userDetail);
            verify(inspectResultService, times(1)).findById(any(ObjectId.class), any(UserDetail.class));
            verify(inspectDetailsService, times(1)).deleteAll(any(Query.class), any(UserDetail.class));
            verify(log, times(1)).info(anyString(), anyString(), anyString(), anyString());
            verify(impl).startInspectTask(inspectDto, "agent-id");
        }
        @Test
        void testInspectResultIdNotEmpty3() {
            taskIds.add("taskId");
            inspectDto.setTaskIds(taskIds);
            updateDto.setInspectResultId(inspectResultId);
            Assertions.assertDoesNotThrow(() -> impl.executeInspect(where, updateDto, userDetail));
            verify(where).get("id");
            verify(impl).findById(any(ObjectId.class), any(UserDetail.class));
            verify(workerService).scheduleTaskToEngine(inspectDto, userDetail);
            verify(where).put(anyString(), any(ObjectId.class));
            verify(impl).updateByWhere(where, inspectDto, userDetail);
            verify(inspectResultService, times(1)).findById(any(ObjectId.class), any(UserDetail.class));
            verify(inspectDetailsService, times(1)).deleteAll(any(Query.class), any(UserDetail.class));
            verify(log, times(1)).info(anyString(), anyString(), anyString(), anyString());
            verify(impl).startInspectTask(inspectDto, "agent-id");
        }
    }

    @Nested
    class startInspectTask {
        InspectDto inspectDto;
        @BeforeEach
        void init() {
            inspectDto = new InspectDto();
            inspectDto.setName("name");
            inspectDto.setId(new ObjectId());
            doNothing().when(log).info(anyString(), anyString(), anyString(), any(ObjectId.class));
            doNothing().when(log).error(anyString(), any(Exception.class));
            when(impl.startInspectTask(inspectDto, "id")).thenCallRealMethod();
        }

        @Test
        void testNormal() {
            doNothing().when(messageQueueService).sendMessage(any(MessageQueueDto.class));
            try(MockedStatic<JsonUtil> ju = mockStatic(JsonUtil.class)) {
                ju.when(() -> JsonUtil.toJsonUseJackson(inspectDto)).thenReturn("{}");
                ju.when(() -> JsonUtil.parseJson("{}", Map.class)).thenReturn(new HashMap<>());
                Assertions.assertDoesNotThrow(() -> impl.startInspectTask(inspectDto, "id"));
                verify(log).info(anyString(), anyString(), anyString(), any(ObjectId.class));
                verify(log, times(0)).error(anyString(), any(Exception.class));
                verify(messageQueueService).sendMessage(any(MessageQueueDto.class));
            }
        }
        @Test
        void testError() {
            doAnswer(a -> {
                throw new Exception("e");
            }).when(messageQueueService).sendMessage(any(MessageQueueDto.class));
            try(MockedStatic<JsonUtil> ju = mockStatic(JsonUtil.class)) {
                ju.when(() -> JsonUtil.toJsonUseJackson(inspectDto)).thenReturn("{}");
                ju.when(() -> JsonUtil.parseJson("{}", Map.class)).thenReturn(new HashMap<>());
                Assertions.assertDoesNotThrow(() -> impl.startInspectTask(inspectDto, "id"));
                verify(log).info(anyString(), anyString(), anyString(), any(ObjectId.class));
                verify(log).error(anyString(), any(Exception.class));
                verify(messageQueueService).sendMessage(any(MessageQueueDto.class));
            }
        }
    }

    @Nested
    class StopInspectTaskTest {
        InspectDto inspectDto;
        @BeforeEach
        void init() {
            inspectDto = new InspectDto();
            inspectDto.setName("name");
            inspectDto.setId(new ObjectId());
            inspectDto.setAgentId("id");
            doNothing().when(log).info(anyString(), anyString(), anyString(), any(ObjectId.class));
            doNothing().when(log).error(anyString(), anyString(), any(Exception.class));
            doNothing().when(log).error(anyString(), anyString());
            doCallRealMethod().when(impl).stopInspectTask(inspectDto);
        }

        @Test
        void testNormal() {
            doNothing().when(messageQueueService).sendMessage(any(MessageQueueDto.class));
            Assertions.assertDoesNotThrow(() -> impl.stopInspectTask(inspectDto));
            verify(log).info(anyString(), anyString(), anyString(), any(ObjectId.class));
            verify(log, times(0)).error(anyString(), anyString(), any(Exception.class));
            verify(log, times(0)).error(anyString(), anyString());
            verify(messageQueueService).sendMessage(any(MessageQueueDto.class));
        }
        @Test
        void testError() {
            doAnswer(a -> {
                throw new Exception("e");
            }).when(messageQueueService).sendMessage(any(MessageQueueDto.class));
            Assertions.assertDoesNotThrow(() -> impl.stopInspectTask(inspectDto));
            verify(log).info(anyString(), anyString(), anyString(), any(ObjectId.class));
            verify(log).error(anyString(), anyString(), any(Exception.class));
            verify(log, times(0)).error(anyString(), anyString());
            verify(messageQueueService).sendMessage(any(MessageQueueDto.class));
        }
        @Test
        void testAgentIsEmpty() {
            inspectDto.setAgentId(null);
            Assertions.assertDoesNotThrow(() -> impl.stopInspectTask(inspectDto));
            verify(log, times(0)).info(anyString(), anyString(), anyString(), any(ObjectId.class));
            verify(log, times(0)).error(anyString(), anyString(), any(Exception.class));
            verify(log, times(1)).error(anyString(), anyString());
            verify(messageQueueService, times(0)).sendMessage(any(MessageQueueDto.class));
        }
    }

    @Test
    void findTaskList() {
        when(impl.findTaskList(userDetail)).thenCallRealMethod();
        doNothing().when(impl).findTask(anyList(), anyString(), any(UserDetail.class));
        Assertions.assertDoesNotThrow(() -> impl.findTaskList(userDetail));
        verify(impl, times(2)).findTask(anyList(), anyString(), any(UserDetail.class));
    }

    @Nested
    class FindTaskTest {
        List<TaskDto> taskDtoList;
        List<TaskDto> taskOfMigrate;
        @BeforeEach
        void init() {
            taskDtoList = new ArrayList<>();

            taskOfMigrate = new ArrayList<>();
            when(userDetail.isRoot()).thenReturn(true);
            when(taskService.findAll(any(Query.class))).thenReturn(taskOfMigrate);
            doCallRealMethod().when(impl).findTask(taskDtoList, TaskDto.SYNC_TYPE_MIGRATE, userDetail);
        }

        @Test
        void testNormal() {
            try(MockedStatic<DataPermissionHelper> dph = mockStatic(DataPermissionHelper.class)) {
                dph.when(() -> DataPermissionHelper.setFilterConditions(any(Boolean.class), any(Query.class), any(UserDetail.class))).thenReturn(true);
                dph.when(() -> DataPermissionHelper.check(
                        any(UserDetail.class),
                        any(DataPermissionMenuEnums.class),
                        any(DataPermissionActionEnums.class),
                        any(DataPermissionDataTypeEnums.class),
                        any(),
                        any(Supplier.class),
                        any(Supplier.class))).thenAnswer(a -> {
                    Supplier argument = a.getArgument(5, Supplier.class);
                    return argument.get();
                });
                impl.findTask(taskDtoList, TaskDto.SYNC_TYPE_MIGRATE, userDetail);
                Assertions.assertEquals(0, taskDtoList.size());
                verify(taskService).findAll(any(Query.class));
            }
        }
        @Test
        void testDataPermissionMenuEnumsIsNull() {
            doCallRealMethod().when(impl).findTask(taskDtoList, "sss", userDetail);
            try(MockedStatic<DataPermissionHelper> dph = mockStatic(DataPermissionHelper.class)) {
                dph.when(() -> DataPermissionHelper.setFilterConditions(any(Boolean.class), any(Query.class), any(UserDetail.class))).thenReturn(true);
                dph.when(() -> DataPermissionHelper.check(
                        any(UserDetail.class),
                        any(DataPermissionMenuEnums.class),
                        any(DataPermissionActionEnums.class),
                        any(DataPermissionDataTypeEnums.class),
                        any(),
                        any(Supplier.class),
                        any(Supplier.class))).thenAnswer(a -> {
                    Supplier argument = a.getArgument(5, Supplier.class);
                    return argument.get();
                });
                impl.findTask(taskDtoList, "sss", userDetail);
                Assertions.assertEquals(0, taskDtoList.size());
                verify(taskService, times(0)).findAll(any(Query.class));
            }
        }
        @Test
        void testUserNotRoot() {
            when(userDetail.isRoot()).thenReturn(false);
            try(MockedStatic<DataPermissionHelper> dph = mockStatic(DataPermissionHelper.class)) {
                dph.when(() -> DataPermissionHelper.setFilterConditions(any(Boolean.class), any(Query.class), any(UserDetail.class))).thenReturn(true);
                dph.when(() -> DataPermissionHelper.check(
                        any(UserDetail.class),
                        any(DataPermissionMenuEnums.class),
                        any(DataPermissionActionEnums.class),
                        any(DataPermissionDataTypeEnums.class),
                        any(),
                        any(Supplier.class),
                        any(Supplier.class))).thenAnswer(a -> {
                    Supplier argument = a.getArgument(5, Supplier.class);
                    return argument.get();
                });
                impl.findTask(taskDtoList, TaskDto.SYNC_TYPE_MIGRATE, userDetail);
                Assertions.assertEquals(0, taskDtoList.size());
                verify(taskService).findAll(any(Query.class));
            }
        }
        @Test
        void testSetFilterConditionsIsTrue() {
            when(userDetail.isRoot()).thenReturn(false);
            try(MockedStatic<DataPermissionHelper> dph = mockStatic(DataPermissionHelper.class)) {
                dph.when(() -> DataPermissionHelper.setFilterConditions(any(Boolean.class), any(Query.class), any(UserDetail.class))).thenReturn(false);
                dph.when(() -> DataPermissionHelper.check(
                        any(UserDetail.class),
                        any(DataPermissionMenuEnums.class),
                        any(DataPermissionActionEnums.class),
                        any(DataPermissionDataTypeEnums.class),
                        any(),
                        any(Supplier.class),
                        any(Supplier.class))).thenAnswer(a -> {
                    Supplier argument = a.getArgument(5, Supplier.class);
                    return argument.get();
                });
                impl.findTask(taskDtoList, TaskDto.SYNC_TYPE_MIGRATE, userDetail);
                Assertions.assertEquals(0, taskDtoList.size());
                verify(taskService).findAll(any(Query.class));
            }
        }
    }
}