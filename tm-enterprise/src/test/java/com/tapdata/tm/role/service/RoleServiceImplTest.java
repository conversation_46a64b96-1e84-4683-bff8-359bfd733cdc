package com.tapdata.tm.role.service;

import com.mongodb.client.result.UpdateResult;
import com.tapdata.tm.Permission.entity.PermissionEntity;
import com.tapdata.tm.Permission.service.PermissionService;
import com.tapdata.tm.base.dto.Filter;
import com.tapdata.tm.base.dto.Page;
import com.tapdata.tm.base.dto.Where;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.role.dto.RoleDto;
import com.tapdata.tm.role.entity.RoleEntity;
import com.tapdata.tm.role.repository.RoleRepository;
import com.tapdata.tm.roleMapping.service.RoleMappingService;
import com.tapdata.tm.userLog.constant.Modular;
import com.tapdata.tm.userLog.constant.Operation;
import com.tapdata.tm.userLog.service.UserLogService;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.internal.verification.Times;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

class RoleServiceImplTest {
    RoleServiceImpl roleService;
    RoleMappingService roleMappingService;
    RoleRepository repository;
    UserLogService userLogService;
    PermissionService permissionService;
    @BeforeEach
    void init() {
        roleService = mock(RoleServiceImpl.class);
        roleMappingService = mock(RoleMappingService.class);
        ReflectionTestUtils.setField(roleService, "roleMappingService", roleMappingService);
        repository = mock(RoleRepository.class);
        ReflectionTestUtils.setField(roleService, "repository", repository);
        userLogService = mock(UserLogService.class);
        ReflectionTestUtils.setField(roleService, "userLogService", userLogService);
        permissionService = mock(PermissionService.class);
        ReflectionTestUtils.setField(roleService, "permissionService", permissionService);
    }
    @Nested
    class testFind {
        @BeforeEach
        void init() {
            when(roleService.find(any(Filter.class), any(UserDetail.class))).thenCallRealMethod();
            when(roleService.find(any(Filter.class))).thenReturn(mock(Page.class));
        }

        @Test
        void find() {
            Filter f = new Filter();
            UserDetail u = mock(UserDetail.class);
            when(u.getUserId()).thenReturn("id");
            Assertions.assertDoesNotThrow(() -> roleService.find(f, u));
            verify(roleService).find(f);
        }
        @Test
        void find1() {
            Filter f = new Filter();
            f.setWhere(null);
            UserDetail u = mock(UserDetail.class);
            when(u.getUserId()).thenReturn("id");
            Assertions.assertDoesNotThrow(() -> roleService.find(f, u));
            verify(roleService).find(f);
        }
    }

    @Nested
    class testDeleteById {
        ObjectId id = mock(ObjectId.class);
        UserDetail userDetail = mock(UserDetail.class);
        @Test
        void deleteByIdNormal() {
            RoleDto roleDto = mock(RoleDto.class);
            when(roleService.findById(id)).thenReturn(roleDto);
            when(roleDto.getId()).thenReturn(id);
            when(roleDto.getName()).thenReturn("test");
            when(userDetail.getUserId()).thenReturn("66d12b5970697e40b53f179e");
            when(repository.deleteById(id, userDetail)).thenReturn(true);
            doCallRealMethod().when(roleService).deleteById(id, userDetail);
            boolean actual = roleService.deleteById(id, userDetail);
            assertTrue(actual);
            verify(userLogService, new Times(1)).addUserLog(any(Modular.class), any(Operation.class), anyString(), anyString(), anyString());
        }
    }

    @Nested
    class updateByWhereTest {
        Where where = mock(Where.class);
        Document doc = mock(Document.class);
        UserDetail userDetail = mock(UserDetail.class);
        @Test
        void testUpdateByWhereNormal() {
            List<RoleDto> roles = new ArrayList<>();
            RoleDto roleDto = mock(RoleDto.class);
            roles.add(roleDto);
            when(userDetail.getUserId()).thenReturn("66d12b5970697e40b53f179e");
            when(roleDto.getId()).thenReturn(new ObjectId());
            when(roleDto.getName()).thenReturn("test");
            when(roleService.findAll(where)).thenReturn(roles);
            UpdateResult updateResult = mock(UpdateResult.class);
            when(repository.filterToQuery(any(Filter.class))).thenReturn(mock(Query.class));
            when(repository.update(any(Query.class), any(Update.class), any(UserDetail.class))).thenReturn(updateResult);
            when(updateResult.getModifiedCount()).thenReturn(1L);
            doCallRealMethod().when(roleService).updateByWhere(where, doc, userDetail);
            roleService.updateByWhere(where, doc, userDetail);
            verify(userLogService, new Times(1)).addUserLog(any(Modular.class), any(Operation.class), anyString(), anyString(), anyString());
        }
    }

    @Nested
    class saveTest {
        RoleDto dto;
        UserDetail userDetail;
        @BeforeEach
        void beforeEach() {
            dto = new RoleDto();
            userDetail = mock(UserDetail.class);
            doCallRealMethod().when(roleService).save(dto, userDetail);
            Class<RoleEntity> entityClass = RoleEntity.class;
            ReflectionTestUtils.setField(roleService, "entityClass", entityClass);
        }
        @Test
        void saveForUpdate() {
            try (MockedStatic<BeanUtils> mb = Mockito
                    .mockStatic(BeanUtils.class)) {
                mb.when(()->BeanUtils.copyProperties(any(RoleEntity.class), any(RoleDto.class))).thenAnswer(invocationOnMock -> {return null;});
                dto.setId(new ObjectId());
                RoleEntity roleEntity = mock(RoleEntity.class);
                when(roleService.convertToEntity(any(Class.class), any(RoleDto.class))).thenReturn(roleEntity);
                when(repository.save(roleEntity, userDetail)).thenReturn(roleEntity);
                roleService.save(dto, userDetail);
                verify(userLogService, new Times(1)).addUserLog(Modular.ROLE, Operation.UPDATE, userDetail.getUserId(), dto.getId().toString(), dto.getName());
            }
        }
    }
}