package com.tapdata.tm.taskinspect.service;

import com.tapdata.tm.base.dto.Field;
import com.tapdata.tm.base.dto.Where;
import com.tapdata.tm.base.exception.BizException;
import com.tapdata.tm.commons.task.dto.TaskDto;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.task.service.TaskService;
import com.tapdata.tm.taskinspect.TaskInspectConfig;
import com.tapdata.tm.taskinspect.dto.TaskInspectDto;
import com.tapdata.tm.taskinspect.repository.TaskInspectRepository;
import com.tapdata.tm.utils.MessageUtil;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Locale;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TaskInspectServiceTest {

    @Mock
    TaskInspectRepository mockTaskInspectRepository;

    @Mock
    TaskService mockTaskService;

    @InjectMocks
    TaskInspectService taskInspectService;

    UserDetail mockUserDetail;
    ObjectId mockTaskId;
    TaskInspectConfig config;

    @BeforeEach
    void setUp() {
        mockUserDetail = mock(UserDetail.class);
        mockTaskId = new ObjectId();
        config = new TaskInspectConfig().init(-1);
    }

    @Nested
    class updateTest {

        @Test
        void testUpdate_Success() throws Throwable {
            // 预定义
            TaskInspectDto mockDto = new TaskInspectDto();
            mockDto.setId(mockTaskId);
            mockDto.setMode(config.getMode());
            mockDto.setIntelligent(config.getIntelligent());
            mockDto.setCustom(config.getCustom());

            TaskInspectService spyService = spy(taskInspectService);
            doReturn(mockDto).when(spyService).upsertByWhere(any(Where.class), any(TaskInspectDto.class), any(UserDetail.class));
            doNothing().when(spyService).sendConfig2Engine(any(ObjectId.class), any(TaskInspectConfig.class));

            // 行为
            TaskInspectDto result = spyService.update(mockTaskId, mockUserDetail, config);

            // 预期检查
            assertNotNull(result);
            assertEquals(mockTaskId, result.getId());
            assertEquals(config.getMode(), result.getMode());
            assertEquals(config.getIntelligent(), result.getIntelligent());
            assertEquals(config.getCustom(), result.getCustom());
            verify(spyService).upsertByWhere(any(Where.class), eq(mockDto), eq(mockUserDetail));
            verify(spyService).sendConfig2Engine(any(ObjectId.class), any(TaskInspectConfig.class));
        }

        @Test
        void testUpdate_Exception() throws Throwable {

            try (MockedStatic<MessageUtil> messageUtilMockedStatic = mockStatic(MessageUtil.class)) {
                messageUtilMockedStatic.when(() -> MessageUtil.getMessage(any(Locale.class), anyString(), anyString())).thenReturn("test-error");

                // 预定义
                TaskInspectService spyService = spy(taskInspectService);
                doReturn(mock(TaskInspectDto.class)).when(spyService).upsertByWhere(any(Where.class), any(TaskInspectDto.class), any(UserDetail.class));
                doThrow(new BizException("TaskInspect.UpdateAgentConfigError", new Exception("Error"))).when(spyService).sendConfig2Engine(any(ObjectId.class), any(TaskInspectConfig.class));

                // 行为和预期检查
                assertThrows(BizException.class, () -> spyService.update(mockTaskId, mockUserDetail, config));
                verify(spyService).upsertByWhere(any(Where.class), any(TaskInspectDto.class), eq(mockUserDetail));
            }
        }
    }

    @Nested
    class sendConfig2EngineTest {

        @Test
        void testSendConfig2Engine_TaskInEngineStatus() throws Throwable {
            // 预定义
            TaskDto mockTaskDto = new TaskDto();
            mockTaskDto.setStatus(TaskDto.STATUS_RUNNING);
            mockTaskDto.setAgentId("agentId");

            doReturn(mockTaskDto).when(mockTaskService).findById(eq(mockTaskId), any(Field.class));

            // 行为
            taskInspectService.sendConfig2Engine(mockTaskId, config);

            // 预期检查
            verify(mockTaskService).callEngineRpc(eq("agentId"), eq(Void.class), eq("TaskInspectRemoteService"), eq("updateConfig"), eq(mockTaskId.toHexString()), eq(config));
        }

        @Test
        void testSendConfig2Engine_TaskNotInEngineStatus() throws Throwable {
            // 预定义
            TaskDto mockTaskDto = new TaskDto();
            mockTaskDto.setStatus("STOPPED");
            mockTaskDto.setAgentId("agentId");

            doReturn(mockTaskDto).when(mockTaskService).findById(eq(mockTaskId), any(Field.class));

            // 行为
            taskInspectService.sendConfig2Engine(mockTaskId, config);

            // 预期检查
            verify(mockTaskService, never()).callEngineRpc(anyString(), eq(Void.class), eq("TaskInspectRemoteService"), eq("updateConfig"), eq(mockTaskId.toHexString()), eq(config));
        }

        @Test
        void testSendConfig2Engine_Exception() throws Throwable {
            // 预定义
            TaskDto mockTaskDto = new TaskDto();
            mockTaskDto.setStatus(TaskDto.STATUS_RUNNING);
            mockTaskDto.setAgentId("agentId");

            doReturn(mockTaskDto).when(mockTaskService).findById(eq(mockTaskId), any(Field.class));
            doThrow(new RuntimeException("Error")).when(mockTaskService).callEngineRpc(eq("agentId"), eq(Void.class), eq("TaskInspectRemoteService"), eq("updateConfig"), eq(mockTaskId.toHexString()), eq(config));

            try (MockedStatic<MessageUtil> messageUtilMockedStatic = mockStatic(MessageUtil.class)) {
                messageUtilMockedStatic.when(()-> MessageUtil.getMessage(any(Locale.class), anyString(), anyString())).thenReturn("test-error");

                // 行为和预期检查
                assertThrows(BizException.class, () -> taskInspectService.sendConfig2Engine(mockTaskId, config));
                verify(mockTaskService).callEngineRpc(eq("agentId"), eq(Void.class), eq("TaskInspectRemoteService"), eq("updateConfig"), eq(mockTaskId.toHexString()), eq(config));
            }
        }
    }
}
