package com.tapdata.tm.taskinspect.controller;

import com.tapdata.tm.base.controller.BaseController;
import com.tapdata.tm.base.dto.Field;
import com.tapdata.tm.base.dto.ResponseMessage;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.permissions.DataPermissionHelper;
import com.tapdata.tm.permissions.constants.DataPermissionActionEnums;
import com.tapdata.tm.permissions.constants.DataPermissionDataTypeEnums;
import com.tapdata.tm.permissions.constants.DataPermissionMenuEnums;
import com.tapdata.tm.task.service.TaskService;
import com.tapdata.tm.taskinspect.dto.TaskInspectResultsDto;
import com.tapdata.tm.taskinspect.service.TaskInspectResultsService;
import com.tapdata.tm.taskinspect.vo.ResultOperationsVo;
import com.tapdata.tm.utils.MongoUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;


/**
 * 任务内校验-差异结果
 *
 * <AUTHOR> href="mailto:<EMAIL>">Harsen</a>
 * @version v1.0 2025/1/17 20:12 Create
 */
@Slf4j
@Setter(onMethod_ = {@Autowired})
@RestController
@RequestMapping("/api/task-inspect-results")
@Tag(name = "TaskInspect", description = "任务内校验差异结果操作接口")
public class TaskInspectResultsController extends BaseController {
    private static final String EX_CODE_NO_PERMISSION = "NoPermission";

    private TaskService taskService;
    private TaskInspectResultsService service;

    private <T> T throwNoPermission() {
        throw new RuntimeException(EX_CODE_NO_PERMISSION);
    }

    private <T> T checkAndCallback(HttpServletRequest request, UserDetail userDetail, ObjectId id, DataPermissionActionEnums action, Supplier<T> supplier) {
        id = Optional.ofNullable(DataPermissionHelper.signDecode(request, id.toHexString())).map(MongoUtils::toObjectId).orElse(id);
        return DataPermissionHelper.checkOfQuery(
            userDetail,
            DataPermissionDataTypeEnums.Task,
            action,
            taskService.dataPermissionFindById(id, new Field()),
            (dto) -> DataPermissionMenuEnums.ofTaskSyncType(dto.getSyncType()),
            supplier,
            this::throwNoPermission
        );
    }

    private void checkOfTaskId(HttpServletRequest request, UserDetail userDetail, String taskId, DataPermissionActionEnums action) {
        if (null == taskId
            || !ObjectId.isValid(taskId)
            || !checkAndCallback(request, userDetail, new ObjectId(taskId), action, () -> true)) {
            throwNoPermission();
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get detail by id")
    public ResponseMessage<TaskInspectResultsDto> get(HttpServletRequest request, @PathVariable(name = "id") String id) {
        if (ObjectId.isValid(id)) {
            ObjectId oid = new ObjectId(id);
            TaskInspectResultsDto result = service.findById(oid);
            if (null != result) {
                UserDetail userDetail = getLoginUser();
                checkOfTaskId(request, userDetail, result.getTaskId(), DataPermissionActionEnums.View);
                return success(result);
            }
        }
        return throwNoPermission();
    }

    @GetMapping("/{id}/operations")
    @Operation(summary = "Get operations by id")
    public ResponseMessage<List<ResultOperationsVo>> getOperations(HttpServletRequest request, @PathVariable(name = "id") String id) {
        if (ObjectId.isValid(id)) {
            ObjectId oid = new ObjectId(id);
            TaskInspectResultsDto result = service.findById(oid);
            if (null != result) {
                UserDetail userDetail = getLoginUser();
                checkOfTaskId(request, userDetail, result.getTaskId(), DataPermissionActionEnums.View);
                return success(result.getOperations());
            }
        }
        return throwNoPermission();
    }
}
