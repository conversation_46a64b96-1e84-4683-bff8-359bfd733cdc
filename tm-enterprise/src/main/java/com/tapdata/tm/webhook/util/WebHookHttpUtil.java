package com.tapdata.tm.webhook.util;

import cn.hutool.core.collection.CollUtil;
import com.tapdata.tm.webhook.entity.HookOneHistory;
import com.tapdata.tm.webhook.enums.PingResult;
import com.tapdata.tm.webhook.enums.WebHookHistoryStatus;
import com.tapdata.tm.webhook.server.WebHookHttpUtilService;
import io.tapdata.entity.simplify.TapSimplify;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpStatus;
import org.apache.http.StatusLine;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

@Slf4j
@Service
@Primary
public class WebHookHttpUtil implements WebHookHttpUtilService {
    protected static final String REQUEST_URL = "Request-URL";
    protected static final String REQUEST_METHOD = "Request-Method";
    protected static final String UTF_8 = "utf-8";
    protected static final String CONTENT_TYPE_JSON = "application/json";
    protected static final String CONTENT_TYPE = "Content-Type";

    @Setter
    @Getter
    @Value("${webhook.http.connectTimout:3000}")
    private int connectTimout; //ms
    @Setter
    @Getter
    @Value("${webhook.http.connectionRequestTimeout:3000}")
    private int connectionRequestTimeout; //ms
    @Setter
    @Getter
    @Value("${webhook.http.retryTimes:3}")
    private int httpRetryTimes;

    @Override
    public boolean checkURL(String url) {
        if (StringUtils.isBlank(url)) {
            log.warn("URL is invalid");
            return false;
        }
        try {
            new URL(url);
            return true;
        } catch (MalformedURLException e) {
            log.warn(e.getMessage());
        }
        return false;
    }

    @Override
    public HookOneHistory post(String url,
                               Map<String, Object> head,
                               Map<String, Object> urlParam,
                               Object body) {
        return httpControl(getHookOneHistoryByParams(url, head, urlParam, body), Utils.toHeads(head));
    }

    public HookOneHistory getHookOneHistoryByParams(String url,
                                                    Map<String, Object> head,
                                                    Map<String, Object> urlParam,
                                                    Object body) {
        HookOneHistory event = new HookOneHistory();
        event.setId(new ObjectId());
        event.setUrl(url);
        String urlParameters = Utils.getUrlParam(urlParam);
        event.setRequestParams(urlParameters);
        String requestBody = Utils.getRequestBody(body);
        event.setRequestBody(requestBody);
        event.setRequestHeaders(Utils.getAllHead(Utils.toHeads(head)));
        return event;
    }

    @Override
    public HookOneHistory post(HookOneHistory history) {
        return httpControl(history, Utils.toHeads(history.getRequestHeaders()));
    }

    protected HookOneHistory httpControl(HookOneHistory history, Header[] headers) {
        int reTryTimes = this.httpRetryTimes < 1 ? 1 : this.httpRetryTimes;
        while (reTryTimes > 0) {
            post(history, headers);
            if (PingResult.SUCCEED.name().equals(history.getStatus())) {
                return history;
            }
            reTryTimes--;
        }
        return history;
    }

    protected HookOneHistory post(HookOneHistory history, Header[] headers) {
        history.setHistoryStatus(WebHookHistoryStatus.ING.name());
        String url = history.getUrl();
        url = Utils.addUrlParamToUrl(url, history.getRequestParams());
        String requestBody = history.getRequestBody();
        if (log.isDebugEnabled()) {
            log.debug("A post request will be send, url：{}, body：{}", url, requestBody);
        }
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);
            Utils.addHeard(httpPost, new BasicHeader(REQUEST_METHOD, "POST"));
            Utils.addHeard(httpPost, new BasicHeader("Accept", "application/json,text/plain,application/*+json,*/*"));
            Utils.addHeard(httpPost, new BasicHeader("Accept-Language", "en-US,en;q=0.5"));
            Utils.addHeard(httpPost, new BasicHeader("Upgrade", "HTTP/2.0,SHTTP/1.3,IRC/6.9,RTA/x11"));
            Utils.addHeardParams(httpPost, headers);
            Utils.addHeard(httpPost, new BasicHeader("X-Event", history.getType()));
            Utils.addHeard(httpPost, new BasicHeader("X-Web-Hook-Event", history.getEventType()));
            Utils.addHeard(httpPost, new BasicHeader("X-Web-Hook-ID", history.getHookId()));
            Utils.addHeard(httpPost, new BasicHeader("X-Web-Hook-Action", "WEBHOOK_HTTP_POST"));
            Utils.addHeard(httpPost, new BasicHeader("User-Agent", "tapdata.io-Hook"));
            Utils.addHeard(httpPost, new BasicHeader("X-Web-Hook-History-ID", history.getId().toHexString()));
            StringEntity entity = new StringEntity(requestBody, ContentType.create(httpPost.getFirstHeader(CONTENT_TYPE).getValue(), StandardCharsets.UTF_8));
            httpPost.setEntity(entity);
            history.setRequestHeaders(Utils.getAllHead(httpPost.getAllHeaders()));
            history.setRequestAt(System.currentTimeMillis());
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(connectTimout)
                    .setConnectionRequestTimeout(connectionRequestTimeout)
                    .build();
            httpPost.setConfig(requestConfig);
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                StatusLine statusLine = response.getStatusLine();
                int statusCode = statusLine.getStatusCode();
                history.setResponseAt(System.currentTimeMillis());
                history.setResponseCode(statusCode);
                history.setResponseStatus(statusLine.toString());
                history.setResponseResult(EntityUtils.toString(response.getEntity(), UTF_8));
                history.setResponseHeaders(Utils.getAllHead(response.getAllHeaders()));
                if (statusCode >= HttpStatus.SC_OK && statusCode < 300) {
                    history.setStatus(PingResult.SUCCEED.name());
                    history.setHistoryStatus(WebHookHistoryStatus.SUCCEED.name());
                    history.setHistoryMessage("Send over");
                    String msg = String.format("Http POST request succeed, url: %s, http code: %s", url, statusCode);
                    log.info(msg);
                } else {
                    history.setStatus(PingResult.FAILED.name());
                    String msg = String.format("Http POST request failed, url: %s, body: %s, http code: %s", url, requestBody, statusCode);
                    history.setHistoryStatus(WebHookHistoryStatus.FAILED.name());
                    history.setHistoryMessage(msg);
                    log.error(msg);
                }
            }
        } catch (Exception e) {
            history.setStatus(PingResult.FAILED.name());
            history.setHistoryStatus(WebHookHistoryStatus.FAILED.name());
            history.setHistoryMessage("Send failed, message" + e.getMessage());
            log.error("Http POST request failed, path: {}, body: {}, message: {}", url, requestBody, e);
        }
        return history;
    }


    public static class Utils {
        private Utils() {
        }

        public static String getAllHead(Header[] headers) {
            if (null == headers || headers.length == 0) {
                return "";
            }
            StringJoiner joiner = new StringJoiner("\n");
            for (Header header : headers) {
                joiner.add(header.toString());
            }
            return joiner.toString();
        }

        public static Header[] toHeads(String heads) {
            if (StringUtils.isBlank(heads)) return new Header[0];
            String[] split = heads.split("\n");
            List<Header> allHeader = new ArrayList<>();
            for (String head : split) {
                int splitIndex = head.indexOf(":");
                if (splitIndex > 0) {
                    Header header = new BasicHeader(
                            head.substring(0, splitIndex).trim(),
                            splitIndex < head.length() - 1 ? head.substring(splitIndex + 1).trim() : ""
                    );
                    allHeader.add(header);
                }
            }
            Header[] hs = new Header[allHeader.size()];
            for (int index = 0; index < allHeader.size(); index++) {
                hs[index] = allHeader.get(index);
            }
            return hs;
        }

        public static Header[] toHeads(Map<String, Object> head) {
            if (null == head) return addContentType(new Header[0], new HashMap<>());
            Header[] hs = new Header[head.size()];
            List<String> hKey = new ArrayList<>(head.keySet());
            for (int index = 0; index < hKey.size(); index++) {
                String key = hKey.get(index).trim();
                String value = String.valueOf(head.get(key)).trim();
                hs[index] = new BasicHeader(key, value);
            }

            return addContentType(hs, head);
        }

        public static Header[] addContentType(Header[] headers, Map<String, Object> head) {
            if (CollUtil.isNotEmpty(head) && head.containsKey(CONTENT_TYPE)) {
                return headers;
            }
            return ArrayUtils.add(headers, new BasicHeader(CONTENT_TYPE, CONTENT_TYPE_JSON));
        }

        public static void addHeardParams(HttpPost post, Header[] head) {
            if (null != head && head.length > 0) {
                for (Header header : head) {
                    if (REQUEST_METHOD.equals(header.getName())) {
                        continue;
                    }
                    addHeard(post, header);
                }
            }
        }

        protected static void addHeard(HttpPost post, Header header) {
            Header[] headers = post.getHeaders(header.getName());
            if (headers.length <= 0) {
                post.addHeader(header);
            }
        }

        public static String addUrlParamToUrl(String url, String urlParams) {
            url = url.trim();
            if (StringUtils.isNotBlank(urlParams)) {
                url = url + (!url.endsWith("?") ? "?" : "") + urlParams;
            }
            return url;
        }

        public static String getUrlParam(Map<String, Object> urlParam) {
            StringJoiner joiner = new StringJoiner("&");
            if (null != urlParam && !urlParam.isEmpty()) {
                urlParam.forEach((key, value) -> joiner.add(key + "=" + (null == value ? "" : String.valueOf(value))));
            }
            return joiner.toString();
        }

        public static String getRequestBody(Object requestBody) {
            if (null == requestBody) return null;
            return TapSimplify.toJson(requestBody);
        }
    }
}
