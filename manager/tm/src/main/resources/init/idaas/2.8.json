[{"createIndexes": "AccessToken", "indexes": [{"name": "_id_", "key": {"_id": 1}}]}, {"createIndexes": "AgentEnvironment", "indexes": [{"name": "_id_", "key": {"_id": 1}}]}, {"createIndexes": "AgentMeasurement", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "tags.taskId_1_tags.type_1_tags.nodeId_1_grnty_1_date_-1", "key": {"tags.taskId": 1, "tags.type": 1, "tags.nodeId": 1, "grnty": 1, "date": -1}, "background": true, "sparse": true}]}, {"createIndexes": "AgentMeasurementV2", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "tags.taskId_1_tags.taskRecordId_1_tags.type_1_grnty_1_date_-1", "key": {"tags.taskId": 1, "tags.taskRecordId": 1, "tags.type": 1, "grnty": 1, "date": -1}, "background": true, "sparse": true}, {"name": "grnty_1_date_1", "key": {"grnty": 1, "date": 1}, "background": true, "sparse": true}]}, {"createIndexes": "ApiCall", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "received_date_1", "key": {"received_date": 1}, "expireAfterSeconds": 7776000, "background": true}]}, {"createIndexes": "ApiServer", "indexes": [{"name": "_id_", "key": {"_id": 1}}]}, {"createIndexes": "App", "indexes": [{"name": "_id_", "key": {"_id": 1}}]}, {"createIndexes": "Application", "indexes": [{"name": "_id_", "key": {"_id": 1}}]}, {"createIndexes": "CdcEvents", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "opDate_1", "key": {"opDate": 1}, "background": true}, {"name": "insertDate_1", "key": {"insertDate": 1}, "background": true}]}, {"createIndexes": "ClusterOperation", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "status_1_uuid_1_type_1_ttl_1", "key": {"type": 1, "uuid": 1, "ttl": 1, "status": 1}, "background": true}]}, {"createIndexes": "ClusterState", "indexes": [{"name": "_id_", "key": {"_id": 1}}]}, {"createIndexes": "ClusterStateLog", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "insertTime_1", "key": {"insertTime": 1}, "expireAfterSeconds": 2592000, "background": true}]}, {"createIndexes": "Connections", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "name_1_user_id_1", "key": {"user_id": 1, "name": 1}, "background": true, "unique": true}]}, {"createIndexes": "CustomerJobLogs", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "dataFlowId_1", "key": {"dataFlowId": 1}, "background": true}, {"name": "level_1", "key": {"level": 1}, "background": true}, {"name": "user_id_1", "key": {"user_id": 1}, "background": true}]}, {"createIndexes": "DataCatalog", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "total_docs_-1", "key": {"total_docs": -1}, "background": true}]}, {"createIndexes": "DataFlowInsight", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "last_updated_1", "key": {"last_updated": 1}, "expireAfterSeconds": 1814400, "background": true}, {"name": "dataFlowId_1_statsType_1_granularity_1_createTime_1_statsTime_1", "key": {"statsType": 1, "dataFlowId": 1, "createTime": 1, "granularity": 1, "statsTime": 1}, "background": true}, {"name": "statsType_1_granularity_1_createTime_-1_dataFlowId_1", "key": {"statsType": 1, "dataFlowId": 1, "createTime": -1, "granularity": 1}, "background": true}, {"name": "statsType_1_granularity_1_statsTime_1__id_1_dataFlowId_1", "key": {"statsType": 1, "dataFlowId": 1, "granularity": 1, "_id": 1, "statsTime": 1}, "background": true}]}, {"createIndexes": "DataFlowsDebug", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "__tapd8.dataFlowId_1___tapd8.stageId_1___tapd8.tableName_1_createTime_1", "key": {"__tapd8.dataFlowId": 1, "__tapd8.tableName": 1, "createTime": 1, "__tapd8.stageId": 1}, "background": true}, {"name": "createTime_1", "key": {"createTime": 1}, "expireAfterSeconds": 604800, "background": true}]}, {"createIndexes": "DatabaseTypes", "indexes": [{"name": "_id_", "key": {"_id": 1}}]}, {"createIndexes": "DeleteCaches", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "timestamp_1", "key": {"timestamp": 1}, "expireAfterSeconds": 604800, "background": true}, {"name": "mongodbUri_1_collectionName_1_data._id_1_timestamp_1", "key": {"data._id": 1, "collectionName": 1, "mongodbUri": 1, "timestamp": 1}, "background": true}]}, {"createIndexes": "DrsScheduleLock", "indexes": [{"name": "_id_", "key": {"_id": 1}}]}, {"createIndexes": "Events", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "groupId_1", "key": {"groupId": 1}, "partialFilterExpression": {"groupId": {"$exists": true}}, "background": true, "unique": true}, {"name": "event_status_1_sendGroupTTL_1_failed_result.retry_1_failed_result.next_retry_1", "key": {"failed_result.retry": 1, "sendGroupTTL": 1, "failed_result.next_retry": 1, "event_status": 1}, "background": true}]}, {"createIndexes": "InconsistentData", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "verificationJobId_1_validateResult_1_nextValidateTime_1", "key": {"nextValidateTime": 1, "verificationJobId": 1, "validateResult": 1}, "partialFilterExpression": {"validateResult": "retry"}, "background": true}]}, {"createIndexes": "Task", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "index_task_name", "key": {"name": 1}, "background": true}, {"name": "index_task_desc", "key": {"desc": 1}, "background": true}, {"name": "index_task_agent_id", "key": {"agentId": 1}, "background": true}, {"name": "index_task_start_time", "key": {"startTime": 1}, "background": true}, {"name": "index_task_create_time", "key": {"createTime": 1}, "background": true}, {"name": "index_task_stop_time", "key": {"stopTime": 1}, "background": true}, {"name": "index_task_ping_time", "key": {"pingTime": 1}, "background": true}, {"name": "index_task_dag_nodes_connectionId", "key": {"dag.nodes.connectionId": 1}, "background": true}]}, {"createIndexes": "Insights", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "stats_name_1_stats_granularity_1", "key": {"stats_granularity": 1, "stats_name": 1}, "background": true}, {"name": "stats_name_1_data.name_1", "key": {"data.name": 1, "stats_name": 1}, "background": true}, {"name": "meta_type_1_is_deleted_1_source.connection_type_1", "key": {"source.connection_type": 1, "meta_type": 1, "is_deleted": 1}, "background": true}, {"name": "stats_name_1_stats_time_1_api_id_1", "key": {"api_id": 1, "stats_name": 1, "stats_time": 1}, "background": true}, {"name": "stats_name_1_stats_granularity_1_stats_time_-1_data.total_records_-1", "key": {"stats_granularity": 1, "stats_name": 1, "data.total_records": -1, "stats_time": -1}, "background": true}]}, {"createIndexes": "InspectDetails", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "ttlTime_1", "key": {"ttlTime": 1}, "expireAfterSeconds": 15552000, "background": true}]}, {"createIndexes": "InspectResult", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "ttlTime_1", "key": {"ttlTime": 1}, "expireAfterSeconds": 15552000, "background": true}]}, {"createIndexes": "InviteCode", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "inviteCode_1", "key": {"inviteCode": 1}, "background": true}, {"name": "quota_1", "key": {"quota": 1}, "background": true}]}, {"createIndexes": "JavascriptFunctions", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "function_name_1", "key": {"function_name": 1}, "background": true, "unique": true}]}, {"createIndexes": "JobDDLHistories", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "jobId_1_sourceConnId_1", "key": {"sourceConnId": 1, "jobId": 1}, "background": true}]}, {"createIndexes": "Jobs", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "sourceConnection.database_name_1", "key": {"sourceConnection.database_name": 1}, "background": true}, {"name": "sourceConnection.database_type_1", "key": {"sourceConnection.database_type": 1}, "background": true}, {"name": "targetConnection.name_1", "key": {"targetConnection.name": 1}, "background": true}, {"name": "status_1", "key": {"status": 1}, "background": true}, {"name": "dataFlowId_1", "key": {"dataFlowId": 1}, "background": true}, {"name": "name_1_user_id_1", "key": {"user_id": 1, "name": 1}, "background": true}]}, {"createIndexes": "LibSupporteds", "indexes": [{"name": "_id_", "key": {"_id": 1}}]}, {"createIndexes": "LineageGraph", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "v_1_value.type_1", "key": {"value.type": 1, "v": 1}, "background": true}, {"name": "v_1_w_1_value.type_1", "key": {"value.type": 1, "v": 1, "w": 1}, "background": true}]}, {"createIndexes": "Logs", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "date_1", "key": {"date": 1}, "expireAfterSeconds": 432000, "background": true}, {"name": "level_text_message_text", "key": {"_fts": "text", "_ftsx": 1}, "textIndexVersion": 3, "background": true, "weights": {"level": 1, "message": 1}, "default_language": "english", "language_override": "language"}, {"name": "contextMap.jobId_1_millis_1", "key": {"contextMap.jobId": 1, "millis": 1}, "background": true}, {"name": "contextMap.dataFlowId_1_millis_1", "key": {"contextMap.dataFlowId": 1, "millis": 1}, "background": true}]}, {"createIndexes": "MeasureLock", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "createdTime_1", "key": {"createdTime": 1}, "expireAfterSeconds": 30, "background": true, "sparse": true}]}, {"createIndexes": "Message", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "userId_1_read_1", "key": {"read": 1, "userId": 1}, "background": true}, {"name": "groupId_1_read_1", "key": {"read": 1, "groupId": 1}, "background": true}, {"name": "userId_1_createTime_1", "key": {"userId": 1, "createTime": 1}, "background": true}]}, {"createIndexes": "MessageQueue", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "last_updated_1", "key": {"last_updated": 1}, "expireAfterSeconds": 2592000, "background": true}]}, {"createIndexes": "MetadataDefinition", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "value_1", "key": {"value": 1}, "background": true, "unique": true}]}, {"createIndexes": "MetadataInstances", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "qualified_name_1", "key": {"qualified_name": 1}, "background": true, "unique": true}, {"name": "is_deleted_1_meta_type_1_create_time_1", "key": {"create_time": 1, "meta_type": 1, "is_deleted": 1}, "background": true}, {"name": "last_updated_1_name_1", "key": {"last_updated": 1, "name": 1}, "background": true}, {"name": "original_name_1", "key": {"original_name": 1}, "background": true}, {"name": "name_1_original_name_1", "key": {"original_name": 1, "name": 1}, "background": true}, {"name": "is_deleted_1_databaseId_1_schemaVersion_1", "key": {"schemaVersion": 1, "is_deleted": 1, "databaseId": 1}, "background": true}, {"name": "meta_type_1__id_1_source._id_1", "key": {"source._id": 1, "meta_type": 1, "_id": 1}, "background": true}, {"name": "source.id_1_meta_type_1", "key": {"source.id": 1, "meta_type": 1}, "background": true}, {"name": "databaseId_1_is_deleted_1_meta_type_1", "key": {"meta_type": 1, "is_deleted": 1, "databaseId": 1}, "background": true}, {"name": "index_meta_data_instances_create_time", "key": {"createTime": 1}, "background": true}, {"name": "index_meta_data_instances_source_database_name", "key": {"source.database_name": 1}, "background": true}]}, {"createIndexes": "MetadataTransformer", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "dataFlowId_1_version_-1", "key": {"version": -1, "dataFlowId": 1}, "background": true}, {"name": "stageId_1_version_1", "key": {"version": 1, "stageId": 1}, "background": true}, {"name": "version_1", "key": {"version": 1}, "background": true}]}, {"createIndexes": "MetadataTransformerItem", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "sinkStageId_1_invalid_1", "key": {"invalid": 1, "sinkStageId": 1}, "background": true}, {"name": "sinkStageId_1_version_1", "key": {"version": 1, "sinkStageId": 1}, "background": true}, {"name": "sinkStageId_1_sourceQualifiedName_1", "key": {"sourceQualifiedName": 1, "sinkStageId": 1}, "background": true}, {"name": "dataFlowId_1_invalid_1", "key": {"dataFlowId": 1, "invalid": 1}, "background": true}, {"name": "dataFlowId_1_version_1", "key": {"version": 1, "dataFlowId": 1}, "background": true}]}, {"createIndexes": "monitoringLogs", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "taskId_1_taskRecordId_1_nodeId_1_level_1_data_-1", "key": {"taskId": 1, "taskRecordId": 1, "nodeId": 1, "level": 1, "date": -1}, "background": true, "sparse": true}]}, {"createIndexes": "Permission", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "name_1", "key": {"name": 1}, "unique": true}]}, {"createIndexes": "Role", "indexes": [{"name": "_id_", "key": {"_id": 1}}]}, {"createIndexes": "RoleMapping", "indexes": [{"name": "_id_", "key": {"_id": 1}}]}, {"createIndexes": "ScheduleTasks", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "last_updated_1", "key": {"last_updated": 1}, "expireAfterSeconds": 2592000, "background": true}, {"name": "task_name_1_last_updated_1", "key": {"task_name": 1, "last_updated": 1}, "background": true}]}, {"createIndexes": "<PERSON><PERSON><PERSON>", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "name_1", "key": {"name": 1}, "background": true, "unique": true}, {"name": "table_name_1", "key": {"table_name": 1}, "background": true}]}, {"createIndexes": "Settings", "indexes": [{"name": "_id_", "key": {"_id": 1}}]}, {"createIndexes": "SubTaskProgress", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "subTaskId_1_srcTableName_1_tgtTableName_1_srcNodeId_1_tgtNodeId_1_type_1", "key": {"srcTableName": 1, "tgtTableName": 1, "type": 1, "subTaskId": 1, "tgtNodeId": 1, "srcNodeId": 1}, "background": true}]}, {"createIndexes": "TaskAutoInspectResults", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "_idx_taskId", "key": {"taskId": 1}}, {"name": "_idx_originalTableName", "key": {"originalTableName": 1}}, {"name": "_idx_originalKeymap", "key": {"originalKeymap": 1}}, {"name": "_idx_unique", "key": {"taskId": 1, "originalTableName": 1, "originalKeymap": 1}, "unique": true}]}, {"createIndexes": "TaskHistories", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "create_time_1", "key": {"create_time": 1}, "expireAfterSeconds": 2592000, "background": true}, {"name": "task_id_1_create_time_-1", "key": {"create_time": -1, "task_id": 1}, "background": true}]}, {"createIndexes": "User", "indexes": [{"name": "_id_", "key": {"_id": 1}}]}, {"createIndexes": "UserLogs", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "user_id_1_type_1_createTime_-1", "key": {"type": 1, "user_id": 1, "createTime": -1}, "background": true}, {"name": "last_updated_1", "key": {"last_updated": 1}, "expireAfterSeconds": 7776000, "background": true}]}, {"createIndexes": "Workers", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "worker_type_1_process_id_1", "key": {"process_id": 1, "worker_type": 1}, "partialFilterExpression": {"worker_type": "tapdata-manager"}, "background": true, "unique": true}, {"name": "ping_date_1", "key": {"ping_date": 1}, "partialFilterExpression": {"worker_type": "system"}, "expireAfterSeconds": 1296000, "background": true}, {"name": "worker_type_1_worker_ip_1_ping_date_-1", "key": {"worker_type": 1, "worker_ip": 1, "ping_date": -1}, "background": 1}]}, {"createIndexes": "fs.chunks", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "files_id_1_n_1", "key": {"n": 1, "files_id": 1}, "unique": true}]}, {"createIndexes": "fs.files", "indexes": [{"name": "_id_", "key": {"_id": 1}}, {"name": "filename_1_uploadDate_1", "key": {"filename": 1, "uploadDate": 1}}]}, {"insert": "ApiServer", "documents": [{"_id": {"$oid": "5d0c286d81baf819b6f014bd"}, "clientName": "Default APIServer", "clientURI": "http://127.0.0.1:3080", "processId": "f3ebe1b88623ca4f933af4e27f4075a0", "user_id": {"$oid": "62bc5008d4958d013d97c7a6"}}]}, {"insert": "Settings", "documents": [{"_id": "1", "category": "Log", "category_sort": 1, "default_value": "info", "documentation": "Enter jobs log level, error/warn/info/debug/trace", "enums": ["error", "warn", "info", "debug", "trace"], "hot_reloading": true, "key": "logLevel", "key_label": "Log level", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "user_visible": true, "value": "info"}, {"_id": "10", "category": "SMTP", "category_sort": 1, "default_value": " ", "documentation": "SMTP Server Port", "hot_reloading": true, "key": "smtp.server.port", "key_label": "SMTP Server Port", "last_update": 0, "last_update_by": "", "mask": "###", "scope": "global", "sort": 5, "user_visible": true, "value": ""}, {"_id": "100", "category": "share_cdc", "category_sort": 20, "default_value": "<PERSON><PERSON>", "documentation": "share_cdc_persistence_mode_doc", "enums": ["<PERSON><PERSON>", "MongoDB", "RocksDB"], "hot_reloading": false, "key": "share_cdc_persistence_mode", "key_label": "share_cdc_persistence_mode", "scope": "global", "sort": 1, "user_visible": false, "value": "<PERSON><PERSON>"}, {"_id": "101", "category": "share_cdc", "category_sort": 20, "default_value": "10000", "documentation": "share_cdc_persistence_memory_size_doc", "hot_reloading": false, "key": "share_cdc_persistence_memory_size", "key_label": "share_cdc_persistence_memory_size", "scope": "global", "sort": 2, "user_visible": false, "value": "10000"}, {"_id": "102", "category": "share_cdc", "category_sort": 20, "default_value": "", "documentation": "share_cdc_persistence_mongodb_uri_db_doc", "hot_reloading": false, "key": "share_cdc_persistence_mongodb_uri_db", "key_label": "share_cdc_persistence_mongodb_uri_db", "scope": "global", "sort": 3, "user_visible": false, "value": ""}, {"_id": "103", "category": "share_cdc", "category_sort": 20, "default_value": "tap_share_cdc_logs", "documentation": "share_cdc_persistence_mongodb_collection_doc", "hot_reloading": false, "key": "share_cdc_persistence_mongodb_collection", "key_label": "share_cdc_persistence_mongodb_collection", "scope": "global", "sort": 4, "user_visible": false, "value": ""}, {"_id": "104", "category": "share_cdc", "category_sort": 20, "default_value": "./rocksdb-data/", "documentation": "share_cdc_persistence_rocksdb_path_doc", "hot_reloading": false, "key": "share_cdc_persistence_rocksdb_path", "key_label": "share_cdc_persistence_rocksdb_path", "scope": "global", "sort": 5, "user_visible": false, "value": ""}, {"_id": "105", "category": "share_cdc", "category_sort": 20, "default_value": "true", "hot_reloading": false, "key": "share_cdc_enable", "key_label": "share_cdc_enable", "scope": "global", "sort": 0, "user_visible": false, "value": "true"}, {"_id": "106", "category": "share_cdc", "category_sort": 20, "default_value": "3", "hot_reloading": false, "key": "share_cdc_ttl_day", "key_label": "share_cdc_ttl_day", "scope": "global", "sort": 0, "user_visible": false, "value": "3"}, {"_id": "11", "category": "SMTP", "category_sort": 1, "default_value": " ", "documentation": "SMTP Server User", "hot_reloading": true, "key": "smtp.server.user", "key_label": "SMTP Server User", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "user_visible": true, "value": ""}, {"_id": "12", "category": "SMTP", "category_sort": 1, "default_value": " ", "documentation": "SMTP Server password", "hot_reloading": true, "key": "smtp.server.password", "key_label": "SMTP Server password", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 2, "user_visible": true, "value": ""}, {"_id": "13", "category": "SMTP", "category_sort": 1, "default_value": " ", "documentation": "Email Receivers，Multiple separated by semicolons", "hot_reloading": true, "key": "email.receivers", "key_label": "Email Receivers", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 7, "user_visible": true, "value": ""}, {"_id": "14", "category": "Download", "category_sort": 5, "default_value": "false", "documentation": "Show download page in dashboard or not", "enums": ["true", "false"], "hot_reloading": true, "key": "download.showPage", "key_label": "Show Page", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "user_visible": false, "value": "false"}, {"_id": "15", "category": "Users", "category_sort": 6, "default_value": "self-signup", "documentation": "Set for sign up.Value as 'disabled' for disabling self-signup; Value as 'self-signup' for enabling self-signup; Value as 'manual-approval' for enabling self-signup, but need to be approved by the administrator", "enums": ["self-signup", "manual-approval", "disabled"], "hot_reloading": true, "key": "users.registery", "key_label": "User Registery", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "user_visible": false, "value": "self-signup"}, {"_id": "16", "category": "DR_Rehearsal", "category_sort": 6, "default_value": "false", "documentation": "hint Enable DR Rehearsal", "enums": ["true", "false"], "hot_reloading": true, "key": "enableDR", "key_label": "Enable DR Rehearsal", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "user_visible": true, "value": "false"}, {"_id": "17", "category": "DR_Rehearsal", "category_sort": 6, "default_value": "/usr/local/bin", "documentation": "hint Mongod path", "hot_reloading": true, "key": "mongod<PERSON><PERSON>", "key_label": "Mongod path", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "user_visible": true, "value": "/usr/local/bin"}, {"_id": "18", "category": "DR_Rehearsal", "category_sort": 6, "default_value": "tapdata", "documentation": "hint SSH User", "hot_reloading": true, "key": "sshUser", "key_label": "SSH User", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 2, "user_visible": true, "value": "tapdata"}, {"_id": "19", "category": "DR_Rehearsal", "category_sort": 6, "default_value": "22", "documentation": "hint SSH Port", "hot_reloading": true, "key": "sshPort", "key_label": "SSH Port", "last_update": 0, "last_update_by": "", "mask": "##", "scope": "global", "sort": 3, "user_visible": true, "value": "22"}, {"_id": "21", "category": "Download", "category_sort": 6, "default_value": "false", "documentation": "Download Agent Page", "enums": ["true", "false"], "hot_reloading": true, "key": "download.showAgentPage", "key_label": "Download Agent Page", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 3, "user_visible": false, "value": "false"}, {"_id": "22", "category": "Api", "category_sort": 6, "default_value": "false", "documentation": "API Design Page", "enums": ["true", "false"], "hot_reloading": true, "key": "Api.design", "key_label": "Data publish display.", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 3, "user_visible": false, "value": "true"}, {"_id": "23", "category": "Background_Analytics", "category_sort": 6, "default_value": "3600", "documentation": "quality Analysis Interval", "hot_reloading": true, "key": "qualityAnalysisInterval", "key_label": "Data quality analysis frequency", "mask": "####", "scope": "global", "sort": 2, "user_visible": true, "value": "3600"}, {"_id": "24", "category": "Background_Analytics", "category_sort": 6, "default_value": "30", "documentation": "dashboard Analysis Interval", "hot_reloading": true, "key": "dashboardAnalysisInterval", "key_label": "Dashboard data analysis frequency", "mask": "##", "scope": "global", "sort": 2, "user_visible": false, "value": "30"}, {"_id": "26", "category": "UI", "default_value": "true", "enums": ["true", "false"], "hot_reloading": false, "key": "UI.showLanguageOption", "scope": "global", "user_visible": false, "value": "true"}, {"_id": "27", "category": "UI", "default_value": "en", "hot_reloading": false, "key": "UI.defaultLanguage", "scope": "global", "user_visible": false, "value": "en"}, {"_id": "28", "category": "UI", "default_value": "logo.svg", "key": "UI.productLogoFile", "scope": "global", "value": "logo.svg"}, {"_id": "29", "category": "UI", "default_value": "Tapdata", "key": "UI.productName", "scope": "global", "value": "Tapdata"}, {"_id": "30", "category": "UI", "default_value": "static/docs/index.html", "key": "UI.helpURL", "scope": "global", "value": "https://docs.tapdata.io"}, {"_id": "31", "category": "Log", "category_sort": 1, "default_value": "20", "documentation": "Filter the interval between duplicate logs (seconds).", "hot_reloading": true, "key": "log4jFilterInterval", "key_label": "Log filter interval", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 2, "user_visible": true, "value": "20"}, {"_id": "32", "category": "Log", "category_sort": 1, "default_value": "true", "documentation": "", "enums": ["true", "false"], "hot_reloading": true, "key": "log4jFilterIsFormat", "key_label": "Log filter is formatted.", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 3, "user_visible": false, "value": "true"}, {"_id": "33", "category": "System", "hot_reloading": false, "key": "buildProfile", "scope": "global", "user_visible": false, "value": "DAAS"}, {"_id": "34", "category": "UI", "default_value": "Tapdata", "key": "UI.productTitle", "scope": "global", "value": "Tapdata"}, {"_id": "38", "category": "SYBASE", "default_value": "100000", "key": "sybaseCDCBufferLimit", "scope": "global", "user_visible": false, "value": "100000"}, {"_id": "39", "category": "SYBASE", "default_value": "3000", "key": "sybaseCDCOffsetAhead", "scope": "global", "user_visible": false, "value": "3000"}, {"_id": "40", "category": "LIB", "default_value": "initialSync,increamentalSync,stats,syncProgress,dataValidate,onData,isMerge,customMapping,dbCloneCDC,sourceAndTarget", "key": "libSupported", "scope": "global", "user_visible": false, "value": "initialSync,increamentalSync,stats,syncProgress,dataValidate,onData,isMerge,customMapping,dbCloneCDC,sourceAndTarget"}, {"_id": "42", "category": "UDP", "category_sort": 7, "default_value": "test", "documentation": "Enter udp source header.", "hot_reloading": true, "key": "udp.header", "key_label": "Header", "scope": "global", "sort": 1, "user_visible": false, "value": "test"}, {"_id": "44", "category": "Job", "category_sort": 8, "default_value": "false", "documentation": "Mongodb target create date docs", "enums": ["true", "false"], "hot_reloading": true, "key": "mongodb.ts", "key_label": "Mongodb target create date", "scope": "global", "sort": 2, "user_visible": true, "value": "false"}, {"_id": "46", "category": "UDP", "category_sort": 7, "default_value": "100", "documentation": "Enter udp target send batch size.", "hot_reloading": true, "key": "udp.sendBatchSize", "key_label": "Send batch size", "scope": "global", "sort": 2, "user_visible": false, "value": "100"}, {"_id": "47", "category": "File", "category_sort": 8, "default_value": "GBK", "documentation": "File default charset for parse.", "hot_reloading": false, "key": "file.defaultCharset", "key_label": "File Charset", "scope": "global", "sort": 1, "user_visible": false, "value": "GBK"}, {"_id": "48", "category": "Database", "category_sort": 9, "default_value": "8388608", "documentation": "Database lob data type size (byte) limit.", "hot_reloading": false, "key": "database.lobMaxSize", "key_label": "<PERSON><PERSON>", "scope": "global", "sort": 1, "user_visible": false, "value": "8388608"}, {"_id": "49", "category": "System", "category_sort": 1, "default_value": "30", "documentation": "Interval to collect system info", "hot_reloading": true, "key": "collectSystemInfo", "key_label": "Collect system info interval", "scope": "global", "sort": 1, "user_visible": true, "value": "60"}, {"_id": "5", "category": "Job", "category_sort": 2, "default_value": "1", "documentation": "Validator to validate data 's sample rate", "hot_reloading": true, "key": "sampleRate", "key_label": "Sample Rate", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 4, "user_visible": true, "value": "1"}, {"_id": "50", "category": "System", "default_value": "false", "documentation": "Need to data rules tag.", "enums": ["true", "false"], "hot_reloading": false, "key": "dataRulesTag", "scope": "global", "user_visible": false, "value": "false"}, {"_id": "51", "category": "ApiServer", "category_sort": 1, "default_value": "10", "documentation": "De<PERSON><PERSON>", "hot_reloading": true, "key": "defaultLimit", "key_label": "De<PERSON><PERSON>", "scope": "global", "sort": 1, "user_visible": true, "value": "10"}, {"_id": "52", "category": "ApiServer", "category_sort": 1, "default_value": "1000", "documentation": "<PERSON>", "hot_reloading": true, "key": "maxLimit", "key_label": "<PERSON>", "scope": "global", "sort": 2, "user_visible": true, "value": "0"}, {"_id": "53", "category": "_DK36", "category_sort": 1, "default_value": "http://www.bing.com", "documentation": "File Down Base Url", "hot_reloading": true, "key": "fileDownBaseUrl", "key_label": "File Down Base Url", "scope": "dk36", "sort": 1, "user_visible": false, "value": "http://www.bing.com"}, {"_id": "54", "category": "System", "default_value": "uuid,process", "documentation": "Build-in js function.", "hot_reloading": false, "key": "jsBuildFunc", "scope": "global", "user_visible": false, "value": "uuid,process", "values": ["uuid", "process"]}, {"_id": "55", "category": "ApiServer", "category_sort": 1, "default_value": "true", "documentation": "Enable API Stats Batch Report", "enums": ["true", "false"], "hot_reloading": true, "key": "apiStatsBatchReport.enableApiStatsBatchReport", "key_label": "Enable API Stats Batch Report", "scope": "global", "sort": 3, "user_visible": true, "value": "true"}, {"_id": "56", "category": "Log", "category_sort": 1, "default_value": "16", "documentation": "Set the average number of events per second to allow", "hot_reloading": true, "key": "log4jFilterRate", "key_label": "Log Filter Rate", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 3, "user_visible": true, "value": "16"}, {"_id": "57", "category": "ApiServer", "category_sort": 1, "default_value": "1000", "documentation": "Size Of Trigger API Stats Report", "hot_reloading": true, "key": "apiStatsBatchReport.sizeOfTriggerApiStatsBatchReport", "key_label": "Size Of Trigger API Stats Report", "scope": "global", "sort": 4, "user_visible": true, "value": "1000"}, {"_id": "58", "category": "ApiServer", "category_sort": 1, "default_value": "5000", "documentation": "Time Span Of Trigger API Stats Report", "hot_reloading": true, "key": "apiStatsBatchReport.timeSpanOfTriggerApiStatsBatchReport", "key_label": "Time Span Of Trigger API Stats Report", "scope": "global", "sort": 5, "user_visible": true, "value": "5000"}, {"_id": "59", "category": "Connections", "category_sort": 1, "default_value": "100", "documentation": "Mongodb will use this sample size when load schema", "hot_reloading": true, "key": "connections.mongodbLoadSchemaSampleSize", "key_label": "Mongodb Load Schema Sample Size", "scope": "global", "sort": 5, "user_visible": true, "value": "100"}, {"_id": "60", "category": "Check_devices", "category_sort": 99, "default_value": "127.0.0.1", "documentation": "", "hot_reloading": true, "key": "dkcheck.ipaddresses", "key_label": "Ip addresses", "scope": "global", "sort": 1, "user_visible": false, "value": "127.0.0.1"}, {"_id": "61", "category": "Check_devices", "category_sort": 99, "default_value": 3000, "documentation": "", "hot_reloading": true, "key": "dkcheck.timeout", "key_label": "PingTimeout", "scope": "global", "sort": 2, "user_visible": false, "value": 3000}, {"_id": "62", "category": "Users", "category_sort": 1, "default_value": "#", "documentation": "Logout forward to this url", "hot_reloading": true, "key": "logoutForwardUrl", "key_label": "Logout forward to this url", "scope": "global", "sort": 5, "user_visible": false, "value": ""}, {"_id": "63", "category": "Oracle", "category_sort": 10, "default_value": "automatically", "documentation": "Oracle logminer mine config", "hot_reloading": true, "key": "Oracle.logminer.mine.config", "key_label": "Oracle logminer mine config", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "user_visible": false, "value": "automatically"}, {"_id": "64", "category": "Job", "category_sort": 2, "default_value": "_", "documentation": "A replacement for the invalid field name", "hot_reloading": true, "key": "job.field_replacement", "key_label": "Job_field_replacement", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 5, "user_visible": true, "value": "_"}, {"_id": "65", "category": "ops", "category_sort": 1, "default_value": "-", "documentation": "server_oversee_url", "hot_reloading": true, "key": "serversOversee", "key_label": "server_oversee_url", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "user_visible": true, "value": ""}, {"_id": "66", "category": "System", "hot_reloading": false, "key": "version", "scope": "global", "user_visible": false, "value": "{\"version\":\"v2.4.0-11-g2693ba50c9\",\n\"backend\":\"v2.4.0-11-g2693ba50c9\"}"}, {"_id": "67", "category": "Job", "category_sort": 1, "default_value": "120", "documentation": "Switch to batch insert mode interval (s) in cdc.", "hot_reloading": true, "key": "switchInsertModeInterval", "key_label": "switch_insert_mode_interval", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 3, "user_visible": true, "value": "120"}, {"_id": "68", "category": "SMTP", "category_sort": 1, "default_value": " ", "documentation": "Send Email Title Prefix", "hot_reloading": true, "key": "email.title.prefix", "key_label": "Send Email Title Prefix", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 8, "user_visible": true, "value": ""}, {"_id": "69", "category": "Job", "category_sort": 2, "default_value": "300000", "documentation": "Job heart timeout", "hot_reloading": true, "key": "jobHeartTimeout", "key_label": "Job heart timeout", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 5, "user_visible": true, "value": "300000"}, {"_id": "7", "category": "Worker", "category_sort": 3, "default_value": "60", "documentation": "Worker heartbeat expire time", "hot_reloading": true, "key": "lastHeartbeat", "key_label": "Worker Heartbeat Expire", "last_update": 0, "last_update_by": "", "mask": "##", "scope": "global", "sort": 5, "user_visible": true, "value": "60"}, {"_id": "72", "category": "Job", "category_sort": 2, "default_value": "false", "documentation": "job_cdc_share_mode_doc", "enums": ["true", "false"], "hot_reloading": true, "key": "job_cdc_share_mode", "key_label": "job_cdc_share_mode", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 8, "user_visible": true, "value": "false"}, {"_id": "73", "category": "Job", "category_sort": 2, "default_value": "false", "documentation": "job_cdc_share_only_doc", "enums": ["true", "false"], "hot_reloading": true, "key": "job_cdc_share_only", "key_label": "job_cdc_share_only", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 9, "user_visible": true, "value": "false"}, {"_id": "74", "category": "SMTP", "category_sort": 1, "default_value": "SSL", "documentation": "Email Communication Protocol", "enums": ["SSL", "TLS"], "hot_reloading": true, "key": "email.server.tls", "key_label": "Email Communication Protocol", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 3, "user_visible": true, "value": "SSL"}, {"_id": "75", "category": "SMTP", "category_sort": 1, "default_value": " ", "documentation": "Email Send Address", "hot_reloading": true, "key": "email.send.address", "key_label": "Email Send Address", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 6, "user_visible": true, "value": ""}, {"_id": "76", "category": "notification", "category_sort": 4, "default_value": "", "documentation": "Notification Setting", "hot_reloading": true, "key": "notification", "key_label": "Notification Setting", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "user_visible": false, "value": "{\"runNotification\":[{\"label\":\"jobStarted\",\"notice\":true,\"email\":false},{\"label\":\"jobPaused\",\"notice\":true,\"email\":true},{\"label\":\"jobDeleted\",\"notice\":true,\"email\":false},{\"label\":\"jobStateError\",\"notice\":true,\"email\":true},{\"label\":\"jobEncounterError\",\"notice\":true,\"email\":true,\"noticeInterval\":\"noticeInterval\",\"Interval\":12,\"util\":\"hour\"},{\"label\":\"CDCLagTime\",\"notice\":true,\"email\":true,\"lagTime\":\"lagTime\",\"lagTimeInterval\":12,\"lagTimeUtil\":\"second\",\"noticeInterval\":\"noticeInterval\",\"noticeIntervalInterval\":24,\"noticeIntervalUtil\":\"hour\"},{\"label\":\"inspectCount\",\"notice\":true,\"email\":true},{\"label\":\"inspectValue\",\"notice\":true,\"email\":true},{\"label\":\"inspectDelete\",\"notice\":true,\"email\":true},{\"label\":\"inspectError\",\"notice\":true,\"email\":true}],\"systemNotification\":[],\"agentNotification\":[{\"label\":\"serverDisconnected\",\"notice\":true,\"email\":true},{\"label\":\"agentStarted\",\"notice\":true,\"email\":false},{\"label\":\"agentStopped\",\"notice\":true,\"email\":true},{\"label\":\"agentCreated\",\"notice\":true,\"email\":false},{\"label\":\"agentDeleted\",\"notice\":true,\"email\":true}]}"}, {"_id": "77", "category": "Connections", "category_sort": 2, "default_value": "true", "documentation": "creatDuplicateSource", "enums": ["true", "false"], "hot_reloading": true, "key": "creatDuplicateSource", "key_label": "creatDuplicateSource", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 9, "user_visible": true, "value": "true"}, {"_id": "78", "category": "Connections", "category_sort": 2, "default_value": "02:00", "documentation": "connection_schema_update_hour", "enums": ["false", "00:00", "01:00", "02:00", "03:00", "04:00", "05:00", "06:00", "07:00", "08:00", "09:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00", "19:00", "20:00", "21:00", "22:00", "23:00"], "hot_reloading": true, "key": "connection_schema_update_hour", "key_label": "connection_schema_update_hour", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 6, "user_visible": true, "value": "02:00"}, {"_id": "79", "category": "Connections", "category_sort": 2, "default_value": "1", "documentation": "connection_schema_update_interval", "enums": ["1", "2", "3", "4", "5", "6", "7", "10", "15", "30", "60", "90"], "hot_reloading": true, "key": "connection_schema_update_interval", "key_label": "connection_schema_update_interval", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 7, "user_visible": true, "value": "1"}, {"_id": "8", "category": "License", "category_sort": 4, "default_value": "user=<EMAIL>,count=1,signature=0cbfaffb4b88441b3446ce1a69ad8ba2", "documentation": "License Key", "hot_reloading": true, "key": "license", "key_label": "License Key", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "user_visible": false, "value": "user=<EMAIL>,count=1,signature=0cbfaffb4b88441b3446ce1a69ad8ba2"}, {"_id": "80", "category": "Job", "category_sort": 2, "default_value": "false", "documentation": "job_cdc_record_doc", "enums": ["true", "false"], "hot_reloading": true, "key": "job_cdc_record", "key_label": "job_cdc_record", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 9, "user_visible": true, "value": "false"}, {"_id": "81", "category": "Job", "category_sort": 2, "default_value": "7", "documentation": "job_cdc_record_ttl_doc", "enums": ["3", "5", "7", "10", "15", "30", "60", "90"], "hot_reloading": true, "key": "job_cdc_record_ttl", "key_label": "job_cdc_record_ttl", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 9, "user_visible": true, "value": "7"}, {"_id": "82", "category": "Inspect", "category_sort": 1, "default_value": " ", "documentation": "Inspect Setting", "hot_reloading": true, "key": "InspectSetting", "key_label": "Inspect Setting", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 6, "user_visible": false, "value": "{\"retentionTime\":3,\"reservedQuantityPerTable\":1000,\"differenceTolerant\":1000,\"rowInspectFrequency\":5,\"rowInspectUtil\":\"minute\",\"rowInspectContinuedTime\":1,\"rowInspectContinuedUtil\":\"day\",\"contentInspectFrequency\":24,\"contentInspectUtil\":\"hour\",\"contentInspectContinuedTime\":30,\"contentInspectContinuedUtil\":\"day\",\"contentInspectStartTime\":\"04:00\"}"}, {"_id": "83", "category": "Job", "category_sort": 9, "default_value": "false", "documentation": "When one document may be updated frequently within very short period(a few updates within one second, for instance), the change stream event received by downstream processor may return the \"fullDocument\" that is inconsistent with the actual version when the update was applied to that document. To avoid this inconsistency, enable this option to store the full document along with the update operation. This will at the expense of additional storage and degraded performance.", "enums": ["true", "false"], "hot_reloading": true, "key": "mongodb.after", "key_label": "Store full record as embedded document in target collection for update operations", "scope": "global", "sort": 2, "user_visible": true, "value": "false"}, {"_id": "84", "category": "ApiServer", "category_sort": 2, "default_value": "0", "documentation": "APIServer time zone", "hot_reloading": false, "key": "APIServerTimeZone", "key_label": "APIServerTimeZone", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 9, "user_visible": false, "value": "0"}, {"_id": "85", "category": "system", "category_sort": 2, "default_value": "0", "documentation": "", "hot_reloading": false, "key": "showLicenseNotice", "key_label": "APIServerTimeZone", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 9, "user_visible": false, "value": "0"}, {"_id": "86", "category": "Job", "category_sort": 9, "default_value": "false", "documentation": "the before field contains a field for each table column and the value that was in that column before the update operation.", "enums": ["true", "false"], "hot_reloading": true, "key": "mongodb.before", "key_label": "Store before field as embedded document in target collection before update operation", "scope": "global", "sort": 2, "user_visible": true, "value": "false"}, {"_id": "87", "category": "Job", "category_sort": 9, "default_value": "false", "documentation": "true: store log to cloud, false: only store to local log file.", "enums": ["true", "false"], "hot_reloading": true, "key": "scriptEngineHttpAppender", "key_label": "Store job script processor log to cloud", "scope": "global", "sort": 2, "user_visible": true, "value": "false"}, {"_id": "88", "category": "ops", "category_sort": 1, "default_value": "9999", "documentation": "flow engine version", "hot_reloading": true, "key": "flowEngineVersion", "key_label": "flow_engine_version", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "user_visible": true, "value": ""}, {"_id": "89", "category": "ops", "category_sort": 1, "default_value": "9999", "documentation": "tapdata agent version", "hot_reloading": true, "key": "tapdataAgentVersion", "key_label": "tapdata_agent_version", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 1, "user_visible": true, "value": ""}, {"_id": "9", "category": "SMTP", "category_sort": 1, "default_value": " ", "documentation": "SMTP Server Host", "hot_reloading": true, "key": "smtp.server.host", "key_label": "SMTP Server Host", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 4, "user_visible": true, "value": ""}, {"_id": "90", "category": "system", "category_sort": 2, "default_value": "90", "documentation": "", "hot_reloading": true, "key": "licenseNoticeDays", "key_label": "licenseNoticeDays", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 9, "user_visible": true, "value": "90"}, {"_id": "91", "category": "Job", "category_sort": 2, "default_value": "false", "documentation": "", "hot_reloading": true, "key": "mongodbChangeStreamLookup", "key_label": "mongodbChangeStreamLookup", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 5, "user_visible": false, "value": "false"}, {"_id": "92", "category": "system", "category_sort": 2, "default_value": true, "documentation": "", "hot_reloading": false, "key": "newMongodbChangeStream", "key_label": "newMongodbChangeStream", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 5, "user_visible": false, "value": true}, {"_id": "93", "category": "system", "category_sort": 2, "default_value": 1, "documentation": "maxCpuUsage", "hot_reloading": false, "key": "maxCpuUsage", "key_label": "maxCpuUsage", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 5, "user_visible": true, "value": 1}, {"_id": "94", "category": "system", "category_sort": 2, "default_value": 0.8, "documentation": "maxHeapMemoryUsage", "hot_reloading": false, "key": "maxHeapMemoryUsage", "key_label": "maxHeapMemoryUsage", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 5, "user_visible": true, "value": 0.8}, {"_id": "95", "category": "system", "category_sort": 2, "default_value": "", "documentation": "", "hot_reloading": false, "key": "integration", "key_label": "integration", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 9, "user_visible": false, "value": ""}, {"_id": "96", "category": "Job", "category_sort": 2, "default_value": 0, "documentation": "", "hot_reloading": true, "key": "lagTime", "key_label": "lagTime", "scope": "global", "sort": 10, "user_visible": true, "value": 0}, {"_id": "97", "category": "SMTP", "category_sort": 2, "default_value": "https://cloud.tapdata.net/console/#/", "documentation": "", "hot_reloading": true, "key": "emailHref", "key_label": "emailHref", "scope": "global", "sort": 10, "user_visible": false, "value": "https://cloud.tapdata.net/console/#/"}, {"_id": "98", "category": "Job", "category_sort": 2, "default_value": "10", "documentation": "task load threshold", "hot_reloading": true, "key": "task.load.threshold", "key_label": "task load threshold", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 4, "user_visible": true, "value": "10"}, {"_id": "99", "category": "Job", "category_sort": 2, "default_value": "10", "documentation": "task load statistics time", "hot_reloading": true, "key": "task.load.statistics.time", "key_label": "task load statistics time", "last_update": 0, "last_update_by": "", "scope": "global", "sort": 4, "user_visible": true, "value": "10"}, {"_id": {"$oid": "62bc500ad4958d013d97d4a5"}, "category": "Frontend", "key": "PRODUCT_TITLE", "value": "Tapdata"}, {"_id": {"$oid": "62bc500ad4958d013d97d4ad"}, "category": "Frontend", "key": "SHOW_LANGUAGE", "value": 1}, {"_id": {"$oid": "62bc500ad4958d013d97d4b4"}, "category": "Frontend", "key": "DEFAULT_LANGUAGE", "value": "en"}, {"_id": {"$oid": "62bc500ad4958d013d97d4bf"}, "category": "Frontend", "key": "SHOW_REGISTER", "value": 0}, {"_id": {"$oid": "62bc500ad4958d013d97d4ca"}, "category": "Frontend", "key": "SHOW_OLD_PAGE", "value": 0}, {"_id": {"$oid": "62bc500ad4958d013d97d4d3"}, "category": "Frontend", "key": "SHOW_PAGE_TITLE", "value": 1}, {"_id": {"$oid": "62bc500ad4958d013d97d4de"}, "category": "Frontend", "key": "SHOW_LICENSE", "value": 1}, {"_id": {"$oid": "62bc500ad4958d013d97d4e5"}, "category": "Frontend", "key": "SHOW_NOTIFICATION", "value": 1}, {"_id": {"$oid": "62bc500ad4958d013d97d4ed"}, "category": "Frontend", "key": "SHOW_QA_AND_HELP", "value": 1}, {"_id": {"$oid": "62bc500ad4958d013d97d4f5"}, "category": "Frontend", "key": "SHOW_SETTING_BUTTON", "value": 1}, {"_id": {"$oid": "62bc500ad4958d013d97d4fd"}, "category": "Frontend", "key": "SHOW_HOME_BUTTON", "value": 1}, {"_id": {"$oid": "62bc500ad4958d013d97d509"}, "category": "Frontend", "key": "ALLOW_DOWNLOAD_AGENT", "value": 0}, {"_id": {"$oid": "62bc500ad4958d013d97d511"}, "category": "Frontend", "key": "USE_CLOUD_MENU", "value": 0}, {"_id": {"$oid": "62bc500ad4958d013d97d51f"}, "category": "Frontend", "key": "SHOW_DK_VERSION", "value": 0}, {"_id": {"$oid": "62bc500ad4958d013d97d536"}, "category": "Frontend", "key": "ALLOW_CONNECTION_TYPE", "isArray": true, "value": "mysql,oracle,mongodb,db2,postgres,sqlserver,kafka,elasticsearch"}, {"_id": {"$oid": "62bc500ad4958d013d97d542"}, "category": "Frontend", "key": "SHOW_PREVIEW", "value": 1}, {"_id": {"$oid": "62bc500ad4958d013d97d550"}, "category": "Frontend", "key": "SHOW_DATA_TRACE", "value": 1}, {"_id": {"$oid": "62bc500ad4958d013d97d558"}, "category": "Frontend", "key": "SHOW_CLUSTER_OR_AGENT", "value": "cluster"}, {"_id": {"$oid": "62bc500ad4958d013d97d568"}, "category": "Frontend", "key": "SHOW_SIMPLE_SCENE", "value": 0}, {"_id": {"$oid": "62bc500ad4958d013d97d573"}, "category": "Frontend", "key": "SHOW_CLASSIFY", "value": 1}]}, {"insert": "Role", "documents": [{"_id": {"$oid": "5b9a0a383fcba02649524bf1"}, "created": {"$date": "2022-06-29T13:13:45.594Z"}, "modified": {"$date": "2022-06-29T13:13:45.594Z"}, "name": "admin", "user_id": "62bc5008d4958d013d97c7a6"}, {"_id": {"$oid": "5d31ae1ab953565ded04badd"}, "created": {"$date": "2022-06-29T13:13:45.624Z"}, "description": "新注册用户的默认角色", "modified": {"$date": "2022-06-29T13:13:55.74Z"}, "name": "新用户默认角色", "register_user_default": true, "user_id": {"$oid": "62bc5008d4958d013d97c7a6"}}]}, {"insert": "App", "documents": [{"_id": {"$oid": "62bc500bd4958d013d97e254"}, "name": "DRS", "AppKey": {"$oid": "60125b2c24a4973ee4420646"}, "AppSecret": {"$oid": "62bc500b6beb33b125022020"}}]}, {"insert": "RoleMapping", "documents": [{"_id": {"$oid": "5d31aeedb953565ded04baed"}, "principalId": "Views metadata", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "5d31aeedb953565ded04baee"}, "principalId": "Data Explorer", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "5d31b4d9a78f47de880f6241"}, "principalId": "Dashboard", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc5009d4958d013d97ce2e"}, "principalId": "62bc5008d4958d013d97c7a6", "principalType": "USER", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}}, {"_id": {"$oid": "62bc500a6beb33b125021fec"}, "principalType": "PERMISSION", "principalId": "datasource", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125021fed"}, "principalType": "PERMISSION", "principalId": "Data_SYNC", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125021fee"}, "principalType": "PERMISSION", "principalId": "Data_verify", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125021fef"}, "principalType": "PERMISSION", "principalId": "data_catalog", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125021ff0"}, "principalType": "PERMISSION", "principalId": "data_catalog_all_data", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125021ff1"}, "principalType": "PERMISSION", "principalId": "data_quality", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125021ff2"}, "principalType": "PERMISSION", "principalId": "data_rules", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125021ff3"}, "principalType": "PERMISSION", "principalId": "time_to_live", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125021ff4"}, "principalType": "PERMISSION", "principalId": "data_lineage", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125021ff5"}, "principalType": "PERMISSION", "principalId": "API_management", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125021ff6"}, "principalType": "PERMISSION", "principalId": "API_data_explorer", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125021ff7"}, "principalType": "PERMISSION", "principalId": "API_doc_test", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125021ff8"}, "principalType": "PERMISSION", "principalId": "API_stats", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125021ff9"}, "principalType": "PERMISSION", "principalId": "API_clients", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125021ffa"}, "principalType": "PERMISSION", "principalId": "API_server", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125021ffb"}, "principalType": "PERMISSION", "principalId": "data_collect", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125021ffc"}, "principalType": "PERMISSION", "principalId": "schedule_jobs", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125021ffd"}, "principalType": "PERMISSION", "principalId": "Cluster_management", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125021ffe"}, "principalType": "PERMISSION", "principalId": "agents", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125021fff"}, "principalType": "PERMISSION", "principalId": "user_management", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125022000"}, "principalType": "PERMISSION", "principalId": "role_management", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125022001"}, "principalType": "PERMISSION", "principalId": "system_settings", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125022002"}, "principalType": "PERMISSION", "principalId": "datasource", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125022003"}, "principalType": "PERMISSION", "principalId": "Data_SYNC", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125022004"}, "principalType": "PERMISSION", "principalId": "Data_verify", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125022005"}, "principalType": "PERMISSION", "principalId": "data_catalog", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125022006"}, "principalType": "PERMISSION", "principalId": "data_catalog_all_data", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125022007"}, "principalType": "PERMISSION", "principalId": "data_quality", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125022008"}, "principalType": "PERMISSION", "principalId": "data_rules", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125022009"}, "principalType": "PERMISSION", "principalId": "time_to_live", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b12502200a"}, "principalType": "PERMISSION", "principalId": "data_lineage", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b12502200b"}, "principalType": "PERMISSION", "principalId": "API_management", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b12502200c"}, "principalType": "PERMISSION", "principalId": "API_data_explorer", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b12502200d"}, "principalType": "PERMISSION", "principalId": "API_doc_test", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b12502200e"}, "principalType": "PERMISSION", "principalId": "API_stats", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b12502200f"}, "principalType": "PERMISSION", "principalId": "API_clients", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125022010"}, "principalType": "PERMISSION", "principalId": "API_server", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125022011"}, "principalType": "PERMISSION", "principalId": "data_collect", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125022012"}, "principalType": "PERMISSION", "principalId": "schedule_jobs", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125022013"}, "principalType": "PERMISSION", "principalId": "Cluster_management", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125022014"}, "principalType": "PERMISSION", "principalId": "agents", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125022015"}, "principalType": "PERMISSION", "principalId": "user_management", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125022016"}, "principalType": "PERMISSION", "principalId": "role_management", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500a6beb33b125022017"}, "principalType": "PERMISSION", "principalId": "system_settings", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d20c"}, "principalId": "data_government", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d217"}, "principalId": "data_government_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d220"}, "principalId": "data_catalog", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d228"}, "principalId": "data_catalog_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d230"}, "principalId": "data_publish", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d234"}, "principalId": "data_publish_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d23a"}, "principalId": "API_data_explorer", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d243"}, "principalId": "API_data_explorer_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d24f"}, "principalId": "API_data_explorer_export", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d255"}, "principalId": "API_data_explorer_deleting", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d25b"}, "principalId": "API_data_time_zone_editing", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d264"}, "principalId": "API_data_creation", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d26a"}, "principalId": "API_data_download", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d26f"}, "principalId": "API_data_explorer_tagging", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d274"}, "principalId": "notice_settings", "principalType": "PERMISSION", "roleId": {"$oid": "5d31ae1ab953565ded04badd"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d595"}, "principalId": "data_search", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d59e"}, "principalId": "data_search_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d5a5"}, "principalId": "shared_cache", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d5b9"}, "principalId": "shared_cache_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d5ca"}, "principalId": "custom_node", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d5dc"}, "principalId": "custom_node_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d5f2"}, "principalId": "function_manager", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d601"}, "principalId": "function_manager_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d60f"}, "principalId": "log_collector", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d614"}, "principalId": "log_collector_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d619"}, "principalId": "notice", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d621"}, "principalId": "notice_settings", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d62b"}, "principalId": "datasource_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d641"}, "principalId": "datasource_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d666"}, "principalId": "datasource_category_management", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d676"}, "principalId": "datasource_category_application", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d682"}, "principalId": "datasource_creation", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d69f"}, "principalId": "datasource_delete", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d6c0"}, "principalId": "datasource_delete_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d6ce"}, "principalId": "datasource_edition", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d6e3"}, "principalId": "datasource_edition_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d6f7"}, "principalId": "data_transmission_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d70e"}, "principalId": "data_transmission", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d723"}, "principalId": "Data_SYNC_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d742"}, "principalId": "Data_SYNC_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d74d"}, "principalId": "SYNC_category_management", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d758"}, "principalId": "SYNC_category_application", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d762"}, "principalId": "SYNC_job_creation", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d774"}, "principalId": "SYNC_job_delete", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d781"}, "principalId": "SYNC_job_delete_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d78d"}, "principalId": "SYNC_job_edition", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d79c"}, "principalId": "SYNC_job_edition_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d7ac"}, "principalId": "SYNC_job_operation", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d7c4"}, "principalId": "SYNC_job_operation_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d7d6"}, "principalId": "SYNC_job_import", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d7e7"}, "principalId": "SYNC_job_export", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d7f7"}, "principalId": "SYNC_Function_management", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d80a"}, "principalId": "Data_verify_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d82a"}, "principalId": "Data_verify_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d833"}, "principalId": "verify_job_creation", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d843"}, "principalId": "verify_job_edition", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d852"}, "principalId": "verify_job_edition_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d868"}, "principalId": "verify_job_delete", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d87a"}, "principalId": "verify_job_delete_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d88f"}, "principalId": "data_government_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d8a6"}, "principalId": "data_government", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d8c6"}, "principalId": "data_catalog_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d90b"}, "principalId": "data_catalog_category_management", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d923"}, "principalId": "data_catalog_category_application", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d932"}, "principalId": "data_catalog_edition", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d93e"}, "principalId": "data_catalog_edition_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d94b"}, "principalId": "meta_data_deleting", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d953"}, "principalId": "meta_data_deleting_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d965"}, "principalId": "new_model_creation", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d980"}, "principalId": "data_quality_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d9a1"}, "principalId": "data_quality_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d9b1"}, "principalId": "data_quality_edition", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d9bf"}, "principalId": "data_quality_edition_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97d9cf"}, "principalId": "data_rules_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97da27"}, "principalId": "data_rules_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97da53"}, "principalId": "data_rule_management", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97da5d"}, "principalId": "data_rule_management_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97da68"}, "principalId": "time_to_live_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97da8b"}, "principalId": "time_to_live_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97dab4"}, "principalId": "time_to_live_management", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97dac4"}, "principalId": "time_to_live_management_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97dacd"}, "principalId": "data_lineage_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97daed"}, "principalId": "data_publish_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97dafe"}, "principalId": "data_publish", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97db0b"}, "principalId": "API_management_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97db25"}, "principalId": "API_management_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97db40"}, "principalId": "API_category_application", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97db49"}, "principalId": "API_category_management", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97db54"}, "principalId": "API_creation", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97db63"}, "principalId": "API_delete", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97db6e"}, "principalId": "API_delete_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97db88"}, "principalId": "API_edition", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97db97"}, "principalId": "API_edition_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97dba8"}, "principalId": "API_publish", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97dbbc"}, "principalId": "API_publish_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97dbc7"}, "principalId": "API_import", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97dbd0"}, "principalId": "API_export", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97dbdb"}, "principalId": "API_data_explorer_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97dbfb"}, "principalId": "API_data_explorer_export", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500ad4958d013d97dc04"}, "principalId": "API_data_explorer_deleting", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dc17"}, "principalId": "API_data_time_zone_editing", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dc23"}, "principalId": "API_data_creation", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dc2e"}, "principalId": "API_data_download", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dc3b"}, "principalId": "API_data_explorer_tagging", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dc46"}, "principalId": "API_doc_test_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dc60"}, "principalId": "API_doc_test_export", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dc70"}, "principalId": "API_stats_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dc92"}, "principalId": "API_clients_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dcac"}, "principalId": "API_clients_amangement", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dcb5"}, "principalId": "API_server_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dcc2"}, "principalId": "API_server_management", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dccb"}, "principalId": "data_collect_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dce1"}, "principalId": "data_collect_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dcf2"}, "principalId": "system_management_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dd04"}, "principalId": "system_management", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dd16"}, "principalId": "schedule_jobs_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dd28"}, "principalId": "schedule_jobs_management", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dd32"}, "principalId": "Cluster_management_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dd51"}, "principalId": "Cluster_management_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dd60"}, "principalId": "Cluster_operation", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dd75"}, "principalId": "status_log", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dd7c"}, "principalId": "agents_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dd91"}, "principalId": "user_management_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dda8"}, "principalId": "user_management_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97ddbd"}, "principalId": "user_creation", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97ddcf"}, "principalId": "user_edition", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dddb"}, "principalId": "user_edition_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dde5"}, "principalId": "user_delete", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97ddf1"}, "principalId": "user_delete_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97de00"}, "principalId": "user_category_management", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97de0c"}, "principalId": "user_category_application", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97de17"}, "principalId": "role_management_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97de25"}, "principalId": "role_management_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97de32"}, "principalId": "role_creation", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97de39"}, "principalId": "role_edition", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97de42"}, "principalId": "role_edition_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97de4d"}, "principalId": "role_delete", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97de5b"}, "principalId": "role_delete_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97de71"}, "principalId": "system_settings_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97de94"}, "principalId": "system_settings_modification", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97deb2"}, "principalId": "chart", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97decc"}, "principalId": "chart_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97dee1"}, "principalId": "dictionary_menu", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97deee"}, "principalId": "dictionary", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}, {"_id": {"$oid": "62bc500bd4958d013d97def8"}, "principalId": "dictionary_all_data", "principalType": "PERMISSION", "roleId": {"$oid": "5b9a0a383fcba02649524bf1"}, "self_only": false}]}, {"insert": "Permission", "documents": [{"_id": "1", "description": "数据搜索模块，关闭此模块及相关功能不可见", "name": "data_search", "need_permission": true, "order": 1, "parentId": "data_government", "resources": [{"type": "page", "code": "data_search", "path": "/search"}], "status": "enable", "type": "read"}, {"_id": "10", "description": "共享挖掘模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "log_collector_menu", "need_permission": true, "order": 10, "parentId": "data_transmission", "resources": [{"type": "page", "code": "log_collector_menu", "path": "/sharedMining"}], "status": "enable", "type": "read"}, {"_id": "100", "description": "API数据浏览加标签", "name": "API_data_explorer_tagging", "need_permission": true, "order": 100, "parentId": "API_data_explorer", "resources": [{"type": "button", "code": "API_data_explorer_tagging"}], "status": "enable", "type": "write"}, {"_id": "101", "description": "API文档测试模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "API_doc_test_menu", "need_permission": true, "order": 101, "parentId": "data_publish", "resources": [{"type": "page", "code": "API_doc_&_test_menu", "path": "/apiDocAndTest"}], "status": "enable", "type": "read"}, {"_id": "102", "description": "API文档测试模块，关闭此模块及相关功能不可见", "name": "API_doc_test", "need_permission": true, "order": 102, "parentId": "data_publish", "resources": [{"type": "page", "code": "API_doc_&_test", "path": "/apiDocAndTest"}], "status": "enable", "type": "read"}, {"_id": "103", "description": "API文档测试批量导出", "name": "API_doc_test_export", "need_permission": true, "order": 103, "parentId": "API_doc_test", "resources": [{"type": "page", "code": "API_doc_&_test_export"}], "status": "enable", "type": "read"}, {"_id": "104", "description": "API统计分析模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "API_stats_menu", "need_permission": true, "order": 104, "parentId": "data_publish", "resources": [{"type": "page", "code": "API_stats_menu", "path": "/apiAnalysis"}], "status": "enable", "type": "read"}, {"_id": "105", "description": "API统计分析模块，关闭此模块及相关功能不可见", "name": "API_stats", "need_permission": true, "order": 105, "parentId": "data_publish", "resources": [{"type": "page", "code": "API_stats", "path": "/apiAnalysis"}], "status": "enable", "type": "read"}, {"_id": "106", "description": "API客户端模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "API_clients_menu", "need_permission": true, "order": 106, "parentId": "data_publish", "resources": [{"type": "page", "code": "API_clients_menu", "path": "/applications"}], "status": "enable", "type": "read"}, {"_id": "107", "description": "API客户端模块，关闭此模块及相关功能不可见", "name": "API_clients", "need_permission": true, "order": 107, "parentId": "data_publish", "resources": [{"type": "page", "code": "API_clients", "path": "/applications"}], "status": "enable", "type": "read"}, {"_id": "108", "description": "", "name": "API_clients_amangement", "need_permission": true, "order": 108, "parentId": "API_clients", "resources": [{"type": "button", "code": "API_clients_amangement"}], "status": "enable", "type": "write"}, {"_id": "109", "description": "API服务器模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "API_server_menu", "need_permission": true, "order": 109, "parentId": "data_publish", "resources": [{"type": "page", "code": "API_server_menu", "path": "/apiServers"}], "status": "enable", "type": "read"}, {"_id": "11", "description": "系统消息通知设置功能", "name": "notice", "need_permission": true, "order": 11, "parentId": "system_management", "resources": [{"type": "button", "code": "home_notice"}], "status": "enable", "type": "read"}, {"_id": "110", "description": "API服务器模块，关闭此模块及相关功能不可见", "name": "API_server", "need_permission": true, "order": 110, "parentId": "data_publish", "resources": [{"type": "page", "code": "API_server", "path": "/apiServers"}], "status": "enable", "type": "read"}, {"_id": "111", "description": "", "name": "API_server_management", "need_permission": true, "order": 111, "parentId": "API_server", "resources": [{"type": "button", "code": "API_server_management"}], "status": "enable", "type": "write"}, {"_id": "112", "dataControl": true, "description": "数据采集旧版模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "data_collect_menu", "need_permission": true, "order": 112, "parentId": "", "resources": [{"type": "page", "code": "data_collect(old)_menu", "path": "/dataCollect"}], "status": "enable", "type": "read"}, {"_id": "113", "dataControl": true, "description": "数据采集旧版模块，关闭此模块及相关功能不可见", "name": "data_collect", "need_permission": true, "order": 113, "parentId": "", "resources": [{"type": "button", "code": "data_collect(old)"}], "status": "enable", "type": "read"}, {"_id": "114", "description": "", "name": "data_collect_all_data", "need_permission": true, "order": 114, "parentId": "data_collect", "resources": [{"type": "page", "code": "data_collect_all_data", "path": "/dataCollect"}], "status": "enable", "type": "readAllData"}, {"_id": "115", "description": "系统管理模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "system_management_menu", "need_permission": true, "order": 115, "parentId": "", "resources": [{"type": "button", "code": "system_management_menu"}], "status": "enable", "type": "read"}, {"_id": "116", "description": "系统管理模块，关闭此模块及相关功能不可见", "name": "system_management", "need_permission": true, "order": 116, "parentId": "", "resources": [{"type": "button", "code": "system_management"}], "status": "enable", "type": "read"}, {"_id": "117", "description": "调度任务模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "schedule_jobs_menu", "need_permission": true, "order": 117, "parentId": "system_management", "resources": [{"type": "page", "code": "schedule_jobs_menu", "path": "/tasks"}], "status": "enable", "type": "read"}, {"_id": "118", "description": "调度任务模块，关闭此模块及相关功能不可见", "name": "schedule_jobs", "need_permission": true, "order": 118, "parentId": "system_management", "resources": [{"type": "page", "code": "schedule_jobs", "path": "/tasks"}], "status": "enable", "type": "read"}, {"_id": "119", "description": "", "name": "schedule_jobs_management", "need_permission": true, "order": 119, "parentId": "schedule_jobs", "resources": [{"type": "button", "code": "schedule_jobs_management"}], "status": "enable", "type": "write"}, {"_id": "12", "description": "系统消息通知设置功能", "name": "notice_settings", "need_permission": true, "order": 12, "parentId": "system_management", "resources": [{"type": "button", "code": "home_notice_settings"}], "status": "enable", "type": "write"}, {"_id": "120", "dataControl": true, "description": "集群管理模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "Cluster_management_menu", "need_permission": true, "order": 120, "parentId": "system_management", "resources": [{"type": "page", "code": "Cluster_management_menu", "path": "/clusterManagement"}], "status": "enable", "type": "read"}, {"_id": "121", "dataControl": true, "description": "集群管理模块，关闭此模块及相关功能不可见", "name": "Cluster_management", "need_permission": true, "order": 121, "parentId": "system_management", "resources": [{"type": "page", "code": "Cluster_management", "path": "/clusterManagement"}], "status": "enable", "type": "read"}, {"_id": "122", "description": "", "name": "Cluster_management_all_data", "need_permission": true, "order": 122, "parentId": "Cluster_management", "resources": [{"type": "button", "code": "Cluster_management_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "123", "description": "", "name": "Cluster_operation", "need_permission": true, "order": 123, "parentId": "Cluster_management", "resources": [{"type": "button", "code": "Cluster_operation"}], "status": "enable", "type": "write"}, {"_id": "124", "description": "", "name": "status_log", "need_permission": true, "order": 124, "parentId": "Cluster_management", "resources": [{"type": "button", "code": "status_log"}], "status": "enable", "type": "read"}, {"_id": "125", "description": "进程管理模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "agents_menu", "need_permission": true, "order": 125, "parentId": "system_management", "resources": [{"type": "page", "code": "agents_menu", "path": "/agents"}], "status": "enable", "type": "read"}, {"_id": "126", "description": "进程管理模块，关闭此模块及相关功能不可见", "name": "agents", "need_permission": true, "order": 126, "parentId": "system_management", "resources": [{"type": "page", "code": "agents", "path": "/agents"}], "status": "enable", "type": "read"}, {"_id": "127", "dataControl": true, "description": "用户管理模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "user_management_menu", "need_permission": true, "order": 127, "parentId": "system_management", "resources": [{"type": "page", "code": "user_management_menu", "path": "/users"}], "status": "enable", "type": "read"}, {"_id": "128", "dataControl": true, "description": "用户管理模块，关闭此模块及相关功能不可见", "name": "user_management", "need_permission": true, "order": 128, "parentId": "system_management", "resources": [{"type": "page", "code": "user_management", "path": "/users"}], "status": "enable", "type": "read"}, {"_id": "129", "description": "", "name": "user_management_all_data", "need_permission": true, "order": 129, "parentId": "user_management", "resources": [{"type": "button", "code": "user_management_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "13", "description": "数据源模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "datasource_menu", "need_permission": true, "order": 13, "parentId": "", "resources": [{"type": "page", "code": "datasource_menu", "path": "/connections"}], "status": "enable", "type": "read"}, {"_id": "130", "description": "", "name": "user_creation", "need_permission": true, "order": 130, "parentId": "user_management", "resources": [{"type": "button", "code": "user_creation"}], "status": "enable", "type": "write"}, {"_id": "131", "dataControl": true, "description": "", "name": "user_edition", "need_permission": true, "order": 131, "parentId": "user_management", "resources": [{"type": "button", "code": "user_edition"}], "status": "enable", "type": "write"}, {"_id": "132", "description": "", "name": "user_edition_all_data", "need_permission": true, "order": 132, "parentId": "user_edition", "resources": [{"type": "button", "code": "user_edition_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "133", "dataControl": true, "description": "", "name": "user_delete", "need_permission": true, "order": 133, "parentId": "user_management", "resources": [{"type": "button", "code": "user_delete"}], "status": "enable", "type": "write"}, {"_id": "134", "description": "", "name": "user_delete_all_data", "need_permission": true, "order": 134, "parentId": "user_delete", "resources": [{"type": "button", "code": "user_delete_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "135", "description": "", "name": "user_category_management", "need_permission": true, "order": 135, "parentId": "user_management", "resources": [{"type": "button", "code": "user_category_management"}], "status": "enable", "type": "write"}, {"_id": "136", "description": "", "name": "user_category_application", "need_permission": true, "order": 136, "parentId": "user_management", "resources": [{"type": "button", "code": "user_category_application"}], "status": "enable", "type": "write"}, {"_id": "137", "dataControl": true, "description": "角色管理模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "role_management_menu", "need_permission": true, "order": 137, "parentId": "system_management", "resources": [{"type": "page", "code": "role_management_menu", "path": "/roles"}], "status": "enable", "type": "read"}, {"_id": "138", "dataControl": true, "description": "角色管理模块，关闭此模块及相关功能不可见", "name": "role_management", "need_permission": true, "order": 138, "parentId": "system_management", "resources": [{"type": "page", "code": "role_management", "path": "/roles"}], "status": "enable", "type": "read"}, {"_id": "139", "description": "", "name": "role_management_all_data", "need_permission": true, "order": 139, "parentId": "role_management", "resources": [{"type": "button", "code": "role_management_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "14", "dataControl": true, "description": "数据源模块，关闭此模块及相关功能不可见", "name": "datasource", "need_permission": true, "order": 14, "parentId": "", "resources": [{"type": "page", "code": "datasource", "path": "/connections"}], "status": "enable", "type": "read"}, {"_id": "140", "description": "", "name": "role_creation", "need_permission": true, "order": 140, "parentId": "role_management", "resources": [{"type": "button", "code": "role_creation"}], "status": "enable", "type": "write"}, {"_id": "141", "dataControl": true, "description": "", "name": "role_edition", "need_permission": true, "order": 141, "parentId": "role_management", "resources": [{"type": "button", "code": "role_edition"}], "status": "enable", "type": "write"}, {"_id": "142", "description": "", "name": "role_edition_all_data", "need_permission": true, "order": 142, "parentId": "role_edition", "resources": [{"type": "button", "code": "role_edition_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "143", "dataControl": true, "description": "", "name": "role_delete", "need_permission": true, "order": 143, "parentId": "role_management", "resources": [{"type": "button", "code": "role_delete"}], "status": "enable", "type": "write"}, {"_id": "144", "description": "", "name": "role_delete_all_data", "need_permission": true, "order": 144, "parentId": "role_delete", "resources": [{"type": "button", "code": "role_delete_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "145", "description": "系统设置模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "system_settings_menu", "need_permission": true, "order": 145, "parentId": "system_management", "resources": [{"type": "page", "code": "system_settings_menu", "path": "/settings"}], "status": "enable", "type": "read"}, {"_id": "146", "description": "系统设置模块，关闭此模块及相关功能不可见", "name": "system_settings", "need_permission": true, "order": 146, "parentId": "system_management", "resources": [{"type": "page", "code": "system_settings", "path": "/settings"}], "status": "enable", "type": "read"}, {"_id": "147", "description": "", "name": "system_settings_modification", "need_permission": true, "order": 147, "parentId": "system_settings", "resources": [{"type": "button", "code": "system_settings_modification"}], "status": "enable", "type": "write"}, {"_id": "148", "dataControl": true, "description": "首页图表", "name": "chart", "need_permission": true, "order": 148, "parentId": "", "resources": [{"type": "button", "code": "chart"}], "status": "enable", "type": "read"}, {"_id": "149", "description": "首页图表", "name": "chart_all_data", "need_permission": true, "order": 149, "parentId": "chart", "resources": [{"type": "button", "code": "chart_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "15", "description": "", "name": "datasource_all_data", "need_permission": true, "order": 15, "parentId": "datasource", "resources": [{"type": "button", "code": "datasource_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "150", "description": "字典模板", "isMenu": true, "name": "dictionary_menu", "need_permission": true, "order": 150, "parentId": "data_government", "resources": [{"type": "page", "code": "dictionary_menu", "path": "/dictionary"}], "status": "enable", "type": "read"}, {"_id": "151", "description": "字典模板", "name": "dictionary", "need_permission": true, "order": 151, "parentId": "data_government", "resources": [{"type": "button", "code": "dictionary"}], "status": "enable", "type": "read"}, {"_id": "152", "description": "字典模板", "name": "dictionary_all_data", "need_permission": true, "order": 152, "parentId": "data_government", "resources": [{"type": "button", "code": "dictionary_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "153", "description": "数据应用", "grantToAdmin": false, "isHidden": true, "name": "Data Application", "need_permission": true, "order": 153, "resources": [{"type": "page", "name": "dataApp"}], "status": "enable"}, {"_id": "154", "description": "数据大屏", "grantToAdmin": false, "isHidden": true, "name": "Data screen", "need_permission": true, "order": 154, "resources": [{"type": "page", "name": "dataSreen"}], "status": "enable"}, {"_id": "155", "description": "自动化流程", "grantToAdmin": false, "isHidden": true, "name": "Automated process", "need_permission": true, "order": 155, "resources": [{"type": "page", "name": "automatedProcess"}], "status": "enable"}, {"_id": "156", "description": "主数据管理", "grantToAdmin": false, "isHidden": true, "name": "Master data management", "need_permission": true, "order": 156, "resources": [{"type": "page", "name": "masterDataManagement"}], "status": "enable"}, {"_id": "16", "description": "对数据源分类的新建、编辑、删除操作", "name": "datasource_category_management", "need_permission": true, "order": 16, "parentId": "datasource", "resources": [{"type": "button", "code": "datasource_catalog_management"}], "status": "enable", "type": "write"}, {"_id": "17", "description": "分类对数据源的应用操作", "name": "datasource_category_application", "need_permission": true, "order": 17, "parentId": "datasource", "resources": [{"type": "button", "code": "datasource_category_application"}], "status": "enable", "type": "write"}, {"_id": "18", "description": "创建新的数据源功能", "name": "datasource_creation", "need_permission": true, "order": 18, "parentId": "datasource", "resources": [{"type": "page", "code": "datasource_creation", "path": "/connection"}], "status": "enable", "type": "write"}, {"_id": "19", "dataControl": true, "description": "删除数据源功能", "name": "datasource_delete", "need_permission": true, "order": 19, "parentId": "datasource", "resources": [{"type": "button", "code": "datasource_delete"}], "status": "enable", "type": "write"}, {"_id": "2", "description": "数据搜索模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "data_search_menu", "need_permission": true, "order": 2, "parentId": "data_government", "resources": [{"type": "page", "code": "data_search_menu", "path": "/search"}], "status": "enable", "type": "read"}, {"_id": "20", "description": "", "name": "datasource_delete_all_data", "need_permission": true, "order": 20, "parentId": "datasource_delete", "resources": [{"type": "button", "code": "datasource_delete_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "21", "dataControl": true, "description": "编辑数据源功能", "name": "datasource_edition", "need_permission": true, "order": 21, "parentId": "datasource", "resources": [{"type": "page", "code": "datasource_edition", "path": "/connection"}], "status": "enable", "type": "write"}, {"_id": "22", "description": "", "name": "datasource_edition_all_data", "need_permission": true, "order": 22, "parentId": "datasource_edition", "resources": [{"type": "button", "code": "datasource_edition_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "23", "description": "数据传输模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "data_transmission_menu", "need_permission": true, "order": 23, "parentId": "", "resources": [{"type": "page", "code": "data_transmission_menu", "path": "/dataFlows"}, {"type": "page", "code": "data_transmission", "path": "/job"}], "status": "enable", "type": "read"}, {"_id": "24", "description": "数据传输模块，关闭此模块及相关功能不可见", "name": "data_transmission", "need_permission": true, "order": 24, "parentId": "", "resources": [{"type": "page", "code": "data_transmission", "path": "/dataFlows"}, {"type": "page", "code": "data_transmission", "path": "/job"}], "status": "enable", "type": "read"}, {"_id": "25", "dataControl": true, "description": "数据同步模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "Data_SYNC_menu", "need_permission": true, "order": 25, "parentId": "data_transmission", "resources": [{"type": "page", "code": "Data_SYNC_menu", "path": "/dataFlows"}], "status": "enable", "type": "read"}, {"_id": "26", "dataControl": true, "description": "数据同步模块，关闭此模块及相关功能不可见", "name": "Data_SYNC", "need_permission": true, "order": 26, "parentId": "data_transmission", "resources": [{"type": "page", "code": "Data_SYNC", "path": "/dataFlows"}, {"type": "page", "code": "Data_SYNC", "path": "/job"}], "status": "enable", "type": "read"}, {"_id": "27", "description": "", "name": "Data_SYNC_all_data", "need_permission": true, "order": 27, "parentId": "Data_SYNC", "resources": [{"type": "button", "code": "Data_SYNC_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "28", "description": "对同步任务分类的新建、编辑、删除操作", "name": "SYNC_category_management", "need_permission": true, "order": 28, "parentId": "Data_SYNC", "resources": [{"type": "button", "code": "SYNC_category_management"}], "status": "enable", "type": "write"}, {"_id": "29", "description": "分类对同步任务的应用操作", "name": "SYNC_category_application", "need_permission": true, "order": 29, "parentId": "Data_SYNC", "resources": [{"type": "button", "code": "SYNC_category_application"}], "status": "enable", "type": "write"}, {"_id": "3", "description": "共享缓存模块，关闭此模块及相关功能不可见", "name": "shared_cache", "need_permission": true, "order": 3, "parentId": "data_transmission", "resources": [{"type": "page", "code": "shared_cache", "path": "/shared-cache"}], "status": "enable", "type": "read"}, {"_id": "30", "description": "创建新的同步任务功能", "name": "SYNC_job_creation", "need_permission": true, "order": 30, "parentId": "Data_SYNC", "resources": [{"type": "button", "code": "SYNC_job_creation"}], "status": "enable", "type": "write"}, {"_id": "31", "dataControl": true, "description": "删除同步任务功能", "name": "SYNC_job_delete", "need_permission": true, "order": 31, "parentId": "Data_SYNC", "resources": [{"type": "button", "code": "SYNC_job_delete"}], "status": "enable", "type": "write"}, {"_id": "32", "description": "", "name": "SYNC_job_delete_all_data", "need_permission": true, "order": 32, "parentId": "SYNC_job_delete", "resources": [{"type": "button", "code": "SYNC_job_delete_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "33", "dataControl": true, "description": "编辑同步任务功能", "name": "SYNC_job_edition", "need_permission": true, "order": 33, "parentId": "Data_SYNC", "resources": [{"type": "button", "code": "SYNC_job_edition"}], "status": "enable", "type": "write"}, {"_id": "34", "description": "", "name": "SYNC_job_edition_all_data", "need_permission": true, "order": 34, "parentId": "SYNC_job_edition", "resources": [{"type": "button", "code": "SYNC_job_edition_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "35", "dataControl": true, "description": "对同步任务的启动、停止、重置等操作", "name": "SYNC_job_operation", "need_permission": true, "order": 35, "parentId": "Data_SYNC", "resources": [{"type": "button", "code": "SYNC_job_operation"}], "status": "enable", "type": "write"}, {"_id": "36", "description": "", "name": "SYNC_job_operation_all_data", "need_permission": true, "order": 36, "parentId": "SYNC_job_operation", "resources": [{"type": "button", "code": "SYNC_job_operation_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "37", "description": "同步任务导入功能", "name": "SYNC_job_import", "need_permission": true, "order": 37, "parentId": "Data_SYNC", "resources": [{"type": "button", "code": "SYNC_job_import"}], "status": "enable", "type": "write"}, {"_id": "38", "description": "同步任务导出功能", "name": "SYNC_job_export", "need_permission": true, "order": 38, "parentId": "Data_SYNC", "resources": [{"type": "button", "code": "SYNC_job_export"}], "status": "enable", "type": "write"}, {"_id": "39", "description": "全局函数的应用和管理", "name": "SYNC_Function_management", "need_permission": true, "order": 39, "parentId": "Data_SYNC", "resources": [{"type": "button", "code": "SYNC_Function_management"}], "status": "enable", "type": "write"}, {"_id": "4", "description": "共享缓存模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "shared_cache_menu", "need_permission": true, "order": 4, "parentId": "data_transmission", "resources": [{"type": "page", "code": "shared_cache_menu", "path": "/shared-cache"}], "status": "enable", "type": "read"}, {"_id": "40", "dataControl": true, "description": "数据校验模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "Data_verify_menu", "need_permission": true, "order": 40, "parentId": "data_transmission", "resources": [{"type": "page", "code": "Data_verify_menu", "path": "/dataVerification"}], "status": "enable", "type": "read"}, {"_id": "41", "dataControl": true, "description": "数据校验模块，关闭此模块及相关功能不可见", "name": "Data_verify", "need_permission": true, "order": 41, "parentId": "data_transmission", "resources": [{"type": "page", "code": "Data_verify", "path": "/dataVerification"}], "status": "enable", "type": "read"}, {"_id": "42", "description": "", "name": "Data_verify_all_data", "need_permission": true, "order": 42, "parentId": "Data_verify", "resources": [{"type": "button", "code": "Data_verify_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "43", "description": "创建校验任务功能", "name": "verify_job_creation", "need_permission": true, "order": 43, "parentId": "Data_verify", "resources": [{"type": "button", "code": "verify_job_creation"}], "status": "enable", "type": "write"}, {"_id": "44", "dataControl": true, "description": "编辑校验任务功能", "name": "verify_job_edition", "need_permission": true, "order": 44, "parentId": "Data_verify", "resources": [{"type": "button", "code": "verify_job_edition"}], "status": "enable", "type": "write"}, {"_id": "45", "description": "", "name": "verify_job_edition_all_data", "need_permission": true, "order": 45, "parentId": "verify_job_edition", "resources": [{"type": "button", "code": "verify_job_edition_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "46", "dataControl": true, "description": "删除校验任务功能", "name": "verify_job_delete", "need_permission": true, "order": 46, "parentId": "Data_verify", "resources": [{"type": "button", "code": "verify_job_delete"}], "status": "enable", "type": "write"}, {"_id": "47", "description": "", "name": "verify_job_delete_all_data", "need_permission": true, "order": 47, "parentId": "verify_job_delete", "resources": [{"type": "button", "code": "verify_job_delete_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "48", "description": "数据治理模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "data_government_menu", "need_permission": true, "order": 48, "parentId": "", "resources": [{"type": "button", "code": "data_government_menu"}], "status": "enable", "type": "read"}, {"_id": "49", "description": "数据治理模块，关闭此模块及相关功能不可见", "name": "data_government", "need_permission": true, "order": 49, "parentId": "", "resources": [{"type": "button", "code": "data_government"}], "status": "enable", "type": "read"}, {"_id": "5", "description": "自定义节点模块，关闭此模块及相关功能不可见", "name": "custom_node", "need_permission": true, "order": 5, "parentId": "data_transmission", "resources": [{"type": "page", "code": "custom_node", "path": "/customNode"}], "status": "enable", "type": "read"}, {"_id": "50", "dataControl": true, "description": "数据目录模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "data_catalog_menu", "need_permission": true, "order": 50, "parentId": "data_government", "resources": [{"type": "page", "code": "data_catalog_menu", "path": "/metadataDefinition"}], "status": "enable", "type": "read"}, {"_id": "51", "dataControl": true, "description": "数据目录模块，关闭此模块及相关功能不可见", "name": "data_catalog", "need_permission": true, "order": 51, "parentId": "data_government", "resources": [{"type": "page", "code": "data_catalog", "path": "/metadataDefinition"}], "status": "enable", "type": "read"}, {"_id": "52", "description": "", "name": "data_catalog_all_data", "need_permission": true, "order": 52, "parentId": "data_catalog", "resources": [{"type": "button", "code": "data_catalog_all_data"}], "status": "enable", "type": "read"}, {"_id": "53", "description": "对元数据分类的新建、编辑、删除操作", "name": "data_catalog_category_management", "need_permission": true, "order": 53, "parentId": "data_catalog", "resources": [{"type": "button", "code": "data_catalog_category_management"}], "status": "enable", "type": "write"}, {"_id": "54", "description": "分类对元数据的应用操作", "name": "data_catalog_category_application", "need_permission": true, "order": 54, "parentId": "data_catalog", "resources": [{"type": "button", "code": "data_catalog_category_application"}], "status": "enable", "type": "write"}, {"_id": "55", "dataControl": true, "description": "对元数据的编辑功能", "name": "data_catalog_edition", "need_permission": true, "order": 55, "parentId": "data_catalog", "resources": [{"type": "page", "code": "data_catalog_edition", "path": "/metadataInstances"}], "status": "enable", "type": "write"}, {"_id": "56", "description": "", "name": "data_catalog_edition_all_data", "need_permission": true, "order": 56, "parentId": "data_catalog_edition", "resources": [{"type": "button", "code": "data_catalog_edition_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "57", "dataControl": true, "description": "元数据删除", "name": "meta_data_deleting", "need_permission": true, "order": 57, "parentId": "data_catalog", "resources": [{"type": "button", "code": "meta_data_deleting"}], "status": "enable", "type": "write"}, {"_id": "58", "description": "", "name": "meta_data_deleting_all_data", "need_permission": true, "order": 58, "parentId": "meta_data_deleting", "resources": [{"type": "button", "code": "meta_data_deleting_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "59", "description": "创建新模型的功能", "name": "new_model_creation", "need_permission": true, "order": 59, "parentId": "data_catalog", "resources": [{"type": "button", "code": "new_model_creation"}], "status": "enable", "type": "write"}, {"_id": "6", "description": "自定义节点模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "custom_node_menu", "need_permission": true, "order": 6, "parentId": "data_transmission", "resources": [{"type": "page", "code": "custom_node_menu", "path": "/customNode"}], "status": "enable", "type": "read"}, {"_id": "60", "dataControl": true, "description": "数据质量模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "data_quality_menu", "need_permission": true, "order": 60, "parentId": "data_government", "resources": [{"type": "page", "code": "data_quality_menu", "path": "/dataQuality"}], "status": "enable", "type": "read"}, {"_id": "61", "dataControl": true, "description": "数据质量模块，关闭此模块及相关功能不可见", "name": "data_quality", "need_permission": true, "order": 61, "parentId": "data_government", "resources": [{"type": "page", "code": "data_quality", "path": "/dataQuality"}], "status": "enable", "type": "read"}, {"_id": "62", "description": "", "name": "data_quality_all_data", "need_permission": true, "order": 62, "parentId": "data_quality", "resources": [{"type": "button", "code": "data_quality_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "63", "dataControl": true, "description": "", "name": "data_quality_edition", "need_permission": true, "order": 63, "parentId": "data_quality", "resources": [{"type": "button", "code": "data_quality_edition"}], "status": "enable", "type": "write"}, {"_id": "64", "description": "", "name": "data_quality_edition_all_data", "need_permission": true, "order": 64, "parentId": "data_quality_edition", "resources": [{"type": "button", "code": "data_quality_edition_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "65", "dataControl": true, "description": "数据规则模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "data_rules_menu", "need_permission": true, "order": 65, "parentId": "data_government", "resources": [{"type": "page", "code": "data_rules_menu", "path": "/dataRules"}], "status": "enable", "type": "read"}, {"_id": "66", "dataControl": true, "description": "数据规则模块，关闭此模块及相关功能不可见", "name": "data_rules", "need_permission": true, "order": 66, "parentId": "data_government", "resources": [{"type": "page", "code": "data_rules", "path": "/dataRules"}], "status": "enable", "type": "read"}, {"_id": "67", "description": "", "name": "data_rules_all_data", "need_permission": true, "order": 67, "parentId": "data_rules", "resources": [{"type": "button", "code": "data_rules_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "68", "dataControl": true, "description": "", "name": "data_rule_management", "need_permission": true, "order": 68, "parentId": "data_rules", "resources": [{"type": "button", "code": "data_rule_management"}], "status": "enable", "type": "write"}, {"_id": "69", "description": "", "name": "data_rule_management_all_data", "need_permission": true, "order": 69, "parentId": "data_rule_management", "resources": [{"type": "button", "code": "data_rule_management_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "7", "description": "函数管理模块，关闭此模块及相关功能不可见", "name": "function_manager", "need_permission": true, "order": 7, "parentId": "data_transmission", "resources": [{"type": "page", "code": "function_manager", "path": "/function"}], "status": "enable", "type": "read"}, {"_id": "70", "dataControl": true, "description": "数据生命周期模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "time_to_live_menu", "need_permission": true, "order": 70, "parentId": "data_government", "resources": [{"type": "page", "code": "time_to_live_menu", "path": "/ttl"}], "status": "enable", "type": "read"}, {"_id": "71", "dataControl": true, "description": "数据生命周期模块，关闭此模块及相关功能不可见", "name": "time_to_live", "need_permission": true, "order": 71, "parentId": "data_government", "resources": [{"type": "page", "code": "time_to_live", "path": "/ttl"}], "status": "enable", "type": "read"}, {"_id": "72", "description": "", "name": "time_to_live_all_data", "need_permission": true, "order": 72, "parentId": "time_to_live", "resources": [{"type": "button", "code": "time_to_live_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "73", "dataControl": true, "description": "", "name": "time_to_live_management", "need_permission": true, "order": 73, "parentId": "time_to_live", "resources": [{"type": "button", "code": "time_to_live_management"}], "status": "enable", "type": "write"}, {"_id": "74", "description": "", "name": "time_to_live_management_all_data", "need_permission": true, "order": 74, "parentId": "time_to_live_management", "resources": [{"type": "button", "code": "time_to_live_management_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "75", "description": "数据地图模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "data_lineage_menu", "need_permission": true, "order": 75, "parentId": "data_government", "resources": [{"type": "page", "code": "data_lineage_menu", "path": "/dataMap"}], "status": "enable", "type": "read"}, {"_id": "76", "description": "数据地图模块，关闭此模块及相关功能不可见", "name": "data_lineage", "need_permission": true, "order": 76, "parentId": "data_government", "resources": [{"type": "page", "code": "data_lineage", "path": "/dataMap"}], "status": "enable", "type": "read"}, {"_id": "77", "description": "数据发布模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "data_publish_menu", "need_permission": true, "order": 77, "parentId": "", "resources": [{"type": "button", "code": "data_publish_menu"}], "status": "enable", "type": "read"}, {"_id": "78", "description": "数据发布模块，关闭此模块及相关功能不可见", "name": "data_publish", "need_permission": true, "order": 78, "parentId": "", "resources": [{"type": "button", "code": "data_publish"}], "status": "enable", "type": "read"}, {"_id": "79", "dataControl": true, "description": "API发布模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "API_management_menu", "need_permission": true, "order": 79, "parentId": "", "resources": [{"type": "page", "code": "API_management_menu", "path": "/modules"}], "status": "enable", "type": "read"}, {"_id": "8", "description": "函数管理模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "function_manager_menu", "need_permission": true, "order": 8, "parentId": "data_transmission", "resources": [{"type": "page", "code": "function_manager_menu", "path": "/function"}], "status": "enable", "type": "read"}, {"_id": "80", "dataControl": true, "description": "API发布模块，关闭此模块及相关功能不可见", "name": "API_management", "need_permission": true, "order": 80, "parentId": "data_publish", "resources": [{"type": "page", "code": "API_management", "path": "/modules"}], "status": "enable", "type": "read"}, {"_id": "81", "description": "", "name": "API_management_all_data", "need_permission": true, "order": 81, "parentId": "API_management", "resources": [{"type": "button", "code": "API_management_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "82", "description": "", "name": "API_category_application", "need_permission": true, "order": 82, "parentId": "API_management", "resources": [{"type": "button", "code": "API_category_application"}], "status": "enable", "type": "write"}, {"_id": "83", "description": "", "name": "API_category_management", "need_permission": true, "order": 83, "parentId": "API_management", "resources": [{"type": "button", "code": "API_category_management"}], "status": "enable", "type": "write"}, {"_id": "84", "description": "", "name": "API_creation", "need_permission": true, "order": 84, "parentId": "API_management", "resources": [{"type": "button", "code": "API_creation"}], "status": "enable", "type": "write"}, {"_id": "85", "dataControl": true, "description": "", "name": "API_delete", "need_permission": true, "order": 85, "parentId": "API_management", "resources": [{"type": "button", "code": "API_delete"}], "status": "enable", "type": "write"}, {"_id": "86", "description": "", "name": "API_delete_all_data", "need_permission": true, "order": 86, "parentId": "API_delete", "resources": [{"type": "button", "code": "API_delete_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "87", "dataControl": true, "description": "", "name": "API_edition", "need_permission": true, "order": 87, "parentId": "API_management", "resources": [{"type": "button", "code": "API_edition"}], "status": "enable", "type": "write"}, {"_id": "88", "description": "", "name": "API_edition_all_data", "need_permission": true, "order": 88, "parentId": "API_edition", "resources": [{"type": "button", "code": "API_edition_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "89", "dataControl": true, "description": "", "name": "API_publish", "need_permission": true, "order": 89, "parentId": "API_management", "resources": [{"type": "button", "code": "API_publish"}], "status": "enable", "type": "write"}, {"_id": "9", "description": "共享挖掘模块，关闭此模块及相关功能不可见", "name": "log_collector", "need_permission": true, "order": 9, "parentId": "data_transmission", "resources": [{"type": "page", "code": "log_collector", "path": "/sharedMining"}], "status": "enable", "type": "read"}, {"_id": "90", "description": "", "name": "API_publish_all_data", "need_permission": true, "order": 90, "parentId": "API_publish", "resources": [{"type": "button", "code": "API_publish_all_data"}], "status": "enable", "type": "readAllData"}, {"_id": "91", "description": "", "name": "API_import", "need_permission": true, "order": 91, "parentId": "API_management", "resources": [{"type": "button", "code": "API_import"}], "status": "enable", "type": "write"}, {"_id": "92", "description": "", "name": "API_export", "need_permission": true, "order": 92, "parentId": "API_management", "resources": [{"type": "button", "code": "API_export"}], "status": "enable", "type": "write"}, {"_id": "93", "description": "API数据浏览模块，关闭此模块及相关功能不可见", "isMenu": true, "name": "API_data_explorer_menu", "need_permission": true, "order": 93, "parentId": "data_publish", "resources": [{"type": "page", "code": "API_data_explorer_menu", "path": "/dataExplorer"}], "status": "enable", "type": "read"}, {"_id": "94", "description": "API数据浏览模块，关闭此模块及相关功能不可见", "name": "API_data_explorer", "need_permission": true, "order": 94, "parentId": "data_publish", "resources": [{"type": "page", "code": "API_data_explorer", "path": "/dataExplorer"}], "status": "enable", "type": "read"}, {"_id": "95", "description": "API数据预览导出", "name": "API_data_explorer_export", "need_permission": true, "order": 95, "parentId": "API_data_explorer", "resources": [{"type": "button", "code": "API_data_explorer_export"}], "status": "enable", "type": "write"}, {"_id": "96", "description": "API数据浏览删除", "name": "API_data_explorer_deleting", "need_permission": true, "order": 96, "parentId": "API_data_explorer", "resources": [{"type": "button", "code": "API_data_explorer_deleting"}], "status": "enable", "type": "write"}, {"_id": "97", "description": "API数据浏览删除", "name": "API_data_time_zone_editing", "need_permission": true, "order": 97, "parentId": "API_data_explorer", "resources": [{"type": "button", "code": "API_data_time_zone_editing"}], "status": "enable", "type": "write"}, {"_id": "98", "description": "API数据浏览删除", "name": "API_data_creation", "need_permission": true, "order": 98, "parentId": "API_data_explorer", "resources": [{"type": "button", "code": "API_data_creation"}], "status": "enable", "type": "write"}, {"_id": "99", "description": "API数据浏览删除", "name": "API_data_download", "need_permission": true, "order": 99, "parentId": "API_data_explorer", "resources": [{"type": "button", "code": "API_data_download"}], "status": "enable", "type": "write"}]}, {"insert": "LibSupporteds", "documents": [{"_id": {"$oid": "62bd4b94aa3635287112057a"}, "databaseType": "mysql", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:00.946Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa3635287112057b"}, "databaseType": "oracle", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.205Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa3635287112057c"}, "databaseType": "mongodb", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.209Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa3635287112057d"}, "databaseType": "sqlserver", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.213Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa3635287112057e"}, "databaseType": "sybase ase", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.216Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa3635287112057f"}, "databaseType": "gridfs", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.22Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120580"}, "databaseType": "file", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.223Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120581"}, "databaseType": "rest api", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.227Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120582"}, "databaseType": "tcp_udp", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.231Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120583"}, "databaseType": "dummy db", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": false, "stats": true, "dataValidate": false, "isMerge": false, "dbCloneCDC": false, "sourceAndTarget": false, "initialSync": true, "customMapping": false}, "createTime": {"$date": "2022-06-30T07:07:01.234Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120584"}, "databaseType": "gbase-8s", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.237Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120585"}, "databaseType": "custom_connection", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.241Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120586"}, "databaseType": "db2", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.244Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120587"}, "databaseType": "gaussdb200", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.249Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120588"}, "databaseType": "postgres", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.253Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120589"}, "databaseType": "greenplum", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.26Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa3635287112058a"}, "databaseType": "elasticsearch", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.263Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa3635287112058b"}, "databaseType": "mem_cache", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.267Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa3635287112058c"}, "databaseType": "log_collect", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.271Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa3635287112058d"}, "databaseType": "logCollector", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.275Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa3635287112058e"}, "databaseType": "redis", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.279Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa3635287112058f"}, "databaseType": "ma<PERSON>b", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.282Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120590"}, "databaseType": "kafka", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.286Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120591"}, "databaseType": "mq", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.29Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120592"}, "databaseType": "mysql pxc", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.293Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120593"}, "databaseType": "jira", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.297Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120594"}, "databaseType": "dameng", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.301Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120595"}, "databaseType": "hive", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.305Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120596"}, "databaseType": "hbase", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.31Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120597"}, "databaseType": "kudu", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.313Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120598"}, "databaseType": "hana", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.317Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa36352871120599"}, "databaseType": "tidb", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.32Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa3635287112059a"}, "databaseType": "vika", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.325Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa3635287112059b"}, "databaseType": "clickhouse", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.328Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa3635287112059c"}, "databaseType": "kundb", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.332Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa3635287112059d"}, "databaseType": "adb_mysql", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.335Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa3635287112059e"}, "databaseType": "adb_postgres", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.339Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa3635287112059f"}, "databaseType": "hazelcast_cloud_cluster", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.343Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa363528711205a0"}, "databaseType": "hazelcastIMDG", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.348Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa363528711205a1"}, "databaseType": "ali<PERSON>_mysql", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.352Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa363528711205a2"}, "databaseType": "ali<PERSON>_mariadb", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.356Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa363528711205a3"}, "databaseType": "aliyun_sqlserver", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.361Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa363528711205a4"}, "databaseType": "aliyun_postgres", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.365Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa363528711205a5"}, "databaseType": "aliyun_mongodb", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.369Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}, {"_id": {"$oid": "62bd4b95aa363528711205a6"}, "databaseType": "unknown", "supportedList": {"onData": true, "syncProgress": true, "increamentalSync": true, "stats": true, "dataValidate": true, "isMerge": false, "dbCloneCDC": true, "sourceAndTarget": true, "initialSync": true, "customMapping": true}, "createTime": {"$date": "2022-06-30T07:07:01.373Z"}, "user_id": "62bc5008d4958d013d97c7a6", "_class": "com.tapdata.tm.libSupported.entity.LibSupportedsEntity"}]}, {"insert": "AccessToken", "documents": [{"_id": "035d9df7ce714e6390a823efc0c017ac291d5729f1d2444aaa17a68fb97f62d2", "ttl": 1209600, "created": {"$date": "2022-06-29T13:14:22.924Z"}, "userId": {"$oid": "62bc5008d4958d013d97c7a6"}, "last_updated": {"$date": "2022-06-29T13:14:22.924Z"}, "authType": "access_code", "_class": "com.tapdata.tm.accessToken.entity.AccessTokenEntity"}, {"_id": "04d94cfa9c9040bba7e9d63fba547a68eb5997e449ac47dfb9baec46963f4562", "ttl": 1209600, "created": {"$date": "2022-06-30T07:06:59.284Z"}, "userId": {"$oid": "62bc5008d4958d013d97c7a6"}, "last_updated": {"$date": "2022-06-30T07:06:59.284Z"}, "authType": "access_code", "_class": "com.tapdata.tm.accessToken.entity.AccessTokenEntity"}, {"_id": "842e0e14591d45c6a57f87312bebce4cc76ff81a2b0d47a4b4cc4186bf9fb0b4", "ttl": 1209600, "created": {"$date": "2022-06-30T07:07:09.996Z"}, "userId": {"$oid": "62bc5008d4958d013d97c7a6"}, "last_updated": {"$date": "2022-06-30T07:07:09.996Z"}, "authType": "access_code", "_class": "com.tapdata.tm.accessToken.entity.AccessTokenEntity"}]}, {"insert": "ScheduleTasks", "documents": [{"_id": {"$oid": "62bc500bd4958d013d97df32"}, "task_type": "BG_JOBS", "period": 600000, "status": "paused", "task_name": "data_qaulity_stats_task", "task_profile": "DAAS"}, {"_id": {"$oid": "62bc500bd4958d013d97df44"}, "task_type": "META_DATA_ANALYZER", "period": 1000, "status": "paused", "task_name": "meta_data_analyzer_task", "task_profile": "DAAS"}, {"_id": {"$oid": "62bc500bd4958d013d97df4a"}, "task_type": "METADATA_INSTANCES_ANALYZE", "period": 1000, "status": "paused", "task_name": "metadataInstances_analyze_task", "task_profile": "DAAS"}, {"_id": {"$oid": "62bc500bd4958d013d97df53"}, "task_type": "DASHBOARD_STATS", "period": 600000, "status": "paused", "task_name": "dashboard_stats_task", "task_profile": "DAAS"}, {"_id": {"$oid": "62bc500bd4958d013d97df59"}, "task_type": "REPLICATE_DATA_STATS", "period": 600000, "status": "paused", "task_name": "replicate_data_stats_task", "task_profile": "DAAS"}, {"_id": {"$oid": "62bc500bd4958d013d97df63"}, "task_name": "api_stats_allTime_allUser_allAPI_task", "period": 60000, "status": "paused", "task_profile": "DAAS", "task_type": "API_STATS_ALLTIME_ALLUSER_ALLAPI"}, {"_id": {"$oid": "62bc500bd4958d013d97df68"}, "task_name": "api_stats_allTime_allUser_everyAPI_task", "period": 60000, "status": "paused", "task_profile": "DAAS", "task_type": "API_STATS_ALLTIME_ALLUSER_EVERYAPI"}, {"_id": {"$oid": "62bc500bd4958d013d97df6d"}, "task_name": "meta_data_stats_mongo_indexes_task", "period": 30000, "status": "scheduling", "task_profile": "DAAS", "task_type": "META_DATA_STATS_MONGO_INDEXES", "agent_id": "842fc5cd-ff19-4c6d-9e75-4447bc65765e", "ping_time": 1656573476081, "user_id": "62bc5008d4958d013d97c7a6"}, {"_id": {"$oid": "62bc500bd4958d013d97df71"}, "task_type": "CLEAR_TARGET_OFFSET", "period": 3600000, "status": "scheduling", "task_name": "clear target offset", "task_profile": "DEFAULT", "agent_id": "842fc5cd-ff19-4c6d-9e75-4447bc65765e", "ping_time": 1656573429011, "user_id": "62bc5008d4958d013d97c7a6"}, {"_id": {"$oid": "62bc500bd4958d013d97df74"}, "task_type": "TIMING_SYNC", "period": 30000, "status": "scheduling", "task_name": "timing_sync_tasks", "task_profile": "DEFAULT", "agent_id": "842fc5cd-ff19-4c6d-9e75-4447bc65765e", "ping_time": 1656573476079, "user_id": "62bc5008d4958d013d97c7a6"}, {"_id": {"$oid": "62bc500bd4958d013d97df78"}, "task_type": "CDC_EVENTS_TTL", "period": 600000, "status": "scheduling", "task_name": "CDC Events TTL", "task_profile": "DEFAULT", "agent_id": "842fc5cd-ff19-4c6d-9e75-4447bc65765e", "ping_time": 1656573476077, "user_id": "62bc5008d4958d013d97c7a6"}, {"_id": {"$oid": "62bc500bd4958d013d97df80"}, "task_type": "CLEAR_PG_SLOT", "period": 3600000, "status": "scheduling", "task_name": "Clear PostgreSQL Slot", "task_profile": "DEFAULT", "agent_id": "842fc5cd-ff19-4c6d-9e75-4447bc65765e", "ping_time": 1656573432045, "user_id": "62bc5008d4958d013d97c7a6"}, {"_id": {"$oid": "62bc500bd4958d013d97df9c"}, "task_type": "CLEAR_AGG_TARGET_TTL_DATA", "cron_expression": "0 0 02 * * ?", "status": "paused", "task_name": "clear_agg_target_ttl_data", "task_profile": "DEFAULT"}]}, {"insert": "MetadataDefinition", "documents": [{"_id": {"$oid": "62bc500b6beb33b125022018"}, "item_type": ["collection", "mongo_view", "table", "view"], "last_updated": {"$date": "2022-06-29T13:13:47.376Z"}, "value": "DaaS"}, {"_id": {"$oid": "62bc500b6beb33b125022019"}, "item_type": ["database"], "last_updated": {"$date": "2022-06-29T13:13:47.385Z"}, "value": "Connections"}, {"_id": {"$oid": "62bc500b6beb33b12502201a"}, "item_type": ["api"], "last_updated": {"$date": "2022-06-29T13:13:47.386Z"}, "value": "Data API"}, {"_id": {"$oid": "62bc500b6beb33b12502201b"}, "item_type": [], "last_updated": {"$date": "2022-06-29T13:13:47.388Z"}, "value": "Projects"}, {"_id": {"$oid": "62bc500b6beb33b12502201c"}, "value": "FDM", "parent_id": "62bc500b6beb33b125022018", "item_type": ["collection", "mongo_view", "table", "view"], "last_updated": {"$date": "2022-06-29T13:13:47.378Z"}}, {"_id": {"$oid": "62bc500b6beb33b12502201d"}, "value": "MDM", "parent_id": "62bc500b6beb33b125022018", "item_type": ["collection", "mongo_view", "table", "view"], "last_updated": {"$date": "2022-06-29T13:13:47.38Z"}}, {"_id": {"$oid": "62bc500b6beb33b12502201e"}, "value": "ADM", "parent_id": "62bc500b6beb33b125022018", "item_type": ["collection", "mongo_view", "table", "view"], "last_updated": {"$date": "2022-06-29T13:13:47.381Z"}}, {"_id": {"$oid": "62bc500b6beb33b12502201f"}, "value": "TDM", "parent_id": "62bc500b6beb33b125022018", "item_type": ["collection", "mongo_view", "table", "view"], "last_updated": {"$date": "2022-06-29T13:13:47.383Z"}}]}, {"insert": "User", "documents": [{"_id": {"$oid": "62bc5008d4958d013d97c7a6"}, "email": "<EMAIL>", "role": 1, "accesscode": "3324cfdf-7d3e-4792-bd32-571638d4562f", "account_status": 1, "emailVerified": true, "emailVerified_from_frontend": true, "password": "$2a$10$VmrRIazvMqgNc.S8kSx5Ju0GOZnY3lh.vS7VLgNBJ52t4TVrqb77K"}]}, {"insert": "Application", "documents": [{"_id": {"$oid": "5c0e750b7a5cd42464a5099d"}, "clientId": "5c0e750b7a5cd42464a5099d", "clientSecret": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9", "clientName": "Data Explorer", "clientAuthenticationMethods": ["client_secret_post", "post", "basic", "client_secret_basic"], "grantTypes": ["implicit", "refresh_token", "client_credentials", "password", "authorization_code", "urn:ietf:params:oauth:grant-type:jwt-bearer"], "redirectUris": "http://127.0.0.1", "scopes": ["admin"], "clientSettings": "{\"@class\":\"java.util.HashMap\",\"setting.client.require-user-consent\":true,\"setting.client.require-proof-key\":false}", "tokenSettings": "{\"@class\":\"java.util.HashMap\",\"setting.token.access-token-time-to-live\":[\"java.time.Duration\",1209600.*********],\"setting.token.refresh-token-time-to-live\":[\"java.time.Duration\",1209600.*********],\"setting.token.id-token-signature-algorithm\":[\"org.springframework.security.oauth2.jose.jws.SignatureAlgorithm\",\"RS256\"],\"setting.token.reuse-refresh-tokens\":true}", "_class": "com.tapdata.tm.oauth2.entity.RegisteredClientEntity"}]}, {"insert": "DrsScheduleLock", "documents": [{"_id": "checkScheduledDataFlow", "lockUntil": {"$date": "2022-06-30T07:16:36.647Z"}, "lockedAt": {"$date": "2022-06-30T07:16:31.647Z"}, "lockedBy": "f03d94adc312"}, {"_id": "checkScheduledSubTask", "lockUntil": {"$date": "2022-06-30T07:16:36.264Z"}, "lockedAt": {"$date": "2022-06-30T07:16:31.264Z"}, "lockedBy": "f03d94adc312"}, {"_id": "checkStoppingSubTask", "lockUntil": {"$date": "2022-06-30T07:16:36.441Z"}, "lockedAt": {"$date": "2022-06-30T07:16:31.441Z"}, "lockedBy": "f03d94adc312"}, {"_id": "migrateDagPlanStartLock", "lockUntil": {"$date": "2022-06-30T07:16:36.166Z"}, "lockedAt": {"$date": "2022-06-30T07:16:31.166Z"}, "lockedBy": "f03d94adc312"}, {"_id": "restart_subtask_lock", "lockUntil": {"$date": "2022-06-30T07:16:36.115Z"}, "lockedAt": {"$date": "2022-06-30T07:16:31.115Z"}, "lockedBy": "f03d94adc312"}]}, {"insert": "JavascriptFunctions", "documents": [{"_id": {"$oid": "62bc500bd4958d013d97e284"}, "function_name": "tcp.toObject(mirror:ScriptObjectMirror)", "className": "tcp", "format": "tcp.toObject(mirror:ScriptObjectMirror)", "functionName": "tcp.toObject", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "toObject", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e288"}, "function_name": "tcp.assemble(vinCode:String, ts:long, type:String, flag:byte)", "className": "tcp", "format": "tcp.assemble(vinCode:String, ts:long, type:String, flag:byte)", "functionName": "tcp.assemble", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "assemble", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e28d"}, "function_name": "tcp.login(host:String, port:int, dataStr:String)", "className": "tcp", "format": "tcp.login(host:String, port:int, dataStr:String)", "functionName": "tcp.login", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "login", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e298"}, "function_name": "tcp.logout(host:String, port:int, dataStr:String)", "className": "tcp", "format": "tcp.logout(host:String, port:int, dataStr:String)", "functionName": "tcp.logout", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "logout", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e2a0"}, "function_name": "tcp.bufBytes(begin:byte[], after:byte[])", "className": "tcp", "format": "tcp.bufBytes(begin:byte[], after:byte[])", "functionName": "tcp.bufBytes", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "bufBytes", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e2a8"}, "function_name": "tcp.send(host:String, port:int, readTimeout:int, dataMap:Map, success:Consumer, fail:Consumer, noRsp:Consumer)", "className": "tcp", "format": "tcp.send(host:String, port:int, readTimeout:int, dataMap:Map, success:Consumer, fail:Consumer, noRsp:Consumer)", "functionName": "tcp.send", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "send", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e2ae"}, "function_name": "tcp.send(host:String, port:int, dataStr:String)", "className": "tcp", "format": "tcp.send(host:String, port:int, dataStr:String)", "functionName": "tcp.send", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "send", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e2b4"}, "function_name": "tcp.pbConvert_Periodic(dataMap:Map)", "className": "tcp", "format": "tcp.pbConvert_Periodic(dataMap:Map)", "functionName": "tcp.pbConvert_Periodic", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "pbConvert_Periodic", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e2b6"}, "function_name": "tcp.pbConvert_PlatformLogin(dataMap:Map)", "className": "tcp", "format": "tcp.pbConvert_PlatformLogin(dataMap:Map)", "functionName": "tcp.pbConvert_PlatformLogin", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "pbConvert_PlatformLogin", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e2ba"}, "function_name": "tcp.pbConvert_PlatformLogout(dataMap:Map)", "className": "tcp", "format": "tcp.pbConvert_PlatformLogout(dataMap:Map)", "functionName": "tcp.pbConvert_PlatformLogout", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "pbConvert_PlatformLogout", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e2c2"}, "function_name": "tcp.pbConvert_VehicleLogin(dataMap:Map)", "className": "tcp", "format": "tcp.pbConvert_VehicleLogin(dataMap:Map)", "functionName": "tcp.pbConvert_VehicleLogin", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "pbConvert_VehicleLogin", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e2cb"}, "function_name": "tcp.pbConvert_VehicleLogout(dataMap:Map)", "className": "tcp", "format": "tcp.pbConvert_VehicleLogout(dataMap:Map)", "functionName": "tcp.pbConvert_VehicleLogout", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "pbConvert_VehicleLogout", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e2db"}, "function_name": "tcp.pbConvert_util(jsonStr:String, builder:Builder)", "className": "tcp", "format": "tcp.pbConvert_util(jsonStr:String, builder:Builder)", "functionName": "tcp.pbConvert_util", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "pbConvert_util", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e2e3"}, "function_name": "tcp.pbConvert(jsonStr:String, pbSchemaStr:String, msgTypeName:String, pbSchemaKey:String, uuid:String)", "className": "tcp", "format": "tcp.pbConvert(jsonStr:String, pbSchemaStr:String, msgTypeName:String, pbSchemaKey:String, uuid:String)", "functionName": "tcp.pbConvert", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "pbConvert", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e2e9"}, "function_name": "tcp.pbConvert(dataMap:Map, schemaObj:Object, msgTypeName:String, pbSchemaKey:String)", "className": "tcp", "format": "tcp.pbConvert(dataMap:Map, schemaObj:Object, msgTypeName:String, pbSchemaKey:String)", "functionName": "tcp.pbConvert", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "pbConvert", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e2eb"}, "function_name": "tcp.pbConvert(dataMap:Map, schemaStr:String, msgTypeName:String, pbSchemaKey:String)", "className": "tcp", "format": "tcp.pbConvert(dataMap:Map, schemaStr:String, msgTypeName:String, pbSchemaKey:String)", "functionName": "tcp.pbConvert", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "pbConvert", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e2f0"}, "function_name": "tcp.pbConvert(jsonStr:String, schemaObj:Object, msgTypeName:String, pbSchemaKey:String, uuid:String)", "className": "tcp", "format": "tcp.pbConvert(jsonStr:String, schemaObj:Object, msgTypeName:String, pbSchemaKey:String, uuid:String)", "functionName": "tcp.pbConvert", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "pbConvert", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e2f4"}, "function_name": "tcp.pbConvert(jsonStr:String, dynamicSchema:DynamicSchema, msgTypeName:String, uuid:String)", "className": "tcp", "format": "tcp.pbConvert(jsonStr:String, dynamicSchema:DynamicSchema, msgTypeName:String, uuid:String)", "functionName": "tcp.pbConvert", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "pbConvert", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e2fc"}, "function_name": "tcp.send_data(host:String, port:int, dataStr:String)", "className": "tcp", "format": "tcp.send_data(host:String, port:int, dataStr:String)", "functionName": "tcp.send_data", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "send_data", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e302"}, "function_name": "mongo.getData(mongodbUri:String, collection:String)", "className": "mongo", "format": "mongo.getData(mongodbUri:String, collection:String)", "functionName": "mongo.getData", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getData", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e30f"}, "function_name": "mongo.getData(mongodbUri:String, collection:String, filter:Map, limit:int, sort:Map)", "className": "mongo", "format": "mongo.getData(mongodbUri:String, collection:String, filter:Map, limit:int, sort:Map)", "functionName": "mongo.getData", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getData", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e31f"}, "function_name": "mongo.getData(mongodbUri:String, collection:String, filter:Map)", "className": "mongo", "format": "mongo.getData(mongodbUri:String, collection:String, filter:Map)", "functionName": "mongo.getData", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getData", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e324"}, "function_name": "mongo.update(mongodbUri:String, collection:String, filter:Map, update:Map)", "className": "mongo", "format": "mongo.update(mongodbUri:String, collection:String, filter:Map, update:Map)", "functionName": "mongo.update", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "update", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e32a"}, "function_name": "mongo.delete(mongodbUri:String, collection:String, filter:Map)", "className": "mongo", "format": "mongo.delete(mongodbUri:String, collection:String, filter:Map)", "functionName": "mongo.delete", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "delete", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e332"}, "function_name": "mongo.insert(mongodbUri:String, collection:String, inserts:List)", "className": "mongo", "format": "mongo.insert(mongodbUri:String, collection:String, inserts:List)", "functionName": "mongo.insert", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "insert", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e33b"}, "function_name": "mongo.insert(mongodbUri:String, collection:String, inserts:Map)", "className": "mongo", "format": "mongo.insert(mongodbUri:String, collection:String, inserts:Map)", "functionName": "mongo.insert", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "insert", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e34b"}, "function_name": "rest.patch(url:String, paramsMap:Map, headersMap:Map, connectTimeout:int, readTimeout:int)", "className": "rest", "format": "rest.patch(url:String, paramsMap:Map, headersMap:Map, connectTimeout:int, readTimeout:int)", "functionName": "rest.patch", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "patch", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e366"}, "function_name": "rest.patch(url:String, paramsMap:Map)", "className": "rest", "format": "rest.patch(url:String, paramsMap:Map)", "functionName": "rest.patch", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "patch", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e36e"}, "function_name": "rest.patch(url:String, paramsMap:Map, connectTimeout:int, readTimeout:int)", "className": "rest", "format": "rest.patch(url:String, paramsMap:Map, connectTimeout:int, readTimeout:int)", "functionName": "rest.patch", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "patch", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e377"}, "function_name": "rest.patch(url:String, paramsMap:Map, headersMap:Map)", "className": "rest", "format": "rest.patch(url:String, paramsMap:Map, headersMap:Map)", "functionName": "rest.patch", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "patch", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e37b"}, "function_name": "rest.post(url:String, paramsMap:Map, returnType:String)", "className": "rest", "format": "rest.post(url:String, paramsMap:Map, returnType:String)", "functionName": "rest.post", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "post", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e383"}, "function_name": "rest.post(url:String, paramsMap:Map, returnType:String, connectTimeout:int, readTimeout:int)", "className": "rest", "format": "rest.post(url:String, paramsMap:Map, returnType:String, connectTimeout:int, readTimeout:int)", "functionName": "rest.post", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "post", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e389"}, "function_name": "rest.post(url:String, paramsMap:Map, connectTimeout:int, readTimeout:int)", "className": "rest", "format": "rest.post(url:String, paramsMap:Map, connectTimeout:int, readTimeout:int)", "functionName": "rest.post", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "post", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e38f"}, "function_name": "rest.post(url:String, paramsMap:Map)", "className": "rest", "format": "rest.post(url:String, paramsMap:Map)", "functionName": "rest.post", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "post", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e396"}, "function_name": "rest.post(url:String, paramsMap:Map, headersMap:Map, returnType:String)", "className": "rest", "format": "rest.post(url:String, paramsMap:Map, headersMap:Map, returnType:String)", "functionName": "rest.post", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "post", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e39e"}, "function_name": "rest.post(url:String, paramsMap:Map, headersMap:Map, returnType:String, connectTimeout:int, readTimeout:int)", "className": "rest", "format": "rest.post(url:String, paramsMap:Map, headersMap:Map, returnType:String, connectTimeout:int, readTimeout:int)", "functionName": "rest.post", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "post", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e3a7"}, "function_name": "rest.post(url:String, params:byte[], headersMap:MultiValueMap, returnType:String)", "className": "rest", "format": "rest.post(url:String, params:byte[], headersMap:MultiValueMap, returnType:String)", "functionName": "rest.post", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "post", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e3ac"}, "function_name": "rest.post(url:String, params:byte[], headersMap:MultiValueMap, returnType:String, connectTimeout:int, readTimeout:int)", "className": "rest", "format": "rest.post(url:String, params:byte[], headersMap:MultiValueMap, returnType:String, connectTimeout:int, readTimeout:int)", "functionName": "rest.post", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "post", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e3b1"}, "function_name": "rest.post(url:String)", "className": "rest", "format": "rest.post(url:String)", "functionName": "rest.post", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "post", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e3b6"}, "function_name": "rest.post(url:String, connectTimeout:int, readTimeout:int)", "className": "rest", "format": "rest.post(url:String, connectTimeout:int, readTimeout:int)", "functionName": "rest.post", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "post", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e3bc"}, "function_name": "rest.post(url:String, returnType:String)", "className": "rest", "format": "rest.post(url:String, returnType:String)", "functionName": "rest.post", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "post", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e3bf"}, "function_name": "rest.post(url:String, returnType:String, connectTimeout:int, readTimeout:int)", "className": "rest", "format": "rest.post(url:String, returnType:String, connectTimeout:int, readTimeout:int)", "functionName": "rest.post", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "post", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e3c3"}, "function_name": "rest.asciiSort(inMap:Map, sort:String)", "className": "rest", "format": "rest.asciiSort(inMap:Map, sort:String)", "functionName": "rest.asciiSort", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "asciiSort", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e3cb"}, "function_name": "rest.recursiveMap(map:Map, pKey:String)", "className": "rest", "format": "rest.recursiveMap(map:Map, pKey:String)", "functionName": "rest.recursiveMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "recursiveMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e3d0"}, "function_name": "rest.recursiveListMap(list:List, pKey:String)", "className": "rest", "format": "rest.recursiveListMap(list:List, pKey:String)", "functionName": "rest.recursiveListMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "recursiveListMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e3d6"}, "function_name": "rest.get(url:String, returnType:String, connectTimeout:int, readTimeout:int)", "className": "rest", "format": "rest.get(url:String, returnType:String, connectTimeout:int, readTimeout:int)", "functionName": "rest.get", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "get", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e3da"}, "function_name": "rest.get(url:String, headersMap:Map)", "className": "rest", "format": "rest.get(url:String, headersMap:Map)", "functionName": "rest.get", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "get", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e3df"}, "function_name": "rest.get(url:String, headersMap:Map, connectTimeout:int, readTimeout:int)", "className": "rest", "format": "rest.get(url:String, headersMap:Map, connectTimeout:int, readTimeout:int)", "functionName": "rest.get", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "get", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e3e8"}, "function_name": "rest.get(url:String, returnType:String)", "className": "rest", "format": "rest.get(url:String, returnType:String)", "functionName": "rest.get", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "get", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e3ee"}, "function_name": "rest.get(url:String, connectTimeout:int, readTimeout:int)", "className": "rest", "format": "rest.get(url:String, connectTimeout:int, readTimeout:int)", "functionName": "rest.get", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "get", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e3f1"}, "function_name": "rest.get(url:String)", "className": "rest", "format": "rest.get(url:String)", "functionName": "rest.get", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "get", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e3f6"}, "function_name": "rest.get(url:String, headersMap:Map, returnType:String)", "className": "rest", "format": "rest.get(url:String, headersMap:Map, returnType:String)", "functionName": "rest.get", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "get", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e3f8"}, "function_name": "rest.get(url:String, headersMap:Map, returnType:String, connectTimeout:int, readTimeout:int)", "className": "rest", "format": "rest.get(url:String, headersMap:Map, returnType:String, connectTimeout:int, readTimeout:int)", "functionName": "rest.get", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "get", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e3fa"}, "function_name": "rest.delete(url:String, headersMap:Map, connectTimeout:int, readTimeout:int)", "className": "rest", "format": "rest.delete(url:String, headersMap:Map, connectTimeout:int, readTimeout:int)", "functionName": "rest.delete", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "delete", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e3fd"}, "function_name": "rest.delete(url:String, headersMap:Map)", "className": "rest", "format": "rest.delete(url:String, headersMap:Map)", "functionName": "rest.delete", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "delete", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e400"}, "function_name": "rest.delete(url:String, connectTimeout:int, readTimeout:int)", "className": "rest", "format": "rest.delete(url:String, connectTimeout:int, readTimeout:int)", "functionName": "rest.delete", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "delete", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e406"}, "function_name": "rest.delete(url:String)", "className": "rest", "format": "rest.delete(url:String)", "functionName": "rest.delete", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "delete", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e40e"}, "function_name": "rest.sort(inMap:Map)", "className": "rest", "format": "rest.sort(inMap:Map)", "functionName": "rest.sort", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "sort", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e412"}, "function_name": "networkUtil.GetAddress(addressType:String)", "className": "networkUtil", "format": "networkUtil.GetAddress(addressType:String)", "functionName": "networkUtil.GetAddress", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "GetAddress", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e419"}, "function_name": "HanLPUtil.hanLPParticiple(inputString:String, language:String)", "className": "<PERSON><PERSON><PERSON><PERSON>", "format": "HanLPUtil.hanLPParticiple(inputString:String, language:String)", "functionName": "HanLPUtil.hanLPParticiple", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "hanLPParticiple", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e41f"}, "function_name": "DateUtil.sameHour(one:Date, two:Date)", "className": "DateUtil", "format": "DateUtil.sameHour(one:Date, two:Date)", "functionName": "DateUtil.sameHour", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "sameHour", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e421"}, "function_name": "DateUtil.sameYear(one:Date, two:Date)", "className": "DateUtil", "format": "DateUtil.sameYear(one:Date, two:Date)", "functionName": "DateUtil.sameYear", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "sameYear", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e424"}, "function_name": "DateUtil.addYears(date:Date, years:int)", "className": "DateUtil", "format": "DateUtil.addYears(date:Date, years:int)", "functionName": "DateUtil.addYears", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "addYears", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e429"}, "function_name": "DateUtil.addYears(instant:Instant, years:int)", "className": "DateUtil", "format": "DateUtil.addYears(instant:Instant, years:int)", "functionName": "DateUtil.addYears", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "addYears", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e42c"}, "function_name": "DateUtil.sameDay(one:Date, two:Date)", "className": "DateUtil", "format": "DateUtil.sameDay(one:Date, two:Date)", "functionName": "DateUtil.sameDay", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "sameDay", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e430"}, "function_name": "DateUtil.addDays(date:Date, days:int)", "className": "DateUtil", "format": "DateUtil.addDays(date:Date, days:int)", "functionName": "DateUtil.addDays", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "addDays", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e432"}, "function_name": "DateUtil.addDays(instant:Instant, days:int)", "className": "DateUtil", "format": "DateUtil.addDays(instant:Instant, days:int)", "functionName": "DateUtil.addDays", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "addDays", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e434"}, "function_name": "DateUtil.convertTimestamp(timestamp:long, fromTimeZone:TimeZone, toTimeZone:TimeZone)", "className": "DateUtil", "format": "DateUtil.convertTimestamp(timestamp:long, fromTimeZone:TimeZone, toTimeZone:TimeZone)", "functionName": "DateUtil.convertTimestamp", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "convertTimestamp", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e436"}, "function_name": "DateUtil.timeStamp2Date(millSeconds:String, format:String)", "className": "DateUtil", "format": "DateUtil.timeStamp2Date(millSeconds:String, format:String)", "functionName": "DateUtil.timeStamp2Date", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "timeStamp2Date", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e43b"}, "function_name": "DateUtil.string2Instant(zoneId:ZoneId, dateString:String)", "className": "DateUtil", "format": "DateUtil.string2Instant(zoneId:ZoneId, dateString:String)", "functionName": "DateUtil.string2Instant", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "string2Instant", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e43e"}, "function_name": "DateUtil.long2Nanos(times:Long, scale:Integer)", "className": "DateUtil", "format": "DateUtil.long2Nanos(times:Long, scale:Integer)", "functionName": "DateUtil.long2Nanos", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "long2Nanos", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e441"}, "function_name": "DateUtil.nanos2Instant(times:Long, fromZoneId:ZoneId)", "className": "DateUtil", "format": "DateUtil.nanos2Instant(times:Long, fromZoneId:ZoneId)", "functionName": "DateUtil.nanos2Instant", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "nanos2Instant", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e444"}, "function_name": "DateUtil.date2Timestamp(date:Date, toZoneId:ZoneId)", "className": "DateUtil", "format": "DateUtil.date2Timestamp(date:Date, toZoneId:ZoneId)", "functionName": "DateUtil.date2Timestamp", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "date2Timestamp", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e44c"}, "function_name": "DateUtil.instant2Timestamp(instant:Instant, toZoneId:ZoneId)", "className": "DateUtil", "format": "DateUtil.instant2Timestamp(instant:Instant, toZoneId:ZoneId)", "functionName": "DateUtil.instant2Timestamp", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "instant2Timestamp", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e452"}, "function_name": "DateUtil.millis2Time(millis:int, fromZoneId:ZoneId)", "className": "DateUtil", "format": "DateUtil.millis2Time(millis:int, fromZoneId:ZoneId)", "functionName": "DateUtil.millis2Time", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "millis2Time", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e456"}, "function_name": "DateUtil.getFullYear(date:Date)", "className": "DateUtil", "format": "DateUtil.getFullYear(date:Date)", "functionName": "DateUtil.getFullYear", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getFullYear", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e45d"}, "function_name": "DateUtil.getFullYear(instant:Instant)", "className": "DateUtil", "format": "DateUtil.getFullYear(instant:Instant)", "functionName": "DateUtil.getFullYear", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getFullYear", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e462"}, "function_name": "DateUtil.getMilliSeconds(date:Date)", "className": "DateUtil", "format": "DateUtil.getMilliSeconds(date:Date)", "functionName": "DateUtil.getMilliSeconds", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getMilliSeconds", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e466"}, "function_name": "DateUtil.getMilliSeconds(instant:Instant)", "className": "DateUtil", "format": "DateUtil.getMilliSeconds(instant:Instant)", "functionName": "DateUtil.getMilliSeconds", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getMilliSeconds", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e469"}, "function_name": "DateUtil.convertMicros(micros:long, fromTimeZone:TimeZone, toTimeZone:TimeZone)", "className": "DateUtil", "format": "DateUtil.convertMicros(micros:long, fromTimeZone:TimeZone, toTimeZone:TimeZone)", "functionName": "DateUtil.convertMicros", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "convertMicros", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e46b"}, "function_name": "DateUtil.toCalendar(date:Date, timeZone:TimeZone)", "className": "DateUtil", "format": "DateUtil.toCalendar(date:Date, timeZone:TimeZone)", "functionName": "DateUtil.toCalendar", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "toCalendar", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e473"}, "function_name": "DateUtil.toCalendar(date:Date)", "className": "DateUtil", "format": "DateUtil.toCalendar(date:Date)", "functionName": "DateUtil.toCalendar", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "toCalendar", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e484"}, "function_name": "DateUtil.determineDateFormat(dateString:String)", "className": "DateUtil", "format": "DateUtil.determineDateFormat(dateString:String)", "functionName": "DateUtil.determineDateFormat", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "determineDateFormat", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e48d"}, "function_name": "DateUtil.addMonths(date:Date, months:int)", "className": "DateUtil", "format": "DateUtil.addMonths(date:Date, months:int)", "functionName": "DateUtil.addMonths", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "addMonths", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e491"}, "function_name": "DateUtil.addMonths(instant:Instant, months:int)", "className": "DateUtil", "format": "DateUtil.addMonths(instant:Instant, months:int)", "functionName": "DateUtil.addMonths", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "addMonths", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e495"}, "function_name": "DateUtil.sameMonth(one:Date, two:Date)", "className": "DateUtil", "format": "DateUtil.sameMonth(one:Date, two:Date)", "functionName": "DateUtil.<PERSON>", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "sameMonth", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e49c"}, "function_name": "DateUtil.sameMinute(one:Date, two:Date)", "className": "DateUtil", "format": "DateUtil.sameMinute(one:Date, two:Date)", "functionName": "DateUtil.sameMinute", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "sameMinute", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e49f"}, "function_name": "DateUtil.sameSecond(one:Date, two:Date)", "className": "DateUtil", "format": "DateUtil.sameSecond(one:Date, two:Date)", "functionName": "DateUtil.sameSecond", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "sameSecond", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e4a4"}, "function_name": "DateUtil.elapsedYears(before:Date, after:Date)", "className": "DateUtil", "format": "DateUtil.elapsedYears(before:Date, after:Date)", "functionName": "DateUtil.elapsedYears", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "elapsedYears", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e4a6"}, "function_name": "DateUtil.elapsedMonths(before:Date, after:Date)", "className": "DateUtil", "format": "DateUtil.elapsedMonths(before:Date, after:Date)", "functionName": "DateUtil.elapsedM<PERSON>hs", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "elapsed<PERSON><PERSON><PERSON>", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e4ab"}, "function_name": "DateUtil.elapsedDays(before:Date, after:Date)", "className": "DateUtil", "format": "DateUtil.elapsedDays(before:Date, after:Date)", "functionName": "DateUtil.elapsedDays", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "elapsedDays", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e4ae"}, "function_name": "DateUtil.elapsedHours(before:Date, after:Date)", "className": "DateUtil", "format": "DateUtil.elapsedHours(before:Date, after:Date)", "functionName": "DateUtil.elapsedHours", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "elapsedHours", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e4b3"}, "function_name": "DateUtil.elapsedMinutes(before:Date, after:Date)", "className": "DateUtil", "format": "DateUtil.elapsedMinutes(before:Date, after:Date)", "functionName": "DateUtil.elapsedMinutes", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "elapsedMinutes", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e4b8"}, "function_name": "DateUtil.elapsedSeconds(before:Date, after:Date)", "className": "DateUtil", "format": "DateUtil.elapsedSeconds(before:Date, after:Date)", "functionName": "DateUtil.elapsedSeconds", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "elapsedSeconds", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e4c1"}, "function_name": "DateUtil.elapsedMillis(before:Date, after:Date)", "className": "DateUtil", "format": "DateUtil.elapsedMillis(before:Date, after:Date)", "functionName": "DateUtil.elapsed<PERSON>", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "elapsed<PERSON><PERSON><PERSON>", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e4c6"}, "function_name": "DateUtil.elapsedTime(before:Date, after:Date)", "className": "DateUtil", "format": "DateUtil.elapsedTime(before:Date, after:Date)", "functionName": "DateUtil.elapsedTime", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "elapsedTime", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e4ca"}, "function_name": "DateUtil.epochDaysToDate(data:Object)", "className": "DateUtil", "format": "DateUtil.epochDaysToDate(data:Object)", "functionName": "DateUtil.epochDaysToDate", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "epochDaysToDate", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e4cd"}, "function_name": "DateUtil.epochMillsToDate(data:Object, timeZone:TimeZone)", "className": "DateUtil", "format": "DateUtil.epochMillsToDate(data:Object, timeZone:TimeZone)", "functionName": "DateUtil.epochMillsToDate", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "epochMillsToDate", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e4d2"}, "function_name": "DateUtil.epochMicrosToDate(data:Object, timeZone:TimeZone)", "className": "DateUtil", "format": "DateUtil.epochMicrosToDate(data:Object, timeZone:TimeZone)", "functionName": "DateUtil.epochMicrosToDate", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "epochMicrosToDate", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e4d9"}, "function_name": "DateUtil.microseondsToStringTime(data:Object)", "className": "DateUtil", "format": "DateUtil.microseondsToStringTime(data:Object)", "functionName": "DateUtil.microseondsToStringTime", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "microseondsToStringTime", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e4ea"}, "function_name": "DateUtil.string2MysqlYear(yearStr:String)", "className": "DateUtil", "format": "DateUtil.string2MysqlYear(yearStr:String)", "functionName": "DateUtil.string2MysqlYear", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "string2MysqlYear", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e4ee"}, "function_name": "DateUtil.addHours(date:Date, hours:int)", "className": "DateUtil", "format": "DateUtil.addHours(date:Date, hours:int)", "functionName": "DateUtil.addHours", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "addHours", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e4f4"}, "function_name": "DateUtil.addHours(instant:Instant, hours:int)", "className": "DateUtil", "format": "DateUtil.addHours(instant:Instant, hours:int)", "functionName": "DateUtil.addHours", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "addHours", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e4f7"}, "function_name": "DateUtil.addMinutes(date:Date, minutes:int)", "className": "DateUtil", "format": "DateUtil.addMinutes(date:Date, minutes:int)", "functionName": "DateUtil.addMinutes", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "addMinutes", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e4fd"}, "function_name": "DateUtil.addMinutes(instant:Instant, minutes:int)", "className": "DateUtil", "format": "DateUtil.addMinutes(instant:Instant, minutes:int)", "functionName": "DateUtil.addMinutes", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "addMinutes", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e502"}, "function_name": "DateUtil.addSeconds(date:Date, seconds:int)", "className": "DateUtil", "format": "DateUtil.addSeconds(date:Date, seconds:int)", "functionName": "DateUtil.addSeconds", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "addSeconds", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e506"}, "function_name": "DateUtil.addSeconds(instant:Instant, seconds:int)", "className": "DateUtil", "format": "DateUtil.addSeconds(instant:Instant, seconds:int)", "functionName": "DateUtil.addSeconds", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "addSeconds", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e50a"}, "function_name": "DateUtil.addMillis(instant:Instant, millis:int)", "className": "DateUtil", "format": "DateUtil.addMillis(instant:Instant, millis:int)", "functionName": "DateUtil.add<PERSON>illis", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "<PERSON><PERSON><PERSON><PERSON>", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e50f"}, "function_name": "DateUtil.addMillis(date:Date, millis:int)", "className": "DateUtil", "format": "DateUtil.addMillis(date:Date, millis:int)", "functionName": "DateUtil.add<PERSON>illis", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "<PERSON><PERSON><PERSON><PERSON>", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e516"}, "function_name": "DateUtil.getYear(date:Date)", "className": "DateUtil", "format": "DateUtil.getYear(date:Date)", "functionName": "DateUtil.getYear", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getYear", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e51d"}, "function_name": "DateUtil.getYear(instant:Instant)", "className": "DateUtil", "format": "DateUtil.getYear(instant:Instant)", "functionName": "DateUtil.getYear", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getYear", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e525"}, "function_name": "DateUtil.getMonth(instant:Instant)", "className": "DateUtil", "format": "DateUtil.getMonth(instant:Instant)", "functionName": "DateUtil.getMonth", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getMonth", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e52a"}, "function_name": "DateUtil.getMonth(date:Date)", "className": "DateUtil", "format": "DateUtil.getMonth(date:Date)", "functionName": "DateUtil.getMonth", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getMonth", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e52f"}, "function_name": "DateUtil.getDate(date:Date)", "className": "DateUtil", "format": "DateUtil.getDate(date:Date)", "functionName": "DateUtil.getDate", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getDate", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e534"}, "function_name": "DateUtil.getDate(instant:Instant)", "className": "DateUtil", "format": "DateUtil.getDate(instant:Instant)", "functionName": "DateUtil.getDate", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getDate", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e539"}, "function_name": "DateUtil.getHours(date:Date)", "className": "DateUtil", "format": "DateUtil.getHours(date:Date)", "functionName": "DateUtil.getHours", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getHours", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e53d"}, "function_name": "DateUtil.getHours(instant:Instant)", "className": "DateUtil", "format": "DateUtil.getHours(instant:Instant)", "functionName": "DateUtil.getHours", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getHours", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e541"}, "function_name": "DateUtil.getMinutes(instant:Instant)", "className": "DateUtil", "format": "DateUtil.getMinutes(instant:Instant)", "functionName": "DateUtil.getMinutes", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getMinutes", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e544"}, "function_name": "DateUtil.getMinutes(date:Date)", "className": "DateUtil", "format": "DateUtil.getMinutes(date:Date)", "functionName": "DateUtil.getMinutes", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getMinutes", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e546"}, "function_name": "DateUtil.getSeconds(date:Date)", "className": "DateUtil", "format": "DateUtil.getSeconds(date:Date)", "functionName": "DateUtil.getSeconds", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getSeconds", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e549"}, "function_name": "DateUtil.getSeconds(instant:Instant)", "className": "DateUtil", "format": "DateUtil.getSeconds(instant:Instant)", "functionName": "DateUtil.getSeconds", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getSeconds", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e54c"}, "function_name": "DateUtil.getTimeZone(timeZoneOffset:Integer)", "className": "DateUtil", "format": "DateUtil.getTimeZone(timeZoneOffset:Integer)", "functionName": "DateUtil.getTimeZone", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getTimeZone", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e54e"}, "function_name": "DateUtil.parse(dateObj:Object, timezone:Integer)", "className": "DateUtil", "format": "DateUtil.parse(dateObj:Object, timezone:Integer)", "functionName": "DateUtil.parse", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "parse", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e552"}, "function_name": "DateUtil.parse(dateObj:Object)", "className": "DateUtil", "format": "DateUtil.parse(dateObj:Object)", "functionName": "DateUtil.parse", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "parse", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e556"}, "function_name": "DateUtil.parse(dateString:String, dateFormat:String, timeZone:TimeZone)", "className": "DateUtil", "format": "DateUtil.parse(dateString:String, dateFormat:String, timeZone:TimeZone)", "functionName": "DateUtil.parse", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "parse", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e558"}, "function_name": "MapUtils.recursiveMapWhenLoadSchema(fields:List, dataMap:Map, tableName:String, parentKey:String)", "className": "MapUtils", "format": "MapUtils.recursiveMapWhenLoadSchema(fields:List, dataMap:Map, tableName:String, parentKey:String)", "functionName": "MapUtils.recursiveMapWhenLoadSchema", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "recursiveMapWhenLoadSchema", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e55a"}, "function_name": "MapUtils.recursiveRemoveByValuePredicate(map:Map, valuePredicate:Predicate)", "className": "MapUtils", "format": "MapUtils.recursiveRemoveByValuePredicate(map:Map, valuePredicate:Predicate)", "functionName": "MapUtils.recursiveRemoveByValuePredicate", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "recursiveRemoveByValuePredicate", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e55d"}, "function_name": "MapUtils.obj2Map(obj:Object)", "className": "MapUtils", "format": "MapUtils.obj2Map(obj:Object)", "functionName": "MapUtils.obj2Map", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "obj2Map", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e561"}, "function_name": "MapUtils.copyToNewMap(map:Map, newMap:Map)", "className": "MapUtils", "format": "MapUtils.copyToNewMap(map:Map, newMap:Map)", "functionName": "MapUtils.copyToNewMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "copyToNewMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e564"}, "function_name": "MapUtils.getValueByKey(dataMap:Map, key:String)", "className": "MapUtils", "format": "MapUtils.getValueByKey(dataMap:Map, key:String)", "functionName": "MapUtils.getValueByKey", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getValueByKey", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e567"}, "function_name": "MapUtils.getValueByKey(dataMap:Map, key:String, replacement:String)", "className": "MapUtils", "format": "MapUtils.getValueByKey(dataMap:Map, key:String, replacement:String)", "functionName": "MapUtils.getValueByKey", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getValueByKey", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e56b"}, "function_name": "MapUtils.needSplit(key:String)", "className": "MapUtils", "format": "MapUtils.needSplit(key:String)", "functionName": "MapUtils.needSplit", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "needSplit", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e573"}, "function_name": "MapUtils.removeValueByKey(dataMap:Map, key:String)", "className": "MapUtils", "format": "MapUtils.removeValueByKey(dataMap:Map, key:String)", "functionName": "MapUtils.removeValueByKey", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "remove<PERSON><PERSON>ueByKey", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e579"}, "function_name": "MapUtils.removeValueByKey(dataMap:Map, key:String, replacement:String)", "className": "MapUtils", "format": "MapUtils.removeValueByKey(dataMap:Map, key:String, replacement:String)", "functionName": "MapUtils.removeValueByKey", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "remove<PERSON><PERSON>ueByKey", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e57c"}, "function_name": "MapUtils.getValuePositionInMap(dataMap:Map, key:String, replacement:String)", "className": "MapUtils", "format": "MapUtils.getValuePositionInMap(dataMap:Map, key:String, replacement:String)", "functionName": "MapUtils.getValuePositionInMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getValuePositionInMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e57e"}, "function_name": "MapUtils.getValuePositionInMap(dataMap:Map, key:String)", "className": "MapUtils", "format": "MapUtils.getValuePositionInMap(dataMap:Map, key:String)", "functionName": "MapUtils.getValuePositionInMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getValuePositionInMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e581"}, "function_name": "MapUtils.deepCloneMap(map:Map, newMap:Map)", "className": "MapUtils", "format": "MapUtils.deepCloneMap(map:Map, newMap:Map)", "functionName": "MapUtils.deepCloneMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "deepCloneMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e587"}, "function_name": "MapUtils.copyToNewDocument(map:Map, newMap:Map)", "className": "MapUtils", "format": "MapUtils.copyToNewDocument(map:Map, newMap:Map)", "functionName": "MapUtils.copyToNewDocument", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "copyToNewDocument", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e58b"}, "function_name": "MapUtils.putValueInMap(dataMap:Map, key:String, value:Object)", "className": "MapUtils", "format": "MapUtils.putValueInMap(dataMap:Map, key:String, value:Object)", "functionName": "MapUtils.putValueInMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "putValueInMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e590"}, "function_name": "MapUtils.putValueInMap(dataMap:Map, key:String, value:Object, replacement:String)", "className": "MapUtils", "format": "MapUtils.putValueInMap(dataMap:Map, key:String, value:Object, replacement:String)", "functionName": "MapUtils.putValueInMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "putValueInMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e594"}, "function_name": "MapUtils.recursiveFlatMap(dataMap:Map, newMap:Map, parentKey:String)", "className": "MapUtils", "format": "MapUtils.recursiveFlatMap(dataMap:Map, newMap:Map, parentKey:String)", "functionName": "MapUtils.recursiveFlatMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "recursiveFlatMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e598"}, "function_name": "MapUtils.obj2Document(obj:Object)", "className": "MapUtils", "format": "MapUtils.obj2Document(obj:Object)", "functionName": "MapUtils.obj2Document", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "obj2Document", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e59c"}, "function_name": "MapUtils.removeKey(map:Object, key:String)", "className": "MapUtils", "format": "MapUtils.removeKey(map:Object, key:String)", "functionName": "MapUtils.removeKey", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "<PERSON><PERSON><PERSON>", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e5a3"}, "function_name": "MapUtils.retainKey(newMap:Object, oldMap:Object, key:String)", "className": "MapUtils", "format": "MapUtils.retainKey(newMap:Object, oldMap:Object, key:String)", "functionName": "MapUtils.retainKey", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "<PERSON><PERSON><PERSON>", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e5a9"}, "function_name": "MapUtils.replaceKey(oldKey:String, map:Object, newkey:String)", "className": "MapUtils", "format": "MapUtils.replaceKey(oldKey:String, map:Object, newkey:String)", "functionName": "MapUtils.replaceKey", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "<PERSON><PERSON><PERSON>", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e5ae"}, "function_name": "MapUtils.recursiveRemoveKey(map:Map, removeKey:String)", "className": "MapUtils", "format": "MapUtils.recursiveRemoveKey(map:Map, removeKey:String)", "functionName": "MapUtils.recursiveRemoveKey", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "recursiveRemoveKey", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e5b3"}, "function_name": "MapUtils.removeNullValue(map:Map)", "className": "MapUtils", "format": "MapUtils.removeNullValue(map:Map)", "functionName": "MapUtils.removeNullValue", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "remove<PERSON>ull<PERSON><PERSON><PERSON>", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e5b6"}, "function_name": "MapUtils.keyToLowerCase(map:Map, layerCounts:int, layerIndex:int)", "className": "MapUtils", "format": "MapUtils.keyToLowerCase(map:Map, layerCounts:int, layerIndex:int)", "functionName": "MapUtils.keyToLowerCase", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "keyToLowerCase", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e5bc"}, "function_name": "MapUtils.keyToLowerCase(map:Map)", "className": "MapUtils", "format": "MapUtils.keyToLowerCase(map:Map)", "functionName": "MapUtils.keyToLowerCase", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "keyToLowerCase", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e5c7"}, "function_name": "MapUtils.keyToUpperCase(map:Map)", "className": "MapUtils", "format": "MapUtils.keyToUpperCase(map:Map)", "functionName": "MapUtils.keyToUpperCase", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "keyToUpperCase", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e5d0"}, "function_name": "MapUtils.getAllKeys(map:Map, recursive:boolean)", "className": "MapUtils", "format": "MapUtils.getAllKeys(map:Map, recursive:boolean)", "functionName": "MapUtils.getAllKeys", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getAllKeys", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e5d6"}, "function_name": "MapUtils.main(args:String[])", "className": "MapUtils", "format": "MapUtils.main(args:String[])", "functionName": "MapUtils.main", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "main", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e5e0"}, "function_name": "MapUtils.containsKey(dataMap:Map, key:String)", "className": "MapUtils", "format": "MapUtils.containsKey(dataMap:Map, key:String)", "functionName": "MapUtils.containsKey", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "<PERSON><PERSON><PERSON>", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e5eb"}, "function_name": "Date.from(arg0:Instant)", "className": "Date", "format": "Date.from(arg0:Instant)", "functionName": "Date.from", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "from", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e5f0"}, "function_name": "Date.UTC(arg0:int, arg1:int, arg2:int, arg3:int, arg4:int, arg5:int)", "className": "Date", "format": "Date.UTC(arg0:int, arg1:int, arg2:int, arg3:int, arg4:int, arg5:int)", "functionName": "Date.UTC", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "UTC", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e5f6"}, "function_name": "Date.parse(arg0:String)", "className": "Date", "format": "Date.parse(arg0:String)", "functionName": "Date.parse", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "parse", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e5fd"}, "function_name": "JSONUtil.json2Map(json:String)", "className": "JSONUtil", "format": "JSONUtil.json2Map(json:String)", "functionName": "JSONUtil.json2Map", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "json2Map", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e608"}, "function_name": "JSONUtil.obj2Json(object:Object)", "className": "JSONUtil", "format": "JSONUtil.obj2Json(object:Object)", "functionName": "JSONUtil.obj2Json", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "obj2Json", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e60f"}, "function_name": "JSONUtil.map2Json(map:Map)", "className": "JSONUtil", "format": "JSONUtil.map2Json(map:Map)", "functionName": "JSONUtil.map2Json", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "map2Json", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e61d"}, "function_name": "JSONUtil.map2POJO(map:Map, className:Class)", "className": "JSONUtil", "format": "JSONUtil.map2POJO(map:Map, className:Class)", "functionName": "JSONUtil.map2POJO", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "map2POJO", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e624"}, "function_name": "JSONUtil.map2POJO(map:Map, typeReference:TypeReference)", "className": "JSONUtil", "format": "JSONUtil.map2POJO(map:Map, typeReference:TypeReference)", "functionName": "JSONUtil.map2POJO", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "map2POJO", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e628"}, "function_name": "JSONUtil.disableFeature(serializationFeature:SerializationFeature)", "className": "JSONUtil", "format": "JSONUtil.disableFeature(serializationFeature:SerializationFeature)", "functionName": "JSONUtil.disableFeature", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "disableFeature", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e62c"}, "function_name": "JSONUtil.enableFeature(serializationFeature:SerializationFeature)", "className": "JSONUtil", "format": "JSONUtil.enableFeature(serializationFeature:SerializationFeature)", "functionName": "JSONUtil.enableFeature", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "enableFeature", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e637"}, "function_name": "JSONUtil.json2List(json:String, classz:Class)", "className": "JSONUtil", "format": "JSONUtil.json2List(json:String, classz:Class)", "functionName": "JSONUtil.json2List", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "json2List", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e639"}, "function_name": "JSONUtil.obj2JsonPretty(object:Object)", "className": "JSONUtil", "format": "JSONUtil.obj2JsonPretty(object:Object)", "functionName": "JSONUtil.obj2JsonPretty", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "obj2JsonPretty", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e63c"}, "function_name": "JSONUtil.json2POJO(json:String, typeReference:TypeReference)", "className": "JSONUtil", "format": "JSONUtil.json2POJO(json:String, typeReference:TypeReference)", "functionName": "JSONUtil.json2POJO", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "json2POJO", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e641"}, "function_name": "JSONUtil.json2POJO(json:String, className:Class)", "className": "JSONUtil", "format": "JSONUtil.json2POJO(json:String, className:Class)", "functionName": "JSONUtil.json2POJO", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "json2POJO", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e648"}, "function_name": "JSONUtil.map2JsonPretty(map:Map)", "className": "JSONUtil", "format": "JSONUtil.map2JsonPretty(map:Map)", "functionName": "JSONUtil.map2JsonPretty", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "map2JsonPretty", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e650"}, "function_name": "util.toDword(l:long)", "className": "util", "format": "util.toDword(l:long)", "functionName": "util.toDword", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "toDword", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e653"}, "function_name": "util.toWord(i:int)", "className": "util", "format": "util.toWord(i:int)", "functionName": "util.to<PERSON>ord", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "toWord", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e65b"}, "function_name": "util.newBytes(size:int)", "className": "util", "format": "util.newBytes(size:int)", "functionName": "util.newBytes", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "newBytes", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e661"}, "function_name": "util.toByte(s:short)", "className": "util", "format": "util.toByte(s:short)", "functionName": "util.toByte", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "toByte", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e668"}, "function_name": "util.toByte(c:char)", "className": "util", "format": "util.toByte(c:char)", "functionName": "util.toByte", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "toByte", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e670"}, "function_name": "util.strToBase64(str:String)", "className": "util", "format": "util.strToBase64(str:String)", "functionName": "util.strToBase64", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "strToBase64", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e675"}, "function_name": "util.base64ToStr(str:String)", "className": "util", "format": "util.base64ToStr(str:String)", "functionName": "util.base64ToStr", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "base64ToStr", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e677"}, "function_name": "util.getCheckCode(bytes:byte[])", "className": "util", "format": "util.getCheckCode(bytes:byte[])", "functionName": "util.getCheckCode", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getCheckCode", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e67b"}, "function_name": "util.getBytesLength(bytes:byte[])", "className": "util", "format": "util.getBytesLength(bytes:byte[])", "functionName": "util.getBytesLength", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "getBytesLength", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e67e"}, "function_name": "util.toBytes(str:String, length:int)", "className": "util", "format": "util.toBytes(str:String, length:int)", "functionName": "util.toBytes", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "toBytes", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e683"}, "function_name": "util.toBytes(str:String)", "className": "util", "format": "util.toBytes(str:String)", "functionName": "util.toBytes", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "toBytes", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e687"}, "function_name": "util.main(args:String[])", "className": "util", "format": "util.main(args:String[])", "functionName": "util.main", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "main", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e689"}, "function_name": "util.arraycopy(src:Object, srcPos:int, dest:Object, destPos:int, length:int)", "className": "util", "format": "util.arraycopy(src:Object, srcPos:int, dest:Object, destPos:int, length:int)", "functionName": "util.arraycopy", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "arraycopy", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e68c"}, "function_name": "util.toHexString(bytes:byte[])", "className": "util", "format": "util.toHexString(bytes:byte[])", "functionName": "util.toHexString", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "toHexString", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e68f"}, "function_name": "UUIDGenerator.uuid14(upper:boolean)", "className": "UUIDGenerator", "format": "UUIDGenerator.uuid14(upper:boolean)", "functionName": "UUIDGenerator.uuid14", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "uuid14", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e692"}, "function_name": "UUIDGenerator.uuid()", "className": "UUIDGenerator", "format": "UUIDGenerator.uuid()", "functionName": "UUIDGenerator.uuid", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "uuid", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e694"}, "function_name": "UUIDGenerator.objectId()", "className": "UUIDGenerator", "format": "UUIDGenerator.objectId()", "functionName": "UUIDGenerator.objectId", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "objectId", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e698"}, "function_name": "UUIDGenerator.objectIdStr()", "className": "UUIDGenerator", "format": "UUIDGenerator.objectIdStr()", "functionName": "UUIDGenerator.objectIdStr", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "objectIdStr", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e69c"}, "function_name": "MD5Util.crypt(str:String, upper:boolean)", "className": "MD5Util", "format": "MD5Util.crypt(str:String, upper:boolean)", "functionName": "MD5Util.crypt", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "crypt", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e69f"}, "function_name": "Collections.min(arg0:Collection, arg1:Comparator)", "className": "Collections", "format": "Collections.min(arg0:Collection, arg1:Comparator)", "functionName": "Collections.min", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "min", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6a5"}, "function_name": "Collections.min(arg0:Collection)", "className": "Collections", "format": "Collections.min(arg0:Collection)", "functionName": "Collections.min", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "min", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6a7"}, "function_name": "Collections.max(arg0:Collection, arg1:Comparator)", "className": "Collections", "format": "Collections.max(arg0:Collection, arg1:Comparator)", "functionName": "Collections.max", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "max", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6ac"}, "function_name": "Collections.max(arg0:Collection)", "className": "Collections", "format": "Collections.max(arg0:Collection)", "functionName": "Collections.max", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "max", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6ae"}, "function_name": "Collections.replaceAll(arg0:List, arg1:Object, arg2:Object)", "className": "Collections", "format": "Collections.replaceAll(arg0:List, arg1:Object, arg2:Object)", "functionName": "Collections.replaceAll", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "replaceAll", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6b2"}, "function_name": "Collections.addAll(arg0:Collection, arg1:Object[])", "className": "Collections", "format": "Collections.addAll(arg0:Collection, arg1:Object[])", "functionName": "Collections.addAll", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "addAll", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6b4"}, "function_name": "Collections.emptyEnumeration()", "className": "Collections", "format": "Collections.emptyEnumeration()", "functionName": "Collections.emptyEnumeration", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "emptyEnumeration", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6b6"}, "function_name": "Collections.newSetFromMap(arg0:Map)", "className": "Collections", "format": "Collections.newSetFromMap(arg0:Map)", "functionName": "Collections.newSetFromMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "newSetFromMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6b9"}, "function_name": "Collections.unmodifiableList(arg0:List)", "className": "Collections", "format": "Collections.unmodifiableList(arg0:List)", "functionName": "Collections.unmodifiableList", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "unmodifiableList", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6bb"}, "function_name": "Collections.list(arg0:Enumeration)", "className": "Collections", "format": "Collections.list(arg0:Enumeration)", "functionName": "Collections.list", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "list", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6be"}, "function_name": "Collections.emptyIterator()", "className": "Collections", "format": "Collections.emptyIterator()", "functionName": "Collections.emptyIterator", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "emptyIterator", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6c1"}, "function_name": "Collections.synchronizedSet(arg0:Set)", "className": "Collections", "format": "Collections.synchronizedSet(arg0:Set)", "functionName": "Collections.synchronizedSet", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "synchronizedSet", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6c3"}, "function_name": "Collections.synchronizedCollection(arg0:Collection)", "className": "Collections", "format": "Collections.synchronizedCollection(arg0:Collection)", "functionName": "Collections.synchronizedCollection", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "synchronizedCollection", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6c6"}, "function_name": "Collections.copy(arg0:List, arg1:List)", "className": "Collections", "format": "Collections.copy(arg0:List, arg1:List)", "functionName": "Collections.copy", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "copy", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6cc"}, "function_name": "Collections.fill(arg0:List, arg1:Object)", "className": "Collections", "format": "Collections.fill(arg0:List, arg1:Object)", "functionName": "Collections.fill", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "fill", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6ce"}, "function_name": "Collections.nCopies(arg0:int, arg1:Object)", "className": "Collections", "format": "Collections.nCopies(arg0:int, arg1:Object)", "functionName": "Collections.nCopies", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "nCopies", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6d3"}, "function_name": "Collections.reverse(arg0:List)", "className": "Collections", "format": "Collections.reverse(arg0:List)", "functionName": "Collections.reverse", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "reverse", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6d7"}, "function_name": "Collections.reverseOrder()", "className": "Collections", "format": "Collections.reverseOrder()", "functionName": "Collections.reverseOrder", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "reverseOrder", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6d9"}, "function_name": "Collections.reverseOrder(arg0:Comparator)", "className": "Collections", "format": "Collections.reverseOrder(arg0:Comparator)", "functionName": "Collections.reverseOrder", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "reverseOrder", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6df"}, "function_name": "Collections.sort(arg0:List, arg1:Comparator)", "className": "Collections", "format": "Collections.sort(arg0:List, arg1:Comparator)", "functionName": "Collections.sort", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "sort", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6e5"}, "function_name": "Collections.sort(arg0:List)", "className": "Collections", "format": "Collections.sort(arg0:List)", "functionName": "Collections.sort", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "sort", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6eb"}, "function_name": "Collections.synchronizedList(arg0:List)", "className": "Collections", "format": "Collections.synchronizedList(arg0:List)", "functionName": "Collections.synchronizedList", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "synchronizedList", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6f1"}, "function_name": "Collections.binarySearch(arg0:List, arg1:Object, arg2:Comparator)", "className": "Collections", "format": "Collections.binarySearch(arg0:List, arg1:Object, arg2:Comparator)", "functionName": "Collections.binarySearch", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "binarySearch", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6f5"}, "function_name": "Collections.binarySearch(arg0:List, arg1:Object)", "className": "Collections", "format": "Collections.binarySearch(arg0:List, arg1:Object)", "functionName": "Collections.binarySearch", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "binarySearch", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e6fd"}, "function_name": "Collections.shuffle(arg0:List)", "className": "Collections", "format": "Collections.shuffle(arg0:List)", "functionName": "Collections.shuffle", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "shuffle", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e701"}, "function_name": "Collections.shuffle(arg0:List, arg1:Random)", "className": "Collections", "format": "Collections.shuffle(arg0:List, arg1:Random)", "functionName": "Collections.shuffle", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "shuffle", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e706"}, "function_name": "Collections.swap(arg0:List, arg1:int, arg2:int)", "className": "Collections", "format": "Collections.swap(arg0:List, arg1:int, arg2:int)", "functionName": "Collections.swap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "swap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e70b"}, "function_name": "Collections.rotate(arg0:List, arg1:int)", "className": "Collections", "format": "Collections.rotate(arg0:List, arg1:int)", "functionName": "Collections.rotate", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "rotate", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e711"}, "function_name": "Collections.indexOfSubList(arg0:List, arg1:List)", "className": "Collections", "format": "Collections.indexOfSubList(arg0:List, arg1:List)", "functionName": "Collections.indexOfSubList", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "indexOfSubList", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e715"}, "function_name": "Collections.lastIndexOfSubList(arg0:List, arg1:List)", "className": "Collections", "format": "Collections.lastIndexOfSubList(arg0:List, arg1:List)", "functionName": "Collections.lastIndexOfSubList", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "lastIndexOfSubList", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e71a"}, "function_name": "Collections.unmodifiableCollection(arg0:Collection)", "className": "Collections", "format": "Collections.unmodifiableCollection(arg0:Collection)", "functionName": "Collections.unmodifiableCollection", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "unmodifiableCollection", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e720"}, "function_name": "Collections.unmodifiableSet(arg0:Set)", "className": "Collections", "format": "Collections.unmodifiableSet(arg0:Set)", "functionName": "Collections.unmodifiableSet", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "unmodifiableSet", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e727"}, "function_name": "Collections.unmodifiableSortedSet(arg0:SortedSet)", "className": "Collections", "format": "Collections.unmodifiableSortedSet(arg0:SortedSet)", "functionName": "Collections.unmodifiableSortedSet", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "unmodifiableSortedSet", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e72b"}, "function_name": "Collections.unmodifiableNavigableSet(arg0:NavigableSet)", "className": "Collections", "format": "Collections.unmodifiableNavigableSet(arg0:NavigableSet)", "functionName": "Collections.unmodifiableNavigableSet", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "unmodifiableNavigableSet", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e732"}, "function_name": "Collections.unmodifiableMap(arg0:Map)", "className": "Collections", "format": "Collections.unmodifiableMap(arg0:Map)", "functionName": "Collections.unmodifiableMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "unmodifiableMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e735"}, "function_name": "Collections.unmodifiableSortedMap(arg0:SortedMap)", "className": "Collections", "format": "Collections.unmodifiableSortedMap(arg0:SortedMap)", "functionName": "Collections.unmodifiableSortedMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "unmodifiableSortedMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e73c"}, "function_name": "Collections.unmodifiableNavigableMap(arg0:NavigableMap)", "className": "Collections", "format": "Collections.unmodifiableNavigableMap(arg0:NavigableMap)", "functionName": "Collections.unmodifiableNavigableMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "unmodifiableNavigableMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e744"}, "function_name": "Collections.synchronizedSortedSet(arg0:SortedSet)", "className": "Collections", "format": "Collections.synchronizedSortedSet(arg0:SortedSet)", "functionName": "Collections.synchronizedSortedSet", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "synchronizedSortedSet", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e752"}, "function_name": "Collections.synchronizedNavigableSet(arg0:NavigableSet)", "className": "Collections", "format": "Collections.synchronizedNavigableSet(arg0:NavigableSet)", "functionName": "Collections.synchronizedNavigableSet", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "synchronizedNavigableSet", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e75e"}, "function_name": "Collections.synchronizedMap(arg0:Map)", "className": "Collections", "format": "Collections.synchronizedMap(arg0:Map)", "functionName": "Collections.synchronizedMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "synchronizedMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500bd4958d013d97e767"}, "function_name": "Collections.synchronizedSortedMap(arg0:SortedMap)", "className": "Collections", "format": "Collections.synchronizedSortedMap(arg0:SortedMap)", "functionName": "Collections.synchronizedSortedMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "synchronizedSortedMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e773"}, "function_name": "Collections.synchronizedNavigableMap(arg0:NavigableMap)", "className": "Collections", "format": "Collections.synchronizedNavigableMap(arg0:NavigableMap)", "functionName": "Collections.synchronizedNavigableMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "synchronizedNavigableMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e781"}, "function_name": "Collections.checkedCollection(arg0:Collection, arg1:Class)", "className": "Collections", "format": "Collections.checkedCollection(arg0:Collection, arg1:Class)", "functionName": "Collections.checkedCollection", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "checkedCollection", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e78a"}, "function_name": "Collections.checkedQueue(arg0:Queue, arg1:Class)", "className": "Collections", "format": "Collections.checkedQueue(arg0:Queue, arg1:Class)", "functionName": "Collections.checkedQueue", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "checkedQueue", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e791"}, "function_name": "Collections.checkedSet(arg0:Set, arg1:Class)", "className": "Collections", "format": "Collections.checkedSet(arg0:Set, arg1:Class)", "functionName": "Collections.checkedSet", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "checkedSet", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e79c"}, "function_name": "Collections.checkedSortedSet(arg0:SortedSet, arg1:Class)", "className": "Collections", "format": "Collections.checkedSortedSet(arg0:SortedSet, arg1:Class)", "functionName": "Collections.checkedSortedSet", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "checkedSortedSet", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e7a6"}, "function_name": "Collections.checkedNavigableSet(arg0:NavigableSet, arg1:Class)", "className": "Collections", "format": "Collections.checkedNavigableSet(arg0:NavigableSet, arg1:Class)", "functionName": "Collections.checkedNavigableSet", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "checkedNavigableSet", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e7b1"}, "function_name": "Collections.checkedList(arg0:List, arg1:Class)", "className": "Collections", "format": "Collections.checkedList(arg0:List, arg1:Class)", "functionName": "Collections.checkedList", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "checkedList", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e7bb"}, "function_name": "Collections.checkedMap(arg0:Map, arg1:Class, arg2:Class)", "className": "Collections", "format": "Collections.checkedMap(arg0:Map, arg1:Class, arg2:Class)", "functionName": "Collections.checkedMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "checkedMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e7c5"}, "function_name": "Collections.checkedSortedMap(arg0:SortedMap, arg1:Class, arg2:Class)", "className": "Collections", "format": "Collections.checkedSortedMap(arg0:SortedMap, arg1:Class, arg2:Class)", "functionName": "Collections.checkedSortedMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "checkedSortedMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e7d2"}, "function_name": "Collections.checkedNavigableMap(arg0:NavigableMap, arg1:Class, arg2:Class)", "className": "Collections", "format": "Collections.checkedNavigableMap(arg0:NavigableMap, arg1:Class, arg2:Class)", "functionName": "Collections.checkedNavigableMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "checkedNavigableMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e7dc"}, "function_name": "Collections.emptyListIterator()", "className": "Collections", "format": "Collections.emptyListIterator()", "functionName": "Collections.emptyListIterator", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "emptyListIterator", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e7e8"}, "function_name": "Collections.emptySet()", "className": "Collections", "format": "Collections.emptySet()", "functionName": "Collections.emptySet", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "emptySet", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e7f0"}, "function_name": "Collections.emptySortedSet()", "className": "Collections", "format": "Collections.emptySortedSet()", "functionName": "Collections.emptySortedSet", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "emptySortedSet", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e7fa"}, "function_name": "Collections.emptyNavigableSet()", "className": "Collections", "format": "Collections.emptyNavigableSet()", "functionName": "Collections.emptyNavigableSet", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "emptyNavigableSet", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e7fe"}, "function_name": "Collections.emptyList()", "className": "Collections", "format": "Collections.emptyList()", "functionName": "Collections.emptyList", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "emptyList", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e804"}, "function_name": "Collections.emptyMap()", "className": "Collections", "format": "Collections.emptyMap()", "functionName": "Collections.emptyMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "emptyMap", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e80e"}, "function_name": "Collections.emptySortedMap()", "className": "Collections", "format": "Collections.emptySortedMap()", "functionName": "Collections.emptySortedMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "emptySortedMap", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e813"}, "function_name": "Collections.emptyNavigableMap()", "className": "Collections", "format": "Collections.emptyNavigableMap()", "functionName": "Collections.emptyNavigableMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "emptyNavigableMap", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e81c"}, "function_name": "Collections.singleton(arg0:Object)", "className": "Collections", "format": "Collections.singleton(arg0:Object)", "functionName": "Collections.singleton", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "singleton", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e822"}, "function_name": "Collections.singletonList(arg0:Object)", "className": "Collections", "format": "Collections.singletonList(arg0:Object)", "functionName": "Collections.singletonList", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "singletonList", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e827"}, "function_name": "Collections.singletonMap(arg0:Object, arg1:Object)", "className": "Collections", "format": "Collections.singletonMap(arg0:Object, arg1:Object)", "functionName": "Collections.singletonMap", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "singletonMap", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e82d"}, "function_name": "Collections.enumeration(arg0:Collection)", "className": "Collections", "format": "Collections.enumeration(arg0:Collection)", "functionName": "Collections.enumeration", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "enumeration", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e835"}, "function_name": "Collections.frequency(arg0:Collection, arg1:Object)", "className": "Collections", "format": "Collections.frequency(arg0:Collection, arg1:Object)", "functionName": "Collections.frequency", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "frequency", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e83d"}, "function_name": "Collections.disjoint(arg0:Collection, arg1:Collection)", "className": "Collections", "format": "Collections.disjoint(arg0:Collection, arg1:Collection)", "functionName": "Collections.disjoint", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "disjoint", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e84a"}, "function_name": "Collections.asLifoQ<PERSON>ue(arg0:Deque)", "className": "Collections", "format": "Collections.asLifoQ<PERSON>ue(arg0:Deque)", "functionName": "Collections.asLifo<PERSON>ue", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "asLifoQueue", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e851"}, "function_name": "uuid()", "className": "", "format": "uuid()", "functionName": "uuid", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "uuid", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e857"}, "function_name": "split_chinese()", "className": "", "format": "split_chinese()", "functionName": "split_chinese", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "split_chinese", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e85d"}, "function_name": "split_chinese(inputString:String, language:String)", "className": "", "format": "split_chinese(inputString:String, language:String)", "functionName": "split_chinese", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "hanLPParticiple", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e865"}, "function_name": "MD5(str:String, upper:boolean)", "className": "", "format": "MD5(str:String, upper:boolean)", "functionName": "MD5", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "crypt", "parameters": "", "type": "system"}, {"_id": {"$oid": "62bc500cd4958d013d97e86a"}, "function_name": "sleep(ms:long)", "className": "", "format": "sleep(ms:long)", "functionName": "sleep", "last_updated": {"$date": "2021-01-01T00:00:00Z"}, "methodName": "sleep", "parameters": "", "type": "system"}]}]