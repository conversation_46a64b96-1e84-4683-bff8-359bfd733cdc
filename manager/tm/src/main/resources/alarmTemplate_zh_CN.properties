TASK_FULL_COMPLETE = 任务#{[taskName]}全量同步已完成，耗时#{[costTime]}ms, 全量完成时间点：#{[snapDoneDate]},告警时间：#{[alarmDate]}
TASK_INCREMENT_START = 任务#{[taskName]}增量同步已开始, 增量时间点#{[cdcTime]}, 告警时间：#{[alarmDate]}
TASK_STATUS_STOP_ERROR = 任务#{[taskName]}已出错停止，请尽快处理！告警时间：#{[alarmDate]}
TASK_STATUS_STOP_MANUAL = 任务#{[taskName]}已被用户[#{[updatorName]}]停止，请关注！告警时间：#{[alarmDate]}
TASK_INCREMENT_DELAY_START = 任务#{[taskName]}增量延迟#{[flag]}阈值#{[threshold]}ms，当前值：#{[currentValue]}ms，请关注！告警时间：#{[alarmDate]}
TASK_INCREMENT_DELAY_ALWAYS = 任务#{[taskName]}增量延迟#{[flag]}阈值#{[threshold]}ms，已持续#{[continueTime]}分钟，当前值：#{[currentValue]}ms，请关注！告警时间：#{[alarmDate]}
TASK_INCREMENT_DELAY_RECOVER = 任务#{[taskName]}增量延迟已恢复正常，当前值：#{[currentValue]}ms。告警时间：#{[alarmDate]}
TASK_INSPECT_DIFFERENCE = 任务「#{[taskName]}」发现差异数据，#{[diffFromTs]} 到 #{[diffToTs]} 差异从 #{[diffFromTotals]} 变为 #{[diffToTotals]}
TASK_INSPECT_DIFFERENCE_RECOVER = 任务「#{[taskName]}」从 #{[diffFirstTs]} 到 #{[diffToTs]} 的差异已恢复
DATANODE_SOURCE_CANNOT_CONNECT = 任务#{[taskName]} 使用的源连接 [#{[sourceName]}]当前无法正常连接，请尽快处理！告警时间：#{[alarmDate]}
DATANODE_SOURCE_CANNOT_CONNECT_ALWAYS = 任务#{[taskName]} 使用的源连接 [#{[sourceName]}]当前无法正常连接，已持续#{[continueTime]}分钟，请尽快处理！告警时间：#{[alarmDate]}
DATANODE_SOURCE_CANNOT_CONNECT_RECOVER = 任务#{[taskName]} 使用的源连接 [#{[sourceName]}]已恢复正常连接。告警时间：#{[alarmDate]}
DATANODE_AVERAGE_HANDLE_CONSUME_START = 任务#{[taskName]}的源节点[#{[nodeName]}]平均处理耗时#{[flag]}阈值#{[threshold]}ms，当前值：#{[currentValue]}ms，请关注！告警时间：#{[alarmDate]}
DATANODE_AVERAGE_HANDLE_CONSUME_ALWAYS = 任务#{[taskName]}的源节点[#{[nodeName]}]平均处理耗时#{[flag]}阈值#{[threshold]}ms，已持续#{[continueTime]}分钟，当前值：#{[currentValue]}ms，请关注！告警时间：#{[alarmDate]}
DATANODE_AVERAGE_HANDLE_CONSUME_RECOVER = 任务#{[taskName]}的源节点[#{[nodeName]}]平均处理耗时已恢复正常，当前值：#{[currentValue]}ms。告警时间：#{[alarmDate]}
PROCESSNODE_AVERAGE_HANDLE_CONSUME_START = 任务#{[taskName]}的处理节点[#{[nodeName]}]平均处理耗时#{[flag]}阈值#{[threshold]}ms，当前值：#{[currentValue]}ms，请关注！告警时间：#{[alarmDate]}
PROCESSNODE_AVERAGE_HANDLE_CONSUME_ALWAYS = 任务#{[taskName]}的处理节点[#{[nodeName]}]平均处理耗时#{[flag]}阈值#{[threshold]}ms，已持续#{[continueTime]}分钟，当前值：#{[currentValue]}ms，请关注！告警时间：#{[alarmDate]}
PROCESSNODE_AVERAGE_HANDLE_CONSUME_RECOVER = 任务#{[taskName]}的处理节点[#{[nodeName]}]平均处理耗时已恢复正常，当前值：#{[currentValue]}ms。告警时间：#{[alarmDate]}
TARGET_AVERAGE_HANDLE_CONSUME_START = 任务#{[taskName]}的目标节点[#{[nodeName]}]平均处理耗时#{[flag]}阈值#{[threshold]}ms，当前值：#{[currentValue]}ms，请关注！告警时间：#{[alarmDate]}
TARGET_AVERAGE_HANDLE_CONSUME_ALWAYS = 任务#{[taskName]}的目标节点[#{[nodeName]}]平均处理耗时#{[flag]}阈值#{[threshold]}ms，已持续#{[continueTime]}分钟，当前值：#{[currentValue]}ms，请关注！告警时间：#{[alarmDate]}
TARGET_AVERAGE_HANDLE_CONSUME_RECOVER = 任务#{[taskName]}的目标节点[#{[nodeName]}]平均处理耗时已恢复正常，当前值：#{[currentValue]}ms。告警时间：#{[alarmDate]}
SYSTEM_FLOW_EGINGE_DOWN_CHANGE_AGENT = 任务#{[taskName]}所在Agent[#{[agentName]}]已停止运行，当前还有#{[number]}个可用Agent，任务将重新调度到Agent[#{[otherAgentName]}]上运行，请关注！告警时间：#{[alarmDate]}
SYSTEM_FLOW_EGINGE_DOWN_NO_AGENT = 任务#{[taskName]}所在Agent[#{[agentName]}]已停止运行，当前已无可用Agent，将会影响任务正常运行，请尽快处理！告警时间：#{[alarmDate]}
SYSTEM_FLOW_EGINGE_RECOVER = 任务#{[taskName]}所在Agent[#{[agentName]}]已恢复运行，告警时间：#{[alarmDate]}
SYSTEM_FLOW_EGINGE_DOWN_CLOUD = 任务#{[taskName]}所在Agent[#{[agentName]}]已停止运行，将会影响任务正常运行，请尽快处理！告警时间：#{[alarmDate]}
GREATER=大于
LESS=小于
INSPECT_TASK_ERROR = 您的校验任务[#{[inspectName]}]已出错停止，时间：#{[alarmDate]}，请关注。
INSPECT_COUNT_ERROR = 您的校验任务[#{[inspectName]}]快速count校验结果不一致，当前差异行数为:#{[count]}，请关注。告警时间：#{[alarmDate]}
INSPECT_VALUE_ALL_ERROR = 您的校验任务[#{[inspectName]}]表全字段值校验结果不一致，当前表数据差:#{[count]}，请关注。告警时间：#{[alarmDate]}
INSPECT_VALUE_JOIN_ERROR = 您的校验任务[#{[inspectName]}]关联字段值校验结果不一致，当前表数据差:#{[count]}，请关注。告警时间：#{[alarmDate]}
TASK_STATUS_STOP = 任务#{[taskName]}已停止，停止时间点：#{[stopTime]}, 告警时间：#{[alarmDate]}
