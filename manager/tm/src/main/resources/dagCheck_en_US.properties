AGENT_CAN_USE_INFO=$date [$taskName][Agent Availability Check]: Detected {1} available Agent(s): [{2}], task will run on Agent: {3}.
AGENT_CAN_USE_ERROR=$date [$taskName][Agent Availability Check]: The specified Agent: {1} is not available, please restore it as soon as possible or specify another Agent.
CUSTOM_NODE_NOT_EXISTS=$date [$taskName][Custom Node Check]: The custom node does not exist.
CUSTOM_NODE_SET_EMPTY=$date [$taskName][Custom Node Check]: The setting {1} of {0} is empty.
CUSTOM_NODE_INFO=$date [$taskName][Custom Node Check]: The custom node {0} passed the check.
TASK_SETTING_INFO=$date [$taskName] [Task Setting Check]: Task '{$taskName}' passes the check.
TASK_SETTING_ERROR=$date [$taskName] [Task Setting Check]: Task '{$taskName}' fails the check. Abnormal item '{Task Name}', the reason is duplicate task name, please reset.
TASK_SETTING_TIMEZONE_CHECK=$date [$taskName] [Default Time Zone Detection] The default time zone connected to the source node of the task is different from the default time zone connected to the target node, which may lead to inconsistency in synchronizing data.
TASK_SCHEDULE_LIMIT=$date [$taskName] [Dispatch inspection] The number of tasks that the engine can be invoked exceeds the limit
SOURCE_SETTING_INFO=$date [$taskName] [Source Node Setting Check]: Node {0} passes the check.
SOURCE_SETTING_NAME_EMPTY=$date [$taskName] [Source Node Setting Check]: Node name is empty for source node {0}.
SOURCE_SETTING_ERROR_SCHEMA=$date [$taskName] [Source Node Setting Check]: Node {0} fails the check, the source database does not have any tables.
SOURCE_SETTING_ERROR_SCHEMA_LOAD=$date [$taskName] [Source Node Setting Check]: Node {0} fails the check, connection model has not been loaded completely.
SOURCE_SETTING_NOT_SELECT_DB=$date [$taskName] [Source Node Setting Check]: Database is not selected for source node {0}.
SOURCE_SETTING_EXP_EMPTY=$date [$taskName] [Source Node Setting Check]: Exception occurs while setting filter conditions for source node {0}.
SOURCE_SETTING_NOT_SELECT_TB=$date [$taskName] [Source Node Setting Check]: Table is not selected for source node {0}.
SOURCE_SETTING_NAME_REPEAT=$date [$taskName] [Source Node Setting Check]: Node {0} fails the check, abnormal item '{Node Name}', the reason is duplicate node name, please reset.
SOURCE_SETTING_CHECK_TYPE=$date [Task Synchronization Type Detection][{0}]The synchronization type {1} of this node does not match the synchronization type of the task it belongs to ({2}), so the task cannot be started normally. Please check the relevant configuration.
SOURCE_SETTING_CHECK_SCHAME=$date [Source model loading detection] {0} The node data model failed to load, the task cannot be started normally, please check the related issues. {1}.
SOURCE_SETTING_CHECK_FIELD=$date [Source type mapping detection] [{0}] [{1}] [{2}] The system is unable to determine the inferred type of this field. Please manually set the type, otherwise the field will be ignored during task execution.
SOURCE_SETTING_CHECK_MARIADB=$date [Mariadb time zone check] [{0}] The time zone setting in the Mariadb connection configuration is only valid in the full phase, and invalid in the incremental phase.
SOURCE_SETTING_CHECK_CDC=$date [cdc check] The configuration of the data source does not support incremental functionality, and incremental tasks cannot be configured. 
SOURCE_SETTING_CHECK_POLLINGCDC=$date [Source Node Settings detection] : The source node {0} has selected field polling for incremental synchronization. Ensure that the selected polling field has an index. Otherwise, the source database may be affected due to query performance problems.
SOURCE_SETTING_CHECK_ISFILTER=$date [Check source node Settings] : Check that data filtering is enabled on the source node {0} . User-defined query or field filtering may affect the source and database performance. Exercise caution when using user-defined query or field filtering
SOURCE_SETTING_CHECK_ISDDL=$date [Source Node Settings detection] : The synchronous DDL Settings are detected on the source node {0} . After the task is started, the DDL operations of the source and database are automatically applied to the target database, which may affect the target database. Exercise caution when performing this operation
TARGET_SETTING_CHECK_EXISTDATAMODE=$date [Target node Settings detection] : The target node {0} is detected to have selected to clear the original table structure and data policy of the target end. After the task is started, the existing target table and data are directly deleted, which may affect the target database
TARGET_SETTING_CHECK_SYNCINDEX=$date [Target node Settings detection] : Index synchronization is enabled on the target node {0}. After the task is started, the source index is automatically synchronized to the target. This behavior may affect the target database
TARGET_SETTING_WRAN_UPDATEFIELD=$date [Target node Settings detection] : To ensure performance, Application automatically creates an index for the update condition field of the target node. To avoid impact on the target database, you can manually create an index before starting the task
BATCH_READ_FUNCTION=full
STREAM_READ_FUNCTION=cdc
TABLE_EDIT_NODE_INFO=$date [$taskName][Table edit node setting check]: Node {1} passed the check
SOURCE_CONNECT_INFO=$date [$taskName][Source connection check]: Connection {0} passed the check
SOURCE_CONNECT_ERROR=$date [$taskName][Source connection check]: Connection {0} failed the check. \n {1}
TARGET_CONNECT_INFO=$date [$taskName][Target connection check]: Connection {0} passed the check
TARGET_CONNECT_ERROR=$date [$taskName][Target connection check]: Connection {0} failed the check. \n {1}
MODEL_PROCESS_INFO=$date [$taskName][Model deduction check]: Deduction progress {0}/{1}, check normal
MODEL_PROCESS_INFO_PRELOG=$date [$taskName][Model deduction check]: Target table structure generation in progress, number of tables to be generated: {0}, estimated time: around {1}s, please wait
MODEL_PROCESS_FAIL=$date [Task Model Simulation Detection] Task configuration model simulation failed, task cannot be started normally, please check the related issues.\n{0}
MODEL_PROCESS_ERROR=$date [$taskName][Model deduction check]: Check abnormal, abnormal reason: {1}
DATA_INSPECT_INFO=$date [$taskName][Data inspection check]: Data inspection is supported, including {2} tables, {3} tables are not supported for inspection
DATA_INSPECT_ERROR=$date [$taskName][Data inspection check]: {2}
DATA_INSPECT_NOT_SUPORTS= Node %s does not support inspection, please delete the node or disable inspection.
DATA_INSPECT_NEED_CLOSED_SOURCE= Please close source %s dynamically added tables or disable inspection.
DATA_INSPECT_NEED_CLOSED_TARGET= Please close target %s dynamically added tables or disable inspection.
FIELD_ADD_NAME_EMPTY= $date [$taskName][Add/Delete Field Node Inspection]: Node name of add/delete field is empty.
FIELD_ADD_ALL_DELETE= $date [$taskName][Add/Delete Field Node Inspection]: All {0} fields in add/delete field node have been deleted.
FIELD_ADD_ALL_PASS= $date [$taskName][Add/Delete Field Node Inspection]: The inspection of add/delete field node {0} has passed.
FIELD_CALC_NAME_EMPTY= $date [$taskName][Field Calculation Node Inspection]: Node name of field calculation is empty.
FIELD_CALC_PASS= $date [$taskName][Field Calculation Node Inspection]: The inspection of field calculation node {0} has passed.
FIELD_EDIT_NAME_EMPTY= $date [$taskName][Field Rename Node Setting Inspection]: Node name of field rename is empty.
FIELD_EDIT_FIELD_EMPTY= $date [$taskName][Field Rename Node Setting Inspection]: Target field name of {0}{1} in field rename node is empty.
FIELD_EDIT_PASS= $date [$taskName][Field Rename Node Setting Inspection]: The inspection of field rename node {0} has passed.
FIELD_MOD_TYPE_NAME_EMPTY= $date [$taskName][Type Modification Node Inspection]: Node name of type modification is empty.
FIELD_MOD_TYPE_PASS= $date [$taskName][Type Modification Node Inspection]: The inspection of type modification node {0} has passed.
JOIN_NODE_NAME_EMPTY= $date [$taskName][Join Node Setting Inspection]: Node name of join is empty.
JOIN_NODE_NOT_SET= $date [$taskName][Join Node Setting Inspection]: The connecting fields of join node {0} have not been set.
JOIN_NODE_SET_ERROR= $date [$taskName][Join Node Setting Inspection]: Join node {0} requires two inputs.
JOIN_NODE_PASS=$date [$taskName][Connection Node Setting Check]: Connection node {0} has passed the check.
JS_EDIT_NAME_EMPTY=$date [$taskName][{0} Node Setting Check]: Node name is empty.
JS_EDIT_PASS=$date [$taskName][{0} Node Setting Check]: Node {1} has passed the check.
MERGE_TABLE_NAME_EMPTY=$date [$taskName][Master-Slave Merging Node Setting Check]: Node name of the master-slave merging node is empty.
MERGE_TABLE_RELATE_EMPTY=$date [$taskName][Master-Slave Merging Node Setting Check]: The related field of the master-slave merging node {0} has not been set.
MERGE_TABLE_PASS=$date [$taskName][Master-Slave Merging Node Setting Check]: Master-slave merging node {0} has passed the check.
ROW_FILTER_NAME_EMPTY=$date [$taskName][Row Filter Node Setting Check]: Node name of the Row Filter node is empty.
ROW_FILTER_EXP_EMPTY=$date [$taskName][Row Filter Node Setting Check]: The condition expression of the Row Filter node {0} is empty.
ROW_FILTER_PASS=$date [$taskName][Row Filter Node Setting Check]: Row Filter node {0} has passed the check.
TARGET_NAME_EMPTY=$date [$taskName][Target Node Setting Check]: Node name of the target node {0} is empty.
TARGET_NAME_NOT_SELECT_DB=$date [$taskName][Target Node Setting Check]: The database of the target node {0} has not been selected.
TARGET_NAME_UPDATE_ERROR=$date [$taskName][Target Node Setting Check]: The update condition field of the target node {0} has not been set.
TARGET_NAME_UPDATE_NOT_EXISTS=$date [$taskName][Target Node Setting Check]: The update condition field {1} does not exist in the target node {0}.
TARGET_NOT_SELECT_TB=$date [$taskName][Target Node Setting Check]: The table of the target node {0} has not been selected.
TARGET_ORACLE_FIELD_EMPTY_TIP=$date [$taskName][Target Node Setting Check]: [{0}][{1}] This Oracle non-empty constraint field does not support writing to "" data.
TARGET_CK_FIELD_FLOAT_TIP=$date [$taskName][Target Node Setting Check]: [{0}] The primary key of this ClickHouse table is float data type, which does not support processing updating and deleting events.
TARGET_SETTING_CHECK_SCHAME=$date [Target model loading detection] The data model of {0} node failed to load, and the task could not be started normally. Please check the related issues. \n{1}.
TARGET_SETTING_CHECK_FIELD=$date [Target Type Mapping Check] [{0}] [{1}] [{2}] The system is unable to determine the inferred type of this field. Please manually set the type, otherwise the field will be ignored during task execution.
TARGET_SETTING_SELECT_FIELD=$date [Source Type Mapping Detection] [{0}] [{1}] [{2}] The target data type mapped by this field is the system's inferred result, which may have deviations. Please check and confirm whether it meets your expectations, and adjust accordingly based on the actual situation.
UNION_NAME_EMPTY=$date [$taskName][Append Merging Node Check]: Node name of the append merging node is empty.
UNION_PASS=$date [$taskName][Append Merging Node Check]: Append merging node {0} has passed the check.
MIGRATE_UNION_NOT_SUPPORT_PARTITION_TABLE=$date [$taskName][Union For Multiple Table]: Multiple table union do not support partitioned tables
MIGRATE_UNION_PASS=$date [$taskName][Union For Multiple Table]: Multiple table union has passed the check.