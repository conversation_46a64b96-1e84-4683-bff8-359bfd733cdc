package com.tapdata.tm.DataCatalogs.controller;

import com.tapdata.tm.base.controller.BaseController;
import com.tapdata.tm.base.dto.*;
import com.tapdata.tm.DataCatalogs.dto.DataCatalogsDto;
import com.tapdata.tm.DataCatalogs.service.DataCatalogsService;
import com.tapdata.tm.utils.MongoUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Date: 2022/01/24
 * @Description:
 */
@Tag(name = "DataCatalogs", description = "DataCatalogs相关接口")
@RestController
@RequestMapping("/api/DataCatalogs")
public class DataCatalogsController extends BaseController {

    @Autowired
    private DataCatalogsService dataCatalogsService;

    /**
     * Create a new instance of the model and persist it into the data source
     * @param dataCatalogs
     * @return
     */
    @Operation(summary = "Create a new instance of the model and persist it into the data source")
    @PostMapping
    public ResponseMessage<DataCatalogsDto> save(@RequestBody DataCatalogsDto dataCatalogs) {
        dataCatalogs.setId(null);
        return success(dataCatalogsService.save(dataCatalogs, getLoginUser()));
    }

    /**
     *  Patch an existing model instance or insert a new one into the data source
     * @param dataCatalogs
     * @return
     */
    @Operation(summary = "Patch an existing model instance or insert a new one into the data source")
    @PatchMapping()
    public ResponseMessage<DataCatalogsDto> update(@RequestBody DataCatalogsDto dataCatalogs) {
        return success(dataCatalogsService.save(dataCatalogs, getLoginUser()));
    }


    /**
     * Find all instances of the model matched by filter from the data source
     * @param filterJson
     * @return
     */
    @Operation(summary = "Find all instances of the model matched by filter from the data source")
    @GetMapping
    public ResponseMessage<Page<DataCatalogsDto>> find(
            @Parameter(in = ParameterIn.QUERY,
                    description = "Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (`{\"where\":{\"something\":\"value\"},\"fields\":{\"something\":true|false},\"sort\": [\"name desc\"],\"page\":1,\"size\":20}`)."
            )
            @RequestParam(value = "filter", required = false) String filterJson) {
        Filter filter = parseFilter(filterJson);
        if (filter == null) {
            filter = new Filter();
        }
        return success(dataCatalogsService.find(filter, getLoginUser()));
    }

    /**
     *  Replace an existing model instance or insert a new one into the data source
     * @param dataCatalogs
     * @return
     */
    @Operation(summary = "Replace an existing model instance or insert a new one into the data source")
    @PutMapping
    public ResponseMessage<DataCatalogsDto> put(@RequestBody DataCatalogsDto dataCatalogs) {
        return success(dataCatalogsService.replaceOrInsert(dataCatalogs, getLoginUser()));
    }


    /**
     * Check whether a model instance exists in the data source
     * @return
     */
    @Operation(summary = "Check whether a model instance exists in the data source")
    @RequestMapping(value = "{id}", method = RequestMethod.HEAD)
    public ResponseMessage<HashMap<String, Boolean>> checkById(@PathVariable("id") String id) {
        long count = dataCatalogsService.count(Where.where("_id", MongoUtils.toObjectId(id)), getLoginUser());
        HashMap<String, Boolean> existsValue = new HashMap<>();
        existsValue.put("exists", count > 0);
        return success(existsValue);
    }

    /**
     *  Patch attributes for a model instance and persist it into the data source
     * @param dataCatalogs
     * @return
     */
    @Operation(summary = "Patch attributes for a model instance and persist it into the data source")
    @PatchMapping("{id}")
    public ResponseMessage<DataCatalogsDto> updateById(@PathVariable("id") String id, @RequestBody DataCatalogsDto dataCatalogs) {
        dataCatalogs.setId(MongoUtils.toObjectId(id));
        return success(dataCatalogsService.save(dataCatalogs, getLoginUser()));
    }


    /**
     * Find a model instance by {{id}} from the data source
     * @param fieldsJson
     * @return
     */
    @Operation(summary = "Find a model instance by {{id}} from the data source")
    @GetMapping("{id}")
    public ResponseMessage<DataCatalogsDto> findById(@PathVariable("id") String id,
            @RequestParam(value = "fields", required = false) String fieldsJson) {
        Field fields = parseField(fieldsJson);
        return success(dataCatalogsService.findById(MongoUtils.toObjectId(id),  fields, getLoginUser()));
    }

    /**
     *  Replace attributes for a model instance and persist it into the data source.
     * @param dataCatalogs
     * @return
     */
    @Operation(summary = "Replace attributes for a model instance and persist it into the data source.")
    @PutMapping("{id}")
    public ResponseMessage<DataCatalogsDto> replceById(@PathVariable("id") String id, @RequestBody DataCatalogsDto dataCatalogs) {
        return success(dataCatalogsService.replaceById(MongoUtils.toObjectId(id), dataCatalogs, getLoginUser()));
    }

    /**
     *  Replace attributes for a model instance and persist it into the data source.
     * @param dataCatalogs
     * @return
     */
    @Operation(summary = "Replace attributes for a model instance and persist it into the data source.")
    @PostMapping("{id}/replace")
    public ResponseMessage<DataCatalogsDto> replaceById2(@PathVariable("id") String id, @RequestBody DataCatalogsDto dataCatalogs) {
        return success(dataCatalogsService.replaceById(MongoUtils.toObjectId(id), dataCatalogs, getLoginUser()));
    }



    /**
     * Delete a model instance by {{id}} from the data source
     * @param id
     * @return
     */
    @Operation(summary = "Delete a model instance by {{id}} from the data source")
    @DeleteMapping("{id}")
    public ResponseMessage<Void> delete(@PathVariable("id") String id) {
        dataCatalogsService.deleteById(MongoUtils.toObjectId(id), getLoginUser());
        return success();
    }

    /**
     *  Check whether a model instance exists in the data source
     * @param id
     * @return
     */
    @Operation(summary = "Check whether a model instance exists in the data source")
    @GetMapping("{id}/exists")
    public ResponseMessage<HashMap<String, Boolean>> checkById1(@PathVariable("id") String id) {
        long count = dataCatalogsService.count(Where.where("_id", MongoUtils.toObjectId(id)), getLoginUser());
        HashMap<String, Boolean> existsValue = new HashMap<>();
        existsValue.put("exists", count > 0);
        return success(existsValue);
    }

    /**
     *  Count instances of the model matched by where from the data source
     * @param whereJson
     * @return
     */
    @Operation(summary = "Count instances of the model matched by where from the data source")
    @GetMapping("count")
    public ResponseMessage<HashMap<String, Long>> count(@RequestParam("where") String whereJson) {
        Where where = parseWhere(whereJson);
        if (where == null) {
            where = new Where();
        }
        long count = dataCatalogsService.count(where, getLoginUser());
        HashMap<String, Long> countValue = new HashMap<>();
        countValue.put("count", count);
        return success(countValue);
    }

    /**
     *  Find first instance of the model matched by filter from the data source.
     * @param filterJson
     * @return
     */
    @Operation(summary = "Find first instance of the model matched by filter from the data source.")
    @GetMapping("findOne")
    public ResponseMessage<DataCatalogsDto> findOne(
            @Parameter(in = ParameterIn.QUERY,
                    description = "Filter defining fields, where, sort, skip, and limit - must be a JSON-encoded string (`{\"where\":{\"something\":\"value\"},\"field\":{\"something\":true|false},\"sort\": [\"name desc\"],\"page\":1,\"size\":20}`)."
            )
            @RequestParam(value = "filter", required = false) String filterJson) {
        Filter filter = parseFilter(filterJson);
        if (filter == null) {
            filter = new Filter();
        }
        return success(dataCatalogsService.findOne(filter, getLoginUser()));
    }

    /**
     *  Update instances of the model matched by {{where}} from the data source.
     * @param whereJson
     * @return
     */
    @Operation(summary = "Update instances of the model matched by {{where}} from the data source")
    @PostMapping("update")
    public ResponseMessage<Map<String, Long>> updateByWhere(@RequestParam("where") String whereJson, @RequestBody DataCatalogsDto dataCatalogs) {
        Where where = parseWhere(whereJson);
        long count = dataCatalogsService.updateByWhere(where, dataCatalogs, getLoginUser());
        HashMap<String, Long> countValue = new HashMap<>();
        countValue.put("count", count);
        return success(countValue);
    }

    /**
     *  Update an existing model instance or insert a new one into the data source based on the where criteria.
     * @param whereJson
     * @return
     */
    @Operation(summary = "Update an existing model instance or insert a new one into the data source based on the where criteria.")
    @PostMapping("upsertWithWhere")
    public ResponseMessage<DataCatalogsDto> upsertByWhere(@RequestParam("where") String whereJson, @RequestBody DataCatalogsDto dataCatalogs) {
        Where where = parseWhere(whereJson);
        return success(dataCatalogsService.upsertByWhere(where, dataCatalogs, getLoginUser()));
    }
    @GetMapping("getList")
    public ResponseMessage<List<Map>> getList(@RequestParam("filter") String filterJson){
      Filter filter = parseFilter(filterJson);
      return  success(dataCatalogsService.getList(filter, getLoginUser()));
    }
    @GetMapping("getCount")
    public ResponseMessage<Long> getCount(@RequestParam("filter") String filterJson){
      Filter filter = parseFilter(filterJson);
      return success(dataCatalogsService.getCount(filter,getLoginUser()));
    }
    @GetMapping("distinctTag")
    public ResponseMessage<List<String>> distinctTag(){
      return success(dataCatalogsService.distinctTag(getLoginUser()));
    }
    @PostMapping("analyzeByConnId")
  public ResponseMessage<Boolean> analyzeByConnId(@RequestParam("ids")List<String>  ids){
    return success(dataCatalogsService.analyzeByConnId(ids,getLoginUser()));
    }
}
