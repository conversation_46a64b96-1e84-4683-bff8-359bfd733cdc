package com.tapdata.tm.task.utils;


import com.tapdata.tm.commons.task.dto.Dag;

/**
 * @Author: Zed
 * @Date: 2021/11/4
 * @Description:
 */
public class DagUtils {

    /**
     * 校验dag中的node数量是否大于某个数值
     * @param dag dag
     * @param num 数值 默认 2
     * @return
     */
    public static boolean checkNodeNum(Dag dag, Integer num) {
        return false;
    }

    public static boolean checkCircle(Dag dag) {
        return false;
    }
}
