{"version": "1.2.0", "project": {"name": "Northwind (Tapdata)", "type": "SQL_SERVER", "lastModified": "2024-03-15T04:35:34.580Z", "schemasId": "7d1f0a7bb3e843b089d7c9affdc09662", "content": {"settings": {"viewMode": "VERTICAL", "shouldRecommendSchema": false, "casing": "CAMEL_CASE", "codegen": {"language": "JAVASCRIPT", "framework": "MONGOOSE_SCHEMAS"}, "keyHandling": "GENERATED"}, "collections": {"630fd23e-8d18-4d4b-8760-7e17a0c7ea03": {"name": "employees", "type": "REGULAR"}, "728ab4fe-4811-4069-b495-f4f8468a771a": {"name": "products", "type": "REGULAR"}, "98df29c4-918f-4c0d-8dc8-9f3ecc90b87e": {"name": "categories", "type": "REGULAR"}, "8f5aef4c-3899-45e3-80bf-ec1f85db4c44": {"name": "orders", "type": "REGULAR"}, "e801d404-fb9b-4d80-9a40-3dcdf7149377": {"name": "suppliers", "type": "REGULAR"}}, "mappings": {"1334091b-5ca0-4ad1-bd0b-c461d30b80ee": {"settings": {"type": "NEW_DOCUMENT", "notes": ""}, "fields": {"_id": {"target": {"name": "_id", "included": true, "isNullExcluded": false, "type": "OBJECT_ID"}, "source": {"name": "_id", "databaseSpecificType": "NONE", "isPrimaryKey": false}}, "EmployeeID": {"target": {"name": "employeeId", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "EmployeeID", "databaseSpecificType": "int identity", "isPrimaryKey": true}}, "LastName": {"target": {"name": "lastName", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "LastName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "FirstName": {"target": {"name": "firstName", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "FirstName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Title": {"target": {"name": "title", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "Title", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "TitleOfCourtesy": {"target": {"name": "titleOfCourtesy", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "TitleOfCourtesy", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "BirthDate": {"target": {"name": "birthDate", "included": true, "isNullExcluded": false, "type": "DATE"}, "source": {"name": "BirthDate", "databaseSpecificType": "datetime", "isPrimaryKey": false}}, "HireDate": {"target": {"name": "hireDate", "included": true, "isNullExcluded": false, "type": "DATE"}, "source": {"name": "HireDate", "databaseSpecificType": "datetime", "isPrimaryKey": false}}, "Address": {"target": {"name": "address", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "Address", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "City": {"target": {"name": "city", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "City", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Region": {"target": {"name": "region", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "Region", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "PostalCode": {"target": {"name": "postalCode", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "PostalCode", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Country": {"target": {"name": "country", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "Country", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "HomePhone": {"target": {"name": "homePhone", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "HomePhone", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Extension": {"target": {"name": "extension", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "Extension", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Photo": {"target": {"name": "photo", "included": true, "isNullExcluded": false, "type": "BIN_DATA"}, "source": {"name": "Photo", "databaseSpecificType": "image", "isPrimaryKey": false}}, "Notes": {"target": {"name": "notes", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "Notes", "databaseSpecificType": "ntext", "isPrimaryKey": false}}, "ReportsTo": {"target": {"name": "reportsTo", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "ReportsTo", "databaseSpecificType": "int", "isPrimaryKey": false}}, "PhotoPath": {"target": {"name": "photoPath", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "PhotoPath", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}}, "calculatedFields": {}, "collectionId": "630fd23e-8d18-4d4b-8760-7e17a0c7ea03", "table": "Northwind.dbo.Employees"}, "559c3fe5-d721-4218-8bcc-0ee6a0545e02": {"settings": {"type": "EMBEDDED_DOCUMENT_ARRAY", "notes": "", "embeddedPath": "direct", "primitive": false}, "fields": {"EmployeeID": {"target": {"name": "employeeId", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "EmployeeID", "databaseSpecificType": "int identity", "isPrimaryKey": true}}, "LastName": {"target": {"name": "lastName", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "LastName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "FirstName": {"target": {"name": "firstName", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "FirstName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Title": {"target": {"name": "title", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "Title", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "TitleOfCourtesy": {"target": {"name": "titleOfCourtesy", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "TitleOfCourtesy", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "BirthDate": {"target": {"name": "birthDate", "included": true, "isNullExcluded": false, "type": "DATE"}, "source": {"name": "BirthDate", "databaseSpecificType": "datetime", "isPrimaryKey": false}}, "HireDate": {"target": {"name": "hireDate", "included": true, "isNullExcluded": false, "type": "DATE"}, "source": {"name": "HireDate", "databaseSpecificType": "datetime", "isPrimaryKey": false}}, "Address": {"target": {"name": "address", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "Address", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "City": {"target": {"name": "city", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "City", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Region": {"target": {"name": "region", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "Region", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "PostalCode": {"target": {"name": "postalCode", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "PostalCode", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Country": {"target": {"name": "country", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "Country", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "HomePhone": {"target": {"name": "homePhone", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "HomePhone", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Extension": {"target": {"name": "extension", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "Extension", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Photo": {"target": {"name": "photo", "included": true, "isNullExcluded": false, "type": "BIN_DATA"}, "source": {"name": "Photo", "databaseSpecificType": "image", "isPrimaryKey": false}}, "Notes": {"target": {"name": "notes", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "Notes", "databaseSpecificType": "ntext", "isPrimaryKey": false}}, "ReportsTo": {"target": {"name": "reportsTo", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "ReportsTo", "databaseSpecificType": "int", "isPrimaryKey": false}}, "PhotoPath": {"target": {"name": "photoPath", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "PhotoPath", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}}, "calculatedFields": {}, "collectionId": "630fd23e-8d18-4d4b-8760-7e17a0c7ea03", "table": "Northwind.dbo.Employees"}, "7f4a1e57-d618-43e2-9a78-c7c2eb8c8492": {"settings": {"type": "EMBEDDED_DOCUMENT_ARRAY", "notes": "", "embeddedPath": "territories", "primitive": false}, "fields": {"EmployeeID": {"target": {"name": "employeeId", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "EmployeeID", "databaseSpecificType": "int", "isPrimaryKey": true}}, "TerritoryID": {"target": {"name": "territoryId", "included": false, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "TerritoryID", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": true}}}, "calculatedFields": {}, "collectionId": "630fd23e-8d18-4d4b-8760-7e17a0c7ea03", "table": "Northwind.dbo.EmployeeTerritories"}, "0c838058-bb5b-4be4-bef6-939d78aeb8f9": {"settings": {"type": "EMBEDDED_DOCUMENT", "notes": "", "embeddedPath": "", "primitive": false}, "fields": {"TerritoryID": {"target": {"name": "territoryId", "included": false, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "TerritoryID", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": true}}, "TerritoryDescription": {"target": {"name": "territoryDescription", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "TerritoryDescription", "databaseSpecificType": "nchar", "isPrimaryKey": false}}, "RegionID": {"target": {"name": "regionId", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "RegionID", "databaseSpecificType": "int", "isPrimaryKey": false}}}, "calculatedFields": {}, "collectionId": "630fd23e-8d18-4d4b-8760-7e17a0c7ea03", "table": "Northwind.dbo.Territories"}, "4adc1109-a302-42ae-82f8-835a44b6719d": {"settings": {"type": "EMBEDDED_DOCUMENT", "notes": "", "embeddedPath": "", "primitive": false}, "fields": {"RegionID": {"target": {"name": "regionId", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "RegionID", "databaseSpecificType": "int", "isPrimaryKey": true}}, "RegionDescription": {"target": {"name": "regionDescription", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "RegionDescription", "databaseSpecificType": "nchar", "isPrimaryKey": false}}}, "calculatedFields": {}, "collectionId": "630fd23e-8d18-4d4b-8760-7e17a0c7ea03", "table": "Northwind.dbo.Region"}, "f48ee897-8c19-4e74-a681-186888f8eb00": {"settings": {"type": "NEW_DOCUMENT", "notes": ""}, "fields": {"_id": {"target": {"name": "_id", "included": true, "isNullExcluded": false, "type": "OBJECT_ID"}, "source": {"name": "_id", "databaseSpecificType": "NONE", "isPrimaryKey": false}}, "ProductID": {"target": {"name": "productId", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "ProductID", "databaseSpecificType": "int identity", "isPrimaryKey": true}}, "ProductName": {"target": {"name": "productName", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "ProductName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "SupplierID": {"target": {"name": "supplierId", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "SupplierID", "databaseSpecificType": "int", "isPrimaryKey": false}}, "CategoryID": {"target": {"name": "categoryId", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "CategoryID", "databaseSpecificType": "int", "isPrimaryKey": false}}, "QuantityPerUnit": {"target": {"name": "quantityPerUnit", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "QuantityPerUnit", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "UnitPrice": {"target": {"name": "unitPrice", "included": true, "isNullExcluded": false, "type": "DECIMAL"}, "source": {"name": "UnitPrice", "databaseSpecificType": "money", "isPrimaryKey": false}}, "UnitsInStock": {"target": {"name": "unitsInStock", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "UnitsInStock", "databaseSpecificType": "smallint", "isPrimaryKey": false}}, "UnitsOnOrder": {"target": {"name": "unitsOnOrder", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "UnitsOnOrder", "databaseSpecificType": "smallint", "isPrimaryKey": false}}, "ReorderLevel": {"target": {"name": "reorderLevel", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "ReorderLevel", "databaseSpecificType": "smallint", "isPrimaryKey": false}}, "Discontinued": {"target": {"name": "discontinued", "included": true, "isNullExcluded": false, "type": "BOOL"}, "source": {"name": "Discontinued", "databaseSpecificType": "bit", "isPrimaryKey": false}}}, "calculatedFields": {}, "collectionId": "728ab4fe-4811-4069-b495-f4f8468a771a", "table": "Northwind.dbo.Products"}, "b8fde192-c492-477a-a567-e27644f0b5b4": {"settings": {"type": "EMBEDDED_DOCUMENT", "notes": "", "embeddedPath": "category", "primitive": false}, "fields": {"CategoryID": {"target": {"name": "categoryId", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "CategoryID", "databaseSpecificType": "int identity", "isPrimaryKey": true}}, "CategoryName": {"target": {"name": "categoryName", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "CategoryName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Description": {"target": {"name": "description", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "Description", "databaseSpecificType": "ntext", "isPrimaryKey": false}}, "Picture": {"target": {"name": "picture", "included": true, "isNullExcluded": false, "type": "BIN_DATA"}, "source": {"name": "Picture", "databaseSpecificType": "image", "isPrimaryKey": false}}}, "calculatedFields": {}, "collectionId": "728ab4fe-4811-4069-b495-f4f8468a771a", "table": "Northwind.dbo.Categories"}, "acc0513f-8aba-44e3-8e18-8bd040d2fa92": {"settings": {"type": "NEW_DOCUMENT", "notes": ""}, "fields": {"_id": {"target": {"name": "_id", "included": true, "isNullExcluded": false, "type": "OBJECT_ID"}, "source": {"name": "_id", "databaseSpecificType": "NONE", "isPrimaryKey": false}}, "CategoryID": {"target": {"name": "categoryId", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "CategoryID", "databaseSpecificType": "int identity", "isPrimaryKey": true}}, "CategoryName": {"target": {"name": "categoryName", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "CategoryName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Description": {"target": {"name": "description", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "Description", "databaseSpecificType": "ntext", "isPrimaryKey": false}}, "Picture": {"target": {"name": "picture", "included": true, "isNullExcluded": false, "type": "BIN_DATA"}, "source": {"name": "Picture", "databaseSpecificType": "image", "isPrimaryKey": false}}}, "calculatedFields": {}, "collectionId": "98df29c4-918f-4c0d-8dc8-9f3ecc90b87e", "table": "Northwind.dbo.Categories"}, "a8ec1a25-38c8-49c3-821b-e900fb731119": {"settings": {"type": "EMBEDDED_DOCUMENT_ARRAY", "notes": "", "embeddedPath": "products", "primitive": false}, "fields": {"ProductID": {"target": {"name": "productId", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "ProductID", "databaseSpecificType": "int identity", "isPrimaryKey": true}}, "ProductName": {"target": {"name": "productName", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "ProductName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "SupplierID": {"target": {"name": "supplierId", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "SupplierID", "databaseSpecificType": "int", "isPrimaryKey": false}}, "CategoryID": {"target": {"name": "categoryId", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "CategoryID", "databaseSpecificType": "int", "isPrimaryKey": false}}, "QuantityPerUnit": {"target": {"name": "quantityPerUnit", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "QuantityPerUnit", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "UnitPrice": {"target": {"name": "unitPrice", "included": true, "isNullExcluded": false, "type": "DECIMAL"}, "source": {"name": "UnitPrice", "databaseSpecificType": "money", "isPrimaryKey": false}}, "UnitsInStock": {"target": {"name": "unitsInStock", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "UnitsInStock", "databaseSpecificType": "smallint", "isPrimaryKey": false}}, "UnitsOnOrder": {"target": {"name": "unitsOnOrder", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "UnitsOnOrder", "databaseSpecificType": "smallint", "isPrimaryKey": false}}, "ReorderLevel": {"target": {"name": "reorderLevel", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "ReorderLevel", "databaseSpecificType": "smallint", "isPrimaryKey": false}}, "Discontinued": {"target": {"name": "discontinued", "included": true, "isNullExcluded": false, "type": "BOOL"}, "source": {"name": "Discontinued", "databaseSpecificType": "bit", "isPrimaryKey": false}}}, "calculatedFields": {}, "collectionId": "98df29c4-918f-4c0d-8dc8-9f3ecc90b87e", "table": "Northwind.dbo.Products"}, "24b12709-8cda-4442-9c1b-f00fabf47eb7": {"settings": {"type": "NEW_DOCUMENT", "notes": ""}, "fields": {"_id": {"target": {"name": "_id", "included": true, "isNullExcluded": false, "type": "OBJECT_ID"}, "source": {"name": "_id", "databaseSpecificType": "NONE", "isPrimaryKey": false}}, "OrderID": {"target": {"name": "orderId", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "OrderID", "databaseSpecificType": "int identity", "isPrimaryKey": true}}, "CustomerID": {"target": {"name": "customerId", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "CustomerID", "databaseSpecificType": "nchar", "isPrimaryKey": false}}, "EmployeeID": {"target": {"name": "employeeId", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "EmployeeID", "databaseSpecificType": "int", "isPrimaryKey": false}}, "OrderDate": {"target": {"name": "orderDate", "included": true, "isNullExcluded": false, "type": "DATE"}, "source": {"name": "OrderDate", "databaseSpecificType": "datetime", "isPrimaryKey": false}}, "RequiredDate": {"target": {"name": "requiredDate", "included": true, "isNullExcluded": false, "type": "DATE"}, "source": {"name": "RequiredDate", "databaseSpecificType": "datetime", "isPrimaryKey": false}}, "ShippedDate": {"target": {"name": "shippedDate", "included": true, "isNullExcluded": false, "type": "DATE"}, "source": {"name": "ShippedDate", "databaseSpecificType": "datetime", "isPrimaryKey": false}}, "ShipVia": {"target": {"name": "shipVia", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "ShipVia", "databaseSpecificType": "int", "isPrimaryKey": false}}, "Freight": {"target": {"name": "freight", "included": true, "isNullExcluded": false, "type": "DECIMAL"}, "source": {"name": "Freight", "databaseSpecificType": "money", "isPrimaryKey": false}}, "ShipName": {"target": {"name": "shipName", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "ShipName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "ShipAddress": {"target": {"name": "shipAddress", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "ShipAddress", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "ShipCity": {"target": {"name": "shipCity", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "ShipCity", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "ShipRegion": {"target": {"name": "shipRegion", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "ShipRegion", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "ShipPostalCode": {"target": {"name": "shipPostalCode", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "ShipPostalCode", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "ShipCountry": {"target": {"name": "shipCountry", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "ShipCountry", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}}, "calculatedFields": {}, "collectionId": "8f5aef4c-3899-45e3-80bf-ec1f85db4c44", "table": "Northwind.dbo.Orders"}, "5d598c5f-e5d7-46c4-b6b8-7f673fc10a2b": {"settings": {"type": "EMBEDDED_DOCUMENT_ARRAY", "notes": "", "embeddedPath": "orderDetails", "primitive": false}, "fields": {"OrderID": {"target": {"name": "orderId", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "OrderID", "databaseSpecificType": "int", "isPrimaryKey": true}}, "ProductID": {"target": {"name": "productId", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "ProductID", "databaseSpecificType": "int", "isPrimaryKey": true}}, "UnitPrice": {"target": {"name": "unitPrice", "included": true, "isNullExcluded": false, "type": "DECIMAL"}, "source": {"name": "UnitPrice", "databaseSpecificType": "money", "isPrimaryKey": false}}, "Quantity": {"target": {"name": "quantity", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "Quantity", "databaseSpecificType": "smallint", "isPrimaryKey": false}}, "Discount": {"target": {"name": "discount", "included": true, "isNullExcluded": false, "type": "DOUBLE"}, "source": {"name": "Discount", "databaseSpecificType": "real", "isPrimaryKey": false}}}, "calculatedFields": {"31715c08-eab2-441d-982f-fff41d0844e0": {"name": "subtotal", "isNullExcluded": false, "expression": "(columns[\"UnitPrice\"] * columns[\"Quantity\"]) - columns[\"Discount\"]"}}, "collectionId": "8f5aef4c-3899-45e3-80bf-ec1f85db4c44", "table": "Northwind.dbo.Order Details"}, "c3ff8d92-cf09-4f3a-9855-b02587d2ac12": {"settings": {"type": "EMBEDDED_DOCUMENT", "notes": "", "embeddedPath": "", "primitive": false}, "fields": {"ProductID": {"target": {"name": "productId", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "ProductID", "databaseSpecificType": "int identity", "isPrimaryKey": true}}, "ProductName": {"target": {"name": "productName", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "ProductName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "SupplierID": {"target": {"name": "supplierId", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "SupplierID", "databaseSpecificType": "int", "isPrimaryKey": false}}, "CategoryID": {"target": {"name": "categoryId", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "CategoryID", "databaseSpecificType": "int", "isPrimaryKey": false}}, "QuantityPerUnit": {"target": {"name": "quantityPerUnit", "included": false, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "QuantityPerUnit", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "UnitPrice": {"target": {"name": "unitPrice", "included": false, "isNullExcluded": false, "type": "DECIMAL"}, "source": {"name": "UnitPrice", "databaseSpecificType": "money", "isPrimaryKey": false}}, "UnitsInStock": {"target": {"name": "unitsInStock", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "UnitsInStock", "databaseSpecificType": "smallint", "isPrimaryKey": false}}, "UnitsOnOrder": {"target": {"name": "unitsOnOrder", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "UnitsOnOrder", "databaseSpecificType": "smallint", "isPrimaryKey": false}}, "ReorderLevel": {"target": {"name": "reorderLevel", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "ReorderLevel", "databaseSpecificType": "smallint", "isPrimaryKey": false}}, "Discontinued": {"target": {"name": "discontinued", "included": false, "isNullExcluded": false, "type": "BOOL"}, "source": {"name": "Discontinued", "databaseSpecificType": "bit", "isPrimaryKey": false}}}, "calculatedFields": {}, "collectionId": "8f5aef4c-3899-45e3-80bf-ec1f85db4c44", "table": "Northwind.dbo.Products"}, "d0edcffc-950e-4dea-8eeb-353e300a0b9d": {"settings": {"type": "NEW_DOCUMENT", "notes": ""}, "fields": {"_id": {"target": {"name": "_id", "included": true, "isNullExcluded": false, "type": "OBJECT_ID"}, "source": {"name": "_id", "databaseSpecificType": "NONE", "isPrimaryKey": false}}, "SupplierID": {"target": {"name": "supplierId", "included": true, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "SupplierID", "databaseSpecificType": "int identity", "isPrimaryKey": true}}, "CompanyName": {"target": {"name": "companyName", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "CompanyName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "ContactName": {"target": {"name": "contactName", "included": false, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "ContactName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "ContactTitle": {"target": {"name": "contactTitle", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "ContactTitle", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Address": {"target": {"name": "address", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "Address", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "City": {"target": {"name": "city", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "City", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Region": {"target": {"name": "region", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "Region", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "PostalCode": {"target": {"name": "postalCode", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "PostalCode", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Country": {"target": {"name": "country", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "Country", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Phone": {"target": {"name": "phone", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "Phone", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "Fax": {"target": {"name": "fax", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "Fax", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "HomePage": {"target": {"name": "homePage", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "HomePage", "databaseSpecificType": "ntext", "isPrimaryKey": false}}}, "calculatedFields": {"f45c6212-a44f-42fc-9c0d-04c067b08eec": {"name": "contact.first<PERSON><PERSON>", "isNullExcluded": false, "expression": "columns[\"ContactName\"].split(' ')[0]"}, "4fa7d130-82a9-489d-bcba-0fe487e54725": {"name": "contact.lastName", "isNullExcluded": false, "expression": "columns[\"ContactName\"].split(' ')[1]"}}, "collectionId": "e801d404-fb9b-4d80-9a40-3dcdf7149377", "table": "Northwind.dbo.Suppliers"}, "fbe33483-d7ba-4067-b4dc-4b5e55147756": {"settings": {"type": "EMBEDDED_DOCUMENT_ARRAY", "notes": "", "embeddedPath": "products", "primitive": true}, "fields": {"ProductID": {"target": {"name": "productId", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "ProductID", "databaseSpecificType": "int identity", "isPrimaryKey": true}}, "ProductName": {"target": {"name": "productName", "included": true, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "ProductName", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "SupplierID": {"target": {"name": "supplierId", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "SupplierID", "databaseSpecificType": "int", "isPrimaryKey": false}}, "CategoryID": {"target": {"name": "categoryId", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "CategoryID", "databaseSpecificType": "int", "isPrimaryKey": false}}, "QuantityPerUnit": {"target": {"name": "quantityPerUnit", "included": false, "isNullExcluded": false, "type": "STRING"}, "source": {"name": "QuantityPerUnit", "databaseSpecificType": "n<PERSON><PERSON><PERSON>", "isPrimaryKey": false}}, "UnitPrice": {"target": {"name": "unitPrice", "included": false, "isNullExcluded": false, "type": "DECIMAL"}, "source": {"name": "UnitPrice", "databaseSpecificType": "money", "isPrimaryKey": false}}, "UnitsInStock": {"target": {"name": "unitsInStock", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "UnitsInStock", "databaseSpecificType": "smallint", "isPrimaryKey": false}}, "UnitsOnOrder": {"target": {"name": "unitsOnOrder", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "UnitsOnOrder", "databaseSpecificType": "smallint", "isPrimaryKey": false}}, "ReorderLevel": {"target": {"name": "reorderLevel", "included": false, "isNullExcluded": false, "type": "INTEGER"}, "source": {"name": "ReorderLevel", "databaseSpecificType": "smallint", "isPrimaryKey": false}}, "Discontinued": {"target": {"name": "discontinued", "included": false, "isNullExcluded": false, "type": "BOOL"}, "source": {"name": "Discontinued", "databaseSpecificType": "bit", "isPrimaryKey": false}}}, "calculatedFields": {}, "collectionId": "e801d404-fb9b-4d80-9a40-3dcdf7149377", "table": "Northwind.dbo.Products"}}, "relationships": {"tables": {"Northwind.dbo.Employees": {"mappings": ["1334091b-5ca0-4ad1-bd0b-c461d30b80ee", "559c3fe5-d721-4218-8bcc-0ee6a0545e02"]}, "Northwind.dbo.EmployeeTerritories": {"mappings": ["7f4a1e57-d618-43e2-9a78-c7c2eb8c8492"]}, "Northwind.dbo.Territories": {"mappings": ["0c838058-bb5b-4be4-bef6-939d78aeb8f9"]}, "Northwind.dbo.Region": {"mappings": ["4adc1109-a302-42ae-82f8-835a44b6719d"]}, "Northwind.dbo.Products": {"mappings": ["f48ee897-8c19-4e74-a681-186888f8eb00", "a8ec1a25-38c8-49c3-821b-e900fb731119", "c3ff8d92-cf09-4f3a-9855-b02587d2ac12", "fbe33483-d7ba-4067-b4dc-4b5e55147756"]}, "Northwind.dbo.Categories": {"mappings": ["b8fde192-c492-477a-a567-e27644f0b5b4", "acc0513f-8aba-44e3-8e18-8bd040d2fa92"]}, "Northwind.dbo.Orders": {"mappings": ["24b12709-8cda-4442-9c1b-f00fabf47eb7"]}, "Northwind.dbo.Order Details": {"mappings": ["5d598c5f-e5d7-46c4-b6b8-7f673fc10a2b"]}, "Northwind.dbo.Suppliers": {"mappings": ["d0edcffc-950e-4dea-8eeb-353e300a0b9d"]}}, "collections": {"630fd23e-8d18-4d4b-8760-7e17a0c7ea03": {"mappings": ["1334091b-5ca0-4ad1-bd0b-c461d30b80ee", "559c3fe5-d721-4218-8bcc-0ee6a0545e02", "7f4a1e57-d618-43e2-9a78-c7c2eb8c8492", "0c838058-bb5b-4be4-bef6-939d78aeb8f9", "4adc1109-a302-42ae-82f8-835a44b6719d"]}, "728ab4fe-4811-4069-b495-f4f8468a771a": {"mappings": ["f48ee897-8c19-4e74-a681-186888f8eb00", "b8fde192-c492-477a-a567-e27644f0b5b4"]}, "98df29c4-918f-4c0d-8dc8-9f3ecc90b87e": {"mappings": ["acc0513f-8aba-44e3-8e18-8bd040d2fa92", "a8ec1a25-38c8-49c3-821b-e900fb731119"]}, "8f5aef4c-3899-45e3-80bf-ec1f85db4c44": {"mappings": ["24b12709-8cda-4442-9c1b-f00fabf47eb7", "5d598c5f-e5d7-46c4-b6b8-7f673fc10a2b", "c3ff8d92-cf09-4f3a-9855-b02587d2ac12"]}, "e801d404-fb9b-4d80-9a40-3dcdf7149377": {"mappings": ["d0edcffc-950e-4dea-8eeb-353e300a0b9d", "fbe33483-d7ba-4067-b4dc-4b5e55147756"]}}, "mappings": {"1334091b-5ca0-4ad1-bd0b-c461d30b80ee": {"children": ["559c3fe5-d721-4218-8bcc-0ee6a0545e02", "7f4a1e57-d618-43e2-9a78-c7c2eb8c8492"]}, "559c3fe5-d721-4218-8bcc-0ee6a0545e02": {"children": []}, "7f4a1e57-d618-43e2-9a78-c7c2eb8c8492": {"children": ["0c838058-bb5b-4be4-bef6-939d78aeb8f9"]}, "0c838058-bb5b-4be4-bef6-939d78aeb8f9": {"children": ["4adc1109-a302-42ae-82f8-835a44b6719d"]}, "4adc1109-a302-42ae-82f8-835a44b6719d": {"children": []}, "f48ee897-8c19-4e74-a681-186888f8eb00": {"children": ["b8fde192-c492-477a-a567-e27644f0b5b4"]}, "b8fde192-c492-477a-a567-e27644f0b5b4": {"children": []}, "acc0513f-8aba-44e3-8e18-8bd040d2fa92": {"children": ["a8ec1a25-38c8-49c3-821b-e900fb731119"]}, "a8ec1a25-38c8-49c3-821b-e900fb731119": {"children": []}, "24b12709-8cda-4442-9c1b-f00fabf47eb7": {"children": ["5d598c5f-e5d7-46c4-b6b8-7f673fc10a2b"]}, "5d598c5f-e5d7-46c4-b6b8-7f673fc10a2b": {"children": ["c3ff8d92-cf09-4f3a-9855-b02587d2ac12"]}, "c3ff8d92-cf09-4f3a-9855-b02587d2ac12": {"children": []}, "d0edcffc-950e-4dea-8eeb-353e300a0b9d": {"children": ["fbe33483-d7ba-4067-b4dc-4b5e55147756"]}, "fbe33483-d7ba-4067-b4dc-4b5e55147756": {"children": []}}}, "diagrams": {"activeTab": "355f0aba-f74d-419f-a580-5aca1ad59341", "tabs": [{"id": "355f0aba-f74d-419f-a580-5aca1ad59341", "name": "Main Diagram", "relational": {"nodes": [{"id": "Northwind.dbo.Orders", "type": "entityCard", "position": {"x": 710, "y": 246}, "width": 244, "height": 296, "hidden": false}, {"id": "Northwind.dbo.Products", "type": "entityCard", "position": {"x": 1438.6666666666667, "y": 294}, "width": 244, "height": 224, "hidden": false}, {"id": "Northwind.dbo.Categories", "type": "entityCard", "position": {"x": 1398, "y": 642}, "width": 244, "height": 116, "hidden": false}, {"id": "Northwind.dbo.Shippers", "type": "entityCard", "position": {"x": 710, "y": 642}, "width": 244, "height": 98, "hidden": false}, {"id": "Northwind.dbo.Employees", "type": "entityCard", "position": {"x": 366, "y": 642}, "width": 244, "height": 368, "hidden": false}, {"id": "Northwind.dbo.Territories", "type": "entityCard", "position": {"x": 12, "y": 777}, "width": 244, "height": 98, "hidden": false}, {"id": "Northwind.dbo.Suppliers", "type": "entityCard", "position": {"x": 1768, "y": 590}, "width": 244, "height": 260, "hidden": false}, {"id": "Northwind.dbo.EmployeeTerritories", "type": "entityCard", "position": {"x": 52.66666666666667, "y": 462}, "width": 244, "height": 80, "hidden": false}, {"id": "Northwind.dbo.Customers", "type": "entityCard", "position": {"x": 1054, "y": 642}, "width": 244, "height": 242, "hidden": false}, {"id": "Northwind.dbo.Region", "type": "entityCard", "position": {"x": 12, "y": 1110}, "width": 244, "height": 80, "hidden": false}, {"id": "Northwind.dbo.Order Details", "type": "entityCard", "position": {"x": 750.6666666666666, "y": 12}, "width": 244, "height": 134, "hidden": false}], "edges": [{"id": "12beadc0-fbd7-4028-b2d5-28b2aacea0b2", "source": "Northwind.dbo.Customers", "target": "Northwind.dbo.Orders", "markerStart": "START_ONE", "markerEnd": "END_MANY"}, {"id": "84a05f1c-fbe6-4a09-841a-58333fc2b935", "source": "Northwind.dbo.Shippers", "target": "Northwind.dbo.Orders", "markerStart": "START_ONE", "markerEnd": "END_MANY"}, {"id": "2a714620-cf05-4258-91a5-4e4353b1d578", "source": "Northwind.dbo.Employees", "target": "Northwind.dbo.Orders", "markerStart": "START_ONE", "markerEnd": "END_MANY"}, {"id": "a7893d44-0bcf-4826-9334-aa0879a97b34", "source": "Northwind.dbo.Categories", "target": "Northwind.dbo.Products", "markerStart": "START_ONE", "markerEnd": "END_MANY"}, {"id": "fe7475a7-c754-4530-a959-9355abacdddb", "source": "Northwind.dbo.Suppliers", "target": "Northwind.dbo.Products", "markerStart": "START_ONE", "markerEnd": "END_MANY"}, {"id": "41345a90-0969-4ee3-bc04-02fdb37637cc", "source": "Northwind.dbo.Employees", "target": "Northwind.dbo.Employees", "markerStart": "START_ONE", "markerEnd": "END_MANY"}, {"id": "d5a3fbaa-709c-4b10-ab39-9c9d4b1efb52", "source": "Northwind.dbo.Region", "target": "Northwind.dbo.Territories", "markerStart": "START_ONE", "markerEnd": "END_MANY"}, {"id": "44751bdb-252c-42dd-9248-9e5dbf42e742", "source": "Northwind.dbo.Territories", "target": "Northwind.dbo.EmployeeTerritories", "markerStart": "START_ONE", "markerEnd": "END_MANY"}, {"id": "7b52ee24-7df5-46ba-94b5-d8a7ee504895", "source": "Northwind.dbo.Employees", "target": "Northwind.dbo.EmployeeTerritories", "markerStart": "START_ONE", "markerEnd": "END_MANY"}, {"id": "cef15864-40a9-4d8c-b142-b26c4424b2aa", "source": "Northwind.dbo.Products", "target": "Northwind.dbo.Order Details", "markerStart": "START_ONE", "markerEnd": "END_MANY"}, {"id": "bacd9ffc-6c32-400e-9ca2-6c9065067beb", "source": "Northwind.dbo.Orders", "target": "Northwind.dbo.Order Details", "markerStart": "START_ONE", "markerEnd": "END_MANY"}]}, "collection": {"nodes": [{"id": "employees", "type": "entityCard", "position": {"x": -189.90632911392413, "y": 41.14177215189875}, "width": 244, "height": 782, "hidden": false}, {"id": "products", "type": "entityCard", "position": {"x": -130.13915451529328, "y": 906.0599759810209}, "width": 244, "height": 332, "hidden": false}, {"id": "categories", "type": "entityCard", "position": {"x": -58.80947901728814, "y": 1328.0953873134677}, "width": 244, "height": 278, "hidden": false}, {"id": "orders", "type": "entityCard", "position": {"x": 411, "y": 884.0953873134677}, "width": 244, "height": 422, "hidden": false}, {"id": "suppliers", "type": "entityCard", "position": {"x": 351.6089500407261, "y": 1393.3234113509723}, "width": 244, "height": 332, "hidden": false}], "edges": [{"id": "f619bdbc-e765-43d4-ae3a-c7e56556ee26", "source": "employees", "target": "employees", "markerStart": "START_ONE_OR_MANY", "markerEnd": "END_ONE_OR_MANY"}, {"id": "1c0c5434-2c02-4675-9160-77fb5824d3ef", "source": "suppliers", "target": "products", "markerStart": "START_ONE", "markerEnd": "END_MANY"}, {"id": "ce7c6b9d-b615-4e15-a709-5de72b79332e", "source": "employees", "target": "orders", "markerStart": "START_ONE", "markerEnd": "END_MANY"}]}}]}}, "connectionDetails": {"jdbc": {"type": "SQL_SERVER", "url": "*************************************************************************************************************************************************", "user": "admin", "isManualUri": false, "host": "migrator-sqlserver.cvkvdfxd4zgh.ap-southeast-2.rds.amazonaws.com", "port": "", "database": "Northwind", "mssqlAuthentication": "SQL_SERVER", "useSSL": true, "trustServerCertificate": true, "savePassword": true}, "mongodb": {"connectionString": "mongodb+srv://cluster0.vzjyr.mongodb.net/Tom?retryWrites=true&w=majority", "database": "TomTapdataTest", "user": "tom", "savePassword": true, "hasTimeSeriesCollection": false}}, "id": "decc22d65352422799c2826be99bf263"}, "schema": {"id": "7d1f0a7bb3e843b089d7c9affdc09662", "full": {"databases": {"Northwind": {"schemas": {"dbo": {"tables": {"Orders": {"type": "TABLE", "size": -1, "columns": {"RequiredDate": {"type": {"databaseSpecificType": "datetime", "precision": 23, "scale": 3}, "ordinalPosition": 5, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ShipName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 40, "scale": 0}, "ordinalPosition": 9, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ShippedDate": {"type": {"databaseSpecificType": "datetime", "precision": 23, "scale": 3}, "ordinalPosition": 6, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": false}, "ShipCity": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 15, "scale": 0}, "ordinalPosition": 11, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CustomerID": {"type": {"databaseSpecificType": "nchar", "precision": 5, "scale": 0}, "ordinalPosition": 2, "foreignKey": {"name": "FK_Orders_Customers", "schema": "dbo", "table": "Customers", "column": "CustomerID", "cardinality": "ZERO_TO_MANY"}, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": false}, "ShipVia": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 7, "foreignKey": {"name": "FK_Orders_Shippers", "schema": "dbo", "table": "Shippers", "column": "ShipperID", "cardinality": "ZERO_TO_MANY"}, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": false}, "ShipPostalCode": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 13, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": false}, "OrderID": {"type": {"databaseSpecificType": "int identity", "precision": 10, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK_Orders"}, "nullable": false, "autoIncremented": true, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "OrderDate": {"type": {"databaseSpecificType": "datetime", "precision": 23, "scale": 3}, "ordinalPosition": 4, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": false}, "ShipRegion": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 15, "scale": 0}, "ordinalPosition": 12, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ShipAddress": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 60, "scale": 0}, "ordinalPosition": 10, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ShipCountry": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 15, "scale": 0}, "ordinalPosition": 14, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "EmployeeID": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 3, "foreignKey": {"name": "FK_Orders_Employees", "schema": "dbo", "table": "Employees", "column": "EmployeeID", "cardinality": "ZERO_TO_MANY"}, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": false}, "Freight": {"type": {"databaseSpecificType": "money", "precision": 19, "scale": 4}, "ordinalPosition": 8, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK_Orders": ["OrderID"]}}, "Products": {"type": "TABLE", "size": -1, "columns": {"CategoryID": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 4, "foreignKey": {"name": "FK_Products_Categories", "schema": "dbo", "table": "Categories", "column": "CategoryID", "cardinality": "ZERO_TO_MANY"}, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": false}, "Discontinued": {"type": {"databaseSpecificType": "bit", "precision": 1, "scale": 0}, "ordinalPosition": 10, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "SupplierID": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 3, "foreignKey": {"name": "FK_Products_Suppliers", "schema": "dbo", "table": "Suppliers", "column": "SupplierID", "cardinality": "ZERO_TO_MANY"}, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": false}, "UnitPrice": {"type": {"databaseSpecificType": "money", "precision": 19, "scale": 4}, "ordinalPosition": 6, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ProductName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 40, "scale": 0}, "ordinalPosition": 2, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": false}, "QuantityPerUnit": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 20, "scale": 0}, "ordinalPosition": 5, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "UnitsOnOrder": {"type": {"databaseSpecificType": "smallint", "precision": 5, "scale": 0}, "ordinalPosition": 8, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ProductID": {"type": {"databaseSpecificType": "int identity", "precision": 10, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK_Products"}, "nullable": false, "autoIncremented": true, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "ReorderLevel": {"type": {"databaseSpecificType": "smallint", "precision": 5, "scale": 0}, "ordinalPosition": 9, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "UnitsInStock": {"type": {"databaseSpecificType": "smallint", "precision": 5, "scale": 0}, "ordinalPosition": 7, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK_Products": ["ProductID"]}}, "Categories": {"type": "TABLE", "size": -1, "columns": {"CategoryID": {"type": {"databaseSpecificType": "int identity", "precision": 10, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK_Categories"}, "nullable": false, "autoIncremented": true, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "Description": {"type": {"databaseSpecificType": "ntext", "precision": 1073741823, "scale": 0}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Picture": {"type": {"databaseSpecificType": "image", "precision": 2147483647, "scale": 0}, "ordinalPosition": 4, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CategoryName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 15, "scale": 0}, "ordinalPosition": 2, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": false}}, "uniqueIndexes": {"PK_Categories": ["CategoryID"]}}, "Shippers": {"type": "TABLE", "size": -1, "columns": {"CompanyName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 40, "scale": 0}, "ordinalPosition": 2, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Phone": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 24, "scale": 0}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ShipperID": {"type": {"databaseSpecificType": "int identity", "precision": 10, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK_Shippers"}, "nullable": false, "autoIncremented": true, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}}, "uniqueIndexes": {"PK_Shippers": ["ShipperID"]}}, "Employees": {"type": "TABLE", "size": -1, "columns": {"Address": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 60, "scale": 0}, "ordinalPosition": 8, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "FirstName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 3, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Title": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 30, "scale": 0}, "ordinalPosition": 4, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "PostalCode": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 11, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": false}, "Photo": {"type": {"databaseSpecificType": "image", "precision": 2147483647, "scale": 0}, "ordinalPosition": 15, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "City": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 15, "scale": 0}, "ordinalPosition": 9, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Extension": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 4, "scale": 0}, "ordinalPosition": 14, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "PhotoPath": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 255, "scale": 0}, "ordinalPosition": 18, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "HomePhone": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 24, "scale": 0}, "ordinalPosition": 13, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "TitleOfCourtesy": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 25, "scale": 0}, "ordinalPosition": 5, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "HireDate": {"type": {"databaseSpecificType": "datetime", "precision": 23, "scale": 3}, "ordinalPosition": 7, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Region": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 15, "scale": 0}, "ordinalPosition": 10, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Country": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 15, "scale": 0}, "ordinalPosition": 12, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ReportsTo": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 17, "foreignKey": {"name": "FK_Employees_Employees", "schema": "dbo", "table": "Employees", "column": "EmployeeID", "cardinality": "ZERO_TO_MANY"}, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "LastName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 20, "scale": 0}, "ordinalPosition": 2, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": false}, "EmployeeID": {"type": {"databaseSpecificType": "int identity", "precision": 10, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK_Employees"}, "nullable": false, "autoIncremented": true, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "Notes": {"type": {"databaseSpecificType": "ntext", "precision": 1073741823, "scale": 0}, "ordinalPosition": 16, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "BirthDate": {"type": {"databaseSpecificType": "datetime", "precision": 23, "scale": 3}, "ordinalPosition": 6, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK_Employees": ["EmployeeID"]}}, "Territories": {"type": "TABLE", "size": -1, "columns": {"TerritoryID": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 20, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK_Territories"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "TerritoryDescription": {"type": {"databaseSpecificType": "nchar", "precision": 50, "scale": 0}, "ordinalPosition": 2, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "RegionID": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 3, "foreignKey": {"name": "FK_Territories_Region", "schema": "dbo", "table": "Region", "column": "RegionID", "cardinality": "ZERO_TO_MANY"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK_Territories": ["TerritoryID"]}}, "CustomerDemographics": {"type": "TABLE", "size": -1, "columns": {"CustomerDesc": {"type": {"databaseSpecificType": "ntext", "precision": 1073741823, "scale": 0}, "ordinalPosition": 2, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CustomerTypeID": {"type": {"databaseSpecificType": "nchar", "precision": 10, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK_CustomerDemographics"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}}, "uniqueIndexes": {"PK_CustomerDemographics": ["CustomerTypeID"]}}, "Suppliers": {"type": "TABLE", "size": -1, "columns": {"SupplierID": {"type": {"databaseSpecificType": "int identity", "precision": 10, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK_Suppliers"}, "nullable": false, "autoIncremented": true, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "CompanyName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 40, "scale": 0}, "ordinalPosition": 2, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": false}, "HomePage": {"type": {"databaseSpecificType": "ntext", "precision": 1073741823, "scale": 0}, "ordinalPosition": 12, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Address": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 60, "scale": 0}, "ordinalPosition": 5, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Phone": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 24, "scale": 0}, "ordinalPosition": 10, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Region": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 15, "scale": 0}, "ordinalPosition": 7, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "PostalCode": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 8, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": false}, "Country": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 15, "scale": 0}, "ordinalPosition": 9, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "City": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 15, "scale": 0}, "ordinalPosition": 6, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Fax": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 24, "scale": 0}, "ordinalPosition": 11, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ContactName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 30, "scale": 0}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ContactTitle": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 30, "scale": 0}, "ordinalPosition": 4, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK_Suppliers": ["SupplierID"]}}, "CustomerCustomerDemo": {"type": "TABLE", "size": -1, "columns": {"CustomerTypeID": {"type": {"databaseSpecificType": "nchar", "precision": 10, "scale": 0}, "ordinalPosition": 2, "primaryKey": {"name": "PK_CustomerCustomerDemo"}, "foreignKey": {"name": "FK_CustomerCustomerDemo", "schema": "dbo", "table": "CustomerDemographics", "column": "CustomerTypeID", "cardinality": "ZERO_TO_MANY"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "CustomerID": {"type": {"databaseSpecificType": "nchar", "precision": 5, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK_CustomerCustomerDemo"}, "foreignKey": {"name": "FK_CustomerCustomerDemo_Customers", "schema": "dbo", "table": "Customers", "column": "CustomerID", "cardinality": "ZERO_TO_MANY"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}}, "uniqueIndexes": {"PK_CustomerCustomerDemo": ["CustomerID", "CustomerTypeID"]}}, "EmployeeTerritories": {"type": "TABLE", "size": -1, "columns": {"TerritoryID": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 20, "scale": 0}, "ordinalPosition": 2, "primaryKey": {"name": "PK_EmployeeTerritories"}, "foreignKey": {"name": "FK_EmployeeTerritories_Territories", "schema": "dbo", "table": "Territories", "column": "TerritoryID", "cardinality": "ZERO_TO_MANY"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "EmployeeID": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK_EmployeeTerritories"}, "foreignKey": {"name": "FK_EmployeeTerritories_Employees", "schema": "dbo", "table": "Employees", "column": "EmployeeID", "cardinality": "ZERO_TO_MANY"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}}, "uniqueIndexes": {"PK_EmployeeTerritories": ["EmployeeID", "TerritoryID"]}}, "Customers": {"type": "TABLE", "size": -1, "columns": {"CompanyName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 40, "scale": 0}, "ordinalPosition": 2, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": false}, "Address": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 60, "scale": 0}, "ordinalPosition": 5, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Phone": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 24, "scale": 0}, "ordinalPosition": 10, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Region": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 15, "scale": 0}, "ordinalPosition": 7, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": false}, "PostalCode": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 10, "scale": 0}, "ordinalPosition": 8, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": false}, "Country": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 15, "scale": 0}, "ordinalPosition": 9, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "CustomerID": {"type": {"databaseSpecificType": "nchar", "precision": 5, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK_Customers"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "City": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 15, "scale": 0}, "ordinalPosition": 6, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": false}, "Fax": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 24, "scale": 0}, "ordinalPosition": 11, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ContactName": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 30, "scale": 0}, "ordinalPosition": 3, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ContactTitle": {"type": {"databaseSpecificType": "n<PERSON><PERSON><PERSON>", "precision": 30, "scale": 0}, "ordinalPosition": 4, "nullable": true, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}}, "uniqueIndexes": {"PK_Customers": ["CustomerID"]}}, "Region": {"type": "TABLE", "size": -1, "columns": {"RegionDescription": {"type": {"databaseSpecificType": "nchar", "precision": 50, "scale": 0}, "ordinalPosition": 2, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "RegionID": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK_Region"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}}, "uniqueIndexes": {"PK_Region": ["RegionID"]}}, "Order Details": {"type": "TABLE", "size": -1, "columns": {"UnitPrice": {"type": {"databaseSpecificType": "money", "precision": 19, "scale": 4}, "ordinalPosition": 3, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Discount": {"type": {"databaseSpecificType": "real", "precision": 24, "scale": 0}, "ordinalPosition": 5, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "Quantity": {"type": {"databaseSpecificType": "smallint", "precision": 5, "scale": 0}, "ordinalPosition": 4, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": false, "uniqueIndexed": false}, "ProductID": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 2, "primaryKey": {"name": "PK_Order_Details"}, "foreignKey": {"name": "FK_Order_Details_Products", "schema": "dbo", "table": "Products", "column": "ProductID", "cardinality": "ZERO_TO_MANY"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}, "OrderID": {"type": {"databaseSpecificType": "int", "precision": 10, "scale": 0}, "ordinalPosition": 1, "primaryKey": {"name": "PK_Order_Details"}, "foreignKey": {"name": "FK_Order_Details_Orders", "schema": "dbo", "table": "Orders", "column": "OrderID", "cardinality": "ZERO_TO_MANY"}, "nullable": false, "autoIncremented": false, "generated": false, "hidden": false, "indexed": true, "uniqueIndexed": true}}, "uniqueIndexes": {"PK_Order_Details": ["OrderID", "ProductID"]}}}, "views": {"Customer and Suppliers by City": {"included": true, "name": "Customer and Suppliers by City", "definition": "create view \"Customer and Suppliers by City\" AS\r\nSELECT City, CompanyName, ContactName, 'Customers' AS Relationship \r\nFROM Customers\r\nUNION SELECT City, CompanyName, ContactName, 'Suppliers'\r\nFROM Suppliers\r\n--ORDER BY City, CompanyName"}, "Products Above Average Price": {"included": true, "name": "Products Above Average Price", "definition": "create view \"Products Above Average Price\" AS\r\nSELECT Products.ProductName, Products.UnitPrice\r\nFROM Products\r\nWHERE Products.UnitPrice>(SELECT AVG(UnitPrice) From Products)\r\n--ORDER BY Products.UnitPrice DESC"}, "Alphabetical list of products": {"included": true, "name": "Alphabetical list of products", "definition": "create view \"Alphabetical list of products\" AS\r\nSELECT Products.*, Categories.CategoryName\r\nFROM Categories INNER JOIN Products ON Categories.CategoryID = Products.CategoryID\r\nWHERE (((Products.Discontinued)=0))"}, "Category Sales for 1997": {"included": true, "name": "Category Sales for 1997", "definition": "create view \"Category Sales for 1997\" AS\r\nSELECT \"Product Sales for 1997\".CategoryName, Sum(\"Product Sales for 1997\".ProductSales) AS CategorySales\r\nFROM \"Product Sales for 1997\"\r\nGROUP BY \"Product Sales for 1997\".CategoryName"}, "Order Details Extended": {"included": true, "name": "Order Details Extended", "definition": "create view \"Order Details Extended\" AS\r\nSELECT \"Order Details\".OrderID, \"Order Details\".ProductID, Products.ProductName, \r\n\t\"Order Details\".UnitPrice, \"Order Details\".Quantity, \"Order Details\".Discount, \r\n\t(CONVERT(money,(\"Order Details\".UnitPrice*Quantity*(1-Discount)/100))*100) AS ExtendedPrice\r\nFROM Products INNER JOIN \"Order Details\" ON Products.ProductID = \"Order Details\".ProductID\r\n--ORDER BY \"Order Details\".OrderID"}, "Orders Qry": {"included": true, "name": "Orders Qry", "definition": "create view \"Orders Qry\" AS\r\nSELECT Orders.OrderID, Orders.CustomerID, Orders.EmployeeID, Orders.OrderDate, Orders.RequiredDate, \r\n\tOrders.ShippedDate, Orders.ShipVia, Orders.Freight, Orders.ShipName, Orders.ShipAddress, Orders.ShipCity, \r\n\tOrders.ShipRegion, Orders.ShipPostalCode, Orders.ShipCountry, \r\n\tCustomers.CompanyName, Customers.Address, Customers.City, Customers.Region, Customers.PostalCode, Customers.Country\r\nFROM Customers INNER JOIN Orders ON Customers.CustomerID = Orders.CustomerID"}, "Invoices": {"included": true, "name": "Invoices", "definition": "create view Invoices AS\r\nSELECT Orders.ShipName, Orders.ShipAddress, Orders.ShipCity, Orders.ShipRegion, Orders.ShipPostalCode, \r\n\tOrders.ShipCountry, Orders.CustomerID, Customers.CompanyName AS CustomerName, Customers.Address, Customers.City, \r\n\tCustomers.Region, Customers.PostalCode, Customers.Country, \r\n\t(FirstName + ' ' + LastName) AS Salesperson, \r\n\tOrders.OrderID, Orders.OrderDate, Orders.RequiredDate, Orders.ShippedDate, Shippers.CompanyName As ShipperName, \r\n\t\"Order Details\".ProductID, Products.ProductName, \"Order Details\".UnitPrice, \"Order Details\".Quantity, \r\n\t\"Order Details\".Discount, \r\n\t(CONVERT(money,(\"Order Details\".UnitPrice*Quantity*(1-Discount)/100))*100) AS ExtendedPrice, Orders.Freight\r\nFROM \tShippers INNER JOIN \r\n\t\t(Products INNER JOIN \r\n\t\t\t(\r\n\t\t\t\t(Employees INNER JOIN \r\n\t\t\t\t\t(Customers INNER JOIN Orders ON Customers.CustomerID = Orders.CustomerID) \r\n\t\t\t\tON Employees.EmployeeID = Orders.EmployeeID) \r\n\t\t\tINNER JOIN \"Order Details\" ON Orders.OrderID = \"Order Details\".OrderID) \r\n\t\tON Products.ProductID = \"Order Details\".ProductID) \r\n\tON Shippers.ShipperID = Orders.ShipVia"}, "Summary of Sales by Quarter": {"included": true, "name": "Summary of Sales by Quarter", "definition": "create view \"Summary of Sales by Quarter\" AS\r\nSELECT Orders.ShippedDate, Orders.OrderID, \"Order Subtotals\".Subtotal\r\nFROM Orders INNER JOIN \"Order Subtotals\" ON Orders.OrderID = \"Order Subtotals\".OrderID\r\nWHERE Orders.ShippedDate IS NOT NULL\r\n--ORDER BY Orders.ShippedDate"}, "Current Product List": {"included": true, "name": "Current Product List", "definition": "create view \"Current Product List\" AS\r\nSELECT Product_List.ProductID, Product_List.ProductName\r\nFROM Products AS Product_List\r\nWHERE (((Product_List.Discontinued)=0))\r\n--ORDER BY Product_List.ProductName"}, "Summary of Sales by Year": {"included": true, "name": "Summary of Sales by Year", "definition": "create view \"Summary of Sales by Year\" AS\r\nSELECT Orders.ShippedDate, Orders.OrderID, \"Order Subtotals\".Subtotal\r\nFROM Orders INNER JOIN \"Order Subtotals\" ON Orders.OrderID = \"Order Subtotals\".OrderID\r\nWHERE Orders.ShippedDate IS NOT NULL\r\n--ORDER BY Orders.ShippedDate"}, "Sales by Category": {"included": true, "name": "Sales by Category", "definition": "create view \"Sales by Category\" AS\r\nSELECT Categories.CategoryID, Categories.CategoryName, Products.ProductName, \r\n\tSum(\"Order Details Extended\".ExtendedPrice) AS ProductSales\r\nFROM \tCategories INNER JOIN \r\n\t\t(Products INNER JOIN \r\n\t\t\t(Orders INNER JOIN \"Order Details Extended\" ON Orders.OrderID = \"Order Details Extended\".OrderID) \r\n\t\tON Products.ProductID = \"Order Details Extended\".ProductID) \r\n\tON Categories.CategoryID = Products.CategoryID\r\nWHERE Orders.OrderDate BETWEEN '19970101' And '19971231'\r\nGROUP BY Categories.CategoryID, Categories.CategoryName, Products.ProductName\r\n--ORDER BY Products.ProductName"}, "Sales Totals by Amount": {"included": true, "name": "Sales Totals by Amount", "definition": "create view \"Sales Totals by Amount\" AS\r\nSELECT \"Order Subtotals\".Subtotal AS SaleAmount, Orders.OrderID, Customers.CompanyName, Orders.ShippedDate\r\nFROM \tCustomers INNER JOIN \r\n\t\t(Orders INNER JOIN \"Order Subtotals\" ON Orders.OrderID = \"Order Subtotals\".OrderID) \r\n\tON Customers.CustomerID = Orders.CustomerID\r\nWHERE (\"Order Subtotals\".Subtotal >2500) AND (Orders.ShippedDate BETWEEN '19970101' And '19971231')"}, "Order Subtotals": {"included": true, "name": "Order Subtotals", "definition": "create view \"Order Subtotals\" AS\r\nSELECT \"Order Details\".OrderID, Sum(CONVERT(money,(\"Order Details\".UnitPrice*Quantity*(1-Discount)/100))*100) AS Subtotal\r\nFROM \"Order Details\"\r\nGROUP BY \"Order Details\".OrderID"}, "TomsDodgyView": {"included": true, "name": "TomsDodgy<PERSON>iew", "definition": "CREATE VIEW TomsDodgyView\r\nAS SELECT * FROM Customers WHERE ContactName LIKE 'A%'"}, "Product Sales for 1997": {"included": true, "name": "Product Sales for 1997", "definition": "create view \"Product Sales for 1997\" AS\r\nSELECT Categories.CategoryName, Products.ProductName, \r\nSum(CONVERT(money,(\"Order Details\".UnitPrice*Quantity*(1-Discount)/100))*100) AS ProductSales\r\nFROM (Categories INNER JOIN Products ON Categories.CategoryID = Products.CategoryID) \r\n\tINNER JOIN (Orders \r\n\t\tINNER JOIN \"Order Details\" ON Orders.OrderID = \"Order Details\".OrderID) \r\n\tON Products.ProductID = \"Order Details\".ProductID\r\nWHERE (((Orders.ShippedDate) Between '19970101' And '19971231'))\r\nGROUP BY Categories.CategoryName, Products.ProductName"}, "Products by Category": {"included": true, "name": "Products by Category", "definition": "create view \"Products by Category\" AS\r\nSELECT Categories.CategoryName, Products.ProductName, Products.QuantityPerUnit, Products.UnitsInStock, Products.Discontinued\r\nFROM Categories INNER JOIN Products ON Categories.CategoryID = Products.CategoryID\r\nWHERE Products.Discontinued <> 1\r\n--ORDER BY Categories.CategoryName, Products.ProductName"}, "Quarterly Orders": {"included": true, "name": "Quarterly Orders", "definition": "create view \"Quarterly Orders\" AS\r\nSELECT DISTINCT Customers.CustomerID, Customers.CompanyName, Customers.City, Customers.Country\r\nFROM Customers RIGHT JOIN Orders ON Customers.CustomerID = Orders.CustomerID\r\nWHERE Orders.OrderDate BETWEEN '19970101' And '19971231'"}}, "routines": {"sp_enable_disable_cdc_all_tables": {"included": false, "name": "sp_enable_disable_cdc_all_tables", "definition": "create procedure sp_enable_disable_cdc_all_tables(@dbname varchar(100), @enable bit)  \nas  \n  \nBEGIN TRY  \nDECLARE @source_name varchar(400);  \ndeclare @sql varchar(1000)  \nDECLARE the_cursor CURSOR FAST_FORWARD FOR  \nSELECT table_name  \nFROM INFORMATION_SCHEMA.TABLES where TABLE_CATALOG=@dbname and table_schema='dbo' and table_name != 'systranschemas'  \nOPEN the_cursor  \nFETCH NEXT FROM the_cursor INTO @source_name  \n  \nWHILE @@FETCH_STATUS = 0  \nBEGIN  \nif @enable = 1  \n  \nset @sql =' Use '+ @dbname+ ';EXEC sys.sp_cdc_enable_table  \n            @source_schema = N''dbo'',@source_name = '+@source_name+'  \n          , @role_name = N'''+'dbo'+''''  \n            \nelse  \nset @sql =' Use '+ @dbname+ ';EXEC sys.sp_cdc_disable_table  \n            @source_schema = N''dbo'',@source_name = '+@source_name+',  @capture_instance =''all'''  \nexec(@sql)  \n  \n  \n  FETCH NEXT FROM the_cursor INTO @source_name  \n  \nEND  \n  \nCLOSE the_cursor  \nDEALLOCATE the_cursor  \n  \n      \nSELECT 'Successful'  \nEND TRY  \nBEGIN CATCH  \nCLOSE the_cursor  \nDEALLOCATE the_cursor  \n  \n    SELECT   \n        ERROR_NUMBER() AS ErrorNumber  \n        ,ERROR_MESSAGE() AS ErrorMessage;  \nEND CATCH"}, "Ten Most Expensive Products": {"included": true, "name": "Ten Most Expensive Products", "definition": "create procedure \"Ten Most Expensive Products\" AS\r\nSET ROWCOUNT 10\r\nSELECT Products.ProductName AS TenMostExpensiveProducts, Products.UnitPrice\r\nFROM Products\r\nORDER BY Products.UnitPrice DESC"}, "CustOrderHist": {"included": true, "name": "CustOrderHist", "definition": "CREATE PROCEDURE [dbo].[CustOrderHist] @CustomerID nchar(5)\r\nAS\r\nSELECT ProductName, Total=SUM(Quantity)\r\nFROM Products P, [Order Details] OD, Orders O, Customers C\r\nWHERE C.CustomerID = @CustomerID\r\nAND C.CustomerID = O.CustomerID AND O.OrderID = OD.OrderID AND OD.ProductID = P.ProductID\r\nGROUP BY ProductName\r\nRETURN 888"}, "sp_renamediagram": {"included": false, "name": "sp_renamediagram", "definition": "CREATE PROCEDURE dbo.sp_renamediagram\r\n\t(\r\n\t\t@diagramname \t\tsysname,\r\n\t\t@owner_id\t\tint\t= null,\r\n\t\t@new_diagramname\tsysname\r\n\t\r\n\t)\r\n\tWITH EXECUTE AS 'dbo'\r\n\tAS\r\n\tBEGIN\r\n\t\tset nocount on\r\n\t\tdeclare @theId \t\t\tint\r\n\t\tdeclare @IsDbo \t\t\tint\r\n\t\t\r\n\t\tdeclare @UIDFound \t\tint\r\n\t\tdeclare @DiagId\t\t\tint\r\n\t\tdeclare @DiagIdTarg\t\tint\r\n\t\tdeclare @u_name\t\t\tsysname\r\n\t\tif((@diagramname is null) or (@new_diagramname is null))\r\n\t\tbegin\r\n\t\t\tRAISERROR ('Invalid value', 16, 1);\r\n\t\t\treturn -1\r\n\t\tend\r\n\t\r\n\t\tEXECUTE AS CALLER;\r\n\t\tselect @theId = DATABASE_PRINCIPAL_ID();\r\n\t\tselect @IsDbo = IS_MEMBER(N'db_owner'); \r\n\t\tif(@owner_id is null)\r\n\t\t\tselect @owner_id = @theId;\r\n\t\tREVERT;\r\n\t\r\n\t\tselect @u_name = USER_NAME(@owner_id)\r\n\t\r\n\t\tselect @DiagId = diagram_id, @UIDFound = principal_id from dbo.sysdiagrams where principal_id = @owner_id and name = @diagramname \r\n\t\tif(@DiagId IS NULL or (@IsDbo = 0 and @UIDFound <> @theId))\r\n\t\tbegin\r\n\t\t\tRAISERROR ('Diagram does not exist or you do not have permission.', 16, 1)\r\n\t\t\treturn -3\r\n\t\tend\r\n\t\r\n\t\t-- if((@u_name is not null) and (@new_diagramname = @diagramname))\t-- nothing will change\r\n\t\t--\treturn 0;\r\n\t\r\n\t\tif(@u_name is null)\r\n\t\t\tselect @DiagIdTarg = diagram_id from dbo.sysdiagrams where principal_id = @theId and name = @new_diagramname\r\n\t\telse\r\n\t\t\tselect @DiagIdTarg = diagram_id from dbo.sysdiagrams where principal_id = @owner_id and name = @new_diagramname\r\n\t\r\n\t\tif((@DiagIdTarg is not null) and  @DiagId <> @DiagIdTarg)\r\n\t\tbegin\r\n\t\t\tRAISERROR ('The name is already used.', 16, 1);\r\n\t\t\treturn -2\r\n\t\tend\t\t\r\n\t\r\n\t\tif(@u_name is null)\r\n\t\t\tupdate dbo.sysdiagrams set [name] = @new_diagramname, principal_id = @theId where diagram_id = @DiagId\r\n\t\telse\r\n\t\t\tupdate dbo.sysdiagrams set [name] = @new_diagramname where diagram_id = @DiagId\r\n\t\treturn 0\r\n\tEND"}, "SalesByCategory": {"included": true, "name": "SalesByCategory", "definition": "CREATE PROCEDURE SalesByCategory\r\n    @CategoryName nvarchar(15), @OrdYear nvarchar(4) = '1998'\r\nAS\r\nIF @OrdYear != '1996' AND @OrdYear != '1997' AND @OrdYear != '1998' \r\nBEGIN\r\n\tSELECT @OrdYear = '1998'\r\nEND\r\n\r\nSELECT ProductName,\r\n\tTotalPurchase=ROUND(SUM(CONVERT(decimal(14,2), OD.Quantity * (1-OD.Discount) * OD.UnitPrice)), 0)\r\nFROM [Order Details] OD, Orders O, Products P, Categories C\r\nWHERE OD.OrderID = O.OrderID \r\n\tAND OD.ProductID = P.ProductID \r\n\tAND P.CategoryID = C.CategoryID\r\n\tAND C.CategoryName = @CategoryName\r\n\tAND SUBSTRING(CONVERT(nvarchar(22), O.OrderDate, 111), 1, 4) = @OrdYear\r\nGROUP BY ProductName\r\nORDER BY ProductName"}, "sp_upgraddiagrams": {"included": false, "name": "sp_upgraddiagrams", "definition": "CREATE PROCEDURE dbo.sp_upgraddiagrams\r\n\tAS\r\n\tBEGIN\r\n\t\tIF OBJECT_ID(N'dbo.sysdiagrams') IS NOT NULL\r\n\t\t\treturn 0;\r\n\t\r\n\t\tCREATE TABLE dbo.sysdiagrams\r\n\t\t(\r\n\t\t\tname sysname NOT NULL,\r\n\t\t\tprincipal_id int NOT NULL,\t-- we may change it to varbinary(85)\r\n\t\t\tdiagram_id int PRIMARY KEY IDENTITY,\r\n\t\t\tversion int,\r\n\t\r\n\t\t\tdefinition varbinary(max)\r\n\t\t\tCONSTRAINT UK_principal_name UNIQUE\r\n\t\t\t(\r\n\t\t\t\tprincipal_id,\r\n\t\t\t\tname\r\n\t\t\t)\r\n\t\t);\r\n\r\n\r\n\t\t/* Add this if we need to have some form of extended properties for diagrams */\r\n\t\t/*\r\n\t\tIF OBJECT_ID(N'dbo.sysdiagram_properties') IS NULL\r\n\t\tBEGIN\r\n\t\t\tCREATE TABLE dbo.sysdiagram_properties\r\n\t\t\t(\r\n\t\t\t\tdiagram_id int,\r\n\t\t\t\tname sysname,\r\n\t\t\t\tvalue varbinary(max) NOT NULL\r\n\t\t\t)\r\n\t\tEND\r\n\t\t*/\r\n\r\n\t\tIF OBJECT_ID(N'dbo.dtproperties') IS NOT NULL\r\n\t\tbegin\r\n\t\t\tinsert into dbo.sysdiagrams\r\n\t\t\t(\r\n\t\t\t\t[name],\r\n\t\t\t\t[principal_id],\r\n\t\t\t\t[version],\r\n\t\t\t\t[definition]\r\n\t\t\t)\r\n\t\t\tselect\t \r\n\t\t\t\tconvert(sysname, dgnm.[uvalue]),\r\n\t\t\t\tDATABASE_PRINCIPAL_ID(N'dbo'),\t\t\t-- will change to the sid of sa\r\n\t\t\t\t0,\t\t\t\t\t\t\t-- zero for old format, dgdef.[version],\r\n\t\t\t\tdgdef.[lvalue]\r\n\t\t\tfrom dbo.[dtproperties] dgnm\r\n\t\t\t\tinner join dbo.[dtproperties] dggd on dggd.[property] = 'DtgSchemaGUID' and dggd.[objectid] = dgnm.[objectid]\t\r\n\t\t\t\tinner join dbo.[dtproperties] dgdef on dgdef.[property] = 'DtgSchemaDATA' and dgdef.[objectid] = dgnm.[objectid]\r\n\t\t\t\t\r\n\t\t\twhere dgnm.[property] = 'DtgSchemaNAME' and dggd.[uvalue] like N'_EA3E6268-D998-11CE-9454-00AA00A3F36E_' \r\n\t\t\treturn 2;\r\n\t\tend\r\n\t\treturn 1;\r\n\tEND"}, "sp_alterdiagram": {"included": false, "name": "sp_alterdiagram", "definition": "CREATE PROCEDURE dbo.sp_alterdiagram\r\n\t(\r\n\t\t@diagramname \tsysname,\r\n\t\t@owner_id\tint\t= null,\r\n\t\t@version \tint,\r\n\t\t@definition \tvarbinary(max)\r\n\t)\r\n\tWITH EXECUTE AS 'dbo'\r\n\tAS\r\n\tBEGIN\r\n\t\tset nocount on\r\n\t\r\n\t\tdeclare @theId \t\t\tint\r\n\t\tdeclare @retval \t\tint\r\n\t\tdeclare @IsDbo \t\t\tint\r\n\t\t\r\n\t\tdeclare @UIDFound \t\tint\r\n\t\tdeclare @DiagId\t\t\tint\r\n\t\tdeclare @ShouldChangeUID\tint\r\n\t\r\n\t\tif(@diagramname is null)\r\n\t\tbegin\r\n\t\t\tRAISERROR ('Invalid ARG', 16, 1)\r\n\t\t\treturn -1\r\n\t\tend\r\n\t\r\n\t\texecute as caller;\r\n\t\tselect @theId = DATABASE_PRINCIPAL_ID();\t \r\n\t\tselect @IsDbo = IS_MEMBER(N'db_owner'); \r\n\t\tif(@owner_id is null)\r\n\t\t\tselect @owner_id = @theId;\r\n\t\trevert;\r\n\t\r\n\t\tselect @ShouldChangeUID = 0\r\n\t\tselect @DiagId = diagram_id, @UIDFound = principal_id from dbo.sysdiagrams where principal_id = @owner_id and name = @diagramname \r\n\t\t\r\n\t\tif(@DiagId IS NULL or (@IsDbo = 0 and @theId <> @UIDFound))\r\n\t\tbegin\r\n\t\t\tRAISERROR ('Diagram does not exist or you do not have permission.', 16, 1);\r\n\t\t\treturn -3\r\n\t\tend\r\n\t\r\n\t\tif(@IsDbo <> 0)\r\n\t\tbegin\r\n\t\t\tif(@UIDFound is null or USER_NAME(@UIDFound) is null) -- invalid principal_id\r\n\t\t\tbegin\r\n\t\t\t\tselect @ShouldChangeUID = 1 ;\r\n\t\t\tend\r\n\t\tend\r\n\r\n\t\t-- update dds data\t\t\t\r\n\t\tupdate dbo.sysdiagrams set definition = @definition where diagram_id = @DiagId ;\r\n\r\n\t\t-- change owner\r\n\t\tif(@ShouldChangeUID = 1)\r\n\t\t\tupdate dbo.sysdiagrams set principal_id = @theId where diagram_id = @DiagId ;\r\n\r\n\t\t-- update dds version\r\n\t\tif(@version is not null)\r\n\t\t\tupdate dbo.sysdiagrams set version = @version where diagram_id = @DiagId ;\r\n\r\n\t\treturn 0\r\n\tEND"}, "Employee Sales by Country": {"included": true, "name": "Employee Sales by Country", "definition": "create procedure \"Employee Sales by Country\" \r\n@Beginning_Date DateTime, @Ending_Date DateTime AS\r\nSELECT Employees.Country, Employees.LastName, Employees.FirstName, Orders.ShippedDate, Orders.OrderID, \"Order Subtotals\".Subtotal AS SaleAmount\r\nFROM Employees INNER JOIN \r\n\t(Orders INNER JOIN \"Order Subtotals\" ON Orders.OrderID = \"Order Subtotals\".OrderID) \r\n\tON Employees.EmployeeID = Orders.EmployeeID\r\nWHERE Orders.ShippedDate Between @Beginning_Date And @Ending_Date"}, "sp_creatediagram": {"included": false, "name": "sp_creatediagram", "definition": "CREATE PROCEDURE dbo.sp_creatediagram\r\n\t(\r\n\t\t@diagramname \tsysname,\r\n\t\t@owner_id\t\tint\t= null, \t\r\n\t\t@version \t\tint,\r\n\t\t@definition \tvarbinary(max)\r\n\t)\r\n\tWITH EXECUTE AS 'dbo'\r\n\tAS\r\n\tBEGIN\r\n\t\tset nocount on\r\n\t\r\n\t\tdeclare @theId int\r\n\t\tdeclare @retval int\r\n\t\tdeclare @IsDbo\tint\r\n\t\tdeclare @userName sysname\r\n\t\tif(@version is null or @diagramname is null)\r\n\t\tbegin\r\n\t\t\tRAISERROR (N'E_INVALIDARG', 16, 1);\r\n\t\t\treturn -1\r\n\t\tend\r\n\t\r\n\t\texecute as caller;\r\n\t\tselect @theId = DATABASE_PRINCIPAL_ID(); \r\n\t\tselect @IsDbo = IS_MEMBER(N'db_owner');\r\n\t\trevert; \r\n\t\t\r\n\t\tif @owner_id is null\r\n\t\tbegin\r\n\t\t\tselect @owner_id = @theId;\r\n\t\tend\r\n\t\telse\r\n\t\tbegin\r\n\t\t\tif @theId <> @owner_id\r\n\t\t\tbegin\r\n\t\t\t\tif @IsDbo = 0\r\n\t\t\t\tbegin\r\n\t\t\t\t\tRAISERROR (N'E_INVALIDARG', 16, 1);\r\n\t\t\t\t\treturn -1\r\n\t\t\t\tend\r\n\t\t\t\tselect @theId = @owner_id\r\n\t\t\tend\r\n\t\tend\r\n\t\t-- next 2 line only for test, will be removed after define name unique\r\n\t\tif EXISTS(select diagram_id from dbo.sysdiagrams where principal_id = @theId and name = @diagramname)\r\n\t\tbegin\r\n\t\t\tRAISERROR ('The name is already used.', 16, 1);\r\n\t\t\treturn -2\r\n\t\tend\r\n\t\r\n\t\tinsert into dbo.sysdiagrams(name, principal_id , version, definition)\r\n\t\t\t\tVALUES(@diagramname, @theId, @version, @definition) ;\r\n\t\t\r\n\t\tselect @retval = @@IDENTITY \r\n\t\treturn @retval\r\n\tEND"}, "sp_helpdiagramdefinition": {"included": false, "name": "sp_helpdiagramdefinition", "definition": "CREATE PROCEDURE dbo.sp_helpdiagramdefinition\r\n\t(\r\n\t\t@diagramname \tsysname,\r\n\t\t@owner_id\tint\t= null \t\t\r\n\t)\r\n\tWITH EXECUTE AS N'dbo'\r\n\tAS\r\n\tBEGIN\r\n\t\tset nocount on\r\n\r\n\t\tdeclare @theId \t\tint\r\n\t\tdeclare @IsDbo \t\tint\r\n\t\tdeclare @DiagId\t\tint\r\n\t\tdeclare @UIDFound\tint\r\n\t\r\n\t\tif(@diagramname is null)\r\n\t\tbegin\r\n\t\t\tRAISERROR (N'E_INVALIDARG', 16, 1);\r\n\t\t\treturn -1\r\n\t\tend\r\n\t\r\n\t\texecute as caller;\r\n\t\tselect @theId = DATABASE_PRINCIPAL_ID();\r\n\t\tselect @IsDbo = IS_MEMBER(N'db_owner');\r\n\t\tif(@owner_id is null)\r\n\t\t\tselect @owner_id = @theId;\r\n\t\trevert; \r\n\t\r\n\t\tselect @DiagId = diagram_id, @UIDFound = principal_id from dbo.sysdiagrams where principal_id = @owner_id and name = @diagramname;\r\n\t\tif(@DiagId IS NULL or (@IsDbo = 0 and @UIDFound <> @theId ))\r\n\t\tbegin\r\n\t\t\tRAISERROR ('Diagram does not exist or you do not have permission.', 16, 1);\r\n\t\t\treturn -3\r\n\t\tend\r\n\r\n\t\tselect version, definition FROM dbo.sysdiagrams where diagram_id = @DiagId ; \r\n\t\treturn 0\r\n\tEND"}, "sp_helpdiagrams": {"included": false, "name": "sp_helpdiagrams", "definition": "CREATE PROCEDURE dbo.sp_helpdiagrams\r\n\t(\r\n\t\t@diagramname sysname = NULL,\r\n\t\t@owner_id int = NULL\r\n\t)\r\n\tWITH EXECUTE AS N'dbo'\r\n\tAS\r\n\tBEGIN\r\n\t\tDECLARE @user sysname\r\n\t\tDECLARE @dboLogin bit\r\n\t\tEXECUTE AS CALLER;\r\n\t\t\tSET @user = USER_NAME();\r\n\t\t\tSET @dboLogin = CONVERT(bit,IS_MEMBER('db_owner'));\r\n\t\tREVERT;\r\n\t\tSELECT\r\n\t\t\t[Database] = DB_NAME(),\r\n\t\t\t[Name] = name,\r\n\t\t\t[ID] = diagram_id,\r\n\t\t\t[Owner] = USER_NAME(principal_id),\r\n\t\t\t[OwnerID] = principal_id\r\n\t\tFROM\r\n\t\t\tsysdiagrams\r\n\t\tWHERE\r\n\t\t\t(@dboLogin = 1 OR USER_NAME(principal_id) = @user) AND\r\n\t\t\t(@diagramname IS NULL OR name = @diagramname) AND\r\n\t\t\t(@owner_id IS NULL OR principal_id = @owner_id)\r\n\t\tORDER BY\r\n\t\t\t4, 5, 1\r\n\tEND"}, "CustOrdersOrders": {"included": true, "name": "CustOrdersOrders", "definition": "CREATE PROCEDURE CustOrdersOrders @CustomerID nchar(5)\r\nAS\r\nSELECT OrderID, \r\n\tOrderDate,\r\n\tRequiredDate,\r\n\tShippedDate\r\nFROM Orders\r\nWHERE CustomerID = @CustomerID\r\nORDER BY OrderID"}, "CustOrdersDetail": {"included": true, "name": "CustOrdersDetail", "definition": "CREATE PROCEDURE CustOrdersDetail @OrderID int\r\nAS\r\nSELECT ProductName,\r\n    UnitPrice=ROUND(Od.UnitPrice, 2),\r\n    Quantity,\r\n    Discount=CONVERT(int, Discount * 100), \r\n    ExtendedPrice=ROUND(CONVERT(money, Quantity * (1 - Discount) * Od.UnitPrice), 2)\r\nFROM Products P, [Order Details] Od\r\nWHERE Od.ProductID = P.ProductID and Od.OrderID = @OrderID"}, "sp_dropdiagram": {"included": false, "name": "sp_dropdiagram", "definition": "CREATE PROCEDURE dbo.sp_dropdiagram\r\n\t(\r\n\t\t@diagramname \tsysname,\r\n\t\t@owner_id\tint\t= null\r\n\t)\r\n\tWITH EXECUTE AS 'dbo'\r\n\tAS\r\n\tBEGIN\r\n\t\tset nocount on\r\n\t\tdeclare @theId \t\t\tint\r\n\t\tdeclare @IsDbo \t\t\tint\r\n\t\t\r\n\t\tdeclare @UIDFound \t\tint\r\n\t\tdeclare @DiagId\t\t\tint\r\n\t\r\n\t\tif(@diagramname is null)\r\n\t\tbegin\r\n\t\t\tRAISERROR ('Invalid value', 16, 1);\r\n\t\t\treturn -1\r\n\t\tend\r\n\t\r\n\t\tEXECUTE AS CALLER;\r\n\t\tselect @theId = DATABASE_PRINCIPAL_ID();\r\n\t\tselect @IsDbo = IS_MEMBER(N'db_owner'); \r\n\t\tif(@owner_id is null)\r\n\t\t\tselect @owner_id = @theId;\r\n\t\tREVERT; \r\n\t\t\r\n\t\tselect @DiagId = diagram_id, @UIDFound = principal_id from dbo.sysdiagrams where principal_id = @owner_id and name = @diagramname \r\n\t\tif(@DiagId IS NULL or (@IsDbo = 0 and @UIDFound <> @theId))\r\n\t\tbegin\r\n\t\t\tRAISERROR ('Diagram does not exist or you do not have permission.', 16, 1)\r\n\t\t\treturn -3\r\n\t\tend\r\n\t\r\n\t\tdelete from dbo.sysdiagrams where diagram_id = @DiagId;\r\n\t\r\n\t\treturn 0;\r\n\tEND"}, "Sales by Year": {"included": true, "name": "Sales by Year", "definition": "create procedure \"Sales by Year\" \r\n\t@Beginning_Date DateTime, @Ending_Date DateTime AS\r\nSELECT Orders.ShippedDate, Orders.OrderID, \"Order Subtotals\".Subtotal, DATENAME(yy,ShippedDate) AS Year\r\nFROM Orders INNER JOIN \"Order Subtotals\" ON Orders.OrderID = \"Order Subtotals\".OrderID\r\nWHERE Orders.ShippedDate Between @Beginning_Date And @Ending_Date"}}, "triggers": {}}}}}, "metadata": {"databaseType": "SQL_SERVER", "databaseVersion": "15.00.4073"}}, "imported": {"Northwind": {"dbo": {"Orders": {}, "Products": {}, "Categories": {}, "Shippers": {}, "Employees": {}, "Territories": {}, "Suppliers": {}, "EmployeeTerritories": {}, "Customers": {}, "Region": {}, "Order Details": {}}}}}, "queries": [{"id": "02bdc07baa104138b828222ad4f38dce", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "Product Sales for 1997", "query": "create view \"Product Sales for 1997\" AS\r\nSELECT Categories.CategoryName, Products.ProductName, \r\nSum(CONVERT(money,(\"Order Details\".UnitPrice*Quantity*(1-Discount)/100))*100) AS ProductSales\r\nFROM (Categories INNER JOIN Products ON Categories.CategoryID = Products.CategoryID) \r\n\tINNER JOIN (Orders \r\n\t\tINNER JOIN \"Order Details\" ON Orders.OrderID = \"Order Details\".OrderID) \r\n\tON Products.ProductID = \"Order Details\".ProductID\r\nWHERE (((Orders.ShippedDate) Between '19970101' And '19971231'))\r\nGROUP BY Categories.CategoryName, Products.ProductName", "createdAt": "2024-02-27T20:14:10.148Z", "queryType": "VIEW"}, "output": {"query": "", "status": "NOT_STARTED", "lastModifiedAt": "2024-02-27T20:14:10.149Z", "language": "JAVASCRIPT"}}, {"id": "05cd3e4b3b02470281e77883aaafc8da", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "Products by Category", "query": "create view \"Products by Category\" AS\r\nSELECT Categories.CategoryName, Products.ProductName, Products.QuantityPerUnit, Products.UnitsInStock, Products.Discontinued\r\nFROM Categories INNER JOIN Products ON Categories.CategoryID = Products.CategoryID\r\nWHERE Products.Discontinued <> 1\r\n--ORDER BY Categories.CategoryName, Products.ProductName", "createdAt": "2024-02-27T20:14:10.148Z", "queryType": "VIEW"}, "output": {"query": "", "status": "NOT_STARTED", "lastModifiedAt": "2024-02-27T20:14:10.149Z", "language": "JAVASCRIPT"}}, {"id": "194f7d6c4d9d431abfad7828f9ada81c", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "Invoices", "query": "create view Invoices AS\r\nSELECT Orders.ShipName, Orders.ShipAddress, Orders.ShipCity, Orders.ShipRegion, Orders.ShipPostalCode, \r\n\tOrders.ShipCountry, Orders.CustomerID, Customers.CompanyName AS CustomerName, Customers.Address, Customers.City, \r\n\tCustomers.Region, Customers.PostalCode, Customers.Country, \r\n\t(FirstName + ' ' + LastName) AS Salesperson, \r\n\tOrders.OrderID, Orders.OrderDate, Orders.RequiredDate, Orders.ShippedDate, Shippers.CompanyName As ShipperName, \r\n\t\"Order Details\".ProductID, Products.ProductName, \"Order Details\".UnitPrice, \"Order Details\".Quantity, \r\n\t\"Order Details\".Discount, \r\n\t(CONVERT(money,(\"Order Details\".UnitPrice*Quantity*(1-Discount)/100))*100) AS ExtendedPrice, Orders.Freight\r\nFROM \tShippers INNER JOIN \r\n\t\t(Products INNER JOIN \r\n\t\t\t(\r\n\t\t\t\t(Employees INNER JOIN \r\n\t\t\t\t\t(Customers INNER JOIN Orders ON Customers.CustomerID = Orders.CustomerID) \r\n\t\t\t\tON Employees.EmployeeID = Orders.EmployeeID) \r\n\t\t\tINNER JOIN \"Order Details\" ON Orders.OrderID = \"Order Details\".OrderID) \r\n\t\tON Products.ProductID = \"Order Details\".ProductID) \r\n\tON Shippers.ShipperID = Orders.ShipVia", "createdAt": "2024-02-27T20:14:10.148Z", "queryType": "VIEW"}, "output": {"query": "", "status": "NOT_STARTED", "lastModifiedAt": "2024-02-27T20:14:10.149Z", "language": "JAVASCRIPT"}}, {"id": "1ce40069a6f24c4c8ac16478979c215a", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "CustOrdersOrders", "query": "CREATE PROCEDURE CustOrdersOrders @CustomerID nchar(5)\r\nAS\r\nSELECT OrderID, \r\n\tOrderDate,\r\n\tRequiredDate,\r\n\tShippedDate\r\nFROM Orders\r\nWHERE CustomerID = @CustomerID\r\nORDER BY OrderID", "createdAt": "2024-02-27T21:10:41.099Z", "queryType": "STORED_PROCEDURE"}, "output": {"query": "", "status": "NOT_STARTED", "lastModifiedAt": "2024-02-27T21:10:41.099Z", "language": "JAVASCRIPT"}}, {"id": "2e66119a63434922a0082abec6616e57", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "SalesByCategory", "query": "CREATE PROCEDURE SalesByCategory\r\n    @CategoryName nvarchar(15), @OrdYear nvarchar(4) = '1998'\r\nAS\r\nIF @OrdYear != '1996' AND @OrdYear != '1997' AND @OrdYear != '1998' \r\nBEGIN\r\n\tSELECT @OrdYear = '1998'\r\nEND\r\n\r\nSELECT ProductName,\r\n\tTotalPurchase=ROUND(SUM(CONVERT(decimal(14,2), OD.Quantity * (1-OD.Discount) * OD.UnitPrice)), 0)\r\nFROM [Order Details] OD, Orders O, Products P, Categories C\r\nWHERE OD.OrderID = O.OrderID \r\n\tAND OD.ProductID = P.ProductID \r\n\tAND P.CategoryID = C.CategoryID\r\n\tAND C.CategoryName = @CategoryName\r\n\tAND SUBSTRING(CONVERT(nvarchar(22), O.OrderDate, 111), 1, 4) = @OrdYear\r\nGROUP BY ProductName\r\nORDER BY ProductName", "createdAt": "2024-02-27T21:10:41.099Z", "queryType": "STORED_PROCEDURE"}, "output": {"query": "String salesByCategory(MongoDatabase db, String categoryName, String ordYear) {\n    if (!ordYear.equals(\"1996\") && !ordYear.equals(\"1997\") && !ordYear.equals(\"1998\")) {\n        ordYear = \"1998\";\n    }\n\n    MongoCollection<Document> ordersCollection = db.getCollection(\"orders\");\n\n    Bson match = Aggregates.match(Filters.and(\n            Filters.eq(\"product.category.categoryName\", categoryName),\n            Filters.eq(\"orderDate\", ordYear)\n    ));\n\n    Bson unwindOrderDetails = Aggregates.unwind(\"$orderDetails\");\n\n    Bson group = Aggregates.group(\"$product.productName\", Accumulators.sum(\"TotalPurchase\", \n            new Document(\"$multiply\", Arrays.asList(\"$orderDetails.quantity\", \"$orderDetails.unitPrice\", new Document(\"$subtract\", Arrays.asList(1, \"$orderDetails.discount\"))))));\n\n    Bson sort = Aggregates.sort(Sorts.ascending(\"_id\"));\n\n    List<Document> results = ordersCollection.aggregate(\n            Arrays.asList(match, unwindOrderDetails, group, sort)\n    ).into(new ArrayList<>());\n\n    return results.toString();\n}", "status": "COMPLETED", "lastModifiedAt": "2024-02-27T21:28:40.592Z", "language": "JAVA"}}, {"id": "3cad300b27de49f68cbf7fa359884e28", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "Products Above Average Price", "query": "create view \"Products Above Average Price\" AS\r\nSELECT Products.ProductName, Products.UnitPrice\r\nFROM Products\r\nWHERE Products.UnitPrice>(SELECT AVG(UnitPrice) From Products)\r\n--ORDER BY Products.UnitPrice DESC", "createdAt": "2024-02-27T20:14:10.148Z", "queryType": "VIEW"}, "output": {"query": "", "status": "NOT_STARTED", "lastModifiedAt": "2024-02-27T20:14:10.149Z", "language": "JAVASCRIPT"}}, {"id": "3fb5d3bcc92044a6aea518798dceb901", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "", "query": "SELECT Products.*, Categories.CategoryName\nFROM Categories INNER JOIN Products ON Categories.CategoryID = Products.CategoryID\nWHERE (((Products.Discontinued)=0))", "createdAt": "2024-02-27T20:21:26.134Z", "queryType": "USER_QUERY"}, "output": {"query": "async function query(db) {\n    return await db.collection('products').aggregate([\n        {\n            $match: {\n                'category': { $ne: null },\n                'discontinued': 0\n            }\n        },\n        {\n            $project: {\n                'category.categoryName': 1,\n                'categoryId': 1,\n                'discontinued': 1\n            }\n        }\n    ]).toArray();\n}", "status": "COMPLETED", "lastModifiedAt": "2024-03-05T21:31:11.186Z", "language": "JAVASCRIPT"}}, {"id": "669a587c6cd1444c85eed309fffe39d0", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "CustOrderHist", "query": "CREATE PROCEDURE [dbo].[CustOrderHist] @CustomerID nchar(5)\r\nAS\r\nSELECT ProductName, Total=SUM(Quantity)\r\nFROM Products P, [Order Details] OD, Orders O, Customers C\r\nWHERE C.CustomerID = @CustomerID\r\nAND C.CustomerID = O.CustomerID AND O.OrderID = OD.OrderID AND OD.ProductID = P.ProductID\r\nGROUP BY ProductName\r\nRETURN 888", "createdAt": "2024-02-27T21:10:41.099Z", "queryType": "STORED_PROCEDURE"}, "output": {"query": "async Task<List<BsonDocument>> CustOrderHist(IMongoDatabase db, string CustomerID)\n{\n    var orders = db.GetCollection<BsonDocument>(\"orders\");\n\n    var result = await orders.Aggregate()\n        .Match(new BsonDocument { { \"customerId\", CustomerID } })\n        .Unwind(\"orderDetails\")\n        .Group(new BsonDocument { { \"_id\", \"$product.productName\" }, { \"Total\", new BsonDocument(\"$sum\", \"$orderDetails.quantity\") } })\n        .ToListAsync();\n\n    return result;\n}", "status": "COMPLETED", "lastModifiedAt": "2024-03-05T04:12:06.094Z", "language": "CSHARP"}}, {"id": "678a218c1f2d4e2fa2c4619abf0ecbea", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "Summary of Sales by Year", "query": "create view \"Summary of Sales by Year\" AS\r\nSELECT Orders.ShippedDate, Orders.OrderID, \"Order Subtotals\".Subtotal\r\nFROM Orders INNER JOIN \"Order Subtotals\" ON Orders.OrderID = \"Order Subtotals\".OrderID\r\nWHERE Orders.ShippedDate IS NOT NULL\r\n--ORDER BY Orders.ShippedDate", "createdAt": "2024-02-27T20:14:10.148Z", "queryType": "VIEW"}, "output": {"query": "", "status": "NOT_STARTED", "lastModifiedAt": "2024-02-27T20:14:10.149Z", "language": "JAVASCRIPT"}}, {"id": "733ae7ee24574438a231cebac006bd88", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "Sales by Category", "query": "create view \"Sales by Category\" AS\r\nSELECT Categories.CategoryID, Categories.CategoryName, Products.ProductName, \r\n\tSum(\"Order Details Extended\".ExtendedPrice) AS ProductSales\r\nFROM \tCategories INNER JOIN \r\n\t\t(Products INNER JOIN \r\n\t\t\t(Orders INNER JOIN \"Order Details Extended\" ON Orders.OrderID = \"Order Details Extended\".OrderID) \r\n\t\tON Products.ProductID = \"Order Details Extended\".ProductID) \r\n\tON Categories.CategoryID = Products.CategoryID\r\nWHERE Orders.OrderDate BETWEEN '19970101' And '19971231'\r\nGROUP BY Categories.CategoryID, Categories.CategoryName, Products.ProductName\r\n--ORDER BY Products.ProductName", "createdAt": "2024-02-27T20:14:10.148Z", "queryType": "VIEW"}, "output": {"query": "", "status": "NOT_STARTED", "lastModifiedAt": "2024-02-27T20:14:10.149Z", "language": "JAVASCRIPT"}}, {"id": "7990bc97de2947fd8101ac07832ffd76", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "", "query": "SELECT * FROM Products WHERE ProductName = 'Hello from Tom'", "createdAt": "2024-03-05T23:37:57.331Z", "queryType": "USER_QUERY"}, "output": {"query": "async function query(db) {\n    return await db.collection('products').find({ productName: 'Hello from <PERSON>' }).toArray();\n}", "status": "COMPLETED", "lastModifiedAt": "2024-03-05T23:39:48.195Z", "language": "JAVASCRIPT"}}, {"id": "7996d30e0a854c22a9c2a6fe3eaadbeb", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "Customer and Suppliers by City", "query": "create view \"Customer and Suppliers by City\" AS\r\nSELECT City, CompanyName, ContactName, 'Customers' AS Relationship \r\nFROM Customers\r\nUNION SELECT City, CompanyName, ContactName, 'Suppliers'\r\nFROM Suppliers\r\n--ORDER BY City, CompanyName", "createdAt": "2024-02-27T20:14:10.148Z", "queryType": "VIEW"}, "output": {"query": "", "status": "NOT_STARTED", "lastModifiedAt": "2024-02-27T20:14:10.149Z", "language": "JAVASCRIPT"}}, {"id": "81f31f4e4ab84b9b807bf04ee31988c2", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "Sales by Year", "query": "create procedure \"Sales by Year\" \r\n\t@Beginning_Date DateTime, @Ending_Date DateTime AS\r\nSELECT Orders.ShippedDate, Orders.OrderID, \"Order Subtotals\".Subtotal, DATENAME(yy,ShippedDate) AS Year\r\nFROM Orders INNER JOIN \"Order Subtotals\" ON Orders.OrderID = \"Order Subtotals\".OrderID\r\nWHERE Orders.ShippedDate Between @Beginning_Date And @Ending_Date", "createdAt": "2024-02-27T21:10:41.099Z", "queryType": "STORED_PROCEDURE"}, "output": {"query": "", "status": "NOT_STARTED", "lastModifiedAt": "2024-02-27T21:10:41.099Z", "language": "JAVASCRIPT"}}, {"id": "8261e29b515a4343a96ec9f1e78d5480", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "Sales Totals by Amount", "query": "create view \"Sales Totals by Amount\" AS\r\nSELECT \"Order Subtotals\".Subtotal AS SaleAmount, Orders.OrderID, Customers.CompanyName, Orders.ShippedDate\r\nFROM \tCustomers INNER JOIN \r\n\t\t(Orders INNER JOIN \"Order Subtotals\" ON Orders.OrderID = \"Order Subtotals\".OrderID) \r\n\tON Customers.CustomerID = Orders.CustomerID\r\nWHERE (\"Order Subtotals\".Subtotal >2500) AND (Orders.ShippedDate BETWEEN '19970101' And '19971231')", "createdAt": "2024-02-27T20:14:10.148Z", "queryType": "VIEW"}, "output": {"query": "", "status": "NOT_STARTED", "lastModifiedAt": "2024-02-27T20:14:10.149Z", "language": "JAVASCRIPT"}}, {"id": "8c8c0cf7c73345a68189e28cc96d583f", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "Quarterly Orders", "query": "create view \"Quarterly Orders\" AS\r\nSELECT DISTINCT Customers.CustomerID, Customers.CompanyName, Customers.City, Customers.Country\r\nFROM Customers RIGHT JOIN Orders ON Customers.CustomerID = Orders.CustomerID\r\nWHERE Orders.OrderDate BETWEEN '19970101' And '19971231'", "createdAt": "2024-02-27T20:14:10.148Z", "queryType": "VIEW"}, "output": {"query": "", "status": "NOT_STARTED", "lastModifiedAt": "2024-02-27T20:14:10.149Z", "language": "JAVASCRIPT"}}, {"id": "8f54314f9482496cb7737d2b8c6a758e", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "Summary of Sales by Quarter", "query": "create view \"Summary of Sales by Quarter\" AS\r\nSELECT Orders.ShippedDate, Orders.OrderID, \"Order Subtotals\".Subtotal\r\nFROM Orders INNER JOIN \"Order Subtotals\" ON Orders.OrderID = \"Order Subtotals\".OrderID\r\nWHERE Orders.ShippedDate IS NOT NULL\r\n--ORDER BY Orders.ShippedDate", "createdAt": "2024-02-27T20:14:10.148Z", "queryType": "VIEW"}, "output": {"query": "", "status": "NOT_STARTED", "lastModifiedAt": "2024-02-27T20:14:10.149Z", "language": "JAVASCRIPT"}}, {"id": "a2647f79a59546e99c60ba5de9ffdf5f", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "Employee Sales by Country", "query": "create procedure \"Employee Sales by Country\" \r\n@Beginning_Date DateTime, @Ending_Date DateTime AS\r\nSELECT Employees.Country, Employees.LastName, Employees.FirstName, Orders.ShippedDate, Orders.OrderID, \"Order Subtotals\".Subtotal AS SaleAmount\r\nFROM Employees INNER JOIN \r\n\t(Orders INNER JOIN \"Order Subtotals\" ON Orders.OrderID = \"Order Subtotals\".OrderID) \r\n\tON Employees.EmployeeID = Orders.EmployeeID\r\nWHERE Orders.ShippedDate Between @Beginning_Date And @Ending_Date", "createdAt": "2024-02-27T21:10:41.099Z", "queryType": "STORED_PROCEDURE"}, "output": {"query": "", "status": "NOT_STARTED", "lastModifiedAt": "2024-02-27T21:10:41.099Z", "language": "JAVASCRIPT"}}, {"id": "af3087cc26c64436bffb21cd07601bab", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "Orders Qry", "query": "create view \"Orders Qry\" AS\r\nSELECT Orders.OrderID, Orders.CustomerID, Orders.EmployeeID, Orders.OrderDate, Orders.RequiredDate, \r\n\tOrders.ShippedDate, Orders.ShipVia, Orders.Freight, Orders.ShipName, Orders.ShipAddress, Orders.ShipCity, \r\n\tOrders.ShipRegion, Orders.ShipPostalCode, Orders.ShipCountry, \r\n\tCustomers.CompanyName, Customers.Address, Customers.City, Customers.Region, Customers.PostalCode, Customers.Country\r\nFROM Customers INNER JOIN Orders ON Customers.CustomerID = Orders.CustomerID", "createdAt": "2024-02-27T20:14:10.148Z", "queryType": "VIEW"}, "output": {"query": "", "status": "NOT_STARTED", "lastModifiedAt": "2024-02-27T20:14:10.149Z", "language": "JAVASCRIPT"}}, {"id": "af6951f50fb14ae0a783d56170c99ca1", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "Ten Most Expensive Products", "query": "create procedure \"Ten Most Expensive Products\" AS\r\nSET ROWCOUNT 10\r\nSELECT Products.ProductName AS TenMostExpensiveProducts, Products.UnitPrice\r\nFROM Products\r\nORDER BY Products.UnitPrice DESC", "createdAt": "2024-02-27T21:10:41.099Z", "queryType": "STORED_PROCEDURE"}, "output": {"query": "", "status": "NOT_STARTED", "lastModifiedAt": "2024-02-27T21:10:41.099Z", "language": "JAVASCRIPT"}}, {"id": "b69dd15482824d33819517b0f9990659", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "Category Sales for 1997", "query": "create view \"Category Sales for 1997\" AS\r\nSELECT \"Product Sales for 1997\".CategoryName, Sum(\"Product Sales for 1997\".ProductSales) AS CategorySales\r\nFROM \"Product Sales for 1997\"\r\nGROUP BY \"Product Sales for 1997\".CategoryName", "createdAt": "2024-02-27T20:14:10.148Z", "queryType": "VIEW"}, "output": {"query": "db.createCollection(\"Category Sales for 1997\", {\n  viewOn: \"Product Sales for 1997\",\n  pipeline: [\n    {\n      $group: {\n        _id: \"$CategoryName\",\n        CategorySales: { $sum: \"$ProductSales\" }\n      }\n    },\n    {\n      $project: {\n        _id: 0,\n        CategoryName: \"$_id\",\n        CategorySales: 1\n      }\n    }\n  ]\n})", "status": "COMPLETED", "lastModifiedAt": "2024-03-05T20:59:33.056Z", "language": "JAVASCRIPT"}}, {"id": "c2594df0fce74f0eaf3ab2cb9a8b5a29", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "TomsDodgy<PERSON>iew", "query": "CREATE VIEW TomsDodgyView\r\nAS SELECT * FROM Customers WHERE ContactName LIKE 'A%'", "createdAt": "2024-02-27T20:14:10.148Z", "queryType": "VIEW"}, "output": {"query": "", "status": "NOT_STARTED", "lastModifiedAt": "2024-02-27T20:14:10.149Z", "language": "JAVASCRIPT"}}, {"id": "cb2d7e62fd19427396aaabd6b5295c4d", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "Order Subtotals", "query": "create view \"Order Subtotals\" AS\r\nSELECT \"Order Details\".OrderID, Sum(CONVERT(money,(\"Order Details\".UnitPrice*Quantity*(1-Discount)/100))*100) AS Subtotal\r\nFROM \"Order Details\"\r\nGROUP BY \"Order Details\".OrderID", "createdAt": "2024-02-27T20:14:10.148Z", "queryType": "VIEW"}, "output": {"query": "", "status": "NOT_STARTED", "lastModifiedAt": "2024-02-27T20:14:10.149Z", "language": "JAVASCRIPT"}}, {"id": "d385bcf22d8b46ddaaa5b8568e212f56", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "CustOrdersDetail", "query": "CREATE PROCEDURE CustOrdersDetail @OrderID int\r\nAS\r\nSELECT ProductName,\r\n    UnitPrice=ROUND(Od.UnitPrice, 2),\r\n    Quantity,\r\n    Discount=CONVERT(int, Discount * 100), \r\n    ExtendedPrice=ROUND(CONVERT(money, Quantity * (1 - Discount) * Od.UnitPrice), 2)\r\nFROM Products P, [Order Details] Od\r\nWHERE Od.ProductID = P.ProductID and Od.OrderID = @OrderID", "createdAt": "2024-02-27T21:10:41.099Z", "queryType": "STORED_PROCEDURE"}, "output": {"query": "const CustOrdersDetail = async (db, OrderID) => {\n    return await db.collection('orders').aggregate([\n        {\n            $match: {\n                orderId: OrderID\n            }\n        },\n        {\n            $unwind: '$orderDetails'\n        },\n        {\n            $project: {\n                productName: '$product.productName',\n                unitPrice: {\n                    $round: ['$orderDetails.unitPrice', 2]\n                },\n                quantity: '$orderDetails.quantity',\n                discount: {\n                    $multiply: ['$orderDetails.discount', 100]\n                },\n                extendedPrice: {\n                    $round: [{\n                        $multiply: [\n                            '$orderDetails.quantity',\n                            {\n                                $subtract: [1, '$orderDetails.discount']\n                            },\n                            '$orderDetails.unitPrice'\n                        ]\n                    }, 2]\n                }\n            }\n        }\n    ]).toArray();\n}", "status": "COMPLETED", "lastModifiedAt": "2024-03-05T21:15:13.958Z", "language": "JAVASCRIPT"}}, {"id": "d39a0f2e412b4d688677cd46296f5764", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "Current Product List", "query": "create view \"Current Product List\" AS\r\nSELECT Product_List.ProductID, Product_List.ProductName\r\nFROM Products AS Product_List\r\nWHERE (((Product_List.Discontinued)=0))\r\n--ORDER BY Product_List.ProductName", "createdAt": "2024-02-27T20:14:10.148Z", "queryType": "VIEW"}, "output": {"query": null, "status": "FAILED", "lastModifiedAt": "2024-03-05T01:43:33.355Z", "language": "JAVASCRIPT", "errorMessage": "Unable to convert code due to unexpected reasons."}}, {"id": "dcd403cbc7e045388e410326fe93e0a6", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "Alphabetical list of products", "query": "create view \"Alphabetical list of products\" AS\r\nSELECT Products.*, Categories.CategoryName\r\nFROM Categories INNER JOIN Products ON Categories.CategoryID = Products.CategoryID\r\nWHERE (((Products.Discontinued)=0))", "createdAt": "2024-02-27T20:14:10.148Z", "queryType": "VIEW"}, "output": {"query": null, "status": "FAILED", "lastModifiedAt": "2024-03-05T20:58:30.241Z", "language": "CSHARP", "errorMessage": "Encountered unexpected error connecting to Atlas."}}, {"id": "f1dacd20cf9b4757adf370dd4ced0c12", "projectId": "decc22d65352422799c2826be99bf263", "input": {"name": "Order Details Extended", "query": "create view \"Order Details Extended\" AS\r\nSELECT \"Order Details\".OrderID, \"Order Details\".ProductID, Products.ProductName, \r\n\t\"Order Details\".UnitPrice, \"Order Details\".Quantity, \"Order Details\".Discount, \r\n\t(CONVERT(money,(\"Order Details\".UnitPrice*Quantity*(1-Discount)/100))*100) AS ExtendedPrice\r\nFROM Products INNER JOIN \"Order Details\" ON Products.ProductID = \"Order Details\".ProductID\r\n--ORDER BY \"Order Details\".OrderID", "createdAt": "2024-02-27T20:14:10.148Z", "queryType": "VIEW"}, "output": {"query": "", "status": "NOT_STARTED", "lastModifiedAt": "2024-02-27T20:14:10.149Z", "language": "JAVASCRIPT"}}]}