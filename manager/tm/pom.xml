<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.tapdata</groupId>
		<artifactId>tm-parent</artifactId>
		<version>0.0.1</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>tm</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>TM</name>
	<description>Tapdata Management</description>
	<packaging>jar</packaging>

	<properties>
		<resource.dir>src/main/resources</resource.dir>
		<maven.build.timestamp.format>yyyy-MM-dd HH:mm:ss</maven.build.timestamp.format>
		<okhttp3.version>4.8.0</okhttp3.version>
		<!-- Testing -->
		<version.junit>4.12</version.junit>
		<version.fest>1.4</version.fest>
	</properties>

	<profiles>
		<profile>
			<id>dev</id>
			<activation>
				<activeByDefault>true</activeByDefault>
				<property>
					<name>!env.GIT_COMMIT_VERSION</name>
				</property>
			</activation>
			<properties>
				<profile.active>dev</profile.active>
				<profile.dir>${resource.dir}/dev</profile.dir>
				<env.GIT_COMMIT_VERSION>${maven.build.timestamp}</env.GIT_COMMIT_VERSION>
				<env.PRODUCT>dfs</env.PRODUCT>
			</properties>
		</profile>
		<profile>
			<id>prod</id>
			<properties>
				<profile.active>prod</profile.active>
				<profile.dir>${resource.dir}/prod</profile.dir>
				<env.GIT_COMMIT_VERSION>${maven.build.timestamp}</env.GIT_COMMIT_VERSION>
				<env.PRODUCT>dfs</env.PRODUCT>
			</properties>
		</profile>
		<profile>
			<id>test</id>
			<properties>
				<profile.active>test</profile.active>
				<profile.dir>${resource.dir}/test</profile.dir>
				<env.GIT_COMMIT_VERSION>${maven.build.timestamp}</env.GIT_COMMIT_VERSION>
				<env.PRODUCT>dfs</env.PRODUCT>
			</properties>
		</profile>
		<profile>
			<id>test3</id>
			<properties>
				<profile.active>test3</profile.active>
				<profile.dir>${resource.dir}/test3</profile.dir>
				<env.GIT_COMMIT_VERSION>${maven.build.timestamp}</env.GIT_COMMIT_VERSION>
				<env.PRODUCT>dfs</env.PRODUCT>
			</properties>
		</profile>
		<profile>
			<id>cloud-dev</id>
			<properties>
				<profile.active>cloud-dev</profile.active>
				<profile.dir>${resource.dir}/cloud-dev</profile.dir>
				<env.GIT_COMMIT_VERSION>${maven.build.timestamp}</env.GIT_COMMIT_VERSION>
				<env.PRODUCT>dfs</env.PRODUCT>
			</properties>
		</profile>
		<profile>
			<id>cloud-uat</id>
			<properties>
				<profile.active>cloud-uat</profile.active>
				<profile.dir>${resource.dir}/cloud-uat</profile.dir>
				<env.GIT_COMMIT_VERSION>${maven.build.timestamp}</env.GIT_COMMIT_VERSION>
				<env.PRODUCT>dfs</env.PRODUCT>
			</properties>
		</profile>
		<profile>
			<id>cloud-test</id>
			<properties>
				<profile.active>cloud-test</profile.active>
				<profile.dir>${resource.dir}/cloud-test</profile.dir>
				<env.GIT_COMMIT_VERSION>${maven.build.timestamp}</env.GIT_COMMIT_VERSION>
				<env.PRODUCT>dfs</env.PRODUCT>
			</properties>
		</profile>
		<profile>
			<id>uat</id>
			<properties>
				<profile.active>uat</profile.active>
				<profile.dir>${resource.dir}/uat</profile.dir>
				<env.GIT_COMMIT_VERSION>${maven.build.timestamp}</env.GIT_COMMIT_VERSION>
				<env.PRODUCT>dfs</env.PRODUCT>
			</properties>
		</profile>
		<profile>
			<id>sit</id>
			<properties>
				<profile.active>sit</profile.active>
				<profile.dir>${resource.dir}/sit</profile.dir>
				<env.GIT_COMMIT_VERSION>${maven.build.timestamp}</env.GIT_COMMIT_VERSION>
				<env.PRODUCT>dfs</env.PRODUCT>
			</properties>
		</profile>
		<profile>
			<id>perf</id>
			<properties>
				<profile.active>perf</profile.active>
				<profile.dir>${resource.dir}/perf</profile.dir>
				<env.GIT_COMMIT_VERSION>${maven.build.timestamp}</env.GIT_COMMIT_VERSION>
				<env.PRODUCT>dfs</env.PRODUCT>
			</properties>
		</profile>
		<profile>
			<id>idaas</id>
			<properties>
				<profile.active>idaas</profile.active>
				<profile.dir>${resource.dir}/idaas</profile.dir>
				<env.GIT_COMMIT_VERSION>${maven.build.timestamp}</env.GIT_COMMIT_VERSION>
				<env.PRODUCT>idaas</env.PRODUCT>
			</properties>
		</profile>
<!--		<profile>-->
<!--			<id>cloud</id>-->
<!--			<dependencies>-->
<!--				<dependency>-->
<!--					<groupId>com.tapdata</groupId>-->
<!--					<artifactId>${tcm.artifactId}</artifactId>-->
<!--					<version>0.0.1</version>-->
<!--					<scope>compile</scope>-->
<!--					<exclusions>-->
<!--						<exclusion>-->
<!--							<groupId>cn.hutool</groupId>-->
<!--							<artifactId>hutool-http</artifactId>-->
<!--						</exclusion>-->
<!--						<exclusion>-->
<!--							<groupId>cn.hutool</groupId>-->
<!--							<artifactId>hutool-log</artifactId>-->
<!--						</exclusion>-->

<!--					</exclusions>-->
<!--				</dependency>-->
<!--			</dependencies>-->
<!--		</profile>-->
	</profiles>

	<dependencies>
		<dependency>
			<groupId>com.tapdata</groupId>
			<artifactId>${tm.impl.artifactId}</artifactId>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>de.flapdoodle.embed</groupId>-->
<!--			<artifactId>de.flapdoodle.embed.mongo</artifactId>-->
<!--			<scope>test</scope>-->
<!--		</dependency>-->
		<dependency>
			<groupId>com.tapdata</groupId>
			<artifactId>tm-sdk</artifactId>
			<version>1.0.4-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>io.tapdata</groupId>
			<artifactId>service-skeleton-module</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>tapdata-pdk-runner</artifactId>
                    <groupId>io.tapdata</groupId>
                </exclusion>
				<exclusion>
					<groupId>io.tapdata</groupId>
					<artifactId>modules-api</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.tapdata</groupId>
					<artifactId>websocket-server-module</artifactId>
				</exclusion>
            </exclusions>
        </dependency>
		<dependency>
			<groupId>io.tapdata</groupId>
			<artifactId>tapdata-proxy</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>tapdata-pdk-runner</artifactId>
                    <groupId>io.tapdata</groupId>
                </exclusion>
				<exclusion>
					<groupId>io.tapdata</groupId>
					<artifactId>websocket-server-module</artifactId>
				</exclusion>
            </exclusions>
        </dependency>
		<dependency>
			<groupId>io.tapdata</groupId>
			<artifactId>mongodb-storage-module</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>tapdata-pdk-runner</artifactId>
                    <groupId>io.tapdata</groupId>
                </exclusion>
				<exclusion>
					<artifactId>mongodb-driver-sync</artifactId>
					<groupId>org.mongodb</groupId>
				</exclusion>
			</exclusions>
        </dependency>
		<dependency>
			<groupId>io.tapdata</groupId>
			<artifactId>modules-api</artifactId>
		</dependency>
		<dependency>
			<groupId>io.tapdata</groupId>
			<artifactId>websocket-server-module</artifactId>
		</dependency>
		<dependency>
			<groupId>com.deepoove</groupId>
			<artifactId>poi-tl-plugin-highlight</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>io.tapdata</groupId>
			<artifactId>tapdata-common</artifactId>
		</dependency>
		<dependency>
			<groupId>io.tapdata</groupId>
			<artifactId>tapdata-pdk-api</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-mongodb</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>mongodb-driver-sync</artifactId>
					<groupId>org.mongodb</groupId>
				</exclusion>
				<exclusion>
					<artifactId>bson-record-codec</artifactId>
					<groupId>org.mongodb</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-elasticsearch</artifactId>
			<version>5.4.2</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-thymeleaf</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-websocket</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-cache</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-core</artifactId>
		</dependency>
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
		</dependency>

		<dependency>
			<groupId>org.hibernate.validator</groupId>
			<artifactId>hibernate-validator</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>1.10.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-collections4</artifactId>
		</dependency>

		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-inline</artifactId>
			<version>4.11.0</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>commons-net</groupId>
			<artifactId>commons-net</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>
		<dependency>
			<groupId>com.jcraft</groupId>
			<artifactId>jsch</artifactId>
			<version>0.1.55</version>
		</dependency>

		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-webmvc-core</artifactId>
			<version>1.8.0</version>
		</dependency>
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-ui</artifactId>
			<version>1.8.0</version>
		</dependency>

		<!-- Swagger Parser for OpenAPI Generator -->
		<dependency>
			<groupId>io.swagger.parser.v3</groupId>
			<artifactId>swagger-parser</artifactId>
			<version>2.1.16</version>
		</dependency>
		<dependency>
			<groupId>io.swagger.core.v3</groupId>
			<artifactId>swagger-core</artifactId>
			<version>2.2.15</version>
		</dependency>

		<!-- SnakeYAML with enhanced configuration -->
		<dependency>
			<groupId>org.yaml</groupId>
			<artifactId>snakeyaml</artifactId>
			<version>2.0</version>
		</dependency>

		<!-- Jackson YAML for enhanced YAML parsing -->
		<dependency>
			<groupId>com.fasterxml.jackson.dataformat</groupId>
			<artifactId>jackson-dataformat-yaml</artifactId>
		</dependency>

		<dependency>
			<groupId>net.javacrumbs.shedlock</groupId>
			<artifactId>shedlock-spring</artifactId>
			<version>4.15.1</version>
		</dependency>
		<dependency>
			<groupId>net.javacrumbs.shedlock</groupId>
			<artifactId>shedlock-provider-mongo</artifactId>
			<version>4.15.1</version>
			<exclusions>
				<exclusion>
					<artifactId>mongodb-driver-sync</artifactId>
					<groupId>org.mongodb</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-inline</artifactId>
			<version>4.11.0</version>
			<scope>test</scope>
		</dependency>

		<!--<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>-->


		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>

		<dependency>
			<groupId>com.networknt</groupId>
			<artifactId>json-schema-validator</artifactId>
			<version>1.0.58</version>
		</dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk18on</artifactId>
            <version>1.80</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
		<dependency>
			<groupId>com.sun.mail</groupId>
			<artifactId>jakarta.mail</artifactId>
			<version>2.0.1</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-core</artifactId>
			<version>5.8.36</version>
		</dependency>
		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.19.1</version>
		</dependency>
		<!--阿里云短信sdk-->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-core</artifactId>
			<version>4.5.10</version>
			<exclusions>
				<exclusion>
					<groupId>org.ini4j</groupId>
					<artifactId>ini4j</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>activation</artifactId>
					<groupId>javax.activation</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-dysmsapi</artifactId>
			<version>2.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.github.ulisesbocchio</groupId>
			<artifactId>jasypt-spring-boot-starter</artifactId>
			<version>2.1.1</version>
		</dependency>

		<!-- Spring security start -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-crypto</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-rsa</artifactId>
			<version>1.1.5</version>
			<exclusions>
				<exclusion>
					<artifactId>bcprov-jdk15on</artifactId>
					<groupId>org.bouncycastle</groupId>
				</exclusion>
                <exclusion>
                    <artifactId>bcpkix-jdk15on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
            </exclusions>
		</dependency>
		<!-- OAuth2 authorization server -->
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-oauth2-authorization-server</artifactId>
			<version>1.4.2</version>
			<exclusions>
				<exclusion>
					<artifactId>nimbus-jose-jwt</artifactId>
					<groupId>com.nimbusds</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- Spring security end -->
		<dependency>
			<groupId>com.nimbusds</groupId>
			<artifactId>nimbus-jose-jwt</artifactId>
			<version>10.0.2</version>
		</dependency>


		<dependency>
			<scope>compile</scope>
			<groupId>com.harium.graph</groupId>
			<artifactId>graph</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>net.rakugakibox.spring.boot</groupId>
			<artifactId>orika-spring-boot-starter</artifactId>
			<version>1.9.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-fileupload2-jakarta-servlet6</artifactId>
			<version>2.0.0-M4</version>
			<exclusions>
				<exclusion>
					<artifactId>commons-io</artifactId>
					<groupId>commons-io</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>io.kubernetes</groupId>
			<artifactId>client-java</artifactId>
			<version>23.0.0</version>
			<exclusions>
				<exclusion>
					<artifactId>bcpkix-jdk15on</artifactId>
					<groupId>org.bouncycastle</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-collections4</artifactId>
					<groupId>org.apache.commons</groupId>
				</exclusion>
				<exclusion>
					<artifactId>protobuf-java</artifactId>
					<groupId>com.google.protobuf</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.quartz-scheduler/quartz -->
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
			<version>2.5.0</version>
		</dependency>
		<dependency>
			<groupId>com.github.oshi</groupId>
			<artifactId>oshi-core</artifactId>
			<version>5.8.5</version>
		</dependency>

		<dependency>
			<groupId>com.lmax</groupId>
			<artifactId>disruptor</artifactId>
			<version>3.4.4</version>
		</dependency>

		<dependency>
			<groupId>com.deepoove</groupId>
			<artifactId>poi-tl</artifactId>
			<version>1.12.1</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.xmlgraphics</groupId>
					<artifactId>batik-codec</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.xmlgraphics</groupId>
					<artifactId>batik-transcoder</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>poi-ooxml</artifactId>
					<groupId>org.apache.poi</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>5.4.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>batik-codec</artifactId>
			<version>1.18</version>
		</dependency>
		<dependency>
			<groupId>org.apache.xmlgraphics</groupId>
			<artifactId>batik-transcoder</artifactId>
			<version>1.18</version>
		</dependency>


		<dependency>
			<groupId>org.awaitility</groupId>
			<artifactId>awaitility</artifactId>
			<version>2.0.0</version>
			<scope>test</scope>
		</dependency>

		<!-- Test dependencies -->
		<dependency>
			<groupId>org.easytesting</groupId>
			<artifactId>fest-assert</artifactId>
			<version>${version.fest}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit</groupId>
			<artifactId>junit-bom</artifactId>
			<version>${junit-jupiter.version}</version>
			<type>pom</type>
			<scope>import</scope>
		</dependency>
		<dependency>
			<groupId>com.github.ben-manes.caffeine</groupId>
			<artifactId>caffeine</artifactId>
		</dependency>

		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<version>${mockito.version}</version>
		</dependency>
		<dependency>
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-java</artifactId>
			<version>4.29.3</version>
		</dependency>

		<dependency>
			<groupId>com.tapdata</groupId>
			<artifactId>mcp-tap-server</artifactId>
			<version>0.0.1</version>
		</dependency>
		<!---->
	</dependencies>

	<build>

		<finalName>${project.artifactId}-${project.version}</finalName>
		<resources>
			<resource>
				<directory>${project.basedir}/${resource.dir}</directory>
				<filtering>true</filtering>
				<excludes>
					<exclude>dev/*</exclude>
					<exclude>prod/*</exclude>
					<exclude>test/*</exclude>
					<exclude>sit/*</exclude>
					<exclude>idaas/*</exclude>
					<exclude>test3/*</exclude>
					<exclude>cloud-dev/*</exclude>
					<exclude>cloud-uat/*</exclude>
					<exclude>cloud-test/*</exclude>
					<exclude>*.yaml</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>${project.basedir}/${resource.dir}</directory>
				<filtering>true</filtering>
				<includes>
					<include>template/*</include>
				</includes>
			</resource>
			<resource>
				<directory>${project.basedir}/${profile.dir}</directory>
				<filtering>true</filtering>
				<excludes>
					<exclude>Dockerfile</exclude>
					<exclude>*.yaml</exclude>
					<exclude>*.js</exclude>
				</excludes>
				<!--<targetPath>../</targetPath>-->
			</resource>
		</resources>

		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
<!--					<fork>true</fork>-->
					<classifier>exec</classifier>
					<!--<executable>true</executable>-->
					<!--<outputDirectory>${project.basedir}/../dist/${project.name}-${project.version}-${profile.active}/lib/</outputDirectory>-->
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>8</source>
					<target>8</target>
				</configuration>
			</plugin>
			<!--在这里修改版本-->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>2.4.3</version>
			</plugin>

			<!--			解决springboot不能渲染前端字体的问题-->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<configuration>
					<nonFilteredFileExtensions>
						<nonFilteredFileExtension>ttf</nonFilteredFileExtension>
						<nonFilteredFileExtension>woff</nonFilteredFileExtension>
						<nonFilteredFileExtension>woff2</nonFilteredFileExtension>
						<nonFilteredFileExtension>doc</nonFilteredFileExtension>
						<nonFilteredFileExtension>docx</nonFilteredFileExtension>
					</nonFilteredFileExtensions>
				</configuration>
			</plugin>

			<!--<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
				<configuration>
					<descriptors>
						<descriptor>${project.basedir}/assembly-static.xml</descriptor>
					</descriptors>
					<finalName>${project.build.finalName}-static</finalName>
					<outputDirectory>${project.basedir}/../dist/${project.version}/</outputDirectory>
				</configuration>
				<executions>
					<execution>
						<id>make-assembly</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
					</execution>
				</executions>
			</plugin>-->

            <!--  单测需要依赖mongodb，暂时移除 -->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-surefire-plugin</artifactId>-->
<!--                <configuration>-->
<!--                    <skipTests>true</skipTests>-->
<!--                </configuration>-->
<!--            </plugin>-->

            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
            </plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
			</plugin>

		</plugins>

		<!--<extensions>
			<extension>
				<groupId>kr.motd.maven</groupId>
				<artifactId>os-maven-plugin</artifactId>
				<version>1.4.0.Final</version>
			</extension>
		</extensions>-->
	</build>
	<distributionManagement>
		<repository>
			<!--必须与 settings.xml 的 id 一致-->
			<id>tapdata-tapdata-maven</id>
			<name>maven</name>
			<url>https://tapdata-maven.pkg.coding.net/repository/tapdata/maven/</url>
		</repository>
	</distributionManagement>
</project>
