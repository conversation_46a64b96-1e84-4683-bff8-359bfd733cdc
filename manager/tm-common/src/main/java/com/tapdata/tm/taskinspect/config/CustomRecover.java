package com.tapdata.tm.taskinspect.config;

import lombok.Getter;
import lombok.Setter;

/**
 * 任务校验-数据修复配置
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2025/1/17 18:16 Create
 */
@Getter
@Setter
public class CustomRecover implements IConfig<CustomRecover> {
    private Boolean enable;

    @Override
    public CustomRecover init(int depth) {
        setEnable(init(getEnable(), false));
        return this;
    }
}
