package io.tapdata.connector.mssql.cdc;

import java.util.List;

/**
 * CT 实例信息
 *
 * <AUTHOR> href="mailto:<EMAIL>"><PERSON><PERSON></a>
 * @version v1.0 2021/12/8 下午11:33 Create
 */
public class TableCaptureInstance {
  private String schema;
  private String tableName;
  private String instanceName;
  private List<String> indexColumns;
  private byte[] startLsn;
  private long createTime;
  private List<String> capturedColumnList;

  public String getSchema() {
    return schema;
  }

  public void setSchema(String schema) {
    this.schema = schema;
  }

  public String getTableName() {
    return tableName;
  }

  public void setTableName(String tableName) {
    this.tableName = tableName;
  }

  public String getInstanceName() {
    return instanceName;
  }

  public void setInstanceName(String instanceName) {
    this.instanceName = instanceName;
  }

  public List<String> getIndexColumns() {
    return indexColumns;
  }

  public void setIndexColumns(List<String> indexColumns) {
    this.indexColumns = indexColumns;
  }

  public byte[] getStartLsn() {
    return startLsn;
  }

  public void setStartLsn(byte[] startLsn) {
    this.startLsn = startLsn;
  }

  public long getCreateTime() {
    return createTime;
  }

  public void setCreateTime(long createTime) {
    this.createTime = createTime;
  }

  public List<String> getCapturedColumnList() {
    return capturedColumnList;
  }

  public void setCapturedColumnList(List<String> capturedColumnList) {
    this.capturedColumnList = capturedColumnList;
  }
}
