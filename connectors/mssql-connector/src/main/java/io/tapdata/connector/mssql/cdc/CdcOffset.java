package io.tapdata.connector.mssql.cdc;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class CdcOffset {
    private String currentStartLSN;
    private Map<String, Object> tablesOffset = new ConcurrentHashMap<>();
    private byte[] ddlOffset;

    public String getCurrentStartLSN() {
        return currentStartLSN;
    }

    public void setCurrentStartLSN(String currentStartLSN) {
        this.currentStartLSN = currentStartLSN;
    }

    public Map<String, Object> getTablesOffset() {
        return tablesOffset;
    }

    public void setTablesOffset(Map<String, Object> tablesOffset) {
        this.tablesOffset = tablesOffset;
    }

    public byte[] getDdlOffset() {
        return ddlOffset;
    }

    public void setDdlOffset(byte[] ddlOffset) {
        this.ddlOffset = ddlOffset;
    }
}
