package io.tapdata.connector.mssql.partition;

import io.tapdata.connector.mssql.MssqlJdbcRunner;
import io.tapdata.connector.mssql.MssqlMaker;
import io.tapdata.connector.mssql.config.MssqlConfig;
import io.tapdata.connector.mssql.partition.vo.MSSQLPartitionInfo;
import io.tapdata.connector.mssql.partition.vo.PartitionFunctionAndStageResult;
import io.tapdata.entity.error.CoreException;
import io.tapdata.entity.event.ddl.table.TapCreateTableEvent;
import io.tapdata.entity.event.ddl.table.TapDropTableEvent;
import io.tapdata.entity.logger.Log;
import io.tapdata.entity.schema.TapIndex;
import io.tapdata.entity.schema.TapIndexField;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.schema.partition.TapPartition;
import io.tapdata.entity.schema.partition.TapSubPartitionTableInfo;
import io.tapdata.kit.EmptyKit;
import io.tapdata.pdk.apis.context.TapConnectorContext;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

public class MssqlPartitionContext {
    public static final String KEY_TABLE_NAME = "table_name";
    public static final String KEY_PARTITION_SCHEMA = "partition_scheme";
    public static final String KEY_PARTITION_FUNCTION = "partition_function";

    final TapConnectorContext tapConnectorContext;
    final Log log;
    protected MssqlJdbcRunner jdbcRunner;
    protected MssqlConfig config;

    protected Set<String> uniqueBoundaries = new HashSet<>();

    public Set<String> getUniqueBoundaries() {
        return uniqueBoundaries;
    }

    public MssqlPartitionContext(TapConnectorContext tapConnectorContext) {
        this.tapConnectorContext = tapConnectorContext;
        this.log = tapConnectorContext.getLog();
    }

    public MssqlPartitionContext withJdbcRunner(MssqlJdbcRunner jdbcRunner) {
        this.jdbcRunner = jdbcRunner;
        return this;
    }

    public MssqlPartitionContext withMssqlConfig(MssqlConfig config) {
        this.config = config;
        return this;
    }

    public List<String> getPartitionTableSql(TapCreateTableEvent partitionTableEvent) {
        TapTable table = partitionTableEvent.getTable();
        String id = table.getPartitionMasterTableId();

        List<String> sqls = new ArrayList<>();
        TapPartition info = table.getPartitionInfo();
        List<MSSQLPartitionInfo> partitionInfo = MssqlMaker.getPartitionInfos(info, log);
        String fieldName = MssqlMaker.getPartitionField(table, log);
        if (null == fieldName) {
            log.warn("Unable find any partition info, can not create partition table, will create it before subpartition created.");
            return sqls;
        }

        final String fieldType = table.getNameFieldMap().get(fieldName).getDataType();
        StringJoiner joiner = new StringJoiner(",");
        partitionInfo.forEach(i -> {
            joiner.add(i.getLeftValue());
            uniqueBoundaries.add(i.getLeftValue());
        });

        final String partitionFunctionName = String.format("%s_%s", config.getPartitionFunctionSuffix(), id);
        final String partitionSchemaName = String.format("%s_%s", config.getPartitionSchemaSuffix(), id);


        sqls.add(String.format(CREATE_PARTITION_FUNCTION_SQL, partitionFunctionName, partitionFunctionName, fieldType, joiner.toString()));
        sqls.add(String.format(CREATE_PARTITION_SCHEMA_SQL, partitionSchemaName, partitionSchemaName, partitionFunctionName));
        sqls.addAll(MssqlMaker.createTable(table, config, String.format(" ON %s (%s)", partitionSchemaName, fieldName)));
        return sqls;
    }

    public List<String> getDropPartitionTableSql(TapDropTableEvent tapDropTableEvent) throws SQLException {
        String tableId = tapDropTableEvent.getTableId();
        PartitionFunctionAndStageResult result = queryPartitionFunctions(tableId);
        List<String> sql = new ArrayList<>();
        try {
            sql.add(MssqlMaker.dropTable(config.getDatabase(), config.getSchema(), tapDropTableEvent.getTableId()));
            Optional.ofNullable(result.getPartitionSchemaName()).ifPresent(partitionSchema ->
                    sql.add(String.format(DROP_PARTITION_SCHEMA_SQL, partitionSchema, partitionSchema))
            );
            Optional.ofNullable(result.getPartitionFunctionName()).ifPresent(partitionFunction ->
                    sql.add(String.format(DROP_PARTITION_FUNCTION_SQL, partitionFunction, partitionFunction))
            );
        } catch (Exception e) {
            throw new CoreException(e,
                    String.format("Append drop table sql failed: %s | Database: %s | Schema: %s | Partition Table name: %s | Partition function: %s | Partition schema: %s",
                            e.getMessage(),
                            config.getDatabase(),
                            config.getSchema(),
                            tapDropTableEvent.getTableId(),
                            result.getPartitionFunctionName(),
                            result.getPartitionSchemaName()
                    ));
        }
        return sql;
    }

    //查询分区函数
    public PartitionFunctionAndStageResult queryPartitionFunctions(String masterTableId) throws SQLException {
        PartitionFunctionAndStageResult result = PartitionFunctionAndStageResult.create()
                .tableName(masterTableId);
        jdbcRunner.query(String.format(QUERY_PARTITION_FUNCTION_STAGE_SQL, masterTableId), r -> {
            while (r.next()) {
                if (masterTableId.equals(r.getString(KEY_TABLE_NAME))) {
                    result.partitionSchemaName(r.getString(KEY_PARTITION_SCHEMA));
                    result.partitionFunctionName(r.getString(KEY_PARTITION_FUNCTION));
                    break;
                }
            }
        });
        return result;
    }

    public List<String> createTableIndexes(String schema, TapTable table, List<TapIndex> indexes) {
        List<String> sqls = new ArrayList<>();
        final boolean isPartitionTable = Objects.nonNull(table.getPartitionMasterTableId())
                && Objects.nonNull(table.getPartitionInfo());
        String partitionFieldName = null;
        String partitionFieldType = null;
        if (isPartitionTable) {
            partitionFieldName = MssqlMaker.getPartitionField(table, log);
            partitionFieldType = table.getNameFieldMap().get(partitionFieldName).getDataType();
        }
        for (TapIndex index : indexes) {
            List<TapIndexField> fieldList = index.getIndexFields();
            Set<String> indexField = fieldList.stream()
                    .map(TapIndexField::getName)
                    .collect(Collectors.toSet());
            if (isPartitionTable
                    && null != partitionFieldName
                    && null != partitionFieldType
                    && !indexField.contains(partitionFieldName)) {
                TapIndexField partitionKey = new TapIndexField();
                partitionKey.setName(partitionFieldName);
                partitionKey.setType(partitionFieldType);
                fieldList.add(partitionKey);
            }
            String sql = MssqlMaker.createTableIndex(schema, table.getId(), index);
            if (!EmptyKit.isBlank(sql)) {
                sqls.add(sql);
            }
        }
        return sqls;
    }

    public MSSQLPartitionInfo getPartitionInfo(TapPartition partitionInfo, String subTableId, Log log) {
        if (null == partitionInfo) {
            Optional.ofNullable(log).ifPresent(l -> log.warn("MSSQL connector unable to create partition, not find any partition info"));
            return null;
        }
        List<TapSubPartitionTableInfo> subPartitionTableInfo = partitionInfo.getSubPartitionTableInfo();
        if (null == subPartitionTableInfo || subPartitionTableInfo.isEmpty()) {
            Optional.ofNullable(log).ifPresent(l -> log.warn("MSSQL connector unable to create partition, not find any partition info"));
            return null;
        }
        Optional<TapSubPartitionTableInfo> subPartitionInfo = subPartitionTableInfo.stream()
                .filter(Objects::nonNull)
                .filter(info -> subTableId.equals(info.getTableName()))
                .findFirst();
        if (!subPartitionInfo.isPresent()) {
            Optional.ofNullable(log).ifPresent(l -> log.warn("MSSQL connector unable to create partition, not find partition info of sub table: {}", subTableId));
            return null;
        }
        TapSubPartitionTableInfo tapSubPartitionTableInfo = subPartitionInfo.get();
        return MssqlMaker.getOnePartitionInfo(partitionInfo, tapSubPartitionTableInfo, subTableId, log);
    }


    public static final String CREATE_PARTITION_FUNCTION_SQL = "IF NOT EXISTS (SELECT * FROM sys.partition_functions WHERE name = '%s')" +
            "CREATE PARTITION FUNCTION %s (%s) " +
            "AS RANGE RIGHT FOR VALUES (%s) ";
    public static final String CREATE_PARTITION_SCHEMA_SQL = "IF NOT EXISTS (SELECT * FROM sys.partition_schemes WHERE name = '%s')" +
            "CREATE PARTITION SCHEME %s " +
            "    AS PARTITION %s  " +
            "    ALL TO ('PRIMARY') ";
    public static final String DROP_PARTITION_FUNCTION_SQL="IF EXISTS (SELECT * FROM sys.partition_functions WHERE name = '%s') " +
            "DROP PARTITION FUNCTION %s";
    public static final String DROP_PARTITION_SCHEMA_SQL="IF EXISTS (SELECT * FROM sys.partition_schemes WHERE name = '%s') " +
            "DROP PARTITION SCHEME %s";

    public static final String QUERY_PARTITION_FUNCTION_STAGE_SQL = "SELECT   " +
            "    t.name AS " + KEY_TABLE_NAME + ",  " +
            "    ps.name AS " + KEY_PARTITION_SCHEMA + ",  " +
            "    pf.name AS " + KEY_PARTITION_FUNCTION + "  " +
            "FROM   " +
            "    sys.tables t  " +
            "    JOIN sys.indexes i ON t.object_id = i.object_id  " +
            "    JOIN sys.partition_schemes ps ON i.data_space_id = ps.data_space_id  " +
            "    JOIN sys.partition_functions pf ON ps.function_id = pf.function_id  " +
            "WHERE   " +
            "    t.name = '%s' ";
}
