package io.tapdata.connector.mssql.ddl;

import io.tapdata.common.CommonDbConfig;
import io.tapdata.common.ddl.DDLSqlGenerator;
import io.tapdata.entity.event.ddl.entity.ValueChange;
import io.tapdata.entity.event.ddl.table.TapAlterFieldAttributesEvent;
import io.tapdata.entity.event.ddl.table.TapAlterFieldNameEvent;
import io.tapdata.entity.event.ddl.table.TapDropFieldEvent;
import io.tapdata.entity.event.ddl.table.TapNewFieldEvent;
import io.tapdata.entity.logger.TapLogger;
import io.tapdata.entity.schema.TapField;
import io.tapdata.kit.EmptyKit;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class MssqlDDLSqlGenerator implements DDLSqlGenerator {

    private final static String TABLE_NAME_FORMAT = "\"%s\".\"%s\".\"%s\"";
    private final static String ALTER_TABLE_PREFIX = "alter table " + TABLE_NAME_FORMAT;
    private final static String COLUMN_NAME_FORMAT = "\"%s\"";
    private final static String FIELD_COMMENT_FORMAT = "\'MS_Description\',\'%s\',\'user\',\'%s\',\'table\',\'%s\',\'column\',\'%s\'";

    @Override
    public List<String> addColumn(CommonDbConfig config, TapNewFieldEvent tapNewFieldEvent) {
        List<String> sqls = new ArrayList<>();
        if (null == tapNewFieldEvent) {
            return null;
        }
        List<TapField> newFields = tapNewFieldEvent.getNewFields();
        if (null == newFields) {
            return null;
        }
        String database = config.getDatabase();
        String schema = config.getSchema();
        String tableId = tapNewFieldEvent.getTableId();
        if (EmptyKit.isBlank(tableId)) {
            throw new RuntimeException("Append add column ddl sql failed, table name is blank");
        }
        for (TapField newField : newFields) {
            StringBuilder addFieldSql = new StringBuilder(String.format(ALTER_TABLE_PREFIX, database, schema, tableId)).append(" add");
            String fieldName = newField.getName();
            if (EmptyKit.isNotBlank(fieldName)) {
                addFieldSql.append(" ").append(String.format(COLUMN_NAME_FORMAT, fieldName));
            } else {
                throw new RuntimeException("Append add column ddl sql failed, field name is blank");
            }
            String dataType = newField.getDataType();
            if (EmptyKit.isNotBlank(dataType)) {
                addFieldSql.append(" ").append(dataType);
            } else {
                throw new RuntimeException("Append add column ddl sql failed, data type is blank");
            }
            Boolean nullable = newField.getNullable();
            if (null != nullable) {
                if (nullable) {
                    addFieldSql.append(" null");
                } else {
                    addFieldSql.append(" not null");
                }
            }
            Object defaultValue = newField.getDefaultValue();
            if (null != defaultValue) {
                addFieldSql.append(" default '").append(defaultValue).append("'");
            }
            sqls.add(addFieldSql.toString());

            String comment = newField.getComment();
            if (EmptyKit.isNotBlank(comment)) {
                sqls.add("execute sp_addextendedproperty " + String.format(FIELD_COMMENT_FORMAT, comment, schema, tableId, fieldName));
            }

            Boolean primaryKey = newField.getPrimaryKey();
            if (null != primaryKey && primaryKey) {
                TapLogger.warn(MssqlDDLSqlGenerator.class.getSimpleName(), "Alter sqlServer table's primary key does not supported, please do it manually");
            }
        }
        return sqls;
    }

    @Override
    public List<String> alterColumnAttr(CommonDbConfig config, TapAlterFieldAttributesEvent tapAlterFieldAttributesEvent) {
        List<String> sqls = new ArrayList<>();
        if (null == tapAlterFieldAttributesEvent) {
            return null;
        }
        String database = config.getDatabase();
        String schema = config.getSchema();
        String tableId = tapAlterFieldAttributesEvent.getTableId();
        if (EmptyKit.isBlank(tableId)) {
            throw new RuntimeException("Append alter column attr ddl sql failed, table name is blank");
        }
        StringBuilder sql = new StringBuilder(String.format(ALTER_TABLE_PREFIX, database, schema, tableId)).append(" alter column");
        String fieldName = tapAlterFieldAttributesEvent.getFieldName();
        if (EmptyKit.isNotBlank(fieldName)) {
            sql.append(" ").append(String.format(COLUMN_NAME_FORMAT, fieldName));
        } else {
            throw new RuntimeException("Append alter column attr ddl sql failed, field name is blank");
        }
        ValueChange<String> dataTypeChange = tapAlterFieldAttributesEvent.getDataTypeChange();
        if (EmptyKit.isNotBlank(dataTypeChange.getAfter())) {
            sql.append(" ").append(dataTypeChange.getAfter());
        } else {
            throw new RuntimeException("Append alter column attr ddl sql failed, data type is blank");
        }
        ValueChange<Boolean> nullableChange = tapAlterFieldAttributesEvent.getNullableChange();
        if (null != nullableChange && null != nullableChange.getAfter()) {
            if (nullableChange.getAfter()) {
                sql.append(" null");
            } else {
                sql.append(" not null");
            }
        }
        sqls.add(sql.toString());
        ValueChange<String> commentChange = tapAlterFieldAttributesEvent.getCommentChange();
        if (null != commentChange && EmptyKit.isNotBlank(commentChange.getAfter())) {
            sqls.add("execute sp_updateextendedproperty " + String.format(FIELD_COMMENT_FORMAT, commentChange.getAfter(), schema, tableId, fieldName));
        }
        ValueChange<Integer> primaryChange = tapAlterFieldAttributesEvent.getPrimaryChange();
        if (null != primaryChange && null != primaryChange.getAfter() && primaryChange.getAfter() > 0) {
            TapLogger.warn(MssqlDDLSqlGenerator.class.getSimpleName(), "Alter sqlServer table's primary key does not supported, please do it manually");
        }
        return sqls;
    }

    @Override
    public List<String> alterColumnName(CommonDbConfig config, TapAlterFieldNameEvent tapAlterFieldNameEvent) {
        if (null == tapAlterFieldNameEvent) {
            return null;
        }
        String database = config.getDatabase();
        String schema = config.getSchema();
        String tableId = tapAlterFieldNameEvent.getTableId();
        if (EmptyKit.isBlank(tableId)) {
            throw new RuntimeException("Append alter column name ddl sql failed, table name is blank");
        }
        ValueChange<String> nameChange = tapAlterFieldNameEvent.getNameChange();
        if (null == nameChange) {
            throw new RuntimeException("Append alter column name ddl sql failed, change name object is null");
        }
        String before = nameChange.getBefore();
        String after = nameChange.getAfter();
        if (EmptyKit.isBlank(before)) {
            throw new RuntimeException("Append alter column name ddl sql failed, old column name is blank");
        }
        if (EmptyKit.isBlank(after)) {
            throw new RuntimeException("Append alter column name ddl sql failed, new column name is blank");
        }
        return Collections.singletonList("exec sp_rename '" + database + "." + schema + "." + tableId + ".[" + before + "]', '" + after + "', 'COLUMN'");
    }

    @Override
    public List<String> dropColumn(CommonDbConfig config, TapDropFieldEvent tapDropFieldEvent) {
        if (null == tapDropFieldEvent) {
            return null;
        }
        String database = config.getDatabase();
        String schema = config.getSchema();
        String tableId = tapDropFieldEvent.getTableId();
        if (EmptyKit.isBlank(tableId)) {
            throw new RuntimeException("Append drop column ddl sql failed, table name is blank");
        }
        String fieldName = tapDropFieldEvent.getFieldName();
        if (EmptyKit.isBlank(fieldName)) {
            throw new RuntimeException("Append drop column ddl sql failed, field name is blank");
        }
        return Collections.singletonList(String.format(ALTER_TABLE_PREFIX, database, schema, tableId) + " drop column " + String.format(COLUMN_NAME_FORMAT, fieldName));
    }
}
