package io.tapdata.connector.mssql;

import com.google.common.collect.Lists;
import io.tapdata.common.CommonDbTest;
import io.tapdata.connector.mssql.config.MssqlConfig;
import io.tapdata.constant.DbTestItem;
import io.tapdata.kit.EmptyKit;
import io.tapdata.pdk.apis.entity.ConnectionOptions;
import io.tapdata.pdk.apis.entity.TestItem;
import io.tapdata.pdk.apis.exception.testItem.TapTestCurrentTimeConsistentEx;
import io.tapdata.pdk.apis.exception.testItem.TapTestReadPrivilegeEx;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

import static io.tapdata.base.ConnectorBase.*;

/**
 * <AUTHOR>
 */
public class MssqlTest extends CommonDbTest {
    protected ConnectionOptions connectionOptions;

    public MssqlTest(MssqlConfig config, Consumer<TestItem> consumer, ConnectionOptions connectionOptions) {
        super(config, consumer);
        jdbcContext = new MssqlJdbcRunner(config);
        this.connectionOptions = connectionOptions;
    }

    @Override
    protected List<String> supportVersions() {
        return Lists.newArrayList("10.*", "11.*", "12.*", "13.*", "14.*", "15.*");
    }

    @Override
    public Boolean testReadPrivilege() {
        List<String> privileges = new ArrayList<>();
        try {
            jdbcContext.query(CHECK_DATABASE_PRIVILEGE, rs -> {
                while (rs.next()) {
                    privileges.add(rs.getString("permission_name"));
                }
            });
        } catch (Throwable e) {
            consumer.accept(new TestItem(DbTestItem.CHECK_TABLE_PRIVILEGE.getContent(), new TapTestReadPrivilegeEx(e), TestItem.RESULT_FAILED));
            return false;
        }
        if (privileges.contains("SELECT") || "dbo".equalsIgnoreCase(commonDbConfig.getSchema())) {
            consumer.accept(testItem(DbTestItem.CHECK_TABLE_PRIVILEGE.getContent(), TestItem.RESULT_SUCCESSFULLY));
            return true;
        } else {
            consumer.accept(testItem(DbTestItem.CHECK_TABLE_PRIVILEGE.getContent(), TestItem.RESULT_SUCCESSFULLY_WITH_WARN,
                    String.format(LACK_OF_PRIVILEGES)));
            return false;
        }
    }

    protected Boolean testWritePrivilege() {
        try {
            List<String> sqls = new ArrayList<>();
            String schemaPrefix = EmptyKit.isNotEmpty(commonDbConfig.getSchema()) ? ("[" + MssqlMaker.escape(commonDbConfig.getSchema(), "]") + "].") : "";
            if (jdbcContext.queryAllTables(Arrays.asList(TEST_WRITE_TABLE, TEST_WRITE_TABLE.toUpperCase())).size() > 0) {
                sqls.add(String.format(TEST_DROP_TABLE, schemaPrefix + TEST_WRITE_TABLE));
            }
            //create
            sqls.add(String.format(TEST_CREATE_TABLE, schemaPrefix + TEST_WRITE_TABLE));
            //insert
            sqls.add(String.format(TEST_WRITE_RECORD, schemaPrefix + TEST_WRITE_TABLE));
            //update
            sqls.add(String.format(TEST_UPDATE_RECORD, schemaPrefix + TEST_WRITE_TABLE));
            //delete
            sqls.add(String.format(TEST_DELETE_RECORD, schemaPrefix + TEST_WRITE_TABLE));
            //drop
            sqls.add(String.format(TEST_DROP_TABLE, schemaPrefix + TEST_WRITE_TABLE));
            jdbcContext.batchExecute(sqls);
            consumer.accept(testItem(TestItem.ITEM_WRITE, TestItem.RESULT_SUCCESSFULLY, TEST_WRITE_SUCCESS));
        } catch (Exception e) {
            consumer.accept(testItem(TestItem.ITEM_WRITE, TestItem.RESULT_FAILED, e.getMessage()));
        }
        return true;
    }

    @Override
    public Boolean testStreamRead() {
        String database = commonDbConfig.getDatabase();
        AtomicBoolean cdcPrivilege = new AtomicBoolean();
        try {
            // check if the cdc is enabled for the database
            jdbcContext.queryWithNext(String.format(CHECK_DATABASE_ENABLE_CDC, MssqlMaker.escape(database, "'")),
                    rs -> cdcPrivilege.set(rs.getBoolean("is_cdc_enabled")));
            if (!cdcPrivilege.get()) {
                consumer.accept(testItem(DbTestItem.CHECK_CDC_PRIVILEGES.getContent(), TestItem.RESULT_SUCCESSFULLY_WITH_WARN,
                        String.format(DATABASE_CDC_DISABLED, database)));
                return true;
            }

            // check if user have access privilege to the cdc tables
            jdbcContext.execute(String.format(SELECT_CDC_CHANGE_TABLES, MssqlMaker.escape(database, "]")));
            consumer.accept(testItem(TestItem.ITEM_READ_LOG, TestItem.RESULT_SUCCESSFULLY));
            return true;
        } catch (Throwable e) {
            e.printStackTrace();
        }
        consumer.accept(testItem(TestItem.ITEM_READ_LOG, TestItem.RESULT_SUCCESSFULLY_WITH_WARN,
                String.format(CDC_TABLE_NO_PRIVILEGE, database)));
        return true;
    }

    @Override
    public Boolean testTimeDifference(){
        try {
            long nowTime = jdbcContext.queryTimestamp();
            connectionOptions.setTimeDifference(getTimeDifference(nowTime));
        } catch (SQLException e) {
            consumer.accept(new TestItem(TestItem.ITEM_TIME_DETECTION, new TapTestCurrentTimeConsistentEx(e), TestItem.RESULT_SUCCESSFULLY_WITH_WARN));
        }
        return true;
    }
    @Override
    protected Boolean testDatasourceInstanceInfo() {
        buildDatasourceInstanceInfo(connectionOptions);
        return true;
    }

    private final static String CHECK_DATABASE_PRIVILEGE = "SELECT permission_name FROM fn_my_permissions(NULL, 'Database');";


    private final static String CHECK_DATABASE_ENABLE_CDC =
            "SELECT [name], database_id, is_cdc_enabled\n" +
                    "FROM sys.databases\n" +
                    "WHERE [name] = N'%s'";
    private final static String SELECT_CDC_CHANGE_TABLES = "SELECT * FROM [%s].cdc.[change_tables]";

    private final static String LACK_OF_PRIVILEGES = "Maybe some tables lack of privileges";
    private final static String DATABASE_CDC_DISABLED = "CDC is not enabled for database %s";
    private final static String CDC_TABLE_NO_PRIVILEGE = "User does not have CDC table privileges for database %s";


}
