package io.tapdata.connector.mssql;

import io.tapdata.common.ResultSetConsumer;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;

import static org.mockito.Mockito.*;
import static org.mockito.Mockito.doCallRealMethod;

public class MssqlJdbcRunnerTest {
    MssqlJdbcRunner mssqlJdbcRunner;
    @BeforeEach
    void init(){
        mssqlJdbcRunner = mock(MssqlJdbcRunner.class);
    }
    @Test
    void test_queryTimestamp() throws SQLException {
        ResultSet resultSet = mock(ResultSet.class);
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        when(resultSet.getTimestamp(1)).thenReturn(timestamp);
        doAnswer(invocation -> {
            ResultSetConsumer resultSetConsumer = invocation.getArgument(1);
            resultSetConsumer.accept(resultSet);
            return null;
        }).when(mssqlJdbcRunner).queryWithNext(any(),any());
        doCallRealMethod().when(mssqlJdbcRunner).queryTimestamp();
        Assertions.assertEquals(timestamp.getTime(),mssqlJdbcRunner.queryTimestamp());

    }
}
