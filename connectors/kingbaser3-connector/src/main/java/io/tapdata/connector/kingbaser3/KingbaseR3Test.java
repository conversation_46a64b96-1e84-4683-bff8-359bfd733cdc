package io.tapdata.connector.kingbaser3;

import io.tapdata.connector.kingbaser3.config.KingbaseR3Config;
import io.tapdata.connector.postgres.PostgresTest;
import io.tapdata.connector.postgres.config.PostgresConfig;
import io.tapdata.constant.DbTestItem;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.pdk.apis.entity.ConnectionOptions;
import io.tapdata.pdk.apis.entity.TestItem;

import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

import static io.tapdata.base.ConnectorBase.testItem;

public class KingbaseR3Test extends PostgresTest {

    public KingbaseR3Test(KingbaseR3Config kingbaseR3Config, Consumer<TestItem> consumer, ConnectionOptions connectionOptions) {
        super(kingbaseR3Config, consumer, connectionOptions);
    }

    @Override
    public KingbaseR3Test initContext() {
        jdbcContext = new KingbaseR3JdbcContext((KingbaseR3Config) commonDbConfig);
        return this;
    }

    @Override
    protected List<String> supportVersions() {
        return Collections.singletonList("8.*");
    }

    @Override
    public Boolean testStreamRead() {
        if ("pg".equals(((KingbaseR3JdbcContext) jdbcContext).getDatabaseMode())) {
            return super.testStreamRead();
        }
        try {
            List<String> testSqls = TapSimplify.list();
            String testSlotName = "test_tapdata_" + UUID.randomUUID().toString().replaceAll("-", "_");
            testSqls.add(String.format(KINGBASE_ORACLE_LOG_PLUGIN_CREATE_TEST, testSlotName, ((PostgresConfig) commonDbConfig).getLogPluginName()));
            testSqls.add(KINGBASE_ORACLE_LOG_PLUGIN_DROP_TEST);
            jdbcContext.batchExecute(testSqls);
            consumer.accept(testItem(TestItem.ITEM_READ_LOG, TestItem.RESULT_SUCCESSFULLY, "Cdc can work normally"));
            return true;
        } catch (Throwable e) {
            consumer.accept(testItem(TestItem.ITEM_READ_LOG, TestItem.RESULT_SUCCESSFULLY_WITH_WARN,
                    String.format("Test log plugin failed: {%s}, Maybe cdc events cannot work", e.getMessage())));
            return null;
        }
    }

    @Override
    protected int tableCount() throws Throwable {
        if ("pg".equals(((KingbaseR3JdbcContext) jdbcContext).getDatabaseMode())) {
            return super.tableCount();
        }
        AtomicInteger tableCount = new AtomicInteger();
        jdbcContext.queryWithNext(KINGBASE_ORACLE_TABLE_NUM, resultSet -> tableCount.set(resultSet.getInt(1)));
        return tableCount.get();
    }
    @Override
    protected Boolean testDatasourceInstanceInfo() {
        buildDatasourceInstanceInfo(connectionOptions);
        return true;
    }

    private final static String KINGBASE_ORACLE_TABLE_NUM = "SELECT COUNT(*) FROM sys_tables WHERE schemaname='%s'";
    private final static String KINGBASE_ORACLE_LOG_PLUGIN_CREATE_TEST = "SELECT sys_create_logical_replication_slot('%s','%s')";
    private final static String KINGBASE_ORACLE_LOG_PLUGIN_DROP_TEST = "select sys_drop_replication_slot(a.slot_name) " +
            "from (select * from sys_replication_slots where slot_name like 'test_tapdata_%') a;";;
}
