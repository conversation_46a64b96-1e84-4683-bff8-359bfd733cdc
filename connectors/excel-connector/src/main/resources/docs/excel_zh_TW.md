## **連接配寘幫助**

### **1.檔案資料來源前提說明**
-由於檔案資料來源的特殊性，連接配寘主要有檔案協定特有的配寘與檔案路徑。 連接無法加載資料模型，模型需要在任務節點配寘了檔案類型對應需要的參數，方可加載，且現時一個連接配寘僅對應一個模型。
-檔案資料來源的增量讀是通過檔案通配掃描，只有新增檔案或原檔案的修改才能感知，掃描週期默認為1分鐘。 刪除檔以及刪除檔內容的資料同步，是不受支持的，且每次都是將涉及檔案再次全量新增，通過更新條件欄位達到修改的目的。

### **2.支持檔案協定**
以下檔案協定路徑分隔符均使用 "/"
#### **LOCAL**
local表示本地（引擎）所在的作業系統的檔案
#### **FTP**
FTP（檔案傳輸通訊協定）可以設定檔案伺服器編碼。
#### **SFTP**
SFTP（安全加密檔案傳輸通訊協定）可以設定檔案伺服器編碼。 Linux系統默認開啟
#### **SMB**
SMB（文件共亯協定）Windows系統支援的網絡文件共亯協定，相容1.x，2.x，3.x。
- 特別留意：文件共亯訪問時，先選擇共亯目錄，隨後才是路徑的填寫。 （共亯目錄/檔案路徑）
#### **S3FS**
S3FS（遵循S3協定檔案系統）

### **3.任務節點通用參數**
#### **模型名（Model）**
任務節點選擇的檔案構建的邏輯模型名稱
#### **包含與排除通配（White&Black）**
通配只針對*模糊匹配，不支持規則運算式。 包含置空表示所有檔案，排除置空表示不排除。 掃描檔案邏輯為從包含匹配的檔案中過濾掉排除匹配的檔案。 遞迴開關表示是否遍歷子目錄。

### **4. excel檔案配寘與用法**
支持xls、xlsx格式的excel檔案解析，可以適配有合併儲存格的表單，有公式輸入的表單，但暫時不支持特大檔案。
#### **檔案密碼**
Excel檔案如果設定有密碼，可以通過該設定解密
#### **頁碼範圍**
如果為空，默認加載所有Sheet頁。 格式舉例：1,3~5,8表示第1頁，第3，4，5頁和第8頁。
#### **數據列範圍**
格式舉例：B~BA表示第B列到第BA列
#### **Excel表頭與數據行**
Excel檔案可以配寘某行作為表頭，也可以自己指定表頭（逗號分隔）。 當表頭行為0表示CSV檔案中沒有標題行，如果同時表頭為空的情况，會自動按Column1，Column2…命名。

### **5. Excel檔案資料類型支持**
- STRING
- TEXT
- DOUBLE
- BOOLEAN
- DATE