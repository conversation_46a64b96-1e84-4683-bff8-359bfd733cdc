## 連接配置幫助

### 參數說明

- `初始化數據量`：全量同步的數據總數，範圍：`0 ~ 9223372036854775807`。
- `增量間隔 + 增量間隔總數`：控制增量產生數據的頻率，表示 `增量間隔` 時間內產生 `增量間隔總數` 條數據，範圍：`0 ~ 2147483647`。
- `增量事件類型`：表示生成哪些事件類型的數據，如果三個都勾選，則先生成 `插入事件` 再生成 `更新事件` 最後生成 `刪除事件`，以這個順序循環直到結束。
- `表名 + 字段定義`：用於定義作為源時的數據模型。

### 模型

#### 字段類型

- `string[($byte)][fixed]`: 字符串
  - `$byte`: 字節長度（默認：`64`）
  - `fixed`: 如果定長字符器加上此標識（默認：非定長）
- `number[($precision,$scale)]`: 數值
  - `$precision`: 長度（範圍 `1-40`，默認 `4`）
  - `$scale`: 精度（範圍 `0-10`，默認 `1`）
- `boolean`: 布爾值
- `date`: 日期
- `array`: 數組
- `binary`: 字節
- `map`: 鍵值對
- `time`: 時間
- `datetime`: 時期+時間
- `now`: 當前時間
- `uuid`: UUID
- `serial[($begin,$step)]`: 自增
  - `$begin`: 開始位置（默認：`1`）
  - `$step`: 步長（默認：`1`）
- `rnumber[($precision)]`: 數字隨機
  - `$precision`: 長度（默認：`4`）
- `rstring[($byte)]`: 指定長度的隨機字符
  - `$byte`: 字節長度（默認：`64`）
- `rdatetime[($fraction)]`: 指定精度的日期
  - `$fraction`：時間精度（默認：`0`，範圍0-9整數）
- `rlongstring[($byte)]`: 指定長度的隨機長字符
  - `$byte`: 字節長度（默認：`1000`）
- `rlongbinary[($byte)]`: 指定長度的隨機二進制
  - `$byte`: 字節長度（默認：`1000`）

#### 字段默認值

生成數據時使用的默認值，不設置時數據為 `null`

### 說明
> 當 `mode='ConnHeartbeat'` 時，其它參數不需要配置：
> - 只作為源
> - 全量無數據
> - 有固定的數據模型：
> ```
> _tapdata_heartbeat_table=[
>   { "type": "string(64)", "pri": true, "name": "id", "def": "$connId" },
>   { "type": "now", "pri": false, "name": "ts" }
> ]
> ```
> - 固定的頻率：`1條/1000ms`
> - 只產生更新事件