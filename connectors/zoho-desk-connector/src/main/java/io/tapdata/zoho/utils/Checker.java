package io.tapdata.zoho.utils;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.util.List;
import java.util.Map;

public class Checker {
    private Checker(){}
    public static Checker create(){
        return new Checker();
    }

    public static boolean isEmpty(Object obj){
        return null == obj ? Boolean.TRUE : ( obj instanceof String ? "".equals(((String)obj).trim()) : Boolean.FALSE );
    }

    public static boolean isNotEmpty(Object obj){
        return !isEmpty(obj);
    }


    public static void main(String[] args) {
        final String start = "\n.add(field(";
        final String end = "\"))";
        JSONObject object = JSONUtil.parseObj(json);
        StringBuilder builder = new StringBuilder();
        object.forEach((key,value)->{
            builder.append(start).append("\"").append(key).append("\",\"");
            if (value instanceof String){
                try {
                    Integer.valueOf((String)value);
                    builder.append("Integer").append(end);
                }catch (Exception e){
                    try {
                        Long.valueOf((String)value);
                        builder.append("Long").append(end);
                    }catch (Exception e1){
                        try {
                            Double.valueOf((String)value);
                            builder.append("Double").append(end);
                        }catch (Exception e2){
                            builder.append("String").append(end);
                        }
                    }
                }
            }else if (value instanceof JSONObject || value instanceof Map){
                builder.append("Map").append(end);
            }else if (value instanceof JSONArray || value instanceof List){
                builder.append("JAVA_Array").append(end);
            }else if (value instanceof Boolean){
                builder.append("Boolean").append(end);
            } else {
                builder.append("Object").append(end);
            }
        });
        System.out.println(builder.toString());
    }

    public static String json = "{\n" +
            "  \"modifiedTime\": \"2022-09-25T14:39:41.000Z\",\n" +
            "  \"subCategory\": null,\n" +
            "  \"statusType\": \"Open\",\n" +
            "  \"subject\": \"测试字段\",\n" +
            "  \"dueDate\": \"2022-09-25T20:32:56.000Z\",\n" +
            "  \"departmentId\": \"10504000000165770\",\n" +
            "  \"channel\": \"Web\",\n" +
            "  \"onholdTime\": null,\n" +
            "  \"language\": \"Chinese (Simplified)\",\n" +
            "  \"source\": {\n" +
            "    \"appName\": null,\n" +
            "    \"extId\": null,\n" +
            "    \"permalink\": null,\n" +
            "    \"type\": \"SYSTEM\",\n" +
            "    \"appPhotoURL\": null\n" +
            "  },\n" +
            "  \"resolution\": null,\n" +
            "  \"sharedDepartments\": [\n" +
            "    \n" +
            "  ],\n" +
            "  \"closedTime\": null,\n" +
            "  \"approvalCount\": \"0\",\n" +
            "  \"isOverDue\": false,\n" +
            "  \"isTrashed\": false,\n" +
            "  \"createdTime\": \"2022-09-25T14:32:56.000Z\",\n" +
            "  \"id\": \"*****************\",\n" +
            "  \"isResponseOverdue\": false,\n" +
            "  \"customerResponseTime\": \"2022-09-25T14:32:56.000Z\",\n" +
            "  \"productId\": \"*****************\",\n" +
            "  \"contactId\": \"*****************\",\n" +
            "  \"threadCount\": \"1\",\n" +
            "  \"secondaryContacts\": [\n" +
            "    \n" +
            "  ],\n" +
            "  \"priority\": \"High\",\n" +
            "  \"classification\": \"Question\",\n" +
            "  \"commentCount\": \"0\",\n" +
            "  \"taskCount\": \"0\",\n" +
            "  \"accountId\": \"*****************\",\n" +
            "  \"phone\": \"***********\",\n" +
            "  \"webUrl\": \"https://desk.zoho.com.cn/support/gavinhome/ShowHomePage.do#Cases/dv/*****************\",\n" +
            "  \"isSpam\": false,\n" +
            "  \"status\": \"Open\",\n" +
            "  \"entitySkills\": [\n" +
            "    \n" +
            "  ],\n" +
            "  \"ticketNumber\": \"105\",\n" +
            "  \"sentiment\": null,\n" +
            "  \"customFields\": {\n" +
            "    \"货币 1\": \"5.11\",\n" +
            "    \"多行 1\": null,\n" +
            "    \"多行 2\": \"33333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333\",\n" +
            "    \"小数 1\": \"122222.111111111\",\n" +
            "    \"单行 1\": \"1111111111111111111111111111111111111111111111111111111111111111\",\n" +
            "    \"电话 1\": null,\n" +
            "    \"检查框 1\": \"true\",\n" +
            "    \"单选框 1\": \"选项1\",\n" +
            "    \"txt\": null,\n" +
            "    \"百分数 1\": \"0.1\",\n" +
            "    \"多选框 1\": \"选项1;3;5;4;6;7;8;9;10;11;12;13;14;15;16;17;18;19;20;21;22;23;24;25;26;27;28;29;30;31;32;33;34;35;36;37;38;39;40;41;42;43;44;45;46;47;48;;选项2;49;50;51;52;53;54;55;56;57;58;59;60;61;62;63;64;65;66;67;68;69;70;71;72;73;74;75;76;77;78;79;80;81;82;83;84;85;86;87;88;89;90;91;92;93;94;95;96;97;98;99;100;101;102\",\n" +
            "    \"电子邮件 1\": \"<EMAIL>\",\n" +
            "    \"URL 1\": \"<EMAIL>\",\n" +
            "    \"整数 2\": \"2\",\n" +
            "    \"整数 1\": null,\n" +
            "    \"日期时间 1\": \"2023-02-01T04:00:00.000Z\",\n" +
            "    \"日期 1\": \"2022-09-25\"\n" +
            "  },\n" +
            "  \"isArchived\": false,\n" +
            "  \"description\": \"<div style=\\\"font-size: 13px; font-family: Regular, Lato, Arial, Helvetica, sans-serif\\\"><div><img src=\\\"https://desk.zoho.com.cn:443/support/ImageDisplay?downloadType=uploadedFile&amp;fileName=1664115787340.png&amp;blockId=d4a8372ab523804624f1117395f07c020ad7e16f5bb4b7e7&amp;zgId=7dd0ebf87c596be4dc91a236738b3d70&amp;mode=view\\\" style=\\\"padding: 0px; max-width: 100%; box-sizing: border-box\\\" /><br /></div><div><br /></div><div><br /></div></div>\",\n" +
            "  \"timeEntryCount\": \"0\",\n" +
            "  \"channelRelatedInfo\": null,\n" +
            "  \"responseDueDate\": null,\n" +
            "  \"isDeleted\": false,\n" +
            "  \"modifiedBy\": \"10504000000083001\",\n" +
            "  \"followerCount\": \"0\",\n" +
            "  \"email\": \"<EMAIL>\",\n" +
            "  \"layoutDetails\": {\n" +
            "    \"id\": \"10504000000177057\",\n" +
            "    \"layoutName\": \"ZoHoSec\"\n" +
            "  },\n" +
            "  \"channelCode\": null,\n" +
            "  \"isFollowing\": false,\n" +
            "  \"cf\": {\n" +
            "    \"cf_duo_xing_2\": \"33333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333333\",\n" +
            "    \"cf_duo_xing_1\": null,\n" +
            "    \"cf_bai_fen_shu_1\": \"0.1\",\n" +
            "    \"cf_dian_zi_you_jian_1\": \"<EMAIL>\",\n" +
            "    \"cf_dan_xing_1\": \"1111111111111111111111111111111111111111111111111111111111111111\",\n" +
            "    \"cf_ri_qi_shi_jian_1\": \"2023-02-01T04:00:00.000Z\",\n" +
            "    \"cf_dian_hua_1\": null,\n" +
            "    \"cf_ri_qi_1\": \"2022-09-25\",\n" +
            "    \"cf_huo_bi_1\": \"5.11\",\n" +
            "    \"cf_jian_cha_kuang_1\": \"true\",\n" +
            "    \"cf_txt\": null,\n" +
            "    \"cf_dan_xuan_kuang_1\": \"选项1\",\n" +
            "    \"cf_zheng_shu_2\": \"2\",\n" +
            "    \"cf_duo_xuan_kuang_1\": \"选项1;3;5;4;6;7;8;9;10;11;12;13;14;15;16;17;18;19;20;21;22;23;24;25;26;27;28;29;30;31;32;33;34;35;36;37;38;39;40;41;42;43;44;45;46;47;48;;选项2;49;50;51;52;53;54;55;56;57;58;59;60;61;62;63;64;65;66;67;68;69;70;71;72;73;74;75;76;77;78;79;80;81;82;83;84;85;86;87;88;89;90;91;92;93;94;95;96;97;98;99;100;101;102\",\n" +
            "    \"cf_url_1\": \"<EMAIL>\",\n" +
            "    \"cf_xiao_shu_1\": \"122222.111111111\",\n" +
            "    \"cf_zheng_shu_1\": null\n" +
            "  },\n" +
            "  \"slaId\": \"10504000000173192\",\n" +
            "  \"layoutId\": \"10504000000177057\",\n" +
            "  \"assigneeId\": \"10504000000083001\",\n" +
            "  \"createdBy\": \"10504000000083001\",\n" +
            "  \"teamId\": null,\n" +
            "  \"tagCount\": \"0\",\n" +
            "  \"attachmentCount\": \"0\",\n" +
            "  \"isEscalated\": false,\n" +
            "  \"category\": null\n" +
            "}";
}
