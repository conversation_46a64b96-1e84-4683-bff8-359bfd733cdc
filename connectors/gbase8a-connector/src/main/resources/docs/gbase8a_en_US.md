## **connection configuration help**

### **1. GBase 8a database description**
Official website address: http://www.gbase8a.com/home.php <br>

GBase 8a database is an analytical database based on MySQL database, which is basically compatible with MySQL syntax, characteristics and field types <br>

Please refer to MySQL for details
### **2. Trial docker installation**
```
docker pull shihd/gbase8a:1.0
docker run -d -p5258:5258 shihd/gbase8a:1.0
```
### **3. supported versions**
At present, all versions of GBase 8a are open to the public

### **4. database particularity prompt (as target)**
- GBase 8a products are mainly used for data analysis. You can set a primary key, but the constraint does not take effect. In addition, indexes are not allowed to be created. After accessing tapdata, it can only rely on the logical primary key, and the data idempotence is not reliable.
- GBase 8a has poor support for transactions, except for multiple inserts in the same transaction.