package io.tapdata.connector.informix.dml;

import io.tapdata.common.JdbcContext;
import io.tapdata.common.RecordWriter;
import io.tapdata.entity.schema.TapTable;

import java.sql.SQLException;

public class InformixRecordWriter extends RecordWriter {

    public InformixRecordWriter(JdbcContext jdbcContext, TapTable tapTable) throws SQLException {
        super(jdbcContext, tapTable);
        insertRecorder = new InformixWriteRecorder(connection, tapTable, jdbcContext.getConfig().getSchema());
        updateRecorder = new InformixWriteRecorder(connection, tapTable, jdbcContext.getConfig().getSchema());
        deleteRecorder = new InformixWriteRecorder(connection, tapTable, jdbcContext.getConfig().getSchema());
    }

}
