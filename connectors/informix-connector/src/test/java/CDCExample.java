import com.informix.jdbcx.IfxDataSource;
import com.informix.stream.api.IfmxStreamRecord;
import com.informix.stream.cdc.IfxCDCEngine;
import com.informix.stream.cdc.records.IfxCDCOperationRecord;

public class CDCExample {
    public static void main(String[] args) throws Exception {
        String url = args.length > 0 ? args[0]
                : "jdbc:informix-sqli://192.168.1.189:9088/syscdcv1:user=informix;password=******;delimident=y";
        IfxDataSource ds = new IfxDataSource(url);
        IfxCDCEngine.Builder builder = new IfxCDCEngine.Builder(ds);
        builder.watchTable("\"syscdcv1\":\"informix\".\"aa0022\"", "\"b1\"", "\"b2\"", "\"b3\"");
        builder.timeout(5); // default 5 second timeout
//        builder.sequenceId(0);
        // Build the engine
        try (IfxCDCEngine engine = builder.build()) {

            // initialize the engine (creates the connections and begins listening for
            // changes)
            engine.init();
            IfmxStreamRecord record = null;

            // This loop is where you can inject logic that compiles
            // transactions, look for commits, throw away rollbacks
            // The data here is all Java typed, so it can be easily then
            // sent to MQTT, other JDBC drivers, streaming engines, or anything
            // else you can think of.
            while ((record = engine.getRecord()) != null) {
                // Print out the basic record information
                System.out.println(record);

                // If it is an insert/update/delete, print the column data
                if (record.hasOperationData()) {
                    System.out.println(((IfxCDCOperationRecord) record).getData());
                }
            }
        }
    }
}