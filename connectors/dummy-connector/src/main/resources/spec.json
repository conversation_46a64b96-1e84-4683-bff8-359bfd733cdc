{"properties": {"name": "Dummy", "icon": "icons/dummy.png", "doc": "${doc}", "id": "dummy", "tags": ["Database"]}, "configOptions": {"pdkExpansion": [], "capabilities": [{"id": "master_slave_merge"}, {"id": "dynamic_schema"}], "connection": {"type": "object", "properties": {"initial_totals": {"type": "number", "title": "${df_initial_totals}", "x-decorator": "FormItem", "x-component": "InputNumber", "x-index": 1, "required": true, "default": 1, "x-reactions": {"dependencies": ["__TAPDATA.connection_type"], "fulfill": {"schema": {"x-decorator-props.style.display": "{{$deps[0]!=='target' ? null:'none'}}", "required": "{{$deps[0]!=='target' ? true:false}}"}}}}, "incremental_interval": {"type": "number", "title": "${df_incremental_interval}", "x-decorator": "FormItem", "x-component": "InputNumber", "x-index": 2, "required": true, "default": 1000, "x-reactions": {"dependencies": ["__TAPDATA.connection_type"], "fulfill": {"schema": {"x-decorator-props.style.display": "{{$deps[0]!=='target' ? null:'none'}}", "required": "{{$deps[0]!=='target' ? true:false}}"}}}}, "incremental_interval_totals": {"type": "number", "title": "${df_incremental_interval_totals}", "x-decorator": "FormItem", "x-decorator-props": {"tooltip": "${df_interval_totals_tooltip}"}, "x-component": "InputNumber", "x-index": 3, "required": true, "default": 1, "x-reactions": {"dependencies": ["__TAPDATA.connection_type"], "fulfill": {"schema": {"x-decorator-props.style.display": "{{$deps[0]!=='target' ? null:'none'}}", "required": "{{$deps[0]!=='target' ? true:false}}"}}}}, "incremental_types": {"type": "array", "title": "${df_incremental_types}", "x-decorator": "FormItem", "x-component": "Checkbox.Group", "x-index": 4, "default": [1], "enum": [{"label": "${df_incremental_types_insert}", "value": 1}, {"label": "${df_incremental_types_update}", "value": 2}, {"label": "${df_incremental_types_delete}", "value": 3}], "x-reactions": {"dependencies": ["__TAPDATA.connection_type"], "fulfill": {"schema": {"x-decorator-props.style.display": "{{$deps[0]!=='target' ? null:'none'}}"}}}}, "write_interval": {"type": "number", "title": "${df_write_interval}", "x-decorator": "FormItem", "x-component": "InputNumber", "x-index": 5, "required": true, "default": 1000, "x-reactions": {"dependencies": ["__TAPDATA.connection_type"], "fulfill": {"schema": {"x-decorator-props.style.display": "{{$deps[0]!=='source' ? null:'none'}}", "required": "{{$deps[0]!=='source' ? true:false}}"}}}}, "write_interval_totals": {"type": "number", "title": "${df_write_interval_totals}", "x-decorator": "FormItem", "x-decorator-props": {"tooltip": "${df_interval_totals_tooltip}"}, "x-component": "InputNumber", "x-index": 6, "required": true, "default": 0, "x-reactions": {"dependencies": ["__TAPDATA.connection_type"], "fulfill": {"schema": {"x-decorator-props.style.display": "{{$deps[0]!=='source' ? null:'none'}}", "required": "{{$deps[0]!=='source' ? true:false}}"}}}}, "write_log": {"type": "boolean", "title": "${df_write_log}", "x-decorator": "FormItem", "x-component": "Switch", "x-index": 7, "required": true, "default": false, "x-reactions": {"dependencies": ["__TAPDATA.connection_type"], "fulfill": {"schema": {"x-decorator-props.style.display": "{{$deps[0]!=='source' ? null:'none'}}", "required": "{{$deps[0]!=='source' ? true:false}}"}}}}, "table_name": {"type": "string", "title": "${df_table_name}", "x-decorator": "FormItem", "x-component": "Input", "x-index": 8, "required": true, "default": "dummy_test", "x-reactions": {"dependencies": ["__TAPDATA.connection_type"], "fulfill": {"schema": {"x-decorator-props.style.display": "{{$deps[0]!=='target' ? null:'none'}}", "required": "{{$deps[0]!=='target' ? true:false}}"}}}}, "table_fields": {"type": "array", "title": "${df_table_fields}", "x-decorator": "FormItem", "x-component": "ArrayTable", "x-index": 9, "required": true, "default": [{"type": "uuid", "pri": true, "name": "id"}, {"type": "rstring", "pri": false, "name": "title"}, {"type": "now", "pri": false, "name": "created"}], "x-reactions": {"dependencies": ["__TAPDATA.connection_type"], "fulfill": {"schema": {"x-decorator-props.style.display": "{{$deps[0]!=='target' ? null:'none'}}", "required": "{{$deps[0]!=='target' ? true:false}}"}}}, "items": {"type": "object", "properties": {"c1": {"type": "void", "x-component": "ArrayTable.Column", "x-index": 1, "x-component-props": {"title": "${df_table_field_name}"}, "properties": {"name": {"type": "string", "required": true, "x-decorator": "FormItem", "x-component": "Input"}}}, "c2": {"type": "void", "x-component": "ArrayTable.Column", "x-index": 2, "x-component-props": {"title": "${df_table_field_type}"}, "properties": {"type": {"type": "string", "required": true, "x-decorator": "FormItem", "x-component": "Select", "default": "string", "x-component-props": {"allowCreate": true, "filterable": true}, "enum": [{"label": "string", "value": "string"}, {"label": "number", "value": "number"}, {"label": "boolean", "value": "boolean"}, {"label": "date", "value": "date"}, {"label": "array", "value": "array"}, {"label": "binary", "value": "binary"}, {"label": "map", "value": "map"}, {"label": "time", "value": "time"}, {"label": "datetime", "value": "datetime"}, {"label": "now", "value": "now"}, {"label": "uuid", "value": "uuid"}, {"label": "serial", "value": "serial"}, {"label": "rnumber", "value": "rnumber"}, {"label": "rstring", "value": "rstring"}, {"label": "rlongstring", "value": "rlongstring"}, {"label": "rlongbinary", "value": "rlongbinary"}, {"label": "rdatetime", "value": "rdatetime"}]}}}, "c4": {"type": "void", "x-component": "ArrayTable.Column", "x-index": 3, "x-component-props": {"title": "${df_table_field_default}"}, "properties": {"def": {"type": "string", "x-decorator": "Editable", "x-component": "Input"}}}, "c5": {"type": "void", "x-component": "ArrayTable.Column", "x-index": 4, "x-component-props": {"width": 60, "title": "${df_table_field_primary}"}, "properties": {"pri": {"type": "boolean", "x-decorator": "FormItem", "x-component": "Switch", "default": false}}}, "c6": {"type": "void", "x-component": "ArrayTable.Column", "x-index": 5, "x-component-props": {"width": 100, "title": "${df_table_field_operator}", "prop": "operations", "fixed": "right"}, "properties": {"operator": {"type": "void", "x-component": "FormItem", "properties": {"remove": {"type": "void", "x-component": "ArrayTable.Remove"}, "moveDown": {"type": "void", "x-component": "ArrayTable.MoveDown"}, "moveUp": {"type": "void", "x-component": "ArrayTable.MoveUp"}}}}}}}, "properties": {"add": {"type": "void", "x-component": "ArrayTable.Addition", "title": "${df_table_field_btn_add}"}}}, "batchBtn": {"type": "void", "x-index": 10, "title": " ", "x-decorator": "FormItem", "x-decorator-props": {"colon": false}, "x-component": "BatchAddField", "x-component-props": {"targetField": "table_fields"}}}}}, "messages": {"default": "en_US", "en_US": {"doc": "docs/dummy_en_US.md", "df_interval_totals_tooltip": "0 indicates no frequency limit", "df_initial_totals": "Initial data amount", "df_incremental_interval": "Incremental interval (ms)", "df_incremental_interval_totals": "Incremental Interval Upper Limits", "df_incremental_types": "incremental event types", "df_incremental_types_insert": "Insert", "df_incremental_types_update": "Update", "df_incremental_types_delete": "Delete", "df_write_interval": "Write Interval (ms)", "df_write_interval_totals": "Write Interval Upper Limit", "df_write_log": "Print write log", "df_table_name": "Table Name", "df_table_fields": "Field Definitions", "df_table_field_name": "Name", "df_table_field_type": "Type", "df_table_field_default": "Default value", "df_table_field_primary": "PK", "df_table_field_operator": "Operation", "df_table_field_btn_add": "Add field"}, "zh_CN": {"doc": "docs/dummy_zh_CN.md", "df_interval_totals_tooltip": "0 为无频率限制", "df_initial_totals": "初始化数据量", "df_incremental_interval": "增量间隔（毫秒）", "df_incremental_interval_totals": "增量间隔上限", "df_incremental_types": "增量事件类型", "df_incremental_types_insert": "插入", "df_incremental_types_update": "更新", "df_incremental_types_delete": "删除", "df_write_interval": "写入间隔(毫秒)", "df_write_interval_totals": "写入间隔上限", "df_write_log": "打印写入日志", "df_table_name": "表名", "df_table_fields": "字段定义", "df_table_field_name": "名称", "df_table_field_type": "类型", "df_table_field_default": "默认值", "df_table_field_primary": "主键", "df_table_field_operator": "操作", "df_table_field_btn_add": "增加字段"}, "zh_TW": {"doc": "docs/dummy_zh_TW.md", "df_interval_totals_tooltip": "0 为无频率限制", "df_initial_totals": "初始化數據量", "df_incremental_interval": "增量間隔（毫秒）", "df_incremental_interval_totals": "增量間隔上限", "df_incremental_types": "增量事件類型", "df_incremental_types_insert": "插入", "df_incremental_types_update": "更新", "df_incremental_types_delete": "刪除", "df_write_interval": "寫入間隔(毫秒)", "df_write_interval_totals": "寫入間隔上限", "df_write_log": "打印寫入日誌", "df_table_name": "表名", "df_table_fields": "字段定義", "df_table_field_name": "名稱", "df_table_field_type": "類型", "df_table_field_default": "默認值", "df_table_field_primary": "主鍵", "df_table_field_operator": "操作", "df_table_field_btn_add": "增加字段"}}, "dataTypes": {"string[($byte)]": {"to": "TapString", "byte": "16m", "defaultByte": 64, "preferByte": 100, "byteRatio": 3}, "number[($precision,$scale)]": {"to": "TapNumber", "precision": [1, 40], "defaultPrecision": 4, "scale": [0, 10], "defaultScale": 1}, "boolean": {"to": "TapBoolean"}, "array": {"to": "TapArray", "pkEnablement": false}, "binary": {"to": "TapBinary", "pkEnablement": false}, "map": {"to": "TapMap", "pkEnablement": false}, "time": {"to": "TapTime", "range": ["-838:59:59", "838:59:59"]}, "date": {"to": "TapDate", "range": ["1000-01-01", "9999-12-31"], "pattern": "yyyy-MM-dd"}, "datetime[($fraction)]": {"to": "TapDateTime", "range": ["1000-01-01 00:00:00.000000", "9999-12-31 23:59:59.999999"], "pattern": "yyyy-MM-dd HH:mm:ss.SSSSSS", "fraction": [0, 6], "defaultFraction": 0}, "timestamp[($fraction)]": {"to": "TapDateTime", "range": ["1970-01-01 00:00:01.000000", "2038-01-19 03:14:07.999999"], "pattern": "yyyy-MM-dd HH:mm:ss.SSSSSS", "fraction": [0, 6], "defaultFraction": 0, "withTimeZone": true}, "now": {"to": "TapDateTime", "range": ["1970-01-01 00:00:01.000000", "2038-01-19 03:14:07.999999"], "pattern": "yyyy-MM-dd HH:mm:ss.SSSSSS", "fraction": [0, 6], "defaultFraction": 6, "withTimeZone": true, "queryOnly": true}, "uuid": {"to": "TapString", "byte": 36, "preferByte": 36, "byteRatio": 3, "queryOnly": true}, "serial[($begin,$step)]": {"to": "TapNumber", "min": [1, 40], "queryOnly": true}, "rnumber[($precision)]": {"to": "TapNumber", "precision": [1, 40], "defaultPrecision": 4, "queryOnly": true}, "rstring[($byte)]": {"to": "TapString", "byte": "16m", "defaultByte": 8, "preferByte": 8, "byteRatio": 3, "queryOnly": true}, "rlongstring[($byte)]": {"to": "TapString", "byte": "2g", "queryOnly": true}, "rlongbinary[($byte)]": {"to": "TapBinary", "byte": "2g", "queryOnly": true}, "rdatetime[($fraction)]": {"to": "TapDateTime", "range": ["1000-01-01 00:00:00.000000", "9999-12-31 23:59:59.999999"], "pattern": "yyyy-MM-dd HH:mm:ss.SSSSSS", "fraction": [0, 6], "defaultFraction": 0, "queryOnly": true}}}