
import java.util.regex.*;
import java.util.regex.Matcher;

public class Main {
    public static void main(String[] args) {
        String str = "\"CLAIM_ID\"[character varying]:'IN_21' \"POLICY_ID\"[character varying]:'<PERSON>G<PERSON><PERSON>GGG' \"CLAIM_DATE\"[timestamp without time zone]:'2022-05-10 00:00:00' \"SETTLED_DATE\"[timestamp without time zone]:'2022-05-10 00:00:00' \"CLAIM_AMOUNT\"[numeric]:1077.00 \"SETTLED_AMOUNT\"[numeric]:1077.00 \"CLAIM_REASON\"[character varying]:'JARAD' \"LAST_CHANGE\"[timestamp without time zone]:'2022-05-10 00:00:00.11' ";
        String str1 = "\"CLAIM_ID\"[character varying]:'IN_21' ";

        //        String[] strArr = str.trim().split("\\s(?=([^\\\']*\\\'[^\\\']*\\\')*[^\\\']*$)",-1);
        Pattern regex = Pattern.compile("\"CLAIM_ID\"\\[character\\svarying\\]:(.*) ");
//        System.out.println(regex.matches( "^(.+)\\[(.+)\\]:(.+)\\s$", str1));
        Matcher mmatcher = regex.matcher(str1);
        while (mmatcher.find()) {
            System.out.println(mmatcher.group(1));
        }
    }
}
