{"properties": {"name": "Sybase", "icon": "icons/sybase.png", "id": "sybase", "doc": "${doc}", "tags": ["Database", "disable<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "configOptions": {"capabilities": [{"id": "dml_insert_policy", "alternatives": ["update_on_exists", "ignore_on_exists"]}, {"id": "dml_update_policy", "alternatives": ["ignore_on_nonexists", "insert_on_nonexists"]}, {"id": "api_server_supported"}], "supportDDL": {"events": ["clear_table_event"]}, "connection": {"type": "object", "properties": {"host": {"type": "string", "title": "${host}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_host", "x-index": 10, "required": true}, "port": {"type": "string", "title": "${port}", "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "database_port", "x-index": 20, "required": true}, "database": {"type": "string", "title": "${database}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_name", "x-index": 30, "required": true}, "schema": {"type": "string", "title": "${schema}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_name", "x-index": 35, "required": true}, "username": {"type": "string", "title": "${username}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_username", "x-index": 40, "required": true}, "password": {"type": "string", "title": "${password}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "database_password", "x-index": 50}, "endian": {"type": "string", "title": "${endian}", "default": 0, "x-decorator": "FormItem", "x-component": "Select", "x-decorator-props": {"tooltip": "${endian_tip}"}, "x-index": 52, "enum": [{"label": "<PERSON> Endian", "value": 0}, {"label": "<PERSON>", "value": 1}]}, "logCdcQuery": {"type": "string", "title": "${log_cdc_query}", "default": 1, "x-decorator": "FormItem", "x-component": "Select", "x-decorator-props": {"tooltip": "${log_cdc_query_tip}"}, "x-index": 60, "enum": [{"label": "Read Log", "value": 0}, {"label": "Query Source", "value": 1}]}, "addtionalString": {"type": "string", "title": "${addtionalString}", "x-decorator": "FormItem", "x-component": "Input", "default": "", "apiServerKey": "addtionalString", "x-index": 80}, "timezone": {"type": "string", "title": "${timezone}", "default": "", "x-decorator": "FormItem", "x-component": "Select", "x-index": 90, "x-display": "hidden", "enum": [{"label": "", "value": ""}, {"label": "UTC -11", "value": "-11:00"}, {"label": "UTC -10", "value": "-10:00"}, {"label": "UTC -09", "value": "-09:00"}, {"label": "UTC -08", "value": "-08:00"}, {"label": "UTC -07", "value": "-07:00"}, {"label": "UTC -06", "value": "-06:00"}, {"label": "UTC -05", "value": "-05:00"}, {"label": "UTC -04", "value": "-04:00"}, {"label": "UTC -03", "value": "-03:00"}, {"label": "UTC -02", "value": "-02:00"}, {"label": "UTC -01", "value": "-01:00"}, {"label": "UTC", "value": "+00:00"}, {"label": "UTC +01", "value": "+01:00"}, {"label": "UTC +02", "value": "+02:00"}, {"label": "UTC +03", "value": "+03:00"}, {"label": "UTC +04", "value": "+04:00"}, {"label": "UTC +05", "value": "+05:00"}, {"label": "UTC +06", "value": "+06:00"}, {"label": "UTC +07", "value": "+07:00"}, {"label": "UTC +08", "value": "+08:00"}, {"label": "UTC +09", "value": "+09:00"}, {"label": "UTC +10", "value": "+10:00"}, {"label": "UTC +11", "value": "+11:00"}, {"label": "UTC +12", "value": "+12:00"}, {"label": "UTC +13", "value": "+13:00"}, {"label": "UTC +14", "value": "+14:00"}]}, "checkTableInConnectionTest": {"type": "boolean", "title": "${checkTableInConnectionTest}", "x-decorator": "FormItem", "x-component": "Switch", "default": false, "x-display": "hidden", "x-index": 100}}}, "node": {"type": "object", "properties": {"logCdcQuery": {"type": "string", "title": "${log_cdc_query}", "default": 1, "x-decorator": "FormItem", "x-component": "Select", "x-decorator-props": {"tooltip": "${log_cdc_query_tip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}], "x-index": 7, "enum": [{"label": "Read Log", "value": 0}, {"label": "Query Source", "value": 1}]}, "logCdcQueryBatchSize": {"type": "string", "title": "${log_cdc_query_batch_size}", "x-decorator": "FormItem", "x-component": "InputNumber", "default": 1000, "x-index": 8, "x-display": "hidden", "required": true, "x-reactions": {"dependencies": ["nodeConfig.logCdcQuery"], "fulfill": {"state": {"display": "{{$deps[0] === 1 ? \"visible\" : \"hidden\"}}"}, "schema": {"required": "{{$deps[0] === 1 ? true : false}}"}}}, "x-decorator-props": {"tooltip": "${log_cdc_query_batch_size_tip}"}}, "logCdcQueryBatchDelay": {"type": "string", "title": "${log_cdc_query_batch_delay}", "x-decorator": "FormItem", "x-component": "InputNumber", "default": 5, "x-index": 9, "x-display": "hidden", "required": true, "x-reactions": {"dependencies": ["nodeConfig.logCdcQuery"], "fulfill": {"state": {"display": "{{$deps[0] === 1 ? \"visible\" : \"hidden\"}}"}, "schema": {"required": "{{$deps[0] === 1 ? true : false}}"}}}, "x-decorator-props": {"tooltip": "${log_cdc_query_batch_delay_tip}"}}, "autoEncode": {"type": "boolean", "title": "${auto_encode}", "x-decorator": "FormItem", "x-component": "Switch", "default": true, "x-index": 11, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"visible": "{{!$deps[0].length}}"}}}]}, "targetAutoEncode": {"type": "boolean", "title": "${targetAutoEncode}", "x-decorator": "FormItem", "x-component": "Switch", "default": true, "x-index": 12, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"visible": "{{$deps[0].length}}"}}}]}, "encode": {"type": "string", "title": "${encode_name}", "x-decorator": "FormItem", "x-component": "Input", "default": "cp850", "x-index": 20, "x-reactions": {"dependencies": ["nodeConfig.autoEncode"], "fulfill": {"state": {"display": "{{$deps[0] ? \"visible\" : \"hidden\"}}"}, "schema": {"required": "{{$deps[0] ? true : false}}"}}}}, "targetEncode": {"type": "string", "title": "${targetEncodeName}", "x-decorator": "FormItem", "x-component": "Input", "default": "big5-ha", "x-index": 20, "x-reactions": {"dependencies": ["nodeConfig.targetAutoEncode"], "fulfill": {"state": {"display": "{{$deps[0] ? \"visible\" : \"hidden\"}}"}, "schema": {"required": "{{$deps[0] ? true : false}}"}}}}, "decode": {"type": "string", "title": "${decode_name}", "x-decorator": "FormItem", "x-component": "Input", "default": "big5-ha", "x-display": "hidden", "x-index": 30, "x-reactions": {"dependencies": ["nodeConfig.autoEncode"], "fulfill": {"state": {"display": "{{$deps[0] ? \"visible\" : \"hidden\"}}"}, "schema": {"required": "{{$deps[0] ? true : false}}"}}}}, "targetDecode": {"type": "string", "title": "${targetDecodeName}", "x-decorator": "FormItem", "x-component": "Input", "default": "cp850", "x-index": 31, "x-reactions": {"dependencies": ["nodeConfig.targetAutoEncode"], "fulfill": {"state": {"display": "{{$deps[0] ? \"visible\" : \"hidden\"}}"}, "schema": {"required": "{{$deps[0] ? true : false}}"}}}}, "outDecode": {"type": "string", "title": "${out_decode_name}", "x-decorator": "FormItem", "x-component": "Input", "default": "utf-8", "x-display": "hidden", "x-index": 40, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "cdcCacheTime": {"type": "string", "title": "${cdcCacheTime}", "x-decorator": "FormItem", "x-component": "InputNumber", "default": 10, "x-index": 90, "x-display": "hidden", "required": true, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "debugLog": {"type": "string", "title": "${debugLog}", "default": 0, "x-decorator": "FormItem", "x-component": "Select", "x-index": 50, "enum": [{"label": "normal", "value": 0}, {"label": "debug", "value": 1}], "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "cdcPlugin": {"type": "string", "title": "${cdcPlugin}", "default": 0, "x-decorator": "FormItem", "x-component": "Select", "x-index": 60, "enum": [{"label": "auto", "value": 0}, {"label": "manual", "value": 1}], "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "autoMinerNormalSleepMs": {"type": "Integer", "title": "${autoMinerNormalSleepMs}", "x-decorator": "FormItem", "x-component": "InputNumber", "default": 10000, "x-index": 51, "required": true, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "autoMinerBrokenSleepMs": {"type": "Integer", "title": "${autoMinerBrokenSleepMs}", "x-decorator": "FormItem", "x-component": "InputNumber", "default": 100, "x-index": 52, "required": true, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "autoMinerRescanBatch": {"type": "Integer", "title": "${autoMinerRescanBatch}", "x-decorator": "FormItem", "x-component": "InputNumber", "default": 99999, "x-index": 53, "required": true, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "manualMinerScanBatch": {"type": "Integer", "title": "${manualMinerScanBatch}", "x-decorator": "FormItem", "x-component": "InputNumber", "default": 999, "x-index": 54, "required": true, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "manualMinerScanTimeoutMs": {"type": "Integer", "title": "${manualMinerScanTimeoutMs}", "x-decorator": "FormItem", "x-component": "InputNumber", "default": 25000, "x-index": 55, "required": true, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "dumpLogTimeS": {"type": "Integer", "title": "${dumpLogTimeS}", "x-decorator": "FormItem", "x-component": "InputNumber", "default": 900, "x-index": 56, "required": true, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "enableLogSpaceMonitor": {"type": "boolean", "title": "${enableLogSpaceMonitor}", "default": true, "x-index": 57, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${enableLogSpaceMonitorTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{!$deps[0].length ? \"visible\":\"hidden\"}}"}}}]}, "logSpaceWarningThreshold": {"type": "Integer", "title": "${logSpaceWarningThreshold}", "x-decorator": "FormItem", "x-component": "InputNumber", "default": 70, "minimum": 1, "maximum": 100, "x-index": 58, "x-decorator-props": {"tooltip": "${logSpaceWarningThresholdTooltip}"}, "x-reactions": [{"dependencies": ["$inputs", "enableLogSpaceMonitor"], "fulfill": {"state": {"display": "{{!$deps[0].length || !$deps[1] ? \"hidden\":\"visible\"}}"}}}]}, "logSpaceCriticalThreshold": {"type": "Integer", "title": "${logSpaceCriticalThreshold}", "x-decorator": "FormItem", "x-component": "InputNumber", "default": 85, "minimum": 1, "maximum": 100, "x-index": 59, "x-decorator-props": {"tooltip": "${logSpaceCriticalThresholdTooltip}"}, "x-reactions": [{"dependencies": ["$inputs", "enableLogSpaceMonitor"], "fulfill": {"state": {"display": "{{!$deps[0].length || !$deps[1] ? \"hidden\":\"visible\"}}"}}}]}, "logSpaceEmergencyThreshold": {"type": "Integer", "title": "${logSpaceEmergencyThreshold}", "x-decorator": "FormItem", "x-component": "InputNumber", "default": 95, "minimum": 1, "maximum": 100, "x-index": 60, "x-decorator-props": {"tooltip": "${logSpaceEmergencyThresholdTooltip}"}, "x-reactions": [{"dependencies": ["$inputs", "enableLogSpaceMonitor"], "fulfill": {"state": {"display": "{{!$deps[0].length || !$deps[1] ? \"hidden\":\"visible\"}}"}}}]}, "logSpaceCheckIntervalMs": {"type": "Integer", "title": "${logSpaceCheckIntervalMs}", "x-decorator": "FormItem", "x-component": "InputNumber", "default": 30000, "minimum": 5000, "maximum": 300000, "x-index": 61, "x-decorator-props": {"tooltip": "${logSpaceCheckIntervalMsTooltip}"}, "x-reactions": [{"dependencies": ["$inputs", "enableLogSpaceMonitor"], "fulfill": {"state": {"display": "{{!$deps[0].length || !$deps[1] ? \"hidden\":\"visible\"}}"}}}]}, "createAutoInc": {"type": "boolean", "title": "${createAutoInc}", "default": false, "x-index": 5, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${createAutoIncTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{$deps[0].length > 0 ? \"visible\":\"hidden\"}}"}}}]}, "applyDefault": {"type": "boolean", "title": "${applyDefault}", "default": false, "x-index": 6, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${applyDefaultTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{$deps[0].length > 0 ? \"visible\":\"hidden\"}}"}}}]}, "applyForeignKey": {"type": "boolean", "title": "${applyForeignKey}", "default": false, "x-index": 8, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${applyForeignKeyTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{$deps[0].length > 0 ? \"visible\":\"hidden\"}}"}}}]}, "foreignKeyConstrain": {"title": "${foreignKeyConstrain}", "type": "void", "x-decorator": "FormItem", "x-decorator-props": {"layout": "horizontal", "tooltip": "${foreignKeyConstrainTooltip}"}, "x-content": "${foreignKeyConstrain}", "x-component": "<PERSON><PERSON>", "x-component-props": {"onClick": "{{downloadForeignKeyConstraint}}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{$deps[0].length > 0 ? \"visible\":\"hidden\"}}"}}}]}}}}, "messages": {"default": "en_US", "en_US": {"host": "Host", "port": "Port", "database": "database", "username": "username", "password": "password", "addtionalString": "Connection Parameter String", "timezone": "timezone", "doc": "docs/sybase_en_US.md", "close_delay_time": "Default delay shutdown time (in seconds)", "encode_name": "Encode Character Set ", "decode_name": "Decode Character Set ", "out_decode_name": "Output Text Character Set", "schema": "<PERSON><PERSON><PERSON>", "fetch_interval": "CDC delay time in seconds", "auto_encode": "Automatic encoding and decoding ", "targetAutoEncode": "Target write codec", "targetEncodeName": "Encoding format", "targetDecodeName": "Decoding format", "cdcCacheTime": "CDC incremental file cache time (in minutes)", "checkTableInConnectionTest": "Open table check", "heartbeat": "Open CDC Heartbeat Monitor", "table_sql": "Heartbeat Table Creation SQL", "heartbeat_tip": "After enabling CDC heartbeat monitoring, a heartbeat table will be need created on the source side by yourself(replicate_io_cdc_heartbeat)", "heart_database": " The database where the heartbeat table is located ", "heart_schema": " The schema where the heartbeat table is located ", "log_cdc_query": "Log CDC Query", "log_cdc_query_tip": "Query log by read log or query source", "log_cdc_query_batch_size": "Batch size of query source", "log_cdc_query_batch_size_tip": "If the Text type data content in your table has a large memory, please reduce this value. Otherwise, increase this value to obtain better query performance (maximum value 2000, minimum value 1, default value 1000)", "log_cdc_query_batch_delay_tip": "Maximum latency of source database query (seconds)", "log_cdc_query_batch_delay": "Maximum latency of source database query (unit: second, value range 1s-100s, default value 5s)", "endian": "<PERSON><PERSON>", "debugLog": "debug log", "cdcPlugin": "cdc plugin", "autoMinerNormalSleepMs": "auto Miner Normal Sleep Ms", "autoMinerBrokenSleepMs": "auto Miner Broken Sleep Ms", "autoMinerRescanBatch": "auto Miner Rescan Batch", "manualMinerScanBatch": "manual Miner <PERSON><PERSON>", "manualMinerScanTimeoutMs": "manual Miner <PERSON><PERSON> Timeout Ms", "dumpLogTimeS": "dump log time seconds", "enableLogSpaceMonitor": "Enable Log Space Monitor", "enableLogSpaceMonitorTooltip": "When enabled, it will monitor Sybase transaction log space usage to prevent database crashes due to log space exhaustion", "logSpaceWarningThreshold": "Log Space Warning Threshold (%)", "logSpaceWarningThresholdTooltip": "Alert when log space usage exceeds this threshold, default 70%", "logSpaceCriticalThreshold": "Log Space Critical Threshold (%)", "logSpaceCriticalThresholdTooltip": "Critical alert when log space usage exceeds this threshold, default 85%", "logSpaceEmergencyThreshold": "Log Space Emergency Threshold (%)", "logSpaceEmergencyThresholdTooltip": "Enter emergency mode when log space usage exceeds this threshold, may pause CDC operations, default 95%", "logSpaceCheckIntervalMs": "Log Space Check Interval (ms)", "logSpaceCheckIntervalMsTooltip": "Time interval for checking log space usage, default 30 seconds", "createAutoInc": "Create Auto Increment", "createAutoIncTooltip": "After enabling, the Sybase target will synchronize the auto-increment column, but limited by the database itself, the initial and step are fixed to 1", "applyDefault": "Apply Default Value", "applyDefaultTooltip": "When the switch is turned on, the default value will be applied to the target. If there are unadapted functions or expressions, it may cause an error", "applyForeignKey": "Apply Foreign Key", "applyForeignKeyTooltip": "When the switch is turned on, the foreign key will be applied to the target. If there are unadapted delete, update cascade strategies will be discarded", "foreignKeyConstrain": "Export foreign key constraints", "foreignKeyConstrainTooltip": "When synchronizing foreign key constraints, since Sybase does not support ignoring foreign key constraints, the target foreign key constraints will be deleted first, and the foreign key constraints need to be exported manually recreated."}, "zh_CN": {"host": "地址", "port": "端口", "database": "数据库", "username": "账号", "password": "密码", "addtionalString": "连接参数", "timezone": "时区", "doc": "docs/sybase_zh_CN.md", "close_delay_time": "默认延迟关闭时间（单位：秒）", "encode_name": "编码格式", "decode_name": "解码格式", "out_decode_name": "文本输出字符集", "schema": "<PERSON><PERSON><PERSON>", "fetch_interval": "CDC延时（单位秒）", "auto_encode": "自动编解码", "targetAutoEncode": "目标写入编解码", "targetEncodeName": "编码格式", "targetDecodeName": "解码格式", "cdcCacheTime": "CDC 增量文件缓存时间（单位：分钟）", "checkTableInConnectionTest": "开启表数据检查", "heartbeat": "开启CDC心跳监听", "table_sql": "心跳表建表语句", "heartbeat_tip": "开启CDC心跳监听后需要您在源端创建心跳表（replicate_io_cdc_heartbeat）", "heart_database": "心跳表所在DataBase", "heart_schema": "心跳表所在Schema", "log_cdc_query": "Log CDC Query", "log_cdc_query_tip": "对象类型数据同步方式有，日志解析或者反查源库", "log_cdc_query_batch_size": "源库查询的批量数", "log_cdc_query_batch_size_tip": "如果您的表中Text类型数据内容内存较大，请调小此值，反之调大此值，以获取更好的查询性能（最大值2000，最小值1，默认值1000）", "log_cdc_query_batch_delay": "源库查询的最大延迟（秒）", "log_cdc_query_batch_delay_tip": "源库查询的最大延迟（单位：秒, 取值范围1s-100s，默认值5s）", "endian": "字节序", "debugLog": "日志级别", "cdcPlugin": "cdc 工作模式", "autoMinerNormalSleepMs": "自动挖掘正常轮询时间", "autoMinerBrokenSleepMs": "自动挖掘页损坏重扫等待时间(ms)", "autoMinerRescanBatch": "自动挖掘页损坏重扫批量", "manualMinerScanBatch": "手动挖掘批量", "manualMinerScanTimeoutMs": "手动挖掘轮询超时(ms)", "dumpLogTimeS": "归档日志间隔(s)", "enableLogSpaceMonitor": "启用日志空间监控", "enableLogSpaceMonitorTooltip": "开启后会监控Sybase事务日志空间使用情况，防止日志空间耗尽导致数据库崩溃", "logSpaceWarningThreshold": "日志空间告警阈值(%)", "logSpaceWarningThresholdTooltip": "当日志空间使用率超过此阈值时发出告警，默认70%", "logSpaceCriticalThreshold": "日志空间严重告警阈值(%)", "logSpaceCriticalThresholdTooltip": "当日志空间使用率超过此阈值时发出严重告警，默认85%", "logSpaceEmergencyThreshold": "日志空间紧急阈值(%)", "logSpaceEmergencyThresholdTooltip": "当日志空间使用率超过此阈值时进入紧急模式，可能暂停CDC操作，默认95%", "logSpaceCheckIntervalMs": "日志空间检查间隔(ms)", "logSpaceCheckIntervalMsTooltip": "检查日志空间使用情况的时间间隔，默认30秒", "createAutoInc": "同步自增列", "createAutoIncTooltip": "开启后，Sybase目标会同步自增列，但受限于数据库本身，初始和步长均固定为1", "applyDefault": "应用默认值", "applyDefaultTooltip": "开关打开时会将默认值应用到目标，如果有未适配的函数或表达式，可能会导致报错", "applyForeignKey": "应用外键", "applyForeignKeyTooltip": "开关打开时会将外键应用到目标，如果有未适配的delete,update级联策略将被丢弃", "foreignKeyConstrain": "导出外键约束", "foreignKeyConstrainTooltip": "同步外键约束时，由于Sybase不支持忽略外键约束无法正确同步数据，会先删除目标外键约束，需要导出外键约束手动重新创建"}, "zh_TW": {"host": "地址", "port": "端口", "database": "數據庫", "username": "賬號", "password": "密碼", "addtionalString": "連接參數", "timezone": "時區", "doc": "docs/sybase_zh_TW.md", "close_delay_time": "默認延遲關閉時間（組織：秒）", "encode_name": "編碼格式", "decode_name": "解碼格式", "out_decode_name": "輸出文字字元集", "schema": "<PERSON><PERSON><PERSON>", "fetch_interval": "CDC延時（組織秒）", "auto_encode": "自動編解碼", "targetAutoEncode": "目標寫入編解碼", "targetEncodeName": "編碼格式", "targetDecodeName": "解碼格式", "cdcCacheTime": "CDC增量檔案緩存時間（組織：分鐘）", "checkTableInConnectionTest": "開啟錶數據檢查", "heartbeat": "开启CDC心跳监听", "table_sql": "心跳錶建錶語句", "heartbeat_tip": " 開啟CDC心跳監聽後需要您在源端創建心跳錶（replicate_io_cdc_heartbeat）", "heart_database": "心跳錶所在Database", "heart_schema": "心跳錶所在Schema", "log_cdc_query": "Log CDC Query", "log_cdc_query_tip": "对象类型数据同步方式有，日志解析或者反查源库", "log_cdc_query_batch_size": "源庫查詢的批量數", "log_cdc_query_batch_size_tip": "如果您的表中Text類型數據內容內存較大，請調小此值，反之調大此值，以獲取更好的查詢性能（最大值2000，最小值1，默認值1000）", "log_cdc_query_batch_delay": "源庫查詢的最大延遲（秒）", "log_cdc_query_batch_delay_tip": "源庫查詢的最大延遲（單位：秒, 取值範圍1s-100s，默認值5s）", "endian": "字節序", "debugLog": "日志级别", "cdcPlugin": "cdc 工作模式", "autoMinerNormalSleepMs": "自动挖掘正常轮询时间", "autoMinerBrokenSleepMs": "自动挖掘页损坏重扫等待时间(ms)", "autoMinerRescanBatch": "自动挖掘页损坏重扫批量", "manualMinerScanBatch": "手动挖掘批量", "manualMinerScanTimeoutMs": "手动挖掘轮询超时(ms)", "dumpLogTimeS": "归档日志间隔(s)", "createAutoInc": "同步自增列", "createAutoIncTooltip": "開啟後，Sybase目標會同步自增列，但受限於數據庫本身，初始和步長均固定為1", "applyDefault": "應用默認值", "applyDefaultTooltip": "開關打開時會將默認值應用到目標，如果有未適配的函數或表達式，可能會導致報錯", "applyForeignKey": "應用外鍵", "applyForeignKeyTooltip": "開關打開時會將外鍵應用到目標，如果有未適配的delete,update級聯策略將被丟棄", "foreignKeyConstrain": "導出外鍵約束", "foreignKeyConstrainTooltip": "同步外鍵約束時，由於Sybase不支持忽略外鍵約束無法正確同步數據，會先刪除目標約束索引，需要導出外鍵約束手動重新創建"}}, "dataTypes": {"char[($byte)]": {"to": "TapString", "byte": 255, "default": 1, "byteRatio": 1, "fixed": true}, "unichar[($byte)]": {"to": "TapString", "byte": 8192, "default": 1, "byteRatio": 1, "fixed": true}, "univarchar[($byte)]": {"to": "TapString", "byte": 8192, "default": 1, "byteRatio": 1}, "varchar($byte)": {"name": "<PERSON><PERSON><PERSON>", "to": "TapString", "byte": 255, "default": 1, "byteRatio": 1}, "nchar($byte)": {"name": "<PERSON><PERSON><PERSON>", "to": "TapString", "default": 1, "byteRatio": 1, "fixed": true}, "nvarchar($byte)": {"name": "<PERSON><PERSON><PERSON>", "to": "TapString", "default": 1, "byteRatio": 1}, "image": {"to": "TapBinary", "byte": "2g", "pkEnablement": false}, "unitext[($param)]": {"to": "TapString", "byte": "2g", "pkEnablement": false}, "text[($param)]": {"to": "TapString", "byte": "2g", "pkEnablement": false}, "binary[($byte)]": {"to": "TapBinary", "byte": 255, "defaultByte": 1, "fixed": true}, "varbinary[($byte)]": {"to": "TapBinary", "byte": 255, "default": 1, "defaultByte": 1}, "timestamp": {"to": "TapBinary", "byte": 16, "queryOnly": true}, "bit(1)": {"to": "TapBoolean", "fixed": true, "bit": 1}, "tinyint[($zerofill)]": {"to": "TapNumber", "bit": 1, "precision": 3, "value": [0, 255]}, "money": {"to": "<PERSON><PERSON><PERSON><PERSON>", "defaultPrecision": 19, "defaultScale": 4, "fixed": true}, "smallmoney": {"to": "<PERSON><PERSON><PERSON><PERSON>", "defaultPrecision": 19, "defaultScale": 4, "fixed": true}, "smallint[($zerofill)]": {"to": "TapNumber", "bit": 16, "value": [-32768, 32767], "precision": 3}, "unsigned smallint[($zerofill)]": {"to": "TapNumber", "unsigned": "unsigned", "bit": 2, "value": [-65536, 65535], "precision": 5}, "usmallint[($zerofill)]": {"to": "TapNumber", "bit": 2, "value": [0, 65535], "defaultPrecision": 5, "defaultScale": 0, "unsigned": "unsigned"}, "uint[($zerofill)]": {"to": "TapNumber", "bit": 32, "value": [0, 4294967295], "defaultPrecision": 10, "defaultScale": 0, "unsigned": "unsigned"}, "int[($zerofill)]": {"to": "TapNumber", "bit": 32, "precision": 10, "value": [-2147483648, 2147483647]}, "unsigned int[($zerofill)]": {"to": "TapNumber", "bit": 32, "precision": 10, "value": [0, 4294967295], "unsigned": "unsigned"}, "bigint[($zerofill)]": {"to": "TapNumber", "bit": 64, "precision": 3, "value": [-9223372036854775808, 9223372036854775807]}, "ubigint[($zerofill)]": {"to": "TapNumber", "bit": 64, "value": [0, 18446744073709551615], "unsigned": "unsigned", "defaultPrecision": 20, "defaultScale": 0}, "unsigned bigint[($zerofill)]": {"to": "TapNumber", "bit": 64, "value": [0, 18446744073709551615], "priority": 1, "defaultPrecision": 20, "defaultScale": 0, "unsigned": "unsigned"}, "decimal[($precision,$scale)]": {"to": "TapNumber", "precision": [1, 38], "scale": [0, 38], "defaultPrecision": 30, "defaultScale": 10, "priority": 1, "fixed": true}, "numeric[($precision,$scale)]": {"to": "TapNumber", "precision": [1, 38], "scale": [0, 38], "defaultPrecision": 30, "defaultScale": 10, "fixed": true}, "float[($precision)]": {"to": "TapNumber", "priority": 2, "precision": [1, 48], "defaultPrecision": 17, "defaultScale": 15, "fixed": false}, "double precision": {"priority": 2, "precision": [1, 17], "preferPrecision": 11, "preferScale": 4, "scale": [0, 17], "fixed": false, "to": "TapNumber"}, "real": {"to": "TapNumber", "bit": 4, "defaultPrecision": 24, "defaultScale": 8, "fixed": false}, "date": {"to": "TapDate", "range": ["1000-01-01", "9999-12-31"], "pattern": "yyyy-MM-dd"}, "time[($fraction)]": {"to": "TapTime", "fraction": [0, 6], "defaultFraction": 3, "range": ["00:00:00", "23:59:59"], "pattern": "HH:mm:ss"}, "bigtime[($fraction)]": {"to": "TapTime", "fraction": [0, 6], "defaultFraction": 6, "range": ["00:00:00", "23:59:59"], "pattern": "HH:mm:ss"}, "datetime": {"to": "TapDateTime", "range": ["1753-01-01 00:00:00", "9999-12-31 23:59:59"], "defaultFraction": 6, "pattern": "yyyy-MM-dd HH:mm:ss"}, "smalldatetime": {"to": "TapDateTime", "range": ["1900-01-01 00:00:00", "2079-06-06 23:59:59"], "defaultFraction": 0, "pattern": "yyyy-MM-dd HH:mm:ss"}, "bigdatetime": {"to": "TapDateTime", "range": ["0001-01-01 00:00:00", "9999-12-31 23:59:59"], "pattern": "yyyy-MM-dd HH:mm:ss.SSSSSS"}}}