package io.tapdata.sybase.monitor;

import io.tapdata.entity.logger.Log;
import io.tapdata.sybase.extend.SybaseConfig;
import io.tapdata.sybase.extend.SybaseContext;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Sybase日志空间监控器
 * 监控事务日志空间使用情况，防止日志空间耗尽导致数据库崩溃
 * 
 * <AUTHOR>
 */
public class SybaseLogSpaceMonitor {
    
    private final SybaseContext sybaseContext;
    private final SybaseConfig sybaseConfig;
    private final Log tapLogger;
    
    // 监控配置参数
    private final int warningThreshold;      // 告警阈值(%)
    private final int criticalThreshold;     // 严重告警阈值(%)
    private final int emergencyThreshold;    // 紧急阈值(%)
    private final long checkIntervalMs;      // 检查间隔(毫秒)
    
    // 监控状态
    private final AtomicBoolean isMonitoring = new AtomicBoolean(false);
    private final AtomicLong lastCheckTime = new AtomicLong(0);
    private final AtomicLong lastAlertTime = new AtomicLong(0);
    private final AtomicBoolean emergencyMode = new AtomicBoolean(false);
    
    // 日志空间信息
    private volatile LogSpaceInfo lastLogSpaceInfo;
    
    /**
     * 日志空间信息类
     */
    public static class LogSpaceInfo {
        private final long totalSizeMB;
        private final long usedSizeMB;
        private final long freeSizeMB;
        private final double usagePercentage;
        private final long timestamp;
        
        public LogSpaceInfo(long totalSizeMB, long usedSizeMB, long freeSizeMB, double usagePercentage) {
            this.totalSizeMB = totalSizeMB;
            this.usedSizeMB = usedSizeMB;
            this.freeSizeMB = freeSizeMB;
            this.usagePercentage = usagePercentage;
            this.timestamp = System.currentTimeMillis();
        }
        
        // Getters
        public long getTotalSizeMB() { return totalSizeMB; }
        public long getUsedSizeMB() { return usedSizeMB; }
        public long getFreeSizeMB() { return freeSizeMB; }
        public double getUsagePercentage() { return usagePercentage; }
        public long getTimestamp() { return timestamp; }
        
        @Override
        public String toString() {
            return String.format("LogSpace[Total: %dMB, Used: %dMB(%.1f%%), Free: %dMB]", 
                totalSizeMB, usedSizeMB, usagePercentage, freeSizeMB);
        }
    }
    
    /**
     * 告警级别枚举
     */
    public enum AlertLevel {
        NORMAL("正常"),
        WARNING("告警"),
        CRITICAL("严重"),
        EMERGENCY("紧急");
        
        private final String description;
        
        AlertLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public SybaseLogSpaceMonitor(SybaseContext sybaseContext, Log tapLogger) {
        this(sybaseContext, tapLogger, 70, 85, 95, 30000); // 默认配置
    }
    
    public SybaseLogSpaceMonitor(SybaseContext sybaseContext, Log tapLogger, 
                                int warningThreshold, int criticalThreshold, 
                                int emergencyThreshold, long checkIntervalMs) {
        this.sybaseContext = sybaseContext;
        this.sybaseConfig = (SybaseConfig) sybaseContext.getConfig();
        this.tapLogger = tapLogger;
        this.warningThreshold = warningThreshold;
        this.criticalThreshold = criticalThreshold;
        this.emergencyThreshold = emergencyThreshold;
        this.checkIntervalMs = checkIntervalMs;
        
        tapLogger.info("SybaseLogSpaceMonitor initialized with thresholds: Warning={}%, Critical={}%, Emergency={}%, CheckInterval={}ms",
            warningThreshold, criticalThreshold, emergencyThreshold, checkIntervalMs);
    }
    
    /**
     * 开始监控
     */
    public void startMonitoring() {
        if (isMonitoring.compareAndSet(false, true)) {
            tapLogger.info("Starting Sybase log space monitoring...");
            // 立即执行一次检查
            checkLogSpace();
        }
    }
    
    /**
     * 停止监控
     */
    public void stopMonitoring() {
        if (isMonitoring.compareAndSet(true, false)) {
            tapLogger.info("Stopping Sybase log space monitoring...");
            emergencyMode.set(false);
        }
    }
    
    /**
     * 检查是否需要进行日志空间检查
     */
    public boolean shouldCheck() {
        if (!isMonitoring.get()) {
            return false;
        }
        
        long currentTime = System.currentTimeMillis();
        long lastCheck = lastCheckTime.get();
        
        // 如果是紧急模式，检查频率更高
        long interval = emergencyMode.get() ? checkIntervalMs / 3 : checkIntervalMs;
        
        return currentTime - lastCheck >= interval;
    }
    
    /**
     * 执行日志空间检查
     */
    public LogSpaceInfo checkLogSpace() {
        if (!isMonitoring.get()) {
            return lastLogSpaceInfo;
        }
        
        try {
            lastCheckTime.set(System.currentTimeMillis());
            LogSpaceInfo logSpaceInfo = queryLogSpaceInfo();
            
            if (logSpaceInfo != null) {
                lastLogSpaceInfo = logSpaceInfo;
                processLogSpaceAlert(logSpaceInfo);
            }
            
            return logSpaceInfo;
            
        } catch (Exception e) {
            tapLogger.error("Failed to check log space: {}", e.getMessage(), e);
            return lastLogSpaceInfo;
        }
    }
    
    /**
     * 查询日志空间信息
     */
    private LogSpaceInfo queryLogSpaceInfo() throws SQLException {
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;
        
        try {
            connection = sybaseContext.getConnection();
            statement = connection.createStatement();
            
            // 查询日志空间使用情况
            String sql = String.format(
                "SELECT " +
                "  db_name() as database_name, " +
                "  (SELECT SUM(size) FROM master.dbo.sysusages WHERE dbid = db_id() AND segmap & 4 = 4) * @@maxpagesize / 1024 / 1024 as total_log_size_mb, " +
                "  lct_admin('logsegment_freepages', db_id()) * @@maxpagesize / 1024 / 1024 as free_log_size_mb " +
                "FROM master.dbo.sysdatabases WHERE name = '%s'", 
                sybaseConfig.getDatabase()
            );
            
            resultSet = statement.executeQuery(sql);
            
            if (resultSet.next()) {
                long totalSizeMB = resultSet.getLong("total_log_size_mb");
                long freeSizeMB = resultSet.getLong("free_log_size_mb");
                long usedSizeMB = totalSizeMB - freeSizeMB;
                double usagePercentage = totalSizeMB > 0 ? (double) usedSizeMB / totalSizeMB * 100 : 0;
                
                return new LogSpaceInfo(totalSizeMB, usedSizeMB, freeSizeMB, usagePercentage);
            }
            
        } finally {
            if (resultSet != null) try { resultSet.close(); } catch (Exception e) {}
            if (statement != null) try { statement.close(); } catch (Exception e) {}
            if (connection != null) try { connection.close(); } catch (Exception e) {}
        }
        
        return null;
    }
    
    /**
     * 处理日志空间告警
     */
    private void processLogSpaceAlert(LogSpaceInfo logSpaceInfo) {
        AlertLevel alertLevel = determineAlertLevel(logSpaceInfo.getUsagePercentage());
        
        // 控制告警频率，避免日志刷屏
        long currentTime = System.currentTimeMillis();
        boolean shouldAlert = false;
        
        switch (alertLevel) {
            case EMERGENCY:
                emergencyMode.set(true);
                shouldAlert = currentTime - lastAlertTime.get() >= 60000; // 紧急情况每分钟告警一次
                break;
            case CRITICAL:
                shouldAlert = currentTime - lastAlertTime.get() >= 300000; // 严重情况每5分钟告警一次
                break;
            case WARNING:
                shouldAlert = currentTime - lastAlertTime.get() >= 600000; // 告警情况每10分钟告警一次
                break;
            case NORMAL:
                if (emergencyMode.get()) {
                    emergencyMode.set(false);
                    tapLogger.info("Log space usage returned to normal: {}", logSpaceInfo);
                }
                shouldAlert = false;
                break;
        }
        
        if (shouldAlert) {
            lastAlertTime.set(currentTime);
            logAlert(alertLevel, logSpaceInfo);
        }
    }
    
    /**
     * 确定告警级别
     */
    private AlertLevel determineAlertLevel(double usagePercentage) {
        if (usagePercentage >= emergencyThreshold) {
            return AlertLevel.EMERGENCY;
        } else if (usagePercentage >= criticalThreshold) {
            return AlertLevel.CRITICAL;
        } else if (usagePercentage >= warningThreshold) {
            return AlertLevel.WARNING;
        } else {
            return AlertLevel.NORMAL;
        }
    }
    
    /**
     * 记录告警日志
     */
    private void logAlert(AlertLevel alertLevel, LogSpaceInfo logSpaceInfo) {
        String message = String.format(
            "[%s] Sybase日志空间使用率告警: %s, 使用率: %.1f%%, 建议立即检查未提交事务和CDC进程状态",
            alertLevel.getDescription(), logSpaceInfo, logSpaceInfo.getUsagePercentage()
        );
        
        switch (alertLevel) {
            case EMERGENCY:
                tapLogger.error("🚨 " + message);
                tapLogger.error("紧急建议: 1) 检查长时间运行的事务 2) 考虑停止CDC避免数据库崩溃 3) 手动执行 dump tran");
                break;
            case CRITICAL:
                tapLogger.warn("⚠️ " + message);
                tapLogger.warn("严重建议: 1) 立即检查未提交事务列表 2) 监控CDC断点推进情况 3) 准备紧急处理方案");
                break;
            case WARNING:
                tapLogger.warn("⚡ " + message);
                tapLogger.warn("告警建议: 1) 检查CDC进程状态 2) 监控事务日志增长趋势 3) 优化dumpLogTimeS配置");
                break;
        }
    }
    
    /**
     * 获取当前日志空间信息
     */
    public LogSpaceInfo getCurrentLogSpaceInfo() {
        return lastLogSpaceInfo;
    }
    
    /**
     * 是否处于紧急模式
     */
    public boolean isEmergencyMode() {
        return emergencyMode.get();
    }
    
    /**
     * 是否正在监控
     */
    public boolean isMonitoring() {
        return isMonitoring.get();
    }
}
