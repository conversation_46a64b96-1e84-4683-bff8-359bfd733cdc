package io.tapdata.sybase.error;

import io.tapdata.exception.TapExClass;
import io.tapdata.exception.TapExCode;
import io.tapdata.exception.TapExLevel;
import io.tapdata.exception.TapExType;

@TapExClass(
        code = 38,
        module = "sybase-connector",
        describe = "Sybase Error Code",
        prefix = "SYBASE")
public class SybaseErrorCode {
    @TapExCode(
            describe = "在进行增量读取的过程中，获取增量时间点信息失败",
            describeCN = "In the process of incremental replication , it failed to obtain the incremental time point information",
            dynamicDescription = "startRid:{},rowId:{}",
            dynamicDescriptionCN = "startRid:{},rowId:{}",
            level = TapExLevel.CRITICAL,
            type = TapExType.RUNTIME
    )
    public static String STREAM_READ_GET_INFO_ERROR = "370001";
}
