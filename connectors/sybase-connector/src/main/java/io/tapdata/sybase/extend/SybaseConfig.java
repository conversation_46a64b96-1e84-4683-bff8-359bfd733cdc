package io.tapdata.sybase.extend;

import io.tapdata.common.CommonDbConfig;
import io.tapdata.kit.EmptyKit;

import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 * @description SybaseConfig create by <PERSON>
 * @create 2023/7/10 18:42
 **/
public class SybaseConfig extends CommonDbConfig {
    protected String username;
    private Boolean autoEncode;
    private String encode;
    private String decode;
    private boolean targetAutoEncode;
    private String targetEncode;
    private String targetDecode;
    private String outDecode;
    private Boolean checkTableInConnectionTest;
    private Integer logCdcQueryBatchSize;
    private Integer logCdcQueryBatchDelay;
    private Integer logCdcQuery;

    private Integer endian;
    private Integer debugLog;

    private Integer cdcPlugin;
    private Integer autoMinerNormalSleepMs;
    private Integer autoMinerBrokenSleepMs;
    private Integer autoMinerRescanBatch;
    private Integer manualMinerScanBatch;
    private Integer manualMinerScanTimeoutMs;
    private Integer dumpLogTimeS;

    public Integer getAutoMinerNormalSleepMs() {
        return autoMinerNormalSleepMs;
    }
    public void setAutoMinerNormalSleepMs(Integer autoMinerNormalSleepMs) {
        this.autoMinerNormalSleepMs = autoMinerNormalSleepMs;
    }
    public Integer getAutoMinerBrokenSleepMs() {
        return autoMinerBrokenSleepMs;
    }

    public boolean isTargetAutoEncode() {
        return targetAutoEncode;
    }

    public void setTargetAutoEncode(boolean targetAutoEncode) {
        this.targetAutoEncode = targetAutoEncode;
    }

    public String getTargetEncode() {
        return targetEncode;
    }

    public void setTargetEncode(String targetEncode) {
        this.targetEncode = targetEncode;
    }

    public String getTargetDecode() {
        return targetDecode;
    }

    public void setTargetDecode(String targetDecode) {
        this.targetDecode = targetDecode;
    }

    public void setAutoMinerBrokenSleepMs(Integer autoMinerBrokenSleepMs) {
        this.autoMinerBrokenSleepMs = autoMinerBrokenSleepMs;
    }
    public Integer getAutoMinerRescanBatch() {
        return autoMinerRescanBatch;
    }
    public void setAutoMinerRescanBatch(Integer autoMinerRescanBatch) {
        this.autoMinerRescanBatch = autoMinerRescanBatch;
    }
    public Integer getManualMinerScanBatch() {
        return manualMinerScanBatch;
    }
    public void setManualMinerScanBatch(Integer manualMinerScanBatch) {
        this.manualMinerScanBatch = manualMinerScanBatch;
    }
    public Integer getManualMinerScanTimeoutMs() {
        return manualMinerScanTimeoutMs;
    }
    public void setManualMinerScanTimeoutMs(Integer manualMinerScanTimeoutMs) {
        this.manualMinerScanTimeoutMs = manualMinerScanTimeoutMs;
    }
    public Integer getDumpLogTimeS() {
        return dumpLogTimeS;
    }
    public void setDumpLogTimeS(Integer dumpLogTimeS) {
        this.dumpLogTimeS = dumpLogTimeS;
    }

    public static final String JDBC_SYBASE_DRIVER = "com.sybase.jdbc42.jdbc.SybDriver";

    public SybaseConfig() {
        setDbType("sybase");
        setEscapeChar(' ');
        setJdbcDriver(JDBC_SYBASE_DRIVER);
        //setJdbcDriver("net.sourceforge.jtds.jdbc.Driver");
        setUsername(username);
    }

    public String getDatabaseUrlPattern() {
        // last %s reserved for extend params  ****************************
        return "**************************";
        //return url.replace("jdbc:sybase:", "jdbc:sybase:Tds:");
    }


    //deal with extend params no matter there is ?
    public String getDatabaseUrl() {
        if (EmptyKit.isNull(this.getExtParams())) {
            this.setExtParams("");
        }
        if (EmptyKit.isNotEmpty(this.getExtParams()) && !this.getExtParams().startsWith("?") && !this.getExtParams().startsWith(":")) {
            this.setExtParams("?" + this.getExtParams());
        }
        return String.format(this.getDatabaseUrlPattern(), this.getHost(), this.getPort(), this.getDatabase(), this.getExtParams());
    }

    @Override
    public SybaseConfig load(Map<String, Object> map) {
        SybaseConfig config = (SybaseConfig) super.load(map);
        if (EmptyKit.isEmpty(map)) {
            return config;
        }
        setUser(EmptyKit.isBlank(getUser()) ? (String) map.get("username") : getUser());
        setUsername(getUser());
        setExtParams(EmptyKit.isBlank(getExtParams()) ? (String) map.get("addtionalString") : getExtParams());
        setSchema(getSchema());
        return config;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    @Override
    public Properties getProperties() {
        Properties properties = super.getProperties();
        if (null == properties) properties = new Properties();
        properties.put("username", username);
        properties.put("user", username);
        properties.put("database", this.getDatabase());
        properties.put("dbType", "SYBASE_ASE");
        properties.put("host", this.getHost());
        properties.put("port", this.getPort());
        properties.put("schema", this.getSchema());
        properties.put("password", EmptyKit.isNull(this.getPassword()) ? "" : this.getPassword());
        properties.put("addtionalString", EmptyKit.isNull(this.getExtParams()) ? "" : this.getExtParams());
        properties.put("jdbcDriver", this.getJdbcDriver());
        return properties;
    }

    public boolean isCheckTableInConnectionTest() {
        return null != checkTableInConnectionTest && checkTableInConnectionTest;
    }

    public void setCheckTableInConnectionTest(boolean checkTableInConnectionTest) {
        this.checkTableInConnectionTest = checkTableInConnectionTest;
    }

    public Boolean getAutoEncode() {
        return autoEncode;
    }

    public void setAutoEncode(Boolean autoEncode) {
        this.autoEncode = autoEncode;
    }

    public Boolean getCheckTableInConnectionTest() {
        return checkTableInConnectionTest;
    }

    public void setCheckTableInConnectionTest(Boolean checkTableInConnectionTest) {
        this.checkTableInConnectionTest = checkTableInConnectionTest;
    }

    public String getEncode() {
        return encode;
    }

    public void setEncode(String encode) {
        this.encode = encode;
    }

    public String getDecode() {
        return decode;
    }

    public void setDecode(String decode) {
        this.decode = decode;
    }

    public String getOutDecode() {
        return outDecode;
    }

    public void setOutDecode(String outDecode) {
        this.outDecode = outDecode;
    }

    public Integer getLogCdcQueryBatchSize() {
        return logCdcQueryBatchSize;
    }

    public void setLogCdcQueryBatchSize(Integer logCdcQueryBatchSize) {
        this.logCdcQueryBatchSize = logCdcQueryBatchSize;
    }

    public Integer getLogCdcQueryBatchDelay() {
        return logCdcQueryBatchDelay;
    }

    public void setLogCdcQueryBatchDelay(Integer logCdcQueryBatchDelay) {
        this.logCdcQueryBatchDelay = logCdcQueryBatchDelay;
    }

    public Integer getLogCdcQuery() {
        return logCdcQuery;
    }

    public Integer getEndian() {
        return endian;
    }

    public Integer getDebugLog() {
        return debugLog;
    }

    public void setDebugLog(Integer debugLog) {
        this.debugLog = debugLog;
    }

    public Integer getCdcPlugin() {
        return cdcPlugin;
    }

    public void setCdcPlugin(Integer cdcPlugin) {
        this.cdcPlugin = cdcPlugin;
    }

    public void setEndian(Integer endian) {
        this.endian = endian;
    }

    public void setLogCdcQuery(Integer logCdcQuery) {
        this.logCdcQuery = logCdcQuery;
    }
}
