package io.tapdata.sybase.filter;

import cn.hutool.core.map.MapUtil;
import io.tapdata.entity.event.TapEvent;
import io.tapdata.entity.event.dml.TapDeleteRecordEvent;
import io.tapdata.entity.event.dml.TapInsertRecordEvent;
import io.tapdata.entity.event.dml.TapUpdateRecordEvent;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.schema.type.TapType;
import io.tapdata.entity.utils.DataMap;
import io.tapdata.kit.EmptyKit;
import io.tapdata.pdk.apis.functions.connector.source.ConnectionConfigWithTables;
import io.tapdata.sybase.SybaseConnectorV2;
import io.tapdata.sybase.cdc.SybaseDataTypeConvert;
import io.tapdata.sybase.extend.SybaseConfig;
import io.tapdata.sybase.extend.SybaseContext;
import io.tapdata.sybase.util.ConnectorUtil;
import io.tapdata.sybase.util.MultiThreadFactory;

import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Time;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

import static io.tapdata.base.ConnectorBase.toJson;

class ReadSourceFilter extends ReadFilter {
    Map<String, List<ConnectionConfigWithTables>> connectionConfigOfTable = new HashMap<>();
    Map<String, Boolean> tableNeedReadSource = new HashMap<>();

    @Override
    public ReadFilter init(FilterEntity params) {
        super.init(params);
        connectionConfigOfTable = groupConnectionConfigWithTables(params.getConnectionConfigWithTables());
        return this;
    }

    private DataMap getConnectionConfig(String fullTableName) {
        if (null == fullTableName || "".equals(fullTableName.trim())) return null;
        String[] split = fullTableName.split("\\.");
        if (split.length != 2) return null;
        String schema = split[0];
        String tableName = split[1];
        return dispatchConnectionConfig(connectionConfigOfTable, schema, tableName, sybaseConfig);
    }

    public Object filterDate(Object oriValue, byte dateType) {
        switch (dateType) {
            case TapType.TYPE_DATETIME:
                if (oriValue instanceof LocalDateTime) {
                    return Timestamp.valueOf((LocalDateTime) oriValue);
                }
                break;
            case TapType.TYPE_TIME:
                if(oriValue instanceof Long){
                    return LocalTime.MIDNIGHT.plus(Duration.ofNanos((Long)oriValue * 1000)).toString();
                }
                break;
        }
        return oriValue;
    }

    public List<TapEvent> readFilter(List<TapEvent> events, TapTable tapTable, Set<String> blockFields, String fullTableName) {
        if (null == tapTable || null == blockFields) return events;
        Boolean needReadSource = null;
        if (!tableNeedReadSource.containsKey(fullTableName) ||  null == (needReadSource = tableNeedReadSource.get(fullTableName))) {
            LinkedHashMap<String, TapField> nameFieldMap = tapTable.getNameFieldMap();
            if (null != nameFieldMap && !nameFieldMap.isEmpty()) {
                try {
                    Map.Entry<String, TapField> entry = nameFieldMap.entrySet().stream()
                            .filter(Objects::nonNull)
                            .filter(f -> {
                                TapField tapField = f.getValue();
                                return isBolField(tapField.getDataType());
                            }).findFirst().orElseGet(null);
                    needReadSource = null != entry;
                } catch (Exception e) {
                    needReadSource = false;
                }
            } else {
                needReadSource = false;
            }
            tableNeedReadSource.put(fullTableName, needReadSource);
        }

        if (!needReadSource) {
            return events;
        }

        DataMap dataMap = getConnectionConfig(fullTableName);
        if (null == dataMap || dataMap.isEmpty()) {
            tableNeedReadSource.put(fullTableName, false);
            log.debug("Can not get connection config to create jdbc connection, fail to get bol from source, full table name: {}", fullTableName);
            return events;
        }
        if (null == events || events.isEmpty()) return events;
        List<Map<String, Object>> primaryKeyValues = new ArrayList<>();
        List<String> primaryKeys = new ArrayList<>(tapTable.primaryKeys(true));
        if (primaryKeys.isEmpty()) {
            tableNeedReadSource.put(fullTableName, false);
            //log.debug("Not fund any primary key in table {}, it's mean can not read from source of this table, auto read from log of this table now", tapTable.getId());
            return events;
        }

        long start = System.currentTimeMillis();
        String sql = null;
        try {
            //step 1: collect primary key's value
            List<TapEvent> collect = events.stream().filter(e -> Objects.nonNull(e) && !(e instanceof TapDeleteRecordEvent)).collect(Collectors.toList());
            if (collect.isEmpty()) {
                return events;
            }
            collect.forEach(e -> {
                Map<String, Object> after = null;
                if (e instanceof TapInsertRecordEvent) {
                    after = ((TapInsertRecordEvent) e).getAfter();
                } else if (e instanceof TapUpdateRecordEvent) {
                    after = ((TapUpdateRecordEvent) e).getAfter();
                }
                if (null != after && !after.isEmpty()) {
                    Map<String, Object> primaryKey = new HashMap<>();
                    for (String key : primaryKeys) {
                        primaryKey.put(key, after.get(key));
                    }
                    primaryKeyValues.add(primaryKey);
                }
            });
            if (primaryKeyValues.isEmpty()) {
                return events;
            }

            //step 2; query blockFields's value by jdbc connection
            List<Map<String, Object>> queryResult = new ArrayList<>();

            Set<String> queryColumns = new HashSet<>(blockFields);
            queryColumns.addAll(primaryKeys);
            String columns = queryColumns.stream().map(c -> " \"" + c + "\" ").collect(Collectors.joining(","));


            boolean needEncode = Boolean.TRUE.equals(sybaseConfig.getAutoEncode());
            String encode = needEncode ? Optional.ofNullable(sybaseConfig.getEncode()).orElse("cp850") : null;
            String decode = needEncode ? Optional.ofNullable(sybaseConfig.getDecode()).orElse("big5") : null;

            List<Object> prepareParams = new ArrayList<>();
            final boolean onlyOnePrimaryKey = primaryKeys.size() == 1;
            final Map<String,Byte> dateTypeFields = ConnectorUtil.getDateTypeFields(tapTable);

            // 分别处理包含null值和不包含null值的主键记录
            List<Map<String, Object>> nonNullPrimaryKeyValues = new ArrayList<>();
            List<Map<String, Object>> nullPrimaryKeyValues = new ArrayList<>();

            for (Map<String, Object> kv : primaryKeyValues) {
                if (kv.values().stream().anyMatch(Objects::isNull)) {
                    nullPrimaryKeyValues.add(kv);
                } else {
                    nonNullPrimaryKeyValues.add(kv);
                }
            }

            List<String> whereClauses = new ArrayList<>();

            // 处理不包含null值的记录
            if (!nonNullPrimaryKeyValues.isEmpty()) {
                if (onlyOnePrimaryKey) {
                    // 单主键场景：使用IN子句
                    String inClause = nonNullPrimaryKeyValues.stream().map(kv -> {
                        Object value = kv.values().iterator().next();
                        if (EmptyKit.isNotEmpty(dateTypeFields) && dateTypeFields.containsKey(primaryKeys.get(0))) {
                            prepareParams.add(filterDate(value, dateTypeFields.get(primaryKeys.get(0))));
                        } else {
                            prepareParams.add(value);
                        }
                        return "?";
                    }).collect(Collectors.joining(","));

                    whereClauses.add(String.format("\"%s\" IN (%s)", primaryKeys.get(0), inClause));
                } else {
                    // 多主键场景：使用OR条件
                    String orClause = nonNullPrimaryKeyValues.stream()
                            .map(kv -> kv.keySet().stream().map(c -> {
                                Object value = kv.get(c);
                                if (EmptyKit.isNotEmpty(dateTypeFields) && dateTypeFields.containsKey(c)) {
                                    prepareParams.add(filterDate(value, dateTypeFields.get(c)));
                                } else {
                                    prepareParams.add(value);
                                }
                                return "\"" + c + "\"=?";
                            }).collect(Collectors.joining(" AND "))
                            ).collect(Collectors.joining(") OR ("));

                    whereClauses.add("(" + orClause + ")");
                }
            }

            // 处理包含null值的记录
            if (!nullPrimaryKeyValues.isEmpty()) {
                String nullClause = nullPrimaryKeyValues.stream()
                        .map(kv -> kv.keySet().stream().map(c -> {
                            Object value = kv.get(c);
                            if (value == null) {
                                return "\"" + c + "\" IS NULL";
                            } else {
                                if (EmptyKit.isNotEmpty(dateTypeFields) && dateTypeFields.containsKey(c)) {
                                    prepareParams.add(filterDate(value, dateTypeFields.get(c)));
                                } else {
                                    prepareParams.add(value);
                                }
                                return "\"" + c + "\"=?";
                            }
                        }).collect(Collectors.joining(" AND "))
                        ).collect(Collectors.joining(") OR ("));

                whereClauses.add("(" + nullClause + ")");
            }

            if (whereClauses.isEmpty()) {
                // 如果没有任何有效的查询条件，返回原始events
                return events;
            }

            String finalWhereClause = whereClauses.stream().collect(Collectors.joining(" OR "));
            sql = String.format("SELECT %s FROM [%s] WHERE %s", columns, fullTableName, finalWhereClause);



            //String outCode = needEncode ? Optional.ofNullable(nodeConfig.getOutDecode()).orElse("utf-8") : null;
            try (SybaseContext sybaseContext = new SybaseContext(new SybaseConfig().load(dataMap))) {
                queryOnce(sybaseContext, prepareParams, sql, queryResult, dateTypeFields.keySet(), needEncode, encode, decode);
            } catch (Exception e) {
                log.error("Query blockFields's value by jdbc connection failed, full table name: {}, error msg: {}, sql: {}, prepare value: {}", fullTableName, e.getMessage(), sql, prepareParams);
                return events;
            }


            //step 3: assemble blockFields's value into tap event
            MultiThreadFactory<TapEvent> multiThreadFactory = new MultiThreadFactory<>(Math.max(Math.min(collect.size() / 50 + (collect.size() % 50 > 0 ? 1 : 0), 5), 1), 50);
            List<TapEvent> needRemoveEvents = new CopyOnWriteArrayList<>();
            multiThreadFactory.handel(collect, e -> {
                Iterator<TapEvent> tapEventIterator = collect.iterator();
                while (tapEventIterator.hasNext()) {
                    TapEvent tapEvent = tapEventIterator.next();
                    Map<String, Object> after = null;
                    if (tapEvent instanceof TapInsertRecordEvent) {
                        after = ((TapInsertRecordEvent) tapEvent).getAfter();
                    } else if (tapEvent instanceof TapUpdateRecordEvent) {
                        after = ((TapUpdateRecordEvent) tapEvent).getAfter();
                    }
                    if (null != after) {
                        boolean lookUpSuccess = false;
                        for (Map<String, Object> result : queryResult) {
                            boolean isThisRecord = false;
                            for (String key : primaryKeys) {
                                Object afterValue = after.get(key);
                                Object resultValue = result.get(key);
                                if (!(isThisRecord =
                                        (null == afterValue && resultValue == null)
                                                || (null != afterValue && afterValue.equals(resultValue)))) break;
                            }
                            if (isThisRecord) {
                                lookUpSuccess = true;
                                after.putAll(result);
                                break;
                            }
                        }
                        if(!lookUpSuccess){
                            needRemoveEvents.add(tapEvent);
                        }
                    }
                }
            });
            for (TapEvent removeEvent : needRemoveEvents) {
                if (removeEvent instanceof TapInsertRecordEvent) {
                    log.warn("There is data in the text field of the table, but it cannot be found in the source table," +
                            "This event will be filtered. tableName:{},op:{},after:{}", ((TapInsertRecordEvent) removeEvent).getTableId(),"insert", ((TapInsertRecordEvent) removeEvent).getAfter());
                } else if (removeEvent instanceof TapUpdateRecordEvent) {
                    log.warn("There is data in the text field of the table, but it cannot be found in the source table," +
                            "this event will be filtered. tableName:{},op:{},after:{}",((TapUpdateRecordEvent) removeEvent).getTableId(), "update", ((TapUpdateRecordEvent) removeEvent).getAfter());
                }
            }
            events.removeAll(needRemoveEvents);
        } finally {
            if (null != sql) {
                log.debug("Read source with table {} for an batch recodes {} cost time: {}ms, sql: {}, all line's primary keys: {}",
                        fullTableName,
                        events.size(),
                        System.currentTimeMillis() - start,
                        sql,
                        toJson(primaryKeyValues));
            }
        }
        return events;
    }


    private void queryOnce(SybaseContext sybaseContext,
                           List<Object> prepareParams,
                           String sql,
                           List<Map<String, Object>> queryResult,
                           Set<String> dateTypeSet,
                           boolean needEncode,
                           String encode,
                           String decode) throws SQLException {
        sybaseContext.prepareQuery(sql, prepareParams, resultSet -> {
            ResultSetMetaData metaData = resultSet.getMetaData();
            Map<String, String> typeAndNameFromMetaData = new HashMap<>();
            int columnCount = metaData.getColumnCount();
            for (int index = 1; index < columnCount + 1; index++) {
                String type = metaData.getColumnTypeName(index);
                if (null == type) continue;
                typeAndNameFromMetaData.put(metaData.getColumnName(index), type.toUpperCase(Locale.ROOT));
            }
            while (null != isAlive && isAlive.test(null) && resultSet.next()) {
                queryResult.add(SybaseDataTypeConvert.filterTimeForDataBase(resultSet, typeAndNameFromMetaData, dateTypeSet, needEncode, encode, decode));
            }
        });
    }
}
