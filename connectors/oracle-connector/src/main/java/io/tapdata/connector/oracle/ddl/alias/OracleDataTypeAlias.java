package io.tapdata.connector.oracle.ddl.alias;

import io.tapdata.common.ddl.alias.DbDataTypeAlias;

public class OracleDataTypeAlias extends DbDataTypeAlias {

    public OracleDataTypeAlias(String alias) {
        super(alias);
    }

    @Override
    protected String toInteger() {
        return "INTEGER";
    }

    @Override
    protected String toDecimal() {
        return "NUMBER";
    }

    @Override
    protected String toVarchar() {
        return "VARCHAR2";
    }

}
