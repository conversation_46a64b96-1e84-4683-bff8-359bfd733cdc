package io.tapdata.connector.oracle.query.entity;

import io.tapdata.common.JdbcContext;

import java.util.function.Supplier;

public class QueryEntity {
    protected JdbcContext jdbcContext;
    protected Supplier<Boolean> supplier;

    public boolean active() {
        return null != supplier && supplier.get();
    }

    public QueryEntity withJdbcContext(JdbcContext jdbcContext) {
        this.jdbcContext = jdbcContext;
        return this;
    }
    public QueryEntity withIsAliveSupplier(Supplier<Boolean> supplier) {
        this.supplier = supplier;
        return this;
    }
    public JdbcContext jdbcContext() {
        return jdbcContext;
    }
}