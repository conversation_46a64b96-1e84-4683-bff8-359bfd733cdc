package io.tapdata.connector.oracle.cdc.logminer.bean;

public class ThreadRedoLog {

    private RedoLog onlineRedoLog;
    private RedoLog archivedRedoLog;
    private Long thread;
    private Long sequence;

    public ThreadRedoLog(Long thread) {
        this.thread = thread;
    }

    public ThreadRedoLog(Long thread, Long sequence) {
        this.thread = thread;
        this.sequence = sequence;
    }

    public Long getThread() {
        return thread;
    }

    public void setThread(Long thread) {
        this.thread = thread;
    }

    public Long getSequence() {
        return sequence;
    }

    public void setSequence(Long sequence) {
        this.sequence = sequence;
    }

    public RedoLog getOnlineRedoLog() {
        return onlineRedoLog;
    }

    public void setOnlineRedoLog(RedoLog onlineRedoLog) {
        this.onlineRedoLog = onlineRedoLog;
    }

    public RedoLog getArchivedRedoLog() {
        return archivedRedoLog;
    }

    public void setArchivedRedoLog(RedoLog archivedRedoLog) {
        this.archivedRedoLog = archivedRedoLog;
    }
}
