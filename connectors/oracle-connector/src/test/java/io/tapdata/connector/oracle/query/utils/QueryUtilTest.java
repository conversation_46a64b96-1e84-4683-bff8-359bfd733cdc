package io.tapdata.connector.oracle.query.utils;

import io.tapdata.entity.utils.DataMap;
import io.tapdata.pdk.apis.entity.TapAdvanceFilter;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import java.util.Map;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class QueryUtilTest {
    @Test
    void testParams() {
        Assertions.assertEquals("ROUND((CAST(\"%s\" as DATE) - TO_DATE('%s','%s')) * %d)", QueryUtil.DATE_TIME_FILTER_SQL);
        Assertions.assertEquals("1970-01-01 00:00:00", QueryUtil.BEGIN_DATE_TIME);
        Assertions.assertEquals("yyyy-mm-dd hh24:mi:ss", QueryUtil.DATE_TIME_FORMAT);
        Assertions.assertEquals(86400, QueryUtil.DATE_TIME_SCALE);
    }

    @Nested
    class BuildWhereSqlTest {
        TapAdvanceFilter filter;
        DataMap match;
        Object customCommandObj;
        @BeforeEach
        void init(){
            filter = mock(TapAdvanceFilter.class);
            match = mock(DataMap.class);
            customCommandObj = mock(Object.class);

            when(match.get("customCommand")).thenReturn(customCommandObj);
        }

        void assertVerify(TapAdvanceFilter f, String defaultSql,
                          Object params, Object sql,
                          DataMap m, boolean empty,
                          String excepted,
                          int getMatch, int isEmpty, int customCommand,
                          int getParam, int getSql) {
            when(filter.getMatch()).thenReturn(m);
            when(match.isEmpty()).thenReturn(empty);
            try(MockedStatic<QueryUtil> qu = mockStatic(QueryUtil.class)) {
                qu.when(() -> QueryUtil.getMap(customCommandObj, "params")).thenReturn(params);
                qu.when(() -> QueryUtil.getMap(params, "sql")).thenReturn(sql);
                qu.when(() -> QueryUtil.buildWhereSql(f, defaultSql)).thenCallRealMethod();
                String whereSql = QueryUtil.buildWhereSql(f, defaultSql);
                Assertions.assertNotNull(whereSql);
                Assertions.assertEquals(excepted, whereSql);
                qu.verify(() -> QueryUtil.getMap(customCommandObj, "params"), times(getParam));
                qu.verify(() -> QueryUtil.getMap(params, "sql"), times(getSql));
                verify(filter, times(getMatch)).getMatch();
                verify(match, times(isEmpty)).isEmpty();
                verify(match, times(customCommand)).get("customCommand");
            }
        }

        @Test
        void testFilterIsNull() {
            assertVerify(null, "",
                    null, null,
                    null, false,
                    "",
                    0, 0, 0,
                    0, 0);
        }
        @Test
        void testDefaultSQLIsNull() {
            assertVerify(filter, null,
                    null, null,
                    null, false,
                    "",
                    1, 0, 0,
                    0, 0);
        }
        @Test
        void testMatchIsNull() {
            assertVerify(filter, "",
                    null, null,
                    null, false,
                    "",
                    1, 0, 0,
                    0, 0);
        }

        @Test
        void testMatchIsEmpty() {
            assertVerify(filter, "",
                    null, null,
                    match, true,
                    "",
                    1, 1, 0,
                    0, 0);
        }

        @Test
        void testParamsIsNull(){
            assertVerify(filter, "",
                    null, null,
                    match, false,
                    "",
                    1, 1, 1,
                    1, 0);
        }

        @Test
        void testSqlIsNull() {
            assertVerify(filter, "",
                    mock(Object.class), null,
                    match, false,
                    "",
                    1, 1, 1,
                    1, 1);
        }

        @Test
        void testSqlNotNull() {
            assertVerify(filter, "",
                    mock(Object.class), "1=1",
                    match, false,
                    "where 1=1",
                    1, 1, 1,
                    1, 1);
        }

        @Test
        void testWhereSqlStartWithWhere() {
            assertVerify(filter, "",
                    mock(Object.class), "where 1=1",
                    match, false,
                    "where 1=1",
                    1, 1, 1,
                    1, 1);
        }

        @Test
        void testWhereSqlStartWithWhereV1() {
            assertVerify(filter, "",
                    mock(Object.class), "Where 1=1",
                    match, false,
                    "Where 1=1",
                    1, 1, 1,
                    1, 1);
        }

        @Test
        void testWhereSqlNotStartWithWhere() {
            assertVerify(filter, "",
                    mock(Object.class), "1=1",
                    match, false,
                    "where 1=1",
                    1, 1, 1,
                    1, 1);
        }
    }

    @Nested
    class GetMapTest {
        @Test
        void testMap() {
            Map<String, Object> map = mock(Map.class);
            String key = "key";
            when(map.get(key)).thenReturn(mock(Object.class));
            Object value = QueryUtil.getMap(map, key);
            Assertions.assertNotNull(value);
        }
        @Test
        void testNotMap() {
            Object map = mock(Object.class);
            String key = "key";
            Object value = QueryUtil.getMap(map, key);
            Assertions.assertNull(value);
        }
    }
}
