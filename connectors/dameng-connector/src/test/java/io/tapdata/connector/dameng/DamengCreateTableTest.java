package io.tapdata.connector.dameng;

import io.tapdata.entity.event.ddl.table.TapCreateTableEvent;
import io.tapdata.entity.schema.TapField;
import io.tapdata.entity.schema.TapTable;
import io.tapdata.entity.simplify.TapSimplify;
import io.tapdata.pdk.apis.context.TapConnectorContext;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;

import static org.mockito.Mockito.*;

public class DamengCreateTableTest {


    @Test
    void testCommentWithSingleQuotation() throws SQLException {

        DamengConnector damengConnector  =new DamengConnector();
        TapConnectorContext connectorContext = Mockito.mock(TapConnectorContext.class);
        TapCreateTableEvent createTableEvent = new TapCreateTableEvent();
        TapTable tapTable = new TapTable();
        tapTable.setId("test");
        createTableEvent.setTable(tapTable);
        LinkedHashMap<String, TapField> nameFieldMap = new LinkedHashMap<>();
        TapField tapField = new TapField();
        String fieldName = "id";
        tapField.setName(fieldName);
        String fieldComment = " primary key'";
        tapField.setComment(fieldComment);
        nameFieldMap.put("id",tapField);
        tapTable.setNameFieldMap(nameFieldMap);
        DamengContext damengContext = Mockito.mock(DamengContext.class);
        when(damengContext.queryAllTables(Collections.singletonList(tapTable.getId()))).thenReturn(new ArrayList<>());
        ReflectionTestUtils.setField(damengConnector,"damengContext",damengContext);
        DamengConfig damengConfig  = Mockito.mock(DamengConfig.class);
        ReflectionTestUtils.setField(damengConnector,"damengConfig",damengConfig);
        when(damengConfig.getSchema()).thenReturn("test");

        ReflectionTestUtils.invokeMethod(damengConnector,"createTableV2",connectorContext,createTableEvent);
        fieldComment = fieldComment.replaceAll("'", "''");
        String expectedData =  "COMMENT ON COLUMN \"" + damengConfig.getSchema() + "\".\"" + tapTable.getId() + "\".\"" + fieldName + "\" IS '" + fieldComment + "'";
        List<String> sqls = TapSimplify.list();
        String sql = "CREATE TABLE \"" + damengConfig.getSchema() + "\".\"" + tapTable.getId() + "\"(";
        sql += ")";
        sqls.add(sql);
        sqls.add(expectedData);
        verify(damengContext, times(1)).batchExecute(sqls);


    }

}
