{"properties": {"name": "Kafka", "icon": "icons/kafka.png", "doc": "${doc}", "id": "kafka", "tags": ["Database", "schema-free"]}, "configOptions": {"supportDDL": {"events": ["new_field_event", "alter_field_name_event", "alter_field_attributes_event", "drop_field_event"]}, "connection": {"type": "object", "properties": {"nameSrvAddr": {"required": true, "type": "string", "title": "${nameSrvAddr}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "mq_nameSrvAddr", "x-decorator-props": {"tooltip": "${nameSrvAddrTip}"}, "x-index": 1}, "mqTopicString": {"type": "string", "title": "${mqTopicString}", "x-decorator": "FormItem", "x-decorator-props": {"tooltip": "${mqTopicStringTooltip}"}, "x-component": "Input", "apiServerKey": "mq_topics", "x-index": 8}, "krb5": {"type": "boolean", "title": "${krb5}", "default": false, "x-decorator": "FormItem", "x-component": "Switch", "apiServerKey": "krb5", "x-reactions": [{"target": "*(krb5<PERSON>eytab,krb5Conf,krb5Principal,krb5ServiceName)", "fulfill": {"state": {"visible": "{{$self.value===true}}"}}}, {"target": "*(mqUsername,mqPassword,kafkaSaslMechanism)", "fulfill": {"state": {"visible": "{{$self.value===false}}"}}}], "x-index": 9}, "krb5Keytab": {"type": "string", "title": "${krb5Keytab}", "x-decorator": "FormItem", "x-component": "TextFileReader", "x-component-props": {"base64": true}, "x-decorator-props": {"tooltip": "${krb5KeytabTip}"}, "apiServerKey": "krb5Keytab", "fileNameField": "krb5KeytabFile", "required": true, "x-index": 20}, "krb5Conf": {"type": "string", "title": "${krb5Conf}", "x-decorator": "FormItem", "x-component": "TextFileReader", "x-component-props": {"base64": true}, "x-decorator-props": {"tooltip": "${krb5ConfTip}"}, "apiServerKey": "krb5Conf", "fileNameField": "krb5ConfFile", "required": true, "x-index": 30}, "krb5Principal": {"type": "string", "title": "${krb5Principal}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "krb5Principal", "x-decorator-props": {"tooltip": "${krb5PrincipalTip}"}, "required": true, "x-index": 40}, "krb5ServiceName": {"type": "string", "title": "${krb5ServiceName}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "krb5ServiceName", "x-decorator-props": {"tooltip": "${krb5ServiceNameTip}"}, "required": true, "x-index": 50}, "mqUsername": {"type": "string", "title": "${mqUsername}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "mq_username", "x-index": 60}, "mqPassword": {"type": "string", "title": "${mqPassword}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "mq_password", "x-index": 70}, "OPTIONAL_FIELDS": {"type": "void", "properties": {"schemaRegister": {"type": "boolean", "title": "${schemaRegister}", "default": false, "x-decorator": "FormItem", "x-decorator-props": {"tooltip": "${schemaRegisterTip}"}, "x-component": "Switch", "apiServerKey": "schemaRegister", "x-reactions": [{"target": "*(schemaRegisterUrl,basicAuth)", "fulfill": {"state": {"visible": "{{$self.value===true}}"}}}], "x-index": 2}, "basicAuth": {"type": "boolean", "title": "${basicAuth}", "default": false, "x-decorator": "FormItem", "x-component": "Switch", "apiServerKey": "basicAuth", "x-reactions": [{"target": "*(authCredentialsSource,authUserName,authPassword)", "fulfill": {"state": {"visible": "{{$self.value===true}}"}}}], "x-index": 4}, "schemaRegisterUrl": {"required": true, "type": "string", "title": "${schemaRegisterUrl}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "schemaRegisterUrl", "x-index": 3}, "authCredentialsSource": {"required": true, "type": "string", "title": "${authCredentialsSource}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "authCredentialsSource", "x-index": 5}, "authUserName": {"required": true, "type": "string", "title": "${authUserName}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_username", "x-index": 6}, "authPassword": {"required": true, "type": "string", "title": "${authPassword}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "database_password", "x-index": 7}, "kafkaSaslMechanism": {"type": "string", "title": "${kafkaSaslMechanism}", "default": "PLAIN", "x-decorator": "FormItem", "x-component": "Select", "apiServerKey": "kafkaSaslMechanism", "x-index": 80, "enum": [{"label": "PLAIN", "value": "PLAIN"}, {"label": "SHA256", "value": "SHA256"}, {"label": "SHA512", "value": "SHA512"}]}, "securityProtocol": {"type": "string", "title": "${securityProtocol}", "default": "SASL_PLAINTEXT", "x-decorator": "FormItem", "x-component": "Select", "apiServerKey": "securityProtocol", "x-index": 90, "enum": [{"label": "SASL_PLAINTEXT", "value": "SASL_PLAINTEXT"}, {"label": "SASL_SSL", "value": "SASL_SSL"}]}, "kafkaIgnoreInvalidRecord": {"type": "boolean", "title": "${kafkaIgnoreInvalidRecord}", "default": false, "x-decorator": "FormItem", "x-component": "Switch", "apiServerKey": "kafkaIgnoreInvalidRecord", "x-index": 100}, "kafkaAcks": {"type": "string", "title": "${kafkaAcks}", "default": "-1", "x-decorator": "FormItem", "x-component": "Select", "apiServerKey": "kafkaAcks", "x-index": 110, "enum": [{"label": "${not_sure}", "value": "0"}, {"label": "${just_write_master}", "value": "1"}, {"label": "${write_most_isr}", "value": "-1"}, {"label": "${write_all_isr}", "value": "all"}]}, "kafkaCompressionType": {"type": "string", "title": "${kafkaCompressionType}", "default": "lz4", "x-decorator": "FormItem", "x-component": "Select", "apiServerKey": "kafkaCompressionType", "x-index": 120, "enum": [{"label": "${kafkaCompressionTypeUncompressed}", "value": "uncompressed"}, {"label": "gzip", "value": "gzip"}, {"label": "snappy", "value": "snappy"}, {"label": "lz4", "value": "lz4"}, {"label": "zstd", "value": "zstd"}]}, "kafkaIgnorePushError": {"type": "boolean", "title": "${kafkaIgnorePushError}", "default": false, "x-decorator": "FormItem", "x-component": "Switch", "apiServerKey": "kafkaIgnorePushError", "x-index": 130}}}}}, "node": {"properties": {"enableScript": {"title": "${enableScript}", "type": "boolean", "default": false, "x-decorator": "FormItem", "x-component": "Switch", "x-decorator-props": {"tooltip": "${scriptTooltip}"}}, "partitionNum": {"required": true, "type": "string", "title": "${partitionNum}", "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "partitionNum", "default": 3, "x-decorator-props": {"tooltip": "${partitionNumTooltip}"}, "x-component-props": {"min": 1, "max": 255}}, "replicasSize": {"required": true, "type": "string", "title": "${replicasSize}", "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "replicasSize", "default": 1, "x-decorator-props": {"tooltip": "${replicasSizeTooltip}"}, "x-component-props": {"min": 1, "max": 255}}, "topicName": {"type": "string", "title": "${topicName}", "x-decorator": "FormItem", "x-component": "Input", "x-decorator-props": {"tooltip": "${topicNameTooltip}"}, "x-reactions": [{"dependencies": ["$inputs"], "fulfill": {"state": {"display": "{{$deps[0].length > 0 ? \"visible\":\"hidden\"}}"}}}]}, "script": {"type": "string", "required": false, "default": "function process(record, op, conditionKeys){\n\n\t// Enter your code at here\n\t return record;\n}", "x-decorator": "FormItem", "x-component": "JsEditor", "x-component-props": {"options": {"showPrintMargin": false, "useWrapMode": true}, "includeBeforeAndAfter": true, "before": "function process(record, op, conditionKeys){", "beforeRegexp": "^[^]*function\\s+process\\s*\\(record, op, conditionKeys\\)\\{", "afterRegexp": "}[^}]*$", "after": "}"}, "x-reactions": {"dependencies": ["nodeConfig.enableScript"], "fulfill": {"schema": {"x-decorator-props.style.display": "{{$deps[0]===true ? null:\"none\"}}"}}}}}}}, "messages": {"default": "en_US", "en_US": {"partitionNumTooltip": "If Topic exists, the number of partitions can only increase, not decrease", "replicasSizeTooltip": "The number of replica sets cannot be changed after the Topic is created. The number of replica sets at creation cannot be greater than the number of Kafka clusters", "replicasSize": "Number of replications", "partitionNum": "Number of partitions", "doc": "docs/kafka_en_US.md", "nameSrvAddr": "Kafka Host", "nameSrvAddrTip": "Kafka host address, multiple addresses are separated by commas, such as *************:9092,*************:9092", "mqTopicString": "topic expression", "mqTopicStringTooltip": "Multiple topics are separated by commas, support *? wildcard expression (non regular)", "krb5": "kerberos authentication", "krb5Keytab": "Key representation file", "krb5KeytabTip": "Keytab file, which can achieve SSH login free", "krb5Conf": "Configuration file", "krb5ConfTip": "Kerberos configuration file", "krb5Principal": "Body Configuration", "krb5PrincipalTip": "for example: kafka/hostname@REALM", "krb5ServiceName": "Service name", "krb5ServiceNameTip": "Service name, usually kafka", "mqUsername": "User Name", "mqPassword": "Password", "kafkaSaslMechanism": "Encryption", "kafkaIgnoreInvalidRecord": "Ignore non-JSON Object format messages", "kafkaAcks": "ACK confirmation mechanism", "not_sure": "Do not confirm", "just_write_master": "Write to master partition only", "write_most_isr": "Write to most ISR partitions", "write_all_isr": "Write to all ISR partitions", "kafkaCompressionType": "Message compression type", "kafkaCompressionTypeUncompressed": "uncompressed", "kafkaIgnorePushError": "Ignore push message exception", "schemaRegister": "schemaRegister", "schemaRegisterTip": "To register a center using third-party mode, an additional address is required. If basic authentication is enabled, authentication information is also required", "schemaRegisterUrl": "schemaRegisterUrl", "authCredentialsSource": "authCredentialsSource", "authUserName": "authUserName", "authPassword": "authPassword", "basicAuth": "basicAuth", "enableScript": "Custom message body format", "topicName": "Topic Name", "topicNameTooltip": "Unified topic name, if filled in, the data of all tables of this task will be written to this topic", "script": "script", "scriptTooltip": "1. Parameter Description:\n· 'record' is the data record, including 'header' and 'data', where 'data' contains 'before' and 'after'.\n· 'op' represents the operation type, with a total of 3 options: insert, update, and delete.\n· 'conditionKeys' is the collection of primary key field names.\n2. Examples:\n· Add the operation type to the Kafka message: record.data.op = op\n· Add a custom header: record.header.test='test header'\n· Delete the 'before' field: delete record.data.before\n· Discard the data record: return null", "securityProtocol": "security Protocol"}, "zh_CN": {"partitionNumTooltip": "若Topic存在，分区的数量只能增加不能减小", "replicasSizeTooltip": "副本集的数量在Topic创建后不可更改。并且创建时副本集的数量不能大于Kafka集群的数量。", "replicasSize": "副本数数量", "partitionNum": "分区数量", "doc": "docs/kafka_zh_CN.md", "nameSrvAddr": "Kafka地址", "nameSrvAddrTip": "Kafka地址，多个地址用逗号分隔，如：*************:9092,*************:9092", "mqTopicString": "主题表达式", "mqTopicStringTooltip": "多个主题用逗号分隔，支持*?通配表达式（非正则）", "krb5": "kerberos 认证", "krb5Keytab": "密钥表示文件", "krb5KeytabTip": "keytab文件，可以实现ssh免登录", "krb5Conf": "配置文件", "krb5ConfTip": "krb5的conf配置文件", "krb5Principal": "主体配置", "krb5PrincipalTip": "例如：kafka/hostname@REALM", "krb5ServiceName": "服务名", "krb5ServiceNameTip": "服务名，一般均为kafka", "mqUsername": "账号", "mqPassword": "密码", "kafkaSaslMechanism": "加密方式", "kafkaIgnoreInvalidRecord": "忽略非JSON对象格式消息", "kafkaAcks": "ACK确认机制", "not_sure": "不确认", "just_write_master": "仅写入master分区", "write_most_isr": "写入大多数ISR分区", "write_all_isr": "写入所有ISR分区", "kafkaCompressionType": "消息压缩类型", "kafkaCompressionTypeUncompressed": "不压缩", "kafkaIgnorePushError": "忽略推送消息异常", "schemaRegister": "模式注册", "schemaRegisterTip": "使用第三方模式注册中心，需要额外提供地址，若打开基本认证还需要提供认证信息等", "schemaRegisterUrl": "模式注册地址", "authCredentialsSource": "认证凭据来源", "authUserName": "认证用户名", "authPassword": "认证密码", "basicAuth": "基本认证", "enableScript": "自定义消息体格式", "topicName": "Topic名称", "topicNameTooltip": "统一topic名称，如果填写内容后，该任务所有表的数据都会写入该topic内", "script": "脚本", "scriptTooltip": "1.参数说明\n·record为每条数据记录，包含header和data，data中包含before和after\n·op为操作类型，共3种：insert，update，delete\n·conditionKeys为主键字段名集合\n2.示例\n·kafka message中添加操作类型: record.data.op = op\n·添加自定义header: record.header.test='test header'\n·删除before: delete record.data.before\n·丢弃该条数据: return null", "securityProtocol": "加密协议"}, "zh_TW": {"partitionNumTooltip": "若Topic存在，分區的數量只能增加不能減小", "replicasSizeTooltip": "副本集的數量在Topic創建後不能更改，並且創建時副本數的數量不能大於Kafka集群的數量。", "replicasSize": "副本數數量", "partitionNum": "分區數量", "doc": "docs/kafka_zh_TW.md", "nameSrvAddr": "Kafka地址", "nameSrvAddrTip": "Kafka地址，多個地址用逗號分隔，如：*************:9092,*************:9092", "mqTopicString": "主題表達式", "mqTopicStringTooltip": "多個主題用逗號分隔，支持*?通配運算式（非正則）", "krb5": "kerberos 認證", "krb5Keytab": "密鑰表示文件", "krb5KeytabTip": "keytab文件，可以實現ssh免登錄", "krb5Conf": "配置文件", "krb5ConfTip": "krb5的conf配置文件", "krb5Principal": "主体配置", "krb5PrincipalTip": "例如：kafka/hostname@REALM", "krb5ServiceName": "服務名", "krb5ServiceNameTip": "服務名，一般均為kafka", "mqUsername": "賬號", "mqPassword": "密碼", "kafkaSaslMechanism": "加密方式", "kafkaIgnoreInvalidRecord": "忽略非JSON對象格式消息", "kafkaAcks": "ACK確認機制", "not_sure": "不確認", "just_write_master": "僅寫入master分區", "write_most_isr": "寫入大多數ISR分區", "write_all_isr": "寫入所有ISR分區", "kafkaCompressionType": "消息壓縮類型", "kafkaCompressionTypeUncompressed": "不壓縮", "kafkaIgnorePushError": "忽略推理消息異常", "schemaRegister": "模式註冊", "schemaRegisterTip": "使用第三方模式註冊中心，需要額外提供地址，若打開基本認證還需要提供認證信息等", "schemaRegisterUrl": "模式註冊地址", "authCredentialsSource": "認證憑據來源", "authUserName": "認證用戶名", "authPassword": "認證密碼", "basicAuth": "基本認證", "enableScript": "自定義消息體格式", "topicName": "Topic名稱", "topicNameTooltip": "統一topic名稱，如果填寫內容後，該任務所有表的數據都會寫入該topic內", "script": "腳本", "scriptTooltip": "1.參數說明\n·record為每條數據記錄，包含header和data，data中包含before和after\n·op為操作類型，共有 3 種：insert，update，delete\n·conditionKeys為主鍵字段名集合\n2.示例\n·在 Kafka message中添加操作類型：record.data.op = op\n·添加自訂header：record.header.test = 'test header'\n·刪除before：delete record.data.before\n·丟棄該條數據：return null", "securityProtocol": "加密協議"}}, "dataTypes": {"OBJECT": {"to": "TapMap"}, "ARRAY": {"to": "TapArray"}, "NUMBER": {"precision": [1, 1000], "scale": [0, 1000], "fixed": true, "preferPrecision": 20, "preferScale": 8, "priority": 1, "to": "TapNumber"}, "INTEGER": {"bit": 32, "priority": 1, "value": [-2147483648, 2147483647], "to": "TapNumber"}, "BOOLEAN": {"to": "TapBoolean"}, "STRING": {"byte": 200, "priority": 1, "defaultByte": 200, "preferByte": 200, "to": "TapString"}, "TEXT": {"to": "TapString"}}}