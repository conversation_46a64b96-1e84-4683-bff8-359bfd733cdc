package io.tapdata.connector.iris;

import io.tapdata.common.JdbcContext;
import io.tapdata.connector.iris.config.IrisConfig;
import io.tapdata.kit.EmptyKit;
import io.tapdata.kit.StringKit;

import java.util.List;


public class IrisJdbcContext extends JdbcContext {
    public IrisJdbcContext(IrisConfig config) {
        super(config);
    }

    @Override
    protected String queryAllTablesSql(String schema, List<String> tableNames) {
        String tableSql = EmptyKit.isNotEmpty(tableNames) ? "AND TABLE_NAME IN (" + StringKit.joinString(tableNames, "'", ",") + ")" : "";
        String IRIS_ALL_TABLE = "SELECT TABLE_NAME as tableName, DESCRIPTION as tableComment FROM information_schema.tables WHERE TABLE_SCHEMA = '%s' %s";
        return String.format(IRIS_ALL_TABLE, schema, tableSql);
    }


    @Override
    protected String queryAllColumnsSql(String schema, List<String> tableNames) {
        String tableSql = EmptyKit.isNotEmpty(tableNames) ? "AND TABLE_NAME IN (" + StringKit.joinString(tableNames, "'", ",") + ")" : "";
        String IRIS_ALL_COLUMN = "SELECT TABLE_NAME as tableName, COLUMN_NAME as columnName, DATA_TYPE as dataType, " +
                "CHARACTER_MAXIMUM_LENGTH as dataLength, NUMERIC_PRECISION as dataPrecision, NUMERIC_SCALE as dataScale," +
                "IS_NULLABLE as nullable, DESCRIPTION as columnComment FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '%s' %s";
        return String.format(IRIS_ALL_COLUMN, schema, tableSql);
    }

    @Override
    protected String queryAllIndexesSql(String schema, List<String> tableNames) {
        String tableSql = EmptyKit.isNotEmpty(tableNames) ? "AND INDEXES.TABLE_NAME IN (" + StringKit.joinString(tableNames, "'", ",") + ")" : "";
        String IRIS_ALL_INDEX = "SELECT INDEXES.TABLE_NAME as tableName, INDEXES.INDEX_NAME as indexName, (CASE WHEN ASC_OR_DESC = 'A' THEN 1 ELSE 0 END) as isAsc, (CASE WHEN NON_UNIQUE = 0 THEN 1 ELSE 0 END) as isUnique, PRIMARY_KEY as isPk, INDEXES.COLUMN_NAME as columnName  FROM information_schema.INDEXES WHERE INDEXES.TABLE_SCHEMA = '%s' %s";
        return String.format(IRIS_ALL_INDEX, schema, tableSql);
    }
}