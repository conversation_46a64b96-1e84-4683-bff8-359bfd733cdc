{"name": "tapdata-web", "version": "4.0.0", "private": true, "packageManager": "pnpm@10.7.0", "type": "module", "scripts": {"start:daas": "pnpm --filter daas start:test", "start:cloud": "pnpm --filter cloud start:test", "start:local": "pnpm --filter cloud start:local", "dev:daas": "pnpm --filter daas start:dev", "dev:cloud": "pnpm --filter cloud start:dev", "taptest:daas": "pnpm --filter daas taptest", "build:daas": "pnpm --filter daas build", "build:cloud": "pnpm --filter cloud build", "daas": "pnpm --filter daas start:test", "cloud": "cross-env NODE_OPTIONS=--openssl-legacy-provider pnpm --filter cloud start", "clean": "rm -rf node_modules && pnpm -r exec -- rm -rf node_modules", "oss": "cross-env NODE_OPTIONS=--openssl-legacy-provider pnpm --filter daas start:dev --mode oss", "dev:oss": "pnpm --filter daas start:dev --mode oss", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "format": "prettier --write src/", "nb": "node scripts/worktree-checkout.js"}, "dependencies": {"element-plus": "^2.9.10", "lodash-es": "^4.17.21", "vue-router": "^4.0.8"}, "devDependencies": {"@iconify-json/lucide": "^1.2.45", "@rushstack/eslint-patch": "^1.8.0", "@sxzz/eslint-config": "^6.1.1", "@sxzz/prettier-config": "^2.2.1", "@tsconfig/node20": "^20.1.4", "@types/jsdom": "^21.1.7", "@types/lodash-es": "^4.17.12", "@types/node": "^20.14.5", "@types/qs": "^6.9.15", "@vue/tsconfig": "^0.5.1", "chalk": "5", "cross-env": "^7.0.3", "eslint": "^9.23.0", "file-loader": "^6.2.0", "inquirer": "9", "inquirer-autocomplete-prompt": "^3.0.1", "jsdom": "^24.1.0", "npm-run-all2": "^6.2.0", "prettier": "^3.5.3", "typescript": "^5.4.5", "vite": "^6.2.4", "vue-tsc": "^2.0.21"}, "pnpm": {"overrides": {"@formily/core": "^2.3.0", "@formily/element-plus": "^1.1.0", "@formily/json-schema": "^2.3.0", "@formily/path": "^2.3.0", "@formily/reactive": "^2.3.0", "@formily/reactive-vue": "^2.3.0", "@formily/shared": "^2.3.0", "@formily/vue": "^2.3.0", "element-plus": "^2.9.11", "vue": "^3.4.29", "vue-virtual-scroller": "2.0.0-beta.8"}, "patchedDependencies": {"@formily/element-plus@1.1.0": "patches/@<EMAIL>"}}, "prettier": "@sxzz/prettier-config"}