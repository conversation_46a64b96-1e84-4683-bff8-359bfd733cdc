<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>io.tapdata</groupId>
		<artifactId>tapdata-common-lib</artifactId>
		<version>2.1-SNAPSHOT</version>
	</parent>
	<version>2.1-SNAPSHOT</version>
    <artifactId>plugin-kit</artifactId>
    <name>plugin-kit</name>
    <packaging>pom</packaging>
    <modules>
		<module>tapdata-api</module>
		<module>tapdata-pdk-api</module>
		<module>tapdata-pdk-runner</module>
<!--        <module>tapdata-tests</module>-->
		<module>connector-archetypes</module>
        <module>tapdata-common</module>
		<module>tapdata-modules</module>
		<module>tapdata-proxy</module>
    </modules>

	<properties>
		<proxy.file.name>${project.artifactId}-v${project.version}</proxy.file.name>
		<connector.file.name>${project.artifactId}-v${project.version}</connector.file.name>
		<tapdata.pdk.runner.verison>2.1-SNAPSHOT</tapdata.pdk.runner.verison>
		<tapdata.api.verison>2.0.1-SNAPSHOT</tapdata.api.verison>
		<tapdata.pdk.api.verison>2.0.1-SNAPSHOT</tapdata.pdk.api.verison>
		<tapdata.pdk.connector.core.version>1.0-SNAPSHOT</tapdata.pdk.connector.core.version>
		<junit.jupiter.version>5.8.1</junit.jupiter.version>
		<junit.platform.version>1.8.1</junit.platform.version>
		<mockito.version>4.11.0</mockito.version>
		<mockito.junit.jupiter.version>4.11.0</mockito.junit.jupiter.version>
		<error-code-core.version>2.1-SNAPSHOT</error-code-core.version>
		<error-code-scanner.version>2.1-SNAPSHOT</error-code-scanner.version>
		<plugin-parent>2.1-SNAPSHOT</plugin-parent>
		<tapdata.modules.version>2.1-SNAPSHOT</tapdata.modules.version>
		<tapdata.proxy.version>2.1-SNAPSHOT</tapdata.proxy.version>
		<commons-io.version>2.18.0</commons-io.version>
		<commons.lang3.version>3.12.0</commons.lang3.version>
		<fastjson.version>1.2.83</fastjson.version>
		<commons.collections4.version>4.4</commons.collections4.version>
		<log4j.version>2.17.1</log4j.version>
		<reflections.version>0.10.2</reflections.version>
		<hutool-all.version>5.8.25</hutool-all.version>
		<commons-compress.version>1.26.0</commons-compress.version>
		<junit.version>4.13.1</junit.version>
		<bcprov-jdk18on.version>1.80</bcprov-jdk18on.version>
		<guava.version>33.4.0-jre</guava.version>
		<netty-all.version>4.1.118.Final</netty-all.version>
	</properties>
    <dependencies>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-suite</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-launcher</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
        </dependency>
    </dependencies>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>io.tapdata</groupId>
				<artifactId>tapdata-api</artifactId>
				<version>${tapdata.api.verison}</version>
			</dependency>
			<dependency>
				<groupId>io.tapdata</groupId>
				<artifactId>tapdata-pdk-api</artifactId>
				<version>${tapdata.pdk.api.verison}</version>
			</dependency>
			<dependency>
				<groupId>io.tapdata</groupId>
				<artifactId>tapdata-pdk-runner</artifactId>
				<version>${tapdata.pdk.runner.verison}</version>
			</dependency>
			<dependency>
				<groupId>io.tapdata</groupId>
				<artifactId>tapdata-modules</artifactId>
				<version>${tapdata.modules.version}</version>
			</dependency>
			<dependency>
				<groupId>io.tapdata</groupId>
				<artifactId>connector-core</artifactId>
				<version>${tapdata.pdk.connector.core.version}</version>
			</dependency>
			<dependency>
				<groupId>io.tapdata</groupId>
				<artifactId>modules-api</artifactId>
				<version>${tapdata.modules.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>${commons-io.version}</version>
			</dependency>
			<dependency>
				<groupId>org.reflections</groupId>
				<artifactId>reflections</artifactId>
				<version>${reflections.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-all</artifactId>
				<version>${hutool-all.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-compress</artifactId>
				<version>${commons-compress.version}</version>
			</dependency>
			<dependency>
				<groupId>org.bouncycastle</groupId>
				<artifactId>bcprov-jdk18on</artifactId>
				<version>${bcprov-jdk18on.version}</version>
			</dependency>
			<dependency>
				<groupId>io.netty</groupId>
				<artifactId>netty-all</artifactId>
				<version>${netty-all.version}</version>
			</dependency>

			<dependency>
				<groupId>org.junit.jupiter</groupId>
				<artifactId>junit-jupiter</artifactId>
				<version>${junit.jupiter.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.junit.jupiter</groupId>
				<artifactId>junit-jupiter-engine</artifactId>
				<version>${junit.jupiter.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.junit.jupiter</groupId>
				<artifactId>junit-jupiter-api</artifactId>
				<version>${junit.jupiter.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.junit.jupiter</groupId>
				<artifactId>junit-jupiter-params</artifactId>
				<version>${junit.jupiter.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.junit.platform</groupId>
				<artifactId>junit-platform-suite</artifactId>
				<version>${junit.platform.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.junit.platform</groupId>
				<artifactId>junit-platform-launcher</artifactId>
				<version>${junit.platform.version}</version>
				<scope>test</scope>
			</dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-junit-jupiter</artifactId>
                <version>${mockito.junit.jupiter.version}</version>
                <scope>test</scope>
            </dependency>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>${guava.version}</version>
			</dependency>
			<dependency>
				<groupId>io.tapdata</groupId>
				<artifactId>error-code-core</artifactId>
				<version>${error-code-core.version}</version>
			</dependency>
			<dependency>
				<groupId>io.tapdata</groupId>
				<artifactId>error-code-scanner</artifactId>
				<version>${error-code-scanner.version}</version>
			</dependency>
			<dependency>
				<groupId>io.tapdata</groupId>
				<artifactId>pdk-error-code</artifactId>
				<version>${error-code-core.version}</version>
			</dependency>
			<dependency>
				<groupId>junit</groupId>
				<artifactId>junit</artifactId>
				<version>${junit.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>4.2.1</version>
                <extensions>true</extensions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                </configuration>
            </plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>properties-maven-plugin</artifactId>
				<version>1.1.0</version>
				<executions>
					<execution>
						<phase>generate-resources</phase>
						<goals>
							<goal>write-project-properties</goal>
						</goals>
						<configuration>
							<outputFile>${basedir}/../iengine/iengine-app/src/main/resources/pluginKit.properties</outputFile>
						</configuration>
					</execution>
				</executions>
			</plugin>
        </plugins>
    </build>
	<repositories>
		<repository>
			<id>tapdata-tapdata-maven</id>
			<name>maven</name>
			<url>https://tapdata-maven.pkg.coding.net/repository/tapdata/maven/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>
	<distributionManagement>
		<repository>
			<!--必须与 settings.xml 的 id 一致-->
			<id>tapdata-tapdata-maven</id>
			<name>maven</name>
			<url>https://tapdata-maven.pkg.coding.net/repository/tapdata/maven/</url>
		</repository>
	</distributionManagement>
</project>

