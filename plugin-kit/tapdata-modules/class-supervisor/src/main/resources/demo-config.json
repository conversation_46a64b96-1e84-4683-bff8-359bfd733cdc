{"info": {"url": "https://www.cnblogs.com/Java-Script/p/11091330.html"}, "classConfigurations": [{"target": [{"type": "extends", "path": "java.lang.Closable", "scanPackage": "*", "isCreate": false}, {"type": "path", "path": "java.lang.Thread", "isCreate": false, "ignore": true}, {"type": "name", "scanPackage": "java.lang", "path": "%Thread%", "isCreate": false, "saveTo": "java.lang", "ignore": true}, {"type": "name", "path": "%Thread_", "isCreate": false, "saveTo": "java.lang", "ignore": true}, {"type": "name", "path": "_Thread_", "isCreate": false, "saveTo": "java.lang", "ignore": true}, {"type": "name", "path": "Thread%", "isCreate": false, "saveTo": "java.lang", "ignore": true}], "method": [{"ignore": true, "isCreate": false, "createWith": "", "name": "run", "args:": [], "returnType": "void", "code": [{"type": "normal", "index": 1, "isAppend": true, "line": "System.out.println(\"2: this is hack code! Be careful please!\");"}, {"type": "before", "line": "System.out.println(\"1: this is hack code! Be careful please!\");"}, {"type": "after", "line": "System.out.println(\"3: this is hack code! Be careful please!\");", "isFinally": true, "isRedundant": false}, {"type": "catch", "line": "System.out.println(\"0: this is hack code! Be careful please!\" + $e.getMessage());throw $e;", "exception": {"path": "java.lang.Throwable", "name": "$e"}}]}, {"ignore": false, "isCreate": true, "createWith": "{super.close();}", "name": "close", "args:": [], "returnType": "void", "code": [{"type": "before", "line": "System.out.println(\"1: this is hack code! Be careful please!\");"}, {"type": "after", "line": "System.out.println(\"3: this is hack code! Be careful please!\");", "isFinally": true, "isRedundant": false}]}], "constructor": [{"ignore": false, "isCreate": false, "args": "*", "type": ["void"], "returnType": "void", "code": [{"type": "before", "line": "System.out.println(\"1: this is hack code! Be careful please!\");"}, {"type": "after", "line": "System.out.println(\"3: this is hack code! Be careful please!\");", "isFinally": true, "isRedundant": false}]}]}]}