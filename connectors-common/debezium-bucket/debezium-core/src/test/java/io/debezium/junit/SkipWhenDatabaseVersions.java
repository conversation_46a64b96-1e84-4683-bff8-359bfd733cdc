/*
 * Copyright Debezium Authors.
 *
 * Licensed under the Apache Software License version 2.0, available at http://www.apache.org/licenses/LICENSE-2.0
 */
package io.debezium.junit;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Collects together multiple {@link SkipWhenDatabaseVersion} annotations.
 *
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ ElementType.TYPE, ElementType.METHOD })
public @interface SkipWhenDatabaseVersions {

    /**
     * Specifies the versions the test should be skipped for.
     */
    SkipWhenDatabaseVersion[] value();
}
