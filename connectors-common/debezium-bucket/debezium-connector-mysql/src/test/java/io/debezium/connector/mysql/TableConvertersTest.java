/*
 * Copyright Debezium Authors.
 *
 * Licensed under the Apache Software License version 2.0, available at http://www.apache.org/licenses/LICENSE-2.0
 */
package io.debezium.connector.mysql;

import static org.junit.Assert.fail;

import org.junit.Ignore;
import org.junit.Test;

/**
 * <AUTHOR>
 */
@Ignore
public class TableConvertersTest {

    @Test
    public void shouldHandleMetadataEventToUpdateTables() {
        fail("Not yet implemented");
    }

    @Test
    public void shouldProduceSourceRecorForMetadataEventWhenConfigured() {
        fail("Not yet implemented");
    }

    @Test
    public void shouldProduceSourceRecorForInsertEvent() {
        fail("Not yet implemented");
    }

    @Test
    public void shouldProduceSourceRecorForUpdateEvent() {
        fail("Not yet implemented");
    }

    @Test
    public void shouldProduceSourceRecorForDeleteEvent() {
        fail("Not yet implemented");
    }

}
