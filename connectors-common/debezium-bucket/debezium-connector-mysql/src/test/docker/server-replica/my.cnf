# For advice on how to change settings please see
# http://dev.mysql.com/doc/refman/5.7/en/server-configuration-defaults.html

# --------------------------------------------------------------------------------------------
# This section specifies 5.5 and cross-version common configurations
# --------------------------------------------------------------------------------------------
[mysqld]
#
# Remove leading # and set to the amount of RAM for the most important data
# cache in MySQL. Start at 70% of total RAM for dedicated server, else 10%.
# innodb_buffer_pool_size = 128M
#
# Remove leading # to turn on a very important data integrity option: logging
# changes to the binary log between backups.
# log_bin
#
# Remove leading # to set options mainly useful for reporting servers.
# The server defaults are faster for transactions and fast SELECTs.
# Adjust sizes as needed, experiment to find the optimal values.
# join_buffer_size = 128M
# sort_buffer_size = 2M
# read_rnd_buffer_size = 2M
skip-host-cache
skip-name-resolve
#datadir=/var/lib/mysql
#socket=/var/lib/mysql/mysql.sock
#secure-file-priv=/var/lib/mysql-files
user=mysqlreplica

# Disabling symbolic-links is recommended to prevent assorted security risks
symbolic-links=0

#log-error=/var/log/mysqld.log
#pid-file=/var/run/mysqld/mysqld.pid

# ----------------------------------------------
# Enable GTIDs on this primary server
# ----------------------------------------------
log_slave_updates         = on

# Enable binary replication log and set the prefix, expiration, and log format.
# The prefix is arbitrary, expiration can be short for integration tests but would
# be longer on a production system. Row-level info is required for ingest to work.
# Server ID is required, but this will vary on production systems
server-id         = 445566
log_bin           = mysql-bin-slave
expire_logs_days  = 3
binlog_format     = row

# --------------------------------------------------------------------------------------------
# This section specifies 5.6 specific configurations
# --------------------------------------------------------------------------------------------
[mysqld-5.6]
gtid_mode                 = on
enforce_gtid_consistency  = on

default_authentication_plugin = mysql_native_password

# --------------------------------------------------------------------------------------------
# This section specifies 5.7 specific configurations
# --------------------------------------------------------------------------------------------
[mysqld-5.7]
gtid_mode                 = on
enforce_gtid_consistency  = on

default_authentication_plugin = mysql_native_password

# --------------------------------------------------------------------------------------------
# This section specifies 8.0 specific configurations
# --------------------------------------------------------------------------------------------
[mysqld-8.0]
gtid_mode                 = on
enforce_gtid_consistency  = on

default_authentication_plugin = mysql_native_password