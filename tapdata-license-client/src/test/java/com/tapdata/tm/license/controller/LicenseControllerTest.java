package com.tapdata.tm.license.controller;

import com.tapdata.tm.base.dto.ResponseMessage;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.license.dto.LicenseDto;
import com.tapdata.tm.license.repository.LicenseRepository;
import com.tapdata.tm.license.service.LicenseService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * create at 2025/2/20 11:01
 */
public class LicenseControllerTest {

	@Test
	void testGetFeatures() {

		LicenseService licenseService = new LicenseService(mock(LicenseRepository.class));
		LicenseController licenseController = new LicenseController();
		licenseController.setLicenseService(licenseService);

		LicenseController licenseControllerSpy = spy(licenseController);
		doReturn(mock(UserDetail.class)).when(licenseControllerSpy).getLoginUser();

		ResponseMessage<LicenseDto> features = licenseControllerSpy.getFeatures();

		Assertions.assertNotNull(features);
		Assertions.assertEquals(ResponseMessage.OK, features.getCode());
	}
}
