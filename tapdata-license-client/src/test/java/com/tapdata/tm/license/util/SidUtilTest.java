package com.tapdata.tm.license.util;

import lombok.SneakyThrows;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.lang.reflect.Method;
import java.net.InetAddress;
import java.net.InterfaceAddress;
import java.net.NetworkInterface;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Vector;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class SidUtilTest {
	@Nested
	class GetAllIpsTest{
		@Test
		@DisplayName("get all ips test for private ip")
		void test1(){
			NetworkInterface networkInterface = mock(NetworkInterface.class);
			Vector v = new Vector();
			v.addElement(networkInterface);
			Enumeration<NetworkInterface> interfaces = v.elements();
			InetAddress address = mock(InetAddress.class);
			Vector vn = new Vector();
			vn.addElement(address);
			Enumeration<InetAddress> addresses = vn.elements();
			List<InterfaceAddress> interfaceAddresses = new ArrayList<>();
			InterfaceAddress interfaceAddress = mock(InterfaceAddress.class);
			interfaceAddresses.add(interfaceAddress);
			try (MockedStatic<NetworkInterface> mb = Mockito
				.mockStatic(NetworkInterface.class)) {
				mb.when(NetworkInterface::getNetworkInterfaces).thenReturn(interfaces);
				when(networkInterface.getInetAddresses()).thenReturn(addresses);
				when(address.isLoopbackAddress()).thenReturn(false);
				when(address.isSiteLocalAddress()).thenReturn(false);
				InetAddress currentAddress = mock(InetAddress.class);
				when(networkInterface.getInterfaceAddresses()).thenReturn(interfaceAddresses);
				when(interfaceAddress.getAddress()).thenReturn(currentAddress);
				when(currentAddress.isLoopbackAddress()).thenReturn(false);
				when(interfaceAddress.getBroadcast()).thenReturn(currentAddress);
				List<String> allIps = SidUtil.getAllIps();
				assertEquals(1,allIps.size());
			}
		}
		@Test
		@SneakyThrows
		@DisplayName("get all ips test for public ip")
		void test2(){
			NetworkInterface networkInterface = mock(NetworkInterface.class);
			Vector v = new Vector();
			v.addElement(networkInterface);
			Enumeration<NetworkInterface> interfaces = v.elements();
			Vector v1 = new Vector();
			v1.addElement(networkInterface);
			Enumeration<NetworkInterface> interfaces1 = v1.elements();
			InetAddress address = mock(InetAddress.class);
			Vector vn = new Vector();
			vn.addElement(address);
			Enumeration<InetAddress> addresses = vn.elements();
			List<InterfaceAddress> interfaceAddresses = new ArrayList<>();
			InterfaceAddress interfaceAddress = mock(InterfaceAddress.class);
			interfaceAddresses.add(interfaceAddress);
			try (MockedStatic<NetworkInterface> mb = Mockito
				.mockStatic(NetworkInterface.class)) {
				mb.when(NetworkInterface::getNetworkInterfaces).thenReturn(interfaces).thenReturn(interfaces1);
				when(networkInterface.getInetAddresses()).thenReturn(addresses);
				when(address.isLoopbackAddress()).thenReturn(false);
				when(address.isSiteLocalAddress()).thenReturn(false);
				InetAddress currentAddress = mock(InetAddress.class);
				when(networkInterface.getInterfaceAddresses()).thenReturn(interfaceAddresses);
				when(interfaceAddress.getAddress()).thenReturn(currentAddress);
				when(currentAddress.isLoopbackAddress()).thenReturn(false);
				when(interfaceAddress.getBroadcast()).thenReturn(currentAddress);
				List<String> allIps = SidUtil.getAllIps();
				assertEquals(1,allIps.size());
			}
		}
	}
}
