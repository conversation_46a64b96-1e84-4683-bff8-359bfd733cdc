package com.tapdata.tm.license.service;

import cn.hutool.crypto.digest.DigestUtil;
import com.google.common.collect.ImmutableMap;
import com.tapdata.tm.base.dto.Field;
import com.tapdata.tm.base.dto.Filter;
import com.tapdata.tm.base.dto.Where;
import com.tapdata.tm.base.exception.BizException;
import com.tapdata.tm.base.service.BaseService;
import com.tapdata.tm.commons.dag.Node;
import com.tapdata.tm.commons.dag.nodes.DataParentNode;
import com.tapdata.tm.commons.schema.DataSourceConnectionDto;
import com.tapdata.tm.commons.task.dto.TaskDto;
import com.tapdata.tm.config.security.UserDetail;
import com.tapdata.tm.ds.service.impl.DataSourceService;
import com.tapdata.tm.license.dto.TaskPipelineLimitDto;
import com.tapdata.tm.license.entity.TaskPipelineLimitEntity;
import com.tapdata.tm.license.repository.TaskPipelineLimitRepository;
import com.tapdata.tm.lock.annotation.Lock;
import com.tapdata.tm.lock.constant.LockType;
import com.tapdata.tm.task.service.TaskService;
import com.tapdata.tm.user.service.UserService;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.tapdata.tm.task.service.TaskServiceImpl.*;

@Service
@Slf4j
public class TaskPipelineLimitService extends BaseService<TaskPipelineLimitDto, TaskPipelineLimitEntity, ObjectId, TaskPipelineLimitRepository> {
	@Autowired
	private TaskService taskService;
	@Autowired
	private UserService userService;
	@Autowired
	private DataSourceService dataSourceService;
	private Map<String, List<String>> pipelineAndTaskIdsMap;
	private static final String TASK_SINGLETON_LOCK = "456e5856-d6d5-4276-8f90-fcb78c86a9bd";
	private Integer datasourcePipelineLimit;

	public TaskPipelineLimitService(@NonNull TaskPipelineLimitRepository repository) {
		super(repository, TaskPipelineLimitDto.class, TaskPipelineLimitEntity.class);
	}

	@Override
	protected void beforeSave(TaskPipelineLimitDto dto, UserDetail userDetail) {

	}


	@Lock(value = TASK_SINGLETON_LOCK, type = LockType.PIPELINE_LIMIT, expireSeconds = 30, valueType = Lock.CUSTOM)
	public boolean recordTaskPipelineGrant(TaskDto taskDto, Integer datasourcePipelineLimit, UserDetail user) {
		this.datasourcePipelineLimit = datasourcePipelineLimit;
		cleanInvalidTaskOccupation();
		cleanInvalidPipeline();
		cleanCurrentTaskOccupation(taskDto);
		return grantForCurrentTask(taskDto, datasourcePipelineLimit, user);
	}


	@Lock(value = TASK_SINGLETON_LOCK, type = LockType.PIPELINE_LIMIT, expireSeconds = 30, valueType = Lock.CUSTOM)
	public long countTaskPipelineInUse() {
		cleanInvalidTaskOccupation();
		cleanInvalidPipeline();
		return countTaskPipeline();
	}

	public long countTaskPipeline() {
		return count(new Query());
	}

	public void validateTaskPipeline() {
		BulkOperations bulkOperations = repository.bulkOperations(BulkOperations.BulkMode.UNORDERED);
		AtomicBoolean needExecute = new AtomicBoolean(false);
		Query query = new Query();
		query.fields().include("_id");
		List<TaskPipelineLimitDto> taskPipelineLimitDtos = findAll(query);
		if (taskPipelineLimitDtos.size() == 0) return;
		if (null != datasourcePipelineLimit && taskPipelineLimitDtos.size() > datasourcePipelineLimit) {
			int remove = taskPipelineLimitDtos.size() - datasourcePipelineLimit;
			for (int i = 0; i < remove; i++) {
				Query removeQuery = Query.query(Criteria.where("_id").is(taskPipelineLimitDtos.get(i).getId()));
				bulkOperations.remove(removeQuery);
				needExecute.set(true);
			}
		}
		if (needExecute.get()) {
			bulkOperations.execute();
		}
		List<TaskDto> runningTasks = getRunningOrScheduleTask(null);
		//遍历运行中的任务，分析获取管道信息，去授权表中查找，没有的话停止任务并更新授权表
		runningTasks.forEach(t -> stopTaskIfNeed(t, t.getUserId()));
	}

	@Lock(value = TASK_SINGLETON_LOCK, type = LockType.PIPELINE_LIMIT, expireSeconds = 30, valueType = Lock.CUSTOM)
	protected void stopTaskIfNeed(TaskDto taskDto, String userId) {
		flushPipelineAndTaskIdsMap();
		AtomicBoolean valid = new AtomicBoolean(true);
		List<Node> sources = taskDto.getDag().getSourceNodes();
		//目标节点
		List<Node> targets = taskDto.getDag().getTargets();

		sources.forEach(source -> {
			if (!valid.get()) return;
			targets.forEach(target -> {
				if (!valid.get()) return;
				//分析管道信息
				String sourceInstanceId = buildInstanceInfo(source).get("id");
				String targetInstanceId = buildInstanceInfo(target).get("id");
				String pipelineId = generatePipelineId(sourceInstanceId, targetInstanceId);
				if (!pipelineAndTaskIdsMap.containsKey(pipelineId) || !pipelineAndTaskIdsMap.get(pipelineId).contains(taskDto.getId().toHexString())) {
					Update update = new Update();
					update.pull("taskIds", taskDto.getId().toHexString());
					update.set("last_updated", new Date());
					updateMany(new Query(), update);
					valid.set(false);
				}
			});
		});
		if (!valid.get()) {
			UserDetail user = userService.loadUserById(new ObjectId(userId));
			taskService.pause(taskDto.getId(), user, false);
			log.warn(String.format("The task [%s] is stopped because pipeline grant is illegal", taskDto.getName()));
		}
	}

	protected void flushPipelineAndTaskIdsMap() {
		pipelineAndTaskIdsMap = new ConcurrentHashMap<>();
		List<TaskPipelineLimitDto> taskPipelineDtos = findAll(new Query());
		taskPipelineDtos.forEach(dto -> {
			pipelineAndTaskIdsMap.put(dto.getPipelineId(), dto.getTaskIds());
		});
	}

	protected void cleanInvalidTaskOccupation() {
		flushPipelineAndTaskIdsMap();
		//查所有taskPipeline的taskId，判断该任务是否在运行中，如果不在从taskIds中移除
		BulkOperations bulkOperations = repository.bulkOperations(BulkOperations.BulkMode.UNORDERED);
		AtomicBoolean needExecute = new AtomicBoolean(false);
		pipelineAndTaskIdsMap.forEach((pipelineId, taskIds) -> {
			taskIds.forEach(id -> {
				List<TaskDto> tasks = getRunningOrScheduleTask(id);
				if (CollectionUtils.isEmpty(tasks)) {
					Query query = Query.query(Criteria.where("pipelineId").is(pipelineId));
					Update update = new Update();
					update.pull("taskIds", id);
					update.set("last_updated", new Date());
					bulkOperations.updateOne(query, update);
					needExecute.set(true);
				}
			});
		});
		if (needExecute.get()) {
			bulkOperations.execute();
		}
	}

	protected List<TaskDto> getRunningOrScheduleTask(String id) {
		Query query = Query.query(Criteria.where(IS_DELETED).ne(true)
			.and(SYNC_TYPE).in(TaskDto.SYNC_TYPE_SYNC, TaskDto.SYNC_TYPE_MIGRATE)
			.and(STATUS).nin(TaskDto.STATUS_DELETE_FAILED, TaskDto.STATUS_DELETING)
			.orOperator(Criteria.where(STATUS).in(TaskDto.STATUS_RUNNING, TaskDto.STATUS_SCHEDULING, TaskDto.STATUS_WAIT_RUN),
				Criteria.where(PLAN_START_DATE_FLAG).is(true),
				Criteria.where(CRONTAB_EXPRESSION_FLAG).is(true)
			));
		if (id != null) {
			query.addCriteria(Criteria.where("_id").is(new ObjectId(id)));
		}
		query.fields().include("_id", "userId", "dag");
		List<TaskDto> tasks = taskService.findAll(query);
		return tasks;
	}

	protected void cleanInvalidPipeline() {
		//清除taskIds为空的taskPipeline
		List<TaskPipelineLimitDto> taskPipelines = findAll(new Query());
		taskPipelines.forEach(t -> {
			if (CollectionUtils.isEmpty(t.getTaskIds())) {
				deleteById(t.getId());
			}
		});
	}

	protected void cleanCurrentTaskOccupation(TaskDto taskDto) {
		flushPipelineAndTaskIdsMap();
		//根据当前的taskId，查询taskPipeline的taskIds中是否存在，如果存在则移除
		BulkOperations bulkOperations = repository.bulkOperations(BulkOperations.BulkMode.UNORDERED);
		AtomicBoolean needExecute = new AtomicBoolean(false);
		pipelineAndTaskIdsMap.forEach((pipelineId, taskIds) -> {
			if (taskIds.contains(taskDto.getId().toHexString())) {
				Query query = Query.query(Criteria.where("pipelineId").is(pipelineId));
				Update update = new Update();
				update.pull("taskIds", taskDto.getId().toHexString());
				update.set("last_updated", new Date());
				bulkOperations.updateOne(query, update);
				needExecute.set(true);
			}
		});
		if (needExecute.get()) {
			bulkOperations.execute();
		}
	}

	protected boolean grantForCurrentTask(TaskDto taskDto, Integer datasourcePipelineLimit, UserDetail user) {
		flushPipelineAndTaskIdsMap();
		AtomicBoolean inLimit = new AtomicBoolean(true);
		//获取任务的源节点和目标节点的connectionId，查询datasourceInstanceId，构建pipeline信息，插入当前的taskId
		if (StringUtils.equalsAny(taskDto.getSyncType(), TaskDto.SYNC_TYPE_MIGRATE, TaskDto.SYNC_TYPE_SYNC)) {
			// 获取所有源节点
			List<Node> sources = taskDto.getDag().getSourceNodes();
			//目标节点
			List<Node> targets = taskDto.getDag().getTargets();
			sources.forEach(source -> {
				if (!inLimit.get()) return;
				targets.forEach(target -> {
					Map<String, String> sourceInstanceMap = buildInstanceInfo(source);
					Map<String, String> targetInstanceMap = buildInstanceInfo(target);
					String sourceInstanceId = sourceInstanceMap.get("id");
					String targetInstanceId = targetInstanceMap.get("id");
					String pipelineId = generatePipelineId(sourceInstanceId, targetInstanceId);
					if (pipelineAndTaskIdsMap.containsKey(pipelineId) && !pipelineAndTaskIdsMap.get(pipelineId).contains(taskDto.getId().toHexString())) {
						Query query = Query.query(Criteria.where("pipelineId").is(pipelineId));
						Update update = new Update();
						update.push("taskIds", taskDto.getId().toHexString());
						update.set("last_updated", new Date());
						update(query, update);
					} else {
						// 查询当前的taskPipeline数量是否超出
						long taskPipelineCount = countTaskPipeline();
						if (taskPipelineCount >= datasourcePipelineLimit) {
							inLimit.set(false);
							return;
						}
						TaskPipelineLimitDto taskPipelineDto = new TaskPipelineLimitDto();
						taskPipelineDto.setPipelineId(pipelineId);
						List<Map<String, String>> instanceInfos = new ArrayList<>();
						instanceInfos.add(sourceInstanceMap);
						instanceInfos.add(targetInstanceMap);
						taskPipelineDto.setInstanceInfos(instanceInfos);
						List<String> taskIds = new ArrayList<>();
						taskIds.add(taskDto.getId().toHexString());
						taskPipelineDto.setTaskIds(taskIds);
						TaskPipelineLimitDto save = save(taskPipelineDto, user);
						if (null != save.getId()) {
							pipelineAndTaskIdsMap.putIfAbsent(pipelineId, taskIds);
						}
					}
				});
			});
		}
		return inLimit.get();
	}

	protected String generatePipelineId(String sourceInstanceId, String targetInstanceId) {
		if (StringUtils.isBlank(sourceInstanceId) || StringUtils.isBlank(targetInstanceId)) {
			return null;
		}
		List<String> instanceIds = new ArrayList<>();
		instanceIds.add(sourceInstanceId);
		instanceIds.add(targetInstanceId);
		Collections.sort(instanceIds);
		return DigestUtil.md5Hex(String.join("|", instanceIds.get(0), instanceIds.get(1)));
	}

	protected Map<String, String> buildInstanceInfo(Node node) {
		if (node instanceof DataParentNode) {
			Map<String, String> instanceInfo = new HashMap<>();
			String connectionId = ((DataParentNode) node).getConnectionId();
			DataSourceConnectionDto connectionDto = dataSourceService.findById(new ObjectId(connectionId), "encryptConfig", "datasourceInstanceTag", "definitionPdkId", "pdkHash", "database_type");
			Map<String, Object> config = connectionDto.getConfig();
			Object instanceId = config.get("datasourceInstanceId");
			if (null == instanceId) {
				instanceInfo.put("id", connectionDto.getId().toHexString());
				instanceInfo.put("tag", connectionDto.getConnectionString());
			} else {
				String instanceTag = connectionDto.getDatasourceInstanceTag();
				instanceInfo.put("id", instanceId.toString());
				instanceInfo.put("tag", instanceTag);
			}
			instanceInfo.put("pdkId", connectionDto.getDefinitionPdkId());
			instanceInfo.put("pdkName", connectionDto.getDatabase_type());
			instanceInfo.put("pdkHash", connectionDto.getPdkHash());
			return instanceInfo;
		} else {
			throw new BizException("License.NodeInstanceIdInvalid");
		}
	}

	public List<TaskPipelineLimitDto> pipelineDetails() {
		return findAll(new Query());
	}
}
