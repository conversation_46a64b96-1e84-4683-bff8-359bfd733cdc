name: Merge Request CI

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}

on:
  push:
    branches: [ "main", "develop", "release-v*.*" ]
  pull_request:
    branches: [ "main", "develop", "release-v*.*" ]

jobs:

  Get-Branch:
    runs-on: ubuntu-latest
    outputs:
      LICENSE_BRANCH: ${{ steps.set-outputs.outputs.LICENSE_BRANCH }}
    steps:
      - name: Get License Branch Name -- Common
        run: |
          echo "LICENSE_BRANCH=${{ github.ref_name }}" >> $GITHUB_ENV
      - name: Get License Branch Name -- Pull Request
        if: ${{ github.event_name == 'pull_request' }}
        run: |
          echo "LICENSE_BRANCH=${{ github.event.pull_request.head.ref }}" >> $GITHUB_ENV
      - name: Get License Branch Name -- Schedule
        if: ${{ github.event_name == 'schedule' }}
        run: |
          echo "LICENSE_BRANCH=develop" >> $GITHUB_ENV
      - name: Set Output
        id: set-outputs
        run: |
          echo "::set-output name=LICENSE_BRANCH::${LICENSE_BRANCH}"

  Get-Stable-Branch:
    needs:
      - Get-Branch
    uses: tapdata/tapdata-application/.github/workflows/get-stable-branch-and-set-tag.yaml@main
    with:
      license-branch: ${{ needs.Get-Branch.outputs.LICENSE_BRANCH }}
    secrets:
      TAPDATA_ENT_CICD_TOKEN: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}

  Sync-Code-to-Office:
    uses: tapdata/tapdata-application/.github/workflows/sync-code-to-office.yaml@main
    needs:
      - Get-Stable-Branch
    with:
      tapdata: main
      tapdata-enterprise: main
      tapdata-license: ${{ needs.Get-Stable-Branch.outputs.LICENSE_BRANCH }}
      tapdata-connectors: main
      tapdata-connectors-enterprise: main
      tapdata-enterprise-web: main
    secrets:
      TAPDATA_ENT_CICD_TOKEN: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}

  Scan-License:
    runs-on: ubuntu-latest
    needs:
      - Sync-Code-to-Office
      - Get-Stable-Branch
    steps:
      - name: Scan -- Tapdata-License
        uses: convictional/trigger-workflow-and-wait@v1.6.1
        with:
          owner: tapdata
          repo: tapdata-application
          github_token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          workflow_file_name: sonarqube-scan.yaml
          ref: main
          wait_interval: 10
          client_payload: '{"tapdata-license": "${{ needs.Get-Stable-Branch.outputs.LICENSE_BRANCH }}"}'
          propagate_failure: true
          trigger_workflow: true
          wait_workflow: true

#  Build-And-Deploy:
#    if: ${{ github.event_name == 'schedule' || inputs.mrci_run }}
#    runs-on: ubuntu-latest
#    needs:
#      - Sync-Code-to-Office
#      - Get-Stable-Branch
#    outputs:
#      IP: ${{ steps.get_ip_port.outputs.IP }}
#      PORT: ${{ steps.get_ip_port.outputs.PORT }}
#    steps:
#      - name: Trigger - Build Tapdata
#        uses: convictional/trigger-workflow-and-wait@v1.6.1
#        with:
#          owner: tapdata
#          repo: tapdata-application
#          github_token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
#          workflow_file_name: build-tapdata-op.yaml
#          ref: main
#          wait_interval: 10
#          client_payload: '{"LISENSE_BRANCH": "${{ needs.Get-Stable-Branch.outputs.LICENSE_BRANCH }}", "TAG_NAME": "${{ needs.Get-Stable-Branch.outputs.TAG_NAME }}"}'
#          propagate_failure: true
#          trigger_workflow: true
#          wait_workflow: true
#      - name: Trigger - Deploy Tapdata
#        uses: convictional/trigger-workflow-and-wait@v1.6.1
#        with:
#          owner: tapdata
#          repo: tapdata-application
#          github_token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
#          workflow_file_name: deploy-tapdata-op.yaml
#          ref: main
#          wait_interval: 10
#          client_payload: '{"TAG_NAME": "${{ needs.Get-Stable-Branch.outputs.TAG_NAME }}", "AUTO_TEST": true}'
#          propagate_failure: true
#          trigger_workflow: true
#          wait_workflow: true
#      - name: Checkout Tapdata Application
#        uses: actions/checkout@v2
#        with:
#          repository: 'tapdata/tapdata-application'
#          ref: "main"
#          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
#          path: tapdata-application
#      - name: Get IP and Port
#        id: get_ip_port
#        run: |
#          bash tapdata-application/build/upgrade.sh --get-ip-port=true --version=${{ needs.Get-Stable-Branch.outputs.TAG_NAME }} --deploy-way=docker-compose
#          IP=$(cat .service_ip_port | awk -F':' '{print $1}')
#          PORT=$(cat .service_ip_port | awk -F':' '{print $2}')
#          echo "::set-output name=IP::$IP"
#          echo "::set-output name=PORT::$PORT"
#
#  TestSigma-Test:
#    if: ${{ github.event_name == 'schedule' || inputs.mrci_run }}
#    runs-on: ubuntu-latest
#    needs:
#      - Build-And-Deploy
#    steps:
#      - name: Set Action
#        if: ${{ github.event_name == 'workflow_dispatch' }}
#        run: |
#          echo "Action=workflow_dispatch" >> $GITHUB_ENV
#      - name: Set Action
#        if: ${{ github.event_name == 'schedule' }}
#        run: |
#          echo "Action=schedule" >> $GITHUB_ENV
#      - name: Trigger - Deploy Tapdata
#        uses: convictional/trigger-workflow-and-wait@v1.6.1
#        with:
#          owner: tapdata
#          repo: tapdata-application
#          github_token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
#          workflow_file_name: testsigma.yaml
#          ref: main
#          wait_interval: 10
#          client_payload: '{"IP": "${{ needs.Build-And-Deploy.outputs.IP }}", "PORT": "${{ needs.Build-And-Deploy.outputs.PORT }}", "Action": "${{ env.Action }}"}'
#          propagate_failure: true
#          trigger_workflow: true
#          wait_workflow: true
#
#  CLOSE_ENV:
#    if: ${{ github.event_name == 'schedule' || failure() || inputs.mrci_run }}
#    runs-on: ubuntu-latest
#    needs:
#      - TestSigma-Test
#      - Get-Stable-Branch
#    steps:
#      - name: Checkout Tapdata Application
#        uses: actions/checkout@v2
#        with:
#          repository: 'tapdata/tapdata-application'
#          ref: "main"
#          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
#          path: tapdata-application
#      - name: Close Test Env
#        id: get_ip_port
#        run: |
#          bash tapdata-application/build/upgrade.sh --delete-env=${{ needs.Get-Stable-Branch.outputs.TAG_NAME }} --delete-env-tag=true
#
