{"properties": {"name": "hive3", "icon": "icons/hive3.png", "id": "hive3", "doc": "${doc}", "tags": ["Database"]}, "configOptions": {"capabilities": [{"id": "dml_insert_policy", "alternatives": ["update_on_exists", "ignore_on_exists"]}, {"id": "dml_update_policy", "alternatives": ["ignore_on_nonexists", "insert_on_nonexists"]}], "connection": {"type": "object", "properties": {"host": {"type": "string", "title": "${host}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_host", "x-index": 1, "required": true}, "port": {"type": "string", "title": "${port}", "x-decorator": "FormItem", "x-component": "InputNumber", "apiServerKey": "database_port", "x-index": 2, "required": true}, "database": {"type": "string", "title": "${database}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_name", "x-index": 3, "required": true}, "user": {"type": "string", "title": "${user}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "database_username", "x-index": 4, "required": true}, "password": {"type": "string", "title": "${password}", "x-decorator": "FormItem", "x-component": "Password", "apiServerKey": "database_password", "x-index": 5}, "extParams": {"type": "string", "title": "${extParams}", "x-decorator": "FormItem", "x-component": "Input", "apiServerKey": "extParams", "x-index": 6}, "timezone": {"type": "string", "title": "${timezone}", "default": "", "x-decorator": "FormItem", "x-component": "Select", "x-index": 7, "enum": [{"label": "", "value": ""}, {"label": "UTC -11", "value": "-11:00"}, {"label": "UTC -10", "value": "-10:00"}, {"label": "UTC -09", "value": "-09:00"}, {"label": "UTC -08", "value": "-08:00"}, {"label": "UTC -07", "value": "-07:00"}, {"label": "UTC -06", "value": "-06:00"}, {"label": "UTC -05", "value": "-05:00"}, {"label": "UTC -04", "value": "-04:00"}, {"label": "UTC -03", "value": "-03:00"}, {"label": "UTC -02", "value": "-02:00"}, {"label": "UTC -01", "value": "-01:00"}, {"label": "UTC", "value": "+00:00"}, {"label": "UTC +01", "value": "+01:00"}, {"label": "UTC +02", "value": "+02:00"}, {"label": "UTC +03", "value": "+03:00"}, {"label": "UTC +04", "value": "+04:00"}, {"label": "UTC +05", "value": "+05:00"}, {"label": "UTC +06", "value": "+06:00"}, {"label": "UTC +07", "value": "+07:00"}, {"label": "UTC +08", "value": "+08:00"}, {"label": "UTC +09", "value": "+09:00"}, {"label": "UTC +10", "value": "+10:00"}, {"label": "UTC +11", "value": "+11:00"}, {"label": "UTC +12", "value": "+12:00"}, {"label": "UTC +13", "value": "+13:00"}, {"label": "UTC +14", "value": "+14:00"}]}}}}, "messages": {"default": "en_US", "en_US": {"host": "Host", "port": "Port", "database": "database", "user": "user", "password": "password", "extParams": "Connection Parameter String", "timezone": "timezone", "doc": "docs/hive3_en_US.md"}, "zh_CN": {"host": "地址", "port": "端口", "database": "数据库", "user": "账号", "password": "密码", "extParams": "连接参数", "timezone": "时区", "doc": "docs/hive3_zh_CN.md"}, "zh_TW": {"host": "地址", "port": "端口", "database": "數據庫", "user": "賬號", "password": "密碼", "extParams": "連接參數", "timezone": "時區", "doc": "docs/hive3_zh_TW.md"}}, "dataTypes": {"string": {"to": "TapString", "byte": "64k", "pkEnablement": false}, "char($byte)": {"to": "TapString", "byte": "255", "byteRatio": 3, "fixed": true}, "varchar($byte)": {"to": "TapString", "byte": "65535"}, "tinyint[($zerofill)]": {"to": "TapNumber", "bit": 8, "precision": 3, "value": [-128, 127]}, "smallint[($zerofill)]": {"to": "TapNumber", "bit": 16, "precision": 5, "value": [-32768, 32767]}, "int[($zerofill)]": {"to": "TapNumber", "bit": 32, "precision": 10, "value": [-2147483648, 2147483647]}, "bigint[($zerofill)]": {"to": "TapNumber", "bit": 64, "precision": 19, "value": [-9223372036854775808, 9223372036854775807]}, "float": {"to": "TapNumber", "name": "float", "bit": 32, "fixed": false}, "double": {"to": "TapNumber", "bit": 64, "fixed": false}, "decimal($precision,$scale)": {"to": "TapNumber", "precision": [1, 38], "scale": [0, 38], "defaultPrecision": 10, "defaultScale": 0}, "date": {"to": "TapDate", "range": ["1000-01-01", "9999-12-31"], "pattern": "yyyy-MM-dd"}, "timestamp": {"to": "TapDateTime", "range": ["1970-01-01 00:00:00.000", "2038-01-11 23:59:59.999"], "pattern": "yyyy-MM-dd HH:mm:ss.SSS", "fraction": [0, 6], "defaultFraction": 0, "withTimeZone": true}, "boolean": {"bit": 1, "name": "boolean", "to": "TapBoolean"}}}