# tapdata-license

## 代码变更注意

### 历史版本问题修复

- 设定要修复的分支为：`release-v3.5.7`
- 从要修复的分支拉出修复分支 `fix-v3.5.7-<issues>-user`
- 代码变更后按 [tapdata-license-client](tapdata-license-client/readme.md) 内容操作
- 提 PR 将代码合并到 `release-v3.5.7` 分支

> 注意，不能合并 main 分支，如果 main 分支需要修复，参考《兼容旧版本》的操作

### 兼容旧版本

- 从 `main` 分支创建修复分支：`fix-main-<issues>-<user>`
- 代码变更后按 [tapdata-license-client](tapdata-license-client/readme.md) 内容操作
- 提 PR 将代码合并到 `main` 分支

### 不能兼容旧版本

- 从 `main` 分支创建新分支：`release-<lastVersion>`
  - `lastVersion` 为应用最后一个发版号（非当前迭代要发版的分支）
- 从 `main` 分支创建修复分支：`fix-main-<issues>-<user>`
- 代码变更后按 [tapdata-license-client](tapdata-license-client/readme.md) 内容操作
- 提 PR 将代码合并到 `main` 分支
