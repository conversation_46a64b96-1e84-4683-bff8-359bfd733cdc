package com.tapdata.manager.gcp;

import org.junit.Assert;
import org.junit.Test;

import java.net.MalformedURLException;
import java.net.URI;

/**
 * <AUTHOR>
 * create at 2023/11/13 19:35
 */
public class SubscribeTest {

    @Test
    public void testUri() throws MalformedURLException {
        String httpProxy = "http://localhost:1235/";

        Assert.assertEquals("localhost", URI.create(httpProxy).toURL().getHost());
        Assert.assertEquals(1235, URI.create(httpProxy).toURL().getPort());
    }

}
