package com.tapdata.manager.agent.dto;

import com.tapdata.tm.client.worker.QueryWorkerMetricResponse;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;

@Data
public class AgentMetric {

    private Integer runningTaskNum = 0;
    private Map<String, Long> runningTask;

    private List<DataFlows> dataFlows;

    private QueryWorkerMetricResponse.SystemInfo systemInfo;

    @Data
    @AllArgsConstructor
    public static class DataFlows {
        private String id;
        private String name;
    }

    public static AgentMetric fromValue(QueryWorkerMetricResponse.Metric tmMetrics) {

        Optional<QueryWorkerMetricResponse.Metric> metricOpt = Optional.ofNullable(tmMetrics);
        Optional<Integer> taskOpt = metricOpt.map(QueryWorkerMetricResponse.Metric::getRunningNum);
        Optional<Map<String, Long>> taskGroupOpt = metricOpt.map(QueryWorkerMetricResponse.Metric::getRunningTaskNum);
        Optional<List<QueryWorkerMetricResponse.DataFlows>> dataFlowsOpt = metricOpt.map(QueryWorkerMetricResponse.Metric::getDataFlows);
        Optional<QueryWorkerMetricResponse.SystemInfo> systemInfosOpt = metricOpt.map(QueryWorkerMetricResponse.Metric::getSystemInfo);

        AgentMetric agentMetric = new AgentMetric();
        agentMetric.setRunningTaskNum(taskOpt.orElse(0));
        agentMetric.setRunningTask(taskGroupOpt.orElse(null));
        agentMetric.setDataFlows(dataFlowsOpt.map(m -> m.stream().map(dfs -> new AgentMetric.DataFlows(dfs.getId(), dfs.getName())).collect(Collectors.toList())).orElse(Collections.emptyList()));
        agentMetric.setSystemInfo(systemInfosOpt.orElse(new QueryWorkerMetricResponse.SystemInfo()));
        return agentMetric;
    }

}
