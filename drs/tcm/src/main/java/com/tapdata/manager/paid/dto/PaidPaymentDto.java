package com.tapdata.manager.paid.dto;

import com.tapdata.manager.subscribe.UsageType;
import com.tapdata.manager.subscribe.dto.SubscribeItemReq;
import lombok.Data;

import java.util.List;
import java.util.Map;


@Data
public class PaidPaymentDto {

    // 价格ID
    private String priceId;
    // 币种
    private String currency;
    // 订单数量
    private Integer quantity;
    // 支付成功的跳转Url
    private String successUrl;

    // 支付失败或者取消支付的跳转Url
    private String cancelUrl;
    // 支付ID
    private String chargeId;

    private String id;

    private String email;
    // 是否续订
    private Boolean renew;
    // 订阅ID
    private String subscribeId;
    // 空或者one_time 为一次 recurring 为订阅
    private String type;

    private String agentId;


    private String refundReason;

    private String refundDescribe;

    private String paymentType;

    private String agentType;

    private String mongodbUrl;
    private String cloudProvider;
    private String mdbPriceId;
    private String platform;

    private String onlyMdb;

    private String subscribeType; // 空或者one_time 为一次 recurring 为订阅
    private UsageType usageType;

    private String resourceId;

    private List<SubscribeItemReq> subscribeItems;

    private String periodUnit;

    private int period;

    private String paymentMethod;

    private String subscribeAlterId;

    private Long startAt;
    private Long endAt;

    private Map<String, String> metadata;
}
