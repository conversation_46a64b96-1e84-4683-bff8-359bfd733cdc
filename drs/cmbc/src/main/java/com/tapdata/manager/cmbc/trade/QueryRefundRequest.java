package com.tapdata.manager.cmbc.trade;

import com.tapdata.manager.cmbc.BaseRequest;
import com.tapdata.manager.common.http.MethodType;
import com.tapdata.manager.common.http.region.ProductDomain;
import lombok.Getter;

/**
 * <AUTHOR>
 * create at 2023/6/28 16:28
 */
@Getter
public class QueryRefundRequest extends BaseRequest<QueryRefundResponse> {

    // 退款操作流水号	orgMerSerialNo	Y	String（32）	退款时的“外部操作流水号”
    private String orgMerSerialNo;

    public QueryRefundRequest(ProductDomain productDomain) {
        super(productDomain, "/bill/samQrySubAcctRefund.do");
        setMethod(MethodType.POST);
    }

    @Override
    public Class<QueryRefundResponse> getResponseClass() {
        return QueryRefundResponse.class;
    }

    public void setOrgMerSerialNo(String orgMerSerialNo) {
        this.orgMerSerialNo = orgMerSerialNo;
        putBodyParameter("orgMerSerialNo", orgMerSerialNo);
    }
}
