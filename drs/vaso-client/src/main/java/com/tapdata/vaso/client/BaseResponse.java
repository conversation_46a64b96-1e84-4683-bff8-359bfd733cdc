package com.tapdata.vaso.client;

import com.tapdata.manager.common.http.DefaultResponse;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * <p></p>
 *
 * <AUTHOR> lhm
 * @date : 2021-02-06 22:00
 **/
@Getter
@Setter
@ToString(callSuper = true)
public class BaseResponse<T> extends DefaultResponse {

    private String state;
    private String requestId;
    private T body;
    private String errorCode;
    private String errorMessage;

    public boolean isSuccess() {
        if (StringUtils.isNotBlank(state) && state.equalsIgnoreCase("OK")) {
            return true;
        }
        return false;
    }

}
