package io.tapdata.pdk.cli;

import org.apache.commons.io.FilenameUtils;

import java.net.URL;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * <PERSON><PERSON><PERSON><PERSON> aims to be the easiest way to create rich command line applications that can run on and off the JVM. Considering picocli? Check what happy users say about picocli.
 * https://picocli.info/
 *
 * <AUTHOR>
 */
public class RegisterMain {
    private static final String BASE_PATH = "/Users/<USER>/IdeaProjects/tapdata-oss/tapdata-connectors-enterprise/";

    private enum ConnectorEnums {
//        GBASE8A(BASE_PATH + "connectors/dist/gbase8a-connector-v1.0-SNAPSHOT.jar", "all", "gbase8a", "basic", "jdbc"),
//        GBASE8S(BASE_PATH + "connectors/dist/gbase8s-connector-v1.0-SNAPSHOT.jar", "all", "gbase8s", "basic", "jdbc"),
//        ORACLE(BASE_PATH + "connectors/dist/oracle-connector-v1.0-SNAPSHOT.jar", "all", "oracle", "basic", "jdbc"),
//        Oceanbase(BASE_PATH + "connectors/dist/oceanbase-oracle-connector-v1.0-SNAPSHOT.jar", "all", "oceanbase-oracle"),
//        MSSQL(BASE_PATH + "connectors/dist/mssql-connector-v1.0-SNAPSHOT.jar", "all", "mssql", "basic", "jdbc"),
//        DB2(BASE_PATH + "connectors/dist/db2-connector-v1.0-SNAPSHOT.jar", "all", "db2", "basic", "jdbc"),
//        KINGBASER6(BASE_PATH + "connectors/dist/kingbaser6-connector-v1.0-SNAPSHOT.jar", "all", "kingbaser6", "basic", "jdbc"),
//        KINGBASER3(BASE_PATH + "connectors/dist/kingbaser3-connector-v1.0-SNAPSHOT.jar", "all", "kingbaser3", "basic", "jdbc"),
//        DAMENG(BASE_PATH + "connectors/dist/dameng-connector-v1.0-SNAPSHOT.jar", "all", "dameng", "basic", "jdbc"),
//        TENCENT_DB_MYSQL(BASE_PATH + "connectors/dist/tencent-db-mysql-connector-v1.0-SNAPSHOT.jar", "all", "tencent-db-mysql"),
//        INFORMIX(BASE_PATH + "connectors/dist/informix-connector-v1.0-SNAPSHOT.jar", "all", "informix"),
        SYBASE(BASE_PATH + "connectors/dist/sybase-connector-v1.0-SNAPSHOT.jar", "all", "sybase")
//        IRIS(BASE_PATH + "connectors/dist/iris-connector-v1.0-SNAPSHOT.jar", "all", "iris"),
        ;

        private final String path;
        private final Set<String> tags = new HashSet<>();

        ConnectorEnums(String path, String... tags) {
            this.path = path;
            if (null != tags) {
                this.tags.addAll(Arrays.asList(tags));
            }
        }

        public boolean contains(String... tags) {
            for (String s : tags) {
                if (this.tags.contains(s)) return true;
            }
            return false;
        }

        public static void addByTags(List<String> postList, String... tags) {
            for (ConnectorEnums c : ConnectorEnums.values()) {
                if (c.contains(tags)) {
                    postList.add(c.path);
                }
            }
        }
    }

    public static void main(String... args) {
        // VM options samples:
        // -Dtags=all -Dserver=http://localhost:3000
        // -Dtags=dummy,mysql
        // -Dserver=http://*************:31966
        // -Dserver=http://*************:31787
        // -Dserver=http://*************:31321
        // -Dbeta=true

        List<String> postList = new ArrayList<>();

        //1.local
        String server = System.getProperty("server", "http://localhost:5173");
        Collections.addAll(postList, "register", "-a", "3324cfdf-7d3e-4792-bd32-571638d4562f", "-ak", "", "-sk", "", "-t", server);

        //2.cloud-test
//        String server = System.getProperty("server", "https://test3.cloud.tapdata.net/console/tm");
//        Collections.addAll(postList, "register", "-a", "da949282212f4fd7b11796cf2599f9a822a7826ee3e04ca9ac7e75e897fa0b9c", "-ak", "VEkQeQUbimtEWweBpLCxKhnKoDvVFOzV", "-sk", "NSp4pvPCyTJOKPADMP4jBpARgQfBSOcl", "-t", server);

        //3.cloud-net
//        String server = System.getProperty("server", "https://cloud.tapdata.net/console/v3/tm");
//        Collections.addAll(postList, "register", "-a", "da949282212f4fd7b11796cf2599f9a822a7826ee3e04ca9ac7e75e897fa0b9c", "-ak", "tCnHIpwY1KdPHFhFgiIMtkdXzU95dCdY", "-sk", "nfM7BI8y30n42IkNrZWrO4IU4tHBxRAi", "-t", server);

        //4.international
//        String server = System.getProperty("server", "http://cloud.tapdata.io/console/tm");
//        Collections.addAll(postList, "register", "-a", "da949282212f4fd7b11796cf2599f9a822a7826ee3e04ca9ac7e75e897fa0b9c", "-ak", "tCnHIpwY1KdPHFhFgiIMtkdXzU95dCdY", "-sk", "nfM7BI8y30n42IkNrZWrO4IU4tHBxRAi", "-t", server);

        String[] tags = System.getProperty("tags", "all").split(",");
        ConnectorEnums.addByTags(postList, tags);
        Main.registerCommands().execute(postList.toArray(new String[0]));
        System.exit(0);
    }

    private static String basePath() {
        URL resource = RegisterMain.class.getClassLoader().getResource("");
        if (null == resource) {
            return "/";
        }

        try {
            Path path = Paths.get(resource.getPath() + "../../../");
            String basePath = path.toFile().getCanonicalPath() + "/";
            System.out.println("basePath:" + basePath);
            return basePath;
        } catch (Throwable throwable) {
            return FilenameUtils.concat(resource.getPath(), "../../../");
        }

    }
}
