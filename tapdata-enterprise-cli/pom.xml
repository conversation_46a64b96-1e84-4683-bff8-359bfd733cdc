<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <groupId>io.tapdata</groupId>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>tapdata-enterprise-cli</artifactId>
    <version>1.0-SNAPSHOT</version>
    <name>tapdata-enterprise-cli</name>
    <modules>
    </modules>

    <properties>
        <java.version>8</java.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>

        <junit.jupiter.version>5.8.1</junit.jupiter.version>
        <junit.platform.version>1.8.1</junit.platform.version>
    </properties>
    <!--<dependencyManagement>-->
    <dependencies>
        <!--        <dependency>-->
        <!--            <groupId>com.yoyosys</groupId>-->
        <!--            <artifactId>mq_2.21</artifactId>-->
        <!--            <version>2.21.74</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>com.huawei</groupId>-->
        <!--            <artifactId>gauss200</artifactId>-->
        <!--            <version>1.0.0</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>io.tapdata</groupId>
            <artifactId>tapdata-cli</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
    </dependencies>
    <repositories>
        <repository>
            <id>tapdata-tapdata-maven</id>
            <name>maven</name>
            <url>https://tapdata-maven.pkg.coding.net/repository/tapdata/maven/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>private-repository</id>
            <name>Hazelcast Private Repository</name>
            <url>https://repository.hazelcast.com/release/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>4.2.1</version>
                <extensions>true</extensions>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
            </plugin>
        </plugins>
    </build>
</project>
